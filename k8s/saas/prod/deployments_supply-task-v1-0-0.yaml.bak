apiVersion: apps/v1
kind: Deployment
metadata:
  annotations: {}
  labels:
    app: supply-task
    env: prd
    hexcloud.cn/backupDateTime: 2022-06-06T04-13-32Z
    hexcloud.cn/fromBackupCluster: hex-saas
  name: supply-task-v1-0-0
  namespace: saas-boh
spec:
  progressDeadlineSeconds: 600
  replicas: 0
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: supply-task
      env: prd
  template:
    metadata:
      annotations:
        config.linkerd.io/skip-inbound-ports: 1024-65535
        config.linkerd.io/skip-outbound-ports: 1024-65535
        linkerd.io/inject: disabled
        sidecar.istio.io/inject: "true"
      labels:
        app: supply-task
        env: prd
        version: v1-0-0
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: boh
                operator: In
                values:
                - "true"
      containers:
      - command:
        - /bin/sh
        - -c
        - python start.py task
        env:
        - name: CONFIG_PATH
          value: /etc/config/config.yaml
        image: ${image}
        imagePullPolicy: IfNotPresent
        name: supply-task-v1-0-0
        resources:
          limits:
            cpu: 500m
            memory: 1Gi
          requests:
            cpu: 50m
            memory: 512Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /etc/config/
          name: theconfigmap
        - mountPath: /logs
          name: logs-volume
      dnsPolicy: ClusterFirst
      imagePullSecrets:
      - name: acr-credential-ca508de040016c12a712c8e34642baef
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
      - configMap:
          defaultMode: 420
          items:
          - key: config.yaml
            path: config.yaml
          name: supply-config
          optional: false
        name: theconfigmap
      - hostPath:
          path: /data/logsData/supply-task
          type: ""
        name: logs-volume
