apiVersion: apps/v1
kind: Deployment
metadata:
  annotations: {}
  labels:
    app: supply
    env: prd
    hexcloud.cn/backupDateTime: 2022-06-06T04-13-32Z
    hexcloud.cn/fromBackupCluster: hex-saas
  name: supply-v1-0-0
  namespace: saas-boh
spec:
  progressDeadlineSeconds: 600
  replicas: 8
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: supply
      env: prd
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "true"
      labels:
        app: supply
        env: prd
        version: v1-0-0
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: boh
                operator: In
                values:
                - "true"
      containers:
      - command:
        - /bin/sh
        - -c
        - python start.py grpc
        env:
        - name: CONFIG_PATH
          value: /etc/config/config.yaml
        image: ${image}
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 10
          periodSeconds: 5
          successThreshold: 1
          tcpSocket:
            port: 8686
          timeoutSeconds: 5
        name: supply-v1-0-0
        ports:
        - containerPort: 8686
          name: grpc
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          initialDelaySeconds: 10
          periodSeconds: 5
          successThreshold: 1
          tcpSocket:
            port: 8686
          timeoutSeconds: 5
        resources:
          limits:
            cpu: 500m
            memory: 1Gi
          requests:
            cpu: 50m
            memory: 488Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /etc/config/
          name: theconfigmap
        - mountPath: /logs
          name: logs-volume
      dnsPolicy: ClusterFirst
#      imagePullSecrets:
#      - name: acr-credential-ca508de040016c12a712c8e34642baef
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
      - configMap:
          defaultMode: 420
          items:
          - key: config.yaml
            path: config.yaml
          name: supply-config
          optional: false
        name: theconfigmap
      - hostPath:
          path: /data/logsData/bom-periphery
          type: ""
        name: logs-volume
