apiVersion: webapp.my.domain/v1
kind: Edas
metadata:
  name: supply
  namespace: boh-test
spec:
  gateway:
    deploy:
      apiVersion: apps/v1
      kind: Deployment
      metadata:
        name: supply
        namespace: boh-test
      spec:
        selector:
          matchLabels:
            app: supply
        strategy: {}
        template:
          metadata:
            labels:
              app: supply
          spec:
            containers:
              - image: ${image}
                imagePullPolicy: IfNotPresent
                name: supply
                ports:
                  - containerPort: 8686
                    name: grpc
                    protocol: TCP
                  - containerPort: 8080
                    name: http
                    protocol: TCP
                resources: {}
                volumeMounts:
                  - mountPath: /data/conf
                    name: tenant-gateway-vol1
            serviceAccountName: adaptl
            volumes:
              - configMap:
                  defaultMode: 256
                  name: supply
                name: tenant-gateway-vol1
      status: {}
    service:
      apiVersion: v1
      kind: Service
      metadata:
      spec:
        ports:
          - name: grpc
            port: 8686
            targetPort: 0
          - name: http
            port: 8080
            targetPort: 0
        selector:
          app: supply
        type: ClusterIP
      status:
        loadBalancer: {}
  group:
    deploy:
      apiVersion: apps/v1
      kind: Deployment
      metadata:
        name: supply-default
        namespace: boh-test
      spec:
        selector:
          matchLabels:
            app: supply-default
        strategy: {}
        template:
          metadata:
            labels:
              app: supply-default
          spec:
            affinity: {}
            containers:
              - command:
                  - /bin/sh
                  - -c
                  - python start.py grpc
                env:
                  - name: CONFIG_PATH
                    value: /etc/config/config.yaml
                image: ${image}
                imagePullPolicy: IfNotPresent
                name: supply-v1-0-0
                ports:
                  - containerPort: 8686
                    name: grpc
                    protocol: TCP
                resources:
                  limits:
                    cpu: 500m
                    memory: 500Mi
                  requests:
                    cpu: 100m
                    memory: 100Mi
                terminationMessagePath: /dev/termination-log
                terminationMessagePolicy: File
                volumeMounts:
                  - mountPath: /etc/config/
                    name: theconfigmap
                  - mountPath: /logs
                    name: logs-volume
            dnsPolicy: ClusterFirst
            imagePullSecrets:
              - name: acr-credential-ca508de040016c12a712c8e34642baef
              - name: hex-container-registry
            restartPolicy: Always
            schedulerName: default-scheduler
            securityContext: {}
            terminationGracePeriodSeconds: 30
            volumes:
              - configMap:
                  defaultMode: 420
                  items:
                    - key: config.yaml
                      path: config.yaml
                  name: supply-config
                  optional: false
                name: theconfigmap
              - hostPath:
                  path: /data/logsData/bom-periphery
                  type: ""
                name: logs-volume
      status: {}
    service:
      apiVersion: v1
      kind: Service
      metadata:
      spec:
        ports:
          - name: grpc
            port: 8686
            targetPort: 0
        selector:
          app: supply-default
        type: ClusterIP