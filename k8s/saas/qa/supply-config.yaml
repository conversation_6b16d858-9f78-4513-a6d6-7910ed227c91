apiVersion: v1
data:
  config.yaml: >-
    Externalpath:
      thirdparty: auth-thirdparty.infra-test:50052
    TracingAnalysisEndpoint:
    http://tracing-analysis-dc-sh.aliyuncs.com/adapt_fa4dixclnb@450d6e1708b1d65_fa4dixclnb@53df7ad2afe8301/api/traces

    TracingHttpName: tenant-gateway.boh-test

    TracingServerName: tenant-gateway.boh-test

    kubernetes:
      namespace: "boh-test"
      labels:
        - key: app.isolation.hexcloud.cn/gw-switch
          value: "on"
        - key: gateway-selector
          value: supply
      rules:
        prefix: app.isolation.match.hexcloud.cn/
    log:
      level: info
    oauth: http://hydra.infra-test:8080

    redis:
      connectTimeout: 5000
      db: "0"
      host: common-redis:6379
      maxActive: 0
      maxIdle: 10
      password: ""
      readTimeout: 180000
      writeTimeout: 3000
    jjy_partner_ids: "1026"
    server:
      grpc:
        name: supply
        port: 8686
      http:
        addr: 0.0.0.0:8080
kind: ConfigMap
metadata:
  name: supply
  namespace: boh-test
