apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: supply-task
  name: supply-task
  namespace: boh-test
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: supply-task
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      annotations:
        linkerd.io/inject: enabled
        sidecar.istio.io/inject: 'false'
      labels:
        app: supply-task
    spec:
      affinity: {}
      containers:
        - command:
            - /bin/sh
            - '-c'
            - python start.py task
          env:
            - name: CONFIG_PATH
              value: /etc/config/config.yaml
          image: ${image}
          imagePullPolicy: IfNotPresent
          name: supply-task-v1-0-0
          resources:
            limits:
              cpu: 800m
              memory: 800Mi
            requests:
              cpu: 50m
              memory: 100Mi
          securityContext:
            capabilities: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /etc/config/
              name: theconfigmap
            - mountPath: /logs
              name: logs-volume
      dnsConfig: {}
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: acr-credential-ca508de040016c12a712c8e34642baef
        - name: hex-container-registry
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
        - configMap:
            defaultMode: 420
            items:
              - key: config.yaml
                path: config.yaml
            name: supply-config
            optional: false
          name: theconfigmap
        - hostPath:
            path: /data/logsData/supply-task
            type: ''
          name: logs-volume

