apiVersion: v1
data:
  config.yaml: |-
    app:
        name: supply
        host: 0.0.0.0
        port: 8686
        max_worker: 10
        mode: grpc     # grpc or task

    logger:
        console:
            level: INFO
            format: "%(asctime)s %(levelname)-8s:%(name)s-%(message)s"
        sqlalchemy:
            engine:
                level: INFO

    data_center: 1  # for snowflake ID generation
    id_generator_addr: uuid.saas-infra:6171
    sentry:
        enabled: False
        dsn: ''
        environment: 'prod'

    mysql:
        user: saas
        password: ER23s64f334wdkBNH
        host: mysql-p0
        port: 3306
        database: saas_boh_supply

    redis_base:
        host: common-redis
        port: 6379
        database: 1
        password: 'ER23s64f334wdkBNH'

    #[START NSQ]
    # supply_code: supply
    # nsq_address: nsqd:4151
    # nsqd_tcp_addresses: nsqd:4150
    #[END NSQ]

    #主档服务配
    product_ags_host: product-ags
    product_ags_port: 5013
    metadata_address: metadata-v1-0-0:5012
    # 租户平台主档中心
    metadata_center_address: metadata.saas-infra:9090

    #库存服务配置
    inventory_host: inventory
    inventory_port: 8283

    #物料服务配置
    bom_host: bom-venom
    bom_port: 50051

    # 成本服务配置
    cost_host: cost-engine
    cost_port: 8801
    cost_callback: 'supply:8686'

    prometheus:
        host: 0.0.0.0
        port: 8080

    tracing:
        service_name: supply-dev
        agent_host: 127.0.0.1
        agent_port: 6831
        sample_type: const
        sample_param: 1
        enable: false

    # oauth服务
    oauth_address: oauth:9002

    # report服务
    report_host: report
    report_port: 8686

    # receipt服务
    receipt_host: receipt
    receipt_port: 8686

    # 商品管理服务配置
    products_manage_host: products-manage
    products_manage_port: 8802

    # 信用支付服务配置
    credit_pay_host: credit-pay-v1-0-0
    credit_pay_port: 8802

    supply_code: boh.supply
    mq:
        rocketmq:
            lookup_address: http://MQ_INST_1551953348333975_BXmKEI3s.cn-shanghai.mq-internal.aliyuncs.com:8080
            consumer_group_name: GID_SUPPLY
            access_key: LTAI4FznT9r3PP2gX3MhoMum
            access_secret: ******************************
            channel: ALIYUN
            thread_num: 2
            batch_size: 2
        amqp:
            host: 1551953348333975.mq-amqp.cn-shanghai-a-internal.aliyuncs.com
            port: 5672
            consumer_group_name: doc_plan
            access_key: LTAI4G9dq1fTwKF93ESqFk7M
            access_secret: ******************************
            username:
            password:
            virtual_host: hex_test
            instance_id: 1551953348333975
    auth_address: auth-permission.saas-infra:6500
    ianvs_address: ianvs.saas-infra:50051
    # 导出服务
    export_address: irons:5001
    third_party_host: integration-service
    third_party_port: 9898
    new_adjust_partner: "331"
    # OPEN API对接BIG的租户
    extra_business_config:
        tp_to_big_partners: [331]
    pm_mysql:
        user: saas_boh
        password: HeK@hex&2099
        host: pc-uf6epe30t4821s0t3.rwlb.rds.aliyuncs.com
        port: 3306
        database: saas_product_manage
    kit_url: http://hipos.hexcloud.cn/api/v2/log/bohdoc/insert
    env: saas-product
kind: ConfigMap
metadata:
  labels:
    app: supply-task-grpc
  name: supply-task-grpc
  namespace: saas-boh
