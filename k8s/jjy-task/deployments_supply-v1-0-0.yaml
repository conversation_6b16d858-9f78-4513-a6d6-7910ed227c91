apiVersion: apps/v1
kind: Deployment
metadata:
  name: supply-task-grpc
  namespace: boh
spec:
  replicas: 8
  selector:
    matchLabels:
      app: supply-task-grpc
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "true"
      labels:
        app: supply-task-grpc
    spec:
      containers:
      - command:
        - /bin/sh
        - -c
        - python start.py task
        env:
        - name: CONFIG_PATH
          value: /etc/config/config.yaml
        image: ${image}
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 10
          periodSeconds: 5
          successThreshold: 1
          tcpSocket:
            port: 8686
          timeoutSeconds: 5
        name: supply-task-grpc
        ports:
        - containerPort: 8686
          name: grpc
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          initialDelaySeconds: 10
          periodSeconds: 5
          successThreshold: 1
          tcpSocket:
            port: 8686
          timeoutSeconds: 5
        resources:
          limits:
            cpu: 1000m
            memory: 4096Mi
          requests:
            cpu: 20m
            memory: 400Mi
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /etc/config/
          name: theconfigmap
        - mountPath: /logs
          name: logs-volume
      dnsPolicy: ClusterFirst
      imagePullSecrets:
      - name: hex-container-registry
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
      - configMap:
          defaultMode: 420
          items:
          - key: config.yaml
            path: config.yaml
          name: supply-task-grpc-config
          optional: false
        name: theconfigmap
      - hostPath:
          path: /data/logsData/bom-periphery
          type: ""
        name: logs-volume
