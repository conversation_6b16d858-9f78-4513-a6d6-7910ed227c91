apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: report-task
  name: report-task
  namespace: lelecha-boh
spec:
  replicas: 10
  selector:
    matchLabels:
      app: report-task
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
    type: RollingUpdate
  template:
    metadata:
      annotations:
        cattle.io/timestamp: '2022-07-29T06:00:20Z'
        kubectl.kubernetes.io/restartedAt: '2023-02-16T17:08:46+08:00'
        sidecar.istio.io/inject: 'true'
      creationTimestamp: null
      labels:
        app: report-task
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: node
                    operator: In
                    values:
                      - lelecha-boh
      containers:
        - command:
            - /bin/sh
            - '-c'
            - python start.py task
          env:
            - name: CONFIG_PATH
              value: /etc/config/config.yaml
          image: registry.hexcloud.cn/histore/report/llc-master:49935bab
          imagePullPolicy: Always
          name: report-task
          resources:
            limits:
              cpu: 300m
              memory: 500Mi
            requests:
              cpu: 100m
              memory: 200Mi
          securityContext:
            capabilities: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /etc/config/
              name: theconfigmap
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
        - configMap:
            defaultMode: 420
            items:
              - key: config.yaml
                path: config.yaml
            name: report-config
          name: theconfigmap