apiVersion: apps/v1
kind: Deployment
metadata:
  annotations: {}
  labels:
    app: supply
    env: prd
    hexcloud.cn/backupDateTime: 2022-01-05T04-13-07Z
    hexcloud.cn/fromBackupCluster: hex-saas
  name: supply-v1-0-0
  namespace: boh
spec:
  progressDeadlineSeconds: 600
  replicas: 30
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: supply
      env: prd
  template:
    metadata:
      annotations:
        config.linkerd.io/skip-outbound-ports: 50051,6500,6171,5012,8686,5013,3306
        linkerd.io/inject: disabled
        sidecar.istio.io/inject: "true"
      labels:
        app: supply
        env: prd
        version: v1-0-0
    spec:
      containers:
      - command:
        - /bin/sh
        - -c
        - python start.py grpc
        env:
        - name: CONFIG_PATH
          value: /etc/config/config.yaml
        image: ${image}
        imagePullPolicy: IfNotPresent
        livenessProbe:
          failureThreshold: 3
          initialDelaySeconds: 10
          periodSeconds: 5
          successThreshold: 1
          tcpSocket:
            port: 8686
          timeoutSeconds: 5
        name: supply-v1-0-0
        ports:
        - containerPort: 8686
          name: grpc
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          initialDelaySeconds: 10
          periodSeconds: 5
          successThreshold: 1
          tcpSocket:
            port: 8686
          timeoutSeconds: 5
        resources:
          limits:
            cpu: 1000m
            memory: 2048Mi
          requests:
            cpu: 50m
            memory: 100Mi
        securityContext:
          capabilities: {}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /etc/config/
          name: theconfigmap
        - mountPath: /logs
          name: logs-volume
      dnsPolicy: ClusterFirst
      imagePullSecrets:
      - name: hex-container-registry
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
      - configMap:
          defaultMode: 420
          items:
          - key: config.yaml
            path: config.yaml
          name: supply-config
          optional: false
        name: theconfigmap
      - hostPath:
          path: /data/logsData/bom-periphery
          type: ""
        name: logs-volume
