apiVersion: v1
data:
  config.yaml: |-
    app:
        name: supply
        host: 0.0.0.0
        port: 8686
        max_worker: 10
        mode: grpc     # grpc or task

    logger:
        console:
            level: DEBUG
            format: "%(asctime)s %(levelname)-8s:%(name)s-%(message)s"
        sqlalchemy:
            engine:
                level: DEBUG

    data_center: 1  # for snowflake ID generation
    id_generator_addr: uuid.infra:6171
    sentry:
        enabled: False
        dsn: ''
        environment: 'jjy-prod'

    mysql:
        user: dy_saas
        password: BohP1&1943
        host: pg-p1-master
        port: 3306
        database: dy_boh_supply
    hologres:
        user: BASIC$zt_sales_report_reader
        password: Jjy@hekuo2023
        # host: rm-uf606j5022erh94it.mysql.rds.aliyuncs.com
        host: hgprecn-cn-pe335est6002-cn-shanghai-vpc-st.hologres.aliyuncs.com
        port: 80
        database: zt_hws_bi
    redis_base:
        host: common-redis
        port: 6379
        database: 1
        password: 'Hekuo2021'

    #[START NSQ]
    # supply_code: supply
    # nsq_address: nsqd:4151
    # nsqd_tcp_addresses: nsqd:4150
    #[END NSQ]

    #主档服务配
    product_ags_host: product-ags
    product_ags_port: 5013
    metadata_address: metadata:5012
    # 租户平台主档中心
    metadata_center_address: metadata.infra:5012

    #库存服务配置
    inventory_host: inventory
    inventory_port: 8283

    #物料服务配置
    bom_host: bom-venom
    bom_port: 50051

    # 成本服务配置
    cost_host: cost-engine
    cost_port: 8801
    cost_callback: 'supply:8686'

    prometheus:
        host: 0.0.0.0
        port: 8080

    tracing:
        service_name: supply-dev
        agent_host: 127.0.0.1
        agent_port: 6831
        sample_type: const
        sample_param: 1
        enable: false

    # report服务
    report_host: report
    report_port: 8686

    # receipt服务
    receipt_host: receipt
    receipt_port: 8686

    # 商品管理服务配置
    products_manage_host: products-manage
    products_manage_port: 8802

    # 信用支付服务配置
    credit_pay_host: credit-pay-v1-0-0
    credit_pay_port: 8802

    supply_code: boh.supply
    mq:
        rocketmq:
            lookup_address: http://MQ_INST_1941198991165364_BYxysS8J.cn-shanghai.mq-vpc.aliyuncs.com:8080
            consumer_group_name: GID_SUPPLY
            access_key: LTAI5tSFNDhbxHR1b3VwM8YX
            access_secret: ******************************
            channel: ALIYUN
            thread_num: 8
            batch_size: 2
        amqp:
            host: 1551953348333975.mq-amqp.cn-shanghai-a-internal.aliyuncs.com
            port: 5672
            consumer_group_name: doc_plan
            access_key: LTAI5tCjXBMfxS5zYv8Kikzo
            access_secret: ******************************
            username:
            password:
            virtual_host: hex_test
            instance_id: 1551953348333975
    auth_address: auth-grpc.hexcloud.cn:6500
    ianvs_address: auth-grpc.hexcloud.cn:50051
    # 导出服务
    export_address: irons:5001
    third_party_host: integration-service
    third_party_port: 9898
    new_adjust_partner: "331"
    jjy_partner_ids: "441,436,431"
    # OPEN API对接BIG的租户
    extra_business_config:
        tp_to_big_partners: [331]
    pm_mysql:
        user: dy_saas
        password: dy_boh@qyp&2099
        host: mysql-p0
        port: 3306
        database: dy_product_manage
    kit_url: http://hipos.hexcloud.cn/api/v2/log/bohdoc/insert
    env: jjy-product
kind: ConfigMap
metadata:
  annotations: {}
  labels:
    app: supply
    env: prd
    hexcloud.cn/backupDateTime: 2022-01-05T04-12-43Z
    hexcloud.cn/fromBackupCluster: hex-saas
  name: supply-config
  namespace: boh
