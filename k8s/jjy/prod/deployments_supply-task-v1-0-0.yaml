apiVersion: apps/v1
kind: Deployment
metadata:
  annotations: {}
  labels:
    app: supply-task
    env: prd
    hexcloud.cn/backupDateTime: 2022-01-05T04-13-07Z
    hexcloud.cn/fromBackupCluster: hex-saas
  name: supply-task-v1-0-0
  namespace: boh
spec:
  progressDeadlineSeconds: 600
  replicas: 0
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: supply-task
      env: prd
  template:
    metadata:
      annotations:
        config.linkerd.io/skip-outbound-ports: 50051,6500,6171,5012,3306,8802
        linkerd.io/inject: disabled
        sidecar.istio.io/inject: "true"
      labels:
        app: supply-task
        env: prd
        version: v1-0-0
    spec:
      containers:
      - command:
        - /bin/sh
        - -c
        - python start.py task
        env:
        - name: CONFIG_PATH
          value: /etc/config/config.yaml
        image: ${image}
        imagePullPolicy: IfNotPresent
        name: supply-task-v1-0-0
        resources:
          limits:
            cpu: 500m
            memory: 1024Mi
          requests:
            cpu: 100m
            memory: 300Mi
        securityContext:
          capabilities: {}
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /etc/config/
          name: theconfigmap
        - mountPath: /logs
          name: logs-volume
      dnsPolicy: ClusterFirst
      imagePullSecrets:
      - name: hex-container-registry
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
      - configMap:
          defaultMode: 420
          items:
          - key: config.yaml
            path: config.yaml
          name: supply-config
          optional: false
        name: theconfigmap
      - hostPath:
          path: /data/logsData/supply-task
          type: ""
        name: logs-volume
