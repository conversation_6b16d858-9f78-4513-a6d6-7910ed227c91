#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的语法检查测试
"""

import ast
import sys

def check_syntax(file_path):
    """检查Python文件的语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 尝试解析AST
        ast.parse(source)
        print(f"✅ {file_path} 语法检查通过")
        return True
    except SyntaxError as e:
        print(f"❌ {file_path} 语法错误: {e}")
        print(f"   行号: {e.lineno}, 列号: {e.offset}")
        return False
    except Exception as e:
        print(f"❌ {file_path} 检查失败: {e}")
        return False

if __name__ == "__main__":
    file_path = "supply/module/transfer_service.py"
    success = check_syntax(file_path)
    sys.exit(0 if success else 1)
