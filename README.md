### 供应链服务

1. python 3.6 (目前开发用的3.6.5)
2. 安装依赖 pip install -r requirements.txt -i https://pypi.hexcloud.cn/hex/pypi
3. 如果安装mysqlclient库问题，解决的方式在:https://stackoverflow.com/questions/43740481/python-setup-py-egg-info-mysqlclient
4. 编译proto: cd supply/proto & sh make_proto.sh
5. 


### 服务启动

1.本地启动task模式:CONFIG_PATH=xxx/supply/config/supply_local.yaml python start.py task
2.本地启动grpc模式:CONFIG_PATH=xxx/supply/config/supply_local.yaml python start.py grpc
3.配置文件环境变量:CONFIG_PATH(默认读取supply_local.yaml)
************.164:31415, 对外的grpc ip和port

##### tips:

​如果出现supply/utils/Enums.py不能提交问题，执行:git config core.ignorecase false(忽略大小写)
编译swagger文件: protoc -I. -I./google/api --swagger_out=logtostderr=true:. ***.proto



### 系统的环境依赖

| 组件             | 说明                                         | 部署方式 |
| ---------------- | -------------------------------------------- | -------- |
| RocketMQ         | 用于做消息通知和消息获取，与其他系统交互使用 | 共享     |
| Redis            | 独立使用，不共享（可用，但是未用）           | 独立     |
| MySQL            | 独立数据存储，不共享                         | 独立     |
| OAuth Server     | 用于处理权限与认证                           | 共享     |
| Metadata Server  | 主档服务：region, store, product             | 共享     |
| Inventory Server | 库存服务                                     | 共享     |