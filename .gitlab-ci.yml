variables:
  DOCKER_REGISTRY_URL: "hex-container-registry.cn-shanghai.cr.aliyuncs.com"
  RANCHER2_CLI_IMAGE: "devops/rancher-cli-2.0"
  CI_SCRIPT_URL: "http://test:<EMAIL>/devops-deployment/ci.cd-script.git"
  CI_SCRIPT_FOLDER: "ci.cd-script"
  REPLICAS: "1" # k8s文件里需要引用



stages:
  - build_runtime
  - build
  - deploy_dev
  - deploy_stage
  - deploy_production

before_script:
  - "docker login -u hex_registry@hextech -p hex-registry123 hex-container-registry.cn-shanghai.cr.aliyuncs.com"
  - 'echo "Build fold : $PWD"'
  - export GITLAB_GROUP_NAME=`echo $CI_REPOSITORY_URL|awk -F'/' '{print $4}'`
  - 'echo "GROUP NAME : $GITLAB_GROUP_NAME"'
  - 'echo "PROJECT NAME : $CI_PROJECT_NAME"'
  - 'echo "PROJECT ID : $CI_PROJECT_ID"'
  - 'echo "PROJECT URL : $CI_PROJECT_URL"'
  - 'echo "ENVIRONMENT URL : $CI_ENVIRONMENT_URL"'
  - 'echo "DOCKER REGISTRY URL : $DOCKER_REGISTRY_URL"'
  - 'export PATH=$PATH:/usr/bin'
  # docker repo name must be lowercase
  - 'DEPLOYMENT_CONTAINER="$DOCKER_REGISTRY_URL/$GITLAB_GROUP_NAME/${CI_PROJECT_NAME}_${CI_COMMIT_REF_NAME}"'
  - 'DEPLOYMENT_CONTAINER=$(echo $DEPLOYMENT_CONTAINER|tr "[:upper:]" "[:lower:]")'
  - 'echo DEPLOYMENT_CONTAINER: $DEPLOYMENT_CONTAINER'
  - 'IMAGE_REPO=$DEPLOYMENT_CONTAINER'
  - 'export IMAGE_TAG=$IMAGE_REPO:${CI_COMMIT_SHA:0:8}'
  - 'export IMAGE_TAG_LATEST=$IMAGE_REPO:latest'
  - 'RUNTIME_REPO=$DEPLOYMENT_CONTAINER'
  - 'RUNTIME_TAG=$RUNTIME_REPO:${CI_COMMIT_SHA:0:8}'
  - 'RUNTIME_TAG_LATEST=$RUNTIME_REPO:latest'

build_runtime:
  stage: build_runtime
  image: docker:git
  services:
    - docker:18.09.7-dind
  when: manual
  allow_failure: true
  script:
    - 'echo "Job $CI_JOB_NAME triggered by $GITLAB_USER_NAME ($GITLAB_USER_ID)"'
    - 'echo "Build on $CI_COMMIT_REF_NAME"'
    - 'echo "HEAD commit SHA $CI_COMMIT_SHA"'
    - 'docker build -f ./deployment/Dockerfile_runtime -t $RUNTIME_TAG -t $RUNTIME_TAG_LATEST .'
    - 'docker push $RUNTIME_TAG'
    - 'docker push $RUNTIME_TAG_LATEST'

build_image:
  stage: build
  image: docker:git
  services:
    - docker:18.09.7-dind
  when: manual
  allow_failure: true
  script:
    - 'echo "Job $CI_JOB_NAME triggered by $GITLAB_USER_NAME ($GITLAB_USER_ID)"'
    - 'echo "Build on $CI_COMMIT_REF_NAME"'
    - 'echo "HEAD commit SHA $CI_COMMIT_SHA"'  
    - 'export RUNTIME_TAG_LATEST=$RUNTIME_TAG_LATEST'
    - 'template=`cat ./deployment/Dockerfile.template`'
    - 'printf "cat<<EOF\n$template\nEOF"|sh>deployment//Dockerfile'
    - 'docker build -f ./Dockerfile -t $IMAGE_TAG -t $IMAGE_TAG_LATEST .'
    - 'docker push $IMAGE_TAG'
    - 'docker push $IMAGE_TAG_LATEST'

