app:
    name: supply
    host: 0.0.0.0
    port: 8686
    max_worker: 5
    mode: grpc     # grpc or task

logger:
    console:
        level: DEBUG
        format: "%(asctime)s %(levelname)-8s:%(name)s-%(message)s"
    sqlalchemy:
        engine:
            level: DEBUG

data_center: 1  # for snowflake ID generation
sentry:
    enabled: False
    dsn: ''
    environment: 'dev'
#mysql:
#    user: root
#    password: root
#    host: 127.0.0.1
#    port: 3306
#    database: root
#postgre:
#    user: root
#    password: root
#    host: 127.0.0.1
#    port: 5432
#    database: root
#redis_base:
#   host: 127.0.0.1
#   port: 6379
#   password: ''
#   database: 0
#sqlite:
#    user: root
#    password: root
mysql:
    user: hex_heytea_prod
    password: Hex@1590@9cd
    # host: rm-uf606j5022erh94it.mysql.rds.aliyuncs.com
    host: 127.0.0.1
    port: 3306

    database: supply_xicha_stage
inventory:
    user: xicha_stage
    password: hex_xicha_ds123
    host: rm-uf606j5022erh94it.mysql.rds.aliyuncs.com
#    host: 127.0.0.1
    port: 3306
    database: xicha_stage_inventory
#[START NSQ]
supply_code: supply
#nsq_address: http://127.0.0.1:4151
#nsqd_tcp_addresses: 127.0.0.1:4150
nsq_address: ************:4151
nsqd_tcp_addresses: ************:4150

#[END NSQ]

#主档服务配置
#metadata_host: ************ #这个其实是product-ags的服务
metadata_host: 127.0.0.1
#metadata_port: 30329
metadata_port: 30671

real_metadata_address: 127.0.0.1:30225

#库存服务配置
#172:17:84:181  30301
inventory_host: ************
# inventory_host: 127.0.0.1
inventory_port: 32000

#物料服务配置
bom_host: 127.0.0.1
bom_port: 32074

# 现金日报服务配置
# report_host: report
# report_port: 8686

# 成本服务配置
cost_host: 127.0.0.1
cost_port: 32327

prometheus:
    host: 0.0.0.0
    port: 7070

tracing:
    service_name: supply-dev
    agent_host: ************
    agent_port: 6831
    sample_type: const
    sample_param: 1
    enable: true
id_generator_addr: ************:6171


# oauth服务
oauth_address: ************:32234

# report服务
report_host: localhost
report_port: 32420
