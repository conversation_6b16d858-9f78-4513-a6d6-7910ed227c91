[{"permission": "revenue", "name": "营收", "children": [{"permission": "revenue.e_ticket_report", "name": "电子小票", "children": [{"permission": "revenue.e_ticket_report.query", "name": "查询电子小票", "apis": [{"code": "operation_report.OperationReport.GetETicketReport"}], "children": []}]}, {"permission": "revenue.store_sales_report", "name": "门店销售报表", "children": [{"permission": "revenue.store_sales_report.query", "name": "查询门店销售报表", "apis": [{"code": "operation_report.OperationReport.GetStoreSalesReport"}], "children": []}]}, {"permission": "revenue.product_sales_report", "name": "单品销售报表", "children": [{"permission": "revenue.product_sales_report.query", "name": "查询单品销售报表", "apis": [{"code": "operation_report.OperationReport.GetProductSalesReport"}], "children": []}]}, {"permission": "revenue.discount_report", "name": "折扣报表", "children": [{"permission": "revenue.discount_report.query", "name": "查询折扣报表", "apis": [{"code": "operation_report.OperationReport.GetDiscountReport"}], "children": []}]}, {"permission": "revenue.payment", "name": "支付报表", "children": [{"permission": "revenue.payment.query", "name": "查询支付报表", "apis": [{"code": "operation_report.OperationReport.GetPaymentReport"}], "children": []}]}, {"permission": "revenue.product_payment", "name": "单品支付报表", "children": [{"permission": "revenue.product_payment.query", "name": "查询单品支付报表", "apis": [{"code": "operation_report.OperationReport.GetProductPaymentReport"}], "children": []}]}, {"permission": "revenue.store_channel_sales", "name": "门店渠道报表", "children": [{"permission": "revenue.store_channel_sales.query", "name": "查询门店渠道报表", "apis": [{"code": "operation_report.OperationReport.GetStoreChannelReport"}], "children": []}]}, {"permission": "revenue.product_channel", "name": "产品渠道报表", "children": [{"permission": "revenue.product_channel.query", "name": "查询单品渠道报表", "apis": [{"code": "operation_report.OperationReport.GetProductChannelReport"}], "children": []}]}, {"permission": "revenue.store_period", "name": "门店时段报表", "children": [{"permission": "revenue.store_period.query", "name": "查询门店时段报表", "apis": [{"code": "operation_report.OperationReport.GetStorePeriodReport"}], "children": []}]}, {"permission": "revenue.store_channel_period", "name": "门店渠道时段报表", "children": [{"permission": "revenue.store_channel_period.query", "name": "查询门店渠道时段报表", "apis": [{"code": "operation_report.OperationReport.GetStoreChannelPeriodReport"}], "children": []}]}, {"permission": "revenue.payment_period", "name": "支付时段报表", "children": [{"permission": "revenue.payment_period.query", "name": "查询支付时段报表", "apis": [{"code": "operation_report.OperationReport.GetPaymentPeriodReport"}], "children": []}]}, {"permission": "revenue.product_period", "name": "单品时段报表", "children": [{"permission": "revenue.product_period.query", "name": "查询单品时段报表", "apis": [{"code": "operation_report.OperationReport.GetProductPeriodReport"}], "children": []}]}, {"permission": "revenue.cash_report_status", "name": "现金日报配置", "children": [{"permission": "revenue.cash_report_status.create", "name": "创建现金日报状态", "apis": [{"code": "cash_report.CashReport.CreateCashReportStatus"}], "children": []}, {"permission": "revenue.cash_report_status.query", "name": "查询现金日报状态", "apis": [{"code": "cash_report.CashReport.GetCashReportStatus"}], "children": []}, {"permission": "revenue.cash_report_status.update", "name": "更新现金日报状态", "apis": [{"code": "cash_report.CashReport.UpdateCashReportStatusName"}], "children": []}, {"permission": "revenue.cash_report_status.delete", "name": "删除现金日报状态", "apis": [{"code": "cash_report.CashReport.DeleteCashReportStatus"}], "children": []}]}, {"permission": "revenue.cash_report_accounting", "name": "现金日报 - 财务", "children": [{"permission": "revenue.cash_report_accounting.query", "name": "查询现金日报-财务", "apis": [{"code": "cash_report.CashReport.GetCashReportSummary"}, {"code": "cash_report.CashReport.GetCashReport"}], "children": []}, {"permission": "revenue.cash_report_accounting.approve", "name": "审批现金日报-财务", "apis": [{"code": "cash_report.CashReport.ApproveCashReport"}], "children": []}, {"permission": "revenue.cash_report_accounting.reject", "name": "驳回现金日报-财务", "apis": [{"code": "cash_report.CashReport.RejectCashReportToAccounting"}], "children": []}, {"permission": "revenue.cash_report_accounting.update", "name": "更新现金日报-财务", "apis": [{"code": "cash_report.CashReport.UpdateCashReport"}], "children": []}]}, {"permission": "revenue.cash_report_store", "name": "现金日报 - 门店", "children": [{"permission": "revenue.cash_report_store.query", "name": "查询现金日报-门店", "apis": [{"code": "cash_report.CashReport.GetCashReportSummary"}, {"code": "cash_report.CashReport.GetCashReport"}], "children": []}, {"permission": "revenue.cash_report_store.approve", "name": "审批现金日报-门店", "apis": [{"code": "cash_report.CashReport.ApproveCashReport"}], "children": []}, {"permission": "revenue.cash_report_store.reject", "name": "驳回现金日报-门店", "apis": [{"code": "cash_report.CashReport.RejectCashReportToStore"}], "children": []}, {"permission": "revenue.cash_report_store.update", "name": "更新现金日报-门店", "apis": [{"code": "cash_report.CashReport.UpdateCashReport"}], "children": []}]}]}, {"permission": "supply", "name": "运营管理", "children": [{"permission": "supply.receiving", "name": "收货单", "children": [{"permission": "supply.receiving.query", "name": "查询配送收货单", "apis": [{"code": "receiving.ReceivingService.Listreceiving"}, {"code": "receiving.ReceivingService.GetReceivingById"}, {"code": "receiving.ReceivingService.GetReceivingProductById"}], "children": []}, {"permission": "supply.receiving.cold", "name": "直送收货单", "apis": [{"code": "receiving.ReceivingService.ListReceivingCold"}, {"code": "receiving.ReceivingService.GetReceivingById"}, {"code": "receiving.ReceivingService.GetReceivingProductById"}], "children": []}, {"permission": "supply.receiving.create", "name": "新建收货单", "apis": [{"code": "receiving.ReceivingService.CreateReceiving"}, {"code": "oauth.HexOauth.GetUserById"}], "children": []}, {"permission": "supply.receiving.confirm", "name": "确认收货单", "apis": [{"code": "receiving.ReceivingService.ConfirmReceiving"}, {"code": "product.ProductService.ListDistributionRegionProductByStoreRequest"}, {"code": "product.ProductService.ListPurchaseRegionProductByStoreId"}, {"code": "metadata.ProductService.ListPurchaseRegionProductByStoreId"}, {"code": "metadata.ProductService.ListDistributionRegionProductByStoreId"}, {"code": "oauth.HexOauth.GetUserById"}], "children": []}]}, {"permission": "supply.receiving_diff", "name": "收货差异单", "children": [{"permission": "supply.receiving_diff.query", "name": "查询配送收货差异单", "apis": [{"code": "receiving_diff.ReceivingDiffService.GetReceivingDiffById"}, {"code": "receiving_diff.ReceivingDiffService.ListReceivingDiff"}, {"code": "receiving_diff.ReceivingDiffService.GetReceivingDiffProductById"}], "children": []}, {"permission": "supply.receiving_diff.cold", "name": "直送收货差异单", "apis": [{"code": "receiving_diff.ReceivingDiffService.GetReceivingDiffById"}, {"code": "receiving_diff.ReceivingDiffService.ListReceivingDiffCold"}, {"code": "receiving_diff.ReceivingDiffService.GetReceivingDiffProductById"}], "children": []}, {"permission": "supply.receiving_diff.create", "name": "新建收货差异单", "apis": [{"code": "receiving_diff.ReceivingDiffService.CreateReceivingDiff"}, {"code": "product.ProductService.ListPurchaseRegionProductByStoreId"}, {"code": "oauth.HexOauth.GetUserById"}], "children": []}, {"permission": "supply.receiving_diff.update", "name": "更新配送收货差异单", "apis": [{"code": "receiving_diff.ReceivingDiffService.UpdateReceivingDiffCold"}, {"code": "receiving_diff.ReceivingDiffService.UpdateReceivingRemark"}, {"code": "metadata.ProductService.ListPurchaseRegionProductByStoreId"}, {"code": "oauth.HexOauth.GetUserById"}, {"code": "receiving_diff.ReceivingDiffService.DeleteReceivingDiff"}], "children": []}, {"permission": "supply.receiving_diff.submit", "name": "提交收货差异单", "apis": [{"code": "receiving_diff.ReceivingDiffService.SubmitReceivingDiff"}, {"code": "metadata.ProductService.ListPurchaseRegionProductByStoreId"}, {"code": "oauth.HexOauth.GetUserById"}], "children": []}, {"permission": "supply.receiving_diff.reject", "name": "驳回收货差异单", "apis": [{"code": "receiving_diff.ReceivingDiffService.RejectReceivingDiff"}, {"code": "oauth.HexOauth.GetUserById"}], "children": []}, {"permission": "supply.receiving_diff.confirm", "name": "确认收货差异单", "apis": [{"code": "receiving_diff.ReceivingDiffService.ConfirmReceivingDiff"}, {"code": "metadata.ProductService.ListPurchaseRegionProductByStoreId"}, {"code": "oauth.HexOauth.GetUserById"}], "children": []}]}, {"permission": "supply.returns", "name": "退货单", "children": [{"permission": "supply.returns.query", "name": "查询退货单", "apis": [{"code": "returns.ReturnService.GetReturnById"}, {"code": "returns.ReturnService.ListReturn"}, {"code": "returns.ReturnService.GetReturnProductById"}], "children": []}, {"permission": "supply.returns.create", "name": "新建退货单", "apis": [{"code": "returns.ReturnService.CreateReturn"}, {"code": "returns.ReturnService.GetValidProduct"}, {"code": "oauth.HexOauth.GetUserById"}, {"code": "metadata.ProductService.GetProductById"}, {"code": "metadata.ProductService.GetUnitById"}], "children": []}, {"permission": "supply.returns.update", "name": "更新退货单", "apis": [{"code": "returns.ReturnService.UpdateReturn"}, {"code": "oauth.HexOauth.GetUserById"}, {"code": "metadata.ProductService.GetProductById"}, {"code": "metadata.ProductService.GetUnitById"}], "children": []}, {"permission": "supply.returns.confirm", "name": "确认退货单", "apis": [{"code": "returns.ReturnService.ConfirmReturn"}, {"code": "oauth.HexOauth.GetUserById"}], "children": []}, {"permission": "supply.returns.submit", "name": "提交退货单", "apis": [{"code": "returns.ReturnService.SubmitReturn"}, {"code": "oauth.HexOauth.GetUserById"}], "children": []}, {"permission": "supply.returns.reject", "name": "驳回退货单", "apis": [{"code": "returns.ReturnService.RejectReturn"}, {"code": "oauth.HexOauth.GetUserById"}], "children": []}, {"permission": "supply.returns.approve", "name": "审核退货单", "apis": [{"code": "returns.ReturnService.ApproveReturn"}, {"code": "oauth.HexOauth.GetUserById"}], "children": []}, {"permission": "supply.returns.delete", "name": "删除退货单", "apis": [{"code": "returns.ReturnService.DeleteReturn"}, {"code": "oauth.HexOauth.GetUserById"}], "children": []}]}, {"permission": "supply.receiving.collect", "name": "收货单详细报表", "apis": [{"code": "receiving.ReceivingBiService.GetReceivingCollect"}, {"code": "store.StoreService.ListStore"}, {"code": "product.ProductService.ListProduct"}, {"code": "product.ProductService.ListProductCategory"}], "children": []}, {"permission": "supply.receiving.collect_detailed", "name": "收货单详细报表", "apis": [{"code": "receiving.ReceivingBiService.GetReceivingDetail"}, {"code": "store.StoreService.ListStore"}, {"code": "product.ProductService.ListProduct"}, {"code": "product.ProductService.ListProductCategory"}], "children": []}, {"permission": "supply.receiving_diff.collect", "name": "收货差异单详细报表", "apis": [{"code": "receiving.ReceivingDiffBiService.GetReceivingDiffCollect"}, {"code": "store.StoreService.ListStore"}, {"code": "product.ProductService.ListProduct"}, {"code": "product.ProductService.ListProductCategory"}], "children": []}, {"permission": "supply.receiving_diff.collect_detailed", "name": "收货差异单详细报表", "apis": [{"code": "receiving.ReceivingBiService.GetReceivingDiffDetail"}, {"code": "store.StoreService.ListStore"}, {"code": "product.ProductService.ListProduct"}, {"code": "product.ProductService.ListProductCategory"}], "children": []}, {"permission": "supply.stocktake_schedule", "name": "盘点计划", "children": [{"permission": "supply.stocktake_schedule.create", "name": "新建盘点计划", "apis": [{"code": "stocktake.StocktakeServicer.CreateStocktakeSchedule"}, {"code": "store.StoreService.ListStore"}, {"code": "region.RegionService.ListRegion"}, {"code": "product.ProductService.ListProduct"}, {"code": "product.ProductService.ListProductCategory"}], "children": []}, {"permission": "supply.stocktake_schedule.query", "name": "查询盘点计划", "apis": [{"code": "stocktake.StocktakeServicer.GetStocktakeScheduleByID"}, {"code": "stocktake.StocktakeServicer.GetStocktakeSchedule"}, {"code": "stocktake.StocktakeServicer.GetStocktakeScheduleBranchByID"}, {"code": "stocktake.StocktakeServicer.GetStocktakeScheduleProductByID"}, {"code": "stocktake.StocktakeServicer.GetStocktakeScheduleCategoryByID"}, {"code": "product.ProductService.GetProductById"}], "children": []}, {"permission": "supply.stocktake_schedule.update", "name": "更新盘点计划", "apis": [{"code": "stocktake.StocktakeServicer.PutStocktakeSchedule"}, {"code": "store.StoreService.ListStore"}, {"code": "region.RegionService.ListRegion"}, {"code": "product.ProductService.ListProduct"}, {"code": "product.ProductService.ListProductCategory"}], "children": []}, {"permission": "supply.stocktake_schedule.confirm", "name": "确认盘点计划", "apis": [{"code": "stocktake.StocktakeServicer.ActionStocktakeSchedule"}], "children": []}, {"permission": "supply.stocktake_schedule.cancel", "name": "作废盘点计划", "apis": [{"code": "stocktake.StocktakeServicer.ActionStocktakeSchedule"}], "children": []}, {"permission": "supply.stocktake_schedule.callback", "name": "回退盘点计划", "apis": [{"code": "stocktake.StocktakeServicer.ActionStocktakeSchedule"}], "children": []}]}, {"permission": "supply.stocktake", "name": "盘点单", "children": [{"permission": "supply.stocktake.checked", "name": "新建盘点单", "apis": [{"code": "stocktake.StocktakeServicer.CheckStocktakeByDocID"}, {"code": "stocktake.StocktakeServicer.CheckedStocktakeByDocID"}], "children": []}, {"permission": "supply.stocktake.update", "name": "更新盘点单", "apis": [{"code": "stocktake.StocktakeServicer.PutStocktakeByDocID"}, {"code": "product.ProductService.ListAttributeRegionProductByStoreId"}, {"code": "product.ProductService.ListProduct"}, {"code": "product.ProductService.ListUnit"}, {"code": "product.ProductService.GetUnitById"}, {"code": "stocktake.StocktakeServicer.DeleteStocktakeProductTags"}], "children": []}, {"permission": "supply.stocktake.submit", "name": "提交盘点单", "apis": [{"code": "stocktake.StocktakeServicer.SubmitStocktakeByDocID"}], "children": []}, {"permission": "supply.stocktake.approve", "name": "审核盘点单", "apis": [{"code": "stocktake.StocktakeServicer.ApproveStocktakeByDocID"}, {"code": "inventory.InventoryService.QueryBranchInventory"}, {"code": "inventory.InventoryService.Batch"}], "children": []}, {"permission": "supply.stocktake.reject", "name": "驳回盘点单", "apis": [{"code": "stocktake.StocktakeServicer.RejectStocktakeProduct"}], "children": []}, {"permission": "supply.stocktake.cancel", "name": "作废盘点单", "apis": [{"code": "stocktake.StocktakeServicer.CancelStocktakeByDocID"}], "children": []}, {"permission": "supply.stocktake.query", "name": "查询盘点单", "apis": [{"code": "stocktake.StocktakeServicer.GetStocktakeByDocID"}, {"code": "stocktake.StocktakeServicer.GetStocktake"}, {"code": "stocktake.StocktakeServicer.GetStocktakeProduct"}], "children": []}, {"permission": "supply.stocktake.tags_query", "name": "查询盘点标签", "apis": [{"code": "stocktake.StocktakeServicer.GetStocktakeTags"}], "children": []}, {"permission": "supply.stocktake.tags_add", "name": "新建盘点标签", "apis": [{"code": "stocktake.StocktakeServicer.ActionStocktakeTags"}], "children": []}, {"permission": "supply.stocktake.tags_delete", "name": "删除盘点标签", "apis": [{"code": "stocktake.StocktakeServicer.ActionStocktakeTags"}], "children": []}, {"permission": "supply.stocktake.tags_update", "name": "更新盘点标签", "apis": [{"code": "stocktake.StocktakeServicer.ActionStocktakeTags"}], "children": []}, {"permission": "supply.stocktake.balance", "name": "门店盘点单损益报表", "apis": [{"code": "stocktake.StocktakeServicer.GetStocktakeBalance"}, {"code": "stocktake.StocktakeServicer.GetStocktakeBalanceProductGroup"}], "children": []}, {"permission": "supply.stocktake.region", "name": "区域盘点单损益报表", "apis": [{"code": "stocktake.StocktakeServicer.StocktakeBalanceRegion"}], "children": []}, {"permission": "supply.stocktake.bi", "name": "盘点单报表", "apis": [{"code": "stocktake.StocktakeServicer.StocktakeBiDetailed"}, {"code": "product.ProductService.ListProduct"}], "children": []}]}, {"permission": "supply.transfer", "name": "调拨单", "children": [{"permission": "supply.transfer.query", "name": "查询调拨单", "apis": [{"code": "stocktake.TransferService.GetTransfer"}, {"code": "stocktake.TransferService.GetTransferByID"}, {"code": "stocktake.TransferService.GetTransferProductByTransferID"}], "children": []}, {"permission": "supply.transfer.create", "name": "新建调拨单", "apis": [{"code": "stocktake.TransferService.GetTransferProductByBranchID"}, {"code": "stocktake.TransferService.CreateTransfer"}, {"code": "product.ProductService.ListStore"}, {"code": "product.ProductService.GetStoreById"}, {"code": "product.ProductService.ListAttributeRegionProductByStoreId"}, {"code": "product.ProductService.ListUnit"}, {"code": "product.ProductService.ListProduct"}, {"code": "inventory.InventoryService.QueryBranchInventory"}], "children": []}, {"permission": "supply.transfer.update", "name": "更新调拨单", "apis": [{"code": "stocktake.TransferService.UpdateTransfer"}, {"code": "product.ProductService.ListUnit"}, {"code": "product.ProductService.ListProduct"}, {"code": "stocktake.TransferService.DeleteTransferProduct"}], "children": []}, {"permission": "supply.transfer.confirm", "name": "确认调拨单", "apis": [{"code": "stocktake.TransferService.ConfirmTransfer"}, {"code": "inventory.InventoryService.Batch"}], "children": []}, {"permission": "supply.transfer.delete", "name": "删除调拨单", "apis": [{"code": "stocktake.TransferService.DeleteTransfer"}], "children": []}, {"permission": "supply.transfer.submit", "name": "提交调拨单", "apis": [{"code": "stocktake.TransferService.SubmitTransfer"}, {"code": "inventory.InventoryService.Batch"}, {"code": "inventory.InventoryService.QueryBranchInventory"}], "children": []}, {"permission": "supply.transfer.collect", "name": "调拨单汇总报表", "apis": [{"code": "stocktake.TransferService.GetTransferCollect"}, {"code": "store.StoreService.ListStore"}, {"code": "product.ProductService.ListProduct"}, {"code": "product.ProductService.ListProductCategory"}], "children": []}, {"permission": "supply.transfer.collect_detailed", "name": "调拨单详细报表", "apis": [{"code": "stocktake.TransferService.GetTransferCollectDetailed"}, {"code": "store.StoreService.ListStore"}, {"code": "product.ProductService.ListProduct"}, {"code": "product.ProductService.ListProductCategory"}], "children": []}]}, {"permission": "supply.adjust_schedule", "name": "报废计划", "children": [{"permission": "supply.adjust_schedule.create", "name": "新建报废计划", "apis": [{"code": "adjust.AdjustService.CreateAdjustSchedule"}, {"code": "store.StoreService.ListStore"}, {"code": "region.RegionService.ListRegion"}, {"code": "product.ProductService.ListProduct"}, {"code": "product.ProductService.ListProductCategory"}], "children": []}, {"permission": "supply.adjust_schedule.query", "name": "查询报废计划", "apis": [{"code": "adjust.AdjustService.GetAdjustScheduleByID"}, {"code": "adjust.AdjustService.GetAdjustScheduleBranchByID"}, {"code": "adjust.AdjustService.GetAdjustScheduleProductByID"}, {"code": "adjust.AdjustService.GetAdjustSchedule"}, {"code": "adjust.AdjustService.GetAdjustScheduleCategoryByID"}, {"code": "product.ProductService.GetProductById"}], "children": []}, {"permission": "supply.adjust_schedule.update", "name": "更新报废计划", "apis": [{"code": "adjust.AdjustService.PutAdjustSchedule"}, {"code": "store.StoreService.ListStore"}, {"code": "region.RegionService.ListRegion"}, {"code": "product.ProductService.ListProduct"}, {"code": "product.ProductService.ListProductCategory"}], "children": []}, {"permission": "supply.adjust_schedule.confirm", "name": "确认报废计划", "apis": [{"code": "adjust.AdjustService.ActionAdjustSchedule"}], "children": []}, {"permission": "supply.adjust_schedule.cancel", "name": "作废报废计划", "apis": [{"code": "adjust.AdjustService.ActionAdjustSchedule"}], "children": []}, {"permission": "supply.adjust_schedule.callback", "name": "回退报废计划", "apis": [{"code": "adjust.AdjustService.ActionAdjustSchedule"}], "children": []}]}, {"permission": "supply.adjust", "name": "报废单", "children": [{"permission": "supply.adjust.query", "name": "查询报废单", "apis": [{"code": "adjust.AdjustService.GetAdjust"}, {"code": "adjust.AdjustService.GetAdjustProduct"}, {"code": "adjust.AdjustService.GetAdjustByID"}], "children": []}, {"permission": "supply.adjust.confirm", "name": "确认报废单", "apis": [{"code": "adjust.AdjustService.ConfirmAdjust"}, {"code": "inventory.InventoryService.Batch"}], "children": []}, {"permission": "supply.adjust.create", "name": "新建报废单", "apis": [{"code": "adjust.AdjustService.GetAdjustProductByStoreID"}, {"code": "inventory.InventoryService.QueryBranchInventory"}, {"code": "adjust.AdjustService.CreatedAdjust"}, {"code": "product.ProductService.GetStoreById"}, {"code": "product.ProductService.ListAttributeRegionProductByStoreId"}, {"code": "product.ProductService.ListUnit"}, {"code": "product.ProductService.GetUnitById"}, {"code": "product.ProductService.ListProduct"}], "children": []}, {"permission": "supply.adjust.update", "name": "更新报废单", "apis": [{"code": "adjust.AdjustService.UpdateAdjust"}, {"code": "product.ProductService.ListAttributeRegionProductByStoreId"}, {"code": "product.ProductService.ListUnit"}, {"code": "product.ProductService.GetUnitById"}, {"code": "product.ProductService.ListProduct"}, {"code": "product.ProductService.GetUnitById"}, {"code": "adjust.AdjustService.DeleteAdjustProduct"}], "children": []}, {"permission": "supply.adjust.delete", "name": "删除报废单", "apis": [{"code": "adjust.AdjustService.DeleteAdjust"}], "children": []}, {"permission": "supply.adjust.collect", "name": "报废单汇总报表", "apis": [{"code": "adjust.AdjustService.GetAdjustBiCollect"}, {"code": "product.ProductService.ListProduct"}, {"code": "product.ProductService.ListStore"}], "children": []}, {"permission": "supply.adjust.collect_detailed", "name": "报废单详细报表", "apis": [{"code": "adjust.AdjustService.GetAdjustCollectDetailed"}, {"code": "product.ProductService.ListProduct"}, {"code": "product.ProductService.ListStore"}], "children": []}, {"permission": "supply.adjust.cancel", "name": "取消报废单", "apis": [{"code": "adjust.AdjustService.CancelAdjust"}], "children": []}]}, {"permission": "supply.demand", "name": "订货单", "children": [{"permission": "supply.demand.sd_list", "name": "查询非热麦门店订货单", "apis": [{"code": "supply.supply.ListDemand"}, {"code": "supply.supply.GetDemandProductDetail"}, {"code": "supply.supply.GetDemandDetail"}, {"code": "supply.supply.UpdateDemandInfo"}, {"code": "oauth.HexOauth.GetUserById"}, {"code": "supply.supply.UpdateDemandProduct"}, {"code": "product.ProductService.GetProductById"}, {"code": "product.ProductService.GetUnitById"}, {"code": "product.ProductService.ListDistributionRegionProductByStoreId"}, {"code": "product.ProductService.ListPurchaseRegionProductByStoreId"}, {"code": "supply.supply.DeleteDemandProduct"}], "children": []}, {"permission": "supply.demand.not_sd_list", "name": "查询热麦门店订货单", "apis": [{"code": "supply.supply.ListDemand"}, {"code": "supply.supply.GetDemandProductDetail"}, {"code": "supply.supply.GetDemandDetail"}, {"code": "supply.supply.UpdateDemandInfo"}, {"code": "oauth.HexOauth.GetUserById"}, {"code": "supply.supply.UpdateDemandProduct"}, {"code": "product.ProductService.GetProductById"}, {"code": "product.ProductService.GetUnitById"}, {"code": "product.ProductService.ListDistributionRegionProductByStoreId"}, {"code": "product.ProductService.ListPurchaseRegionProductByStoreId"}, {"code": "supply.supply.ResetDemandMain"}, {"code": "supply.supply.DeleteDemandProduct"}], "children": []}, {"permission": "supply.demand.hd_list", "name": "查询紧急订货单", "apis": [{"code": "supply.supply.ListDemand"}, {"code": "supply.supply.GetDemandProductDetail"}, {"code": "supply.supply.GetDemandDetail"}, {"code": "supply.supply.UpdateDemandInfo"}, {"code": "oauth.HexOauth.GetUserById"}, {"code": "supply.supply.UpdateDemandProduct"}, {"code": "product.ProductService.GetProductById"}, {"code": "product.ProductService.GetUnitById"}, {"code": "product.ProductService.ListDistributionRegionProductByStoreId"}, {"code": "product.ProductService.ListPurchaseRegionProductByStoreId"}, {"code": "supply.supply.ResetDemandMain"}, {"code": "supply.supply.DeleteDemandProduct"}], "children": []}, {"permission": "supply.demand.md_list", "name": "查询主配订货单", "apis": [{"code": "supply.supply.ListDemand"}, {"code": "supply.supply.GetDemandProductDetail"}, {"code": "supply.supply.GetDemandDetail"}, {"code": "supply.supply.UpdateDemandInfo"}, {"code": "oauth.HexOauth.GetUserById"}, {"code": "supply.supply.UpdateDemandProduct"}, {"code": "product.ProductService.GetProductById"}, {"code": "product.ProductService.GetUnitById"}, {"code": "product.ProductService.ListDistributionRegionProductByStoreId"}, {"code": "product.ProductService.ListPurchaseRegionProductByStoreId"}, {"code": "supply.supply.ResetDemandMain"}, {"code": "supply.supply.DeleteDemandProduct"}], "children": []}, {"permission": "supply.demand.md_create", "name": "新建主配订货单", "apis": [{"code": "supply.supply.CreateDemandMain"}, {"code": "oauth.HexOauth.GetUserById"}, {"code": "product.ProductService.GetUnitById"}, {"code": "product.ProductService.ListDistributionRegionProductByStoreId"}, {"code": "product.ProductService.ListPurchaseRegionProductByStoreId"}, {"code": "store.StoreService.GetStoreById"}, {"code": "supply.supply.GetDemandProductDetail"}, {"code": "supply.supply.GetValidProductByStoreId"}, {"code": "product.ProductService.GetProductById"}, {"code": "product.ProductService.ListProduct"}], "children": []}, {"permission": "supply.demand.hd_create", "name": "新建紧急订货单", "apis": [{"code": "supply.supply.CreateDemandMain"}, {"code": "oauth.HexOauth.GetUserById"}, {"code": "product.ProductService.GetUnitById"}, {"code": "product.ProductService.ListDistributionRegionProductByStoreId"}, {"code": "product.ProductService.ListPurchaseRegionProductByStoreId"}, {"code": "store.StoreService.GetStoreById"}, {"code": "supply.supply.GetDemandProductDetail"}, {"code": "supply.supply.GetValidProductByStoreId"}, {"code": "product.ProductService.GetProductById"}, {"code": "product.ProductService.ListProduct"}], "children": []}, {"permission": "supply.demand.delete", "name": "删除订货单(紧急、主配)", "apis": [{"code": "supply.supply.DeleteDemandMain"}], "children": []}, {"permission": "supply.demand.export", "name": "导出订货单明细", "apis": [{"code": "supply.supply.ExportDemandMain"}], "children": []}, {"permission": "supply.demand.status", "name": "订货单状态", "children": [{"permission": "supply.demand.status.f_commit", "name": "成品提交(门市订货单)", "apis": [{"code": "supply.supply.ChangeDemandStatus"}], "children": []}, {"permission": "supply.demand.status.commit", "name": "提交订货单", "apis": [{"code": "supply.supply.ChangeDemandStatus"}], "children": []}, {"permission": "supply.demand.status.reject", "name": "驳回订货单", "apis": [{"code": "supply.supply.ChangeDemandStatus"}], "children": []}, {"permission": "supply.demand.status.approve", "name": "审核订货单", "apis": [{"code": "supply.supply.ChangeDemandStatus"}], "children": []}]}]}, {"permission": "supply.order", "name": "要货单", "children": [{"permission": "supply.order.list", "name": "查询要货单", "apis": [{"code": "supply.supply.ListDemandOrder"}, {"code": "supply.supply.GetDemandOrderDetail"}, {"code": "supply.supply.GetDemandOrderProductDetail"}], "children": []}, {"permission": "supply.order.report", "name": "查询要货单报表", "apis": [{"code": "supply.supply.QueryBiOrderReport"}], "children": []}]}, {"permission": "supply.vacancy", "name": "缺货检核", "children": [{"permission": "supply.vacancy.create", "name": "创建缺货检核单", "apis": [{"code": "supply.supply.CreateVacancyOrder"}], "children": []}, {"permission": "supply.vacancy.list", "name": "查询缺货检核单", "apis": [{"code": "supply.supply.ListVacancy"}, {"code": "supply.supply.GetVacancyDetail"}, {"code": "supply.supply.GetVacancyProduct"}, {"code": "supply.supply.GetVacancyOrderProduct"}, {"code": "supply.supply.UpdateVacancyRemark"}], "children": []}, {"permission": "supply.vacancy.product_update", "name": "修改缺货商品配送数量", "apis": [{"code": "supply.supply.UpdateVacancyQuantity"}], "children": []}, {"permission": "supply.vacancy.reckeck", "name": "重新检核", "apis": [{"code": "supply.supply.ReVacancyQuantity"}], "children": []}, {"permission": "supply.vacancy.execute", "name": "发送(执行)要货单", "apis": [{"code": "supply.supply.ExecuteVacancy"}], "children": []}, {"permission": "supply.vacancy.export", "name": "导出缺货信息", "apis": [{"code": "supply.supply.ExportVacancyProduct"}], "children": []}]}, {"permission": "supply.first_delivery", "name": "首配", "children": [{"permission": "supply.first_delivery.query", "name": "查询首配信息", "apis": [{"code": "supply.supply.ListFirstDelivery"}], "children": []}, {"permission": "supply.first_delivery.export", "name": "导出首配信息", "apis": [{"code": "supply.supply.ExportFirstDelivery"}], "children": []}]}]}]