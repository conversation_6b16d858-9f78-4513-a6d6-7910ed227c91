# 新建接口
#curl 'https://saas-boh-qa.hexcloud.cn/api/v2/supply/mobile/franchisee/returns' \
#  -H 'Content-Type: application/json' \
#  -H 'Authorization: Bearer QxBFmCapZqMxODQrbG9KmYD4jITfkHIli3ogigETOs0.tmkTd4p2Yg12fdJD_AWsay-lz-tt8kr8qV6kWp3lKt0' \
#  -H 'referer: https://5000000000353633.eco.dingtalkapps.com/index.html' \
#  -H 'accept-charset: utf-8' \
#  -H 'user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 12_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 ChannelId(0) LyraVM Nebula AliApp(DingTalk/6.0.20) DingTalkIDE/6.0.20 Ariver/1.0.6 AlipayIDE language/zh-CN ClientLang/zh_CN' \
#  --data-binary '{"return_by":"4679554862445232129","type":"BO","sub_type":"fs_store","return_reason":"包装破损","remark":"89","products":[{"product_code":"2022101701","product_id":"4679592391831715841","product_name":"麻辣香干","quantity":"1","return_to":"4681027869202186241","unit_id":"4491998805654274048","unit_name":"箱","attachments":["https://next-picture.oss-cn-shanghai.aliyuncs.com/saas/stage/apmlf0833b036bd71d8ef493514fa7d2c21e-1668678051503.jpg","https://next-picture.oss-cn-shanghai.aliyuncs.com/saas/stage/apml238c1df4b6f5d65e486880e2a06acfb5-1668678080256.jpg","https://next-picture.oss-cn-shanghai.aliyuncs.com/saas/stage/apml94407cbee846634acc65ad139ab51302-1668678080253.jpg","https://next-picture.oss-cn-shanghai.aliyuncs.com/saas/stage/apml50d5ce5d90206967c76d64f27d6c011f-1668678080280.jpg","https://next-picture.oss-cn-shanghai.aliyuncs.com/saas/stage/apml6531655b7f0b2ba8553fae5082ca6dc0-1668678080319.jpg"],"logistics_type":"NMD"},{"product_code":"2022101706","product_id":"4685002247061372929","product_name":"麻辣鸭头","quantity":"1","return_to":"4681027869202186241","unit_id":"4491998805654274048","unit_name":"箱","logistics_type":"NMD"},{"product_code":"2022101707","product_id":"4685064577098973185","product_name":"商品名称很长很长很长的带特殊符号☀※卐☊→ 的名字，特别","quantity":"2","return_to":"4681027869202186241","unit_id":"4491998805654274048","unit_name":"箱","attachments":["https://next-picture.oss-cn-shanghai.aliyuncs.com/saas/stage/apml8ee5be82d0ae2e0d86ff5d1c5f469696-1668678040978.png"],"logistics_type":"NMD"}],"is_direct":false,"return_delivery_date":"2022-11-18T00:00:00Z","attachments":["https://next-picture.oss-cn-shanghai.aliyuncs.com/saas/stage/apml93b1440dd9c68de01c1e486580273a0b-1668678088342.png"],"logistics_type":"NMD","request_id":16686780977164956,"source_id":"4688346170519158785","source_code":"SH12211100001","lan":"zh-CN"}' \
# 列表接口
#curl --request GET 'https://saas-boh-qa.hexcloud.cn/api/v2/supply/mobile/franchisee/returns' \
#  -H 'Content-Type: application/json' \
#  -H 'Authorization: Bearer _6UQD_SC8xp-UaNXPvHIREPZuwPg-H-876haAnia83E.VVtA05Od6MGfW4a1Gjvel3qCy_Ej49LzWJrVDc2P1pQ' \
#  -H 'referer: https://5000000000353633.eco.dingtalkapps.com/index.html' \
#  -H 'accept-charset: utf-8' \
#  -H 'user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 12_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 ChannelId(0) LyraVM Nebula AliApp(DingTalk/6.0.20) DingTalkIDE/6.0.20 Ariver/1.0.6 AlipayIDE language/zh-CN ClientLang/zh_CN' \
#  --data-binary '{"store_ids":["4679554862445232129"],"type":"BO","sub_type":"fs_store","logistics_type":"NMD","request_id":16686625735644660,"source_id":"4690502807555715073","source_code":"SH12211160004","lan":"zh-CN"}'

# 收货单确认
#curl 'https://saas-boh-qa.hexcloud.cn/api/v2/receipt/franchisee/mobile/receiving/4690485039989043201/CONFIRM' \
#  -H 'Content-Type: application/json' \
#  -H 'Authorization: Bearer R72z8np3JBgnJOgsD4xsegBG2L_2EjsvFmDHDzxQFDE.81v34wddMHap3aUNsGv0UQi_lA-x8bstgW7Q96-AJlQ' \
#  -H 'referer: https://5000000000353633.eco.dingtalkapps.com/index.html' \
#  -H 'accept-charset: utf-8' \
#  -H 'user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 12_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 ChannelId(0) LyraVM Nebula AlipayDefined(nt:WIFI,ws:360|0|2.0) AliApp(DingTalk/6.5.0) DingTalkIDE/6.5.0 Ariver/1.0.6 AlipayIDE language/zh-CN' \
#  --data-binary '{"id":"4690485039989043201","action":"CONFIRM","deal_products":[{"id":"4690485040077123585","product_id":"4690416505177833472","receive_quantity":0.01},{"id":"4690485040077123586","product_id":"4690417120222183424","receive_quantity":0.33},{"id":"4690485040081317889","product_id":"4690417231488679936","receive_quantity":0.4}],"lan":"zh-CN"}' \
#  --compressed

# 退货单明细
#curl --request GET 'https://saas-boh-qa.hexcloud.cn/api/v2/supply/mobile/franchisee/returns/4688696157773860865' \
#  -H 'Content-Type: application/json' \
#  -H 'Authorization: Bearer _6UQD_SC8xp-UaNXPvHIREPZuwPg-H-876haAnia83E.VVtA05Od6MGfW4a1Gjvel3qCy_Ej49LzWJrVDc2P1pQ' \
#  -H 'referer: https://5000000000353633.eco.dingtalkapps.com/index.html' \
#  -H 'accept-charset: utf-8' \
#  -H 'user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 12_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 ChannelId(0) LyraVM Nebula AliApp(DingTalk/6.0.20) DingTalkIDE/6.0.20 Ariver/1.0.6 AlipayIDE language/zh-CN ClientLang/zh_CN'

# 获取可退货商品
#curl 'https://saas-boh-qa.hexcloud.cn/api/v2/supply/mobile/franchisee/return/product?store_id=4679554862445232129&lan=zh-CN' \
#  -H 'Content-Type: application/json' \
#  -H 'Authorization: Bearer UfZg1K69uNRffzF3qbrEI7dBkc2VZdjjWX8vsl6JGGM.RjdektokkuTcgtOl-HP-2JaiNpQcTXZMNduYauSblH4' \
#  -H 'referer: https://5000000000353633.eco.dingtalkapps.com/index.html' \
#  -H 'accept-charset: utf-8' \
#  -H 'user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 12_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 ChannelId(0) LyraVM Nebula AliApp(DingTalk/6.0.20) DingTalkIDE/6.0.20 Ariver/1.0.6 AlipayIDE language/zh-CN ClientLang/zh_CN' \
#  --compressed

#curl 'https://saas-boh-qa.hexcloud.cn/api/v2/supply/mobile/franchisee/returns?received_bys=4679554862445232129&code=1392211180001&limit=-1&lan=zh-CN' \
#  -H 'Content-Type: application/json' \
#  -H 'Authorization: Bearer 2V51Ml3BQoXiFkydYb4dl1Bc8BLcuOXsNhPLsNPQysw.KXvnFZEHkZHKKOCcU4-bmcLK6SWSSRu9MpfSHQ77_NY' \
#  -H 'referer: https://5000000000353633.eco.dingtalkapps.com/index.html' \
#  -H 'accept-charset: utf-8' \
#  -H 'user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 12_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 ChannelId(0) LyraVM Nebula AliApp(DingTalk/6.0.20) DingTalkIDE/6.0.20 Ariver/1.0.6 AlipayIDE language/zh-CN ClientLang/zh_CN' \


#curl 'https://saas-boh-qa.hexcloud.cn/api/v2/supply/mobile/receive-diff/4692356687623946240/product?lan=zh-CN' \
#  -H 'Content-Type: application/json' \
#  -H 'Authorization: Bearer G2jG6RGi13xnU2dzSY0-jExvF9eW9kihe-05XwoP7l8.7nzARmoGbYPO_ImnUaAdsUIEqxEke-hRjzmMtpz0egU' \
#  -H 'referer: https://5000000000353633.eco.dingtalkapps.com/index.html' \
#  -H 'accept-charset: utf-8' \
#  -H 'user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 12_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 ChannelId(0) LyraVM Nebula AlipayDefined(nt:WIFI,ws:360|0|3.0) AliApp(DingTalk/6.5.0) DingTalkIDE/6.5.0 Ariver/1.0.6 AlipayIDE language/zh-CN' \
#  --compressed

#curl 'https://saas-boh-qa.hexcloud.cn/api/v2/supply/frs_store_extra/returns' \
#  -H 'Content-Type: application/json' \
#  -H 'Authorization: Bearer G2jG6RGi13xnU2dzSY0-jExvF9eW9kihe-05XwoP7l8.7nzARmoGbYPO_ImnUaAdsUIEqxEke-hRjzmMtpz0egU' \
#  -H 'referer: https://5000000000353633.eco.dingtalkapps.com/index.html' \
#  -H 'accept-charset: utf-8' \
#  -H 'user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 12_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 ChannelId(0) LyraVM Nebula AlipayDefined(nt:WIFI,ws:360|0|3.0) AliApp(DingTalk/6.5.0) DingTalkIDE/6.5.0 Ariver/1.0.6 AlipayIDE language/zh-CN' \

#curl 'https://saas-boh-qa.hexcloud.cn/api/v2/receipt/store/receive/no_diff?start_date=2022-10-31T16:00:00Z&end_date=2022-11-30T15:59:59Z&store_id=4499438648374099968&lan=zh-CN' \
#  -H 'Content-Type: application/json' \
#  -H 'Authorization: Bearer YM9UMQEhnSI0LENtm7T1IhQWZvg7S6KXjKZJSoA9TrU.2jfsA8e920IN2lwrtaxnyiTfn-ATztmdwiGxXlxmyvk' \
#  -H 'referer: https://5000000000353633.eco.dingtalkapps.com/index.html' \
#  -H 'accept-charset: utf-8' \
#  -H 'user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 12_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 ChannelId(0) LyraVM Nebula AlipayDefined(nt:WIFI,ws:360|0|3.0) AliApp(DingTalk/6.5.0) DingTalkIDE/6.5.0 Ariver/1.0.6 AlipayIDE language/zh-CN' \
#  --compressed

# 新建接口
#curl 'https://boh-test.hexcloud.cn/api/v2/supply/mobile/franchisee/returns' \
#  -H 'Content-Type: application/json' \
#  -H 'Authorization: Bearer 63_Yb_jvyMgyHxXiwV6uoc6rOU9U9aAy1SrwXS99Vhk.uuKMwX6WXgXh8WCSxtGiqsKincOS_f4nyVO8X1loFeI' \
#  -H 'referer: https://5000000000353633.eco.dingtalkapps.com/index.html' \
#  -H 'accept-charset: utf-8' \
#  -H 'user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 12_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 ChannelId(0) LyraVM Nebula AliApp(DingTalk/6.0.20) DingTalkIDE/6.0.20 Ariver/1.0.6 AlipayIDE language/zh-CN ClientLang/zh_CN' \
#  --data-binary '{"return_by":"4524867362492383232","type":"BO","sub_type":"fs_store","return_reason":"包装破损","remark":"89","products":[{"product_code":"2022101701","product_id":"4679592391831715841","product_name":"麻辣香干","quantity":"1","return_to":"4681027869202186241","unit_id":"4491998805654274048","unit_name":"箱","attachments":["https://next-picture.oss-cn-shanghai.aliyuncs.com/saas/stage/apmlf0833b036bd71d8ef493514fa7d2c21e-1668678051503.jpg","https://next-picture.oss-cn-shanghai.aliyuncs.com/saas/stage/apml238c1df4b6f5d65e486880e2a06acfb5-1668678080256.jpg","https://next-picture.oss-cn-shanghai.aliyuncs.com/saas/stage/apml94407cbee846634acc65ad139ab51302-1668678080253.jpg","https://next-picture.oss-cn-shanghai.aliyuncs.com/saas/stage/apml50d5ce5d90206967c76d64f27d6c011f-1668678080280.jpg","https://next-picture.oss-cn-shanghai.aliyuncs.com/saas/stage/apml6531655b7f0b2ba8553fae5082ca6dc0-1668678080319.jpg"],"logistics_type":"NMD"},{"product_code":"2022101706","product_id":"4685002247061372929","product_name":"麻辣鸭头","quantity":"1","return_to":"4681027869202186241","unit_id":"4491998805654274048","unit_name":"箱","logistics_type":"NMD"},{"product_code":"2022101707","product_id":"4685064577098973185","product_name":"商品名称很长很长很长的带特殊符号☀※卐☊→ 的名字，特别","quantity":"2","return_to":"4681027869202186241","unit_id":"4491998805654274048","unit_name":"箱","attachments":["https://next-picture.oss-cn-shanghai.aliyuncs.com/saas/stage/apml8ee5be82d0ae2e0d86ff5d1c5f469696-1668678040978.png"],"logistics_type":"NMD"}],"is_direct":false,"return_delivery_date":"2022-11-18T00:00:00Z","attachments":["https://next-picture.oss-cn-shanghai.aliyuncs.com/saas/stage/apml93b1440dd9c68de01c1e486580273a0b-1668678088342.png"],"logistics_type":"NMD","request_id":16686780977164956,"source_id":"4688346170519158785","source_code":"SH12211100001","lan":"zh-CN"}'

#curl 'http://saas-boh-qa.hexcloud.cn/api/v2/supply/frs_store_extra/returns?stringified=true&include_total=true&sub_type=fs_store&limit=20&offset=0&delivery_type=WAREHOUSE&return_date_from=2022-10-26T16:00:00.000Z&return_date_to=2022-11-25T15:59:59.000Z&delivery_date_from=2022-10-26T16:00:00.000Z&delivery_date_to=2022-11-25T15:59:59.000Z&status=SUBMITTED&status=REJECTED&status=DELETED&status=APPROVED&status=DELIVERED&status=CONFIRMED&type=BO&lan=zh-CN' \
#  -H 'Accept: */*' \
#  -H 'Accept-Language: zh-CN,zh;q=0.9' \
#  -H 'Cache-Control: max-age=0' \
#  -H 'Connection: keep-alive' \
#  -H 'Cookie: AUTH_TOKEN_DEVELOPMENT=bearer%20eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************.LZttysR1k5xc95O5cPPvp6-S2pLffJsZNyL_t6HVui4; hws_cross_token_dev=XvaGnw-LMzEJhBKLZL55PPlH0vSGI9PNbVPrzdgOFMY.U-1HLIra7yHzOwnPFgNe-0y5BM3eN-dwuMXzMzTKV4A; menuCollapsed=0; hws_cross_token_qa=aFcXhQ7IW4ahtu6Gtu6MYWxRZLtpBb-Ak_fyPO5MuXo.l_fSUNla1YVssBzqu9MpLKfKGgkHDIkgtHQcrYzUyhY' \
#  -H 'Sec-Fetch-Dest: empty' \
#  -H 'Sec-Fetch-Mode: cors' \
#  -H 'Sec-Fetch-Site: same-origin' \
#  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
#  -H 'authorization: Bearer aFcXhQ7IW4ahtu6Gtu6MYWxRZLtpBb-Ak_fyPO5MuXo.l_fSUNla1YVssBzqu9MpLKfKGgkHDIkgtHQcrYzUyhY' \
#  -H 'content-type: application/json' \
#  -H 'sec-ch-ua: "Chromium";v="106", "Google Chrome";v="106", "Not;A=Brand";v="99"' \
#  -H 'sec-ch-ua-mobile: ?0' \
#  -H 'sec-ch-ua-platform: "macOS"' \
#  --compressed

#curl 'https://saas-boh-qa.hexcloud.cn/api/v2/supply/mobile/receive-diff/4693762834281070592/submit' \
#  -X 'POST' \
#  -H 'Content-Type: application/json' \
#  -H 'Authorization: Bearer 1TT4dEOIrk7rs5H-0zdKJROLiPgGdW6lRr_njgUW5HA.2jTBfLtN6lxwlBYLlQ6DsRkJhUuHxix503WZWSYqs4Y' \
#  -H 'referer: https://5000000000353633.eco.dingtalkapps.com/index.html' \
#  -H 'accept-charset: utf-8' \
#  -H 'user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 12_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 ChannelId(0) LyraVM Nebula AlipayDefined(nt:WIFI,ws:360|0|3.0) AliApp(DingTalk/6.5.0) DingTalkIDE/6.5.0 Ariver/1.0.6 AlipayIDE language/zh-CN' \
#  --compressed

#curl -H 'Content-Type: application/json' -H 'Authorization: Bearer Brp9eHmPhiTBPoI9DhAejLhA7RjOT1mMQ-e6bvsVpVI.9Ve5va_QbloJwx5lAe223hE9LXf4z12SjnOLC8dIjLg' -H 'referer: https://5000000000353633.eco.dingtalkapps.com/index.html' -H 'Accept-Charset: UTF-8' -H 'Host: saas-boh-qa.hexcloud.cn' -H 'User-Agent: Mozilla/5.0 (Linux; U; Android 10; zh-CN; HMA-AL00 Build/HUAWEIHMA-AL00) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/69.0.3497.100 UWS/********** Mobile Safari/537.36 UCBS/**********_220801135555 ChannelId(5) NebulaSDK/1.8.100112 Nebula AliApp(DingTalk/6.5.55) com.alibaba.android.rimet/27080848 Channel/227200 language/zh-CN abi/64 Hmos/1 colorScheme/light NebulaX/1.0.0 Ariver/1.0.0' --data-binary '{"return_by":"4679554862445232129","type":"BO","sub_type":"fs_store","return_reason":"鍗冲皢杩囨湡","remark":"","products":[{"product_code":"2022101701","product_id":"4679592391831715841","product_name":"楹昏荆棣欏共","quantity":"0.1","return_to":"4681027869202186241","unit_id":"4491998805654274048","unit_name":"绠�","logistics_type":"NMD"}],"is_direct":false,"return_delivery_date":"2022-11-28T00:00:00Z","attachments":["https://next-picture.oss-cn-shanghai.aliyuncs.com/saas/stage/b2df4b5c74f9b4e6cc58cfb60e2b144-1669620080637.image"],"logistics_type":"NMD","request_id":16696200826482632,"source_id":"4694793636613427201","source_code":"SH12211280002","lan":"zh-CN"}' 'https://saas-boh-qa.hexcloud.cn/api/v2/supply/mobile/franchisee/returns'

#curl 'https://saas-boh-qa.hexcloud.cn/api/v2/supply/mobile/franchisee/return/product?store_id=4685065760928694273&lan=zh-CN' \
#  -H 'Content-Type: application/json' \
#  -H 'Authorization: Bearer KA3Y5EhSMfFoQYIMuEJGlo1UmqN8ed6oilWxk7Uz_3c.fOkxDu-xcvK_oqFLpr_HIc7_T1UdMUXaI4qfLtIL2jE' \
#  -H 'referer: https://5000000000353633.eco.dingtalkapps.com/index.html' \
#  -H 'accept-charset: utf-8' \
#  -H 'user-agent: Mozilla/5.0 (iPhone; CPU iPhone OS 12_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 ChannelId(0) LyraVM Nebula AlipayDefined(nt:WIFI,ws:360|0|3.0) AliApp(DingTalk/6.5.0) DingTalkIDE/6.5.0 Ariver/1.0.6 AlipayIDE language/zh-CN' \
#  --compressed

#curl 'https://saas-boh-qa.hexcloud.cn/api/v2/supply/mobile/receive-diff?receive_date_from=2022-11-30T16:00:00Z&receive_date_to=2022-12-01T15:59:59Z&store_ids=4483201431196696576&lan=zh-CN' \
#  -H 'Connection: keep-alive' \
#  -H 'Authorization: Bearer cQeTE0EateFKv_SpHnck8cNPWMD61QiOZp-SYaJUVeI.-Ls4m0eXhhXWgKdQ8MEQbEkov8U8I-8kXukvYoenxXc' \
#  -H 'User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1 wechatdevtools/1.05.2204250 MicroMessenger/8.0.5 Language/zh_CN webview/' \
#  -H 'content-type: application/json' \
#  -H 'Accept: */*' \
#  -H 'Sec-Fetch-Site: cross-site' \
#  -H 'Sec-Fetch-Mode: cors' \
#  -H 'Sec-Fetch-Dest: empty' \
#  -H 'Referer: https://servicewechat.com/wxb8b8cfbc10447bfb/devtools/page-frame.html' \
#  --compressed

#curl 'https://saas-boh-qa.hexcloud.cn/api/v2/supply/frs_store_extra/receive-diff?stringified=true&include_total=true&sub_type=fs_store&limit=20&offset=0&delivery_type=WAREHOUSE&status=SUBMITTED&status=CONFIRMED&status=REJECTED&delivery_bys=4695522004718092288&lan=zh-CN' \
#  -H 'Accept: */*' \
#  -H 'Accept-Language: zh-CN,zh;q=0.9' \
#  -H 'Cache-Control: max-age=0' \
#  -H 'Connection: keep-alive' \
#  -H 'Cookie: AUTH_TOKEN_DEVELOPMENT=bearer%20eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************.9Wcnc_JMIqqLvs9OgAxumo_Afl4Ha1XGzPUR0vhMSnI; menuCollapsed=0; hws_cross_token_dev=6c2u7G4EeZMHkH7jwqT5PSJ_SQdfoTvFfpuueu3xn1I.GMJNQxaTla94sJIjq5JoM6aVn17YQKjWi096nJg2tdY; hws_cross_token_qa=1uaYhyiGpkCQBE1Z-ubqvb2KOOT6SyLIyDmCaQJDPEU.xnTqQs4ivAcYVfJBbCJDO6W8o5S1y4K_PJqrm-79uw8' \
#  -H 'Sec-Fetch-Dest: empty' \
#  -H 'Sec-Fetch-Mode: cors' \
#  -H 'Sec-Fetch-Site: same-origin' \
#  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
#  -H 'authorization: Bearer 1uaYhyiGpkCQBE1Z-ubqvb2KOOT6SyLIyDmCaQJDPEU.xnTqQs4ivAcYVfJBbCJDO6W8o5S1y4K_PJqrm-79uw8' \
#  -H 'content-type: application/json' \
#  -H 'sec-ch-ua: "Chromium";v="106", "Google Chrome";v="106", "Not;A=Brand";v="99"' \
#  -H 'sec-ch-ua-mobile: ?0' \
#  -H 'sec-ch-ua-platform: "macOS"' \
#  --compressed

  curl 'https://saas-boh-qa.hexcloud.cn/api/v2/receipt/franchisee/mobile/receiving/4697419925367336961/product?lan=zh-CN' \
  -H 'Accept: */*' \
  -H 'Accept-Language: zh-CN,zh;q=0.9' \
  -H 'Cache-Control: max-age=0' \
  -H 'Connection: keep-alive' \
  -H 'Cookie: AUTH_TOKEN_DEVELOPMENT=bearer%20eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************.9Wcnc_JMIqqLvs9OgAxumo_Afl4Ha1XGzPUR0vhMSnI; menuCollapsed=0; hws_cross_token_dev=6c2u7G4EeZMHkH7jwqT5PSJ_SQdfoTvFfpuueu3xn1I.GMJNQxaTla94sJIjq5JoM6aVn17YQKjWi096nJg2tdY; hws_cross_token_qa=1uaYhyiGpkCQBE1Z-ubqvb2KOOT6SyLIyDmCaQJDPEU.xnTqQs4ivAcYVfJBbCJDO6W8o5S1y4K_PJqrm-79uw8' \
  -H 'Sec-Fetch-Dest: empty' \
  -H 'Sec-Fetch-Mode: cors' \
  -H 'Sec-Fetch-Site: same-origin' \
  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
  -H 'authorization: Bearer 1uaYhyiGpkCQBE1Z-ubqvb2KOOT6SyLIyDmCaQJDPEU.xnTqQs4ivAcYVfJBbCJDO6W8o5S1y4K_PJqrm-79uw8' \
  -H 'content-type: application/json' \
  -H 'sec-ch-ua: "Chromium";v="106", "Google Chrome";v="106", "Not;A=Brand";v="99"' \
  -H 'sec-ch-ua-mobile: ?0' \
  -H 'sec-ch-ua-platform: "macOS"' \
  --compressed