aniso8601==4.1.0
appnope==0.1.0
backcall==0.1.0
certifi==2018.11.29
chardet==3.0.4
Click==7.0
decorator==4.3.2
fakeredis==1.0
Flask==1.0.2
Flask-Cors==3.0.7
flask-restplus==0.12.1
gevent==1.4.0
packaging==21.3
google-api-core==1.31.5
google-api-python-client==2.0.2
google-auth==1.35.0
google-auth-httplib2==0.1.0
googleapis-common-protos==1.56.1
greenlet==0.4.15
grpcio==1.17.1
grpcio-reflection==1.17.1
grpcio-tools==1.17.1
hex-exception==0.0.1
idna==2.8
ipython==7.3.0
ipython-genutils==0.2.0
itsdangerous==1.1.0
jedi==0.13.3
Jinja2==2.10
jsonschema==2.6.0
MarkupSafe==1.1.0
mysqlclient==1.4.2
numpy==1.16.2
pandas==1.1.5
parso==0.3.4
pexpect==4.6.0
pickleshare==0.7.5
prompt-toolkit==2.0.9
protobuf==3.15.6
psycopg2-binary==2.7.7
ptyprocess==0.6.0
Pygments==2.3.1
pynsq==0.8.3
python-dateutil==2.8.0
pytz==2018.9
PyYAML==3.13
raven==6.10.0
redis==3.1.0
requests==2.21.0
six==1.15.0
sortedcontainers==2.1.0
SQLAlchemy==1.3.24
tornado==4.5.3
traitlets==4.3.2
urllib3==1.24.1
wcwidth==0.1.7
Werkzeug==0.14.1
jaeger-client==3.13.0
opentracing==1.3.0
python-grpc-prometheus==0.2.0
grpcio-opentracing==1.1.3
hex-grpc-healthcheck==0.0.1
pyexcel==0.6.6
pyexcel-io==0.6.4
pyexcel-xls==0.6.2
pyexcel-xlsx==0.6.0
pymysql==1.0.2
xmq-python==0.0.28
auth-python==0.0.16
retrying==1.3.4
