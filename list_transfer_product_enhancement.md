# list_transfer_product 函数完善说明

## 功能概述

完善了 `supply/module/transfer_service.py` 中的 `list_transfer_product` 函数，新增了获取商品当前库存并转换为调拨单位数量的功能。

## 主要改进

### 1. 获取商品当前库存
- 通过 `inventory_service.get_products_inventory_by_branch_id()` 获取调出门店的商品实时库存
- 库存数据以商品ID为键的字典格式返回

### 2. 单位转换功能
- 获取商品主档信息，包含单位转换率
- 将核算单位的库存数量转换为调拨单位的数量
- 转换公式：`调拨单位数量 = 核算单位库存数量 / 单位转换率`

### 3. 数据格式处理
- 正确处理从 repository 返回的字典格式数据
- 兼容 `include_total` 参数的不同返回格式
- 统一处理商品ID和单位ID的获取方式

## 新增返回字段

函数在原有调拨商品信息基础上，新增了以下字段：

1. **current_inventory_quantity**: 当前库存数量（核算单位）
2. **transfer_unit_inventory_quantity**: 调拨单位的当前库存数量
3. **unit_name**: 调拨单位名称
4. **unit_code**: 调拨单位编码

## 函数签名

```python
def list_transfer_product(self, partner_id=None, user_id=None, limit=None, offset=None, 
                         include_total=False, product_ids=None, order=None, transfer_id=None):
    """获取调拨单商品列表，包含当前库存并转换为调拨单位数量
    
    :param partner_id: 合作伙伴ID
    :param user_id: 用户ID
    :param limit: 分页限制
    :param offset: 分页偏移
    :param include_total: 是否包含总数
    :param product_ids: 商品ID列表
    :param order: 排序
    :param transfer_id: 调拨单ID
    :return: 调拨商品列表，包含库存信息和单位转换
    """
```

## 实现逻辑

1. **获取调拨单商品列表**
   - 调用 repository 的 `list_transfer_product` 方法
   - 处理可能包含总数的返回格式

2. **获取调拨单信息**
   - 通过 `transfer_id` 获取调拨单主表信息
   - 提取调出门店ID (`shipping_store`)

3. **收集商品ID**
   - 遍历调拨商品列表，提取所有商品ID
   - 兼容字典和对象两种数据格式

4. **获取库存信息**
   - 调用库存服务获取调出门店的商品实时库存
   - 返回以商品ID为键的库存字典

5. **获取商品主档信息**
   - 调用元数据服务获取商品信息，包含单位转换率
   - 获取单位信息字典

6. **处理每个调拨商品**
   - 添加当前库存数量（核算单位）
   - 根据单位转换率计算调拨单位的库存数量
   - 添加单位名称和编码信息

## 错误修复

1. **解包错误修复**
   - 修复了当 `include_total=False` 时的值解包错误
   - 正确处理 repository 返回的不同格式

2. **属性访问错误修复**
   - 修复了字典对象没有 `product_id` 属性的错误
   - 统一使用字典键访问方式

## 使用示例

```python
# 获取调拨单商品列表（不包含总数）
products = transfer_service.list_transfer_product(
    partner_id=partner_id,
    user_id=user_id,
    transfer_id=transfer_id
)

# 获取调拨单商品列表（包含总数）
total, products = transfer_service.list_transfer_product(
    partner_id=partner_id,
    user_id=user_id,
    transfer_id=transfer_id,
    include_total=True
)

# 每个商品包含的新字段
for product in products:
    print(f"商品ID: {product['product_id']}")
    print(f"当前库存(核算单位): {product['current_inventory_quantity']}")
    print(f"当前库存(调拨单位): {product['transfer_unit_inventory_quantity']}")
    print(f"调拨单位: {product['unit_name']} ({product['unit_code']})")
```

## 依赖服务

- `inventory_service`: 获取商品库存信息
- `metadata_service`: 获取商品主档和单位信息
- `TransferRepository`: 获取调拨单和商品信息

## 注意事项

1. 库存数量转换时保留6位小数精度
2. 当单位转换率为0时，调拨单位库存数量设为0
3. 函数兼容原有的分页和排序功能
4. 保持向后兼容性，不影响现有调用方式
