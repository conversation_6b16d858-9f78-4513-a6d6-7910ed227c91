FROM python:3.6
COPY .. /app/supply
WORKDIR /app/supply
RUN pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/
ONBUILD RUN python -m grpc_tools.protoc -I/app/supply/supply/proto --python_out=/app/supply/supply/proto --grpc_python_out=/app/supply/supply/proto /app/supply/supply/proto/supply.proto
HEALTHCHECK --interval=10s --start-period=5s CMD python /app/supply/scripts/health_check.py
ENTRYPOINT ["python"]
CMD ["start.py"]