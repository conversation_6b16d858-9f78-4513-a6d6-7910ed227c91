# 服务部署需求

| 数据库导入脚本 |  依赖组件              | 依赖服务          | 服务端口             | 环境变量 | 配置文件 | 启动命令 | 健康检查  |  服务镜像 | 运行资源需求  |验证|网关设置|其他|
|-----------|--------|-------------------|---------------|------------------|------|------|------|---|---|---|---|---|
|  [链接](#mysql)  |    [链接](#component)    | [链接](#service) | [链接](#port) | [链接](#env) |   [链接](#config)   |     [链接](#command)|   [链接](#healthcheck)   | [链接](#version)  | [链接](#resource)  |[链接](#check) |[链接](#gateway)|链接|

> 如果没有则不加链接，表示没有对应内容


## <a name="db">数据库导入脚本</a>


* mysql
  * 数据结构脚本: [supply_schema.sql](db/supply_schema.sql)
  * 数据初始化脚本: [supply_data.sql](db/supply_data.sql)
  * 
   
   

> 全部提供脚本链接。不要用程序生成等其他方式。

## <a name="component">依赖组件</a>
* mysql
* rocketmq
    
    
    
## <a name="service">依赖服务</a>
* <a href="https://gitlab.hexcloud.cn/histore/product-ags/blob/saas-master/deployment/docs/product_ags_saas.md">product_ags
* <a href="https://gitlab.hexcloud.cn/histore/metadata/blob/tenant/deployment/deployment_tenant.md">metadata</a>
* <a href="https://gitlab.hexcloud.cn/histore/inventory/blob/feature/seesaw-mq/deployments/deployment_doc/inventory_saas.md">inventory</a>
* bom-venom
* <a href="https://gitlab.hexcloud.cn/histore/cost-engine/blob/master/deployment/deployment.md">costEngine</a>
* <a href="https://gitlab.hexcloud.cn/histore/report/blob/saas-master/deployment/deployment_doc/report_saas.md">report</a>
* <a href="https://gitlab.hexcloud.cn/histore/receipt/blob/saas-master/receipt/deployment/deployment_doc/receipt_saas.md">receipt</a>
* <a href="https://gitlab.hexcloud.cn/saas/hex-auth/blob/master/deployment/deployment.md">auth-permission</a>

> 需提供响应服务部署文档链接

## <a name="port">服务端口</a>
* 8686tcp02:8686   简明描述：TCP	 	ClusterIP	 	与容器端口相同

## <a name="env">环境变量</a>
* CONFIG_PATH: /etc/config/config.yaml

## <a name="config">配置文件</a>

***标为TODO的都要修改***

```

    app:
        name: supply
        host: 0.0.0.0
        port: 8686      # 启动端口
        max_worker: 5   # 启动进程数
        mode: grpc     # grpc or task
    
    logger:
        console:
            level: DEBUG
            format: "%(asctime)s %(levelname)-8s:%(name)s-%(message)s"
        sqlalchemy:
            engine:
                level: DEBUG
    
    data_center: 1  # for snowflake ID generation
    id_generator_addr:  #TODO - UUID GRPC地址比如uuid.infra:6171
    sentry:
        enabled: False
        dsn: ''
        environment: 'dev'
    
    mysql:
        user:  # TODO
        password: #TODO
        host:  #TODO
        port: #TODO
        database:  #TODO - supply数据库，saas_test_boh_supply
    
    #主档服务
    product_ags_host:  #TODO - product-ags服务地址
    product_ags_port:  #TODO - product-ags端口
    metadata_address:  #TODO -主档地址，如metadata:5012
    
    #库存服务配置
    inventory_host: #TODO - inventory库存地址
    inventory_port: #TODO - inventory库存端口 
    
    #物料服务配置
    bom_host: #TODO - bom地址
    bom_port: #TODO - bom端口50051
    
    # 成本服务配置
    cost_host:  #TODO - 成本服务服务地址
    cost_port:  #TODO - 成本服务服务端口
    cost_callback: 'supply:8686' #TODO - 成本服务服务回调地址
    
    prometheus:
        host: 0.0.0.0
        port: 8080
    
    tracing:
        enable: false # tracing需要关闭
    
    
    # report服务
    report_host:  #TODO - report grpc服务地址
    report_port:  #TODO - report服务端口，如8686
    
    # receipt服务
    receipt_host: #TODO - receipt grpc服务地址
    receipt_port: #TODO - receipt grpc服务端口8686 
    
    mq:
        rocketmq:
            lookup_address:  #TODO -  rocketmq地址，如http://xxxx.aliyuncs.com:8080
            consumer_group_name: GID_SUPPLY # 消息groupname，需要在阿里云提前创建
            access_key:  #TODO -  rocketmq access_key，需要在阿里云提前获取
            access_secret: #TODO -  rocketmq access_secret, 需要在阿里云提前获取
            channel: ALIYUN # 固定值，不用变
            thread_num: 2 # 线程数
            batch_size: 2 # 每次处理消息数量
    
    auth_address:  #TODO -  对应infra集群里面的permission服务地址，如auth-permission.infra-dev:6500
    ianvs_address: #TODO - 用户服务地址，如ianvs.infra-dev:50051
```



>配置文件依赖的基础组件，内外部服务需要有下划线作为特殊标记. 
>需要有配置项的说明列表

## <a name="command">启动命令</a>
* Command: 
* Entrypoint: /bin/sh -c 'python start.py grpc'

> 分别对应rancher界面上的 Entrypoint和Command

## <a name="healthcheck">健康检查</a>



> 给出这个服务正常运行的标识：可是是一个urI，可以是某个端口


## <a name="version">服务镜像</a>
registry.hexcloud.cn/histore/supply/saas-master:afeeb0fa
## <a name="resource">运行资源需求</a>
* cpu
    request:  10 milli CPUs      limit: 128 milli CPUs
* memory
    request:  50 MiB      limit: 128 MiB

## <a name="check">验证</a>

> 如何验证部署成功，如：服务启动，检查某uri返回特定内容等

## <a name="others">网关设置</a>  

  > ingress 配置设置等

## <a name="others">其他</a>

> 以上内容均需要区分不同环境，至少要有prd，qa



