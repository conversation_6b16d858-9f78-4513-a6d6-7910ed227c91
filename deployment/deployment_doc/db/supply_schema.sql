/*
 Navicat Premium Data Transfer

 Source Server         : saas-mysql
 Source Server Type    : MySQL
 Source Server Version : 50728
 Source Host           : rm-uf6xuw7c9aqu09c26so.mysql.rds.aliyuncs.com:3306
 Source Schema         : saas_test_boh_supply

 Target Server Type    : MySQL
 Target Server Version : 50728
 File Encoding         : 65001

 Date: 27/05/2021 15:43:10
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for doc_batch_plan
-- ----------------------------
-- DROP TABLE IF EXISTS `doc_batch_plan`;
CREATE TABLE `doc_batch_plan` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `batch_id` bigint(20) DEFAULT NULL,
  `plan_id` bigint(20) DEFAULT NULL,
  `plan_date` datetime DEFAULT NULL,
  `plan_type` varchar(10) DEFAULT NULL,
  `process_status` varchar(10) DEFAULT NULL,
  `reason` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `method` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `_batch_plan_uc` (`plan_id`,`plan_date`),
  KEY `doc_batch_plan_partner_id` (`partner_id`),
  KEY `doc_batch_plan_plan_type` (`plan_type`),
  KEY `doc_batch_plan_process_status` (`process_status`),
  KEY `doc_batch_plan_plan_id` (`batch_id`,`plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for doc_batch_plan_branch
-- ----------------------------
-- DROP TABLE IF EXISTS `doc_batch_plan_branch`;
CREATE TABLE `doc_batch_plan_branch` (
  `id` bigint(20) NOT NULL,
  `plan_batch_id` bigint(20) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `batch_id` bigint(20) DEFAULT NULL,
  `branch_id` bigint(20) DEFAULT NULL,
  `plan_id` bigint(20) DEFAULT NULL,
  `plan_date` datetime DEFAULT NULL,
  `plan_type` varchar(10) DEFAULT NULL,
  `process_status` varchar(10) DEFAULT NULL,
  `reason` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `method` varchar(10) DEFAULT NULL,
  `retry_count` bigint(20) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `_batch_plan_branch_date` (`plan_id`,`plan_date`,`branch_id`),
  KEY `doc_batch_plan_branch_partner_id` (`partner_id`),
  KEY `doc_batch_plan_branch_plan_type` (`plan_type`),
  KEY `doc_batch_plan_branch_status` (`process_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for doc_code
-- ----------------------------
-- DROP TABLE IF EXISTS `doc_code`;
CREATE TABLE `doc_code` (
  `id` bigint(20) NOT NULL,
  `value` bigint(20) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `doc_code_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- Table structure for doc_plan_category_by_store
-- ----------------------------
-- DROP TABLE IF EXISTS `doc_plan_category_by_store`;
create table doc_plan_category_by_store
(
	id bigint not null
		primary key,
	partner_id bigint null,
	modify_id bigint null,
	plan_id bigint null,
	log_id bigint null,
	store_id bigint null,
	category_id bigint null,
	category_code varchar(50) null,
	category_name varchar(50) null,
	deleted tinyint(1) null,
	created_at datetime null,
	updated_at datetime null,
	created_by bigint null,
	updated_by bigint null,
	user_id bigint null,
	created_name varchar(50) null,
	updated_name varchar(50) null
);

create index doc_plan_category_by_store_log_id
	on doc_plan_category_by_store (log_id);

create index doc_plan_category_by_store_modify_id
	on doc_plan_category_by_store (modify_id);

create index doc_plan_category_by_store_partner_id
	on doc_plan_category_by_store (partner_id);

create index doc_plan_category_by_store_plan_id
	on doc_plan_category_by_store (plan_id);

create index doc_plan_category_by_store_product
	on doc_plan_category_by_store (category_id);

-- ----------------------------
-- Table structure for doc_plan
-- ----------------------------
-- DROP TABLE IF EXISTS `doc_plan`;
CREATE TABLE `doc_plan` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `code` varchar(50) DEFAULT NULL,
  `name` varchar(50) DEFAULT NULL,
  `plan_type` varchar(10) DEFAULT NULL,
  `product_method` varchar(10) DEFAULT NULL,
  `branch_method` varchar(10) DEFAULT NULL,
  `method` varchar(10) DEFAULT NULL,
  `month_method` varchar(100) DEFAULT NULL,
  `sub_month_method` varchar(100) DEFAULT NULL,
  `week_method` varchar(100) DEFAULT NULL,
  `sub_week_method` varchar(100) DEFAULT NULL,
  `day_method` varchar(100) DEFAULT NULL,
  `sub_day_method` varchar(100) DEFAULT NULL,
  `interval` int(11) DEFAULT NULL,
  `status` varchar(10) DEFAULT NULL,
  `start` datetime DEFAULT NULL,
  `end` datetime DEFAULT NULL,
  `before` int(11) DEFAULT NULL,
  `start_before` datetime DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `calculate_inventory` tinyint(1) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `request_id` bigint(20) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `doc_deadline` datetime DEFAULT NULL,
  `doc_cancel` datetime DEFAULT NULL,
  `last_time` datetime DEFAULT NULL,
  `next_time` datetime DEFAULT NULL,
  `is_recreate` tinyint(1) DEFAULT '0',
  `sub_type` varchar(50) DEFAULT NULL,
  `extend` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `doc_plan_request_id` (`request_id`),
  KEY `doc_plan_is_last_end` (`end`),
  KEY `doc_plan_is_last_start_before` (`start_before`),
  KEY `doc_plan_is_last_time` (`last_time`),
  KEY `doc_plan_is_recreate` (`is_recreate`),
  KEY `doc_plan_method` (`method`),
  KEY `doc_plan_partner_id` (`partner_id`),
  KEY `doc_plan_status` (`status`),
  KEY `doc_plan_type` (`plan_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for doc_plan_branch
-- ----------------------------
-- DROP TABLE IF EXISTS `doc_plan_branch`;
CREATE TABLE `doc_plan_branch` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `plan_id` bigint(20) DEFAULT NULL,
  `branch_id` bigint(20) DEFAULT NULL,
  `branch_type` varchar(50) DEFAULT NULL,
  `branch_name` varchar(50) DEFAULT NULL,
  `branch_code` varchar(50) DEFAULT NULL,
  `deleted` tinyint(1) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `doc_plan_branch_branch` (`branch_id`),
  KEY `doc_plan_branch_partner_id` (`partner_id`),
  KEY `doc_plan_branch_plan_id` (`plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for doc_plan_category
-- ----------------------------
-- DROP TABLE IF EXISTS `doc_plan_category`;
CREATE TABLE `doc_plan_category` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `plan_id` bigint(20) DEFAULT NULL,
  `category_id` bigint(20) DEFAULT NULL,
  `category_code` varchar(50) DEFAULT NULL,
  `category_name` varchar(50) DEFAULT NULL,
  `deleted` tinyint(1) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `doc_plan_category_category_id` (`category_id`),
  KEY `doc_plan_category_partner_id` (`partner_id`),
  KEY `doc_plan_category_plan_id` (`plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for doc_plan_category_position
-- ----------------------------
-- DROP TABLE IF EXISTS `doc_plan_category_position`;
CREATE TABLE `doc_plan_category_position` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `plan_id` bigint(20) DEFAULT NULL,
  `store_id` bigint(20) DEFAULT NULL,
  `position_id` bigint(20) DEFAULT NULL,
  `category_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `doc_plan_category_position_ps_id` (`plan_id`,`store_id`),
  KEY `doc_plan_category_position_position_id` (`position_id`),
  KEY `doc_plan_category_position_product_id` (`category_id`),
  KEY `doc_plan_category_position_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for doc_plan_control_doc_status
-- ----------------------------
-- DROP TABLE IF EXISTS `doc_plan_control_doc_status`;
CREATE TABLE `doc_plan_control_doc_status` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `plan_id` bigint(20) DEFAULT NULL,
  `request_id` bigint(20) DEFAULT NULL,
  `time` varchar(50) DEFAULT NULL,
  `time_around` varchar(50) DEFAULT NULL,
  `start_status` varchar(100) DEFAULT NULL,
  `end_status` varchar(100) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `send` tinyint(1) DEFAULT '0',
  `fields` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `_doc_plan_control_doc_status_request_id` (`request_id`),
  KEY `doc_plan_control_doc_status_plan_id` (`plan_id`),
  KEY `doc_plan_control_doc_status_time` (`time`),
  KEY `doc_plan_control_doc_status_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for doc_plan_position
-- ----------------------------
-- DROP TABLE IF EXISTS `doc_plan_position`;
CREATE TABLE `doc_plan_position` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `plan_id` bigint(20) DEFAULT NULL,
  `store_id` bigint(20) DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `doc_plan_position_ps_id` (`plan_id`,`store_id`),
  KEY `doc_plan_position_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for doc_plan_product
-- ----------------------------
-- DROP TABLE IF EXISTS `doc_plan_product`;
CREATE TABLE `doc_plan_product` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `plan_id` bigint(20) DEFAULT NULL,
  `product_id` bigint(20) DEFAULT NULL,
  `product_code` varchar(50) DEFAULT NULL,
  `product_name` varchar(50) DEFAULT NULL,
  `deleted` tinyint(1) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `doc_plan_product_partner_id` (`partner_id`),
  KEY `doc_plan_product_plan_id` (`plan_id`),
  KEY `doc_plan_product_product` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for doc_plan_product_by_store
-- ----------------------------
-- DROP TABLE IF EXISTS `doc_plan_product_by_store`;
CREATE TABLE `doc_plan_product_by_store` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `modify_id` bigint(20) DEFAULT NULL,
  `plan_id` bigint(20) DEFAULT NULL,
  `log_id` bigint(20) DEFAULT NULL,
  `store_id` bigint(20) DEFAULT NULL,
  `product_id` bigint(20) DEFAULT NULL,
  `product_code` varchar(50) DEFAULT NULL,
  `product_name` varchar(50) DEFAULT NULL,
  `deleted` tinyint(1) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `doc_plan_product_by_store_log_id` (`log_id`),
  KEY `doc_plan_product_by_store_modify_id` (`modify_id`),
  KEY `doc_plan_product_by_store_partner_id` (`partner_id`),
  KEY `doc_plan_product_by_store_plan_id` (`plan_id`),
  KEY `doc_plan_product_by_store_product` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for doc_plan_product_modify
-- ----------------------------
-- DROP TABLE IF EXISTS `doc_plan_product_modify`;
CREATE TABLE `doc_plan_product_modify` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `plan_id` bigint(20) DEFAULT NULL,
  `store_id` bigint(20) DEFAULT NULL,
  `branch_id` bigint(20) DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `reason` varchar(250) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `doc_plan_category_by_store_branch_id` (`branch_id`),
  KEY `doc_plan_category_by_store_status` (`status`),
  KEY `doc_plan_product_modify_partner_id` (`partner_id`),
  KEY `doc_plan_product_modify_plan_id` (`plan_id`),
  KEY `doc_plan_product_modify_store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for doc_plan_product_modify_log
-- ----------------------------
-- DROP TABLE IF EXISTS `doc_plan_product_modify_log`;
CREATE TABLE `doc_plan_product_modify_log` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `plan_id` bigint(20) DEFAULT NULL,
  `store_id` bigint(20) DEFAULT NULL,
  `modify_id` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `doc_plan_product_modify_log_modify_id` (`modify_id`),
  KEY `doc_plan_product_modify_log_partner_id` (`partner_id`),
  KEY `doc_plan_product_modify_log_plan_id` (`plan_id`),
  KEY `doc_plan_product_modify_log_store_id` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for doc_plan_product_position
-- ----------------------------
-- DROP TABLE IF EXISTS `doc_plan_product_position`;
CREATE TABLE `doc_plan_product_position` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `plan_id` bigint(20) DEFAULT NULL,
  `store_id` bigint(20) DEFAULT NULL,
  `position_id` bigint(20) DEFAULT NULL,
  `product_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `doc_plan_product_position_ps_id` (`plan_id`,`store_id`),
  KEY `doc_plan_product_position_position_id` (`position_id`),
  KEY `doc_plan_product_position_product_id` (`product_id`),
  KEY `doc_plan_product_position_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for doc_plan_store
-- ----------------------------
-- DROP TABLE IF EXISTS `doc_plan_store`;
CREATE TABLE `doc_plan_store` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `plan_id` bigint(20) DEFAULT NULL,
  `store_id` bigint(20) DEFAULT NULL,
  `store_type` varchar(50) DEFAULT NULL,
  `store_name` varchar(50) DEFAULT NULL,
  `store_code` varchar(50) DEFAULT NULL,
  `deleted` tinyint(1) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `doc_plan_store_partner_id` (`partner_id`),
  KEY `doc_plan_store_plan_id` (`plan_id`),
  KEY `doc_plan_store_store` (`store_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for invoice_blending_order
-- ----------------------------
-- DROP TABLE IF EXISTS `invoice_blending_order`;
CREATE TABLE `invoice_blending_order` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(20) DEFAULT NULL COMMENT '创建人名字',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) DEFAULT NULL COMMENT '更新人名字',
  `code` varchar(50) DEFAULT NULL COMMENT '勾兑单编号',
  `status` varchar(30) DEFAULT NULL COMMENT '勾兑单状态',
  `company_id` bigint(20) DEFAULT NULL COMMENT '公司id',
  `company_code` varchar(50) DEFAULT NULL COMMENT '公司编号',
  `company_name` varchar(50) DEFAULT NULL COMMENT '公司名称',
  `supplier_id` bigint(20) DEFAULT NULL COMMENT '供应商id',
  `supplier_code` varchar(50) DEFAULT NULL COMMENT '供应商编号',
  `supplier_name` varchar(50) DEFAULT NULL COMMENT '供应商名称',
  `order_ids_json` text COMMENT '勾兑的单据列表{"order_ids":[1,2,3]}',
  `invoice_ids_json` text COMMENT '勾兑的发票id列表 {"invoice_ids":[1,2,3]}',
  `order_sum_price` decimal(18,8) DEFAULT NULL COMMENT '单据含税合计',
  `order_sum_tax` decimal(18,8) DEFAULT NULL COMMENT '单据税额合计',
  `invoice_sum_price` decimal(18,8) DEFAULT NULL COMMENT '发票含税合计',
  `invoice_sum_tax` decimal(18,8) DEFAULT NULL COMMENT '发票税额合计',
  `diff_price` decimal(18,8) DEFAULT NULL COMMENT '差异金额 发票含税合计-单据含税合计',
  `diff_reason` varchar(100) DEFAULT NULL COMMENT '差异原因',
  PRIMARY KEY (`id`),
  KEY `invoice_blending_order_code` (`code`),
  KEY `invoice_blending_order_company_id` (`company_id`),
  KEY `invoice_blending_order_partner_id` (`partner_id`),
  KEY `invoice_blending_order_status` (`status`),
  KEY `invoice_blending_order_supplier_id` (`supplier_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发票勾兑单';

-- ----------------------------
-- Table structure for invoices
-- ----------------------------
-- DROP TABLE IF EXISTS `invoices`;
CREATE TABLE `invoices` (
  `id` bigint(20) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `invoices_number` varchar(255) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `company_id` bigint(20) unsigned DEFAULT NULL,
  `supplier_id` bigint(20) unsigned DEFAULT NULL,
  `invoice_date` datetime DEFAULT NULL,
  `tax_excluded` double DEFAULT NULL,
  `after_taxation` double DEFAULT NULL,
  `tax` double DEFAULT NULL,
  `updated_by` bigint(20) unsigned DEFAULT NULL,
  `partner_id` bigint(20) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_invoices_deleted_at` (`deleted_at`),
  KEY `idx_invoices_company_id` (`company_id`),
  KEY `idx_invoices_supplier_id` (`supplier_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for metadata_product
-- ----------------------------
-- DROP TABLE IF EXISTS `metadata_product`;
CREATE TABLE `metadata_product` (
  `id` bigint(20) NOT NULL,
  `name` varchar(50) DEFAULT NULL,
  `code` varchar(50) DEFAULT NULL,
  `bom_type` varchar(50) DEFAULT NULL,
  `product_type` varchar(50) DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `updated` datetime DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `uom_id` bigint(20) DEFAULT NULL,
  `bom_uom_id` bigint(20) DEFAULT NULL,
  `category_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `metadata_product_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='主档商品同步表';

-- ----------------------------
-- Table structure for metadata_store
-- ----------------------------
-- DROP TABLE IF EXISTS `metadata_store`;
CREATE TABLE `metadata_store` (
  `id` bigint(20) NOT NULL,
  `name` varchar(50) DEFAULT NULL,
  `code` varchar(50) DEFAULT NULL,
  `second_code` varchar(50) DEFAULT NULL,
  `store_type` varchar(50) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `status` varchar(50) DEFAULT NULL,
  `created` datetime DEFAULT NULL,
  `updated` datetime DEFAULT NULL,
  `branch_region_id` bigint(20) DEFAULT NULL,
  `geo_region_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `metadata_store_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='主档门店同步表';

-- ----------------------------
-- Table structure for purchase_review_order
-- ----------------------------
-- DROP TABLE IF EXISTS `purchase_review_order`;
CREATE TABLE `purchase_review_order` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_name` varchar(20) DEFAULT NULL COMMENT '创建人名字',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) DEFAULT NULL COMMENT '更新人名字',
  `order_code` varchar(50) DEFAULT NULL COMMENT '订单编号',
  `order_type` varchar(30) DEFAULT NULL COMMENT '单据类型',
  `order_status` varchar(20) DEFAULT NULL COMMENT '单据业务状态(INITED,COMPLETE)',
  `received_date` datetime DEFAULT NULL COMMENT '收货日期',
  `received_by` bigint(20) DEFAULT NULL COMMENT '门店或仓库id',
  `received_code` varchar(15) DEFAULT NULL COMMENT '门店或仓库编号',
  `received_name` varchar(50) DEFAULT NULL COMMENT '门店或仓库名称',
  `supplier_id` bigint(20) DEFAULT NULL COMMENT '供应商id',
  `supplier_code` varchar(15) DEFAULT NULL COMMENT '供应商编码',
  `supplier_name` varchar(50) DEFAULT NULL COMMENT '供应商名称',
  `sum_price_tax` decimal(18,8) DEFAULT '0.********' COMMENT '总含税价',
  `adjust_sum_price_tax` decimal(18,8) DEFAULT '0.********' COMMENT '复核后总含税价',
  `sum_price` decimal(18,8) DEFAULT '0.********' COMMENT '总未税价',
  `adjust_sum_price` decimal(18,8) DEFAULT '0.********' COMMENT '复核后总未税价',
  `batch_id` bigint(20) DEFAULT NULL COMMENT '主单id',
  `is_modify` tinyint(1) DEFAULT NULL COMMENT '是否变更价格(否0,是1)',
  `review_by` bigint(20) DEFAULT NULL COMMENT '审核人id',
  `branch_type` varchar(20) DEFAULT NULL COMMENT '区分门店/仓库',
  `company_id` bigint(20) DEFAULT NULL COMMENT '公司id',
  `status` varchar(20) DEFAULT NULL COMMENT '单据业务状态给发票勾兑使用INITED/APPROVED',
  `origin_code` varchar(50) DEFAULT NULL COMMENT '原单编号',
  `adjust_info` text COMMENT '对应的调整单信息json',
  `generate_adjust` tinyint(1) DEFAULT '0' COMMENT '是否生成调价单0/1',
  `adjust_code` varchar(50) DEFAULT NULL COMMENT '生成的复核调价单号code',
  `is_adjust` tinyint(1) DEFAULT '0' COMMENT '标记是否是调整单',
  `cost_center_id` bigint(20) DEFAULT NULL COMMENT '成本中心id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `purchase_review_batch_id` (`batch_id`),
  KEY `purchase_review_is_modify` (`is_modify`),
  KEY `purchase_review_order_code` (`order_code`),
  KEY `purchase_review_order_receive_by` (`received_by`),
  KEY `purchase_review_order_status` (`order_status`),
  KEY `purchase_review_order_supplier_id` (`supplier_id`),
  KEY `purchase_review_order_type` (`order_type`),
  KEY `purchase_review_received_date` (`received_date`),
  KEY `purchase_review_branch_type` (`branch_type`),
  KEY `purchase_review_company_id` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购复核单主表';

-- ----------------------------
-- Table structure for purchase_review_order_product
-- ----------------------------
-- DROP TABLE IF EXISTS `purchase_review_order_product`;
CREATE TABLE `purchase_review_order_product` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(20) DEFAULT NULL COMMENT '创建人名字',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) DEFAULT NULL COMMENT '更新人名字',
  `order_id` bigint(20) DEFAULT NULL COMMENT '关联的采购复核单id',
  `product_id` bigint(20) DEFAULT NULL COMMENT '商品id',
  `product_code` varchar(50) DEFAULT NULL COMMENT '商品code',
  `product_name` varchar(50) DEFAULT NULL COMMENT '商品名称',
  `product_category_id` bigint(20) DEFAULT NULL COMMENT '商品分类id',
  `product_type` varchar(50) DEFAULT NULL COMMENT '商品属性',
  `unit_id` bigint(20) DEFAULT NULL COMMENT '单位id(门店中为订货单位/仓库为采购单位)',
  `unit_name` varchar(50) DEFAULT NULL COMMENT '单位名称',
  `unit_spec` varchar(50) DEFAULT NULL COMMENT '单位规格',
  `spec` varchar(30) DEFAULT NULL COMMENT '商品规格',
  `quantity` decimal(18,8) DEFAULT NULL COMMENT '订货/退货数量',
  `actual_quantity` decimal(18,8) DEFAULT NULL COMMENT '实际数量',
  `tax_rate` decimal(16,3) DEFAULT NULL COMMENT '税率',
  `price` decimal(18,8) DEFAULT NULL COMMENT '未税单价',
  `adjust_price` decimal(18,8) DEFAULT NULL COMMENT '复核后未税单价',
  `price_tax` decimal(18,8) DEFAULT NULL COMMENT '含税单价',
  `adjust_price_tax` decimal(18,8) DEFAULT NULL COMMENT '复核后含税单价',
  `sum_price` decimal(18,8) DEFAULT NULL COMMENT '未税总价',
  `adjust_sum_price` decimal(18,8) DEFAULT NULL COMMENT '复核后未税总价',
  `sum_price_tax` decimal(18,8) DEFAULT NULL COMMENT '含税总价',
  `adjust_sum_price_tax` decimal(18,8) DEFAULT NULL COMMENT '复核后含税总价',
  `is_modify` tinyint(1) DEFAULT NULL COMMENT '是否变更价格(否0,是1)',
  PRIMARY KEY (`id`),
  KEY `purchase_review_order_product_is_modify` (`is_modify`),
  KEY `purchase_review_order_product_order_id` (`order_id`),
  KEY `purchase_review_order_product_partner_id` (`partner_id`),
  KEY `purchase_review_order_product_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购复核商品明细';

-- ----------------------------
-- Table structure for receipt_flow_log
-- ----------------------------
-- DROP TABLE IF EXISTS `receipt_flow_log`;
CREATE TABLE `receipt_flow_log` (
  `id` bigint(20) NOT NULL,
  `doc_id` bigint(20) DEFAULT NULL COMMENT '单据ID',
  `doc_type` varchar(15) DEFAULT NULL COMMENT '单据类型',
  `sub_doc_type` varchar(15) DEFAULT NULL COMMENT '预留单据子类型',
  `doc_status` varchar(10) DEFAULT NULL COMMENT '单据状态',
  `branch_id` bigint(20) DEFAULT NULL COMMENT '组织(门店等)ID',
  `sub_branch_id` bigint(20) DEFAULT NULL COMMENT '仓位ID',
  `operation` varchar(20) DEFAULT NULL COMMENT '操作',
  `posterior_doc_id` bigint(20) DEFAULT NULL COMMENT '下一个单据id',
  `posterior_doc_type` varchar(15) DEFAULT NULL COMMENT '下一个单据类型',
  `posterior_doc_status` varchar(10) DEFAULT NULL COMMENT '下一个单据状态',
  `posterior_operation` varchar(20) DEFAULT NULL COMMENT '下一个操作',
  `process_status` varchar(15) DEFAULT NULL COMMENT '操作状态 INITED/PROCESSING/COMPLETED',
  `partner_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  `remark` varchar(50) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `receipt_flow_log_doc_id` (`doc_id`),
  KEY `receipt_flow_log_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for receipt_flow_product_log
-- ----------------------------
-- DROP TABLE IF EXISTS `receipt_flow_product_log`;
CREATE TABLE `receipt_flow_product_log` (
  `id` bigint(20) NOT NULL,
  `doc_id` bigint(20) DEFAULT NULL,
  `type` varchar(30) DEFAULT NULL COMMENT '操作业务类型： (AUTO_TRANSFER-自动调拨/AUTO_CONVERT-自动物料转换)',
  `reusable` tinyint(1) DEFAULT '0' COMMENT '标记商品是否可以重复操作',
  `product_id` bigint(20) DEFAULT NULL,
  `target_product_id` bigint(20) DEFAULT NULL COMMENT '物料转换目标物料id',
  `created_by` bigint(20) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  `flow_id` bigint(20) DEFAULT NULL COMMENT '对应工作流id',
  `batch_id` bigint(20) DEFAULT NULL COMMENT '批次id对应每次创建单据请求的request_id',
  `business_date` datetime DEFAULT NULL COMMENT '业务发生时间',
  `rule_id` bigint(20) DEFAULT NULL COMMENT '规则id(调拨规则/物料转换规则)',
  `receiving_position` bigint(20) DEFAULT NULL COMMENT '自动调拨接收仓位',
  `partner_id` bigint(20) DEFAULT NULL,
  `quantity` decimal(18,8) DEFAULT NULL COMMENT '原物料数量',
  `target_quantity` decimal(18,8) DEFAULT NULL COMMENT '目标物料数量',
  `status` varchar(20) DEFAULT NULL COMMENT '状态INITED、SUCCESS\n',
  `unit_id` bigint(20) DEFAULT NULL COMMENT '单位',
  `target_unit_id` bigint(20) DEFAULT NULL COMMENT '物料转换目标物料单位',
  PRIMARY KEY (`id`),
  KEY `receipt_flow_product_log_doc_id` (`doc_id`),
  KEY `receipt_flow_product_log__flow_id` (`flow_id`),
  KEY `receipt_flow_product_log__partner` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for sales_explained_report
-- ----------------------------
-- DROP TABLE IF EXISTS `sales_explained_report`;
CREATE TABLE `sales_explained_report` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `batch_id` bigint(20) DEFAULT NULL,
  `store_id` bigint(20) DEFAULT NULL,
  `product_id` bigint(20) DEFAULT NULL,
  `bom_product_id` bigint(20) DEFAULT NULL,
  `product_qty` decimal(38,16) DEFAULT NULL,
  `qty` decimal(38,16) DEFAULT NULL,
  `sales_date` bigint(20) DEFAULT NULL,
  `e_ticket_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for send_cost_center_batch
-- ----------------------------
-- DROP TABLE IF EXISTS `send_cost_center_batch`;
CREATE TABLE `send_cost_center_batch` (
  `id` bigint(20) NOT NULL,
  `batch_id` bigint(20) NOT NULL COMMENT '批次id这里存单据id做唯一限制',
  `data` text COMMENT '待同步数据json{"data":data_list}',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL COMMENT '单据同步状态',
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `batch_type` varchar(50) DEFAULT NULL COMMENT '批次单据业务类型',
  PRIMARY KEY (`id`),
  UNIQUE KEY `send_cost_center_batch_batch_id_uindex` (`batch_id`),
  KEY `send_cost_center_batch_partner_id` (`partner_id`),
  KEY `send_cost_center_batch_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='同步成本中心数据缓存批次控制表';

-- ----------------------------
-- Table structure for stocktake_month_code
-- ----------------------------
-- DROP TABLE IF EXISTS `stocktake_month_code`;
CREATE TABLE `stocktake_month_code` (
  `id` bigint(20) NOT NULL,
  `value` bigint(20) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_adjust
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_adjust`;
CREATE TABLE `supply_adjust` (
  `id` bigint(20) NOT NULL,
  `branch_batch_id` bigint(20) DEFAULT NULL,
  `schedule_id` bigint(20) DEFAULT NULL,
  `code` varchar(50) DEFAULT NULL,
  `reason_type` varchar(25) DEFAULT NULL,
  `adjust_store` bigint(20) DEFAULT NULL,
  `adjust_date` datetime DEFAULT NULL,
  `status` varchar(10) DEFAULT NULL,
  `process_status` varchar(10) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `request_id` bigint(20) DEFAULT NULL,
  `receive_id` bigint(20) DEFAULT NULL,
  `receive_code` varchar(50) DEFAULT NULL,
  `branch_type` varchar(30) DEFAULT NULL,
  `cost_trans_status` tinyint(1) DEFAULT '0' COMMENT '是否传输成本中心 0:未传输，1:已传输',
  `source` varchar(30) DEFAULT NULL COMMENT '单据来源：pos端报废/报废计划/手动创建',
  PRIMARY KEY (`id`),
  KEY `supply_adjust_index1` (`branch_type`),
  KEY `supply_adjust_index2` (`partner_id`),
  KEY `supply_adjust__index3` (`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_adjust_bom_report
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_adjust_bom_report`;
CREATE TABLE `supply_adjust_bom_report` (
  `id` bigint(20) NOT NULL,
  `adjust_id` bigint(20) DEFAULT NULL,
  `batch_id` bigint(20) DEFAULT NULL,
  `store_id` bigint(20) DEFAULT NULL,
  `product_id` bigint(20) DEFAULT NULL,
  `unit_id` bigint(20) DEFAULT NULL,
  `unit_name` varchar(50) DEFAULT NULL,
  `unit_spec` varchar(50) DEFAULT NULL,
  `accounting_unit_id` bigint(20) DEFAULT NULL,
  `accounting_unit_name` varchar(50) DEFAULT NULL,
  `accounting_unit_spec` varchar(50) DEFAULT NULL,
  `bom_product_id` bigint(20) DEFAULT NULL,
  `uom_id` bigint(20) DEFAULT NULL,
  `bom_unit_id` bigint(20) DEFAULT NULL,
  `bom_unit_name` varchar(50) DEFAULT NULL,
  `bom_unit_spec` varchar(50) DEFAULT NULL,
  `bom_accounting_unit_id` bigint(20) DEFAULT NULL,
  `bom_accounting_unit_name` varchar(50) DEFAULT NULL,
  `bom_accounting_unit_spec` varchar(50) DEFAULT NULL,
  `product_qty` decimal(18,8) DEFAULT NULL,
  `accounting_product_qty` decimal(18,8) DEFAULT NULL,
  `reason_type` varchar(25) DEFAULT NULL,
  `qty` decimal(18,8) DEFAULT NULL,
  `accounting_qty` decimal(18,8) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `bom_rate` decimal(18,8) DEFAULT NULL,
  `line_id` bigint(20) DEFAULT NULL,
  `line_m_qty` decimal(18,8) DEFAULT NULL,
  `line_m_accounting_qty` decimal(18,8) DEFAULT NULL,
  `position_id` bigint(20) DEFAULT NULL COMMENT '仓位id',
  PRIMARY KEY (`id`),
  KEY `supply_adjust_bom_report_adjust_id` (`adjust_id`),
  KEY `supply_adjust_bom_report_bom_product_id` (`bom_product_id`),
  KEY `supply_adjust_bom_report_product_id` (`product_id`),
  KEY `supply_adjust_bom_report_store_id` (`store_id`),
  KEY `supply_adjust_bom_report_line_id` (`line_id`),
  KEY `supply_adjust_bom_report_position_id` (`position_id`),
  KEY `supply_adjust_bom_report_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_adjust_bom_report_batch
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_adjust_bom_report_batch`;
CREATE TABLE `supply_adjust_bom_report_batch` (
  `id` bigint(20) NOT NULL,
  `adjust_id` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `supply_adjust_bom_report_batch_adjust_id` (`adjust_id`),
  KEY `supply_adjust_bom_report_batch_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_adjust_detail
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_adjust_detail`;
CREATE TABLE `supply_adjust_detail` (
  `id` bigint(20) NOT NULL,
  `branch_batch_id` bigint(20) DEFAULT NULL,
  `adjust_id` bigint(20) DEFAULT NULL,
  `schedule_id` bigint(20) DEFAULT NULL,
  `code` varchar(50) DEFAULT NULL,
  `schedule_code` varchar(50) DEFAULT NULL,
  `reason_type` varchar(25) DEFAULT NULL,
  `adjust_order_number` bigint(20) DEFAULT NULL,
  `adjust_date` datetime DEFAULT NULL,
  `adjust_store` bigint(20) DEFAULT NULL,
  `adjust_store_secondary_id` varchar(50) DEFAULT NULL,
  `status` varchar(10) DEFAULT NULL,
  `process_status` varchar(10) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `request_id` bigint(20) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  `receive_id` bigint(20) DEFAULT NULL,
  `schedule_name` varchar(50) DEFAULT NULL,
  `receive_code` varchar(50) DEFAULT NULL,
  `branch_type` varchar(30) DEFAULT NULL,
  `cost_center_id` bigint(20) DEFAULT NULL COMMENT '所属成本中心ID',
  `source` varchar(30) DEFAULT NULL COMMENT '单据来源：pos端报废/报废计划/手动创建',
  PRIMARY KEY (`id`),
  KEY `supply_adjust_detail_index1` (`adjust_id`),
  KEY `supply_adjust_detail_index2` (`partner_id`),
  KEY `supply_adjust_detail_index3` (`adjust_store`),
  KEY `supply_adjust_detail_index4` (`created_at`),
  KEY `supply_adjust_detail_index5` (`status`),
  KEY `supply_adjust_detail_index6` (`reason_type`),
  KEY `supply_adjust_detail_index7` (`adjust_date`),
  KEY `supply_adjust_detail_index8` (`branch_type`),
  KEY `supply_adjust_detail__index9` (`source`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_adjust_log
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_adjust_log`;
CREATE TABLE `supply_adjust_log` (
  `id` bigint(20) NOT NULL,
  `adjust_id` bigint(20) DEFAULT NULL,
  `adjust_status` varchar(10) DEFAULT NULL,
  `reason` varchar(255) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_adjust_product
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_adjust_product`;
CREATE TABLE `supply_adjust_product` (
  `id` bigint(20) NOT NULL,
  `adjust_id` bigint(20) DEFAULT NULL,
  `product_id` bigint(20) DEFAULT NULL,
  `product_code` varchar(50) DEFAULT NULL,
  `product_name` varchar(50) DEFAULT NULL,
  `material_number` varchar(25) DEFAULT NULL,
  `unit_id` bigint(20) DEFAULT NULL,
  `unit_name` varchar(50) DEFAULT NULL,
  `unit_spec` varchar(50) DEFAULT NULL,
  `accounting_unit_id` bigint(20) DEFAULT NULL,
  `accounting_unit_name` varchar(50) DEFAULT NULL,
  `accounting_unit_spec` varchar(50) DEFAULT NULL,
  `quantity` decimal(18,8) DEFAULT NULL,
  `accounting_quantity` decimal(18,8) DEFAULT NULL,
  `confirmed_quantity` decimal(18,8) DEFAULT NULL,
  `item_number` int(11) DEFAULT NULL,
  `is_confirmed` tinyint(1) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `adjust_store` bigint(20) DEFAULT NULL,
  `adjust_date` datetime DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  `reason_type` varchar(25) DEFAULT NULL,
  `is_bom` tinyint(1) DEFAULT NULL,
  `convert_accounting_quantity` decimal(18,8) DEFAULT NULL,
  `position_id` bigint(20) DEFAULT NULL,
  `sku_remark` text COMMENT '商品配方物料属性详情',
  PRIMARY KEY (`id`),
  KEY `supply_adjust_product_adjust_id` (`adjust_id`),
  KEY `supply_adjust_product_index` (`adjust_store`,`product_id`,`adjust_date`),
  KEY `supply_adjust_product_product_id` (`product_id`),
  KEY `supply_adjust_product_adjust_date` (`adjust_date`),
  KEY `supply_adjust_product_partner_id` (`partner_id`),
  KEY `supply_adjust_product_position_id` (`position_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_attachments
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_attachments`;
CREATE TABLE `supply_attachments` (
  `id` bigint(20) NOT NULL,
  `doc_id` bigint(20) DEFAULT NULL,
  `doc_type` varchar(25) DEFAULT NULL,
  `attachment` text,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `signature` text,
  `nosign_reason` text,
  `delay_reason` text,
  PRIMARY KEY (`id`),
  KEY `supply_attachments_doc_id` (`doc_id`),
  KEY `supply_attachments_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_cost_trigger_log
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_cost_trigger_log`;
CREATE TABLE `supply_cost_trigger_log` (
  `id` bigint(20) NOT NULL,
  `batch_no` bigint(20) DEFAULT NULL,
  `branch_id` bigint(20) DEFAULT NULL,
  `branch_type` varchar(255) DEFAULT NULL,
  `req_type` varchar(50) DEFAULT NULL,
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `sent` varchar(255) DEFAULT NULL,
  `ret` varchar(255) DEFAULT NULL,
  `success` tinyint(255) DEFAULT NULL,
  `msg` varchar(255) DEFAULT NULL,
  `status` varchar(10) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(255) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `period_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supply_cost_trigger_log_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_cost_trigger_period
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_cost_trigger_period`;
CREATE TABLE `supply_cost_trigger_period` (
  `id` bigint(20) NOT NULL,
  `code` varchar(50) DEFAULT NULL,
  `name` varchar(50) DEFAULT NULL,
  `branch_id` bigint(20) DEFAULT NULL,
  `branch_type` varchar(255) DEFAULT NULL,
  `req_type` varchar(50) DEFAULT NULL,
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `status` varchar(10) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(255) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supply_cost_trigger_period_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_daily_cut_log
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_daily_cut_log`;
CREATE TABLE `supply_daily_cut_log` (
  `id` bigint(20) NOT NULL,
  `batch_no` varchar(30) DEFAULT NULL,
  `inventory_id` bigint(20) DEFAULT NULL,
  `store_id` bigint(20) DEFAULT NULL,
  `product_ids` text,
  `end_time` datetime DEFAULT NULL,
  `send` text,
  `ret` text,
  `success` tinyint(4) DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_at` varchar(255) DEFAULT NULL,
  `send_product_nums` int(255) DEFAULT NULL,
  `success_nums` int(255) DEFAULT NULL,
  `fail_nums` int(255) DEFAULT NULL,
  `receive_nums` int(255) DEFAULT NULL,
  `retry_nums` int(255) DEFAULT NULL,
  `branch_type` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_index` (`store_id`,`end_time`),
  KEY `supply_daily_cut_log_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_demand
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_demand`;
CREATE TABLE `supply_demand` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `batch_id` bigint(20) DEFAULT NULL COMMENT '订货单关联的supply_demand_batch_store批次id',
  `code` varchar(50) DEFAULT NULL COMMENT '订单编号',
  `type` varchar(10) DEFAULT NULL COMMENT '门店(SD)或者主配类型(MD)',
  `reason_type` varchar(10) DEFAULT NULL COMMENT '原因',
  `receive_by` bigint(20) DEFAULT NULL COMMENT '收获门店id',
  `receive_name` varchar(50) DEFAULT NULL,
  `distribute_by` bigint(20) DEFAULT NULL COMMENT '供应商id',
  `store_secondary_id` varchar(50) DEFAULT NULL COMMENT '门店编码',
  `store_type` varchar(50) DEFAULT NULL,
  `distribution_type` varchar(10) DEFAULT NULL COMMENT '供应商类型',
  `demand_date` datetime DEFAULT NULL COMMENT '订货日期',
  `review_by` bigint(20) DEFAULT NULL COMMENT '审核人id',
  `arrival_date` datetime DEFAULT NULL COMMENT '到货日期',
  `description` varchar(255) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `sub_type` varchar(10) DEFAULT NULL COMMENT '主配类型，STORE按门店，PRODUCT按商品',
  `remark` varchar(255) DEFAULT NULL,
  `has_product` tinyint(1) DEFAULT '0' COMMENT '是否含有商品, 1是含有，0是不含有',
  `is_sys` tinyint(1) DEFAULT '0' COMMENT '业务状态是否是系统最后处理, 1是，0不是',
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_name` varchar(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL COMMENT '单据业务状态(INITED,CONFIRMED,REJECTED,APPROVED,FREEZE,CANCELLED)',
  `process_status` varchar(20) DEFAULT NULL COMMENT '生成要货单处理状态(INITED,PROCESSING,SUCCESS,FAILED)',
  `is_plan` tinyint(1) DEFAULT '0',
  `is_adjust` tinyint(1) DEFAULT '0',
  `send_type` varchar(50) DEFAULT NULL,
  `bus_type` varchar(50) DEFAULT NULL,
  `is_auto` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supply_demand_index2` (`code`),
  KEY `supply_demand_index3` (`receive_by`),
  KEY `supply_demand_index4` (`partner_id`),
  KEY `supply_demand_index6` (`type`),
  KEY `supply_demand_index7` (`status`),
  KEY `supply_demand_index8` (`demand_date`,`arrival_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订货单主表';

-- ----------------------------
-- Table structure for supply_demand_log
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_demand_log`;
CREATE TABLE `supply_demand_log` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `store_id` bigint(20) DEFAULT NULL COMMENT '门店id',
  `demand_id` bigint(20) DEFAULT NULL COMMENT '订货单id',
  `demand_code` varchar(50) DEFAULT NULL COMMENT '订货单编号',
  `action_code` varchar(50) DEFAULT NULL COMMENT '操作编码',
  `action_name` varchar(50) DEFAULT NULL COMMENT '操作名称',
  `updated_name` varchar(50) DEFAULT NULL COMMENT '操作人姓名',
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_by` bigint(20) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `index_demand_code` (`demand_code`),
  KEY `index_demand_id` (`demand_id`),
  KEY `index_store_id` (`store_id`),
  KEY `supply_demand_log_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订货单操作日志';

-- ----------------------------
-- Table structure for supply_demand_master_upload
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_demand_master_upload`;
CREATE TABLE `supply_demand_master_upload` (
  `id` bigint(20) NOT NULL,
  `status` varchar(10) DEFAULT NULL,
  `batch_id` bigint(20) DEFAULT NULL,
  `store_id` bigint(20) DEFAULT NULL,
  `store_code` varchar(50) DEFAULT NULL,
  `store_name` varchar(50) DEFAULT NULL,
  `product_id` bigint(20) DEFAULT NULL,
  `product_code` varchar(50) DEFAULT NULL,
  `product_name` varchar(50) DEFAULT NULL,
  `start_date` datetime DEFAULT NULL,
  `end_date` datetime DEFAULT NULL,
  `quantity` decimal(18,8) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `row_num` int(11) DEFAULT NULL,
  `master_id` bigint(20) DEFAULT NULL,
  `master_code` varchar(50) DEFAULT NULL,
  `store_type` varchar(50) DEFAULT NULL,
  `centre_id` bigint(20) DEFAULT NULL,
  `centre_code` varchar(50) DEFAULT NULL,
  `centre_name` varchar(50) DEFAULT NULL,
  `remark` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supply_demand_master_upload_partner_id` (`partner_id`),
  KEY `supply_demand_master_upload_batch_id` (`batch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_demand_master_upload_batch
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_demand_master_upload_batch`;
CREATE TABLE `supply_demand_master_upload_batch` (
  `id` bigint(20) NOT NULL,
  `status` varchar(10) DEFAULT NULL,
  `file_name` varchar(200) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `updated_name` varchar(100) DEFAULT NULL,
  `file_type` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supply_demand_master_upload_batch_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_demand_product
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_demand_product`;
CREATE TABLE `supply_demand_product` (
  `id` bigint(20) NOT NULL,
  `demand_id` bigint(20) DEFAULT NULL COMMENT '订货单详细关联的订货主表',
  `product_id` bigint(20) DEFAULT NULL COMMENT '商品id',
  `product_code` varchar(50) DEFAULT NULL COMMENT '商品code',
  `product_name` varchar(50) DEFAULT NULL COMMENT '商品名称',
  `product_category_id` bigint(20) DEFAULT NULL COMMENT '商品类别id',
  `product_category_name` varchar(50) DEFAULT NULL,
  `sale_type` varchar(50) DEFAULT NULL,
  `product_type` varchar(50) DEFAULT NULL,
  `unit_id` bigint(20) DEFAULT NULL COMMENT '订货单位id',
  `unit_name` varchar(50) DEFAULT NULL COMMENT '订货单位名称',
  `unit_spec` varchar(50) DEFAULT NULL COMMENT '单位规格',
  `spec` varchar(50) DEFAULT NULL COMMENT '规格',
  `accounting_unit_id` bigint(20) DEFAULT NULL COMMENT '核算单位id',
  `accounting_unit_name` varchar(50) DEFAULT NULL COMMENT '核算单位name',
  `accounting_unit_spec` varchar(50) DEFAULT NULL COMMENT '核算规格',
  `quantity` decimal(18,8) DEFAULT NULL COMMENT '订货数量',
  `finished_quantity` decimal(18,8) DEFAULT NULL COMMENT '成品订货数量',
  `tea_quantity` decimal(18,8) DEFAULT NULL COMMENT '茶饮订货数量',
  `bread_quantity` decimal(18,8) DEFAULT NULL COMMENT '面包订货数量',
  `raw_quantity` decimal(18,8) DEFAULT NULL COMMENT '原料订货数量',
  `suggest_quantity` decimal(18,8) DEFAULT NULL COMMENT '建议订货量',
  `confirmed_quantity` decimal(18,8) DEFAULT NULL COMMENT '确认数量',
  `accounting_quantity` decimal(18,8) DEFAULT NULL COMMENT '核算单位数量',
  `min_quantity` decimal(18,8) DEFAULT NULL COMMENT '最小订货量',
  `max_quantity` decimal(18,8) DEFAULT NULL COMMENT '最大订货量',
  `increment_quantity` decimal(18,8) DEFAULT NULL COMMENT '递增订货量',
  `unit_rate` decimal(18,8) DEFAULT NULL COMMENT '单位转换比率',
  `purchase_tax` decimal(18,8) DEFAULT NULL COMMENT '采购价格',
  `purchase_price` decimal(18,8) DEFAULT NULL COMMENT '采购价格',
  `arrival_days` int(11) DEFAULT NULL COMMENT '到货天数',
  `distribution_circle` varchar(25) DEFAULT NULL COMMENT '配送周期类型',
  `distribution_type` varchar(10) DEFAULT NULL COMMENT '配送类型，直送/配送',
  `distribute_by` bigint(20) DEFAULT NULL COMMENT '配送/供应商中心',
  `storage_type` varchar(25) DEFAULT NULL COMMENT '储藏类型',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '租户id',
  `cycle_extends` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supply_demand_product_index2` (`demand_id`,`product_id`),
  KEY `supply_demand_product_index3` (`product_name`),
  KEY `supply_demand_product_index4` (`created_at`),
  KEY `supply_demand_product_index5` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订货单商品，关联supply_demand';

-- ----------------------------
-- Table structure for supply_doc_code
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_doc_code`;
CREATE TABLE `supply_doc_code` (
  `id` bigint(20) NOT NULL,
  `config_id` bigint(20) DEFAULT NULL,
  `value` int(11) NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `created_by` varchar(50) DEFAULT NULL,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_by` varchar(50) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `_config_id_partner_id_uc` (`config_id`,`partner_id`),
  KEY `supply_code` (`partner_id`,`config_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_doc_code_config_main
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_doc_code_config_main`;
CREATE TABLE `supply_doc_code_config_main` (
  `id` bigint(20) NOT NULL,
  `name` varchar(50) DEFAULT NULL,
  `type` varchar(10) DEFAULT NULL,
  `prefix` varchar(50) DEFAULT NULL,
  `value_length` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `partner_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `index` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_doc_code_config_partner
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_doc_code_config_partner`;
CREATE TABLE `supply_doc_code_config_partner` (
  `id` bigint(20) NOT NULL,
  `config_id` bigint(20) DEFAULT NULL,
  `prefix` varchar(50) DEFAULT NULL,
  `value_length` int(11) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `config_id` (`config_id`),
  KEY `supply_code_config_partner_index` (`partner_id`,`config_id`),
  CONSTRAINT `supply_doc_code_config_partner_ibfk_1` FOREIGN KEY (`config_id`) REFERENCES `supply_doc_code_config_main` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_inventory_action_doc
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_inventory_action_doc`;
CREATE TABLE `supply_inventory_action_doc` (
  `id` bigint(20) NOT NULL,
  `batch_no` varchar(50) DEFAULT NULL,
  `code` varchar(50) DEFAULT NULL,
  `batch_action` bigint(20) DEFAULT NULL,
  `action_dec` varchar(25) DEFAULT NULL,
  `batch_id` bigint(20) DEFAULT NULL,
  `status` varchar(10) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `message` mediumtext,
  PRIMARY KEY (`id`),
  KEY `supply_inv_bat_id` (`batch_id`),
  KEY `supply_inv_bat_batch_no` (`batch_no`),
  KEY `supply_inv_bat_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_material_convert
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_material_convert`;
CREATE TABLE `supply_material_convert` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_name` varchar(20) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '创建人名字',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '更新人名字',
  `code` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '订单编号',
  `status` varchar(20) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '订单状态/新建INITED/已确认CONFIRMED/已作废INVALID',
  `convert_type` varchar(15) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '转换类型(自动AUTO、手动MANUAL)',
  `convert_date` datetime DEFAULT NULL COMMENT '物料转换日期',
  `branch_id` bigint(20) DEFAULT NULL COMMENT '门店/仓库/加工中心id',
  `branch_code` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '门店/仓库/加工中心编号',
  `branch_name` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '门店/仓库/加工中心名称',
  `position_id` bigint(20) NOT NULL COMMENT '仓位id',
  `position_name` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '仓位名称',
  `position_code` varchar(30) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '仓位code',
  `branch_type` varchar(20) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '区分门店/仓库',
  `convert_rule` bigint(20) DEFAULT NULL COMMENT '转换规则ID',
  `opened_position` tinyint(1) DEFAULT '0' COMMENT '是否开启多仓位',
  `process_status` varchar(30) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '(预留)物料转换状态(INITED、PROCESSING、COMPLETED)',
  `request_id` bigint(20) NOT NULL COMMENT '幂等性校验请求id',
  `remark` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '备注',
  `cost_center_id` bigint(20) DEFAULT NULL COMMENT '所属成本中心id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `request_id` (`request_id`),
  KEY `supply_material_convert_convert_date` (`convert_date`),
  KEY `supply_material_convert_partner_id` (`partner_id`),
  KEY `supply_material_convert_code` (`code`),
  KEY `supply_material_branch_id` (`branch_id`),
  KEY `supply_material_position_id` (`position_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='物料转换单';

-- ----------------------------
-- Table structure for supply_material_convert_detail
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_material_convert_detail`;
CREATE TABLE `supply_material_convert_detail` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '创建人名称',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '更新人名称',
  `product_id` bigint(20) DEFAULT NULL COMMENT '商品/物料id',
  `product_code` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '商品/物料编码',
  `product_name` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '商品/物料名称',
  `product_type` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '商品类型',
  `unit_id` bigint(20) DEFAULT NULL COMMENT '单位',
  `unit_name` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '单位名称',
  `unit_spec` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '单位规格',
  `quantity` decimal(18,8) DEFAULT NULL COMMENT '物料数量',
  `type` varchar(20) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '物料类型(原始物料origin/目标物料target)',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `main_id` bigint(20) DEFAULT NULL COMMENT '关联的物料转换id',
  `unit_rate` decimal(18,8) DEFAULT NULL COMMENT '单位转换率',
  PRIMARY KEY (`id`),
  KEY `supply_material_convert_detail_main_id` (`main_id`),
  KEY `supply_material_convert_detail_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='物料转换明细表';

-- ----------------------------
-- Table structure for supply_material_convert_log
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_material_convert_log`;
CREATE TABLE `supply_material_convert_log` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '创建人名称',
  `main_id` bigint(20) DEFAULT NULL COMMENT '关联的物料转换id',
  `action` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '操作状态（确认COMPLETED/作废INVALID等）',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `supply_material_convert_log_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='物料转换操作记录';

-- ----------------------------
-- Table structure for supply_packing_receipts
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_packing_receipts`;
CREATE TABLE `supply_packing_receipts` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_name` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '创建人名字',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '更新人名字',
  `code` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '订单编号',
  `status` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '订单状态/新建INITED/提交SUBMITTED/审核APPROVED/驳回REJECTED',
  `type` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '类型(预留)',
  `packing_date` datetime DEFAULT NULL COMMENT '包装日期',
  `machining_center_id` bigint(20) DEFAULT NULL COMMENT '加工中心id',
  `position_id` bigint(20) DEFAULT NULL COMMENT '仓位id',
  `process_status` varchar(30) COLLATE utf8_bin DEFAULT NULL COMMENT '单据轮转状态(预留)',
  `opened_position` tinyint(1) DEFAULT '0' COMMENT '是否开启多仓位',
  `packing_rule` bigint(20) DEFAULT NULL COMMENT '包装规则ID',
  `request_id` bigint(20) NOT NULL COMMENT '幂等性校验请求id',
  `remark` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `request_id` (`request_id`),
  KEY `supply_packing_receipts_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='包装单';

-- ----------------------------
-- Table structure for supply_packing_receipts_detail
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_packing_receipts_detail`;
CREATE TABLE `supply_packing_receipts_detail` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_name` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '创建人名字',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '更新人名字',
  `main_id` bigint(20) NOT NULL COMMENT '主单包装单ID',
  `status` varchar(20) COLLATE utf8_bin NOT NULL COMMENT '订单状态/新建INITED/提交SUBMITTED/审核APPROVED/驳回REJECTED',
  `code` varchar(50) COLLATE utf8_bin NOT NULL COMMENT '订单编号',
  `packing_date` datetime NOT NULL COMMENT '包装日期',
  `type` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '类型(预留)',
  `machining_center_id` bigint(20) NOT NULL COMMENT '加工中心id',
  `machining_center_code` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '加工中心编号',
  `machining_center_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '加工中心名称',
  `position_id` bigint(20) DEFAULT NULL COMMENT '仓位id',
  `position_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '仓位名称',
  `position_code` varchar(30) COLLATE utf8_bin DEFAULT NULL COMMENT '仓位code',
  `packing_rule` bigint(20) DEFAULT NULL COMMENT '包装规则ID',
  `target_material` bigint(20) NOT NULL COMMENT '目标物料ID',
  `target_material_code` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '目标物料编码',
  `target_material_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '目标物料名称',
  `target_material_unit_id` bigint(20) NOT NULL COMMENT '目标物料单位ID',
  `target_material_unit_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '目标物料单位名称',
  `target_material_unit_spec` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '目标物料单位规格',
  `packed_quantity` decimal(18,8) NOT NULL DEFAULT '0.********' COMMENT '包装数量',
  `opened_position` tinyint(1) DEFAULT '0' COMMENT '是否开启多仓位',
  `request_id` bigint(20) NOT NULL COMMENT '幂等性校验请求id',
  `remark` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '备注',
  `cost_center_id` bigint(20) DEFAULT NULL COMMENT '所属成本中心id',
  `target_material_unit_rate` decimal(18,6) DEFAULT NULL COMMENT '单位转换率',
  PRIMARY KEY (`id`),
  UNIQUE KEY `request_id` (`request_id`),
  KEY `supply_packing_receipts_detail_partner_id` (`partner_id`),
  KEY `supply_packing_receipts_detail_main_id` (`main_id`),
  KEY `supply_packing_receipts_detail_code` (`code`),
  KEY `supply_packing_receipts_detail_packing_date` (`packing_date`),
  KEY `supply_packing_receipts_detail_mc_id` (`machining_center_id`),
  KEY `supply_packing_receipts_detail_position_id` (`position_id`),
  KEY `supply_packing_receipts_detail_target_material` (`target_material`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='包装单详情表';

-- ----------------------------
-- Table structure for supply_packing_receipts_items
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_packing_receipts_items`;
CREATE TABLE `supply_packing_receipts_items` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_name` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '创建人名字',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '更新人名字',
  `main_id` bigint(20) NOT NULL COMMENT '主单包装单ID',
  `product_id` bigint(20) NOT NULL COMMENT '包装物料/耗材ID',
  `product_code` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '包装物料/耗材编码',
  `product_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '包装物料/耗材名称',
  `unit_id` bigint(20) NOT NULL COMMENT '包装物料/耗材单位ID',
  `unit_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '包装物料/耗材单位名称',
  `unit_spec` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '包装物料/耗材单位规格',
  `quantity` decimal(18,8) NOT NULL DEFAULT '0.********' COMMENT '物料/耗材数量',
  `type` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '区分物料`material`/耗材`consumable`',
  `unit_rate` decimal(18,8) DEFAULT NULL COMMENT '单位转换率',
  PRIMARY KEY (`id`),
  KEY `supply_packing_receipts_items_main_id` (`main_id`),
  KEY `supply_packing_receipts_items_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='包装物料/耗材明细条目表';

-- ----------------------------
-- Table structure for supply_packing_receipts_log
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_packing_receipts_log`;
CREATE TABLE `supply_packing_receipts_log` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '创建人名称',
  `main_id` bigint(20) DEFAULT NULL COMMENT '关联的包装单id',
  `action` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '操作状态（确认COMPLETED/作废INVALID等）',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `supply_packing_receipts_log_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='包装单操作日志表';

-- ----------------------------
-- Table structure for supply_price_adjustment
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_price_adjustment`;
CREATE TABLE `supply_price_adjustment` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_name` varchar(20) DEFAULT NULL COMMENT '创建人名字',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) DEFAULT NULL COMMENT '更新人名字',
  `code` varchar(50) DEFAULT NULL COMMENT '订单编号',
  `status` varchar(20) DEFAULT NULL COMMENT '订单状态/新建/已审核',
  `order_type` varchar(30) DEFAULT NULL COMMENT '单据类型',
  `adjust_date` datetime DEFAULT NULL COMMENT '调价日期',
  `branch_id` bigint(20) DEFAULT NULL COMMENT '门店或仓库id',
  `branch_code` varchar(15) DEFAULT NULL COMMENT '门店或仓库编号',
  `branch_name` varchar(50) DEFAULT NULL COMMENT '门店或仓库名称',
  `supplier_id` bigint(20) DEFAULT NULL COMMENT '供应商id',
  `supplier_code` varchar(15) DEFAULT NULL COMMENT '供应商编码',
  `supplier_name` varchar(50) DEFAULT NULL COMMENT '供应商名称',
  `batch_id` bigint(20) NOT NULL COMMENT '原始单据id',
  `batch_code` varchar(50) NOT NULL COMMENT '原始单据编号',
  `sum_price_tax` decimal(18,8) DEFAULT '0.********' COMMENT '总含税价',
  `adjust_sum_price_tax` decimal(18,8) DEFAULT '0.********' COMMENT '复核后总含税价',
  `sum_price` decimal(18,8) DEFAULT '0.********' COMMENT '总未税价',
  `adjust_sum_price` decimal(18,8) DEFAULT '0.********' COMMENT '复核后总未税价',
  `branch_type` varchar(20) DEFAULT NULL COMMENT '区分门店/仓库',
  `cost_trans_status` tinyint(1) DEFAULT '0' COMMENT '是否传输成本引擎 0:未传输，1:已传输',
  `cost_update` tinyint(4) DEFAULT NULL COMMENT '影响成本更新的次数，用来校验',
  `cost_center_id` bigint(20) DEFAULT NULL COMMENT '成本中心ID',
  `blending_status` varchar(20) DEFAULT NULL COMMENT '发票勾兑状态INITED/COMPLETED->待勾兑/已勾兑',
  `company_id` bigint(20) DEFAULT NULL COMMENT '公司id',
  PRIMARY KEY (`id`),
  KEY `supply_price_adjustment_adjust_date` (`adjust_date`),
  KEY `supply_price_adjustment_batch_id` (`batch_id`),
  KEY `supply_price_adjustment_code` (`code`),
  KEY `supply_price_adjustment_partner_id` (`partner_id`),
  KEY `supply_price_adjustment_receive_by` (`branch_id`),
  KEY `supply_price_adjustment_supplier_id` (`supplier_id`),
  KEY `supply_price_adjustment_company_id_index` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='调价单';

-- ----------------------------
-- Table structure for supply_price_adjustment_detail
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_price_adjustment_detail`;
CREATE TABLE `supply_price_adjustment_detail` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(20) DEFAULT NULL COMMENT '创建人名字',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) DEFAULT NULL COMMENT '更新人名字',
  `adjust_id` bigint(20) NOT NULL COMMENT '调价单id',
  `product_id` bigint(20) DEFAULT NULL COMMENT '商品id',
  `product_code` varchar(50) DEFAULT NULL COMMENT '商品code',
  `product_name` varchar(50) DEFAULT NULL COMMENT '商品名称',
  `product_category_id` bigint(20) DEFAULT NULL COMMENT '商品分类id',
  `product_type` varchar(50) DEFAULT NULL COMMENT '商品属性',
  `unit_id` bigint(20) DEFAULT NULL COMMENT '单位id(门店中为订货单位/仓库为采购单位)',
  `unit_name` varchar(50) DEFAULT NULL COMMENT '单位名称',
  `unit_spec` varchar(50) DEFAULT NULL COMMENT '单位规格',
  `quantity` decimal(18,8) DEFAULT NULL COMMENT '订货/退货数量',
  `actual_quantity` decimal(18,8) DEFAULT NULL COMMENT '实际数量',
  `tax_rate` decimal(16,4) DEFAULT NULL COMMENT '税率',
  `price` decimal(18,8) DEFAULT NULL COMMENT '未税单价',
  `adjust_price` decimal(18,8) DEFAULT NULL COMMENT '复核后未税单价',
  `price_tax` decimal(18,8) DEFAULT NULL COMMENT '含税单价',
  `adjust_price_tax` decimal(18,8) DEFAULT NULL COMMENT '复核后含税单价',
  `sum_price` decimal(18,8) DEFAULT NULL COMMENT '未税总价',
  `adjust_sum_price` decimal(18,8) DEFAULT NULL COMMENT '复核后未税总价',
  `sum_price_tax` decimal(18,8) DEFAULT NULL COMMENT '含税总价',
  `adjust_sum_price_tax` decimal(18,8) DEFAULT NULL COMMENT '复核后含税总价',
  PRIMARY KEY (`id`),
  KEY `supply_price_adjustment_detail_adjust_id` (`adjust_id`),
  KEY `supply_price_adjustment_detail_partner_id` (`partner_id`),
  KEY `supply_price_adjustment_detail_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='调价单明细';

-- ----------------------------
-- Table structure for supply_processing_cost_allocation
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_processing_cost_allocation`;
CREATE TABLE `supply_processing_cost_allocation` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_name` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '创建人名字',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '更新人名字',
  `status` varchar(20) COLLATE utf8_bin NOT NULL COMMENT '单据状态 INITED/SUCCESS/FAILED',
  `machining_center_id` bigint(20) NOT NULL COMMENT '加工中心id',
  `position_id` bigint(20) DEFAULT NULL COMMENT '仓位id',
  `product_id` bigint(20) DEFAULT NULL COMMENT '加工物料ID',
  `processing_unit_id` bigint(20) NOT NULL COMMENT '加工时单位ID',
  `processing_unit_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '加工时单位名称',
  `processing_quantity` decimal(18,8) DEFAULT '0.********' COMMENT '加工物料数量',
  `accounting_unit_id` bigint(20) NOT NULL COMMENT '核算单位ID',
  `accounting_unit_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '核算单位名称',
  `accounting_quantity` decimal(18,8) DEFAULT '0.********' COMMENT '加工物料核算数量',
  `amounts` decimal(18,8) DEFAULT '0.********' COMMENT '总加工费用(核算单位)',
  `cost_rcpt_id` bigint(20) NOT NULL COMMENT '读取的加工费用单id',
  `cost_center_id` bigint(20) DEFAULT NULL COMMENT '成本中心ID',
  `remark` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '备注',
  `deleted` tinyint(1) DEFAULT '0' COMMENT '软删除标记',
  PRIMARY KEY (`id`),
  KEY `supply_processing_cost_allocation_mc_id` (`machining_center_id`),
  KEY `supply_processing_cost_allocation_partner_id` (`partner_id`),
  KEY `supply_processing_cost_allocation_status` (`status`),
  KEY `supply_processing_cost_allocation_cost_rcpt_id` (`cost_rcpt_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='加工费用分摊表';

-- ----------------------------
-- Table structure for supply_processing_cost_detail
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_processing_cost_detail`;
CREATE TABLE `supply_processing_cost_detail` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_name` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '创建人名字',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '更新人名字',
  `status` varchar(20) COLLATE utf8_bin NOT NULL COMMENT '订单状态/新建INITED/提交SUBMITTED/审核APPROVED/驳回REJECTED',
  `code` varchar(50) COLLATE utf8_bin NOT NULL COMMENT '订单编号',
  `month` varchar(10) COLLATE utf8_bin NOT NULL COMMENT '生效月份',
  `type` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '类型(预留)',
  `machining_center_id` bigint(20) NOT NULL COMMENT '加工中心id',
  `machining_center_code` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '加工中心编号',
  `machining_center_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '加工中心名称',
  `position_id` bigint(20) DEFAULT NULL COMMENT '仓位id',
  `position_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '仓位名称',
  `position_code` varchar(30) COLLATE utf8_bin DEFAULT NULL COMMENT '仓位code',
  `unit_id` bigint(20) NOT NULL COMMENT '目标物料单位ID',
  `unit_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '目标物料单位名称',
  `unit_spec` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '目标物料单位规格',
  `unit_cost` decimal(18,8) DEFAULT '0.********' COMMENT '当月加工单价',
  `quantity` decimal(18,8) DEFAULT '0.********' COMMENT '计价数量',
  `opened_position` tinyint(1) DEFAULT '0' COMMENT '是否开启多仓位',
  `request_id` bigint(20) NOT NULL COMMENT '幂等性校验请求id',
  `remark` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '备注',
  `cost_center_id` bigint(20) DEFAULT NULL COMMENT '所属成本中心id',
  `process_status` varchar(30) COLLATE utf8_bin DEFAULT NULL COMMENT '处理状态 标记分摊计算状态：INITED/PROCESSING/SUCCESS',
  `period_id` bigint(20) DEFAULT NULL COMMENT '账期ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `request_id` (`request_id`),
  KEY `supply_processing_cost_detail_code` (`code`),
  KEY `supply_processing_cost_detail_mc_id` (`machining_center_id`),
  KEY `supply_processing_cost_detail_partner_id` (`partner_id`),
  KEY `supply_processing_cost_detail_position_id` (`position_id`),
  KEY `supply_processing_cost_detail_month` (`month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='加工费用表';

-- ----------------------------
-- Table structure for supply_processing_cost_log
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_processing_cost_log`;
CREATE TABLE `supply_processing_cost_log` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '创建人名称',
  `main_id` bigint(20) DEFAULT NULL COMMENT '关联的加工单id',
  `action` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '操作状态',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `supply_processing_cost_log_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='加工费用操作日志表';

-- ----------------------------
-- Table structure for supply_processing_receipts
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_processing_receipts`;
CREATE TABLE `supply_processing_receipts` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_name` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '创建人名字',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '更新人名字',
  `code` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '订单编号',
  `status` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '订单状态/新建INITED/提交SUBMITTED/审核APPROVED/驳回REJECTED',
  `type` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '类型(预留)',
  `processing_date` datetime DEFAULT NULL COMMENT '加工日期',
  `machining_center_id` bigint(20) DEFAULT NULL COMMENT '加工中心id',
  `position_id` bigint(20) DEFAULT NULL COMMENT '仓位id',
  `process_status` varchar(30) COLLATE utf8_bin DEFAULT NULL COMMENT '单据轮转状态(预留)',
  `remark` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '备注',
  `opened_position` tinyint(1) DEFAULT '0' COMMENT '是否开启多仓位',
  `request_id` bigint(20) NOT NULL COMMENT '幂等性校验请求id',
  `processing_rule` bigint(20) DEFAULT NULL COMMENT '加工规则ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `request_id` (`request_id`),
  KEY `supply_processing_receipts_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='加工单';

-- ----------------------------
-- Table structure for supply_processing_receipts_detail
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_processing_receipts_detail`;
CREATE TABLE `supply_processing_receipts_detail` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_name` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '创建人名字',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '更新人名字',
  `main_id` bigint(20) NOT NULL COMMENT '主单包装单ID',
  `status` varchar(20) COLLATE utf8_bin NOT NULL COMMENT '订单状态/新建INITED/提交SUBMITTED/审核APPROVED/驳回REJECTED',
  `code` varchar(50) COLLATE utf8_bin NOT NULL COMMENT '订单编号',
  `processing_date` datetime NOT NULL COMMENT '包装日期',
  `type` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '类型(预留)',
  `machining_center_id` bigint(20) NOT NULL COMMENT '加工中心id',
  `machining_center_code` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '加工中心编号',
  `machining_center_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '加工中心名称',
  `position_id` bigint(20) DEFAULT NULL COMMENT '仓位id',
  `position_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '仓位名称',
  `position_code` varchar(30) COLLATE utf8_bin DEFAULT NULL COMMENT '仓位code',
  `processing_rule` bigint(20) DEFAULT NULL COMMENT '加工规则ID',
  `target_material` bigint(20) NOT NULL COMMENT '目标物料ID',
  `target_material_code` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '目标物料编码',
  `target_material_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '目标物料名称',
  `target_material_unit_id` bigint(20) NOT NULL COMMENT '目标物料单位ID',
  `target_material_unit_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '目标物料单位名称',
  `target_material_unit_spec` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '目标物料单位规格',
  `theory_output_rate` decimal(16,4) DEFAULT '0.0000' COMMENT '理论产出率',
  `actual_output_rate` decimal(16,4) DEFAULT '0.0000' COMMENT '实际产出率',
  `actual_output_quantity` decimal(18,8) DEFAULT '0.********' COMMENT '实际产出数量',
  `opened_position` tinyint(1) DEFAULT '0' COMMENT '是否开启多仓位',
  `request_id` bigint(20) NOT NULL COMMENT '幂等性校验请求id',
  `remark` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '备注',
  `cost_center_id` bigint(20) DEFAULT NULL COMMENT '所属成本中心id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `request_id` (`request_id`),
  KEY `supply_processing_receipts_detail_code` (`code`),
  KEY `supply_processing_receipts_detail_main_id` (`main_id`),
  KEY `supply_processing_receipts_detail_mc_id` (`machining_center_id`),
  KEY `supply_processing_receipts_detail_pro_date` (`processing_date`),
  KEY `supply_processing_receipts_detail_partner_id` (`partner_id`),
  KEY `supply_processing_receipts_detail_position_id` (`position_id`),
  KEY `supply_processing_receipts_detail_target_material` (`target_material`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='加工单详情表';

-- ----------------------------
-- Table structure for supply_processing_receipts_log
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_processing_receipts_log`;
CREATE TABLE `supply_processing_receipts_log` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '创建人名称',
  `main_id` bigint(20) DEFAULT NULL COMMENT '关联的加工单id',
  `action` varchar(50) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '操作状态',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `supply_processing_receipts_log_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='加工单操作日志表';

-- ----------------------------
-- Table structure for supply_processing_receipts_products
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_processing_receipts_products`;
CREATE TABLE `supply_processing_receipts_products` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_name` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '创建人名字',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) COLLATE utf8_bin DEFAULT NULL COMMENT '更新人名字',
  `main_id` bigint(20) NOT NULL COMMENT '主单包装单ID',
  `product_id` bigint(20) NOT NULL COMMENT '加工物料ID',
  `product_code` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '加工物料编号',
  `product_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '加工物料名称',
  `unit_id` bigint(20) NOT NULL COMMENT '加工物料单位ID',
  `unit_name` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '加工物料单位名称',
  `unit_spec` varchar(50) COLLATE utf8_bin DEFAULT NULL COMMENT '加工物料单位规格',
  `material_rate` decimal(16,4) DEFAULT '0.0000' COMMENT '物料占比',
  `actual_quantity` decimal(18,8) NOT NULL DEFAULT '0.********' COMMENT '实际加工物料数量',
  `unit_rate` decimal(18,8) DEFAULT NULL COMMENT '单位转换率',
  PRIMARY KEY (`id`),
  KEY `supply_processing_receipts_products_main_id` (`main_id`),
  KEY `supply_processing_receipts_products_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='加工物料明细表';

-- ----------------------------
-- Table structure for supply_receiving_diff
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_receiving_diff`;
CREATE TABLE `supply_receiving_diff` (
  `id` bigint(20) NOT NULL,
  `code` varchar(50) DEFAULT NULL,
  `received_by` bigint(20) DEFAULT NULL,
  `delivery_by` bigint(20) DEFAULT NULL,
  `receiving_id` bigint(20) DEFAULT NULL,
  `receiving_code` varchar(50) DEFAULT NULL,
  `master_id` bigint(20) DEFAULT NULL,
  `master_code` varchar(50) DEFAULT NULL,
  `status` varchar(10) DEFAULT NULL,
  `demand_date` datetime DEFAULT NULL,
  `delivery_date` datetime DEFAULT NULL,
  `receive_date` datetime DEFAULT NULL,
  `reason_type` varchar(50) DEFAULT NULL,
  `review_by` bigint(20) DEFAULT NULL,
  `store_secondary_id` varchar(50) DEFAULT NULL,
  `inventory_status` varchar(10) DEFAULT NULL,
  `inventory_req_id` bigint(50) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `reject_reason` varchar(255) DEFAULT NULL,
  `attachments` text,
  `product_nums` bigint(20) DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `received_type` varchar(50) DEFAULT NULL,
  `request_id` bigint(20) DEFAULT NULL,
  `logistics_type` varchar(50) DEFAULT NULL,
  `send_type` varchar(50) DEFAULT NULL,
  `branch_type` varchar(255) DEFAULT NULL,
  `sub_receive_by` bigint(20) DEFAULT NULL COMMENT '子账户接收方',
  `auto_confirm_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `request_id` (`request_id`),
  KEY `supply_receiving_diff_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_receiving_diff_log
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_receiving_diff_log`;
CREATE TABLE `supply_receiving_diff_log` (
  `id` bigint(20) NOT NULL,
  `doc_id` bigint(20) DEFAULT NULL,
  `operation` varchar(30) DEFAULT NULL,
  `success` tinyint(255) DEFAULT NULL,
  `created_by` bigint(50) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supply_receiving_diff_log_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_receiving_diff_product
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_receiving_diff_product`;
CREATE TABLE `supply_receiving_diff_product` (
  `id` bigint(20) NOT NULL,
  `receiving_id` bigint(20) DEFAULT NULL,
  `received_by` bigint(20) DEFAULT NULL,
  `diff_id` bigint(20) DEFAULT NULL,
  `product_id` bigint(20) DEFAULT NULL,
  `product_code` varchar(50) DEFAULT NULL,
  `product_name` varchar(50) CHARACTER SET utf8 DEFAULT NULL,
  `material_number` varchar(25) DEFAULT NULL,
  `unit_id` bigint(20) DEFAULT NULL,
  `unit_name` varchar(50) DEFAULT NULL,
  `unit_spec` varchar(50) DEFAULT NULL,
  `unit_rate` decimal(18,8) DEFAULT NULL,
  `accounting_unit_id` bigint(20) DEFAULT NULL,
  `accounting_unit_name` varchar(50) DEFAULT NULL,
  `accounting_unit_spec` varchar(50) DEFAULT NULL,
  `received_quantity` decimal(18,8) DEFAULT NULL,
  `received_accounting_quantity` decimal(18,8) DEFAULT NULL,
  `confirmed_quantity` decimal(18,8) DEFAULT NULL,
  `confirmed_accounting_quantity` decimal(18,8) DEFAULT NULL,
  `diff_quantity` decimal(18,8) DEFAULT NULL,
  `diff_accounting_quantity` decimal(18,8) DEFAULT NULL,
  `s_diff_quantity` decimal(18,8) DEFAULT NULL,
  `s_diff_accounting_quantity` decimal(18,8) DEFAULT NULL,
  `d_diff_quantity` decimal(18,8) DEFAULT NULL,
  `d_diff_accounting_quantity` decimal(18,8) DEFAULT NULL,
  `reason_type` varchar(10) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `cost_price` decimal(18,8) DEFAULT NULL COMMENT '不含税价',
  `tax_price` decimal(18,8) DEFAULT NULL COMMENT '含税价',
  `tax_rate` decimal(18,8) DEFAULT NULL COMMENT '税率',
  `real_quantity` float DEFAULT '0' COMMENT '供应商对账平台使用的退货数量',
  `sub_receive_by` bigint(20) DEFAULT NULL COMMENT '子账户接收方',
  PRIMARY KEY (`id`),
  KEY `supply_receiving_p_partner_id` (`partner_id`),
  KEY `supply_receiving_diff_p_index2` (`received_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_receiving_log
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_receiving_log`;
CREATE TABLE `supply_receiving_log` (
  `id` bigint(20) NOT NULL,
  `doc_id` bigint(20) DEFAULT NULL,
  `operation` varchar(30) DEFAULT NULL,
  `success` tinyint(255) DEFAULT NULL,
  `created_by` varchar(50) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supply_receiving_diff_log_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_return
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_return`;
CREATE TABLE `supply_return` (
  `id` bigint(20) NOT NULL,
  `code` varchar(50) DEFAULT NULL,
  `return_by` bigint(20) DEFAULT NULL,
  `return_to` bigint(20) DEFAULT NULL,
  `status` varchar(10) DEFAULT NULL,
  `return_number` varchar(50) DEFAULT NULL,
  `return_delivery_number` varchar(20) DEFAULT NULL,
  `review_by` bigint(20) DEFAULT NULL,
  `return_date` datetime DEFAULT NULL,
  `return_delivery_date` datetime DEFAULT NULL,
  `return_reason` varchar(50) DEFAULT NULL,
  `type` varchar(10) DEFAULT NULL,
  `sub_type` varchar(10) DEFAULT NULL,
  `store_secondary_id` varchar(20) DEFAULT NULL,
  `inventory_status` varchar(50) DEFAULT NULL,
  `inventory_req_id` bigint(20) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `reject_reason` varchar(255) DEFAULT NULL,
  `attachments` text,
  `product_nums` bigint(20) DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `logistics_type` varchar(50) DEFAULT NULL,
  `source_id` bigint(20) DEFAULT NULL,
  `source_code` varchar(25) DEFAULT NULL,
  `request_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supply_return_partner_id` (`partner_id`),
  KEY `index1` (`code`) USING BTREE,
  KEY `index2` (`return_by`) USING BTREE,
  KEY `index3` (`status`) USING BTREE,
  KEY `index4` (`logistics_type`) USING BTREE,
  KEY `index5` (`type`) USING BTREE,
  KEY `index6` (`sub_type`) USING BTREE,
  KEY `index7` (`source_code`) USING BTREE,
  KEY `index8` (`source_id`) USING BTREE,
  KEY `index9` (`return_date`) USING BTREE,
  KEY `index10` (`return_delivery_date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_return_log
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_return_log`;
CREATE TABLE `supply_return_log` (
  `id` bigint(20) NOT NULL,
  `doc_id` bigint(20) DEFAULT NULL,
  `operation` varchar(30) DEFAULT NULL,
  `success` tinyint(255) DEFAULT NULL,
  `created_by` bigint(50) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supply_return_log_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_return_product
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_return_product`;
CREATE TABLE `supply_return_product` (
  `id` bigint(20) NOT NULL,
  `return_id` bigint(20) DEFAULT NULL,
  `return_by` bigint(20) DEFAULT NULL,
  `return_to` bigint(20) DEFAULT NULL,
  `material_number` varchar(25) DEFAULT NULL,
  `product_id` bigint(20) DEFAULT NULL,
  `product_code` varchar(50) DEFAULT NULL,
  `product_name` varchar(50) DEFAULT NULL,
  `unit_id` bigint(20) DEFAULT NULL,
  `unit_name` varchar(50) DEFAULT NULL,
  `unit_spec` varchar(50) DEFAULT NULL,
  `unit_rate` decimal(18,8) DEFAULT NULL,
  `accounting_unit_id` bigint(20) DEFAULT NULL,
  `accounting_unit_name` varchar(50) DEFAULT NULL,
  `accounting_unit_spec` varchar(50) DEFAULT NULL,
  `quantity` decimal(18,8) DEFAULT NULL,
  `accounting_quantity` decimal(18,8) DEFAULT NULL,
  `confirmed_quantity` decimal(18,8) DEFAULT NULL,
  `accounting_confirmed_quantity` decimal(18,8) DEFAULT NULL,
  `returned_quantity` decimal(18,8) DEFAULT NULL,
  `accounting_returned_quantity` decimal(18,8) DEFAULT NULL,
  `is_confirmed` tinyint(1) DEFAULT NULL,
  `inventory_status` varchar(10) DEFAULT NULL,
  `inventory_req_id` bigint(20) DEFAULT NULL,
  `return_date` datetime DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `status` varchar(10) DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `price` decimal(18,8) DEFAULT NULL,
  `real_quantity` float DEFAULT '0' COMMENT '供应商对账平台使用的退货数量。',
  `tax_rate` decimal(16,3) DEFAULT NULL COMMENT '税率',
  `price_tax` decimal(18,8) DEFAULT NULL COMMENT '含税单价',
  PRIMARY KEY (`id`),
  KEY `supply_return_p_partner_id` (`partner_id`),
  KEY `supply_return_p_return_by` (`return_by`),
  KEY `supply_return_p_return_date` (`return_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_st_doc
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_st_doc`;
CREATE TABLE `supply_st_doc` (
  `id` bigint(20) NOT NULL,
  `branch_batch_id` bigint(20) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `schedule_id` bigint(20) DEFAULT NULL,
  `branch_id` bigint(20) DEFAULT NULL,
  `batch_id` bigint(20) DEFAULT NULL,
  `code` varchar(50) DEFAULT NULL,
  `type` varchar(10) DEFAULT NULL,
  `target_date` datetime DEFAULT NULL,
  `status` varchar(10) DEFAULT NULL,
  `process_status` varchar(10) DEFAULT NULL,
  `forecasting` tinyint(1) DEFAULT NULL,
  `forecasting_time` datetime DEFAULT NULL,
  `calculate_inventory` tinyint(1) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `_branch_batch_branch_uc` (`branch_batch_id`,`branch_id`),
  KEY `supply_stdoc_branch_id` (`branch_id`),
  KEY `supply_stdoc_partner_id` (`partner_id`),
  KEY `supply_stdoc_process_status` (`process_status`,`updated_at`),
  KEY `supply_stdoc_status` (`updated_at`),
  KEY `supply_stdoc_target_date` (`target_date`),
  KEY `supply_stdoc_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_st_doc_details
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_st_doc_details`;
CREATE TABLE `supply_st_doc_details` (
  `id` bigint(20) NOT NULL,
  `branch_batch_id` bigint(20) DEFAULT NULL,
  `doc_id` bigint(20) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `schedule_id` bigint(20) DEFAULT NULL,
  `branch_id` bigint(20) DEFAULT NULL,
  `branch_type` varchar(20) DEFAULT NULL,
  `batch_id` bigint(20) DEFAULT NULL,
  `code` varchar(50) DEFAULT NULL,
  `schedule_code` varchar(50) DEFAULT NULL,
  `type` varchar(10) DEFAULT NULL,
  `result_type` varchar(10) DEFAULT NULL,
  `target_date` datetime DEFAULT NULL,
  `status` varchar(10) DEFAULT NULL,
  `process_status` varchar(10) DEFAULT NULL,
  `store_secondary_id` varchar(50) DEFAULT NULL,
  `review_by` bigint(20) DEFAULT NULL,
  `calculate_inventory` tinyint(1) DEFAULT NULL,
  `forecasting` tinyint(1) DEFAULT NULL,
  `forecasting_time` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `st_diff_flag` int(11) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  `schedule_name` varchar(50) DEFAULT NULL,
  `diff_err_message` text,
  `month_err_message` text,
  `original_code` varchar(50) DEFAULT NULL,
  `original_doc_id` bigint(20) DEFAULT NULL,
  `is_recreate` tinyint(1) DEFAULT '0',
  `recreate_code` varchar(50) DEFAULT NULL,
  `recreate_doc_id` bigint(20) DEFAULT NULL,
  `submit_name` varchar(50) DEFAULT NULL,
  `approve_name` varchar(50) DEFAULT NULL,
  `stocktake_type` varchar(50) DEFAULT NULL,
  `request_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_supply_st_doc_details_unique` (`request_id`),
  KEY `supply_stdocd_branch_id` (`branch_id`),
  KEY `supply_stdocd_branch_type` (`branch_type`),
  KEY `supply_stdocd_code` (`code`),
  KEY `supply_stdocd_doc_id` (`doc_id`),
  KEY `supply_stdocd_partner_id` (`partner_id`),
  KEY `supply_stdocd_sch_code` (`schedule_code`),
  KEY `supply_stdocd_sch_schedule_id` (`schedule_id`),
  KEY `supply_stdocd_sch_stocktake_type` (`stocktake_type`),
  KEY `supply_stdocd_status` (`status`),
  KEY `supply_stdocd_target_date` (`target_date`),
  KEY `supply_stdocd_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_st_doc_log
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_st_doc_log`;
CREATE TABLE `supply_st_doc_log` (
  `id` bigint(20) NOT NULL,
  `doc_id` bigint(20) DEFAULT NULL,
  `doc_status` varchar(25) DEFAULT NULL,
  `reason` varchar(255) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supply_st_doc_log_doc_id` (`doc_id`),
  KEY `supply_st_doc_log_partner_id` (`partner_id`),
  KEY `supply_st_doc_log_doc_status` (`doc_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_st_month_message
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_st_month_message`;
CREATE TABLE `supply_st_month_message` (
  `id` bigint(20) NOT NULL,
  `doc_id` bigint(20) DEFAULT NULL,
  `target_date` datetime DEFAULT NULL,
  `status` varchar(10) DEFAULT NULL,
  `month_message` mediumtext,
  `diff_message` mediumtext,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supply_st_month_message_partner_id` (`partner_id`),
  KEY `supply_st_month_message_target_date` (`target_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_st_position_product
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_st_position_product`;
CREATE TABLE `supply_st_position_product` (
  `id` bigint(20) NOT NULL,
  `doc_id` bigint(20) DEFAULT NULL,
  `position_id` bigint(20) DEFAULT NULL,
  `product_id` bigint(20) DEFAULT NULL,
  `quantity` decimal(18,8) DEFAULT NULL,
  `accounting_quantity` decimal(18,8) DEFAULT NULL,
  `inventory_quantity` decimal(18,8) DEFAULT NULL,
  `diff_quantity` decimal(18,8) DEFAULT NULL,
  `unit_rate` decimal(18,8) DEFAULT NULL,
  `unit_id` bigint(20) DEFAULT NULL,
  `unit_name` varchar(50) DEFAULT NULL,
  `unit_spec` varchar(50) DEFAULT NULL,
  `accounting_unit_id` bigint(20) DEFAULT NULL,
  `accounting_unit_name` varchar(50) DEFAULT NULL,
  `accounting_unit_spec` varchar(50) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supply_st_position_product_doc_id` (`doc_id`),
  KEY `supply_st_position_partner_id` (`partner_id`),
  KEY `supply_st_position_product_position_id` (`position_id`),
  KEY `supply_st_position_product_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_st_product
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_st_product`;
CREATE TABLE `supply_st_product` (
  `id` bigint(20) NOT NULL,
  `doc_id` bigint(20) DEFAULT NULL,
  `product_id` bigint(20) DEFAULT NULL,
  `product_code` varchar(50) DEFAULT NULL,
  `product_name` varchar(50) DEFAULT NULL,
  `unit_id` bigint(20) DEFAULT NULL,
  `unit_name` varchar(50) DEFAULT NULL,
  `unit_spec` varchar(50) DEFAULT NULL,
  `accounting_unit_id` bigint(20) DEFAULT NULL,
  `accounting_unit_name` varchar(50) DEFAULT NULL,
  `accounting_unit_spec` varchar(50) DEFAULT NULL,
  `quantity` decimal(18,8) DEFAULT NULL,
  `accounting_quantity` decimal(18,8) DEFAULT NULL,
  `inventory_quantity` decimal(18,8) DEFAULT NULL,
  `diff_quantity` decimal(18,8) DEFAULT NULL,
  `unit_diff_quantity` decimal(18,8) DEFAULT NULL,
  `unit_rate` decimal(18,8) DEFAULT NULL,
  `material_number` varchar(25) DEFAULT NULL,
  `item_number` int(11) DEFAULT NULL,
  `ignored` tinyint(1) DEFAULT NULL,
  `deleted` tinyint(1) DEFAULT NULL,
  `is_system` tinyint(1) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `status` varchar(10) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `extends` varchar(255) DEFAULT NULL,
  `storage_type` varchar(10) DEFAULT NULL,
  `branch_id` bigint(20) DEFAULT NULL,
  `target_date` datetime DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `is_pda` tinyint(1) DEFAULT NULL,
  `is_empty` tinyint(1) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  `tag_quantity` decimal(18,8) DEFAULT NULL,
  `is_bom` tinyint(1) DEFAULT '0',
  `convert_accounting_quantity` decimal(18,8) DEFAULT NULL,
  `is_null` tinyint(1) DEFAULT '0',
  `position_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supply_stdocp_partner_id` (`partner_id`),
  KEY `supply_stdocp_doc_id` (`doc_id`),
  KEY `supply_stdocp_doc_target_date` (`target_date`),
  KEY `supply_stdocp_product_id` (`product_id`),
  KEY `supply_st_product_position_id` (`position_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_st_product_import_record
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_st_product_import_record`;
CREATE TABLE `supply_st_product_import_record` (
  `id` bigint(20) NOT NULL,
  `status` varchar(30) DEFAULT NULL COMMENT '导入记录状态',
  `doc_id` bigint(20) DEFAULT NULL COMMENT '盘点单id',
  `filename` varchar(100) DEFAULT NULL COMMENT '导入文件名',
  `file_data` mediumtext COMMENT '导入Excel数据',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(20) DEFAULT NULL COMMENT '创建人名字',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) DEFAULT NULL COMMENT '更新人名字',
  `partner_id` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supply_st_product_import_record_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='盘点单商品导入记录表';

-- ----------------------------
-- Table structure for supply_st_product_tag_name
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_st_product_tag_name`;
CREATE TABLE `supply_st_product_tag_name` (
  `id` bigint(20) NOT NULL,
  `doc_id` bigint(20) DEFAULT NULL,
  `stp_id` bigint(20) DEFAULT NULL,
  `product_id` bigint(20) DEFAULT NULL,
  `tag_name` varchar(50) DEFAULT NULL,
  `tag_quantity` decimal(18,8) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `accounting_quantity` decimal(18,8) DEFAULT NULL,
  `unit_id` bigint(20) DEFAULT NULL,
  `unit_name` varchar(50) DEFAULT NULL,
  `unit_spec` varchar(50) DEFAULT NULL,
  `unit_rate` decimal(18,8) DEFAULT NULL,
  `accounting_unit_id` bigint(20) DEFAULT NULL,
  `accounting_unit_name` varchar(50) DEFAULT NULL,
  `accounting_unit_spec` varchar(50) DEFAULT NULL,
  `tag_id` bigint(20) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supply_stdocp_doc_id` (`doc_id`),
  KEY `supply_stdocp_partner_id` (`partner_id`),
  KEY `supply_stdocp_stp_id_tag_name` (`stp_id`,`tag_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_st_product_tags
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_st_product_tags`;
CREATE TABLE `supply_st_product_tags` (
  `id` bigint(20) NOT NULL,
  `name` varchar(50) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  `branch_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supply_st_product_tags_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_store_self_picking
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_store_self_picking`;
CREATE TABLE `supply_store_self_picking` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_name` varchar(20) DEFAULT NULL COMMENT '创建人名字',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) DEFAULT NULL COMMENT '更新人名字',
  `code` varchar(50) DEFAULT NULL COMMENT '订单编号',
  `status` varchar(20) DEFAULT NULL COMMENT '单据业务状态(INITED,SUBMITTED,REJECTED,APPROVED)',
  `process_status` varchar(20) DEFAULT NULL COMMENT '操作状态*预留',
  `order_date` datetime DEFAULT NULL COMMENT '自采日期',
  `branch_id` bigint(20) NOT NULL COMMENT '门店id',
  `branch_code` varchar(50) DEFAULT NULL COMMENT '门店编号',
  `branch_name` varchar(50) DEFAULT NULL COMMENT '门店名称',
  `reason` varchar(20) DEFAULT NULL COMMENT '原因',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `total_amount` decimal(18,8) DEFAULT '0.********' COMMENT '合计金额',
  `branch_type` varchar(30) DEFAULT NULL COMMENT '区分门店/加工中心-STORE/MACHINING_CENTER',
  `request_id` bigint(20) DEFAULT NULL,
  `attachments` text COMMENT ' 附件 "[ats1, ats2,...]"',
  PRIMARY KEY (`id`),
  UNIQUE KEY `supply_store_self_picking_request_id_uindex` (`request_id`),
  KEY `supply_store_self_picking_code` (`code`),
  KEY `supply_store_self_picking_date` (`order_date`),
  KEY `supply_store_self_picking_partner_id` (`partner_id`),
  KEY `supply_store_self_picking_receive_by` (`branch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门店自采单';

-- ----------------------------
-- Table structure for supply_store_self_picking_log
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_store_self_picking_log`;
CREATE TABLE `supply_store_self_picking_log` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(50) DEFAULT NULL COMMENT '创建人名称',
  `main_id` bigint(20) DEFAULT NULL COMMENT '关联的自采单id',
  `action` varchar(50) DEFAULT NULL COMMENT '操作状态',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `supply_store_self_picking_log_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门店自采单日志表';

-- ----------------------------
-- Table structure for supply_store_self_picking_product
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_store_self_picking_product`;
CREATE TABLE `supply_store_self_picking_product` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(20) DEFAULT NULL COMMENT '创建人名字',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) DEFAULT NULL COMMENT '更新人名字',
  `main_id` bigint(20) DEFAULT NULL COMMENT '关联的采购单id',
  `product_id` bigint(20) DEFAULT NULL COMMENT '商品id',
  `product_code` varchar(50) DEFAULT NULL COMMENT '商品code',
  `product_name` varchar(50) DEFAULT NULL COMMENT '商品名称',
  `product_spec` varchar(30) DEFAULT NULL COMMENT '商品规格',
  `unit_id` bigint(20) DEFAULT NULL COMMENT '单位id',
  `unit_name` varchar(50) DEFAULT NULL COMMENT '单位名称',
  `unit_spec` varchar(50) DEFAULT NULL COMMENT '单位规格',
  `quantity` decimal(18,8) DEFAULT NULL COMMENT '数量',
  `amount` decimal(18,8) DEFAULT NULL COMMENT '金额(单价*数量)',
  `price` decimal(18,8) DEFAULT NULL COMMENT '单价',
  `unit_rate` decimal(18,8) DEFAULT NULL COMMENT '单位转换率',
  `tax_rate` decimal(18,8) DEFAULT NULL COMMENT '税率',
  PRIMARY KEY (`id`),
  KEY `self_picking_product_main_id` (`main_id`),
  KEY `self_picking_product_partner_id` (`partner_id`),
  KEY `self_picking_product_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门店自采单商品明细';

-- ----------------------------
-- Table structure for supply_transfer
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_transfer`;
CREATE TABLE `supply_transfer` (
  `id` bigint(20) NOT NULL,
  `master_id` bigint(20) DEFAULT NULL,
  `code` varchar(50) DEFAULT NULL,
  `type` varchar(25) DEFAULT NULL COMMENT '调拨类型(自动AUTO/手动MANUAL)',
  `sub_type` varchar(25) DEFAULT NULL COMMENT '区分内部`INTERNAL`/外部`EXTERNAL`调拨',
  `reason_type` varchar(25) DEFAULT NULL,
  `receiving_store` bigint(20) DEFAULT NULL,
  `shipping_store` bigint(20) DEFAULT NULL,
  `transfer_date` datetime DEFAULT NULL COMMENT '调拨日期',
  `status` varchar(10) DEFAULT NULL,
  `process_status` varchar(10) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `extends` varchar(255) DEFAULT NULL,
  `request_id` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `auto_confirm` tinyint(1) DEFAULT '0',
  `branch_type` varchar(30) DEFAULT NULL COMMENT '组织类型，区分门店/仓库/加工中心',
  `receiving_position` bigint(20) DEFAULT NULL COMMENT '接收仓位',
  `shipping_position` bigint(20) DEFAULT NULL COMMENT '调出仓位',
  `sub_account_type` varchar(20) DEFAULT NULL COMMENT '子账户类型，如果开启了多仓位值为"position"没配置为None',
  PRIMARY KEY (`id`),
  KEY `supply_transfer_branch_type` (`branch_type`),
  KEY `supply_transfer_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_transfer_detail
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_transfer_detail`;
CREATE TABLE `supply_transfer_detail` (
  `id` bigint(20) NOT NULL,
  `transfer_id` bigint(20) DEFAULT NULL,
  `master_id` bigint(20) DEFAULT NULL,
  `code` varchar(50) DEFAULT NULL,
  `type` varchar(25) DEFAULT NULL COMMENT '调拨类型(自动AUTO/手动MANUAL)',
  `sub_type` varchar(25) DEFAULT NULL COMMENT '区分内部`INTERNAL`/外部`EXTERNAL`调拨',
  `reason_type` varchar(25) DEFAULT NULL,
  `transfer_order_number` bigint(20) DEFAULT NULL,
  `transfer_date` datetime DEFAULT NULL,
  `shipping_store` bigint(20) DEFAULT NULL,
  `shipper` bigint(20) DEFAULT NULL,
  `shipping_date` datetime DEFAULT NULL,
  `shipping_store_name` varchar(50) DEFAULT NULL,
  `receiving_store` bigint(20) DEFAULT NULL,
  `receiver` bigint(20) DEFAULT NULL,
  `receiving_date` datetime DEFAULT NULL,
  `receiving_store_name` varchar(50) DEFAULT NULL,
  `status` varchar(10) DEFAULT NULL,
  `process_status` varchar(10) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `extends` varchar(255) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `request_id` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  `auto_confirm` tinyint(1) DEFAULT '0',
  `branch_type` varchar(30) DEFAULT NULL COMMENT '组织类型，区分门店/仓库/加工中心',
  `receiving_position` bigint(20) DEFAULT NULL COMMENT '接收仓位id',
  `receiving_position_name` varchar(50) DEFAULT NULL COMMENT '接收仓位名称',
  `shipping_position` bigint(20) DEFAULT NULL COMMENT '调出仓位id',
  `shipping_position_name` varchar(50) DEFAULT NULL COMMENT '调出仓位名称',
  `sub_account_type` varchar(20) DEFAULT NULL COMMENT '子账户类型，如果开启了多仓位值为"position"没配置为None',
  PRIMARY KEY (`id`),
  KEY `supply_trans_detail_index10` (`branch_type`),
  KEY `supply_trans_detail_index2` (`transfer_id`,`process_status`),
  KEY `supply_trans_detail_index3` (`partner_id`),
  KEY `supply_trans_detail_index4` (`transfer_date`),
  KEY `supply_trans_detail_index5` (`shipping_store`),
  KEY `supply_trans_detail_index6` (`receiving_store`),
  KEY `supply_trans_detail_index7` (`status`),
  KEY `supply_trans_detail_index8` (`code`),
  KEY `supply_trans_detail_index11` (`receiving_position`),
  KEY `supply_trans_detail_index12` (`shipping_position`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_transfer_log
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_transfer_log`;
CREATE TABLE `supply_transfer_log` (
  `id` bigint(20) NOT NULL,
  `transfer_id` bigint(20) DEFAULT NULL,
  `transfer_status` varchar(10) DEFAULT NULL,
  `reason` varchar(255) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supply_trans_log_index1` (`transfer_id`),
  KEY `supply_trans_log_index2` (`transfer_status`),
  KEY `supply_trans_log_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for supply_transfer_product
-- ----------------------------
-- DROP TABLE IF EXISTS `supply_transfer_product`;
CREATE TABLE `supply_transfer_product` (
  `id` bigint(20) NOT NULL,
  `transfer_id` bigint(20) DEFAULT NULL,
  `product_id` bigint(20) DEFAULT NULL,
  `product_code` varchar(50) DEFAULT NULL,
  `product_name` varchar(50) DEFAULT NULL,
  `material_number` varchar(25) DEFAULT NULL,
  `unit_id` bigint(20) DEFAULT NULL,
  `unit_name` varchar(50) DEFAULT NULL,
  `unit_spec` varchar(50) DEFAULT NULL,
  `accounting_unit_id` bigint(20) DEFAULT NULL,
  `accounting_unit_name` varchar(50) DEFAULT NULL,
  `accounting_unit_spec` varchar(50) DEFAULT NULL,
  `quantity` decimal(18,8) DEFAULT NULL,
  `accounting_quantity` decimal(18,8) DEFAULT NULL,
  `accounting_received_quantity` decimal(18,8) DEFAULT NULL,
  `item_number` int(11) DEFAULT NULL,
  `is_confirmed` tinyint(1) DEFAULT NULL,
  `partner_id` bigint(20) DEFAULT NULL,
  `extends` varchar(255) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_by` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_by` bigint(20) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `shipping_store` bigint(20) DEFAULT NULL,
  `receiving_store` bigint(20) DEFAULT NULL,
  `transfer_date` datetime DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `confirmed_received_quantity` decimal(18,8) DEFAULT NULL,
  `created_name` varchar(50) DEFAULT NULL,
  `updated_name` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `supply_trans_product_index1` (`transfer_id`),
  KEY `supply_trans_product_index2` (`material_number`),
  KEY `supply_trans_product_partner_id` (`partner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for warehouse_purchase_order
-- ----------------------------
-- DROP TABLE IF EXISTS `warehouse_purchase_order`;
CREATE TABLE `warehouse_purchase_order` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_name` varchar(20) DEFAULT NULL COMMENT '创建人名字',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) DEFAULT NULL COMMENT '更新人名字',
  `order_code` varchar(50) DEFAULT NULL COMMENT '订单编号',
  `order_type` varchar(10) DEFAULT NULL COMMENT '订单类型',
  `purchase_type` varchar(20) DEFAULT NULL COMMENT '采购类型',
  `purchase_reason` varchar(20) DEFAULT NULL COMMENT '采购原因',
  `order_status` varchar(20) DEFAULT NULL COMMENT '单据业务状态(INITED,SUBMITTED,REJECTED,APPROVED)',
  `order_date` datetime DEFAULT NULL COMMENT '订货日期',
  `arrival_date` datetime DEFAULT NULL COMMENT '到货日期',
  `received_by` bigint(20) DEFAULT NULL COMMENT '收获仓库id',
  `received_name` varchar(50) DEFAULT NULL COMMENT '收货仓库名称',
  `supplier_id` bigint(20) DEFAULT NULL COMMENT '供应商id',
  `supplier_name` varchar(50) DEFAULT NULL COMMENT '供应商名称',
  `review_by` bigint(20) DEFAULT NULL COMMENT '审核人id',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `sum_price_tax` decimal(18,8) DEFAULT '0.********' COMMENT '总含税价',
  `sum_tax` decimal(18,8) DEFAULT '0.********' COMMENT '总税',
  `sum_price` decimal(18,8) DEFAULT '0.********' COMMENT '合计金额(不含税)',
  `branch_type` varchar(30) DEFAULT NULL COMMENT '区分仓库/加工中心-WAREHOUSE/MACHINING_CENTER',
  `request_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `warehouse_purchase_order_request_id_uindex` (`request_id`),
  KEY `warehouse_purchase_order_partner_id` (`partner_id`),
  KEY `warehouse_purchase_order_arrival_date` (`arrival_date`),
  KEY `warehouse_purchase_order_code` (`order_code`),
  KEY `warehouse_purchase_order_date` (`order_date`),
  KEY `warehouse_purchase_order_purchase_reason` (`purchase_reason`),
  KEY `warehouse_purchase_order_purchase_type` (`purchase_type`),
  KEY `warehouse_purchase_order_receive_by` (`received_by`),
  KEY `warehouse_purchase_order_status` (`order_status`),
  KEY `warehouse_purchase_order_supplier_id` (`supplier_id`),
  KEY `warehouse_purchase_order_type` (`order_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='仓库采购单主表';

-- ----------------------------
-- Table structure for warehouse_purchase_order_product
-- ----------------------------
-- DROP TABLE IF EXISTS `warehouse_purchase_order_product`;
CREATE TABLE `warehouse_purchase_order_product` (
  `id` bigint(20) NOT NULL,
  `partner_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_name` varchar(20) DEFAULT NULL COMMENT '创建人名字',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_name` varchar(20) DEFAULT NULL COMMENT '更新人名字',
  `order_id` bigint(20) DEFAULT NULL COMMENT '关联的采购单id',
  `product_id` bigint(20) DEFAULT NULL COMMENT '商品id',
  `product_code` varchar(50) DEFAULT NULL COMMENT '商品code',
  `product_name` varchar(50) DEFAULT NULL COMMENT '商品名称',
  `product_category_id` bigint(20) DEFAULT NULL COMMENT '商品分类id',
  `sale_type` varchar(50) DEFAULT NULL COMMENT '销售类型',
  `product_type` varchar(50) DEFAULT NULL COMMENT '商品属性',
  `purchase_unit_id` bigint(20) DEFAULT NULL COMMENT '采购单位id',
  `purchase_unit_name` varchar(50) DEFAULT NULL COMMENT '采购单位名称',
  `purchase_unit_spec` varchar(50) DEFAULT NULL COMMENT '采购单位规格',
  `accounting_unit_id` bigint(20) DEFAULT NULL COMMENT '核算单位id',
  `accounting_unit_name` varchar(50) DEFAULT NULL COMMENT '核算单位名称',
  `accounting_unit_spec` varchar(50) DEFAULT NULL COMMENT '核算单位规格',
  `spec` varchar(30) DEFAULT NULL COMMENT '商品规格',
  `quantity` decimal(18,8) DEFAULT NULL COMMENT '订货数量',
  `unit_rate` decimal(18,8) DEFAULT NULL COMMENT '单位转换比率',
  `tax_rate` decimal(18,8) DEFAULT NULL COMMENT '税率',
  `purchase_price` decimal(18,8) DEFAULT NULL COMMENT '采购价格(含税单价*数量)',
  `storage_type` varchar(30) DEFAULT NULL COMMENT '储藏类型',
  `price_tax` decimal(18,8) DEFAULT NULL COMMENT '含税价(单价)',
  `price` decimal(18,8) DEFAULT NULL COMMENT '单价成本',
  PRIMARY KEY (`id`),
  KEY `purchase_order_product_order_id` (`order_id`),
  KEY `purchase_order_product_partner_id` (`partner_id`),
  KEY `purchase_order_product_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='采购单商品明细';

SET FOREIGN_KEY_CHECKS = 1;

-- 2021-06-25单据状态推进新增过滤空单据
alter table doc_plan_control_doc_status
	add doc_filter varchar(30) null comment '单据过滤条件:NULL/NOT_NULL/ALL';

-- 加工单详情加商品单位转换率
alter table supply_processing_receipts_detail
	add target_material_unit_rate decimal(18,8) null comment '目标物料单位转换率';

-- 采购复核优化
alter table purchase_review_order drop column status;
alter table purchase_review_order drop column adjust_code;

alter table purchase_review_order change order_status status varchar(20) null comment '单据业务状态(INITED,COMPLETED)';

alter table purchase_review_order
	add process_status varchar(20) default 'INITED' comment '单据业务状态给发票勾兑使用INITED/APPROVED' after status;

alter table purchase_review_order
	add source_type varchar(30) null comment '单据来源';

create index purchase_review_order_source_type
	on purchase_review_order (source_type);
create index purchase_review_order_origin_code_inx
	on purchase_review_order (origin_code);

alter table supply_price_adjustment_detail
	add adjust_code varchar(50) not null comment '调价单code' after adjust_id;

create index supply_price_adjustment_detail__adjust_code
	on supply_price_adjustment_detail (adjust_code);

create index supply_price_adjustment_batch_code_inx
	on supply_price_adjustment (batch_code);


create table if not exists purchase_review_log
(
	id bigint not null
		primary key,
	partner_id bigint null,
	created_by bigint null comment '创建人',
	created_name varchar(50) null comment '创建人名称',
	main_id bigint null comment '关联的复核单id',
	action varchar(50) null comment '操作状态',
	created_at datetime null comment '创建时间'
)
comment '采购复核单日志表';

create index purchase_review_log_partner_id
	on purchase_review_log (partner_id);

alter table purchase_review_order change adjust_sum_price_tax pre_sum_price_tax decimal(18,8) default 0.******** null comment '复核前总含税价';

alter table purchase_review_order change adjust_sum_price pre_sum_price decimal(18,8) default 0.******** null comment '复核前总未税价';

alter table purchase_review_order_product change adjust_price pre_price decimal(18,8) default 0.******** null comment '复核前未税单价';

alter table purchase_review_order_product change adjust_price_tax pre_price_tax decimal(18,8) default 0.******** null comment '复核前含税单价';

alter table purchase_review_order_product change adjust_sum_price pre_sum_price decimal(18,8) default 0.******** null comment '复核前未税合计';

alter table purchase_review_order_product change adjust_sum_price_tax pre_sum_price_tax decimal(18,8) null comment '复核前含税合计';

alter table supply_price_adjustment change adjust_sum_price_tax pre_sum_price_tax decimal(18,8) default 0.******** null comment '调整前总含税价';

alter table supply_price_adjustment change adjust_sum_price pre_sum_price decimal(18,8) default 0.******** null comment '调整前总未税价';

alter table supply_price_adjustment_detail change adjust_price pre_price decimal(18,8) default 0.******** null comment '调整前未税单价';

alter table supply_price_adjustment_detail change adjust_price_tax pre_price_tax decimal(18,8) default 0.******** null comment '调整前含税单价';

alter table supply_price_adjustment_detail modify sum_price decimal(18,8) default 0.******** null comment '未税合计';

alter table supply_price_adjustment_detail change adjust_sum_price pre_sum_price decimal(18,8) default 0.******** null comment '调整前未税合计';

alter table supply_price_adjustment_detail modify sum_price_tax decimal(18,8) default 0.******** null comment '含税合计';

alter table supply_price_adjustment_detail change adjust_sum_price_tax pre_sum_price_tax decimal(18,8) default 0.******** null comment '调整前含税合计';



-- 2021-08-24 报废增加驳回原因
alter table supply_adjust_detail
	add reject_reason varchar(255) null comment '驳回原因';

-- 订货计划盘点属性筛选加索引
create index doc_plan__sub_type on doc_plan (sub_type);



-- 加盟商订货 新增三个表
create table supply_franchisee_demand
(
	id bigint not null comment '主键id'
		primary key,
	partner_id bigint not null comment '商户id',
	batch_id bigint not null comment '唯一请求/批次id',
	code varchar(50) not null comment '订单编号',
	type varchar(10) null comment '订单类型(FD-加盟商订货)',
	sub_type varchar(10) null comment '主配类型*预留',
	bus_type varchar(30) null comment '业务类型(来源等)',
	received_by bigint null comment '收货方id',
	received_code varchar(50) null,
	received_name varchar(50) null comment '收货方名称',
	store_type varchar(50) null,
	franchisee_id bigint null comment '加盟商id',
	distribute_by bigint null comment '配送方id',
	demand_date datetime not null comment '订货日期',
	arrival_date datetime null comment '到货日期',
	status varchar(20) null comment '单据业务状态(INITED,CONFIRMED,REJECTED,APPROVING,APPROVED,CANCELLED,SUCCESS)',
	process_status varchar(20) null comment '处理状态:(INITED,PROCESSING,SUCCESS,FAILED)',
	review_by bigint null comment '审核人',
	reject_reason varchar(255) null comment '驳回原因',
	remark varchar(255) null comment '备注',
	reason varchar(10) null comment '原因',
	created_by bigint null,
	created_name varchar(20) null,
	created_at datetime null comment '创建时间',
	updated_by bigint null,
	updated_name varchar(20) null,
	updated_at datetime null comment '更新时间',
	sum_price_tax decimal(18,8) default 0.******** null comment '合计含税金额',
	sum_tax decimal(18,8) default 0.******** null comment '总税额',
	pay_amount decimal(18,8) default 0.******** null comment '实际付款金额',
	attachments text null comment '付款凭证'
)
comment '加盟商订货单';

create index supply_f_demand_batch_id
	on supply_franchisee_demand (batch_id);

create index supply_f_demand_code
	on supply_franchisee_demand (code);

create index supply_f_demand_demand_date
	on supply_franchisee_demand (demand_date);

create index supply_f_demand_franchisee_id
	on supply_franchisee_demand (franchisee_id);

create index supply_f_demand_index4
	on supply_franchisee_demand (partner_id);

create index supply_f_demand_received_by
	on supply_franchisee_demand (received_by);

create index supply_f_demand_status
	on supply_franchisee_demand (status);

create index supply_f_demand_type
	on supply_franchisee_demand (type);


create table supply_franchisee_demand_product
(
	id bigint not null comment '主键id'
		primary key,
	demand_id bigint not null comment '关联订单id',
	partner_id bigint not null comment '租户id',
	status varchar(30) null comment '状态: INITED#未分配,PROCESS#已分配, SUCCESS#已生单)',
	product_id bigint not null comment '商品id',
	product_code varchar(50) null comment '商品编号',
	product_name varchar(50) null comment '商品名称',
	org_product_id bigint null comment '被替换的原商品id',
	category_id bigint null comment '商品类别id',
	category_name varchar(50) null comment '商品类别名称',
	unit_id bigint null comment '订货单位id',
	unit_name varchar(50) null comment '订货单位名称',
	unit_spec varchar(50) null comment '单位规格',
	accounting_unit_id bigint null comment '核算单位id',
	accounting_unit_name varchar(50) null comment '核算单位名称',
	accounting_unit_spec varchar(50) null comment '核算规格',
	unit_rate decimal(18,8) null comment '单位转换比率',
	quantity decimal(18,8) null comment '订货数量',
	accounting_quantity decimal(18,8) null comment '核算单位数量',
	tax_price decimal(18,8) null comment '含税单价',
	cost_price decimal(18,8) null comment '成本单价',
	tax_rate decimal(18,8) null comment '税率',
	amount decimal(18,8) null comment '单条合计金额',
	arrival_days int null comment '到货天数',
	distribution_type varchar(10) null comment '配送类型，直送/配送',
	distribute_by bigint null comment '配送/供应商中心',
	created_by bigint null,
	created_name varchar(20) null,
	created_at datetime null comment '创建时间',
	updated_by bigint null,
	updated_name varchar(20) null,
	updated_at datetime null comment '更新时间'
)
comment '加盟商订货单商品';

create index supply_f_demand_product_index1
	on supply_franchisee_demand_product (demand_id, product_id);

create index supply_f_demand_product_partner_id
	on supply_franchisee_demand_product (partner_id);

create index supply_franchisee_demand_product_status
	on supply_franchisee_demand_product (status);




create table supply_franchisee_demand_log
(
	id bigint not null comment '主键id'
		primary key,
	partner_id bigint not null comment '商户id',
	demand_id bigint null comment '订货单id',
	action varchar(50) null comment '操作状态',
	created_by bigint null,
	created_name varchar(20) null,
	created_at datetime null comment '创建时间'
)
comment '加盟商订货单操作日志';

create index index_demand_id
	on supply_franchisee_demand_log (demand_id);

create index index_partner_id
	on supply_franchisee_demand_log (partner_id);

-- 采购复核再次优化
alter table purchase_review_order
	add reject_reason varchar(100) null comment '驳回原因';

alter table purchase_review_order_product
	add prev_price_tax decimal(18,8) null comment '上一次调整含税价' after orig_price_tax;


create table if not exists purchase_review_price_adjust_log
(
	id bigint not null
		primary key,
	partner_id bigint null,
	created_by bigint null comment '创建人',
	created_name varchar(50) null comment '创建人名称',
	main_id bigint null comment '关联的复核单id',
    product_id bigint null comment '商品id',
    price_tax decimal(18,8) null comment '含税单价',
	orig_price_tax decimal(18,8) null comment '原始含税单价',
	prev_price_tax decimal(18,8) null comment '上一次调整含税价',
	created_at datetime null comment '创建时间'
)
comment '采购复核调价记录';

create index price_adjust_log_unix_index
	on purchase_review_log (partner_id, main_id);

alter table purchase_review_order modify status varchar(20) null comment '单据业务状态(INITED,SUBMITTED,APPROVED,REJECTED)';

alter table purchase_review_order modify process_status varchar(20) null comment '单据业务状态给发票勾兑使用INITED/COMPLETED';

alter table purchase_review_order change pre_sum_price_tax orig_sum_price_tax decimal(18,8) default 0.******** null comment '原始总含税价';

alter table purchase_review_order change pre_sum_price orig_sum_price decimal(18,8) default 0.******** null comment '原始总未税价';

alter table purchase_review_order_product change pre_price orig_price decimal(18,8) null comment '原始未税单价';

alter table purchase_review_order_product change pre_price_tax orig_price_tax decimal(18,8) null comment '原始含税单价';

alter table purchase_review_order_product change pre_sum_price orig_sum_price decimal(18,8) null comment '原始未税合计';

alter table purchase_review_order_product change pre_sum_price_tax orig_sum_price_tax decimal(18,8) null comment '原始含税合计';



-- 账期初始化优化
create table task_batch_period_init
(
	id bigint not null comment '主键id'
		primary key,
	partner_id bigint null comment '商户id',
	created_at datetime null comment '创建时间',
	updated_at datetime null comment '更新时间',
	process_status varchar(20) null comment '操作状态(INITED,SUCCESS,FAILED)',
	period_name varchar(30) not null comment '账期名称(2021-01)，和partner_id做唯一key校验',
	content text comment '请求主档内容fields',
	constraint task_batch_period_init_uIndex
		unique (partner_id, period_name)
)
comment '账期初始化创建批次控制表';


-- 20211118 报废单上传附件
alter table supply_adjust_detail
	add attachments text null comment '附件(json)';

-- 2022-02-14 调拨增加跨公司调拨标记
alter table supply_transfer_detail
	add cross_company boolean null comment '// 是否跨公司调拨';

