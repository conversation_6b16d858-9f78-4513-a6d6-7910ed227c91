#### supply线上环境变量配置

##### 空值需要衣线上环境更改


##### 1、固定变量

```
app:
    name: supply
    host: 0.0.0.0
    port: 8686
    max_worker: 5
    mode: grpc     # grpc or task
    
logger:
    console:
        level: DEBUG
        format: "%(asctime)s %(levelname)-8s:%(name)s-%(message)s"
        
data_center: 1 

sentry:
    enabled: False
    dsn: ''
    environment: 'dev'
    
prometheus:
    host: 0.0.0.0
    port: 7070
    
tracing:
    service_name: supply-stage
    agent_host: ************
    agent_port: 6831
    sample_type: const
    sample_param: 1
    enable: false
```

##### 2、数据库配置

```
redis_base:
   host: 
   port: 
   password: 
   database: 
mysql:
    user: 
    password: 
    host: 
    port: 
    database: 
```

##### 3、nsq配置

```
supply_code: supply
#http://ip:port
nsq_address: 
#ip:port
nsqd_tcp_addresses: 
```

##### 4、依赖服务配置

```
#主档服务配置
metadata_host: 
metadata_port: 

#库存服务配置
inventory_host: 
inventory_port: 

#物料服务配置
bom_host: 
bom_port: 


# oauth服务ip:port
oauth_address: 
```