# -*- coding: utf8 -*-

import sys
import os
from pathlib import Path
import logging
# logging.basicConfig()
# logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)

base_path = Path(__file__).parent.resolve().joinpath('supply')
sys.path.append(str(base_path))
sys.path.append(os.path.dirname(base_path))
sys.path.append(str(base_path.joinpath('proto')))
sys.path.append(str(base_path.joinpath('proto').joinpath("metadata")))
sys.path.append(str(base_path.joinpath('proto').joinpath("oauth")))


from supply.Grpc import run
from supply import APP_CONFIG
from supply import logger

app_config = APP_CONFIG.get('app')
host = app_config.get('host', '127.0.0.1')
port = int(app_config.get('port', 8686))
max_worker = app_config.get('max_worker', 5)
key_path = app_config.get('key_path')
cert_path = app_config.get('cert_path')
try:
    mode = sys.argv[1]
    if mode not in ('grpc', 'task'):
        mode = app_config.get('mode')
except Exception as e:
    mode = app_config.get('mode')


if __name__ == '__main__':
    
    run(mode, host, port, max_worker, key_path, cert_path)