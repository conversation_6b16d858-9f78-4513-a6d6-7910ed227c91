# -*- coding: utf8 -*-
import logging
# logging.basicConfig()
# logging.getLogger('sqlalchemy.engine').setLevel(logging.INFO)

import logging
import os
import time
import threading
from pathlib import Path
import yaml
from raven import Client as SentryClient
import functools

from supply.utils.setting import load_setting
from supply.utils.logger import init_logging

import pymysql
pymysql.install_as_MySQLdb()

request_info = threading.local()

major_version = 0
minor_version = 0
patch_version = 0

__version__ = '{}.{}.{}'.format(major_version, minor_version, patch_version)

APP_BASE_DIR = Path(__file__).parent.parent.resolve()

TIMEOUT = 60
# read config file
CONFIG_PATH = os.getenv('CONFIG_PATH', None)
if CONFIG_PATH:
    CONFIG_PATH = APP_BASE_DIR.joinpath('config', CONFIG_PATH)
else:
    CONFIG_PATH = APP_BASE_DIR.joinpath('config', 'supply_local.yaml')
if not CONFIG_PATH.is_file():
    raise FileNotFoundError
with CONFIG_PATH.open(encoding='utf-8') as f:
    APP_CONFIG = yaml.load(f,Loader=yaml.Loader)

APP_NAME = APP_CONFIG['app']['name']

APP_CONFIG = load_setting(yaml_file=CONFIG_PATH.absolute(), env_prefix='SUPPLY')
LOGGER_LEVEL_MAP = {
    'DEBUG': logging.DEBUG,
    'INFO': logging.INFO,
    'WARNING': logging.WARNING,
    'ERROR': logging.ERROR,
    'CRITICAL': logging.CRITICAL, }

init_logging()
logger = logging.getLogger(APP_NAME)
LOGGER_LEVEL = APP_CONFIG.get('logger', {}).get('console', {}).get('level', '').upper()
if not LOGGER_LEVEL_MAP.get(LOGGER_LEVEL):
    raise ValueError('Invalid logger level config of `{}`'.format(LOGGER_LEVEL))
logger.setLevel(LOGGER_LEVEL_MAP[LOGGER_LEVEL])

logger.info("config file path:{}".format(CONFIG_PATH))
SENTRY_CONFIG = APP_CONFIG.get('sentry')
if SENTRY_CONFIG and SENTRY_CONFIG.pop('enabled', False):
    SENTRY_CONFIG['release'] = __version__
    if not SENTRY_CONFIG.get('dsn'):
        raise ValueError('Missing Sentry DSN')
    sentry_cli = SentryClient(**SENTRY_CONFIG)
else:
    sentry_cli = None


# NSQ_TCP_ADDRESS = APP_CONFIG.get("nsqd_tcp_addresses")
# NSQ_ADDRESS = APP_CONFIG.get("nsq_address")
# ENABLE_NSQ = APP_CONFIG.get("enable_nsq")


# def init_tracer(config):
#     from jaeger_client import Config
#     sampler_config = {
#         'type': config['sample_type'],
#         'param': float(config['sample_param'])
#     }
#     agent_config = {
#         'reporting_host': config['agent_host'],
#         'reporting_port': int(config['agent_port']),
#     }
#     conf = {
#         'service_name': config['service_name'],
#         'sampler': sampler_config,
#         'local_agent': agent_config,
#         'enabled': config['enable'],
#         'logging': False,
#     }
#     conf_obj = Config(config=conf, validate=True)
#     conf_obj.initialize_tracer()
#     print('init tracer over')

# init_tracer(APP_CONFIG['tracing'])

def time_cost(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        logger.info("function_is_{} time_cost:{}".format(func.__name__,time.time() - start))
        return result
    return wrapper