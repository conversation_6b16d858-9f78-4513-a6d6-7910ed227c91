from .mq_factory import ConsumerFactory
from ..encode import CJsonEncoder
from rocketmq.client import PushConsumer, ConsumeStatus
import json
import logging
import time


def rocketmq_handler_wrapper(handlers):
    def message_handler(message):
        f = handlers.get(message.tags.decode('utf-8'))
        ret = False
        data = message
        if message.reconsume_times > 1:
            return ConsumeStatus.CONSUME_SUCCESS
        if f != None:
            try:
                logging.error("Receive msg tags: {}".format(message.tags))
                if isinstance(message.body, dict):
                    data = message.body
                else:
                    data = json.loads(message.body.decode('utf-8'))
            except Exception as ex:
                logging.error("The handlers is: {} \n load message Err: {}".format(f, ex))
                # print("The handlers is: {}".format(f))
                # print("Error to load message to json: ", ex)
            try:
                start = time.time()
                ret = f(data)
                # logging.info(f"handler time cost: {f}, {time.time() - start} seconds")
            except Exception as ex:
                logging.error("Error to handle the message: ", ex)
                # print("The data is : {} \n, The message tags is: {} \n, The handlers is: {}".format(data, message.tags.decode('utf-8'), f))
                # print("Error to handle the message: ", ex)
            # 重试有bug，先不重试
            # if ret:
            #     return ConsumeStatus.CONSUME_SUCCESS
            # else:
            #     print("Message reconsume_times: ", message.reconsume_times)
            #     if message.reconsume_times>2:
            #         return ConsumeStatus.CONSUME_SUCCESS
            #     else:
            #         return ConsumeStatus.RECONSUME_LATER
            return ConsumeStatus.CONSUME_SUCCESS
        else:
            try:
                print("Unknown message: ", message.body)
            except Exception as ex:
                pass
            return ConsumeStatus.CONSUME_SUCCESS

    return message_handler


class ConsumerRocketMQ(ConsumerFactory):
    def __init__(self, consumer_group_name='', channel='', lookup_address='', node_address='', access_key='',
                 access_secret='', thread_num=None, batch_size=None, **mq_options):
        self.consumer = PushConsumer(consumer_group_name)
        self.consumer.set_name_server_address(lookup_address)
        if thread_num and thread_num > 0:
            self.consumer.set_thread_count(thread_num)
        if batch_size and batch_size > 0:
            self.consumer.set_message_batch_max_size(batch_size)
        if access_key:
            self.consumer.set_session_credentials(
                access_key=access_key, access_secret=access_secret, channel=channel)
        self.__handlers = dict()
        self.__topic_group = dict()

    def Register(self, topic_group: str, topic: str, callback, **route):
        self.__handlers[topic] = callback
        if topic_group not in self.__topic_group:
            self.__topic_group[topic_group] = []
        self.__topic_group[topic_group].append(topic)

    def Start(self):
        for topic_group, topics in self.__topic_group.items():
            topics.sort()
            self.consumer.subscribe(
                topic_group, rocketmq_handler_wrapper(self.__handlers), expression="||".join(topics))
        self.consumer.start()

    def Stop(self):
        self.consumer.shutdown()
