import logging

import requests

from supply import APP_CONFIG

requests.adapters.DEFAULT_RETRIES = 5
logger = logging.getLogger("nsq_error")


class NSQService(object):
    def __init__(self):
        self.nsq_host = APP_CONFIG['nsqd_host']
        self.connected = self.ping()

    def __serialize(self, message):
        return message

    def stats(self):
        r = requests.get(self.nsq_host + '/stats')

    def join_url(self,pre_url, pro_url):
        return "%s/%s" % (pre_url.rstrip('/'), pro_url.lstrip('/'))

    def ping(self):
        try:
            r = requests.get(self.join_url(self.nsq_host, 'ping'))
            if r.status_code == 200:
                return True
        except Exception as e:
            logging.warning('nsq cannot be connected %s' % e)
            return False
        return False

    def __pub(self, topic, message, pub_method='put', defer=False):
        try:
            with requests.session() as r:
                r.keep_alive = False
                if defer:
                    res = r.post(self.join_url(self.nsq_host, pub_method),
                                 params=dict(topic=topic,defer=defer),
                                 data=self.__serialize(message))
                else:
                    res = r.post(self.join_url(self.nsq_host, pub_method),
                                 params=dict(topic=topic),
                                 data=self.__serialize(message))
                res.raise_for_status()
                return res.content
        except Exception as e:
            # logging.warning('nsq topic %s cannot be send, error: %s' % (topic, e.message))
            logger.error('nsq topic %s cannot be send, args: %s, error: %s' % (topic, message, e))
        return None

    def publish(self, topic, message, defer=False):
        return self.__pub(topic, message, 'pub', defer)

    def m_publish(self, topic, message,defer=False):
        if isinstance(message, (set, list)):
            return self.__pub(topic, "\n".join(message), 'mpub',defer)
        else:
            return self.__pub(topic, message, 'mpub',defer)

class TaskService(object):
    def __init__(self, group=None):
        self.group = group
        self.__tasks = dict()

    def get_tasks(self):
        return self.__tasks

    def bind(self, topic=None, channel=None, error_handler=None):
        def decorator(f):
            if topic is None or topic == '':
                raise AttributeError('topic is empty')
            if channel is None or channel == '':
                raise AttributeError('channel is empty')
            key = '%s:%s' % (topic, channel)
            if key in self.__tasks:
                raise AttributeError('the combination of topic:%s and channel:%s is existing' % (topic, channel))
            self.__tasks[key] = dict(message_handler=f, error_handler=error_handler)
            return f

        return decorator