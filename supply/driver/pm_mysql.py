# -*- coding: utf8 -*-

from sqlalchemy.orm import (
    Session,
    scoped_session,
    sessionmaker,
)

from supply.driver import (
    make_engine,
    make_session,
    gen_commit_deco,
    db_commit_required,
)
from supply import APP_CONFIG


try:
    MYSQL_CONFIG = APP_CONFIG['pm_mysql']
except KeyError:
    raise KeyError('MySQL config not found')
try:
    mysql_user = MYSQL_CONFIG['user']
    mysql_password = MYSQL_CONFIG['password']
    mysql_host = MYSQL_CONFIG['host']
    mysql_port = int(MYSQL_CONFIG['port'])
    mysql_database = MYSQL_CONFIG['database']
except KeyError:
    raise KeyError('MySQL config wrong')

db_scheme = 'mysql+mysqldb'
engine = make_engine(db_scheme, mysql_user, mysql_password, mysql_host, mysql_port, mysql_database, 'utf8')
session = make_session(engine)  # 这个是线程全局唯一session(不需要实例化，不能和下面的混用)
session_maker = sessionmaker(bind=engine, expire_on_commit=False)   # 这个session每次需要新建session(不能和上面的混用)
session_maker = scoped_session(session_maker)
# DB commit decorator
db_commit = gen_commit_deco(session)