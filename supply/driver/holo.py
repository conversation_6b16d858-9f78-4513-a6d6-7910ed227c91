# -*- coding: utf8 -*-
import logging

from supply.driver import (
    make_engine,
    make_session,
    gen_commit_deco,
    db_commit_required,
)
from sqlalchemy.orm import Session, sessionmaker
from supply import APP_CONFIG

try:
    HOLOGRES_CONFIG = APP_CONFIG['hologres']
except KeyError:
    HOLOGRES_CONFIG = {}
    logging.info('Hologres config not found')


try:
    pg_user = HOLOGRES_CONFIG['user']
    pg_password = HOLOGRES_CONFIG['password']
    pg_host = HOLOGRES_CONFIG['host']
    pg_port = int(HOLOGRES_CONFIG['port'])
    pg_database = HOLOGRES_CONFIG['database']
except KeyError:
    engine, session, session_maker, db_commit = None, None, None, None
    logging.info('Hologres config not found')
    # raise KeyError('PostgreSQL config not found')
else:
    db_scheme = 'postgresql+psycopg2'
    engine = make_engine(db_scheme, pg_user, pg_password, pg_host, pg_port, pg_database)
    session = make_session(engine)  # 这个是全局session
    session_maker = sessionmaker(class_=Session, expire_on_commit=True, autoflush=True, bind=engine)  # 每次使用新建session
    # DB commit decorator
    db_commit = gen_commit_deco(session)

