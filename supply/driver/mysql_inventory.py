# -*- coding: utf8 -*-

from sqlalchemy.orm import (
    Session,
    scoped_session,
    sessionmaker,
)

from supply.driver import (
    make_engine,
    make_session,
    gen_commit_deco,
    db_commit_required,
)
from supply import APP_CONFIG


try:
    MYSQL_CONFIG = APP_CONFIG['inventory']
except KeyError:
    raise KeyError('MySQL config not found')
try:
    mysql_user = MYSQL_CONFIG['user']
    mysql_password = MYSQL_CONFIG['password']
    mysql_host = MYSQL_CONFIG['host']
    mysql_port = int(MYSQL_CONFIG['port'])
    mysql_database = MYSQL_CONFIG['database']
except KeyError:
    raise KeyError('MySQL config wrong')

db_scheme = 'mysql+mysqldb'
engine = make_engine(db_scheme, mysql_user, mysql_password, mysql_host, mysql_port, mysql_database, 'utf8')
session = make_session(engine) # 这个是线程全局唯一session(不需要实例化，不能和下面的混用)
session_maker = sessionmaker(bind=engine, expire_on_commit=False) #这个session每次需要新建session(不能和上面的混用)
# DB commit decorator
db_commit = gen_commit_deco(session)

class DummyTransaction(object):
    def __init__(self, auto_commit=True):
        self.scope_session = session_maker()
        self.auto_commit = auto_commit

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # print 'exit dummy transaction'
        if exc_type or exc_tb:
            print (exc_type, exc_tb)
            self.scope_session.rollback()
            self.scope_session.close()
            return False
        try:
            if self.auto_commit:
                self.scope_session.commit()
            return True
        except Exception as e:
            raise e
            self.scope_session.rollback()
        finally:
            # print 'remove session'
            self.scope_session.close()
