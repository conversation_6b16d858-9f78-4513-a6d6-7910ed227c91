# -*- coding: utf8 -*-

from supply import APP_CONFIG, logger, time_cost
from supply.client.metadata_service import get_struct, metadata_service
from supply.client.auth_permission import auth_permission
from datetime import datetime, timedelta, date
import requests
import json
import time


try:
    REDIS_CONFIG = APP_CONFIG['redis_base']
    redis_config = {}
    redis_config['host'] = REDIS_CONFIG.get('host', '')
    redis_config['port'] = int(REDIS_CONFIG.get('port', 0))
    redis_config['db'] = int(REDIS_CONFIG.get('database', 0))
    redis_config['decode_responses'] = True
    if REDIS_CONFIG.get('password'):
        redis_config['password'] = REDIS_CONFIG['password']

    from redis import StrictRedis, ConnectionPool
    pool = ConnectionPool(**redis_config)
    pool_encode = ConnectionPool(**{**redis_config, 'decode_responses': False})
    redis_cli = StrictRedis(connection_pool=pool, decode_responses=True)
    redis_cli_encode = StrictRedis(connection_pool=pool_encode, decode_responses=False)
except KeyError:
    logger.info('Redis config not found. Use in-memory fake redis instead.')
    from fakeredis import FakeStrictRedis
    redis_config = {}
    redis_config['decode_responses'] = True
    redis_cli = FakeStrictRedis(**redis_config)
    redis_cli_encode = redis_cli


class Redis_cli(object):
    client = redis_cli

    DEFAULT_EXPIRED_TIME = 60

    center_code_key = "center_code_%s"    
    vendor_center_code_key = "vendor_center_code_%s"    
    store_scope_key = "store_scope_%s"
    store_full_scope_key = "store_full_scope_%s"
    unfinished_doc_key = "supply:{}:{}:{}"         # key定义规则: 服务名:partner_id:门店Id:单据类型
    frs_demand_shopping_cart_key = "supply:{}:{}:{}"    # 加盟商订货购物车缓存key: 服务名:租户ID:门店ID:订货类型ID

    @classmethod
    @get_struct
    def get_center_code(cls, center_id, partner_id=None, user_id=None):
        # 根据id获得配送中心code
        res = redis_cli.get(cls.center_code_key % center_id)
        if res:
            return str(res)
        else:
            center = metadata_service.get_distribution_center(center_id, partner_id=partner_id, user_id=partner_id)
            redis_cli.set(cls.center_code_key % center_id, center.get("code"), ex=cls.DEFAULT_EXPIRED_TIME)
            return str(center.get("code"))

    @classmethod
    @get_struct
    def get_vendor_center_code(cls, vendor_id, partner_id=None, user_id=None):
        # 根据id获得配送中心code
        res = redis_cli.get(cls.vendor_center_code_key % vendor_id)
        if res:
            return str(res)
        else:
            center = metadata_service.get_vendor_center(vendor_id, partner_id=partner_id, user_id=partner_id)
            redis_cli.set(cls.vendor_center_code_key % vendor_id, center.get("code"), ex=cls.DEFAULT_EXPIRED_TIME)
            return str(center.get("code"))

    # auth-permission鉴权
    @classmethod
    def get_store_data_scope(cls, partner_id, user_id):
        res = redis_cli.get(cls.store_full_scope_key % user_id)
        if res == 'full':
            return 'full'
        res = redis_cli.lrange(cls.store_scope_key % user_id, 0, -1)
        if not res:
            scope = auth_permission.list_data_scope_check(partner_id, user_id, 'store')
            if scope.get("full_access") == True:
                redis_cli.set(cls.store_full_scope_key % user_id, "full")
                redis_cli.expire(cls.store_full_scope_key % user_id, cls.DEFAULT_EXPIRED_TIME)
                return 'full'
            res = scope.get("ids")
            if res:
                redis_cli.lpush(cls.store_scope_key % user_id, *res)
                redis_cli.expire(cls.store_scope_key % user_id, cls.DEFAULT_EXPIRED_TIME)
        if res:
            res = [int(i) for i in res]
        else:
            res = []
        return res

    # metadata鉴权
    @classmethod
    def get_store_data_scope_old(cls, partner_id, user_id):
        res = redis_cli.get(cls.store_full_scope_key % user_id)
        if res == 'full':
            return 'full'

        res = redis_cli.lrange(cls.store_scope_key % user_id, 0, -1)
        if not res:
            scope = metadata_service.get_store_scope(partner_id=partner_id, user_id=user_id)
            if scope.get("full_access") == True:
                redis_cli.set(cls.store_full_scope_key % user_id, "full")
                redis_cli.expire(cls.store_full_scope_key % user_id, cls.DEFAULT_EXPIRED_TIME)
                return 'full'
            res = scope.get("scope_store_ids")
            if res:
                redis_cli.lpush(cls.store_scope_key % user_id, *res)
                redis_cli.expire(cls.store_scope_key % user_id, cls.DEFAULT_EXPIRED_TIME)
        if res:
            res = [int(i) for i in res]
        else:
            res = []
        return res

    @classmethod
    def get_is_holiday(cls, dt: datetime):
        '''
        判断是否是假期, 使用的api: http://api.goseek.cn
        1代表节假日，2代表调休日
        '''
        prefix_holiday = "is_holiday_"
        try:
            if isinstance(dt, datetime):
                dt = dt.strftime('%Y%m%d') # 20190521
            data = redis_cli.get(prefix_holiday + dt)
            if not data:
                url = "http://api.goseek.cn/Tools/holiday?date=" + dt
                resp = requests.get(url, timeout=3)
                content = json.loads(resp.content)
                logger.info("resp_content:" + str(content))
                data = content.get('data')
                redis_cli.set(prefix_holiday+ dt, str(data), ex=cls.DEFAULT_EXPIRED_TIME * 60)
            if str(data) in ("1", "2"):
                return True
            else:
                return False
        except Exception as e:
            import traceback
            logger.error("get_is_holiday_error:".format(traceback.format_exc()))
            return False

    @classmethod
    def get_unfinished_doc_cache(cls, partner_id, store_id, doc_type):
        doc_list = []
        res = redis_cli.get(cls.unfinished_doc_key.format(partner_id, store_id, doc_type))
        if res:
            doc_dict = json.loads(res)
            doc_list = doc_dict.get(doc_type, [])
        return doc_list

    @classmethod
    @time_cost
    def get_unfinished_doc_map(cls, partner_id, store_id, doc_types):
        doc_map = dict()
        doc_types = [cls.unfinished_doc_key.format(partner_id, store_id, doc_type) for doc_type in doc_types]
        # with redis_cli.pipeline(transaction=False) as p:
        res = redis_cli.mget(doc_types)
        # res = p.execute()
        if res:
            for doc in res:
                doc_dict = json.loads(doc) if doc else None
                if doc_dict:
                    doc_map.update(doc_dict)
        return doc_map

    @classmethod
    @time_cost
    def set_unfinished_doc_cache_by_pipeline(cls, insert_cache_mapping: dict):
        with redis_cli.pipeline(transaction=False) as p:
            for key, value in insert_cache_mapping.items():
                doc_data = json.dumps(value)
                p.set(key, doc_data)
                # 先设置60分钟过期时间
                p.expire(key, time=cls.DEFAULT_EXPIRED_TIME * 1)
            p.execute()
        return True

    @classmethod
    def set_unfinished_doc_cache(cls, partner_id, store_id, doc_type, doc_data):
        doc_data = json.dumps(doc_data)
        key = cls.unfinished_doc_key.format(partner_id, store_id, doc_type)
        redis_cli.set(key, doc_data)
        # 先设置30分钟过期时间
        redis_cli.expire(key, time=cls.DEFAULT_EXPIRED_TIME * 30)
        return True

    @classmethod
    def clean_unfinished_doc_cache(cls, partner_id, store_id, doc_type):
        key = cls.unfinished_doc_key.format(partner_id, store_id, doc_type)
        redis_cli.delete(key)

    @classmethod
    def set_shopping_cart_cache(cls, partner_id, store_id, order_type_id, value):
        value = json.dumps(value)
        key = cls.frs_demand_shopping_cart_key.format(partner_id, store_id, order_type_id)
        redis_cli.set(key, value)
        # 缓存设置当晚0点过期
        expired_time = int(
            time.mktime((date.today() + timedelta(days=1)).timetuple()) - time.mktime(datetime.now().timetuple()))
        redis_cli.expire(key, time=expired_time)
        return True

    @classmethod
    def get_shopping_cart_cache(cls, partner_id, store_id, order_type_id):
        values = []
        res = redis_cli.get(cls.frs_demand_shopping_cart_key.format(partner_id, store_id, order_type_id))
        if res:
            values = json.loads(res)
        return values


redis_server = Redis_cli()
