import logging

import requests
# from retrying import retry

from .xmq_python.producer import Producer
from .xmq_python.consumer import Consumer

requests.adapters.DEFAULT_RETRIES = 5
logger = logging.getLogger("mq_error")
from supply import APP_CONFIG

consumer_type = APP_CONFIG.get('mq', {}).get('rocketmq', {}).get('consumer_type', 'GRPC')
TOPIC = {}
# TTT = {}

class MQProducer(object):
    def __init__(self):
        self.__mq_producer = None

    def init(self, **mq_options):
        self.__mq_producer = Producer(**mq_options)

    def start(self):
        self.__mq_producer.Start()

    def stop(self):
        self.__mq_producer.Stop()


    def publish(self, topic_group, topic, message):

        return self.__mq_producer.Publish(topic_group, topic, message)


mq_producer = MQProducer()


class MQComsumer(object):
    def __init__(self):
        self.__mq_consumer = None

    def init(self, **mq_options):
        self.__mq_consumer = Consumer(**mq_options)

    def start(self):
        self.__mq_consumer.Start()

    def stop(self):
        self.__mq_consumer.Stop()

    def register(self, topic_group, topic, h):
        self.__mq_consumer.Register(topic_group, topic, h)


mq_consumer = MQComsumer()


def register_mq_task(topic_group, topic):
    def decorator(f):
        if consumer_type == 'PUSH':
            mq_consumer.register(topic_group, topic, f)
        else:
            logging.info(f'{f.__name__}, {topic}')
            TOPIC[topic] = f
            # if topic_group in TTT:
            #     TTT[topic_group][topic] = f
            # else:
            #     TTT[topic_group] = {topic: f}
        return f

    return decorator
