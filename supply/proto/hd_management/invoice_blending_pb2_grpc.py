# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from hd_management import invoice_blending_pb2 as hd__management_dot_invoice__blending__pb2


class InvoiceBlendingStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateInvoiceBlendingOrder = channel.unary_unary(
        '/invoice_blending.InvoiceBlending/CreateInvoiceBlendingOrder',
        request_serializer=hd__management_dot_invoice__blending__pb2.CreateInvoiceBlendingOrderRequest.SerializeToString,
        response_deserializer=hd__management_dot_invoice__blending__pb2.CreateInvoiceBlendingOrderResponse.FromString,
        )
    self.ChangeInvoiceBlendingStatus = channel.unary_unary(
        '/invoice_blending.InvoiceBlending/ChangeInvoiceBlendingStatus',
        request_serializer=hd__management_dot_invoice__blending__pb2.ChangeInvoiceBlendingStatusRequest.SerializeToString,
        response_deserializer=hd__management_dot_invoice__blending__pb2.ChangeInvoiceBlendingStatusResponse.FromString,
        )
    self.UpdateInvoiceBlendingOrder = channel.unary_unary(
        '/invoice_blending.InvoiceBlending/UpdateInvoiceBlendingOrder',
        request_serializer=hd__management_dot_invoice__blending__pb2.UpdateInvoiceBlendingOrderRequest.SerializeToString,
        response_deserializer=hd__management_dot_invoice__blending__pb2.UpdateInvoiceBlendingOrderResponse.FromString,
        )
    self.ListInvoiceBlending = channel.unary_unary(
        '/invoice_blending.InvoiceBlending/ListInvoiceBlending',
        request_serializer=hd__management_dot_invoice__blending__pb2.ListInvoiceBlendingRequest.SerializeToString,
        response_deserializer=hd__management_dot_invoice__blending__pb2.ListInvoiceBlendingResponse.FromString,
        )
    self.GetInvoiceBlendingDetail = channel.unary_unary(
        '/invoice_blending.InvoiceBlending/GetInvoiceBlendingDetail',
        request_serializer=hd__management_dot_invoice__blending__pb2.GetInvoiceBlendingDetailRequest.SerializeToString,
        response_deserializer=hd__management_dot_invoice__blending__pb2.GetInvoiceBlendingDetailResponse.FromString,
        )


class InvoiceBlendingServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def CreateInvoiceBlendingOrder(self, request, context):
    """新建发票勾兑单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ChangeInvoiceBlendingStatus(self, request, context):
    """变更发票勾兑单状态
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateInvoiceBlendingOrder(self, request, context):
    """更新勾兑单(驳回的勾兑单允许进行修改)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListInvoiceBlending(self, request, context):
    """查询勾兑单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetInvoiceBlendingDetail(self, request, context):
    """勾兑单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_InvoiceBlendingServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateInvoiceBlendingOrder': grpc.unary_unary_rpc_method_handler(
          servicer.CreateInvoiceBlendingOrder,
          request_deserializer=hd__management_dot_invoice__blending__pb2.CreateInvoiceBlendingOrderRequest.FromString,
          response_serializer=hd__management_dot_invoice__blending__pb2.CreateInvoiceBlendingOrderResponse.SerializeToString,
      ),
      'ChangeInvoiceBlendingStatus': grpc.unary_unary_rpc_method_handler(
          servicer.ChangeInvoiceBlendingStatus,
          request_deserializer=hd__management_dot_invoice__blending__pb2.ChangeInvoiceBlendingStatusRequest.FromString,
          response_serializer=hd__management_dot_invoice__blending__pb2.ChangeInvoiceBlendingStatusResponse.SerializeToString,
      ),
      'UpdateInvoiceBlendingOrder': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateInvoiceBlendingOrder,
          request_deserializer=hd__management_dot_invoice__blending__pb2.UpdateInvoiceBlendingOrderRequest.FromString,
          response_serializer=hd__management_dot_invoice__blending__pb2.UpdateInvoiceBlendingOrderResponse.SerializeToString,
      ),
      'ListInvoiceBlending': grpc.unary_unary_rpc_method_handler(
          servicer.ListInvoiceBlending,
          request_deserializer=hd__management_dot_invoice__blending__pb2.ListInvoiceBlendingRequest.FromString,
          response_serializer=hd__management_dot_invoice__blending__pb2.ListInvoiceBlendingResponse.SerializeToString,
      ),
      'GetInvoiceBlendingDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetInvoiceBlendingDetail,
          request_deserializer=hd__management_dot_invoice__blending__pb2.GetInvoiceBlendingDetailRequest.FromString,
          response_serializer=hd__management_dot_invoice__blending__pb2.GetInvoiceBlendingDetailResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'invoice_blending.InvoiceBlending', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
