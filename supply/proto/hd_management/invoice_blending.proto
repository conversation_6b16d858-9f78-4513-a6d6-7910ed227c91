syntax = "proto3";

package invoice_blending;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";


service InvoiceBlending{
    // 新建发票勾兑单
    rpc CreateInvoiceBlendingOrder (CreateInvoiceBlendingOrderRequest) returns (CreateInvoiceBlendingOrderResponse){
        option (google.api.http) = {
        post: "/api/v2/supply/invoice/blending/create"
        body: "*"
        };
    };
    // 变更发票勾兑单状态
    rpc ChangeInvoiceBlendingStatus (ChangeInvoiceBlendingStatusRequest) returns (ChangeInvoiceBlendingStatusResponse){
        option (google.api.http) = {
        put: "/api/v2/supply/invoice/blending/status"
        body: "*"
        };
    }
    // 更新勾兑单(驳回的勾兑单允许进行修改)
    rpc UpdateInvoiceBlendingOrder (UpdateInvoiceBlendingOrderRequest) returns (UpdateInvoiceBlendingOrderResponse){
        option (google.api.http) = {
        put: "/api/v2/supply/invoice/blending/update"
        body: "*"
        };
    }
    // 查询勾兑单
    rpc ListInvoiceBlending (ListInvoiceBlendingRequest) returns (ListInvoiceBlendingResponse){
        option (google.api.http) = {
        get: "/api/v2/supply/invoice/blending/list"
        };
    }
    // 勾兑单详情
    rpc GetInvoiceBlendingDetail (GetInvoiceBlendingDetailRequest) returns (GetInvoiceBlendingDetailResponse){
        option (google.api.http) = {
        get: "/api/v2/supply/invoice/blending/detail"
        };
    }
}


// 新建发票勾兑单请求参数
message CreateInvoiceBlendingOrderRequest{
    // 公司id
    uint64 company_id = 1;
    // 供应商id
    uint64 supplier_id = 2;
    // 单据id列表
    repeated uint64 order_ids = 3;
    // 单据含税合计 直送收货单含税金额+采购收货单含税金额-直送退货单含税金额-采购退货单含税金额
    double order_sum_price = 4;
    // 单据税额合计
    double order_sum_tax = 5;
    // 发票id列表
    repeated uint64 invoice_ids = 6;
    // 发票含税合计
    double invoice_sum_price = 7;
    // 发票合计税额
    double invoice_sum_tax = 8;
    // 差异金额 发票含税合计-单据含税合计
    double diff_price = 9;
    // 差异原因
    string diff_reason = 10;
}
// 新建发票勾兑单返回参数
message CreateInvoiceBlendingOrderResponse{
    // 勾兑单id
    uint64 id = 1;
    // 保存结果（success/failed）
    string result = 2;
}

// 变更发票勾兑单状态(审核/驳回)请求参数
message ChangeInvoiceBlendingStatusRequest{
    // 勾兑单id 支持多张一起审核/驳回
    repeated uint64 ids = 1;
    // 操作状态(APPROVED, REJECTED)
    string status = 2;
}
// 变更发票勾兑单状态(审核/驳回)返回参数
message ChangeInvoiceBlendingStatusResponse {
    // 勾兑单ids
    repeated uint64 ids = 1;
    // 变更结果（success/failed）
    string result = 2;
}

// 更新发票勾兑单信息请求参数
message UpdateInvoiceBlendingOrderRequest{
    // 勾兑单id
    uint64 id = 1;
    // 勾兑单数据(和创建的时候一样)
    CreateInvoiceBlendingOrderRequest order_data = 2;
}
// 更新发票勾兑单信息返回参数
message UpdateInvoiceBlendingOrderResponse{
    // 勾兑单id
    uint64 id = 1;
    // 变更结果（success/failed）
    string result = 2;
}

// 查询勾兑单请求参数
message ListInvoiceBlendingRequest{
    // 勾兑单编号
    string code = 1;
    // 勾兑单状态'SUBMITTED'已提交 'REJECTED'已驳回 'APPROVED' 已审核
    string status = 2;
    // 公司id列表
    repeated uint64 company_ids = 3;
    // 供应商id列表
    repeated uint64 supplier_ids = 4;
    int64 limit = 5;
    uint64 offset = 6;
    bool include_total = 7;
    // 排序方式  asc or desc
    string sort_type = 8;
    // 排序字段
    string sort = 9;
}
message OrderDetail{
    // 勾兑单id
    uint64 id = 1;
    uint64 partner_id = 2;
    // 勾兑单编号
    string code = 3;
    // 状态
    string status = 4;
    // 公司id
    uint64 company_id = 5;
    // 公司编号
    string company_code = 6;
    // 公司名称
    string company_name = 7;
    // 供应商id
    uint64 supplier_id = 8;
    // 供应商编号
    string supplier_code = 9;
    // 供应商名称
    string supplier_name = 10;
    // 单据id列表
    repeated uint64 order_ids =11;
    // 单据含税合计
    double order_sum_price = 12;
    // 单据税额合计
    double order_sum_tax = 13;
    // 发票id列表
    repeated uint64 invoice_ids = 14;
    // 发票含税合计
    double invoice_sum_price = 15;
    // 发票合计税额
    double invoice_sum_tax = 16;
    // 差异金额
    double diff_price = 17;
    // 差异原因
    string diff_reason = 18;
    // 创建人
    uint64 created_by = 19;
    // 创建人名字
    string created_name = 20;
    // 创建时间
    google.protobuf.Timestamp created_at = 21;
    // 更新人
    uint64 updated_by = 22;
    // 更新人名字
    string updated_name = 23;
    // 更新时间
    google.protobuf.Timestamp updated_at = 24;
}

// 查询勾兑单返回参数
message ListInvoiceBlendingResponse{
    repeated OrderDetail rows = 1;
    uint32 total = 2;
}

// 勾兑单详情请求参数
message GetInvoiceBlendingDetailRequest{
    // 勾兑单id
    uint64 id = 1;
}

// 勾兑单详情返回参数
message GetInvoiceBlendingDetailResponse{
    // 勾兑单id
    uint64 id = 1;
    uint64 partner_id = 2;
    // 勾兑单编号
    string code = 3;
    // 状态
    string status = 4;
    // 公司id
    uint64 company_id = 5;
    // 公司编号
    string company_code = 6;
    // 公司名称
    string company_name = 7;
    // 供应商id
    uint64 supplier_id = 8;
    // 供应商编号
    string supplier_code = 9;
    // 供应商名称
    string supplier_name = 10;
    // 单据id列表
    repeated uint64 order_ids =11;
    // 单据含税合计
    double order_sum_price = 12;
    // 单据税额合计
    double order_sum_tax = 13;
    // 发票id列表
    repeated uint64 invoice_ids = 14;
    // 发票含税合计
    double invoice_sum_price = 15;
    // 发票合计税额
    double invoice_sum_tax = 16;
    // 差异金额
    double diff_price = 17;
    // 差异原因
    string diff_reason = 18;
    // 创建人
    uint64 created_by = 19;
    // 创建人名字
    string created_name = 20;
    // 创建时间
    google.protobuf.Timestamp created_at = 21;
    // 更新人
    uint64 updated_by = 22;
    // 更新人名字
    string updated_name = 23;
    // 更新时间
    google.protobuf.Timestamp updated_at = 24;
}