# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: hd_management/invoice_blending.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='hd_management/invoice_blending.proto',
  package='invoice_blending',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n$hd_management/invoice_blending.proto\x12\x10invoice_blending\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x81\x02\n!CreateInvoiceBlendingOrderRequest\x12\x12\n\ncompany_id\x18\x01 \x01(\x04\x12\x13\n\x0bsupplier_id\x18\x02 \x01(\x04\x12\x11\n\torder_ids\x18\x03 \x03(\x04\x12\x17\n\x0forder_sum_price\x18\x04 \x01(\x01\x12\x15\n\rorder_sum_tax\x18\x05 \x01(\x01\x12\x13\n\x0binvoice_ids\x18\x06 \x03(\x04\x12\x19\n\x11invoice_sum_price\x18\x07 \x01(\x01\x12\x17\n\x0finvoice_sum_tax\x18\x08 \x01(\x01\x12\x12\n\ndiff_price\x18\t \x01(\x01\x12\x13\n\x0b\x64iff_reason\x18\n \x01(\t\"@\n\"CreateInvoiceBlendingOrderResponse\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\"A\n\"ChangeInvoiceBlendingStatusRequest\x12\x0b\n\x03ids\x18\x01 \x03(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\"B\n#ChangeInvoiceBlendingStatusResponse\x12\x0b\n\x03ids\x18\x01 \x03(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\"x\n!UpdateInvoiceBlendingOrderRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12G\n\norder_data\x18\x02 \x01(\x0b\x32\x33.invoice_blending.CreateInvoiceBlendingOrderRequest\"@\n\"UpdateInvoiceBlendingOrderResponse\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\"\xbc\x01\n\x1aListInvoiceBlendingRequest\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x13\n\x0b\x63ompany_ids\x18\x03 \x03(\x04\x12\x14\n\x0csupplier_ids\x18\x04 \x03(\x04\x12\r\n\x05limit\x18\x05 \x01(\x03\x12\x0e\n\x06offset\x18\x06 \x01(\x04\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12\x11\n\tsort_type\x18\x08 \x01(\t\x12\x0c\n\x04sort\x18\t \x01(\t\"\xb7\x04\n\x0bOrderDetail\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x12\n\ncompany_id\x18\x05 \x01(\x04\x12\x14\n\x0c\x63ompany_code\x18\x06 \x01(\t\x12\x14\n\x0c\x63ompany_name\x18\x07 \x01(\t\x12\x13\n\x0bsupplier_id\x18\x08 \x01(\x04\x12\x15\n\rsupplier_code\x18\t \x01(\t\x12\x15\n\rsupplier_name\x18\n \x01(\t\x12\x11\n\torder_ids\x18\x0b \x03(\x04\x12\x17\n\x0forder_sum_price\x18\x0c \x01(\x01\x12\x15\n\rorder_sum_tax\x18\r \x01(\x01\x12\x13\n\x0binvoice_ids\x18\x0e \x03(\x04\x12\x19\n\x11invoice_sum_price\x18\x0f \x01(\x01\x12\x17\n\x0finvoice_sum_tax\x18\x10 \x01(\x01\x12\x12\n\ndiff_price\x18\x11 \x01(\x01\x12\x13\n\x0b\x64iff_reason\x18\x12 \x01(\t\x12\x12\n\ncreated_by\x18\x13 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x14 \x01(\t\x12.\n\ncreated_at\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\x16 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x17 \x01(\t\x12.\n\nupdated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"Y\n\x1bListInvoiceBlendingResponse\x12+\n\x04rows\x18\x01 \x03(\x0b\x32\x1d.invoice_blending.OrderDetail\x12\r\n\x05total\x18\x02 \x01(\r\"-\n\x1fGetInvoiceBlendingDetailRequest\x12\n\n\x02id\x18\x01 \x01(\x04\"\xcc\x04\n GetInvoiceBlendingDetailResponse\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x12\n\ncompany_id\x18\x05 \x01(\x04\x12\x14\n\x0c\x63ompany_code\x18\x06 \x01(\t\x12\x14\n\x0c\x63ompany_name\x18\x07 \x01(\t\x12\x13\n\x0bsupplier_id\x18\x08 \x01(\x04\x12\x15\n\rsupplier_code\x18\t \x01(\t\x12\x15\n\rsupplier_name\x18\n \x01(\t\x12\x11\n\torder_ids\x18\x0b \x03(\x04\x12\x17\n\x0forder_sum_price\x18\x0c \x01(\x01\x12\x15\n\rorder_sum_tax\x18\r \x01(\x01\x12\x13\n\x0binvoice_ids\x18\x0e \x03(\x04\x12\x19\n\x11invoice_sum_price\x18\x0f \x01(\x01\x12\x17\n\x0finvoice_sum_tax\x18\x10 \x01(\x01\x12\x12\n\ndiff_price\x18\x11 \x01(\x01\x12\x13\n\x0b\x64iff_reason\x18\x12 \x01(\t\x12\x12\n\ncreated_by\x18\x13 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x14 \x01(\t\x12.\n\ncreated_at\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\x16 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x17 \x01(\t\x12.\n\nupdated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp2\xa2\x07\n\x0fInvoiceBlending\x12\xba\x01\n\x1a\x43reateInvoiceBlendingOrder\x12\x33.invoice_blending.CreateInvoiceBlendingOrderRequest\x1a\x34.invoice_blending.CreateInvoiceBlendingOrderResponse\"1\x82\xd3\xe4\x93\x02+\"&/api/v2/supply/invoice/blending/create:\x01*\x12\xbd\x01\n\x1b\x43hangeInvoiceBlendingStatus\x12\x34.invoice_blending.ChangeInvoiceBlendingStatusRequest\x1a\x35.invoice_blending.ChangeInvoiceBlendingStatusResponse\"1\x82\xd3\xe4\x93\x02+\x1a&/api/v2/supply/invoice/blending/status:\x01*\x12\xba\x01\n\x1aUpdateInvoiceBlendingOrder\x12\x33.invoice_blending.UpdateInvoiceBlendingOrderRequest\x1a\x34.invoice_blending.UpdateInvoiceBlendingOrderResponse\"1\x82\xd3\xe4\x93\x02+\x1a&/api/v2/supply/invoice/blending/update:\x01*\x12\xa0\x01\n\x13ListInvoiceBlending\x12,.invoice_blending.ListInvoiceBlendingRequest\x1a-.invoice_blending.ListInvoiceBlendingResponse\",\x82\xd3\xe4\x93\x02&\x12$/api/v2/supply/invoice/blending/list\x12\xb1\x01\n\x18GetInvoiceBlendingDetail\x12\x31.invoice_blending.GetInvoiceBlendingDetailRequest\x1a\x32.invoice_blending.GetInvoiceBlendingDetailResponse\".\x82\xd3\xe4\x93\x02(\x12&/api/v2/supply/invoice/blending/detailb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_CREATEINVOICEBLENDINGORDERREQUEST = _descriptor.Descriptor(
  name='CreateInvoiceBlendingOrderRequest',
  full_name='invoice_blending.CreateInvoiceBlendingOrderRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='company_id', full_name='invoice_blending.CreateInvoiceBlendingOrderRequest.company_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_id', full_name='invoice_blending.CreateInvoiceBlendingOrderRequest.supplier_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_ids', full_name='invoice_blending.CreateInvoiceBlendingOrderRequest.order_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_sum_price', full_name='invoice_blending.CreateInvoiceBlendingOrderRequest.order_sum_price', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_sum_tax', full_name='invoice_blending.CreateInvoiceBlendingOrderRequest.order_sum_tax', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='invoice_ids', full_name='invoice_blending.CreateInvoiceBlendingOrderRequest.invoice_ids', index=5,
      number=6, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='invoice_sum_price', full_name='invoice_blending.CreateInvoiceBlendingOrderRequest.invoice_sum_price', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='invoice_sum_tax', full_name='invoice_blending.CreateInvoiceBlendingOrderRequest.invoice_sum_tax', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_price', full_name='invoice_blending.CreateInvoiceBlendingOrderRequest.diff_price', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_reason', full_name='invoice_blending.CreateInvoiceBlendingOrderRequest.diff_reason', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=122,
  serialized_end=379,
)


_CREATEINVOICEBLENDINGORDERRESPONSE = _descriptor.Descriptor(
  name='CreateInvoiceBlendingOrderResponse',
  full_name='invoice_blending.CreateInvoiceBlendingOrderResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='invoice_blending.CreateInvoiceBlendingOrderResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='invoice_blending.CreateInvoiceBlendingOrderResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=381,
  serialized_end=445,
)


_CHANGEINVOICEBLENDINGSTATUSREQUEST = _descriptor.Descriptor(
  name='ChangeInvoiceBlendingStatusRequest',
  full_name='invoice_blending.ChangeInvoiceBlendingStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ids', full_name='invoice_blending.ChangeInvoiceBlendingStatusRequest.ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='invoice_blending.ChangeInvoiceBlendingStatusRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=447,
  serialized_end=512,
)


_CHANGEINVOICEBLENDINGSTATUSRESPONSE = _descriptor.Descriptor(
  name='ChangeInvoiceBlendingStatusResponse',
  full_name='invoice_blending.ChangeInvoiceBlendingStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ids', full_name='invoice_blending.ChangeInvoiceBlendingStatusResponse.ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='invoice_blending.ChangeInvoiceBlendingStatusResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=514,
  serialized_end=580,
)


_UPDATEINVOICEBLENDINGORDERREQUEST = _descriptor.Descriptor(
  name='UpdateInvoiceBlendingOrderRequest',
  full_name='invoice_blending.UpdateInvoiceBlendingOrderRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='invoice_blending.UpdateInvoiceBlendingOrderRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_data', full_name='invoice_blending.UpdateInvoiceBlendingOrderRequest.order_data', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=582,
  serialized_end=702,
)


_UPDATEINVOICEBLENDINGORDERRESPONSE = _descriptor.Descriptor(
  name='UpdateInvoiceBlendingOrderResponse',
  full_name='invoice_blending.UpdateInvoiceBlendingOrderResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='invoice_blending.UpdateInvoiceBlendingOrderResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='invoice_blending.UpdateInvoiceBlendingOrderResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=704,
  serialized_end=768,
)


_LISTINVOICEBLENDINGREQUEST = _descriptor.Descriptor(
  name='ListInvoiceBlendingRequest',
  full_name='invoice_blending.ListInvoiceBlendingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='invoice_blending.ListInvoiceBlendingRequest.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='invoice_blending.ListInvoiceBlendingRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_ids', full_name='invoice_blending.ListInvoiceBlendingRequest.company_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_ids', full_name='invoice_blending.ListInvoiceBlendingRequest.supplier_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='invoice_blending.ListInvoiceBlendingRequest.limit', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='invoice_blending.ListInvoiceBlendingRequest.offset', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='invoice_blending.ListInvoiceBlendingRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort_type', full_name='invoice_blending.ListInvoiceBlendingRequest.sort_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='invoice_blending.ListInvoiceBlendingRequest.sort', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=771,
  serialized_end=959,
)


_ORDERDETAIL = _descriptor.Descriptor(
  name='OrderDetail',
  full_name='invoice_blending.OrderDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='invoice_blending.OrderDetail.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='invoice_blending.OrderDetail.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='invoice_blending.OrderDetail.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='invoice_blending.OrderDetail.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_id', full_name='invoice_blending.OrderDetail.company_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_code', full_name='invoice_blending.OrderDetail.company_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_name', full_name='invoice_blending.OrderDetail.company_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_id', full_name='invoice_blending.OrderDetail.supplier_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_code', full_name='invoice_blending.OrderDetail.supplier_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_name', full_name='invoice_blending.OrderDetail.supplier_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_ids', full_name='invoice_blending.OrderDetail.order_ids', index=10,
      number=11, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_sum_price', full_name='invoice_blending.OrderDetail.order_sum_price', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_sum_tax', full_name='invoice_blending.OrderDetail.order_sum_tax', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='invoice_ids', full_name='invoice_blending.OrderDetail.invoice_ids', index=13,
      number=14, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='invoice_sum_price', full_name='invoice_blending.OrderDetail.invoice_sum_price', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='invoice_sum_tax', full_name='invoice_blending.OrderDetail.invoice_sum_tax', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_price', full_name='invoice_blending.OrderDetail.diff_price', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_reason', full_name='invoice_blending.OrderDetail.diff_reason', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='invoice_blending.OrderDetail.created_by', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='invoice_blending.OrderDetail.created_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='invoice_blending.OrderDetail.created_at', index=20,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='invoice_blending.OrderDetail.updated_by', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='invoice_blending.OrderDetail.updated_name', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='invoice_blending.OrderDetail.updated_at', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=962,
  serialized_end=1529,
)


_LISTINVOICEBLENDINGRESPONSE = _descriptor.Descriptor(
  name='ListInvoiceBlendingResponse',
  full_name='invoice_blending.ListInvoiceBlendingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='invoice_blending.ListInvoiceBlendingResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='invoice_blending.ListInvoiceBlendingResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1531,
  serialized_end=1620,
)


_GETINVOICEBLENDINGDETAILREQUEST = _descriptor.Descriptor(
  name='GetInvoiceBlendingDetailRequest',
  full_name='invoice_blending.GetInvoiceBlendingDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='invoice_blending.GetInvoiceBlendingDetailRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1622,
  serialized_end=1667,
)


_GETINVOICEBLENDINGDETAILRESPONSE = _descriptor.Descriptor(
  name='GetInvoiceBlendingDetailResponse',
  full_name='invoice_blending.GetInvoiceBlendingDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_id', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.company_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_code', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.company_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_name', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.company_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_id', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.supplier_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_code', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.supplier_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_name', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.supplier_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_ids', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.order_ids', index=10,
      number=11, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_sum_price', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.order_sum_price', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_sum_tax', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.order_sum_tax', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='invoice_ids', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.invoice_ids', index=13,
      number=14, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='invoice_sum_price', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.invoice_sum_price', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='invoice_sum_tax', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.invoice_sum_tax', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_price', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.diff_price', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_reason', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.diff_reason', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.created_by', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.created_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.created_at', index=20,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.updated_by', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.updated_name', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='invoice_blending.GetInvoiceBlendingDetailResponse.updated_at', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1670,
  serialized_end=2258,
)

_UPDATEINVOICEBLENDINGORDERREQUEST.fields_by_name['order_data'].message_type = _CREATEINVOICEBLENDINGORDERREQUEST
_ORDERDETAIL.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ORDERDETAIL.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTINVOICEBLENDINGRESPONSE.fields_by_name['rows'].message_type = _ORDERDETAIL
_GETINVOICEBLENDINGDETAILRESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETINVOICEBLENDINGDETAILRESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['CreateInvoiceBlendingOrderRequest'] = _CREATEINVOICEBLENDINGORDERREQUEST
DESCRIPTOR.message_types_by_name['CreateInvoiceBlendingOrderResponse'] = _CREATEINVOICEBLENDINGORDERRESPONSE
DESCRIPTOR.message_types_by_name['ChangeInvoiceBlendingStatusRequest'] = _CHANGEINVOICEBLENDINGSTATUSREQUEST
DESCRIPTOR.message_types_by_name['ChangeInvoiceBlendingStatusResponse'] = _CHANGEINVOICEBLENDINGSTATUSRESPONSE
DESCRIPTOR.message_types_by_name['UpdateInvoiceBlendingOrderRequest'] = _UPDATEINVOICEBLENDINGORDERREQUEST
DESCRIPTOR.message_types_by_name['UpdateInvoiceBlendingOrderResponse'] = _UPDATEINVOICEBLENDINGORDERRESPONSE
DESCRIPTOR.message_types_by_name['ListInvoiceBlendingRequest'] = _LISTINVOICEBLENDINGREQUEST
DESCRIPTOR.message_types_by_name['OrderDetail'] = _ORDERDETAIL
DESCRIPTOR.message_types_by_name['ListInvoiceBlendingResponse'] = _LISTINVOICEBLENDINGRESPONSE
DESCRIPTOR.message_types_by_name['GetInvoiceBlendingDetailRequest'] = _GETINVOICEBLENDINGDETAILREQUEST
DESCRIPTOR.message_types_by_name['GetInvoiceBlendingDetailResponse'] = _GETINVOICEBLENDINGDETAILRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CreateInvoiceBlendingOrderRequest = _reflection.GeneratedProtocolMessageType('CreateInvoiceBlendingOrderRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEINVOICEBLENDINGORDERREQUEST,
  __module__ = 'hd_management.invoice_blending_pb2'
  # @@protoc_insertion_point(class_scope:invoice_blending.CreateInvoiceBlendingOrderRequest)
  ))
_sym_db.RegisterMessage(CreateInvoiceBlendingOrderRequest)

CreateInvoiceBlendingOrderResponse = _reflection.GeneratedProtocolMessageType('CreateInvoiceBlendingOrderResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEINVOICEBLENDINGORDERRESPONSE,
  __module__ = 'hd_management.invoice_blending_pb2'
  # @@protoc_insertion_point(class_scope:invoice_blending.CreateInvoiceBlendingOrderResponse)
  ))
_sym_db.RegisterMessage(CreateInvoiceBlendingOrderResponse)

ChangeInvoiceBlendingStatusRequest = _reflection.GeneratedProtocolMessageType('ChangeInvoiceBlendingStatusRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHANGEINVOICEBLENDINGSTATUSREQUEST,
  __module__ = 'hd_management.invoice_blending_pb2'
  # @@protoc_insertion_point(class_scope:invoice_blending.ChangeInvoiceBlendingStatusRequest)
  ))
_sym_db.RegisterMessage(ChangeInvoiceBlendingStatusRequest)

ChangeInvoiceBlendingStatusResponse = _reflection.GeneratedProtocolMessageType('ChangeInvoiceBlendingStatusResponse', (_message.Message,), dict(
  DESCRIPTOR = _CHANGEINVOICEBLENDINGSTATUSRESPONSE,
  __module__ = 'hd_management.invoice_blending_pb2'
  # @@protoc_insertion_point(class_scope:invoice_blending.ChangeInvoiceBlendingStatusResponse)
  ))
_sym_db.RegisterMessage(ChangeInvoiceBlendingStatusResponse)

UpdateInvoiceBlendingOrderRequest = _reflection.GeneratedProtocolMessageType('UpdateInvoiceBlendingOrderRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEINVOICEBLENDINGORDERREQUEST,
  __module__ = 'hd_management.invoice_blending_pb2'
  # @@protoc_insertion_point(class_scope:invoice_blending.UpdateInvoiceBlendingOrderRequest)
  ))
_sym_db.RegisterMessage(UpdateInvoiceBlendingOrderRequest)

UpdateInvoiceBlendingOrderResponse = _reflection.GeneratedProtocolMessageType('UpdateInvoiceBlendingOrderResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEINVOICEBLENDINGORDERRESPONSE,
  __module__ = 'hd_management.invoice_blending_pb2'
  # @@protoc_insertion_point(class_scope:invoice_blending.UpdateInvoiceBlendingOrderResponse)
  ))
_sym_db.RegisterMessage(UpdateInvoiceBlendingOrderResponse)

ListInvoiceBlendingRequest = _reflection.GeneratedProtocolMessageType('ListInvoiceBlendingRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTINVOICEBLENDINGREQUEST,
  __module__ = 'hd_management.invoice_blending_pb2'
  # @@protoc_insertion_point(class_scope:invoice_blending.ListInvoiceBlendingRequest)
  ))
_sym_db.RegisterMessage(ListInvoiceBlendingRequest)

OrderDetail = _reflection.GeneratedProtocolMessageType('OrderDetail', (_message.Message,), dict(
  DESCRIPTOR = _ORDERDETAIL,
  __module__ = 'hd_management.invoice_blending_pb2'
  # @@protoc_insertion_point(class_scope:invoice_blending.OrderDetail)
  ))
_sym_db.RegisterMessage(OrderDetail)

ListInvoiceBlendingResponse = _reflection.GeneratedProtocolMessageType('ListInvoiceBlendingResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTINVOICEBLENDINGRESPONSE,
  __module__ = 'hd_management.invoice_blending_pb2'
  # @@protoc_insertion_point(class_scope:invoice_blending.ListInvoiceBlendingResponse)
  ))
_sym_db.RegisterMessage(ListInvoiceBlendingResponse)

GetInvoiceBlendingDetailRequest = _reflection.GeneratedProtocolMessageType('GetInvoiceBlendingDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETINVOICEBLENDINGDETAILREQUEST,
  __module__ = 'hd_management.invoice_blending_pb2'
  # @@protoc_insertion_point(class_scope:invoice_blending.GetInvoiceBlendingDetailRequest)
  ))
_sym_db.RegisterMessage(GetInvoiceBlendingDetailRequest)

GetInvoiceBlendingDetailResponse = _reflection.GeneratedProtocolMessageType('GetInvoiceBlendingDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETINVOICEBLENDINGDETAILRESPONSE,
  __module__ = 'hd_management.invoice_blending_pb2'
  # @@protoc_insertion_point(class_scope:invoice_blending.GetInvoiceBlendingDetailResponse)
  ))
_sym_db.RegisterMessage(GetInvoiceBlendingDetailResponse)



_INVOICEBLENDING = _descriptor.ServiceDescriptor(
  name='InvoiceBlending',
  full_name='invoice_blending.InvoiceBlending',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=2261,
  serialized_end=3191,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateInvoiceBlendingOrder',
    full_name='invoice_blending.InvoiceBlending.CreateInvoiceBlendingOrder',
    index=0,
    containing_service=None,
    input_type=_CREATEINVOICEBLENDINGORDERREQUEST,
    output_type=_CREATEINVOICEBLENDINGORDERRESPONSE,
    serialized_options=_b('\202\323\344\223\002+\"&/api/v2/supply/invoice/blending/create:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ChangeInvoiceBlendingStatus',
    full_name='invoice_blending.InvoiceBlending.ChangeInvoiceBlendingStatus',
    index=1,
    containing_service=None,
    input_type=_CHANGEINVOICEBLENDINGSTATUSREQUEST,
    output_type=_CHANGEINVOICEBLENDINGSTATUSRESPONSE,
    serialized_options=_b('\202\323\344\223\002+\032&/api/v2/supply/invoice/blending/status:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateInvoiceBlendingOrder',
    full_name='invoice_blending.InvoiceBlending.UpdateInvoiceBlendingOrder',
    index=2,
    containing_service=None,
    input_type=_UPDATEINVOICEBLENDINGORDERREQUEST,
    output_type=_UPDATEINVOICEBLENDINGORDERRESPONSE,
    serialized_options=_b('\202\323\344\223\002+\032&/api/v2/supply/invoice/blending/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListInvoiceBlending',
    full_name='invoice_blending.InvoiceBlending.ListInvoiceBlending',
    index=3,
    containing_service=None,
    input_type=_LISTINVOICEBLENDINGREQUEST,
    output_type=_LISTINVOICEBLENDINGRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\022$/api/v2/supply/invoice/blending/list'),
  ),
  _descriptor.MethodDescriptor(
    name='GetInvoiceBlendingDetail',
    full_name='invoice_blending.InvoiceBlending.GetInvoiceBlendingDetail',
    index=4,
    containing_service=None,
    input_type=_GETINVOICEBLENDINGDETAILREQUEST,
    output_type=_GETINVOICEBLENDINGDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\002(\022&/api/v2/supply/invoice/blending/detail'),
  ),
])
_sym_db.RegisterServiceDescriptor(_INVOICEBLENDING)

DESCRIPTOR.services_by_name['InvoiceBlending'] = _INVOICEBLENDING

# @@protoc_insertion_point(module_scope)
