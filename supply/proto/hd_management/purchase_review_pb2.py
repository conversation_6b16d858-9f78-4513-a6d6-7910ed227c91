# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: hd_management/purchase_review.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='hd_management/purchase_review.proto',
  package='purchase_review',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n#hd_management/purchase_review.proto\x12\x0fpurchase_review\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xea\x03\n CreatePurchaseReviewOrderRequest\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\x12\x12\n\norder_type\x18\x02 \x01(\t\x12\x31\n\rreceived_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0breceived_by\x18\x04 \x01(\x04\x12\x15\n\rreceived_name\x18\x05 \x01(\t\x12\x15\n\rreceived_code\x18\x06 \x01(\t\x12\x13\n\x0bsupplier_id\x18\x07 \x01(\x04\x12\x15\n\rsupplier_name\x18\x08 \x01(\t\x12\x15\n\rsupplier_code\x18\t \x01(\t\x12\x15\n\rsum_price_tax\x18\n \x01(\x01\x12\x11\n\tsum_price\x18\x0b \x01(\x01\x12*\n\x08products\x18\x0c \x03(\x0b\x32\x18.purchase_review.Product\x12\x13\n\x0b\x62ranch_type\x18\r \x01(\t\x12\x12\n\ncompany_id\x18\x0e \x01(\x04\x12\x13\n\x0borigin_code\x18\x0f \x01(\t\x12\x13\n\x0b\x61\x64just_info\x18\x10 \x01(\t\x12\x11\n\tis_adjust\x18\x11 \x01(\x08\x12\x16\n\x0e\x63ost_center_id\x18\x12 \x01(\x04\x12\x13\n\x0bsource_type\x18\x13 \x01(\t\"\xb9\x04\n\x07Product\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x03 \x01(\t\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12\x1b\n\x13product_category_id\x18\x05 \x01(\x04\x12\x14\n\x0cproduct_type\x18\x06 \x01(\t\x12\x0f\n\x07unit_id\x18\x07 \x01(\x04\x12\x11\n\tunit_name\x18\x08 \x01(\t\x12\x11\n\tunit_spec\x18\t \x01(\t\x12\x0c\n\x04spec\x18\n \x01(\t\x12\x10\n\x08quantity\x18\x0b \x01(\x01\x12\x17\n\x0f\x61\x63tual_quantity\x18\x0c \x01(\x01\x12\x10\n\x08tax_rate\x18\r \x01(\x01\x12\r\n\x05price\x18\x0e \x01(\x01\x12\x12\n\norig_price\x18\x0f \x01(\x01\x12\x11\n\tsum_price\x18\x10 \x01(\x01\x12\x16\n\x0eorig_sum_price\x18\x11 \x01(\x01\x12\x11\n\tprice_tax\x18\x12 \x01(\x01\x12\x16\n\x0eprev_price_tax\x18\x13 \x01(\x01\x12\x15\n\rsum_price_tax\x18\x14 \x01(\x01\x12\x16\n\x0eorig_price_tax\x18\x15 \x01(\x01\x12\x1a\n\x12orig_sum_price_tax\x18\x16 \x01(\x01\x12\x11\n\tis_modify\x18\x17 \x01(\r\x12/\n\x0b\x61\x64just_logs\x18\x18 \x03(\x0b\x32\x1a.purchase_review.AdjustLog\x12\x0f\n\x07sum_tax\x18\x19 \x01(\x01\x12\x14\n\x0corig_sum_tax\x18\x1a \x01(\x01\"\xa8\x01\n\tAdjustLog\x12\x16\n\x0eprev_price_tax\x18\x01 \x01(\x01\x12\x11\n\tprice_tax\x18\x02 \x01(\x01\x12\x16\n\x0eorig_price_tax\x18\x03 \x01(\x01\x12\x12\n\nupdated_by\x18\x04 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x05 \x01(\t\x12.\n\nupdated_at\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"E\n!CreatePurchaseReviewOrderResponse\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\"\xb3\x03\n\x1eListPurchaseReviewOrderRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\norder_code\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x12\n\norder_type\x18\x05 \x01(\t\x12\x14\n\x0csupplier_ids\x18\x07 \x03(\x04\x12\x14\n\x0creceived_ids\x18\x08 \x03(\x04\x12\x13\n\x0borigin_code\x18\t \x01(\t\x12\r\n\x05limit\x18\n \x01(\x03\x12\x0e\n\x06offset\x18\x0b \x01(\x04\x12\x15\n\rinclude_total\x18\x0c \x01(\x08\x12\r\n\x05order\x18\r \x01(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x0f \x01(\t\x12\x12\n\ncompany_id\x18\x10 \x01(\x04\x12\x16\n\x0eprocess_status\x18\x11 \x01(\t\x12\x11\n\tis_adjust\x18\x12 \x01(\x08\x12\x13\n\x0bsource_type\x18\x13 \x01(\t\"\x8c\x06\n\x0bOrderDetail\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x12\n\norder_code\x18\x02 \x01(\t\x12\x12\n\norder_type\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x31\n\rreceived_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0breceived_by\x18\x06 \x01(\x04\x12\x15\n\rreceived_name\x18\x07 \x01(\t\x12\x15\n\rreceived_code\x18\x08 \x01(\t\x12\x13\n\x0bsupplier_id\x18\t \x01(\x04\x12\x15\n\rsupplier_name\x18\n \x01(\t\x12\x15\n\rsupplier_code\x18\x0b \x01(\t\x12\x15\n\rsum_price_tax\x18\x0c \x01(\x01\x12\x1a\n\x12orig_sum_price_tax\x18\r \x01(\x01\x12\x11\n\tsum_price\x18\x0e \x01(\x01\x12\x16\n\x0eorig_sum_price\x18\x0f \x01(\x01\x12\x11\n\treview_by\x18\x10 \x01(\x04\x12\x12\n\ncreated_by\x18\x11 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x12 \x01(\t\x12.\n\ncreated_at\x18\x13 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\x14 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x15 \x01(\t\x12.\n\nupdated_at\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tis_modify\x18\x17 \x01(\r\x12\x13\n\x0b\x62ranch_type\x18\x18 \x01(\t\x12\x12\n\ncompany_id\x18\x19 \x01(\x04\x12\x16\n\x0eprocess_status\x18\x1a \x01(\t\x12\x13\n\x0borigin_code\x18\x1b \x01(\t\x12\x13\n\x0b\x61\x64just_info\x18\x1c \x01(\t\x12\x11\n\tis_adjust\x18\x1d \x01(\x08\x12\x17\n\x0fgenerate_adjust\x18\x1f \x01(\x08\x12\x16\n\x0e\x63ost_center_id\x18  \x01(\x04\x12\x13\n\x0bsource_type\x18! \x01(\t\"\\\n\x1fListPurchaseReviewOrderResponse\x12*\n\x04rows\x18\x01 \x03(\x0b\x32\x1c.purchase_review.OrderDetail\x12\r\n\x05total\x18\x02 \x01(\x04\"h\n\x1eGetPurchaseReviewDetailRequest\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x03\x12\x0e\n\x06offset\x18\x03 \x01(\x04\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\"\xfb\x06\n\x1fGetPurchaseReviewDetailResponse\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x12\n\norder_code\x18\x02 \x01(\t\x12\x12\n\norder_type\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x31\n\rreceived_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0breceived_by\x18\x06 \x01(\x04\x12\x15\n\rreceived_name\x18\x07 \x01(\t\x12\x15\n\rreceived_code\x18\x08 \x01(\t\x12\x13\n\x0bsupplier_id\x18\t \x01(\x04\x12\x15\n\rsupplier_name\x18\n \x01(\t\x12\x15\n\rsupplier_code\x18\x0b \x01(\t\x12\x15\n\rsum_price_tax\x18\x0c \x01(\x01\x12\x1a\n\x12orig_sum_price_tax\x18\r \x01(\x01\x12\x11\n\tsum_price\x18\x0e \x01(\x01\x12\x16\n\x0eorig_sum_price\x18\x0f \x01(\x01\x12\x11\n\treview_by\x18\x10 \x01(\x04\x12\x12\n\ncreated_by\x18\x11 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x12 \x01(\t\x12.\n\ncreated_at\x18\x13 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\x14 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x15 \x01(\t\x12.\n\nupdated_at\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tis_modify\x18\x17 \x01(\r\x12*\n\x08products\x18\x18 \x03(\x0b\x32\x18.purchase_review.Product\x12\x15\n\rtotal_product\x18\x19 \x01(\r\x12\x13\n\x0b\x62ranch_type\x18\x1a \x01(\t\x12\x12\n\ncompany_id\x18\x1b \x01(\x04\x12\x16\n\x0eprocess_status\x18\x1c \x01(\t\x12\x13\n\x0borigin_code\x18\x1d \x01(\t\x12\x14\n\x0c\x61\x64just_codes\x18\x1e \x03(\t\x12\x11\n\tis_adjust\x18\x1f \x01(\x08\x12\x17\n\x0fgenerate_adjust\x18! \x01(\x08\x12\x16\n\x0e\x63ost_center_id\x18\" \x01(\x04\x12\x13\n\x0bsource_type\x18# \x01(\t\x12\x15\n\rreject_reason\x18$ \x01(\t\"p\n UpdatePurchaseReviewOrderRequest\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12*\n\x08products\x18\x02 \x03(\x0b\x32\x18.purchase_review.Product\x12\x0e\n\x06status\x18\x04 \x01(\t\"E\n!UpdatePurchaseReviewOrderResponse\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\"b\n&ChangePurchaseReviewOrderStatusRequest\x12\x11\n\torder_ids\x18\x01 \x03(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x15\n\rreject_reason\x18\x03 \x01(\t\"L\n\'ChangePurchaseReviewOrderStatusResponse\x12\x11\n\torder_ids\x18\x01 \x03(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\"\xbb\t\n\x14PurchaseReviewDetail\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x12\n\norder_code\x18\x02 \x01(\t\x12\x12\n\norder_type\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x31\n\rreceived_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0breceived_by\x18\x06 \x01(\x04\x12\x15\n\rreceived_name\x18\x07 \x01(\t\x12\x15\n\rreceived_code\x18\x08 \x01(\t\x12\x13\n\x0bsupplier_id\x18\t \x01(\x04\x12\x15\n\rsupplier_name\x18\n \x01(\t\x12\x15\n\rsupplier_code\x18\x0b \x01(\t\x12\x11\n\treview_by\x18\x0c \x01(\x04\x12\x12\n\ncreated_by\x18\r \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x0e \x01(\t\x12.\n\ncreated_at\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\x10 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x11 \x01(\t\x12.\n\nupdated_at\x18\x12 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tis_modify\x18\x13 \x01(\r\x12\x13\n\x0b\x62ranch_type\x18\x14 \x01(\t\x12\x12\n\ncompany_id\x18\x15 \x01(\x04\x12\x16\n\x0eprocess_status\x18\x16 \x01(\t\x12\x13\n\x0borigin_code\x18\x17 \x01(\t\x12\x14\n\x0c\x61\x64just_codes\x18\x18 \x03(\t\x12\x11\n\tis_adjust\x18\x19 \x01(\x08\x12\x17\n\x0fgenerate_adjust\x18\x1a \x01(\x08\x12\x16\n\x0e\x63ost_center_id\x18\x1b \x01(\x04\x12\x13\n\x0bsource_type\x18\x1c \x01(\t\x12\x15\n\rreject_reason\x18\x1d \x01(\t\x12\n\n\x02id\x18\x1e \x01(\x04\x12\x12\n\nproduct_id\x18* \x01(\x04\x12\x14\n\x0cproduct_code\x18+ \x01(\t\x12\x14\n\x0cproduct_name\x18, \x01(\t\x12\x1b\n\x13product_category_id\x18- \x01(\x04\x12\x14\n\x0cproduct_type\x18. \x01(\t\x12\x0f\n\x07unit_id\x18/ \x01(\x04\x12\x11\n\tunit_name\x18\x30 \x01(\t\x12\x11\n\tunit_spec\x18\x31 \x01(\t\x12\x0c\n\x04spec\x18\x32 \x01(\t\x12\x10\n\x08quantity\x18\x33 \x01(\x01\x12\x17\n\x0f\x61\x63tual_quantity\x18\x34 \x01(\x01\x12\x10\n\x08tax_rate\x18\x35 \x01(\x01\x12\r\n\x05price\x18\x36 \x01(\x01\x12\x12\n\norig_price\x18\x37 \x01(\x01\x12\x11\n\tsum_price\x18\x38 \x01(\x01\x12\x16\n\x0eorig_sum_price\x18\x39 \x01(\x01\x12\x11\n\tprice_tax\x18: \x01(\x01\x12\x16\n\x0eprev_price_tax\x18; \x01(\x01\x12\x15\n\rsum_price_tax\x18< \x01(\x01\x12\x16\n\x0eorig_price_tax\x18= \x01(\x01\x12\x1a\n\x12orig_sum_price_tax\x18> \x01(\x01\x12\x0f\n\x07sum_tax\x18\x41 \x01(\x01\x12\x14\n\x0corig_sum_tax\x18\x42 \x01(\x01\"f\n ListPurchaseReviewDetailResponse\x12\x33\n\x04rows\x18\x01 \x03(\x0b\x32%.purchase_review.PurchaseReviewDetail\x12\r\n\x05total\x18\x02 \x01(\x04\x32\xe9\x08\n\x0ePurchaseReview\x12\xb3\x01\n\x19\x43reatePurchaseReviewOrder\x12\x31.purchase_review.CreatePurchaseReviewOrderRequest\x1a\x32.purchase_review.CreatePurchaseReviewOrderResponse\"/\x82\xd3\xe4\x93\x02)\"$/api/v2/supply/purchase/review/order:\x01*\x12\xa9\x01\n\x17ListPurchaseReviewOrder\x12/.purchase_review.ListPurchaseReviewOrderRequest\x1a\x30.purchase_review.ListPurchaseReviewOrderResponse\"+\x82\xd3\xe4\x93\x02%\x12#/api/v2/supply/purchase/review/list\x12\xab\x01\n\x17GetPurchaseReviewDetail\x12/.purchase_review.GetPurchaseReviewDetailRequest\x1a\x30.purchase_review.GetPurchaseReviewDetailResponse\"-\x82\xd3\xe4\x93\x02\'\x12%/api/v2/supply/purchase/review/detail\x12\xbf\x01\n\x19UpdatePurchaseReviewOrder\x12\x31.purchase_review.UpdatePurchaseReviewOrderRequest\x1a\x32.purchase_review.UpdatePurchaseReviewOrderResponse\";\x82\xd3\xe4\x93\x02\x35\x1a\x30/api/v2/supply/purchase/review/{order_id}/update:\x01*\x12\xcf\x01\n\x1f\x43hangePurchaseReviewOrderStatus\x12\x37.purchase_review.ChangePurchaseReviewOrderStatusRequest\x1a\x38.purchase_review.ChangePurchaseReviewOrderStatusResponse\"9\x82\xd3\xe4\x93\x02\x33\x1a./api/v2/supply/purchase/review/{status}/modify:\x01*\x12\xb2\x01\n\x18ListPurchaseReviewDetail\x12/.purchase_review.ListPurchaseReviewOrderRequest\x1a\x31.purchase_review.ListPurchaseReviewDetailResponse\"2\x82\xd3\xe4\x93\x02,\x12*/api/v2/supply/purchase/review/detail/listb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_CREATEPURCHASEREVIEWORDERREQUEST = _descriptor.Descriptor(
  name='CreatePurchaseReviewOrderRequest',
  full_name='purchase_review.CreatePurchaseReviewOrderRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='purchase_review.CreatePurchaseReviewOrderRequest.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='purchase_review.CreatePurchaseReviewOrderRequest.order_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_date', full_name='purchase_review.CreatePurchaseReviewOrderRequest.received_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='purchase_review.CreatePurchaseReviewOrderRequest.received_by', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_name', full_name='purchase_review.CreatePurchaseReviewOrderRequest.received_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_code', full_name='purchase_review.CreatePurchaseReviewOrderRequest.received_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_id', full_name='purchase_review.CreatePurchaseReviewOrderRequest.supplier_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_name', full_name='purchase_review.CreatePurchaseReviewOrderRequest.supplier_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_code', full_name='purchase_review.CreatePurchaseReviewOrderRequest.supplier_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price_tax', full_name='purchase_review.CreatePurchaseReviewOrderRequest.sum_price_tax', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price', full_name='purchase_review.CreatePurchaseReviewOrderRequest.sum_price', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='purchase_review.CreatePurchaseReviewOrderRequest.products', index=11,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='purchase_review.CreatePurchaseReviewOrderRequest.branch_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_id', full_name='purchase_review.CreatePurchaseReviewOrderRequest.company_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='origin_code', full_name='purchase_review.CreatePurchaseReviewOrderRequest.origin_code', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_info', full_name='purchase_review.CreatePurchaseReviewOrderRequest.adjust_info', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='purchase_review.CreatePurchaseReviewOrderRequest.is_adjust', index=16,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='purchase_review.CreatePurchaseReviewOrderRequest.cost_center_id', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_type', full_name='purchase_review.CreatePurchaseReviewOrderRequest.source_type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=120,
  serialized_end=610,
)


_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='purchase_review.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='purchase_review.Product.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='purchase_review.Product.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='purchase_review.Product.product_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='purchase_review.Product.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_category_id', full_name='purchase_review.Product.product_category_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='purchase_review.Product.product_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='purchase_review.Product.unit_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='purchase_review.Product.unit_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='purchase_review.Product.unit_spec', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='purchase_review.Product.spec', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='purchase_review.Product.quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actual_quantity', full_name='purchase_review.Product.actual_quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='purchase_review.Product.tax_rate', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='purchase_review.Product.price', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orig_price', full_name='purchase_review.Product.orig_price', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price', full_name='purchase_review.Product.sum_price', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orig_sum_price', full_name='purchase_review.Product.orig_sum_price', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_tax', full_name='purchase_review.Product.price_tax', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='prev_price_tax', full_name='purchase_review.Product.prev_price_tax', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price_tax', full_name='purchase_review.Product.sum_price_tax', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orig_price_tax', full_name='purchase_review.Product.orig_price_tax', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orig_sum_price_tax', full_name='purchase_review.Product.orig_sum_price_tax', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_modify', full_name='purchase_review.Product.is_modify', index=22,
      number=23, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_logs', full_name='purchase_review.Product.adjust_logs', index=23,
      number=24, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_tax', full_name='purchase_review.Product.sum_tax', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orig_sum_tax', full_name='purchase_review.Product.orig_sum_tax', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=613,
  serialized_end=1182,
)


_ADJUSTLOG = _descriptor.Descriptor(
  name='AdjustLog',
  full_name='purchase_review.AdjustLog',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='prev_price_tax', full_name='purchase_review.AdjustLog.prev_price_tax', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_tax', full_name='purchase_review.AdjustLog.price_tax', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orig_price_tax', full_name='purchase_review.AdjustLog.orig_price_tax', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='purchase_review.AdjustLog.updated_by', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='purchase_review.AdjustLog.updated_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='purchase_review.AdjustLog.updated_at', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1185,
  serialized_end=1353,
)


_CREATEPURCHASEREVIEWORDERRESPONSE = _descriptor.Descriptor(
  name='CreatePurchaseReviewOrderResponse',
  full_name='purchase_review.CreatePurchaseReviewOrderResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='purchase_review.CreatePurchaseReviewOrderResponse.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='purchase_review.CreatePurchaseReviewOrderResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1355,
  serialized_end=1424,
)


_LISTPURCHASEREVIEWORDERREQUEST = _descriptor.Descriptor(
  name='ListPurchaseReviewOrderRequest',
  full_name='purchase_review.ListPurchaseReviewOrderRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='purchase_review.ListPurchaseReviewOrderRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='purchase_review.ListPurchaseReviewOrderRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='purchase_review.ListPurchaseReviewOrderRequest.order_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='purchase_review.ListPurchaseReviewOrderRequest.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='purchase_review.ListPurchaseReviewOrderRequest.order_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_ids', full_name='purchase_review.ListPurchaseReviewOrderRequest.supplier_ids', index=5,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_ids', full_name='purchase_review.ListPurchaseReviewOrderRequest.received_ids', index=6,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='origin_code', full_name='purchase_review.ListPurchaseReviewOrderRequest.origin_code', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='purchase_review.ListPurchaseReviewOrderRequest.limit', index=8,
      number=10, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='purchase_review.ListPurchaseReviewOrderRequest.offset', index=9,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='purchase_review.ListPurchaseReviewOrderRequest.include_total', index=10,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='purchase_review.ListPurchaseReviewOrderRequest.order', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='purchase_review.ListPurchaseReviewOrderRequest.sort', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='purchase_review.ListPurchaseReviewOrderRequest.branch_type', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_id', full_name='purchase_review.ListPurchaseReviewOrderRequest.company_id', index=14,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='purchase_review.ListPurchaseReviewOrderRequest.process_status', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='purchase_review.ListPurchaseReviewOrderRequest.is_adjust', index=16,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_type', full_name='purchase_review.ListPurchaseReviewOrderRequest.source_type', index=17,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1427,
  serialized_end=1862,
)


_ORDERDETAIL = _descriptor.Descriptor(
  name='OrderDetail',
  full_name='purchase_review.OrderDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='purchase_review.OrderDetail.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='purchase_review.OrderDetail.order_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='purchase_review.OrderDetail.order_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='purchase_review.OrderDetail.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_date', full_name='purchase_review.OrderDetail.received_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='purchase_review.OrderDetail.received_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_name', full_name='purchase_review.OrderDetail.received_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_code', full_name='purchase_review.OrderDetail.received_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_id', full_name='purchase_review.OrderDetail.supplier_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_name', full_name='purchase_review.OrderDetail.supplier_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_code', full_name='purchase_review.OrderDetail.supplier_code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price_tax', full_name='purchase_review.OrderDetail.sum_price_tax', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orig_sum_price_tax', full_name='purchase_review.OrderDetail.orig_sum_price_tax', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price', full_name='purchase_review.OrderDetail.sum_price', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orig_sum_price', full_name='purchase_review.OrderDetail.orig_sum_price', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='purchase_review.OrderDetail.review_by', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='purchase_review.OrderDetail.created_by', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='purchase_review.OrderDetail.created_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='purchase_review.OrderDetail.created_at', index=18,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='purchase_review.OrderDetail.updated_by', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='purchase_review.OrderDetail.updated_name', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='purchase_review.OrderDetail.updated_at', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_modify', full_name='purchase_review.OrderDetail.is_modify', index=22,
      number=23, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='purchase_review.OrderDetail.branch_type', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_id', full_name='purchase_review.OrderDetail.company_id', index=24,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='purchase_review.OrderDetail.process_status', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='origin_code', full_name='purchase_review.OrderDetail.origin_code', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_info', full_name='purchase_review.OrderDetail.adjust_info', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='purchase_review.OrderDetail.is_adjust', index=28,
      number=29, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='generate_adjust', full_name='purchase_review.OrderDetail.generate_adjust', index=29,
      number=31, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='purchase_review.OrderDetail.cost_center_id', index=30,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_type', full_name='purchase_review.OrderDetail.source_type', index=31,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1865,
  serialized_end=2645,
)


_LISTPURCHASEREVIEWORDERRESPONSE = _descriptor.Descriptor(
  name='ListPurchaseReviewOrderResponse',
  full_name='purchase_review.ListPurchaseReviewOrderResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='purchase_review.ListPurchaseReviewOrderResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='purchase_review.ListPurchaseReviewOrderResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2647,
  serialized_end=2739,
)


_GETPURCHASEREVIEWDETAILREQUEST = _descriptor.Descriptor(
  name='GetPurchaseReviewDetailRequest',
  full_name='purchase_review.GetPurchaseReviewDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='purchase_review.GetPurchaseReviewDetailRequest.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='purchase_review.GetPurchaseReviewDetailRequest.limit', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='purchase_review.GetPurchaseReviewDetailRequest.offset', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='purchase_review.GetPurchaseReviewDetailRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2741,
  serialized_end=2845,
)


_GETPURCHASEREVIEWDETAILRESPONSE = _descriptor.Descriptor(
  name='GetPurchaseReviewDetailResponse',
  full_name='purchase_review.GetPurchaseReviewDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='purchase_review.GetPurchaseReviewDetailResponse.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='purchase_review.GetPurchaseReviewDetailResponse.order_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='purchase_review.GetPurchaseReviewDetailResponse.order_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='purchase_review.GetPurchaseReviewDetailResponse.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_date', full_name='purchase_review.GetPurchaseReviewDetailResponse.received_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='purchase_review.GetPurchaseReviewDetailResponse.received_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_name', full_name='purchase_review.GetPurchaseReviewDetailResponse.received_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_code', full_name='purchase_review.GetPurchaseReviewDetailResponse.received_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_id', full_name='purchase_review.GetPurchaseReviewDetailResponse.supplier_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_name', full_name='purchase_review.GetPurchaseReviewDetailResponse.supplier_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_code', full_name='purchase_review.GetPurchaseReviewDetailResponse.supplier_code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price_tax', full_name='purchase_review.GetPurchaseReviewDetailResponse.sum_price_tax', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orig_sum_price_tax', full_name='purchase_review.GetPurchaseReviewDetailResponse.orig_sum_price_tax', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price', full_name='purchase_review.GetPurchaseReviewDetailResponse.sum_price', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orig_sum_price', full_name='purchase_review.GetPurchaseReviewDetailResponse.orig_sum_price', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='purchase_review.GetPurchaseReviewDetailResponse.review_by', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='purchase_review.GetPurchaseReviewDetailResponse.created_by', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='purchase_review.GetPurchaseReviewDetailResponse.created_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='purchase_review.GetPurchaseReviewDetailResponse.created_at', index=18,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='purchase_review.GetPurchaseReviewDetailResponse.updated_by', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='purchase_review.GetPurchaseReviewDetailResponse.updated_name', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='purchase_review.GetPurchaseReviewDetailResponse.updated_at', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_modify', full_name='purchase_review.GetPurchaseReviewDetailResponse.is_modify', index=22,
      number=23, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='purchase_review.GetPurchaseReviewDetailResponse.products', index=23,
      number=24, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_product', full_name='purchase_review.GetPurchaseReviewDetailResponse.total_product', index=24,
      number=25, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='purchase_review.GetPurchaseReviewDetailResponse.branch_type', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_id', full_name='purchase_review.GetPurchaseReviewDetailResponse.company_id', index=26,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='purchase_review.GetPurchaseReviewDetailResponse.process_status', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='origin_code', full_name='purchase_review.GetPurchaseReviewDetailResponse.origin_code', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_codes', full_name='purchase_review.GetPurchaseReviewDetailResponse.adjust_codes', index=29,
      number=30, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='purchase_review.GetPurchaseReviewDetailResponse.is_adjust', index=30,
      number=31, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='generate_adjust', full_name='purchase_review.GetPurchaseReviewDetailResponse.generate_adjust', index=31,
      number=33, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='purchase_review.GetPurchaseReviewDetailResponse.cost_center_id', index=32,
      number=34, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_type', full_name='purchase_review.GetPurchaseReviewDetailResponse.source_type', index=33,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='purchase_review.GetPurchaseReviewDetailResponse.reject_reason', index=34,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2848,
  serialized_end=3739,
)


_UPDATEPURCHASEREVIEWORDERREQUEST = _descriptor.Descriptor(
  name='UpdatePurchaseReviewOrderRequest',
  full_name='purchase_review.UpdatePurchaseReviewOrderRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='purchase_review.UpdatePurchaseReviewOrderRequest.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='purchase_review.UpdatePurchaseReviewOrderRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='purchase_review.UpdatePurchaseReviewOrderRequest.status', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3741,
  serialized_end=3853,
)


_UPDATEPURCHASEREVIEWORDERRESPONSE = _descriptor.Descriptor(
  name='UpdatePurchaseReviewOrderResponse',
  full_name='purchase_review.UpdatePurchaseReviewOrderResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='purchase_review.UpdatePurchaseReviewOrderResponse.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='purchase_review.UpdatePurchaseReviewOrderResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3855,
  serialized_end=3924,
)


_CHANGEPURCHASEREVIEWORDERSTATUSREQUEST = _descriptor.Descriptor(
  name='ChangePurchaseReviewOrderStatusRequest',
  full_name='purchase_review.ChangePurchaseReviewOrderStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_ids', full_name='purchase_review.ChangePurchaseReviewOrderStatusRequest.order_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='purchase_review.ChangePurchaseReviewOrderStatusRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='purchase_review.ChangePurchaseReviewOrderStatusRequest.reject_reason', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3926,
  serialized_end=4024,
)


_CHANGEPURCHASEREVIEWORDERSTATUSRESPONSE = _descriptor.Descriptor(
  name='ChangePurchaseReviewOrderStatusResponse',
  full_name='purchase_review.ChangePurchaseReviewOrderStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_ids', full_name='purchase_review.ChangePurchaseReviewOrderStatusResponse.order_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='purchase_review.ChangePurchaseReviewOrderStatusResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4026,
  serialized_end=4102,
)


_PURCHASEREVIEWDETAIL = _descriptor.Descriptor(
  name='PurchaseReviewDetail',
  full_name='purchase_review.PurchaseReviewDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='purchase_review.PurchaseReviewDetail.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='purchase_review.PurchaseReviewDetail.order_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='purchase_review.PurchaseReviewDetail.order_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='purchase_review.PurchaseReviewDetail.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_date', full_name='purchase_review.PurchaseReviewDetail.received_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='purchase_review.PurchaseReviewDetail.received_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_name', full_name='purchase_review.PurchaseReviewDetail.received_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_code', full_name='purchase_review.PurchaseReviewDetail.received_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_id', full_name='purchase_review.PurchaseReviewDetail.supplier_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_name', full_name='purchase_review.PurchaseReviewDetail.supplier_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_code', full_name='purchase_review.PurchaseReviewDetail.supplier_code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='purchase_review.PurchaseReviewDetail.review_by', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='purchase_review.PurchaseReviewDetail.created_by', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='purchase_review.PurchaseReviewDetail.created_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='purchase_review.PurchaseReviewDetail.created_at', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='purchase_review.PurchaseReviewDetail.updated_by', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='purchase_review.PurchaseReviewDetail.updated_name', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='purchase_review.PurchaseReviewDetail.updated_at', index=17,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_modify', full_name='purchase_review.PurchaseReviewDetail.is_modify', index=18,
      number=19, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='purchase_review.PurchaseReviewDetail.branch_type', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_id', full_name='purchase_review.PurchaseReviewDetail.company_id', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='purchase_review.PurchaseReviewDetail.process_status', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='origin_code', full_name='purchase_review.PurchaseReviewDetail.origin_code', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_codes', full_name='purchase_review.PurchaseReviewDetail.adjust_codes', index=23,
      number=24, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='purchase_review.PurchaseReviewDetail.is_adjust', index=24,
      number=25, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='generate_adjust', full_name='purchase_review.PurchaseReviewDetail.generate_adjust', index=25,
      number=26, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='purchase_review.PurchaseReviewDetail.cost_center_id', index=26,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_type', full_name='purchase_review.PurchaseReviewDetail.source_type', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='purchase_review.PurchaseReviewDetail.reject_reason', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='purchase_review.PurchaseReviewDetail.id', index=29,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='purchase_review.PurchaseReviewDetail.product_id', index=30,
      number=42, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='purchase_review.PurchaseReviewDetail.product_code', index=31,
      number=43, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='purchase_review.PurchaseReviewDetail.product_name', index=32,
      number=44, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_category_id', full_name='purchase_review.PurchaseReviewDetail.product_category_id', index=33,
      number=45, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='purchase_review.PurchaseReviewDetail.product_type', index=34,
      number=46, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='purchase_review.PurchaseReviewDetail.unit_id', index=35,
      number=47, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='purchase_review.PurchaseReviewDetail.unit_name', index=36,
      number=48, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='purchase_review.PurchaseReviewDetail.unit_spec', index=37,
      number=49, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='purchase_review.PurchaseReviewDetail.spec', index=38,
      number=50, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='purchase_review.PurchaseReviewDetail.quantity', index=39,
      number=51, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actual_quantity', full_name='purchase_review.PurchaseReviewDetail.actual_quantity', index=40,
      number=52, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='purchase_review.PurchaseReviewDetail.tax_rate', index=41,
      number=53, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='purchase_review.PurchaseReviewDetail.price', index=42,
      number=54, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orig_price', full_name='purchase_review.PurchaseReviewDetail.orig_price', index=43,
      number=55, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price', full_name='purchase_review.PurchaseReviewDetail.sum_price', index=44,
      number=56, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orig_sum_price', full_name='purchase_review.PurchaseReviewDetail.orig_sum_price', index=45,
      number=57, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_tax', full_name='purchase_review.PurchaseReviewDetail.price_tax', index=46,
      number=58, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='prev_price_tax', full_name='purchase_review.PurchaseReviewDetail.prev_price_tax', index=47,
      number=59, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price_tax', full_name='purchase_review.PurchaseReviewDetail.sum_price_tax', index=48,
      number=60, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orig_price_tax', full_name='purchase_review.PurchaseReviewDetail.orig_price_tax', index=49,
      number=61, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orig_sum_price_tax', full_name='purchase_review.PurchaseReviewDetail.orig_sum_price_tax', index=50,
      number=62, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_tax', full_name='purchase_review.PurchaseReviewDetail.sum_tax', index=51,
      number=65, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orig_sum_tax', full_name='purchase_review.PurchaseReviewDetail.orig_sum_tax', index=52,
      number=66, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4105,
  serialized_end=5316,
)


_LISTPURCHASEREVIEWDETAILRESPONSE = _descriptor.Descriptor(
  name='ListPurchaseReviewDetailResponse',
  full_name='purchase_review.ListPurchaseReviewDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='purchase_review.ListPurchaseReviewDetailResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='purchase_review.ListPurchaseReviewDetailResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5318,
  serialized_end=5420,
)

_CREATEPURCHASEREVIEWORDERREQUEST.fields_by_name['received_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEPURCHASEREVIEWORDERREQUEST.fields_by_name['products'].message_type = _PRODUCT
_PRODUCT.fields_by_name['adjust_logs'].message_type = _ADJUSTLOG
_ADJUSTLOG.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPURCHASEREVIEWORDERREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPURCHASEREVIEWORDERREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ORDERDETAIL.fields_by_name['received_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ORDERDETAIL.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ORDERDETAIL.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPURCHASEREVIEWORDERRESPONSE.fields_by_name['rows'].message_type = _ORDERDETAIL
_GETPURCHASEREVIEWDETAILRESPONSE.fields_by_name['received_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPURCHASEREVIEWDETAILRESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPURCHASEREVIEWDETAILRESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPURCHASEREVIEWDETAILRESPONSE.fields_by_name['products'].message_type = _PRODUCT
_UPDATEPURCHASEREVIEWORDERREQUEST.fields_by_name['products'].message_type = _PRODUCT
_PURCHASEREVIEWDETAIL.fields_by_name['received_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PURCHASEREVIEWDETAIL.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PURCHASEREVIEWDETAIL.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPURCHASEREVIEWDETAILRESPONSE.fields_by_name['rows'].message_type = _PURCHASEREVIEWDETAIL
DESCRIPTOR.message_types_by_name['CreatePurchaseReviewOrderRequest'] = _CREATEPURCHASEREVIEWORDERREQUEST
DESCRIPTOR.message_types_by_name['Product'] = _PRODUCT
DESCRIPTOR.message_types_by_name['AdjustLog'] = _ADJUSTLOG
DESCRIPTOR.message_types_by_name['CreatePurchaseReviewOrderResponse'] = _CREATEPURCHASEREVIEWORDERRESPONSE
DESCRIPTOR.message_types_by_name['ListPurchaseReviewOrderRequest'] = _LISTPURCHASEREVIEWORDERREQUEST
DESCRIPTOR.message_types_by_name['OrderDetail'] = _ORDERDETAIL
DESCRIPTOR.message_types_by_name['ListPurchaseReviewOrderResponse'] = _LISTPURCHASEREVIEWORDERRESPONSE
DESCRIPTOR.message_types_by_name['GetPurchaseReviewDetailRequest'] = _GETPURCHASEREVIEWDETAILREQUEST
DESCRIPTOR.message_types_by_name['GetPurchaseReviewDetailResponse'] = _GETPURCHASEREVIEWDETAILRESPONSE
DESCRIPTOR.message_types_by_name['UpdatePurchaseReviewOrderRequest'] = _UPDATEPURCHASEREVIEWORDERREQUEST
DESCRIPTOR.message_types_by_name['UpdatePurchaseReviewOrderResponse'] = _UPDATEPURCHASEREVIEWORDERRESPONSE
DESCRIPTOR.message_types_by_name['ChangePurchaseReviewOrderStatusRequest'] = _CHANGEPURCHASEREVIEWORDERSTATUSREQUEST
DESCRIPTOR.message_types_by_name['ChangePurchaseReviewOrderStatusResponse'] = _CHANGEPURCHASEREVIEWORDERSTATUSRESPONSE
DESCRIPTOR.message_types_by_name['PurchaseReviewDetail'] = _PURCHASEREVIEWDETAIL
DESCRIPTOR.message_types_by_name['ListPurchaseReviewDetailResponse'] = _LISTPURCHASEREVIEWDETAILRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CreatePurchaseReviewOrderRequest = _reflection.GeneratedProtocolMessageType('CreatePurchaseReviewOrderRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPURCHASEREVIEWORDERREQUEST,
  __module__ = 'hd_management.purchase_review_pb2'
  # @@protoc_insertion_point(class_scope:purchase_review.CreatePurchaseReviewOrderRequest)
  ))
_sym_db.RegisterMessage(CreatePurchaseReviewOrderRequest)

Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCT,
  __module__ = 'hd_management.purchase_review_pb2'
  # @@protoc_insertion_point(class_scope:purchase_review.Product)
  ))
_sym_db.RegisterMessage(Product)

AdjustLog = _reflection.GeneratedProtocolMessageType('AdjustLog', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTLOG,
  __module__ = 'hd_management.purchase_review_pb2'
  # @@protoc_insertion_point(class_scope:purchase_review.AdjustLog)
  ))
_sym_db.RegisterMessage(AdjustLog)

CreatePurchaseReviewOrderResponse = _reflection.GeneratedProtocolMessageType('CreatePurchaseReviewOrderResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPURCHASEREVIEWORDERRESPONSE,
  __module__ = 'hd_management.purchase_review_pb2'
  # @@protoc_insertion_point(class_scope:purchase_review.CreatePurchaseReviewOrderResponse)
  ))
_sym_db.RegisterMessage(CreatePurchaseReviewOrderResponse)

ListPurchaseReviewOrderRequest = _reflection.GeneratedProtocolMessageType('ListPurchaseReviewOrderRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTPURCHASEREVIEWORDERREQUEST,
  __module__ = 'hd_management.purchase_review_pb2'
  # @@protoc_insertion_point(class_scope:purchase_review.ListPurchaseReviewOrderRequest)
  ))
_sym_db.RegisterMessage(ListPurchaseReviewOrderRequest)

OrderDetail = _reflection.GeneratedProtocolMessageType('OrderDetail', (_message.Message,), dict(
  DESCRIPTOR = _ORDERDETAIL,
  __module__ = 'hd_management.purchase_review_pb2'
  # @@protoc_insertion_point(class_scope:purchase_review.OrderDetail)
  ))
_sym_db.RegisterMessage(OrderDetail)

ListPurchaseReviewOrderResponse = _reflection.GeneratedProtocolMessageType('ListPurchaseReviewOrderResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTPURCHASEREVIEWORDERRESPONSE,
  __module__ = 'hd_management.purchase_review_pb2'
  # @@protoc_insertion_point(class_scope:purchase_review.ListPurchaseReviewOrderResponse)
  ))
_sym_db.RegisterMessage(ListPurchaseReviewOrderResponse)

GetPurchaseReviewDetailRequest = _reflection.GeneratedProtocolMessageType('GetPurchaseReviewDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPURCHASEREVIEWDETAILREQUEST,
  __module__ = 'hd_management.purchase_review_pb2'
  # @@protoc_insertion_point(class_scope:purchase_review.GetPurchaseReviewDetailRequest)
  ))
_sym_db.RegisterMessage(GetPurchaseReviewDetailRequest)

GetPurchaseReviewDetailResponse = _reflection.GeneratedProtocolMessageType('GetPurchaseReviewDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPURCHASEREVIEWDETAILRESPONSE,
  __module__ = 'hd_management.purchase_review_pb2'
  # @@protoc_insertion_point(class_scope:purchase_review.GetPurchaseReviewDetailResponse)
  ))
_sym_db.RegisterMessage(GetPurchaseReviewDetailResponse)

UpdatePurchaseReviewOrderRequest = _reflection.GeneratedProtocolMessageType('UpdatePurchaseReviewOrderRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPURCHASEREVIEWORDERREQUEST,
  __module__ = 'hd_management.purchase_review_pb2'
  # @@protoc_insertion_point(class_scope:purchase_review.UpdatePurchaseReviewOrderRequest)
  ))
_sym_db.RegisterMessage(UpdatePurchaseReviewOrderRequest)

UpdatePurchaseReviewOrderResponse = _reflection.GeneratedProtocolMessageType('UpdatePurchaseReviewOrderResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPURCHASEREVIEWORDERRESPONSE,
  __module__ = 'hd_management.purchase_review_pb2'
  # @@protoc_insertion_point(class_scope:purchase_review.UpdatePurchaseReviewOrderResponse)
  ))
_sym_db.RegisterMessage(UpdatePurchaseReviewOrderResponse)

ChangePurchaseReviewOrderStatusRequest = _reflection.GeneratedProtocolMessageType('ChangePurchaseReviewOrderStatusRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHANGEPURCHASEREVIEWORDERSTATUSREQUEST,
  __module__ = 'hd_management.purchase_review_pb2'
  # @@protoc_insertion_point(class_scope:purchase_review.ChangePurchaseReviewOrderStatusRequest)
  ))
_sym_db.RegisterMessage(ChangePurchaseReviewOrderStatusRequest)

ChangePurchaseReviewOrderStatusResponse = _reflection.GeneratedProtocolMessageType('ChangePurchaseReviewOrderStatusResponse', (_message.Message,), dict(
  DESCRIPTOR = _CHANGEPURCHASEREVIEWORDERSTATUSRESPONSE,
  __module__ = 'hd_management.purchase_review_pb2'
  # @@protoc_insertion_point(class_scope:purchase_review.ChangePurchaseReviewOrderStatusResponse)
  ))
_sym_db.RegisterMessage(ChangePurchaseReviewOrderStatusResponse)

PurchaseReviewDetail = _reflection.GeneratedProtocolMessageType('PurchaseReviewDetail', (_message.Message,), dict(
  DESCRIPTOR = _PURCHASEREVIEWDETAIL,
  __module__ = 'hd_management.purchase_review_pb2'
  # @@protoc_insertion_point(class_scope:purchase_review.PurchaseReviewDetail)
  ))
_sym_db.RegisterMessage(PurchaseReviewDetail)

ListPurchaseReviewDetailResponse = _reflection.GeneratedProtocolMessageType('ListPurchaseReviewDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTPURCHASEREVIEWDETAILRESPONSE,
  __module__ = 'hd_management.purchase_review_pb2'
  # @@protoc_insertion_point(class_scope:purchase_review.ListPurchaseReviewDetailResponse)
  ))
_sym_db.RegisterMessage(ListPurchaseReviewDetailResponse)



_PURCHASEREVIEW = _descriptor.ServiceDescriptor(
  name='PurchaseReview',
  full_name='purchase_review.PurchaseReview',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=5423,
  serialized_end=6552,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreatePurchaseReviewOrder',
    full_name='purchase_review.PurchaseReview.CreatePurchaseReviewOrder',
    index=0,
    containing_service=None,
    input_type=_CREATEPURCHASEREVIEWORDERREQUEST,
    output_type=_CREATEPURCHASEREVIEWORDERRESPONSE,
    serialized_options=_b('\202\323\344\223\002)\"$/api/v2/supply/purchase/review/order:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListPurchaseReviewOrder',
    full_name='purchase_review.PurchaseReview.ListPurchaseReviewOrder',
    index=1,
    containing_service=None,
    input_type=_LISTPURCHASEREVIEWORDERREQUEST,
    output_type=_LISTPURCHASEREVIEWORDERRESPONSE,
    serialized_options=_b('\202\323\344\223\002%\022#/api/v2/supply/purchase/review/list'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPurchaseReviewDetail',
    full_name='purchase_review.PurchaseReview.GetPurchaseReviewDetail',
    index=2,
    containing_service=None,
    input_type=_GETPURCHASEREVIEWDETAILREQUEST,
    output_type=_GETPURCHASEREVIEWDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\022%/api/v2/supply/purchase/review/detail'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdatePurchaseReviewOrder',
    full_name='purchase_review.PurchaseReview.UpdatePurchaseReviewOrder',
    index=3,
    containing_service=None,
    input_type=_UPDATEPURCHASEREVIEWORDERREQUEST,
    output_type=_UPDATEPURCHASEREVIEWORDERRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\0320/api/v2/supply/purchase/review/{order_id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ChangePurchaseReviewOrderStatus',
    full_name='purchase_review.PurchaseReview.ChangePurchaseReviewOrderStatus',
    index=4,
    containing_service=None,
    input_type=_CHANGEPURCHASEREVIEWORDERSTATUSREQUEST,
    output_type=_CHANGEPURCHASEREVIEWORDERSTATUSRESPONSE,
    serialized_options=_b('\202\323\344\223\0023\032./api/v2/supply/purchase/review/{status}/modify:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListPurchaseReviewDetail',
    full_name='purchase_review.PurchaseReview.ListPurchaseReviewDetail',
    index=5,
    containing_service=None,
    input_type=_LISTPURCHASEREVIEWORDERREQUEST,
    output_type=_LISTPURCHASEREVIEWDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\002,\022*/api/v2/supply/purchase/review/detail/list'),
  ),
])
_sym_db.RegisterServiceDescriptor(_PURCHASEREVIEW)

DESCRIPTOR.services_by_name['PurchaseReview'] = _PURCHASEREVIEW

# @@protoc_insertion_point(module_scope)
