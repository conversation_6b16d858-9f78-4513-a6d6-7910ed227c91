{"swagger": "2.0", "info": {"title": "hd_management/purchase_review.proto", "version": "version not set"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/purchase/review/detail": {"get": {"summary": "查询采购复核单详情", "operationId": "GetPurchaseReviewDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/purchase_reviewGetPurchaseReviewDetailResponse"}}}, "parameters": [{"name": "order_id", "description": "单据id.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}], "tags": ["Pur<PERSON>Review"]}}, "/api/v2/supply/purchase/review/detail/list": {"get": {"summary": "查询采购复核单明细", "operationId": "ListPurchaseReviewDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/purchase_reviewListPurchaseReviewDetailResponse"}}}, "parameters": [{"name": "start_date", "description": "开始收货日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "结束收货日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "order_code", "description": "订单编号.", "in": "query", "required": false, "type": "string"}, {"name": "status", "description": "单据状态: '所有'状态不需要传此参数\nINITED     # 待复核\nSUBMITTED  # 待确认\nAPPROVED   # 已复核\nREJECTED   # 已驳回.", "in": "query", "required": false, "type": "string"}, {"name": "order_type", "description": "单据类型.", "in": "query", "required": false, "type": "string"}, {"name": "supplier_ids", "description": "供应商id列表.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "received_ids", "description": "收货仓库/门店id列表.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "origin_code", "description": "原单编号.", "in": "query", "required": false, "type": "string"}, {"name": "limit", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "order", "description": "排序方式  asc or desc.", "in": "query", "required": false, "type": "string"}, {"name": "sort", "description": "排序字段.", "in": "query", "required": false, "type": "string"}, {"name": "branch_type", "description": "区分门店仓库 WAREHOUSE/STORE.", "in": "query", "required": false, "type": "string"}, {"name": "company_id", "description": "公司id.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "process_status", "description": "发票勾兑状态 INITED=待勾兑 APPROVED=已勾兑 '所有'不传此参数.", "in": "query", "required": false, "type": "string"}, {"name": "is_adjust", "description": "标记是否是调整单.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "source_type", "description": "单据来源, 单选, 默认所有不传此参数.", "in": "query", "required": false, "type": "string"}], "tags": ["Pur<PERSON>Review"]}}, "/api/v2/supply/purchase/review/list": {"get": {"summary": "采购复核单列表查询", "operationId": "ListPurchaseReviewOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/purchase_reviewListPurchaseReviewOrderResponse"}}}, "parameters": [{"name": "start_date", "description": "开始收货日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "结束收货日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "order_code", "description": "订单编号.", "in": "query", "required": false, "type": "string"}, {"name": "status", "description": "单据状态: '所有'状态不需要传此参数\nINITED     # 待复核\nSUBMITTED  # 待确认\nAPPROVED   # 已复核\nREJECTED   # 已驳回.", "in": "query", "required": false, "type": "string"}, {"name": "order_type", "description": "单据类型.", "in": "query", "required": false, "type": "string"}, {"name": "supplier_ids", "description": "供应商id列表.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "received_ids", "description": "收货仓库/门店id列表.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "origin_code", "description": "原单编号.", "in": "query", "required": false, "type": "string"}, {"name": "limit", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "order", "description": "排序方式  asc or desc.", "in": "query", "required": false, "type": "string"}, {"name": "sort", "description": "排序字段.", "in": "query", "required": false, "type": "string"}, {"name": "branch_type", "description": "区分门店仓库 WAREHOUSE/STORE.", "in": "query", "required": false, "type": "string"}, {"name": "company_id", "description": "公司id.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "process_status", "description": "发票勾兑状态 INITED=待勾兑 APPROVED=已勾兑 '所有'不传此参数.", "in": "query", "required": false, "type": "string"}, {"name": "is_adjust", "description": "标记是否是调整单.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "source_type", "description": "单据来源, 单选, 默认所有不传此参数.", "in": "query", "required": false, "type": "string"}], "tags": ["Pur<PERSON>Review"]}}, "/api/v2/supply/purchase/review/order": {"post": {"summary": "创建采购复核单接口", "operationId": "CreatePurchaseReviewOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/purchase_reviewCreatePurchaseReviewOrderResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/purchase_reviewCreatePurchaseReviewOrderRequest"}}], "tags": ["Pur<PERSON>Review"]}}, "/api/v2/supply/purchase/review/{order_id}/update": {"put": {"summary": "更新采购复核详情", "operationId": "UpdatePurchaseReviewOrder", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/purchase_reviewUpdatePurchaseReviewOrderResponse"}}}, "parameters": [{"name": "order_id", "description": "单据id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/purchase_reviewUpdatePurchaseReviewOrderRequest"}}], "tags": ["Pur<PERSON>Review"]}}, "/api/v2/supply/purchase/review/{status}/modify": {"put": {"summary": "修改采购复核状态", "operationId": "ChangePurchaseReviewOrderStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/purchase_reviewChangePurchaseReviewOrderStatusResponse"}}}, "parameters": [{"name": "status", "description": "更新的状态\nINITED     # 待复核\nSUBMITTED  # 提交->待确认\nAPPROVED   # 复核->已复核\nREJECTED   # 驳回->已驳回", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/purchase_reviewChangePurchaseReviewOrderStatusRequest"}}], "tags": ["Pur<PERSON>Review"]}}}, "definitions": {"purchase_reviewAdjustLog": {"type": "object", "properties": {"prev_price_tax": {"type": "number", "format": "double", "title": "含税单价调整前"}, "price_tax": {"type": "number", "format": "double", "title": "含税单价 调整后"}, "orig_price_tax": {"type": "number", "format": "double", "title": "原始含税单价"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人"}, "updated_name": {"type": "string", "title": "更新人名字"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}}}, "purchase_reviewChangePurchaseReviewOrderStatusRequest": {"type": "object", "properties": {"order_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "单据id列表 目前支持批量勾兑和批量提交、复核未调价的单据"}, "status": {"type": "string", "title": "更新的状态\nINITED     # 待复核\nSUBMITTED  # 提交->待确认\nAPPROVED   # 复核->已复核\nREJECTED   # 驳回->已驳回"}, "reject_reason": {"type": "string", "title": "驳回原因"}}, "title": "采购复核(修改状态)请求参数"}, "purchase_reviewChangePurchaseReviewOrderStatusResponse": {"type": "object", "properties": {"order_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "result": {"type": "string"}}, "title": "采购复核(修改状态)返回参数"}, "purchase_reviewCreatePurchaseReviewOrderRequest": {"type": "object", "properties": {"batch_id": {"type": "string", "format": "uint64", "title": "主单id"}, "order_type": {"type": "string", "title": "订单类型-收货单RECEIVE/退货单RETURN"}, "received_date": {"type": "string", "format": "date-time", "title": "收货时间"}, "received_by": {"type": "string", "format": "uint64", "title": "门店或仓库id"}, "received_name": {"type": "string", "title": "门店或仓库名称"}, "received_code": {"type": "string", "title": "门店或仓库编号"}, "supplier_id": {"type": "string", "format": "uint64", "title": "供应商id"}, "supplier_name": {"type": "string", "title": "供应商名称"}, "supplier_code": {"type": "string", "title": "供应商编号"}, "sum_price_tax": {"type": "number", "format": "double", "title": "总含税金额"}, "sum_price": {"type": "number", "format": "double", "title": "总未税金额"}, "products": {"type": "array", "items": {"$ref": "#/definitions/purchase_reviewProduct"}, "title": "商品信息"}, "branch_type": {"type": "string", "title": "区分门店仓库 WAREHOUSE/STORE"}, "company_id": {"type": "string", "format": "uint64", "title": "公司id"}, "origin_code": {"type": "string", "title": "原单编号"}, "adjust_info": {"type": "string", "title": "调价单信息: {adjust_codes: [...]}"}, "is_adjust": {"type": "boolean", "format": "boolean", "title": "标记是否是调整单"}, "cost_center_id": {"type": "string", "format": "uint64"}, "source_type": {"type": "string", "title": "单据来源：\n订货计划(SD)、紧急订货(HD)、总部分配(MD)、库存调整(AD)、对账调整(CAD)\n原单退货(BO)、非原单退货(NBO)、收货差异(REC_DIFF)、采购收货(PUR_REC)、采购退货(PUR_RETURN)"}}, "title": "创建采购复核单请求参数"}, "purchase_reviewCreatePurchaseReviewOrderResponse": {"type": "object", "properties": {"order_id": {"type": "string", "format": "uint64"}, "result": {"type": "string"}}, "title": "创建采购复核单返回参数"}, "purchase_reviewGetPurchaseReviewDetailResponse": {"type": "object", "properties": {"order_id": {"type": "string", "format": "uint64", "title": "订单id"}, "order_code": {"type": "string", "title": "订单编号"}, "order_type": {"type": "string", "title": "单据类型"}, "status": {"type": "string", "title": "单据状态"}, "received_date": {"type": "string", "format": "date-time", "title": "采购日期"}, "received_by": {"type": "string", "format": "uint64", "title": "收货仓库or门店id"}, "received_name": {"type": "string", "title": "收货仓库or门店名称"}, "received_code": {"type": "string", "title": "收货仓库or门店编号"}, "supplier_id": {"type": "string", "format": "uint64", "title": "供应商id or code"}, "supplier_name": {"type": "string", "title": "供应商名称"}, "supplier_code": {"type": "string", "title": "供应商编号"}, "sum_price_tax": {"type": "number", "format": "double", "title": "总含税金额"}, "orig_sum_price_tax": {"type": "number", "format": "double", "title": "原始总含税金额"}, "sum_price": {"type": "number", "format": "double", "title": "总未税金额"}, "orig_sum_price": {"type": "number", "format": "double", "title": "原始总未税金额"}, "review_by": {"type": "string", "format": "uint64", "title": "复核人"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人"}, "created_name": {"type": "string", "title": "创建人名字"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人"}, "updated_name": {"type": "string", "title": "更新人名字"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "is_modify": {"type": "integer", "format": "int64", "title": "是否变更价格标记0/1"}, "products": {"type": "array", "items": {"$ref": "#/definitions/purchase_reviewProduct"}, "title": "商品详情"}, "total_product": {"type": "integer", "format": "int64", "title": "总商品数"}, "branch_type": {"type": "string", "title": "区分门店仓库 WAREHOUSE/STORE"}, "company_id": {"type": "string", "format": "uint64", "title": "公司id"}, "process_status": {"type": "string", "title": "单据业务状态给发票勾兑使用 'INITED' 待勾兑 'COMPLETED'  # 已勾兑"}, "origin_code": {"type": "string", "title": "原单编号"}, "adjust_codes": {"type": "array", "items": {"type": "string"}, "title": "调价单号们"}, "is_adjust": {"type": "boolean", "format": "boolean", "title": "标记是否是调整单"}, "generate_adjust": {"type": "boolean", "format": "boolean", "title": "是否生成调价单"}, "cost_center_id": {"type": "string", "format": "uint64", "title": "成本中心id"}, "source_type": {"type": "string", "title": "单据来源：\n订货计划(SD)、紧急订货(HD)、总部分配(MD)、库存调整(AD)、对账调整(CAD)\n原单退货(BO)、非原单退货(NBO)、收货差异(REC_DIFF)、采购收货(PUR_REC)、采购退货(PUR_RETURN)"}, "reject_reason": {"type": "string", "title": "驳回原因"}}, "title": "采购复核单详情返回参数"}, "purchase_reviewListPurchaseReviewDetailResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/purchase_reviewPurchaseReviewDetail"}}, "total": {"type": "string", "format": "uint64"}}, "title": "采购复核单明细返回参数"}, "purchase_reviewListPurchaseReviewOrderResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/purchase_reviewOrderDetail"}}, "total": {"type": "string", "format": "uint64"}}, "title": "采购复核订单列表返回"}, "purchase_reviewOrderDetail": {"type": "object", "properties": {"order_id": {"type": "string", "format": "uint64", "title": "订单id"}, "order_code": {"type": "string", "title": "订单编号"}, "order_type": {"type": "string", "title": "单据类型"}, "status": {"type": "string", "title": "单据状态：\nINITED     # 待复核\nSUBMITTED  # 待确认\nAPPROVED   # 已复核\nREJECTED   # 已驳回"}, "received_date": {"type": "string", "format": "date-time", "title": "采购日期"}, "received_by": {"type": "string", "format": "uint64", "title": "收货仓库or门店id"}, "received_name": {"type": "string", "title": "收货仓库or门店名称"}, "received_code": {"type": "string", "title": "收货仓库or门店编号"}, "supplier_id": {"type": "string", "format": "uint64", "title": "供应商id or code"}, "supplier_name": {"type": "string", "title": "供应商名称"}, "supplier_code": {"type": "string", "title": "供应商编号"}, "sum_price_tax": {"type": "number", "format": "double", "title": "总含税金额"}, "orig_sum_price_tax": {"type": "number", "format": "double", "title": "原始总含税金额"}, "sum_price": {"type": "number", "format": "double", "title": "总未税金额"}, "orig_sum_price": {"type": "number", "format": "double", "title": "原始总未税金额"}, "review_by": {"type": "string", "format": "uint64", "title": "复核人"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人"}, "created_name": {"type": "string", "title": "创建人名字"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人"}, "updated_name": {"type": "string", "title": "更新人名字"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "is_modify": {"type": "integer", "format": "int64", "title": "是否变更价格标记0/1"}, "branch_type": {"type": "string", "title": "区分门店仓库 WAREHOUSE/STORE"}, "company_id": {"type": "string", "format": "uint64", "title": "公司id"}, "process_status": {"type": "string", "title": "单据业务状态给发票勾兑使用 INITED = 'INITED' 待勾兑 APPROVED = 'COMPLETED'  # 已勾兑"}, "origin_code": {"type": "string", "title": "原单编号"}, "adjust_info": {"type": "string", "title": "调价单信息"}, "is_adjust": {"type": "boolean", "format": "boolean", "title": "标记是否是调整单"}, "generate_adjust": {"type": "boolean", "format": "boolean", "title": "是否生成调价单"}, "cost_center_id": {"type": "string", "format": "uint64"}, "source_type": {"type": "string", "title": "单据来源：\n订货计划(SD)、紧急订货(HD)、总部分配(MD)、库存调整(AD)、对账调整(CAD)\n原单退货(BO)、非原单退货(NBO)、收货差异(REC_DIFF)、采购收货(PUR_REC)、采购退货(PUR_RETURN)"}}}, "purchase_reviewProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id(数据更新时必传)"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id(必传)"}, "product_code": {"type": "string", "title": "商品编号"}, "product_name": {"type": "string", "title": "商品名称"}, "product_category_id": {"type": "string", "format": "uint64", "title": "商品类别"}, "product_type": {"type": "string", "title": "商品类型"}, "unit_id": {"type": "string", "format": "uint64", "title": "单位id(门店中为订货单位/仓库为采购单位)"}, "unit_name": {"type": "string", "title": "单位名称"}, "unit_spec": {"type": "string", "title": "单位规格"}, "spec": {"type": "string", "title": "商品规格"}, "quantity": {"type": "number", "format": "double", "title": "订货/退货数量"}, "actual_quantity": {"type": "number", "format": "double", "title": "实收数量(必传)"}, "tax_rate": {"type": "number", "format": "double", "title": "税率(必传)"}, "price": {"type": "number", "format": "double", "title": "未税单价"}, "orig_price": {"type": "number", "format": "double", "title": "原始未税单价"}, "sum_price": {"type": "number", "format": "double", "title": "总未税价"}, "orig_sum_price": {"type": "number", "format": "double", "title": "原始未税合计"}, "price_tax": {"type": "number", "format": "double", "title": "含税单价(必传)"}, "prev_price_tax": {"type": "number", "format": "double", "title": "上次修改含税单价(必传)"}, "sum_price_tax": {"type": "number", "format": "double", "title": "含税总价"}, "orig_price_tax": {"type": "number", "format": "double", "title": "原始含税单价"}, "orig_sum_price_tax": {"type": "number", "format": "double", "title": "原始含税合计"}, "is_modify": {"type": "integer", "format": "int64", "title": "是否变更价格标记0/1 (有价格变动必传)"}, "adjust_logs": {"type": "array", "items": {"$ref": "#/definitions/purchase_reviewAdjustLog"}}, "sum_tax": {"type": "number", "format": "double", "title": "合计税额"}, "orig_sum_tax": {"type": "number", "format": "double", "title": "原始合计税额"}}}, "purchase_reviewPurchaseReviewDetail": {"type": "object", "properties": {"order_id": {"type": "string", "format": "uint64", "title": "订单id"}, "order_code": {"type": "string", "title": "订单编号"}, "order_type": {"type": "string", "title": "单据类型"}, "status": {"type": "string", "title": "单据状态"}, "received_date": {"type": "string", "format": "date-time", "title": "采购日期"}, "received_by": {"type": "string", "format": "uint64", "title": "收货仓库or门店id"}, "received_name": {"type": "string", "title": "收货仓库or门店名称"}, "received_code": {"type": "string", "title": "收货仓库or门店编号"}, "supplier_id": {"type": "string", "format": "uint64", "title": "供应商id or code"}, "supplier_name": {"type": "string", "title": "供应商名称"}, "supplier_code": {"type": "string", "title": "供应商编号"}, "review_by": {"type": "string", "format": "uint64", "title": "复核人"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人"}, "created_name": {"type": "string", "title": "创建人名字"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人"}, "updated_name": {"type": "string", "title": "更新人名字"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "is_modify": {"type": "integer", "format": "int64", "title": "是否变更价格标记0/1"}, "branch_type": {"type": "string", "title": "区分门店仓库 WAREHOUSE/STORE"}, "company_id": {"type": "string", "format": "uint64", "title": "公司id"}, "process_status": {"type": "string", "title": "单据业务状态给发票勾兑使用 'INITED' 待勾兑 'COMPLETED'  # 已勾兑"}, "origin_code": {"type": "string", "title": "原单编号"}, "adjust_codes": {"type": "array", "items": {"type": "string"}, "title": "调价单号们"}, "is_adjust": {"type": "boolean", "format": "boolean", "title": "标记是否是调整单"}, "generate_adjust": {"type": "boolean", "format": "boolean", "title": "是否生成调价单"}, "cost_center_id": {"type": "string", "format": "uint64", "title": "成本中心id"}, "source_type": {"type": "string", "title": "单据来源：\n订货计划(SD)、紧急订货(HD)、总部分配(MD)、库存调整(AD)、对账调整(CAD)\n原单退货(BO)、非原单退货(NBO)、收货差异(REC_DIFF)、采购收货(PUR_REC)、采购退货(PUR_RETURN)"}, "reject_reason": {"type": "string", "title": "驳回原因"}, "id": {"type": "string", "format": "uint64", "title": "唯一ID（商品在数据库中的主键）"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_code": {"type": "string", "title": "商品编号"}, "product_name": {"type": "string", "title": "商品名称"}, "product_category_id": {"type": "string", "format": "uint64", "title": "商品类别"}, "product_type": {"type": "string", "title": "商品类型"}, "unit_id": {"type": "string", "format": "uint64", "title": "单位id(门店中为订货单位/仓库为采购单位)"}, "unit_name": {"type": "string", "title": "单位名称"}, "unit_spec": {"type": "string", "title": "单位规格"}, "spec": {"type": "string", "title": "商品规格"}, "quantity": {"type": "number", "format": "double", "title": "订货/退货数量"}, "actual_quantity": {"type": "number", "format": "double", "title": "实收数量(必传)"}, "tax_rate": {"type": "number", "format": "double", "title": "税率(必传)"}, "price": {"type": "number", "format": "double", "title": "未税单价"}, "orig_price": {"type": "number", "format": "double", "title": "原始未税单价"}, "sum_price": {"type": "number", "format": "double", "title": "总未税价"}, "orig_sum_price": {"type": "number", "format": "double", "title": "原始未税合计"}, "price_tax": {"type": "number", "format": "double", "title": "含税单价(必传)"}, "prev_price_tax": {"type": "number", "format": "double", "title": "上次修改含税单价(必传)"}, "sum_price_tax": {"type": "number", "format": "double", "title": "含税总价"}, "orig_price_tax": {"type": "number", "format": "double", "title": "原始含税单价"}, "orig_sum_price_tax": {"type": "number", "format": "double", "title": "原始含税合计"}, "sum_tax": {"type": "number", "format": "double", "title": "合计税额"}, "orig_sum_tax": {"type": "number", "format": "double", "title": "原始合计税额"}}}, "purchase_reviewUpdatePurchaseReviewOrderRequest": {"type": "object", "properties": {"order_id": {"type": "string", "format": "uint64", "title": "单据id"}, "products": {"type": "array", "items": {"$ref": "#/definitions/purchase_reviewProduct"}, "title": "更新商品明细"}, "status": {"type": "string", "title": "更新同时仅支持提交:SUBMITTED"}}, "title": "更新采购复核详情请求参数"}, "purchase_reviewUpdatePurchaseReviewOrderResponse": {"type": "object", "properties": {"order_id": {"type": "string", "format": "uint64"}, "result": {"type": "string"}}, "title": "更新采购复核详情返回参数"}}}