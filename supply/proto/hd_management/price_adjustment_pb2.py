# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: hd_management/price_adjustment.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='hd_management/price_adjustment.proto',
  package='price_adjustment',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n$hd_management/price_adjustment.proto\x12\x10price_adjustment\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x87\x04\n\x1c\x43reatePriceAdjustmentRequest\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\x12\x12\n\nbatch_code\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x12\n\norder_type\x18\x03 \x01(\t\x12/\n\x0b\x61\x64just_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tbranch_id\x18\x06 \x01(\x04\x12\x13\n\x0b\x62ranch_name\x18\x07 \x01(\t\x12\x13\n\x0b\x62ranch_code\x18\x08 \x01(\t\x12\x13\n\x0bsupplier_id\x18\t \x01(\x04\x12\x15\n\rsupplier_name\x18\n \x01(\t\x12\x15\n\rsupplier_code\x18\x0b \x01(\t\x12\x15\n\rsum_price_tax\x18\x0c \x01(\x01\x12\x1c\n\x14\x61\x64just_sum_price_tax\x18\r \x01(\x01\x12\x11\n\tsum_price\x18\x0e \x01(\x01\x12\x18\n\x10\x61\x64just_sum_price\x18\x0f \x01(\x01\x12-\n\x08products\x18\x10 \x03(\x0b\x32\x1b.price_adjustment.AdProduct\x12\x13\n\x0b\x62ranch_type\x18\x11 \x01(\t\x12\x19\n\x11\x63ost_trans_status\x18\x12 \x01(\x08\x12\x13\n\x0b\x63ost_update\x18\x13 \x01(\r\x12\x16\n\x0e\x63ost_center_id\x18\x14 \x01(\x04\"\xc2\x05\n\tAdProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x12\n\ncreated_by\x18\x03 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x04 \x01(\t\x12\x12\n\nupdated_by\x18\x05 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x06 \x01(\t\x12\x11\n\tadjust_id\x18\x07 \x01(\x04\x12\x12\n\nproduct_id\x18\x08 \x01(\x04\x12\x14\n\x0cproduct_code\x18\t \x01(\t\x12\x14\n\x0cproduct_name\x18\n \x01(\t\x12\x1b\n\x13product_category_id\x18\x0b \x01(\x04\x12\x14\n\x0cproduct_type\x18\x0c \x01(\t\x12\x0f\n\x07unit_id\x18\r \x01(\x04\x12\x11\n\tunit_name\x18\x0e \x01(\t\x12\x11\n\tunit_spec\x18\x0f \x01(\t\x12\x0c\n\x04spec\x18\x10 \x01(\t\x12\x10\n\x08quantity\x18\x11 \x01(\x01\x12\x17\n\x0f\x61\x63tual_quantity\x18\x12 \x01(\x01\x12\x10\n\x08tax_rate\x18\x13 \x01(\x01\x12\r\n\x05price\x18\x14 \x01(\x01\x12\x11\n\tpre_price\x18\x15 \x01(\x01\x12\x11\n\tsum_price\x18\x16 \x01(\x01\x12\x15\n\rpre_sum_price\x18\x17 \x01(\x01\x12\x11\n\tprice_tax\x18\x18 \x01(\x01\x12\x15\n\rsum_price_tax\x18\x19 \x01(\x01\x12\x15\n\rpre_price_tax\x18\x1a \x01(\x01\x12\x19\n\x11pre_sum_price_tax\x18\x1b \x01(\x01\x12\x0b\n\x03tax\x18\x1c \x01(\x01\x12\x0f\n\x07pre_tax\x18\x1d \x01(\x01\x12.\n\ncreated_at\x18\x1e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x61\x64just_code\x18  \x01(\t\";\n\x1d\x43reatePriceAdjustmentResponse\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\"\x85\x03\n\x1aListPriceAdjustmentRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\norder_code\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x12\n\norder_type\x18\x05 \x01(\t\x12\x14\n\x0csupplier_ids\x18\x07 \x03(\x04\x12\x12\n\nbranch_ids\x18\x08 \x03(\x04\x12\x12\n\nbatch_code\x18\t \x01(\t\x12\r\n\x05limit\x18\n \x01(\x03\x12\x0e\n\x06offset\x18\x0b \x01(\x04\x12\x15\n\rinclude_total\x18\x0c \x01(\x08\x12\r\n\x05order\x18\r \x01(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x0f \x01(\t\x12\x12\n\ncompany_id\x18\x10 \x01(\x04\x12\x17\n\x0f\x62lending_status\x18\x11 \x01(\t\"\xd7\x05\n\x12PriceAdjustmentRow\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\x12\x12\n\nbatch_code\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x12\n\norder_type\x18\x04 \x01(\t\x12/\n\x0b\x61\x64just_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tbranch_id\x18\x06 \x01(\x04\x12\x13\n\x0b\x62ranch_name\x18\x07 \x01(\t\x12\x13\n\x0b\x62ranch_code\x18\x08 \x01(\t\x12\x13\n\x0bsupplier_id\x18\t \x01(\x04\x12\x15\n\rsupplier_name\x18\n \x01(\t\x12\x15\n\rsupplier_code\x18\x0b \x01(\t\x12\x15\n\rsum_price_tax\x18\x0c \x01(\x01\x12\x19\n\x11pre_sum_price_tax\x18\r \x01(\x01\x12\x11\n\tsum_price\x18\x0e \x01(\x01\x12\x15\n\rpre_sum_price\x18\x0f \x01(\x01\x12\x13\n\x0b\x62ranch_type\x18\x11 \x01(\t\x12\x19\n\x11\x63ost_trans_status\x18\x12 \x01(\x08\x12\x13\n\x0b\x63ost_update\x18\x13 \x01(\r\x12\x12\n\npartner_id\x18\x14 \x01(\x04\x12\x12\n\ncreated_by\x18\x15 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x16 \x01(\t\x12\x12\n\nupdated_by\x18\x17 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x18 \x01(\t\x12.\n\ncreated_at\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\n\n\x02id\x18\x1b \x01(\x04\x12\x0c\n\x04\x63ode\x18\x1c \x01(\t\x12\x16\n\x0e\x63ost_center_id\x18\x1d \x01(\x04\x12\x12\n\ncompany_id\x18\x1e \x01(\x04\x12\x17\n\x0f\x62lending_status\x18\x1f \x01(\t\"`\n\x1bListPriceAdjustmentResponse\x12\x32\n\x04rows\x18\x01 \x03(\x0b\x32$.price_adjustment.PriceAdjustmentRow\x12\r\n\x05total\x18\x02 \x01(\x04\"I\n\x1fGetPriceAdjustmentDetailRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x13\n\x0b\x61\x64just_code\x18\x02 \x01(\t\"\x94\x06\n GetPriceAdjustmentDetailResponse\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\x12\x12\n\nbatch_code\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x12\n\norder_type\x18\x04 \x01(\t\x12/\n\x0b\x61\x64just_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tbranch_id\x18\x06 \x01(\x04\x12\x13\n\x0b\x62ranch_name\x18\x07 \x01(\t\x12\x13\n\x0b\x62ranch_code\x18\x08 \x01(\t\x12\x13\n\x0bsupplier_id\x18\t \x01(\x04\x12\x15\n\rsupplier_name\x18\n \x01(\t\x12\x15\n\rsupplier_code\x18\x0b \x01(\t\x12\x15\n\rsum_price_tax\x18\x0c \x01(\x01\x12\x19\n\x11pre_sum_price_tax\x18\r \x01(\x01\x12\x11\n\tsum_price\x18\x0e \x01(\x01\x12\x15\n\rpre_sum_price\x18\x0f \x01(\x01\x12\x13\n\x0b\x62ranch_type\x18\x11 \x01(\t\x12\x19\n\x11\x63ost_trans_status\x18\x12 \x01(\x08\x12\x13\n\x0b\x63ost_update\x18\x13 \x01(\r\x12\x12\n\npartner_id\x18\x14 \x01(\x04\x12\x12\n\ncreated_by\x18\x15 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x16 \x01(\t\x12\x12\n\nupdated_by\x18\x17 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x18 \x01(\t\x12.\n\ncreated_at\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\n\n\x02id\x18\x1b \x01(\x04\x12\x0c\n\x04\x63ode\x18\x1c \x01(\t\x12\x16\n\x0e\x63ost_center_id\x18\x1e \x01(\x04\x12-\n\x08products\x18\x1d \x03(\x0b\x32\x1b.price_adjustment.AdProduct\x12\x12\n\ncompany_id\x18  \x01(\x04\x12\x17\n\x0f\x62lending_status\x18! \x01(\t\"\x83\x01\n\x1cUpdatePriceAdjustmentRequest\x12\x0b\n\x03ids\x18\x01 \x03(\x04\x12\x17\n\x0finclude_product\x18\x02 \x01(\x08\x12-\n\x08products\x18\x03 \x03(\x0b\x32\x1b.price_adjustment.AdProduct\x12\x0e\n\x06status\x18\x04 \x01(\t\"<\n\x1dUpdatePriceAdjustmentResponse\x12\x0b\n\x03ids\x18\x01 \x03(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t2\xc4\x05\n\x0fPriceAdjustment\x12\xab\x01\n\x15\x43reatePriceAdjustment\x12..price_adjustment.CreatePriceAdjustmentRequest\x1a/.price_adjustment.CreatePriceAdjustmentResponse\"1\x82\xd3\xe4\x93\x02+\"&/api/v2/supply/create/price/adjustment:\x01*\x12\xa0\x01\n\x13ListPriceAdjustment\x12,.price_adjustment.ListPriceAdjustmentRequest\x1a-.price_adjustment.ListPriceAdjustmentResponse\",\x82\xd3\xe4\x93\x02&\x12$/api/v2/supply/price/adjustment/list\x12\xb1\x01\n\x18GetPriceAdjustmentDetail\x12\x31.price_adjustment.GetPriceAdjustmentDetailRequest\x1a\x32.price_adjustment.GetPriceAdjustmentDetailResponse\".\x82\xd3\xe4\x93\x02(\x12&/api/v2/supply/price/adjustment/detail\x12\xab\x01\n\x15UpdatePriceAdjustment\x12..price_adjustment.UpdatePriceAdjustmentRequest\x1a/.price_adjustment.UpdatePriceAdjustmentResponse\"1\x82\xd3\xe4\x93\x02+\x1a&/api/v2/supply/price/adjustment/update:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_CREATEPRICEADJUSTMENTREQUEST = _descriptor.Descriptor(
  name='CreatePriceAdjustmentRequest',
  full_name='price_adjustment.CreatePriceAdjustmentRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='price_adjustment.CreatePriceAdjustmentRequest.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='price_adjustment.CreatePriceAdjustmentRequest.batch_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='price_adjustment.CreatePriceAdjustmentRequest.status', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='price_adjustment.CreatePriceAdjustmentRequest.order_type', index=3,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='price_adjustment.CreatePriceAdjustmentRequest.adjust_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='price_adjustment.CreatePriceAdjustmentRequest.branch_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='price_adjustment.CreatePriceAdjustmentRequest.branch_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_code', full_name='price_adjustment.CreatePriceAdjustmentRequest.branch_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_id', full_name='price_adjustment.CreatePriceAdjustmentRequest.supplier_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_name', full_name='price_adjustment.CreatePriceAdjustmentRequest.supplier_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_code', full_name='price_adjustment.CreatePriceAdjustmentRequest.supplier_code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price_tax', full_name='price_adjustment.CreatePriceAdjustmentRequest.sum_price_tax', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_sum_price_tax', full_name='price_adjustment.CreatePriceAdjustmentRequest.adjust_sum_price_tax', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price', full_name='price_adjustment.CreatePriceAdjustmentRequest.sum_price', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_sum_price', full_name='price_adjustment.CreatePriceAdjustmentRequest.adjust_sum_price', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='price_adjustment.CreatePriceAdjustmentRequest.products', index=15,
      number=16, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='price_adjustment.CreatePriceAdjustmentRequest.branch_type', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_trans_status', full_name='price_adjustment.CreatePriceAdjustmentRequest.cost_trans_status', index=17,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_update', full_name='price_adjustment.CreatePriceAdjustmentRequest.cost_update', index=18,
      number=19, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='price_adjustment.CreatePriceAdjustmentRequest.cost_center_id', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=122,
  serialized_end=641,
)


_ADPRODUCT = _descriptor.Descriptor(
  name='AdProduct',
  full_name='price_adjustment.AdProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='price_adjustment.AdProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='price_adjustment.AdProduct.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='price_adjustment.AdProduct.created_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='price_adjustment.AdProduct.created_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='price_adjustment.AdProduct.updated_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='price_adjustment.AdProduct.updated_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='price_adjustment.AdProduct.adjust_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='price_adjustment.AdProduct.product_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='price_adjustment.AdProduct.product_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='price_adjustment.AdProduct.product_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_category_id', full_name='price_adjustment.AdProduct.product_category_id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='price_adjustment.AdProduct.product_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='price_adjustment.AdProduct.unit_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='price_adjustment.AdProduct.unit_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='price_adjustment.AdProduct.unit_spec', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='price_adjustment.AdProduct.spec', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='price_adjustment.AdProduct.quantity', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actual_quantity', full_name='price_adjustment.AdProduct.actual_quantity', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='price_adjustment.AdProduct.tax_rate', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='price_adjustment.AdProduct.price', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_price', full_name='price_adjustment.AdProduct.pre_price', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price', full_name='price_adjustment.AdProduct.sum_price', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_sum_price', full_name='price_adjustment.AdProduct.pre_sum_price', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_tax', full_name='price_adjustment.AdProduct.price_tax', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price_tax', full_name='price_adjustment.AdProduct.sum_price_tax', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_price_tax', full_name='price_adjustment.AdProduct.pre_price_tax', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_sum_price_tax', full_name='price_adjustment.AdProduct.pre_sum_price_tax', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax', full_name='price_adjustment.AdProduct.tax', index=27,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_tax', full_name='price_adjustment.AdProduct.pre_tax', index=28,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='price_adjustment.AdProduct.created_at', index=29,
      number=30, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='price_adjustment.AdProduct.updated_at', index=30,
      number=31, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_code', full_name='price_adjustment.AdProduct.adjust_code', index=31,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=644,
  serialized_end=1350,
)


_CREATEPRICEADJUSTMENTRESPONSE = _descriptor.Descriptor(
  name='CreatePriceAdjustmentResponse',
  full_name='price_adjustment.CreatePriceAdjustmentResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='price_adjustment.CreatePriceAdjustmentResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='price_adjustment.CreatePriceAdjustmentResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1352,
  serialized_end=1411,
)


_LISTPRICEADJUSTMENTREQUEST = _descriptor.Descriptor(
  name='ListPriceAdjustmentRequest',
  full_name='price_adjustment.ListPriceAdjustmentRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='price_adjustment.ListPriceAdjustmentRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='price_adjustment.ListPriceAdjustmentRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='price_adjustment.ListPriceAdjustmentRequest.order_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='price_adjustment.ListPriceAdjustmentRequest.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='price_adjustment.ListPriceAdjustmentRequest.order_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_ids', full_name='price_adjustment.ListPriceAdjustmentRequest.supplier_ids', index=5,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='price_adjustment.ListPriceAdjustmentRequest.branch_ids', index=6,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='price_adjustment.ListPriceAdjustmentRequest.batch_code', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='price_adjustment.ListPriceAdjustmentRequest.limit', index=8,
      number=10, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='price_adjustment.ListPriceAdjustmentRequest.offset', index=9,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='price_adjustment.ListPriceAdjustmentRequest.include_total', index=10,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='price_adjustment.ListPriceAdjustmentRequest.order', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='price_adjustment.ListPriceAdjustmentRequest.sort', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='price_adjustment.ListPriceAdjustmentRequest.branch_type', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_id', full_name='price_adjustment.ListPriceAdjustmentRequest.company_id', index=14,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='blending_status', full_name='price_adjustment.ListPriceAdjustmentRequest.blending_status', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1414,
  serialized_end=1803,
)


_PRICEADJUSTMENTROW = _descriptor.Descriptor(
  name='PriceAdjustmentRow',
  full_name='price_adjustment.PriceAdjustmentRow',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='price_adjustment.PriceAdjustmentRow.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='price_adjustment.PriceAdjustmentRow.batch_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='price_adjustment.PriceAdjustmentRow.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='price_adjustment.PriceAdjustmentRow.order_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='price_adjustment.PriceAdjustmentRow.adjust_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='price_adjustment.PriceAdjustmentRow.branch_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='price_adjustment.PriceAdjustmentRow.branch_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_code', full_name='price_adjustment.PriceAdjustmentRow.branch_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_id', full_name='price_adjustment.PriceAdjustmentRow.supplier_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_name', full_name='price_adjustment.PriceAdjustmentRow.supplier_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_code', full_name='price_adjustment.PriceAdjustmentRow.supplier_code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price_tax', full_name='price_adjustment.PriceAdjustmentRow.sum_price_tax', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_sum_price_tax', full_name='price_adjustment.PriceAdjustmentRow.pre_sum_price_tax', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price', full_name='price_adjustment.PriceAdjustmentRow.sum_price', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_sum_price', full_name='price_adjustment.PriceAdjustmentRow.pre_sum_price', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='price_adjustment.PriceAdjustmentRow.branch_type', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_trans_status', full_name='price_adjustment.PriceAdjustmentRow.cost_trans_status', index=16,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_update', full_name='price_adjustment.PriceAdjustmentRow.cost_update', index=17,
      number=19, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='price_adjustment.PriceAdjustmentRow.partner_id', index=18,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='price_adjustment.PriceAdjustmentRow.created_by', index=19,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='price_adjustment.PriceAdjustmentRow.created_name', index=20,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='price_adjustment.PriceAdjustmentRow.updated_by', index=21,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='price_adjustment.PriceAdjustmentRow.updated_name', index=22,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='price_adjustment.PriceAdjustmentRow.created_at', index=23,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='price_adjustment.PriceAdjustmentRow.updated_at', index=24,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='price_adjustment.PriceAdjustmentRow.id', index=25,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='price_adjustment.PriceAdjustmentRow.code', index=26,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='price_adjustment.PriceAdjustmentRow.cost_center_id', index=27,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_id', full_name='price_adjustment.PriceAdjustmentRow.company_id', index=28,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='blending_status', full_name='price_adjustment.PriceAdjustmentRow.blending_status', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1806,
  serialized_end=2533,
)


_LISTPRICEADJUSTMENTRESPONSE = _descriptor.Descriptor(
  name='ListPriceAdjustmentResponse',
  full_name='price_adjustment.ListPriceAdjustmentResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='price_adjustment.ListPriceAdjustmentResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='price_adjustment.ListPriceAdjustmentResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2535,
  serialized_end=2631,
)


_GETPRICEADJUSTMENTDETAILREQUEST = _descriptor.Descriptor(
  name='GetPriceAdjustmentDetailRequest',
  full_name='price_adjustment.GetPriceAdjustmentDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='price_adjustment.GetPriceAdjustmentDetailRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_code', full_name='price_adjustment.GetPriceAdjustmentDetailRequest.adjust_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2633,
  serialized_end=2706,
)


_GETPRICEADJUSTMENTDETAILRESPONSE = _descriptor.Descriptor(
  name='GetPriceAdjustmentDetailResponse',
  full_name='price_adjustment.GetPriceAdjustmentDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.batch_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.order_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.adjust_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.branch_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.branch_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_code', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.branch_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_id', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.supplier_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_name', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.supplier_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_code', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.supplier_code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price_tax', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.sum_price_tax', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_sum_price_tax', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.pre_sum_price_tax', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.sum_price', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_sum_price', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.pre_sum_price', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.branch_type', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_trans_status', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.cost_trans_status', index=16,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_update', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.cost_update', index=17,
      number=19, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.partner_id', index=18,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.created_by', index=19,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.created_name', index=20,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.updated_by', index=21,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.updated_name', index=22,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.created_at', index=23,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.updated_at', index=24,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.id', index=25,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.code', index=26,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.cost_center_id', index=27,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.products', index=28,
      number=29, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_id', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.company_id', index=29,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='blending_status', full_name='price_adjustment.GetPriceAdjustmentDetailResponse.blending_status', index=30,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2709,
  serialized_end=3497,
)


_UPDATEPRICEADJUSTMENTREQUEST = _descriptor.Descriptor(
  name='UpdatePriceAdjustmentRequest',
  full_name='price_adjustment.UpdatePriceAdjustmentRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ids', full_name='price_adjustment.UpdatePriceAdjustmentRequest.ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_product', full_name='price_adjustment.UpdatePriceAdjustmentRequest.include_product', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='price_adjustment.UpdatePriceAdjustmentRequest.products', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='price_adjustment.UpdatePriceAdjustmentRequest.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3500,
  serialized_end=3631,
)


_UPDATEPRICEADJUSTMENTRESPONSE = _descriptor.Descriptor(
  name='UpdatePriceAdjustmentResponse',
  full_name='price_adjustment.UpdatePriceAdjustmentResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ids', full_name='price_adjustment.UpdatePriceAdjustmentResponse.ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='price_adjustment.UpdatePriceAdjustmentResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3633,
  serialized_end=3693,
)

_CREATEPRICEADJUSTMENTREQUEST.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEPRICEADJUSTMENTREQUEST.fields_by_name['products'].message_type = _ADPRODUCT
_ADPRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPRICEADJUSTMENTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPRICEADJUSTMENTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRICEADJUSTMENTROW.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRICEADJUSTMENTROW.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRICEADJUSTMENTROW.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPRICEADJUSTMENTRESPONSE.fields_by_name['rows'].message_type = _PRICEADJUSTMENTROW
_GETPRICEADJUSTMENTDETAILRESPONSE.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPRICEADJUSTMENTDETAILRESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPRICEADJUSTMENTDETAILRESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPRICEADJUSTMENTDETAILRESPONSE.fields_by_name['products'].message_type = _ADPRODUCT
_UPDATEPRICEADJUSTMENTREQUEST.fields_by_name['products'].message_type = _ADPRODUCT
DESCRIPTOR.message_types_by_name['CreatePriceAdjustmentRequest'] = _CREATEPRICEADJUSTMENTREQUEST
DESCRIPTOR.message_types_by_name['AdProduct'] = _ADPRODUCT
DESCRIPTOR.message_types_by_name['CreatePriceAdjustmentResponse'] = _CREATEPRICEADJUSTMENTRESPONSE
DESCRIPTOR.message_types_by_name['ListPriceAdjustmentRequest'] = _LISTPRICEADJUSTMENTREQUEST
DESCRIPTOR.message_types_by_name['PriceAdjustmentRow'] = _PRICEADJUSTMENTROW
DESCRIPTOR.message_types_by_name['ListPriceAdjustmentResponse'] = _LISTPRICEADJUSTMENTRESPONSE
DESCRIPTOR.message_types_by_name['GetPriceAdjustmentDetailRequest'] = _GETPRICEADJUSTMENTDETAILREQUEST
DESCRIPTOR.message_types_by_name['GetPriceAdjustmentDetailResponse'] = _GETPRICEADJUSTMENTDETAILRESPONSE
DESCRIPTOR.message_types_by_name['UpdatePriceAdjustmentRequest'] = _UPDATEPRICEADJUSTMENTREQUEST
DESCRIPTOR.message_types_by_name['UpdatePriceAdjustmentResponse'] = _UPDATEPRICEADJUSTMENTRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CreatePriceAdjustmentRequest = _reflection.GeneratedProtocolMessageType('CreatePriceAdjustmentRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPRICEADJUSTMENTREQUEST,
  __module__ = 'hd_management.price_adjustment_pb2'
  # @@protoc_insertion_point(class_scope:price_adjustment.CreatePriceAdjustmentRequest)
  ))
_sym_db.RegisterMessage(CreatePriceAdjustmentRequest)

AdProduct = _reflection.GeneratedProtocolMessageType('AdProduct', (_message.Message,), dict(
  DESCRIPTOR = _ADPRODUCT,
  __module__ = 'hd_management.price_adjustment_pb2'
  # @@protoc_insertion_point(class_scope:price_adjustment.AdProduct)
  ))
_sym_db.RegisterMessage(AdProduct)

CreatePriceAdjustmentResponse = _reflection.GeneratedProtocolMessageType('CreatePriceAdjustmentResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPRICEADJUSTMENTRESPONSE,
  __module__ = 'hd_management.price_adjustment_pb2'
  # @@protoc_insertion_point(class_scope:price_adjustment.CreatePriceAdjustmentResponse)
  ))
_sym_db.RegisterMessage(CreatePriceAdjustmentResponse)

ListPriceAdjustmentRequest = _reflection.GeneratedProtocolMessageType('ListPriceAdjustmentRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTPRICEADJUSTMENTREQUEST,
  __module__ = 'hd_management.price_adjustment_pb2'
  # @@protoc_insertion_point(class_scope:price_adjustment.ListPriceAdjustmentRequest)
  ))
_sym_db.RegisterMessage(ListPriceAdjustmentRequest)

PriceAdjustmentRow = _reflection.GeneratedProtocolMessageType('PriceAdjustmentRow', (_message.Message,), dict(
  DESCRIPTOR = _PRICEADJUSTMENTROW,
  __module__ = 'hd_management.price_adjustment_pb2'
  # @@protoc_insertion_point(class_scope:price_adjustment.PriceAdjustmentRow)
  ))
_sym_db.RegisterMessage(PriceAdjustmentRow)

ListPriceAdjustmentResponse = _reflection.GeneratedProtocolMessageType('ListPriceAdjustmentResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTPRICEADJUSTMENTRESPONSE,
  __module__ = 'hd_management.price_adjustment_pb2'
  # @@protoc_insertion_point(class_scope:price_adjustment.ListPriceAdjustmentResponse)
  ))
_sym_db.RegisterMessage(ListPriceAdjustmentResponse)

GetPriceAdjustmentDetailRequest = _reflection.GeneratedProtocolMessageType('GetPriceAdjustmentDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRICEADJUSTMENTDETAILREQUEST,
  __module__ = 'hd_management.price_adjustment_pb2'
  # @@protoc_insertion_point(class_scope:price_adjustment.GetPriceAdjustmentDetailRequest)
  ))
_sym_db.RegisterMessage(GetPriceAdjustmentDetailRequest)

GetPriceAdjustmentDetailResponse = _reflection.GeneratedProtocolMessageType('GetPriceAdjustmentDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPRICEADJUSTMENTDETAILRESPONSE,
  __module__ = 'hd_management.price_adjustment_pb2'
  # @@protoc_insertion_point(class_scope:price_adjustment.GetPriceAdjustmentDetailResponse)
  ))
_sym_db.RegisterMessage(GetPriceAdjustmentDetailResponse)

UpdatePriceAdjustmentRequest = _reflection.GeneratedProtocolMessageType('UpdatePriceAdjustmentRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPRICEADJUSTMENTREQUEST,
  __module__ = 'hd_management.price_adjustment_pb2'
  # @@protoc_insertion_point(class_scope:price_adjustment.UpdatePriceAdjustmentRequest)
  ))
_sym_db.RegisterMessage(UpdatePriceAdjustmentRequest)

UpdatePriceAdjustmentResponse = _reflection.GeneratedProtocolMessageType('UpdatePriceAdjustmentResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPRICEADJUSTMENTRESPONSE,
  __module__ = 'hd_management.price_adjustment_pb2'
  # @@protoc_insertion_point(class_scope:price_adjustment.UpdatePriceAdjustmentResponse)
  ))
_sym_db.RegisterMessage(UpdatePriceAdjustmentResponse)



_PRICEADJUSTMENT = _descriptor.ServiceDescriptor(
  name='PriceAdjustment',
  full_name='price_adjustment.PriceAdjustment',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=3696,
  serialized_end=4404,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreatePriceAdjustment',
    full_name='price_adjustment.PriceAdjustment.CreatePriceAdjustment',
    index=0,
    containing_service=None,
    input_type=_CREATEPRICEADJUSTMENTREQUEST,
    output_type=_CREATEPRICEADJUSTMENTRESPONSE,
    serialized_options=_b('\202\323\344\223\002+\"&/api/v2/supply/create/price/adjustment:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListPriceAdjustment',
    full_name='price_adjustment.PriceAdjustment.ListPriceAdjustment',
    index=1,
    containing_service=None,
    input_type=_LISTPRICEADJUSTMENTREQUEST,
    output_type=_LISTPRICEADJUSTMENTRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\022$/api/v2/supply/price/adjustment/list'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPriceAdjustmentDetail',
    full_name='price_adjustment.PriceAdjustment.GetPriceAdjustmentDetail',
    index=2,
    containing_service=None,
    input_type=_GETPRICEADJUSTMENTDETAILREQUEST,
    output_type=_GETPRICEADJUSTMENTDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\002(\022&/api/v2/supply/price/adjustment/detail'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdatePriceAdjustment',
    full_name='price_adjustment.PriceAdjustment.UpdatePriceAdjustment',
    index=3,
    containing_service=None,
    input_type=_UPDATEPRICEADJUSTMENTREQUEST,
    output_type=_UPDATEPRICEADJUSTMENTRESPONSE,
    serialized_options=_b('\202\323\344\223\002+\032&/api/v2/supply/price/adjustment/update:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_PRICEADJUSTMENT)

DESCRIPTOR.services_by_name['PriceAdjustment'] = _PRICEADJUSTMENT

# @@protoc_insertion_point(module_scope)
