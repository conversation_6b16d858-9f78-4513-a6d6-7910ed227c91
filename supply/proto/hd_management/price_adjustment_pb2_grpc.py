# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from hd_management import price_adjustment_pb2 as hd__management_dot_price__adjustment__pb2


class PriceAdjustmentStub(object):
  """调价单
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreatePriceAdjustment = channel.unary_unary(
        '/price_adjustment.PriceAdjustment/CreatePriceAdjustment',
        request_serializer=hd__management_dot_price__adjustment__pb2.CreatePriceAdjustmentRequest.SerializeToString,
        response_deserializer=hd__management_dot_price__adjustment__pb2.CreatePriceAdjustmentResponse.FromString,
        )
    self.ListPriceAdjustment = channel.unary_unary(
        '/price_adjustment.PriceAdjustment/ListPriceAdjustment',
        request_serializer=hd__management_dot_price__adjustment__pb2.ListPriceAdjustmentRequest.SerializeToString,
        response_deserializer=hd__management_dot_price__adjustment__pb2.ListPriceAdjustmentResponse.FromString,
        )
    self.GetPriceAdjustmentDetail = channel.unary_unary(
        '/price_adjustment.PriceAdjustment/GetPriceAdjustmentDetail',
        request_serializer=hd__management_dot_price__adjustment__pb2.GetPriceAdjustmentDetailRequest.SerializeToString,
        response_deserializer=hd__management_dot_price__adjustment__pb2.GetPriceAdjustmentDetailResponse.FromString,
        )
    self.UpdatePriceAdjustment = channel.unary_unary(
        '/price_adjustment.PriceAdjustment/UpdatePriceAdjustment',
        request_serializer=hd__management_dot_price__adjustment__pb2.UpdatePriceAdjustmentRequest.SerializeToString,
        response_deserializer=hd__management_dot_price__adjustment__pb2.UpdatePriceAdjustmentResponse.FromString,
        )


class PriceAdjustmentServicer(object):
  """调价单
  """

  def CreatePriceAdjustment(self, request, context):
    """创建调价单接口 暂未启用
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListPriceAdjustment(self, request, context):
    """调价单列表查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPriceAdjustmentDetail(self, request, context):
    """查询调价单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdatePriceAdjustment(self, request, context):
    """调价单复核(修改状态) 暂未启用
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_PriceAdjustmentServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreatePriceAdjustment': grpc.unary_unary_rpc_method_handler(
          servicer.CreatePriceAdjustment,
          request_deserializer=hd__management_dot_price__adjustment__pb2.CreatePriceAdjustmentRequest.FromString,
          response_serializer=hd__management_dot_price__adjustment__pb2.CreatePriceAdjustmentResponse.SerializeToString,
      ),
      'ListPriceAdjustment': grpc.unary_unary_rpc_method_handler(
          servicer.ListPriceAdjustment,
          request_deserializer=hd__management_dot_price__adjustment__pb2.ListPriceAdjustmentRequest.FromString,
          response_serializer=hd__management_dot_price__adjustment__pb2.ListPriceAdjustmentResponse.SerializeToString,
      ),
      'GetPriceAdjustmentDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetPriceAdjustmentDetail,
          request_deserializer=hd__management_dot_price__adjustment__pb2.GetPriceAdjustmentDetailRequest.FromString,
          response_serializer=hd__management_dot_price__adjustment__pb2.GetPriceAdjustmentDetailResponse.SerializeToString,
      ),
      'UpdatePriceAdjustment': grpc.unary_unary_rpc_method_handler(
          servicer.UpdatePriceAdjustment,
          request_deserializer=hd__management_dot_price__adjustment__pb2.UpdatePriceAdjustmentRequest.FromString,
          response_serializer=hd__management_dot_price__adjustment__pb2.UpdatePriceAdjustmentResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'price_adjustment.PriceAdjustment', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
