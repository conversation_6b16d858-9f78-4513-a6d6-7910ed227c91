syntax = "proto3";

package purchase_review;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";


service PurchaseReview {
    // 创建采购复核单接口
    rpc CreatePurchaseReviewOrder (CreatePurchaseReviewOrderRequest) returns (CreatePurchaseReviewOrderResponse){
        option (google.api.http) = {
        post: "/api/v2/supply/purchase/review/order"
        body: "*"
        };
    }
    // 采购复核单列表查询
    rpc ListPurchaseReviewOrder (ListPurchaseReviewOrderRequest) returns (ListPurchaseReviewOrderResponse){
        option (google.api.http) = {
        get: "/api/v2/supply/purchase/review/list"
        };
    }
    // 查询采购复核单详情
    rpc GetPurchaseReviewDetail (GetPurchaseReviewDetailRequest) returns (GetPurchaseReviewDetailResponse){
        option (google.api.http) = {
        get: "/api/v2/supply/purchase/review/detail"
        };
    }
    // 更新采购复核详情
    rpc UpdatePurchaseReviewOrder (UpdatePurchaseReviewOrderRequest) returns (UpdatePurchaseReviewOrderResponse){
        option (google.api.http) = {
        put: "/api/v2/supply/purchase/review/{order_id}/update"
        body: "*"
        };
    }
    // 修改采购复核状态
    rpc ChangePurchaseReviewOrderStatus (ChangePurchaseReviewOrderStatusRequest) returns (ChangePurchaseReviewOrderStatusResponse){
        option (google.api.http) = {
        put: "/api/v2/supply/purchase/review/{status}/modify"
        body: "*"
        };
    }
    // 查询采购复核单明细
    rpc ListPurchaseReviewDetail (ListPurchaseReviewOrderRequest) returns (ListPurchaseReviewDetailResponse){
        option (google.api.http) = {
        get: "/api/v2/supply/purchase/review/detail/list"
        };
    }
}

// 创建采购复核单请求参数
message CreatePurchaseReviewOrderRequest{
    // 主单id
    uint64 batch_id = 1;
    // 订单类型-收货单RECEIVE/退货单RETURN
    string order_type = 2;
    // 收货时间
    google.protobuf.Timestamp received_date = 3;
    // 门店或仓库id
    uint64 received_by = 4;
    // 门店或仓库名称
    string received_name = 5;
    // 门店或仓库编号
    string received_code = 6;
    // 供应商id
    uint64 supplier_id = 7;
    // 供应商名称
    string supplier_name = 8;
    // 供应商编号
    string supplier_code = 9;
    // 总含税金额
    double sum_price_tax = 10;
    // 总未税金额
    double sum_price = 11;
    // 商品信息
    repeated Product products = 12;
    // 区分门店仓库 WAREHOUSE/STORE
    string branch_type = 13;
    // 公司id
    uint64 company_id = 14;
    // 原单编号
    string origin_code = 15;
    // 调价单信息: {adjust_codes: [...]}
    string adjust_info = 16;
    // 标记是否是调整单
    bool is_adjust = 17;
    uint64 cost_center_id = 18;
    // 单据来源：
    // 订货计划(SD)、紧急订货(HD)、总部分配(MD)、库存调整(AD)、对账调整(CAD)
    // 原单退货(BO)、非原单退货(NBO)、收货差异(REC_DIFF)、采购收货(PUR_REC)、采购退货(PUR_RETURN)
    string source_type = 19;
}
message Product {
    // id(数据更新时必传)
    uint64 id = 1;
    // 商品id(必传)
    uint64 product_id = 2;
    // 商品编号
    string product_code = 3;
    // 商品名称
    string product_name = 4;
    // 商品类别
    uint64 product_category_id = 5;
    // 商品类型
    string product_type = 6;
    // 单位id(门店中为订货单位/仓库为采购单位)
    uint64 unit_id = 7;
    // 单位名称
    string unit_name = 8;
    // 单位规格
    string unit_spec = 9;
    // 商品规格
    string spec = 10;
    // 订货/退货数量
    double quantity = 11;
    // 实收数量(必传)
    double actual_quantity = 12;
    // 税率(必传)
    double tax_rate = 13;
    // 未税单价
    double price = 14;
    // 原始未税单价
    double orig_price = 15;
    // 总未税价
    double sum_price = 16;
    // 原始未税合计
    double orig_sum_price = 17;
    // 含税单价(必传)
    double price_tax = 18;
    // 上次修改含税单价(必传)
    double prev_price_tax = 19;
    // 含税总价
    double sum_price_tax = 20;
    // 原始含税单价
    double orig_price_tax = 21;
    // 原始含税合计
    double orig_sum_price_tax = 22;
    // 是否变更价格标记0/1 (有价格变动必传)
    uint32 is_modify = 23;
    repeated AdjustLog adjust_logs = 24;
    // 合计税额
    double sum_tax = 25;
    // 原始合计税额
    double orig_sum_tax = 26;
}

message AdjustLog {
    // 含税单价调整前
    double prev_price_tax = 1;
    // 含税单价 调整后
    double price_tax = 2;
    // 原始含税单价
    double orig_price_tax = 3;
    // 更新人
    uint64 updated_by = 4;
    // 更新人名字
    string updated_name = 5;
    // 更新时间
    google.protobuf.Timestamp updated_at = 6;
}

// 创建采购复核单返回参数
message CreatePurchaseReviewOrderResponse{
    uint64 order_id = 1;
    string result = 2;
}

// 采购复核订单列表请求参数
message ListPurchaseReviewOrderRequest{
    // 开始收货日期
    google.protobuf.Timestamp start_date = 1;
    // 结束收货日期
    google.protobuf.Timestamp end_date = 2;
    // 订单编号
    string order_code = 3;
    // 单据状态: '所有'状态不需要传此参数
    // INITED     # 待复核
    // SUBMITTED  # 待确认
    // APPROVED   # 已复核
    // REJECTED   # 已驳回
    string status = 4;
    // 单据类型
    string order_type = 5;
    // 供应商id列表
    repeated uint64 supplier_ids = 7;
    // 收货仓库/门店id列表
    repeated uint64 received_ids = 8;
    // 原单编号
    string origin_code = 9;
    int64 limit = 10;
    uint64 offset = 11;
    bool include_total = 12;
    // 排序方式  asc or desc
    string order = 13;
    // 排序字段
    string sort = 14;
    // 区分门店仓库 WAREHOUSE/STORE
    string branch_type = 15;
    // 公司id
    uint64 company_id = 16;
    // 发票勾兑状态 INITED=待勾兑 APPROVED=已勾兑 '所有'不传此参数
    string process_status = 17;
    // 标记是否是调整单
    bool is_adjust = 18;
    // 单据来源, 单选, 默认所有不传此参数
    string source_type = 19;
}
message OrderDetail {
    // 订单id
    uint64 order_id = 1;
    // 订单编号
    string order_code = 2;
    // 单据类型
    string order_type = 3;
    // 单据状态：
    // INITED     # 待复核
    // SUBMITTED  # 待确认
    // APPROVED   # 已复核
    // REJECTED   # 已驳回
    string status = 4;
    // 采购日期
    google.protobuf.Timestamp received_date = 5;
    // 收货仓库or门店id
    uint64 received_by = 6;
    // 收货仓库or门店名称
    string received_name = 7;
    // 收货仓库or门店编号
    string received_code = 8;
    // 供应商id or code
    uint64 supplier_id = 9;
    // 供应商名称
    string supplier_name = 10;
    // 供应商编号
    string supplier_code = 11;
    // 总含税金额
    double sum_price_tax = 12;
    // 原始总含税金额
    double orig_sum_price_tax = 13;
    // 总未税金额
    double sum_price = 14;
    // 原始总未税金额
    double orig_sum_price = 15;
    // 复核人
    uint64 review_by = 16;
    // 创建人
    uint64 created_by = 17;
    // 创建人名字
    string created_name = 18;
    // 创建时间
    google.protobuf.Timestamp created_at = 19;
    // 更新人
    uint64 updated_by = 20;
    // 更新人名字
    string updated_name = 21;
    // 更新时间
    google.protobuf.Timestamp updated_at = 22;
    // 是否变更价格标记0/1
    uint32 is_modify = 23;
    // 区分门店仓库 WAREHOUSE/STORE
    string branch_type = 24;
    // 公司id
    uint64 company_id = 25;
    // 单据业务状态给发票勾兑使用 INITED = 'INITED' 待勾兑 APPROVED = 'COMPLETED'  # 已勾兑
    string process_status = 26;
    // 原单编号
    string origin_code = 27;
    // 调价单信息
    string adjust_info = 28;
    // 标记是否是调整单
    bool is_adjust = 29;
    // 是否生成调价单
    bool generate_adjust = 31;
    uint64 cost_center_id = 32;
    // 单据来源：
    // 订货计划(SD)、紧急订货(HD)、总部分配(MD)、库存调整(AD)、对账调整(CAD)
    // 原单退货(BO)、非原单退货(NBO)、收货差异(REC_DIFF)、采购收货(PUR_REC)、采购退货(PUR_RETURN)
    string source_type = 33;
}
// 采购复核订单列表返回
message ListPurchaseReviewOrderResponse{
    repeated OrderDetail rows = 1;
    uint64 total = 2;
}

// 采购复核单详情请求参数
message GetPurchaseReviewDetailRequest {
    // 单据id
    uint64 order_id = 1;
    int64 limit = 2;
    uint64 offset = 3;
    bool include_total = 4;
}
// 采购复核单详情返回参数
message GetPurchaseReviewDetailResponse {
    // 订单id
    uint64 order_id = 1;
    // 订单编号
    string order_code = 2;
    // 单据类型
    string order_type = 3;
    // 单据状态
    string status = 4;
    // 采购日期
    google.protobuf.Timestamp received_date = 5;
    // 收货仓库or门店id
    uint64 received_by = 6;
    // 收货仓库or门店名称
    string received_name = 7;
    // 收货仓库or门店编号
    string received_code = 8;
    // 供应商id or code
    uint64 supplier_id = 9;
    // 供应商名称
    string supplier_name = 10;
    // 供应商编号
    string supplier_code = 11;
    // 总含税金额
    double sum_price_tax = 12;
    // 原始总含税金额
    double orig_sum_price_tax = 13;
    // 总未税金额
    double sum_price = 14;
    // 原始总未税金额
    double orig_sum_price = 15;
    // 复核人
    uint64 review_by = 16;
    // 创建人
    uint64 created_by = 17;
    // 创建人名字
    string created_name = 18;
    // 创建时间
    google.protobuf.Timestamp created_at = 19;
    // 更新人
    uint64 updated_by = 20;
    // 更新人名字
    string updated_name = 21;
    // 更新时间
    google.protobuf.Timestamp updated_at = 22;
    // 是否变更价格标记0/1
    uint32 is_modify = 23;
    // 商品详情
    repeated Product products = 24;
    // 总商品数
    uint32 total_product = 25;
    // 区分门店仓库 WAREHOUSE/STORE
    string branch_type = 26;
    // 公司id
    uint64 company_id = 27;
    // 单据业务状态给发票勾兑使用 'INITED' 待勾兑 'COMPLETED'  # 已勾兑
    string process_status = 28;
    // 原单编号
    string origin_code = 29;
    // 调价单号们
    repeated string adjust_codes = 30;
    // 标记是否是调整单
    bool is_adjust = 31;
    // 是否生成调价单
    bool generate_adjust = 33;
    // 成本中心id
    uint64 cost_center_id = 34;
    // 单据来源：
    // 订货计划(SD)、紧急订货(HD)、总部分配(MD)、库存调整(AD)、对账调整(CAD)
    // 原单退货(BO)、非原单退货(NBO)、收货差异(REC_DIFF)、采购收货(PUR_REC)、采购退货(PUR_RETURN)
    string source_type = 35;
    // 驳回原因
    string reject_reason = 36;
}

// 更新采购复核详情请求参数
message UpdatePurchaseReviewOrderRequest{
    // 单据id
    uint64 order_id = 1;
    // 更新商品明细
    repeated Product products = 2;
    // 更新同时仅支持提交:SUBMITTED
    string status = 4;
}
// 更新采购复核详情返回参数
message UpdatePurchaseReviewOrderResponse{
    uint64 order_id = 1;
    string result = 2;
}

// 采购复核(修改状态)请求参数
message ChangePurchaseReviewOrderStatusRequest{
    // 单据id列表 目前支持批量勾兑和批量提交、复核未调价的单据
    repeated uint64 order_ids = 1;
    // 更新的状态
    // INITED     # 待复核
    // SUBMITTED  # 提交->待确认
    // APPROVED   # 复核->已复核
    // REJECTED   # 驳回->已驳回
    string status = 2;
    // 驳回原因
    string reject_reason = 3;
}
// 采购复核(修改状态)返回参数
message ChangePurchaseReviewOrderStatusResponse{
    repeated uint64 order_ids = 1;
    string result = 2;
}

message PurchaseReviewDetail {
    // 订单id
    uint64 order_id = 1;
    // 订单编号
    string order_code = 2;
    // 单据类型
    string order_type = 3;
    // 单据状态
    string status = 4;
    // 采购日期
    google.protobuf.Timestamp received_date = 5;
    // 收货仓库or门店id
    uint64 received_by = 6;
    // 收货仓库or门店名称
    string received_name = 7;
    // 收货仓库or门店编号
    string received_code = 8;
    // 供应商id or code
    uint64 supplier_id = 9;
    // 供应商名称
    string supplier_name = 10;
    // 供应商编号
    string supplier_code = 11;
    // 复核人
    uint64 review_by = 12;
    // 创建人
    uint64 created_by = 13;
    // 创建人名字
    string created_name = 14;
    // 创建时间
    google.protobuf.Timestamp created_at = 15;
    // 更新人
    uint64 updated_by = 16;
    // 更新人名字
    string updated_name = 17;
    // 更新时间
    google.protobuf.Timestamp updated_at = 18;
    // 是否变更价格标记0/1
    uint32 is_modify = 19;
    // 区分门店仓库 WAREHOUSE/STORE
    string branch_type = 20;
    // 公司id
    uint64 company_id = 21;
    // 单据业务状态给发票勾兑使用 'INITED' 待勾兑 'COMPLETED'  # 已勾兑
    string process_status = 22;
    // 原单编号
    string origin_code = 23;
    // 调价单号们
    repeated string adjust_codes = 24;
    // 标记是否是调整单
    bool is_adjust = 25;
    // 是否生成调价单
    bool generate_adjust = 26;
    // 成本中心id
    uint64 cost_center_id = 27;
    // 单据来源：
    // 订货计划(SD)、紧急订货(HD)、总部分配(MD)、库存调整(AD)、对账调整(CAD)
    // 原单退货(BO)、非原单退货(NBO)、收货差异(REC_DIFF)、采购收货(PUR_REC)、采购退货(PUR_RETURN)
    string source_type = 28;
    // 驳回原因
    string reject_reason = 29;
    // 唯一ID（商品在数据库中的主键）
    uint64 id = 30;
    // 商品id
    uint64 product_id = 42;
    // 商品编号
    string product_code = 43;
    // 商品名称
    string product_name = 44;
    // 商品类别
    uint64 product_category_id = 45;
    // 商品类型
    string product_type = 46;
    // 单位id(门店中为订货单位/仓库为采购单位)
    uint64 unit_id = 47;
    // 单位名称
    string unit_name = 48;
    // 单位规格
    string unit_spec = 49;
    // 商品规格
    string spec = 50;
    // 订货/退货数量
    double quantity = 51;
    // 实收数量(必传)
    double actual_quantity = 52;
    // 税率(必传)
    double tax_rate = 53;
    // 未税单价
    double price = 54;
    // 原始未税单价
    double orig_price = 55;
    // 总未税价
    double sum_price = 56;
    // 原始未税合计
    double orig_sum_price = 57;
    // 含税单价(必传)
    double price_tax = 58;
    // 上次修改含税单价(必传)
    double prev_price_tax = 59;
    // 含税总价
    double sum_price_tax = 60;
    // 原始含税单价
    double orig_price_tax = 61;
    // 原始含税合计
    double orig_sum_price_tax = 62;
    // 合计税额
    double sum_tax = 65;
    // 原始合计税额
    double orig_sum_tax = 66;
}
// 采购复核单明细返回参数
message ListPurchaseReviewDetailResponse {
    repeated PurchaseReviewDetail rows = 1;
    uint64 total = 2;
}