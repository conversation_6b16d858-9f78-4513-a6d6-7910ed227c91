syntax = "proto3";

package price_adjustment;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";


// 调价单
service PriceAdjustment{
    // 创建调价单接口 暂未启用
    rpc CreatePriceAdjustment (CreatePriceAdjustmentRequest) returns (CreatePriceAdjustmentResponse){
        option (google.api.http) = {
        post: "/api/v2/supply/create/price/adjustment"
        body: "*"
        };
    }
    // 调价单列表查询
    rpc ListPriceAdjustment (ListPriceAdjustmentRequest) returns (ListPriceAdjustmentResponse){
        option (google.api.http) = {
        get: "/api/v2/supply/price/adjustment/list"
        };
    }
    // 查询调价单详情
    rpc GetPriceAdjustmentDetail (GetPriceAdjustmentDetailRequest) returns (GetPriceAdjustmentDetailResponse){
        option (google.api.http) = {
        get: "/api/v2/supply/price/adjustment/detail"
        };
    }
    // 调价单复核(修改状态) 暂未启用
    rpc UpdatePriceAdjustment (UpdatePriceAdjustmentRequest) returns (UpdatePriceAdjustmentResponse){
        option (google.api.http) = {
        put: "/api/v2/supply/price/adjustment/update"
        body: "*"
        };
    }
}

// 创建调价单请求参数
message CreatePriceAdjustmentRequest{
    // 主单id
    uint64 batch_id = 1;
    // 主单code
    string batch_code = 2;
    // 单据状态
    // 新建(待生效)：INITED
    // 生效中：APPROVED
    // 已作废：INVALID
    string status = 4;
    // 单据类型 (复核调价单REVIEW_ADJUST/财务调价单FINANCE_ADJUST）
    string order_type = 3;
    // 调价时间
    google.protobuf.Timestamp adjust_date = 5;
    // 门店或仓库id
    uint64 branch_id = 6;
    // 门店或仓库名称
    string branch_name = 7;
    // 门店或仓库编号
    string branch_code = 8;
    // 供应商id
    uint64 supplier_id = 9;
    // 供应商名称
    string supplier_name = 10;
    // 供应商编号
    string supplier_code = 11;
    // 总含税金额
    double sum_price_tax = 12;
    // 调价后总含税金额
    double adjust_sum_price_tax = 13;
    // 总未税金额
    double sum_price = 14;
    // 调价后总未税金额
    double adjust_sum_price = 15;
    // 商品信息
    repeated AdProduct products = 16;
    // 区分门店仓库 WAREHOUSE/STORE
    string branch_type = 17;
    // 是否传输成本引擎 0:未传输，1:已传输
    bool cost_trans_status = 18;
    // 影响成本更新的次数，之后用来校验
    uint32 cost_update = 19;
    // 成本中心id
    uint64 cost_center_id = 20;
}

message AdProduct {
    uint64 id = 1;
    uint64 partner_id = 2;
    uint64 created_by = 3;
    string created_name = 4;
    uint64 updated_by = 5;
    string updated_name = 6;
    // 关联的调价单id
    uint64 adjust_id = 7;
    // 商品id
    uint64 product_id = 8;
    // 商品编号
    string product_code = 9;
    // 商品名称
    string product_name = 10;
    // 商品类别
    uint64 product_category_id = 11;
    // 商品类型
    string product_type = 12;
    // 单位id(门店中为订货单位/仓库为采购单位)
    uint64 unit_id = 13;
    // 单位名称
    string unit_name = 14;
    // 单位规格
    string unit_spec = 15;
    // 商品规格
    string spec = 16;
    // 订货/退货数量
    double quantity = 17;
    // 实收数量
    double actual_quantity = 18;
    // 税率
    double tax_rate = 19;
    // 未税(单价)
    double price = 20;
    // 调价前未税单价
    double pre_price = 21;
    // 调价后未税合计
    double sum_price = 22;
    // 调价前未税合计
    double pre_sum_price = 23;
    // 调价后含税单价
    double price_tax = 24;
    // 调价后含税合计
    double sum_price_tax = 25;
    // 调价前含税单价
    double pre_price_tax = 26;
    // 调价前含税合计
    double pre_sum_price_tax = 27;
    // 调整后税额
    double tax = 28;
    // 调整前税额
    double pre_tax = 29;
    google.protobuf.Timestamp created_at = 30;
    google.protobuf.Timestamp updated_at = 31;
    // 调价单号
    string adjust_code = 32;
}

// 创建调价单返回参数
message CreatePriceAdjustmentResponse{
    uint64 id = 1;
    string result = 2;
}

// 调价单列表查询请求
message ListPriceAdjustmentRequest {
    // 开始调价日期
    google.protobuf.Timestamp start_date = 1;
    // 结束调价日期
    google.protobuf.Timestamp end_date = 2;
    // 订单编号
    string order_code = 3;
    // 单据状态
    // 新建(待生效)：INITED
    // 生效中：APPROVED
    // 已作废：INVALID
    string status = 4;
    // 单据类型（复核调价单REVIEW_ADJUST/财务调价单FINANCE_ADJUST）
    string order_type = 5;
    // 供应商id列表
    repeated uint64 supplier_ids = 7;
    // 仓库/门店id列表
    repeated uint64 branch_ids = 8;
    // 原单号
    string batch_code = 9;
    int64 limit = 10;
    uint64 offset = 11;
    bool include_total = 12;
    // 排序方式  asc or desc
    string order = 13;
    // 排序字段
    string sort = 14;
    // 区分门店仓库 WAREHOUSE/STORE
    string branch_type = 15;
    // 公司id
    uint64 company_id = 16;
    // 发票勾兑状态
    string blending_status = 17;
}

message PriceAdjustmentRow {
    // 主单id
    uint64 batch_id = 1;
    // 主单code
    string batch_code = 2;
    // 单据状态
    // 新建(待生效)：INITED
    // 生效中：APPROVED
    // 已作废：INVALID
    string status = 3;
    // 单据类型 (复核调价单REVIEW_ADJUST/财务调价单FINANCE_ADJUST）
    string order_type = 4;
    // 调价时间
    google.protobuf.Timestamp adjust_date = 5;
    // 门店或仓库id
    uint64 branch_id = 6;
    // 门店或仓库名称
    string branch_name = 7;
    // 门店或仓库编号
    string branch_code = 8;
    // 供应商id
    uint64 supplier_id = 9;
    // 供应商名称
    string supplier_name = 10;
    // 供应商编号
    string supplier_code = 11;
    // 总含税金额
    double sum_price_tax = 12;
    // 调价前总含税金额
    double pre_sum_price_tax = 13;
    // 总未税金额
    double sum_price = 14;
    // 调价前总未税金额
    double pre_sum_price = 15;
    // 区分门店仓库 WAREHOUSE/STORE
    string branch_type = 17;
    // 是否传输成本引擎 0:未传输，1:已传输
    bool cost_trans_status = 18;
    // 影响成本更新的次数，之后用来校验
    uint32 cost_update = 19;
    uint64 partner_id = 20;
    uint64 created_by = 21;
    string created_name = 22;
    uint64 updated_by = 23;
    string updated_name = 24;
    google.protobuf.Timestamp created_at = 25;
    google.protobuf.Timestamp updated_at = 26;
    uint64 id = 27;
    string code = 28;
    uint64 cost_center_id = 29;
    // 公司id
    uint64 company_id = 30;
    // 发票勾兑状态
    string blending_status = 31;
}

// 调价单列表查询返回
message ListPriceAdjustmentResponse {
    repeated PriceAdjustmentRow rows = 1;
    uint64 total = 2;
}

// 查询调价单详情请求
message GetPriceAdjustmentDetailRequest {
    // 以下参数二选一
    // 调价单据id
    uint64 adjust_id = 1;
    // 调价单编号
    string adjust_code = 2;
}
// 查询调价单详情返回
message GetPriceAdjustmentDetailResponse {
    // 主单id
    uint64 batch_id = 1;
    // 主单code
    string batch_code = 2;
    // 单据状态
    // 新建(待生效)：INITED
    // 生效中：APPROVED
    // 已作废：INVALID
    string status = 3;
    // 单据类型 (复核调价单REVIEW_ADJUST/财务调价单FINANCE_ADJUST）
    string order_type = 4;
    // 调价时间
    google.protobuf.Timestamp adjust_date = 5;
    // 门店或仓库id
    uint64 branch_id = 6;
    // 门店或仓库名称
    string branch_name = 7;
    // 门店或仓库编号
    string branch_code = 8;
    // 供应商id
    uint64 supplier_id = 9;
    // 供应商名称
    string supplier_name = 10;
    // 供应商编号
    string supplier_code = 11;
    // 总含税金额
    double sum_price_tax = 12;
    // 调价前总含税金额
    double pre_sum_price_tax = 13;
    // 总未税金额
    double sum_price = 14;
    // 调价前总未税金额
    double pre_sum_price = 15;
    // 区分门店仓库 WAREHOUSE/STORE
    string branch_type = 17;
    // 是否传输成本引擎 0:未传输，1:已传输
    bool cost_trans_status = 18;
    // 影响成本更新的次数，之后用来校验
    uint32 cost_update = 19;
    uint64 partner_id = 20;
    uint64 created_by = 21;
    string created_name = 22;
    uint64 updated_by = 23;
    string updated_name = 24;
    google.protobuf.Timestamp created_at = 25;
    google.protobuf.Timestamp updated_at = 26;
    uint64 id = 27;
    string code = 28;
    uint64 cost_center_id = 30;
    repeated AdProduct products = 29;
    // 公司id
    uint64 company_id = 32;
    // 发票勾兑状态
    string blending_status = 33;
}

// 更新调价单请求
message UpdatePriceAdjustmentRequest {
    // 单据id列表（在不修改商品的情况下支持批量审核）
    repeated uint64 ids = 1;
    // 是否有商品价格变动
    bool include_product = 2;
    // 商品列表，当有商品列表的时候order_ids必须只包含一个订单
    repeated AdProduct products = 3;
    // 新建(待生效)：INITED
    // 生效中：APPROVED
    // 已作废：INVALID
    string status = 4;
}
// 更新调价单返回
message UpdatePriceAdjustmentResponse {
    repeated uint64 ids = 1;
    string result = 2;
}