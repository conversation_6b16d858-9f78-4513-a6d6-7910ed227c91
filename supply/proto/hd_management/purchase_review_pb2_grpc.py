# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from hd_management import purchase_review_pb2 as hd__management_dot_purchase__review__pb2


class PurchaseReviewStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreatePurchaseReviewOrder = channel.unary_unary(
        '/purchase_review.PurchaseReview/CreatePurchaseReviewOrder',
        request_serializer=hd__management_dot_purchase__review__pb2.CreatePurchaseReviewOrderRequest.SerializeToString,
        response_deserializer=hd__management_dot_purchase__review__pb2.CreatePurchaseReviewOrderResponse.FromString,
        )
    self.ListPurchaseReviewOrder = channel.unary_unary(
        '/purchase_review.PurchaseReview/ListPurchaseReviewOrder',
        request_serializer=hd__management_dot_purchase__review__pb2.ListPurchaseReviewOrderRequest.SerializeToString,
        response_deserializer=hd__management_dot_purchase__review__pb2.ListPurchaseReviewOrderResponse.FromString,
        )
    self.GetPurchaseReviewDetail = channel.unary_unary(
        '/purchase_review.PurchaseReview/GetPurchaseReviewDetail',
        request_serializer=hd__management_dot_purchase__review__pb2.GetPurchaseReviewDetailRequest.SerializeToString,
        response_deserializer=hd__management_dot_purchase__review__pb2.GetPurchaseReviewDetailResponse.FromString,
        )
    self.UpdatePurchaseReviewOrder = channel.unary_unary(
        '/purchase_review.PurchaseReview/UpdatePurchaseReviewOrder',
        request_serializer=hd__management_dot_purchase__review__pb2.UpdatePurchaseReviewOrderRequest.SerializeToString,
        response_deserializer=hd__management_dot_purchase__review__pb2.UpdatePurchaseReviewOrderResponse.FromString,
        )
    self.ChangePurchaseReviewOrderStatus = channel.unary_unary(
        '/purchase_review.PurchaseReview/ChangePurchaseReviewOrderStatus',
        request_serializer=hd__management_dot_purchase__review__pb2.ChangePurchaseReviewOrderStatusRequest.SerializeToString,
        response_deserializer=hd__management_dot_purchase__review__pb2.ChangePurchaseReviewOrderStatusResponse.FromString,
        )
    self.ListPurchaseReviewDetail = channel.unary_unary(
        '/purchase_review.PurchaseReview/ListPurchaseReviewDetail',
        request_serializer=hd__management_dot_purchase__review__pb2.ListPurchaseReviewOrderRequest.SerializeToString,
        response_deserializer=hd__management_dot_purchase__review__pb2.ListPurchaseReviewDetailResponse.FromString,
        )


class PurchaseReviewServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def CreatePurchaseReviewOrder(self, request, context):
    """创建采购复核单接口
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListPurchaseReviewOrder(self, request, context):
    """采购复核单列表查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPurchaseReviewDetail(self, request, context):
    """查询采购复核单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdatePurchaseReviewOrder(self, request, context):
    """更新采购复核详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ChangePurchaseReviewOrderStatus(self, request, context):
    """修改采购复核状态
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListPurchaseReviewDetail(self, request, context):
    """查询采购复核单明细
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_PurchaseReviewServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreatePurchaseReviewOrder': grpc.unary_unary_rpc_method_handler(
          servicer.CreatePurchaseReviewOrder,
          request_deserializer=hd__management_dot_purchase__review__pb2.CreatePurchaseReviewOrderRequest.FromString,
          response_serializer=hd__management_dot_purchase__review__pb2.CreatePurchaseReviewOrderResponse.SerializeToString,
      ),
      'ListPurchaseReviewOrder': grpc.unary_unary_rpc_method_handler(
          servicer.ListPurchaseReviewOrder,
          request_deserializer=hd__management_dot_purchase__review__pb2.ListPurchaseReviewOrderRequest.FromString,
          response_serializer=hd__management_dot_purchase__review__pb2.ListPurchaseReviewOrderResponse.SerializeToString,
      ),
      'GetPurchaseReviewDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetPurchaseReviewDetail,
          request_deserializer=hd__management_dot_purchase__review__pb2.GetPurchaseReviewDetailRequest.FromString,
          response_serializer=hd__management_dot_purchase__review__pb2.GetPurchaseReviewDetailResponse.SerializeToString,
      ),
      'UpdatePurchaseReviewOrder': grpc.unary_unary_rpc_method_handler(
          servicer.UpdatePurchaseReviewOrder,
          request_deserializer=hd__management_dot_purchase__review__pb2.UpdatePurchaseReviewOrderRequest.FromString,
          response_serializer=hd__management_dot_purchase__review__pb2.UpdatePurchaseReviewOrderResponse.SerializeToString,
      ),
      'ChangePurchaseReviewOrderStatus': grpc.unary_unary_rpc_method_handler(
          servicer.ChangePurchaseReviewOrderStatus,
          request_deserializer=hd__management_dot_purchase__review__pb2.ChangePurchaseReviewOrderStatusRequest.FromString,
          response_serializer=hd__management_dot_purchase__review__pb2.ChangePurchaseReviewOrderStatusResponse.SerializeToString,
      ),
      'ListPurchaseReviewDetail': grpc.unary_unary_rpc_method_handler(
          servicer.ListPurchaseReviewDetail,
          request_deserializer=hd__management_dot_purchase__review__pb2.ListPurchaseReviewOrderRequest.FromString,
          response_serializer=hd__management_dot_purchase__review__pb2.ListPurchaseReviewDetailResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'purchase_review.PurchaseReview', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
