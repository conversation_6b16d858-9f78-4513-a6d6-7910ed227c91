# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: receiving_bi.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='receiving_bi.proto',
  package='receiving',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x12receiving_bi.proto\x12\treceiving\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xff\x01\n\x1aGetReceivingCollectRequest\x12\x0e\n\x06st_ids\x18\x01 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x02 \x03(\x04\x12\x11\n\tis_direct\x18\x03 \x01(\x08\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12.\n\nstart_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x07 \x01(\x05\x12\x0e\n\x06offset\x18\x08 \x01(\x05\x12\x15\n\rinclude_total\x18\t \x01(\x08\"i\n\x1bGetReceivingCollectResponse\x12)\n\x04rows\x18\x01 \x03(\x0b\x32\x1b.receiving.ReceivingCollect\x12\x1f\n\x05total\x18\x02 \x01(\x0b\x32\x10.receiving.Total\"\x8c\x02\n\x19GetReceivingDetailRequest\x12\x0e\n\x06st_ids\x18\x01 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x02 \x03(\x04\x12\x11\n\tis_direct\x18\x03 \x01(\x08\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12.\n\nstart_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x07 \x01(\x05\x12\x0e\n\x06offset\x18\x08 \x01(\x05\x12\x15\n\rinclude_total\x18\t \x01(\x08\x12\x0c\n\x04\x63ode\x18\n \x01(\t\"i\n\x1aGetReceivingDetailResponse\x12*\n\x04rows\x18\x01 \x03(\x0b\x32\x1c.receiving.ReceivingDetailed\x12\x1f\n\x05total\x18\x02 \x01(\x0b\x32\x10.receiving.Total\"\xab\x01\n\x05Total\x12\r\n\x05\x63ount\x18\x01 \x01(\x01\x12\x18\n\x10sum_all_quantity\x18\x02 \x01(\x01\x12\x1e\n\x16sum_confirmed_quantity\x18\x03 \x01(\x01\x12\x1d\n\x15sum_received_quantity\x18\x04 \x01(\x01\x12\x1d\n\x15sum_required_quantity\x18\x05 \x01(\x01\x12\x1b\n\x13sum_s_diff_quantity\x18\x06 \x01(\x01\"\xd9\x03\n\x10ReceivingCollect\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x01 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x02 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x03 \x01(\x04\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x15\n\rcategory_name\x18\x05 \x01(\t\x12\x17\n\x0f\x63\x61tegory_parent\x18\x06 \x01(\t\x12\x1a\n\x12\x63onfirmed_quantity\x18\x07 \x01(\x01\x12\x12\n\nproduct_id\x18\x08 \x01(\x04\x12\x14\n\x0cproduct_code\x18\t \x01(\t\x12\x14\n\x0cproduct_name\x18\n \x01(\t\x12\x14\n\x0cproduct_spec\x18\x0b \x01(\t\x12\x10\n\x08quantity\x18\x0c \x01(\x01\x12\x19\n\x11received_quantity\x18\r \x01(\x01\x12\x19\n\x11required_quantity\x18\x0e \x01(\x01\x12\x17\n\x0fs_diff_quantity\x18\x0f \x01(\x01\x12\x10\n\x08store_id\x18\x10 \x01(\x04\x12\x12\n\nstore_code\x18\x11 \x01(\t\x12\x12\n\nstore_name\x18\x12 \x01(\t\x12\x0f\n\x07unit_id\x18\x13 \x01(\x04\x12\x11\n\tunit_name\x18\x14 \x01(\t\"\x97\x05\n\x11ReceivingDetailed\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x01 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x02 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x03 \x01(\x04\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x15\n\rcategory_name\x18\x05 \x01(\t\x12\x17\n\x0f\x63\x61tegory_parent\x18\x06 \x01(\t\x12\x1a\n\x12\x63onfirmed_quantity\x18\x07 \x01(\x01\x12\x12\n\nproduct_id\x18\x08 \x01(\x04\x12\x14\n\x0cproduct_code\x18\t \x01(\t\x12\x14\n\x0cproduct_name\x18\n \x01(\t\x12\x14\n\x0cproduct_spec\x18\x0b \x01(\t\x12\x10\n\x08quantity\x18\x0c \x01(\x01\x12\x19\n\x11received_quantity\x18\r \x01(\x01\x12\x19\n\x11required_quantity\x18\x0e \x01(\x01\x12\x17\n\x0fs_diff_quantity\x18\x0f \x01(\x01\x12\x10\n\x08store_id\x18\x10 \x01(\x04\x12\x12\n\nstore_code\x18\x11 \x01(\t\x12\x12\n\nstore_name\x18\x12 \x01(\t\x12\x0f\n\x07unit_id\x18\x13 \x01(\x04\x12\x11\n\tunit_name\x18\x14 \x01(\t\x12\x15\n\rdelivery_date\x18\x15 \x01(\t\x12\x13\n\x0b\x64\x65mand_date\x18\x16 \x01(\t\x12\x16\n\x0ereceiving_code\x18\x17 \x01(\t\x12\x16\n\x0ereceiving_date\x18\x18 \x01(\t\x12\x14\n\x0creceiving_id\x18\x19 \x01(\x04\x12\n\n\x02id\x18\x1a \x01(\x04\x12\x14\n\x0cjde_order_id\x18\x1b \x01(\x04\x12\x16\n\x0ejde_order_type\x18\x1c \x01(\t\x12\x0f\n\x07jde_mcu\x18\x1d \x01(\t2\xba\x02\n\x12ReceivingBiService\x12\x91\x01\n\x13GetReceivingCollect\x12%.receiving.GetReceivingCollectRequest\x1a&.receiving.GetReceivingCollectResponse\"+\x82\xd3\xe4\x93\x02%\x12#/api/v2/supply/receiving/bi/collect\x12\x8f\x01\n\x12GetReceivingDetail\x12$.receiving.GetReceivingDetailRequest\x1a%.receiving.GetReceivingDetailResponse\",\x82\xd3\xe4\x93\x02&\x12$/api/v2/supply/receiving/bi/detailedb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_GETRECEIVINGCOLLECTREQUEST = _descriptor.Descriptor(
  name='GetReceivingCollectRequest',
  full_name='receiving.GetReceivingCollectRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='st_ids', full_name='receiving.GetReceivingCollectRequest.st_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='receiving.GetReceivingCollectRequest.category_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='receiving.GetReceivingCollectRequest.is_direct', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='receiving.GetReceivingCollectRequest.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='receiving.GetReceivingCollectRequest.start_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='receiving.GetReceivingCollectRequest.end_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='receiving.GetReceivingCollectRequest.limit', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='receiving.GetReceivingCollectRequest.offset', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='receiving.GetReceivingCollectRequest.include_total', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=97,
  serialized_end=352,
)


_GETRECEIVINGCOLLECTRESPONSE = _descriptor.Descriptor(
  name='GetReceivingCollectResponse',
  full_name='receiving.GetReceivingCollectResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='receiving.GetReceivingCollectResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='receiving.GetReceivingCollectResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=354,
  serialized_end=459,
)


_GETRECEIVINGDETAILREQUEST = _descriptor.Descriptor(
  name='GetReceivingDetailRequest',
  full_name='receiving.GetReceivingDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='st_ids', full_name='receiving.GetReceivingDetailRequest.st_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='receiving.GetReceivingDetailRequest.category_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='receiving.GetReceivingDetailRequest.is_direct', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='receiving.GetReceivingDetailRequest.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='receiving.GetReceivingDetailRequest.start_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='receiving.GetReceivingDetailRequest.end_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='receiving.GetReceivingDetailRequest.limit', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='receiving.GetReceivingDetailRequest.offset', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='receiving.GetReceivingDetailRequest.include_total', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='receiving.GetReceivingDetailRequest.code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=462,
  serialized_end=730,
)


_GETRECEIVINGDETAILRESPONSE = _descriptor.Descriptor(
  name='GetReceivingDetailResponse',
  full_name='receiving.GetReceivingDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='receiving.GetReceivingDetailResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='receiving.GetReceivingDetailResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=732,
  serialized_end=837,
)


_TOTAL = _descriptor.Descriptor(
  name='Total',
  full_name='receiving.Total',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='count', full_name='receiving.Total.count', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_all_quantity', full_name='receiving.Total.sum_all_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_confirmed_quantity', full_name='receiving.Total.sum_confirmed_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_received_quantity', full_name='receiving.Total.sum_received_quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_required_quantity', full_name='receiving.Total.sum_required_quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_s_diff_quantity', full_name='receiving.Total.sum_s_diff_quantity', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=840,
  serialized_end=1011,
)


_RECEIVINGCOLLECT = _descriptor.Descriptor(
  name='ReceivingCollect',
  full_name='receiving.ReceivingCollect',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='receiving.ReceivingCollect.accounting_unit_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='receiving.ReceivingCollect.accounting_unit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='receiving.ReceivingCollect.category_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='receiving.ReceivingCollect.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='receiving.ReceivingCollect.category_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_parent', full_name='receiving.ReceivingCollect.category_parent', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='receiving.ReceivingCollect.confirmed_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='receiving.ReceivingCollect.product_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='receiving.ReceivingCollect.product_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='receiving.ReceivingCollect.product_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='receiving.ReceivingCollect.product_spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='receiving.ReceivingCollect.quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_quantity', full_name='receiving.ReceivingCollect.received_quantity', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='required_quantity', full_name='receiving.ReceivingCollect.required_quantity', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_quantity', full_name='receiving.ReceivingCollect.s_diff_quantity', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='receiving.ReceivingCollect.store_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='receiving.ReceivingCollect.store_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='receiving.ReceivingCollect.store_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='receiving.ReceivingCollect.unit_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='receiving.ReceivingCollect.unit_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1014,
  serialized_end=1487,
)


_RECEIVINGDETAILED = _descriptor.Descriptor(
  name='ReceivingDetailed',
  full_name='receiving.ReceivingDetailed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='receiving.ReceivingDetailed.accounting_unit_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='receiving.ReceivingDetailed.accounting_unit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='receiving.ReceivingDetailed.category_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='receiving.ReceivingDetailed.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='receiving.ReceivingDetailed.category_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_parent', full_name='receiving.ReceivingDetailed.category_parent', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='receiving.ReceivingDetailed.confirmed_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='receiving.ReceivingDetailed.product_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='receiving.ReceivingDetailed.product_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='receiving.ReceivingDetailed.product_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='receiving.ReceivingDetailed.product_spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='receiving.ReceivingDetailed.quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_quantity', full_name='receiving.ReceivingDetailed.received_quantity', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='required_quantity', full_name='receiving.ReceivingDetailed.required_quantity', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_quantity', full_name='receiving.ReceivingDetailed.s_diff_quantity', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='receiving.ReceivingDetailed.store_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='receiving.ReceivingDetailed.store_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='receiving.ReceivingDetailed.store_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='receiving.ReceivingDetailed.unit_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='receiving.ReceivingDetailed.unit_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='receiving.ReceivingDetailed.delivery_date', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='receiving.ReceivingDetailed.demand_date', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_code', full_name='receiving.ReceivingDetailed.receiving_code', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_date', full_name='receiving.ReceivingDetailed.receiving_date', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_id', full_name='receiving.ReceivingDetailed.receiving_id', index=24,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='receiving.ReceivingDetailed.id', index=25,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_order_id', full_name='receiving.ReceivingDetailed.jde_order_id', index=26,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_order_type', full_name='receiving.ReceivingDetailed.jde_order_type', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_mcu', full_name='receiving.ReceivingDetailed.jde_mcu', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1490,
  serialized_end=2153,
)

_GETRECEIVINGCOLLECTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVINGCOLLECTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVINGCOLLECTRESPONSE.fields_by_name['rows'].message_type = _RECEIVINGCOLLECT
_GETRECEIVINGCOLLECTRESPONSE.fields_by_name['total'].message_type = _TOTAL
_GETRECEIVINGDETAILREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVINGDETAILREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVINGDETAILRESPONSE.fields_by_name['rows'].message_type = _RECEIVINGDETAILED
_GETRECEIVINGDETAILRESPONSE.fields_by_name['total'].message_type = _TOTAL
DESCRIPTOR.message_types_by_name['GetReceivingCollectRequest'] = _GETRECEIVINGCOLLECTREQUEST
DESCRIPTOR.message_types_by_name['GetReceivingCollectResponse'] = _GETRECEIVINGCOLLECTRESPONSE
DESCRIPTOR.message_types_by_name['GetReceivingDetailRequest'] = _GETRECEIVINGDETAILREQUEST
DESCRIPTOR.message_types_by_name['GetReceivingDetailResponse'] = _GETRECEIVINGDETAILRESPONSE
DESCRIPTOR.message_types_by_name['Total'] = _TOTAL
DESCRIPTOR.message_types_by_name['ReceivingCollect'] = _RECEIVINGCOLLECT
DESCRIPTOR.message_types_by_name['ReceivingDetailed'] = _RECEIVINGDETAILED
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetReceivingCollectRequest = _reflection.GeneratedProtocolMessageType('GetReceivingCollectRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGCOLLECTREQUEST,
  __module__ = 'receiving_bi_pb2'
  # @@protoc_insertion_point(class_scope:receiving.GetReceivingCollectRequest)
  ))
_sym_db.RegisterMessage(GetReceivingCollectRequest)

GetReceivingCollectResponse = _reflection.GeneratedProtocolMessageType('GetReceivingCollectResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGCOLLECTRESPONSE,
  __module__ = 'receiving_bi_pb2'
  # @@protoc_insertion_point(class_scope:receiving.GetReceivingCollectResponse)
  ))
_sym_db.RegisterMessage(GetReceivingCollectResponse)

GetReceivingDetailRequest = _reflection.GeneratedProtocolMessageType('GetReceivingDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGDETAILREQUEST,
  __module__ = 'receiving_bi_pb2'
  # @@protoc_insertion_point(class_scope:receiving.GetReceivingDetailRequest)
  ))
_sym_db.RegisterMessage(GetReceivingDetailRequest)

GetReceivingDetailResponse = _reflection.GeneratedProtocolMessageType('GetReceivingDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGDETAILRESPONSE,
  __module__ = 'receiving_bi_pb2'
  # @@protoc_insertion_point(class_scope:receiving.GetReceivingDetailResponse)
  ))
_sym_db.RegisterMessage(GetReceivingDetailResponse)

Total = _reflection.GeneratedProtocolMessageType('Total', (_message.Message,), dict(
  DESCRIPTOR = _TOTAL,
  __module__ = 'receiving_bi_pb2'
  # @@protoc_insertion_point(class_scope:receiving.Total)
  ))
_sym_db.RegisterMessage(Total)

ReceivingCollect = _reflection.GeneratedProtocolMessageType('ReceivingCollect', (_message.Message,), dict(
  DESCRIPTOR = _RECEIVINGCOLLECT,
  __module__ = 'receiving_bi_pb2'
  # @@protoc_insertion_point(class_scope:receiving.ReceivingCollect)
  ))
_sym_db.RegisterMessage(ReceivingCollect)

ReceivingDetailed = _reflection.GeneratedProtocolMessageType('ReceivingDetailed', (_message.Message,), dict(
  DESCRIPTOR = _RECEIVINGDETAILED,
  __module__ = 'receiving_bi_pb2'
  # @@protoc_insertion_point(class_scope:receiving.ReceivingDetailed)
  ))
_sym_db.RegisterMessage(ReceivingDetailed)



_RECEIVINGBISERVICE = _descriptor.ServiceDescriptor(
  name='ReceivingBiService',
  full_name='receiving.ReceivingBiService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=2156,
  serialized_end=2470,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetReceivingCollect',
    full_name='receiving.ReceivingBiService.GetReceivingCollect',
    index=0,
    containing_service=None,
    input_type=_GETRECEIVINGCOLLECTREQUEST,
    output_type=_GETRECEIVINGCOLLECTRESPONSE,
    serialized_options=_b('\202\323\344\223\002%\022#/api/v2/supply/receiving/bi/collect'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReceivingDetail',
    full_name='receiving.ReceivingBiService.GetReceivingDetail',
    index=1,
    containing_service=None,
    input_type=_GETRECEIVINGDETAILREQUEST,
    output_type=_GETRECEIVINGDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\022$/api/v2/supply/receiving/bi/detailed'),
  ),
])
_sym_db.RegisterServiceDescriptor(_RECEIVINGBISERVICE)

DESCRIPTOR.services_by_name['ReceivingBiService'] = _RECEIVINGBISERVICE

# @@protoc_insertion_point(module_scope)
