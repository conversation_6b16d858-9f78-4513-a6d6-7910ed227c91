# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import inventory_cut_pb2 as inventory__cut__pb2


class InventoryCutServiceStub(object):
  """库存报表相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CutDailySnapshot = channel.unary_unary(
        '/inventory_cut.InventoryCutService/CutDailySnapshot',
        request_serializer=inventory__cut__pb2.CutDailySnapshotRequest.SerializeToString,
        response_deserializer=inventory__cut__pb2.CutDailySnapshotResponse.FromString,
        )
    self.TaskCallBack = channel.unary_unary(
        '/inventory_cut.InventoryCutService/TaskCallBack',
        request_serializer=inventory__cut__pb2.TaskCallBackRequest.SerializeToString,
        response_deserializer=inventory__cut__pb2.CommonReply.FromString,
        )
    self.ListCostTriggerLog = channel.unary_unary(
        '/inventory_cut.InventoryCutService/ListCostTriggerLog',
        request_serializer=inventory__cut__pb2.ListCostTriggerLogRequest.SerializeToString,
        response_deserializer=inventory__cut__pb2.ListCostTriggerLogResponse.FromString,
        )
    self.ListPeriod = channel.unary_unary(
        '/inventory_cut.InventoryCutService/ListPeriod',
        request_serializer=inventory__cut__pb2.ListPeriodRequest.SerializeToString,
        response_deserializer=inventory__cut__pb2.ListPeriodResponse.FromString,
        )
    self.TriggerCostCount = channel.unary_unary(
        '/inventory_cut.InventoryCutService/TriggerCostCount',
        request_serializer=inventory__cut__pb2.TriggerCostCountRequest.SerializeToString,
        response_deserializer=inventory__cut__pb2.TriggerCostCountResponse.FromString,
        )


class InventoryCutServiceServicer(object):
  """库存报表相关服务
  """

  def CutDailySnapshot(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def TaskCallBack(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListCostTriggerLog(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListPeriod(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def TriggerCostCount(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_InventoryCutServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CutDailySnapshot': grpc.unary_unary_rpc_method_handler(
          servicer.CutDailySnapshot,
          request_deserializer=inventory__cut__pb2.CutDailySnapshotRequest.FromString,
          response_serializer=inventory__cut__pb2.CutDailySnapshotResponse.SerializeToString,
      ),
      'TaskCallBack': grpc.unary_unary_rpc_method_handler(
          servicer.TaskCallBack,
          request_deserializer=inventory__cut__pb2.TaskCallBackRequest.FromString,
          response_serializer=inventory__cut__pb2.CommonReply.SerializeToString,
      ),
      'ListCostTriggerLog': grpc.unary_unary_rpc_method_handler(
          servicer.ListCostTriggerLog,
          request_deserializer=inventory__cut__pb2.ListCostTriggerLogRequest.FromString,
          response_serializer=inventory__cut__pb2.ListCostTriggerLogResponse.SerializeToString,
      ),
      'ListPeriod': grpc.unary_unary_rpc_method_handler(
          servicer.ListPeriod,
          request_deserializer=inventory__cut__pb2.ListPeriodRequest.FromString,
          response_serializer=inventory__cut__pb2.ListPeriodResponse.SerializeToString,
      ),
      'TriggerCostCount': grpc.unary_unary_rpc_method_handler(
          servicer.TriggerCostCount,
          request_deserializer=inventory__cut__pb2.TriggerCostCountRequest.FromString,
          response_serializer=inventory__cut__pb2.TriggerCostCountResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'inventory_cut.InventoryCutService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
