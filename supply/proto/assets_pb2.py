# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: assets.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='assets.proto',
  package='receiving',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x0c\x61ssets.proto\x12\treceiving\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xf0\x01\n\x1c\x43reateAssetsReceivingRequest\x12\x0c\n\x04kcoo\x18\x01 \x01(\t\x12\x0c\n\x04\x64oco\x18\x02 \x01(\x04\x12\x0c\n\x04\x64\x63to\x18\x03 \x01(\t\x12\x0c\n\x04lnid\x18\x04 \x01(\x04\x12\x0b\n\x03mcu\x18\x05 \x01(\t\x12\x0b\n\x03\x61n8\x18\x06 \x01(\x04\x12*\n\x08products\x18\x07 \x03(\x0b\x32\x18.receiving.AssetsProduct\x12(\n\x04trdj\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12(\n\x04qrdj\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"b\n\x1d\x43reateAssetsReceivingResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x31\n\x08products\x18\x02 \x03(\x0b\x32\x1f.receiving.ConfirmAssetsProduct\"^\n\x1d\x43onfirmAssetsReceivingRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x31\n\x08products\x18\x02 \x03(\x0b\x32\x1f.receiving.ConfirmAssetsProduct\"0\n\x1e\x43onfirmAssetsReceivingResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\xf2\x01\n\x1aListAssetsReceivingRequest\x12\x10\n\x08\x61sset_id\x18\x01 \x03(\x04\x12\x11\n\tstore_ids\x18\x02 \x03(\x04\x12\x0e\n\x06status\x18\x03 \x03(\t\x12.\n\nstart_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x07 \x01(\x05\x12\x0e\n\x06offset\x18\x08 \x01(\x05\x12\x14\n\x0cjde_order_id\x18\t \x01(\t\x12\x0c\n\x04\x63ode\x18\n \x01(\t\"M\n\x1bListAssetsReceivingResponse\x12\x1f\n\x04rows\x18\x01 \x03(\x0b\x32\x11.receiving.Assets\x12\r\n\x05total\x18\x02 \x01(\x04\"!\n\x13GetAssetByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\"`\n\x1cListAssetProductsByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\"V\n\x1dListAssetProductsByIdResponse\x12&\n\x04rows\x18\x01 \x03(\x0b\x32\x18.receiving.AssetsProduct\x12\r\n\x05total\x18\x02 \x01(\x04\"\xd8\x02\n\rAssetsProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08\x61sset_id\x18\x02 \x01(\x04\x12\x0c\n\x04litm\x18\x03 \x01(\t\x12\x0b\n\x03uom\x18\x04 \x01(\t\x12\x0c\n\x04uorg\x18\x05 \x01(\x01\x12\x12\n\ncreated_by\x18\x06 \x01(\x04\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\x08 \x01(\t\x12\x12\n\nupdated_by\x18\t \x01(\x04\x12.\n\nupdated_at\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0cupdated_name\x18\x0b \x01(\t\x12\x0e\n\x06status\x18\x0c \x01(\t\x12\x12\n\npartner_id\x18\r \x01(\x04\x12\x12\n\nproduct_id\x18\x0e \x01(\x04\x12\x14\n\x0cproduct_name\x18\x0f \x01(\t\"4\n\x14\x43onfirmAssetsProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08quantity\x18\x02 \x01(\x01\"\x9a\x04\n\x06\x41ssets\x12\x0c\n\x04kcoo\x18\x01 \x01(\t\x12\x0c\n\x04\x64oco\x18\x02 \x01(\t\x12\x0c\n\x04\x64\x63to\x18\x03 \x01(\t\x12\x0c\n\x04lnid\x18\x04 \x01(\x04\x12\x0b\n\x03mcu\x18\x05 \x01(\t\x12\x0b\n\x03\x61n8\x18\x06 \x01(\x04\x12(\n\x04trdj\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12(\n\x04qrdj\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x64l03\x18\n \x01(\x04\x12\x0c\n\x04ktln\x18\x0b \x01(\t\x12\x0e\n\x06status\x18\r \x01(\t\x12\x12\n\npartner_id\x18\x0e \x01(\x04\x12\x14\n\x0cproducts_num\x18\x0f \x01(\x04\x12\x12\n\ncreated_by\x18\x10 \x01(\x04\x12.\n\ncreated_at\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\x12 \x01(\t\x12\x12\n\nupdated_by\x18\x13 \x01(\x04\x12.\n\nupdated_at\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0cupdated_name\x18\x15 \x01(\t\x12\n\n\x02id\x18\x16 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x17 \x01(\t\x12\x0e\n\x06mcu_id\x18\x18 \x01(\x04\x12\x10\n\x08mcu_name\x18\x19 \x01(\t\x12\x10\n\x08store_id\x18\x1a \x01(\x04\x12\x12\n\nstore_name\x18\x1b \x01(\t2\xf3\x05\n\x10\x41ssetsRecService\x12\x95\x01\n\x15\x43reateAssetsReceiving\x12\'.receiving.CreateAssetsReceivingRequest\x1a(.receiving.CreateAssetsReceivingResponse\")\x82\xd3\xe4\x93\x02#\"\x1e/api/v2/supply/asset/receiving:\x01*\x12\xa5\x01\n\x16\x43onfirmAssetsReceiving\x12(.receiving.ConfirmAssetsReceivingRequest\x1a).receiving.ConfirmAssetsReceivingResponse\"6\x82\xd3\xe4\x93\x02\x30\x1a+/api/v2/supply/asset/receiving/{id}/confirm:\x01*\x12\x8c\x01\n\x13ListAssetsReceiving\x12%.receiving.ListAssetsReceivingRequest\x1a&.receiving.ListAssetsReceivingResponse\"&\x82\xd3\xe4\x93\x02 \x12\x1e/api/v2/supply/asset/receiving\x12n\n\x0cGetAssetById\x12\x1e.receiving.GetAssetByIdRequest\x1a\x11.receiving.Assets\"+\x82\xd3\xe4\x93\x02%\x12#/api/v2/supply/asset/receiving/{id}\x12\x9f\x01\n\x15ListAssetProductsById\x12\'.receiving.ListAssetProductsByIdRequest\x1a(.receiving.ListAssetProductsByIdResponse\"3\x82\xd3\xe4\x93\x02-\x12+/api/v2/supply/asset/receiving/{id}/productb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_CREATEASSETSRECEIVINGREQUEST = _descriptor.Descriptor(
  name='CreateAssetsReceivingRequest',
  full_name='receiving.CreateAssetsReceivingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='kcoo', full_name='receiving.CreateAssetsReceivingRequest.kcoo', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doco', full_name='receiving.CreateAssetsReceivingRequest.doco', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dcto', full_name='receiving.CreateAssetsReceivingRequest.dcto', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lnid', full_name='receiving.CreateAssetsReceivingRequest.lnid', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mcu', full_name='receiving.CreateAssetsReceivingRequest.mcu', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='an8', full_name='receiving.CreateAssetsReceivingRequest.an8', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='receiving.CreateAssetsReceivingRequest.products', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trdj', full_name='receiving.CreateAssetsReceivingRequest.trdj', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qrdj', full_name='receiving.CreateAssetsReceivingRequest.qrdj', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=91,
  serialized_end=331,
)


_CREATEASSETSRECEIVINGRESPONSE = _descriptor.Descriptor(
  name='CreateAssetsReceivingResponse',
  full_name='receiving.CreateAssetsReceivingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='receiving.CreateAssetsReceivingResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='receiving.CreateAssetsReceivingResponse.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=333,
  serialized_end=431,
)


_CONFIRMASSETSRECEIVINGREQUEST = _descriptor.Descriptor(
  name='ConfirmAssetsReceivingRequest',
  full_name='receiving.ConfirmAssetsReceivingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receiving.ConfirmAssetsReceivingRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='receiving.ConfirmAssetsReceivingRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=433,
  serialized_end=527,
)


_CONFIRMASSETSRECEIVINGRESPONSE = _descriptor.Descriptor(
  name='ConfirmAssetsReceivingResponse',
  full_name='receiving.ConfirmAssetsReceivingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='receiving.ConfirmAssetsReceivingResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=529,
  serialized_end=577,
)


_LISTASSETSRECEIVINGREQUEST = _descriptor.Descriptor(
  name='ListAssetsReceivingRequest',
  full_name='receiving.ListAssetsReceivingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='asset_id', full_name='receiving.ListAssetsReceivingRequest.asset_id', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='receiving.ListAssetsReceivingRequest.store_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='receiving.ListAssetsReceivingRequest.status', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='receiving.ListAssetsReceivingRequest.start_date', index=3,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='receiving.ListAssetsReceivingRequest.end_date', index=4,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='receiving.ListAssetsReceivingRequest.limit', index=5,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='receiving.ListAssetsReceivingRequest.offset', index=6,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_order_id', full_name='receiving.ListAssetsReceivingRequest.jde_order_id', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='receiving.ListAssetsReceivingRequest.code', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=580,
  serialized_end=822,
)


_LISTASSETSRECEIVINGRESPONSE = _descriptor.Descriptor(
  name='ListAssetsReceivingResponse',
  full_name='receiving.ListAssetsReceivingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='receiving.ListAssetsReceivingResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='receiving.ListAssetsReceivingResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=824,
  serialized_end=901,
)


_GETASSETBYIDREQUEST = _descriptor.Descriptor(
  name='GetAssetByIdRequest',
  full_name='receiving.GetAssetByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receiving.GetAssetByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=903,
  serialized_end=936,
)


_LISTASSETPRODUCTSBYIDREQUEST = _descriptor.Descriptor(
  name='ListAssetProductsByIdRequest',
  full_name='receiving.ListAssetProductsByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receiving.ListAssetProductsByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='receiving.ListAssetProductsByIdRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='receiving.ListAssetProductsByIdRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='receiving.ListAssetProductsByIdRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=938,
  serialized_end=1034,
)


_LISTASSETPRODUCTSBYIDRESPONSE = _descriptor.Descriptor(
  name='ListAssetProductsByIdResponse',
  full_name='receiving.ListAssetProductsByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='receiving.ListAssetProductsByIdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='receiving.ListAssetProductsByIdResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1036,
  serialized_end=1122,
)


_ASSETSPRODUCT = _descriptor.Descriptor(
  name='AssetsProduct',
  full_name='receiving.AssetsProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receiving.AssetsProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='asset_id', full_name='receiving.AssetsProduct.asset_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='litm', full_name='receiving.AssetsProduct.litm', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='uom', full_name='receiving.AssetsProduct.uom', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='uorg', full_name='receiving.AssetsProduct.uorg', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='receiving.AssetsProduct.created_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='receiving.AssetsProduct.created_at', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='receiving.AssetsProduct.created_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='receiving.AssetsProduct.updated_by', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='receiving.AssetsProduct.updated_at', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='receiving.AssetsProduct.updated_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='receiving.AssetsProduct.status', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='receiving.AssetsProduct.partner_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='receiving.AssetsProduct.product_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='receiving.AssetsProduct.product_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1125,
  serialized_end=1469,
)


_CONFIRMASSETSPRODUCT = _descriptor.Descriptor(
  name='ConfirmAssetsProduct',
  full_name='receiving.ConfirmAssetsProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receiving.ConfirmAssetsProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='receiving.ConfirmAssetsProduct.quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1471,
  serialized_end=1523,
)


_ASSETS = _descriptor.Descriptor(
  name='Assets',
  full_name='receiving.Assets',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='kcoo', full_name='receiving.Assets.kcoo', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doco', full_name='receiving.Assets.doco', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dcto', full_name='receiving.Assets.dcto', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lnid', full_name='receiving.Assets.lnid', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mcu', full_name='receiving.Assets.mcu', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='an8', full_name='receiving.Assets.an8', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trdj', full_name='receiving.Assets.trdj', index=6,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qrdj', full_name='receiving.Assets.qrdj', index=7,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dl03', full_name='receiving.Assets.dl03', index=8,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ktln', full_name='receiving.Assets.ktln', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='receiving.Assets.status', index=10,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='receiving.Assets.partner_id', index=11,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products_num', full_name='receiving.Assets.products_num', index=12,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='receiving.Assets.created_by', index=13,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='receiving.Assets.created_at', index=14,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='receiving.Assets.created_name', index=15,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='receiving.Assets.updated_by', index=16,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='receiving.Assets.updated_at', index=17,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='receiving.Assets.updated_name', index=18,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='receiving.Assets.id', index=19,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='receiving.Assets.code', index=20,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mcu_id', full_name='receiving.Assets.mcu_id', index=21,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mcu_name', full_name='receiving.Assets.mcu_name', index=22,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='receiving.Assets.store_id', index=23,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='receiving.Assets.store_name', index=24,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1526,
  serialized_end=2064,
)

_CREATEASSETSRECEIVINGREQUEST.fields_by_name['products'].message_type = _ASSETSPRODUCT
_CREATEASSETSRECEIVINGREQUEST.fields_by_name['trdj'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEASSETSRECEIVINGREQUEST.fields_by_name['qrdj'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEASSETSRECEIVINGRESPONSE.fields_by_name['products'].message_type = _CONFIRMASSETSPRODUCT
_CONFIRMASSETSRECEIVINGREQUEST.fields_by_name['products'].message_type = _CONFIRMASSETSPRODUCT
_LISTASSETSRECEIVINGREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTASSETSRECEIVINGREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTASSETSRECEIVINGRESPONSE.fields_by_name['rows'].message_type = _ASSETS
_LISTASSETPRODUCTSBYIDRESPONSE.fields_by_name['rows'].message_type = _ASSETSPRODUCT
_ASSETSPRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ASSETSPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ASSETS.fields_by_name['trdj'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ASSETS.fields_by_name['qrdj'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ASSETS.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ASSETS.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['CreateAssetsReceivingRequest'] = _CREATEASSETSRECEIVINGREQUEST
DESCRIPTOR.message_types_by_name['CreateAssetsReceivingResponse'] = _CREATEASSETSRECEIVINGRESPONSE
DESCRIPTOR.message_types_by_name['ConfirmAssetsReceivingRequest'] = _CONFIRMASSETSRECEIVINGREQUEST
DESCRIPTOR.message_types_by_name['ConfirmAssetsReceivingResponse'] = _CONFIRMASSETSRECEIVINGRESPONSE
DESCRIPTOR.message_types_by_name['ListAssetsReceivingRequest'] = _LISTASSETSRECEIVINGREQUEST
DESCRIPTOR.message_types_by_name['ListAssetsReceivingResponse'] = _LISTASSETSRECEIVINGRESPONSE
DESCRIPTOR.message_types_by_name['GetAssetByIdRequest'] = _GETASSETBYIDREQUEST
DESCRIPTOR.message_types_by_name['ListAssetProductsByIdRequest'] = _LISTASSETPRODUCTSBYIDREQUEST
DESCRIPTOR.message_types_by_name['ListAssetProductsByIdResponse'] = _LISTASSETPRODUCTSBYIDRESPONSE
DESCRIPTOR.message_types_by_name['AssetsProduct'] = _ASSETSPRODUCT
DESCRIPTOR.message_types_by_name['ConfirmAssetsProduct'] = _CONFIRMASSETSPRODUCT
DESCRIPTOR.message_types_by_name['Assets'] = _ASSETS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CreateAssetsReceivingRequest = _reflection.GeneratedProtocolMessageType('CreateAssetsReceivingRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEASSETSRECEIVINGREQUEST,
  __module__ = 'assets_pb2'
  # @@protoc_insertion_point(class_scope:receiving.CreateAssetsReceivingRequest)
  ))
_sym_db.RegisterMessage(CreateAssetsReceivingRequest)

CreateAssetsReceivingResponse = _reflection.GeneratedProtocolMessageType('CreateAssetsReceivingResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEASSETSRECEIVINGRESPONSE,
  __module__ = 'assets_pb2'
  # @@protoc_insertion_point(class_scope:receiving.CreateAssetsReceivingResponse)
  ))
_sym_db.RegisterMessage(CreateAssetsReceivingResponse)

ConfirmAssetsReceivingRequest = _reflection.GeneratedProtocolMessageType('ConfirmAssetsReceivingRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMASSETSRECEIVINGREQUEST,
  __module__ = 'assets_pb2'
  # @@protoc_insertion_point(class_scope:receiving.ConfirmAssetsReceivingRequest)
  ))
_sym_db.RegisterMessage(ConfirmAssetsReceivingRequest)

ConfirmAssetsReceivingResponse = _reflection.GeneratedProtocolMessageType('ConfirmAssetsReceivingResponse', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMASSETSRECEIVINGRESPONSE,
  __module__ = 'assets_pb2'
  # @@protoc_insertion_point(class_scope:receiving.ConfirmAssetsReceivingResponse)
  ))
_sym_db.RegisterMessage(ConfirmAssetsReceivingResponse)

ListAssetsReceivingRequest = _reflection.GeneratedProtocolMessageType('ListAssetsReceivingRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTASSETSRECEIVINGREQUEST,
  __module__ = 'assets_pb2'
  # @@protoc_insertion_point(class_scope:receiving.ListAssetsReceivingRequest)
  ))
_sym_db.RegisterMessage(ListAssetsReceivingRequest)

ListAssetsReceivingResponse = _reflection.GeneratedProtocolMessageType('ListAssetsReceivingResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTASSETSRECEIVINGRESPONSE,
  __module__ = 'assets_pb2'
  # @@protoc_insertion_point(class_scope:receiving.ListAssetsReceivingResponse)
  ))
_sym_db.RegisterMessage(ListAssetsReceivingResponse)

GetAssetByIdRequest = _reflection.GeneratedProtocolMessageType('GetAssetByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETASSETBYIDREQUEST,
  __module__ = 'assets_pb2'
  # @@protoc_insertion_point(class_scope:receiving.GetAssetByIdRequest)
  ))
_sym_db.RegisterMessage(GetAssetByIdRequest)

ListAssetProductsByIdRequest = _reflection.GeneratedProtocolMessageType('ListAssetProductsByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTASSETPRODUCTSBYIDREQUEST,
  __module__ = 'assets_pb2'
  # @@protoc_insertion_point(class_scope:receiving.ListAssetProductsByIdRequest)
  ))
_sym_db.RegisterMessage(ListAssetProductsByIdRequest)

ListAssetProductsByIdResponse = _reflection.GeneratedProtocolMessageType('ListAssetProductsByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTASSETPRODUCTSBYIDRESPONSE,
  __module__ = 'assets_pb2'
  # @@protoc_insertion_point(class_scope:receiving.ListAssetProductsByIdResponse)
  ))
_sym_db.RegisterMessage(ListAssetProductsByIdResponse)

AssetsProduct = _reflection.GeneratedProtocolMessageType('AssetsProduct', (_message.Message,), dict(
  DESCRIPTOR = _ASSETSPRODUCT,
  __module__ = 'assets_pb2'
  # @@protoc_insertion_point(class_scope:receiving.AssetsProduct)
  ))
_sym_db.RegisterMessage(AssetsProduct)

ConfirmAssetsProduct = _reflection.GeneratedProtocolMessageType('ConfirmAssetsProduct', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMASSETSPRODUCT,
  __module__ = 'assets_pb2'
  # @@protoc_insertion_point(class_scope:receiving.ConfirmAssetsProduct)
  ))
_sym_db.RegisterMessage(ConfirmAssetsProduct)

Assets = _reflection.GeneratedProtocolMessageType('Assets', (_message.Message,), dict(
  DESCRIPTOR = _ASSETS,
  __module__ = 'assets_pb2'
  # @@protoc_insertion_point(class_scope:receiving.Assets)
  ))
_sym_db.RegisterMessage(Assets)



_ASSETSRECSERVICE = _descriptor.ServiceDescriptor(
  name='AssetsRecService',
  full_name='receiving.AssetsRecService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=2067,
  serialized_end=2822,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateAssetsReceiving',
    full_name='receiving.AssetsRecService.CreateAssetsReceiving',
    index=0,
    containing_service=None,
    input_type=_CREATEASSETSRECEIVINGREQUEST,
    output_type=_CREATEASSETSRECEIVINGRESPONSE,
    serialized_options=_b('\202\323\344\223\002#\"\036/api/v2/supply/asset/receiving:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmAssetsReceiving',
    full_name='receiving.AssetsRecService.ConfirmAssetsReceiving',
    index=1,
    containing_service=None,
    input_type=_CONFIRMASSETSRECEIVINGREQUEST,
    output_type=_CONFIRMASSETSRECEIVINGRESPONSE,
    serialized_options=_b('\202\323\344\223\0020\032+/api/v2/supply/asset/receiving/{id}/confirm:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListAssetsReceiving',
    full_name='receiving.AssetsRecService.ListAssetsReceiving',
    index=2,
    containing_service=None,
    input_type=_LISTASSETSRECEIVINGREQUEST,
    output_type=_LISTASSETSRECEIVINGRESPONSE,
    serialized_options=_b('\202\323\344\223\002 \022\036/api/v2/supply/asset/receiving'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAssetById',
    full_name='receiving.AssetsRecService.GetAssetById',
    index=3,
    containing_service=None,
    input_type=_GETASSETBYIDREQUEST,
    output_type=_ASSETS,
    serialized_options=_b('\202\323\344\223\002%\022#/api/v2/supply/asset/receiving/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListAssetProductsById',
    full_name='receiving.AssetsRecService.ListAssetProductsById',
    index=4,
    containing_service=None,
    input_type=_LISTASSETPRODUCTSBYIDREQUEST,
    output_type=_LISTASSETPRODUCTSBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002-\022+/api/v2/supply/asset/receiving/{id}/product'),
  ),
])
_sym_db.RegisterServiceDescriptor(_ASSETSRECSERVICE)

DESCRIPTOR.services_by_name['AssetsRecService'] = _ASSETSRECSERVICE

# @@protoc_insertion_point(module_scope)
