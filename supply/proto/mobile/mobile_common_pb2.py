# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mobile/mobile_common.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='mobile/mobile_common.proto',
  package='mobile_common',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1amobile/mobile_common.proto\x12\rmobile_common\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xab\x01\n\x17GetUnfinishedDocRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x10\n\x08store_id\x18\x03 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x04 \x01(\t\x12\x0b\n\x03lan\x18\x05 \x01(\t\"|\n\x11UnfinishedReceipt\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x16\n\x0eshipping_store\x18\x05 \x01(\x04\x12\x17\n\x0freceiving_store\x18\x06 \x01(\x04\x12\x0c\n\x04type\x18\x07 \x01(\t\"\xd1\x03\n\x18GetUnfinishedDocResponse\x12\x0f\n\x07handler\x18\x01 \x01(\x08\x12\x30\n\x06\x61\x64just\x18\x02 \x03(\x0b\x32 .mobile_common.UnfinishedReceipt\x12\x38\n\x0ereceiving_diff\x18\x03 \x03(\x0b\x32 .mobile_common.UnfinishedReceipt\x12\x32\n\x08transfer\x18\x04 \x03(\x0b\x32 .mobile_common.UnfinishedReceipt\x12\x33\n\treceiving\x18\x05 \x03(\x0b\x32 .mobile_common.UnfinishedReceipt\x12\x30\n\x06return\x18\x06 \x03(\x0b\x32 .mobile_common.UnfinishedReceipt\x12\x33\n\tstocktake\x18\n \x03(\x0b\x32 .mobile_common.UnfinishedReceipt\x12\x30\n\x06\x64\x65mand\x18\x0b \x03(\x0b\x32 .mobile_common.UnfinishedReceipt\x12\x36\n\x0cself_picking\x18\r \x03(\x0b\x32 .mobile_common.UnfinishedReceipt2\xa9\x01\n\x13MobileCommonService\x12\x91\x01\n\x10GetUnfinishedDoc\x12&.mobile_common.GetUnfinishedDocRequest\x1a\'.mobile_common.GetUnfinishedDocResponse\",\x82\xd3\xe4\x93\x02&\x12$/api/v2/supply/mobile/unfinished/docb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_GETUNFINISHEDDOCREQUEST = _descriptor.Descriptor(
  name='GetUnfinishedDocRequest',
  full_name='mobile_common.GetUnfinishedDocRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='mobile_common.GetUnfinishedDocRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='mobile_common.GetUnfinishedDocRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='mobile_common.GetUnfinishedDocRequest.store_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_common.GetUnfinishedDocRequest.branch_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='mobile_common.GetUnfinishedDocRequest.lan', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=109,
  serialized_end=280,
)


_UNFINISHEDRECEIPT = _descriptor.Descriptor(
  name='UnfinishedReceipt',
  full_name='mobile_common.UnfinishedReceipt',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_common.UnfinishedReceipt.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_common.UnfinishedReceipt.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_common.UnfinishedReceipt.status', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='mobile_common.UnfinishedReceipt.shipping_store', index=3,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='mobile_common.UnfinishedReceipt.receiving_store', index=4,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='mobile_common.UnfinishedReceipt.type', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=282,
  serialized_end=406,
)


_GETUNFINISHEDDOCRESPONSE = _descriptor.Descriptor(
  name='GetUnfinishedDocResponse',
  full_name='mobile_common.GetUnfinishedDocResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='handler', full_name='mobile_common.GetUnfinishedDocResponse.handler', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust', full_name='mobile_common.GetUnfinishedDocResponse.adjust', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_diff', full_name='mobile_common.GetUnfinishedDocResponse.receiving_diff', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer', full_name='mobile_common.GetUnfinishedDocResponse.transfer', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving', full_name='mobile_common.GetUnfinishedDocResponse.receiving', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return', full_name='mobile_common.GetUnfinishedDocResponse.return', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='mobile_common.GetUnfinishedDocResponse.stocktake', index=6,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand', full_name='mobile_common.GetUnfinishedDocResponse.demand', index=7,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='self_picking', full_name='mobile_common.GetUnfinishedDocResponse.self_picking', index=8,
      number=13, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=409,
  serialized_end=874,
)

_GETUNFINISHEDDOCREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETUNFINISHEDDOCREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETUNFINISHEDDOCRESPONSE.fields_by_name['adjust'].message_type = _UNFINISHEDRECEIPT
_GETUNFINISHEDDOCRESPONSE.fields_by_name['receiving_diff'].message_type = _UNFINISHEDRECEIPT
_GETUNFINISHEDDOCRESPONSE.fields_by_name['transfer'].message_type = _UNFINISHEDRECEIPT
_GETUNFINISHEDDOCRESPONSE.fields_by_name['receiving'].message_type = _UNFINISHEDRECEIPT
_GETUNFINISHEDDOCRESPONSE.fields_by_name['return'].message_type = _UNFINISHEDRECEIPT
_GETUNFINISHEDDOCRESPONSE.fields_by_name['stocktake'].message_type = _UNFINISHEDRECEIPT
_GETUNFINISHEDDOCRESPONSE.fields_by_name['demand'].message_type = _UNFINISHEDRECEIPT
_GETUNFINISHEDDOCRESPONSE.fields_by_name['self_picking'].message_type = _UNFINISHEDRECEIPT
DESCRIPTOR.message_types_by_name['GetUnfinishedDocRequest'] = _GETUNFINISHEDDOCREQUEST
DESCRIPTOR.message_types_by_name['UnfinishedReceipt'] = _UNFINISHEDRECEIPT
DESCRIPTOR.message_types_by_name['GetUnfinishedDocResponse'] = _GETUNFINISHEDDOCRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetUnfinishedDocRequest = _reflection.GeneratedProtocolMessageType('GetUnfinishedDocRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETUNFINISHEDDOCREQUEST,
  __module__ = 'mobile.mobile_common_pb2'
  # @@protoc_insertion_point(class_scope:mobile_common.GetUnfinishedDocRequest)
  ))
_sym_db.RegisterMessage(GetUnfinishedDocRequest)

UnfinishedReceipt = _reflection.GeneratedProtocolMessageType('UnfinishedReceipt', (_message.Message,), dict(
  DESCRIPTOR = _UNFINISHEDRECEIPT,
  __module__ = 'mobile.mobile_common_pb2'
  # @@protoc_insertion_point(class_scope:mobile_common.UnfinishedReceipt)
  ))
_sym_db.RegisterMessage(UnfinishedReceipt)

GetUnfinishedDocResponse = _reflection.GeneratedProtocolMessageType('GetUnfinishedDocResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETUNFINISHEDDOCRESPONSE,
  __module__ = 'mobile.mobile_common_pb2'
  # @@protoc_insertion_point(class_scope:mobile_common.GetUnfinishedDocResponse)
  ))
_sym_db.RegisterMessage(GetUnfinishedDocResponse)



_MOBILECOMMONSERVICE = _descriptor.ServiceDescriptor(
  name='MobileCommonService',
  full_name='mobile_common.MobileCommonService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=877,
  serialized_end=1046,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetUnfinishedDoc',
    full_name='mobile_common.MobileCommonService.GetUnfinishedDoc',
    index=0,
    containing_service=None,
    input_type=_GETUNFINISHEDDOCREQUEST,
    output_type=_GETUNFINISHEDDOCRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\022$/api/v2/supply/mobile/unfinished/doc'),
  ),
])
_sym_db.RegisterServiceDescriptor(_MOBILECOMMONSERVICE)

DESCRIPTOR.services_by_name['MobileCommonService'] = _MOBILECOMMONSERVICE

# @@protoc_insertion_point(module_scope)
