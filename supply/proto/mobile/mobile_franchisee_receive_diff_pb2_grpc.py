# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from mobile import mobile_franchisee_receive_diff_pb2 as mobile_dot_mobile__franchisee__receive__diff__pb2


class FranchiseeMobileReceivingDiffServiceStub(object):
  """ReceivingDiffService 收货差异相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.ListReceiveDiff = channel.unary_unary(
        '/franchisee_mobile_receive_diff.FranchiseeMobileReceivingDiffService/ListReceiveDiff',
        request_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.ListReceiveDiffRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.ListReceiveDiffResponse.FromString,
        )
    self.GetReceiveDiffById = channel.unary_unary(
        '/franchisee_mobile_receive_diff.FranchiseeMobileReceivingDiffService/GetReceiveDiffById',
        request_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.GetReceiveDiffByIdRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.ReceiveDiff.FromString,
        )
    self.GetReceiveDiffProductById = channel.unary_unary(
        '/franchisee_mobile_receive_diff.FranchiseeMobileReceivingDiffService/GetReceiveDiffProductById',
        request_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.GetReceiveDiffProductByIdRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.GetReceiveDiffProductByIdResponse.FromString,
        )
    self.SubmitReceiveDiff = channel.unary_unary(
        '/franchisee_mobile_receive_diff.FranchiseeMobileReceivingDiffService/SubmitReceiveDiff',
        request_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.SubmitReceiveDiffRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.SubmitReceiveDiffResponse.FromString,
        )
    self.ConfirmReceiveDiff = channel.unary_unary(
        '/franchisee_mobile_receive_diff.FranchiseeMobileReceivingDiffService/ConfirmReceiveDiff',
        request_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.ConfirmReceiveDiffRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.ConfirmReceiveDiffResponse.FromString,
        )
    self.RejectReceiveDiff = channel.unary_unary(
        '/franchisee_mobile_receive_diff.FranchiseeMobileReceivingDiffService/RejectReceiveDiff',
        request_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.RejectReceiveDiffRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.RejectReceiveDiffResponse.FromString,
        )
    self.UpdateReceiveDiff = channel.unary_unary(
        '/franchisee_mobile_receive_diff.FranchiseeMobileReceivingDiffService/UpdateReceiveDiff',
        request_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.UpdateReceiveDiffRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.UpdateReceiveDiffResponse.FromString,
        )
    self.DeleteReceiveDiff = channel.unary_unary(
        '/franchisee_mobile_receive_diff.FranchiseeMobileReceivingDiffService/DeleteReceiveDiff',
        request_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.DeleteReceiveDiffRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.DeleteReceiveDiffResponse.FromString,
        )
    self.GetHistoryById = channel.unary_unary(
        '/franchisee_mobile_receive_diff.FranchiseeMobileReceivingDiffService/GetHistoryById',
        request_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.GetHistoryByIdRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.HistoryResponse.FromString,
        )
    self.CreateReceivingDiff = channel.unary_unary(
        '/franchisee_mobile_receive_diff.FranchiseeMobileReceivingDiffService/CreateReceivingDiff',
        request_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.CreateReceivingDiffRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.CreateReceivingDiffResponse.FromString,
        )


class FranchiseeMobileReceivingDiffServiceServicer(object):
  """ReceivingDiffService 收货差异相关服务
  """

  def ListReceiveDiff(self, request, context):
    """查询收货差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceiveDiffById(self, request, context):
    """根据id查询收货差异单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceiveDiffProductById(self, request, context):
    """根据id查商品详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SubmitReceiveDiff(self, request, context):
    """提交收货差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ConfirmReceiveDiff(self, request, context):
    """确认收货差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RejectReceiveDiff(self, request, context):
    """驳回收货差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateReceiveDiff(self, request, context):
    """更新收货单差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteReceiveDiff(self, request, context):
    """删除收货差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetHistoryById(self, request, context):
    """根据id查询单据历史记录
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateReceivingDiff(self, request, context):
    """手动新建收货差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_FranchiseeMobileReceivingDiffServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'ListReceiveDiff': grpc.unary_unary_rpc_method_handler(
          servicer.ListReceiveDiff,
          request_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.ListReceiveDiffRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.ListReceiveDiffResponse.SerializeToString,
      ),
      'GetReceiveDiffById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceiveDiffById,
          request_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.GetReceiveDiffByIdRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.ReceiveDiff.SerializeToString,
      ),
      'GetReceiveDiffProductById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceiveDiffProductById,
          request_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.GetReceiveDiffProductByIdRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.GetReceiveDiffProductByIdResponse.SerializeToString,
      ),
      'SubmitReceiveDiff': grpc.unary_unary_rpc_method_handler(
          servicer.SubmitReceiveDiff,
          request_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.SubmitReceiveDiffRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.SubmitReceiveDiffResponse.SerializeToString,
      ),
      'ConfirmReceiveDiff': grpc.unary_unary_rpc_method_handler(
          servicer.ConfirmReceiveDiff,
          request_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.ConfirmReceiveDiffRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.ConfirmReceiveDiffResponse.SerializeToString,
      ),
      'RejectReceiveDiff': grpc.unary_unary_rpc_method_handler(
          servicer.RejectReceiveDiff,
          request_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.RejectReceiveDiffRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.RejectReceiveDiffResponse.SerializeToString,
      ),
      'UpdateReceiveDiff': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateReceiveDiff,
          request_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.UpdateReceiveDiffRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.UpdateReceiveDiffResponse.SerializeToString,
      ),
      'DeleteReceiveDiff': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteReceiveDiff,
          request_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.DeleteReceiveDiffRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.DeleteReceiveDiffResponse.SerializeToString,
      ),
      'GetHistoryById': grpc.unary_unary_rpc_method_handler(
          servicer.GetHistoryById,
          request_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.GetHistoryByIdRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.HistoryResponse.SerializeToString,
      ),
      'CreateReceivingDiff': grpc.unary_unary_rpc_method_handler(
          servicer.CreateReceivingDiff,
          request_deserializer=mobile_dot_mobile__franchisee__receive__diff__pb2.CreateReceivingDiffRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__receive__diff__pb2.CreateReceivingDiffResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'franchisee_mobile_receive_diff.FranchiseeMobileReceivingDiffService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
