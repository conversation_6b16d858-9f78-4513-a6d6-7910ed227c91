{"swagger": "2.0", "info": {"title": "mobile/mobile_franchisee_transfer.proto", "version": "version not set"}, "tags": [{"name": "mobileFranchiseeTransfer"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/mobile/franchisee/transfer": {"get": {"summary": "GetTransfer 查询调拨单", "operationId": "mobileFranchiseeTransfer_GetTransfer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_transferGetTransferResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "include_total", "description": "是否包含总数", "in": "query", "required": false, "type": "boolean"}, {"name": "status", "description": "状态", "in": "query", "required": false, "type": "array", "items": {"type": "string", "enum": ["NONE", "INITED", "SUBMITTED", "CONFIRMED", "CANCELLED"]}, "collectionFormat": "multi"}, {"name": "shipping_stores", "description": "调出门店", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "receiving_stores", "description": "调入门店", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "product_ids", "description": "商品ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "offset", "description": "分页开始处", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "description": "返回条数", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "start_date", "description": "查询开始时间", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "查询结束时间", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "ids", "description": "单据id列表查询", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "order", "description": "排序(默认asc)", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "branch_type", "description": "组织类型，区分门店/仓库/加工中心 STORE/WAREHOUSE/MACHINING_CENTER", "in": "query", "required": false, "type": "string"}, {"name": "types", "description": "调拨类型(自动AUTO/手动MANUAL)", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "sub_type", "description": "区分内部`INTERNAL`/外部`EXTERNAL`调拨", "in": "query", "required": false, "type": "string"}, {"name": "receiving_positions", "description": "接收仓位*预留*", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "shipping_positions", "description": "调出仓位*预留*", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}], "tags": ["mobileFranchiseeTransfer"]}}, "/api/v2/supply/mobile/franchisee/transfer/bi/detailed": {"get": {"summary": "GetTransferCollectDetailed调拨单明细汇总报表", "operationId": "mobileFranchiseeTransfer_GetTransferCollectDetailed", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_transferGetTransferCollectDetailedResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "st_ids", "description": "门店id列表", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "store_ids", "description": "掉入调出门店id，不用", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "category_ids", "description": "商品类别列表", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "product_name", "in": "query", "required": false, "type": "string"}, {"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean"}, {"name": "is_in", "description": "true时返回收货门店，false返回调出门店", "in": "query", "required": false, "type": "boolean"}, {"name": "order", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "jde_code", "in": "query", "required": false, "type": "string"}, {"name": "is_wms_store", "in": "query", "required": false, "type": "boolean"}, {"name": "branch_type", "description": "区分门店还是仓库", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "type", "description": "调拨类型(自动AUTO/手动MANUAL)", "in": "query", "required": false, "type": "string"}, {"name": "sub_type", "description": "区分内部`INTERNAL`/外部`EXTERNAL`调拨(必传)", "in": "query", "required": false, "type": "string"}], "tags": ["mobileFranchiseeTransfer"]}}, "/api/v2/supply/mobile/franchisee/transfer/collect/report": {"get": {"summary": "GetTransferCollect调拨单汇总报表", "operationId": "mobileFranchiseeTransfer_GetTransferCollect", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_transferGetTransferCollectResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "st_ids", "description": "门店id列表", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "store_ids", "description": "掉入调出门店id，不用", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "category_ids", "description": "商品类别列表", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "product_name", "in": "query", "required": false, "type": "string"}, {"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean"}, {"name": "is_in", "description": "true时返回收货门店，false返回调出门店", "in": "query", "required": false, "type": "boolean"}, {"name": "order", "description": "排序(默认asc)", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "jde_code", "in": "query", "required": false, "type": "string"}, {"name": "is_wms_store", "in": "query", "required": false, "type": "boolean"}, {"name": "branch_type", "description": "区分门店还是仓库", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "type", "description": "调拨类型(自动AUTO/手动MANUAL)", "in": "query", "required": false, "type": "string"}, {"name": "sub_type", "description": "区分内部`INTERNAL`/外部`EXTERNAL`调拨(必传)", "in": "query", "required": false, "type": "string"}], "tags": ["mobileFranchiseeTransfer"]}}, "/api/v2/supply/mobile/franchisee/transfer/main": {"post": {"summary": "CreateTransfer 创建调拨单", "operationId": "mobileFranchiseeTransfer_CreateTransfer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_transferTransfer"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mobile_franchisee_transferCreateTransferRequest"}}], "tags": ["mobileFranchiseeTransfer"]}}, "/api/v2/supply/mobile/franchisee/transfer/main/{transfer_id}/cancel": {"post": {"summary": "CancelTransfer 取消调拨单13", "operationId": "mobileFranchiseeTransfer_CancelTransfer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_transferTransfer"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "transfer_id", "description": "调拨单ID", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object"}}], "tags": ["mobileFranchiseeTransfer"]}}, "/api/v2/supply/mobile/franchisee/transfer/main/{transfer_id}/confirm": {"post": {"summary": "ConfirmTransfer 确认调拨单", "operationId": "mobileFranchiseeTransfer_ConfirmTransfer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_transferTransfer"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "transfer_id", "description": "调拨单ID", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"receiver": {"type": "string", "format": "uint64"}, "receiving_store": {"type": "string", "format": "uint64"}, "products": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_transferConfirmPostTransferProduct"}}, "branch_type": {"type": "string", "title": "组织类型，区分门店/仓库/加工中心 STORE/WAREHOUSE/MACHINING_CENTER"}}}}], "tags": ["mobileFranchiseeTransfer"]}}, "/api/v2/supply/mobile/franchisee/transfer/main/{transfer_id}/delete": {"post": {"summary": "DeleteTransfer 删除调拨单", "operationId": "mobileFranchiseeTransfer_DeleteTransfer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_transferDeleteTransferResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "transfer_id", "description": "调拨单ID", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object"}}], "tags": ["mobileFranchiseeTransfer"]}}, "/api/v2/supply/mobile/franchisee/transfer/main/{transfer_id}/submit": {"post": {"summary": "SubmitTransfer 提交调拨单", "operationId": "mobileFranchiseeTransfer_SubmitTransfer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_transferTransfer"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "transfer_id", "description": "调拨单ID", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"receiver": {"type": "string", "format": "uint64"}, "receiving_store": {"type": "string", "format": "uint64"}, "products": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_transferPostTransferProduct"}}, "branch_type": {"type": "string", "title": "组织类型，区分门店/仓库/加工中心 STORE/WAREHOUSE/MACHINING_CENTER"}}}}], "tags": ["mobileFranchiseeTransfer"]}}, "/api/v2/supply/mobile/franchisee/transfer/main/{transfer_id}/update": {"post": {"summary": "UpdateTransfer 修改调拨单", "operationId": "mobileFranchiseeTransfer_UpdateTransfer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_transferUpdateTransferResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "transfer_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"products": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_transferPostTransferProduct"}}, "remark": {"type": "string", "title": "备注"}, "transfer_date": {"type": "string", "format": "date-time", "title": "调拨单日期"}, "branch_type": {"type": "string", "title": "组织类型，区分门店/仓库/加工中心 STORE/WAREHOUSE/MACHINING_CENTER"}, "type": {"type": "string", "title": "调拨类型(自动AUTO/手动MANUAL)*预留*"}, "sub_type": {"type": "string", "title": "区分内部`INTERNAL`/外部`EXTERNAL`调拨*预留*"}}}}], "tags": ["mobileFranchiseeTransfer"]}}, "/api/v2/supply/mobile/franchisee/transfer/product/{transfer_id}/delete": {"post": {"summary": "DeleteTransferProduct 删除调拨单商品", "operationId": "mobileFranchiseeTransfer_DeleteTransferProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_transferDeleteTransferProductResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "transfer_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "调拨商品条目ID"}}}}], "tags": ["mobileFranchiseeTransfer"]}}, "/api/v2/supply/mobile/franchisee/transfer/store/{store_id}/category": {"get": {"summary": "GetTransferProductCategoryByBranchID 取得门店可调拨商品的分类列表", "operationId": "mobileFranchiseeTransfer_GetTransferProductCategoryByBranchID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_transferGetTransferProductCategoryByBranchIDResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "store_id", "description": "门店id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["mobileFranchiseeTransfer"]}}, "/api/v2/supply/mobile/franchisee/transfer/store/{store_id}/product": {"get": {"summary": "GetTransferProductByBranchID 取得门店可调拨商品", "operationId": "mobileFranchiseeTransfer_GetTransferProductByBranchID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_transferGetTransferProductByBranchIDResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "store_id", "description": "门店id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "include_total", "description": "是否包含总数", "in": "query", "required": false, "type": "boolean"}, {"name": "order", "description": "排序方式", "in": "query", "required": false, "type": "string"}, {"name": "offset", "description": "分页开始处", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "description": "返回条数", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "search_fields", "description": "模糊查询方式", "in": "query", "required": false, "type": "string"}, {"name": "search", "description": "模糊查询条件", "in": "query", "required": false, "type": "string"}, {"name": "category_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "order_by", "description": "排序条件", "in": "query", "required": false, "type": "string"}, {"name": "sort", "description": "asc or desc，暂时未使用", "in": "query", "required": false, "type": "string"}], "tags": ["mobileFranchiseeTransfer"]}}, "/api/v2/supply/mobile/franchisee/transfer/store/{store_id}/region": {"get": {"summary": "GetTransferRegionByID 查询相同属性区域门店", "operationId": "mobileFranchiseeTransfer_GetTransferRegionByBranchID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_transferGetTransferRegionByBranchIDResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "store_id", "description": "门店ID", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["mobileFranchiseeTransfer"]}}, "/api/v2/supply/mobile/franchisee/transfer/{transfer_id}": {"get": {"summary": "GetTransferByID 查询一个调拨单", "operationId": "mobileFranchiseeTransfer_GetTransferByID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_transferGetTransferByIDResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "transfer_id", "description": "调拨单id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["mobileFranchiseeTransfer"]}}, "/api/v2/supply/mobile/franchisee/transfer/{transfer_id}/log": {"get": {"summary": "调拨历史记录", "operationId": "mobileFranchiseeTransfer_GetTransferLog", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_transferGetTransferLogResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "transfer_id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["mobileFranchiseeTransfer"]}}, "/api/v2/supply/mobile/franchisee/transfer/{transfer_id}/product": {"get": {"summary": "GetTransferProductByTransferID 获取一个调拨单商品", "operationId": "mobileFranchiseeTransfer_GetTransferProductByTransferID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_transferGetTransferProductByTransferIDResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "transfer_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "include_total", "description": "是否包含总数", "in": "query", "required": false, "type": "boolean"}, {"name": "order", "description": "排序方式", "in": "query", "required": false, "type": "string"}, {"name": "offset", "description": "分页开始处", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "description": "返回条数", "in": "query", "required": false, "type": "integer", "format": "int64"}], "tags": ["mobileFranchiseeTransfer"]}}}, "definitions": {"mobile_franchisee_transferCategoryItem": {"type": "object", "properties": {"category_id": {"type": "string", "format": "uint64"}, "category_name": {"type": "string"}, "product_count": {"type": "string", "format": "uint64"}}}, "mobile_franchisee_transferConfirmPostTransferProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64", "title": "商品ID"}, "unit_id": {"type": "string", "format": "uint64", "title": "调拨单位ID"}, "confirmed_received_quantity": {"type": "number", "format": "double", "title": "收货数量"}}}, "mobile_franchisee_transferCreateTransferRequest": {"type": "object", "properties": {"request_id": {"type": "string", "format": "uint64", "title": "请求ID"}, "shipping_store": {"type": "string", "format": "uint64", "title": "调出门店"}, "receiving_store": {"type": "string", "format": "uint64", "title": "接收门店"}, "products": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_transferPostTransferProduct"}}, "remark": {"type": "string", "title": "备注"}, "shipping_date": {"type": "string", "format": "date-time", "title": "调出时间"}, "transfer_date": {"type": "string", "format": "date-time", "title": "调拨单日期"}, "branch_type": {"type": "string", "title": "组织类型，区分门店/仓库/加工中心 STORE/WAREHOUSE/MACHINING_CENTER"}, "type": {"type": "string", "title": "调拨类型(自动AUTO/手动MANUAL)"}, "sub_type": {"type": "string", "title": "区分内部`INTERNAL`/外部`EXTERNAL`调拨"}, "receiving_position": {"type": "string", "format": "uint64", "title": "接收仓位*预留*"}, "shipping_position": {"type": "string", "format": "uint64", "title": "调出仓位*预留*"}, "receiving_position_name": {"type": "string", "title": "接收仓位名称*预留*"}, "shipping_position_name": {"type": "string", "title": "调出仓位名称*预留*"}, "sub_account_type": {"type": "string", "title": "子账户类型，如果开启了多仓位值为\"position\"没配置多仓位为空*预留*"}}}, "mobile_franchisee_transferDeleteTransferProductResponse": {"type": "object", "properties": {"result": {"type": "boolean"}}}, "mobile_franchisee_transferDeleteTransferResponse": {"type": "object", "properties": {"result": {"type": "boolean"}}}, "mobile_franchisee_transferGetTransferByIDResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "调拨单ID"}, "partner_id": {"type": "string", "format": "uint64"}, "shipping_store": {"type": "string", "format": "uint64", "title": "调出门店ID"}, "shipping_store_name": {"type": "string", "title": "调拨门店名称"}, "receiving_store": {"type": "string", "format": "uint64", "title": "收货门店ID"}, "receiving_store_name": {"type": "string", "title": "收货门店名称"}, "code": {"type": "string", "title": "调拨单号"}, "remark": {"type": "string", "title": "备注"}, "status": {"$ref": "#/definitions/mobile_franchisee_transferGetTransferByIDResponseSTATUS"}, "process_status": {"$ref": "#/definitions/mobile_franchisee_transferP_STATUS", "title": "操作状态"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人"}, "transfer_date": {"type": "string", "format": "date-time", "title": "调拨单日期"}, "receiving_date": {"type": "string", "format": "date-time", "title": "调拨单收货日"}, "shipping_date": {"type": "string", "format": "date-time", "title": "调拨时间"}, "created_at": {"type": "string", "format": "date-time", "title": "调拨单创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "branch_type": {"type": "string", "title": "组织类型，区分门店/仓库/加工中心 STORE/WAREHOUSE/MACHINING_CENTER"}, "sub_type": {"type": "string", "title": "区分内部`INTERNAL`/外部`EXTERNAL`调拨"}, "type": {"type": "string", "title": "调拨类型(自动AUTO/手动MANUAL)"}, "receiving_position": {"type": "string", "format": "uint64", "title": "接收仓位id*预留*"}, "receiving_position_name": {"type": "string", "title": "接收仓位名称*预留*"}, "shipping_position": {"type": "string", "format": "uint64", "title": "调出仓位id*预留*"}, "shipping_position_name": {"type": "string", "title": "调出仓位名称*预留*"}, "sub_account_type": {"type": "string", "title": "子账户类型，如果开启了多仓位值为\"position\"没配置多仓位为空*预留*"}, "extends": {"type": "string", "title": "扩展字段*预留*"}, "total_amount": {"type": "number", "format": "double", "title": "调拨总金额"}, "total_sales_amount": {"type": "number", "format": "double", "title": "调拨零售总金额"}}}, "mobile_franchisee_transferGetTransferByIDResponseSTATUS": {"type": "string", "enum": ["NONE", "INITED", "SUBMITTED", "CONFIRMED", "CANCELLED"], "default": "NONE", "title": "调拨单状态"}, "mobile_franchisee_transferGetTransferCollectDetailedResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_transferTransferCollectDetailed"}}, "total": {"$ref": "#/definitions/mobile_franchisee_transferTransferCollectTotal"}}}, "mobile_franchisee_transferGetTransferCollectResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_transferTransferCollect"}}, "total": {"$ref": "#/definitions/mobile_franchisee_transferTransferCollectTotal"}}}, "mobile_franchisee_transferGetTransferLogResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_transferLog"}}, "total": {"type": "string", "format": "uint64"}}}, "mobile_franchisee_transferGetTransferProductByBranchIDResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_transferSelectTransferProduct"}}, "total": {"type": "integer", "format": "int64"}, "inventory_unchanged_rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_transferSelectTransferProduct"}, "title": "库存未异动的商品"}}}, "mobile_franchisee_transferGetTransferProductByTransferIDResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_transferTransferProduct"}}, "total": {"type": "integer", "format": "int64"}}}, "mobile_franchisee_transferGetTransferProductCategoryByBranchIDResponse": {"type": "object", "properties": {"category_items": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_transferCategoryItem"}, "title": "门店id"}}}, "mobile_franchisee_transferGetTransferRegionByBranchIDResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_transferRegionBranch"}}}}, "mobile_franchisee_transferGetTransferRequestSTATUS": {"type": "string", "enum": ["NONE", "INITED", "SUBMITTED", "CONFIRMED", "CANCELLED"], "default": "NONE"}, "mobile_franchisee_transferGetTransferResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_transferTransfer"}}, "total": {"type": "integer", "format": "int64"}}}, "mobile_franchisee_transferLog": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "历史记录id"}, "status": {"type": "string", "title": "状态"}, "created_by": {"type": "string", "format": "uint64", "title": "操作人"}, "created_at": {"type": "string", "format": "date-time", "title": "操作时间"}, "reason": {"type": "string", "title": "原因"}, "created_name": {"type": "string", "title": "操作人名称"}}}, "mobile_franchisee_transferP_STATUS": {"type": "string", "enum": ["NONE", "INITED", "PROCESSING", "SUCCESSED", "FAILED"], "default": "NONE"}, "mobile_franchisee_transferPostTransferProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64", "title": "商品ID"}, "unit_id": {"type": "string", "format": "uint64", "title": "调拨单位ID"}, "quantity": {"type": "number", "format": "double", "title": "调拨数量"}, "tax_rate": {"type": "number", "format": "double"}, "tax_price": {"type": "number", "format": "double"}, "cost_price": {"type": "number", "format": "double"}, "amount": {"type": "number", "format": "double"}, "sales_price": {"type": "number", "format": "double"}, "sales_amount": {"type": "number", "format": "double"}}}, "mobile_franchisee_transferRegionBranch": {"type": "object", "properties": {"address": {"type": "string", "title": "门店地址"}, "id": {"type": "string", "format": "uint64", "title": "门店ID"}, "name": {"type": "string", "title": "门店名字"}, "code": {"type": "string"}}}, "mobile_franchisee_transferSelectTransferProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64", "title": "商品ID"}, "product_code": {"type": "string", "title": "商品编码"}, "category_id": {"type": "string", "format": "uint64", "title": "单位分类"}, "product_name": {"type": "string", "title": "商品名字"}, "unit": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_transferTransferProductUnit"}}, "barcode": {"type": "array", "items": {"type": "string"}}, "spec": {"type": "string"}, "category_name": {"type": "string"}, "model_name": {"type": "string"}, "real_inventory_qty": {"type": "number", "format": "double", "title": "实时库存数量"}}}, "mobile_franchisee_transferTransfer": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "调拨单ID"}, "partner_id": {"type": "string", "format": "uint64"}, "shipping_store": {"type": "string", "format": "uint64", "title": "调出门店ID"}, "shipping_store_name": {"type": "string", "title": "调出门店名称"}, "receiving_store": {"type": "string", "format": "uint64", "title": "收货门店ID"}, "receiving_store_name": {"type": "string", "title": "收货门店名称"}, "code": {"type": "string", "title": "调拨单号"}, "remark": {"type": "string", "title": "备注"}, "status": {"$ref": "#/definitions/mobile_franchisee_transferTransferSTATUS"}, "process_status": {"$ref": "#/definitions/mobile_franchisee_transferP_STATUS", "title": "操作状态"}, "created_by": {"type": "string", "format": "uint64", "title": "创建者"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新者"}, "transfer_date": {"type": "string", "format": "date-time", "title": "调拨单日期"}, "receiving_date": {"type": "string", "format": "date-time", "title": "调拨单收货日"}, "shipping_date": {"type": "string", "format": "date-time", "title": "调拨时间"}, "created_at": {"type": "string", "format": "date-time", "title": "调拨单创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "branch_type": {"type": "string", "title": "组织类型，区分门店/仓库/加工中心 STORE/WAREHOUSE/MACHINING_CENTER"}, "sub_type": {"type": "string", "title": "区分内部`INTERNAL`/外部`EXTERNAL`调拨"}, "type": {"type": "string", "title": "调拨类型(自动AUTO/手动MANUAL)"}, "receiving_position": {"type": "string", "format": "uint64", "title": "接收仓位id*预留*"}, "receiving_position_name": {"type": "string", "title": "接收仓位名称*预留*"}, "shipping_position": {"type": "string", "format": "uint64", "title": "调出仓位id*预留*"}, "shipping_position_name": {"type": "string", "title": "调出仓位名称*预留*"}, "sub_account_type": {"type": "string", "title": "子账户类型，如果开启了多仓位值为\"position\"没配置多仓位为空*预留*"}, "extends": {"type": "string", "title": "扩展字段*预留*"}, "total_amount": {"type": "number", "format": "double", "title": "调拨总金额"}, "total_sales_amount": {"type": "number", "format": "double", "title": "调拨零售总金额"}}}, "mobile_franchisee_transferTransferCollect": {"type": "object", "properties": {"accounting_quantity": {"type": "number", "format": "double"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "category_code": {"type": "string"}, "category_id": {"type": "string", "format": "uint64"}, "category_name": {"type": "string"}, "category_parent": {"type": "object", "title": "商品标签层级"}, "product_code": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "product_price": {"type": "number", "format": "double"}, "receiving_store": {"type": "string", "format": "uint64"}, "receiving_store_code": {"type": "string"}, "receiving_store_us_id": {"type": "string"}, "receiving_store_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "shipping_store": {"type": "string", "format": "uint64"}, "shipping_store_code": {"type": "string"}, "shipping_store_name": {"type": "string"}, "shipping_store_us_id": {"type": "string"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "product_spec": {"type": "string"}, "price": {"type": "number", "format": "double"}, "receiving_position": {"type": "string", "format": "uint64"}, "receiving_position_code": {"type": "string"}, "receiving_position_name": {"type": "string"}, "shipping_position": {"type": "string", "format": "uint64"}, "shipping_position_code": {"type": "string"}, "shipping_position_name": {"type": "string"}}}, "mobile_franchisee_transferTransferCollectDetailed": {"type": "object", "properties": {"accounting_quantity": {"type": "number", "format": "double"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "category_code": {"type": "string"}, "category_id": {"type": "string", "format": "uint64"}, "category_name": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "product_code": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "product_spec": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "receiving_store": {"type": "string", "format": "uint64"}, "receiving_store_code": {"type": "string"}, "receiving_store_name": {"type": "string"}, "shipping_store": {"type": "string", "format": "uint64"}, "shipping_store_code": {"type": "string"}, "shipping_store_name": {"type": "string"}, "status": {"type": "string"}, "transfer_code": {"type": "string"}, "transfer_date": {"type": "string", "format": "date-time"}, "transfer_id": {"type": "string", "format": "uint64"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "shipping_store_us_id": {"type": "string"}, "receiving_store_us_id": {"type": "string"}, "price": {"type": "number", "format": "double"}, "jde_code": {"type": "string"}, "code": {"type": "string"}, "receiving_position": {"type": "string", "format": "uint64"}, "receiving_position_code": {"type": "string"}, "receiving_position_name": {"type": "string"}, "shipping_position": {"type": "string", "format": "uint64"}, "shipping_position_code": {"type": "string"}, "shipping_position_name": {"type": "string"}}}, "mobile_franchisee_transferTransferCollectTotal": {"type": "object", "properties": {"count": {"type": "string", "format": "uint64"}, "sum_accounting_quantity": {"type": "number", "format": "double"}, "sum_quantity": {"type": "number", "format": "double"}}}, "mobile_franchisee_transferTransferProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "调拨商品条目ID"}, "product_id": {"type": "string", "format": "uint64", "title": "商品ID"}, "transfer_id": {"type": "string", "format": "uint64", "title": "调拨单ID"}, "unit_id": {"type": "string", "format": "uint64", "title": "调拨单位ID"}, "unit_name": {"type": "string", "title": "调拨单位名字"}, "unit_spec": {"type": "string", "title": "调拨单位分类"}, "accounting_quantity": {"type": "number", "format": "double", "title": "核算数量"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "核算单位ID"}, "accounting_unit_name": {"type": "string", "title": "核算单位名字"}, "accounting_unit_spec": {"type": "string", "title": "核算单位分类"}, "accounting_received_quantity": {"type": "number", "format": "double", "title": "确认数量"}, "item_number": {"type": "integer", "format": "int64", "title": "条目数量"}, "product_code": {"type": "string", "title": "商品编码"}, "product_name": {"type": "string", "title": "商品名字"}, "quantity": {"type": "number", "format": "double", "title": "数量"}, "receiving_store": {"type": "string", "format": "uint64", "title": "接收门店"}, "shipping_store": {"type": "string", "format": "uint64", "title": "调出门店"}, "created_by": {"type": "string", "format": "uint64", "title": "创建者"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新者"}, "transfer_date": {"type": "string", "format": "date-time", "title": "调拨单日期"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "extends": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "user_id": {"type": "string", "format": "uint64"}, "confirmed_received_quantity": {"type": "number", "format": "double", "title": "确认收货数量"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "category_id": {"type": "string", "format": "uint64"}, "category_name": {"type": "string"}, "model_name": {"type": "string"}, "tax_rate": {"type": "number", "format": "double"}, "tax_price": {"type": "number", "format": "double"}, "cost_price": {"type": "number", "format": "double"}, "amount": {"type": "number", "format": "double"}, "sales_price": {"type": "number", "format": "double"}, "sales_amount": {"type": "number", "format": "double"}, "unit_rate": {"type": "number", "format": "double"}}}, "mobile_franchisee_transferTransferProductUnit": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "调拨单位ID"}, "quantity": {"type": "number", "format": "double", "title": "调拨数量"}, "name": {"type": "string"}, "tax_rate": {"type": "number", "format": "double"}, "tax_price": {"type": "number", "format": "double"}, "cost_price": {"type": "number", "format": "double"}, "sales_price": {"type": "number", "format": "double"}, "unit_rate": {"type": "number", "format": "double"}}}, "mobile_franchisee_transferTransferSTATUS": {"type": "string", "enum": ["NONE", "INITED", "SUBMITTED", "CONFIRMED", "CANCELLED"], "default": "NONE", "title": "调拨单状态"}, "mobile_franchisee_transferUpdateTransferResponse": {"type": "object", "properties": {"result": {"type": "boolean"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "protobufNullValue": {"type": "string", "enum": ["NULL_VALUE"], "default": "NULL_VALUE", "description": "`NullValue` is a singleton enumeration to represent the null value for the\n`Value` type union.\n\nThe JSON representation for `NullValue` is JSON `null`.\n\n - NULL_VALUE: Null value."}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}}}