# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mobile/mobile_franchisee_return.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='mobile/mobile_franchisee_return.proto',
  package='franchisee_returns',
  syntax='proto3',
  serialized_options=_b('Z\024./franchisee_returns'),
  serialized_pb=_b('\n%mobile/mobile_franchisee_return.proto\x12\x12\x66ranchisee_returns\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xaa\x06\n\x11ListReturnRequest\x12\x11\n\tstore_ids\x18\x01 \x03(\x04\x12\x0e\n\x06status\x18\x02 \x03(\t\x12.\n\nstart_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\r\n\x05limit\x18\x06 \x01(\x05\x12\x0e\n\x06offset\x18\x07 \x01(\x05\x12\x15\n\rinclude_total\x18\x08 \x01(\x08\x12\x11\n\tis_direct\x18\t \x01(\x08\x12\x10\n\x08order_by\x18\n \x01(\t\x12\x16\n\x0elogistics_type\x18\x0c \x01(\t\x12\x11\n\tis_adjust\x18\r \x01(\x08\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\r\n\x05order\x18\x0f \x01(\t\x12\x14\n\x0cstorage_type\x18\x11 \x01(\t\x12\x0c\n\x04type\x18\x12 \x01(\t\x12\x10\n\x08sub_type\x18\x13 \x01(\t\x12\x14\n\x0creceive_code\x18\x14 \x01(\t\x12\x0b\n\x03lan\x18\x15 \x01(\t\x12\x34\n\x10return_date_from\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0ereturn_date_to\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x36\n\x12\x64\x65livery_date_from\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x10\x64\x65livery_date_to\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rreturn_number\x18\x1a \x01(\t\x12\x13\n\x0bproduct_ids\x18\x1b \x03(\x04\x12\x11\n\treturn_by\x18\x1c \x03(\x04\x12\x11\n\treturn_to\x18\x1d \x03(\x04\x12\x11\n\tsource_id\x18\x1e \x01(\x04\x12\x13\n\x0bsource_code\x18\x1f \x01(\t\x12\x14\n\x0cpayment_ways\x18  \x01(\t\x12\x15\n\rdelivery_code\x18! \x01(\t\x12\x0b\n\x03ids\x18\" \x03(\x04\"N\n\x12ListReturnResponse\x12)\n\x04rows\x18\x01 \x03(\x0b\x32\x1b.franchisee_returns.Returns\x12\r\n\x05total\x18\x02 \x01(\x04\"\xcf\x0b\n\x07Returns\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x11\n\treturn_by\x18\x03 \x01(\x04\x12\x15\n\rreturn_number\x18\x04 \x01(\t\x12\x0e\n\x06status\x18\x05 \x01(\t\x12\x11\n\treview_by\x18\x06 \x01(\x04\x12\x13\n\x0bis_returned\x18\x07 \x01(\x08\x12\x1e\n\x16return_delivery_number\x18\x08 \x01(\t\x12/\n\x0breturn_date\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x38\n\x14return_delivery_date\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rreturn_reason\x18\x0b \x01(\t\x12\x0c\n\x04type\x18\x0c \x01(\t\x12\x10\n\x08sub_type\x18\r \x01(\t\x12\x0e\n\x06remark\x18\x0e \x01(\t\x12\x1a\n\x12store_secondary_id\x18\x0f \x01(\t\x12.\n\ncreated_at\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x12 \x01(\x04\x12\x12\n\nupdated_by\x18\x13 \x01(\x04\x12\x11\n\treturn_to\x18\x14 \x01(\x04\x12\x18\n\x10inventory_status\x18\x15 \x01(\t\x12\x18\n\x10inventory_req_id\x18\x16 \x01(\x04\x12\x12\n\npartner_id\x18\x17 \x01(\x04\x12\x11\n\tis_direct\x18\x18 \x01(\x08\x12\x15\n\rreject_reason\x18\x19 \x01(\t\x12\x34\n\x0b\x61ttachments\x18\x1a \x03(\x0b\x32\x1f.franchisee_returns.Attachments\x12\x14\n\x0c\x63reated_name\x18\x1b \x01(\t\x12\x14\n\x0cupdated_name\x18\x1c \x01(\t\x12\x14\n\x0cproduct_nums\x18\x1d \x01(\x04\x12\x16\n\x0elogistics_type\x18\x1f \x01(\t\x12\x11\n\tis_adjust\x18  \x01(\x08\x12\x11\n\tsend_type\x18! \x01(\t\x12\x12\n\ntrans_type\x18# \x01(\t\x12\x14\n\x0cstorage_type\x18$ \x01(\t\x12\x14\n\x0creceiving_id\x18% \x01(\x04\x12\x16\n\x0ereceiving_code\x18& \x01(\t\x12\x31\n\rdelivery_date\x18\' \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nrequest_id\x18( \x01(\x04\x12\x15\n\rrefund_amount\x18) \x01(\x01\x12\x15\n\rfranchisee_id\x18\x37 \x01(\x04\x12\x17\n\x0f\x66ranchisee_code\x18\x38 \x01(\t\x12\x17\n\x0f\x66ranchisee_name\x18\x39 \x01(\t\x12\x13\n\x0bpayment_way\x18: \x01(\t\x12\x15\n\rdelivery_code\x18; \x01(\t\x12\x14\n\x0creceive_code\x18< \x01(\t\x12\x11\n\tsource_id\x18= \x01(\x04\x12\x13\n\x0bsource_code\x18> \x01(\t\x12\x16\n\x0ereturn_to_code\x18? \x01(\t\x12\x16\n\x0ereturn_to_name\x18@ \x01(\t\x12\x15\n\rreturns_price\x18\x41 \x01(\x01\x12\x14\n\x0cproduct_code\x18\x42 \x01(\t\x12\x14\n\x0cproduct_name\x18\x43 \x01(\t\x12\x11\n\tunit_spec\x18\x44 \x01(\t\x12\x11\n\tprice_tax\x18\x45 \x01(\x01\x12\x10\n\x08quantity\x18\x46 \x01(\x01\x12\x16\n\x0ereturn_by_code\x18G \x01(\t\x12\x16\n\x0ereturn_by_name\x18H \x01(\t\x12\x11\n\trefund_id\x18\x32 \x01(\x04\x12\x13\n\x0brefund_code\x18\x33 \x01(\t\x12\x16\n\x0ewarehouse_type\x18\x34 \x01(\t\x12\x13\n\x0blong_effect\x18\x35 \x01(\t\"\xef\x02\n\x13\x43reateReturnRequest\x12\x11\n\treturn_by\x18\x01 \x01(\x04\x12\x16\n\x0elogistics_type\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x10\n\x08sub_type\x18\x04 \x01(\t\x12\x38\n\x14return_delivery_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tsource_id\x18\x06 \x01(\x04\x12\x13\n\x0bsource_code\x18\x07 \x01(\t\x12\x15\n\rreturn_reason\x18\x08 \x01(\t\x12\x0e\n\x06remark\x18\t \x01(\t\x12\x34\n\x0b\x61ttachments\x18\n \x03(\x0b\x32\x1f.franchisee_returns.Attachments\x12\x12\n\nrequest_id\x18\x0b \x01(\x04\x12-\n\x08products\x18\x0c \x03(\x0b\x32\x1b.franchisee_returns.Product\x12\x0b\n\x03lan\x18\x14 \x01(\t\":\n\x14\x43reateReturnResponse\x12\x11\n\treturn_id\x18\x01 \x03(\x04\x12\x0f\n\x07payload\x18\x02 \x01(\x08\"\xf9\x02\n\x07Product\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x0f\n\x07unit_id\x18\x02 \x01(\x04\x12\x10\n\x08quantity\x18\x03 \x01(\x01\x12\x1a\n\x12\x63onfirmed_quantity\x18\x04 \x01(\x01\x12\x14\n\x0cproduct_code\x18\x05 \x01(\t\x12\x14\n\x0cproduct_name\x18\x06 \x01(\t\x12\x11\n\tunit_name\x18\x07 \x01(\t\x12\x11\n\tunit_spec\x18\x08 \x01(\t\x12\x11\n\tunit_rate\x18\t \x01(\x02\x12\x10\n\x08tax_rate\x18\n \x01(\x02\x12\r\n\x05price\x18\x0b \x01(\x02\x12\x11\n\tprice_tax\x18\x0c \x01(\x02\x12\n\n\x02id\x18\r \x01(\x04\x12\x11\n\treturn_to\x18\x0e \x01(\x04\x12\x16\n\x0elogistics_type\x18\x0f \x01(\t\x12\x15\n\rrefund_amount\x18\x10 \x01(\x01\x12\x34\n\x0b\x61ttachments\x18\x11 \x03(\x0b\x32\x1f.franchisee_returns.Attachments\"/\n\x14\x43onfirmReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"(\n\x15\x43onfirmReturnResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"\x1e\n\x0b\x43ommonReply\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\xc5\x03\n\x1b\x43heckReturnAvailableRequest\x12\x11\n\treturn_by\x18\x01 \x01(\x04\x12\x38\n\x14return_delivery_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x10\n\x08sub_type\x18\x04 \x01(\t\x12\x15\n\rreturn_reason\x18\x05 \x01(\t\x12\x0e\n\x06remark\x18\x06 \x01(\t\x12-\n\x08products\x18\x07 \x03(\x0b\x32\x1b.franchisee_returns.Product\x12\x11\n\tis_direct\x18\x08 \x01(\x08\x12\x11\n\treturn_to\x18\t \x01(\x04\x12\x34\n\x0b\x61ttachments\x18\n \x03(\x0b\x32\x1f.franchisee_returns.Attachments\x12\x16\n\x0elogistics_type\x18\x0b \x01(\t\x12\x11\n\tis_adjust\x18\x0c \x01(\x08\x12\x14\n\x0creceiving_id\x18\r \x01(\x04\x12\x0b\n\x03lan\x18\x0e \x01(\t\x12\x11\n\treturn_id\x18\x0f \x01(\x04\x12\x11\n\tsource_id\x18\x10 \x01(\x04\x12\x13\n\x0bsource_code\x18\x11 \x01(\t\"\xec\x01\n\x16GetValidProductRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12\x14\n\x0cproduct_name\x18\x06 \x01(\t\x12\x0b\n\x03lan\x18\x07 \x01(\t\x12\x10\n\x08store_id\x18\x08 \x01(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\t \x03(\x04\x12\x10\n\x08order_by\x18\n \x01(\t\x12\x0c\n\x04sort\x18\x0b \x01(\t\x12\x0e\n\x06search\x18\x0c \x01(\t\x12\x15\n\rsearch_fields\x18\r \x01(\t\"\x9c\x01\n\x17GetValidProductResponse\x12.\n\x04rows\x18\x01 \x03(\x0b\x32 .franchisee_returns.ValidProduct\x12\r\n\x05total\x18\x02 \x01(\r\x12\x42\n\x18inventory_unchanged_rows\x18\x03 \x03(\x0b\x32 .franchisee_returns.ValidProduct\"\xb2\x04\n\x15ListReturnColdRequest\x12\x11\n\tstore_ids\x18\x01 \x03(\x04\x12\x0e\n\x06status\x18\x02 \x03(\t\x12.\n\nstart_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\r\n\x05limit\x18\x06 \x01(\x05\x12\x0e\n\x06offset\x18\x07 \x01(\x05\x12\x15\n\rinclude_total\x18\x08 \x01(\x08\x12\x11\n\tis_direct\x18\t \x01(\x08\x12\x16\n\x0elogistics_type\x18\x0c \x01(\t\x12\x11\n\tis_adjust\x18\r \x01(\x08\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\r\n\x05order\x18\x0f \x01(\t\x12\x14\n\x0cstorage_type\x18\x11 \x01(\t\x12\x0b\n\x03lan\x18\x12 \x01(\t\x12\x34\n\x10return_date_from\x18\x13 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0ereturn_date_to\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x36\n\x12\x64\x65livery_date_from\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x10\x64\x65livery_date_to\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"R\n\x16ListReturnColdResponse\x12)\n\x04rows\x18\x01 \x03(\x0b\x32\x1b.franchisee_returns.Returns\x12\r\n\x05total\x18\x02 \x01(\x04\"/\n\x14GetReturnByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"K\n\x15GetReturnByIdResponse\x12\x32\n\rreturn_detail\x18\x01 \x01(\x0b\x32\x1b.franchisee_returns.Returns\"\x89\x01\n\x1bGetReturnProductByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\r\n\x05order\x18\x0f \x01(\t\x12\x0b\n\x03lan\x18\x10 \x01(\t\"t\n\x1cGetReturnProductByIdResponse\x12/\n\x04rows\x18\x01 \x03(\x0b\x32!.franchisee_returns.ProductDetail\x12\r\n\x05total\x18\x02 \x01(\x04\x12\x14\n\x0ctotal_amount\x18\x03 \x01(\x01\".\n\x13SubmitReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\'\n\x14SubmitReturnResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"E\n\x13RejectReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x15\n\rreject_reason\x18\x02 \x01(\t\x12\x0b\n\x03lan\x18\x03 \x01(\t\"\'\n\x14RejectReturnResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"p\n\x14\x41pproveReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\ntrans_type\x18\x02 \x01(\t\x12\x0b\n\x03lan\x18\x03 \x01(\t\x12\x16\n\x0ewarehouse_type\x18\x04 \x01(\t\x12\x13\n\x0blong_effect\x18\x05 \x01(\t\"(\n\x15\x41pproveReturnResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"A\n\x15\x44\x65liveryReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\x12\x0f\n\x07\x66s_code\x18\x03 \x01(\t\")\n\x16\x44\x65liveryReturnResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"\x9f\x02\n\x13UpdateReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12-\n\x08products\x18\x02 \x03(\x0b\x32\x1b.franchisee_returns.Product\x12\x15\n\rreturn_reason\x18\x03 \x01(\t\x12\x0e\n\x06remark\x18\x04 \x01(\t\x12\x38\n\x14return_delivery_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\treturn_to\x18\x06 \x01(\x04\x12\x34\n\x0b\x61ttachments\x18\x07 \x03(\x0b\x32\x1f.franchisee_returns.Attachments\x12\x0b\n\x03lan\x18\x08 \x01(\t\x12\x16\n\x0elogistics_type\x18\t \x01(\t\"\'\n\x14UpdateReturnResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\">\n\x13UpdateRemarkRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06remark\x18\x02 \x01(\t\x12\x0b\n\x03lan\x18\x03 \x01(\t\"\'\n\x14UpdateRemarkResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\".\n\x13\x44\x65leteReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\'\n\x14\x44\x65leteReturnResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"\xc5\x01\n\x0cValidProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12\x0c\n\x04spec\x18\x04 \x01(\t\x12&\n\x04unit\x18\x05 \x01(\x0b\x32\x18.franchisee_returns.Unit\x12\x11\n\ttax_price\x18\x06 \x01(\x01\x12\x10\n\x08\x64istr_by\x18\x07 \x01(\x04\x12\x1a\n\x12real_inventory_qty\x18\x08 \x01(\x01\"@\n\x04Unit\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08quantity\x18\x02 \x01(\x05\x12\x0c\n\x04rate\x18\x03 \x01(\x01\x12\x0c\n\x04name\x18\x04 \x01(\t\"\x98\x08\n\rProductDetail\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x11\n\treturn_id\x18\x02 \x01(\x04\x12\x11\n\treturn_by\x18\x03 \x01(\x04\x12\x17\n\x0fmaterial_number\x18\x04 \x01(\t\x12\x14\n\x0cproduct_code\x18\x05 \x01(\t\x12\x12\n\nproduct_id\x18\x06 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x10\n\x08quantity\x18\x08 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\t \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\n \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x0b \x01(\t\x12\x0f\n\x07unit_id\x18\x0c \x01(\x04\x12\x11\n\tunit_name\x18\r \x01(\t\x12\x11\n\tunit_rate\x18\x0e \x01(\x01\x12\x11\n\tunit_spec\x18\x0f \x01(\t\x12%\n\x1d\x61\x63\x63ounting_confirmed_quantity\x18\x10 \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x11 \x01(\x01\x12$\n\x1c\x61\x63\x63ounting_returned_quantity\x18\x12 \x01(\x01\x12\x1a\n\x12\x63onfirmed_quantity\x18\x13 \x01(\x01\x12\x19\n\x11returned_quantity\x18\x14 \x01(\x01\x12\x14\n\x0cis_confirmed\x18\x15 \x01(\x08\x12/\n\x0breturn_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\treturn_to\x18\x17 \x01(\x04\x12\x18\n\x10inventory_status\x18\x18 \x01(\t\x12\x18\n\x10inventory_req_id\x18\x19 \x01(\x04\x12.\n\ncreated_at\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x1b \x01(\x04\x12.\n\nupdated_at\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\x1d \x01(\x04\x12\x12\n\npartner_id\x18\x1e \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1f \x01(\t\x12\x14\n\x0cupdated_name\x18  \x01(\t\x12\x14\n\x0cstorage_type\x18# \x01(\t\x12\r\n\x05price\x18$ \x01(\x01\x12\x10\n\x08tax_rate\x18% \x01(\x02\x12\x11\n\tprice_tax\x18& \x01(\x01\x12\x11\n\tsum_price\x18\' \x01(\x01\x12\x12\n\ntax_amount\x18( \x01(\x01\x12\x14\n\x0creturn_price\x18) \x01(\x01\x12\x34\n\x0b\x61ttachments\x18* \x03(\x0b\x32\x1f.franchisee_returns.Attachments\x12\x14\n\x0cretail_price\x18+ \x01(\x01\"0\n\x1a\x43reateAdjustReturnResponse\x12\x12\n\nreturn_ids\x18\x01 \x03(\x04\"#\n\x15GetHistoryByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\"K\n\x0fHistoryResponse\x12)\n\x04rows\x18\x01 \x03(\x0b\x32\x1b.franchisee_returns.History\x12\r\n\x05total\x18\x02 \x01(\x04\"\x82\x01\n\x07History\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x12\n\nupdated_by\x18\x03 \x01(\x04\x12.\n\nupdated_at\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x17\n\x0fupdated_by_name\x18\x05 \x01(\t\"(\n\x0b\x41ttachments\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\"a\n\"GetReturnCategoryByBranchIdRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x15\n\ris_franchisee\x18\x02 \x01(\x08\x12\x12\n\ndistr_type\x18\x03 \x01(\t\"\xd6\x01\n#GetReturnCategoryByBranchIdResponse\x12\\\n\x0e\x63\x61tegory_items\x18\x01 \x03(\x0b\x32\x44.franchisee_returns.GetReturnCategoryByBranchIdResponse.CategoryItem\x1aQ\n\x0c\x43\x61tegoryItem\x12\x13\n\x0b\x63\x61tegory_id\x18\x01 \x01(\x04\x12\x15\n\rcategory_name\x18\x02 \x01(\t\x12\x15\n\rproduct_count\x18\x03 \x01(\x04\x32\xe2\x17\n\x17\x46ranchiseeReturnService\x12\x96\x01\n\x0c\x43reateReturn\x12\'.franchisee_returns.CreateReturnRequest\x1a(.franchisee_returns.CreateReturnResponse\"3\x82\xd3\xe4\x93\x02-\"(/api/v2/supply/mobile/franchisee/returns:\x01*\x12\xa6\x01\n\rConfirmReturn\x12(.franchisee_returns.ConfirmReturnRequest\x1a).franchisee_returns.ConfirmReturnResponse\"@\x82\xd3\xe4\x93\x02:\"5/api/v2/supply/mobile/franchisee/returns/{id}/confirm:\x01*\x12\x8d\x01\n\nListReturn\x12%.franchisee_returns.ListReturnRequest\x1a&.franchisee_returns.ListReturnResponse\"0\x82\xd3\xe4\x93\x02*\x12(/api/v2/supply/mobile/franchisee/returns\x12\xa3\x01\n\x0fGetValidProduct\x12*.franchisee_returns.GetValidProductRequest\x1a+.franchisee_returns.GetValidProductResponse\"7\x82\xd3\xe4\x93\x02\x31\x12//api/v2/supply/mobile/franchisee/return/product\x12\x9a\x01\n\x0eListReturnCold\x12).franchisee_returns.ListReturnColdRequest\x1a&.franchisee_returns.ListReturnResponse\"5\x82\xd3\xe4\x93\x02/\x12-/api/v2/supply/mobile/franchisee/returns/cold\x12\x8d\x01\n\rGetReturnById\x12(.franchisee_returns.GetReturnByIdRequest\x1a\x1b.franchisee_returns.Returns\"5\x82\xd3\xe4\x93\x02/\x12-/api/v2/supply/mobile/franchisee/returns/{id}\x12\xb8\x01\n\x14GetReturnProductById\x12/.franchisee_returns.GetReturnProductByIdRequest\x1a\x30.franchisee_returns.GetReturnProductByIdResponse\"=\x82\xd3\xe4\x93\x02\x37\x12\x35/api/v2/supply/mobile/franchisee/returns/{id}/product\x12\xa2\x01\n\x0cSubmitReturn\x12\'.franchisee_returns.SubmitReturnRequest\x1a(.franchisee_returns.SubmitReturnResponse\"?\x82\xd3\xe4\x93\x02\x39\"4/api/v2/supply/mobile/franchisee/returns/{id}/submit:\x01*\x12\xa2\x01\n\x0cRejectReturn\x12\'.franchisee_returns.RejectReturnRequest\x1a(.franchisee_returns.RejectReturnResponse\"?\x82\xd3\xe4\x93\x02\x39\"4/api/v2/supply/mobile/franchisee/returns/{id}/reject:\x01*\x12\xa6\x01\n\rApproveReturn\x12(.franchisee_returns.ApproveReturnRequest\x1a).franchisee_returns.ApproveReturnResponse\"@\x82\xd3\xe4\x93\x02:\"5/api/v2/supply/mobile/franchisee/returns/{id}/approve:\x01*\x12\xaa\x01\n\x0e\x44\x65liveryReturn\x12).franchisee_returns.DeliveryReturnRequest\x1a*.franchisee_returns.DeliveryReturnResponse\"A\x82\xd3\xe4\x93\x02;\"6/api/v2/supply/mobile/franchisee/returns/{id}/delivery:\x01*\x12\xa2\x01\n\x0cUpdateReturn\x12\'.franchisee_returns.UpdateReturnRequest\x1a(.franchisee_returns.UpdateReturnResponse\"?\x82\xd3\xe4\x93\x02\x39\"4/api/v2/supply/mobile/franchisee/returns/{id}/update:\x01*\x12\xa9\x01\n\x0cUpdateRemark\x12\'.franchisee_returns.UpdateRemarkRequest\x1a(.franchisee_returns.UpdateRemarkResponse\"F\x82\xd3\xe4\x93\x02@\";/api/v2/supply/mobile/franchisee/returns/{id}/update/remark:\x01*\x12\xa2\x01\n\x0c\x44\x65leteReturn\x12\'.franchisee_returns.DeleteReturnRequest\x1a(.franchisee_returns.DeleteReturnResponse\"?\x82\xd3\xe4\x93\x02\x39\"4/api/v2/supply/mobile/franchisee/returns/{id}/delete:\x01*\x12\xa9\x01\n\x12\x43reateAdjustReturn\x12\'.franchisee_returns.CreateReturnRequest\x1a..franchisee_returns.CreateAdjustReturnResponse\":\x82\xd3\xe4\x93\x02\x34\"//api/v2/supply/mobile/franchisee/adjust_returns:\x01*\x12\xa7\x01\n\x19\x43heckReturnAvailableByrec\x12/.franchisee_returns.CheckReturnAvailableRequest\x1a\x1f.franchisee_returns.CommonReply\"8\x82\xd3\xe4\x93\x02\x32\"-/api/v2/supply/mobile/franchisee/return/check:\x01*\x12\x9e\x01\n\x0eGetHistoryById\x12).franchisee_returns.GetHistoryByIdRequest\x1a#.franchisee_returns.HistoryResponse\"<\x82\xd3\xe4\x93\x02\x36\x12\x34/api/v2/supply/mobile/franchisee/return/{id}/history\x12\xd9\x01\n\x1bGetReturnCategoryByBranchId\x12\x36.franchisee_returns.GetReturnCategoryByBranchIdRequest\x1a\x37.franchisee_returns.GetReturnCategoryByBranchIdResponse\"I\x82\xd3\xe4\x93\x02\x43\x12\x41/api/v2/supply/mobile/franchisee/return/store/{store_id}/categoryB\x16Z\x14./franchisee_returnsb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_LISTRETURNREQUEST = _descriptor.Descriptor(
  name='ListReturnRequest',
  full_name='franchisee_returns.ListReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='franchisee_returns.ListReturnRequest.store_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_returns.ListReturnRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='franchisee_returns.ListReturnRequest.start_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='franchisee_returns.ListReturnRequest.end_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='franchisee_returns.ListReturnRequest.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_returns.ListReturnRequest.limit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_returns.ListReturnRequest.offset', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='franchisee_returns.ListReturnRequest.include_total', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='franchisee_returns.ListReturnRequest.is_direct', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by', full_name='franchisee_returns.ListReturnRequest.order_by', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='franchisee_returns.ListReturnRequest.logistics_type', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='franchisee_returns.ListReturnRequest.is_adjust', index=11,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='franchisee_returns.ListReturnRequest.sort', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='franchisee_returns.ListReturnRequest.order', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='franchisee_returns.ListReturnRequest.storage_type', index=14,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='franchisee_returns.ListReturnRequest.type', index=15,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='franchisee_returns.ListReturnRequest.sub_type', index=16,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_code', full_name='franchisee_returns.ListReturnRequest.receive_code', index=17,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_returns.ListReturnRequest.lan', index=18,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_from', full_name='franchisee_returns.ListReturnRequest.return_date_from', index=19,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_to', full_name='franchisee_returns.ListReturnRequest.return_date_to', index=20,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_from', full_name='franchisee_returns.ListReturnRequest.delivery_date_from', index=21,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_to', full_name='franchisee_returns.ListReturnRequest.delivery_date_to', index=22,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_number', full_name='franchisee_returns.ListReturnRequest.return_number', index=23,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='franchisee_returns.ListReturnRequest.product_ids', index=24,
      number=27, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_by', full_name='franchisee_returns.ListReturnRequest.return_by', index=25,
      number=28, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='franchisee_returns.ListReturnRequest.return_to', index=26,
      number=29, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_id', full_name='franchisee_returns.ListReturnRequest.source_id', index=27,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_code', full_name='franchisee_returns.ListReturnRequest.source_code', index=28,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_ways', full_name='franchisee_returns.ListReturnRequest.payment_ways', index=29,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_code', full_name='franchisee_returns.ListReturnRequest.delivery_code', index=30,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='franchisee_returns.ListReturnRequest.ids', index=31,
      number=34, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=125,
  serialized_end=935,
)


_LISTRETURNRESPONSE = _descriptor.Descriptor(
  name='ListReturnResponse',
  full_name='franchisee_returns.ListReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_returns.ListReturnResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_returns.ListReturnResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=937,
  serialized_end=1015,
)


_RETURNS = _descriptor.Descriptor(
  name='Returns',
  full_name='franchisee_returns.Returns',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_returns.Returns.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='franchisee_returns.Returns.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_by', full_name='franchisee_returns.Returns.return_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_number', full_name='franchisee_returns.Returns.return_number', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_returns.Returns.status', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='franchisee_returns.Returns.review_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_returned', full_name='franchisee_returns.Returns.is_returned', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_number', full_name='franchisee_returns.Returns.return_delivery_number', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date', full_name='franchisee_returns.Returns.return_date', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_date', full_name='franchisee_returns.Returns.return_delivery_date', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_reason', full_name='franchisee_returns.Returns.return_reason', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='franchisee_returns.Returns.type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='franchisee_returns.Returns.sub_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_returns.Returns.remark', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='franchisee_returns.Returns.store_secondary_id', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='franchisee_returns.Returns.created_at', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='franchisee_returns.Returns.updated_at', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_returns.Returns.created_by', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='franchisee_returns.Returns.updated_by', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='franchisee_returns.Returns.return_to', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_status', full_name='franchisee_returns.Returns.inventory_status', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_req_id', full_name='franchisee_returns.Returns.inventory_req_id', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='franchisee_returns.Returns.partner_id', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='franchisee_returns.Returns.is_direct', index=23,
      number=24, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='franchisee_returns.Returns.reject_reason', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_returns.Returns.attachments', index=25,
      number=26, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='franchisee_returns.Returns.created_name', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='franchisee_returns.Returns.updated_name', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_nums', full_name='franchisee_returns.Returns.product_nums', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='franchisee_returns.Returns.logistics_type', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='franchisee_returns.Returns.is_adjust', index=30,
      number=32, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='send_type', full_name='franchisee_returns.Returns.send_type', index=31,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_type', full_name='franchisee_returns.Returns.trans_type', index=32,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='franchisee_returns.Returns.storage_type', index=33,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_id', full_name='franchisee_returns.Returns.receiving_id', index=34,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_code', full_name='franchisee_returns.Returns.receiving_code', index=35,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='franchisee_returns.Returns.delivery_date', index=36,
      number=39, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='franchisee_returns.Returns.request_id', index=37,
      number=40, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_amount', full_name='franchisee_returns.Returns.refund_amount', index=38,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='franchisee_returns.Returns.franchisee_id', index=39,
      number=55, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_code', full_name='franchisee_returns.Returns.franchisee_code', index=40,
      number=56, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_name', full_name='franchisee_returns.Returns.franchisee_name', index=41,
      number=57, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_way', full_name='franchisee_returns.Returns.payment_way', index=42,
      number=58, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_code', full_name='franchisee_returns.Returns.delivery_code', index=43,
      number=59, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_code', full_name='franchisee_returns.Returns.receive_code', index=44,
      number=60, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_id', full_name='franchisee_returns.Returns.source_id', index=45,
      number=61, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_code', full_name='franchisee_returns.Returns.source_code', index=46,
      number=62, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to_code', full_name='franchisee_returns.Returns.return_to_code', index=47,
      number=63, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to_name', full_name='franchisee_returns.Returns.return_to_name', index=48,
      number=64, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='returns_price', full_name='franchisee_returns.Returns.returns_price', index=49,
      number=65, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='franchisee_returns.Returns.product_code', index=50,
      number=66, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='franchisee_returns.Returns.product_name', index=51,
      number=67, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='franchisee_returns.Returns.unit_spec', index=52,
      number=68, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_tax', full_name='franchisee_returns.Returns.price_tax', index=53,
      number=69, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='franchisee_returns.Returns.quantity', index=54,
      number=70, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_by_code', full_name='franchisee_returns.Returns.return_by_code', index=55,
      number=71, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_by_name', full_name='franchisee_returns.Returns.return_by_name', index=56,
      number=72, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_id', full_name='franchisee_returns.Returns.refund_id', index=57,
      number=50, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_code', full_name='franchisee_returns.Returns.refund_code', index=58,
      number=51, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='warehouse_type', full_name='franchisee_returns.Returns.warehouse_type', index=59,
      number=52, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='long_effect', full_name='franchisee_returns.Returns.long_effect', index=60,
      number=53, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1018,
  serialized_end=2505,
)


_CREATERETURNREQUEST = _descriptor.Descriptor(
  name='CreateReturnRequest',
  full_name='franchisee_returns.CreateReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='return_by', full_name='franchisee_returns.CreateReturnRequest.return_by', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='franchisee_returns.CreateReturnRequest.logistics_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='franchisee_returns.CreateReturnRequest.type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='franchisee_returns.CreateReturnRequest.sub_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_date', full_name='franchisee_returns.CreateReturnRequest.return_delivery_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_id', full_name='franchisee_returns.CreateReturnRequest.source_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_code', full_name='franchisee_returns.CreateReturnRequest.source_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_reason', full_name='franchisee_returns.CreateReturnRequest.return_reason', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_returns.CreateReturnRequest.remark', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_returns.CreateReturnRequest.attachments', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='franchisee_returns.CreateReturnRequest.request_id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='franchisee_returns.CreateReturnRequest.products', index=11,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_returns.CreateReturnRequest.lan', index=12,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2508,
  serialized_end=2875,
)


_CREATERETURNRESPONSE = _descriptor.Descriptor(
  name='CreateReturnResponse',
  full_name='franchisee_returns.CreateReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='return_id', full_name='franchisee_returns.CreateReturnResponse.return_id', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_returns.CreateReturnResponse.payload', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2877,
  serialized_end=2935,
)


_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='franchisee_returns.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='franchisee_returns.Product.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='franchisee_returns.Product.unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='franchisee_returns.Product.quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='franchisee_returns.Product.confirmed_quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='franchisee_returns.Product.product_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='franchisee_returns.Product.product_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='franchisee_returns.Product.unit_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='franchisee_returns.Product.unit_spec', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='franchisee_returns.Product.unit_rate', index=8,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='franchisee_returns.Product.tax_rate', index=9,
      number=10, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='franchisee_returns.Product.price', index=10,
      number=11, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_tax', full_name='franchisee_returns.Product.price_tax', index=11,
      number=12, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_returns.Product.id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='franchisee_returns.Product.return_to', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='franchisee_returns.Product.logistics_type', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_amount', full_name='franchisee_returns.Product.refund_amount', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_returns.Product.attachments', index=16,
      number=17, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2938,
  serialized_end=3315,
)


_CONFIRMRETURNREQUEST = _descriptor.Descriptor(
  name='ConfirmReturnRequest',
  full_name='franchisee_returns.ConfirmReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_returns.ConfirmReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_returns.ConfirmReturnRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3317,
  serialized_end=3364,
)


_CONFIRMRETURNRESPONSE = _descriptor.Descriptor(
  name='ConfirmReturnResponse',
  full_name='franchisee_returns.ConfirmReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_returns.ConfirmReturnResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3366,
  serialized_end=3406,
)


_COMMONREPLY = _descriptor.Descriptor(
  name='CommonReply',
  full_name='franchisee_returns.CommonReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='franchisee_returns.CommonReply.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3408,
  serialized_end=3438,
)


_CHECKRETURNAVAILABLEREQUEST = _descriptor.Descriptor(
  name='CheckReturnAvailableRequest',
  full_name='franchisee_returns.CheckReturnAvailableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='return_by', full_name='franchisee_returns.CheckReturnAvailableRequest.return_by', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_date', full_name='franchisee_returns.CheckReturnAvailableRequest.return_delivery_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='franchisee_returns.CheckReturnAvailableRequest.type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='franchisee_returns.CheckReturnAvailableRequest.sub_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_reason', full_name='franchisee_returns.CheckReturnAvailableRequest.return_reason', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_returns.CheckReturnAvailableRequest.remark', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='franchisee_returns.CheckReturnAvailableRequest.products', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='franchisee_returns.CheckReturnAvailableRequest.is_direct', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='franchisee_returns.CheckReturnAvailableRequest.return_to', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_returns.CheckReturnAvailableRequest.attachments', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='franchisee_returns.CheckReturnAvailableRequest.logistics_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='franchisee_returns.CheckReturnAvailableRequest.is_adjust', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_id', full_name='franchisee_returns.CheckReturnAvailableRequest.receiving_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_returns.CheckReturnAvailableRequest.lan', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_id', full_name='franchisee_returns.CheckReturnAvailableRequest.return_id', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_id', full_name='franchisee_returns.CheckReturnAvailableRequest.source_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_code', full_name='franchisee_returns.CheckReturnAvailableRequest.source_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3441,
  serialized_end=3894,
)


_GETVALIDPRODUCTREQUEST = _descriptor.Descriptor(
  name='GetValidProductRequest',
  full_name='franchisee_returns.GetValidProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_returns.GetValidProductRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_returns.GetValidProductRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_returns.GetValidProductRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='franchisee_returns.GetValidProductRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='franchisee_returns.GetValidProductRequest.product_name', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_returns.GetValidProductRequest.lan', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='franchisee_returns.GetValidProductRequest.store_id', index=6,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='franchisee_returns.GetValidProductRequest.category_ids', index=7,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by', full_name='franchisee_returns.GetValidProductRequest.order_by', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='franchisee_returns.GetValidProductRequest.sort', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='franchisee_returns.GetValidProductRequest.search', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='franchisee_returns.GetValidProductRequest.search_fields', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3897,
  serialized_end=4133,
)


_GETVALIDPRODUCTRESPONSE = _descriptor.Descriptor(
  name='GetValidProductResponse',
  full_name='franchisee_returns.GetValidProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_returns.GetValidProductResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_returns.GetValidProductResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_unchanged_rows', full_name='franchisee_returns.GetValidProductResponse.inventory_unchanged_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4136,
  serialized_end=4292,
)


_LISTRETURNCOLDREQUEST = _descriptor.Descriptor(
  name='ListReturnColdRequest',
  full_name='franchisee_returns.ListReturnColdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='franchisee_returns.ListReturnColdRequest.store_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_returns.ListReturnColdRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='franchisee_returns.ListReturnColdRequest.start_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='franchisee_returns.ListReturnColdRequest.end_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='franchisee_returns.ListReturnColdRequest.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_returns.ListReturnColdRequest.limit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_returns.ListReturnColdRequest.offset', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='franchisee_returns.ListReturnColdRequest.include_total', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='franchisee_returns.ListReturnColdRequest.is_direct', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='franchisee_returns.ListReturnColdRequest.logistics_type', index=9,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='franchisee_returns.ListReturnColdRequest.is_adjust', index=10,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='franchisee_returns.ListReturnColdRequest.sort', index=11,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='franchisee_returns.ListReturnColdRequest.order', index=12,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='franchisee_returns.ListReturnColdRequest.storage_type', index=13,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_returns.ListReturnColdRequest.lan', index=14,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_from', full_name='franchisee_returns.ListReturnColdRequest.return_date_from', index=15,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_to', full_name='franchisee_returns.ListReturnColdRequest.return_date_to', index=16,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_from', full_name='franchisee_returns.ListReturnColdRequest.delivery_date_from', index=17,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_to', full_name='franchisee_returns.ListReturnColdRequest.delivery_date_to', index=18,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4295,
  serialized_end=4857,
)


_LISTRETURNCOLDRESPONSE = _descriptor.Descriptor(
  name='ListReturnColdResponse',
  full_name='franchisee_returns.ListReturnColdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_returns.ListReturnColdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_returns.ListReturnColdResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4859,
  serialized_end=4941,
)


_GETRETURNBYIDREQUEST = _descriptor.Descriptor(
  name='GetReturnByIdRequest',
  full_name='franchisee_returns.GetReturnByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_returns.GetReturnByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_returns.GetReturnByIdRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4943,
  serialized_end=4990,
)


_GETRETURNBYIDRESPONSE = _descriptor.Descriptor(
  name='GetReturnByIdResponse',
  full_name='franchisee_returns.GetReturnByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='return_detail', full_name='franchisee_returns.GetReturnByIdResponse.return_detail', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4992,
  serialized_end=5067,
)


_GETRETURNPRODUCTBYIDREQUEST = _descriptor.Descriptor(
  name='GetReturnProductByIdRequest',
  full_name='franchisee_returns.GetReturnProductByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_returns.GetReturnProductByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_returns.GetReturnProductByIdRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_returns.GetReturnProductByIdRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='franchisee_returns.GetReturnProductByIdRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='franchisee_returns.GetReturnProductByIdRequest.sort', index=4,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='franchisee_returns.GetReturnProductByIdRequest.order', index=5,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_returns.GetReturnProductByIdRequest.lan', index=6,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5070,
  serialized_end=5207,
)


_GETRETURNPRODUCTBYIDRESPONSE = _descriptor.Descriptor(
  name='GetReturnProductByIdResponse',
  full_name='franchisee_returns.GetReturnProductByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_returns.GetReturnProductByIdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_returns.GetReturnProductByIdResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='franchisee_returns.GetReturnProductByIdResponse.total_amount', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5209,
  serialized_end=5325,
)


_SUBMITRETURNREQUEST = _descriptor.Descriptor(
  name='SubmitReturnRequest',
  full_name='franchisee_returns.SubmitReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_returns.SubmitReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_returns.SubmitReturnRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5327,
  serialized_end=5373,
)


_SUBMITRETURNRESPONSE = _descriptor.Descriptor(
  name='SubmitReturnResponse',
  full_name='franchisee_returns.SubmitReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_returns.SubmitReturnResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5375,
  serialized_end=5414,
)


_REJECTRETURNREQUEST = _descriptor.Descriptor(
  name='RejectReturnRequest',
  full_name='franchisee_returns.RejectReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_returns.RejectReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='franchisee_returns.RejectReturnRequest.reject_reason', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_returns.RejectReturnRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5416,
  serialized_end=5485,
)


_REJECTRETURNRESPONSE = _descriptor.Descriptor(
  name='RejectReturnResponse',
  full_name='franchisee_returns.RejectReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_returns.RejectReturnResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5487,
  serialized_end=5526,
)


_APPROVERETURNREQUEST = _descriptor.Descriptor(
  name='ApproveReturnRequest',
  full_name='franchisee_returns.ApproveReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_returns.ApproveReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_type', full_name='franchisee_returns.ApproveReturnRequest.trans_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_returns.ApproveReturnRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='warehouse_type', full_name='franchisee_returns.ApproveReturnRequest.warehouse_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='long_effect', full_name='franchisee_returns.ApproveReturnRequest.long_effect', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5528,
  serialized_end=5640,
)


_APPROVERETURNRESPONSE = _descriptor.Descriptor(
  name='ApproveReturnResponse',
  full_name='franchisee_returns.ApproveReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_returns.ApproveReturnResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5642,
  serialized_end=5682,
)


_DELIVERYRETURNREQUEST = _descriptor.Descriptor(
  name='DeliveryReturnRequest',
  full_name='franchisee_returns.DeliveryReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_returns.DeliveryReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_returns.DeliveryReturnRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fs_code', full_name='franchisee_returns.DeliveryReturnRequest.fs_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5684,
  serialized_end=5749,
)


_DELIVERYRETURNRESPONSE = _descriptor.Descriptor(
  name='DeliveryReturnResponse',
  full_name='franchisee_returns.DeliveryReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_returns.DeliveryReturnResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5751,
  serialized_end=5792,
)


_UPDATERETURNREQUEST = _descriptor.Descriptor(
  name='UpdateReturnRequest',
  full_name='franchisee_returns.UpdateReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_returns.UpdateReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='franchisee_returns.UpdateReturnRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_reason', full_name='franchisee_returns.UpdateReturnRequest.return_reason', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_returns.UpdateReturnRequest.remark', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_date', full_name='franchisee_returns.UpdateReturnRequest.return_delivery_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='franchisee_returns.UpdateReturnRequest.return_to', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_returns.UpdateReturnRequest.attachments', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_returns.UpdateReturnRequest.lan', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='franchisee_returns.UpdateReturnRequest.logistics_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5795,
  serialized_end=6082,
)


_UPDATERETURNRESPONSE = _descriptor.Descriptor(
  name='UpdateReturnResponse',
  full_name='franchisee_returns.UpdateReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_returns.UpdateReturnResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6084,
  serialized_end=6123,
)


_UPDATEREMARKREQUEST = _descriptor.Descriptor(
  name='UpdateRemarkRequest',
  full_name='franchisee_returns.UpdateRemarkRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_returns.UpdateRemarkRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_returns.UpdateRemarkRequest.remark', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_returns.UpdateRemarkRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6125,
  serialized_end=6187,
)


_UPDATEREMARKRESPONSE = _descriptor.Descriptor(
  name='UpdateRemarkResponse',
  full_name='franchisee_returns.UpdateRemarkResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_returns.UpdateRemarkResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6189,
  serialized_end=6228,
)


_DELETERETURNREQUEST = _descriptor.Descriptor(
  name='DeleteReturnRequest',
  full_name='franchisee_returns.DeleteReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_returns.DeleteReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_returns.DeleteReturnRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6230,
  serialized_end=6276,
)


_DELETERETURNRESPONSE = _descriptor.Descriptor(
  name='DeleteReturnResponse',
  full_name='franchisee_returns.DeleteReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_returns.DeleteReturnResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6278,
  serialized_end=6317,
)


_VALIDPRODUCT = _descriptor.Descriptor(
  name='ValidProduct',
  full_name='franchisee_returns.ValidProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='franchisee_returns.ValidProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='franchisee_returns.ValidProduct.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='franchisee_returns.ValidProduct.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='franchisee_returns.ValidProduct.spec', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit', full_name='franchisee_returns.ValidProduct.unit', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='franchisee_returns.ValidProduct.tax_price', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_by', full_name='franchisee_returns.ValidProduct.distr_by', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='real_inventory_qty', full_name='franchisee_returns.ValidProduct.real_inventory_qty', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6320,
  serialized_end=6517,
)


_UNIT = _descriptor.Descriptor(
  name='Unit',
  full_name='franchisee_returns.Unit',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_returns.Unit.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='franchisee_returns.Unit.quantity', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='franchisee_returns.Unit.rate', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='franchisee_returns.Unit.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6519,
  serialized_end=6583,
)


_PRODUCTDETAIL = _descriptor.Descriptor(
  name='ProductDetail',
  full_name='franchisee_returns.ProductDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_returns.ProductDetail.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_id', full_name='franchisee_returns.ProductDetail.return_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_by', full_name='franchisee_returns.ProductDetail.return_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='franchisee_returns.ProductDetail.material_number', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='franchisee_returns.ProductDetail.product_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='franchisee_returns.ProductDetail.product_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='franchisee_returns.ProductDetail.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='franchisee_returns.ProductDetail.quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='franchisee_returns.ProductDetail.accounting_unit_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='franchisee_returns.ProductDetail.accounting_unit_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='franchisee_returns.ProductDetail.accounting_unit_spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='franchisee_returns.ProductDetail.unit_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='franchisee_returns.ProductDetail.unit_name', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='franchisee_returns.ProductDetail.unit_rate', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='franchisee_returns.ProductDetail.unit_spec', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_confirmed_quantity', full_name='franchisee_returns.ProductDetail.accounting_confirmed_quantity', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='franchisee_returns.ProductDetail.accounting_quantity', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_returned_quantity', full_name='franchisee_returns.ProductDetail.accounting_returned_quantity', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='franchisee_returns.ProductDetail.confirmed_quantity', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='returned_quantity', full_name='franchisee_returns.ProductDetail.returned_quantity', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_confirmed', full_name='franchisee_returns.ProductDetail.is_confirmed', index=20,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date', full_name='franchisee_returns.ProductDetail.return_date', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='franchisee_returns.ProductDetail.return_to', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_status', full_name='franchisee_returns.ProductDetail.inventory_status', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_req_id', full_name='franchisee_returns.ProductDetail.inventory_req_id', index=24,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='franchisee_returns.ProductDetail.created_at', index=25,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_returns.ProductDetail.created_by', index=26,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='franchisee_returns.ProductDetail.updated_at', index=27,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='franchisee_returns.ProductDetail.updated_by', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='franchisee_returns.ProductDetail.partner_id', index=29,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='franchisee_returns.ProductDetail.created_name', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='franchisee_returns.ProductDetail.updated_name', index=31,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='franchisee_returns.ProductDetail.storage_type', index=32,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='franchisee_returns.ProductDetail.price', index=33,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='franchisee_returns.ProductDetail.tax_rate', index=34,
      number=37, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_tax', full_name='franchisee_returns.ProductDetail.price_tax', index=35,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price', full_name='franchisee_returns.ProductDetail.sum_price', index=36,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_amount', full_name='franchisee_returns.ProductDetail.tax_amount', index=37,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_price', full_name='franchisee_returns.ProductDetail.return_price', index=38,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_returns.ProductDetail.attachments', index=39,
      number=42, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='retail_price', full_name='franchisee_returns.ProductDetail.retail_price', index=40,
      number=43, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6586,
  serialized_end=7634,
)


_CREATEADJUSTRETURNRESPONSE = _descriptor.Descriptor(
  name='CreateAdjustReturnResponse',
  full_name='franchisee_returns.CreateAdjustReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='return_ids', full_name='franchisee_returns.CreateAdjustReturnResponse.return_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7636,
  serialized_end=7684,
)


_GETHISTORYBYIDREQUEST = _descriptor.Descriptor(
  name='GetHistoryByIdRequest',
  full_name='franchisee_returns.GetHistoryByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_returns.GetHistoryByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7686,
  serialized_end=7721,
)


_HISTORYRESPONSE = _descriptor.Descriptor(
  name='HistoryResponse',
  full_name='franchisee_returns.HistoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_returns.HistoryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_returns.HistoryResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7723,
  serialized_end=7798,
)


_HISTORY = _descriptor.Descriptor(
  name='History',
  full_name='franchisee_returns.History',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_returns.History.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_returns.History.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='franchisee_returns.History.updated_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='franchisee_returns.History.updated_at', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by_name', full_name='franchisee_returns.History.updated_by_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7801,
  serialized_end=7931,
)


_ATTACHMENTS = _descriptor.Descriptor(
  name='Attachments',
  full_name='franchisee_returns.Attachments',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='franchisee_returns.Attachments.type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='franchisee_returns.Attachments.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7933,
  serialized_end=7973,
)


_GETRETURNCATEGORYBYBRANCHIDREQUEST = _descriptor.Descriptor(
  name='GetReturnCategoryByBranchIdRequest',
  full_name='franchisee_returns.GetReturnCategoryByBranchIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='franchisee_returns.GetReturnCategoryByBranchIdRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_franchisee', full_name='franchisee_returns.GetReturnCategoryByBranchIdRequest.is_franchisee', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='franchisee_returns.GetReturnCategoryByBranchIdRequest.distr_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7975,
  serialized_end=8072,
)


_GETRETURNCATEGORYBYBRANCHIDRESPONSE_CATEGORYITEM = _descriptor.Descriptor(
  name='CategoryItem',
  full_name='franchisee_returns.GetReturnCategoryByBranchIdResponse.CategoryItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='category_id', full_name='franchisee_returns.GetReturnCategoryByBranchIdResponse.CategoryItem.category_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='franchisee_returns.GetReturnCategoryByBranchIdResponse.CategoryItem.category_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_count', full_name='franchisee_returns.GetReturnCategoryByBranchIdResponse.CategoryItem.product_count', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8208,
  serialized_end=8289,
)

_GETRETURNCATEGORYBYBRANCHIDRESPONSE = _descriptor.Descriptor(
  name='GetReturnCategoryByBranchIdResponse',
  full_name='franchisee_returns.GetReturnCategoryByBranchIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='category_items', full_name='franchisee_returns.GetReturnCategoryByBranchIdResponse.category_items', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_GETRETURNCATEGORYBYBRANCHIDRESPONSE_CATEGORYITEM, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8075,
  serialized_end=8289,
)

_LISTRETURNREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNREQUEST.fields_by_name['return_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNREQUEST.fields_by_name['return_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNREQUEST.fields_by_name['delivery_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNREQUEST.fields_by_name['delivery_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNRESPONSE.fields_by_name['rows'].message_type = _RETURNS
_RETURNS.fields_by_name['return_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RETURNS.fields_by_name['return_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RETURNS.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RETURNS.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RETURNS.fields_by_name['attachments'].message_type = _ATTACHMENTS
_RETURNS.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATERETURNREQUEST.fields_by_name['return_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATERETURNREQUEST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_CREATERETURNREQUEST.fields_by_name['products'].message_type = _PRODUCT
_PRODUCT.fields_by_name['attachments'].message_type = _ATTACHMENTS
_CHECKRETURNAVAILABLEREQUEST.fields_by_name['return_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CHECKRETURNAVAILABLEREQUEST.fields_by_name['products'].message_type = _PRODUCT
_CHECKRETURNAVAILABLEREQUEST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_GETVALIDPRODUCTRESPONSE.fields_by_name['rows'].message_type = _VALIDPRODUCT
_GETVALIDPRODUCTRESPONSE.fields_by_name['inventory_unchanged_rows'].message_type = _VALIDPRODUCT
_LISTRETURNCOLDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNCOLDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNCOLDREQUEST.fields_by_name['return_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNCOLDREQUEST.fields_by_name['return_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNCOLDREQUEST.fields_by_name['delivery_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNCOLDREQUEST.fields_by_name['delivery_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNCOLDRESPONSE.fields_by_name['rows'].message_type = _RETURNS
_GETRETURNBYIDRESPONSE.fields_by_name['return_detail'].message_type = _RETURNS
_GETRETURNPRODUCTBYIDRESPONSE.fields_by_name['rows'].message_type = _PRODUCTDETAIL
_UPDATERETURNREQUEST.fields_by_name['products'].message_type = _PRODUCT
_UPDATERETURNREQUEST.fields_by_name['return_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATERETURNREQUEST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_VALIDPRODUCT.fields_by_name['unit'].message_type = _UNIT
_PRODUCTDETAIL.fields_by_name['return_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCTDETAIL.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCTDETAIL.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCTDETAIL.fields_by_name['attachments'].message_type = _ATTACHMENTS
_HISTORYRESPONSE.fields_by_name['rows'].message_type = _HISTORY
_HISTORY.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRETURNCATEGORYBYBRANCHIDRESPONSE_CATEGORYITEM.containing_type = _GETRETURNCATEGORYBYBRANCHIDRESPONSE
_GETRETURNCATEGORYBYBRANCHIDRESPONSE.fields_by_name['category_items'].message_type = _GETRETURNCATEGORYBYBRANCHIDRESPONSE_CATEGORYITEM
DESCRIPTOR.message_types_by_name['ListReturnRequest'] = _LISTRETURNREQUEST
DESCRIPTOR.message_types_by_name['ListReturnResponse'] = _LISTRETURNRESPONSE
DESCRIPTOR.message_types_by_name['Returns'] = _RETURNS
DESCRIPTOR.message_types_by_name['CreateReturnRequest'] = _CREATERETURNREQUEST
DESCRIPTOR.message_types_by_name['CreateReturnResponse'] = _CREATERETURNRESPONSE
DESCRIPTOR.message_types_by_name['Product'] = _PRODUCT
DESCRIPTOR.message_types_by_name['ConfirmReturnRequest'] = _CONFIRMRETURNREQUEST
DESCRIPTOR.message_types_by_name['ConfirmReturnResponse'] = _CONFIRMRETURNRESPONSE
DESCRIPTOR.message_types_by_name['CommonReply'] = _COMMONREPLY
DESCRIPTOR.message_types_by_name['CheckReturnAvailableRequest'] = _CHECKRETURNAVAILABLEREQUEST
DESCRIPTOR.message_types_by_name['GetValidProductRequest'] = _GETVALIDPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['GetValidProductResponse'] = _GETVALIDPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['ListReturnColdRequest'] = _LISTRETURNCOLDREQUEST
DESCRIPTOR.message_types_by_name['ListReturnColdResponse'] = _LISTRETURNCOLDRESPONSE
DESCRIPTOR.message_types_by_name['GetReturnByIdRequest'] = _GETRETURNBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetReturnByIdResponse'] = _GETRETURNBYIDRESPONSE
DESCRIPTOR.message_types_by_name['GetReturnProductByIdRequest'] = _GETRETURNPRODUCTBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetReturnProductByIdResponse'] = _GETRETURNPRODUCTBYIDRESPONSE
DESCRIPTOR.message_types_by_name['SubmitReturnRequest'] = _SUBMITRETURNREQUEST
DESCRIPTOR.message_types_by_name['SubmitReturnResponse'] = _SUBMITRETURNRESPONSE
DESCRIPTOR.message_types_by_name['RejectReturnRequest'] = _REJECTRETURNREQUEST
DESCRIPTOR.message_types_by_name['RejectReturnResponse'] = _REJECTRETURNRESPONSE
DESCRIPTOR.message_types_by_name['ApproveReturnRequest'] = _APPROVERETURNREQUEST
DESCRIPTOR.message_types_by_name['ApproveReturnResponse'] = _APPROVERETURNRESPONSE
DESCRIPTOR.message_types_by_name['DeliveryReturnRequest'] = _DELIVERYRETURNREQUEST
DESCRIPTOR.message_types_by_name['DeliveryReturnResponse'] = _DELIVERYRETURNRESPONSE
DESCRIPTOR.message_types_by_name['UpdateReturnRequest'] = _UPDATERETURNREQUEST
DESCRIPTOR.message_types_by_name['UpdateReturnResponse'] = _UPDATERETURNRESPONSE
DESCRIPTOR.message_types_by_name['UpdateRemarkRequest'] = _UPDATEREMARKREQUEST
DESCRIPTOR.message_types_by_name['UpdateRemarkResponse'] = _UPDATEREMARKRESPONSE
DESCRIPTOR.message_types_by_name['DeleteReturnRequest'] = _DELETERETURNREQUEST
DESCRIPTOR.message_types_by_name['DeleteReturnResponse'] = _DELETERETURNRESPONSE
DESCRIPTOR.message_types_by_name['ValidProduct'] = _VALIDPRODUCT
DESCRIPTOR.message_types_by_name['Unit'] = _UNIT
DESCRIPTOR.message_types_by_name['ProductDetail'] = _PRODUCTDETAIL
DESCRIPTOR.message_types_by_name['CreateAdjustReturnResponse'] = _CREATEADJUSTRETURNRESPONSE
DESCRIPTOR.message_types_by_name['GetHistoryByIdRequest'] = _GETHISTORYBYIDREQUEST
DESCRIPTOR.message_types_by_name['HistoryResponse'] = _HISTORYRESPONSE
DESCRIPTOR.message_types_by_name['History'] = _HISTORY
DESCRIPTOR.message_types_by_name['Attachments'] = _ATTACHMENTS
DESCRIPTOR.message_types_by_name['GetReturnCategoryByBranchIdRequest'] = _GETRETURNCATEGORYBYBRANCHIDREQUEST
DESCRIPTOR.message_types_by_name['GetReturnCategoryByBranchIdResponse'] = _GETRETURNCATEGORYBYBRANCHIDRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ListReturnRequest = _reflection.GeneratedProtocolMessageType('ListReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTRETURNREQUEST,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.ListReturnRequest)
  ))
_sym_db.RegisterMessage(ListReturnRequest)

ListReturnResponse = _reflection.GeneratedProtocolMessageType('ListReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTRETURNRESPONSE,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.ListReturnResponse)
  ))
_sym_db.RegisterMessage(ListReturnResponse)

Returns = _reflection.GeneratedProtocolMessageType('Returns', (_message.Message,), dict(
  DESCRIPTOR = _RETURNS,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.Returns)
  ))
_sym_db.RegisterMessage(Returns)

CreateReturnRequest = _reflection.GeneratedProtocolMessageType('CreateReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATERETURNREQUEST,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.CreateReturnRequest)
  ))
_sym_db.RegisterMessage(CreateReturnRequest)

CreateReturnResponse = _reflection.GeneratedProtocolMessageType('CreateReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATERETURNRESPONSE,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.CreateReturnResponse)
  ))
_sym_db.RegisterMessage(CreateReturnResponse)

Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCT,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.Product)
  ))
_sym_db.RegisterMessage(Product)

ConfirmReturnRequest = _reflection.GeneratedProtocolMessageType('ConfirmReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMRETURNREQUEST,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.ConfirmReturnRequest)
  ))
_sym_db.RegisterMessage(ConfirmReturnRequest)

ConfirmReturnResponse = _reflection.GeneratedProtocolMessageType('ConfirmReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMRETURNRESPONSE,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.ConfirmReturnResponse)
  ))
_sym_db.RegisterMessage(ConfirmReturnResponse)

CommonReply = _reflection.GeneratedProtocolMessageType('CommonReply', (_message.Message,), dict(
  DESCRIPTOR = _COMMONREPLY,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.CommonReply)
  ))
_sym_db.RegisterMessage(CommonReply)

CheckReturnAvailableRequest = _reflection.GeneratedProtocolMessageType('CheckReturnAvailableRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHECKRETURNAVAILABLEREQUEST,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.CheckReturnAvailableRequest)
  ))
_sym_db.RegisterMessage(CheckReturnAvailableRequest)

GetValidProductRequest = _reflection.GeneratedProtocolMessageType('GetValidProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETVALIDPRODUCTREQUEST,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.GetValidProductRequest)
  ))
_sym_db.RegisterMessage(GetValidProductRequest)

GetValidProductResponse = _reflection.GeneratedProtocolMessageType('GetValidProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETVALIDPRODUCTRESPONSE,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.GetValidProductResponse)
  ))
_sym_db.RegisterMessage(GetValidProductResponse)

ListReturnColdRequest = _reflection.GeneratedProtocolMessageType('ListReturnColdRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTRETURNCOLDREQUEST,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.ListReturnColdRequest)
  ))
_sym_db.RegisterMessage(ListReturnColdRequest)

ListReturnColdResponse = _reflection.GeneratedProtocolMessageType('ListReturnColdResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTRETURNCOLDRESPONSE,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.ListReturnColdResponse)
  ))
_sym_db.RegisterMessage(ListReturnColdResponse)

GetReturnByIdRequest = _reflection.GeneratedProtocolMessageType('GetReturnByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRETURNBYIDREQUEST,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.GetReturnByIdRequest)
  ))
_sym_db.RegisterMessage(GetReturnByIdRequest)

GetReturnByIdResponse = _reflection.GeneratedProtocolMessageType('GetReturnByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRETURNBYIDRESPONSE,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.GetReturnByIdResponse)
  ))
_sym_db.RegisterMessage(GetReturnByIdResponse)

GetReturnProductByIdRequest = _reflection.GeneratedProtocolMessageType('GetReturnProductByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRETURNPRODUCTBYIDREQUEST,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.GetReturnProductByIdRequest)
  ))
_sym_db.RegisterMessage(GetReturnProductByIdRequest)

GetReturnProductByIdResponse = _reflection.GeneratedProtocolMessageType('GetReturnProductByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRETURNPRODUCTBYIDRESPONSE,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.GetReturnProductByIdResponse)
  ))
_sym_db.RegisterMessage(GetReturnProductByIdResponse)

SubmitReturnRequest = _reflection.GeneratedProtocolMessageType('SubmitReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITRETURNREQUEST,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.SubmitReturnRequest)
  ))
_sym_db.RegisterMessage(SubmitReturnRequest)

SubmitReturnResponse = _reflection.GeneratedProtocolMessageType('SubmitReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITRETURNRESPONSE,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.SubmitReturnResponse)
  ))
_sym_db.RegisterMessage(SubmitReturnResponse)

RejectReturnRequest = _reflection.GeneratedProtocolMessageType('RejectReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _REJECTRETURNREQUEST,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.RejectReturnRequest)
  ))
_sym_db.RegisterMessage(RejectReturnRequest)

RejectReturnResponse = _reflection.GeneratedProtocolMessageType('RejectReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _REJECTRETURNRESPONSE,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.RejectReturnResponse)
  ))
_sym_db.RegisterMessage(RejectReturnResponse)

ApproveReturnRequest = _reflection.GeneratedProtocolMessageType('ApproveReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _APPROVERETURNREQUEST,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.ApproveReturnRequest)
  ))
_sym_db.RegisterMessage(ApproveReturnRequest)

ApproveReturnResponse = _reflection.GeneratedProtocolMessageType('ApproveReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _APPROVERETURNRESPONSE,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.ApproveReturnResponse)
  ))
_sym_db.RegisterMessage(ApproveReturnResponse)

DeliveryReturnRequest = _reflection.GeneratedProtocolMessageType('DeliveryReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELIVERYRETURNREQUEST,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.DeliveryReturnRequest)
  ))
_sym_db.RegisterMessage(DeliveryReturnRequest)

DeliveryReturnResponse = _reflection.GeneratedProtocolMessageType('DeliveryReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELIVERYRETURNRESPONSE,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.DeliveryReturnResponse)
  ))
_sym_db.RegisterMessage(DeliveryReturnResponse)

UpdateReturnRequest = _reflection.GeneratedProtocolMessageType('UpdateReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERETURNREQUEST,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.UpdateReturnRequest)
  ))
_sym_db.RegisterMessage(UpdateReturnRequest)

UpdateReturnResponse = _reflection.GeneratedProtocolMessageType('UpdateReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERETURNRESPONSE,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.UpdateReturnResponse)
  ))
_sym_db.RegisterMessage(UpdateReturnResponse)

UpdateRemarkRequest = _reflection.GeneratedProtocolMessageType('UpdateRemarkRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEREMARKREQUEST,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.UpdateRemarkRequest)
  ))
_sym_db.RegisterMessage(UpdateRemarkRequest)

UpdateRemarkResponse = _reflection.GeneratedProtocolMessageType('UpdateRemarkResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEREMARKRESPONSE,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.UpdateRemarkResponse)
  ))
_sym_db.RegisterMessage(UpdateRemarkResponse)

DeleteReturnRequest = _reflection.GeneratedProtocolMessageType('DeleteReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETERETURNREQUEST,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.DeleteReturnRequest)
  ))
_sym_db.RegisterMessage(DeleteReturnRequest)

DeleteReturnResponse = _reflection.GeneratedProtocolMessageType('DeleteReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETERETURNRESPONSE,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.DeleteReturnResponse)
  ))
_sym_db.RegisterMessage(DeleteReturnResponse)

ValidProduct = _reflection.GeneratedProtocolMessageType('ValidProduct', (_message.Message,), dict(
  DESCRIPTOR = _VALIDPRODUCT,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.ValidProduct)
  ))
_sym_db.RegisterMessage(ValidProduct)

Unit = _reflection.GeneratedProtocolMessageType('Unit', (_message.Message,), dict(
  DESCRIPTOR = _UNIT,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.Unit)
  ))
_sym_db.RegisterMessage(Unit)

ProductDetail = _reflection.GeneratedProtocolMessageType('ProductDetail', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTDETAIL,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.ProductDetail)
  ))
_sym_db.RegisterMessage(ProductDetail)

CreateAdjustReturnResponse = _reflection.GeneratedProtocolMessageType('CreateAdjustReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEADJUSTRETURNRESPONSE,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.CreateAdjustReturnResponse)
  ))
_sym_db.RegisterMessage(CreateAdjustReturnResponse)

GetHistoryByIdRequest = _reflection.GeneratedProtocolMessageType('GetHistoryByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETHISTORYBYIDREQUEST,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.GetHistoryByIdRequest)
  ))
_sym_db.RegisterMessage(GetHistoryByIdRequest)

HistoryResponse = _reflection.GeneratedProtocolMessageType('HistoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _HISTORYRESPONSE,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.HistoryResponse)
  ))
_sym_db.RegisterMessage(HistoryResponse)

History = _reflection.GeneratedProtocolMessageType('History', (_message.Message,), dict(
  DESCRIPTOR = _HISTORY,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.History)
  ))
_sym_db.RegisterMessage(History)

Attachments = _reflection.GeneratedProtocolMessageType('Attachments', (_message.Message,), dict(
  DESCRIPTOR = _ATTACHMENTS,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.Attachments)
  ))
_sym_db.RegisterMessage(Attachments)

GetReturnCategoryByBranchIdRequest = _reflection.GeneratedProtocolMessageType('GetReturnCategoryByBranchIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRETURNCATEGORYBYBRANCHIDREQUEST,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.GetReturnCategoryByBranchIdRequest)
  ))
_sym_db.RegisterMessage(GetReturnCategoryByBranchIdRequest)

GetReturnCategoryByBranchIdResponse = _reflection.GeneratedProtocolMessageType('GetReturnCategoryByBranchIdResponse', (_message.Message,), dict(

  CategoryItem = _reflection.GeneratedProtocolMessageType('CategoryItem', (_message.Message,), dict(
    DESCRIPTOR = _GETRETURNCATEGORYBYBRANCHIDRESPONSE_CATEGORYITEM,
    __module__ = 'mobile.mobile_franchisee_return_pb2'
    # @@protoc_insertion_point(class_scope:franchisee_returns.GetReturnCategoryByBranchIdResponse.CategoryItem)
    ))
  ,
  DESCRIPTOR = _GETRETURNCATEGORYBYBRANCHIDRESPONSE,
  __module__ = 'mobile.mobile_franchisee_return_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_returns.GetReturnCategoryByBranchIdResponse)
  ))
_sym_db.RegisterMessage(GetReturnCategoryByBranchIdResponse)
_sym_db.RegisterMessage(GetReturnCategoryByBranchIdResponse.CategoryItem)


DESCRIPTOR._options = None

_FRANCHISEERETURNSERVICE = _descriptor.ServiceDescriptor(
  name='FranchiseeReturnService',
  full_name='franchisee_returns.FranchiseeReturnService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=8292,
  serialized_end=11334,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateReturn',
    full_name='franchisee_returns.FranchiseeReturnService.CreateReturn',
    index=0,
    containing_service=None,
    input_type=_CREATERETURNREQUEST,
    output_type=_CREATERETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002-\"(/api/v2/supply/mobile/franchisee/returns:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmReturn',
    full_name='franchisee_returns.FranchiseeReturnService.ConfirmReturn',
    index=1,
    containing_service=None,
    input_type=_CONFIRMRETURNREQUEST,
    output_type=_CONFIRMRETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002:\"5/api/v2/supply/mobile/franchisee/returns/{id}/confirm:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListReturn',
    full_name='franchisee_returns.FranchiseeReturnService.ListReturn',
    index=2,
    containing_service=None,
    input_type=_LISTRETURNREQUEST,
    output_type=_LISTRETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002*\022(/api/v2/supply/mobile/franchisee/returns'),
  ),
  _descriptor.MethodDescriptor(
    name='GetValidProduct',
    full_name='franchisee_returns.FranchiseeReturnService.GetValidProduct',
    index=3,
    containing_service=None,
    input_type=_GETVALIDPRODUCTREQUEST,
    output_type=_GETVALIDPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\0021\022//api/v2/supply/mobile/franchisee/return/product'),
  ),
  _descriptor.MethodDescriptor(
    name='ListReturnCold',
    full_name='franchisee_returns.FranchiseeReturnService.ListReturnCold',
    index=4,
    containing_service=None,
    input_type=_LISTRETURNCOLDREQUEST,
    output_type=_LISTRETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002/\022-/api/v2/supply/mobile/franchisee/returns/cold'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReturnById',
    full_name='franchisee_returns.FranchiseeReturnService.GetReturnById',
    index=5,
    containing_service=None,
    input_type=_GETRETURNBYIDREQUEST,
    output_type=_RETURNS,
    serialized_options=_b('\202\323\344\223\002/\022-/api/v2/supply/mobile/franchisee/returns/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReturnProductById',
    full_name='franchisee_returns.FranchiseeReturnService.GetReturnProductById',
    index=6,
    containing_service=None,
    input_type=_GETRETURNPRODUCTBYIDREQUEST,
    output_type=_GETRETURNPRODUCTBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\0225/api/v2/supply/mobile/franchisee/returns/{id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='SubmitReturn',
    full_name='franchisee_returns.FranchiseeReturnService.SubmitReturn',
    index=7,
    containing_service=None,
    input_type=_SUBMITRETURNREQUEST,
    output_type=_SUBMITRETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\"4/api/v2/supply/mobile/franchisee/returns/{id}/submit:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='RejectReturn',
    full_name='franchisee_returns.FranchiseeReturnService.RejectReturn',
    index=8,
    containing_service=None,
    input_type=_REJECTRETURNREQUEST,
    output_type=_REJECTRETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\"4/api/v2/supply/mobile/franchisee/returns/{id}/reject:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ApproveReturn',
    full_name='franchisee_returns.FranchiseeReturnService.ApproveReturn',
    index=9,
    containing_service=None,
    input_type=_APPROVERETURNREQUEST,
    output_type=_APPROVERETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002:\"5/api/v2/supply/mobile/franchisee/returns/{id}/approve:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeliveryReturn',
    full_name='franchisee_returns.FranchiseeReturnService.DeliveryReturn',
    index=10,
    containing_service=None,
    input_type=_DELIVERYRETURNREQUEST,
    output_type=_DELIVERYRETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002;\"6/api/v2/supply/mobile/franchisee/returns/{id}/delivery:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateReturn',
    full_name='franchisee_returns.FranchiseeReturnService.UpdateReturn',
    index=11,
    containing_service=None,
    input_type=_UPDATERETURNREQUEST,
    output_type=_UPDATERETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\"4/api/v2/supply/mobile/franchisee/returns/{id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateRemark',
    full_name='franchisee_returns.FranchiseeReturnService.UpdateRemark',
    index=12,
    containing_service=None,
    input_type=_UPDATEREMARKREQUEST,
    output_type=_UPDATEREMARKRESPONSE,
    serialized_options=_b('\202\323\344\223\002@\";/api/v2/supply/mobile/franchisee/returns/{id}/update/remark:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteReturn',
    full_name='franchisee_returns.FranchiseeReturnService.DeleteReturn',
    index=13,
    containing_service=None,
    input_type=_DELETERETURNREQUEST,
    output_type=_DELETERETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\"4/api/v2/supply/mobile/franchisee/returns/{id}/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CreateAdjustReturn',
    full_name='franchisee_returns.FranchiseeReturnService.CreateAdjustReturn',
    index=14,
    containing_service=None,
    input_type=_CREATERETURNREQUEST,
    output_type=_CREATEADJUSTRETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\0024\"//api/v2/supply/mobile/franchisee/adjust_returns:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CheckReturnAvailableByrec',
    full_name='franchisee_returns.FranchiseeReturnService.CheckReturnAvailableByrec',
    index=15,
    containing_service=None,
    input_type=_CHECKRETURNAVAILABLEREQUEST,
    output_type=_COMMONREPLY,
    serialized_options=_b('\202\323\344\223\0022\"-/api/v2/supply/mobile/franchisee/return/check:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetHistoryById',
    full_name='franchisee_returns.FranchiseeReturnService.GetHistoryById',
    index=16,
    containing_service=None,
    input_type=_GETHISTORYBYIDREQUEST,
    output_type=_HISTORYRESPONSE,
    serialized_options=_b('\202\323\344\223\0026\0224/api/v2/supply/mobile/franchisee/return/{id}/history'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReturnCategoryByBranchId',
    full_name='franchisee_returns.FranchiseeReturnService.GetReturnCategoryByBranchId',
    index=17,
    containing_service=None,
    input_type=_GETRETURNCATEGORYBYBRANCHIDREQUEST,
    output_type=_GETRETURNCATEGORYBYBRANCHIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002C\022A/api/v2/supply/mobile/franchisee/return/store/{store_id}/category'),
  ),
])
_sym_db.RegisterServiceDescriptor(_FRANCHISEERETURNSERVICE)

DESCRIPTOR.services_by_name['FranchiseeReturnService'] = _FRANCHISEERETURNSERVICE

# @@protoc_insertion_point(module_scope)
