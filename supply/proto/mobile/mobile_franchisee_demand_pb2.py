# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mobile/mobile_franchisee_demand.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='mobile/mobile_franchisee_demand.proto',
  package='mobile_franchisee_demand',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n%mobile/mobile_franchisee_demand.proto\x12\x18mobile_franchisee_demand\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xf5\x02\n\x14\x43reateFDemandRequest\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\x12/\n\x0b\x64\x65mand_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0breceived_by\x18\x03 \x01(\x04\x12\x15\n\rdistribute_by\x18\x04 \x01(\x04\x12\x0e\n\x06status\x18\x05 \x01(\t\x12\x33\n\x08products\x18\x06 \x03(\x0b\x32!.mobile_franchisee_demand.Product\x12\x15\n\rfranchisee_id\x18\x07 \x01(\x04\x12\x15\n\rorder_type_id\x18\x08 \x01(\x04\x12\x0e\n\x06remark\x18\n \x01(\t\x12\x0c\n\x04type\x18\x0b \x01(\t\x12\x0e\n\x06reason\x18\x0c \x01(\t\x12\x10\n\x08\x62us_type\x18\r \x01(\t\x12\x13\n\x0bpayment_way\x18\x0e \x01(\t\x12\x15\n\rtrade_company\x18\x0f \x01(\x04\x12\x0f\n\x07\x65xtends\x18\x10 \x01(\t\"@\n\x15\x43reateFDemandResponse\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12\x14\n\x0ctotal_amount\x18\x02 \x01(\x01\"\xb6\x03\n\x12ListFDemandRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x03 \x03(\t\x12\x14\n\x0creceived_bys\x18\x04 \x03(\x04\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\r\n\x05types\x18\x06 \x03(\t\x12\x10\n\x08sub_type\x18\x07 \x01(\t\x12\x12\n\nchain_type\x18\x08 \x01(\t\x12\x11\n\tbus_types\x18\t \x03(\t\x12\x0e\n\x06offset\x18\n \x01(\x04\x12\r\n\x05limit\x18\x0b \x01(\x04\x12\r\n\x05order\x18\x0c \x01(\t\x12\x0c\n\x04sort\x18\r \x01(\t\x12\x15\n\rinclude_total\x18\x0e \x01(\x08\x12\x0b\n\x03ids\x18\x0f \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x10 \x03(\x04\x12\x16\n\x0e\x66ranchisee_ids\x18\x11 \x03(\x04\x12\x14\n\x0cpayment_ways\x18\x12 \x03(\t\x12\x16\n\x0eorder_type_ids\x18\x13 \x03(\x04\x12\x0b\n\x03lan\x18\x14 \x01(\t\"T\n\x13ListFDemandResponse\x12.\n\x04rows\x18\x01 \x03(\x0b\x32 .mobile_franchisee_demand.Demand\x12\r\n\x05total\x18\x02 \x01(\x04\"\xb2\x0c\n\x06\x44\x65mand\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08\x62\x61tch_id\x18\x03 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\x0c\n\x04type\x18\x05 \x01(\t\x12\x10\n\x08sub_type\x18\x06 \x01(\t\x12\x10\n\x08\x62us_type\x18\x07 \x01(\t\x12\x13\n\x0breceived_by\x18\x08 \x01(\x04\x12\x15\n\rreceived_code\x18\t \x01(\t\x12\x15\n\rreceived_name\x18\n \x01(\t\x12\x15\n\rdistribute_by\x18\x0b \x01(\x04\x12\x12\n\nstore_type\x18\x0c \x01(\t\x12\x15\n\rfranchisee_id\x18\r \x01(\x04\x12\x17\n\x0f\x66ranchisee_code\x18\x0e \x01(\t\x12\x17\n\x0f\x66ranchisee_name\x18\x0f \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x12 \x01(\t\x12\x16\n\x0eprocess_status\x18\x13 \x01(\t\x12\x15\n\rreject_reason\x18\x14 \x01(\t\x12\x0e\n\x06remark\x18\x15 \x01(\t\x12\x0e\n\x06reason\x18\x16 \x01(\t\x12.\n\ncreated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x19 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1a \x01(\t\x12\x12\n\nupdated_by\x18\x1b \x01(\x04\x12\x14\n\x0cupdated_name\x18\x1c \x01(\t\x12\x11\n\treview_by\x18\x1d \x01(\x04\x12\x13\n\x0bhas_product\x18\x1e \x01(\x05\x12\x15\n\rsum_price_tax\x18\x1f \x01(\x01\x12\x0f\n\x07sum_tax\x18  \x01(\x01\x12\x12\n\npay_amount\x18! \x01(\x01\x12\x13\n\x0b\x61ttachments\x18\" \x03(\t\x12\x0f\n\x07\x65xtends\x18# \x01(\t\x12\x36\n\x06orders\x18$ \x03(\x0b\x32&.mobile_franchisee_demand.Demand.Order\x12\x13\n\x0bpayment_way\x18% \x01(\t\x12\x12\n\nchain_type\x18& \x01(\t\x12\x11\n\tis_adjust\x18\' \x01(\x08\x12\x16\n\x0e\x63onfirm_amount\x18( \x01(\x01\x12\x15\n\rorder_type_id\x18) \x01(\x04\x12\x17\n\x0forder_type_name\x18* \x01(\t\x12\x17\n\x0forder_type_code\x18+ \x01(\t\x12\x14\n\x0csales_amount\x18, \x01(\x01\x12\x1c\n\x14\x63onfirm_sales_amount\x18- \x01(\x01\x12\x38\n\x07refunds\x18. \x03(\x0b\x32\'.mobile_franchisee_demand.Demand.Refund\x12:\n\x08receives\x18/ \x03(\x0b\x32(.mobile_franchisee_demand.Demand.Receive\x12\x16\n\x0e\x61pprove_amount\x18\x30 \x01(\x01\x12\x1c\n\x14\x61pprove_sales_amount\x18\x31 \x01(\x01\x12G\n\x0forder_type_time\x18\x32 \x01(\x0b\x32..mobile_franchisee_demand.Demand.OrderTypeTime\x12\x12\n\nprice_type\x18\x35 \x01(\t\x1a-\n\x05Order\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x12\n\norder_code\x18\x02 \x01(\t\x1a@\n\x06Refund\x12\x11\n\trefund_id\x18\x01 \x01(\x04\x12\x13\n\x0brefund_code\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\x1a\x43\n\x07Receive\x12\x12\n\nreceive_id\x18\x01 \x01(\x04\x12\x14\n\x0creceive_code\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\x1a\x37\n\rOrderTypeTime\x12\x12\n\norder_time\x18\x01 \x03(\t\x12\x12\n\naudit_time\x18\x02 \x03(\t\"*\n\x15GetFDemandByIdRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\"*\n\x15GetHistoryByIdRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\"Q\n\x0fHistoryResponse\x12/\n\x04rows\x18\x01 \x03(\x0b\x32!.mobile_franchisee_demand.History\x12\r\n\x05total\x18\x02 \x01(\x04\"\x7f\n\x07History\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x12\n\ncreated_by\x18\x03 \x01(\x04\x12.\n\ncreated_at\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\x05 \x01(\t\"\x94\x01\n\x16GetProductsByIdRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x03\x12\x0e\n\x06offset\x18\x03 \x01(\t\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12\r\n\x05order\x18\x05 \x01(\t\x12\x0c\n\x04sort\x18\x06 \x01(\t\x12\x14\n\x0c\x61ttach_extra\x18\x07 \x01(\x08\"R\n\x10ProductsResponse\x12/\n\x04rows\x18\x01 \x03(\x0b\x32!.mobile_franchisee_demand.Product\x12\r\n\x05total\x18\x02 \x01(\x04\"\x95\x0b\n\x07Product\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x03 \x01(\t\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12\x16\n\x0eorg_product_id\x18\x05 \x01(\x04\x12\x18\n\x10org_product_code\x18\x06 \x01(\t\x12\x18\n\x10org_product_name\x18\x07 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x08 \x01(\x04\x12\x15\n\rcategory_name\x18\t \x01(\t\x12\x14\n\x0cproduct_spec\x18\n \x01(\t\x12\x0f\n\x07unit_id\x18\x0b \x01(\x04\x12\x11\n\tunit_name\x18\x0c \x01(\t\x12\x11\n\tunit_spec\x18\r \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x0e \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x0f \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x10 \x01(\t\x12\x11\n\tunit_rate\x18\x11 \x01(\x01\x12\x10\n\x08quantity\x18\x12 \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x13 \x01(\x01\x12\x14\n\x0cmin_quantity\x18\x14 \x01(\x01\x12\x14\n\x0cmax_quantity\x18\x15 \x01(\x01\x12\x1a\n\x12increment_quantity\x18\x16 \x01(\x01\x12\x11\n\ttax_price\x18\x17 \x01(\x01\x12\x12\n\ncost_price\x18\x18 \x01(\x01\x12\x10\n\x08tax_rate\x18\x19 \x01(\x01\x12\x0e\n\x06\x61mount\x18\x1a \x01(\x01\x12\x14\n\x0c\x61rrival_days\x18\x1b \x01(\t\x12\x19\n\x11\x64istribution_type\x18\x1c \x01(\t\x12\x15\n\rdistribute_by\x18\x1d \x01(\x04\x12\x17\n\x0f\x64istribute_name\x18\x1e \x01(\t\x12\x0e\n\x06status\x18\x1f \x01(\t\x12\x17\n\x0fyesterday_sales\x18  \x01(\x01\x12\x15\n\rinventory_qty\x18! \x01(\x01\x12\x12\n\non_way_qty\x18\" \x01(\x01\x12\x11\n\tis_delete\x18# \x01(\r\x12.\n\ncreated_at\x18$ \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18% \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18& \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\' \x01(\t\x12\x12\n\nupdated_by\x18( \x01(\x04\x12\x14\n\x0cupdated_name\x18) \x01(\t\x12\x11\n\tdemand_id\x18* \x01(\x04\x12\x12\n\npartner_id\x18+ \x01(\x04\x12\x13\n\x0b\x61llow_order\x18, \x01(\x08\x12\x18\n\x10\x63onfirm_quantity\x18- \x01(\x01\x12\x16\n\x0e\x63onfirm_amount\x18. \x01(\x01\x12\x0f\n\x07\x65xtends\x18/ \x01(\t\x12\x14\n\x0cstorage_type\x18\x30 \x01(\t\x12\x13\n\x0bsales_price\x18\x31 \x01(\x01\x12\x14\n\x0csales_amount\x18\x32 \x01(\x01\x12\x1c\n\x14\x63onfirm_sales_amount\x18\x33 \x01(\x01\x12\x18\n\x10\x61pprove_quantity\x18\x34 \x01(\x01\x12\x16\n\x0e\x61pprove_amount\x18\x35 \x01(\x01\x12\x1c\n\x14\x61pprove_sales_amount\x18\x36 \x01(\x01\x12\x16\n\x0eis_confirm_qty\x18\x37 \x01(\x08\x12\x16\n\x0eis_approve_qty\x18\x38 \x01(\x08\x12<\n\x11relation_products\x18\x39 \x03(\x0b\x32!.mobile_franchisee_demand.Product\x12\x14\n\x0cproduct_type\x18: \x01(\t\x12\r\n\x05ratio\x18; \x01(\t\x12\x11\n\tconfigure\x18< \x01(\x05\"~\n\x14UpdateProductRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12\x0e\n\x06remark\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x33\n\x08products\x18\x04 \x03(\x0b\x32!.mobile_franchisee_demand.Product\"h\n\x16UpdateProductsResponse\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12;\n\x04msgs\x18\x02 \x03(\x0b\x32-.mobile_franchisee_demand.CheckProductMessage\"x\n\x16\x44\x65\x61lFDemandByIdRequest\x12\x12\n\ndemand_ids\x18\x01 \x03(\x04\x12\x0e\n\x06\x61\x63tion\x18\x02 \x01(\t\x12\x0e\n\x06remark\x18\x03 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x04 \x03(\t\x12\x15\n\rreject_reason\x18\x05 \x01(\t\")\n\x17\x44\x65\x61lFDemandByIdResponse\x12\x0e\n\x06result\x18\x01 \x01(\t\"\xe0\x03\n\x18\x41\x64\x64\x46\x44\x65mandProductRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12O\n\x08products\x18\x02 \x03(\x0b\x32=.mobile_franchisee_demand.AddFDemandProductRequest.AddProduct\x1a\xdf\x02\n\nAddProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x18\n\x10\x61pprove_quantity\x18\x02 \x01(\x01\x12\x11\n\ttax_price\x18\x03 \x01(\x01\x12\x10\n\x08tax_rate\x18\x04 \x01(\x01\x12\x13\n\x0bsales_price\x18\x05 \x01(\x01\x12\x14\n\x0c\x61rrival_days\x18\x06 \x01(\t\x12\x14\n\x0cmin_quantity\x18\x07 \x01(\x01\x12\x14\n\x0cmax_quantity\x18\x08 \x01(\x01\x12\x1a\n\x12increment_quantity\x18\t \x01(\x01\x12\x0f\n\x07\x65xtends\x18\n \x01(\t\x12X\n\x11relation_products\x18\x0b \x03(\x0b\x32=.mobile_franchisee_demand.AddFDemandProductRequest.AddProduct\x12\r\n\x05ratio\x18\x0c \x01(\t\x12\x11\n\tconfigure\x18\r \x01(\x05\".\n\x19\x41\x64\x64\x46\x44\x65mandProductResponse\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\"\xf2\x01\n\x19QueryFDemandReportRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x03 \x03(\t\x12\x14\n\x0creceived_bys\x18\x04 \x03(\x04\x12\x0e\n\x06offset\x18\n \x01(\x04\x12\r\n\x05limit\x18\x0b \x01(\x04\x12\r\n\x05order\x18\x0c \x01(\t\x12\x0c\n\x04sort\x18\r \x01(\t\x12\x15\n\rinclude_total\x18\x0e \x01(\x08\"\xd6\x01\n\x0c\x44\x65mandReport\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12\x13\n\x0breceived_by\x18\x02 \x01(\x04\x12\x15\n\rreceived_code\x18\x03 \x01(\t\x12\x15\n\rreceived_name\x18\x04 \x01(\t\x12\x0e\n\x06\x61mount\x18\x05 \x01(\x01\x12\x16\n\x0e\x63onfirm_amount\x18\x07 \x01(\x01\x12\x14\n\x0csales_amount\x18\x08 \x01(\x01\x12\x1c\n\x14\x63onfirm_sales_amount\x18\t \x01(\x01\x12\x14\n\x0ctotal_demand\x18\n \x01(\r\"\xb9\x01\n\x1aQueryFDemandReportResponse\x12\x34\n\x04rows\x18\x01 \x03(\x0b\x32&.mobile_franchisee_demand.DemandReport\x12\x14\n\x0ctotal_amount\x18\x02 \x01(\x01\x12\x14\n\x0ctotal_demand\x18\x03 \x01(\r\x12\x14\n\x0c\x64\x65mand_price\x18\x04 \x01(\x01\x12\r\n\x05total\x18\x05 \x01(\x04\x12\x14\n\x0csales_amount\x18\x06 \x01(\x01\"\x80\x01\n\x1b\x41\x64\x64ShoppingCartCacheRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x15\n\rorder_type_id\x18\x02 \x01(\x04\x12\x38\n\x08products\x18\x03 \x03(\x0b\x32&.mobile_franchisee_demand.ProductCache\"C\n\x1c\x41\x64\x64ShoppingCartCacheResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\"4\n\x0cProductCache\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x10\n\x08quantity\x18\x02 \x01(\x01\"F\n\x1bGetShoppingCartCacheRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x15\n\rorder_type_id\x18\x02 \x01(\x04\"X\n\x1cGetShoppingCartCacheResponse\x12\x38\n\x08products\x18\x01 \x03(\x0b\x32&.mobile_franchisee_demand.ProductCache\"l\n\x17\x43heckFDemandTodoRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12,\n\x08\x62us_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x10\n\x08store_id\x18\x03 \x01(\x04\"E\n\x0bTodoReceipt\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x0c\n\x04type\x18\x07 \x01(\t\"\x9f\x01\n\x18\x43heckFDemandTodoResponse\x12\x0f\n\x07handler\x18\x01 \x01(\x08\x12\x38\n\treceiving\x18\x02 \x03(\x0b\x32%.mobile_franchisee_demand.TodoReceipt\x12\x38\n\tstocktake\x18\x03 \x03(\x0b\x32%.mobile_franchisee_demand.TodoReceipt\"\xa2\x02\n\x13\x43heckProductMessage\x12\x14\n\x0cproduct_name\x18\x01 \x01(\t\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x0c\n\x04spec\x18\x03 \x01(\t\x12\x10\n\x08quantity\x18\x04 \x01(\x01\x12\x14\n\x0cmin_quantity\x18\x05 \x01(\x01\x12\x1a\n\x12increment_quantity\x18\x06 \x01(\x01\x12\x14\n\x0cmax_quantity\x18\x07 \x01(\x01\x12\x0c\n\x04unit\x18\x08 \x01(\t\x12\x11\n\ttax_price\x18\t \x01(\x01\x12\x0e\n\x06\x61mount\x18\n \x01(\x01\x12\x14\n\x0cstorage_type\x18\x0b \x01(\t\x12\n\n\x02id\x18\x0c \x01(\x04\x12\x12\n\nproduct_id\x18\r \x01(\x04\x12\x10\n\x08tax_rate\x18\x0e \x01(\x01\"\xf5\x01\n\x11\x43heckOrderMessage\x12\x0e\n\x06status\x18\x01 \x01(\t\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x15\n\rorder_type_id\x18\x04 \x01(\x04\x12\x17\n\x0f\x66ranchisee_name\x18\x05 \x01(\t\x12\x0e\n\x06\x61mount\x18\x06 \x01(\x01\x12/\n\x0b\x64\x65mand_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0b\n\x03msg\x18\t \x01(\t*\x96\x01\n\x06Status\x12\n\n\x06INITED\x10\x00\x12\x0b\n\x07PREPARE\x10\x01\x12\x0c\n\x08P_SUBMIT\x10\x02\x12\r\n\tSUBMITTED\x10\x04\x12\r\n\tR_APPROVE\x10\x03\x12\x0c\n\x08REJECTED\x10\x05\x12\r\n\tCANCELLED\x10\x06\x12\r\n\tCONFIRMED\x10\x07\x12\r\n\tAPPROVING\x10\n\x12\x0c\n\x08\x41PPROVED\x10\x0b*\x18\n\x04Type\x12\x07\n\x03\x46SD\x10\x00\x12\x07\n\x03\x46MD\x10\x01\x32\xc2\x11\n\x1dMobileFranchiseeDemandService\x12\xab\x01\n\rCreateFDemand\x12..mobile_franchisee_demand.CreateFDemandRequest\x1a/.mobile_franchisee_demand.CreateFDemandResponse\"9\x82\xd3\xe4\x93\x02\x33\"./api/v2/supply/mobile/franchisee/demand/create:\x01*\x12\x9b\x01\n\x0bListFDemand\x12,.mobile_franchisee_demand.ListFDemandRequest\x1a-.mobile_franchisee_demand.ListFDemandResponse\"/\x82\xd3\xe4\x93\x02)\x12\'/api/v2/supply/mobile/franchisee/demand\x12\xa0\x01\n\x0eGetFDemandById\x12/.mobile_franchisee_demand.GetFDemandByIdRequest\x1a .mobile_franchisee_demand.Demand\";\x82\xd3\xe4\x93\x02\x35\x12\x33/api/v2/supply/mobile/franchisee/demand/{demand_id}\x12\xb1\x01\n\x0eGetHistoryById\x12/.mobile_franchisee_demand.GetHistoryByIdRequest\x1a).mobile_franchisee_demand.HistoryResponse\"C\x82\xd3\xe4\x93\x02=\x12;/api/v2/supply/mobile/franchisee/demand/{demand_id}/history\x12\xb4\x01\n\x0fGetProductsById\x12\x30.mobile_franchisee_demand.GetProductsByIdRequest\x1a*.mobile_franchisee_demand.ProductsResponse\"C\x82\xd3\xe4\x93\x02=\x12;/api/v2/supply/mobile/franchisee/demand/{demand_id}/product\x12\xba\x01\n\x0eUpdateProducts\x12..mobile_franchisee_demand.UpdateProductRequest\x1a\x30.mobile_franchisee_demand.UpdateProductsResponse\"F\x82\xd3\xe4\x93\x02@\";/api/v2/supply/mobile/franchisee/demand/{demand_id}/product:\x01*\x12\xb8\x01\n\x0f\x44\x65\x61lFDemandById\x12\x30.mobile_franchisee_demand.DealFDemandByIdRequest\x1a\x31.mobile_franchisee_demand.DealFDemandByIdResponse\"@\x82\xd3\xe4\x93\x02:\"5/api/v2/supply/mobile/franchisee/demand/deal/{action}:\x01*\x12\xbc\x01\n\x11\x41\x64\x64\x46\x44\x65mandProduct\x12\x32.mobile_franchisee_demand.AddFDemandProductRequest\x1a\x33.mobile_franchisee_demand.AddFDemandProductResponse\">\x82\xd3\xe4\x93\x02\x38\"3/api/v2/supply/mobile/franchisee/demand/product/add:\x01*\x12\xba\x01\n\x12QueryFDemandReport\x12\x33.mobile_franchisee_demand.QueryFDemandReportRequest\x1a\x34.mobile_franchisee_demand.QueryFDemandReportResponse\"9\x82\xd3\xe4\x93\x02\x33\"./api/v2/supply/mobile/franchisee/demand/report:\x01*\x12\xcd\x01\n\x14\x41\x64\x64ShoppingCartCache\x12\x35.mobile_franchisee_demand.AddShoppingCartCacheRequest\x1a\x36.mobile_franchisee_demand.AddShoppingCartCacheResponse\"F\x82\xd3\xe4\x93\x02@\";/api/v2/supply/mobile/franchisee/demand/shopping-cart/cache:\x01*\x12\xca\x01\n\x14GetShoppingCartCache\x12\x35.mobile_franchisee_demand.GetShoppingCartCacheRequest\x1a\x36.mobile_franchisee_demand.GetShoppingCartCacheResponse\"C\x82\xd3\xe4\x93\x02=\x12;/api/v2/supply/mobile/franchisee/demand/shopping-cart/cache\x12\xb5\x01\n\x10\x43heckFDemandTodo\x12\x31.mobile_franchisee_demand.CheckFDemandTodoRequest\x1a\x32.mobile_franchisee_demand.CheckFDemandTodoResponse\":\x82\xd3\xe4\x93\x02\x34\x12\x32/api/v2/supply/mobile/franchisee/demand/todo/checkb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])

_STATUS = _descriptor.EnumDescriptor(
  name='Status',
  full_name='mobile_franchisee_demand.Status',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='INITED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PREPARE', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='P_SUBMIT', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=3, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='R_APPROVE', index=4, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REJECTED', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=7, number=7,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVING', index=8, number=10,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVED', index=9, number=11,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7536,
  serialized_end=7686,
)
_sym_db.RegisterEnumDescriptor(_STATUS)

Status = enum_type_wrapper.EnumTypeWrapper(_STATUS)
_TYPE = _descriptor.EnumDescriptor(
  name='Type',
  full_name='mobile_franchisee_demand.Type',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='FSD', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FMD', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7688,
  serialized_end=7712,
)
_sym_db.RegisterEnumDescriptor(_TYPE)

Type = enum_type_wrapper.EnumTypeWrapper(_TYPE)
INITED = 0
PREPARE = 1
P_SUBMIT = 2
SUBMITTED = 4
R_APPROVE = 3
REJECTED = 5
CANCELLED = 6
CONFIRMED = 7
APPROVING = 10
APPROVED = 11
FSD = 0
FMD = 1



_CREATEFDEMANDREQUEST = _descriptor.Descriptor(
  name='CreateFDemandRequest',
  full_name='mobile_franchisee_demand.CreateFDemandRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='mobile_franchisee_demand.CreateFDemandRequest.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='mobile_franchisee_demand.CreateFDemandRequest.demand_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='mobile_franchisee_demand.CreateFDemandRequest.received_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_by', full_name='mobile_franchisee_demand.CreateFDemandRequest.distribute_by', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_franchisee_demand.CreateFDemandRequest.status', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='mobile_franchisee_demand.CreateFDemandRequest.products', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='mobile_franchisee_demand.CreateFDemandRequest.franchisee_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='mobile_franchisee_demand.CreateFDemandRequest.order_type_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_franchisee_demand.CreateFDemandRequest.remark', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='mobile_franchisee_demand.CreateFDemandRequest.type', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='mobile_franchisee_demand.CreateFDemandRequest.reason', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bus_type', full_name='mobile_franchisee_demand.CreateFDemandRequest.bus_type', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_way', full_name='mobile_franchisee_demand.CreateFDemandRequest.payment_way', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trade_company', full_name='mobile_franchisee_demand.CreateFDemandRequest.trade_company', index=13,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='mobile_franchisee_demand.CreateFDemandRequest.extends', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=131,
  serialized_end=504,
)


_CREATEFDEMANDRESPONSE = _descriptor.Descriptor(
  name='CreateFDemandResponse',
  full_name='mobile_franchisee_demand.CreateFDemandResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='mobile_franchisee_demand.CreateFDemandResponse.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='mobile_franchisee_demand.CreateFDemandResponse.total_amount', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=506,
  serialized_end=570,
)


_LISTFDEMANDREQUEST = _descriptor.Descriptor(
  name='ListFDemandRequest',
  full_name='mobile_franchisee_demand.ListFDemandRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='mobile_franchisee_demand.ListFDemandRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='mobile_franchisee_demand.ListFDemandRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_franchisee_demand.ListFDemandRequest.status', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_bys', full_name='mobile_franchisee_demand.ListFDemandRequest.received_bys', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_franchisee_demand.ListFDemandRequest.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='types', full_name='mobile_franchisee_demand.ListFDemandRequest.types', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='mobile_franchisee_demand.ListFDemandRequest.sub_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='chain_type', full_name='mobile_franchisee_demand.ListFDemandRequest.chain_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bus_types', full_name='mobile_franchisee_demand.ListFDemandRequest.bus_types', index=8,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_franchisee_demand.ListFDemandRequest.offset', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_franchisee_demand.ListFDemandRequest.limit', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_franchisee_demand.ListFDemandRequest.order', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='mobile_franchisee_demand.ListFDemandRequest.sort', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='mobile_franchisee_demand.ListFDemandRequest.include_total', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='mobile_franchisee_demand.ListFDemandRequest.ids', index=14,
      number=15, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='mobile_franchisee_demand.ListFDemandRequest.product_ids', index=15,
      number=16, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_ids', full_name='mobile_franchisee_demand.ListFDemandRequest.franchisee_ids', index=16,
      number=17, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_ways', full_name='mobile_franchisee_demand.ListFDemandRequest.payment_ways', index=17,
      number=18, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_ids', full_name='mobile_franchisee_demand.ListFDemandRequest.order_type_ids', index=18,
      number=19, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='mobile_franchisee_demand.ListFDemandRequest.lan', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=573,
  serialized_end=1011,
)


_LISTFDEMANDRESPONSE = _descriptor.Descriptor(
  name='ListFDemandResponse',
  full_name='mobile_franchisee_demand.ListFDemandResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_franchisee_demand.ListFDemandResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_franchisee_demand.ListFDemandResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1013,
  serialized_end=1097,
)


_DEMAND_ORDER = _descriptor.Descriptor(
  name='Order',
  full_name='mobile_franchisee_demand.Demand.Order',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='mobile_franchisee_demand.Demand.Order.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='mobile_franchisee_demand.Demand.Order.order_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2449,
  serialized_end=2494,
)

_DEMAND_REFUND = _descriptor.Descriptor(
  name='Refund',
  full_name='mobile_franchisee_demand.Demand.Refund',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='refund_id', full_name='mobile_franchisee_demand.Demand.Refund.refund_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_code', full_name='mobile_franchisee_demand.Demand.Refund.refund_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_franchisee_demand.Demand.Refund.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2496,
  serialized_end=2560,
)

_DEMAND_RECEIVE = _descriptor.Descriptor(
  name='Receive',
  full_name='mobile_franchisee_demand.Demand.Receive',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receive_id', full_name='mobile_franchisee_demand.Demand.Receive.receive_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_code', full_name='mobile_franchisee_demand.Demand.Receive.receive_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_franchisee_demand.Demand.Receive.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2562,
  serialized_end=2629,
)

_DEMAND_ORDERTYPETIME = _descriptor.Descriptor(
  name='OrderTypeTime',
  full_name='mobile_franchisee_demand.Demand.OrderTypeTime',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_time', full_name='mobile_franchisee_demand.Demand.OrderTypeTime.order_time', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='audit_time', full_name='mobile_franchisee_demand.Demand.OrderTypeTime.audit_time', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2631,
  serialized_end=2686,
)

_DEMAND = _descriptor.Descriptor(
  name='Demand',
  full_name='mobile_franchisee_demand.Demand',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_franchisee_demand.Demand.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='mobile_franchisee_demand.Demand.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='mobile_franchisee_demand.Demand.batch_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_franchisee_demand.Demand.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='mobile_franchisee_demand.Demand.type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='mobile_franchisee_demand.Demand.sub_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bus_type', full_name='mobile_franchisee_demand.Demand.bus_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='mobile_franchisee_demand.Demand.received_by', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_code', full_name='mobile_franchisee_demand.Demand.received_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_name', full_name='mobile_franchisee_demand.Demand.received_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_by', full_name='mobile_franchisee_demand.Demand.distribute_by', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='mobile_franchisee_demand.Demand.store_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='mobile_franchisee_demand.Demand.franchisee_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_code', full_name='mobile_franchisee_demand.Demand.franchisee_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_name', full_name='mobile_franchisee_demand.Demand.franchisee_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='mobile_franchisee_demand.Demand.demand_date', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='mobile_franchisee_demand.Demand.arrival_date', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_franchisee_demand.Demand.status', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='mobile_franchisee_demand.Demand.process_status', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='mobile_franchisee_demand.Demand.reject_reason', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_franchisee_demand.Demand.remark', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='mobile_franchisee_demand.Demand.reason', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='mobile_franchisee_demand.Demand.created_at', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='mobile_franchisee_demand.Demand.updated_at', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='mobile_franchisee_demand.Demand.created_by', index=24,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='mobile_franchisee_demand.Demand.created_name', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='mobile_franchisee_demand.Demand.updated_by', index=26,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='mobile_franchisee_demand.Demand.updated_name', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='mobile_franchisee_demand.Demand.review_by', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='has_product', full_name='mobile_franchisee_demand.Demand.has_product', index=29,
      number=30, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price_tax', full_name='mobile_franchisee_demand.Demand.sum_price_tax', index=30,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_tax', full_name='mobile_franchisee_demand.Demand.sum_tax', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pay_amount', full_name='mobile_franchisee_demand.Demand.pay_amount', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='mobile_franchisee_demand.Demand.attachments', index=33,
      number=34, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='mobile_franchisee_demand.Demand.extends', index=34,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orders', full_name='mobile_franchisee_demand.Demand.orders', index=35,
      number=36, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_way', full_name='mobile_franchisee_demand.Demand.payment_way', index=36,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='chain_type', full_name='mobile_franchisee_demand.Demand.chain_type', index=37,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='mobile_franchisee_demand.Demand.is_adjust', index=38,
      number=39, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_amount', full_name='mobile_franchisee_demand.Demand.confirm_amount', index=39,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='mobile_franchisee_demand.Demand.order_type_id', index=40,
      number=41, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_name', full_name='mobile_franchisee_demand.Demand.order_type_name', index=41,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_code', full_name='mobile_franchisee_demand.Demand.order_type_code', index=42,
      number=43, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='mobile_franchisee_demand.Demand.sales_amount', index=43,
      number=44, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_sales_amount', full_name='mobile_franchisee_demand.Demand.confirm_sales_amount', index=44,
      number=45, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refunds', full_name='mobile_franchisee_demand.Demand.refunds', index=45,
      number=46, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receives', full_name='mobile_franchisee_demand.Demand.receives', index=46,
      number=47, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_amount', full_name='mobile_franchisee_demand.Demand.approve_amount', index=47,
      number=48, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_sales_amount', full_name='mobile_franchisee_demand.Demand.approve_sales_amount', index=48,
      number=49, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_time', full_name='mobile_franchisee_demand.Demand.order_type_time', index=49,
      number=50, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_type', full_name='mobile_franchisee_demand.Demand.price_type', index=50,
      number=53, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_DEMAND_ORDER, _DEMAND_REFUND, _DEMAND_RECEIVE, _DEMAND_ORDERTYPETIME, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1100,
  serialized_end=2686,
)


_GETFDEMANDBYIDREQUEST = _descriptor.Descriptor(
  name='GetFDemandByIdRequest',
  full_name='mobile_franchisee_demand.GetFDemandByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='mobile_franchisee_demand.GetFDemandByIdRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2688,
  serialized_end=2730,
)


_GETHISTORYBYIDREQUEST = _descriptor.Descriptor(
  name='GetHistoryByIdRequest',
  full_name='mobile_franchisee_demand.GetHistoryByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='mobile_franchisee_demand.GetHistoryByIdRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2732,
  serialized_end=2774,
)


_HISTORYRESPONSE = _descriptor.Descriptor(
  name='HistoryResponse',
  full_name='mobile_franchisee_demand.HistoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_franchisee_demand.HistoryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_franchisee_demand.HistoryResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2776,
  serialized_end=2857,
)


_HISTORY = _descriptor.Descriptor(
  name='History',
  full_name='mobile_franchisee_demand.History',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_franchisee_demand.History.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_franchisee_demand.History.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='mobile_franchisee_demand.History.created_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='mobile_franchisee_demand.History.created_at', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='mobile_franchisee_demand.History.created_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2859,
  serialized_end=2986,
)


_GETPRODUCTSBYIDREQUEST = _descriptor.Descriptor(
  name='GetProductsByIdRequest',
  full_name='mobile_franchisee_demand.GetProductsByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='mobile_franchisee_demand.GetProductsByIdRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_franchisee_demand.GetProductsByIdRequest.limit', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_franchisee_demand.GetProductsByIdRequest.offset', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='mobile_franchisee_demand.GetProductsByIdRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_franchisee_demand.GetProductsByIdRequest.order', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='mobile_franchisee_demand.GetProductsByIdRequest.sort', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attach_extra', full_name='mobile_franchisee_demand.GetProductsByIdRequest.attach_extra', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2989,
  serialized_end=3137,
)


_PRODUCTSRESPONSE = _descriptor.Descriptor(
  name='ProductsResponse',
  full_name='mobile_franchisee_demand.ProductsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_franchisee_demand.ProductsResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_franchisee_demand.ProductsResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3139,
  serialized_end=3221,
)


_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='mobile_franchisee_demand.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_franchisee_demand.Product.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_franchisee_demand.Product.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='mobile_franchisee_demand.Product.product_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_franchisee_demand.Product.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='org_product_id', full_name='mobile_franchisee_demand.Product.org_product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='org_product_code', full_name='mobile_franchisee_demand.Product.org_product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='org_product_name', full_name='mobile_franchisee_demand.Product.org_product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='mobile_franchisee_demand.Product.category_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='mobile_franchisee_demand.Product.category_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='mobile_franchisee_demand.Product.product_spec', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='mobile_franchisee_demand.Product.unit_id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='mobile_franchisee_demand.Product.unit_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='mobile_franchisee_demand.Product.unit_spec', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='mobile_franchisee_demand.Product.accounting_unit_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='mobile_franchisee_demand.Product.accounting_unit_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='mobile_franchisee_demand.Product.accounting_unit_spec', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='mobile_franchisee_demand.Product.unit_rate', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='mobile_franchisee_demand.Product.quantity', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='mobile_franchisee_demand.Product.accounting_quantity', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_quantity', full_name='mobile_franchisee_demand.Product.min_quantity', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_quantity', full_name='mobile_franchisee_demand.Product.max_quantity', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_quantity', full_name='mobile_franchisee_demand.Product.increment_quantity', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='mobile_franchisee_demand.Product.tax_price', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='mobile_franchisee_demand.Product.cost_price', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='mobile_franchisee_demand.Product.tax_rate', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='mobile_franchisee_demand.Product.amount', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_days', full_name='mobile_franchisee_demand.Product.arrival_days', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='mobile_franchisee_demand.Product.distribution_type', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_by', full_name='mobile_franchisee_demand.Product.distribute_by', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_name', full_name='mobile_franchisee_demand.Product.distribute_name', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_franchisee_demand.Product.status', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='yesterday_sales', full_name='mobile_franchisee_demand.Product.yesterday_sales', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_qty', full_name='mobile_franchisee_demand.Product.inventory_qty', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='on_way_qty', full_name='mobile_franchisee_demand.Product.on_way_qty', index=33,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_delete', full_name='mobile_franchisee_demand.Product.is_delete', index=34,
      number=35, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='mobile_franchisee_demand.Product.created_at', index=35,
      number=36, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='mobile_franchisee_demand.Product.updated_at', index=36,
      number=37, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='mobile_franchisee_demand.Product.created_by', index=37,
      number=38, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='mobile_franchisee_demand.Product.created_name', index=38,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='mobile_franchisee_demand.Product.updated_by', index=39,
      number=40, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='mobile_franchisee_demand.Product.updated_name', index=40,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='mobile_franchisee_demand.Product.demand_id', index=41,
      number=42, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='mobile_franchisee_demand.Product.partner_id', index=42,
      number=43, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_order', full_name='mobile_franchisee_demand.Product.allow_order', index=43,
      number=44, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_quantity', full_name='mobile_franchisee_demand.Product.confirm_quantity', index=44,
      number=45, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_amount', full_name='mobile_franchisee_demand.Product.confirm_amount', index=45,
      number=46, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='mobile_franchisee_demand.Product.extends', index=46,
      number=47, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='mobile_franchisee_demand.Product.storage_type', index=47,
      number=48, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='mobile_franchisee_demand.Product.sales_price', index=48,
      number=49, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='mobile_franchisee_demand.Product.sales_amount', index=49,
      number=50, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_sales_amount', full_name='mobile_franchisee_demand.Product.confirm_sales_amount', index=50,
      number=51, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_quantity', full_name='mobile_franchisee_demand.Product.approve_quantity', index=51,
      number=52, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_amount', full_name='mobile_franchisee_demand.Product.approve_amount', index=52,
      number=53, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_sales_amount', full_name='mobile_franchisee_demand.Product.approve_sales_amount', index=53,
      number=54, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_confirm_qty', full_name='mobile_franchisee_demand.Product.is_confirm_qty', index=54,
      number=55, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_approve_qty', full_name='mobile_franchisee_demand.Product.is_approve_qty', index=55,
      number=56, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation_products', full_name='mobile_franchisee_demand.Product.relation_products', index=56,
      number=57, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='mobile_franchisee_demand.Product.product_type', index=57,
      number=58, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ratio', full_name='mobile_franchisee_demand.Product.ratio', index=58,
      number=59, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='configure', full_name='mobile_franchisee_demand.Product.configure', index=59,
      number=60, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3224,
  serialized_end=4653,
)


_UPDATEPRODUCTREQUEST = _descriptor.Descriptor(
  name='UpdateProductRequest',
  full_name='mobile_franchisee_demand.UpdateProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='mobile_franchisee_demand.UpdateProductRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_franchisee_demand.UpdateProductRequest.remark', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_franchisee_demand.UpdateProductRequest.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='mobile_franchisee_demand.UpdateProductRequest.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4655,
  serialized_end=4781,
)


_UPDATEPRODUCTSRESPONSE = _descriptor.Descriptor(
  name='UpdateProductsResponse',
  full_name='mobile_franchisee_demand.UpdateProductsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='mobile_franchisee_demand.UpdateProductsResponse.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msgs', full_name='mobile_franchisee_demand.UpdateProductsResponse.msgs', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4783,
  serialized_end=4887,
)


_DEALFDEMANDBYIDREQUEST = _descriptor.Descriptor(
  name='DealFDemandByIdRequest',
  full_name='mobile_franchisee_demand.DealFDemandByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_ids', full_name='mobile_franchisee_demand.DealFDemandByIdRequest.demand_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='mobile_franchisee_demand.DealFDemandByIdRequest.action', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_franchisee_demand.DealFDemandByIdRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='mobile_franchisee_demand.DealFDemandByIdRequest.attachments', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='mobile_franchisee_demand.DealFDemandByIdRequest.reject_reason', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4889,
  serialized_end=5009,
)


_DEALFDEMANDBYIDRESPONSE = _descriptor.Descriptor(
  name='DealFDemandByIdResponse',
  full_name='mobile_franchisee_demand.DealFDemandByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='mobile_franchisee_demand.DealFDemandByIdResponse.result', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5011,
  serialized_end=5052,
)


_ADDFDEMANDPRODUCTREQUEST_ADDPRODUCT = _descriptor.Descriptor(
  name='AddProduct',
  full_name='mobile_franchisee_demand.AddFDemandProductRequest.AddProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_franchisee_demand.AddFDemandProductRequest.AddProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_quantity', full_name='mobile_franchisee_demand.AddFDemandProductRequest.AddProduct.approve_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='mobile_franchisee_demand.AddFDemandProductRequest.AddProduct.tax_price', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='mobile_franchisee_demand.AddFDemandProductRequest.AddProduct.tax_rate', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='mobile_franchisee_demand.AddFDemandProductRequest.AddProduct.sales_price', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_days', full_name='mobile_franchisee_demand.AddFDemandProductRequest.AddProduct.arrival_days', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_quantity', full_name='mobile_franchisee_demand.AddFDemandProductRequest.AddProduct.min_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_quantity', full_name='mobile_franchisee_demand.AddFDemandProductRequest.AddProduct.max_quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_quantity', full_name='mobile_franchisee_demand.AddFDemandProductRequest.AddProduct.increment_quantity', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='mobile_franchisee_demand.AddFDemandProductRequest.AddProduct.extends', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation_products', full_name='mobile_franchisee_demand.AddFDemandProductRequest.AddProduct.relation_products', index=10,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ratio', full_name='mobile_franchisee_demand.AddFDemandProductRequest.AddProduct.ratio', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='configure', full_name='mobile_franchisee_demand.AddFDemandProductRequest.AddProduct.configure', index=12,
      number=13, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5184,
  serialized_end=5535,
)

_ADDFDEMANDPRODUCTREQUEST = _descriptor.Descriptor(
  name='AddFDemandProductRequest',
  full_name='mobile_franchisee_demand.AddFDemandProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='mobile_franchisee_demand.AddFDemandProductRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='mobile_franchisee_demand.AddFDemandProductRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_ADDFDEMANDPRODUCTREQUEST_ADDPRODUCT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5055,
  serialized_end=5535,
)


_ADDFDEMANDPRODUCTRESPONSE = _descriptor.Descriptor(
  name='AddFDemandProductResponse',
  full_name='mobile_franchisee_demand.AddFDemandProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='mobile_franchisee_demand.AddFDemandProductResponse.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5537,
  serialized_end=5583,
)


_QUERYFDEMANDREPORTREQUEST = _descriptor.Descriptor(
  name='QueryFDemandReportRequest',
  full_name='mobile_franchisee_demand.QueryFDemandReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='mobile_franchisee_demand.QueryFDemandReportRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='mobile_franchisee_demand.QueryFDemandReportRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_franchisee_demand.QueryFDemandReportRequest.status', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_bys', full_name='mobile_franchisee_demand.QueryFDemandReportRequest.received_bys', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_franchisee_demand.QueryFDemandReportRequest.offset', index=4,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_franchisee_demand.QueryFDemandReportRequest.limit', index=5,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_franchisee_demand.QueryFDemandReportRequest.order', index=6,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='mobile_franchisee_demand.QueryFDemandReportRequest.sort', index=7,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='mobile_franchisee_demand.QueryFDemandReportRequest.include_total', index=8,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5586,
  serialized_end=5828,
)


_DEMANDREPORT = _descriptor.Descriptor(
  name='DemandReport',
  full_name='mobile_franchisee_demand.DemandReport',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='mobile_franchisee_demand.DemandReport.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='mobile_franchisee_demand.DemandReport.received_by', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_code', full_name='mobile_franchisee_demand.DemandReport.received_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_name', full_name='mobile_franchisee_demand.DemandReport.received_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='mobile_franchisee_demand.DemandReport.amount', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_amount', full_name='mobile_franchisee_demand.DemandReport.confirm_amount', index=5,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='mobile_franchisee_demand.DemandReport.sales_amount', index=6,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_sales_amount', full_name='mobile_franchisee_demand.DemandReport.confirm_sales_amount', index=7,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_demand', full_name='mobile_franchisee_demand.DemandReport.total_demand', index=8,
      number=10, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5831,
  serialized_end=6045,
)


_QUERYFDEMANDREPORTRESPONSE = _descriptor.Descriptor(
  name='QueryFDemandReportResponse',
  full_name='mobile_franchisee_demand.QueryFDemandReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_franchisee_demand.QueryFDemandReportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='mobile_franchisee_demand.QueryFDemandReportResponse.total_amount', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_demand', full_name='mobile_franchisee_demand.QueryFDemandReportResponse.total_demand', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_price', full_name='mobile_franchisee_demand.QueryFDemandReportResponse.demand_price', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_franchisee_demand.QueryFDemandReportResponse.total', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='mobile_franchisee_demand.QueryFDemandReportResponse.sales_amount', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6048,
  serialized_end=6233,
)


_ADDSHOPPINGCARTCACHEREQUEST = _descriptor.Descriptor(
  name='AddShoppingCartCacheRequest',
  full_name='mobile_franchisee_demand.AddShoppingCartCacheRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='mobile_franchisee_demand.AddShoppingCartCacheRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='mobile_franchisee_demand.AddShoppingCartCacheRequest.order_type_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='mobile_franchisee_demand.AddShoppingCartCacheRequest.products', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6236,
  serialized_end=6364,
)


_ADDSHOPPINGCARTCACHERESPONSE = _descriptor.Descriptor(
  name='AddShoppingCartCacheResponse',
  full_name='mobile_franchisee_demand.AddShoppingCartCacheResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='mobile_franchisee_demand.AddShoppingCartCacheResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='mobile_franchisee_demand.AddShoppingCartCacheResponse.description', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6366,
  serialized_end=6433,
)


_PRODUCTCACHE = _descriptor.Descriptor(
  name='ProductCache',
  full_name='mobile_franchisee_demand.ProductCache',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_franchisee_demand.ProductCache.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='mobile_franchisee_demand.ProductCache.quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6435,
  serialized_end=6487,
)


_GETSHOPPINGCARTCACHEREQUEST = _descriptor.Descriptor(
  name='GetShoppingCartCacheRequest',
  full_name='mobile_franchisee_demand.GetShoppingCartCacheRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='mobile_franchisee_demand.GetShoppingCartCacheRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='mobile_franchisee_demand.GetShoppingCartCacheRequest.order_type_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6489,
  serialized_end=6559,
)


_GETSHOPPINGCARTCACHERESPONSE = _descriptor.Descriptor(
  name='GetShoppingCartCacheResponse',
  full_name='mobile_franchisee_demand.GetShoppingCartCacheResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='products', full_name='mobile_franchisee_demand.GetShoppingCartCacheResponse.products', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6561,
  serialized_end=6649,
)


_CHECKFDEMANDTODOREQUEST = _descriptor.Descriptor(
  name='CheckFDemandTodoRequest',
  full_name='mobile_franchisee_demand.CheckFDemandTodoRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='mobile_franchisee_demand.CheckFDemandTodoRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bus_date', full_name='mobile_franchisee_demand.CheckFDemandTodoRequest.bus_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='mobile_franchisee_demand.CheckFDemandTodoRequest.store_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6651,
  serialized_end=6759,
)


_TODORECEIPT = _descriptor.Descriptor(
  name='TodoReceipt',
  full_name='mobile_franchisee_demand.TodoReceipt',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_franchisee_demand.TodoReceipt.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_franchisee_demand.TodoReceipt.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_franchisee_demand.TodoReceipt.status', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='mobile_franchisee_demand.TodoReceipt.type', index=3,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6761,
  serialized_end=6830,
)


_CHECKFDEMANDTODORESPONSE = _descriptor.Descriptor(
  name='CheckFDemandTodoResponse',
  full_name='mobile_franchisee_demand.CheckFDemandTodoResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='handler', full_name='mobile_franchisee_demand.CheckFDemandTodoResponse.handler', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving', full_name='mobile_franchisee_demand.CheckFDemandTodoResponse.receiving', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='mobile_franchisee_demand.CheckFDemandTodoResponse.stocktake', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6833,
  serialized_end=6992,
)


_CHECKPRODUCTMESSAGE = _descriptor.Descriptor(
  name='CheckProductMessage',
  full_name='mobile_franchisee_demand.CheckProductMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_franchisee_demand.CheckProductMessage.product_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='mobile_franchisee_demand.CheckProductMessage.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='mobile_franchisee_demand.CheckProductMessage.spec', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='mobile_franchisee_demand.CheckProductMessage.quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_quantity', full_name='mobile_franchisee_demand.CheckProductMessage.min_quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_quantity', full_name='mobile_franchisee_demand.CheckProductMessage.increment_quantity', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_quantity', full_name='mobile_franchisee_demand.CheckProductMessage.max_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit', full_name='mobile_franchisee_demand.CheckProductMessage.unit', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='mobile_franchisee_demand.CheckProductMessage.tax_price', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='mobile_franchisee_demand.CheckProductMessage.amount', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='mobile_franchisee_demand.CheckProductMessage.storage_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_franchisee_demand.CheckProductMessage.id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_franchisee_demand.CheckProductMessage.product_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='mobile_franchisee_demand.CheckProductMessage.tax_rate', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6995,
  serialized_end=7285,
)


_CHECKORDERMESSAGE = _descriptor.Descriptor(
  name='CheckOrderMessage',
  full_name='mobile_franchisee_demand.CheckOrderMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_franchisee_demand.CheckOrderMessage.status', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_franchisee_demand.CheckOrderMessage.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='mobile_franchisee_demand.CheckOrderMessage.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='mobile_franchisee_demand.CheckOrderMessage.order_type_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_name', full_name='mobile_franchisee_demand.CheckOrderMessage.franchisee_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='mobile_franchisee_demand.CheckOrderMessage.amount', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='mobile_franchisee_demand.CheckOrderMessage.demand_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='mobile_franchisee_demand.CheckOrderMessage.arrival_date', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msg', full_name='mobile_franchisee_demand.CheckOrderMessage.msg', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7288,
  serialized_end=7533,
)

_CREATEFDEMANDREQUEST.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEFDEMANDREQUEST.fields_by_name['products'].message_type = _PRODUCT
_LISTFDEMANDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTFDEMANDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTFDEMANDRESPONSE.fields_by_name['rows'].message_type = _DEMAND
_DEMAND_ORDER.containing_type = _DEMAND
_DEMAND_REFUND.containing_type = _DEMAND
_DEMAND_RECEIVE.containing_type = _DEMAND
_DEMAND_ORDERTYPETIME.containing_type = _DEMAND
_DEMAND.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMAND.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMAND.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMAND.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMAND.fields_by_name['orders'].message_type = _DEMAND_ORDER
_DEMAND.fields_by_name['refunds'].message_type = _DEMAND_REFUND
_DEMAND.fields_by_name['receives'].message_type = _DEMAND_RECEIVE
_DEMAND.fields_by_name['order_type_time'].message_type = _DEMAND_ORDERTYPETIME
_HISTORYRESPONSE.fields_by_name['rows'].message_type = _HISTORY
_HISTORY.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCTSRESPONSE.fields_by_name['rows'].message_type = _PRODUCT
_PRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCT.fields_by_name['relation_products'].message_type = _PRODUCT
_UPDATEPRODUCTREQUEST.fields_by_name['products'].message_type = _PRODUCT
_UPDATEPRODUCTSRESPONSE.fields_by_name['msgs'].message_type = _CHECKPRODUCTMESSAGE
_ADDFDEMANDPRODUCTREQUEST_ADDPRODUCT.fields_by_name['relation_products'].message_type = _ADDFDEMANDPRODUCTREQUEST_ADDPRODUCT
_ADDFDEMANDPRODUCTREQUEST_ADDPRODUCT.containing_type = _ADDFDEMANDPRODUCTREQUEST
_ADDFDEMANDPRODUCTREQUEST.fields_by_name['products'].message_type = _ADDFDEMANDPRODUCTREQUEST_ADDPRODUCT
_QUERYFDEMANDREPORTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYFDEMANDREPORTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYFDEMANDREPORTRESPONSE.fields_by_name['rows'].message_type = _DEMANDREPORT
_ADDSHOPPINGCARTCACHEREQUEST.fields_by_name['products'].message_type = _PRODUCTCACHE
_GETSHOPPINGCARTCACHERESPONSE.fields_by_name['products'].message_type = _PRODUCTCACHE
_CHECKFDEMANDTODOREQUEST.fields_by_name['bus_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CHECKFDEMANDTODORESPONSE.fields_by_name['receiving'].message_type = _TODORECEIPT
_CHECKFDEMANDTODORESPONSE.fields_by_name['stocktake'].message_type = _TODORECEIPT
_CHECKORDERMESSAGE.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CHECKORDERMESSAGE.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['CreateFDemandRequest'] = _CREATEFDEMANDREQUEST
DESCRIPTOR.message_types_by_name['CreateFDemandResponse'] = _CREATEFDEMANDRESPONSE
DESCRIPTOR.message_types_by_name['ListFDemandRequest'] = _LISTFDEMANDREQUEST
DESCRIPTOR.message_types_by_name['ListFDemandResponse'] = _LISTFDEMANDRESPONSE
DESCRIPTOR.message_types_by_name['Demand'] = _DEMAND
DESCRIPTOR.message_types_by_name['GetFDemandByIdRequest'] = _GETFDEMANDBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetHistoryByIdRequest'] = _GETHISTORYBYIDREQUEST
DESCRIPTOR.message_types_by_name['HistoryResponse'] = _HISTORYRESPONSE
DESCRIPTOR.message_types_by_name['History'] = _HISTORY
DESCRIPTOR.message_types_by_name['GetProductsByIdRequest'] = _GETPRODUCTSBYIDREQUEST
DESCRIPTOR.message_types_by_name['ProductsResponse'] = _PRODUCTSRESPONSE
DESCRIPTOR.message_types_by_name['Product'] = _PRODUCT
DESCRIPTOR.message_types_by_name['UpdateProductRequest'] = _UPDATEPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['UpdateProductsResponse'] = _UPDATEPRODUCTSRESPONSE
DESCRIPTOR.message_types_by_name['DealFDemandByIdRequest'] = _DEALFDEMANDBYIDREQUEST
DESCRIPTOR.message_types_by_name['DealFDemandByIdResponse'] = _DEALFDEMANDBYIDRESPONSE
DESCRIPTOR.message_types_by_name['AddFDemandProductRequest'] = _ADDFDEMANDPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['AddFDemandProductResponse'] = _ADDFDEMANDPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['QueryFDemandReportRequest'] = _QUERYFDEMANDREPORTREQUEST
DESCRIPTOR.message_types_by_name['DemandReport'] = _DEMANDREPORT
DESCRIPTOR.message_types_by_name['QueryFDemandReportResponse'] = _QUERYFDEMANDREPORTRESPONSE
DESCRIPTOR.message_types_by_name['AddShoppingCartCacheRequest'] = _ADDSHOPPINGCARTCACHEREQUEST
DESCRIPTOR.message_types_by_name['AddShoppingCartCacheResponse'] = _ADDSHOPPINGCARTCACHERESPONSE
DESCRIPTOR.message_types_by_name['ProductCache'] = _PRODUCTCACHE
DESCRIPTOR.message_types_by_name['GetShoppingCartCacheRequest'] = _GETSHOPPINGCARTCACHEREQUEST
DESCRIPTOR.message_types_by_name['GetShoppingCartCacheResponse'] = _GETSHOPPINGCARTCACHERESPONSE
DESCRIPTOR.message_types_by_name['CheckFDemandTodoRequest'] = _CHECKFDEMANDTODOREQUEST
DESCRIPTOR.message_types_by_name['TodoReceipt'] = _TODORECEIPT
DESCRIPTOR.message_types_by_name['CheckFDemandTodoResponse'] = _CHECKFDEMANDTODORESPONSE
DESCRIPTOR.message_types_by_name['CheckProductMessage'] = _CHECKPRODUCTMESSAGE
DESCRIPTOR.message_types_by_name['CheckOrderMessage'] = _CHECKORDERMESSAGE
DESCRIPTOR.enum_types_by_name['Status'] = _STATUS
DESCRIPTOR.enum_types_by_name['Type'] = _TYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CreateFDemandRequest = _reflection.GeneratedProtocolMessageType('CreateFDemandRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEFDEMANDREQUEST,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.CreateFDemandRequest)
  ))
_sym_db.RegisterMessage(CreateFDemandRequest)

CreateFDemandResponse = _reflection.GeneratedProtocolMessageType('CreateFDemandResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEFDEMANDRESPONSE,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.CreateFDemandResponse)
  ))
_sym_db.RegisterMessage(CreateFDemandResponse)

ListFDemandRequest = _reflection.GeneratedProtocolMessageType('ListFDemandRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTFDEMANDREQUEST,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.ListFDemandRequest)
  ))
_sym_db.RegisterMessage(ListFDemandRequest)

ListFDemandResponse = _reflection.GeneratedProtocolMessageType('ListFDemandResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTFDEMANDRESPONSE,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.ListFDemandResponse)
  ))
_sym_db.RegisterMessage(ListFDemandResponse)

Demand = _reflection.GeneratedProtocolMessageType('Demand', (_message.Message,), dict(

  Order = _reflection.GeneratedProtocolMessageType('Order', (_message.Message,), dict(
    DESCRIPTOR = _DEMAND_ORDER,
    __module__ = 'mobile.mobile_franchisee_demand_pb2'
    # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.Demand.Order)
    ))
  ,

  Refund = _reflection.GeneratedProtocolMessageType('Refund', (_message.Message,), dict(
    DESCRIPTOR = _DEMAND_REFUND,
    __module__ = 'mobile.mobile_franchisee_demand_pb2'
    # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.Demand.Refund)
    ))
  ,

  Receive = _reflection.GeneratedProtocolMessageType('Receive', (_message.Message,), dict(
    DESCRIPTOR = _DEMAND_RECEIVE,
    __module__ = 'mobile.mobile_franchisee_demand_pb2'
    # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.Demand.Receive)
    ))
  ,

  OrderTypeTime = _reflection.GeneratedProtocolMessageType('OrderTypeTime', (_message.Message,), dict(
    DESCRIPTOR = _DEMAND_ORDERTYPETIME,
    __module__ = 'mobile.mobile_franchisee_demand_pb2'
    # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.Demand.OrderTypeTime)
    ))
  ,
  DESCRIPTOR = _DEMAND,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.Demand)
  ))
_sym_db.RegisterMessage(Demand)
_sym_db.RegisterMessage(Demand.Order)
_sym_db.RegisterMessage(Demand.Refund)
_sym_db.RegisterMessage(Demand.Receive)
_sym_db.RegisterMessage(Demand.OrderTypeTime)

GetFDemandByIdRequest = _reflection.GeneratedProtocolMessageType('GetFDemandByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETFDEMANDBYIDREQUEST,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.GetFDemandByIdRequest)
  ))
_sym_db.RegisterMessage(GetFDemandByIdRequest)

GetHistoryByIdRequest = _reflection.GeneratedProtocolMessageType('GetHistoryByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETHISTORYBYIDREQUEST,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.GetHistoryByIdRequest)
  ))
_sym_db.RegisterMessage(GetHistoryByIdRequest)

HistoryResponse = _reflection.GeneratedProtocolMessageType('HistoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _HISTORYRESPONSE,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.HistoryResponse)
  ))
_sym_db.RegisterMessage(HistoryResponse)

History = _reflection.GeneratedProtocolMessageType('History', (_message.Message,), dict(
  DESCRIPTOR = _HISTORY,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.History)
  ))
_sym_db.RegisterMessage(History)

GetProductsByIdRequest = _reflection.GeneratedProtocolMessageType('GetProductsByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTSBYIDREQUEST,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.GetProductsByIdRequest)
  ))
_sym_db.RegisterMessage(GetProductsByIdRequest)

ProductsResponse = _reflection.GeneratedProtocolMessageType('ProductsResponse', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTSRESPONSE,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.ProductsResponse)
  ))
_sym_db.RegisterMessage(ProductsResponse)

Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCT,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.Product)
  ))
_sym_db.RegisterMessage(Product)

UpdateProductRequest = _reflection.GeneratedProtocolMessageType('UpdateProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPRODUCTREQUEST,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.UpdateProductRequest)
  ))
_sym_db.RegisterMessage(UpdateProductRequest)

UpdateProductsResponse = _reflection.GeneratedProtocolMessageType('UpdateProductsResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPRODUCTSRESPONSE,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.UpdateProductsResponse)
  ))
_sym_db.RegisterMessage(UpdateProductsResponse)

DealFDemandByIdRequest = _reflection.GeneratedProtocolMessageType('DealFDemandByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _DEALFDEMANDBYIDREQUEST,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.DealFDemandByIdRequest)
  ))
_sym_db.RegisterMessage(DealFDemandByIdRequest)

DealFDemandByIdResponse = _reflection.GeneratedProtocolMessageType('DealFDemandByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _DEALFDEMANDBYIDRESPONSE,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.DealFDemandByIdResponse)
  ))
_sym_db.RegisterMessage(DealFDemandByIdResponse)

AddFDemandProductRequest = _reflection.GeneratedProtocolMessageType('AddFDemandProductRequest', (_message.Message,), dict(

  AddProduct = _reflection.GeneratedProtocolMessageType('AddProduct', (_message.Message,), dict(
    DESCRIPTOR = _ADDFDEMANDPRODUCTREQUEST_ADDPRODUCT,
    __module__ = 'mobile.mobile_franchisee_demand_pb2'
    # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.AddFDemandProductRequest.AddProduct)
    ))
  ,
  DESCRIPTOR = _ADDFDEMANDPRODUCTREQUEST,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.AddFDemandProductRequest)
  ))
_sym_db.RegisterMessage(AddFDemandProductRequest)
_sym_db.RegisterMessage(AddFDemandProductRequest.AddProduct)

AddFDemandProductResponse = _reflection.GeneratedProtocolMessageType('AddFDemandProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _ADDFDEMANDPRODUCTRESPONSE,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.AddFDemandProductResponse)
  ))
_sym_db.RegisterMessage(AddFDemandProductResponse)

QueryFDemandReportRequest = _reflection.GeneratedProtocolMessageType('QueryFDemandReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYFDEMANDREPORTREQUEST,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.QueryFDemandReportRequest)
  ))
_sym_db.RegisterMessage(QueryFDemandReportRequest)

DemandReport = _reflection.GeneratedProtocolMessageType('DemandReport', (_message.Message,), dict(
  DESCRIPTOR = _DEMANDREPORT,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.DemandReport)
  ))
_sym_db.RegisterMessage(DemandReport)

QueryFDemandReportResponse = _reflection.GeneratedProtocolMessageType('QueryFDemandReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYFDEMANDREPORTRESPONSE,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.QueryFDemandReportResponse)
  ))
_sym_db.RegisterMessage(QueryFDemandReportResponse)

AddShoppingCartCacheRequest = _reflection.GeneratedProtocolMessageType('AddShoppingCartCacheRequest', (_message.Message,), dict(
  DESCRIPTOR = _ADDSHOPPINGCARTCACHEREQUEST,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.AddShoppingCartCacheRequest)
  ))
_sym_db.RegisterMessage(AddShoppingCartCacheRequest)

AddShoppingCartCacheResponse = _reflection.GeneratedProtocolMessageType('AddShoppingCartCacheResponse', (_message.Message,), dict(
  DESCRIPTOR = _ADDSHOPPINGCARTCACHERESPONSE,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.AddShoppingCartCacheResponse)
  ))
_sym_db.RegisterMessage(AddShoppingCartCacheResponse)

ProductCache = _reflection.GeneratedProtocolMessageType('ProductCache', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTCACHE,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.ProductCache)
  ))
_sym_db.RegisterMessage(ProductCache)

GetShoppingCartCacheRequest = _reflection.GeneratedProtocolMessageType('GetShoppingCartCacheRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSHOPPINGCARTCACHEREQUEST,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.GetShoppingCartCacheRequest)
  ))
_sym_db.RegisterMessage(GetShoppingCartCacheRequest)

GetShoppingCartCacheResponse = _reflection.GeneratedProtocolMessageType('GetShoppingCartCacheResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSHOPPINGCARTCACHERESPONSE,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.GetShoppingCartCacheResponse)
  ))
_sym_db.RegisterMessage(GetShoppingCartCacheResponse)

CheckFDemandTodoRequest = _reflection.GeneratedProtocolMessageType('CheckFDemandTodoRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHECKFDEMANDTODOREQUEST,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.CheckFDemandTodoRequest)
  ))
_sym_db.RegisterMessage(CheckFDemandTodoRequest)

TodoReceipt = _reflection.GeneratedProtocolMessageType('TodoReceipt', (_message.Message,), dict(
  DESCRIPTOR = _TODORECEIPT,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.TodoReceipt)
  ))
_sym_db.RegisterMessage(TodoReceipt)

CheckFDemandTodoResponse = _reflection.GeneratedProtocolMessageType('CheckFDemandTodoResponse', (_message.Message,), dict(
  DESCRIPTOR = _CHECKFDEMANDTODORESPONSE,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.CheckFDemandTodoResponse)
  ))
_sym_db.RegisterMessage(CheckFDemandTodoResponse)

CheckProductMessage = _reflection.GeneratedProtocolMessageType('CheckProductMessage', (_message.Message,), dict(
  DESCRIPTOR = _CHECKPRODUCTMESSAGE,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.CheckProductMessage)
  ))
_sym_db.RegisterMessage(CheckProductMessage)

CheckOrderMessage = _reflection.GeneratedProtocolMessageType('CheckOrderMessage', (_message.Message,), dict(
  DESCRIPTOR = _CHECKORDERMESSAGE,
  __module__ = 'mobile.mobile_franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_demand.CheckOrderMessage)
  ))
_sym_db.RegisterMessage(CheckOrderMessage)



_MOBILEFRANCHISEEDEMANDSERVICE = _descriptor.ServiceDescriptor(
  name='MobileFranchiseeDemandService',
  full_name='mobile_franchisee_demand.MobileFranchiseeDemandService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=7715,
  serialized_end=9957,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateFDemand',
    full_name='mobile_franchisee_demand.MobileFranchiseeDemandService.CreateFDemand',
    index=0,
    containing_service=None,
    input_type=_CREATEFDEMANDREQUEST,
    output_type=_CREATEFDEMANDRESPONSE,
    serialized_options=_b('\202\323\344\223\0023\"./api/v2/supply/mobile/franchisee/demand/create:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListFDemand',
    full_name='mobile_franchisee_demand.MobileFranchiseeDemandService.ListFDemand',
    index=1,
    containing_service=None,
    input_type=_LISTFDEMANDREQUEST,
    output_type=_LISTFDEMANDRESPONSE,
    serialized_options=_b('\202\323\344\223\002)\022\'/api/v2/supply/mobile/franchisee/demand'),
  ),
  _descriptor.MethodDescriptor(
    name='GetFDemandById',
    full_name='mobile_franchisee_demand.MobileFranchiseeDemandService.GetFDemandById',
    index=2,
    containing_service=None,
    input_type=_GETFDEMANDBYIDREQUEST,
    output_type=_DEMAND,
    serialized_options=_b('\202\323\344\223\0025\0223/api/v2/supply/mobile/franchisee/demand/{demand_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetHistoryById',
    full_name='mobile_franchisee_demand.MobileFranchiseeDemandService.GetHistoryById',
    index=3,
    containing_service=None,
    input_type=_GETHISTORYBYIDREQUEST,
    output_type=_HISTORYRESPONSE,
    serialized_options=_b('\202\323\344\223\002=\022;/api/v2/supply/mobile/franchisee/demand/{demand_id}/history'),
  ),
  _descriptor.MethodDescriptor(
    name='GetProductsById',
    full_name='mobile_franchisee_demand.MobileFranchiseeDemandService.GetProductsById',
    index=4,
    containing_service=None,
    input_type=_GETPRODUCTSBYIDREQUEST,
    output_type=_PRODUCTSRESPONSE,
    serialized_options=_b('\202\323\344\223\002=\022;/api/v2/supply/mobile/franchisee/demand/{demand_id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateProducts',
    full_name='mobile_franchisee_demand.MobileFranchiseeDemandService.UpdateProducts',
    index=5,
    containing_service=None,
    input_type=_UPDATEPRODUCTREQUEST,
    output_type=_UPDATEPRODUCTSRESPONSE,
    serialized_options=_b('\202\323\344\223\002@\";/api/v2/supply/mobile/franchisee/demand/{demand_id}/product:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DealFDemandById',
    full_name='mobile_franchisee_demand.MobileFranchiseeDemandService.DealFDemandById',
    index=6,
    containing_service=None,
    input_type=_DEALFDEMANDBYIDREQUEST,
    output_type=_DEALFDEMANDBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002:\"5/api/v2/supply/mobile/franchisee/demand/deal/{action}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='AddFDemandProduct',
    full_name='mobile_franchisee_demand.MobileFranchiseeDemandService.AddFDemandProduct',
    index=7,
    containing_service=None,
    input_type=_ADDFDEMANDPRODUCTREQUEST,
    output_type=_ADDFDEMANDPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\"3/api/v2/supply/mobile/franchisee/demand/product/add:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryFDemandReport',
    full_name='mobile_franchisee_demand.MobileFranchiseeDemandService.QueryFDemandReport',
    index=8,
    containing_service=None,
    input_type=_QUERYFDEMANDREPORTREQUEST,
    output_type=_QUERYFDEMANDREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\0023\"./api/v2/supply/mobile/franchisee/demand/report:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='AddShoppingCartCache',
    full_name='mobile_franchisee_demand.MobileFranchiseeDemandService.AddShoppingCartCache',
    index=9,
    containing_service=None,
    input_type=_ADDSHOPPINGCARTCACHEREQUEST,
    output_type=_ADDSHOPPINGCARTCACHERESPONSE,
    serialized_options=_b('\202\323\344\223\002@\";/api/v2/supply/mobile/franchisee/demand/shopping-cart/cache:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetShoppingCartCache',
    full_name='mobile_franchisee_demand.MobileFranchiseeDemandService.GetShoppingCartCache',
    index=10,
    containing_service=None,
    input_type=_GETSHOPPINGCARTCACHEREQUEST,
    output_type=_GETSHOPPINGCARTCACHERESPONSE,
    serialized_options=_b('\202\323\344\223\002=\022;/api/v2/supply/mobile/franchisee/demand/shopping-cart/cache'),
  ),
  _descriptor.MethodDescriptor(
    name='CheckFDemandTodo',
    full_name='mobile_franchisee_demand.MobileFranchiseeDemandService.CheckFDemandTodo',
    index=11,
    containing_service=None,
    input_type=_CHECKFDEMANDTODOREQUEST,
    output_type=_CHECKFDEMANDTODORESPONSE,
    serialized_options=_b('\202\323\344\223\0024\0222/api/v2/supply/mobile/franchisee/demand/todo/check'),
  ),
])
_sym_db.RegisterServiceDescriptor(_MOBILEFRANCHISEEDEMANDSERVICE)

DESCRIPTOR.services_by_name['MobileFranchiseeDemandService'] = _MOBILEFRANCHISEEDEMANDSERVICE

# @@protoc_insertion_point(module_scope)
