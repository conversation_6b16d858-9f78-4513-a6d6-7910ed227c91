# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mobile/mobile_self_picking.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='mobile/mobile_self_picking.proto',
  package='mobile_self_picking',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n mobile/mobile_self_picking.proto\x12\x13mobile_self_picking\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xed\x01\n\x1d\x43reateStoreSelfPickingRequest\x12.\n\norder_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tbranch_id\x18\x03 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x06 \x01(\t\x12\x0e\n\x06remark\x18\x07 \x01(\t\x12\x0e\n\x06reason\x18\x08 \x01(\t\x12\x12\n\nrequest_id\x18\n \x01(\x04\x12+\n\x05items\x18\x1a \x03(\x0b\x32\x1c.mobile_self_picking.Product\x12\x13\n\x0b\x61ttachments\x18\x0b \x03(\t\"\xad\x03\n\x07Product\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0f\n\x07main_id\x18\x02 \x01(\x04\x12\x12\n\nproduct_id\x18\x03 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x04 \x01(\t\x12\x14\n\x0cproduct_name\x18\x05 \x01(\t\x12\x0f\n\x07unit_id\x18\x07 \x01(\x04\x12\x11\n\tunit_name\x18\x08 \x01(\t\x12\x12\n\nmodel_code\x18\t \x01(\t\x12\x12\n\nmodel_name\x18\n \x01(\t\x12\x10\n\x08quantity\x18\x0c \x01(\x01\x12\r\n\x05price\x18\r \x01(\x01\x12\x0e\n\x06\x61mount\x18\x0e \x01(\x01\x12\x12\n\npartner_id\x18\x10 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x12 \x01(\t\x12\x14\n\x0cupdated_name\x18\x14 \x01(\t\x12.\n\ncreated_at\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12(\n\x05units\x18\x17 \x03(\x0b\x32\x19.mobile_self_picking.Unit\"D\n\x1e\x43reateStoreSelfPickingResponse\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\"\xc7\x02\n\x1bListStoreSelfPickingRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nbranch_ids\x18\x03 \x03(\x04\x12\x13\n\x0b\x62ranch_type\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x0e\n\x06reason\x18\x06 \x01(\t\x12\x13\n\x0bproduct_ids\x18\x07 \x03(\x04\x12\x0e\n\x06status\x18\t \x03(\t\x12\r\n\x05limit\x18\n \x01(\x03\x12\x0e\n\x06offset\x18\x0b \x01(\x04\x12\x15\n\rinclude_total\x18\x0c \x01(\x08\x12\r\n\x05order\x18\r \x01(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\x0b\n\x03ids\x18\x0f \x03(\x04\"\x9e\x03\n\x0bSelfPicking\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\n \x01(\t\x12\x0c\n\x04\x63ode\x18\x0b \x01(\t\x12.\n\norder_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tbranch_id\x18\x0e \x01(\x04\x12\x13\n\x0b\x62ranch_code\x18\x0f \x01(\t\x12\x13\n\x0b\x62ranch_name\x18\x10 \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x12 \x01(\t\x12\x0e\n\x06reason\x18\x13 \x01(\t\x12\x0e\n\x06remark\x18\x14 \x01(\t\x12\x14\n\x0ctotal_amount\x18\x15 \x01(\x01\x12\x13\n\x0breason_name\x18\x17 \x01(\t\x12\x0f\n\x07\x65xtends\x18\x18 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x19 \x03(\t\"]\n\x1cListStoreSelfPickingResponse\x12.\n\x04rows\x18\x01 \x03(\x0b\x32 .mobile_self_picking.SelfPicking\x12\r\n\x05total\x18\x02 \x01(\x04\"6\n GetStoreSelfPickingDetailRequest\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\"\xfc\x03\n!GetStoreSelfPickingDetailResponse\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x04 \x01(\t\x12\x14\n\x0cupdated_name\x18\x06 \x01(\t\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\n \x01(\t\x12\x0c\n\x04\x63ode\x18\x0b \x01(\t\x12.\n\norder_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tbranch_id\x18\x0e \x01(\x04\x12\x13\n\x0b\x62ranch_code\x18\x0f \x01(\t\x12\x13\n\x0b\x62ranch_name\x18\x10 \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x12 \x01(\t\x12\x0e\n\x06reason\x18\x13 \x01(\t\x12\x0e\n\x06remark\x18\x14 \x01(\t\x12\x14\n\x0ctotal_amount\x18\x15 \x01(\x01\x12+\n\x05items\x18\" \x03(\x0b\x32\x1c.mobile_self_picking.Product\x12\x13\n\x0b\x61ttachments\x18\x16 \x03(\t\x12\x13\n\x0breason_name\x18\x17 \x01(\t\"\xad\x02\n\x1dUpdateStoreSelfPickingRequest\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12.\n\norder_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tbranch_id\x18\x0e \x01(\x04\x12\x13\n\x0b\x62ranch_code\x18\x0f \x01(\t\x12\x13\n\x0b\x62ranch_name\x18\x10 \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x12 \x01(\t\x12\x0e\n\x06reason\x18\x13 \x01(\t\x12\x0e\n\x06remark\x18\x14 \x01(\t\x12\x14\n\x0ctotal_amount\x18\x15 \x01(\x01\x12\x13\n\x0b\x61ttachments\x18\x16 \x03(\t\x12+\n\x05items\x18\" \x03(\x0b\x32\x1c.mobile_self_picking.Product\"D\n\x1eUpdateStoreSelfPickingResponse\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\"\xc5\x01\n!GetPickingProductByStoreIdRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x15\n\rinclude_total\x18\x02 \x01(\x08\x12\r\n\x05order\x18\x03 \x01(\t\x12\x0e\n\x06offset\x18\x04 \x01(\r\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x0c\n\x04sort\x18\n \x01(\t\x12\x15\n\rsearch_fields\x18\x06 \x01(\t\x12\x0e\n\x06search\x18\x07 \x01(\t\x12\x14\n\x0c\x63\x61tegory_ids\x18\x08 \x03(\x04\"k\n\"GetPickingProductByStoreIdResponse\x12\x36\n\x04rows\x18\x01 \x03(\x0b\x32(.mobile_self_picking.AllowPickingProduct\x12\r\n\x05total\x18\x02 \x01(\r\"\x88\x02\n\x13\x41llowPickingProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x04 \x01(\x04\x12\x15\n\rcategory_name\x18\x05 \x01(\t\x12(\n\x05units\x18\x06 \x03(\x0b\x32\x19.mobile_self_picking.Unit\x12\x0f\n\x07\x62\x61rcode\x18\x07 \x03(\t\x12\x0c\n\x04spec\x18\x08 \x01(\t\x12\x14\n\x0cproduct_type\x18\t \x01(\t\x12\x12\n\nmodel_code\x18\x12 \x01(\t\x12\x12\n\nmodel_name\x18\x13 \x01(\t\"\x92\x02\n\x04Unit\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x0f\n\x07tp_code\x18\x06 \x01(\t\x12\x0f\n\x07updated\x18\x07 \x01(\t\x12\x0c\n\x04rate\x18\x08 \x01(\x01\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\t \x01(\x08\x12\r\n\x05order\x18\n \x01(\x08\x12\x10\n\x08purchase\x18\x0b \x01(\x08\x12\r\n\x05sales\x18\x0c \x01(\x08\x12\x11\n\tstocktake\x18\r \x01(\x08\x12\x0b\n\x03\x62om\x18\x0e \x01(\x08\x12\x19\n\x11\x64\x65\x66\x61ult_stocktake\x18\x0f \x01(\x08\x12\x10\n\x08transfer\x18\x10 \x01(\x08\"T\n\x1e\x43hangeSelfPickingStatusRequest\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x0e\n\x06remark\x18\x03 \x01(\t\"E\n\x1f\x43hangeSelfPickingStatusResponse\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\".\n\x18GetSelfPickingLogRequest\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\"R\n\x19GetSelfPickingLogResponse\x12&\n\x04rows\x18\x01 \x03(\x0b\x32\x18.mobile_self_picking.Log\x12\r\n\x05total\x18\x02 \x01(\x04\"\x8b\x01\n\x03Log\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x12\n\ncreated_by\x18\x03 \x01(\x04\x12.\n\ncreated_at\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06reason\x18\x05 \x01(\t\x12\x14\n\x0c\x63reated_name\x18\x06 \x01(\t2\xdb\n\n\x16MobileStoreSelfPicking\x12\xb7\x01\n\x16\x43reateStoreSelfPicking\x12\x32.mobile_self_picking.CreateStoreSelfPickingRequest\x1a\x33.mobile_self_picking.CreateStoreSelfPickingResponse\"4\x82\xd3\xe4\x93\x02.\")/api/v2/supply/mobile/self/picking/create:\x01*\x12\xac\x01\n\x14ListStoreSelfPicking\x12\x30.mobile_self_picking.ListStoreSelfPickingRequest\x1a\x31.mobile_self_picking.ListStoreSelfPickingResponse\"/\x82\xd3\xe4\x93\x02)\x12\'/api/v2/supply/mobile/self/picking/list\x12\xca\x01\n\x19GetStoreSelfPickingDetail\x12\x35.mobile_self_picking.GetStoreSelfPickingDetailRequest\x1a\x36.mobile_self_picking.GetStoreSelfPickingDetailResponse\">\x82\xd3\xe4\x93\x02\x38\x12\x36/api/v2/supply/mobile/self/picking/{receipt_id}/detail\x12\xb7\x01\n\x16UpdateStoreSelfPicking\x12\x32.mobile_self_picking.UpdateStoreSelfPickingRequest\x1a\x33.mobile_self_picking.UpdateStoreSelfPickingResponse\"4\x82\xd3\xe4\x93\x02.\")/api/v2/supply/mobile/self/picking/update:\x01*\x12\xc9\x01\n\x17\x43hangeSelfPickingStatus\x12\x33.mobile_self_picking.ChangeSelfPickingStatusRequest\x1a\x34.mobile_self_picking.ChangeSelfPickingStatusResponse\"C\x82\xd3\xe4\x93\x02=\"8/api/v2/supply/mobile/self/picking/{status}/{receipt_id}:\x01*\x12\xd2\x01\n\x1aGetPickingProductByStoreId\x12\x36.mobile_self_picking.GetPickingProductByStoreIdRequest\x1a\x37.mobile_self_picking.GetPickingProductByStoreIdResponse\"C\x82\xd3\xe4\x93\x02=\x12;/api/v2/supply/mobile/self/picking/store/{store_id}/product\x12\xaf\x01\n\x11GetSelfPickingLog\x12-.mobile_self_picking.GetSelfPickingLogRequest\x1a..mobile_self_picking.GetSelfPickingLogResponse\";\x82\xd3\xe4\x93\x02\x35\x12\x33/api/v2/supply/mobile/self/picking/{receipt_id}/logb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_CREATESTORESELFPICKINGREQUEST = _descriptor.Descriptor(
  name='CreateStoreSelfPickingRequest',
  full_name='mobile_self_picking.CreateStoreSelfPickingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_date', full_name='mobile_self_picking.CreateStoreSelfPickingRequest.order_date', index=0,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='mobile_self_picking.CreateStoreSelfPickingRequest.branch_id', index=1,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_self_picking.CreateStoreSelfPickingRequest.branch_type', index=2,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_self_picking.CreateStoreSelfPickingRequest.remark', index=3,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='mobile_self_picking.CreateStoreSelfPickingRequest.reason', index=4,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='mobile_self_picking.CreateStoreSelfPickingRequest.request_id', index=5,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='items', full_name='mobile_self_picking.CreateStoreSelfPickingRequest.items', index=6,
      number=26, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='mobile_self_picking.CreateStoreSelfPickingRequest.attachments', index=7,
      number=11, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=121,
  serialized_end=358,
)


_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='mobile_self_picking.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_self_picking.Product.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_id', full_name='mobile_self_picking.Product.main_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_self_picking.Product.product_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='mobile_self_picking.Product.product_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_self_picking.Product.product_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='mobile_self_picking.Product.unit_id', index=5,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='mobile_self_picking.Product.unit_name', index=6,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_code', full_name='mobile_self_picking.Product.model_code', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_name', full_name='mobile_self_picking.Product.model_name', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='mobile_self_picking.Product.quantity', index=9,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='mobile_self_picking.Product.price', index=10,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='mobile_self_picking.Product.amount', index=11,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='mobile_self_picking.Product.partner_id', index=12,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='mobile_self_picking.Product.created_name', index=13,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='mobile_self_picking.Product.updated_name', index=14,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='mobile_self_picking.Product.created_at', index=15,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='mobile_self_picking.Product.updated_at', index=16,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='mobile_self_picking.Product.units', index=17,
      number=23, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=361,
  serialized_end=790,
)


_CREATESTORESELFPICKINGRESPONSE = _descriptor.Descriptor(
  name='CreateStoreSelfPickingResponse',
  full_name='mobile_self_picking.CreateStoreSelfPickingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='mobile_self_picking.CreateStoreSelfPickingResponse.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='mobile_self_picking.CreateStoreSelfPickingResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=792,
  serialized_end=860,
)


_LISTSTORESELFPICKINGREQUEST = _descriptor.Descriptor(
  name='ListStoreSelfPickingRequest',
  full_name='mobile_self_picking.ListStoreSelfPickingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='mobile_self_picking.ListStoreSelfPickingRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='mobile_self_picking.ListStoreSelfPickingRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='mobile_self_picking.ListStoreSelfPickingRequest.branch_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_self_picking.ListStoreSelfPickingRequest.branch_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_self_picking.ListStoreSelfPickingRequest.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='mobile_self_picking.ListStoreSelfPickingRequest.reason', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='mobile_self_picking.ListStoreSelfPickingRequest.product_ids', index=6,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_self_picking.ListStoreSelfPickingRequest.status', index=7,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_self_picking.ListStoreSelfPickingRequest.limit', index=8,
      number=10, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_self_picking.ListStoreSelfPickingRequest.offset', index=9,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='mobile_self_picking.ListStoreSelfPickingRequest.include_total', index=10,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_self_picking.ListStoreSelfPickingRequest.order', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='mobile_self_picking.ListStoreSelfPickingRequest.sort', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='mobile_self_picking.ListStoreSelfPickingRequest.ids', index=13,
      number=15, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=863,
  serialized_end=1190,
)


_SELFPICKING = _descriptor.Descriptor(
  name='SelfPicking',
  full_name='mobile_self_picking.SelfPicking',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_self_picking.SelfPicking.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='mobile_self_picking.SelfPicking.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='mobile_self_picking.SelfPicking.created_at', index=2,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='mobile_self_picking.SelfPicking.updated_at', index=3,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_self_picking.SelfPicking.status', index=4,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_self_picking.SelfPicking.code', index=5,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date', full_name='mobile_self_picking.SelfPicking.order_date', index=6,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='mobile_self_picking.SelfPicking.branch_id', index=7,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_code', full_name='mobile_self_picking.SelfPicking.branch_code', index=8,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='mobile_self_picking.SelfPicking.branch_name', index=9,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_self_picking.SelfPicking.branch_type', index=10,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='mobile_self_picking.SelfPicking.reason', index=11,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_self_picking.SelfPicking.remark', index=12,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='mobile_self_picking.SelfPicking.total_amount', index=13,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_name', full_name='mobile_self_picking.SelfPicking.reason_name', index=14,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='mobile_self_picking.SelfPicking.extends', index=15,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='mobile_self_picking.SelfPicking.attachments', index=16,
      number=25, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1193,
  serialized_end=1607,
)


_LISTSTORESELFPICKINGRESPONSE = _descriptor.Descriptor(
  name='ListStoreSelfPickingResponse',
  full_name='mobile_self_picking.ListStoreSelfPickingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_self_picking.ListStoreSelfPickingResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_self_picking.ListStoreSelfPickingResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1609,
  serialized_end=1702,
)


_GETSTORESELFPICKINGDETAILREQUEST = _descriptor.Descriptor(
  name='GetStoreSelfPickingDetailRequest',
  full_name='mobile_self_picking.GetStoreSelfPickingDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='mobile_self_picking.GetStoreSelfPickingDetailRequest.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1704,
  serialized_end=1758,
)


_GETSTORESELFPICKINGDETAILRESPONSE = _descriptor.Descriptor(
  name='GetStoreSelfPickingDetailResponse',
  full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse.created_name', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse.updated_name', index=3,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse.created_at', index=4,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse.updated_at', index=5,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse.status', index=6,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse.code', index=7,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date', full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse.order_date', index=8,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse.branch_id', index=9,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_code', full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse.branch_code', index=10,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse.branch_name', index=11,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse.branch_type', index=12,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse.reason', index=13,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse.remark', index=14,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse.total_amount', index=15,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='items', full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse.items', index=16,
      number=34, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse.attachments', index=17,
      number=22, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_name', full_name='mobile_self_picking.GetStoreSelfPickingDetailResponse.reason_name', index=18,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1761,
  serialized_end=2269,
)


_UPDATESTORESELFPICKINGREQUEST = _descriptor.Descriptor(
  name='UpdateStoreSelfPickingRequest',
  full_name='mobile_self_picking.UpdateStoreSelfPickingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='mobile_self_picking.UpdateStoreSelfPickingRequest.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date', full_name='mobile_self_picking.UpdateStoreSelfPickingRequest.order_date', index=1,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='mobile_self_picking.UpdateStoreSelfPickingRequest.branch_id', index=2,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_code', full_name='mobile_self_picking.UpdateStoreSelfPickingRequest.branch_code', index=3,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='mobile_self_picking.UpdateStoreSelfPickingRequest.branch_name', index=4,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_self_picking.UpdateStoreSelfPickingRequest.branch_type', index=5,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='mobile_self_picking.UpdateStoreSelfPickingRequest.reason', index=6,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_self_picking.UpdateStoreSelfPickingRequest.remark', index=7,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='mobile_self_picking.UpdateStoreSelfPickingRequest.total_amount', index=8,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='mobile_self_picking.UpdateStoreSelfPickingRequest.attachments', index=9,
      number=22, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='items', full_name='mobile_self_picking.UpdateStoreSelfPickingRequest.items', index=10,
      number=34, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2272,
  serialized_end=2573,
)


_UPDATESTORESELFPICKINGRESPONSE = _descriptor.Descriptor(
  name='UpdateStoreSelfPickingResponse',
  full_name='mobile_self_picking.UpdateStoreSelfPickingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='mobile_self_picking.UpdateStoreSelfPickingResponse.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='mobile_self_picking.UpdateStoreSelfPickingResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2575,
  serialized_end=2643,
)


_GETPICKINGPRODUCTBYSTOREIDREQUEST = _descriptor.Descriptor(
  name='GetPickingProductByStoreIdRequest',
  full_name='mobile_self_picking.GetPickingProductByStoreIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='mobile_self_picking.GetPickingProductByStoreIdRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='mobile_self_picking.GetPickingProductByStoreIdRequest.include_total', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_self_picking.GetPickingProductByStoreIdRequest.order', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_self_picking.GetPickingProductByStoreIdRequest.offset', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_self_picking.GetPickingProductByStoreIdRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='mobile_self_picking.GetPickingProductByStoreIdRequest.sort', index=5,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='mobile_self_picking.GetPickingProductByStoreIdRequest.search_fields', index=6,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='mobile_self_picking.GetPickingProductByStoreIdRequest.search', index=7,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='mobile_self_picking.GetPickingProductByStoreIdRequest.category_ids', index=8,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2646,
  serialized_end=2843,
)


_GETPICKINGPRODUCTBYSTOREIDRESPONSE = _descriptor.Descriptor(
  name='GetPickingProductByStoreIdResponse',
  full_name='mobile_self_picking.GetPickingProductByStoreIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_self_picking.GetPickingProductByStoreIdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_self_picking.GetPickingProductByStoreIdResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2845,
  serialized_end=2952,
)


_ALLOWPICKINGPRODUCT = _descriptor.Descriptor(
  name='AllowPickingProduct',
  full_name='mobile_self_picking.AllowPickingProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_self_picking.AllowPickingProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='mobile_self_picking.AllowPickingProduct.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_self_picking.AllowPickingProduct.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='mobile_self_picking.AllowPickingProduct.category_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='mobile_self_picking.AllowPickingProduct.category_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='mobile_self_picking.AllowPickingProduct.units', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='barcode', full_name='mobile_self_picking.AllowPickingProduct.barcode', index=6,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='mobile_self_picking.AllowPickingProduct.spec', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='mobile_self_picking.AllowPickingProduct.product_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_code', full_name='mobile_self_picking.AllowPickingProduct.model_code', index=9,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_name', full_name='mobile_self_picking.AllowPickingProduct.model_name', index=10,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2955,
  serialized_end=3219,
)


_UNIT = _descriptor.Descriptor(
  name='Unit',
  full_name='mobile_self_picking.Unit',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_self_picking.Unit.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='mobile_self_picking.Unit.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='mobile_self_picking.Unit.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='mobile_self_picking.Unit.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_self_picking.Unit.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tp_code', full_name='mobile_self_picking.Unit.tp_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='mobile_self_picking.Unit.updated', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='mobile_self_picking.Unit.rate', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default', full_name='mobile_self_picking.Unit.default', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_self_picking.Unit.order', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase', full_name='mobile_self_picking.Unit.purchase', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales', full_name='mobile_self_picking.Unit.sales', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='mobile_self_picking.Unit.stocktake', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom', full_name='mobile_self_picking.Unit.bom', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default_stocktake', full_name='mobile_self_picking.Unit.default_stocktake', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer', full_name='mobile_self_picking.Unit.transfer', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3222,
  serialized_end=3496,
)


_CHANGESELFPICKINGSTATUSREQUEST = _descriptor.Descriptor(
  name='ChangeSelfPickingStatusRequest',
  full_name='mobile_self_picking.ChangeSelfPickingStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='mobile_self_picking.ChangeSelfPickingStatusRequest.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_self_picking.ChangeSelfPickingStatusRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_self_picking.ChangeSelfPickingStatusRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3498,
  serialized_end=3582,
)


_CHANGESELFPICKINGSTATUSRESPONSE = _descriptor.Descriptor(
  name='ChangeSelfPickingStatusResponse',
  full_name='mobile_self_picking.ChangeSelfPickingStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='mobile_self_picking.ChangeSelfPickingStatusResponse.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='mobile_self_picking.ChangeSelfPickingStatusResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3584,
  serialized_end=3653,
)


_GETSELFPICKINGLOGREQUEST = _descriptor.Descriptor(
  name='GetSelfPickingLogRequest',
  full_name='mobile_self_picking.GetSelfPickingLogRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='mobile_self_picking.GetSelfPickingLogRequest.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3655,
  serialized_end=3701,
)


_GETSELFPICKINGLOGRESPONSE = _descriptor.Descriptor(
  name='GetSelfPickingLogResponse',
  full_name='mobile_self_picking.GetSelfPickingLogResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_self_picking.GetSelfPickingLogResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_self_picking.GetSelfPickingLogResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3703,
  serialized_end=3785,
)


_LOG = _descriptor.Descriptor(
  name='Log',
  full_name='mobile_self_picking.Log',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_self_picking.Log.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_self_picking.Log.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='mobile_self_picking.Log.created_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='mobile_self_picking.Log.created_at', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='mobile_self_picking.Log.reason', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='mobile_self_picking.Log.created_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3788,
  serialized_end=3927,
)

_CREATESTORESELFPICKINGREQUEST.fields_by_name['order_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATESTORESELFPICKINGREQUEST.fields_by_name['items'].message_type = _PRODUCT
_PRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCT.fields_by_name['units'].message_type = _UNIT
_LISTSTORESELFPICKINGREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTSTORESELFPICKINGREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SELFPICKING.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SELFPICKING.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SELFPICKING.fields_by_name['order_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTSTORESELFPICKINGRESPONSE.fields_by_name['rows'].message_type = _SELFPICKING
_GETSTORESELFPICKINGDETAILRESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTORESELFPICKINGDETAILRESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTORESELFPICKINGDETAILRESPONSE.fields_by_name['order_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTORESELFPICKINGDETAILRESPONSE.fields_by_name['items'].message_type = _PRODUCT
_UPDATESTORESELFPICKINGREQUEST.fields_by_name['order_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATESTORESELFPICKINGREQUEST.fields_by_name['items'].message_type = _PRODUCT
_GETPICKINGPRODUCTBYSTOREIDRESPONSE.fields_by_name['rows'].message_type = _ALLOWPICKINGPRODUCT
_ALLOWPICKINGPRODUCT.fields_by_name['units'].message_type = _UNIT
_GETSELFPICKINGLOGRESPONSE.fields_by_name['rows'].message_type = _LOG
_LOG.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['CreateStoreSelfPickingRequest'] = _CREATESTORESELFPICKINGREQUEST
DESCRIPTOR.message_types_by_name['Product'] = _PRODUCT
DESCRIPTOR.message_types_by_name['CreateStoreSelfPickingResponse'] = _CREATESTORESELFPICKINGRESPONSE
DESCRIPTOR.message_types_by_name['ListStoreSelfPickingRequest'] = _LISTSTORESELFPICKINGREQUEST
DESCRIPTOR.message_types_by_name['SelfPicking'] = _SELFPICKING
DESCRIPTOR.message_types_by_name['ListStoreSelfPickingResponse'] = _LISTSTORESELFPICKINGRESPONSE
DESCRIPTOR.message_types_by_name['GetStoreSelfPickingDetailRequest'] = _GETSTORESELFPICKINGDETAILREQUEST
DESCRIPTOR.message_types_by_name['GetStoreSelfPickingDetailResponse'] = _GETSTORESELFPICKINGDETAILRESPONSE
DESCRIPTOR.message_types_by_name['UpdateStoreSelfPickingRequest'] = _UPDATESTORESELFPICKINGREQUEST
DESCRIPTOR.message_types_by_name['UpdateStoreSelfPickingResponse'] = _UPDATESTORESELFPICKINGRESPONSE
DESCRIPTOR.message_types_by_name['GetPickingProductByStoreIdRequest'] = _GETPICKINGPRODUCTBYSTOREIDREQUEST
DESCRIPTOR.message_types_by_name['GetPickingProductByStoreIdResponse'] = _GETPICKINGPRODUCTBYSTOREIDRESPONSE
DESCRIPTOR.message_types_by_name['AllowPickingProduct'] = _ALLOWPICKINGPRODUCT
DESCRIPTOR.message_types_by_name['Unit'] = _UNIT
DESCRIPTOR.message_types_by_name['ChangeSelfPickingStatusRequest'] = _CHANGESELFPICKINGSTATUSREQUEST
DESCRIPTOR.message_types_by_name['ChangeSelfPickingStatusResponse'] = _CHANGESELFPICKINGSTATUSRESPONSE
DESCRIPTOR.message_types_by_name['GetSelfPickingLogRequest'] = _GETSELFPICKINGLOGREQUEST
DESCRIPTOR.message_types_by_name['GetSelfPickingLogResponse'] = _GETSELFPICKINGLOGRESPONSE
DESCRIPTOR.message_types_by_name['Log'] = _LOG
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CreateStoreSelfPickingRequest = _reflection.GeneratedProtocolMessageType('CreateStoreSelfPickingRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATESTORESELFPICKINGREQUEST,
  __module__ = 'mobile.mobile_self_picking_pb2'
  # @@protoc_insertion_point(class_scope:mobile_self_picking.CreateStoreSelfPickingRequest)
  ))
_sym_db.RegisterMessage(CreateStoreSelfPickingRequest)

Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCT,
  __module__ = 'mobile.mobile_self_picking_pb2'
  # @@protoc_insertion_point(class_scope:mobile_self_picking.Product)
  ))
_sym_db.RegisterMessage(Product)

CreateStoreSelfPickingResponse = _reflection.GeneratedProtocolMessageType('CreateStoreSelfPickingResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATESTORESELFPICKINGRESPONSE,
  __module__ = 'mobile.mobile_self_picking_pb2'
  # @@protoc_insertion_point(class_scope:mobile_self_picking.CreateStoreSelfPickingResponse)
  ))
_sym_db.RegisterMessage(CreateStoreSelfPickingResponse)

ListStoreSelfPickingRequest = _reflection.GeneratedProtocolMessageType('ListStoreSelfPickingRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTSTORESELFPICKINGREQUEST,
  __module__ = 'mobile.mobile_self_picking_pb2'
  # @@protoc_insertion_point(class_scope:mobile_self_picking.ListStoreSelfPickingRequest)
  ))
_sym_db.RegisterMessage(ListStoreSelfPickingRequest)

SelfPicking = _reflection.GeneratedProtocolMessageType('SelfPicking', (_message.Message,), dict(
  DESCRIPTOR = _SELFPICKING,
  __module__ = 'mobile.mobile_self_picking_pb2'
  # @@protoc_insertion_point(class_scope:mobile_self_picking.SelfPicking)
  ))
_sym_db.RegisterMessage(SelfPicking)

ListStoreSelfPickingResponse = _reflection.GeneratedProtocolMessageType('ListStoreSelfPickingResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTSTORESELFPICKINGRESPONSE,
  __module__ = 'mobile.mobile_self_picking_pb2'
  # @@protoc_insertion_point(class_scope:mobile_self_picking.ListStoreSelfPickingResponse)
  ))
_sym_db.RegisterMessage(ListStoreSelfPickingResponse)

GetStoreSelfPickingDetailRequest = _reflection.GeneratedProtocolMessageType('GetStoreSelfPickingDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTORESELFPICKINGDETAILREQUEST,
  __module__ = 'mobile.mobile_self_picking_pb2'
  # @@protoc_insertion_point(class_scope:mobile_self_picking.GetStoreSelfPickingDetailRequest)
  ))
_sym_db.RegisterMessage(GetStoreSelfPickingDetailRequest)

GetStoreSelfPickingDetailResponse = _reflection.GeneratedProtocolMessageType('GetStoreSelfPickingDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTORESELFPICKINGDETAILRESPONSE,
  __module__ = 'mobile.mobile_self_picking_pb2'
  # @@protoc_insertion_point(class_scope:mobile_self_picking.GetStoreSelfPickingDetailResponse)
  ))
_sym_db.RegisterMessage(GetStoreSelfPickingDetailResponse)

UpdateStoreSelfPickingRequest = _reflection.GeneratedProtocolMessageType('UpdateStoreSelfPickingRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATESTORESELFPICKINGREQUEST,
  __module__ = 'mobile.mobile_self_picking_pb2'
  # @@protoc_insertion_point(class_scope:mobile_self_picking.UpdateStoreSelfPickingRequest)
  ))
_sym_db.RegisterMessage(UpdateStoreSelfPickingRequest)

UpdateStoreSelfPickingResponse = _reflection.GeneratedProtocolMessageType('UpdateStoreSelfPickingResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATESTORESELFPICKINGRESPONSE,
  __module__ = 'mobile.mobile_self_picking_pb2'
  # @@protoc_insertion_point(class_scope:mobile_self_picking.UpdateStoreSelfPickingResponse)
  ))
_sym_db.RegisterMessage(UpdateStoreSelfPickingResponse)

GetPickingProductByStoreIdRequest = _reflection.GeneratedProtocolMessageType('GetPickingProductByStoreIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPICKINGPRODUCTBYSTOREIDREQUEST,
  __module__ = 'mobile.mobile_self_picking_pb2'
  # @@protoc_insertion_point(class_scope:mobile_self_picking.GetPickingProductByStoreIdRequest)
  ))
_sym_db.RegisterMessage(GetPickingProductByStoreIdRequest)

GetPickingProductByStoreIdResponse = _reflection.GeneratedProtocolMessageType('GetPickingProductByStoreIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPICKINGPRODUCTBYSTOREIDRESPONSE,
  __module__ = 'mobile.mobile_self_picking_pb2'
  # @@protoc_insertion_point(class_scope:mobile_self_picking.GetPickingProductByStoreIdResponse)
  ))
_sym_db.RegisterMessage(GetPickingProductByStoreIdResponse)

AllowPickingProduct = _reflection.GeneratedProtocolMessageType('AllowPickingProduct', (_message.Message,), dict(
  DESCRIPTOR = _ALLOWPICKINGPRODUCT,
  __module__ = 'mobile.mobile_self_picking_pb2'
  # @@protoc_insertion_point(class_scope:mobile_self_picking.AllowPickingProduct)
  ))
_sym_db.RegisterMessage(AllowPickingProduct)

Unit = _reflection.GeneratedProtocolMessageType('Unit', (_message.Message,), dict(
  DESCRIPTOR = _UNIT,
  __module__ = 'mobile.mobile_self_picking_pb2'
  # @@protoc_insertion_point(class_scope:mobile_self_picking.Unit)
  ))
_sym_db.RegisterMessage(Unit)

ChangeSelfPickingStatusRequest = _reflection.GeneratedProtocolMessageType('ChangeSelfPickingStatusRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHANGESELFPICKINGSTATUSREQUEST,
  __module__ = 'mobile.mobile_self_picking_pb2'
  # @@protoc_insertion_point(class_scope:mobile_self_picking.ChangeSelfPickingStatusRequest)
  ))
_sym_db.RegisterMessage(ChangeSelfPickingStatusRequest)

ChangeSelfPickingStatusResponse = _reflection.GeneratedProtocolMessageType('ChangeSelfPickingStatusResponse', (_message.Message,), dict(
  DESCRIPTOR = _CHANGESELFPICKINGSTATUSRESPONSE,
  __module__ = 'mobile.mobile_self_picking_pb2'
  # @@protoc_insertion_point(class_scope:mobile_self_picking.ChangeSelfPickingStatusResponse)
  ))
_sym_db.RegisterMessage(ChangeSelfPickingStatusResponse)

GetSelfPickingLogRequest = _reflection.GeneratedProtocolMessageType('GetSelfPickingLogRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSELFPICKINGLOGREQUEST,
  __module__ = 'mobile.mobile_self_picking_pb2'
  # @@protoc_insertion_point(class_scope:mobile_self_picking.GetSelfPickingLogRequest)
  ))
_sym_db.RegisterMessage(GetSelfPickingLogRequest)

GetSelfPickingLogResponse = _reflection.GeneratedProtocolMessageType('GetSelfPickingLogResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSELFPICKINGLOGRESPONSE,
  __module__ = 'mobile.mobile_self_picking_pb2'
  # @@protoc_insertion_point(class_scope:mobile_self_picking.GetSelfPickingLogResponse)
  ))
_sym_db.RegisterMessage(GetSelfPickingLogResponse)

Log = _reflection.GeneratedProtocolMessageType('Log', (_message.Message,), dict(
  DESCRIPTOR = _LOG,
  __module__ = 'mobile.mobile_self_picking_pb2'
  # @@protoc_insertion_point(class_scope:mobile_self_picking.Log)
  ))
_sym_db.RegisterMessage(Log)



_MOBILESTORESELFPICKING = _descriptor.ServiceDescriptor(
  name='MobileStoreSelfPicking',
  full_name='mobile_self_picking.MobileStoreSelfPicking',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=3930,
  serialized_end=5301,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateStoreSelfPicking',
    full_name='mobile_self_picking.MobileStoreSelfPicking.CreateStoreSelfPicking',
    index=0,
    containing_service=None,
    input_type=_CREATESTORESELFPICKINGREQUEST,
    output_type=_CREATESTORESELFPICKINGRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\")/api/v2/supply/mobile/self/picking/create:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListStoreSelfPicking',
    full_name='mobile_self_picking.MobileStoreSelfPicking.ListStoreSelfPicking',
    index=1,
    containing_service=None,
    input_type=_LISTSTORESELFPICKINGREQUEST,
    output_type=_LISTSTORESELFPICKINGRESPONSE,
    serialized_options=_b('\202\323\344\223\002)\022\'/api/v2/supply/mobile/self/picking/list'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStoreSelfPickingDetail',
    full_name='mobile_self_picking.MobileStoreSelfPicking.GetStoreSelfPickingDetail',
    index=2,
    containing_service=None,
    input_type=_GETSTORESELFPICKINGDETAILREQUEST,
    output_type=_GETSTORESELFPICKINGDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\0226/api/v2/supply/mobile/self/picking/{receipt_id}/detail'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateStoreSelfPicking',
    full_name='mobile_self_picking.MobileStoreSelfPicking.UpdateStoreSelfPicking',
    index=3,
    containing_service=None,
    input_type=_UPDATESTORESELFPICKINGREQUEST,
    output_type=_UPDATESTORESELFPICKINGRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\")/api/v2/supply/mobile/self/picking/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ChangeSelfPickingStatus',
    full_name='mobile_self_picking.MobileStoreSelfPicking.ChangeSelfPickingStatus',
    index=4,
    containing_service=None,
    input_type=_CHANGESELFPICKINGSTATUSREQUEST,
    output_type=_CHANGESELFPICKINGSTATUSRESPONSE,
    serialized_options=_b('\202\323\344\223\002=\"8/api/v2/supply/mobile/self/picking/{status}/{receipt_id}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPickingProductByStoreId',
    full_name='mobile_self_picking.MobileStoreSelfPicking.GetPickingProductByStoreId',
    index=5,
    containing_service=None,
    input_type=_GETPICKINGPRODUCTBYSTOREIDREQUEST,
    output_type=_GETPICKINGPRODUCTBYSTOREIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002=\022;/api/v2/supply/mobile/self/picking/store/{store_id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='GetSelfPickingLog',
    full_name='mobile_self_picking.MobileStoreSelfPicking.GetSelfPickingLog',
    index=6,
    containing_service=None,
    input_type=_GETSELFPICKINGLOGREQUEST,
    output_type=_GETSELFPICKINGLOGRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\0223/api/v2/supply/mobile/self/picking/{receipt_id}/log'),
  ),
])
_sym_db.RegisterServiceDescriptor(_MOBILESTORESELFPICKING)

DESCRIPTOR.services_by_name['MobileStoreSelfPicking'] = _MOBILESTORESELFPICKING

# @@protoc_insertion_point(module_scope)
