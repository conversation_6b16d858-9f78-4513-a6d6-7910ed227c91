syntax = "proto3";
package mobile_franchisee_refund;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// 加盟商订货退款 -- 小程序入口
service MobileFranchiseeRefundService{
    // 查询退款单列表
    rpc ListMobileFRefund (ListMobileFRefundRequest) returns (ListMobileFRefundResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/mobile/franchisee/refund"
        };
    }
    // 查询退款单详情
    rpc GetMobileFRefundById(GetFRefundByIdRequest) returns (GetMobileFRefundByIdResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/mobile/franchisee/refund/{refund_id}"
        };
    }
    // 根据id查询单据历史记录
    rpc GetMobileRefundHistory(GetMobileRefundHistoryRequest) returns (RefundHistoryResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/mobile/franchisee/refund/{refund_id}/history"
        };
    }
    // 修改退款单状态统一入口
    rpc DealMobileFRefundById (DealMobileFRefundByIdRequest) returns (DealMobileFRefundByIdResponse) {
        option (google.api.http) = {
            post: "/api/v2/supply/mobile/franchisee/refund/deal"
            body: "*"
        };
    }
}

// 订单状态枚举
enum Status {
    INITED = 0;      // 待审核
    REJECTED = 3;    // 已驳回
    CANCELLED = 4;   // 已取消
    APPROVED = 9;    // 已退款
}


message ListMobileFRefundRequest{
    // 退款申请开始日期
    google.protobuf.Timestamp refund_start_date = 1;
    // 退款申请结束日期
    google.protobuf.Timestamp refund_end_date = 2;
    // 退款单状态
    repeated string status = 3;
    // 接收方ids
    repeated uint64 received_bys = 4;
    // 加盟商ids
    repeated uint64 franchisee_ids = 5;
    // 退款单号
    string code = 6;
    int64 limit = 7;
    uint64 offset = 8;
    bool include_total = 9;
    // 排序方式  asc or desc
    string order = 10;
    // 排序字段
    string sort = 11;
    // 退款审核开始日期
    google.protobuf.Timestamp approved_start_date = 12;
    // 退款审核结束日期
    google.protobuf.Timestamp approved_end_date = 13;
    // 退款单类型(来源)
    // 仅退款: ORDER_REFUND
    // 缺货退款: OOS_REFUND
    // 退货退款: RETURN_REFUND
    // 差异退款: DIFF_REFUND
    repeated string types = 14;
    // 关联主单类型(单号筛选时使用)
    // 订货单FRS_DEMAND
    // 要货单FRS_WHS_DEMAND
    // 退货单FRS_REFUND
    // 收货差异单FRS_REC_DIFF
    string main_type = 15;
    // 关联主单号
    string main_code = 16;
    // 退款支付方式
    // "BohCreditPay"  BOH信用付
    // "WXMPay"        微信小程序
    // "Cash"          现金
    // "Voucher"       代金券
    repeated string payment_ways = 17;
    // 退款单id
    repeated uint64 ids = 18;
}

message ListMobileFRefundResponse{
    repeated Refund rows = 1;
    uint64 total = 2;
}

message Refund {
    // 单据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // 关联主单ID(订货单/退货单)
    uint64 main_id = 3;
    // 关联主单单号
    string main_code = 4;
    // 关联主单类型
    string main_type = 5;
    // 单号
    string code = 6;
    // 退款单类型：订货退款ORDER_REFUND、缺货退款OOS_REFUND、退货退款RETURN_REFUND、收货差异退款DIFF_REFUND
    string type = 7;
    // 收货门店id
    uint64 received_by = 8;
    // 收货门店编码
    string received_code = 9;
    // 收货门店名称
    string received_name = 10;
    // 加盟商id
    uint64 franchisee_id = 13;
    // 加盟商code
    string franchisee_code = 14;
    // 加盟商名称
    string franchisee_name = 15;
    // 退款申请日期
    google.protobuf.Timestamp refund_date = 16;
    // 单据业务状态
    string status = 17;
    // 处理状态
    string process_status = 18;
    // 驳回原因
    string reject_reason = 20;
    // 备注
    string remark = 21;
    // 原因
    string reason = 22;
    // 创建日期
    google.protobuf.Timestamp created_at = 23;
    // 更新时间
    google.protobuf.Timestamp updated_at = 24;
    // 创建人
    uint64 created_by = 25;
    string created_name = 26;
    // 更新人id
    uint64 updated_by = 27;
    string updated_name = 28;
    // 审核人id
    uint64 review_by = 29;
    // 退款金额
    double refund_amount = 30;
    // 实际付款金额
    double pay_amount = 31;
    // 付款凭证（附件）
    repeated string attachments = 32;
    // 扩展信息
    string extends = 33;
    // 退款审核日期
    google.protobuf.Timestamp approved_date = 34;
    // 退款支付方式(线下支付OFFLINE/信用付CREDIT/线上支付ONLINE等)
    string payment_way = 35;
    // 贸易公司
    uint64 trade_company = 36;
    // 公司名称
    string company_name = 37;
    // 公司编号
    string company_code = 38;
    // 订货单ID
    uint64 batch_id = 39;
}

message RefundProduct {
    // 数据id
    uint64 id = 1;
    // 商品id
    uint64 product_id = 2;
    // 商品编号
    string product_code = 3;
    // 商品名称
    string product_name = 4;
    // 退款单ID
    uint64 refund_id = 5;
    uint64 partner_id = 6;
    // 商品分类id
    uint64 category_id = 8;
    // 商品分类名称
    string category_name = 9;
    // 商品规格
    string product_spec = 10;
    // 单位ID
    uint64 unit_id = 11;
    // 单位名称
    string unit_name = 12;
    // 单位规格
    string unit_spec = 13;
    // 单位转换率
    double unit_rate = 17;
    // 退货数量
    double quantity = 18;
    // 含税单价
    double tax_price = 20;
    // 成本价
    double cost_price = 21;
    // 税率
    double tax_rate = 22;
    // 单项合计金额
    double amount = 23;
    // 创建人
    uint64 created_by = 24;
    string created_name = 25;
    // 更新人id
    uint64 updated_by = 26;
    string updated_name = 27;
    // 创建日期
    google.protobuf.Timestamp created_at = 28;
    // 更新时间
    google.protobuf.Timestamp updated_at = 29;
}

message GetFRefundByIdRequest{
    // 退款单id
    uint64 refund_id = 1;
}

message GetMobileFRefundByIdResponse {
    // 单据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // 关联主单ID(订货单/退货单/收货单等)
    uint64 main_id = 3;
    // 关联主单单号
    string main_code = 4;
    // 关联主单类型
    string main_type = 5;
    // 单号
    string code = 6;
    // 退款单类型：订货退款ORDER_REFUND、缺货退款OOS_REFUND、退货退款RETURN_REFUND、收货差异退款DIFF_REFUND
    string type = 7;
    // 收货门店id
    uint64 received_by = 8;
    // 收货门店编码
    string received_code = 9;
    // 收货门店名称
    string received_name = 10;
    // 加盟商id
    uint64 franchisee_id = 13;
    // 加盟商code
    string franchisee_code = 14;
    // 加盟商名称
    string franchisee_name = 15;
    // 退款申请日期
    google.protobuf.Timestamp refund_date = 16;
    // 单据业务状态
    string status = 17;
    // 处理状态
    string process_status = 18;
    // 驳回原因
    string reject_reason = 20;
    // 备注
    string remark = 21;
    // 原因
    string reason = 22;
    // 创建日期
    google.protobuf.Timestamp created_at = 23;
    // 更新时间
    google.protobuf.Timestamp updated_at = 24;
    // 创建人
    uint64 created_by = 25;
    string created_name = 26;
    // 更新人id
    uint64 updated_by = 27;
    string updated_name = 28;
    // 审核人id
    uint64 review_by = 29;
    // 退款金额
    double refund_amount = 30;
    // 实际付款金额
    double pay_amount = 31;
    // 付款凭证（附件）
    repeated string attachments = 32;
    // 扩展信息
    string extends = 33;
    // 退款审核日期
    google.protobuf.Timestamp approved_date = 34;
    // 退款付款方式(线下支付OFFLINE、信用付CREDIT、线上支付ONLINE等)
    string payment_way = 35;
    // 贸易公司
    uint64 trade_company = 36;
    // 公司名称
    string company_name = 37;
    // 公司编号
    string company_code = 38;
    // 订货单ID
    uint64 batch_id = 39;
    // 商品详情
    repeated RefundProduct products = 40;
}

message GetMobileRefundHistoryRequest{
    // 退款单id
    uint64 refund_id = 1;
}
message RefundHistoryResponse{
    repeated RefundHistory rows = 1;
    uint64 total = 2;
}
message RefundHistory{
    // 历史记录id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // 退款单id
    uint64 refund_id = 3;
    // 退款单操作
    string action = 4;
    // 操作人名称或者系统自动操作
    uint64 created_by = 5;
    // 操作时间
    google.protobuf.Timestamp created_at = 6;
    // 操作人名称
    string created_name = 7;
}

// 处理单据
message DealMobileFRefundByIdRequest {
    // 退款单ID
    uint64 refund_id = 1;
    // 单据业务状态，门店端只支持取消操作，对应上方Status枚举
    string action = 2;
    // 备注
    string remark = 3;
    // 附件
    repeated string attachments = 4;
    // 驳回原因
    string reject_reason = 5;
}

// 统一返回对象
message DealMobileFRefundByIdResponse {
    string result = 1;
}
