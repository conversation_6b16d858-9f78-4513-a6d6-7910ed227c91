syntax = "proto3";

package mobile_common;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";


service MobileCommonService {
    //GetUnfinishedDoc 首页未完成单据待办任务
    rpc GetUnfinishedDoc (GetUnfinishedDocRequest) returns (GetUnfinishedDocResponse) {
        option (google.api.http) = { get: "/api/v2/supply/mobile/unfinished/doc" };
    }
}

message GetUnfinishedDocRequest {
    google.protobuf.Timestamp start_date = 1;
    google.protobuf.Timestamp end_date = 2;
    uint64 store_id = 3;
    // "STORE"
    // "FRS_STORE"
    string branch_type = 4;
    string lan = 5;
}

message UnfinishedReceipt {
    // 单据id
    uint64 id = 1;
    // 单号
    string code = 2;
    // 单据状态
    string status = 4;
    // 给调拨单据用, 调出门店
    uint64 shipping_store = 5;
    // 接收门店
    uint64 receiving_store = 6;
    // 单据类型
    string type = 7;
}

message GetUnfinishedDocResponse {
    bool handler = 1;
    repeated UnfinishedReceipt adjust = 2;
    repeated UnfinishedReceipt receiving_diff = 3;
    repeated UnfinishedReceipt transfer = 4;
    repeated UnfinishedReceipt receiving = 5;
    repeated UnfinishedReceipt return = 6;
    repeated UnfinishedReceipt stocktake = 10;
    repeated UnfinishedReceipt demand = 11;
    repeated UnfinishedReceipt self_picking = 13;
}