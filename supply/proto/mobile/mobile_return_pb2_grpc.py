# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from mobile import mobile_return_pb2 as mobile_dot_mobile__return__pb2


class MobileReturnServiceStub(object):
  """MobileReturnService 退货移动端服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateReturn = channel.unary_unary(
        '/mobile_returns.MobileReturnService/CreateReturn',
        request_serializer=mobile_dot_mobile__return__pb2.CreateReturnRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__return__pb2.CreateReturnResponse.FromString,
        )
    self.ListReturn = channel.unary_unary(
        '/mobile_returns.MobileReturnService/ListReturn',
        request_serializer=mobile_dot_mobile__return__pb2.ListReturnRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__return__pb2.ListReturnResponse.FromString,
        )
    self.GetReturnById = channel.unary_unary(
        '/mobile_returns.MobileReturnService/GetReturnById',
        request_serializer=mobile_dot_mobile__return__pb2.GetReturnByIdRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__return__pb2.Returns.FromString,
        )
    self.GetReturnProductById = channel.unary_unary(
        '/mobile_returns.MobileReturnService/GetReturnProductById',
        request_serializer=mobile_dot_mobile__return__pb2.GetReturnProductByIdRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__return__pb2.GetReturnProductByIdResponse.FromString,
        )
    self.DealReturn = channel.unary_unary(
        '/mobile_returns.MobileReturnService/DealReturn',
        request_serializer=mobile_dot_mobile__return__pb2.DealReturnRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__return__pb2.DealReturnResponse.FromString,
        )
    self.UpdateReturn = channel.unary_unary(
        '/mobile_returns.MobileReturnService/UpdateReturn',
        request_serializer=mobile_dot_mobile__return__pb2.UpdateReturnRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__return__pb2.DealReturnResponse.FromString,
        )
    self.GetValidReturnProduct = channel.unary_unary(
        '/mobile_returns.MobileReturnService/GetValidReturnProduct',
        request_serializer=mobile_dot_mobile__return__pb2.GetValidReturnProductRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__return__pb2.ValidReturnProductResponse.FromString,
        )
    self.GetHistoryById = channel.unary_unary(
        '/mobile_returns.MobileReturnService/GetHistoryById',
        request_serializer=mobile_dot_mobile__return__pb2.GetHistoryByIdRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__return__pb2.HistoryResponse.FromString,
        )


class MobileReturnServiceServicer(object):
  """MobileReturnService 退货移动端服务
  """

  def CreateReturn(self, request, context):
    """创建退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListReturn(self, request, context):
    """查询退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReturnById(self, request, context):
    """根据id查询退货单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReturnProductById(self, request, context):
    """根据id查询退货单商品详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DealReturn(self, request, context):
    """处理退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateReturn(self, request, context):
    """更新退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetValidReturnProduct(self, request, context):
    """查询可退货商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetHistoryById(self, request, context):
    """根据id查询单据历史记录
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_MobileReturnServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateReturn': grpc.unary_unary_rpc_method_handler(
          servicer.CreateReturn,
          request_deserializer=mobile_dot_mobile__return__pb2.CreateReturnRequest.FromString,
          response_serializer=mobile_dot_mobile__return__pb2.CreateReturnResponse.SerializeToString,
      ),
      'ListReturn': grpc.unary_unary_rpc_method_handler(
          servicer.ListReturn,
          request_deserializer=mobile_dot_mobile__return__pb2.ListReturnRequest.FromString,
          response_serializer=mobile_dot_mobile__return__pb2.ListReturnResponse.SerializeToString,
      ),
      'GetReturnById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReturnById,
          request_deserializer=mobile_dot_mobile__return__pb2.GetReturnByIdRequest.FromString,
          response_serializer=mobile_dot_mobile__return__pb2.Returns.SerializeToString,
      ),
      'GetReturnProductById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReturnProductById,
          request_deserializer=mobile_dot_mobile__return__pb2.GetReturnProductByIdRequest.FromString,
          response_serializer=mobile_dot_mobile__return__pb2.GetReturnProductByIdResponse.SerializeToString,
      ),
      'DealReturn': grpc.unary_unary_rpc_method_handler(
          servicer.DealReturn,
          request_deserializer=mobile_dot_mobile__return__pb2.DealReturnRequest.FromString,
          response_serializer=mobile_dot_mobile__return__pb2.DealReturnResponse.SerializeToString,
      ),
      'UpdateReturn': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateReturn,
          request_deserializer=mobile_dot_mobile__return__pb2.UpdateReturnRequest.FromString,
          response_serializer=mobile_dot_mobile__return__pb2.DealReturnResponse.SerializeToString,
      ),
      'GetValidReturnProduct': grpc.unary_unary_rpc_method_handler(
          servicer.GetValidReturnProduct,
          request_deserializer=mobile_dot_mobile__return__pb2.GetValidReturnProductRequest.FromString,
          response_serializer=mobile_dot_mobile__return__pb2.ValidReturnProductResponse.SerializeToString,
      ),
      'GetHistoryById': grpc.unary_unary_rpc_method_handler(
          servicer.GetHistoryById,
          request_deserializer=mobile_dot_mobile__return__pb2.GetHistoryByIdRequest.FromString,
          response_serializer=mobile_dot_mobile__return__pb2.HistoryResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'mobile_returns.MobileReturnService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
