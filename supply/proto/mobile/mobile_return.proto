syntax = "proto3";
package mobile_returns;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
option go_package = "./mobile_returns";

// MobileReturnService 退货移动端服务
service MobileReturnService {

    // 创建退货单
    rpc CreateReturn (CreateReturnRequest) returns (CreateReturnResponse) {
        option (google.api.http) = {
            post:"/api/v2/supply/mobile/returns"
            body:"*"
        };
    }

    // 查询退货单
    rpc ListReturn (ListReturnRequest) returns (ListReturnResponse) {
        option (google.api.http) = {
            get:"/api/v2/supply/mobile/returns"
        };
    }

    // 根据id查询退货单详情
    rpc GetReturnById (GetReturnByIdRequest) returns (Returns) {
        option (google.api.http) = {
            get:"/api/v2/supply/mobile/returns/{id}"
        };
    }

    // 根据id查询退货单商品详情
    rpc GetReturnProductById (GetReturnProductByIdRequest) returns (GetReturnProductByIdResponse) {
        option (google.api.http) = {
            get:"/api/v2/supply/mobile/returns/{id}/product"
        };
    }

    // 处理退货单
    rpc DealReturn (DealReturnRequest) returns (DealReturnResponse) {
        option (google.api.http) = {
            post:"/api/v2/supply/mobile/returns/{id}/{action}"
            body:"*"
        };
    }

    // 更新退货单
    rpc UpdateReturn (UpdateReturnRequest) returns (DealReturnResponse) {
        option (google.api.http) = {
            post:"/api/v2/supply/mobile/returns/update/by/{id}"
            body:"*"
        };
    }

    // 查询可退货商品
    rpc GetValidReturnProduct (GetValidReturnProductRequest) returns (ValidReturnProductResponse) {
        option (google.api.http) = {
            get:"/api/v2/supply/mobile/returns/store/product/valid"
        };
    }

    // 根据id查询单据历史记录
    rpc GetHistoryById(GetHistoryByIdRequest) returns (HistoryResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/mobile/return/{id}/history"
        };
    }
}

message CommonReply {
    bool success = 1;
}

message CreateReturnRequest {
    // 退货方id
    uint64 return_by = 1;
    // 单据物流来源类型:PUR 直送/ NMD 配送
    string logistics_type = 2;
    // 退货类型: 原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD
    string type = 3;
    // 退货方类型： store/ warehouse/ machining
    string sub_type = 4;
    // 交货日期
    google.protobuf.Timestamp return_delivery_date = 5;
    // 退货单来源id
    uint64 source_id = 6;
    string source_code = 7;
    // 退货原因
    string return_reason = 8;
    // 备注
    string remark = 9;
    // 附件
    repeated Attachments attachments = 10;
    // 唯一请求号，保证不重复请求
    uint64 request_id = 11;
    // 商品
    repeated Product products = 12;
    // 语言
    string lan = 20;
}

message CreateReturnResponse {
    repeated uint64 return_id = 1;
    bool payload = 2;
}

message GetValidReturnProductRequest {
    uint64 store_id = 1;
    // 物流模式
    string distr_type = 3;
    // 配送中心
    uint64 distr_by = 4;
    // 商品分类
    repeated uint64 category_ids = 5;
    repeated uint64 product_ids = 6;
    int64 limit = 8;
    int64 offset = 9;
    // 排序条件
    string order_by = 10;
    // asc or desc，暂时未使用
    string sort = 11;
    // 搜索商品
    string search = 12;
    // 搜索字段
    string search_fields = 13;
}

// 获取可退货商品返回参数
message ValidReturnProductResponse {
    repeated ValidReturnProduct rows = 1;
    int32 total = 2;
    // 库存未异动的商品
    repeated ValidReturnProduct inventory_unchanged_rows = 3;
}

message ValidReturnProduct {
    // 商品
    uint64 product_id = 1;
    string product_code = 2;
    string product_name = 3;
    // 规格
    string spec = 4;
    // 订货单位
    uint64 unit_id = 5;
    string unit_name = 6;
    string unit_spec = 7;
    double unit_rate = 8;
    // 商品类别
    uint64 category_id = 9;
    string category_code = 10;
    string category_name = 11;
    // 配送/供应商中心
    uint64 distr_by = 12;
    // 配送(NMD)、采购(PUR)
    string distr_type = 13;

    // 
    string product_type = 14;
    string storage_type = 15;
    // 
    double purchase_price = 16;
    double purchase_tax = 17;
    double tax_rate = 18;
    double price = 19;
    double price_tax = 20;
    // 实时库存数量
    double real_inventory_qty = 21;
}

message GetHistoryByIdRequest{
    // 退货单id
    uint64 id = 1;
}
message HistoryResponse{
    repeated History rows = 1;
    uint64 total = 2;
}
message History{
    // 历史记录id
    uint64 id = 1;
    // 退货单状态
    string status = 2;
    // 操作人名称或者系统自动操作
    uint64 updated_by = 3;
    // 操作时间
    google.protobuf.Timestamp updated_at = 4;
    string updated_by_name = 5;
}

message ListReturnRequest {
    // 退货方id
    repeated uint64 store_ids = 1;
    // 订单状态
    repeated string status = 2;
    // 退货单号
    string code = 3;
    // 来源id
    uint64 source_id = 4;
    string source_code = 5;
    // 单据物流来源类型:PUR 直送/ NMD 配送
    string logistics_type = 6;
    // 退货类型: 原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD
    string type = 7;
    // 退货方类型： store/ warehouse/ machining
    string sub_type = 8;
    // 商品ids
    repeated uint64 product_ids = 9;
    // 退货开始日期
    google.protobuf.Timestamp return_date_from = 10;
    // 退货结束日期
    google.protobuf.Timestamp return_date_to = 11;
    // 提货开始日期
    google.protobuf.Timestamp delivery_date_from = 12;
    // 提货结束日期
    google.protobuf.Timestamp delivery_date_to = 13;
    // 分页大小
    int32 limit = 20;
    // 跳过行数
    int32 offset = 21;
    // 排序
    string order = 22;
    // 排序字段
    string sort = 23;
    
    string lan = 24;
    repeated uint64 ids = 25;
    // 配送接收方id
    repeated uint64 return_tos = 26;
}

message ListReturnResponse {

    repeated Returns rows = 1;
    uint64 total = 2;
}

message GetReturnByIdRequest {
    // 退货单id
    uint64 id = 1;
    // 是否显示详情
    // 可否删除？
    // bool is_details = 2;
    string lan = 2;
}

message GetReturnByIdResponse {
    Returns return_detail = 1;
}

message GetReturnProductByIdRequest {
    // id
    uint64 id = 1;
    // 商户id
    // uint64 partner_id = 2;
    // 分页大小
    int32 limit = 2;
    // 跳过行数
    int32 offset = 3;
    // 返回总条数
    bool include_total = 4;
    // 排序字段
    string sort = 14;
    // 排序顺序
    string order = 15;
    string lan = 16;

}

message GetReturnProductByIdResponse {
    repeated ProductDetail rows = 1;
    uint64 total = 2;
}

// action枚举：
// submit-提交
// reject-驳回
// approve-审核
// delivery-提货
// confirm-确认
// delete-删除
message DealReturnRequest {
    uint64 id = 1;
    string action = 2;
    string reject_reason = 3;
    string trans_type = 4;
    // 退回仓库类型(Scrap/ Normal)
    string warehouse_type = 5;

}

message DealReturnResponse {
    uint64 id = 1;
    bool status = 2;
}


message UpdateReturnRequest {
    //
    uint64 id = 1;
    //
    repeated Product products = 2;
    // 退货原因
    string return_reason = 3;
    // 备注
    string remark = 4;
    // 退货交货时间
    google.protobuf.Timestamp return_delivery_date = 5;
    // 供应商/配送中心
    uint64 return_to = 6;
    // 附件
    repeated Attachments attachments = 7;
    string lan = 8;
    string logistics_type = 9;
}


message Product {
    // 商品表里的id（supply_receiving_diff_product.id)
    uint64 product_id = 1;
    // 订货单位id
    uint64 unit_id = 2;
    // 数量
    double quantity = 3;
    // 收货数量
    double confirmed_quantity = 4;
    //
    string product_code = 5;
    string product_name = 6;
    //
    string unit_name = 7;
    string unit_spec = 8;
    // 单位换算比例
    float unit_rate = 9;
    //
    float tax_rate = 10;
    float price = 11;
    float price_tax = 12;
    uint64 id = 13;
    // 仓库/配送中心
    uint64 return_to = 14;
    repeated Attachments attachments = 15;
    // 
    string logistics_type = 16;
}

message Returns {
    // id
    uint64 id = 1;
    // 退货单单号
    string code = 2;
    // 退货门店
    uint64 return_by = 3;
    // 退货编号
    string return_number = 4;
    // 退货单状态
    string status = 5;
    // 审核人
    uint64 review_by = 6;
    // 退货提货单单单号
    string return_delivery_number = 8;
    // 退货日期
    google.protobuf.Timestamp return_date = 9;
    // 预计退货提货时间
    google.protobuf.Timestamp return_delivery_date = 10;
    // 退货原因
    string return_reason = 11;
    // 退货类型: 原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD
    string type = 12;
    // 退货方类型： store/ warehouse/ machining
    string sub_type = 13;
    // 备注
    string remark = 14;
    // 预留门店号
    string store_secondary_id = 15;
    // 创建时间
    google.protobuf.Timestamp created_at = 16;
    // 更新时间
    google.protobuf.Timestamp updated_at = 17;
    // 创建人id
    uint64 created_by = 18;
    // 更新人id
    uint64 updated_by = 19;
    // 退货接受方 门店id
    uint64 return_to = 20;
    // // 库存引擎调用
    // enum inventoryStatus {
    //     SENT = 0; // 已发送
    //     SENT_SUC = 2; //调用成功
    //     SENT_FAIL = 3; //调用失败
    // }
    string inventory_status = 21;
    // 库存id预留字段
    uint64 inventory_req_id = 22;
    // 商户id
    uint64 partner_id = 23;
    // 驳回原因
    string reject_reason = 25;
    // 附件
    repeated Attachments attachments = 26;
    // 创建人
    string created_name = 27;
    // 更新人
    string updated_name = 28;
    // 商品数量
    uint64 product_nums = 29;
    // 单据物流来源类型:PUR 直送/ NMD 配送
    string logistics_type = 31;
    // 提货方式
    string trans_type = 35;
    // 来源
    uint64 source_id = 37;
    string source_code = 38;
    // 实际提货日期
    google.protobuf.Timestamp delivery_date = 39;
    uint64 request_id = 40;
    // 退货接受方 门店id
    string return_to_name = 41;
    // 退货接收方电话
    string phone = 42;
    // 加盟商
    uint64 franchisee_id = 43;
    string payment_way = 44;
    string receive_code = 45;
    string delivery_code = 46;
    // 退款单号
    uint64 refund_id = 50;
    string refund_code = 51;
    string warehouse_type = 52;
        // 是否长效
    string long_effect = 53;

}

message Unit {
    // id
    uint64 id = 1;
    // 单位数量
    int32 quantity = 2;
    //
    double rate = 3;
}

message ProductDetail {
    // id
    uint64 id = 1;
    // 退货单id
    uint64 return_id = 2;
    // 退货门店
    uint64 return_by = 3;

    // 物料编码
    string material_number = 4;
    // 商品编码
    string product_code = 5;
    // 商品id
    uint64 product_id = 6;
    // 商品名称
    string product_name = 7;
    // 数量
    double quantity = 8;

    // 单位id
    uint64 accounting_unit_id = 9;
    // 单位名称
    string accounting_unit_name = 10;
    // 单位规格
    string accounting_unit_spec = 11;
    // 单位id
    uint64 unit_id = 12;
    // 单位名称
    string unit_name = 13;
    // 单位换算比例
    double unit_rate = 14;
    // 单位规格
    string unit_spec = 15;
    // 确认退货数量
    double accounting_confirmed_quantity = 16;
    //
    double accounting_quantity = 17;
    //
    double accounting_returned_quantity = 18;
    // 确认收货数量
    double confirmed_quantity = 19;
    // 退货数量
    double returned_quantity = 20;

    // 状态
    bool is_confirmed = 21;
    // 退货日期
    google.protobuf.Timestamp return_date = 22;
    // 接收方
    uint64 return_to = 23;
    // 订货日期
    // google.protobuf.Timestamp demand_date = 23;

    // 库存引擎调用
    // enum inventoryStatus {
    //     SENT = 1; // 已发送
    //     SENT_SUC = 2; //调用成功
    //     SENT_FAIL = 3; //调用失败
    // }
    string inventory_status = 24;
    // 库存id预留字段
    uint64 inventory_req_id = 25;
    // 创建时间
    google.protobuf.Timestamp created_at = 26;
    // 创建人id
    uint64 created_by = 27;
    // 更新日期
    google.protobuf.Timestamp updated_at = 28;
    // 更新人id
    uint64 updated_by = 29;
    // 商户id
    uint64 partner_id = 30;
    // 创建人
    string created_name = 31;
    // 更新人
    string updated_name = 32;
    string storage_type = 35;
    string category_code = 36;
    uint64 category_id = 37;
    string category_name = 38;
    // 未税单价
    double price = 39;
    // 税率
    float tax_rate = 40;
    // 含税单价
    double price_tax = 41;
    // 采购总价
    double sum_price = 42;
    // 零售价
    double retail_price = 43;
    // 附件
    repeated Attachments attachments = 45;
}

message Attachments {
    string type = 1;
    string url = 2;
}