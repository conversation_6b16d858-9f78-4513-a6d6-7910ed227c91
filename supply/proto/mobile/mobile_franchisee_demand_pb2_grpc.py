# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from mobile import mobile_franchisee_demand_pb2 as mobile_dot_mobile__franchisee__demand__pb2


class MobileFranchiseeDemandServiceStub(object):
  """加盟商订货 —— 移动端
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateFDemand = channel.unary_unary(
        '/mobile_franchisee_demand.MobileFranchiseeDemandService/CreateFDemand',
        request_serializer=mobile_dot_mobile__franchisee__demand__pb2.CreateFDemandRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__demand__pb2.CreateFDemandResponse.FromString,
        )
    self.ListFDemand = channel.unary_unary(
        '/mobile_franchisee_demand.MobileFranchiseeDemandService/ListFDemand',
        request_serializer=mobile_dot_mobile__franchisee__demand__pb2.ListFDemandRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__demand__pb2.ListFDemandResponse.FromString,
        )
    self.GetFDemandById = channel.unary_unary(
        '/mobile_franchisee_demand.MobileFranchiseeDemandService/GetFDemandById',
        request_serializer=mobile_dot_mobile__franchisee__demand__pb2.GetFDemandByIdRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__demand__pb2.Demand.FromString,
        )
    self.GetHistoryById = channel.unary_unary(
        '/mobile_franchisee_demand.MobileFranchiseeDemandService/GetHistoryById',
        request_serializer=mobile_dot_mobile__franchisee__demand__pb2.GetHistoryByIdRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__demand__pb2.HistoryResponse.FromString,
        )
    self.GetProductsById = channel.unary_unary(
        '/mobile_franchisee_demand.MobileFranchiseeDemandService/GetProductsById',
        request_serializer=mobile_dot_mobile__franchisee__demand__pb2.GetProductsByIdRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__demand__pb2.ProductsResponse.FromString,
        )
    self.UpdateProducts = channel.unary_unary(
        '/mobile_franchisee_demand.MobileFranchiseeDemandService/UpdateProducts',
        request_serializer=mobile_dot_mobile__franchisee__demand__pb2.UpdateProductRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__demand__pb2.UpdateProductsResponse.FromString,
        )
    self.DealFDemandById = channel.unary_unary(
        '/mobile_franchisee_demand.MobileFranchiseeDemandService/DealFDemandById',
        request_serializer=mobile_dot_mobile__franchisee__demand__pb2.DealFDemandByIdRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__demand__pb2.DealFDemandByIdResponse.FromString,
        )
    self.AddFDemandProduct = channel.unary_unary(
        '/mobile_franchisee_demand.MobileFranchiseeDemandService/AddFDemandProduct',
        request_serializer=mobile_dot_mobile__franchisee__demand__pb2.AddFDemandProductRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__demand__pb2.AddFDemandProductResponse.FromString,
        )
    self.QueryFDemandReport = channel.unary_unary(
        '/mobile_franchisee_demand.MobileFranchiseeDemandService/QueryFDemandReport',
        request_serializer=mobile_dot_mobile__franchisee__demand__pb2.QueryFDemandReportRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__demand__pb2.QueryFDemandReportResponse.FromString,
        )
    self.AddShoppingCartCache = channel.unary_unary(
        '/mobile_franchisee_demand.MobileFranchiseeDemandService/AddShoppingCartCache',
        request_serializer=mobile_dot_mobile__franchisee__demand__pb2.AddShoppingCartCacheRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__demand__pb2.AddShoppingCartCacheResponse.FromString,
        )
    self.GetShoppingCartCache = channel.unary_unary(
        '/mobile_franchisee_demand.MobileFranchiseeDemandService/GetShoppingCartCache',
        request_serializer=mobile_dot_mobile__franchisee__demand__pb2.GetShoppingCartCacheRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__demand__pb2.GetShoppingCartCacheResponse.FromString,
        )
    self.CheckFDemandTodo = channel.unary_unary(
        '/mobile_franchisee_demand.MobileFranchiseeDemandService/CheckFDemandTodo',
        request_serializer=mobile_dot_mobile__franchisee__demand__pb2.CheckFDemandTodoRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__demand__pb2.CheckFDemandTodoResponse.FromString,
        )


class MobileFranchiseeDemandServiceServicer(object):
  """加盟商订货 —— 移动端
  """

  def CreateFDemand(self, request, context):
    """创建订货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListFDemand(self, request, context):
    """查询订货单列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetFDemandById(self, request, context):
    """根据id查询单个订货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetHistoryById(self, request, context):
    """根据id查询单据历史记录
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProductsById(self, request, context):
    """根据id查询单据内的商品列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateProducts(self, request, context):
    """更新订货单商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DealFDemandById(self, request, context):
    """修改订单状态统一入口
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddFDemandProduct(self, request, context):
    """订单新增商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryFDemandReport(self, request, context):
    """门店订货清单报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddShoppingCartCache(self, request, context):
    """添加购物车缓存
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetShoppingCartCache(self, request, context):
    """查询购物车缓存
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CheckFDemandTodo(self, request, context):
    """检查订货待办能否订货
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_MobileFranchiseeDemandServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateFDemand': grpc.unary_unary_rpc_method_handler(
          servicer.CreateFDemand,
          request_deserializer=mobile_dot_mobile__franchisee__demand__pb2.CreateFDemandRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__demand__pb2.CreateFDemandResponse.SerializeToString,
      ),
      'ListFDemand': grpc.unary_unary_rpc_method_handler(
          servicer.ListFDemand,
          request_deserializer=mobile_dot_mobile__franchisee__demand__pb2.ListFDemandRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__demand__pb2.ListFDemandResponse.SerializeToString,
      ),
      'GetFDemandById': grpc.unary_unary_rpc_method_handler(
          servicer.GetFDemandById,
          request_deserializer=mobile_dot_mobile__franchisee__demand__pb2.GetFDemandByIdRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__demand__pb2.Demand.SerializeToString,
      ),
      'GetHistoryById': grpc.unary_unary_rpc_method_handler(
          servicer.GetHistoryById,
          request_deserializer=mobile_dot_mobile__franchisee__demand__pb2.GetHistoryByIdRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__demand__pb2.HistoryResponse.SerializeToString,
      ),
      'GetProductsById': grpc.unary_unary_rpc_method_handler(
          servicer.GetProductsById,
          request_deserializer=mobile_dot_mobile__franchisee__demand__pb2.GetProductsByIdRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__demand__pb2.ProductsResponse.SerializeToString,
      ),
      'UpdateProducts': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateProducts,
          request_deserializer=mobile_dot_mobile__franchisee__demand__pb2.UpdateProductRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__demand__pb2.UpdateProductsResponse.SerializeToString,
      ),
      'DealFDemandById': grpc.unary_unary_rpc_method_handler(
          servicer.DealFDemandById,
          request_deserializer=mobile_dot_mobile__franchisee__demand__pb2.DealFDemandByIdRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__demand__pb2.DealFDemandByIdResponse.SerializeToString,
      ),
      'AddFDemandProduct': grpc.unary_unary_rpc_method_handler(
          servicer.AddFDemandProduct,
          request_deserializer=mobile_dot_mobile__franchisee__demand__pb2.AddFDemandProductRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__demand__pb2.AddFDemandProductResponse.SerializeToString,
      ),
      'QueryFDemandReport': grpc.unary_unary_rpc_method_handler(
          servicer.QueryFDemandReport,
          request_deserializer=mobile_dot_mobile__franchisee__demand__pb2.QueryFDemandReportRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__demand__pb2.QueryFDemandReportResponse.SerializeToString,
      ),
      'AddShoppingCartCache': grpc.unary_unary_rpc_method_handler(
          servicer.AddShoppingCartCache,
          request_deserializer=mobile_dot_mobile__franchisee__demand__pb2.AddShoppingCartCacheRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__demand__pb2.AddShoppingCartCacheResponse.SerializeToString,
      ),
      'GetShoppingCartCache': grpc.unary_unary_rpc_method_handler(
          servicer.GetShoppingCartCache,
          request_deserializer=mobile_dot_mobile__franchisee__demand__pb2.GetShoppingCartCacheRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__demand__pb2.GetShoppingCartCacheResponse.SerializeToString,
      ),
      'CheckFDemandTodo': grpc.unary_unary_rpc_method_handler(
          servicer.CheckFDemandTodo,
          request_deserializer=mobile_dot_mobile__franchisee__demand__pb2.CheckFDemandTodoRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__demand__pb2.CheckFDemandTodoResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'mobile_franchisee_demand.MobileFranchiseeDemandService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
