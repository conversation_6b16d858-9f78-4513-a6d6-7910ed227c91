{"swagger": "2.0", "info": {"title": "mobile/mobile_franchisee_return.proto", "version": "version not set"}, "tags": [{"name": "FranchiseeReturnService"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/mobile/franchisee/adjust_returns": {"post": {"summary": "创建退货单（进行拆单）", "operationId": "FranchiseeReturnService_CreateAdjustReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_returnsCreateAdjustReturnResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/franchisee_returnsCreateReturnRequest"}}], "tags": ["FranchiseeReturnService"]}}, "/api/v2/supply/mobile/franchisee/return/check": {"post": {"summary": "校验按收货原单退货是否超退", "operationId": "FranchiseeReturnService_CheckReturnAvailableByrec", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_returnsCommonReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/franchisee_returnsCheckReturnAvailableRequest"}}], "tags": ["FranchiseeReturnService"]}}, "/api/v2/supply/mobile/franchisee/return/product": {"get": {"summary": "查询可退货商品", "operationId": "FranchiseeReturnService_GetValidProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_returnsGetValidProductResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "description": "id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "description": "分页大小", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "跳过行数", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "include_total", "description": "返回总条数", "in": "query", "required": false, "type": "boolean"}, {"name": "product_name", "description": "商户id\nuint64 partner_id = 5;\n商品名称", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "store_id", "description": "门店id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "category_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "order_by", "description": "排序条件", "in": "query", "required": false, "type": "string"}, {"name": "sort", "description": "asc or desc，暂时未使用", "in": "query", "required": false, "type": "string"}, {"name": "search", "description": "搜索商品", "in": "query", "required": false, "type": "string"}, {"name": "search_fields", "description": "搜索字段", "in": "query", "required": false, "type": "string"}], "tags": ["FranchiseeReturnService"]}}, "/api/v2/supply/mobile/franchisee/return/store/{store_id}/category": {"get": {"summary": "按门店id查询可退货的商品分类", "operationId": "FranchiseeReturnService_GetReturnCategoryByBranchId", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_returnsGetReturnCategoryByBranchIdResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "store_id", "description": "门店id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "is_franchisee", "description": "是否是加盟", "in": "query", "required": false, "type": "boolean"}, {"name": "distr_type", "description": "物流模式", "in": "query", "required": false, "type": "string"}], "tags": ["FranchiseeReturnService"]}}, "/api/v2/supply/mobile/franchisee/return/{id}/history": {"get": {"summary": "根据id查询单据历史记录", "operationId": "FranchiseeReturnService_GetHistoryById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_returnsHistoryResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "description": "退货单id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["FranchiseeReturnService"]}}, "/api/v2/supply/mobile/franchisee/returns": {"get": {"summary": "查询退货单", "operationId": "FranchiseeReturnService_ListReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_returnsListReturnResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "store_ids", "description": "门店id", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "status", "description": "订单状态", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "start_date", "description": "退货开始日期", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "退货结束日期", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "code", "description": "退货单号\ndq目前没有", "in": "query", "required": false, "type": "string"}, {"name": "limit", "description": "收货单号\ndq目前没有\nstring receiving_ids = 10;\n分页大小", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "跳过行数", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "include_total", "description": "返回总条数", "in": "query", "required": false, "type": "boolean"}, {"name": "is_direct", "description": "是否直送", "in": "query", "required": false, "type": "boolean"}, {"name": "order_by", "description": "排序", "in": "query", "required": false, "type": "string"}, {"name": "logistics_type", "description": "单据物流来源类型:BREAD 面包工厂", "in": "query", "required": false, "type": "string"}, {"name": "is_adjust", "description": "调整单标志", "in": "query", "required": false, "type": "boolean"}, {"name": "sort", "description": "排序字段", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序顺序", "in": "query", "required": false, "type": "string"}, {"name": "storage_type", "in": "query", "required": false, "type": "string"}, {"name": "type", "description": "来源：原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD", "in": "query", "required": false, "type": "string"}, {"name": "sub_type", "description": "branch类型： store/ warehouse/ machining", "in": "query", "required": false, "type": "string"}, {"name": "receive_code", "description": "收货单号/退货入库单", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "return_date_from", "description": "退货开始日期", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "return_date_to", "description": "退货结束日期", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "delivery_date_from", "description": "提货开始日期", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "delivery_date_to", "description": "提货结束日期", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "return_number", "description": "差异单号", "in": "query", "required": false, "type": "string"}, {"name": "product_ids", "description": "商品ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "return_by", "description": "退货方id", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "return_to", "description": "退货方接收方", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "source_id", "description": "来源id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "source_code", "in": "query", "required": false, "type": "string"}, {"name": "payment_ways", "in": "query", "required": false, "type": "string"}, {"name": "delivery_code", "description": "发货单", "in": "query", "required": false, "type": "string"}, {"name": "ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}], "tags": ["FranchiseeReturnService"]}, "post": {"summary": "创建退货单", "operationId": "FranchiseeReturnService_CreateReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_returnsCreateReturnResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/franchisee_returnsCreateReturnRequest"}}], "tags": ["FranchiseeReturnService"]}}, "/api/v2/supply/mobile/franchisee/returns/cold": {"get": {"summary": "查询退货单--冷链", "operationId": "FranchiseeReturnService_ListReturnCold", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_returnsListReturnResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "store_ids", "description": "门店id", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "status", "description": "订单状态", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "start_date", "description": "退货开始日期", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "退货结束日期", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "code", "description": "退货单号\ndq目前没有", "in": "query", "required": false, "type": "string"}, {"name": "limit", "description": "收货单号\ndq目前没有\nstring receiving_ids = 10;\n分页大小", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "跳过行数", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "include_total", "description": "返回总条数", "in": "query", "required": false, "type": "boolean"}, {"name": "is_direct", "description": "是否直送", "in": "query", "required": false, "type": "boolean"}, {"name": "logistics_type", "description": "单据物流来源类型:BREAD 面包工厂", "in": "query", "required": false, "type": "string"}, {"name": "is_adjust", "description": "调整单标志", "in": "query", "required": false, "type": "boolean"}, {"name": "sort", "description": "排序字段", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序顺序", "in": "query", "required": false, "type": "string"}, {"name": "storage_type", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "return_date_from", "description": "退货开始日期", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "return_date_to", "description": "退货结束日期", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "delivery_date_from", "description": "提货开始日期", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "delivery_date_to", "description": "提货结束日期", "in": "query", "required": false, "type": "string", "format": "date-time"}], "tags": ["FranchiseeReturnService"]}}, "/api/v2/supply/mobile/franchisee/returns/{id}": {"get": {"summary": "根据id查询退货单详情", "operationId": "FranchiseeReturnService_GetReturnById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_returnsReturns"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "description": "退货单id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "description": "是否显示详情\n可否删除？\nbool is_details = 2;", "in": "query", "required": false, "type": "string"}], "tags": ["FranchiseeReturnService"]}}, "/api/v2/supply/mobile/franchisee/returns/{id}/approve": {"post": {"summary": "审核退货单", "operationId": "FranchiseeReturnService_ApproveReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_returnsApproveReturnResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"trans_type": {"type": "string", "title": "提货方式"}, "lan": {"type": "string"}}}}], "tags": ["FranchiseeReturnService"]}}, "/api/v2/supply/mobile/franchisee/returns/{id}/confirm": {"post": {"summary": "确认退货", "operationId": "FranchiseeReturnService_ConfirmReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_returnsConfirmReturnResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"lan": {"type": "string"}}}}], "tags": ["FranchiseeReturnService"]}}, "/api/v2/supply/mobile/franchisee/returns/{id}/delete": {"post": {"summary": "删除新建的退货单", "operationId": "FranchiseeReturnService_DeleteReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_returnsDeleteReturnResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"lan": {"type": "string"}}}}], "tags": ["FranchiseeReturnService"]}}, "/api/v2/supply/mobile/franchisee/returns/{id}/delivery": {"post": {"summary": "确认提货", "operationId": "FranchiseeReturnService_DeliveryReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_returnsDeliveryReturnResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"lan": {"type": "string"}, "fs_code": {"type": "string", "title": "快递单号"}}}}], "tags": ["FranchiseeReturnService"]}}, "/api/v2/supply/mobile/franchisee/returns/{id}/product": {"get": {"summary": "根据id查询退货单商品详情\n查询退货单详情时需调用", "operationId": "FranchiseeReturnService_GetReturnProductById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_returnsGetReturnProductByIdResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "limit", "description": "商户id\nuint64 partner_id = 2;\n分页大小", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "跳过行数", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "include_total", "description": "返回总条数", "in": "query", "required": false, "type": "boolean"}, {"name": "sort", "description": "排序字段", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序顺序", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["FranchiseeReturnService"]}}, "/api/v2/supply/mobile/franchisee/returns/{id}/reject": {"post": {"summary": "驳回退货单", "operationId": "FranchiseeReturnService_RejectReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_returnsRejectReturnResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"reject_reason": {"type": "string", "title": "驳回原因"}, "lan": {"type": "string"}}}}], "tags": ["FranchiseeReturnService"]}}, "/api/v2/supply/mobile/franchisee/returns/{id}/submit": {"post": {"summary": "提交退货单", "operationId": "FranchiseeReturnService_SubmitReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_returnsSubmitReturnResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"lan": {"type": "string"}}}}], "tags": ["FranchiseeReturnService"]}}, "/api/v2/supply/mobile/franchisee/returns/{id}/update": {"post": {"summary": "更新退货单\n修改退货数量 - 无保存修改按钮\n添加退货商品", "operationId": "FranchiseeReturnService_UpdateReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_returnsUpdateReturnResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"products": {"type": "array", "items": {"$ref": "#/definitions/franchisee_returnsProduct"}}, "return_reason": {"type": "string", "title": "退货原因"}, "remark": {"type": "string", "title": "备注"}, "return_delivery_date": {"type": "string", "format": "date-time", "title": "退货交货时间"}, "return_to": {"type": "string", "format": "uint64", "title": "供应商/配送中心"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/franchisee_returnsAttachments"}, "title": "附件"}, "lan": {"type": "string"}, "logistics_type": {"type": "string"}}}}], "tags": ["FranchiseeReturnService"]}}, "/api/v2/supply/mobile/franchisee/returns/{id}/update/remark": {"post": {"summary": "修改备注", "operationId": "FranchiseeReturnService_UpdateRemark", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_returnsUpdateRemarkResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"remark": {"type": "string", "title": "remark"}, "lan": {"type": "string", "title": "user_id\nuint64 user_id = 3;"}}}}], "tags": ["FranchiseeReturnService"]}}}, "definitions": {"GetReturnCategoryByBranchIdResponseCategoryItem": {"type": "object", "properties": {"category_id": {"type": "string", "format": "uint64"}, "category_name": {"type": "string"}, "product_count": {"type": "string", "format": "uint64"}}}, "franchisee_returnsApproveReturnResponse": {"type": "object", "properties": {"payload": {"type": "boolean"}}}, "franchisee_returnsAttachments": {"type": "object", "properties": {"type": {"type": "string"}, "url": {"type": "string"}}}, "franchisee_returnsCheckReturnAvailableRequest": {"type": "object", "properties": {"return_by": {"type": "string", "format": "uint64", "title": "退货门店\n= 门店id"}, "return_delivery_date": {"type": "string", "format": "date-time", "title": "交货日期"}, "type": {"type": "string", "title": "退货类型"}, "sub_type": {"type": "string", "title": "子类型"}, "return_reason": {"type": "string", "title": "退货原因"}, "remark": {"type": "string", "title": "备注"}, "products": {"type": "array", "items": {"$ref": "#/definitions/franchisee_returnsProduct"}, "title": "商品"}, "is_direct": {"type": "boolean", "title": "是否直送"}, "return_to": {"type": "string", "format": "uint64", "title": "仓库/配送中心"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/franchisee_returnsAttachments"}, "title": "附件"}, "logistics_type": {"type": "string", "title": "单据物流来源类型:BREAD 面包工厂"}, "is_adjust": {"type": "boolean", "title": "调整单标志"}, "receiving_id": {"type": "string", "format": "uint64"}, "lan": {"type": "string"}, "return_id": {"type": "string", "format": "uint64"}, "source_id": {"type": "string", "format": "uint64", "title": "退货单来源id"}, "source_code": {"type": "string"}}}, "franchisee_returnsCommonReply": {"type": "object", "properties": {"success": {"type": "boolean"}}}, "franchisee_returnsConfirmReturnResponse": {"type": "object", "properties": {"payload": {"type": "boolean"}}}, "franchisee_returnsCreateAdjustReturnResponse": {"type": "object", "properties": {"return_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "franchisee_returnsCreateReturnRequest": {"type": "object", "properties": {"return_by": {"type": "string", "format": "uint64", "title": "退货方id"}, "logistics_type": {"type": "string", "title": "单据物流来源类型:PUR 直送/ NMD 配送"}, "type": {"type": "string", "title": "退货类型: 原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD/ FRS 加盟商退货"}, "sub_type": {"type": "string", "title": "退货方类型： store/ warehouse/ machining"}, "return_delivery_date": {"type": "string", "format": "date-time", "title": "交货日期"}, "source_id": {"type": "string", "format": "uint64", "title": "退货单来源id"}, "source_code": {"type": "string"}, "return_reason": {"type": "string", "title": "退货原因"}, "remark": {"type": "string", "title": "备注"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/franchisee_returnsAttachments"}, "title": "附件"}, "request_id": {"type": "string", "format": "uint64", "title": "唯一请求号，保证不重复请求"}, "products": {"type": "array", "items": {"$ref": "#/definitions/franchisee_returnsProduct"}, "title": "商品"}, "lan": {"type": "string", "title": "语言"}}}, "franchisee_returnsCreateReturnResponse": {"type": "object", "properties": {"return_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "payload": {"type": "boolean"}}}, "franchisee_returnsDeleteReturnResponse": {"type": "object", "properties": {"payload": {"type": "boolean"}}}, "franchisee_returnsDeliveryReturnResponse": {"type": "object", "properties": {"payload": {"type": "boolean"}}}, "franchisee_returnsGetReturnCategoryByBranchIdResponse": {"type": "object", "properties": {"category_items": {"type": "array", "items": {"$ref": "#/definitions/GetReturnCategoryByBranchIdResponseCategoryItem"}}}}, "franchisee_returnsGetReturnProductByIdResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/franchisee_returnsProductDetail"}}, "total": {"type": "string", "format": "uint64"}, "total_amount": {"type": "number", "format": "double"}}}, "franchisee_returnsGetValidProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/franchisee_returnsValidProduct"}}, "total": {"type": "integer", "format": "int64"}, "inventory_unchanged_rows": {"type": "array", "items": {"$ref": "#/definitions/franchisee_returnsValidProduct"}, "title": "库存未异动的商品"}}}, "franchisee_returnsHistory": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "历史记录id"}, "status": {"type": "string", "title": "退货单状态"}, "updated_by": {"type": "string", "format": "uint64", "title": "操作人名称或者系统自动操作"}, "updated_at": {"type": "string", "format": "date-time", "title": "操作时间"}, "updated_by_name": {"type": "string"}}}, "franchisee_returnsHistoryResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/franchisee_returnsHistory"}}, "total": {"type": "string", "format": "uint64"}}}, "franchisee_returnsListReturnResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/franchisee_returnsReturns"}}, "total": {"type": "string", "format": "uint64"}}}, "franchisee_returnsProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64", "title": "商品表里的id（supply_receiving_diff_product.id)"}, "unit_id": {"type": "string", "format": "uint64", "title": "订货单位id"}, "quantity": {"type": "number", "format": "double", "title": "数量"}, "confirmed_quantity": {"type": "number", "format": "double", "title": "收货数量"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "unit_name": {"type": "string"}, "unit_spec": {"type": "string"}, "unit_rate": {"type": "number", "format": "float", "title": "单位换算比例"}, "tax_rate": {"type": "number", "format": "float"}, "price": {"type": "number", "format": "float"}, "price_tax": {"type": "number", "format": "float"}, "id": {"type": "string", "format": "uint64"}, "return_to": {"type": "string", "format": "uint64", "title": "退货接收方"}, "logistics_type": {"type": "string"}, "refund_amount": {"type": "number", "format": "double", "title": "退款金额"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/franchisee_returnsAttachments"}}}}, "franchisee_returnsProductDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "return_id": {"type": "string", "format": "uint64", "title": "退货单id"}, "return_by": {"type": "string", "format": "uint64", "title": "退货门店"}, "material_number": {"type": "string", "title": "物料编码"}, "product_code": {"type": "string", "title": "商品编码"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_name": {"type": "string", "title": "商品名称"}, "quantity": {"type": "number", "format": "double", "title": "数量"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "单位id"}, "accounting_unit_name": {"type": "string", "title": "单位名称"}, "accounting_unit_spec": {"type": "string", "title": "单位规格"}, "unit_id": {"type": "string", "format": "uint64", "title": "单位id"}, "unit_name": {"type": "string", "title": "单位名称"}, "unit_rate": {"type": "number", "format": "double", "title": "单位换算比例"}, "unit_spec": {"type": "string", "title": "单位规格"}, "accounting_confirmed_quantity": {"type": "number", "format": "double", "title": "确认退货数量"}, "accounting_quantity": {"type": "number", "format": "double"}, "accounting_returned_quantity": {"type": "number", "format": "double"}, "confirmed_quantity": {"type": "number", "format": "double", "title": "确认收货数量"}, "returned_quantity": {"type": "number", "format": "double", "title": "退货数量"}, "is_confirmed": {"type": "boolean", "title": "状态"}, "return_date": {"type": "string", "format": "date-time", "title": "退货日期"}, "return_to": {"type": "string", "format": "uint64", "title": "接收方"}, "inventory_status": {"type": "string", "title": "库存引擎调用\nenum inventoryStatus {\n    SENT = 1; // 已发送\n    SENT_SUC = 2; //调用成功\n    SENT_FAIL = 3; //调用失败\n}"}, "inventory_req_id": {"type": "string", "format": "uint64", "title": "库存id预留字段"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人id"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新日期"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "created_name": {"type": "string", "title": "创建人"}, "updated_name": {"type": "string", "title": "更新人"}, "storage_type": {"type": "string"}, "price": {"type": "number", "format": "double", "title": "未税单价"}, "tax_rate": {"type": "number", "format": "float", "title": "税率"}, "price_tax": {"type": "number", "format": "double", "title": "含税单价"}, "sum_price": {"type": "number", "format": "double", "title": "采购总价"}, "tax_amount": {"type": "number", "format": "double", "title": "税额"}, "return_price": {"type": "number", "format": "double", "title": "退货金额"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/franchisee_returnsAttachments"}, "title": "附件"}, "retail_price": {"type": "number", "format": "double", "title": "零售价"}}}, "franchisee_returnsRejectReturnResponse": {"type": "object", "properties": {"payload": {"type": "boolean"}}}, "franchisee_returnsReturns": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "code": {"type": "string", "title": "退货单单号"}, "return_by": {"type": "string", "format": "uint64", "title": "退货门店"}, "return_number": {"type": "string", "title": "退货编号"}, "status": {"type": "string", "title": "退货单状态"}, "review_by": {"type": "string", "format": "uint64", "title": "审核人"}, "is_returned": {"type": "boolean", "title": "提货状态\n原extends"}, "return_delivery_number": {"type": "string", "title": "退货提货单单单号"}, "return_date": {"type": "string", "format": "date-time", "title": "退货日期"}, "return_delivery_date": {"type": "string", "format": "date-time", "title": "预计退货提货时间"}, "return_reason": {"type": "string", "title": "退货原因"}, "type": {"type": "string", "title": "退货方式\n{ '01': '统退', '02': '直退' }"}, "sub_type": {"type": "string", "title": "退货类型\n{ '01': '自主退货', '02': '召回' }"}, "remark": {"type": "string", "title": "备注"}, "store_secondary_id": {"type": "string", "title": "预留门店号"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人id"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "return_to": {"type": "string", "format": "uint64", "title": "退货接受方 门店id"}, "inventory_status": {"type": "string", "title": "// 库存引擎调用\nenum inventoryStatus {\n    SENT = 0; // 已发送\n    SENT_SUC = 2; //调用成功\n    SENT_FAIL = 3; //调用失败\n}"}, "inventory_req_id": {"type": "string", "format": "uint64", "title": "库存id预留字段"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "is_direct": {"type": "boolean", "title": "收否直送"}, "reject_reason": {"type": "string", "title": "驳回原因"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/franchisee_returnsAttachments"}, "title": "附件"}, "created_name": {"type": "string", "title": "创建人"}, "updated_name": {"type": "string", "title": "更新人"}, "product_nums": {"type": "string", "format": "uint64", "title": "商品数量"}, "logistics_type": {"type": "string", "title": "单据物流来源类型:BREAD 面包工厂"}, "is_adjust": {"type": "boolean", "title": "调整单标志"}, "send_type": {"type": "string", "title": "对接三方的渠道"}, "trans_type": {"type": "string", "title": "提货方式"}, "storage_type": {"type": "string"}, "receiving_id": {"type": "string", "format": "uint64"}, "receiving_code": {"type": "string"}, "delivery_date": {"type": "string", "format": "date-time", "title": "实际提货日期"}, "request_id": {"type": "string", "format": "uint64"}, "refund_amount": {"type": "number", "format": "double", "title": "退款金额"}, "franchisee_id": {"type": "string", "format": "uint64", "title": "加盟商信息"}, "franchisee_code": {"type": "string"}, "franchisee_name": {"type": "string"}, "payment_way": {"type": "string", "title": "退款方式"}, "delivery_code": {"type": "string"}, "receive_code": {"type": "string"}, "source_id": {"type": "string", "format": "uint64", "title": "来源"}, "source_code": {"type": "string"}, "return_to_code": {"type": "string"}, "return_to_name": {"type": "string"}, "returns_price": {"type": "number", "format": "double", "title": "商品的报表信息"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "unit_spec": {"type": "string"}, "price_tax": {"type": "number", "format": "double"}, "quantity": {"type": "number", "format": "double"}, "return_by_code": {"type": "string"}, "return_by_name": {"type": "string"}, "refund_id": {"type": "string", "format": "uint64", "title": "退款单号"}, "refund_code": {"type": "string"}}}, "franchisee_returnsSubmitReturnResponse": {"type": "object", "properties": {"payload": {"type": "boolean"}}}, "franchisee_returnsUnit": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "quantity": {"type": "integer", "format": "int32", "title": "单位数量"}, "rate": {"type": "number", "format": "double"}, "name": {"type": "string", "title": "单位名称"}}}, "franchisee_returnsUpdateRemarkResponse": {"type": "object", "properties": {"payload": {"type": "boolean"}}}, "franchisee_returnsUpdateReturnResponse": {"type": "object", "properties": {"payload": {"type": "boolean"}}}, "franchisee_returnsValidProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64", "title": "id"}, "product_code": {"type": "string", "title": "商品编号"}, "product_name": {"type": "string", "title": "商品名称"}, "spec": {"type": "string", "title": "规格"}, "unit": {"$ref": "#/definitions/franchisee_returnsUnit", "title": "单位"}, "tax_price": {"type": "number", "format": "double", "title": "单价"}, "distr_by": {"type": "string", "format": "uint64", "title": "配送中心"}, "real_inventory_qty": {"type": "number", "format": "double", "title": "实时库存数量"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}}}