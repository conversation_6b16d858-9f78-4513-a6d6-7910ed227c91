{"swagger": "2.0", "info": {"title": "mobile/mobile_return.proto", "version": "version not set"}, "tags": [{"name": "MobileReturnService"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/mobile/return/{id}/history": {"get": {"summary": "根据id查询单据历史记录", "operationId": "MobileReturnService_GetHistoryById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_returnsHistoryResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "description": "退货单id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["MobileReturnService"]}}, "/api/v2/supply/mobile/returns": {"get": {"summary": "查询退货单", "operationId": "MobileReturnService_ListReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_returnsListReturnResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "store_ids", "description": "退货方id", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "status", "description": "订单状态", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "code", "description": "退货单号", "in": "query", "required": false, "type": "string"}, {"name": "source_id", "description": "来源id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "source_code", "in": "query", "required": false, "type": "string"}, {"name": "logistics_type", "description": "单据物流来源类型:PUR 直送/ NMD 配送", "in": "query", "required": false, "type": "string"}, {"name": "type", "description": "退货类型: 原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD", "in": "query", "required": false, "type": "string"}, {"name": "sub_type", "description": "退货方类型： store/ warehouse/ machining", "in": "query", "required": false, "type": "string"}, {"name": "product_ids", "description": "商品ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "return_date_from", "description": "退货开始日期", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "return_date_to", "description": "退货结束日期", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "delivery_date_from", "description": "提货开始日期", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "delivery_date_to", "description": "提货结束日期", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "limit", "description": "分页大小", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "跳过行数", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "order", "description": "排序", "in": "query", "required": false, "type": "string"}, {"name": "sort", "description": "排序字段", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "return_tos", "description": "配送接收方id", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}], "tags": ["MobileReturnService"]}, "post": {"summary": "创建退货单", "operationId": "MobileReturnService_CreateReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_returnsCreateReturnResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mobile_returnsCreateReturnRequest"}}], "tags": ["MobileReturnService"]}}, "/api/v2/supply/mobile/returns/store/product/valid": {"get": {"summary": "查询可退货商品", "operationId": "MobileReturnService_GetValidReturnProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_returnsValidReturnProductResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "store_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "distr_type", "description": "物流模式", "in": "query", "required": false, "type": "string"}, {"name": "distr_by", "description": "配送中心", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "category_ids", "description": "商品分类", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "product_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "limit", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "order_by", "description": "排序条件", "in": "query", "required": false, "type": "string"}, {"name": "sort", "description": "asc or desc，暂时未使用", "in": "query", "required": false, "type": "string"}, {"name": "search", "description": "搜索商品", "in": "query", "required": false, "type": "string"}, {"name": "search_fields", "description": "搜索字段", "in": "query", "required": false, "type": "string"}], "tags": ["MobileReturnService"]}}, "/api/v2/supply/mobile/returns/update/by/{id}": {"post": {"summary": "更新退货单", "operationId": "MobileReturnService_UpdateReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_returnsDealReturnResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"products": {"type": "array", "items": {"$ref": "#/definitions/mobile_returnsProduct"}}, "return_reason": {"type": "string", "title": "退货原因"}, "remark": {"type": "string", "title": "备注"}, "return_delivery_date": {"type": "string", "format": "date-time", "title": "退货交货时间"}, "return_to": {"type": "string", "format": "uint64", "title": "供应商/配送中心"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/mobile_returnsAttachments"}, "title": "附件"}, "lan": {"type": "string"}, "logistics_type": {"type": "string"}}}}], "tags": ["MobileReturnService"]}}, "/api/v2/supply/mobile/returns/{id}": {"get": {"summary": "根据id查询退货单详情", "operationId": "MobileReturnService_GetReturnById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_returnsReturns"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "description": "退货单id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "description": "是否显示详情\n可否删除？\nbool is_details = 2;", "in": "query", "required": false, "type": "string"}], "tags": ["MobileReturnService"]}}, "/api/v2/supply/mobile/returns/{id}/product": {"get": {"summary": "根据id查询退货单商品详情", "operationId": "MobileReturnService_GetReturnProductById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_returnsGetReturnProductByIdResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "limit", "description": "商户id\nuint64 partner_id = 2;\n分页大小", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "跳过行数", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "include_total", "description": "返回总条数", "in": "query", "required": false, "type": "boolean"}, {"name": "sort", "description": "排序字段", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序顺序", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["MobileReturnService"]}}, "/api/v2/supply/mobile/returns/{id}/{action}": {"post": {"summary": "处理退货单", "operationId": "MobileReturnService_DealReturn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_returnsDealReturnResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "action", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"reject_reason": {"type": "string"}, "trans_type": {"type": "string"}}, "title": "action枚举：\nsubmit-提交\nreject-驳回\napprove-审核\ndelivery-提货\nconfirm-确认\ndelete-删除"}}], "tags": ["MobileReturnService"]}}}, "definitions": {"mobile_returnsAttachments": {"type": "object", "properties": {"type": {"type": "string"}, "url": {"type": "string"}}}, "mobile_returnsCreateReturnRequest": {"type": "object", "properties": {"return_by": {"type": "string", "format": "uint64", "title": "退货方id"}, "logistics_type": {"type": "string", "title": "单据物流来源类型:PUR 直送/ NMD 配送"}, "type": {"type": "string", "title": "退货类型: 原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD"}, "sub_type": {"type": "string", "title": "退货方类型： store/ warehouse/ machining"}, "return_delivery_date": {"type": "string", "format": "date-time", "title": "交货日期"}, "source_id": {"type": "string", "format": "uint64", "title": "退货单来源id"}, "source_code": {"type": "string"}, "return_reason": {"type": "string", "title": "退货原因"}, "remark": {"type": "string", "title": "备注"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/mobile_returnsAttachments"}, "title": "附件"}, "request_id": {"type": "string", "format": "uint64", "title": "唯一请求号，保证不重复请求"}, "products": {"type": "array", "items": {"$ref": "#/definitions/mobile_returnsProduct"}, "title": "商品"}, "lan": {"type": "string", "title": "语言"}}}, "mobile_returnsCreateReturnResponse": {"type": "object", "properties": {"return_id": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "payload": {"type": "boolean"}}}, "mobile_returnsDealReturnResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "status": {"type": "boolean"}}}, "mobile_returnsGetReturnProductByIdResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_returnsProductDetail"}}, "total": {"type": "string", "format": "uint64"}}}, "mobile_returnsHistory": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "历史记录id"}, "status": {"type": "string", "title": "退货单状态"}, "updated_by": {"type": "string", "format": "uint64", "title": "操作人名称或者系统自动操作"}, "updated_at": {"type": "string", "format": "date-time", "title": "操作时间"}, "updated_by_name": {"type": "string"}}}, "mobile_returnsHistoryResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_returnsHistory"}}, "total": {"type": "string", "format": "uint64"}}}, "mobile_returnsListReturnResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_returnsReturns"}}, "total": {"type": "string", "format": "uint64"}}}, "mobile_returnsProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64", "title": "商品表里的id（supply_receiving_diff_product.id)"}, "unit_id": {"type": "string", "format": "uint64", "title": "订货单位id"}, "quantity": {"type": "number", "format": "double", "title": "数量"}, "confirmed_quantity": {"type": "number", "format": "double", "title": "收货数量"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "unit_name": {"type": "string"}, "unit_spec": {"type": "string"}, "unit_rate": {"type": "number", "format": "float", "title": "单位换算比例"}, "tax_rate": {"type": "number", "format": "float"}, "price": {"type": "number", "format": "float"}, "price_tax": {"type": "number", "format": "float"}, "id": {"type": "string", "format": "uint64"}, "return_to": {"type": "string", "format": "uint64", "title": "仓库/配送中心"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/mobile_returnsAttachments"}}, "logistics_type": {"type": "string"}}}, "mobile_returnsProductDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "return_id": {"type": "string", "format": "uint64", "title": "退货单id"}, "return_by": {"type": "string", "format": "uint64", "title": "退货门店"}, "material_number": {"type": "string", "title": "物料编码"}, "product_code": {"type": "string", "title": "商品编码"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_name": {"type": "string", "title": "商品名称"}, "quantity": {"type": "number", "format": "double", "title": "数量"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "单位id"}, "accounting_unit_name": {"type": "string", "title": "单位名称"}, "accounting_unit_spec": {"type": "string", "title": "单位规格"}, "unit_id": {"type": "string", "format": "uint64", "title": "单位id"}, "unit_name": {"type": "string", "title": "单位名称"}, "unit_rate": {"type": "number", "format": "double", "title": "单位换算比例"}, "unit_spec": {"type": "string", "title": "单位规格"}, "accounting_confirmed_quantity": {"type": "number", "format": "double", "title": "确认退货数量"}, "accounting_quantity": {"type": "number", "format": "double"}, "accounting_returned_quantity": {"type": "number", "format": "double"}, "confirmed_quantity": {"type": "number", "format": "double", "title": "确认收货数量"}, "returned_quantity": {"type": "number", "format": "double", "title": "退货数量"}, "is_confirmed": {"type": "boolean", "title": "状态"}, "return_date": {"type": "string", "format": "date-time", "title": "退货日期"}, "return_to": {"type": "string", "format": "uint64", "title": "接收方"}, "inventory_status": {"type": "string", "title": "库存引擎调用\nenum inventoryStatus {\n    SENT = 1; // 已发送\n    SENT_SUC = 2; //调用成功\n    SENT_FAIL = 3; //调用失败\n}"}, "inventory_req_id": {"type": "string", "format": "uint64", "title": "库存id预留字段"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人id"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新日期"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "created_name": {"type": "string", "title": "创建人"}, "updated_name": {"type": "string", "title": "更新人"}, "storage_type": {"type": "string"}, "category_code": {"type": "string"}, "category_id": {"type": "string", "format": "uint64"}, "category_name": {"type": "string"}, "price": {"type": "number", "format": "double", "title": "未税单价"}, "tax_rate": {"type": "number", "format": "float", "title": "税率"}, "price_tax": {"type": "number", "format": "double", "title": "含税单价"}, "sum_price": {"type": "number", "format": "double", "title": "采购总价"}, "retail_price": {"type": "number", "format": "double", "title": "零售价"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/mobile_returnsAttachments"}, "title": "附件"}}}, "mobile_returnsReturns": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "code": {"type": "string", "title": "退货单单号"}, "return_by": {"type": "string", "format": "uint64", "title": "退货门店"}, "return_number": {"type": "string", "title": "退货编号"}, "status": {"type": "string", "title": "退货单状态"}, "review_by": {"type": "string", "format": "uint64", "title": "审核人"}, "return_delivery_number": {"type": "string", "title": "退货提货单单单号"}, "return_date": {"type": "string", "format": "date-time", "title": "退货日期"}, "return_delivery_date": {"type": "string", "format": "date-time", "title": "预计退货提货时间"}, "return_reason": {"type": "string", "title": "退货原因"}, "type": {"type": "string", "title": "退货类型: 原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD"}, "sub_type": {"type": "string", "title": "退货方类型： store/ warehouse/ machining"}, "remark": {"type": "string", "title": "备注"}, "store_secondary_id": {"type": "string", "title": "预留门店号"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人id"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "return_to": {"type": "string", "format": "uint64", "title": "退货接受方 门店id"}, "inventory_status": {"type": "string", "title": "// 库存引擎调用\nenum inventoryStatus {\n    SENT = 0; // 已发送\n    SENT_SUC = 2; //调用成功\n    SENT_FAIL = 3; //调用失败\n}"}, "inventory_req_id": {"type": "string", "format": "uint64", "title": "库存id预留字段"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "reject_reason": {"type": "string", "title": "驳回原因"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/mobile_returnsAttachments"}, "title": "附件"}, "created_name": {"type": "string", "title": "创建人"}, "updated_name": {"type": "string", "title": "更新人"}, "product_nums": {"type": "string", "format": "uint64", "title": "商品数量"}, "logistics_type": {"type": "string", "title": "单据物流来源类型:PUR 直送/ NMD 配送"}, "trans_type": {"type": "string", "title": "提货方式"}, "source_id": {"type": "string", "format": "uint64", "title": "来源"}, "source_code": {"type": "string"}, "delivery_date": {"type": "string", "format": "date-time", "title": "实际提货日期"}, "request_id": {"type": "string", "format": "uint64"}, "return_to_name": {"type": "string", "title": "退货接受方 门店id"}, "phone": {"type": "string", "title": "退货接收方电话"}, "franchisee_id": {"type": "string", "format": "uint64", "title": "加盟商"}, "payment_way": {"type": "string"}, "receive_code": {"type": "string"}, "delivery_code": {"type": "string"}, "refund_id": {"type": "string", "format": "uint64", "title": "退款单号"}, "refund_code": {"type": "string"}}}, "mobile_returnsValidReturnProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64", "title": "商品"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "spec": {"type": "string", "title": "规格"}, "unit_id": {"type": "string", "format": "uint64", "title": "订货单位"}, "unit_name": {"type": "string"}, "unit_spec": {"type": "string"}, "unit_rate": {"type": "number", "format": "double"}, "category_id": {"type": "string", "format": "uint64", "title": "商品类别"}, "category_code": {"type": "string"}, "category_name": {"type": "string"}, "distr_by": {"type": "string", "format": "uint64", "title": "配送/供应商中心"}, "distr_type": {"type": "string", "title": "配送(NMD)、采购(PUR)"}, "product_type": {"type": "string"}, "storage_type": {"type": "string"}, "purchase_price": {"type": "number", "format": "double"}, "purchase_tax": {"type": "number", "format": "double"}, "tax_rate": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double"}, "price_tax": {"type": "number", "format": "double"}, "real_inventory_qty": {"type": "number", "format": "double", "title": "实时库存数量"}}}, "mobile_returnsValidReturnProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_returnsValidReturnProduct"}}, "total": {"type": "integer", "format": "int32"}, "inventory_unchanged_rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_returnsValidReturnProduct"}, "title": "库存未异动的商品"}}, "title": "获取可退货商品返回参数"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}}}