# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mobile/mobile_demand.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='mobile/mobile_demand.proto',
  package='mobile_demand',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1amobile/mobile_demand.proto\x12\rmobile_demand\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xba\x02\n\x11ListDemandRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x03 \x03(\t\x12\x11\n\tstore_ids\x18\x04 \x03(\x04\x12\x13\n\x0bhas_product\x18\x05 \x01(\t\x12\x11\n\tis_adjust\x18\x06 \x01(\x08\x12\x15\n\rschedule_name\x18\x07 \x01(\t\x12\r\n\x05\x63odes\x18\x08 \x03(\t\x12\x0c\n\x04sort\x18\t \x01(\t\x12\r\n\x05order\x18\n \x01(\t\x12\r\n\x05limit\x18\x0b \x01(\t\x12\x0e\n\x06offset\x18\x0c \x01(\t\x12\x0b\n\x03ids\x18\r \x03(\x04\x12\r\n\x05types\x18\x0e \x03(\t\"H\n\x12ListDemandResponse\x12#\n\x04rows\x18\x01 \x03(\x0b\x32\x15.mobile_demand.Demand\x12\r\n\x05total\x18\x02 \x01(\x04\"\xfa\x01\n\x06\x44\x65mand\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tis_adjust\x18\x07 \x01(\x08\x12\x0e\n\x06remark\x18\x08 \x01(\t\x12)\n\x08schedule\x18\t \x01(\x0b\x32\x17.mobile_demand.Schedule\x12$\n\x06orders\x18\n \x03(\x0b\x32\x14.mobile_demand.Order\x12\x0c\n\x04type\x18\x0b \x01(\t\x12\x13\n\x0bhas_product\x18\x0c \x01(\t\"\x8f\x02\n\x08Schedule\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06method\x18\x02 \x01(\t\x12\x14\n\x0cmonth_method\x18\x03 \x01(\t\x12\x13\n\x0bweek_method\x18\x04 \x01(\t\x12\x12\n\nday_method\x18\x05 \x01(\t\x12\x10\n\x08interval\x18\x06 \x01(\x04\x12.\n\nstart_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x36\n\x0bstatus_plan\x18\t \x03(\x0b\x32!.mobile_demand.ScheduleStatusPlan\"u\n\x12ScheduleStatusPlan\x12\x0c\n\x04time\x18\x01 \x01(\t\x12\x14\n\x0cstart_status\x18\x02 \x01(\t\x12\x12\n\nend_status\x18\x03 \x01(\t\x12\x13\n\x0btime_around\x18\x04 \x01(\t\x12\x12\n\ndoc_filter\x18\x05 \x01(\t\"H\n\x05Order\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x15\n\rdelivery_name\x18\x04 \x01(\t\"\"\n\x14GetDemandByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\"#\n\x15GetHistoryByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\"F\n\x0fHistoryResponse\x12$\n\x04rows\x18\x01 \x03(\x0b\x32\x16.mobile_demand.History\x12\r\n\x05total\x18\x02 \x01(\x04\"\xa8\x01\n\x07History\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x12\n\nupdated_by\x18\x03 \x01(\x04\x12.\n\nupdated_at\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x17\n\x0fupdated_by_name\x18\x05 \x01(\t\x12$\n\x06orders\x18\n \x03(\x0b\x32\x14.mobile_demand.Order\"`\n\x16GetProductsByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04sort\x18\t \x01(\t\x12\r\n\x05order\x18\n \x01(\t\x12\r\n\x05limit\x18\x0b \x01(\t\x12\x0e\n\x06offset\x18\x0c \x01(\t\"G\n\x10ProductsResponse\x12$\n\x04rows\x18\x01 \x03(\x0b\x32\x16.mobile_demand.Product\x12\r\n\x05total\x18\x02 \x01(\x04\"\x87\x06\n\x07Product\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x03 \x01(\t\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12\x0c\n\x04spec\x18\x05 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x06 \x01(\x04\x12\x15\n\rcategory_name\x18\x07 \x01(\t\x12\x14\n\x0cmin_quantity\x18\x08 \x01(\x01\x12\x1a\n\x12increment_quantity\x18\t \x01(\x01\x12\x14\n\x0cmax_quantity\x18\n \x01(\x01\x12\x10\n\x08quantity\x18\x0b \x01(\x01\x12\x11\n\tunit_name\x18\x0c \x01(\t\x12\x14\n\x0c\x61llow_adjust\x18\r \x01(\x08\x12\x17\n\x0f\x61llow_stocktake\x18\x0e \x01(\x08\x12\x16\n\x0e\x61llow_transfer\x18\x0f \x01(\x08\x12\x18\n\x10\x61llow_main_order\x18\x10 \x01(\x08\x12\x1b\n\x13\x61llow_self_purchase\x18\x11 \x01(\x08\x12\x1b\n\x13\x63ircle_per_thousand\x18\x12 \x01(\t\x12\x18\n\x10replenish_method\x18\x13 \x01(\t\x12\x19\n\x11safe_stock_method\x18\x14 \x01(\t\x12\x10\n\x08unfreeze\x18\x15 \x01(\x01\x12 \n\x18\x64istribution_region_name\x18\x16 \x01(\t\x12\x19\n\x11\x64istribution_name\x18\x17 \x01(\t\x12\x1c\n\x14planned_arrival_days\x18\x19 \x01(\x01\x12\x13\n\x0b\x63ircle_type\x18\x1a \x01(\t\x12\x0e\n\x06\x63ycles\x18\x1b \x03(\t\x12.\n\nstart_date\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rinterval_days\x18\x1d \x01(\x01\x12\x19\n\x11\x64istribution_type\x18\x1e \x01(\t\x12\x1a\n\x12store_unfinish_qty\x18\x1f \x01(\x01\x12\x15\n\rstore_inv_qty\x18  \x01(\x01\x12\x12\n\npartner_id\x18! \x01(\x04\"d\n\x14UpdateProductRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06remark\x18\x02 \x01(\t\x12\x30\n\x08products\x18\x03 \x03(\x0b\x32\x1e.mobile_demand.ProductQuantity\"C\n\x0fProductQuantity\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x10\n\x08quantity\x18\x03 \x01(\x01\"]\n\x16UpdateProductsResponse\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12\x30\n\x08products\x18\x02 \x03(\x0b\x32\x1e.mobile_demand.ProductQuantity\"2\n\x1cGetProductsDetailByIdRequest\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\"]\n\x15\x44\x65\x61lDemandByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06\x61\x63tion\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x04 \x01(\t\"\x1f\n\x08Response\x12\x13\n\x0b\x64\x65scription\x18\x01 \x01(\t2\xe7\x07\n\x13MobileDemandService\x12w\n\nListDemand\x12 .mobile_demand.ListDemandRequest\x1a!.mobile_demand.ListDemandResponse\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/api/v2/supply/mobile/demand\x12v\n\rGetDemandById\x12#.mobile_demand.GetDemandByIdRequest\x1a\x15.mobile_demand.Demand\")\x82\xd3\xe4\x93\x02#\x12!/api/v2/supply/mobile/demand/{id}\x12\x89\x01\n\x0eGetHistoryById\x12$.mobile_demand.GetHistoryByIdRequest\x1a\x1e.mobile_demand.HistoryResponse\"1\x82\xd3\xe4\x93\x02+\x12)/api/v2/supply/mobile/demand/{id}/history\x12\x8c\x01\n\x0fGetProductsById\x12%.mobile_demand.GetProductsByIdRequest\x1a\x1f.mobile_demand.ProductsResponse\"1\x82\xd3\xe4\x93\x02+\x12)/api/v2/supply/mobile/demand/{id}/product\x12\x92\x01\n\x0eUpdateProducts\x12#.mobile_demand.UpdateProductRequest\x1a%.mobile_demand.UpdateProductsResponse\"4\x82\xd3\xe4\x93\x02.\")/api/v2/supply/mobile/demand/{id}/product:\x01*\x12\xa0\x01\n\x15GetProductsDetailById\x12+.mobile_demand.GetProductsDetailByIdRequest\x1a\x1f.mobile_demand.ProductsResponse\"9\x82\xd3\xe4\x93\x02\x33\x12\x31/api/v2/supply/mobile/demand/product/{product_id}\x12\x8b\x01\n\x0e\x44\x65\x61lDemandById\x12$.mobile_demand.DealDemandByIdRequest\x1a\x17.mobile_demand.Response\":\x82\xd3\xe4\x93\x02\x34\"//api/v2/supply/mobile/demand/deal/{id}/{action}:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_LISTDEMANDREQUEST = _descriptor.Descriptor(
  name='ListDemandRequest',
  full_name='mobile_demand.ListDemandRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='mobile_demand.ListDemandRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='mobile_demand.ListDemandRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_demand.ListDemandRequest.status', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='mobile_demand.ListDemandRequest.store_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='has_product', full_name='mobile_demand.ListDemandRequest.has_product', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='mobile_demand.ListDemandRequest.is_adjust', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='mobile_demand.ListDemandRequest.schedule_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='codes', full_name='mobile_demand.ListDemandRequest.codes', index=7,
      number=8, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='mobile_demand.ListDemandRequest.sort', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_demand.ListDemandRequest.order', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_demand.ListDemandRequest.limit', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_demand.ListDemandRequest.offset', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='mobile_demand.ListDemandRequest.ids', index=12,
      number=13, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='types', full_name='mobile_demand.ListDemandRequest.types', index=13,
      number=14, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=109,
  serialized_end=423,
)


_LISTDEMANDRESPONSE = _descriptor.Descriptor(
  name='ListDemandResponse',
  full_name='mobile_demand.ListDemandResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_demand.ListDemandResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_demand.ListDemandResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=425,
  serialized_end=497,
)


_DEMAND = _descriptor.Descriptor(
  name='Demand',
  full_name='mobile_demand.Demand',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_demand.Demand.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_demand.Demand.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_demand.Demand.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='mobile_demand.Demand.demand_date', index=3,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='mobile_demand.Demand.is_adjust', index=4,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_demand.Demand.remark', index=5,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule', full_name='mobile_demand.Demand.schedule', index=6,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orders', full_name='mobile_demand.Demand.orders', index=7,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='mobile_demand.Demand.type', index=8,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='has_product', full_name='mobile_demand.Demand.has_product', index=9,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=500,
  serialized_end=750,
)


_SCHEDULE = _descriptor.Descriptor(
  name='Schedule',
  full_name='mobile_demand.Schedule',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='mobile_demand.Schedule.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='method', full_name='mobile_demand.Schedule.method', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='month_method', full_name='mobile_demand.Schedule.month_method', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='week_method', full_name='mobile_demand.Schedule.week_method', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='day_method', full_name='mobile_demand.Schedule.day_method', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='interval', full_name='mobile_demand.Schedule.interval', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='mobile_demand.Schedule.start_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='mobile_demand.Schedule.end_date', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status_plan', full_name='mobile_demand.Schedule.status_plan', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=753,
  serialized_end=1024,
)


_SCHEDULESTATUSPLAN = _descriptor.Descriptor(
  name='ScheduleStatusPlan',
  full_name='mobile_demand.ScheduleStatusPlan',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='time', full_name='mobile_demand.ScheduleStatusPlan.time', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_status', full_name='mobile_demand.ScheduleStatusPlan.start_status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_status', full_name='mobile_demand.ScheduleStatusPlan.end_status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='time_around', full_name='mobile_demand.ScheduleStatusPlan.time_around', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_filter', full_name='mobile_demand.ScheduleStatusPlan.doc_filter', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1026,
  serialized_end=1143,
)


_ORDER = _descriptor.Descriptor(
  name='Order',
  full_name='mobile_demand.Order',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_demand.Order.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_demand.Order.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_demand.Order.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_name', full_name='mobile_demand.Order.delivery_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1145,
  serialized_end=1217,
)


_GETDEMANDBYIDREQUEST = _descriptor.Descriptor(
  name='GetDemandByIdRequest',
  full_name='mobile_demand.GetDemandByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_demand.GetDemandByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1219,
  serialized_end=1253,
)


_GETHISTORYBYIDREQUEST = _descriptor.Descriptor(
  name='GetHistoryByIdRequest',
  full_name='mobile_demand.GetHistoryByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_demand.GetHistoryByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1255,
  serialized_end=1290,
)


_HISTORYRESPONSE = _descriptor.Descriptor(
  name='HistoryResponse',
  full_name='mobile_demand.HistoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_demand.HistoryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_demand.HistoryResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1292,
  serialized_end=1362,
)


_HISTORY = _descriptor.Descriptor(
  name='History',
  full_name='mobile_demand.History',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_demand.History.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_demand.History.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='mobile_demand.History.updated_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='mobile_demand.History.updated_at', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by_name', full_name='mobile_demand.History.updated_by_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orders', full_name='mobile_demand.History.orders', index=5,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1365,
  serialized_end=1533,
)


_GETPRODUCTSBYIDREQUEST = _descriptor.Descriptor(
  name='GetProductsByIdRequest',
  full_name='mobile_demand.GetProductsByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_demand.GetProductsByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='mobile_demand.GetProductsByIdRequest.sort', index=1,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_demand.GetProductsByIdRequest.order', index=2,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_demand.GetProductsByIdRequest.limit', index=3,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_demand.GetProductsByIdRequest.offset', index=4,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1535,
  serialized_end=1631,
)


_PRODUCTSRESPONSE = _descriptor.Descriptor(
  name='ProductsResponse',
  full_name='mobile_demand.ProductsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_demand.ProductsResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_demand.ProductsResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1633,
  serialized_end=1704,
)


_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='mobile_demand.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_demand.Product.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_demand.Product.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='mobile_demand.Product.product_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_demand.Product.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='mobile_demand.Product.spec', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='mobile_demand.Product.category_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='mobile_demand.Product.category_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_quantity', full_name='mobile_demand.Product.min_quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_quantity', full_name='mobile_demand.Product.increment_quantity', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_quantity', full_name='mobile_demand.Product.max_quantity', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='mobile_demand.Product.quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='mobile_demand.Product.unit_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_adjust', full_name='mobile_demand.Product.allow_adjust', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_stocktake', full_name='mobile_demand.Product.allow_stocktake', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_transfer', full_name='mobile_demand.Product.allow_transfer', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_main_order', full_name='mobile_demand.Product.allow_main_order', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_self_purchase', full_name='mobile_demand.Product.allow_self_purchase', index=16,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='circle_per_thousand', full_name='mobile_demand.Product.circle_per_thousand', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='replenish_method', full_name='mobile_demand.Product.replenish_method', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='safe_stock_method', full_name='mobile_demand.Product.safe_stock_method', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unfreeze', full_name='mobile_demand.Product.unfreeze', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_region_name', full_name='mobile_demand.Product.distribution_region_name', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_name', full_name='mobile_demand.Product.distribution_name', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='planned_arrival_days', full_name='mobile_demand.Product.planned_arrival_days', index=23,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='circle_type', full_name='mobile_demand.Product.circle_type', index=24,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cycles', full_name='mobile_demand.Product.cycles', index=25,
      number=27, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='mobile_demand.Product.start_date', index=26,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='interval_days', full_name='mobile_demand.Product.interval_days', index=27,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='mobile_demand.Product.distribution_type', index=28,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_unfinish_qty', full_name='mobile_demand.Product.store_unfinish_qty', index=29,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_inv_qty', full_name='mobile_demand.Product.store_inv_qty', index=30,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='mobile_demand.Product.partner_id', index=31,
      number=33, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1707,
  serialized_end=2482,
)


_UPDATEPRODUCTREQUEST = _descriptor.Descriptor(
  name='UpdateProductRequest',
  full_name='mobile_demand.UpdateProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_demand.UpdateProductRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_demand.UpdateProductRequest.remark', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='mobile_demand.UpdateProductRequest.products', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2484,
  serialized_end=2584,
)


_PRODUCTQUANTITY = _descriptor.Descriptor(
  name='ProductQuantity',
  full_name='mobile_demand.ProductQuantity',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_demand.ProductQuantity.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_demand.ProductQuantity.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='mobile_demand.ProductQuantity.quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2586,
  serialized_end=2653,
)


_UPDATEPRODUCTSRESPONSE = _descriptor.Descriptor(
  name='UpdateProductsResponse',
  full_name='mobile_demand.UpdateProductsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='mobile_demand.UpdateProductsResponse.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='mobile_demand.UpdateProductsResponse.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2655,
  serialized_end=2748,
)


_GETPRODUCTSDETAILBYIDREQUEST = _descriptor.Descriptor(
  name='GetProductsDetailByIdRequest',
  full_name='mobile_demand.GetProductsDetailByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_demand.GetProductsDetailByIdRequest.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2750,
  serialized_end=2800,
)


_DEALDEMANDBYIDREQUEST = _descriptor.Descriptor(
  name='DealDemandByIdRequest',
  full_name='mobile_demand.DealDemandByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_demand.DealDemandByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='mobile_demand.DealDemandByIdRequest.action', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='mobile_demand.DealDemandByIdRequest.description', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='mobile_demand.DealDemandByIdRequest.attachments', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2802,
  serialized_end=2895,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='mobile_demand.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='description', full_name='mobile_demand.Response.description', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2897,
  serialized_end=2928,
)

_LISTDEMANDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTDEMANDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTDEMANDRESPONSE.fields_by_name['rows'].message_type = _DEMAND
_DEMAND.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMAND.fields_by_name['schedule'].message_type = _SCHEDULE
_DEMAND.fields_by_name['orders'].message_type = _ORDER
_SCHEDULE.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SCHEDULE.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SCHEDULE.fields_by_name['status_plan'].message_type = _SCHEDULESTATUSPLAN
_HISTORYRESPONSE.fields_by_name['rows'].message_type = _HISTORY
_HISTORY.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_HISTORY.fields_by_name['orders'].message_type = _ORDER
_PRODUCTSRESPONSE.fields_by_name['rows'].message_type = _PRODUCT
_PRODUCT.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATEPRODUCTREQUEST.fields_by_name['products'].message_type = _PRODUCTQUANTITY
_UPDATEPRODUCTSRESPONSE.fields_by_name['products'].message_type = _PRODUCTQUANTITY
DESCRIPTOR.message_types_by_name['ListDemandRequest'] = _LISTDEMANDREQUEST
DESCRIPTOR.message_types_by_name['ListDemandResponse'] = _LISTDEMANDRESPONSE
DESCRIPTOR.message_types_by_name['Demand'] = _DEMAND
DESCRIPTOR.message_types_by_name['Schedule'] = _SCHEDULE
DESCRIPTOR.message_types_by_name['ScheduleStatusPlan'] = _SCHEDULESTATUSPLAN
DESCRIPTOR.message_types_by_name['Order'] = _ORDER
DESCRIPTOR.message_types_by_name['GetDemandByIdRequest'] = _GETDEMANDBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetHistoryByIdRequest'] = _GETHISTORYBYIDREQUEST
DESCRIPTOR.message_types_by_name['HistoryResponse'] = _HISTORYRESPONSE
DESCRIPTOR.message_types_by_name['History'] = _HISTORY
DESCRIPTOR.message_types_by_name['GetProductsByIdRequest'] = _GETPRODUCTSBYIDREQUEST
DESCRIPTOR.message_types_by_name['ProductsResponse'] = _PRODUCTSRESPONSE
DESCRIPTOR.message_types_by_name['Product'] = _PRODUCT
DESCRIPTOR.message_types_by_name['UpdateProductRequest'] = _UPDATEPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['ProductQuantity'] = _PRODUCTQUANTITY
DESCRIPTOR.message_types_by_name['UpdateProductsResponse'] = _UPDATEPRODUCTSRESPONSE
DESCRIPTOR.message_types_by_name['GetProductsDetailByIdRequest'] = _GETPRODUCTSDETAILBYIDREQUEST
DESCRIPTOR.message_types_by_name['DealDemandByIdRequest'] = _DEALDEMANDBYIDREQUEST
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ListDemandRequest = _reflection.GeneratedProtocolMessageType('ListDemandRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTDEMANDREQUEST,
  __module__ = 'mobile.mobile_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_demand.ListDemandRequest)
  ))
_sym_db.RegisterMessage(ListDemandRequest)

ListDemandResponse = _reflection.GeneratedProtocolMessageType('ListDemandResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTDEMANDRESPONSE,
  __module__ = 'mobile.mobile_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_demand.ListDemandResponse)
  ))
_sym_db.RegisterMessage(ListDemandResponse)

Demand = _reflection.GeneratedProtocolMessageType('Demand', (_message.Message,), dict(
  DESCRIPTOR = _DEMAND,
  __module__ = 'mobile.mobile_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_demand.Demand)
  ))
_sym_db.RegisterMessage(Demand)

Schedule = _reflection.GeneratedProtocolMessageType('Schedule', (_message.Message,), dict(
  DESCRIPTOR = _SCHEDULE,
  __module__ = 'mobile.mobile_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_demand.Schedule)
  ))
_sym_db.RegisterMessage(Schedule)

ScheduleStatusPlan = _reflection.GeneratedProtocolMessageType('ScheduleStatusPlan', (_message.Message,), dict(
  DESCRIPTOR = _SCHEDULESTATUSPLAN,
  __module__ = 'mobile.mobile_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_demand.ScheduleStatusPlan)
  ))
_sym_db.RegisterMessage(ScheduleStatusPlan)

Order = _reflection.GeneratedProtocolMessageType('Order', (_message.Message,), dict(
  DESCRIPTOR = _ORDER,
  __module__ = 'mobile.mobile_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_demand.Order)
  ))
_sym_db.RegisterMessage(Order)

GetDemandByIdRequest = _reflection.GeneratedProtocolMessageType('GetDemandByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDEMANDBYIDREQUEST,
  __module__ = 'mobile.mobile_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_demand.GetDemandByIdRequest)
  ))
_sym_db.RegisterMessage(GetDemandByIdRequest)

GetHistoryByIdRequest = _reflection.GeneratedProtocolMessageType('GetHistoryByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETHISTORYBYIDREQUEST,
  __module__ = 'mobile.mobile_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_demand.GetHistoryByIdRequest)
  ))
_sym_db.RegisterMessage(GetHistoryByIdRequest)

HistoryResponse = _reflection.GeneratedProtocolMessageType('HistoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _HISTORYRESPONSE,
  __module__ = 'mobile.mobile_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_demand.HistoryResponse)
  ))
_sym_db.RegisterMessage(HistoryResponse)

History = _reflection.GeneratedProtocolMessageType('History', (_message.Message,), dict(
  DESCRIPTOR = _HISTORY,
  __module__ = 'mobile.mobile_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_demand.History)
  ))
_sym_db.RegisterMessage(History)

GetProductsByIdRequest = _reflection.GeneratedProtocolMessageType('GetProductsByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTSBYIDREQUEST,
  __module__ = 'mobile.mobile_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_demand.GetProductsByIdRequest)
  ))
_sym_db.RegisterMessage(GetProductsByIdRequest)

ProductsResponse = _reflection.GeneratedProtocolMessageType('ProductsResponse', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTSRESPONSE,
  __module__ = 'mobile.mobile_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_demand.ProductsResponse)
  ))
_sym_db.RegisterMessage(ProductsResponse)

Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCT,
  __module__ = 'mobile.mobile_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_demand.Product)
  ))
_sym_db.RegisterMessage(Product)

UpdateProductRequest = _reflection.GeneratedProtocolMessageType('UpdateProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPRODUCTREQUEST,
  __module__ = 'mobile.mobile_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_demand.UpdateProductRequest)
  ))
_sym_db.RegisterMessage(UpdateProductRequest)

ProductQuantity = _reflection.GeneratedProtocolMessageType('ProductQuantity', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTQUANTITY,
  __module__ = 'mobile.mobile_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_demand.ProductQuantity)
  ))
_sym_db.RegisterMessage(ProductQuantity)

UpdateProductsResponse = _reflection.GeneratedProtocolMessageType('UpdateProductsResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPRODUCTSRESPONSE,
  __module__ = 'mobile.mobile_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_demand.UpdateProductsResponse)
  ))
_sym_db.RegisterMessage(UpdateProductsResponse)

GetProductsDetailByIdRequest = _reflection.GeneratedProtocolMessageType('GetProductsDetailByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTSDETAILBYIDREQUEST,
  __module__ = 'mobile.mobile_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_demand.GetProductsDetailByIdRequest)
  ))
_sym_db.RegisterMessage(GetProductsDetailByIdRequest)

DealDemandByIdRequest = _reflection.GeneratedProtocolMessageType('DealDemandByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _DEALDEMANDBYIDREQUEST,
  __module__ = 'mobile.mobile_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_demand.DealDemandByIdRequest)
  ))
_sym_db.RegisterMessage(DealDemandByIdRequest)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'mobile.mobile_demand_pb2'
  # @@protoc_insertion_point(class_scope:mobile_demand.Response)
  ))
_sym_db.RegisterMessage(Response)



_MOBILEDEMANDSERVICE = _descriptor.ServiceDescriptor(
  name='MobileDemandService',
  full_name='mobile_demand.MobileDemandService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=2931,
  serialized_end=3930,
  methods=[
  _descriptor.MethodDescriptor(
    name='ListDemand',
    full_name='mobile_demand.MobileDemandService.ListDemand',
    index=0,
    containing_service=None,
    input_type=_LISTDEMANDREQUEST,
    output_type=_LISTDEMANDRESPONSE,
    serialized_options=_b('\202\323\344\223\002\036\022\034/api/v2/supply/mobile/demand'),
  ),
  _descriptor.MethodDescriptor(
    name='GetDemandById',
    full_name='mobile_demand.MobileDemandService.GetDemandById',
    index=1,
    containing_service=None,
    input_type=_GETDEMANDBYIDREQUEST,
    output_type=_DEMAND,
    serialized_options=_b('\202\323\344\223\002#\022!/api/v2/supply/mobile/demand/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetHistoryById',
    full_name='mobile_demand.MobileDemandService.GetHistoryById',
    index=2,
    containing_service=None,
    input_type=_GETHISTORYBYIDREQUEST,
    output_type=_HISTORYRESPONSE,
    serialized_options=_b('\202\323\344\223\002+\022)/api/v2/supply/mobile/demand/{id}/history'),
  ),
  _descriptor.MethodDescriptor(
    name='GetProductsById',
    full_name='mobile_demand.MobileDemandService.GetProductsById',
    index=3,
    containing_service=None,
    input_type=_GETPRODUCTSBYIDREQUEST,
    output_type=_PRODUCTSRESPONSE,
    serialized_options=_b('\202\323\344\223\002+\022)/api/v2/supply/mobile/demand/{id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateProducts',
    full_name='mobile_demand.MobileDemandService.UpdateProducts',
    index=4,
    containing_service=None,
    input_type=_UPDATEPRODUCTREQUEST,
    output_type=_UPDATEPRODUCTSRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\")/api/v2/supply/mobile/demand/{id}/product:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetProductsDetailById',
    full_name='mobile_demand.MobileDemandService.GetProductsDetailById',
    index=5,
    containing_service=None,
    input_type=_GETPRODUCTSDETAILBYIDREQUEST,
    output_type=_PRODUCTSRESPONSE,
    serialized_options=_b('\202\323\344\223\0023\0221/api/v2/supply/mobile/demand/product/{product_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='DealDemandById',
    full_name='mobile_demand.MobileDemandService.DealDemandById',
    index=6,
    containing_service=None,
    input_type=_DEALDEMANDBYIDREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\0024\"//api/v2/supply/mobile/demand/deal/{id}/{action}:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_MOBILEDEMANDSERVICE)

DESCRIPTOR.services_by_name['MobileDemandService'] = _MOBILEDEMANDSERVICE

# @@protoc_insertion_point(module_scope)
