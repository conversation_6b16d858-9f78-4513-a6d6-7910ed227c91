# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from mobile import mobile_demand_pb2 as mobile_dot_mobile__demand__pb2


class MobileDemandServiceStub(object):
  """门店订货 —— 移动端 
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.ListDemand = channel.unary_unary(
        '/mobile_demand.MobileDemandService/ListDemand',
        request_serializer=mobile_dot_mobile__demand__pb2.ListDemandRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__demand__pb2.ListDemandResponse.FromString,
        )
    self.GetDemandById = channel.unary_unary(
        '/mobile_demand.MobileDemandService/GetDemandById',
        request_serializer=mobile_dot_mobile__demand__pb2.GetDemandByIdRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__demand__pb2.Demand.FromString,
        )
    self.GetHistoryById = channel.unary_unary(
        '/mobile_demand.MobileDemandService/GetHistoryById',
        request_serializer=mobile_dot_mobile__demand__pb2.GetHistoryByIdRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__demand__pb2.HistoryResponse.FromString,
        )
    self.GetProductsById = channel.unary_unary(
        '/mobile_demand.MobileDemandService/GetProductsById',
        request_serializer=mobile_dot_mobile__demand__pb2.GetProductsByIdRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__demand__pb2.ProductsResponse.FromString,
        )
    self.UpdateProducts = channel.unary_unary(
        '/mobile_demand.MobileDemandService/UpdateProducts',
        request_serializer=mobile_dot_mobile__demand__pb2.UpdateProductRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__demand__pb2.UpdateProductsResponse.FromString,
        )
    self.GetProductsDetailById = channel.unary_unary(
        '/mobile_demand.MobileDemandService/GetProductsDetailById',
        request_serializer=mobile_dot_mobile__demand__pb2.GetProductsDetailByIdRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__demand__pb2.ProductsResponse.FromString,
        )
    self.DealDemandById = channel.unary_unary(
        '/mobile_demand.MobileDemandService/DealDemandById',
        request_serializer=mobile_dot_mobile__demand__pb2.DealDemandByIdRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__demand__pb2.Response.FromString,
        )


class MobileDemandServiceServicer(object):
  """门店订货 —— 移动端 
  """

  def ListDemand(self, request, context):
    """查询订货单列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDemandById(self, request, context):
    """根据id查询单个订货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetHistoryById(self, request, context):
    """根据id查询单据历史记录
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProductsById(self, request, context):
    """根据id查询单据内的商品列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateProducts(self, request, context):
    """更新订货单商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProductsDetailById(self, request, context):
    """查询单据内的商品详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DealDemandById(self, request, context):
    """修改订单状态统一入口
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_MobileDemandServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'ListDemand': grpc.unary_unary_rpc_method_handler(
          servicer.ListDemand,
          request_deserializer=mobile_dot_mobile__demand__pb2.ListDemandRequest.FromString,
          response_serializer=mobile_dot_mobile__demand__pb2.ListDemandResponse.SerializeToString,
      ),
      'GetDemandById': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandById,
          request_deserializer=mobile_dot_mobile__demand__pb2.GetDemandByIdRequest.FromString,
          response_serializer=mobile_dot_mobile__demand__pb2.Demand.SerializeToString,
      ),
      'GetHistoryById': grpc.unary_unary_rpc_method_handler(
          servicer.GetHistoryById,
          request_deserializer=mobile_dot_mobile__demand__pb2.GetHistoryByIdRequest.FromString,
          response_serializer=mobile_dot_mobile__demand__pb2.HistoryResponse.SerializeToString,
      ),
      'GetProductsById': grpc.unary_unary_rpc_method_handler(
          servicer.GetProductsById,
          request_deserializer=mobile_dot_mobile__demand__pb2.GetProductsByIdRequest.FromString,
          response_serializer=mobile_dot_mobile__demand__pb2.ProductsResponse.SerializeToString,
      ),
      'UpdateProducts': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateProducts,
          request_deserializer=mobile_dot_mobile__demand__pb2.UpdateProductRequest.FromString,
          response_serializer=mobile_dot_mobile__demand__pb2.UpdateProductsResponse.SerializeToString,
      ),
      'GetProductsDetailById': grpc.unary_unary_rpc_method_handler(
          servicer.GetProductsDetailById,
          request_deserializer=mobile_dot_mobile__demand__pb2.GetProductsDetailByIdRequest.FromString,
          response_serializer=mobile_dot_mobile__demand__pb2.ProductsResponse.SerializeToString,
      ),
      'DealDemandById': grpc.unary_unary_rpc_method_handler(
          servicer.DealDemandById,
          request_deserializer=mobile_dot_mobile__demand__pb2.DealDemandByIdRequest.FromString,
          response_serializer=mobile_dot_mobile__demand__pb2.Response.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'mobile_demand.MobileDemandService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
