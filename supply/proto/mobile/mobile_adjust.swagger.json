{"swagger": "2.0", "info": {"title": "mobile/mobile_adjust.proto", "version": "version not set"}, "tags": [{"name": "mobileAdjust"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/mobile/adjust": {"get": {"summary": "GetAdjust查询报废", "operationId": "mobileAdjust_GetAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_adjustGetAdjustResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "include_total", "in": "query", "required": false, "type": "boolean"}, {"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "reason_type", "in": "query", "required": false, "type": "string"}, {"name": "branches", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "product_ids", "description": "商品ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "status", "description": "每日损耗计划状态\n\n - INITED: 新建\n - SUBMITTED: 提交\n - APPROVED: 审核\n - REJECTED: 驳回\n - CANCELLED: 作废", "in": "query", "required": false, "type": "array", "items": {"type": "string", "enum": ["NONE", "INITED", "SUBMITTED", "APPROVED", "REJECTED", "CANCELLED"]}, "collectionFormat": "multi"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序(默认asc)", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "ids", "description": "单据id列表查询", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "branch_type", "in": "query", "required": false, "type": "string"}, {"name": "sources", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "tags": ["mobileAdjust"]}}, "/api/v2/supply/mobile/adjust/auto_create/cancel": {"post": {"summary": "CancelAdjust 取消报废单", "operationId": "mobileAdjust_CancelAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_adjustCancelAdjustResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mobile_adjustCancelAdjustRequest"}}], "tags": ["mobileAdjust"]}}, "/api/v2/supply/mobile/adjust/create": {"post": {"summary": "CreatedAdjust手动创建一个报废单", "operationId": "mobileAdjust_CreateAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_adjustAdjust"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mobile_adjustCreateAdjustRequest"}}], "tags": ["mobileAdjust"]}}, "/api/v2/supply/mobile/adjust/product/{adjust_id}/delete": {"post": {"summary": "DeleteAdjustProduct删除报废单的商品", "operationId": "mobileAdjust_DeleteAdjustProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_adjustDeleteAdjustProductResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}}], "tags": ["mobileAdjust"]}}, "/api/v2/supply/mobile/adjust/store/products": {"get": {"summary": "GetAdjustProductByStoreID查询门店可报废商品", "operationId": "mobileAdjust_GetAdjustProductByStoreID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_adjustGetAdjustProductByStoreIDResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "store_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean"}, {"name": "adjust_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "search", "in": "query", "required": false, "type": "string"}, {"name": "search_fields", "in": "query", "required": false, "type": "string"}, {"name": "category_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "order_by", "description": "排序条件", "in": "query", "required": false, "type": "string"}, {"name": "sort", "description": "asc or desc， 暂时未使用", "in": "query", "required": false, "type": "string"}], "tags": ["mobileAdjust"]}}, "/api/v2/supply/mobile/adjust/{adjust_id}": {"get": {"summary": "GetAdjustByID查询一个报废单", "operationId": "mobileAdjust_GetAdjustByID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_adjustAdjust"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["mobileAdjust"]}}, "/api/v2/supply/mobile/adjust/{adjust_id}/approve": {"post": {"summary": "审核一个报废单", "operationId": "mobileAdjust_ApproveAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_adjustApproveAdjustResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object"}}], "tags": ["mobileAdjust"]}}, "/api/v2/supply/mobile/adjust/{adjust_id}/confirm": {"post": {"summary": "ConfirmAdjust确认一个报废单", "operationId": "mobileAdjust_ConfirmAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_adjustConfirmAdjustResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"branch_type": {"type": "string"}}}}], "tags": ["mobileAdjust"]}}, "/api/v2/supply/mobile/adjust/{adjust_id}/delete": {"post": {"summary": "DeleteAdjust删除一个报废单", "operationId": "mobileAdjust_DeleteAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_adjustDeleteAdjustResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object"}}], "tags": ["mobileAdjust"]}}, "/api/v2/supply/mobile/adjust/{adjust_id}/log": {"get": {"summary": "报废历史记录", "operationId": "mobileAdjust_GetAdjustLog", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_adjustGetAdjustLogResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["mobileAdjust"]}}, "/api/v2/supply/mobile/adjust/{adjust_id}/product": {"get": {"summary": "GetAdjustProduct报废单商品查询", "operationId": "mobileAdjust_GetAdjustProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_adjustGetAdjustProductResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean"}, {"name": "order", "in": "query", "required": false, "type": "string"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["mobileAdjust"]}}, "/api/v2/supply/mobile/adjust/{adjust_id}/reject": {"post": {"summary": "驳回一个报废单", "operationId": "mobileAdjust_RejectAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_adjustRejectAdjustResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"reject_reason": {"type": "string", "title": "驳回原因"}}}}], "tags": ["mobileAdjust"]}}, "/api/v2/supply/mobile/adjust/{adjust_id}/submit": {"post": {"summary": "提交一个报废单", "operationId": "mobileAdjust_SubmitAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_adjustSubmitAdjustResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object"}}], "tags": ["mobileAdjust"]}}, "/api/v2/supply/mobile/adjust/{adjust_id}/update": {"post": {"summary": "UpdateAdjust更新一个报废单", "operationId": "mobileAdjust_UpdateAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_adjustAdjust"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"products": {"type": "array", "items": {"$ref": "#/definitions/mobile_adjustCreateAdjustProduct"}}, "remark": {"type": "string"}, "reason_type": {"type": "string"}, "adjust_date": {"type": "string", "format": "date-time"}, "branch_type": {"type": "string", "title": "区分门店还是仓库: STORE or WAREHOUSE"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/mobile_adjustAttachments"}, "title": "附件"}, "position_id": {"type": "string", "format": "uint64"}}}}], "tags": ["mobileAdjust"]}}}, "definitions": {"GetAdjustRequestSTATUS": {"type": "string", "enum": ["NONE", "INITED", "SUBMITTED", "APPROVED", "REJECTED", "CANCELLED"], "default": "NONE", "title": "- INITED: 新建\n - SUBMITTED: 提交\n - APPROVED: 审核\n - REJECTED: 驳回\n - CANCELLED: 作废"}, "SkuRemarkTag": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "code": {"type": "string"}, "name": {"type": "string"}}}, "mobile_adjustAdjust": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "adjust_store": {"type": "string", "format": "uint64"}, "code": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "process_status": {"type": "string"}, "reason_type": {"type": "string"}, "remark": {"type": "string"}, "status": {"type": "string"}, "adjust_date": {"type": "string", "format": "date-time"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_by": {"type": "string", "format": "uint64"}, "updated_by": {"type": "string", "format": "uint64"}, "user_id": {"type": "string", "format": "uint64"}, "reason_name": {"type": "string"}, "schedule_id": {"type": "string", "format": "uint64"}, "request_id": {"type": "string", "format": "uint64"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "schedule_name": {"type": "string"}, "branch_type": {"type": "string"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/mobile_adjustAttachments"}, "title": "附件"}, "source": {"type": "string", "title": "单据来源\n\"POS_ADJUST\"      # POS端报废\n\"MANUAL_CREATED\"  # 手动新建\n\"PLAN_CREATED\"    # 报废计划创建"}, "extends": {"type": "string"}, "is_empty": {"type": "boolean", "title": "单据是否为空True/False"}, "status_plan": {"type": "array", "items": {"$ref": "#/definitions/mobile_adjustScheduleStatusPlan"}, "title": "计划单据状态自动变更设置"}, "reject_reason": {"type": "string", "title": "驳回原因"}, "total_amount": {"type": "number", "format": "double"}, "total_sales_amount": {"type": "number", "format": "double"}}}, "mobile_adjustAdjustPositionProducts": {"type": "object", "properties": {"position_id": {"type": "string", "format": "uint64"}, "position_code": {"type": "string"}, "position_name": {"type": "string"}, "products": {"type": "array", "items": {"$ref": "#/definitions/mobile_adjustAdjustProduct"}}, "total": {"type": "string", "format": "uint64"}}}, "mobile_adjustAdjustProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "accounting_quantity": {"type": "number", "format": "double"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "accounting_unit_spec": {"type": "string"}, "adjust_id": {"type": "string", "format": "uint64"}, "adjust_store": {"type": "string", "format": "uint64"}, "confirmed_quantity": {"type": "number", "format": "double"}, "created_by": {"type": "string", "format": "uint64"}, "is_confirmed": {"type": "boolean"}, "item_number": {"type": "integer", "format": "int64"}, "material_number": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "product_code": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "spec": {"type": "string"}, "stocktake_quantity": {"type": "number", "format": "double"}, "stocktake_unit_id": {"type": "string", "format": "uint64"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "unit_spec": {"type": "string"}, "updated_by": {"type": "string", "format": "uint64"}, "adjust_date": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_at": {"type": "string", "format": "date-time"}, "user_id": {"type": "string", "format": "uint64"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "reason_type": {"type": "string"}, "convert_accounting_quantity": {"type": "number", "format": "double"}, "is_bom": {"type": "boolean"}, "position_id": {"type": "string", "format": "uint64"}, "sku_remark": {"type": "string"}, "units": {"type": "array", "items": {"$ref": "#/definitions/mobile_adjustCreateAdjustProductUint"}}, "model_name": {"type": "string"}, "tax_rate": {"type": "number", "format": "double"}, "tax_price": {"type": "number", "format": "double"}, "amount": {"type": "number", "format": "double"}, "tax_amount": {"type": "number", "format": "double"}, "cost_price": {"type": "number", "format": "double"}, "sales_amount": {"type": "number", "format": "double"}, "sales_price": {"type": "number", "format": "double"}}}, "mobile_adjustAdjustProductByStore": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "loss_report_order": {"type": "string"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "model_name": {"type": "string", "title": "商品规格名称"}, "storage_type": {"type": "string"}, "category_id": {"type": "string", "format": "uint64", "title": "单位分类"}, "category_name": {"type": "string"}, "units": {"type": "array", "items": {"$ref": "#/definitions/mobile_adjustCreateAdjustProductUint"}}, "barcode": {"type": "array", "items": {"type": "string"}}, "real_inventory_qty": {"type": "number", "format": "double", "title": "实时库存数量"}}}, "mobile_adjustApproveAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean"}}}, "mobile_adjustAttachments": {"type": "object", "properties": {"type": {"type": "string"}, "url": {"type": "string"}}}, "mobile_adjustCancelAdjustRequest": {"type": "object", "properties": {"adjust_id": {"type": "string", "format": "uint64"}}}, "mobile_adjustCancelAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean"}}}, "mobile_adjustConfirmAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean"}}}, "mobile_adjustCreateAdjustProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "product_id": {"type": "string", "format": "uint64"}, "unit_id": {"type": "string", "format": "uint64"}, "quantity": {"type": "number", "format": "double"}, "reason_type": {"type": "string"}, "position_id": {"type": "string", "format": "uint64"}, "skuRemark": {"type": "array", "items": {"$ref": "#/definitions/mobile_adjustSkuRemark"}}, "tax_rate": {"type": "number", "format": "double"}, "tax_price": {"type": "number", "format": "double"}, "amount": {"type": "number", "format": "double"}, "tax_amount": {"type": "number", "format": "double"}, "cost_price": {"type": "number", "format": "double"}, "sales_price": {"type": "number", "format": "double"}, "sales_amount": {"type": "number", "format": "double"}}}, "mobile_adjustCreateAdjustProductUint": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "数据id"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "scope_id": {"type": "string", "format": "uint64", "title": "scope_id"}, "name": {"type": "string"}, "code": {"type": "string"}, "updated": {"type": "string"}, "rate": {"type": "number", "format": "double"}, "default": {"type": "boolean"}, "order": {"type": "boolean"}, "purchase": {"type": "boolean"}, "sales": {"type": "boolean"}, "stocktake": {"type": "boolean"}, "bom": {"type": "boolean"}, "default_stocktake": {"type": "boolean"}, "transfer": {"type": "boolean"}, "tax_rate": {"type": "number", "format": "double"}, "tax_price": {"type": "number", "format": "double"}, "cost_price": {"type": "number", "format": "double"}, "sales_price": {"type": "number", "format": "double"}}}, "mobile_adjustCreateAdjustRequest": {"type": "object", "properties": {"adjust_store": {"type": "string", "format": "uint64"}, "products": {"type": "array", "items": {"$ref": "#/definitions/mobile_adjustCreateAdjustProduct"}}, "reason_type": {"type": "string"}, "remark": {"type": "string"}, "request_id": {"type": "string", "format": "uint64"}, "adjust_date": {"type": "string", "format": "date-time"}, "branch_type": {"type": "string", "title": "区分门店还是仓库: STORE or WAREHOUSE"}, "position_id": {"type": "string", "format": "uint64"}, "source": {"type": "string", "title": "单据来源\n\"POS_ADJUST\"      # POS端报废\n\"MANUAL_CREATED\"  # 手动新建\n\"PLAN_CREATED\"    # 报废计划创建"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/mobile_adjustAttachments"}, "title": "附件"}}}, "mobile_adjustDeleteAdjustProductResponse": {"type": "object", "properties": {"result": {"type": "boolean"}}}, "mobile_adjustDeleteAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean"}}}, "mobile_adjustGetAdjustLogResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_adjustLog"}}, "total": {"type": "string", "format": "uint64"}}}, "mobile_adjustGetAdjustProductByStoreIDResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_adjustAdjustProductByStore"}}, "total": {"type": "integer", "format": "int64"}, "inventory_unchanged_rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_adjustAdjustProductByStore"}, "title": "库存未异动的商品"}}}, "mobile_adjustGetAdjustProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_adjustAdjustProduct"}}, "total": {"type": "integer", "format": "int64"}, "position_rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_adjustAdjustPositionProducts"}}}}, "mobile_adjustGetAdjustResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_adjustAdjust"}}, "total": {"type": "integer", "format": "int64"}}}, "mobile_adjustLog": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "历史记录id"}, "status": {"type": "string", "title": "状态"}, "created_by": {"type": "string", "format": "uint64", "title": "操作人"}, "created_at": {"type": "string", "format": "date-time", "title": "操作时间"}, "reason": {"type": "string", "title": "原因"}, "created_name": {"type": "string", "title": "操作人名称"}}}, "mobile_adjustRejectAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean"}}}, "mobile_adjustScheduleStatusPlan": {"type": "object", "properties": {"time": {"type": "string", "format": "date-time", "title": "报废单自动变更时间"}, "start_status": {"type": "array", "items": {"type": "string"}, "title": "状态变更开始状态"}, "end_status": {"type": "string", "title": "状态变更结束状态"}, "time_around": {"type": "string", "title": "状态自动变更时间周期"}, "doc_filter": {"type": "string", "title": "单据过滤条件"}}}, "mobile_adjustSkuRemark": {"type": "object", "properties": {"name": {"$ref": "#/definitions/SkuRemarkTag"}, "values": {"$ref": "#/definitions/SkuRemarkTag"}}}, "mobile_adjustSubmitAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}}}