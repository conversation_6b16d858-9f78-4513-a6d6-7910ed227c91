# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from mobile import mobile_franchisee_refund_pb2 as mobile_dot_mobile__franchisee__refund__pb2


class MobileFranchiseeRefundServiceStub(object):
  """加盟商订货退款 -- 小程序入口
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.ListMobileFRefund = channel.unary_unary(
        '/mobile_franchisee_refund.MobileFranchiseeRefundService/ListMobileFRefund',
        request_serializer=mobile_dot_mobile__franchisee__refund__pb2.ListMobileFRefundRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__refund__pb2.ListMobileFRefundResponse.FromString,
        )
    self.GetMobileFRefundById = channel.unary_unary(
        '/mobile_franchisee_refund.MobileFranchiseeRefundService/GetMobileFRefundById',
        request_serializer=mobile_dot_mobile__franchisee__refund__pb2.GetFRefundByIdRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__refund__pb2.GetMobileFRefundByIdResponse.FromString,
        )
    self.GetMobileRefundHistory = channel.unary_unary(
        '/mobile_franchisee_refund.MobileFranchiseeRefundService/GetMobileRefundHistory',
        request_serializer=mobile_dot_mobile__franchisee__refund__pb2.GetMobileRefundHistoryRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__refund__pb2.RefundHistoryResponse.FromString,
        )
    self.DealMobileFRefundById = channel.unary_unary(
        '/mobile_franchisee_refund.MobileFranchiseeRefundService/DealMobileFRefundById',
        request_serializer=mobile_dot_mobile__franchisee__refund__pb2.DealMobileFRefundByIdRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__refund__pb2.DealMobileFRefundByIdResponse.FromString,
        )


class MobileFranchiseeRefundServiceServicer(object):
  """加盟商订货退款 -- 小程序入口
  """

  def ListMobileFRefund(self, request, context):
    """查询退款单列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetMobileFRefundById(self, request, context):
    """查询退款单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetMobileRefundHistory(self, request, context):
    """根据id查询单据历史记录
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DealMobileFRefundById(self, request, context):
    """修改退款单状态统一入口
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_MobileFranchiseeRefundServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'ListMobileFRefund': grpc.unary_unary_rpc_method_handler(
          servicer.ListMobileFRefund,
          request_deserializer=mobile_dot_mobile__franchisee__refund__pb2.ListMobileFRefundRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__refund__pb2.ListMobileFRefundResponse.SerializeToString,
      ),
      'GetMobileFRefundById': grpc.unary_unary_rpc_method_handler(
          servicer.GetMobileFRefundById,
          request_deserializer=mobile_dot_mobile__franchisee__refund__pb2.GetFRefundByIdRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__refund__pb2.GetMobileFRefundByIdResponse.SerializeToString,
      ),
      'GetMobileRefundHistory': grpc.unary_unary_rpc_method_handler(
          servicer.GetMobileRefundHistory,
          request_deserializer=mobile_dot_mobile__franchisee__refund__pb2.GetMobileRefundHistoryRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__refund__pb2.RefundHistoryResponse.SerializeToString,
      ),
      'DealMobileFRefundById': grpc.unary_unary_rpc_method_handler(
          servicer.DealMobileFRefundById,
          request_deserializer=mobile_dot_mobile__franchisee__refund__pb2.DealMobileFRefundByIdRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__refund__pb2.DealMobileFRefundByIdResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'mobile_franchisee_refund.MobileFranchiseeRefundService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
