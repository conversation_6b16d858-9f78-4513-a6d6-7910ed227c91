# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from mobile import mobile_stocktake_pb2 as mobile_dot_mobile__stocktake__pb2


class MobileStockTakeStub(object):
  """Stocktake 盘点服务
  Create_request里加请求ID，幂等检查create的API
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CheckStocktakeByDocID = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/CheckStocktakeByDocID',
        request_serializer=mobile_dot_mobile__stocktake__pb2.CheckStocktakeByDocIDRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.CheckStocktakeByDocIDResponse.FromString,
        )
    self.ConfirmStocktakeByDocID = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/ConfirmStocktakeByDocID',
        request_serializer=mobile_dot_mobile__stocktake__pb2.ConfirmStocktakeByDocIDRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.ConfirmStocktakeByDocIDResponse.FromString,
        )
    self.ApproveStocktakeByDocID = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/ApproveStocktakeByDocID',
        request_serializer=mobile_dot_mobile__stocktake__pb2.ApproveStocktakeByDocIDRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.ApproveStocktakeByDocIDResponse.FromString,
        )
    self.RejectStocktakeProduct = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/RejectStocktakeProduct',
        request_serializer=mobile_dot_mobile__stocktake__pb2.RejectStocktakeProductRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.RejectStocktakeProductResponse.FromString,
        )
    self.CancelStocktakeByDocID = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/CancelStocktakeByDocID',
        request_serializer=mobile_dot_mobile__stocktake__pb2.CancelStocktakeByDocIDRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.CancelStocktakeByDocIDResponse.FromString,
        )
    self.GetStocktakeByDocID = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/GetStocktakeByDocID',
        request_serializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeByDocIDRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.Stocktake.FromString,
        )
    self.GetStocktake = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/GetStocktake',
        request_serializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeResponse.FromString,
        )
    self.GetStocktakeByIds = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/GetStocktakeByIds',
        request_serializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeByIdsRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeByIdsResponse.FromString,
        )
    self.GetStocktakeProduct = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/GetStocktakeProduct',
        request_serializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeProductRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeProductResponse.FromString,
        )
    self.PutStocktakeByDocID = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/PutStocktakeByDocID',
        request_serializer=mobile_dot_mobile__stocktake__pb2.PutStocktakeByDocIDRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.PutStocktakeByDocIDResponse.FromString,
        )
    self.CheckedStocktakeByDocID = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/CheckedStocktakeByDocID',
        request_serializer=mobile_dot_mobile__stocktake__pb2.CheckedStocktakeByDocIDRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.CheckedStocktakeByDocIDResponse.FromString,
        )
    self.GetStocktakeTags = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/GetStocktakeTags',
        request_serializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeTagsRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeTagsResponse.FromString,
        )
    self.GetStocktakeTagsById = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/GetStocktakeTagsById',
        request_serializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeTagsByIdRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.StocktakeTags.FromString,
        )
    self.ActionStocktakeTags = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/ActionStocktakeTags',
        request_serializer=mobile_dot_mobile__stocktake__pb2.ActionStocktakeTagsRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.ActionStocktakeTagsResponse.FromString,
        )
    self.DeleteStocktakeProductTags = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/DeleteStocktakeProductTags',
        request_serializer=mobile_dot_mobile__stocktake__pb2.DeleteStocktakeProductTagsRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.DeleteStocktakeProductTagsResponse.FromString,
        )
    self.GetStocktakeBalance = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/GetStocktakeBalance',
        request_serializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeBalanceRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeBalanceResponse.FromString,
        )
    self.SubmitStocktakeByDocID = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/SubmitStocktakeByDocID',
        request_serializer=mobile_dot_mobile__stocktake__pb2.SubmitStocktakeByDocIDRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.SubmitStocktakeByDocIDResponse.FromString,
        )
    self.GetStocktakeBalanceProductGroup = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/GetStocktakeBalanceProductGroup',
        request_serializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeBalanceProductGroupRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeBalanceProductGroupResponse.FromString,
        )
    self.StocktakeBiDetailed = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/StocktakeBiDetailed',
        request_serializer=mobile_dot_mobile__stocktake__pb2.StocktakeBiDetailedRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.StocktakeBiDetailedResponse.FromString,
        )
    self.StocktakeBalanceRegion = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/StocktakeBalanceRegion',
        request_serializer=mobile_dot_mobile__stocktake__pb2.StocktakeBalanceRegionRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.StocktakeBalanceRegionResponse.FromString,
        )
    self.AdvanceStocktakeDiff = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/AdvanceStocktakeDiff',
        request_serializer=mobile_dot_mobile__stocktake__pb2.AdvanceStocktakeDiffRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.AdvanceStocktakeDiffResponse.FromString,
        )
    self.StocktakeDiffReport = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/StocktakeDiffReport',
        request_serializer=mobile_dot_mobile__stocktake__pb2.StocktakeDiffReportRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.StocktakeDiffReportResponse.FromString,
        )
    self.RecreateStocktakeDoc = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/RecreateStocktakeDoc',
        request_serializer=mobile_dot_mobile__stocktake__pb2.RecreateStocktakeDocRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.RecreateStocktakeDocResponse.FromString,
        )
    self.GetStocktakeLog = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/GetStocktakeLog',
        request_serializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeLogRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeLogResponse.FromString,
        )
    self.GetNewStocktakeId = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/GetNewStocktakeId',
        request_serializer=mobile_dot_mobile__stocktake__pb2.GetNewStocktakeIdRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.GetNewStocktakeIdResponse.FromString,
        )
    self.ManuallyCreateStocktake = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/ManuallyCreateStocktake',
        request_serializer=mobile_dot_mobile__stocktake__pb2.ManuallyCreateStocktakeRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.ManuallyCreateStocktakeResponse.FromString,
        )
    self.GetStocktakeProductByStoreID = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/GetStocktakeProductByStoreID',
        request_serializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeProductByStoreIDRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeProductByStoreIDResponse.FromString,
        )
    self.AddProductsToStocktake = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/AddProductsToStocktake',
        request_serializer=mobile_dot_mobile__stocktake__pb2.AddProductsToStocktakeRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.AddProductsToStocktakeResponse.FromString,
        )
    self.DelProductsFromStocktake = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/DelProductsFromStocktake',
        request_serializer=mobile_dot_mobile__stocktake__pb2.DelProductsFromStocktakeRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.DelProductsFromStocktakeResponse.FromString,
        )
    self.AddProductsToStocktakeForPOS = channel.unary_unary(
        '/mobile_stocktake.MobileStockTake/AddProductsToStocktakeForPOS',
        request_serializer=mobile_dot_mobile__stocktake__pb2.PutStocktakeByDocIDRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__stocktake__pb2.PutStocktakeByDocIDResponse.FromString,
        )


class MobileStockTakeServicer(object):
  """Stocktake 盘点服务
  Create_request里加请求ID，幂等检查create的API
  """

  def CheckStocktakeByDocID(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ConfirmStocktakeByDocID(self, request, context):
    """备注:盘点商品在INITED和REJECTED状态可以填写数量，CONFITRMED，不能。
    ConfirmStocktakeByDocID 确认盘点单（status=CONFITRMED，核算完库存后'FINALIZED'）15
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ApproveStocktakeByDocID(self, request, context):
    """APPROVEDStocktakeByDocID  财务确认盘点单（status=APPROVED最终状态，）16
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RejectStocktakeProduct(self, request, context):
    """RejectStocktakeProduct 财务驳回提交后的部分盘点单明细商品（status=REJECTED，部分商品status=REJECTED）17
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CancelStocktakeByDocID(self, request, context):
    """CancelStocktakeByDocID  作废盘点单（status=CANCELED）18
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStocktakeByDocID(self, request, context):
    """GetStocktakeByDocID 获取一个盘点单19
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStocktake(self, request, context):
    """GetStocktake 查询盘点单20
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStocktakeByIds(self, request, context):
    """根据盘点单ID查询盘点单移动端用
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStocktakeProduct(self, request, context):
    """GetStocktakeProduct 查询盘点单明细商品21
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def PutStocktakeByDocID(self, request, context):
    """PutStocktakeByDocID 提交盘点单明细商品数量，更新盘点单, 22
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CheckedStocktakeByDocID(self, request, context):
    """CheckedStocktakeByDocID 检查完成盘点单23
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStocktakeTags(self, request, context):
    """GetStocktakeTags获取盘点标签24
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStocktakeTagsById(self, request, context):
    """获取一个盘点标签
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ActionStocktakeTags(self, request, context):
    """ActionStocktakeTags增加，删除，更新,获取盘点标签25
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteStocktakeProductTags(self, request, context):
    """DeleteStocktakeProductTags删除盘点商品标签条目26
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStocktakeBalance(self, request, context):
    """GetStocktakeBalance盘点单损益报表27
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SubmitStocktakeByDocID(self, request, context):
    """SubmitStocktakeByDocID 提交盘点单28
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStocktakeBalanceProductGroup(self, request, context):
    """GetStocktakeBalanceProductGroup门店盘点单损益汇总报表29
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def StocktakeBiDetailed(self, request, context):
    """StocktakeBiDetailed盘点单报表30
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def StocktakeBalanceRegion(self, request, context):
    """StocktakeBalanceRegion区域盘点31
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AdvanceStocktakeDiff(self, request, context):
    """AdvanceStocktakeDiff  提前查看盘点损益32
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def StocktakeDiffReport(self, request, context):
    """StocktakeDiffReport  盘点差异表33
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RecreateStocktakeDoc(self, request, context):
    """RecreateStocktakeDoc  重盘功能，重新生成盘点单35
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStocktakeLog(self, request, context):
    """盘点历史记录
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetNewStocktakeId(self, request, context):
    """获取一个新盘点单id
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ManuallyCreateStocktake(self, request, context):
    """手动创建盘点单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStocktakeProductByStoreID(self, request, context):
    """GetStocktakeProductByStoreID 查询门店可盘点商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddProductsToStocktake(self, request, context):
    """向手动盘点单增加商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DelProductsFromStocktake(self, request, context):
    """从手动盘点单删除商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddProductsToStocktakeForPOS(self, request, context):
    """向手动盘点单增加商品，更新盘点单, 22
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_MobileStockTakeServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CheckStocktakeByDocID': grpc.unary_unary_rpc_method_handler(
          servicer.CheckStocktakeByDocID,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.CheckStocktakeByDocIDRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.CheckStocktakeByDocIDResponse.SerializeToString,
      ),
      'ConfirmStocktakeByDocID': grpc.unary_unary_rpc_method_handler(
          servicer.ConfirmStocktakeByDocID,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.ConfirmStocktakeByDocIDRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.ConfirmStocktakeByDocIDResponse.SerializeToString,
      ),
      'ApproveStocktakeByDocID': grpc.unary_unary_rpc_method_handler(
          servicer.ApproveStocktakeByDocID,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.ApproveStocktakeByDocIDRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.ApproveStocktakeByDocIDResponse.SerializeToString,
      ),
      'RejectStocktakeProduct': grpc.unary_unary_rpc_method_handler(
          servicer.RejectStocktakeProduct,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.RejectStocktakeProductRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.RejectStocktakeProductResponse.SerializeToString,
      ),
      'CancelStocktakeByDocID': grpc.unary_unary_rpc_method_handler(
          servicer.CancelStocktakeByDocID,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.CancelStocktakeByDocIDRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.CancelStocktakeByDocIDResponse.SerializeToString,
      ),
      'GetStocktakeByDocID': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktakeByDocID,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeByDocIDRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.Stocktake.SerializeToString,
      ),
      'GetStocktake': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktake,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeResponse.SerializeToString,
      ),
      'GetStocktakeByIds': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktakeByIds,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeByIdsRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeByIdsResponse.SerializeToString,
      ),
      'GetStocktakeProduct': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktakeProduct,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeProductRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeProductResponse.SerializeToString,
      ),
      'PutStocktakeByDocID': grpc.unary_unary_rpc_method_handler(
          servicer.PutStocktakeByDocID,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.PutStocktakeByDocIDRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.PutStocktakeByDocIDResponse.SerializeToString,
      ),
      'CheckedStocktakeByDocID': grpc.unary_unary_rpc_method_handler(
          servicer.CheckedStocktakeByDocID,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.CheckedStocktakeByDocIDRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.CheckedStocktakeByDocIDResponse.SerializeToString,
      ),
      'GetStocktakeTags': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktakeTags,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeTagsRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeTagsResponse.SerializeToString,
      ),
      'GetStocktakeTagsById': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktakeTagsById,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeTagsByIdRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.StocktakeTags.SerializeToString,
      ),
      'ActionStocktakeTags': grpc.unary_unary_rpc_method_handler(
          servicer.ActionStocktakeTags,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.ActionStocktakeTagsRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.ActionStocktakeTagsResponse.SerializeToString,
      ),
      'DeleteStocktakeProductTags': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteStocktakeProductTags,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.DeleteStocktakeProductTagsRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.DeleteStocktakeProductTagsResponse.SerializeToString,
      ),
      'GetStocktakeBalance': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktakeBalance,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeBalanceRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeBalanceResponse.SerializeToString,
      ),
      'SubmitStocktakeByDocID': grpc.unary_unary_rpc_method_handler(
          servicer.SubmitStocktakeByDocID,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.SubmitStocktakeByDocIDRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.SubmitStocktakeByDocIDResponse.SerializeToString,
      ),
      'GetStocktakeBalanceProductGroup': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktakeBalanceProductGroup,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeBalanceProductGroupRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeBalanceProductGroupResponse.SerializeToString,
      ),
      'StocktakeBiDetailed': grpc.unary_unary_rpc_method_handler(
          servicer.StocktakeBiDetailed,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.StocktakeBiDetailedRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.StocktakeBiDetailedResponse.SerializeToString,
      ),
      'StocktakeBalanceRegion': grpc.unary_unary_rpc_method_handler(
          servicer.StocktakeBalanceRegion,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.StocktakeBalanceRegionRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.StocktakeBalanceRegionResponse.SerializeToString,
      ),
      'AdvanceStocktakeDiff': grpc.unary_unary_rpc_method_handler(
          servicer.AdvanceStocktakeDiff,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.AdvanceStocktakeDiffRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.AdvanceStocktakeDiffResponse.SerializeToString,
      ),
      'StocktakeDiffReport': grpc.unary_unary_rpc_method_handler(
          servicer.StocktakeDiffReport,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.StocktakeDiffReportRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.StocktakeDiffReportResponse.SerializeToString,
      ),
      'RecreateStocktakeDoc': grpc.unary_unary_rpc_method_handler(
          servicer.RecreateStocktakeDoc,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.RecreateStocktakeDocRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.RecreateStocktakeDocResponse.SerializeToString,
      ),
      'GetStocktakeLog': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktakeLog,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeLogRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeLogResponse.SerializeToString,
      ),
      'GetNewStocktakeId': grpc.unary_unary_rpc_method_handler(
          servicer.GetNewStocktakeId,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.GetNewStocktakeIdRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.GetNewStocktakeIdResponse.SerializeToString,
      ),
      'ManuallyCreateStocktake': grpc.unary_unary_rpc_method_handler(
          servicer.ManuallyCreateStocktake,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.ManuallyCreateStocktakeRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.ManuallyCreateStocktakeResponse.SerializeToString,
      ),
      'GetStocktakeProductByStoreID': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktakeProductByStoreID,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeProductByStoreIDRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.GetStocktakeProductByStoreIDResponse.SerializeToString,
      ),
      'AddProductsToStocktake': grpc.unary_unary_rpc_method_handler(
          servicer.AddProductsToStocktake,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.AddProductsToStocktakeRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.AddProductsToStocktakeResponse.SerializeToString,
      ),
      'DelProductsFromStocktake': grpc.unary_unary_rpc_method_handler(
          servicer.DelProductsFromStocktake,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.DelProductsFromStocktakeRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.DelProductsFromStocktakeResponse.SerializeToString,
      ),
      'AddProductsToStocktakeForPOS': grpc.unary_unary_rpc_method_handler(
          servicer.AddProductsToStocktakeForPOS,
          request_deserializer=mobile_dot_mobile__stocktake__pb2.PutStocktakeByDocIDRequest.FromString,
          response_serializer=mobile_dot_mobile__stocktake__pb2.PutStocktakeByDocIDResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'mobile_stocktake.MobileStockTake', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
