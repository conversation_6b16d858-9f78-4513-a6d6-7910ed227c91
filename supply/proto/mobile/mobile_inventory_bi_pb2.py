# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mobile/mobile_inventory_bi.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='mobile/mobile_inventory_bi.proto',
  package='mobile_inventory_bi',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n mobile/mobile_inventory_bi.proto\x12\x13mobile_inventory_bi\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xa9\x02\n\x18RealtimeInventoryRequest\x12\x12\n\nbranch_ids\x18\x01 \x03(\x04\x12\x13\n\x0bgeo_regions\x18\x02 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x03 \x03(\t\x12\x13\n\x0bproduct_ids\x18\x04 \x03(\x04\x12\r\n\x05limit\x18\x05 \x01(\x05\x12\x0e\n\x06offset\x18\x06 \x01(\x05\x12\r\n\x05order\x18\x07 \x01(\t\x12\x0c\n\x04sort\x18\x08 \x01(\t\x12\x0f\n\x07\x65xclude\x18\t \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\n \x01(\t\x12\x0b\n\x03lan\x18\x0b \x01(\t\x12\x14\n\x0cposition_ids\x18\x0c \x03(\x04\x12\x15\n\rreturn_fields\x18\r \x01(\t\x12\x1d\n\x15product_search_fields\x18\x0e \x01(\t\"X\n\x19RealtimeInventoryResponse\x12,\n\x04rows\x18\x01 \x03(\x0b\x32\x1e.mobile_inventory_bi.Inventory\x12\r\n\x05total\x18\x02 \x01(\x05\"\xd2\x06\n\tInventory\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nproduct_id\x18\x05 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x08 \x01(\x04\x12\x15\n\rcategory_code\x18\t \x01(\t\x12\x15\n\rcategory_name\x18\n \x01(\t\x12\x0c\n\x04spec\x18\x0b \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x0c \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\r \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x0e \x01(\t\x12\x0b\n\x03qty\x18\x0f \x01(\x01\x12\x16\n\x0e\x64\x65mand_unit_id\x18\x10 \x01(\x04\x12\x18\n\x10\x64\x65mand_unit_code\x18\x11 \x01(\t\x12\x18\n\x10\x64\x65mand_unit_name\x18\x12 \x01(\t\x12\x12\n\ndemand_qty\x18\x13 \x01(\x01\x12\x16\n\x0eproduct_status\x18\x14 \x01(\t\x12\x12\n\nfreeze_qty\x18\x15 \x01(\x01\x12\x12\n\nbroker_qty\x18\x16 \x01(\x01\x12\x36\n\x0c\x65xtra_detail\x18\x17 \x03(\x0b\x32 .mobile_inventory_bi.ExtraDetail\x12\x18\n\x10purchase_unit_id\x18\x18 \x01(\x04\x12\x1a\n\x12purchase_unit_code\x18\x19 \x01(\t\x12\x1a\n\x12purchase_unit_name\x18\x1a \x01(\t\x12\x14\n\x0cpurchase_qty\x18\x1b \x01(\x01\x12\x30\n\x08\x63hildren\x18\x1c \x03(\x0b\x32\x1e.mobile_inventory_bi.Inventory\x12\x13\n\x0bposition_id\x18\x1d \x01(\x04\x12\x15\n\rposition_name\x18\x1e \x01(\t\x12\x15\n\rposition_code\x18\x1f \x01(\t\x12\x11\n\ttax_price\x18# \x01(\x01\x12\x12\n\ncost_price\x18$ \x01(\x01\x12\x12\n\nsku_amount\x18% \x01(\x01\x12\x19\n\x11\x64\x65mand_broker_qty\x18& \x01(\x01\"^\n\x0b\x45xtraDetail\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x0b\n\x03qty\x18\x02 \x01(\x01\x12\x10\n\x08sku_type\x18\x03 \x01(\t\x12\x0e\n\x06\x61mount\x18\x04 \x01(\x01\x12\x12\n\ndemand_qty\x18\x05 \x01(\x01\"\xf6\x02\n\x18QueryInventoryLogRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x13\n\x0bproduct_ids\x18\x02 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x03 \x03(\x04\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x05 \x01(\t\x12\r\n\x05limit\x18\x06 \x01(\x03\x12\x0e\n\x06offset\x18\x07 \x01(\x03\x12.\n\nstart_date\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\norder_type\x18\n \x01(\t\x12\x14\n\x0c\x61\x63\x63ount_type\x18\x0b \x01(\t\x12\x0b\n\x03lan\x18\x0c \x01(\t\x12\x14\n\x0cposition_ids\x18\r \x03(\x04\x12\x15\n\rreturn_fields\x18\x0e \x01(\t\x12\x1d\n\x15product_search_fields\x18\x0f \x01(\t\"o\n\x19QueryInventoryLogResponse\x12/\n\x04rows\x18\x01 \x03(\x0b\x32!.mobile_inventory_bi.InventoryLog\x12\r\n\x05total\x18\x02 \x01(\x05\x12\x12\n\namount_sum\x18\x03 \x01(\x01\"\xd5\x05\n\x0cInventoryLog\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nproduct_id\x18\x05 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x0c\n\x04spec\x18\x08 \x01(\t\x12\x12\n\norder_code\x18\t \x01(\t\x12\x12\n\norder_type\x18\n \x01(\t\x12.\n\norder_time\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06\x61\x63tion\x18\x0c \x01(\t\x12\x0e\n\x06status\x18\r \x01(\t\x12\x0b\n\x03qty\x18\x0e \x01(\x01\x12\x10\n\x08stock_id\x18\x0f \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x10 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\x11 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x12 \x01(\t\x12\x16\n\x0e\x64\x65mand_unit_id\x18\x13 \x01(\x04\x12\x18\n\x10\x64\x65mand_unit_code\x18\x14 \x01(\t\x12\x18\n\x10\x64\x65mand_unit_name\x18\x15 \x01(\t\x12\x12\n\ndemand_qty\x18\x16 \x01(\x01\x12\x13\n\x0b\x63\x61tegory_id\x18\x17 \x01(\x04\x12\x15\n\rcategory_code\x18\x18 \x01(\t\x12\x15\n\rcategory_name\x18\x19 \x01(\t\x12\x14\n\x0cjde_order_id\x18\x1a \x01(\t\x12\x16\n\x0ejde_order_type\x18\x1b \x01(\t\x12\x0f\n\x07jde_mcu\x18\x1c \x01(\t\x12\x14\n\x0c\x61\x63\x63ount_type\x18\x1d \x01(\t\x12\x16\n\x0esub_account_id\x18\x1e \x01(\x04\x12\x18\n\x10sub_account_code\x18\x1f \x01(\t\x12\x18\n\x10sub_account_name\x18  \x01(\t\"\x95\x03\n\x15\x44\x61ilyInventoryRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x02 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x03 \x03(\x04\x12.\n\nstart_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x06 \x01(\x05\x12\x0e\n\x06offset\x18\x07 \x01(\x05\x12\x0c\n\x04\x63ode\x18\t \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\n \x01(\t\x12\x0e\n\x06if_pre\x18\x0b \x01(\t\x12\x0e\n\x06if_end\x18\x0c \x01(\t\x12\x15\n\rexclude_empty\x18\r \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x0e \x01(\t\x12\x0b\n\x03lan\x18\x0f \x01(\t\x12\x14\n\x0cposition_ids\x18\x10 \x03(\x03\x12\x15\n\rreturn_fields\x18\x11 \x01(\t\x12\x1d\n\x15product_search_fields\x18\x12 \x01(\t\"Z\n\x16\x44\x61ilyInventoryResponse\x12\x31\n\x04rows\x18\x01 \x03(\x0b\x32#.mobile_inventory_bi.DailyInventory\x12\r\n\x05total\x18\x02 \x01(\x05\"\xe9\x0b\n\x0e\x44\x61ilyInventory\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nproduct_id\x18\x05 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x08 \x01(\x04\x12\x15\n\rcategory_code\x18\t \x01(\t\x12\x15\n\rcategory_name\x18\n \x01(\t\x12\x0c\n\x04spec\x18\x0b \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x0c \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\r \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x0e \x01(\t\x12\x0f\n\x07pre_qty\x18\x0f \x01(\x01\x12\x0b\n\x03qty\x18\x10 \x01(\x01\x12\x15\n\rtrans_deposit\x18\x11 \x01(\x01\x12\x16\n\x0etrans_withdraw\x18\x12 \x01(\x01\x12\x14\n\x0cstoc_deposit\x18\x13 \x01(\x01\x12\x15\n\rstoc_withdraw\x18\x14 \x01(\x01\x12\x13\n\x0b\x61\x64j_deposit\x18\x15 \x01(\x01\x12\x14\n\x0c\x61\x64j_withdraw\x18\x16 \x01(\x01\x12\x13\n\x0brec_deposit\x18\x17 \x01(\x01\x12\x18\n\x10rec_diff_deposit\x18\x18 \x01(\x01\x12\x19\n\x11rec_diff_withdraw\x18\x19 \x01(\x01\x12\x14\n\x0cret_withdraw\x18\x1a \x01(\x01\x12\x12\n\nstart_time\x18\x1b \x01(\t\x12\x10\n\x08\x65nd_time\x18\x1c \x01(\t\x12\x15\n\rsales_deposit\x18\x1d \x01(\x01\x12\x16\n\x0esales_withdraw\x18\x1e \x01(\x01\x12\x14\n\x0cllyl_deposit\x18\x1f \x01(\x01\x12\x15\n\rllyl_withdraw\x18  \x01(\x01\x12\x15\n\rcpcrk_deposit\x18! \x01(\x01\x12\x16\n\x0e\x63pcrk_withdraw\x18\" \x01(\x01\x12\x14\n\x0crec_withdraw\x18# \x01(\x01\x12\x13\n\x0bret_deposit\x18$ \x01(\x01\x12\x12\n\nfreeze_qty\x18% \x01(\x01\x12\x18\n\x10purchase_deposit\x18& \x01(\x01\x12\x10\n\x08low_cost\x18\' \x01(\x01\x12\x18\n\x10inventory_adjust\x18( \x01(\x01\x12\x16\n\x0einventory_init\x18) \x01(\x01\x12\x13\n\x0bsub_account\x18* \x01(\t\x12\x35\n\x08\x63hildren\x18+ \x03(\x0b\x32#.mobile_inventory_bi.DailyInventory\x12\x13\n\x0bposition_id\x18, \x01(\x04\x12\x15\n\rposition_name\x18- \x01(\t\x12\x15\n\rposition_code\x18. \x01(\t\x12\x15\n\rspick_deposit\x18/ \x01(\x01\x12\x1e\n\x16material_trans_deposit\x18\x30 \x01(\x01\x12\x1f\n\x17material_trans_withdraw\x18\x31 \x01(\x01\x12\x1a\n\x12processing_deposit\x18\x32 \x01(\x01\x12\x1b\n\x13processing_withdraw\x18\x33 \x01(\x01\x12\x17\n\x0fpacking_deposit\x18\x34 \x01(\x01\x12\x18\n\x10packing_withdraw\x18\x35 \x01(\x01\x12\x14\n\x0cret_transfer\x18\x36 \x01(\x01\x12\x16\n\x0etrans_transfer\x18\x37 \x01(\x01\x12\x16\n\x0etrans_delivery\x18\x38 \x01(\x01\x12\x16\n\x0etrans_purchase\x18\x39 \x01(\x01\x12\x1c\n\x14trans_return_release\x18: \x01(\x01\x12\x1e\n\x16trans_transfer_release\x18; \x01(\x01\x12\x1e\n\x16trans_delivery_release\x18< \x01(\x01\x12\x1e\n\x16trans_purchase_release\x18= \x01(\x01\x12\x13\n\x0btrans_begin\x18> \x01(\x01\x12\x11\n\ttrans_end\x18? \x01(\x01\x32\xc7\x05\n\x18MobileInventoryBiService\x12\xa4\x01\n\x11RealtimeInventory\x12-.mobile_inventory_bi.RealtimeInventoryRequest\x1a..mobile_inventory_bi.RealtimeInventoryResponse\"0\x82\xd3\xe4\x93\x02*\x12(/api/v2/supply/mobile/inventory/realtime\x12\xb1\x01\n\x14\x46rsRealtimeInventory\x12-.mobile_inventory_bi.RealtimeInventoryRequest\x1a..mobile_inventory_bi.RealtimeInventoryResponse\":\x82\xd3\xe4\x93\x02\x34\x12\x32/api/v2/supply/mobile/frs_store/inventory/realtime\x12\xa7\x01\n\x0f\x46rsInventoryLog\x12-.mobile_inventory_bi.QueryInventoryLogRequest\x1a..mobile_inventory_bi.QueryInventoryLogResponse\"5\x82\xd3\xe4\x93\x02/\x12-/api/v2/supply/mobile/frs_store/inventory/log\x12\xa5\x01\n\x11\x46rsDailyInventory\x12*.mobile_inventory_bi.DailyInventoryRequest\x1a+.mobile_inventory_bi.DailyInventoryResponse\"7\x82\xd3\xe4\x93\x02\x31\x12//api/v2/supply/mobile/frs_store/inventory/dailyb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_REALTIMEINVENTORYREQUEST = _descriptor.Descriptor(
  name='RealtimeInventoryRequest',
  full_name='mobile_inventory_bi.RealtimeInventoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='mobile_inventory_bi.RealtimeInventoryRequest.branch_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='geo_regions', full_name='mobile_inventory_bi.RealtimeInventoryRequest.geo_regions', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='mobile_inventory_bi.RealtimeInventoryRequest.category_ids', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='mobile_inventory_bi.RealtimeInventoryRequest.product_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_inventory_bi.RealtimeInventoryRequest.limit', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_inventory_bi.RealtimeInventoryRequest.offset', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_inventory_bi.RealtimeInventoryRequest.order', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='mobile_inventory_bi.RealtimeInventoryRequest.sort', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exclude', full_name='mobile_inventory_bi.RealtimeInventoryRequest.exclude', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_inventory_bi.RealtimeInventoryRequest.branch_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='mobile_inventory_bi.RealtimeInventoryRequest.lan', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='mobile_inventory_bi.RealtimeInventoryRequest.position_ids', index=11,
      number=12, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='mobile_inventory_bi.RealtimeInventoryRequest.return_fields', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_search_fields', full_name='mobile_inventory_bi.RealtimeInventoryRequest.product_search_fields', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=121,
  serialized_end=418,
)


_REALTIMEINVENTORYRESPONSE = _descriptor.Descriptor(
  name='RealtimeInventoryResponse',
  full_name='mobile_inventory_bi.RealtimeInventoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_inventory_bi.RealtimeInventoryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_inventory_bi.RealtimeInventoryResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=420,
  serialized_end=508,
)


_INVENTORY = _descriptor.Descriptor(
  name='Inventory',
  full_name='mobile_inventory_bi.Inventory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_inventory_bi.Inventory.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='mobile_inventory_bi.Inventory.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='mobile_inventory_bi.Inventory.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='mobile_inventory_bi.Inventory.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_inventory_bi.Inventory.product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='mobile_inventory_bi.Inventory.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_inventory_bi.Inventory.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='mobile_inventory_bi.Inventory.category_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='mobile_inventory_bi.Inventory.category_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='mobile_inventory_bi.Inventory.category_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='mobile_inventory_bi.Inventory.spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='mobile_inventory_bi.Inventory.accounting_unit_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='mobile_inventory_bi.Inventory.accounting_unit_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='mobile_inventory_bi.Inventory.accounting_unit_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='mobile_inventory_bi.Inventory.qty', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_id', full_name='mobile_inventory_bi.Inventory.demand_unit_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_code', full_name='mobile_inventory_bi.Inventory.demand_unit_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_name', full_name='mobile_inventory_bi.Inventory.demand_unit_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_qty', full_name='mobile_inventory_bi.Inventory.demand_qty', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_status', full_name='mobile_inventory_bi.Inventory.product_status', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='freeze_qty', full_name='mobile_inventory_bi.Inventory.freeze_qty', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='broker_qty', full_name='mobile_inventory_bi.Inventory.broker_qty', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extra_detail', full_name='mobile_inventory_bi.Inventory.extra_detail', index=22,
      number=23, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_unit_id', full_name='mobile_inventory_bi.Inventory.purchase_unit_id', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_unit_code', full_name='mobile_inventory_bi.Inventory.purchase_unit_code', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_unit_name', full_name='mobile_inventory_bi.Inventory.purchase_unit_name', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_qty', full_name='mobile_inventory_bi.Inventory.purchase_qty', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='children', full_name='mobile_inventory_bi.Inventory.children', index=27,
      number=28, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='mobile_inventory_bi.Inventory.position_id', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='mobile_inventory_bi.Inventory.position_name', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='mobile_inventory_bi.Inventory.position_code', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='mobile_inventory_bi.Inventory.tax_price', index=31,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='mobile_inventory_bi.Inventory.cost_price', index=32,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sku_amount', full_name='mobile_inventory_bi.Inventory.sku_amount', index=33,
      number=37, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_broker_qty', full_name='mobile_inventory_bi.Inventory.demand_broker_qty', index=34,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=511,
  serialized_end=1361,
)


_EXTRADETAIL = _descriptor.Descriptor(
  name='ExtraDetail',
  full_name='mobile_inventory_bi.ExtraDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_inventory_bi.ExtraDetail.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='mobile_inventory_bi.ExtraDetail.qty', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sku_type', full_name='mobile_inventory_bi.ExtraDetail.sku_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='mobile_inventory_bi.ExtraDetail.amount', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_qty', full_name='mobile_inventory_bi.ExtraDetail.demand_qty', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1363,
  serialized_end=1457,
)


_QUERYINVENTORYLOGREQUEST = _descriptor.Descriptor(
  name='QueryInventoryLogRequest',
  full_name='mobile_inventory_bi.QueryInventoryLogRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='mobile_inventory_bi.QueryInventoryLogRequest.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='mobile_inventory_bi.QueryInventoryLogRequest.product_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='mobile_inventory_bi.QueryInventoryLogRequest.category_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_inventory_bi.QueryInventoryLogRequest.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='mobile_inventory_bi.QueryInventoryLogRequest.action', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_inventory_bi.QueryInventoryLogRequest.limit', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_inventory_bi.QueryInventoryLogRequest.offset', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='mobile_inventory_bi.QueryInventoryLogRequest.start_date', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='mobile_inventory_bi.QueryInventoryLogRequest.end_date', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='mobile_inventory_bi.QueryInventoryLogRequest.order_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account_type', full_name='mobile_inventory_bi.QueryInventoryLogRequest.account_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='mobile_inventory_bi.QueryInventoryLogRequest.lan', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='mobile_inventory_bi.QueryInventoryLogRequest.position_ids', index=12,
      number=13, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='mobile_inventory_bi.QueryInventoryLogRequest.return_fields', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_search_fields', full_name='mobile_inventory_bi.QueryInventoryLogRequest.product_search_fields', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1460,
  serialized_end=1834,
)


_QUERYINVENTORYLOGRESPONSE = _descriptor.Descriptor(
  name='QueryInventoryLogResponse',
  full_name='mobile_inventory_bi.QueryInventoryLogResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_inventory_bi.QueryInventoryLogResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_inventory_bi.QueryInventoryLogResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount_sum', full_name='mobile_inventory_bi.QueryInventoryLogResponse.amount_sum', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1836,
  serialized_end=1947,
)


_INVENTORYLOG = _descriptor.Descriptor(
  name='InventoryLog',
  full_name='mobile_inventory_bi.InventoryLog',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_inventory_bi.InventoryLog.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='mobile_inventory_bi.InventoryLog.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='mobile_inventory_bi.InventoryLog.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='mobile_inventory_bi.InventoryLog.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_inventory_bi.InventoryLog.product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='mobile_inventory_bi.InventoryLog.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_inventory_bi.InventoryLog.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='mobile_inventory_bi.InventoryLog.spec', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='mobile_inventory_bi.InventoryLog.order_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='mobile_inventory_bi.InventoryLog.order_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_time', full_name='mobile_inventory_bi.InventoryLog.order_time', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='mobile_inventory_bi.InventoryLog.action', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_inventory_bi.InventoryLog.status', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='mobile_inventory_bi.InventoryLog.qty', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stock_id', full_name='mobile_inventory_bi.InventoryLog.stock_id', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='mobile_inventory_bi.InventoryLog.accounting_unit_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='mobile_inventory_bi.InventoryLog.accounting_unit_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='mobile_inventory_bi.InventoryLog.accounting_unit_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_id', full_name='mobile_inventory_bi.InventoryLog.demand_unit_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_code', full_name='mobile_inventory_bi.InventoryLog.demand_unit_code', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_name', full_name='mobile_inventory_bi.InventoryLog.demand_unit_name', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_qty', full_name='mobile_inventory_bi.InventoryLog.demand_qty', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='mobile_inventory_bi.InventoryLog.category_id', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='mobile_inventory_bi.InventoryLog.category_code', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='mobile_inventory_bi.InventoryLog.category_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_order_id', full_name='mobile_inventory_bi.InventoryLog.jde_order_id', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_order_type', full_name='mobile_inventory_bi.InventoryLog.jde_order_type', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_mcu', full_name='mobile_inventory_bi.InventoryLog.jde_mcu', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account_type', full_name='mobile_inventory_bi.InventoryLog.account_type', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_id', full_name='mobile_inventory_bi.InventoryLog.sub_account_id', index=29,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_code', full_name='mobile_inventory_bi.InventoryLog.sub_account_code', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_name', full_name='mobile_inventory_bi.InventoryLog.sub_account_name', index=31,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1950,
  serialized_end=2675,
)


_DAILYINVENTORYREQUEST = _descriptor.Descriptor(
  name='DailyInventoryRequest',
  full_name='mobile_inventory_bi.DailyInventoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='mobile_inventory_bi.DailyInventoryRequest.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='mobile_inventory_bi.DailyInventoryRequest.category_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='mobile_inventory_bi.DailyInventoryRequest.product_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='mobile_inventory_bi.DailyInventoryRequest.start_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='mobile_inventory_bi.DailyInventoryRequest.end_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_inventory_bi.DailyInventoryRequest.limit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_inventory_bi.DailyInventoryRequest.offset', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_inventory_bi.DailyInventoryRequest.code', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='mobile_inventory_bi.DailyInventoryRequest.action', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='if_pre', full_name='mobile_inventory_bi.DailyInventoryRequest.if_pre', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='if_end', full_name='mobile_inventory_bi.DailyInventoryRequest.if_end', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exclude_empty', full_name='mobile_inventory_bi.DailyInventoryRequest.exclude_empty', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_inventory_bi.DailyInventoryRequest.branch_type', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='mobile_inventory_bi.DailyInventoryRequest.lan', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='mobile_inventory_bi.DailyInventoryRequest.position_ids', index=14,
      number=16, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='mobile_inventory_bi.DailyInventoryRequest.return_fields', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_search_fields', full_name='mobile_inventory_bi.DailyInventoryRequest.product_search_fields', index=16,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2678,
  serialized_end=3083,
)


_DAILYINVENTORYRESPONSE = _descriptor.Descriptor(
  name='DailyInventoryResponse',
  full_name='mobile_inventory_bi.DailyInventoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_inventory_bi.DailyInventoryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_inventory_bi.DailyInventoryResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3085,
  serialized_end=3175,
)


_DAILYINVENTORY = _descriptor.Descriptor(
  name='DailyInventory',
  full_name='mobile_inventory_bi.DailyInventory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_inventory_bi.DailyInventory.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='mobile_inventory_bi.DailyInventory.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='mobile_inventory_bi.DailyInventory.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='mobile_inventory_bi.DailyInventory.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_inventory_bi.DailyInventory.product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='mobile_inventory_bi.DailyInventory.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_inventory_bi.DailyInventory.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='mobile_inventory_bi.DailyInventory.category_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='mobile_inventory_bi.DailyInventory.category_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='mobile_inventory_bi.DailyInventory.category_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='mobile_inventory_bi.DailyInventory.spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='mobile_inventory_bi.DailyInventory.accounting_unit_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='mobile_inventory_bi.DailyInventory.accounting_unit_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='mobile_inventory_bi.DailyInventory.accounting_unit_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_qty', full_name='mobile_inventory_bi.DailyInventory.pre_qty', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='mobile_inventory_bi.DailyInventory.qty', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_deposit', full_name='mobile_inventory_bi.DailyInventory.trans_deposit', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_withdraw', full_name='mobile_inventory_bi.DailyInventory.trans_withdraw', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stoc_deposit', full_name='mobile_inventory_bi.DailyInventory.stoc_deposit', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stoc_withdraw', full_name='mobile_inventory_bi.DailyInventory.stoc_withdraw', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adj_deposit', full_name='mobile_inventory_bi.DailyInventory.adj_deposit', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adj_withdraw', full_name='mobile_inventory_bi.DailyInventory.adj_withdraw', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_deposit', full_name='mobile_inventory_bi.DailyInventory.rec_deposit', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_diff_deposit', full_name='mobile_inventory_bi.DailyInventory.rec_diff_deposit', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_diff_withdraw', full_name='mobile_inventory_bi.DailyInventory.rec_diff_withdraw', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ret_withdraw', full_name='mobile_inventory_bi.DailyInventory.ret_withdraw', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_time', full_name='mobile_inventory_bi.DailyInventory.start_time', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='mobile_inventory_bi.DailyInventory.end_time', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_deposit', full_name='mobile_inventory_bi.DailyInventory.sales_deposit', index=28,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_withdraw', full_name='mobile_inventory_bi.DailyInventory.sales_withdraw', index=29,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='llyl_deposit', full_name='mobile_inventory_bi.DailyInventory.llyl_deposit', index=30,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='llyl_withdraw', full_name='mobile_inventory_bi.DailyInventory.llyl_withdraw', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cpcrk_deposit', full_name='mobile_inventory_bi.DailyInventory.cpcrk_deposit', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cpcrk_withdraw', full_name='mobile_inventory_bi.DailyInventory.cpcrk_withdraw', index=33,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_withdraw', full_name='mobile_inventory_bi.DailyInventory.rec_withdraw', index=34,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ret_deposit', full_name='mobile_inventory_bi.DailyInventory.ret_deposit', index=35,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='freeze_qty', full_name='mobile_inventory_bi.DailyInventory.freeze_qty', index=36,
      number=37, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_deposit', full_name='mobile_inventory_bi.DailyInventory.purchase_deposit', index=37,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='low_cost', full_name='mobile_inventory_bi.DailyInventory.low_cost', index=38,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_adjust', full_name='mobile_inventory_bi.DailyInventory.inventory_adjust', index=39,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_init', full_name='mobile_inventory_bi.DailyInventory.inventory_init', index=40,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account', full_name='mobile_inventory_bi.DailyInventory.sub_account', index=41,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='children', full_name='mobile_inventory_bi.DailyInventory.children', index=42,
      number=43, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='mobile_inventory_bi.DailyInventory.position_id', index=43,
      number=44, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='mobile_inventory_bi.DailyInventory.position_name', index=44,
      number=45, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='mobile_inventory_bi.DailyInventory.position_code', index=45,
      number=46, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spick_deposit', full_name='mobile_inventory_bi.DailyInventory.spick_deposit', index=46,
      number=47, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_trans_deposit', full_name='mobile_inventory_bi.DailyInventory.material_trans_deposit', index=47,
      number=48, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_trans_withdraw', full_name='mobile_inventory_bi.DailyInventory.material_trans_withdraw', index=48,
      number=49, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_deposit', full_name='mobile_inventory_bi.DailyInventory.processing_deposit', index=49,
      number=50, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_withdraw', full_name='mobile_inventory_bi.DailyInventory.processing_withdraw', index=50,
      number=51, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_deposit', full_name='mobile_inventory_bi.DailyInventory.packing_deposit', index=51,
      number=52, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_withdraw', full_name='mobile_inventory_bi.DailyInventory.packing_withdraw', index=52,
      number=53, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ret_transfer', full_name='mobile_inventory_bi.DailyInventory.ret_transfer', index=53,
      number=54, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_transfer', full_name='mobile_inventory_bi.DailyInventory.trans_transfer', index=54,
      number=55, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_delivery', full_name='mobile_inventory_bi.DailyInventory.trans_delivery', index=55,
      number=56, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_purchase', full_name='mobile_inventory_bi.DailyInventory.trans_purchase', index=56,
      number=57, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_return_release', full_name='mobile_inventory_bi.DailyInventory.trans_return_release', index=57,
      number=58, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_transfer_release', full_name='mobile_inventory_bi.DailyInventory.trans_transfer_release', index=58,
      number=59, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_delivery_release', full_name='mobile_inventory_bi.DailyInventory.trans_delivery_release', index=59,
      number=60, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_purchase_release', full_name='mobile_inventory_bi.DailyInventory.trans_purchase_release', index=60,
      number=61, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_begin', full_name='mobile_inventory_bi.DailyInventory.trans_begin', index=61,
      number=62, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_end', full_name='mobile_inventory_bi.DailyInventory.trans_end', index=62,
      number=63, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3178,
  serialized_end=4691,
)

_REALTIMEINVENTORYRESPONSE.fields_by_name['rows'].message_type = _INVENTORY
_INVENTORY.fields_by_name['extra_detail'].message_type = _EXTRADETAIL
_INVENTORY.fields_by_name['children'].message_type = _INVENTORY
_QUERYINVENTORYLOGREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYINVENTORYLOGREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYINVENTORYLOGRESPONSE.fields_by_name['rows'].message_type = _INVENTORYLOG
_INVENTORYLOG.fields_by_name['order_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DAILYINVENTORYREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DAILYINVENTORYREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DAILYINVENTORYRESPONSE.fields_by_name['rows'].message_type = _DAILYINVENTORY
_DAILYINVENTORY.fields_by_name['children'].message_type = _DAILYINVENTORY
DESCRIPTOR.message_types_by_name['RealtimeInventoryRequest'] = _REALTIMEINVENTORYREQUEST
DESCRIPTOR.message_types_by_name['RealtimeInventoryResponse'] = _REALTIMEINVENTORYRESPONSE
DESCRIPTOR.message_types_by_name['Inventory'] = _INVENTORY
DESCRIPTOR.message_types_by_name['ExtraDetail'] = _EXTRADETAIL
DESCRIPTOR.message_types_by_name['QueryInventoryLogRequest'] = _QUERYINVENTORYLOGREQUEST
DESCRIPTOR.message_types_by_name['QueryInventoryLogResponse'] = _QUERYINVENTORYLOGRESPONSE
DESCRIPTOR.message_types_by_name['InventoryLog'] = _INVENTORYLOG
DESCRIPTOR.message_types_by_name['DailyInventoryRequest'] = _DAILYINVENTORYREQUEST
DESCRIPTOR.message_types_by_name['DailyInventoryResponse'] = _DAILYINVENTORYRESPONSE
DESCRIPTOR.message_types_by_name['DailyInventory'] = _DAILYINVENTORY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

RealtimeInventoryRequest = _reflection.GeneratedProtocolMessageType('RealtimeInventoryRequest', (_message.Message,), dict(
  DESCRIPTOR = _REALTIMEINVENTORYREQUEST,
  __module__ = 'mobile.mobile_inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:mobile_inventory_bi.RealtimeInventoryRequest)
  ))
_sym_db.RegisterMessage(RealtimeInventoryRequest)

RealtimeInventoryResponse = _reflection.GeneratedProtocolMessageType('RealtimeInventoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _REALTIMEINVENTORYRESPONSE,
  __module__ = 'mobile.mobile_inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:mobile_inventory_bi.RealtimeInventoryResponse)
  ))
_sym_db.RegisterMessage(RealtimeInventoryResponse)

Inventory = _reflection.GeneratedProtocolMessageType('Inventory', (_message.Message,), dict(
  DESCRIPTOR = _INVENTORY,
  __module__ = 'mobile.mobile_inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:mobile_inventory_bi.Inventory)
  ))
_sym_db.RegisterMessage(Inventory)

ExtraDetail = _reflection.GeneratedProtocolMessageType('ExtraDetail', (_message.Message,), dict(
  DESCRIPTOR = _EXTRADETAIL,
  __module__ = 'mobile.mobile_inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:mobile_inventory_bi.ExtraDetail)
  ))
_sym_db.RegisterMessage(ExtraDetail)

QueryInventoryLogRequest = _reflection.GeneratedProtocolMessageType('QueryInventoryLogRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYINVENTORYLOGREQUEST,
  __module__ = 'mobile.mobile_inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:mobile_inventory_bi.QueryInventoryLogRequest)
  ))
_sym_db.RegisterMessage(QueryInventoryLogRequest)

QueryInventoryLogResponse = _reflection.GeneratedProtocolMessageType('QueryInventoryLogResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYINVENTORYLOGRESPONSE,
  __module__ = 'mobile.mobile_inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:mobile_inventory_bi.QueryInventoryLogResponse)
  ))
_sym_db.RegisterMessage(QueryInventoryLogResponse)

InventoryLog = _reflection.GeneratedProtocolMessageType('InventoryLog', (_message.Message,), dict(
  DESCRIPTOR = _INVENTORYLOG,
  __module__ = 'mobile.mobile_inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:mobile_inventory_bi.InventoryLog)
  ))
_sym_db.RegisterMessage(InventoryLog)

DailyInventoryRequest = _reflection.GeneratedProtocolMessageType('DailyInventoryRequest', (_message.Message,), dict(
  DESCRIPTOR = _DAILYINVENTORYREQUEST,
  __module__ = 'mobile.mobile_inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:mobile_inventory_bi.DailyInventoryRequest)
  ))
_sym_db.RegisterMessage(DailyInventoryRequest)

DailyInventoryResponse = _reflection.GeneratedProtocolMessageType('DailyInventoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _DAILYINVENTORYRESPONSE,
  __module__ = 'mobile.mobile_inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:mobile_inventory_bi.DailyInventoryResponse)
  ))
_sym_db.RegisterMessage(DailyInventoryResponse)

DailyInventory = _reflection.GeneratedProtocolMessageType('DailyInventory', (_message.Message,), dict(
  DESCRIPTOR = _DAILYINVENTORY,
  __module__ = 'mobile.mobile_inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:mobile_inventory_bi.DailyInventory)
  ))
_sym_db.RegisterMessage(DailyInventory)



_MOBILEINVENTORYBISERVICE = _descriptor.ServiceDescriptor(
  name='MobileInventoryBiService',
  full_name='mobile_inventory_bi.MobileInventoryBiService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=4694,
  serialized_end=5405,
  methods=[
  _descriptor.MethodDescriptor(
    name='RealtimeInventory',
    full_name='mobile_inventory_bi.MobileInventoryBiService.RealtimeInventory',
    index=0,
    containing_service=None,
    input_type=_REALTIMEINVENTORYREQUEST,
    output_type=_REALTIMEINVENTORYRESPONSE,
    serialized_options=_b('\202\323\344\223\002*\022(/api/v2/supply/mobile/inventory/realtime'),
  ),
  _descriptor.MethodDescriptor(
    name='FrsRealtimeInventory',
    full_name='mobile_inventory_bi.MobileInventoryBiService.FrsRealtimeInventory',
    index=1,
    containing_service=None,
    input_type=_REALTIMEINVENTORYREQUEST,
    output_type=_REALTIMEINVENTORYRESPONSE,
    serialized_options=_b('\202\323\344\223\0024\0222/api/v2/supply/mobile/frs_store/inventory/realtime'),
  ),
  _descriptor.MethodDescriptor(
    name='FrsInventoryLog',
    full_name='mobile_inventory_bi.MobileInventoryBiService.FrsInventoryLog',
    index=2,
    containing_service=None,
    input_type=_QUERYINVENTORYLOGREQUEST,
    output_type=_QUERYINVENTORYLOGRESPONSE,
    serialized_options=_b('\202\323\344\223\002/\022-/api/v2/supply/mobile/frs_store/inventory/log'),
  ),
  _descriptor.MethodDescriptor(
    name='FrsDailyInventory',
    full_name='mobile_inventory_bi.MobileInventoryBiService.FrsDailyInventory',
    index=3,
    containing_service=None,
    input_type=_DAILYINVENTORYREQUEST,
    output_type=_DAILYINVENTORYRESPONSE,
    serialized_options=_b('\202\323\344\223\0021\022//api/v2/supply/mobile/frs_store/inventory/daily'),
  ),
])
_sym_db.RegisterServiceDescriptor(_MOBILEINVENTORYBISERVICE)

DESCRIPTOR.services_by_name['MobileInventoryBiService'] = _MOBILEINVENTORYBISERVICE

# @@protoc_insertion_point(module_scope)
