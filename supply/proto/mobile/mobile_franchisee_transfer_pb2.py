# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mobile/mobile_franchisee_transfer.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='mobile/mobile_franchisee_transfer.proto',
  package='mobile_franchisee_transfer',
  syntax='proto3',
  serialized_options=_b('Z\034./mobile_franchisee_transfer'),
  serialized_pb=_b('\n\'mobile/mobile_franchisee_transfer.proto\x12\x1amobile_franchisee_transfer\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1cgoogle/protobuf/struct.proto\"\x13\n\x04Pong\x12\x0b\n\x03msg\x18\x01 \x01(\t\"\xab\x04\n\x12GetTransferRequest\x12\x15\n\rinclude_total\x18\x01 \x01(\x08\x12\x45\n\x06status\x18\x02 \x03(\x0e\x32\x35.mobile_franchisee_transfer.GetTransferRequest.STATUS\x12\x17\n\x0fshipping_stores\x18\x03 \x03(\x04\x12\x18\n\x10receiving_stores\x18\x04 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x05 \x03(\x04\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12\r\n\x05limit\x18\x07 \x01(\r\x12.\n\nstart_date\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x63ode\x18\x0b \x01(\t\x12\x0b\n\x03ids\x18\x0c \x03(\x04\x12\r\n\x05order\x18\x0f \x01(\t\x12\x0c\n\x04sort\x18\x10 \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x11 \x01(\t\x12\r\n\x05types\x18\x13 \x03(\t\x12\x10\n\x08sub_type\x18\x14 \x01(\t\x12\x1b\n\x13receiving_positions\x18\x15 \x03(\x04\x12\x1a\n\x12shipping_positions\x18\x16 \x03(\x04\"K\n\x06STATUS\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tSUBMITTED\x10\x02\x12\r\n\tCONFIRMED\x10\x03\x12\r\n\tCANCELLED\x10\x04\"\xd4\x07\n\x08Transfer\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x03 \x01(\x04\x12\x16\n\x0eshipping_store\x18\x05 \x01(\x04\x12\x1b\n\x13shipping_store_name\x18\x06 \x01(\t\x12\x17\n\x0freceiving_store\x18\x08 \x01(\x04\x12\x1c\n\x14receiving_store_name\x18\x07 \x01(\t\x12\x0c\n\x04\x63ode\x18\t \x01(\t\x12\x0e\n\x06remark\x18\r \x01(\t\x12;\n\x06status\x18\x11 \x01(\x0e\x32+.mobile_franchisee_transfer.Transfer.STATUS\x12<\n\x0eprocess_status\x18\x12 \x01(\x0e\x32$.mobile_franchisee_transfer.P_STATUS\x12\x12\n\ncreated_by\x18\x14 \x01(\x04\x12\x12\n\nupdated_by\x18\x15 \x01(\x04\x12\x31\n\rtransfer_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0ereceiving_date\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rshipping_date\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\x1d \x01(\t\x12\x14\n\x0cupdated_name\x18\x1e \x01(\t\x12\x13\n\x0b\x62ranch_type\x18! \x01(\t\x12\x10\n\x08sub_type\x18\" \x01(\t\x12\x0c\n\x04type\x18\x10 \x01(\t\x12\x1a\n\x12receiving_position\x18# \x01(\x04\x12\x1f\n\x17receiving_position_name\x18$ \x01(\t\x12\x19\n\x11shipping_position\x18% \x01(\x04\x12\x1e\n\x16shipping_position_name\x18& \x01(\t\x12\x18\n\x10sub_account_type\x18\' \x01(\t\x12\x0f\n\x07\x65xtends\x18( \x01(\t\x12\x14\n\x0ctotal_amount\x18) \x01(\x01\x12\x1a\n\x12total_sales_amount\x18* \x01(\x01\"K\n\x06STATUS\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tSUBMITTED\x10\x02\x12\r\n\tCONFIRMED\x10\x03\x12\r\n\tCANCELLED\x10\x04\"X\n\x13GetTransferResponse\x12\x32\n\x04rows\x18\x01 \x03(\x0b\x32$.mobile_franchisee_transfer.Transfer\x12\r\n\x05total\x18\x02 \x01(\r\"-\n\x16GetTransferByIDRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\"\xf2\x07\n\x17GetTransferByIDResponse\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x03 \x01(\x04\x12\x16\n\x0eshipping_store\x18\x05 \x01(\x04\x12\x1b\n\x13shipping_store_name\x18\x06 \x01(\t\x12\x17\n\x0freceiving_store\x18\x07 \x01(\x04\x12\x1c\n\x14receiving_store_name\x18\x08 \x01(\t\x12\x0c\n\x04\x63ode\x18\t \x01(\t\x12\x0e\n\x06remark\x18\r \x01(\t\x12J\n\x06status\x18\x11 \x01(\x0e\x32:.mobile_franchisee_transfer.GetTransferByIDResponse.STATUS\x12<\n\x0eprocess_status\x18\x12 \x01(\x0e\x32$.mobile_franchisee_transfer.P_STATUS\x12\x12\n\ncreated_by\x18\x14 \x01(\x04\x12\x12\n\nupdated_by\x18\x15 \x01(\x04\x12\x31\n\rtransfer_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0ereceiving_date\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rshipping_date\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\x1c \x01(\t\x12\x14\n\x0cupdated_name\x18\x1d \x01(\t\x12\x13\n\x0b\x62ranch_type\x18  \x01(\t\x12\x10\n\x08sub_type\x18\" \x01(\t\x12\x0c\n\x04type\x18\x10 \x01(\t\x12\x1a\n\x12receiving_position\x18# \x01(\x04\x12\x1f\n\x17receiving_position_name\x18$ \x01(\t\x12\x19\n\x11shipping_position\x18% \x01(\x04\x12\x1e\n\x16shipping_position_name\x18& \x01(\t\x12\x18\n\x10sub_account_type\x18\' \x01(\t\x12\x0f\n\x07\x65xtends\x18! \x01(\t\x12\x14\n\x0ctotal_amount\x18) \x01(\x01\x12\x1a\n\x12total_sales_amount\x18* \x01(\x01\"K\n\x06STATUS\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tSUBMITTED\x10\x02\x12\r\n\tCONFIRMED\x10\x03\x12\r\n\tCANCELLED\x10\x04\"\xd9\x01\n#GetTransferProductByBranchIDRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x15\n\rinclude_total\x18\x02 \x01(\x08\x12\r\n\x05order\x18\x03 \x01(\t\x12\x0e\n\x06offset\x18\x04 \x01(\r\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x15\n\rsearch_fields\x18\x06 \x01(\t\x12\x0e\n\x06search\x18\x07 \x01(\t\x12\x14\n\x0c\x63\x61tegory_ids\x18\x08 \x03(\x04\x12\x10\n\x08order_by\x18\t \x01(\t\x12\x0c\n\x04sort\x18\n \x01(\t\"Q\n\x0c\x43\x61tegoryItem\x12\x13\n\x0b\x63\x61tegory_id\x18\x01 \x01(\x04\x12\x15\n\rcategory_name\x18\x02 \x01(\t\x12\x15\n\rproduct_count\x18\x03 \x01(\x04\"?\n+GetTransferProductCategoryByBranchIDRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\"p\n,GetTransferProductCategoryByBranchIDResponse\x12@\n\x0e\x63\x61tegory_items\x18\x01 \x03(\x0b\x32(.mobile_franchisee_transfer.CategoryItem\"\xa2\x01\n\x13TransferProductUnit\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08quantity\x18\x02 \x01(\x01\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x10\n\x08tax_rate\x18\x04 \x01(\x01\x12\x11\n\ttax_price\x18\x05 \x01(\x01\x12\x12\n\ncost_price\x18\x06 \x01(\x01\x12\x13\n\x0bsales_price\x18\x07 \x01(\x01\x12\x11\n\tunit_rate\x18\x08 \x01(\x01\"\x91\x02\n\x15SelectTransferProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x03 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12=\n\x04unit\x18\x05 \x03(\x0b\x32/.mobile_franchisee_transfer.TransferProductUnit\x12\x0f\n\x07\x62\x61rcode\x18\x06 \x03(\t\x12\x0c\n\x04spec\x18\x07 \x01(\t\x12\x15\n\rcategory_name\x18\x08 \x01(\t\x12\x12\n\nmodel_name\x18\t \x01(\t\x12\x1a\n\x12real_inventory_qty\x18\n \x01(\x01\"\xcb\x01\n$GetTransferProductByBranchIDResponse\x12?\n\x04rows\x18\x01 \x03(\x0b\x32\x31.mobile_franchisee_transfer.SelectTransferProduct\x12\r\n\x05total\x18\x02 \x01(\r\x12S\n\x18inventory_unchanged_rows\x18\x03 \x03(\x0b\x32\x31.mobile_franchisee_transfer.SelectTransferProduct\"C\n\"GetTransferRegionByBranchIDRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"G\n\x0cRegionBranch\x12\x0f\n\x07\x61\x64\x64ress\x18\x01 \x01(\t\x12\n\n\x02id\x18\x02 \x01(\x04\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\"]\n#GetTransferRegionByBranchIDResponse\x12\x36\n\x04rows\x18\x01 \x03(\x0b\x32(.mobile_franchisee_transfer.RegionBranch\"\x81\x01\n%GetTransferProductByTransferIDRequest\x12\x15\n\rinclude_total\x18\x01 \x01(\x08\x12\r\n\x05order\x18\x02 \x01(\t\x12\x0e\n\x06offset\x18\x03 \x01(\r\x12\r\n\x05limit\x18\x04 \x01(\r\x12\x13\n\x0btransfer_id\x18\x05 \x01(\x04\"\xa5\x07\n\x0fTransferProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x13\n\x0btransfer_id\x18\x03 \x01(\x04\x12\x0f\n\x07unit_id\x18\x04 \x01(\x04\x12\x11\n\tunit_name\x18\x05 \x01(\t\x12\x11\n\tunit_spec\x18\x06 \x01(\t\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x07 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x08 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\t \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\n \x01(\t\x12$\n\x1c\x61\x63\x63ounting_received_quantity\x18\x0b \x01(\x01\x12\x13\n\x0bitem_number\x18\x0e \x01(\r\x12\x14\n\x0cproduct_code\x18\x10 \x01(\t\x12\x14\n\x0cproduct_name\x18\x11 \x01(\t\x12\x10\n\x08quantity\x18\x12 \x01(\x01\x12\x17\n\x0freceiving_store\x18\x13 \x01(\x04\x12\x16\n\x0eshipping_store\x18\x14 \x01(\x04\x12\x12\n\ncreated_by\x18\x15 \x01(\x04\x12\x12\n\nupdated_by\x18\x16 \x01(\x04\x12\x31\n\rtransfer_date\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07\x65xtends\x18\x1a \x01(\t\x12\x12\n\npartner_id\x18\x1b \x01(\x04\x12\x0f\n\x07user_id\x18\x1c \x01(\x04\x12#\n\x1b\x63onfirmed_received_quantity\x18\x1d \x01(\x01\x12\x14\n\x0c\x63reated_name\x18\x1e \x01(\t\x12\x14\n\x0cupdated_name\x18\x1f \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18  \x01(\x04\x12\x15\n\rcategory_name\x18! \x01(\t\x12\x12\n\nmodel_name\x18\" \x01(\t\x12\x10\n\x08tax_rate\x18# \x01(\x01\x12\x11\n\ttax_price\x18$ \x01(\x01\x12\x12\n\ncost_price\x18% \x01(\x01\x12\x0e\n\x06\x61mount\x18& \x01(\x01\x12\x13\n\x0bsales_price\x18\' \x01(\x01\x12\x14\n\x0csales_amount\x18( \x01(\x01\x12\x11\n\tunit_rate\x18) \x01(\x01\"r\n&GetTransferProductByTransferIDResponse\x12\x39\n\x04rows\x18\x01 \x03(\x0b\x32+.mobile_franchisee_transfer.TransferProduct\x12\r\n\x05total\x18\x02 \x01(\r\"\xc0\x01\n\x13PostTransferProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x0f\n\x07unit_id\x18\x02 \x01(\x04\x12\x10\n\x08quantity\x18\x03 \x01(\x01\x12\x10\n\x08tax_rate\x18\x04 \x01(\x01\x12\x11\n\ttax_price\x18\x05 \x01(\x01\x12\x12\n\ncost_price\x18\x06 \x01(\x01\x12\x0e\n\x06\x61mount\x18\x07 \x01(\x01\x12\x13\n\x0bsales_price\x18\x08 \x01(\x01\x12\x14\n\x0csales_amount\x18\t \x01(\x01\"\xdc\x03\n\x15\x43reateTransferRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x16\n\x0eshipping_store\x18\x02 \x01(\x04\x12\x17\n\x0freceiving_store\x18\x03 \x01(\x04\x12\x41\n\x08products\x18\x04 \x03(\x0b\x32/.mobile_franchisee_transfer.PostTransferProduct\x12\x0e\n\x06remark\x18\x05 \x01(\t\x12\x31\n\rshipping_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rtransfer_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\x08 \x01(\t\x12\x0c\n\x04type\x18\n \x01(\t\x12\x10\n\x08sub_type\x18\x0b \x01(\t\x12\x1a\n\x12receiving_position\x18\x0c \x01(\x04\x12\x19\n\x11shipping_position\x18\r \x01(\x04\x12\x1f\n\x17receiving_position_name\x18\x0e \x01(\t\x12\x1e\n\x16shipping_position_name\x18\x0f \x01(\t\x12\x18\n\x10sub_account_type\x18\x10 \x01(\t\"\xe7\x01\n\x15UpdateTransferRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\x12\x41\n\x08products\x18\x02 \x03(\x0b\x32/.mobile_franchisee_transfer.PostTransferProduct\x12\x0e\n\x06remark\x18\x03 \x01(\t\x12\x31\n\rtransfer_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\x05 \x01(\t\x12\x0c\n\x04type\x18\n \x01(\t\x12\x10\n\x08sub_type\x18\x0b \x01(\t\"(\n\x16UpdateTransferResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"f\n\x1a\x43onfirmPostTransferProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x0f\n\x07unit_id\x18\x02 \x01(\x04\x12#\n\x1b\x63onfirmed_received_quantity\x18\x03 \x01(\x01\"\xb7\x01\n\x16\x43onfirmTransferRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\x12\x10\n\x08receiver\x18\x02 \x01(\x04\x12\x17\n\x0freceiving_store\x18\x03 \x01(\x04\x12H\n\x08products\x18\x04 \x03(\x0b\x32\x36.mobile_franchisee_transfer.ConfirmPostTransferProduct\x12\x13\n\x0b\x62ranch_type\x18\x05 \x01(\t\"@\n\x1c\x44\x65leteTransferProductRequest\x12\x0b\n\x03ids\x18\x01 \x03(\x04\x12\x13\n\x0btransfer_id\x18\x02 \x01(\x04\"/\n\x1d\x44\x65leteTransferProductResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\",\n\x15\x44\x65leteTransferRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\"(\n\x16\x44\x65leteTransferResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\xaf\x01\n\x15SubmitTransferRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\x12\x10\n\x08receiver\x18\x02 \x01(\x04\x12\x17\n\x0freceiving_store\x18\x03 \x01(\x04\x12\x41\n\x08products\x18\x04 \x03(\x0b\x32/.mobile_franchisee_transfer.PostTransferProduct\x12\x13\n\x0b\x62ranch_type\x18\x05 \x01(\t\"\xa3\x01\n\x1eSubmitTransferReceivingRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\x12\x10\n\x08receiver\x18\x02 \x01(\x04\x12\x17\n\x0freceiving_store\x18\x03 \x01(\x04\x12\x41\n\x08products\x18\x04 \x03(\x0b\x32/.mobile_franchisee_transfer.PostTransferProduct\",\n\x15\x43\x61ncelTransferRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\"\xa2\x03\n\x19GetTransferCollectRequest\x12\x0e\n\x06st_ids\x18\x01 \x03(\x04\x12\x11\n\tstore_ids\x18\x02 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x03 \x03(\x04\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12.\n\nstart_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x07 \x01(\r\x12\x0e\n\x06offset\x18\x08 \x01(\r\x12\x15\n\rinclude_total\x18\t \x01(\x08\x12\r\n\x05is_in\x18\n \x01(\x08\x12\r\n\x05order\x18\x0b \x01(\t\x12\x0c\n\x04sort\x18\x0c \x01(\t\x12\x0c\n\x04\x63ode\x18\r \x01(\t\x12\x10\n\x08jde_code\x18\x0e \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0f \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x10 \x01(\t\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x0c\n\x04type\x18\x12 \x01(\t\x12\x10\n\x08sub_type\x18\x13 \x01(\t\"\xac\x06\n\x0fTransferCollect\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x05 \x01(\x04\x12\x15\n\rcategory_name\x18\x06 \x01(\t\x12\x30\n\x0f\x63\x61tegory_parent\x18\x0b \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x14\n\x0cproduct_code\x18\x0c \x01(\t\x12\x12\n\nproduct_id\x18\r \x01(\x04\x12\x14\n\x0cproduct_name\x18\x0e \x01(\t\x12\x15\n\rproduct_price\x18\x0f \x01(\x01\x12\x17\n\x0freceiving_store\x18\x10 \x01(\x04\x12\x1c\n\x14receiving_store_code\x18\x11 \x01(\t\x12\x1d\n\x15receiving_store_us_id\x18\x12 \x01(\t\x12\x1c\n\x14receiving_store_name\x18\x13 \x01(\t\x12\x10\n\x08quantity\x18\x14 \x01(\x01\x12\x16\n\x0eshipping_store\x18\x15 \x01(\x04\x12\x1b\n\x13shipping_store_code\x18\x16 \x01(\t\x12\x1b\n\x13shipping_store_name\x18\x17 \x01(\t\x12\x1c\n\x14shipping_store_us_id\x18\x18 \x01(\t\x12\x0f\n\x07unit_id\x18\x19 \x01(\x04\x12\x11\n\tunit_name\x18\x1a \x01(\t\x12\x14\n\x0cproduct_spec\x18\x1b \x01(\t\x12\r\n\x05price\x18\x1c \x01(\x01\x12\x1a\n\x12receiving_position\x18\x1d \x01(\x04\x12\x1f\n\x17receiving_position_code\x18\x1e \x01(\t\x12\x1f\n\x17receiving_position_name\x18\x1f \x01(\t\x12\x19\n\x11shipping_position\x18  \x01(\x04\x12\x1e\n\x16shipping_position_code\x18! \x01(\t\x12\x1e\n\x16shipping_position_name\x18\" \x01(\t\"\\\n\x14TransferCollectTotal\x12\r\n\x05\x63ount\x18\x01 \x01(\x04\x12\x1f\n\x17sum_accounting_quantity\x18\x02 \x01(\x01\x12\x14\n\x0csum_quantity\x18\x03 \x01(\x01\"\x98\x01\n\x1aGetTransferCollectResponse\x12\x39\n\x04rows\x18\x01 \x03(\x0b\x32+.mobile_franchisee_transfer.TransferCollect\x12?\n\x05total\x18\x02 \x01(\x0b\x32\x30.mobile_franchisee_transfer.TransferCollectTotal\"\xaa\x03\n!GetTransferCollectDetailedRequest\x12\x0e\n\x06st_ids\x18\x01 \x03(\x04\x12\x11\n\tstore_ids\x18\x02 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x03 \x03(\x04\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12.\n\nstart_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x07 \x01(\r\x12\x0e\n\x06offset\x18\x08 \x01(\r\x12\x15\n\rinclude_total\x18\t \x01(\x08\x12\r\n\x05is_in\x18\n \x01(\x08\x12\r\n\x05order\x18\x0b \x01(\t\x12\x0c\n\x04sort\x18\x0c \x01(\t\x12\x0c\n\x04\x63ode\x18\r \x01(\t\x12\x10\n\x08jde_code\x18\x0e \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0f \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x10 \x01(\t\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x0c\n\x04type\x18\x12 \x01(\t\x12\x10\n\x08sub_type\x18\x13 \x01(\t\"\x86\x07\n\x17TransferCollectDetailed\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x05 \x01(\x04\x12\x15\n\rcategory_name\x18\x06 \x01(\t\x12\n\n\x02id\x18\x07 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x08 \x01(\t\x12\x12\n\nproduct_id\x18\t \x01(\x04\x12\x14\n\x0cproduct_name\x18\n \x01(\t\x12\x14\n\x0cproduct_spec\x18\x0b \x01(\t\x12\x10\n\x08quantity\x18\x0c \x01(\x01\x12\x17\n\x0freceiving_store\x18\r \x01(\x04\x12\x1c\n\x14receiving_store_code\x18\x0e \x01(\t\x12\x1c\n\x14receiving_store_name\x18\x0f \x01(\t\x12\x16\n\x0eshipping_store\x18\x10 \x01(\x04\x12\x1b\n\x13shipping_store_code\x18\x11 \x01(\t\x12\x1b\n\x13shipping_store_name\x18\x12 \x01(\t\x12\x0e\n\x06status\x18\x13 \x01(\t\x12\x15\n\rtransfer_code\x18\x14 \x01(\t\x12\x31\n\rtransfer_date\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0btransfer_id\x18\x16 \x01(\x04\x12\x0f\n\x07unit_id\x18\x17 \x01(\x04\x12\x11\n\tunit_name\x18\x18 \x01(\t\x12\x1c\n\x14shipping_store_us_id\x18\x19 \x01(\t\x12\x1d\n\x15receiving_store_us_id\x18\x1a \x01(\t\x12\r\n\x05price\x18\x1b \x01(\x01\x12\x10\n\x08jde_code\x18! \x01(\t\x12\x0c\n\x04\x63ode\x18\" \x01(\t\x12\x1a\n\x12receiving_position\x18( \x01(\x04\x12\x1f\n\x17receiving_position_code\x18) \x01(\t\x12\x1f\n\x17receiving_position_name\x18* \x01(\t\x12\x19\n\x11shipping_position\x18+ \x01(\x04\x12\x1e\n\x16shipping_position_code\x18, \x01(\t\x12\x1e\n\x16shipping_position_name\x18- \x01(\t\"\xa8\x01\n\"GetTransferCollectDetailedResponse\x12\x41\n\x04rows\x18\x01 \x03(\x0b\x32\x33.mobile_franchisee_transfer.TransferCollectDetailed\x12?\n\x05total\x18\x02 \x01(\x0b\x32\x30.mobile_franchisee_transfer.TransferCollectTotal\",\n\x15GetTransferLogRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\"V\n\x16GetTransferLogResponse\x12-\n\x04rows\x18\x01 \x03(\x0b\x32\x1f.mobile_franchisee_transfer.Log\x12\r\n\x05total\x18\x02 \x01(\x04\"\x8b\x01\n\x03Log\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x12\n\ncreated_by\x18\x03 \x01(\x04\x12.\n\ncreated_at\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06reason\x18\x05 \x01(\t\x12\x14\n\x0c\x63reated_name\x18\x06 \x01(\t*K\n\x08P_STATUS\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06INITED\x10\x01\x12\x0e\n\nPROCESSING\x10\x02\x12\r\n\tSUCCESSED\x10\x03\x12\n\n\x06\x46\x41ILED\x10\x04\x32\x8c\x1a\n\x18mobileFranchiseeTransfer\x12\xa4\x01\n\x0e\x43reateTransfer\x12\x31.mobile_franchisee_transfer.CreateTransferRequest\x1a$.mobile_franchisee_transfer.Transfer\"9\x82\xd3\xe4\x93\x02\x33\"./api/v2/supply/mobile/franchisee/transfer/main:\x01*\x12\xc7\x01\n\x0eUpdateTransfer\x12\x31.mobile_franchisee_transfer.UpdateTransferRequest\x1a\x32.mobile_franchisee_transfer.UpdateTransferResponse\"N\x82\xd3\xe4\x93\x02H\"C/api/v2/supply/mobile/franchisee/transfer/main/{transfer_id}/update:\x01*\x12\xa1\x01\n\x0bGetTransfer\x12..mobile_franchisee_transfer.GetTransferRequest\x1a/.mobile_franchisee_transfer.GetTransferResponse\"1\x82\xd3\xe4\x93\x02+\x12)/api/v2/supply/mobile/franchisee/transfer\x12\xbb\x01\n\x0fGetTransferByID\x12\x32.mobile_franchisee_transfer.GetTransferByIDRequest\x1a\x33.mobile_franchisee_transfer.GetTransferByIDResponse\"?\x82\xd3\xe4\x93\x02\x39\x12\x37/api/v2/supply/mobile/franchisee/transfer/{transfer_id}\x12\xed\x01\n\x1cGetTransferProductByBranchID\x12?.mobile_franchisee_transfer.GetTransferProductByBranchIDRequest\x1a@.mobile_franchisee_transfer.GetTransferProductByBranchIDResponse\"J\x82\xd3\xe4\x93\x02\x44\x12\x42/api/v2/supply/mobile/franchisee/transfer/store/{store_id}/product\x12\x86\x02\n$GetTransferProductCategoryByBranchID\x12G.mobile_franchisee_transfer.GetTransferProductCategoryByBranchIDRequest\x1aH.mobile_franchisee_transfer.GetTransferProductCategoryByBranchIDResponse\"K\x82\xd3\xe4\x93\x02\x45\x12\x43/api/v2/supply/mobile/franchisee/transfer/store/{store_id}/category\x12\xe9\x01\n\x1bGetTransferRegionByBranchID\x12>.mobile_franchisee_transfer.GetTransferRegionByBranchIDRequest\x1a?.mobile_franchisee_transfer.GetTransferRegionByBranchIDResponse\"I\x82\xd3\xe4\x93\x02\x43\x12\x41/api/v2/supply/mobile/franchisee/transfer/store/{store_id}/region\x12\xf0\x01\n\x1eGetTransferProductByTransferID\x12\x41.mobile_franchisee_transfer.GetTransferProductByTransferIDRequest\x1a\x42.mobile_franchisee_transfer.GetTransferProductByTransferIDResponse\"G\x82\xd3\xe4\x93\x02\x41\x12?/api/v2/supply/mobile/franchisee/transfer/{transfer_id}/product\x12\xbc\x01\n\x0f\x43onfirmTransfer\x12\x32.mobile_franchisee_transfer.ConfirmTransferRequest\x1a$.mobile_franchisee_transfer.Transfer\"O\x82\xd3\xe4\x93\x02I\"D/api/v2/supply/mobile/franchisee/transfer/main/{transfer_id}/confirm:\x01*\x12\xdf\x01\n\x15\x44\x65leteTransferProduct\x12\x38.mobile_franchisee_transfer.DeleteTransferProductRequest\x1a\x39.mobile_franchisee_transfer.DeleteTransferProductResponse\"Q\x82\xd3\xe4\x93\x02K\"F/api/v2/supply/mobile/franchisee/transfer/product/{transfer_id}/delete:\x01*\x12\xc7\x01\n\x0e\x44\x65leteTransfer\x12\x31.mobile_franchisee_transfer.DeleteTransferRequest\x1a\x32.mobile_franchisee_transfer.DeleteTransferResponse\"N\x82\xd3\xe4\x93\x02H\"C/api/v2/supply/mobile/franchisee/transfer/main/{transfer_id}/delete:\x01*\x12\xb9\x01\n\x0eSubmitTransfer\x12\x31.mobile_franchisee_transfer.SubmitTransferRequest\x1a$.mobile_franchisee_transfer.Transfer\"N\x82\xd3\xe4\x93\x02H\"C/api/v2/supply/mobile/franchisee/transfer/main/{transfer_id}/submit:\x01*\x12\xb9\x01\n\x0e\x43\x61ncelTransfer\x12\x31.mobile_franchisee_transfer.CancelTransferRequest\x1a$.mobile_franchisee_transfer.Transfer\"N\x82\xd3\xe4\x93\x02H\"C/api/v2/supply/mobile/franchisee/transfer/main/{transfer_id}/cancel:\x01*\x12\xc5\x01\n\x12GetTransferCollect\x12\x35.mobile_franchisee_transfer.GetTransferCollectRequest\x1a\x36.mobile_franchisee_transfer.GetTransferCollectResponse\"@\x82\xd3\xe4\x93\x02:\x12\x38/api/v2/supply/mobile/franchisee/transfer/collect/report\x12\xda\x01\n\x1aGetTransferCollectDetailed\x12=.mobile_franchisee_transfer.GetTransferCollectDetailedRequest\x1a>.mobile_franchisee_transfer.GetTransferCollectDetailedResponse\"=\x82\xd3\xe4\x93\x02\x37\x12\x35/api/v2/supply/mobile/franchisee/transfer/bi/detailed\x12\xbc\x01\n\x0eGetTransferLog\x12\x31.mobile_franchisee_transfer.GetTransferLogRequest\x1a\x32.mobile_franchisee_transfer.GetTransferLogResponse\"C\x82\xd3\xe4\x93\x02=\x12;/api/v2/supply/mobile/franchisee/transfer/{transfer_id}/logB\x1eZ\x1c./mobile_franchisee_transferb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,google_dot_protobuf_dot_struct__pb2.DESCRIPTOR,])

_P_STATUS = _descriptor.EnumDescriptor(
  name='P_STATUS',
  full_name='mobile_franchisee_transfer.P_STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PROCESSING', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUCCESSED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FAILED', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=10525,
  serialized_end=10600,
)
_sym_db.RegisterEnumDescriptor(_P_STATUS)

P_STATUS = enum_type_wrapper.EnumTypeWrapper(_P_STATUS)
NONE = 0
INITED = 1
PROCESSING = 2
SUCCESSED = 3
FAILED = 4


_GETTRANSFERREQUEST_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='mobile_franchisee_transfer.GetTransferRequest.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=666,
  serialized_end=741,
)
_sym_db.RegisterEnumDescriptor(_GETTRANSFERREQUEST_STATUS)

_TRANSFER_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='mobile_franchisee_transfer.Transfer.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=666,
  serialized_end=741,
)
_sym_db.RegisterEnumDescriptor(_TRANSFER_STATUS)

_GETTRANSFERBYIDRESPONSE_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='mobile_franchisee_transfer.GetTransferByIDResponse.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=666,
  serialized_end=741,
)
_sym_db.RegisterEnumDescriptor(_GETTRANSFERBYIDRESPONSE_STATUS)


_PONG = _descriptor.Descriptor(
  name='Pong',
  full_name='mobile_franchisee_transfer.Pong',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='msg', full_name='mobile_franchisee_transfer.Pong.msg', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=164,
  serialized_end=183,
)


_GETTRANSFERREQUEST = _descriptor.Descriptor(
  name='GetTransferRequest',
  full_name='mobile_franchisee_transfer.GetTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='include_total', full_name='mobile_franchisee_transfer.GetTransferRequest.include_total', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_franchisee_transfer.GetTransferRequest.status', index=1,
      number=2, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_stores', full_name='mobile_franchisee_transfer.GetTransferRequest.shipping_stores', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_stores', full_name='mobile_franchisee_transfer.GetTransferRequest.receiving_stores', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='mobile_franchisee_transfer.GetTransferRequest.product_ids', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_franchisee_transfer.GetTransferRequest.offset', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_franchisee_transfer.GetTransferRequest.limit', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='mobile_franchisee_transfer.GetTransferRequest.start_date', index=7,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='mobile_franchisee_transfer.GetTransferRequest.end_date', index=8,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_franchisee_transfer.GetTransferRequest.code', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='mobile_franchisee_transfer.GetTransferRequest.ids', index=10,
      number=12, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_franchisee_transfer.GetTransferRequest.order', index=11,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='mobile_franchisee_transfer.GetTransferRequest.sort', index=12,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_franchisee_transfer.GetTransferRequest.branch_type', index=13,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='types', full_name='mobile_franchisee_transfer.GetTransferRequest.types', index=14,
      number=19, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='mobile_franchisee_transfer.GetTransferRequest.sub_type', index=15,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_positions', full_name='mobile_franchisee_transfer.GetTransferRequest.receiving_positions', index=16,
      number=21, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_positions', full_name='mobile_franchisee_transfer.GetTransferRequest.shipping_positions', index=17,
      number=22, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _GETTRANSFERREQUEST_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=186,
  serialized_end=741,
)


_TRANSFER = _descriptor.Descriptor(
  name='Transfer',
  full_name='mobile_franchisee_transfer.Transfer',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_franchisee_transfer.Transfer.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='mobile_franchisee_transfer.Transfer.partner_id', index=1,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='mobile_franchisee_transfer.Transfer.shipping_store', index=2,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_name', full_name='mobile_franchisee_transfer.Transfer.shipping_store_name', index=3,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='mobile_franchisee_transfer.Transfer.receiving_store', index=4,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_name', full_name='mobile_franchisee_transfer.Transfer.receiving_store_name', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_franchisee_transfer.Transfer.code', index=6,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_franchisee_transfer.Transfer.remark', index=7,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_franchisee_transfer.Transfer.status', index=8,
      number=17, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='mobile_franchisee_transfer.Transfer.process_status', index=9,
      number=18, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='mobile_franchisee_transfer.Transfer.created_by', index=10,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='mobile_franchisee_transfer.Transfer.updated_by', index=11,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='mobile_franchisee_transfer.Transfer.transfer_date', index=12,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_date', full_name='mobile_franchisee_transfer.Transfer.receiving_date', index=13,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_date', full_name='mobile_franchisee_transfer.Transfer.shipping_date', index=14,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='mobile_franchisee_transfer.Transfer.created_at', index=15,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='mobile_franchisee_transfer.Transfer.updated_at', index=16,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='mobile_franchisee_transfer.Transfer.created_name', index=17,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='mobile_franchisee_transfer.Transfer.updated_name', index=18,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_franchisee_transfer.Transfer.branch_type', index=19,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='mobile_franchisee_transfer.Transfer.sub_type', index=20,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='mobile_franchisee_transfer.Transfer.type', index=21,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position', full_name='mobile_franchisee_transfer.Transfer.receiving_position', index=22,
      number=35, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_name', full_name='mobile_franchisee_transfer.Transfer.receiving_position_name', index=23,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position', full_name='mobile_franchisee_transfer.Transfer.shipping_position', index=24,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_name', full_name='mobile_franchisee_transfer.Transfer.shipping_position_name', index=25,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='mobile_franchisee_transfer.Transfer.sub_account_type', index=26,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='mobile_franchisee_transfer.Transfer.extends', index=27,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='mobile_franchisee_transfer.Transfer.total_amount', index=28,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_sales_amount', full_name='mobile_franchisee_transfer.Transfer.total_sales_amount', index=29,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _TRANSFER_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=744,
  serialized_end=1724,
)


_GETTRANSFERRESPONSE = _descriptor.Descriptor(
  name='GetTransferResponse',
  full_name='mobile_franchisee_transfer.GetTransferResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_franchisee_transfer.GetTransferResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_franchisee_transfer.GetTransferResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1726,
  serialized_end=1814,
)


_GETTRANSFERBYIDREQUEST = _descriptor.Descriptor(
  name='GetTransferByIDRequest',
  full_name='mobile_franchisee_transfer.GetTransferByIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_franchisee_transfer.GetTransferByIDRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1816,
  serialized_end=1861,
)


_GETTRANSFERBYIDRESPONSE = _descriptor.Descriptor(
  name='GetTransferByIDResponse',
  full_name='mobile_franchisee_transfer.GetTransferByIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.partner_id', index=1,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.shipping_store', index=2,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_name', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.shipping_store_name', index=3,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.receiving_store', index=4,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_name', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.receiving_store_name', index=5,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.code', index=6,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.remark', index=7,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.status', index=8,
      number=17, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.process_status', index=9,
      number=18, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.created_by', index=10,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.updated_by', index=11,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.transfer_date', index=12,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_date', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.receiving_date', index=13,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_date', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.shipping_date', index=14,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.created_at', index=15,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.updated_at', index=16,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.created_name', index=17,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.updated_name', index=18,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.branch_type', index=19,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.sub_type', index=20,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.type', index=21,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.receiving_position', index=22,
      number=35, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_name', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.receiving_position_name', index=23,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.shipping_position', index=24,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_name', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.shipping_position_name', index=25,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.sub_account_type', index=26,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.extends', index=27,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.total_amount', index=28,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_sales_amount', full_name='mobile_franchisee_transfer.GetTransferByIDResponse.total_sales_amount', index=29,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _GETTRANSFERBYIDRESPONSE_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1864,
  serialized_end=2874,
)


_GETTRANSFERPRODUCTBYBRANCHIDREQUEST = _descriptor.Descriptor(
  name='GetTransferProductByBranchIDRequest',
  full_name='mobile_franchisee_transfer.GetTransferProductByBranchIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='mobile_franchisee_transfer.GetTransferProductByBranchIDRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='mobile_franchisee_transfer.GetTransferProductByBranchIDRequest.include_total', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_franchisee_transfer.GetTransferProductByBranchIDRequest.order', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_franchisee_transfer.GetTransferProductByBranchIDRequest.offset', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_franchisee_transfer.GetTransferProductByBranchIDRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='mobile_franchisee_transfer.GetTransferProductByBranchIDRequest.search_fields', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='mobile_franchisee_transfer.GetTransferProductByBranchIDRequest.search', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='mobile_franchisee_transfer.GetTransferProductByBranchIDRequest.category_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by', full_name='mobile_franchisee_transfer.GetTransferProductByBranchIDRequest.order_by', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='mobile_franchisee_transfer.GetTransferProductByBranchIDRequest.sort', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2877,
  serialized_end=3094,
)


_CATEGORYITEM = _descriptor.Descriptor(
  name='CategoryItem',
  full_name='mobile_franchisee_transfer.CategoryItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='category_id', full_name='mobile_franchisee_transfer.CategoryItem.category_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='mobile_franchisee_transfer.CategoryItem.category_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_count', full_name='mobile_franchisee_transfer.CategoryItem.product_count', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3096,
  serialized_end=3177,
)


_GETTRANSFERPRODUCTCATEGORYBYBRANCHIDREQUEST = _descriptor.Descriptor(
  name='GetTransferProductCategoryByBranchIDRequest',
  full_name='mobile_franchisee_transfer.GetTransferProductCategoryByBranchIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='mobile_franchisee_transfer.GetTransferProductCategoryByBranchIDRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3179,
  serialized_end=3242,
)


_GETTRANSFERPRODUCTCATEGORYBYBRANCHIDRESPONSE = _descriptor.Descriptor(
  name='GetTransferProductCategoryByBranchIDResponse',
  full_name='mobile_franchisee_transfer.GetTransferProductCategoryByBranchIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='category_items', full_name='mobile_franchisee_transfer.GetTransferProductCategoryByBranchIDResponse.category_items', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3244,
  serialized_end=3356,
)


_TRANSFERPRODUCTUNIT = _descriptor.Descriptor(
  name='TransferProductUnit',
  full_name='mobile_franchisee_transfer.TransferProductUnit',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_franchisee_transfer.TransferProductUnit.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='mobile_franchisee_transfer.TransferProductUnit.quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='mobile_franchisee_transfer.TransferProductUnit.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='mobile_franchisee_transfer.TransferProductUnit.tax_rate', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='mobile_franchisee_transfer.TransferProductUnit.tax_price', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='mobile_franchisee_transfer.TransferProductUnit.cost_price', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='mobile_franchisee_transfer.TransferProductUnit.sales_price', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='mobile_franchisee_transfer.TransferProductUnit.unit_rate', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3359,
  serialized_end=3521,
)


_SELECTTRANSFERPRODUCT = _descriptor.Descriptor(
  name='SelectTransferProduct',
  full_name='mobile_franchisee_transfer.SelectTransferProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_franchisee_transfer.SelectTransferProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='mobile_franchisee_transfer.SelectTransferProduct.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='mobile_franchisee_transfer.SelectTransferProduct.category_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_franchisee_transfer.SelectTransferProduct.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit', full_name='mobile_franchisee_transfer.SelectTransferProduct.unit', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='barcode', full_name='mobile_franchisee_transfer.SelectTransferProduct.barcode', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='mobile_franchisee_transfer.SelectTransferProduct.spec', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='mobile_franchisee_transfer.SelectTransferProduct.category_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_name', full_name='mobile_franchisee_transfer.SelectTransferProduct.model_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='real_inventory_qty', full_name='mobile_franchisee_transfer.SelectTransferProduct.real_inventory_qty', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3524,
  serialized_end=3797,
)


_GETTRANSFERPRODUCTBYBRANCHIDRESPONSE = _descriptor.Descriptor(
  name='GetTransferProductByBranchIDResponse',
  full_name='mobile_franchisee_transfer.GetTransferProductByBranchIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_franchisee_transfer.GetTransferProductByBranchIDResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_franchisee_transfer.GetTransferProductByBranchIDResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_unchanged_rows', full_name='mobile_franchisee_transfer.GetTransferProductByBranchIDResponse.inventory_unchanged_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3800,
  serialized_end=4003,
)


_GETTRANSFERREGIONBYBRANCHIDREQUEST = _descriptor.Descriptor(
  name='GetTransferRegionByBranchIDRequest',
  full_name='mobile_franchisee_transfer.GetTransferRegionByBranchIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='mobile_franchisee_transfer.GetTransferRegionByBranchIDRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='mobile_franchisee_transfer.GetTransferRegionByBranchIDRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4005,
  serialized_end=4072,
)


_REGIONBRANCH = _descriptor.Descriptor(
  name='RegionBranch',
  full_name='mobile_franchisee_transfer.RegionBranch',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='address', full_name='mobile_franchisee_transfer.RegionBranch.address', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_franchisee_transfer.RegionBranch.id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='mobile_franchisee_transfer.RegionBranch.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_franchisee_transfer.RegionBranch.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4074,
  serialized_end=4145,
)


_GETTRANSFERREGIONBYBRANCHIDRESPONSE = _descriptor.Descriptor(
  name='GetTransferRegionByBranchIDResponse',
  full_name='mobile_franchisee_transfer.GetTransferRegionByBranchIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_franchisee_transfer.GetTransferRegionByBranchIDResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4147,
  serialized_end=4240,
)


_GETTRANSFERPRODUCTBYTRANSFERIDREQUEST = _descriptor.Descriptor(
  name='GetTransferProductByTransferIDRequest',
  full_name='mobile_franchisee_transfer.GetTransferProductByTransferIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='include_total', full_name='mobile_franchisee_transfer.GetTransferProductByTransferIDRequest.include_total', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_franchisee_transfer.GetTransferProductByTransferIDRequest.order', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_franchisee_transfer.GetTransferProductByTransferIDRequest.offset', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_franchisee_transfer.GetTransferProductByTransferIDRequest.limit', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_franchisee_transfer.GetTransferProductByTransferIDRequest.transfer_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4243,
  serialized_end=4372,
)


_TRANSFERPRODUCT = _descriptor.Descriptor(
  name='TransferProduct',
  full_name='mobile_franchisee_transfer.TransferProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_franchisee_transfer.TransferProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_franchisee_transfer.TransferProduct.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_franchisee_transfer.TransferProduct.transfer_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='mobile_franchisee_transfer.TransferProduct.unit_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='mobile_franchisee_transfer.TransferProduct.unit_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='mobile_franchisee_transfer.TransferProduct.unit_spec', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='mobile_franchisee_transfer.TransferProduct.accounting_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='mobile_franchisee_transfer.TransferProduct.accounting_unit_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='mobile_franchisee_transfer.TransferProduct.accounting_unit_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='mobile_franchisee_transfer.TransferProduct.accounting_unit_spec', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_received_quantity', full_name='mobile_franchisee_transfer.TransferProduct.accounting_received_quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_number', full_name='mobile_franchisee_transfer.TransferProduct.item_number', index=11,
      number=14, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='mobile_franchisee_transfer.TransferProduct.product_code', index=12,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_franchisee_transfer.TransferProduct.product_name', index=13,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='mobile_franchisee_transfer.TransferProduct.quantity', index=14,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='mobile_franchisee_transfer.TransferProduct.receiving_store', index=15,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='mobile_franchisee_transfer.TransferProduct.shipping_store', index=16,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='mobile_franchisee_transfer.TransferProduct.created_by', index=17,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='mobile_franchisee_transfer.TransferProduct.updated_by', index=18,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='mobile_franchisee_transfer.TransferProduct.transfer_date', index=19,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='mobile_franchisee_transfer.TransferProduct.created_at', index=20,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='mobile_franchisee_transfer.TransferProduct.updated_at', index=21,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='mobile_franchisee_transfer.TransferProduct.extends', index=22,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='mobile_franchisee_transfer.TransferProduct.partner_id', index=23,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='mobile_franchisee_transfer.TransferProduct.user_id', index=24,
      number=28, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_received_quantity', full_name='mobile_franchisee_transfer.TransferProduct.confirmed_received_quantity', index=25,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='mobile_franchisee_transfer.TransferProduct.created_name', index=26,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='mobile_franchisee_transfer.TransferProduct.updated_name', index=27,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='mobile_franchisee_transfer.TransferProduct.category_id', index=28,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='mobile_franchisee_transfer.TransferProduct.category_name', index=29,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_name', full_name='mobile_franchisee_transfer.TransferProduct.model_name', index=30,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='mobile_franchisee_transfer.TransferProduct.tax_rate', index=31,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='mobile_franchisee_transfer.TransferProduct.tax_price', index=32,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='mobile_franchisee_transfer.TransferProduct.cost_price', index=33,
      number=37, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='mobile_franchisee_transfer.TransferProduct.amount', index=34,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='mobile_franchisee_transfer.TransferProduct.sales_price', index=35,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='mobile_franchisee_transfer.TransferProduct.sales_amount', index=36,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='mobile_franchisee_transfer.TransferProduct.unit_rate', index=37,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4375,
  serialized_end=5308,
)


_GETTRANSFERPRODUCTBYTRANSFERIDRESPONSE = _descriptor.Descriptor(
  name='GetTransferProductByTransferIDResponse',
  full_name='mobile_franchisee_transfer.GetTransferProductByTransferIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_franchisee_transfer.GetTransferProductByTransferIDResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_franchisee_transfer.GetTransferProductByTransferIDResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5310,
  serialized_end=5424,
)


_POSTTRANSFERPRODUCT = _descriptor.Descriptor(
  name='PostTransferProduct',
  full_name='mobile_franchisee_transfer.PostTransferProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_franchisee_transfer.PostTransferProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='mobile_franchisee_transfer.PostTransferProduct.unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='mobile_franchisee_transfer.PostTransferProduct.quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='mobile_franchisee_transfer.PostTransferProduct.tax_rate', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='mobile_franchisee_transfer.PostTransferProduct.tax_price', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='mobile_franchisee_transfer.PostTransferProduct.cost_price', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='mobile_franchisee_transfer.PostTransferProduct.amount', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='mobile_franchisee_transfer.PostTransferProduct.sales_price', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='mobile_franchisee_transfer.PostTransferProduct.sales_amount', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5427,
  serialized_end=5619,
)


_CREATETRANSFERREQUEST = _descriptor.Descriptor(
  name='CreateTransferRequest',
  full_name='mobile_franchisee_transfer.CreateTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='mobile_franchisee_transfer.CreateTransferRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='mobile_franchisee_transfer.CreateTransferRequest.shipping_store', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='mobile_franchisee_transfer.CreateTransferRequest.receiving_store', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='mobile_franchisee_transfer.CreateTransferRequest.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_franchisee_transfer.CreateTransferRequest.remark', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_date', full_name='mobile_franchisee_transfer.CreateTransferRequest.shipping_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='mobile_franchisee_transfer.CreateTransferRequest.transfer_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_franchisee_transfer.CreateTransferRequest.branch_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='mobile_franchisee_transfer.CreateTransferRequest.type', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='mobile_franchisee_transfer.CreateTransferRequest.sub_type', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position', full_name='mobile_franchisee_transfer.CreateTransferRequest.receiving_position', index=10,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position', full_name='mobile_franchisee_transfer.CreateTransferRequest.shipping_position', index=11,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_name', full_name='mobile_franchisee_transfer.CreateTransferRequest.receiving_position_name', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_name', full_name='mobile_franchisee_transfer.CreateTransferRequest.shipping_position_name', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='mobile_franchisee_transfer.CreateTransferRequest.sub_account_type', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5622,
  serialized_end=6098,
)


_UPDATETRANSFERREQUEST = _descriptor.Descriptor(
  name='UpdateTransferRequest',
  full_name='mobile_franchisee_transfer.UpdateTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_franchisee_transfer.UpdateTransferRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='mobile_franchisee_transfer.UpdateTransferRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_franchisee_transfer.UpdateTransferRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='mobile_franchisee_transfer.UpdateTransferRequest.transfer_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_franchisee_transfer.UpdateTransferRequest.branch_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='mobile_franchisee_transfer.UpdateTransferRequest.type', index=5,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='mobile_franchisee_transfer.UpdateTransferRequest.sub_type', index=6,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6101,
  serialized_end=6332,
)


_UPDATETRANSFERRESPONSE = _descriptor.Descriptor(
  name='UpdateTransferResponse',
  full_name='mobile_franchisee_transfer.UpdateTransferResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='mobile_franchisee_transfer.UpdateTransferResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6334,
  serialized_end=6374,
)


_CONFIRMPOSTTRANSFERPRODUCT = _descriptor.Descriptor(
  name='ConfirmPostTransferProduct',
  full_name='mobile_franchisee_transfer.ConfirmPostTransferProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_franchisee_transfer.ConfirmPostTransferProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='mobile_franchisee_transfer.ConfirmPostTransferProduct.unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_received_quantity', full_name='mobile_franchisee_transfer.ConfirmPostTransferProduct.confirmed_received_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6376,
  serialized_end=6478,
)


_CONFIRMTRANSFERREQUEST = _descriptor.Descriptor(
  name='ConfirmTransferRequest',
  full_name='mobile_franchisee_transfer.ConfirmTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_franchisee_transfer.ConfirmTransferRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiver', full_name='mobile_franchisee_transfer.ConfirmTransferRequest.receiver', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='mobile_franchisee_transfer.ConfirmTransferRequest.receiving_store', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='mobile_franchisee_transfer.ConfirmTransferRequest.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_franchisee_transfer.ConfirmTransferRequest.branch_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6481,
  serialized_end=6664,
)


_DELETETRANSFERPRODUCTREQUEST = _descriptor.Descriptor(
  name='DeleteTransferProductRequest',
  full_name='mobile_franchisee_transfer.DeleteTransferProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ids', full_name='mobile_franchisee_transfer.DeleteTransferProductRequest.ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_franchisee_transfer.DeleteTransferProductRequest.transfer_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6666,
  serialized_end=6730,
)


_DELETETRANSFERPRODUCTRESPONSE = _descriptor.Descriptor(
  name='DeleteTransferProductResponse',
  full_name='mobile_franchisee_transfer.DeleteTransferProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='mobile_franchisee_transfer.DeleteTransferProductResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6732,
  serialized_end=6779,
)


_DELETETRANSFERREQUEST = _descriptor.Descriptor(
  name='DeleteTransferRequest',
  full_name='mobile_franchisee_transfer.DeleteTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_franchisee_transfer.DeleteTransferRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6781,
  serialized_end=6825,
)


_DELETETRANSFERRESPONSE = _descriptor.Descriptor(
  name='DeleteTransferResponse',
  full_name='mobile_franchisee_transfer.DeleteTransferResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='mobile_franchisee_transfer.DeleteTransferResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6827,
  serialized_end=6867,
)


_SUBMITTRANSFERREQUEST = _descriptor.Descriptor(
  name='SubmitTransferRequest',
  full_name='mobile_franchisee_transfer.SubmitTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_franchisee_transfer.SubmitTransferRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiver', full_name='mobile_franchisee_transfer.SubmitTransferRequest.receiver', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='mobile_franchisee_transfer.SubmitTransferRequest.receiving_store', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='mobile_franchisee_transfer.SubmitTransferRequest.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_franchisee_transfer.SubmitTransferRequest.branch_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6870,
  serialized_end=7045,
)


_SUBMITTRANSFERRECEIVINGREQUEST = _descriptor.Descriptor(
  name='SubmitTransferReceivingRequest',
  full_name='mobile_franchisee_transfer.SubmitTransferReceivingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_franchisee_transfer.SubmitTransferReceivingRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiver', full_name='mobile_franchisee_transfer.SubmitTransferReceivingRequest.receiver', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='mobile_franchisee_transfer.SubmitTransferReceivingRequest.receiving_store', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='mobile_franchisee_transfer.SubmitTransferReceivingRequest.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7048,
  serialized_end=7211,
)


_CANCELTRANSFERREQUEST = _descriptor.Descriptor(
  name='CancelTransferRequest',
  full_name='mobile_franchisee_transfer.CancelTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_franchisee_transfer.CancelTransferRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7213,
  serialized_end=7257,
)


_GETTRANSFERCOLLECTREQUEST = _descriptor.Descriptor(
  name='GetTransferCollectRequest',
  full_name='mobile_franchisee_transfer.GetTransferCollectRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='st_ids', full_name='mobile_franchisee_transfer.GetTransferCollectRequest.st_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='mobile_franchisee_transfer.GetTransferCollectRequest.store_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='mobile_franchisee_transfer.GetTransferCollectRequest.category_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_franchisee_transfer.GetTransferCollectRequest.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='mobile_franchisee_transfer.GetTransferCollectRequest.start_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='mobile_franchisee_transfer.GetTransferCollectRequest.end_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_franchisee_transfer.GetTransferCollectRequest.limit', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_franchisee_transfer.GetTransferCollectRequest.offset', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='mobile_franchisee_transfer.GetTransferCollectRequest.include_total', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_in', full_name='mobile_franchisee_transfer.GetTransferCollectRequest.is_in', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_franchisee_transfer.GetTransferCollectRequest.order', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='mobile_franchisee_transfer.GetTransferCollectRequest.sort', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_franchisee_transfer.GetTransferCollectRequest.code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_code', full_name='mobile_franchisee_transfer.GetTransferCollectRequest.jde_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='mobile_franchisee_transfer.GetTransferCollectRequest.is_wms_store', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_franchisee_transfer.GetTransferCollectRequest.branch_type', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='mobile_franchisee_transfer.GetTransferCollectRequest.lan', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='mobile_franchisee_transfer.GetTransferCollectRequest.type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='mobile_franchisee_transfer.GetTransferCollectRequest.sub_type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7260,
  serialized_end=7678,
)


_TRANSFERCOLLECT = _descriptor.Descriptor(
  name='TransferCollect',
  full_name='mobile_franchisee_transfer.TransferCollect',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='mobile_franchisee_transfer.TransferCollect.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='mobile_franchisee_transfer.TransferCollect.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='mobile_franchisee_transfer.TransferCollect.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='mobile_franchisee_transfer.TransferCollect.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='mobile_franchisee_transfer.TransferCollect.category_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='mobile_franchisee_transfer.TransferCollect.category_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_parent', full_name='mobile_franchisee_transfer.TransferCollect.category_parent', index=6,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='mobile_franchisee_transfer.TransferCollect.product_code', index=7,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_franchisee_transfer.TransferCollect.product_id', index=8,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_franchisee_transfer.TransferCollect.product_name', index=9,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_price', full_name='mobile_franchisee_transfer.TransferCollect.product_price', index=10,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='mobile_franchisee_transfer.TransferCollect.receiving_store', index=11,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_code', full_name='mobile_franchisee_transfer.TransferCollect.receiving_store_code', index=12,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_us_id', full_name='mobile_franchisee_transfer.TransferCollect.receiving_store_us_id', index=13,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_name', full_name='mobile_franchisee_transfer.TransferCollect.receiving_store_name', index=14,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='mobile_franchisee_transfer.TransferCollect.quantity', index=15,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='mobile_franchisee_transfer.TransferCollect.shipping_store', index=16,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_code', full_name='mobile_franchisee_transfer.TransferCollect.shipping_store_code', index=17,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_name', full_name='mobile_franchisee_transfer.TransferCollect.shipping_store_name', index=18,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_us_id', full_name='mobile_franchisee_transfer.TransferCollect.shipping_store_us_id', index=19,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='mobile_franchisee_transfer.TransferCollect.unit_id', index=20,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='mobile_franchisee_transfer.TransferCollect.unit_name', index=21,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='mobile_franchisee_transfer.TransferCollect.product_spec', index=22,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='mobile_franchisee_transfer.TransferCollect.price', index=23,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position', full_name='mobile_franchisee_transfer.TransferCollect.receiving_position', index=24,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_code', full_name='mobile_franchisee_transfer.TransferCollect.receiving_position_code', index=25,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_name', full_name='mobile_franchisee_transfer.TransferCollect.receiving_position_name', index=26,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position', full_name='mobile_franchisee_transfer.TransferCollect.shipping_position', index=27,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_code', full_name='mobile_franchisee_transfer.TransferCollect.shipping_position_code', index=28,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_name', full_name='mobile_franchisee_transfer.TransferCollect.shipping_position_name', index=29,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7681,
  serialized_end=8493,
)


_TRANSFERCOLLECTTOTAL = _descriptor.Descriptor(
  name='TransferCollectTotal',
  full_name='mobile_franchisee_transfer.TransferCollectTotal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='count', full_name='mobile_franchisee_transfer.TransferCollectTotal.count', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_accounting_quantity', full_name='mobile_franchisee_transfer.TransferCollectTotal.sum_accounting_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_quantity', full_name='mobile_franchisee_transfer.TransferCollectTotal.sum_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8495,
  serialized_end=8587,
)


_GETTRANSFERCOLLECTRESPONSE = _descriptor.Descriptor(
  name='GetTransferCollectResponse',
  full_name='mobile_franchisee_transfer.GetTransferCollectResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_franchisee_transfer.GetTransferCollectResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_franchisee_transfer.GetTransferCollectResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8590,
  serialized_end=8742,
)


_GETTRANSFERCOLLECTDETAILEDREQUEST = _descriptor.Descriptor(
  name='GetTransferCollectDetailedRequest',
  full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='st_ids', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest.st_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest.store_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest.category_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest.start_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest.end_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest.limit', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest.offset', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest.include_total', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_in', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest.is_in', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest.order', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest.sort', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest.code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_code', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest.jde_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest.is_wms_store', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest.branch_type', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest.lan', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest.type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedRequest.sub_type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8745,
  serialized_end=9171,
)


_TRANSFERCOLLECTDETAILED = _descriptor.Descriptor(
  name='TransferCollectDetailed',
  full_name='mobile_franchisee_transfer.TransferCollectDetailed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='mobile_franchisee_transfer.TransferCollectDetailed.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='mobile_franchisee_transfer.TransferCollectDetailed.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='mobile_franchisee_transfer.TransferCollectDetailed.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='mobile_franchisee_transfer.TransferCollectDetailed.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='mobile_franchisee_transfer.TransferCollectDetailed.category_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='mobile_franchisee_transfer.TransferCollectDetailed.category_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_franchisee_transfer.TransferCollectDetailed.id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='mobile_franchisee_transfer.TransferCollectDetailed.product_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_franchisee_transfer.TransferCollectDetailed.product_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_franchisee_transfer.TransferCollectDetailed.product_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='mobile_franchisee_transfer.TransferCollectDetailed.product_spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='mobile_franchisee_transfer.TransferCollectDetailed.quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='mobile_franchisee_transfer.TransferCollectDetailed.receiving_store', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_code', full_name='mobile_franchisee_transfer.TransferCollectDetailed.receiving_store_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_name', full_name='mobile_franchisee_transfer.TransferCollectDetailed.receiving_store_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='mobile_franchisee_transfer.TransferCollectDetailed.shipping_store', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_code', full_name='mobile_franchisee_transfer.TransferCollectDetailed.shipping_store_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_name', full_name='mobile_franchisee_transfer.TransferCollectDetailed.shipping_store_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_franchisee_transfer.TransferCollectDetailed.status', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_code', full_name='mobile_franchisee_transfer.TransferCollectDetailed.transfer_code', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='mobile_franchisee_transfer.TransferCollectDetailed.transfer_date', index=20,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_franchisee_transfer.TransferCollectDetailed.transfer_id', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='mobile_franchisee_transfer.TransferCollectDetailed.unit_id', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='mobile_franchisee_transfer.TransferCollectDetailed.unit_name', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_us_id', full_name='mobile_franchisee_transfer.TransferCollectDetailed.shipping_store_us_id', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_us_id', full_name='mobile_franchisee_transfer.TransferCollectDetailed.receiving_store_us_id', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='mobile_franchisee_transfer.TransferCollectDetailed.price', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_code', full_name='mobile_franchisee_transfer.TransferCollectDetailed.jde_code', index=27,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_franchisee_transfer.TransferCollectDetailed.code', index=28,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position', full_name='mobile_franchisee_transfer.TransferCollectDetailed.receiving_position', index=29,
      number=40, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_code', full_name='mobile_franchisee_transfer.TransferCollectDetailed.receiving_position_code', index=30,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_name', full_name='mobile_franchisee_transfer.TransferCollectDetailed.receiving_position_name', index=31,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position', full_name='mobile_franchisee_transfer.TransferCollectDetailed.shipping_position', index=32,
      number=43, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_code', full_name='mobile_franchisee_transfer.TransferCollectDetailed.shipping_position_code', index=33,
      number=44, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_name', full_name='mobile_franchisee_transfer.TransferCollectDetailed.shipping_position_name', index=34,
      number=45, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9174,
  serialized_end=10076,
)


_GETTRANSFERCOLLECTDETAILEDRESPONSE = _descriptor.Descriptor(
  name='GetTransferCollectDetailedResponse',
  full_name='mobile_franchisee_transfer.GetTransferCollectDetailedResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_franchisee_transfer.GetTransferCollectDetailedResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10079,
  serialized_end=10247,
)


_GETTRANSFERLOGREQUEST = _descriptor.Descriptor(
  name='GetTransferLogRequest',
  full_name='mobile_franchisee_transfer.GetTransferLogRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_franchisee_transfer.GetTransferLogRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10249,
  serialized_end=10293,
)


_GETTRANSFERLOGRESPONSE = _descriptor.Descriptor(
  name='GetTransferLogResponse',
  full_name='mobile_franchisee_transfer.GetTransferLogResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_franchisee_transfer.GetTransferLogResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_franchisee_transfer.GetTransferLogResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10295,
  serialized_end=10381,
)


_LOG = _descriptor.Descriptor(
  name='Log',
  full_name='mobile_franchisee_transfer.Log',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_franchisee_transfer.Log.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_franchisee_transfer.Log.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='mobile_franchisee_transfer.Log.created_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='mobile_franchisee_transfer.Log.created_at', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='mobile_franchisee_transfer.Log.reason', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='mobile_franchisee_transfer.Log.created_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10384,
  serialized_end=10523,
)

_GETTRANSFERREQUEST.fields_by_name['status'].enum_type = _GETTRANSFERREQUEST_STATUS
_GETTRANSFERREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERREQUEST_STATUS.containing_type = _GETTRANSFERREQUEST
_TRANSFER.fields_by_name['status'].enum_type = _TRANSFER_STATUS
_TRANSFER.fields_by_name['process_status'].enum_type = _P_STATUS
_TRANSFER.fields_by_name['transfer_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFER.fields_by_name['receiving_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFER.fields_by_name['shipping_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFER.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFER.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFER_STATUS.containing_type = _TRANSFER
_GETTRANSFERRESPONSE.fields_by_name['rows'].message_type = _TRANSFER
_GETTRANSFERBYIDRESPONSE.fields_by_name['status'].enum_type = _GETTRANSFERBYIDRESPONSE_STATUS
_GETTRANSFERBYIDRESPONSE.fields_by_name['process_status'].enum_type = _P_STATUS
_GETTRANSFERBYIDRESPONSE.fields_by_name['transfer_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERBYIDRESPONSE.fields_by_name['receiving_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERBYIDRESPONSE.fields_by_name['shipping_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERBYIDRESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERBYIDRESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERBYIDRESPONSE_STATUS.containing_type = _GETTRANSFERBYIDRESPONSE
_GETTRANSFERPRODUCTCATEGORYBYBRANCHIDRESPONSE.fields_by_name['category_items'].message_type = _CATEGORYITEM
_SELECTTRANSFERPRODUCT.fields_by_name['unit'].message_type = _TRANSFERPRODUCTUNIT
_GETTRANSFERPRODUCTBYBRANCHIDRESPONSE.fields_by_name['rows'].message_type = _SELECTTRANSFERPRODUCT
_GETTRANSFERPRODUCTBYBRANCHIDRESPONSE.fields_by_name['inventory_unchanged_rows'].message_type = _SELECTTRANSFERPRODUCT
_GETTRANSFERREGIONBYBRANCHIDRESPONSE.fields_by_name['rows'].message_type = _REGIONBRANCH
_TRANSFERPRODUCT.fields_by_name['transfer_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFERPRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFERPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERPRODUCTBYTRANSFERIDRESPONSE.fields_by_name['rows'].message_type = _TRANSFERPRODUCT
_CREATETRANSFERREQUEST.fields_by_name['products'].message_type = _POSTTRANSFERPRODUCT
_CREATETRANSFERREQUEST.fields_by_name['shipping_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATETRANSFERREQUEST.fields_by_name['transfer_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATETRANSFERREQUEST.fields_by_name['products'].message_type = _POSTTRANSFERPRODUCT
_UPDATETRANSFERREQUEST.fields_by_name['transfer_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CONFIRMTRANSFERREQUEST.fields_by_name['products'].message_type = _CONFIRMPOSTTRANSFERPRODUCT
_SUBMITTRANSFERREQUEST.fields_by_name['products'].message_type = _POSTTRANSFERPRODUCT
_SUBMITTRANSFERRECEIVINGREQUEST.fields_by_name['products'].message_type = _POSTTRANSFERPRODUCT
_GETTRANSFERCOLLECTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERCOLLECTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFERCOLLECT.fields_by_name['category_parent'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_GETTRANSFERCOLLECTRESPONSE.fields_by_name['rows'].message_type = _TRANSFERCOLLECT
_GETTRANSFERCOLLECTRESPONSE.fields_by_name['total'].message_type = _TRANSFERCOLLECTTOTAL
_GETTRANSFERCOLLECTDETAILEDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERCOLLECTDETAILEDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFERCOLLECTDETAILED.fields_by_name['transfer_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERCOLLECTDETAILEDRESPONSE.fields_by_name['rows'].message_type = _TRANSFERCOLLECTDETAILED
_GETTRANSFERCOLLECTDETAILEDRESPONSE.fields_by_name['total'].message_type = _TRANSFERCOLLECTTOTAL
_GETTRANSFERLOGRESPONSE.fields_by_name['rows'].message_type = _LOG
_LOG.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['Pong'] = _PONG
DESCRIPTOR.message_types_by_name['GetTransferRequest'] = _GETTRANSFERREQUEST
DESCRIPTOR.message_types_by_name['Transfer'] = _TRANSFER
DESCRIPTOR.message_types_by_name['GetTransferResponse'] = _GETTRANSFERRESPONSE
DESCRIPTOR.message_types_by_name['GetTransferByIDRequest'] = _GETTRANSFERBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetTransferByIDResponse'] = _GETTRANSFERBYIDRESPONSE
DESCRIPTOR.message_types_by_name['GetTransferProductByBranchIDRequest'] = _GETTRANSFERPRODUCTBYBRANCHIDREQUEST
DESCRIPTOR.message_types_by_name['CategoryItem'] = _CATEGORYITEM
DESCRIPTOR.message_types_by_name['GetTransferProductCategoryByBranchIDRequest'] = _GETTRANSFERPRODUCTCATEGORYBYBRANCHIDREQUEST
DESCRIPTOR.message_types_by_name['GetTransferProductCategoryByBranchIDResponse'] = _GETTRANSFERPRODUCTCATEGORYBYBRANCHIDRESPONSE
DESCRIPTOR.message_types_by_name['TransferProductUnit'] = _TRANSFERPRODUCTUNIT
DESCRIPTOR.message_types_by_name['SelectTransferProduct'] = _SELECTTRANSFERPRODUCT
DESCRIPTOR.message_types_by_name['GetTransferProductByBranchIDResponse'] = _GETTRANSFERPRODUCTBYBRANCHIDRESPONSE
DESCRIPTOR.message_types_by_name['GetTransferRegionByBranchIDRequest'] = _GETTRANSFERREGIONBYBRANCHIDREQUEST
DESCRIPTOR.message_types_by_name['RegionBranch'] = _REGIONBRANCH
DESCRIPTOR.message_types_by_name['GetTransferRegionByBranchIDResponse'] = _GETTRANSFERREGIONBYBRANCHIDRESPONSE
DESCRIPTOR.message_types_by_name['GetTransferProductByTransferIDRequest'] = _GETTRANSFERPRODUCTBYTRANSFERIDREQUEST
DESCRIPTOR.message_types_by_name['TransferProduct'] = _TRANSFERPRODUCT
DESCRIPTOR.message_types_by_name['GetTransferProductByTransferIDResponse'] = _GETTRANSFERPRODUCTBYTRANSFERIDRESPONSE
DESCRIPTOR.message_types_by_name['PostTransferProduct'] = _POSTTRANSFERPRODUCT
DESCRIPTOR.message_types_by_name['CreateTransferRequest'] = _CREATETRANSFERREQUEST
DESCRIPTOR.message_types_by_name['UpdateTransferRequest'] = _UPDATETRANSFERREQUEST
DESCRIPTOR.message_types_by_name['UpdateTransferResponse'] = _UPDATETRANSFERRESPONSE
DESCRIPTOR.message_types_by_name['ConfirmPostTransferProduct'] = _CONFIRMPOSTTRANSFERPRODUCT
DESCRIPTOR.message_types_by_name['ConfirmTransferRequest'] = _CONFIRMTRANSFERREQUEST
DESCRIPTOR.message_types_by_name['DeleteTransferProductRequest'] = _DELETETRANSFERPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['DeleteTransferProductResponse'] = _DELETETRANSFERPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['DeleteTransferRequest'] = _DELETETRANSFERREQUEST
DESCRIPTOR.message_types_by_name['DeleteTransferResponse'] = _DELETETRANSFERRESPONSE
DESCRIPTOR.message_types_by_name['SubmitTransferRequest'] = _SUBMITTRANSFERREQUEST
DESCRIPTOR.message_types_by_name['SubmitTransferReceivingRequest'] = _SUBMITTRANSFERRECEIVINGREQUEST
DESCRIPTOR.message_types_by_name['CancelTransferRequest'] = _CANCELTRANSFERREQUEST
DESCRIPTOR.message_types_by_name['GetTransferCollectRequest'] = _GETTRANSFERCOLLECTREQUEST
DESCRIPTOR.message_types_by_name['TransferCollect'] = _TRANSFERCOLLECT
DESCRIPTOR.message_types_by_name['TransferCollectTotal'] = _TRANSFERCOLLECTTOTAL
DESCRIPTOR.message_types_by_name['GetTransferCollectResponse'] = _GETTRANSFERCOLLECTRESPONSE
DESCRIPTOR.message_types_by_name['GetTransferCollectDetailedRequest'] = _GETTRANSFERCOLLECTDETAILEDREQUEST
DESCRIPTOR.message_types_by_name['TransferCollectDetailed'] = _TRANSFERCOLLECTDETAILED
DESCRIPTOR.message_types_by_name['GetTransferCollectDetailedResponse'] = _GETTRANSFERCOLLECTDETAILEDRESPONSE
DESCRIPTOR.message_types_by_name['GetTransferLogRequest'] = _GETTRANSFERLOGREQUEST
DESCRIPTOR.message_types_by_name['GetTransferLogResponse'] = _GETTRANSFERLOGRESPONSE
DESCRIPTOR.message_types_by_name['Log'] = _LOG
DESCRIPTOR.enum_types_by_name['P_STATUS'] = _P_STATUS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Pong = _reflection.GeneratedProtocolMessageType('Pong', (_message.Message,), dict(
  DESCRIPTOR = _PONG,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.Pong)
  ))
_sym_db.RegisterMessage(Pong)

GetTransferRequest = _reflection.GeneratedProtocolMessageType('GetTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERREQUEST,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.GetTransferRequest)
  ))
_sym_db.RegisterMessage(GetTransferRequest)

Transfer = _reflection.GeneratedProtocolMessageType('Transfer', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFER,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.Transfer)
  ))
_sym_db.RegisterMessage(Transfer)

GetTransferResponse = _reflection.GeneratedProtocolMessageType('GetTransferResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERRESPONSE,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.GetTransferResponse)
  ))
_sym_db.RegisterMessage(GetTransferResponse)

GetTransferByIDRequest = _reflection.GeneratedProtocolMessageType('GetTransferByIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERBYIDREQUEST,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.GetTransferByIDRequest)
  ))
_sym_db.RegisterMessage(GetTransferByIDRequest)

GetTransferByIDResponse = _reflection.GeneratedProtocolMessageType('GetTransferByIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERBYIDRESPONSE,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.GetTransferByIDResponse)
  ))
_sym_db.RegisterMessage(GetTransferByIDResponse)

GetTransferProductByBranchIDRequest = _reflection.GeneratedProtocolMessageType('GetTransferProductByBranchIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERPRODUCTBYBRANCHIDREQUEST,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.GetTransferProductByBranchIDRequest)
  ))
_sym_db.RegisterMessage(GetTransferProductByBranchIDRequest)

CategoryItem = _reflection.GeneratedProtocolMessageType('CategoryItem', (_message.Message,), dict(
  DESCRIPTOR = _CATEGORYITEM,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.CategoryItem)
  ))
_sym_db.RegisterMessage(CategoryItem)

GetTransferProductCategoryByBranchIDRequest = _reflection.GeneratedProtocolMessageType('GetTransferProductCategoryByBranchIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERPRODUCTCATEGORYBYBRANCHIDREQUEST,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.GetTransferProductCategoryByBranchIDRequest)
  ))
_sym_db.RegisterMessage(GetTransferProductCategoryByBranchIDRequest)

GetTransferProductCategoryByBranchIDResponse = _reflection.GeneratedProtocolMessageType('GetTransferProductCategoryByBranchIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERPRODUCTCATEGORYBYBRANCHIDRESPONSE,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.GetTransferProductCategoryByBranchIDResponse)
  ))
_sym_db.RegisterMessage(GetTransferProductCategoryByBranchIDResponse)

TransferProductUnit = _reflection.GeneratedProtocolMessageType('TransferProductUnit', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFERPRODUCTUNIT,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.TransferProductUnit)
  ))
_sym_db.RegisterMessage(TransferProductUnit)

SelectTransferProduct = _reflection.GeneratedProtocolMessageType('SelectTransferProduct', (_message.Message,), dict(
  DESCRIPTOR = _SELECTTRANSFERPRODUCT,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.SelectTransferProduct)
  ))
_sym_db.RegisterMessage(SelectTransferProduct)

GetTransferProductByBranchIDResponse = _reflection.GeneratedProtocolMessageType('GetTransferProductByBranchIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERPRODUCTBYBRANCHIDRESPONSE,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.GetTransferProductByBranchIDResponse)
  ))
_sym_db.RegisterMessage(GetTransferProductByBranchIDResponse)

GetTransferRegionByBranchIDRequest = _reflection.GeneratedProtocolMessageType('GetTransferRegionByBranchIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERREGIONBYBRANCHIDREQUEST,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.GetTransferRegionByBranchIDRequest)
  ))
_sym_db.RegisterMessage(GetTransferRegionByBranchIDRequest)

RegionBranch = _reflection.GeneratedProtocolMessageType('RegionBranch', (_message.Message,), dict(
  DESCRIPTOR = _REGIONBRANCH,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.RegionBranch)
  ))
_sym_db.RegisterMessage(RegionBranch)

GetTransferRegionByBranchIDResponse = _reflection.GeneratedProtocolMessageType('GetTransferRegionByBranchIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERREGIONBYBRANCHIDRESPONSE,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.GetTransferRegionByBranchIDResponse)
  ))
_sym_db.RegisterMessage(GetTransferRegionByBranchIDResponse)

GetTransferProductByTransferIDRequest = _reflection.GeneratedProtocolMessageType('GetTransferProductByTransferIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERPRODUCTBYTRANSFERIDREQUEST,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.GetTransferProductByTransferIDRequest)
  ))
_sym_db.RegisterMessage(GetTransferProductByTransferIDRequest)

TransferProduct = _reflection.GeneratedProtocolMessageType('TransferProduct', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFERPRODUCT,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.TransferProduct)
  ))
_sym_db.RegisterMessage(TransferProduct)

GetTransferProductByTransferIDResponse = _reflection.GeneratedProtocolMessageType('GetTransferProductByTransferIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERPRODUCTBYTRANSFERIDRESPONSE,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.GetTransferProductByTransferIDResponse)
  ))
_sym_db.RegisterMessage(GetTransferProductByTransferIDResponse)

PostTransferProduct = _reflection.GeneratedProtocolMessageType('PostTransferProduct', (_message.Message,), dict(
  DESCRIPTOR = _POSTTRANSFERPRODUCT,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.PostTransferProduct)
  ))
_sym_db.RegisterMessage(PostTransferProduct)

CreateTransferRequest = _reflection.GeneratedProtocolMessageType('CreateTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATETRANSFERREQUEST,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.CreateTransferRequest)
  ))
_sym_db.RegisterMessage(CreateTransferRequest)

UpdateTransferRequest = _reflection.GeneratedProtocolMessageType('UpdateTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATETRANSFERREQUEST,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.UpdateTransferRequest)
  ))
_sym_db.RegisterMessage(UpdateTransferRequest)

UpdateTransferResponse = _reflection.GeneratedProtocolMessageType('UpdateTransferResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATETRANSFERRESPONSE,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.UpdateTransferResponse)
  ))
_sym_db.RegisterMessage(UpdateTransferResponse)

ConfirmPostTransferProduct = _reflection.GeneratedProtocolMessageType('ConfirmPostTransferProduct', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMPOSTTRANSFERPRODUCT,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.ConfirmPostTransferProduct)
  ))
_sym_db.RegisterMessage(ConfirmPostTransferProduct)

ConfirmTransferRequest = _reflection.GeneratedProtocolMessageType('ConfirmTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMTRANSFERREQUEST,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.ConfirmTransferRequest)
  ))
_sym_db.RegisterMessage(ConfirmTransferRequest)

DeleteTransferProductRequest = _reflection.GeneratedProtocolMessageType('DeleteTransferProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETETRANSFERPRODUCTREQUEST,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.DeleteTransferProductRequest)
  ))
_sym_db.RegisterMessage(DeleteTransferProductRequest)

DeleteTransferProductResponse = _reflection.GeneratedProtocolMessageType('DeleteTransferProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETETRANSFERPRODUCTRESPONSE,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.DeleteTransferProductResponse)
  ))
_sym_db.RegisterMessage(DeleteTransferProductResponse)

DeleteTransferRequest = _reflection.GeneratedProtocolMessageType('DeleteTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETETRANSFERREQUEST,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.DeleteTransferRequest)
  ))
_sym_db.RegisterMessage(DeleteTransferRequest)

DeleteTransferResponse = _reflection.GeneratedProtocolMessageType('DeleteTransferResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETETRANSFERRESPONSE,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.DeleteTransferResponse)
  ))
_sym_db.RegisterMessage(DeleteTransferResponse)

SubmitTransferRequest = _reflection.GeneratedProtocolMessageType('SubmitTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITTRANSFERREQUEST,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.SubmitTransferRequest)
  ))
_sym_db.RegisterMessage(SubmitTransferRequest)

SubmitTransferReceivingRequest = _reflection.GeneratedProtocolMessageType('SubmitTransferReceivingRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITTRANSFERRECEIVINGREQUEST,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.SubmitTransferReceivingRequest)
  ))
_sym_db.RegisterMessage(SubmitTransferReceivingRequest)

CancelTransferRequest = _reflection.GeneratedProtocolMessageType('CancelTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _CANCELTRANSFERREQUEST,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.CancelTransferRequest)
  ))
_sym_db.RegisterMessage(CancelTransferRequest)

GetTransferCollectRequest = _reflection.GeneratedProtocolMessageType('GetTransferCollectRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERCOLLECTREQUEST,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.GetTransferCollectRequest)
  ))
_sym_db.RegisterMessage(GetTransferCollectRequest)

TransferCollect = _reflection.GeneratedProtocolMessageType('TransferCollect', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFERCOLLECT,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.TransferCollect)
  ))
_sym_db.RegisterMessage(TransferCollect)

TransferCollectTotal = _reflection.GeneratedProtocolMessageType('TransferCollectTotal', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFERCOLLECTTOTAL,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.TransferCollectTotal)
  ))
_sym_db.RegisterMessage(TransferCollectTotal)

GetTransferCollectResponse = _reflection.GeneratedProtocolMessageType('GetTransferCollectResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERCOLLECTRESPONSE,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.GetTransferCollectResponse)
  ))
_sym_db.RegisterMessage(GetTransferCollectResponse)

GetTransferCollectDetailedRequest = _reflection.GeneratedProtocolMessageType('GetTransferCollectDetailedRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERCOLLECTDETAILEDREQUEST,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.GetTransferCollectDetailedRequest)
  ))
_sym_db.RegisterMessage(GetTransferCollectDetailedRequest)

TransferCollectDetailed = _reflection.GeneratedProtocolMessageType('TransferCollectDetailed', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFERCOLLECTDETAILED,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.TransferCollectDetailed)
  ))
_sym_db.RegisterMessage(TransferCollectDetailed)

GetTransferCollectDetailedResponse = _reflection.GeneratedProtocolMessageType('GetTransferCollectDetailedResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERCOLLECTDETAILEDRESPONSE,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.GetTransferCollectDetailedResponse)
  ))
_sym_db.RegisterMessage(GetTransferCollectDetailedResponse)

GetTransferLogRequest = _reflection.GeneratedProtocolMessageType('GetTransferLogRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERLOGREQUEST,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.GetTransferLogRequest)
  ))
_sym_db.RegisterMessage(GetTransferLogRequest)

GetTransferLogResponse = _reflection.GeneratedProtocolMessageType('GetTransferLogResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERLOGRESPONSE,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.GetTransferLogResponse)
  ))
_sym_db.RegisterMessage(GetTransferLogResponse)

Log = _reflection.GeneratedProtocolMessageType('Log', (_message.Message,), dict(
  DESCRIPTOR = _LOG,
  __module__ = 'mobile.mobile_franchisee_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_transfer.Log)
  ))
_sym_db.RegisterMessage(Log)


DESCRIPTOR._options = None

_MOBILEFRANCHISEETRANSFER = _descriptor.ServiceDescriptor(
  name='mobileFranchiseeTransfer',
  full_name='mobile_franchisee_transfer.mobileFranchiseeTransfer',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=10603,
  serialized_end=13943,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateTransfer',
    full_name='mobile_franchisee_transfer.mobileFranchiseeTransfer.CreateTransfer',
    index=0,
    containing_service=None,
    input_type=_CREATETRANSFERREQUEST,
    output_type=_TRANSFER,
    serialized_options=_b('\202\323\344\223\0023\"./api/v2/supply/mobile/franchisee/transfer/main:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateTransfer',
    full_name='mobile_franchisee_transfer.mobileFranchiseeTransfer.UpdateTransfer',
    index=1,
    containing_service=None,
    input_type=_UPDATETRANSFERREQUEST,
    output_type=_UPDATETRANSFERRESPONSE,
    serialized_options=_b('\202\323\344\223\002H\"C/api/v2/supply/mobile/franchisee/transfer/main/{transfer_id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransfer',
    full_name='mobile_franchisee_transfer.mobileFranchiseeTransfer.GetTransfer',
    index=2,
    containing_service=None,
    input_type=_GETTRANSFERREQUEST,
    output_type=_GETTRANSFERRESPONSE,
    serialized_options=_b('\202\323\344\223\002+\022)/api/v2/supply/mobile/franchisee/transfer'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferByID',
    full_name='mobile_franchisee_transfer.mobileFranchiseeTransfer.GetTransferByID',
    index=3,
    containing_service=None,
    input_type=_GETTRANSFERBYIDREQUEST,
    output_type=_GETTRANSFERBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\0227/api/v2/supply/mobile/franchisee/transfer/{transfer_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferProductByBranchID',
    full_name='mobile_franchisee_transfer.mobileFranchiseeTransfer.GetTransferProductByBranchID',
    index=4,
    containing_service=None,
    input_type=_GETTRANSFERPRODUCTBYBRANCHIDREQUEST,
    output_type=_GETTRANSFERPRODUCTBYBRANCHIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002D\022B/api/v2/supply/mobile/franchisee/transfer/store/{store_id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferProductCategoryByBranchID',
    full_name='mobile_franchisee_transfer.mobileFranchiseeTransfer.GetTransferProductCategoryByBranchID',
    index=5,
    containing_service=None,
    input_type=_GETTRANSFERPRODUCTCATEGORYBYBRANCHIDREQUEST,
    output_type=_GETTRANSFERPRODUCTCATEGORYBYBRANCHIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002E\022C/api/v2/supply/mobile/franchisee/transfer/store/{store_id}/category'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferRegionByBranchID',
    full_name='mobile_franchisee_transfer.mobileFranchiseeTransfer.GetTransferRegionByBranchID',
    index=6,
    containing_service=None,
    input_type=_GETTRANSFERREGIONBYBRANCHIDREQUEST,
    output_type=_GETTRANSFERREGIONBYBRANCHIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002C\022A/api/v2/supply/mobile/franchisee/transfer/store/{store_id}/region'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferProductByTransferID',
    full_name='mobile_franchisee_transfer.mobileFranchiseeTransfer.GetTransferProductByTransferID',
    index=7,
    containing_service=None,
    input_type=_GETTRANSFERPRODUCTBYTRANSFERIDREQUEST,
    output_type=_GETTRANSFERPRODUCTBYTRANSFERIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002A\022?/api/v2/supply/mobile/franchisee/transfer/{transfer_id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmTransfer',
    full_name='mobile_franchisee_transfer.mobileFranchiseeTransfer.ConfirmTransfer',
    index=8,
    containing_service=None,
    input_type=_CONFIRMTRANSFERREQUEST,
    output_type=_TRANSFER,
    serialized_options=_b('\202\323\344\223\002I\"D/api/v2/supply/mobile/franchisee/transfer/main/{transfer_id}/confirm:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteTransferProduct',
    full_name='mobile_franchisee_transfer.mobileFranchiseeTransfer.DeleteTransferProduct',
    index=9,
    containing_service=None,
    input_type=_DELETETRANSFERPRODUCTREQUEST,
    output_type=_DELETETRANSFERPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\002K\"F/api/v2/supply/mobile/franchisee/transfer/product/{transfer_id}/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteTransfer',
    full_name='mobile_franchisee_transfer.mobileFranchiseeTransfer.DeleteTransfer',
    index=10,
    containing_service=None,
    input_type=_DELETETRANSFERREQUEST,
    output_type=_DELETETRANSFERRESPONSE,
    serialized_options=_b('\202\323\344\223\002H\"C/api/v2/supply/mobile/franchisee/transfer/main/{transfer_id}/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='SubmitTransfer',
    full_name='mobile_franchisee_transfer.mobileFranchiseeTransfer.SubmitTransfer',
    index=11,
    containing_service=None,
    input_type=_SUBMITTRANSFERREQUEST,
    output_type=_TRANSFER,
    serialized_options=_b('\202\323\344\223\002H\"C/api/v2/supply/mobile/franchisee/transfer/main/{transfer_id}/submit:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CancelTransfer',
    full_name='mobile_franchisee_transfer.mobileFranchiseeTransfer.CancelTransfer',
    index=12,
    containing_service=None,
    input_type=_CANCELTRANSFERREQUEST,
    output_type=_TRANSFER,
    serialized_options=_b('\202\323\344\223\002H\"C/api/v2/supply/mobile/franchisee/transfer/main/{transfer_id}/cancel:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferCollect',
    full_name='mobile_franchisee_transfer.mobileFranchiseeTransfer.GetTransferCollect',
    index=13,
    containing_service=None,
    input_type=_GETTRANSFERCOLLECTREQUEST,
    output_type=_GETTRANSFERCOLLECTRESPONSE,
    serialized_options=_b('\202\323\344\223\002:\0228/api/v2/supply/mobile/franchisee/transfer/collect/report'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferCollectDetailed',
    full_name='mobile_franchisee_transfer.mobileFranchiseeTransfer.GetTransferCollectDetailed',
    index=14,
    containing_service=None,
    input_type=_GETTRANSFERCOLLECTDETAILEDREQUEST,
    output_type=_GETTRANSFERCOLLECTDETAILEDRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\0225/api/v2/supply/mobile/franchisee/transfer/bi/detailed'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferLog',
    full_name='mobile_franchisee_transfer.mobileFranchiseeTransfer.GetTransferLog',
    index=15,
    containing_service=None,
    input_type=_GETTRANSFERLOGREQUEST,
    output_type=_GETTRANSFERLOGRESPONSE,
    serialized_options=_b('\202\323\344\223\002=\022;/api/v2/supply/mobile/franchisee/transfer/{transfer_id}/log'),
  ),
])
_sym_db.RegisterServiceDescriptor(_MOBILEFRANCHISEETRANSFER)

DESCRIPTOR.services_by_name['mobileFranchiseeTransfer'] = _MOBILEFRANCHISEETRANSFER

# @@protoc_insertion_point(module_scope)
