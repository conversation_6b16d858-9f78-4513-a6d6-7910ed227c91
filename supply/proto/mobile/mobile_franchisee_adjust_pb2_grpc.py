# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from mobile import mobile_franchisee_adjust_pb2 as mobile_dot_mobile__franchisee__adjust__pb2


class mobileFranchiseeAdjustStub(object):
  """加盟商报废
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetAdjust = channel.unary_unary(
        '/mobile_franchisee_adjust.mobileFranchiseeAdjust/GetAdjust',
        request_serializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustResponse.FromString,
        )
    self.CreateAdjust = channel.unary_unary(
        '/mobile_franchisee_adjust.mobileFranchiseeAdjust/CreateAdjust',
        request_serializer=mobile_dot_mobile__franchisee__adjust__pb2.CreateAdjustRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.Adjust.FromString,
        )
    self.GetAdjustProduct = channel.unary_unary(
        '/mobile_franchisee_adjust.mobileFranchiseeAdjust/GetAdjustProduct',
        request_serializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustProductRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustProductResponse.FromString,
        )
    self.GetAdjustByID = channel.unary_unary(
        '/mobile_franchisee_adjust.mobileFranchiseeAdjust/GetAdjustByID',
        request_serializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustByIDRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.Adjust.FromString,
        )
    self.ConfirmAdjust = channel.unary_unary(
        '/mobile_franchisee_adjust.mobileFranchiseeAdjust/ConfirmAdjust',
        request_serializer=mobile_dot_mobile__franchisee__adjust__pb2.ConfirmAdjustRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.ConfirmAdjustResponse.FromString,
        )
    self.SubmitAdjust = channel.unary_unary(
        '/mobile_franchisee_adjust.mobileFranchiseeAdjust/SubmitAdjust',
        request_serializer=mobile_dot_mobile__franchisee__adjust__pb2.SubmitAdjustRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.SubmitAdjustResponse.FromString,
        )
    self.ApproveAdjust = channel.unary_unary(
        '/mobile_franchisee_adjust.mobileFranchiseeAdjust/ApproveAdjust',
        request_serializer=mobile_dot_mobile__franchisee__adjust__pb2.ApproveAdjustRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.ApproveAdjustResponse.FromString,
        )
    self.RejectAdjust = channel.unary_unary(
        '/mobile_franchisee_adjust.mobileFranchiseeAdjust/RejectAdjust',
        request_serializer=mobile_dot_mobile__franchisee__adjust__pb2.RejectAdjustRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.RejectAdjustResponse.FromString,
        )
    self.GetAdjustProductByStoreID = channel.unary_unary(
        '/mobile_franchisee_adjust.mobileFranchiseeAdjust/GetAdjustProductByStoreID',
        request_serializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustProductByStoreIDRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustProductByStoreIDResponse.FromString,
        )
    self.GetAdjustProductCategoryByStoreID = channel.unary_unary(
        '/mobile_franchisee_adjust.mobileFranchiseeAdjust/GetAdjustProductCategoryByStoreID',
        request_serializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustProductCategoryByStoreIDRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustProductCategoryByStoreIDResponse.FromString,
        )
    self.UpdateAdjust = channel.unary_unary(
        '/mobile_franchisee_adjust.mobileFranchiseeAdjust/UpdateAdjust',
        request_serializer=mobile_dot_mobile__franchisee__adjust__pb2.UpdateAdjustRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.Adjust.FromString,
        )
    self.DeleteAdjust = channel.unary_unary(
        '/mobile_franchisee_adjust.mobileFranchiseeAdjust/DeleteAdjust',
        request_serializer=mobile_dot_mobile__franchisee__adjust__pb2.DeleteAdjustRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.DeleteAdjustResponse.FromString,
        )
    self.DeleteAdjustProduct = channel.unary_unary(
        '/mobile_franchisee_adjust.mobileFranchiseeAdjust/DeleteAdjustProduct',
        request_serializer=mobile_dot_mobile__franchisee__adjust__pb2.DeleteAdjustProductRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.DeleteAdjustProductResponse.FromString,
        )
    self.CancelAdjust = channel.unary_unary(
        '/mobile_franchisee_adjust.mobileFranchiseeAdjust/CancelAdjust',
        request_serializer=mobile_dot_mobile__franchisee__adjust__pb2.CancelAdjustRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.CancelAdjustResponse.FromString,
        )
    self.CreatedAdjustByCode = channel.unary_unary(
        '/mobile_franchisee_adjust.mobileFranchiseeAdjust/CreatedAdjustByCode',
        request_serializer=mobile_dot_mobile__franchisee__adjust__pb2.CreatedAdjustByCodeRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.Adjust.FromString,
        )
    self.GetAdjustLog = channel.unary_unary(
        '/mobile_franchisee_adjust.mobileFranchiseeAdjust/GetAdjustLog',
        request_serializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustLogRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustLogResponse.FromString,
        )


class mobileFranchiseeAdjustServicer(object):
  """加盟商报废
  """

  def GetAdjust(self, request, context):
    """GetAdjust查询报废
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateAdjust(self, request, context):
    """CreatedAdjust手动创建一个报废单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetAdjustProduct(self, request, context):
    """GetAdjustProduct报废单商品查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetAdjustByID(self, request, context):
    """GetAdjustByID查询一个报废单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ConfirmAdjust(self, request, context):
    """ConfirmAdjust确认一个报废单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SubmitAdjust(self, request, context):
    """提交一个报废单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ApproveAdjust(self, request, context):
    """审核一个报废单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RejectAdjust(self, request, context):
    """驳回一个报废单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetAdjustProductByStoreID(self, request, context):
    """GetAdjustProductByStoreID查询门店可报废商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetAdjustProductCategoryByStoreID(self, request, context):
    """GetAdjustProductByStoreID查询门店可报废商品分类
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateAdjust(self, request, context):
    """UpdateAdjust更新一个报废单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteAdjust(self, request, context):
    """DeleteAdjust删除一个报废单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteAdjustProduct(self, request, context):
    """DeleteAdjustProduct删除报废单的商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CancelAdjust(self, request, context):
    """CancelAdjust 取消报废单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreatedAdjustByCode(self, request, context):
    """第三方根据code创建报废
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetAdjustLog(self, request, context):
    """报废历史记录
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_mobileFranchiseeAdjustServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.GetAdjust,
          request_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustResponse.SerializeToString,
      ),
      'CreateAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.CreateAdjust,
          request_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.CreateAdjustRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__adjust__pb2.Adjust.SerializeToString,
      ),
      'GetAdjustProduct': grpc.unary_unary_rpc_method_handler(
          servicer.GetAdjustProduct,
          request_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustProductRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustProductResponse.SerializeToString,
      ),
      'GetAdjustByID': grpc.unary_unary_rpc_method_handler(
          servicer.GetAdjustByID,
          request_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustByIDRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__adjust__pb2.Adjust.SerializeToString,
      ),
      'ConfirmAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.ConfirmAdjust,
          request_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.ConfirmAdjustRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__adjust__pb2.ConfirmAdjustResponse.SerializeToString,
      ),
      'SubmitAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.SubmitAdjust,
          request_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.SubmitAdjustRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__adjust__pb2.SubmitAdjustResponse.SerializeToString,
      ),
      'ApproveAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.ApproveAdjust,
          request_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.ApproveAdjustRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__adjust__pb2.ApproveAdjustResponse.SerializeToString,
      ),
      'RejectAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.RejectAdjust,
          request_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.RejectAdjustRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__adjust__pb2.RejectAdjustResponse.SerializeToString,
      ),
      'GetAdjustProductByStoreID': grpc.unary_unary_rpc_method_handler(
          servicer.GetAdjustProductByStoreID,
          request_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustProductByStoreIDRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustProductByStoreIDResponse.SerializeToString,
      ),
      'GetAdjustProductCategoryByStoreID': grpc.unary_unary_rpc_method_handler(
          servicer.GetAdjustProductCategoryByStoreID,
          request_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustProductCategoryByStoreIDRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustProductCategoryByStoreIDResponse.SerializeToString,
      ),
      'UpdateAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateAdjust,
          request_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.UpdateAdjustRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__adjust__pb2.Adjust.SerializeToString,
      ),
      'DeleteAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteAdjust,
          request_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.DeleteAdjustRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__adjust__pb2.DeleteAdjustResponse.SerializeToString,
      ),
      'DeleteAdjustProduct': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteAdjustProduct,
          request_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.DeleteAdjustProductRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__adjust__pb2.DeleteAdjustProductResponse.SerializeToString,
      ),
      'CancelAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.CancelAdjust,
          request_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.CancelAdjustRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__adjust__pb2.CancelAdjustResponse.SerializeToString,
      ),
      'CreatedAdjustByCode': grpc.unary_unary_rpc_method_handler(
          servicer.CreatedAdjustByCode,
          request_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.CreatedAdjustByCodeRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__adjust__pb2.Adjust.SerializeToString,
      ),
      'GetAdjustLog': grpc.unary_unary_rpc_method_handler(
          servicer.GetAdjustLog,
          request_deserializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustLogRequest.FromString,
          response_serializer=mobile_dot_mobile__franchisee__adjust__pb2.GetAdjustLogResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'mobile_franchisee_adjust.mobileFranchiseeAdjust', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
