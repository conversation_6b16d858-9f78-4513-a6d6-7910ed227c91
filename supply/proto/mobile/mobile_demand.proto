syntax = "proto3";
package mobile_demand;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

//门店订货 —— 移动端 
service MobileDemandService{
    // 查询订货单列表
    rpc ListDemand(ListDemandRequest) returns (ListDemandResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/mobile/demand"
        };
    }
    // 根据id查询单个订货单
    rpc GetDemandById(GetDemandByIdRequest) returns (Demand){
        option (google.api.http) = {
            get:"/api/v2/supply/mobile/demand/{id}"
        };
    }
    // 根据id查询单据历史记录
    rpc GetHistoryById(GetHistoryByIdRequest) returns (HistoryResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/mobile/demand/{id}/history"
        };
    }
    // 根据id查询单据内的商品列表
    rpc GetProductsById(GetProductsByIdRequest) returns (ProductsResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/mobile/demand/{id}/product"
        };
    }
    // 更新订货单商品
    rpc UpdateProducts(UpdateProductRequest) returns (UpdateProductsResponse){
        option (google.api.http) = {
            post:"/api/v2/supply/mobile/demand/{id}/product"
            body:"*"
        };
    }

     // 查询单据内的商品详情
     rpc GetProductsDetailById(GetProductsDetailByIdRequest) returns (ProductsResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/mobile/demand/product/{product_id}"
        };
    }

    // 修改订单状态统一入口
    rpc DealDemandById (DealDemandByIdRequest) returns (Response) {
        option (google.api.http) = {
        post: "/api/v2/supply/mobile/demand/deal/{id}/{action}"
        body: "*"
        };
    };
}


message ListDemandRequest{
    // 订货开始日期
    google.protobuf.Timestamp start_date = 1;
    // 订货结束日期
    google.protobuf.Timestamp end_date = 2;
    // 订货单状态
    repeated string status = 3;
    // 门店id
    repeated uint64 store_ids = 4;
    // 是否包含商品
    string has_product = 5;
    // 是否为调整单
    bool is_adjust = 6;
    // 订货计划名称（模糊搜索）
    string schedule_name = 7;
    // 订货单号（模糊搜索）
    repeated string codes = 8;
    // 排序sort（预留的排序字段）
    string sort = 9;
    // 排序order（预留的排序字段）
    string order = 10;
    // -1搜所有
    string limit = 11;
    // 
    string offset = 12;
    // 
    repeated uint64 ids = 13;
    repeated string types = 14;

}
message ListDemandResponse{
    repeated Demand rows = 1;
    uint64 total = 2;
}
message Demand{
    // 订货单id
    uint64 id = 1;
    // 订货单号
    string code = 2;
    // 单据状态
    string status = 3;
    // 订货日期
    google.protobuf.Timestamp demand_date = 6;
    // 是否为调整单
    bool is_adjust = 7;
    // 备注 
    string remark = 8;
    // 订货计划
    Schedule schedule = 9;
    // 要货单
    repeated Order orders = 10;
    // 订货单类型
    string type = 11;
    // 是否包含商品
    string has_product = 12;
}
message Schedule{
    // 订货计划名称
    string name = 1;
    // 订货计划循环类型
    string method = 2;
    // 订货计划按月循环规则
    string month_method = 3;
    // 订货计划按周循环规则
    string week_method = 4;
    // 订货计划按天循环规则
    string day_method = 5;
    // 订货计划按天循环间隔
    uint64 interval = 6;
    google.protobuf.Timestamp start_date = 7;
    google.protobuf.Timestamp end_date = 8;
    // 订货计划单据状态自动变更设置
    repeated ScheduleStatusPlan status_plan = 9;
}
message ScheduleStatusPlan{
    // 订货计划中，订货单状态自动变更时间
    string time = 1;
    // 订货单状态变更开始状态
    string start_status = 2;
    // 订货单状态变更结束状态
    string end_status = 3;
    // 订货单状态自动变更时间周期
    string time_around = 4;
    // 单据过滤条件
    string doc_filter = 5;
}
message Order{
    // 要货单id
    uint64 id = 1;
    // 要货单号
    string code = 2;
    // 要货单状态
    string status = 3;
    // 供应商名称
    string delivery_name = 4;
}
message GetDemandByIdRequest{
    // 订货单id
    uint64 id = 1;
}
message GetHistoryByIdRequest{
    // 订货单id
    uint64 id = 1;
}
message HistoryResponse{
    repeated History rows = 1;
    uint64 total = 2;
}
message History{
    // 历史记录id
    uint64 id = 1;
    // 订货单状态
    string status = 2;
    // 操作人名称或者系统自动操作
    uint64 updated_by = 3;
    // 操作时间
    google.protobuf.Timestamp updated_at = 4;
    string updated_by_name = 5;
    // 要货单
    repeated Order orders = 10;
}
message GetProductsByIdRequest{
    // 订货单id
    uint64 id = 1;
    string sort = 9;
    // 排序order（预留的排序字段）
    string order = 10;
    // -1搜所有
    string limit = 11;
    // 
    string offset = 12;
}
message ProductsResponse{
    repeated Product rows = 1;
    uint64 total = 2;
}
message Product{
    // 数据id
    uint64 id = 1;
    // 商品id
    uint64 product_id = 2;
    // 商品编号
    string product_code = 3;
    // 商品名称
    string product_name = 4;
    // 商品规格
    string spec = 5;
    // 商品分类id
    uint64 category_id = 6;
    // 商品分类名称
    string category_name = 7;
    // 最小订量
    double min_quantity = 8;
    // 递增订量
    double increment_quantity = 9;
    // 最大订量
    double max_quantity = 10;
    // 订货数量
    double quantity = 11;
    // 订货单位
    string unit_name = 12;
    // 是否可报废
    bool allow_adjust = 13;
    // 是否可盘点
    bool allow_stocktake = 14;
    // 是否可调拨
    bool allow_transfer = 15;
    // 是否可主配
    bool allow_main_order = 16;
    // 是否可自采
    bool allow_self_purchase = 17;
    // 千元用量周期
    string circle_per_thousand = 18;
    // 补货计算方式
    string replenish_method = 19;
    // 安全存量算法
    string safe_stock_method = 20;
    // 解冻期（小时）
    double unfreeze = 21;
    // 配送区域名称
    string distribution_region_name = 22;
    // 配送方名称
    string distribution_name = 23;
    // 预计到货天数
    double planned_arrival_days = 25;
    // 循环规则类型
    string circle_type = 26;
    // 按周或按月循环的订货日
    repeated string cycles = 27;
    // 按天循环订货开始日期
    google.protobuf.Timestamp start_date = 28;
    // 按天循环订货间隔天数
    double interval_days = 29;
    // 配送方式
    string distribution_type = 30;
    // 门店待收货数量
    double store_unfinish_qty = 31;
    // 门店实时库存
    double store_inv_qty = 32;
    uint64 partner_id = 33;
}
message UpdateProductRequest{
    // 订货单id
    uint64 id = 1;
    // 备注
    string remark = 2;
    // 商品
    repeated ProductQuantity products = 3;
}
message ProductQuantity{
    // 数据id
    uint64 id = 1;
    // 商品id
    uint64 product_id = 2;
    // 订货数量
    double quantity = 3;
}
message UpdateProductsResponse{
    // 订货单id
    uint64 demand_id = 1;
    // 商品
    repeated ProductQuantity products = 2;
}

message GetProductsDetailByIdRequest{
    // 商品主档id
    uint64 product_id = 1;

}

// 处理单据
message DealDemandByIdRequest {
    uint64 id = 1;
    // 单据业务状态(INITED,F_COMMIT(成品订货提交),COMMIT(提交), CAL_DONE(原料计算完成, 这步不能通过前端修改), CAL_FAILED(原料计算失败，这步也不能通过前端修改),REJECTED(驳回订货单),APPROVED(审核订货单通过),FREEZE(冻结订货单),CANCELLED
    string action = 2;
    string description = 3;
    // 附件
    string attachments = 4;
}

// 统一返回对象
message Response {
    string description = 1;
}