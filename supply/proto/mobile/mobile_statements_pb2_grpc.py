# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from mobile import mobile_statements_pb2 as mobile_dot_mobile__statements__pb2


class MobileStatementsServiceStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.ListReconciliation = channel.unary_unary(
        '/mobile.MobileStatementsService/ListReconciliation',
        request_serializer=mobile_dot_mobile__statements__pb2.ListReconciliationRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__statements__pb2.ListReconciliationResponse.FromString,
        )


class MobileStatementsServiceServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def ListReconciliation(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_MobileStatementsServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'ListReconciliation': grpc.unary_unary_rpc_method_handler(
          servicer.ListReconciliation,
          request_deserializer=mobile_dot_mobile__statements__pb2.ListReconciliationRequest.FromString,
          response_serializer=mobile_dot_mobile__statements__pb2.ListReconciliationResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'mobile.MobileStatementsService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
