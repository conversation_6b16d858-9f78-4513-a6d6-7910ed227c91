# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from mobile import mobile_inventory_bi_pb2 as mobile_dot_mobile__inventory__bi__pb2


class MobileInventoryBiServiceStub(object):
  """库存报表相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.RealtimeInventory = channel.unary_unary(
        '/mobile_inventory_bi.MobileInventoryBiService/RealtimeInventory',
        request_serializer=mobile_dot_mobile__inventory__bi__pb2.RealtimeInventoryRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__inventory__bi__pb2.RealtimeInventoryResponse.FromString,
        )
    self.FrsRealtimeInventory = channel.unary_unary(
        '/mobile_inventory_bi.MobileInventoryBiService/FrsRealtimeInventory',
        request_serializer=mobile_dot_mobile__inventory__bi__pb2.RealtimeInventoryRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__inventory__bi__pb2.RealtimeInventoryResponse.FromString,
        )
    self.FrsInventoryLog = channel.unary_unary(
        '/mobile_inventory_bi.MobileInventoryBiService/FrsInventoryLog',
        request_serializer=mobile_dot_mobile__inventory__bi__pb2.QueryInventoryLogRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__inventory__bi__pb2.QueryInventoryLogResponse.FromString,
        )
    self.FrsDailyInventory = channel.unary_unary(
        '/mobile_inventory_bi.MobileInventoryBiService/FrsDailyInventory',
        request_serializer=mobile_dot_mobile__inventory__bi__pb2.DailyInventoryRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__inventory__bi__pb2.DailyInventoryResponse.FromString,
        )


class MobileInventoryBiServiceServicer(object):
  """库存报表相关服务
  """

  def RealtimeInventory(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def FrsRealtimeInventory(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def FrsInventoryLog(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def FrsDailyInventory(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_MobileInventoryBiServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'RealtimeInventory': grpc.unary_unary_rpc_method_handler(
          servicer.RealtimeInventory,
          request_deserializer=mobile_dot_mobile__inventory__bi__pb2.RealtimeInventoryRequest.FromString,
          response_serializer=mobile_dot_mobile__inventory__bi__pb2.RealtimeInventoryResponse.SerializeToString,
      ),
      'FrsRealtimeInventory': grpc.unary_unary_rpc_method_handler(
          servicer.FrsRealtimeInventory,
          request_deserializer=mobile_dot_mobile__inventory__bi__pb2.RealtimeInventoryRequest.FromString,
          response_serializer=mobile_dot_mobile__inventory__bi__pb2.RealtimeInventoryResponse.SerializeToString,
      ),
      'FrsInventoryLog': grpc.unary_unary_rpc_method_handler(
          servicer.FrsInventoryLog,
          request_deserializer=mobile_dot_mobile__inventory__bi__pb2.QueryInventoryLogRequest.FromString,
          response_serializer=mobile_dot_mobile__inventory__bi__pb2.QueryInventoryLogResponse.SerializeToString,
      ),
      'FrsDailyInventory': grpc.unary_unary_rpc_method_handler(
          servicer.FrsDailyInventory,
          request_deserializer=mobile_dot_mobile__inventory__bi__pb2.DailyInventoryRequest.FromString,
          response_serializer=mobile_dot_mobile__inventory__bi__pb2.DailyInventoryResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'mobile_inventory_bi.MobileInventoryBiService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
