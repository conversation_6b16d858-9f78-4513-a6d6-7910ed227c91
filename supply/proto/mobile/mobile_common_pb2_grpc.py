# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from mobile import mobile_common_pb2 as mobile_dot_mobile__common__pb2


class MobileCommonServiceStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetUnfinishedDoc = channel.unary_unary(
        '/mobile_common.MobileCommonService/GetUnfinishedDoc',
        request_serializer=mobile_dot_mobile__common__pb2.GetUnfinishedDocRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__common__pb2.GetUnfinishedDocResponse.FromString,
        )


class MobileCommonServiceServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def GetUnfinishedDoc(self, request, context):
    """GetUnfinishedDoc 首页未完成单据待办任务
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_MobileCommonServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetUnfinishedDoc': grpc.unary_unary_rpc_method_handler(
          servicer.GetUnfinishedDoc,
          request_deserializer=mobile_dot_mobile__common__pb2.GetUnfinishedDocRequest.FromString,
          response_serializer=mobile_dot_mobile__common__pb2.GetUnfinishedDocResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'mobile_common.MobileCommonService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
