syntax = "proto3";

package mobile_franchisee_stocktake;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
option go_package = "./mobile_franchisee_stocktake";

//Stocktake 盘点服务
//Create_request里加请求ID，幂等检查create的API
service MobileFranchiseeStockTake {
    rpc CheckStocktakeByDocID (CheckStocktakeByDocIDRequest) returns (CheckStocktakeByDocIDResponse) {
        option (google.api.http) = { post: "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/confirm/check" };
    }
    //备注:盘点商品在INITED和REJECTED状态可以填写数量，CONFITRMED，不能。
    //ConfirmStocktakeByDocID 确认盘点单（status=CONFITRMED，核算完库存后'FINALIZED'）15
    rpc ConfirmStocktakeByDocID (ConfirmStocktakeByDocIDRequest) returns (ConfirmStocktakeByDocIDResponse) {
        option (google.api.http) = { post: "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/confirm" };
    }
    //APPROVEDStocktakeByDocID  财务确认盘点单（status=APPROVED最终状态，）16
    rpc ApproveStocktakeByDocID (ApproveStocktakeByDocIDRequest) returns (ApproveStocktakeByDocIDResponse) {
        option (google.api.http) = { post: "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/approve" body: "*"};
    }
    //RejectStocktakeProduct 财务驳回提交后的部分盘点单明细商品（status=REJECTED，部分商品status=REJECTED）17
    rpc RejectStocktakeProduct (RejectStocktakeProductRequest) returns (RejectStocktakeProductResponse) {
        option (google.api.http) = { post: "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/reject" body: "*"};
    }
    //CancelStocktakeByDocID  作废盘点单（status=CANCELED）18
    rpc CancelStocktakeByDocID (CancelStocktakeByDocIDRequest) returns (CancelStocktakeByDocIDResponse) {
        option (google.api.http) = { post: "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/cancel" };
    }
    //GetStocktakeByDocID 获取一个盘点单19
    rpc GetStocktakeByDocID (GetStocktakeByDocIDRequest) returns (Stocktake) {
        option (google.api.http) = { get: "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}" };
    }
    //GetStocktake 查询盘点单20
    rpc GetStocktake (GetStocktakeRequest) returns (GetStocktakeResponse) {
        option (google.api.http) = { get: "/api/v2/supply/mobile/franchisee/stocktake" };
    }
    // 根据盘点单ID查询盘点单移动端用
    rpc GetStocktakeByIds (GetStocktakeByIdsRequest) returns (GetStocktakeByIdsResponse) {
        option (google.api.http) = { get: "/api/v2/supply/mobile/franchisee/stocktake/by/ids" };
    }
    //GetStocktakeProduct 查询盘点单明细商品21
    rpc GetStocktakeProduct (GetStocktakeProductRequest) returns (GetStocktakeProductResponse) {
        option (google.api.http) = { get: "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/product" };
    }
    //PutStocktakeByDocID 提交盘点单明细商品数量，更新盘点单, 22
    rpc PutStocktakeByDocID (PutStocktakeByDocIDRequest) returns (PutStocktakeByDocIDResponse) {
        option (google.api.http) = { post: "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}" body: "*"};
    }
    //CheckedStocktakeByDocID 检查完成盘点单23
    rpc CheckedStocktakeByDocID (CheckedStocktakeByDocIDRequest) returns (CheckedStocktakeByDocIDResponse) {
        option (google.api.http) = { post: "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/init/check" body: "*"};
    }
    //GetStocktakeTags获取盘点标签24
    rpc GetStocktakeTags (GetStocktakeTagsRequest) returns (GetStocktakeTagsResponse) {
        option (google.api.http) = { get: "/api/v2/supply/mobile/franchisee/stocktake/product/tags"};
    }
    // 获取一个盘点标签
    rpc GetStocktakeTagsById (GetStocktakeTagsByIdRequest) returns (StocktakeTags) {
        option (google.api.http) = { get: "/api/v2/supply/mobile/franchisee/stocktake/tags/{tag_id}"};
    }
    //ActionStocktakeTags增加，删除，更新,获取盘点标签25
    rpc ActionStocktakeTags (ActionStocktakeTagsRequest) returns (ActionStocktakeTagsResponse) {
        option (google.api.http) = { post: "/api/v2/supply/mobile/franchisee/stocktake/product/tags/action" body: "*"};
    }
    //DeleteStocktakeProductTags删除盘点商品标签条目26
    rpc DeleteStocktakeProductTags (DeleteStocktakeProductTagsRequest) returns (DeleteStocktakeProductTagsResponse) {
        option (google.api.http) = { post: "/api/v2/supply/mobile/franchisee/stocktake/product/tags/clean" body: "*"};
    }
    //GetStocktakeBalance盘点单损益报表27
    rpc GetStocktakeBalance (GetStocktakeBalanceRequest) returns (GetStocktakeBalanceResponse) {
        option (google.api.http) = { get: "/api/v2/supply/mobile/franchisee/stocktake/bi/balance"};
    }
    //SubmitStocktakeByDocID 提交盘点单28
    rpc SubmitStocktakeByDocID (SubmitStocktakeByDocIDRequest) returns (SubmitStocktakeByDocIDResponse) {
        option (google.api.http) = { post: "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/submit" body: "*"};
    }
    //GetStocktakeBalanceProductGroup门店盘点单损益汇总报表29
    rpc GetStocktakeBalanceProductGroup (GetStocktakeBalanceProductGroupRequest) returns (GetStocktakeBalanceProductGroupResponse) {
        option (google.api.http) = { get: "/api/v2/supply/mobile/franchisee/stocktake/balance/{doc_id}/product/group" };
    }
    //StocktakeBiDetailed盘点单报表30
    rpc StocktakeBiDetailed (StocktakeBiDetailedRequest) returns (StocktakeBiDetailedResponse) {
        option (google.api.http) = { get: "/api/v2/supply/mobile/franchisee/stocktake/bi/detail" };
    }
    //StocktakeBalanceRegion区域盘点31
    rpc StocktakeBalanceRegion (StocktakeBalanceRegionRequest) returns (StocktakeBalanceRegionResponse) {
        option (google.api.http) = { get: "/api/v2/supply/mobile/franchisee/stocktake/balance/product/group" };
    }
    //AdvanceStocktakeDiff  提前查看盘点损益32
    rpc AdvanceStocktakeDiff (AdvanceStocktakeDiffRequest) returns (AdvanceStocktakeDiffResponse) {
        option (google.api.http) = { get: "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/advance" };
    }
    //StocktakeDiffReport  盘点差异表33
    rpc StocktakeDiffReport (StocktakeDiffReportRequest) returns (StocktakeDiffReportResponse) {
        option (google.api.http) = { get: "/api/v2/supply/mobile/franchisee/stocktake/diff/report" };
    }
    //RecreateStocktakeDoc  重盘功能，重新生成盘点单35
    rpc RecreateStocktakeDoc (RecreateStocktakeDocRequest) returns (RecreateStocktakeDocResponse) {
        option (google.api.http) = { post: "/api/v2/supply/mobile/franchisee/recreate_stocktake_doc" body: "*"};
    }
    // 盘点历史记录
    rpc GetStocktakeLog (GetStocktakeLogRequest) returns (GetStocktakeLogResponse){
        option (google.api.http) = { get: "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/log"};
    }
    // 获取一个新盘点单id
    rpc GetNewStocktakeId(GetNewStocktakeIdRequest) returns (GetNewStocktakeIdResponse){
        option (google.api.http) = { post: "/api/v2/supply/mobile/franchisee/stocktake/manual/newid" body: "*" };
    }
    // 手动创建盘点单
    rpc ManuallyCreateStocktake(ManuallyCreateStocktakeRequest) returns (ManuallyCreateStocktakeResponse) {
        option (google.api.http) = { post: "/api/v2/supply/mobile/franchisee/stocktake" body: "*" };
    }
    // GetStocktakeProductByStoreID 查询门店可盘点商品
    rpc GetStocktakeProductByStoreID (GetStocktakeProductByStoreIDRequest) returns (GetStocktakeProductByStoreIDResponse) {
        option (google.api.http) = {get:"/api/v2/supply/mobile/franchisee/store/query/stocktake/products"};
    }
    // GetStocktakeProductCategoryByStoreID 查询门店可盘点商品分类
    rpc GetStocktakeProductCategoryByStoreID (GetStocktakeProductCategoryByStoreIDRequest) returns (GetStocktakeProductCategoryByStoreIDResponse) {
        option (google.api.http) = {get:"/api/v2/supply/mobile/franchisee/store/query/stocktake/category"};
    }
}

message GetStocktakeProductByStoreIDRequest {
    uint64 store_id = 1;
    uint32 limit = 2;
    uint32 offset = 3;
    bool include_total = 4;
    string search = 5;
    string search_fields = 6;
    repeated uint64 category_ids = 7;
    string lan= 8;
    bool attach_price = 9;
    repeated string exc_codes = 10;
    repeated string in_codes = 11;
    repeated string exc_upcs = 12;
    repeated string in_upcs = 13;
    string order_by = 14;
    string sort = 15;
}

message GetStocktakeProductByStoreIDResponse {
    repeated StocktakeProductLine rows = 1;
    uint32 total = 2;
    repeated StocktakeProductLine inventory_unchanged_rows = 3;
}

message CategoryItem
{
    uint64 category_id = 1;
    string category_name = 2;
    uint64 product_count = 3;
}
message GetStocktakeProductCategoryByStoreIDRequest {
    //门店id
    uint64 store_id = 1;
}
message GetStocktakeProductCategoryByStoreIDResponse {
    //门店id
    repeated CategoryItem category_items = 1;
}

message StocktakeProductLine {
    uint64 product_id = 1;
    string loss_report_order = 2;
    string product_code = 3;
    string product_name = 4;
    string model_name = 5;
    string storage_type = 6;
    //单位分类
    uint64 category_id = 7;
    string category_name = 8;
    repeated StocktakeProductUint units = 9;
    repeated string barcode = 10;
        // 含税单价
    double tax_price = 11;
    // 零售价
    double sales_price = 12;
    // 实时库存数量
    double real_inventory_qty = 13;
}

message StocktakeProductUint {
    // 数据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    string name = 4;
    string code = 5;
    string updated = 6;
    double rate = 7;
    bool default = 8;
    bool order = 9;
    bool purchase = 10;
    bool sales = 11;
    bool stocktake = 12;
    bool bom = 13;
    bool default_stocktake = 14;
    bool transfer = 15;
    double tax_rate = 16;
    double tax_price = 17;
    double cost_price = 18;
    double sales_price = 19;
}


message GetNewStocktakeIdRequest{
}

message GetNewStocktakeIdResponse{
    uint64 new_doc_id = 1;
}

message ManuallyCreateStocktakeProduct{
    uint64 product_id = 1;
    uint64 position_id = 2;
    double quantity = 3;
    uint64 unit_id = 4;
    double tax_price = 5;
    double tax_rate = 6;
    double amount = 7;
    double diff_amount = 8;
    double sales_price = 9;
    double sales_amount = 10;
}

message ManuallyCreateStocktakeRequest{
    google.protobuf.Timestamp target_date = 1;
    uint64 branch_id = 2;
    uint64 new_doc_id = 3;
    bool calculate_inventory = 4;
    repeated ManuallyCreateStocktakeProduct product_list = 5;
    string source = 6;
}

message ManuallyCreateStocktakeResponse{
    bool result = 1;
}

// A account resource.
message GetProductByStocktakeTypeRequest {
    //stocktake_type 盘点类型
    string stocktake_type = 1;
    uint64 branch_id = 2;
    string storage_type = 3;
    string lan = 4;
}

message StocktakeProductType {
    //商品编码
    string code = 1;
    //商品id
    uint64 id = 2;
    //商品名字
    string name = 3;
    //商品分类
    string spec = 4;
    //储存方式
    string storage_type = 5;
}
message GetProductByStocktakeTypeResponse {
    repeated StocktakeProductType rows = 1;
}

message GetStocktakeByDocIDRequest {
    //盘点单doc_id
    uint64 doc_id = 1;
    string lan = 2;
}
message Stocktake {
    //盘点单id
    uint64 id = 1;
    uint64 partner_id = 2;
    //盘点批次id
    uint64 branch_batch_id = 3;
    //盘点单门店id
    uint64 branch_id = 4;
    //盘点计划id
    uint64 schedule_id = 5;
    //盘点单门店类型
    string branch_type = 6;
    //是否计算库存
    bool calculate_inventory = 7;
    //盘点单编码
    string code = 8;
    //标识符
    string remark = 11;
    //结果类型
    string result_type = 12;
    //盘点计划编码
    string schedule_code = 13;
    //盘点差异标识
    int32 st_diff_flag = 14;
    enum STATUS {
        // 计划生成初始状态
        STARTED = 0;
        // 新建状态
        INITED = 1;
        // 取消状态
        CANCELLED = 2;
        // 提交
        SUBMITTED = 3;
        // 驳回
        REJECTED = 4;
        //财务核算检查后状态
        APPROVED = 5;
        //录入盘点单位确定状态
        CONFIRMED = 6;
        // 月盘全盘0提交
        SUBMIT_0 = 7;
        APPROVE_0 = 8;
    }
    //盘点单状态
    STATUS status = 15;
    //过程状态
    STATUS process_status = 16;
    //门店us_id
    uint64 store_secondary_id = 17;
    //盘点单类型
    string type = 18;
    //创建者
    uint64 created_by = 19;
    //更新者
    uint64 updated_by = 20;
    //校验者
    uint64 review_by = 21;
    //盘点日期
    google.protobuf.Timestamp target_date = 22;
    //创建时间
    google.protobuf.Timestamp created_at = 23;
    //更新时间
    google.protobuf.Timestamp updated_at = 24;
    uint64 user_id = 25;
    string created_name = 26;
    string updated_name = 27;
    //盘点计划名称
    string schedule_name = 28;
    string diff_err_message = 29;
    string month_err_message = 31;
    string original_code = 35;
    uint64 original_doc_id = 36;
    bool is_recreate = 37;
    string recreate_code = 38;
    uint64 recreate_doc_id = 39;
    string submit_name = 40;
    string approve_name = 41;
    string stocktake_type = 42;
    string reject_reason = 44;
    // 自动作废时间(utc)
    google.protobuf.Timestamp auto_invalid_time = 45;
    // 自动审核时间(utc)
    google.protobuf.Timestamp auto_approve_time = 46;
    bool is_empty = 47;
    // 计划单据状态自动变更设置
    repeated ScheduleStatusPlan status_plan = 48;
    // 盘点单金额合计
    double total_amount = 50;
    // 盘点单差异金额合计
    double total_diff_amount = 51;
    // 盘点单零售金额合计
    double total_sales_amount = 52;
        // 盘点单差异金额合计
    double total_diff_sales_amount = 53;

}
message ScheduleStatusPlan{
    // 报废单自动变更时间
    google.protobuf.Timestamp time = 1;
    // 状态变更开始状态
    repeated string start_status = 2;
    // 状态变更结束状态
    string end_status = 3;
    // 状态自动变更时间周期
    string time_around = 4;
    // 单据过滤条件
    string doc_filter = 5;
}

message GetStocktakeByIdsRequest {
    repeated uint64 ids = 1;
    string lan = 2;
}

message GetStocktakeByIdsResponse {
    repeated Stocktake rows = 1;
    uint32 total = 2;
}

message GetStocktakeRequest {
    //是否包含总数
    bool include_total = 1;
    //门店ID
    repeated uint64 store_ids = 2;
    //营运区域id
    repeated uint64 branch_ids = 3;
    enum S_STATUS {
        OPENED = 0;
        CLOSED = 1;
    }
    //门店状态
    repeated S_STATUS store_status = 4;
    enum S_TYPE {
        W = 0;
        D = 1;
        M = 2;
    }
    //盘点单类型
    repeated S_TYPE _type = 5;
    enum STATUS {
        // 计划生成初始状态
        STARTED = 0;
        // 创建状态
        INITED = 1;
        // 取消状态
        CANCELLED = 2;
        // 提交
        SUBMITTED = 3;
        // 驳回
        REJECTED = 4;
        //财务核算检查后状态
        APPROVED = 5;
        //录入盘点单位确定状态
        CONFIRMED = 6;
        // 月盘全盘0提交
        SUBMIT_0 = 7;
        APPROVE_0 = 8;
    }
    //盘点单状态
    repeated STATUS status = 6;
    string schedule_code = 7;
    // 商品ids
    repeated uint64 product_ids = 8;
    //分页开始处
    uint32 offset = 9;
    //分页限制数
    uint32 limit = 10;
    //查询开始时间
    google.protobuf.Timestamp start_date = 11;
    //查询结束时间
    google.protobuf.Timestamp end_date = 12;
    google.protobuf.Timestamp target_date = 13;
    string code = 14;
    // 单据id列表查询
    repeated uint64 ids = 21;
    bool is_create = 15;
    // 盘点属性(不定期-R、计划生成-PLAN)
    string stocktake_type = 16;
    // 排序(默认asc)
    string order = 17;
    string sort = 18;
    string branch_type = 19;
    string lan = 20;
    // 盘点计划名称
    string schedule_name = 22;
}
message GetStocktakeResponse {
    repeated Stocktake rows = 1;
    uint32 total = 2;
}
message GetStocktakeProductRequest {
    //盘点单doc_id
    uint64 doc_id = 1;
    //是否包含核算单位
    bool include_unit = 2;
    uint32 limit = 3;
    uint32 offset = 4;
    bool include_total = 5;
    uint64 category_id = 6;
    string storage_type = 7;
    string product_name = 8;
    string lan = 9;
    bool include_barcode = 10;
}
message StocktakeProductTagName {
    uint64 id = 1;
    uint64 doc_id = 2;
    uint64 stp_id = 3;
    uint64 product_id = 4;
    uint64 tag_id = 5;
    string tag_name = 6;
    double tag_quantity = 7;
    double accounting_quantity = 8;
    uint64 unit_id = 9;
    string unit_spec = 10;
    string unit_name = 11;
    double unit_rate = 12;
    uint64 accounting_unit_id = 13;
    string accounting_unit_name = 14;
    string accounting_unit_spec = 15;
    uint64 partner_id = 16;
    uint64 user_id = 17;
    uint64 created_by = 18;
    uint64 updated_by = 19;
    //创建时间
    google.protobuf.Timestamp created_at = 20;
    //更新时间
    google.protobuf.Timestamp updated_at = 21;
    string created_name = 22;
    string updated_name = 23;
    double tax_rate = 24;
    double tax_price = 25;
    double cost_price = 26;
    double sales_price = 27;
}
message StocktakeProductUnits {
    uint64 unit_id = 1;
    string unit_name = 2;
    string unit_spec = 3;
    double unit_rate = 4;
    double tax_rate = 5;
    double tax_price = 6;
    double cost_price = 7;
    double sales_price = 8;
    bool default = 9;
    bool stocktake = 10;
}
message StocktakeProduct {
    //盘点单详情id
    uint64 id = 1;
    uint64 partner_id = 2;
    //盘点单id
    uint64 doc_id = 3;
    //门店id
    uint64 branch_id = 4;
    //商品id
    uint64 product_id = 5;
    //核算数量
    double accounting_quantity = 6;
    //核算单位id
    uint64 accounting_unit_id = 7;
    //核算单位名字
    string accounting_unit_name = 8;
    //核算单位分类
    string accounting_unit_spec = 9;
    //盘点单编号
    string code = 10;
    bool deleted = 11;
    //盘点差异量
    double diff_quantity = 12;
    //盘点排序码
    string display_order = 13;
    string extends = 14;
    bool ignored = 15;
    //理论库存
    double inventory_quantity = 16;
    bool is_system = 17;
    uint64 item_number = 18;
    //物料编码
    string material_number = 19;
    //商品编码
    string product_code = 20;
    //商品名字
    string product_name = 21;
    //盘点数量
    double quantity = 22;
    //盘点单类型
    string st_type = 23;
    //储藏类型
    string storage_type = 24;
    //核算差异量
    double unit_diff_quantity = 25;
    //盘点单位id
    uint64 unit_id = 26;
    //盘点单位名字
    string unit_name = 27;
    //盘点单位比率
    double unit_rate = 28;
    //盘点单位类型
    string unit_spec = 29;
    //盘点单位列表
    repeated StocktakeProductUnits units = 30;
    //创建者
    uint64 created_by = 31;
    //更新者
    uint64 updated_by = 32;
    //盘点单日期
    google.protobuf.Timestamp target_date = 33;
    //创建时间
    google.protobuf.Timestamp created_at = 34;
    //更新时间
    google.protobuf.Timestamp updated_at = 35;
    uint64 user_id = 36;
    //盘点商品状态'INITED','REJECTED','CONFIRMED','FINALIZED','APPROVED'
    string status = 37;
    bool is_pda = 38;
    //商品标签明细
    repeated StocktakeProductTagName product_tags = 39;
    bool is_empty = 40;
    string created_name = 41;
    string updated_name = 42;
    bool is_null = 43;
    double tag_quantity = 44;
    double convert_accounting_quantity = 45;
    bool is_bom = 46;
    bool allow_stocktake_edit = 47;
    // 商品规格
    string spec = 48;
    // 损益金额
    double diff_price = 49;
    repeated string barcode = 50;
    //商品类别id
    uint64 category_id = 54;
    uint64 position_id = 55;
    string category_name = 56;
    // 税率
    double tax_rate = 57;
    // 含税单价
    double tax_price = 58;
	// 含税金额
    double amount = 59;
    // 差异金额
    double diff_amount = 60;
    // 成本价
    double cost_price = 61;
    // 零售金额
    double sales_amount = 62;
    // 零售价
    double sales_price = 63;
    double diff_sales_amount = 64;
}
message GetStocktakeProductResponse {
    repeated StocktakeProduct rows = 1;
    uint64 total = 2;
    repeated StocktakePositionProducts position_rows = 3;
}
message UserCreateStocktakeRequest {
    //请求ID
    uint64 request_id = 1;
    //门店ID
    uint64 branch_id = 2;
    //盘点类型
    string _type = 3;
    //盘点日期
    google.protobuf.Timestamp target_date = 4;
    string lan = 5;
}
message UserCreateStocktakeResponse {
    //盘点单id
    uint64 id = 1;
    uint64 partner_id = 2;
    //盘点批次id
    uint64 branch_batch_id = 3;
    //盘点单门店id
    uint64 branch_id = 4;
    //盘点计划id
    uint64 schedule_id = 5;
    //盘点单门店类型
    string branch_type = 6;
    //是否计算库存
    bool calculate_inventory = 7;
    //盘点单编码
    string code = 8;
    //是否预计算
    bool forecasting = 9;
    //预计算时间
    string forecasting_time = 10;
    //标识符
    string remark = 11;
    //结果类型
    string result_type = 12;
    //盘点计划编码
    string schedule_code = 13;
    //盘点差异标识
    int32 st_diff_flag = 14;
    enum STATUS {
        //用户手动创建状态
        INITED = 0;
    }
    //盘点单状态
    STATUS status = 15;
    //过程状态
    STATUS process_status = 16;
    //门店us_id
    uint64 store_secondary_id = 17;
    //盘点单类型
    string type = 18;
    //创建者
    uint64 created_by = 19;
    //更新者
    uint64 updated_by = 20;
    //校验者
    uint64 review_by = 21;
    //盘点日期
    google.protobuf.Timestamp target_date = 22;
    //创建时间
    google.protobuf.Timestamp created_at = 23;
    //更新时间
    google.protobuf.Timestamp updated_at = 24;
    uint64 user_id = 25;
    string created_name = 26;
    string updated_name = 27;
}
message TagQuantity {
    //标签id
    uint64 id = 1;
    uint64 tag_id = 2;
    //标签名
    string tag_name = 3;
    //标签名对应数量
    double tag_quantity = 4;
    //标签盘点单位
    uint64 unit_id = 5;
}
message PutStocktakeProducts {
    uint64 id = 1;
    //总数
    double quantity = 2;
    uint64 unit_id = 3;
    repeated TagQuantity tag_products = 4;
    //是否pda盘点
    bool is_pda = 5;
    //是否置0
    bool is_empty = 6;
    //PC盘点是否置空
    bool is_null = 7;
    // 删除的商品下标签id列表
    repeated uint64 del_tag_ids = 8;
}
message PutStocktakeByDocIDRequest {
    //盘点单doc_id
    uint64 doc_id = 1;
    repeated PutStocktakeProducts products = 2;
    string lan = 3;
    // 月盘单全盘零
    bool all_zero = 4;
}
message RejectStocktakeProductRequest {
    //盘点单doc_id
    uint64 doc_id = 1;
    string reason = 3;
    string lan = 4;
}
message RejectStocktakeProductResponse {
    bool result = 1;
}
message PutStocktakeByDocIDResponse {
    bool result = 1;
}
message CheckStocktakeByDocIDRequest {
    //盘点单doc_id
    uint64 doc_id = 1;
    // 是否已经检查过
    bool check = 2;
    // 区分门店仓库 WAREHOUSE/STORE
    string branch_type = 3;
    string lan = 4;
}
message CheckStocktakeByDocIDDetail {
    uint64 id = 1;
    string code = 2;
    string status = 3;
}
message CheckStocktakeByDocIDResponse {
    bool handler = 1;
    repeated CheckStocktakeByDocIDDetail adjust = 2;
    repeated CheckStocktakeByDocIDDetail receiving_diff = 3;
    repeated CheckStocktakeByDocIDDetail transfer = 4;
    repeated CheckStocktakeByDocIDDetail receiving = 5;
    repeated CheckStocktakeByDocIDDetail return = 6;
    repeated CheckStocktakeByDocIDDetail direct_receiving = 7;
    repeated CheckStocktakeByDocIDDetail direct_receiving_diff = 8;
    repeated CheckStocktakeByDocIDDetail direct_return = 9;
    repeated CheckStocktakeByDocIDDetail stocktake = 10;
    repeated CheckStocktakeByDocIDDetail demand = 11;
    repeated CheckStocktakeByDocIDDetail self_picking = 12;
}
message ConfirmStocktakeByDocIDRequest {
    //盘点单doc_id
    uint64 doc_id = 1;
    bool check = 2;
    string lan = 3;
}
message SubmitStocktakeByDocIDResponse {
    bool result = 1;
}
message SubmitStocktakeByDocIDRequest {
    //盘点单doc_id
    uint64 doc_id = 1;
    string submit_name = 2;
    string lan = 3;
    // 是否全盘零
    bool all_zero = 4;
    string source = 5;
}
message ApproveStocktakeByDocIDRequest {
    //盘点单doc_id
    uint64 doc_id = 1;
    string type = 2;
    string approve_name = 3;
    string lan = 4;
    // 是否全盘零
    bool all_zero = 5;
    string source = 6;
}
message ApproveStocktakeByDocIDResponse {
    bool result = 1;
    bool handler = 2;
    repeated CheckStocktakeByDocIDDetail adjust = 3;
    repeated CheckStocktakeByDocIDDetail receiving_diff = 4;
    repeated CheckStocktakeByDocIDDetail transfer = 5;
    repeated CheckStocktakeByDocIDDetail receiving = 6;
    repeated CheckStocktakeByDocIDDetail return = 7;
    repeated CheckStocktakeByDocIDDetail direct_receiving = 8;
    repeated CheckStocktakeByDocIDDetail direct_receiving_diff = 9;
    repeated CheckStocktakeByDocIDDetail direct_return = 10;
    repeated CheckStocktakeByDocIDDetail self_picking = 12;
}
message ConfirmStocktakeByDocIDResponse {
    bool result = 1;
}
message CancelStocktakeByDocIDRequest {
    //盘点单doc_id
    uint64 doc_id = 1;
    string lan = 2;
}
message CancelStocktakeByDocIDResponse {
    bool result = 1;
}
message SupplySTBatch {
    uint64 id = 1;
    uint64 partner_id = 2;
    //批次日期
    google.protobuf.Timestamp schedule_date = 3;
    enum P_STATUS {
        INITED = 0; //初始化成功
        PROC_FAIL = 1; //初始化失败
        PROCESSING = 2; //执行中
        PROCESSED = 3; //执行失败
    }
    uint32 success = 4;
    uint32 fail = 5;
    uint32 count = 6;
    P_STATUS process_status = 7;
    string reason = 8;
    google.protobuf.Timestamp created = 9;
    google.protobuf.Timestamp updated = 10;
}
message SupplySTBatchSchedule {
    uint64 id = 1;
    uint64 partner_id = 2;
    //批次ID
    uint64 batch_id = 3;
    //计划ID
    uint64 schedule_id = 4;
    google.protobuf.Timestamp schedule_date = 5;
    enum P_STATUS {
        INITED = 0;
        PROC_FAIL = 1;
        PROCESSING = 2;
        PROCESSED = 3;
    }
    P_STATUS process_status = 6;
    uint32 success = 7;
    uint32 fail = 8;
    uint32 count = 9;
    string reason = 10;
    google.protobuf.Timestamp created = 11;
    google.protobuf.Timestamp updated = 12;
}
message SupplySTBatchBranch {
    uint64 id = 1;
    uint64 partner_id = 2;
    //唯一键
    uint64 schedule_batch_id = 3;
    //计划ID
    uint64 schedule_id = 4;
    //批次ID
    uint64 batch_id = 5;
    //门店ID
    uint64 branch_id = 6;
    google.protobuf.Timestamp schedule_date = 7;
    enum P_STATUS {
        INITED = 0;
        PROC_FAIL = 1;
        PROCESSING = 2;
        PROCESSED = 3;
    }
    P_STATUS process_status = 8;
    uint32 success = 9;
    uint32 fail = 10;
    uint32 count = 11;
    string reason = 12;
    google.protobuf.Timestamp created = 13;
    google.protobuf.Timestamp updated = 14;
}
message CreateStocktakeBatchRequest {
    //请求ID
    uint64 request_id = 1;
    //请求时间
    google.protobuf.Timestamp request_date = 2;
    string lan = 3;
}
message CreateStocktakeBatchResponse {
    bool result = 1;
    //盘点计划数量
    uint32 schedule_count = 2;
    //请求时间
    google.protobuf.Timestamp request_date = 3;
}
message SYSCreateStocktakeScheduleRequest {
    //请求ID
    uint64 request_id = 1;
    //批次ID
    uint64 batch_id = 2;
    //计划ID
    uint64 schedule_id = 3;
    uint64 schedule_batch_id = 4;
    //请求时间
    google.protobuf.Timestamp request_date = 5;
    string lan = 6;
}
message SYSCreateStocktakeScheduleResponse {
    bool result = 1;
    //盘点门店数量
    uint32 branch_count = 2;
    //请求时间
    google.protobuf.Timestamp request_date = 3;
}
message SubCreateStocktakeRequest {
    //请求ID
    uint64 request_id = 1;
    //门店ID
    uint64 branch_id = 2;
    uint64 branch_batch_id = 3;
    uint64 schedule_batch_id = 4;
    //请求时间
    google.protobuf.Timestamp request_date = 5;
    string lan = 6;
}
message SubCreateStocktakeResponse {
    bool result = 1;
    //盘点商品数量
    uint32 product_count = 2;
    //请求时间
    google.protobuf.Timestamp request_date = 3;
}
message CreateStocktakeRequest {
    //请求ID
    uint64 request_id = 1;
    //请求时间
    google.protobuf.Timestamp request_date = 2;
    string lan = 3;
}
message CreateStocktakeResponse {
    bool result = 1;
    //请求时间
    google.protobuf.Timestamp request_date = 3;
}
message CheckedStocktakeByDocIDRequest {
    //盘点单doc_id
    uint64 doc_id = 1;
    // 区分门店/仓库 WAREHOUSE/STORE
    string branch_type = 2;
}
//赶任务木想写注释了
message CheckedStocktakeByDocIDResponse {
    bool result = 1;
}
message GetStocktakeTagsRequest {
    uint64 branch_id = 1;
    string lan = 2;
    string tag_name = 3;
    repeated uint64 branch_ids = 4;
}
message StocktakeTags {
    uint64 id = 1;
    string name = 2;
    uint64 partner_id = 3;
    uint64 user_id = 4;
    uint64 created_by = 5;
    uint64 updated_by = 6;
    google.protobuf.Timestamp created_at = 7;
    google.protobuf.Timestamp updated_at = 8;
    string created_name = 9;
    string updated_name = 10;
    uint64 branch_id = 11;
    string branch_name = 12;
}
message GetStocktakeTagsResponse {
    repeated StocktakeTags rows = 1;
}

message GetStocktakeTagsByIdRequest {
    uint64 tag_id = 1;
}

message ActionStocktakeTagsRequest {
    uint64 tag_id = 1;
    enum Action {
        get = 0;    // 弃用，查询接口拆开了
        create = 1;
        delete = 2;
        update = 3;
        copy = 4;
    }
    Action action = 2;
    string name = 3;
    // 复制新增操作对应原门店
    uint64 branch_id = 4;
    string lan = 5;
    // 门店id列表
    repeated uint64 branch_ids = 6;
    // 区域id列表
    repeated uint64 region_ids = 7;
    // 添加维度：全市场/管理区域/门店->all/region/store
    string add_dimension = 8;
    // 标签id-批量删除用
    repeated uint64 tag_ids = 9;
    // 批量修改中原标签名称
    string origin_name = 10;
    // 需要copy的门店
    uint64 copy_branch = 11;
}
message ActionStocktakeTagsResponse {
    bool result = 1;
    uint64 id = 2;
    string name = 3;
    uint64 partner_id = 4;
    uint64 user_id = 5;
    uint64 created_by = 6;
    uint64 updated_by = 7;
    google.protobuf.Timestamp created_at = 8;
    google.protobuf.Timestamp updated_at = 9;
    string created_name = 10;
    string updated_name = 11;
    uint64 branch_id = 12;
}
message DeleteStocktakeProductTagsRequest {
    repeated uint64 id = 1;
}
message DeleteStocktakeProductTagsResponse {
    bool result = 1;
}
message GetStocktakeBalanceRequest {
    google.protobuf.Timestamp start_date = 1;
    google.protobuf.Timestamp end_date = 2;
    repeated uint64 store_ids = 3;
    uint32 limit = 4;
    uint32 offset = 5;
    google.protobuf.Timestamp target_date = 6;
    repeated string status = 7;
    bool include_total = 8;
    string schedule_code = 9;
    string stocktake_type = 10;
    bool is_wms_store = 11;
    string branch_type = 12;
    string lan = 13;
}
message StocktakeBalance {
    uint64 branch_batch_id = 1;
    uint64 branch_id = 2;
    string branch_type = 3;
    bool calculate_inventory = 4;
    string code = 5;
    bool forecasting = 6;
    google.protobuf.Timestamp forecasting_time = 7;
    uint64 id = 8;
    string process_status = 9;
    string remark = 10;
    string result_type = 11;
    uint64 review_by = 12;
    string schedule_code = 13;
    uint64 schedule_id = 14;
    int32 st_diff_flag = 15;
    string status = 16;
    uint64 store_secondary_id = 17;
    google.protobuf.Timestamp target_date = 18;
    string type = 19;
    uint64 partner_id = 20;
    uint64 user_id = 21;
    uint64 created_by = 22;
    uint64 updated_by = 23;
    string created_name = 24;
    string updated_name = 25;
    google.protobuf.Timestamp created_at = 26;
    google.protobuf.Timestamp updated_at = 27;
    string schedule_name = 28;
    string diff_err_message = 29;
    string diff_jde_status = 30;
    string month_err_message = 31;
    string month_jde_status = 32;
    string jde_diff_code = 33;
    string jde_month_code = 34;
    string branch_name = 35;
    string branch_code = 36;
    string original_code = 37;
    uint64 original_doc_id = 38;
    bool is_recreate = 39;
    string recreate_code = 40;
    uint64 recreate_doc_id = 41;
    string submit_name = 42;
    string approve_name = 43;
    string stocktake_type = 44;
    uint64 request_id = 45;
    double total_amount = 46;
    double total_diff_amount = 47;
    double total_sales_amount = 48;
        // 盘点单差异金额合计
    double total_diff_sales_amount = 49;
}
message GetStocktakeBalanceResponse {
    repeated StocktakeBalance rows = 1;
    uint64 total = 2;
}
message GetStocktakeBalanceProductGroupRequest {
    uint64 doc_id = 1;
    string lan = 2;
}
message TagProductBi {
    string tag_name = 1;
    double tag_quantity = 2;
    string tag_unit_name = 3;
    double tag_uint_rate = 4;
}
message StocktakeBalanceProductGroup {
    double accounting_quantity = 1;
    uint64 accounting_unit_id = 2;
    string accounting_unit_name = 3;
    string accounting_unit_spec = 4;
    double diff_quantity = 5;
    double inventory_quantity = 6;
    bool is_system = 7;
    string material_number = 8;
    string product_code = 9;
    uint64 product_id = 10;
    string product_name = 11;
    double quantity = 12;
    string storage_type = 13;
    string tag_code = 14;
    string tag_name = 15;
    double unit_diff_quantity = 16;
    uint64 unit_id = 17;
    string unit_name = 18;
    double unit_rate = 19;
    string unit_spec = 20;
    repeated TagProductBi tag_details = 21;
    double accounting_inventory_quantity = 22;
    double accounting_diff_quantity = 23;
    uint64 position_id = 25;
    string position_code = 26;
    string position_name = 27;
}
message GetStocktakeBalanceProductGroupResponse {
    repeated StocktakeBalanceProductGroup rows = 1;
}
message StocktakeBiDetailedRequest {
    google.protobuf.Timestamp start_date = 1;
    google.protobuf.Timestamp end_date = 2;
    repeated uint64 category_ids = 3;
    string type = 4;
    uint32 offset = 6;
    uint32 limit = 7;
    bool include_total = 8;
    string product_name = 9;
    repeated uint64 store_ids = 10;
    // 排序(默认asc)
    string order = 11;
    string sort = 12;
    string code = 13;
    string jde_code = 14;
    bool is_wms_store = 15;
    // 区分门店还是仓库/加工中心 STORE/WAREHOUSE/MACHINING_CENTER
    string branch_type = 16;
    string lan = 17;
}
message StocktakeBiDetailed {
    double accounting_quantity = 1;
    uint64 accounting_unit_id = 2;
    string accounting_unit_name = 3;
    string accounting_unit_spec = 4;
    string branch_1_name = 5;
    string branch_2_name = 6;
    uint64 branch_id = 7;
    string category_code = 8;
    uint64 category_id = 9;
    string category_name = 10;
    //"category_parent"
    string code = 11;
    bool deleted = 12;
    double diff_quantity = 13;
    string display_order = 14;
    uint64 doc_id = 15;
    string extends = 16;
    uint64 id = 17;
    bool ignored = 18;
    double inventory_quantity = 19;
    bool is_system = 20;
    uint64 item_number = 21;
    string material_number = 22;
    string product_code = 23;
    uint64 product_id = 24;
    string product_name = 25;
    double quantity = 26;
    string type = 27;
    string storage_type = 28;
    string store_code = 29;
    string store_name = 30;
    google.protobuf.Timestamp target_date = 31;
    double unit_diff_quantity = 32;
    uint64 unit_id = 33;
    string unit_name = 34;
    double unit_rate = 35;
    string unit_spec = 36;
    uint64 partner_id = 37;
    uint64 created_by = 38;
    uint64 updated_by = 39;
    string created_name = 40;
    string updated_name = 41;
    google.protobuf.Timestamp created_at = 42;
    google.protobuf.Timestamp updated_at = 43;
    bool is_empty = 44;
    bool is_pda = 45;
    string status = 46;
    string units = 47;
    uint64 user_id = 48;
    bool is_null = 49;
    double tag_quantity = 50;
    repeated ProductTagBi product_tags = 51;
    //商品状态、JDE盘点单号、规格、理论数量（加在盘点数量旁边）、盘点差异（百分比）、新建时间、操作人，提交时间、操作人。
    bool is_enable = 52;
    string jde_code = 53;
    string spec = 54;
    double diff_percentage = 55;
    google.protobuf.Timestamp created_time = 56;
    string created_user_name = 57;
    google.protobuf.Timestamp submitted_time = 58;
    string submitted_user_name = 59;
    string branch_type = 60;
    uint64 position_id = 62;
    string position_code = 63;
    string position_name = 64;
}
message ProductTagBi {
    uint64 tag_unit_id = 1;
    string tag_unit_name = 2;
    double tag_quantity = 3;
    string tag_name = 4;
    uint64 accounting_unit_id = 5;
    string accounting_unit_name = 6;
    double accounting_quantity = 7;
    uint64 id = 8;
}
message ST_total {
    uint64 count = 1;
    double sum_quantity = 2;
    double sum_accounting_quantity = 3;
}
message StocktakeBiDetailedResponse {
    repeated StocktakeBiDetailed rows = 1;
    ST_total total = 2;
}
message StocktakeBalanceRegionRequest {
    bool include_total = 1;
    uint32 limit = 2;
    uint32 offset = 3;
    google.protobuf.Timestamp start = 4;
    google.protobuf.Timestamp end = 5;
    uint64 region_id = 6;
    repeated uint64 product_ids = 7;
    string type = 8;
    string branch_type = 9;
    string lan = 10;

}
message StocktakeBalanceRegionDetails {
    double accounting_inventory_quantity = 1;
    double accounting_quantity = 2;
    string accounting_unit_name = 3;
    string branch_name = 4;
    double diff_quantity = 5;
    string doc_type = 6;
    double inventory_quantity = 7;
    string material_number = 8;
    uint64 product_id = 9;
    string product_name = 10;
    double quantity = 11;
    uint64 store_id = 12;
    string store_name = 13;
    uint64 tag_1 = 14;
    string tag_1_name_pinyin = 15;
    double unit_diff_quantity = 16;
    string unit_name = 17;
    double unit_rate = 18;
}
message StocktakeBalanceRegion {
    double accounting_inventory_quantity = 1;
    double accounting_quantity = 2;
    string accounting_unit_name = 3;
    repeated StocktakeBalanceRegionDetails details = 4;
    string unit_name = 5;
    string product_name = 7;
    double quantity = 8;
    double inventory_quantity = 9;
    double accoinventory_quantity = 10;
    double diff_quantity = 11;
    double unit_diff_quantity = 12;
    double unit_rate = 13;
    string doc_type = 14;
    uint64 product_id = 15;
    uint64 store_id = 16;
    string store_name = 17;
    string store_code = 18;
    string product_code = 19;
    double accounting_diff_quantity = 20;
}
message StocktakeBalanceRegionResponse {
    repeated StocktakeBalanceRegion rows = 1;
    uint64 total = 2;
}
message StoreDataScopeRequest {
    string search = 1;
    string search_fields = 2;
    repeated uint64 ids = 3;
    string return_fields = 4;
    string filters = 5;
    string relation_filters = 6;
    int64 limit = 7;
    uint64 offset = 8;
    string lan = 9;
}
message ScopeStores {
    string id = 1;
    string name = 2;
    string code = 3;
    string second_code = 4;
    string type = 5;
    string address = 6;
    string tel = 7;
    string contact = 8;
    string status = 9;
    string name_en = 10;
    string open_date = 11;
    string close_date = 12;
    string email = 13;
    repeated string geo_region = 14;
    repeated string branch_region = 15;
    repeated string order_region = 16;
    repeated string distribution_region = 17;
    repeated string purchase_region = 18;
    repeated string market_region = 19;
    repeated string transfer_region = 20;
    repeated string attribute_region = 21;
}
message StoreDataScope {
    repeated ScopeStores rows = 1;
    uint64 total = 2;
}
message AdvanceStocktakeDiffRequest {
    uint64 doc_id = 1;
    string lan = 2;
}
message AdvanceStocktakeDiffResponse {
    repeated StocktakeProduct rows = 1;
    uint64 total = 2;
    repeated StocktakePositionProducts position_rows = 3;
}
message StocktakeDiffReportRequest {
    google.protobuf.Timestamp start_date = 1;
    google.protobuf.Timestamp end_date = 2;
    repeated uint64 store_ids = 3;
    repeated uint64 product_ids = 4;
    uint32 limit = 5;
    uint32 offset = 6;
    google.protobuf.Timestamp target_date = 7;
    string code = 8;
    string stocktake_type = 9;
    string jde_diff_code = 10;
    string schedule_code = 11;
    string status = 12;
    // 排序(默认asc)
    string order = 13;
    string sort = 14;
    // 上限 区分0，传字符串
    string upper_limit = 15;
    // 下限 区分0，传字符串
    string lower_limit = 16;
    bool is_wms_store = 17;
    string lan = 18;
}
message StocktakeDiffReportRow {
    string code = 1;
    uint64 store_id = 2;
    string store_code = 3;
    string store_name = 4;
    string company_code = 5;
    string product_code = 6;
    uint64 product_id = 7;
    string product_name = 8;
    string type = 9;
    double diff_quantity = 10;
    double quantity = 11;
    double accounting_quantity = 12;
    double inventory_quantity = 13;
    double diff_quantity_percentage = 14;
    google.protobuf.Timestamp target_date = 15;
    uint64 unit_id = 16;
    string unit_name = 17;
    double unit_rate = 18;
    string unit_code = 19;
    uint64 accounting_unit_id = 20;
    string accounting_unit_name = 21;
    string accounting_unit_code = 22;
    string jde_diff_code = 23;
    string company_name = 24;
    double accounting_inventory_quantity = 25;
    double accounting_diff_quantity = 26;
    string unit_spec = 27;
    string status = 28;
    string schedule_code = 29;
}
message StocktakeDiffReportResponse {
    repeated StocktakeDiffReportRow rows = 1;
    uint64 total = 2;
}
message CheckDemandDetail {
    uint64 id = 1;
    string code = 2;
    string type = 3;
    bool is_plan = 4;
}
message RecreateStocktakeDocRequest {
    repeated uint64 doc_ids = 1;
    bool calculate_inventory = 2;
    string schedule_name = 3;
    string remark = 4;
    string schedule_code = 5;
    uint64 schedule_id = 6;
    string lan = 7;
}
message RecreateStocktakeDocResponse {
    bool result = 1;
    repeated uint64 restocktake_doc_ids = 2;
    repeated uint64 has_recreate_doc_id_no_confirm = 3;
}
enum PeriodGroupMethod {
    // 按天汇总
    BY_DAY = 0;
    // 按月汇总
    BY_MONTH = 1;
    // 按年汇总
    BY_YEAR = 2;
}

message StocktakeDiffCollectReportRequest {
    google.protobuf.Timestamp start_date = 1;
    google.protobuf.Timestamp end_date = 2;
    repeated uint64 store_ids = 3;
    repeated uint64 product_ids = 4;
    uint32 limit = 5;
    uint32 offset = 6;
    google.protobuf.Timestamp target_date = 7;
    string code = 8;
    string stocktake_type = 9;
    string jde_diff_code = 10;
    string schedule_code = 11;
    string status = 12;
    // 排序(默认asc)
    string order = 13;
    string sort = 14;
    string period_symbol = 15;
    // 上限 区分0，传字符串
    string upper_limit = 16;
    // 下限 区分0，传字符串
    string lower_limit = 17;
    bool is_wms_store = 18;
    string lan = 19;
}
message StocktakeDiffCollectReportRow {
    uint64 store_id = 1;
    string store_code = 2;
    string store_name = 3;
    string company_code = 4;
    string product_code = 5;
    uint64 product_id = 6;
    string product_name = 7;
    double diff_quantity = 8;
    double quantity = 9;
    double accounting_quantity = 10;
    double inventory_quantity = 11;
    double diff_quantity_percentage = 12;
    uint64 unit_id = 13;
    string unit_name = 14;
    double unit_rate = 15;
    string unit_code = 16;
    uint64 accounting_unit_id = 17;
    string accounting_unit_name = 18;
    string accounting_unit_code = 19;
    string company_name = 20;
    double accounting_inventory_quantity = 21;
    double accounting_diff_quantity = 22;
    string unit_spec = 23;
    string period_symbol = 24;
}
message StocktakeDiffCollectReportResponse {
    repeated StocktakeDiffCollectReportRow rows = 1;
    uint64 total = 2;
}
//["模块", "门店编码", "门店名称", "订单状态", "JDE单号", "BOH单号", "更新人", "更新时间", "日期"]
message UncompleteDocReportRequest {
    string bus_date = 1;
    repeated uint64 store_ids = 2;
    string jde_code = 3;
    string code = 4;
    uint64 limit = 5;
    uint64 offset = 6;
    repeated string model = 7;
    // 排序(默认asc)
    string order = 13;
    string sort = 14;
    bool is_wms_store = 15;
    string lan = 16;
}
message UncompleteDocReportResponse {
    message UncompleteDocReport {
        string model = 1;
        string store_code = 2;
        string store_name = 3;
        string status = 4;
        string jde_code = 5;
        string code = 6;
        string doc_update_name = 7;
        string doc_update_time = 8;
        string doc_date = 9;
        string bus_date = 10;
        uint64 store_id = 11;
        string jde_type = 12;
    }
    repeated UncompleteDocReport rows = 1;
    uint64 total = 2;
}

// 仓库/门店盘点单商品明细导入请求参数
message StocktakeProductImportRequest {
    //盘点单doc_id
    uint64 doc_id = 1;
    // 文件名称
    string file_name = 2;
    // 文件流
    string file_data = 3;
}

message ProductImportResponseRows {
    // 行号
    uint64 row_num = 1;
    // 商品编号
    string product_code = 2;
    // 商品名称
    string product_name = 3;
    // 盘点数量
    double quantity = 4;
    // 储藏类型
    string storage_type = 5;
    // 规格
    string spec = 6;
    // 单位
    string unit = 7;
    // 异常原因
    string error_msg = 8;
    // 仓位ID
    string position_id = 9;
    // 仓位编号
    string position_code = 10;
    // 仓位名称
    string position_name = 11;
}

// 仓库/门店盘点单商品明细导入返回参数
message StocktakeProductImportResponse {
    // 导入失败为False,成功True
    bool result = 1;
    repeated ProductImportResponseRows rows = 2;
    string file_name = 3;
    // 总行数
    uint64 rows_num = 4;
    // 导入记录id
    uint64 batch_id = 5;
}

// 仓库/门店盘点单商品导入状态更新请求参数
message UpdateStocktakeImportBatchRequest {
    // 导入记录id
    uint64 batch_id = 1;
    // 更新状态CANCEL/CONFIRM
    string status = 2;
}

// 仓库/门店盘点单商品导入状态更新返回参数
message UpdateStocktakeImportBatchResponse {
    // 更新失败为False,成功True
    bool result = 1;
    // 盘点单号
    uint64 doc_id = 2;
}

//position
message StocktakePositionProducts{
    uint64 position_id = 1;
    string position_code = 2;
    string position_name = 3;
    repeated StocktakeProduct products = 4;
    uint64 total = 5;
}

message GetStocktakeLogRequest{
    uint64 doc_id = 1;
}


message GetStocktakeLogResponse{
    repeated Log rows = 1;
    uint64 total = 2;
}
message Log{
    // 历史记录id
    uint64 id = 1;
    // 状态
    string status = 2;
    // 操作人
    uint64 created_by = 3;
    // 操作时间
    google.protobuf.Timestamp created_at = 4;
    // 原因
    string reason = 5;
    // 操作人名称
    string created_name = 6;
}
