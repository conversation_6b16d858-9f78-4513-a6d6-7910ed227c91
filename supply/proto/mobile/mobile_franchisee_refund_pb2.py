# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mobile/mobile_franchisee_refund.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='mobile/mobile_franchisee_refund.proto',
  package='mobile_franchisee_refund',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n%mobile/mobile_franchisee_refund.proto\x12\x18mobile_franchisee_refund\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xed\x03\n\x18ListMobileFRefundRequest\x12\x35\n\x11refund_start_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x33\n\x0frefund_end_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x03 \x03(\t\x12\x14\n\x0creceived_bys\x18\x04 \x03(\x04\x12\x16\n\x0e\x66ranchisee_ids\x18\x05 \x03(\x04\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\r\n\x05limit\x18\x07 \x01(\x03\x12\x0e\n\x06offset\x18\x08 \x01(\x04\x12\x15\n\rinclude_total\x18\t \x01(\x08\x12\r\n\x05order\x18\n \x01(\t\x12\x0c\n\x04sort\x18\x0b \x01(\t\x12\x37\n\x13\x61pproved_start_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x11\x61pproved_end_date\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05types\x18\x0e \x03(\t\x12\x11\n\tmain_type\x18\x0f \x01(\t\x12\x11\n\tmain_code\x18\x10 \x01(\t\x12\x14\n\x0cpayment_ways\x18\x11 \x03(\t\x12\x0b\n\x03ids\x18\x12 \x03(\x04\"Z\n\x19ListMobileFRefundResponse\x12.\n\x04rows\x18\x01 \x03(\x0b\x32 .mobile_franchisee_refund.Refund\x12\r\n\x05total\x18\x02 \x01(\x04\"\xcc\x06\n\x06Refund\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x0f\n\x07main_id\x18\x03 \x01(\x04\x12\x11\n\tmain_code\x18\x04 \x01(\t\x12\x11\n\tmain_type\x18\x05 \x01(\t\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\x0c\n\x04type\x18\x07 \x01(\t\x12\x13\n\x0breceived_by\x18\x08 \x01(\x04\x12\x15\n\rreceived_code\x18\t \x01(\t\x12\x15\n\rreceived_name\x18\n \x01(\t\x12\x15\n\rfranchisee_id\x18\r \x01(\x04\x12\x17\n\x0f\x66ranchisee_code\x18\x0e \x01(\t\x12\x17\n\x0f\x66ranchisee_name\x18\x0f \x01(\t\x12/\n\x0brefund_date\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x11 \x01(\t\x12\x16\n\x0eprocess_status\x18\x12 \x01(\t\x12\x15\n\rreject_reason\x18\x14 \x01(\t\x12\x0e\n\x06remark\x18\x15 \x01(\t\x12\x0e\n\x06reason\x18\x16 \x01(\t\x12.\n\ncreated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x19 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1a \x01(\t\x12\x12\n\nupdated_by\x18\x1b \x01(\x04\x12\x14\n\x0cupdated_name\x18\x1c \x01(\t\x12\x11\n\treview_by\x18\x1d \x01(\x04\x12\x15\n\rrefund_amount\x18\x1e \x01(\x01\x12\x12\n\npay_amount\x18\x1f \x01(\x01\x12\x13\n\x0b\x61ttachments\x18  \x03(\t\x12\x0f\n\x07\x65xtends\x18! \x01(\t\x12\x31\n\rapproved_date\x18\" \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0bpayment_way\x18# \x01(\t\x12\x15\n\rtrade_company\x18$ \x01(\x04\x12\x14\n\x0c\x63ompany_name\x18% \x01(\t\x12\x14\n\x0c\x63ompany_code\x18& \x01(\t\x12\x10\n\x08\x62\x61tch_id\x18\' \x01(\x04\"\x9d\x04\n\rRefundProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x03 \x01(\t\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12\x11\n\trefund_id\x18\x05 \x01(\x04\x12\x12\n\npartner_id\x18\x06 \x01(\x04\x12\x13\n\x0b\x63\x61tegory_id\x18\x08 \x01(\x04\x12\x15\n\rcategory_name\x18\t \x01(\t\x12\x14\n\x0cproduct_spec\x18\n \x01(\t\x12\x0f\n\x07unit_id\x18\x0b \x01(\x04\x12\x11\n\tunit_name\x18\x0c \x01(\t\x12\x11\n\tunit_spec\x18\r \x01(\t\x12\x11\n\tunit_rate\x18\x11 \x01(\x01\x12\x10\n\x08quantity\x18\x12 \x01(\x01\x12\x11\n\ttax_price\x18\x14 \x01(\x01\x12\x12\n\ncost_price\x18\x15 \x01(\x01\x12\x10\n\x08tax_rate\x18\x16 \x01(\x01\x12\x0e\n\x06\x61mount\x18\x17 \x01(\x01\x12\x12\n\ncreated_by\x18\x18 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x19 \x01(\t\x12\x12\n\nupdated_by\x18\x1a \x01(\x04\x12\x14\n\x0cupdated_name\x18\x1b \x01(\t\x12.\n\ncreated_at\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1d \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"*\n\x15GetFRefundByIdRequest\x12\x11\n\trefund_id\x18\x01 \x01(\x04\"\x9d\x07\n\x1cGetMobileFRefundByIdResponse\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x0f\n\x07main_id\x18\x03 \x01(\x04\x12\x11\n\tmain_code\x18\x04 \x01(\t\x12\x11\n\tmain_type\x18\x05 \x01(\t\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\x0c\n\x04type\x18\x07 \x01(\t\x12\x13\n\x0breceived_by\x18\x08 \x01(\x04\x12\x15\n\rreceived_code\x18\t \x01(\t\x12\x15\n\rreceived_name\x18\n \x01(\t\x12\x15\n\rfranchisee_id\x18\r \x01(\x04\x12\x17\n\x0f\x66ranchisee_code\x18\x0e \x01(\t\x12\x17\n\x0f\x66ranchisee_name\x18\x0f \x01(\t\x12/\n\x0brefund_date\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x11 \x01(\t\x12\x16\n\x0eprocess_status\x18\x12 \x01(\t\x12\x15\n\rreject_reason\x18\x14 \x01(\t\x12\x0e\n\x06remark\x18\x15 \x01(\t\x12\x0e\n\x06reason\x18\x16 \x01(\t\x12.\n\ncreated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x19 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1a \x01(\t\x12\x12\n\nupdated_by\x18\x1b \x01(\x04\x12\x14\n\x0cupdated_name\x18\x1c \x01(\t\x12\x11\n\treview_by\x18\x1d \x01(\x04\x12\x15\n\rrefund_amount\x18\x1e \x01(\x01\x12\x12\n\npay_amount\x18\x1f \x01(\x01\x12\x13\n\x0b\x61ttachments\x18  \x03(\t\x12\x0f\n\x07\x65xtends\x18! \x01(\t\x12\x31\n\rapproved_date\x18\" \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0bpayment_way\x18# \x01(\t\x12\x15\n\rtrade_company\x18$ \x01(\x04\x12\x14\n\x0c\x63ompany_name\x18% \x01(\t\x12\x14\n\x0c\x63ompany_code\x18& \x01(\t\x12\x10\n\x08\x62\x61tch_id\x18\' \x01(\x04\x12\x39\n\x08products\x18( \x03(\x0b\x32\'.mobile_franchisee_refund.RefundProduct\"2\n\x1dGetMobileRefundHistoryRequest\x12\x11\n\trefund_id\x18\x01 \x01(\x04\"]\n\x15RefundHistoryResponse\x12\x35\n\x04rows\x18\x01 \x03(\x0b\x32\'.mobile_franchisee_refund.RefundHistory\x12\r\n\x05total\x18\x02 \x01(\x04\"\xac\x01\n\rRefundHistory\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x11\n\trefund_id\x18\x03 \x01(\x04\x12\x0e\n\x06\x61\x63tion\x18\x04 \x01(\t\x12\x12\n\ncreated_by\x18\x05 \x01(\x04\x12.\n\ncreated_at\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\x07 \x01(\t\"}\n\x1c\x44\x65\x61lMobileFRefundByIdRequest\x12\x11\n\trefund_id\x18\x01 \x01(\x04\x12\x0e\n\x06\x61\x63tion\x18\x02 \x01(\t\x12\x0e\n\x06remark\x18\x03 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x04 \x03(\t\x12\x15\n\rreject_reason\x18\x05 \x01(\t\"/\n\x1d\x44\x65\x61lMobileFRefundByIdResponse\x12\x0e\n\x06result\x18\x01 \x01(\t*?\n\x06Status\x12\n\n\x06INITED\x10\x00\x12\x0c\n\x08REJECTED\x10\x03\x12\r\n\tCANCELLED\x10\x04\x12\x0c\n\x08\x41PPROVED\x10\t2\x9c\x06\n\x1dMobileFranchiseeRefundService\x12\xad\x01\n\x11ListMobileFRefund\x12\x32.mobile_franchisee_refund.ListMobileFRefundRequest\x1a\x33.mobile_franchisee_refund.ListMobileFRefundResponse\"/\x82\xd3\xe4\x93\x02)\x12\'/api/v2/supply/mobile/franchisee/refund\x12\xbc\x01\n\x14GetMobileFRefundById\x12/.mobile_franchisee_refund.GetFRefundByIdRequest\x1a\x36.mobile_franchisee_refund.GetMobileFRefundByIdResponse\";\x82\xd3\xe4\x93\x02\x35\x12\x33/api/v2/supply/mobile/franchisee/refund/{refund_id}\x12\xc7\x01\n\x16GetMobileRefundHistory\x12\x37.mobile_franchisee_refund.GetMobileRefundHistoryRequest\x1a/.mobile_franchisee_refund.RefundHistoryResponse\"C\x82\xd3\xe4\x93\x02=\x12;/api/v2/supply/mobile/franchisee/refund/{refund_id}/history\x12\xc1\x01\n\x15\x44\x65\x61lMobileFRefundById\x12\x36.mobile_franchisee_refund.DealMobileFRefundByIdRequest\x1a\x37.mobile_franchisee_refund.DealMobileFRefundByIdResponse\"7\x82\xd3\xe4\x93\x02\x31\",/api/v2/supply/mobile/franchisee/refund/deal:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])

_STATUS = _descriptor.EnumDescriptor(
  name='Status',
  full_name='mobile_franchisee_refund.Status',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='INITED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REJECTED', index=1, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=2, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVED', index=3, number=9,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3579,
  serialized_end=3642,
)
_sym_db.RegisterEnumDescriptor(_STATUS)

Status = enum_type_wrapper.EnumTypeWrapper(_STATUS)
INITED = 0
REJECTED = 3
CANCELLED = 4
APPROVED = 9



_LISTMOBILEFREFUNDREQUEST = _descriptor.Descriptor(
  name='ListMobileFRefundRequest',
  full_name='mobile_franchisee_refund.ListMobileFRefundRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='refund_start_date', full_name='mobile_franchisee_refund.ListMobileFRefundRequest.refund_start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_end_date', full_name='mobile_franchisee_refund.ListMobileFRefundRequest.refund_end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_franchisee_refund.ListMobileFRefundRequest.status', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_bys', full_name='mobile_franchisee_refund.ListMobileFRefundRequest.received_bys', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_ids', full_name='mobile_franchisee_refund.ListMobileFRefundRequest.franchisee_ids', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_franchisee_refund.ListMobileFRefundRequest.code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_franchisee_refund.ListMobileFRefundRequest.limit', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_franchisee_refund.ListMobileFRefundRequest.offset', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='mobile_franchisee_refund.ListMobileFRefundRequest.include_total', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_franchisee_refund.ListMobileFRefundRequest.order', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='mobile_franchisee_refund.ListMobileFRefundRequest.sort', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approved_start_date', full_name='mobile_franchisee_refund.ListMobileFRefundRequest.approved_start_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approved_end_date', full_name='mobile_franchisee_refund.ListMobileFRefundRequest.approved_end_date', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='types', full_name='mobile_franchisee_refund.ListMobileFRefundRequest.types', index=13,
      number=14, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_type', full_name='mobile_franchisee_refund.ListMobileFRefundRequest.main_type', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_code', full_name='mobile_franchisee_refund.ListMobileFRefundRequest.main_code', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_ways', full_name='mobile_franchisee_refund.ListMobileFRefundRequest.payment_ways', index=16,
      number=17, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='mobile_franchisee_refund.ListMobileFRefundRequest.ids', index=17,
      number=18, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=131,
  serialized_end=624,
)


_LISTMOBILEFREFUNDRESPONSE = _descriptor.Descriptor(
  name='ListMobileFRefundResponse',
  full_name='mobile_franchisee_refund.ListMobileFRefundResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_franchisee_refund.ListMobileFRefundResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_franchisee_refund.ListMobileFRefundResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=626,
  serialized_end=716,
)


_REFUND = _descriptor.Descriptor(
  name='Refund',
  full_name='mobile_franchisee_refund.Refund',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_franchisee_refund.Refund.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='mobile_franchisee_refund.Refund.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_id', full_name='mobile_franchisee_refund.Refund.main_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_code', full_name='mobile_franchisee_refund.Refund.main_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_type', full_name='mobile_franchisee_refund.Refund.main_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_franchisee_refund.Refund.code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='mobile_franchisee_refund.Refund.type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='mobile_franchisee_refund.Refund.received_by', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_code', full_name='mobile_franchisee_refund.Refund.received_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_name', full_name='mobile_franchisee_refund.Refund.received_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='mobile_franchisee_refund.Refund.franchisee_id', index=10,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_code', full_name='mobile_franchisee_refund.Refund.franchisee_code', index=11,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_name', full_name='mobile_franchisee_refund.Refund.franchisee_name', index=12,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_date', full_name='mobile_franchisee_refund.Refund.refund_date', index=13,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_franchisee_refund.Refund.status', index=14,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='mobile_franchisee_refund.Refund.process_status', index=15,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='mobile_franchisee_refund.Refund.reject_reason', index=16,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_franchisee_refund.Refund.remark', index=17,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='mobile_franchisee_refund.Refund.reason', index=18,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='mobile_franchisee_refund.Refund.created_at', index=19,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='mobile_franchisee_refund.Refund.updated_at', index=20,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='mobile_franchisee_refund.Refund.created_by', index=21,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='mobile_franchisee_refund.Refund.created_name', index=22,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='mobile_franchisee_refund.Refund.updated_by', index=23,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='mobile_franchisee_refund.Refund.updated_name', index=24,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='mobile_franchisee_refund.Refund.review_by', index=25,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_amount', full_name='mobile_franchisee_refund.Refund.refund_amount', index=26,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pay_amount', full_name='mobile_franchisee_refund.Refund.pay_amount', index=27,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='mobile_franchisee_refund.Refund.attachments', index=28,
      number=32, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='mobile_franchisee_refund.Refund.extends', index=29,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approved_date', full_name='mobile_franchisee_refund.Refund.approved_date', index=30,
      number=34, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_way', full_name='mobile_franchisee_refund.Refund.payment_way', index=31,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trade_company', full_name='mobile_franchisee_refund.Refund.trade_company', index=32,
      number=36, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_name', full_name='mobile_franchisee_refund.Refund.company_name', index=33,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_code', full_name='mobile_franchisee_refund.Refund.company_code', index=34,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='mobile_franchisee_refund.Refund.batch_id', index=35,
      number=39, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=719,
  serialized_end=1563,
)


_REFUNDPRODUCT = _descriptor.Descriptor(
  name='RefundProduct',
  full_name='mobile_franchisee_refund.RefundProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_franchisee_refund.RefundProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_franchisee_refund.RefundProduct.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='mobile_franchisee_refund.RefundProduct.product_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_franchisee_refund.RefundProduct.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_id', full_name='mobile_franchisee_refund.RefundProduct.refund_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='mobile_franchisee_refund.RefundProduct.partner_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='mobile_franchisee_refund.RefundProduct.category_id', index=6,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='mobile_franchisee_refund.RefundProduct.category_name', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='mobile_franchisee_refund.RefundProduct.product_spec', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='mobile_franchisee_refund.RefundProduct.unit_id', index=9,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='mobile_franchisee_refund.RefundProduct.unit_name', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='mobile_franchisee_refund.RefundProduct.unit_spec', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='mobile_franchisee_refund.RefundProduct.unit_rate', index=12,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='mobile_franchisee_refund.RefundProduct.quantity', index=13,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='mobile_franchisee_refund.RefundProduct.tax_price', index=14,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='mobile_franchisee_refund.RefundProduct.cost_price', index=15,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='mobile_franchisee_refund.RefundProduct.tax_rate', index=16,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='mobile_franchisee_refund.RefundProduct.amount', index=17,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='mobile_franchisee_refund.RefundProduct.created_by', index=18,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='mobile_franchisee_refund.RefundProduct.created_name', index=19,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='mobile_franchisee_refund.RefundProduct.updated_by', index=20,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='mobile_franchisee_refund.RefundProduct.updated_name', index=21,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='mobile_franchisee_refund.RefundProduct.created_at', index=22,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='mobile_franchisee_refund.RefundProduct.updated_at', index=23,
      number=29, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1566,
  serialized_end=2107,
)


_GETFREFUNDBYIDREQUEST = _descriptor.Descriptor(
  name='GetFRefundByIdRequest',
  full_name='mobile_franchisee_refund.GetFRefundByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='refund_id', full_name='mobile_franchisee_refund.GetFRefundByIdRequest.refund_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2109,
  serialized_end=2151,
)


_GETMOBILEFREFUNDBYIDRESPONSE = _descriptor.Descriptor(
  name='GetMobileFRefundByIdResponse',
  full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_id', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.main_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_code', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.main_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_type', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.main_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.received_by', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_code', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.received_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_name', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.received_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.franchisee_id', index=10,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_code', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.franchisee_code', index=11,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_name', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.franchisee_name', index=12,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_date', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.refund_date', index=13,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.status', index=14,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.process_status', index=15,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.reject_reason', index=16,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.remark', index=17,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.reason', index=18,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.created_at', index=19,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.updated_at', index=20,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.created_by', index=21,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.created_name', index=22,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.updated_by', index=23,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.updated_name', index=24,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.review_by', index=25,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_amount', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.refund_amount', index=26,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pay_amount', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.pay_amount', index=27,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.attachments', index=28,
      number=32, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.extends', index=29,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approved_date', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.approved_date', index=30,
      number=34, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_way', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.payment_way', index=31,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trade_company', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.trade_company', index=32,
      number=36, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_name', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.company_name', index=33,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_code', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.company_code', index=34,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.batch_id', index=35,
      number=39, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='mobile_franchisee_refund.GetMobileFRefundByIdResponse.products', index=36,
      number=40, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2154,
  serialized_end=3079,
)


_GETMOBILEREFUNDHISTORYREQUEST = _descriptor.Descriptor(
  name='GetMobileRefundHistoryRequest',
  full_name='mobile_franchisee_refund.GetMobileRefundHistoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='refund_id', full_name='mobile_franchisee_refund.GetMobileRefundHistoryRequest.refund_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3081,
  serialized_end=3131,
)


_REFUNDHISTORYRESPONSE = _descriptor.Descriptor(
  name='RefundHistoryResponse',
  full_name='mobile_franchisee_refund.RefundHistoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_franchisee_refund.RefundHistoryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_franchisee_refund.RefundHistoryResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3133,
  serialized_end=3226,
)


_REFUNDHISTORY = _descriptor.Descriptor(
  name='RefundHistory',
  full_name='mobile_franchisee_refund.RefundHistory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_franchisee_refund.RefundHistory.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='mobile_franchisee_refund.RefundHistory.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_id', full_name='mobile_franchisee_refund.RefundHistory.refund_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='mobile_franchisee_refund.RefundHistory.action', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='mobile_franchisee_refund.RefundHistory.created_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='mobile_franchisee_refund.RefundHistory.created_at', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='mobile_franchisee_refund.RefundHistory.created_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3229,
  serialized_end=3401,
)


_DEALMOBILEFREFUNDBYIDREQUEST = _descriptor.Descriptor(
  name='DealMobileFRefundByIdRequest',
  full_name='mobile_franchisee_refund.DealMobileFRefundByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='refund_id', full_name='mobile_franchisee_refund.DealMobileFRefundByIdRequest.refund_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='mobile_franchisee_refund.DealMobileFRefundByIdRequest.action', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_franchisee_refund.DealMobileFRefundByIdRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='mobile_franchisee_refund.DealMobileFRefundByIdRequest.attachments', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='mobile_franchisee_refund.DealMobileFRefundByIdRequest.reject_reason', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3403,
  serialized_end=3528,
)


_DEALMOBILEFREFUNDBYIDRESPONSE = _descriptor.Descriptor(
  name='DealMobileFRefundByIdResponse',
  full_name='mobile_franchisee_refund.DealMobileFRefundByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='mobile_franchisee_refund.DealMobileFRefundByIdResponse.result', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3530,
  serialized_end=3577,
)

_LISTMOBILEFREFUNDREQUEST.fields_by_name['refund_start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTMOBILEFREFUNDREQUEST.fields_by_name['refund_end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTMOBILEFREFUNDREQUEST.fields_by_name['approved_start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTMOBILEFREFUNDREQUEST.fields_by_name['approved_end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTMOBILEFREFUNDRESPONSE.fields_by_name['rows'].message_type = _REFUND
_REFUND.fields_by_name['refund_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_REFUND.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_REFUND.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_REFUND.fields_by_name['approved_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_REFUNDPRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_REFUNDPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETMOBILEFREFUNDBYIDRESPONSE.fields_by_name['refund_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETMOBILEFREFUNDBYIDRESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETMOBILEFREFUNDBYIDRESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETMOBILEFREFUNDBYIDRESPONSE.fields_by_name['approved_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETMOBILEFREFUNDBYIDRESPONSE.fields_by_name['products'].message_type = _REFUNDPRODUCT
_REFUNDHISTORYRESPONSE.fields_by_name['rows'].message_type = _REFUNDHISTORY
_REFUNDHISTORY.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['ListMobileFRefundRequest'] = _LISTMOBILEFREFUNDREQUEST
DESCRIPTOR.message_types_by_name['ListMobileFRefundResponse'] = _LISTMOBILEFREFUNDRESPONSE
DESCRIPTOR.message_types_by_name['Refund'] = _REFUND
DESCRIPTOR.message_types_by_name['RefundProduct'] = _REFUNDPRODUCT
DESCRIPTOR.message_types_by_name['GetFRefundByIdRequest'] = _GETFREFUNDBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetMobileFRefundByIdResponse'] = _GETMOBILEFREFUNDBYIDRESPONSE
DESCRIPTOR.message_types_by_name['GetMobileRefundHistoryRequest'] = _GETMOBILEREFUNDHISTORYREQUEST
DESCRIPTOR.message_types_by_name['RefundHistoryResponse'] = _REFUNDHISTORYRESPONSE
DESCRIPTOR.message_types_by_name['RefundHistory'] = _REFUNDHISTORY
DESCRIPTOR.message_types_by_name['DealMobileFRefundByIdRequest'] = _DEALMOBILEFREFUNDBYIDREQUEST
DESCRIPTOR.message_types_by_name['DealMobileFRefundByIdResponse'] = _DEALMOBILEFREFUNDBYIDRESPONSE
DESCRIPTOR.enum_types_by_name['Status'] = _STATUS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ListMobileFRefundRequest = _reflection.GeneratedProtocolMessageType('ListMobileFRefundRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTMOBILEFREFUNDREQUEST,
  __module__ = 'mobile.mobile_franchisee_refund_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_refund.ListMobileFRefundRequest)
  ))
_sym_db.RegisterMessage(ListMobileFRefundRequest)

ListMobileFRefundResponse = _reflection.GeneratedProtocolMessageType('ListMobileFRefundResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTMOBILEFREFUNDRESPONSE,
  __module__ = 'mobile.mobile_franchisee_refund_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_refund.ListMobileFRefundResponse)
  ))
_sym_db.RegisterMessage(ListMobileFRefundResponse)

Refund = _reflection.GeneratedProtocolMessageType('Refund', (_message.Message,), dict(
  DESCRIPTOR = _REFUND,
  __module__ = 'mobile.mobile_franchisee_refund_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_refund.Refund)
  ))
_sym_db.RegisterMessage(Refund)

RefundProduct = _reflection.GeneratedProtocolMessageType('RefundProduct', (_message.Message,), dict(
  DESCRIPTOR = _REFUNDPRODUCT,
  __module__ = 'mobile.mobile_franchisee_refund_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_refund.RefundProduct)
  ))
_sym_db.RegisterMessage(RefundProduct)

GetFRefundByIdRequest = _reflection.GeneratedProtocolMessageType('GetFRefundByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETFREFUNDBYIDREQUEST,
  __module__ = 'mobile.mobile_franchisee_refund_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_refund.GetFRefundByIdRequest)
  ))
_sym_db.RegisterMessage(GetFRefundByIdRequest)

GetMobileFRefundByIdResponse = _reflection.GeneratedProtocolMessageType('GetMobileFRefundByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETMOBILEFREFUNDBYIDRESPONSE,
  __module__ = 'mobile.mobile_franchisee_refund_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_refund.GetMobileFRefundByIdResponse)
  ))
_sym_db.RegisterMessage(GetMobileFRefundByIdResponse)

GetMobileRefundHistoryRequest = _reflection.GeneratedProtocolMessageType('GetMobileRefundHistoryRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETMOBILEREFUNDHISTORYREQUEST,
  __module__ = 'mobile.mobile_franchisee_refund_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_refund.GetMobileRefundHistoryRequest)
  ))
_sym_db.RegisterMessage(GetMobileRefundHistoryRequest)

RefundHistoryResponse = _reflection.GeneratedProtocolMessageType('RefundHistoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _REFUNDHISTORYRESPONSE,
  __module__ = 'mobile.mobile_franchisee_refund_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_refund.RefundHistoryResponse)
  ))
_sym_db.RegisterMessage(RefundHistoryResponse)

RefundHistory = _reflection.GeneratedProtocolMessageType('RefundHistory', (_message.Message,), dict(
  DESCRIPTOR = _REFUNDHISTORY,
  __module__ = 'mobile.mobile_franchisee_refund_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_refund.RefundHistory)
  ))
_sym_db.RegisterMessage(RefundHistory)

DealMobileFRefundByIdRequest = _reflection.GeneratedProtocolMessageType('DealMobileFRefundByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _DEALMOBILEFREFUNDBYIDREQUEST,
  __module__ = 'mobile.mobile_franchisee_refund_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_refund.DealMobileFRefundByIdRequest)
  ))
_sym_db.RegisterMessage(DealMobileFRefundByIdRequest)

DealMobileFRefundByIdResponse = _reflection.GeneratedProtocolMessageType('DealMobileFRefundByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _DEALMOBILEFREFUNDBYIDRESPONSE,
  __module__ = 'mobile.mobile_franchisee_refund_pb2'
  # @@protoc_insertion_point(class_scope:mobile_franchisee_refund.DealMobileFRefundByIdResponse)
  ))
_sym_db.RegisterMessage(DealMobileFRefundByIdResponse)



_MOBILEFRANCHISEEREFUNDSERVICE = _descriptor.ServiceDescriptor(
  name='MobileFranchiseeRefundService',
  full_name='mobile_franchisee_refund.MobileFranchiseeRefundService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=3645,
  serialized_end=4441,
  methods=[
  _descriptor.MethodDescriptor(
    name='ListMobileFRefund',
    full_name='mobile_franchisee_refund.MobileFranchiseeRefundService.ListMobileFRefund',
    index=0,
    containing_service=None,
    input_type=_LISTMOBILEFREFUNDREQUEST,
    output_type=_LISTMOBILEFREFUNDRESPONSE,
    serialized_options=_b('\202\323\344\223\002)\022\'/api/v2/supply/mobile/franchisee/refund'),
  ),
  _descriptor.MethodDescriptor(
    name='GetMobileFRefundById',
    full_name='mobile_franchisee_refund.MobileFranchiseeRefundService.GetMobileFRefundById',
    index=1,
    containing_service=None,
    input_type=_GETFREFUNDBYIDREQUEST,
    output_type=_GETMOBILEFREFUNDBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\0223/api/v2/supply/mobile/franchisee/refund/{refund_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetMobileRefundHistory',
    full_name='mobile_franchisee_refund.MobileFranchiseeRefundService.GetMobileRefundHistory',
    index=2,
    containing_service=None,
    input_type=_GETMOBILEREFUNDHISTORYREQUEST,
    output_type=_REFUNDHISTORYRESPONSE,
    serialized_options=_b('\202\323\344\223\002=\022;/api/v2/supply/mobile/franchisee/refund/{refund_id}/history'),
  ),
  _descriptor.MethodDescriptor(
    name='DealMobileFRefundById',
    full_name='mobile_franchisee_refund.MobileFranchiseeRefundService.DealMobileFRefundById',
    index=3,
    containing_service=None,
    input_type=_DEALMOBILEFREFUNDBYIDREQUEST,
    output_type=_DEALMOBILEFREFUNDBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0021\",/api/v2/supply/mobile/franchisee/refund/deal:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_MOBILEFRANCHISEEREFUNDSERVICE)

DESCRIPTOR.services_by_name['MobileFranchiseeRefundService'] = _MOBILEFRANCHISEEREFUNDSERVICE

# @@protoc_insertion_point(module_scope)
