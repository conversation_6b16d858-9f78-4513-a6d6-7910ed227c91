# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from mobile import mobile_transfer_pb2 as mobile_dot_mobile__transfer__pb2


class mobileTransferStub(object):
  """Transfer 调拨服务 - 移动端
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateTransfer = channel.unary_unary(
        '/mobile_transfer.mobileTransfer/CreateTransfer',
        request_serializer=mobile_dot_mobile__transfer__pb2.CreateTransferRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__transfer__pb2.Transfer.FromString,
        )
    self.UpdateTransfer = channel.unary_unary(
        '/mobile_transfer.mobileTransfer/UpdateTransfer',
        request_serializer=mobile_dot_mobile__transfer__pb2.UpdateTransferRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__transfer__pb2.UpdateTransferResponse.FromString,
        )
    self.GetTransfer = channel.unary_unary(
        '/mobile_transfer.mobileTransfer/GetTransfer',
        request_serializer=mobile_dot_mobile__transfer__pb2.GetTransferRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__transfer__pb2.GetTransferResponse.FromString,
        )
    self.GetTransferByID = channel.unary_unary(
        '/mobile_transfer.mobileTransfer/GetTransferByID',
        request_serializer=mobile_dot_mobile__transfer__pb2.GetTransferByIDRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__transfer__pb2.GetTransferByIDResponse.FromString,
        )
    self.GetTransferProductByBranchID = channel.unary_unary(
        '/mobile_transfer.mobileTransfer/GetTransferProductByBranchID',
        request_serializer=mobile_dot_mobile__transfer__pb2.GetTransferProductByBranchIDRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__transfer__pb2.GetTransferProductByBranchIDResponse.FromString,
        )
    self.GetTransferRegionByBranchID = channel.unary_unary(
        '/mobile_transfer.mobileTransfer/GetTransferRegionByBranchID',
        request_serializer=mobile_dot_mobile__transfer__pb2.GetTransferRegionByBranchIDRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__transfer__pb2.GetTransferRegionByBranchIDResponse.FromString,
        )
    self.GetTransferProductByTransferID = channel.unary_unary(
        '/mobile_transfer.mobileTransfer/GetTransferProductByTransferID',
        request_serializer=mobile_dot_mobile__transfer__pb2.GetTransferProductByTransferIDRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__transfer__pb2.GetTransferProductByTransferIDResponse.FromString,
        )
    self.ConfirmTransfer = channel.unary_unary(
        '/mobile_transfer.mobileTransfer/ConfirmTransfer',
        request_serializer=mobile_dot_mobile__transfer__pb2.ConfirmTransferRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__transfer__pb2.Transfer.FromString,
        )
    self.DeleteTransferProduct = channel.unary_unary(
        '/mobile_transfer.mobileTransfer/DeleteTransferProduct',
        request_serializer=mobile_dot_mobile__transfer__pb2.DeleteTransferProductRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__transfer__pb2.DeleteTransferProductResponse.FromString,
        )
    self.DeleteTransfer = channel.unary_unary(
        '/mobile_transfer.mobileTransfer/DeleteTransfer',
        request_serializer=mobile_dot_mobile__transfer__pb2.DeleteTransferRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__transfer__pb2.DeleteTransferResponse.FromString,
        )
    self.SubmitTransfer = channel.unary_unary(
        '/mobile_transfer.mobileTransfer/SubmitTransfer',
        request_serializer=mobile_dot_mobile__transfer__pb2.SubmitTransferRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__transfer__pb2.Transfer.FromString,
        )
    self.CancelTransfer = channel.unary_unary(
        '/mobile_transfer.mobileTransfer/CancelTransfer',
        request_serializer=mobile_dot_mobile__transfer__pb2.CancelTransferRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__transfer__pb2.Transfer.FromString,
        )
    self.GetTransferCollect = channel.unary_unary(
        '/mobile_transfer.mobileTransfer/GetTransferCollect',
        request_serializer=mobile_dot_mobile__transfer__pb2.GetTransferCollectRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__transfer__pb2.GetTransferCollectResponse.FromString,
        )
    self.GetTransferCollectDetailed = channel.unary_unary(
        '/mobile_transfer.mobileTransfer/GetTransferCollectDetailed',
        request_serializer=mobile_dot_mobile__transfer__pb2.GetTransferCollectDetailedRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__transfer__pb2.GetTransferCollectDetailedResponse.FromString,
        )
    self.GetTransferLog = channel.unary_unary(
        '/mobile_transfer.mobileTransfer/GetTransferLog',
        request_serializer=mobile_dot_mobile__transfer__pb2.GetTransferLogRequest.SerializeToString,
        response_deserializer=mobile_dot_mobile__transfer__pb2.GetTransferLogResponse.FromString,
        )


class mobileTransferServicer(object):
  """Transfer 调拨服务 - 移动端
  """

  def CreateTransfer(self, request, context):
    """CreateTransfer 创建调拨单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateTransfer(self, request, context):
    """UpdateTransfer 修改调拨单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransfer(self, request, context):
    """GetTransfer 查询调拨单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferByID(self, request, context):
    """GetTransferByID 查询一个调拨单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferProductByBranchID(self, request, context):
    """GetTransferProductByBranchID 取得门店可调拨商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferRegionByBranchID(self, request, context):
    """GetTransferRegionByID 查询相同属性区域门店
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferProductByTransferID(self, request, context):
    """GetTransferProductByTransferID 获取一个调拨单商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ConfirmTransfer(self, request, context):
    """ConfirmTransfer 确认调拨单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteTransferProduct(self, request, context):
    """DeleteTransferProduct 删除调拨单商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteTransfer(self, request, context):
    """DeleteTransfer 删除调拨单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SubmitTransfer(self, request, context):
    """SubmitTransfer 提交调拨单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CancelTransfer(self, request, context):
    """CancelTransfer 取消调拨单13
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferCollect(self, request, context):
    """GetTransferCollect调拨单汇总报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferCollectDetailed(self, request, context):
    """GetTransferCollectDetailed调拨单明细汇总报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferLog(self, request, context):
    """调拨历史记录
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_mobileTransferServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.CreateTransfer,
          request_deserializer=mobile_dot_mobile__transfer__pb2.CreateTransferRequest.FromString,
          response_serializer=mobile_dot_mobile__transfer__pb2.Transfer.SerializeToString,
      ),
      'UpdateTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateTransfer,
          request_deserializer=mobile_dot_mobile__transfer__pb2.UpdateTransferRequest.FromString,
          response_serializer=mobile_dot_mobile__transfer__pb2.UpdateTransferResponse.SerializeToString,
      ),
      'GetTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransfer,
          request_deserializer=mobile_dot_mobile__transfer__pb2.GetTransferRequest.FromString,
          response_serializer=mobile_dot_mobile__transfer__pb2.GetTransferResponse.SerializeToString,
      ),
      'GetTransferByID': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferByID,
          request_deserializer=mobile_dot_mobile__transfer__pb2.GetTransferByIDRequest.FromString,
          response_serializer=mobile_dot_mobile__transfer__pb2.GetTransferByIDResponse.SerializeToString,
      ),
      'GetTransferProductByBranchID': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferProductByBranchID,
          request_deserializer=mobile_dot_mobile__transfer__pb2.GetTransferProductByBranchIDRequest.FromString,
          response_serializer=mobile_dot_mobile__transfer__pb2.GetTransferProductByBranchIDResponse.SerializeToString,
      ),
      'GetTransferRegionByBranchID': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferRegionByBranchID,
          request_deserializer=mobile_dot_mobile__transfer__pb2.GetTransferRegionByBranchIDRequest.FromString,
          response_serializer=mobile_dot_mobile__transfer__pb2.GetTransferRegionByBranchIDResponse.SerializeToString,
      ),
      'GetTransferProductByTransferID': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferProductByTransferID,
          request_deserializer=mobile_dot_mobile__transfer__pb2.GetTransferProductByTransferIDRequest.FromString,
          response_serializer=mobile_dot_mobile__transfer__pb2.GetTransferProductByTransferIDResponse.SerializeToString,
      ),
      'ConfirmTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.ConfirmTransfer,
          request_deserializer=mobile_dot_mobile__transfer__pb2.ConfirmTransferRequest.FromString,
          response_serializer=mobile_dot_mobile__transfer__pb2.Transfer.SerializeToString,
      ),
      'DeleteTransferProduct': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteTransferProduct,
          request_deserializer=mobile_dot_mobile__transfer__pb2.DeleteTransferProductRequest.FromString,
          response_serializer=mobile_dot_mobile__transfer__pb2.DeleteTransferProductResponse.SerializeToString,
      ),
      'DeleteTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteTransfer,
          request_deserializer=mobile_dot_mobile__transfer__pb2.DeleteTransferRequest.FromString,
          response_serializer=mobile_dot_mobile__transfer__pb2.DeleteTransferResponse.SerializeToString,
      ),
      'SubmitTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.SubmitTransfer,
          request_deserializer=mobile_dot_mobile__transfer__pb2.SubmitTransferRequest.FromString,
          response_serializer=mobile_dot_mobile__transfer__pb2.Transfer.SerializeToString,
      ),
      'CancelTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.CancelTransfer,
          request_deserializer=mobile_dot_mobile__transfer__pb2.CancelTransferRequest.FromString,
          response_serializer=mobile_dot_mobile__transfer__pb2.Transfer.SerializeToString,
      ),
      'GetTransferCollect': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferCollect,
          request_deserializer=mobile_dot_mobile__transfer__pb2.GetTransferCollectRequest.FromString,
          response_serializer=mobile_dot_mobile__transfer__pb2.GetTransferCollectResponse.SerializeToString,
      ),
      'GetTransferCollectDetailed': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferCollectDetailed,
          request_deserializer=mobile_dot_mobile__transfer__pb2.GetTransferCollectDetailedRequest.FromString,
          response_serializer=mobile_dot_mobile__transfer__pb2.GetTransferCollectDetailedResponse.SerializeToString,
      ),
      'GetTransferLog': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferLog,
          request_deserializer=mobile_dot_mobile__transfer__pb2.GetTransferLogRequest.FromString,
          response_serializer=mobile_dot_mobile__transfer__pb2.GetTransferLogResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'mobile_transfer.mobileTransfer', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
