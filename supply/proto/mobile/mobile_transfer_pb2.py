# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: mobile/mobile_transfer.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='mobile/mobile_transfer.proto',
  package='mobile_transfer',
  syntax='proto3',
  serialized_options=_b('Z\021./mobile_transfer'),
  serialized_pb=_b('\n\x1cmobile/mobile_transfer.proto\x12\x0fmobile_transfer\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1cgoogle/protobuf/struct.proto\"\x13\n\x04Pong\x12\x0b\n\x03msg\x18\x01 \x01(\t\"\xa0\x04\n\x12GetTransferRequest\x12\x15\n\rinclude_total\x18\x01 \x01(\x08\x12:\n\x06status\x18\x02 \x03(\x0e\x32*.mobile_transfer.GetTransferRequest.STATUS\x12\x17\n\x0fshipping_stores\x18\x03 \x03(\x04\x12\x18\n\x10receiving_stores\x18\x04 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x05 \x03(\x04\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12\r\n\x05limit\x18\x07 \x01(\r\x12.\n\nstart_date\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x63ode\x18\x0b \x01(\t\x12\x0b\n\x03ids\x18\x0c \x03(\x04\x12\r\n\x05order\x18\x0f \x01(\t\x12\x0c\n\x04sort\x18\x10 \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x11 \x01(\t\x12\r\n\x05types\x18\x13 \x03(\t\x12\x10\n\x08sub_type\x18\x14 \x01(\t\x12\x1b\n\x13receiving_positions\x18\x15 \x03(\x04\x12\x1a\n\x12shipping_positions\x18\x16 \x03(\x04\"K\n\x06STATUS\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tSUBMITTED\x10\x02\x12\r\n\tCONFIRMED\x10\x03\x12\r\n\tCANCELLED\x10\x04\"\xbe\x07\n\x08Transfer\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x03 \x01(\x04\x12\x16\n\x0eshipping_store\x18\x05 \x01(\x04\x12\x1b\n\x13shipping_store_name\x18\x06 \x01(\t\x12\x17\n\x0freceiving_store\x18\x08 \x01(\x04\x12\x1c\n\x14receiving_store_name\x18\x07 \x01(\t\x12\x0c\n\x04\x63ode\x18\t \x01(\t\x12\x0e\n\x06remark\x18\r \x01(\t\x12\x30\n\x06status\x18\x11 \x01(\x0e\x32 .mobile_transfer.Transfer.STATUS\x12\x31\n\x0eprocess_status\x18\x12 \x01(\x0e\x32\x19.mobile_transfer.P_STATUS\x12\x12\n\ncreated_by\x18\x14 \x01(\x04\x12\x12\n\nupdated_by\x18\x15 \x01(\x04\x12\x31\n\rtransfer_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0ereceiving_date\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rshipping_date\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\x1d \x01(\t\x12\x14\n\x0cupdated_name\x18\x1e \x01(\t\x12\x13\n\x0b\x62ranch_type\x18! \x01(\t\x12\x10\n\x08sub_type\x18\" \x01(\t\x12\x0c\n\x04type\x18\x10 \x01(\t\x12\x1a\n\x12receiving_position\x18# \x01(\x04\x12\x1f\n\x17receiving_position_name\x18$ \x01(\t\x12\x19\n\x11shipping_position\x18% \x01(\x04\x12\x1e\n\x16shipping_position_name\x18& \x01(\t\x12\x18\n\x10sub_account_type\x18\' \x01(\t\x12\x0f\n\x07\x65xtends\x18( \x01(\t\x12\x14\n\x0ctotal_amount\x18) \x01(\x01\x12\x1a\n\x12total_sales_amount\x18* \x01(\x01\"K\n\x06STATUS\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tSUBMITTED\x10\x02\x12\r\n\tCONFIRMED\x10\x03\x12\r\n\tCANCELLED\x10\x04\"M\n\x13GetTransferResponse\x12\'\n\x04rows\x18\x01 \x03(\x0b\x32\x19.mobile_transfer.Transfer\x12\r\n\x05total\x18\x02 \x01(\r\"-\n\x16GetTransferByIDRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\"\xdc\x07\n\x17GetTransferByIDResponse\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x03 \x01(\x04\x12\x16\n\x0eshipping_store\x18\x05 \x01(\x04\x12\x1b\n\x13shipping_store_name\x18\x06 \x01(\t\x12\x17\n\x0freceiving_store\x18\x07 \x01(\x04\x12\x1c\n\x14receiving_store_name\x18\x08 \x01(\t\x12\x0c\n\x04\x63ode\x18\t \x01(\t\x12\x0e\n\x06remark\x18\r \x01(\t\x12?\n\x06status\x18\x11 \x01(\x0e\x32/.mobile_transfer.GetTransferByIDResponse.STATUS\x12\x31\n\x0eprocess_status\x18\x12 \x01(\x0e\x32\x19.mobile_transfer.P_STATUS\x12\x12\n\ncreated_by\x18\x14 \x01(\x04\x12\x12\n\nupdated_by\x18\x15 \x01(\x04\x12\x31\n\rtransfer_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0ereceiving_date\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rshipping_date\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\x1c \x01(\t\x12\x14\n\x0cupdated_name\x18\x1d \x01(\t\x12\x13\n\x0b\x62ranch_type\x18  \x01(\t\x12\x10\n\x08sub_type\x18\" \x01(\t\x12\x0c\n\x04type\x18\x10 \x01(\t\x12\x1a\n\x12receiving_position\x18# \x01(\x04\x12\x1f\n\x17receiving_position_name\x18$ \x01(\t\x12\x19\n\x11shipping_position\x18% \x01(\x04\x12\x1e\n\x16shipping_position_name\x18& \x01(\t\x12\x18\n\x10sub_account_type\x18\' \x01(\t\x12\x0f\n\x07\x65xtends\x18! \x01(\t\x12\x14\n\x0ctotal_amount\x18) \x01(\x01\x12\x1a\n\x12total_sales_amount\x18* \x01(\x01\"K\n\x06STATUS\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tSUBMITTED\x10\x02\x12\r\n\tCONFIRMED\x10\x03\x12\r\n\tCANCELLED\x10\x04\"\xd9\x01\n#GetTransferProductByBranchIDRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x15\n\rinclude_total\x18\x02 \x01(\x08\x12\r\n\x05order\x18\x03 \x01(\t\x12\x0e\n\x06offset\x18\x04 \x01(\r\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x15\n\rsearch_fields\x18\x06 \x01(\t\x12\x0e\n\x06search\x18\x07 \x01(\t\x12\x14\n\x0c\x63\x61tegory_ids\x18\x08 \x03(\x04\x12\x10\n\x08order_by\x18\t \x01(\t\x12\x0c\n\x04sort\x18\n \x01(\t\"\xa2\x01\n\x13TransferProductUnit\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08quantity\x18\x02 \x01(\x01\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x10\n\x08tax_rate\x18\x04 \x01(\x01\x12\x11\n\ttax_price\x18\x05 \x01(\x01\x12\x12\n\ncost_price\x18\x06 \x01(\x01\x12\x13\n\x0bsales_price\x18\x07 \x01(\x01\x12\x11\n\tunit_rate\x18\x08 \x01(\x01\"\x86\x02\n\x15SelectTransferProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x03 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12\x32\n\x04unit\x18\x05 \x03(\x0b\x32$.mobile_transfer.TransferProductUnit\x12\x0f\n\x07\x62\x61rcode\x18\x06 \x03(\t\x12\x0c\n\x04spec\x18\x07 \x01(\t\x12\x15\n\rcategory_name\x18\x08 \x01(\t\x12\x12\n\nmodel_name\x18\t \x01(\t\x12\x1a\n\x12real_inventory_qty\x18\n \x01(\x01\"\xb5\x01\n$GetTransferProductByBranchIDResponse\x12\x34\n\x04rows\x18\x01 \x03(\x0b\x32&.mobile_transfer.SelectTransferProduct\x12\r\n\x05total\x18\x02 \x01(\r\x12H\n\x18inventory_unchanged_rows\x18\x03 \x03(\x0b\x32&.mobile_transfer.SelectTransferProduct\"C\n\"GetTransferRegionByBranchIDRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"G\n\x0cRegionBranch\x12\x0f\n\x07\x61\x64\x64ress\x18\x01 \x01(\t\x12\n\n\x02id\x18\x02 \x01(\x04\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\"R\n#GetTransferRegionByBranchIDResponse\x12+\n\x04rows\x18\x01 \x03(\x0b\x32\x1d.mobile_transfer.RegionBranch\"\x81\x01\n%GetTransferProductByTransferIDRequest\x12\x15\n\rinclude_total\x18\x01 \x01(\x08\x12\r\n\x05order\x18\x02 \x01(\t\x12\x0e\n\x06offset\x18\x03 \x01(\r\x12\r\n\x05limit\x18\x04 \x01(\r\x12\x13\n\x0btransfer_id\x18\x05 \x01(\x04\"\xc1\x07\n\x0fTransferProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x13\n\x0btransfer_id\x18\x03 \x01(\x04\x12\x0f\n\x07unit_id\x18\x04 \x01(\x04\x12\x11\n\tunit_name\x18\x05 \x01(\t\x12\x11\n\tunit_spec\x18\x06 \x01(\t\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x07 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x08 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\t \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\n \x01(\t\x12$\n\x1c\x61\x63\x63ounting_received_quantity\x18\x0b \x01(\x01\x12\x13\n\x0bitem_number\x18\x0e \x01(\r\x12\x14\n\x0cproduct_code\x18\x10 \x01(\t\x12\x14\n\x0cproduct_name\x18\x11 \x01(\t\x12\x10\n\x08quantity\x18\x12 \x01(\x01\x12\x17\n\x0freceiving_store\x18\x13 \x01(\x04\x12\x16\n\x0eshipping_store\x18\x14 \x01(\x04\x12\x12\n\ncreated_by\x18\x15 \x01(\x04\x12\x12\n\nupdated_by\x18\x16 \x01(\x04\x12\x31\n\rtransfer_date\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07\x65xtends\x18\x1a \x01(\t\x12\x12\n\npartner_id\x18\x1b \x01(\x04\x12\x0f\n\x07user_id\x18\x1c \x01(\x04\x12#\n\x1b\x63onfirmed_received_quantity\x18\x1d \x01(\x01\x12\x14\n\x0c\x63reated_name\x18\x1e \x01(\t\x12\x14\n\x0cupdated_name\x18\x1f \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18  \x01(\x04\x12\x15\n\rcategory_name\x18! \x01(\t\x12\x12\n\nmodel_name\x18\" \x01(\t\x12\x10\n\x08tax_rate\x18# \x01(\x01\x12\x11\n\ttax_price\x18$ \x01(\x01\x12\x12\n\ncost_price\x18% \x01(\x01\x12\x0e\n\x06\x61mount\x18& \x01(\x01\x12\x13\n\x0bsales_price\x18\' \x01(\x01\x12\x14\n\x0csales_amount\x18( \x01(\x01\x12\x11\n\tunit_rate\x18) \x01(\x01\x12\x1a\n\x12real_inventory_qty\x18* \x01(\x01\"g\n&GetTransferProductByTransferIDResponse\x12.\n\x04rows\x18\x01 \x03(\x0b\x32 .mobile_transfer.TransferProduct\x12\r\n\x05total\x18\x02 \x01(\r\"\xc0\x01\n\x13PostTransferProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x0f\n\x07unit_id\x18\x02 \x01(\x04\x12\x10\n\x08quantity\x18\x03 \x01(\x01\x12\x10\n\x08tax_rate\x18\x04 \x01(\x01\x12\x11\n\ttax_price\x18\x05 \x01(\x01\x12\x12\n\ncost_price\x18\x06 \x01(\x01\x12\x0e\n\x06\x61mount\x18\x07 \x01(\x01\x12\x13\n\x0bsales_price\x18\x08 \x01(\x01\x12\x14\n\x0csales_amount\x18\t \x01(\x01\"\xd1\x03\n\x15\x43reateTransferRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x16\n\x0eshipping_store\x18\x02 \x01(\x04\x12\x17\n\x0freceiving_store\x18\x03 \x01(\x04\x12\x36\n\x08products\x18\x04 \x03(\x0b\x32$.mobile_transfer.PostTransferProduct\x12\x0e\n\x06remark\x18\x05 \x01(\t\x12\x31\n\rshipping_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\rtransfer_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\x08 \x01(\t\x12\x0c\n\x04type\x18\n \x01(\t\x12\x10\n\x08sub_type\x18\x0b \x01(\t\x12\x1a\n\x12receiving_position\x18\x0c \x01(\x04\x12\x19\n\x11shipping_position\x18\r \x01(\x04\x12\x1f\n\x17receiving_position_name\x18\x0e \x01(\t\x12\x1e\n\x16shipping_position_name\x18\x0f \x01(\t\x12\x18\n\x10sub_account_type\x18\x10 \x01(\t\"\xdc\x01\n\x15UpdateTransferRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\x12\x36\n\x08products\x18\x02 \x03(\x0b\x32$.mobile_transfer.PostTransferProduct\x12\x0e\n\x06remark\x18\x03 \x01(\t\x12\x31\n\rtransfer_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\x05 \x01(\t\x12\x0c\n\x04type\x18\n \x01(\t\x12\x10\n\x08sub_type\x18\x0b \x01(\t\"(\n\x16UpdateTransferResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"f\n\x1a\x43onfirmPostTransferProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x0f\n\x07unit_id\x18\x02 \x01(\x04\x12#\n\x1b\x63onfirmed_received_quantity\x18\x03 \x01(\x01\"\xac\x01\n\x16\x43onfirmTransferRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\x12\x10\n\x08receiver\x18\x02 \x01(\x04\x12\x17\n\x0freceiving_store\x18\x03 \x01(\x04\x12=\n\x08products\x18\x04 \x03(\x0b\x32+.mobile_transfer.ConfirmPostTransferProduct\x12\x13\n\x0b\x62ranch_type\x18\x05 \x01(\t\"@\n\x1c\x44\x65leteTransferProductRequest\x12\x0b\n\x03ids\x18\x01 \x03(\x04\x12\x13\n\x0btransfer_id\x18\x02 \x01(\x04\"/\n\x1d\x44\x65leteTransferProductResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\",\n\x15\x44\x65leteTransferRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\"(\n\x16\x44\x65leteTransferResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\xa4\x01\n\x15SubmitTransferRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\x12\x10\n\x08receiver\x18\x02 \x01(\x04\x12\x17\n\x0freceiving_store\x18\x03 \x01(\x04\x12\x36\n\x08products\x18\x04 \x03(\x0b\x32$.mobile_transfer.PostTransferProduct\x12\x13\n\x0b\x62ranch_type\x18\x05 \x01(\t\"\x98\x01\n\x1eSubmitTransferReceivingRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\x12\x10\n\x08receiver\x18\x02 \x01(\x04\x12\x17\n\x0freceiving_store\x18\x03 \x01(\x04\x12\x36\n\x08products\x18\x04 \x03(\x0b\x32$.mobile_transfer.PostTransferProduct\",\n\x15\x43\x61ncelTransferRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\"\xa2\x03\n\x19GetTransferCollectRequest\x12\x0e\n\x06st_ids\x18\x01 \x03(\x04\x12\x11\n\tstore_ids\x18\x02 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x03 \x03(\x04\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12.\n\nstart_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x07 \x01(\r\x12\x0e\n\x06offset\x18\x08 \x01(\r\x12\x15\n\rinclude_total\x18\t \x01(\x08\x12\r\n\x05is_in\x18\n \x01(\x08\x12\r\n\x05order\x18\x0b \x01(\t\x12\x0c\n\x04sort\x18\x0c \x01(\t\x12\x0c\n\x04\x63ode\x18\r \x01(\t\x12\x10\n\x08jde_code\x18\x0e \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0f \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x10 \x01(\t\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x0c\n\x04type\x18\x12 \x01(\t\x12\x10\n\x08sub_type\x18\x13 \x01(\t\"\xac\x06\n\x0fTransferCollect\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x05 \x01(\x04\x12\x15\n\rcategory_name\x18\x06 \x01(\t\x12\x30\n\x0f\x63\x61tegory_parent\x18\x0b \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x14\n\x0cproduct_code\x18\x0c \x01(\t\x12\x12\n\nproduct_id\x18\r \x01(\x04\x12\x14\n\x0cproduct_name\x18\x0e \x01(\t\x12\x15\n\rproduct_price\x18\x0f \x01(\x01\x12\x17\n\x0freceiving_store\x18\x10 \x01(\x04\x12\x1c\n\x14receiving_store_code\x18\x11 \x01(\t\x12\x1d\n\x15receiving_store_us_id\x18\x12 \x01(\t\x12\x1c\n\x14receiving_store_name\x18\x13 \x01(\t\x12\x10\n\x08quantity\x18\x14 \x01(\x01\x12\x16\n\x0eshipping_store\x18\x15 \x01(\x04\x12\x1b\n\x13shipping_store_code\x18\x16 \x01(\t\x12\x1b\n\x13shipping_store_name\x18\x17 \x01(\t\x12\x1c\n\x14shipping_store_us_id\x18\x18 \x01(\t\x12\x0f\n\x07unit_id\x18\x19 \x01(\x04\x12\x11\n\tunit_name\x18\x1a \x01(\t\x12\x14\n\x0cproduct_spec\x18\x1b \x01(\t\x12\r\n\x05price\x18\x1c \x01(\x01\x12\x1a\n\x12receiving_position\x18\x1d \x01(\x04\x12\x1f\n\x17receiving_position_code\x18\x1e \x01(\t\x12\x1f\n\x17receiving_position_name\x18\x1f \x01(\t\x12\x19\n\x11shipping_position\x18  \x01(\x04\x12\x1e\n\x16shipping_position_code\x18! \x01(\t\x12\x1e\n\x16shipping_position_name\x18\" \x01(\t\"\\\n\x14TransferCollectTotal\x12\r\n\x05\x63ount\x18\x01 \x01(\x04\x12\x1f\n\x17sum_accounting_quantity\x18\x02 \x01(\x01\x12\x14\n\x0csum_quantity\x18\x03 \x01(\x01\"\x82\x01\n\x1aGetTransferCollectResponse\x12.\n\x04rows\x18\x01 \x03(\x0b\x32 .mobile_transfer.TransferCollect\x12\x34\n\x05total\x18\x02 \x01(\x0b\x32%.mobile_transfer.TransferCollectTotal\"\xaa\x03\n!GetTransferCollectDetailedRequest\x12\x0e\n\x06st_ids\x18\x01 \x03(\x04\x12\x11\n\tstore_ids\x18\x02 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x03 \x03(\x04\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12.\n\nstart_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x07 \x01(\r\x12\x0e\n\x06offset\x18\x08 \x01(\r\x12\x15\n\rinclude_total\x18\t \x01(\x08\x12\r\n\x05is_in\x18\n \x01(\x08\x12\r\n\x05order\x18\x0b \x01(\t\x12\x0c\n\x04sort\x18\x0c \x01(\t\x12\x0c\n\x04\x63ode\x18\r \x01(\t\x12\x10\n\x08jde_code\x18\x0e \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0f \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x10 \x01(\t\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x0c\n\x04type\x18\x12 \x01(\t\x12\x10\n\x08sub_type\x18\x13 \x01(\t\"\x86\x07\n\x17TransferCollectDetailed\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x05 \x01(\x04\x12\x15\n\rcategory_name\x18\x06 \x01(\t\x12\n\n\x02id\x18\x07 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x08 \x01(\t\x12\x12\n\nproduct_id\x18\t \x01(\x04\x12\x14\n\x0cproduct_name\x18\n \x01(\t\x12\x14\n\x0cproduct_spec\x18\x0b \x01(\t\x12\x10\n\x08quantity\x18\x0c \x01(\x01\x12\x17\n\x0freceiving_store\x18\r \x01(\x04\x12\x1c\n\x14receiving_store_code\x18\x0e \x01(\t\x12\x1c\n\x14receiving_store_name\x18\x0f \x01(\t\x12\x16\n\x0eshipping_store\x18\x10 \x01(\x04\x12\x1b\n\x13shipping_store_code\x18\x11 \x01(\t\x12\x1b\n\x13shipping_store_name\x18\x12 \x01(\t\x12\x0e\n\x06status\x18\x13 \x01(\t\x12\x15\n\rtransfer_code\x18\x14 \x01(\t\x12\x31\n\rtransfer_date\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0btransfer_id\x18\x16 \x01(\x04\x12\x0f\n\x07unit_id\x18\x17 \x01(\x04\x12\x11\n\tunit_name\x18\x18 \x01(\t\x12\x1c\n\x14shipping_store_us_id\x18\x19 \x01(\t\x12\x1d\n\x15receiving_store_us_id\x18\x1a \x01(\t\x12\r\n\x05price\x18\x1b \x01(\x01\x12\x10\n\x08jde_code\x18! \x01(\t\x12\x0c\n\x04\x63ode\x18\" \x01(\t\x12\x1a\n\x12receiving_position\x18( \x01(\x04\x12\x1f\n\x17receiving_position_code\x18) \x01(\t\x12\x1f\n\x17receiving_position_name\x18* \x01(\t\x12\x19\n\x11shipping_position\x18+ \x01(\x04\x12\x1e\n\x16shipping_position_code\x18, \x01(\t\x12\x1e\n\x16shipping_position_name\x18- \x01(\t\"\x92\x01\n\"GetTransferCollectDetailedResponse\x12\x36\n\x04rows\x18\x01 \x03(\x0b\x32(.mobile_transfer.TransferCollectDetailed\x12\x34\n\x05total\x18\x02 \x01(\x0b\x32%.mobile_transfer.TransferCollectTotal\",\n\x15GetTransferLogRequest\x12\x13\n\x0btransfer_id\x18\x01 \x01(\x04\"K\n\x16GetTransferLogResponse\x12\"\n\x04rows\x18\x01 \x03(\x0b\x32\x14.mobile_transfer.Log\x12\r\n\x05total\x18\x02 \x01(\x04\"\x8b\x01\n\x03Log\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x12\n\ncreated_by\x18\x03 \x01(\x04\x12.\n\ncreated_at\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06reason\x18\x05 \x01(\t\x12\x14\n\x0c\x63reated_name\x18\x06 \x01(\t*K\n\x08P_STATUS\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06INITED\x10\x01\x12\x0e\n\nPROCESSING\x10\x02\x12\r\n\tSUCCESSED\x10\x03\x12\n\n\x06\x46\x41ILED\x10\x04\x32\x8a\x14\n\x0emobileTransfer\x12\x83\x01\n\x0e\x43reateTransfer\x12&.mobile_transfer.CreateTransferRequest\x1a\x19.mobile_transfer.Transfer\".\x82\xd3\xe4\x93\x02(\"#/api/v2/supply/mobile/transfer/main:\x01*\x12\xa6\x01\n\x0eUpdateTransfer\x12&.mobile_transfer.UpdateTransferRequest\x1a\'.mobile_transfer.UpdateTransferResponse\"C\x82\xd3\xe4\x93\x02=\"8/api/v2/supply/mobile/transfer/main/{transfer_id}/update:\x01*\x12\x80\x01\n\x0bGetTransfer\x12#.mobile_transfer.GetTransferRequest\x1a$.mobile_transfer.GetTransferResponse\"&\x82\xd3\xe4\x93\x02 \x12\x1e/api/v2/supply/mobile/transfer\x12\x9a\x01\n\x0fGetTransferByID\x12\'.mobile_transfer.GetTransferByIDRequest\x1a(.mobile_transfer.GetTransferByIDResponse\"4\x82\xd3\xe4\x93\x02.\x12,/api/v2/supply/mobile/transfer/{transfer_id}\x12\xcc\x01\n\x1cGetTransferProductByBranchID\x12\x34.mobile_transfer.GetTransferProductByBranchIDRequest\x1a\x35.mobile_transfer.GetTransferProductByBranchIDResponse\"?\x82\xd3\xe4\x93\x02\x39\x12\x37/api/v2/supply/mobile/transfer/store/{store_id}/product\x12\xc8\x01\n\x1bGetTransferRegionByBranchID\x12\x33.mobile_transfer.GetTransferRegionByBranchIDRequest\x1a\x34.mobile_transfer.GetTransferRegionByBranchIDResponse\">\x82\xd3\xe4\x93\x02\x38\x12\x36/api/v2/supply/mobile/transfer/store/{store_id}/region\x12\xcf\x01\n\x1eGetTransferProductByTransferID\x12\x36.mobile_transfer.GetTransferProductByTransferIDRequest\x1a\x37.mobile_transfer.GetTransferProductByTransferIDResponse\"<\x82\xd3\xe4\x93\x02\x36\x12\x34/api/v2/supply/mobile/transfer/{transfer_id}/product\x12\x9b\x01\n\x0f\x43onfirmTransfer\x12\'.mobile_transfer.ConfirmTransferRequest\x1a\x19.mobile_transfer.Transfer\"D\x82\xd3\xe4\x93\x02>\"9/api/v2/supply/mobile/transfer/main/{transfer_id}/confirm:\x01*\x12\xbe\x01\n\x15\x44\x65leteTransferProduct\x12-.mobile_transfer.DeleteTransferProductRequest\x1a..mobile_transfer.DeleteTransferProductResponse\"F\x82\xd3\xe4\x93\x02@\";/api/v2/supply/mobile/transfer/product/{transfer_id}/delete:\x01*\x12\xa6\x01\n\x0e\x44\x65leteTransfer\x12&.mobile_transfer.DeleteTransferRequest\x1a\'.mobile_transfer.DeleteTransferResponse\"C\x82\xd3\xe4\x93\x02=\"8/api/v2/supply/mobile/transfer/main/{transfer_id}/delete:\x01*\x12\x98\x01\n\x0eSubmitTransfer\x12&.mobile_transfer.SubmitTransferRequest\x1a\x19.mobile_transfer.Transfer\"C\x82\xd3\xe4\x93\x02=\"8/api/v2/supply/mobile/transfer/main/{transfer_id}/submit:\x01*\x12\x98\x01\n\x0e\x43\x61ncelTransfer\x12&.mobile_transfer.CancelTransferRequest\x1a\x19.mobile_transfer.Transfer\"C\x82\xd3\xe4\x93\x02=\"8/api/v2/supply/mobile/transfer/main/{transfer_id}/cancel:\x01*\x12\xa4\x01\n\x12GetTransferCollect\x12*.mobile_transfer.GetTransferCollectRequest\x1a+.mobile_transfer.GetTransferCollectResponse\"5\x82\xd3\xe4\x93\x02/\x12-/api/v2/supply/mobile/transfer/collect/report\x12\xb9\x01\n\x1aGetTransferCollectDetailed\x12\x32.mobile_transfer.GetTransferCollectDetailedRequest\x1a\x33.mobile_transfer.GetTransferCollectDetailedResponse\"2\x82\xd3\xe4\x93\x02,\x12*/api/v2/supply/mobile/transfer/bi/detailed\x12\x9b\x01\n\x0eGetTransferLog\x12&.mobile_transfer.GetTransferLogRequest\x1a\'.mobile_transfer.GetTransferLogResponse\"8\x82\xd3\xe4\x93\x02\x32\x12\x30/api/v2/supply/mobile/transfer/{transfer_id}/logB\x13Z\x11./mobile_transferb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,google_dot_protobuf_dot_struct__pb2.DESCRIPTOR,])

_P_STATUS = _descriptor.EnumDescriptor(
  name='P_STATUS',
  full_name='mobile_transfer.P_STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PROCESSING', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUCCESSED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FAILED', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=10038,
  serialized_end=10113,
)
_sym_db.RegisterEnumDescriptor(_P_STATUS)

P_STATUS = enum_type_wrapper.EnumTypeWrapper(_P_STATUS)
NONE = 0
INITED = 1
PROCESSING = 2
SUCCESSED = 3
FAILED = 4


_GETTRANSFERREQUEST_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='mobile_transfer.GetTransferRequest.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=633,
  serialized_end=708,
)
_sym_db.RegisterEnumDescriptor(_GETTRANSFERREQUEST_STATUS)

_TRANSFER_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='mobile_transfer.Transfer.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=633,
  serialized_end=708,
)
_sym_db.RegisterEnumDescriptor(_TRANSFER_STATUS)

_GETTRANSFERBYIDRESPONSE_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='mobile_transfer.GetTransferByIDResponse.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=633,
  serialized_end=708,
)
_sym_db.RegisterEnumDescriptor(_GETTRANSFERBYIDRESPONSE_STATUS)


_PONG = _descriptor.Descriptor(
  name='Pong',
  full_name='mobile_transfer.Pong',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='msg', full_name='mobile_transfer.Pong.msg', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=142,
  serialized_end=161,
)


_GETTRANSFERREQUEST = _descriptor.Descriptor(
  name='GetTransferRequest',
  full_name='mobile_transfer.GetTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='include_total', full_name='mobile_transfer.GetTransferRequest.include_total', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_transfer.GetTransferRequest.status', index=1,
      number=2, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_stores', full_name='mobile_transfer.GetTransferRequest.shipping_stores', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_stores', full_name='mobile_transfer.GetTransferRequest.receiving_stores', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='mobile_transfer.GetTransferRequest.product_ids', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_transfer.GetTransferRequest.offset', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_transfer.GetTransferRequest.limit', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='mobile_transfer.GetTransferRequest.start_date', index=7,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='mobile_transfer.GetTransferRequest.end_date', index=8,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_transfer.GetTransferRequest.code', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='mobile_transfer.GetTransferRequest.ids', index=10,
      number=12, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_transfer.GetTransferRequest.order', index=11,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='mobile_transfer.GetTransferRequest.sort', index=12,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_transfer.GetTransferRequest.branch_type', index=13,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='types', full_name='mobile_transfer.GetTransferRequest.types', index=14,
      number=19, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='mobile_transfer.GetTransferRequest.sub_type', index=15,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_positions', full_name='mobile_transfer.GetTransferRequest.receiving_positions', index=16,
      number=21, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_positions', full_name='mobile_transfer.GetTransferRequest.shipping_positions', index=17,
      number=22, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _GETTRANSFERREQUEST_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=164,
  serialized_end=708,
)


_TRANSFER = _descriptor.Descriptor(
  name='Transfer',
  full_name='mobile_transfer.Transfer',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_transfer.Transfer.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='mobile_transfer.Transfer.partner_id', index=1,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='mobile_transfer.Transfer.shipping_store', index=2,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_name', full_name='mobile_transfer.Transfer.shipping_store_name', index=3,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='mobile_transfer.Transfer.receiving_store', index=4,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_name', full_name='mobile_transfer.Transfer.receiving_store_name', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_transfer.Transfer.code', index=6,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_transfer.Transfer.remark', index=7,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_transfer.Transfer.status', index=8,
      number=17, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='mobile_transfer.Transfer.process_status', index=9,
      number=18, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='mobile_transfer.Transfer.created_by', index=10,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='mobile_transfer.Transfer.updated_by', index=11,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='mobile_transfer.Transfer.transfer_date', index=12,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_date', full_name='mobile_transfer.Transfer.receiving_date', index=13,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_date', full_name='mobile_transfer.Transfer.shipping_date', index=14,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='mobile_transfer.Transfer.created_at', index=15,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='mobile_transfer.Transfer.updated_at', index=16,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='mobile_transfer.Transfer.created_name', index=17,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='mobile_transfer.Transfer.updated_name', index=18,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_transfer.Transfer.branch_type', index=19,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='mobile_transfer.Transfer.sub_type', index=20,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='mobile_transfer.Transfer.type', index=21,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position', full_name='mobile_transfer.Transfer.receiving_position', index=22,
      number=35, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_name', full_name='mobile_transfer.Transfer.receiving_position_name', index=23,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position', full_name='mobile_transfer.Transfer.shipping_position', index=24,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_name', full_name='mobile_transfer.Transfer.shipping_position_name', index=25,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='mobile_transfer.Transfer.sub_account_type', index=26,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='mobile_transfer.Transfer.extends', index=27,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='mobile_transfer.Transfer.total_amount', index=28,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_sales_amount', full_name='mobile_transfer.Transfer.total_sales_amount', index=29,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _TRANSFER_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=711,
  serialized_end=1669,
)


_GETTRANSFERRESPONSE = _descriptor.Descriptor(
  name='GetTransferResponse',
  full_name='mobile_transfer.GetTransferResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_transfer.GetTransferResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_transfer.GetTransferResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1671,
  serialized_end=1748,
)


_GETTRANSFERBYIDREQUEST = _descriptor.Descriptor(
  name='GetTransferByIDRequest',
  full_name='mobile_transfer.GetTransferByIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_transfer.GetTransferByIDRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1750,
  serialized_end=1795,
)


_GETTRANSFERBYIDRESPONSE = _descriptor.Descriptor(
  name='GetTransferByIDResponse',
  full_name='mobile_transfer.GetTransferByIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_transfer.GetTransferByIDResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='mobile_transfer.GetTransferByIDResponse.partner_id', index=1,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='mobile_transfer.GetTransferByIDResponse.shipping_store', index=2,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_name', full_name='mobile_transfer.GetTransferByIDResponse.shipping_store_name', index=3,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='mobile_transfer.GetTransferByIDResponse.receiving_store', index=4,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_name', full_name='mobile_transfer.GetTransferByIDResponse.receiving_store_name', index=5,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_transfer.GetTransferByIDResponse.code', index=6,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_transfer.GetTransferByIDResponse.remark', index=7,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_transfer.GetTransferByIDResponse.status', index=8,
      number=17, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='mobile_transfer.GetTransferByIDResponse.process_status', index=9,
      number=18, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='mobile_transfer.GetTransferByIDResponse.created_by', index=10,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='mobile_transfer.GetTransferByIDResponse.updated_by', index=11,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='mobile_transfer.GetTransferByIDResponse.transfer_date', index=12,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_date', full_name='mobile_transfer.GetTransferByIDResponse.receiving_date', index=13,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_date', full_name='mobile_transfer.GetTransferByIDResponse.shipping_date', index=14,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='mobile_transfer.GetTransferByIDResponse.created_at', index=15,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='mobile_transfer.GetTransferByIDResponse.updated_at', index=16,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='mobile_transfer.GetTransferByIDResponse.created_name', index=17,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='mobile_transfer.GetTransferByIDResponse.updated_name', index=18,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_transfer.GetTransferByIDResponse.branch_type', index=19,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='mobile_transfer.GetTransferByIDResponse.sub_type', index=20,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='mobile_transfer.GetTransferByIDResponse.type', index=21,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position', full_name='mobile_transfer.GetTransferByIDResponse.receiving_position', index=22,
      number=35, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_name', full_name='mobile_transfer.GetTransferByIDResponse.receiving_position_name', index=23,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position', full_name='mobile_transfer.GetTransferByIDResponse.shipping_position', index=24,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_name', full_name='mobile_transfer.GetTransferByIDResponse.shipping_position_name', index=25,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='mobile_transfer.GetTransferByIDResponse.sub_account_type', index=26,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='mobile_transfer.GetTransferByIDResponse.extends', index=27,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='mobile_transfer.GetTransferByIDResponse.total_amount', index=28,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_sales_amount', full_name='mobile_transfer.GetTransferByIDResponse.total_sales_amount', index=29,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _GETTRANSFERBYIDRESPONSE_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1798,
  serialized_end=2786,
)


_GETTRANSFERPRODUCTBYBRANCHIDREQUEST = _descriptor.Descriptor(
  name='GetTransferProductByBranchIDRequest',
  full_name='mobile_transfer.GetTransferProductByBranchIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='mobile_transfer.GetTransferProductByBranchIDRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='mobile_transfer.GetTransferProductByBranchIDRequest.include_total', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_transfer.GetTransferProductByBranchIDRequest.order', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_transfer.GetTransferProductByBranchIDRequest.offset', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_transfer.GetTransferProductByBranchIDRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='mobile_transfer.GetTransferProductByBranchIDRequest.search_fields', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='mobile_transfer.GetTransferProductByBranchIDRequest.search', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='mobile_transfer.GetTransferProductByBranchIDRequest.category_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by', full_name='mobile_transfer.GetTransferProductByBranchIDRequest.order_by', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='mobile_transfer.GetTransferProductByBranchIDRequest.sort', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2789,
  serialized_end=3006,
)


_TRANSFERPRODUCTUNIT = _descriptor.Descriptor(
  name='TransferProductUnit',
  full_name='mobile_transfer.TransferProductUnit',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_transfer.TransferProductUnit.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='mobile_transfer.TransferProductUnit.quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='mobile_transfer.TransferProductUnit.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='mobile_transfer.TransferProductUnit.tax_rate', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='mobile_transfer.TransferProductUnit.tax_price', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='mobile_transfer.TransferProductUnit.cost_price', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='mobile_transfer.TransferProductUnit.sales_price', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='mobile_transfer.TransferProductUnit.unit_rate', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3009,
  serialized_end=3171,
)


_SELECTTRANSFERPRODUCT = _descriptor.Descriptor(
  name='SelectTransferProduct',
  full_name='mobile_transfer.SelectTransferProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_transfer.SelectTransferProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='mobile_transfer.SelectTransferProduct.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='mobile_transfer.SelectTransferProduct.category_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_transfer.SelectTransferProduct.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit', full_name='mobile_transfer.SelectTransferProduct.unit', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='barcode', full_name='mobile_transfer.SelectTransferProduct.barcode', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='mobile_transfer.SelectTransferProduct.spec', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='mobile_transfer.SelectTransferProduct.category_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_name', full_name='mobile_transfer.SelectTransferProduct.model_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='real_inventory_qty', full_name='mobile_transfer.SelectTransferProduct.real_inventory_qty', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3174,
  serialized_end=3436,
)


_GETTRANSFERPRODUCTBYBRANCHIDRESPONSE = _descriptor.Descriptor(
  name='GetTransferProductByBranchIDResponse',
  full_name='mobile_transfer.GetTransferProductByBranchIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_transfer.GetTransferProductByBranchIDResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_transfer.GetTransferProductByBranchIDResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_unchanged_rows', full_name='mobile_transfer.GetTransferProductByBranchIDResponse.inventory_unchanged_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3439,
  serialized_end=3620,
)


_GETTRANSFERREGIONBYBRANCHIDREQUEST = _descriptor.Descriptor(
  name='GetTransferRegionByBranchIDRequest',
  full_name='mobile_transfer.GetTransferRegionByBranchIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='mobile_transfer.GetTransferRegionByBranchIDRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='mobile_transfer.GetTransferRegionByBranchIDRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3622,
  serialized_end=3689,
)


_REGIONBRANCH = _descriptor.Descriptor(
  name='RegionBranch',
  full_name='mobile_transfer.RegionBranch',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='address', full_name='mobile_transfer.RegionBranch.address', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_transfer.RegionBranch.id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='mobile_transfer.RegionBranch.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_transfer.RegionBranch.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3691,
  serialized_end=3762,
)


_GETTRANSFERREGIONBYBRANCHIDRESPONSE = _descriptor.Descriptor(
  name='GetTransferRegionByBranchIDResponse',
  full_name='mobile_transfer.GetTransferRegionByBranchIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_transfer.GetTransferRegionByBranchIDResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3764,
  serialized_end=3846,
)


_GETTRANSFERPRODUCTBYTRANSFERIDREQUEST = _descriptor.Descriptor(
  name='GetTransferProductByTransferIDRequest',
  full_name='mobile_transfer.GetTransferProductByTransferIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='include_total', full_name='mobile_transfer.GetTransferProductByTransferIDRequest.include_total', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_transfer.GetTransferProductByTransferIDRequest.order', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_transfer.GetTransferProductByTransferIDRequest.offset', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_transfer.GetTransferProductByTransferIDRequest.limit', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_transfer.GetTransferProductByTransferIDRequest.transfer_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3849,
  serialized_end=3978,
)


_TRANSFERPRODUCT = _descriptor.Descriptor(
  name='TransferProduct',
  full_name='mobile_transfer.TransferProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_transfer.TransferProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_transfer.TransferProduct.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_transfer.TransferProduct.transfer_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='mobile_transfer.TransferProduct.unit_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='mobile_transfer.TransferProduct.unit_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='mobile_transfer.TransferProduct.unit_spec', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='mobile_transfer.TransferProduct.accounting_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='mobile_transfer.TransferProduct.accounting_unit_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='mobile_transfer.TransferProduct.accounting_unit_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='mobile_transfer.TransferProduct.accounting_unit_spec', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_received_quantity', full_name='mobile_transfer.TransferProduct.accounting_received_quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_number', full_name='mobile_transfer.TransferProduct.item_number', index=11,
      number=14, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='mobile_transfer.TransferProduct.product_code', index=12,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_transfer.TransferProduct.product_name', index=13,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='mobile_transfer.TransferProduct.quantity', index=14,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='mobile_transfer.TransferProduct.receiving_store', index=15,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='mobile_transfer.TransferProduct.shipping_store', index=16,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='mobile_transfer.TransferProduct.created_by', index=17,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='mobile_transfer.TransferProduct.updated_by', index=18,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='mobile_transfer.TransferProduct.transfer_date', index=19,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='mobile_transfer.TransferProduct.created_at', index=20,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='mobile_transfer.TransferProduct.updated_at', index=21,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='mobile_transfer.TransferProduct.extends', index=22,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='mobile_transfer.TransferProduct.partner_id', index=23,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='mobile_transfer.TransferProduct.user_id', index=24,
      number=28, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_received_quantity', full_name='mobile_transfer.TransferProduct.confirmed_received_quantity', index=25,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='mobile_transfer.TransferProduct.created_name', index=26,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='mobile_transfer.TransferProduct.updated_name', index=27,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='mobile_transfer.TransferProduct.category_id', index=28,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='mobile_transfer.TransferProduct.category_name', index=29,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_name', full_name='mobile_transfer.TransferProduct.model_name', index=30,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='mobile_transfer.TransferProduct.tax_rate', index=31,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='mobile_transfer.TransferProduct.tax_price', index=32,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='mobile_transfer.TransferProduct.cost_price', index=33,
      number=37, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='mobile_transfer.TransferProduct.amount', index=34,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='mobile_transfer.TransferProduct.sales_price', index=35,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='mobile_transfer.TransferProduct.sales_amount', index=36,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='mobile_transfer.TransferProduct.unit_rate', index=37,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='real_inventory_qty', full_name='mobile_transfer.TransferProduct.real_inventory_qty', index=38,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3981,
  serialized_end=4942,
)


_GETTRANSFERPRODUCTBYTRANSFERIDRESPONSE = _descriptor.Descriptor(
  name='GetTransferProductByTransferIDResponse',
  full_name='mobile_transfer.GetTransferProductByTransferIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_transfer.GetTransferProductByTransferIDResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_transfer.GetTransferProductByTransferIDResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4944,
  serialized_end=5047,
)


_POSTTRANSFERPRODUCT = _descriptor.Descriptor(
  name='PostTransferProduct',
  full_name='mobile_transfer.PostTransferProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_transfer.PostTransferProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='mobile_transfer.PostTransferProduct.unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='mobile_transfer.PostTransferProduct.quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='mobile_transfer.PostTransferProduct.tax_rate', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='mobile_transfer.PostTransferProduct.tax_price', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='mobile_transfer.PostTransferProduct.cost_price', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='mobile_transfer.PostTransferProduct.amount', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='mobile_transfer.PostTransferProduct.sales_price', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='mobile_transfer.PostTransferProduct.sales_amount', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5050,
  serialized_end=5242,
)


_CREATETRANSFERREQUEST = _descriptor.Descriptor(
  name='CreateTransferRequest',
  full_name='mobile_transfer.CreateTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='mobile_transfer.CreateTransferRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='mobile_transfer.CreateTransferRequest.shipping_store', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='mobile_transfer.CreateTransferRequest.receiving_store', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='mobile_transfer.CreateTransferRequest.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_transfer.CreateTransferRequest.remark', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_date', full_name='mobile_transfer.CreateTransferRequest.shipping_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='mobile_transfer.CreateTransferRequest.transfer_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_transfer.CreateTransferRequest.branch_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='mobile_transfer.CreateTransferRequest.type', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='mobile_transfer.CreateTransferRequest.sub_type', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position', full_name='mobile_transfer.CreateTransferRequest.receiving_position', index=10,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position', full_name='mobile_transfer.CreateTransferRequest.shipping_position', index=11,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_name', full_name='mobile_transfer.CreateTransferRequest.receiving_position_name', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_name', full_name='mobile_transfer.CreateTransferRequest.shipping_position_name', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='mobile_transfer.CreateTransferRequest.sub_account_type', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5245,
  serialized_end=5710,
)


_UPDATETRANSFERREQUEST = _descriptor.Descriptor(
  name='UpdateTransferRequest',
  full_name='mobile_transfer.UpdateTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_transfer.UpdateTransferRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='mobile_transfer.UpdateTransferRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='mobile_transfer.UpdateTransferRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='mobile_transfer.UpdateTransferRequest.transfer_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_transfer.UpdateTransferRequest.branch_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='mobile_transfer.UpdateTransferRequest.type', index=5,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='mobile_transfer.UpdateTransferRequest.sub_type', index=6,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5713,
  serialized_end=5933,
)


_UPDATETRANSFERRESPONSE = _descriptor.Descriptor(
  name='UpdateTransferResponse',
  full_name='mobile_transfer.UpdateTransferResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='mobile_transfer.UpdateTransferResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5935,
  serialized_end=5975,
)


_CONFIRMPOSTTRANSFERPRODUCT = _descriptor.Descriptor(
  name='ConfirmPostTransferProduct',
  full_name='mobile_transfer.ConfirmPostTransferProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_transfer.ConfirmPostTransferProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='mobile_transfer.ConfirmPostTransferProduct.unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_received_quantity', full_name='mobile_transfer.ConfirmPostTransferProduct.confirmed_received_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5977,
  serialized_end=6079,
)


_CONFIRMTRANSFERREQUEST = _descriptor.Descriptor(
  name='ConfirmTransferRequest',
  full_name='mobile_transfer.ConfirmTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_transfer.ConfirmTransferRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiver', full_name='mobile_transfer.ConfirmTransferRequest.receiver', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='mobile_transfer.ConfirmTransferRequest.receiving_store', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='mobile_transfer.ConfirmTransferRequest.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_transfer.ConfirmTransferRequest.branch_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6082,
  serialized_end=6254,
)


_DELETETRANSFERPRODUCTREQUEST = _descriptor.Descriptor(
  name='DeleteTransferProductRequest',
  full_name='mobile_transfer.DeleteTransferProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ids', full_name='mobile_transfer.DeleteTransferProductRequest.ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_transfer.DeleteTransferProductRequest.transfer_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6256,
  serialized_end=6320,
)


_DELETETRANSFERPRODUCTRESPONSE = _descriptor.Descriptor(
  name='DeleteTransferProductResponse',
  full_name='mobile_transfer.DeleteTransferProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='mobile_transfer.DeleteTransferProductResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6322,
  serialized_end=6369,
)


_DELETETRANSFERREQUEST = _descriptor.Descriptor(
  name='DeleteTransferRequest',
  full_name='mobile_transfer.DeleteTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_transfer.DeleteTransferRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6371,
  serialized_end=6415,
)


_DELETETRANSFERRESPONSE = _descriptor.Descriptor(
  name='DeleteTransferResponse',
  full_name='mobile_transfer.DeleteTransferResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='mobile_transfer.DeleteTransferResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6417,
  serialized_end=6457,
)


_SUBMITTRANSFERREQUEST = _descriptor.Descriptor(
  name='SubmitTransferRequest',
  full_name='mobile_transfer.SubmitTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_transfer.SubmitTransferRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiver', full_name='mobile_transfer.SubmitTransferRequest.receiver', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='mobile_transfer.SubmitTransferRequest.receiving_store', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='mobile_transfer.SubmitTransferRequest.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_transfer.SubmitTransferRequest.branch_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6460,
  serialized_end=6624,
)


_SUBMITTRANSFERRECEIVINGREQUEST = _descriptor.Descriptor(
  name='SubmitTransferReceivingRequest',
  full_name='mobile_transfer.SubmitTransferReceivingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_transfer.SubmitTransferReceivingRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiver', full_name='mobile_transfer.SubmitTransferReceivingRequest.receiver', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='mobile_transfer.SubmitTransferReceivingRequest.receiving_store', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='mobile_transfer.SubmitTransferReceivingRequest.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6627,
  serialized_end=6779,
)


_CANCELTRANSFERREQUEST = _descriptor.Descriptor(
  name='CancelTransferRequest',
  full_name='mobile_transfer.CancelTransferRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_transfer.CancelTransferRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6781,
  serialized_end=6825,
)


_GETTRANSFERCOLLECTREQUEST = _descriptor.Descriptor(
  name='GetTransferCollectRequest',
  full_name='mobile_transfer.GetTransferCollectRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='st_ids', full_name='mobile_transfer.GetTransferCollectRequest.st_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='mobile_transfer.GetTransferCollectRequest.store_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='mobile_transfer.GetTransferCollectRequest.category_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_transfer.GetTransferCollectRequest.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='mobile_transfer.GetTransferCollectRequest.start_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='mobile_transfer.GetTransferCollectRequest.end_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_transfer.GetTransferCollectRequest.limit', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_transfer.GetTransferCollectRequest.offset', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='mobile_transfer.GetTransferCollectRequest.include_total', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_in', full_name='mobile_transfer.GetTransferCollectRequest.is_in', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_transfer.GetTransferCollectRequest.order', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='mobile_transfer.GetTransferCollectRequest.sort', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_transfer.GetTransferCollectRequest.code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_code', full_name='mobile_transfer.GetTransferCollectRequest.jde_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='mobile_transfer.GetTransferCollectRequest.is_wms_store', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_transfer.GetTransferCollectRequest.branch_type', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='mobile_transfer.GetTransferCollectRequest.lan', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='mobile_transfer.GetTransferCollectRequest.type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='mobile_transfer.GetTransferCollectRequest.sub_type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6828,
  serialized_end=7246,
)


_TRANSFERCOLLECT = _descriptor.Descriptor(
  name='TransferCollect',
  full_name='mobile_transfer.TransferCollect',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='mobile_transfer.TransferCollect.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='mobile_transfer.TransferCollect.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='mobile_transfer.TransferCollect.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='mobile_transfer.TransferCollect.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='mobile_transfer.TransferCollect.category_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='mobile_transfer.TransferCollect.category_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_parent', full_name='mobile_transfer.TransferCollect.category_parent', index=6,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='mobile_transfer.TransferCollect.product_code', index=7,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_transfer.TransferCollect.product_id', index=8,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_transfer.TransferCollect.product_name', index=9,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_price', full_name='mobile_transfer.TransferCollect.product_price', index=10,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='mobile_transfer.TransferCollect.receiving_store', index=11,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_code', full_name='mobile_transfer.TransferCollect.receiving_store_code', index=12,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_us_id', full_name='mobile_transfer.TransferCollect.receiving_store_us_id', index=13,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_name', full_name='mobile_transfer.TransferCollect.receiving_store_name', index=14,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='mobile_transfer.TransferCollect.quantity', index=15,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='mobile_transfer.TransferCollect.shipping_store', index=16,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_code', full_name='mobile_transfer.TransferCollect.shipping_store_code', index=17,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_name', full_name='mobile_transfer.TransferCollect.shipping_store_name', index=18,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_us_id', full_name='mobile_transfer.TransferCollect.shipping_store_us_id', index=19,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='mobile_transfer.TransferCollect.unit_id', index=20,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='mobile_transfer.TransferCollect.unit_name', index=21,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='mobile_transfer.TransferCollect.product_spec', index=22,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='mobile_transfer.TransferCollect.price', index=23,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position', full_name='mobile_transfer.TransferCollect.receiving_position', index=24,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_code', full_name='mobile_transfer.TransferCollect.receiving_position_code', index=25,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_name', full_name='mobile_transfer.TransferCollect.receiving_position_name', index=26,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position', full_name='mobile_transfer.TransferCollect.shipping_position', index=27,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_code', full_name='mobile_transfer.TransferCollect.shipping_position_code', index=28,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_name', full_name='mobile_transfer.TransferCollect.shipping_position_name', index=29,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7249,
  serialized_end=8061,
)


_TRANSFERCOLLECTTOTAL = _descriptor.Descriptor(
  name='TransferCollectTotal',
  full_name='mobile_transfer.TransferCollectTotal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='count', full_name='mobile_transfer.TransferCollectTotal.count', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_accounting_quantity', full_name='mobile_transfer.TransferCollectTotal.sum_accounting_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_quantity', full_name='mobile_transfer.TransferCollectTotal.sum_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8063,
  serialized_end=8155,
)


_GETTRANSFERCOLLECTRESPONSE = _descriptor.Descriptor(
  name='GetTransferCollectResponse',
  full_name='mobile_transfer.GetTransferCollectResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_transfer.GetTransferCollectResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_transfer.GetTransferCollectResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8158,
  serialized_end=8288,
)


_GETTRANSFERCOLLECTDETAILEDREQUEST = _descriptor.Descriptor(
  name='GetTransferCollectDetailedRequest',
  full_name='mobile_transfer.GetTransferCollectDetailedRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='st_ids', full_name='mobile_transfer.GetTransferCollectDetailedRequest.st_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='mobile_transfer.GetTransferCollectDetailedRequest.store_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='mobile_transfer.GetTransferCollectDetailedRequest.category_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_transfer.GetTransferCollectDetailedRequest.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='mobile_transfer.GetTransferCollectDetailedRequest.start_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='mobile_transfer.GetTransferCollectDetailedRequest.end_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='mobile_transfer.GetTransferCollectDetailedRequest.limit', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='mobile_transfer.GetTransferCollectDetailedRequest.offset', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='mobile_transfer.GetTransferCollectDetailedRequest.include_total', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_in', full_name='mobile_transfer.GetTransferCollectDetailedRequest.is_in', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='mobile_transfer.GetTransferCollectDetailedRequest.order', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='mobile_transfer.GetTransferCollectDetailedRequest.sort', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_transfer.GetTransferCollectDetailedRequest.code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_code', full_name='mobile_transfer.GetTransferCollectDetailedRequest.jde_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='mobile_transfer.GetTransferCollectDetailedRequest.is_wms_store', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='mobile_transfer.GetTransferCollectDetailedRequest.branch_type', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='mobile_transfer.GetTransferCollectDetailedRequest.lan', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='mobile_transfer.GetTransferCollectDetailedRequest.type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='mobile_transfer.GetTransferCollectDetailedRequest.sub_type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8291,
  serialized_end=8717,
)


_TRANSFERCOLLECTDETAILED = _descriptor.Descriptor(
  name='TransferCollectDetailed',
  full_name='mobile_transfer.TransferCollectDetailed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='mobile_transfer.TransferCollectDetailed.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='mobile_transfer.TransferCollectDetailed.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='mobile_transfer.TransferCollectDetailed.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='mobile_transfer.TransferCollectDetailed.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='mobile_transfer.TransferCollectDetailed.category_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='mobile_transfer.TransferCollectDetailed.category_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_transfer.TransferCollectDetailed.id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='mobile_transfer.TransferCollectDetailed.product_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='mobile_transfer.TransferCollectDetailed.product_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='mobile_transfer.TransferCollectDetailed.product_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='mobile_transfer.TransferCollectDetailed.product_spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='mobile_transfer.TransferCollectDetailed.quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store', full_name='mobile_transfer.TransferCollectDetailed.receiving_store', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_code', full_name='mobile_transfer.TransferCollectDetailed.receiving_store_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_name', full_name='mobile_transfer.TransferCollectDetailed.receiving_store_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store', full_name='mobile_transfer.TransferCollectDetailed.shipping_store', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_code', full_name='mobile_transfer.TransferCollectDetailed.shipping_store_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_name', full_name='mobile_transfer.TransferCollectDetailed.shipping_store_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_transfer.TransferCollectDetailed.status', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_code', full_name='mobile_transfer.TransferCollectDetailed.transfer_code', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_date', full_name='mobile_transfer.TransferCollectDetailed.transfer_date', index=20,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_transfer.TransferCollectDetailed.transfer_id', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='mobile_transfer.TransferCollectDetailed.unit_id', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='mobile_transfer.TransferCollectDetailed.unit_name', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_store_us_id', full_name='mobile_transfer.TransferCollectDetailed.shipping_store_us_id', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_store_us_id', full_name='mobile_transfer.TransferCollectDetailed.receiving_store_us_id', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='mobile_transfer.TransferCollectDetailed.price', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_code', full_name='mobile_transfer.TransferCollectDetailed.jde_code', index=27,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='mobile_transfer.TransferCollectDetailed.code', index=28,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position', full_name='mobile_transfer.TransferCollectDetailed.receiving_position', index=29,
      number=40, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_code', full_name='mobile_transfer.TransferCollectDetailed.receiving_position_code', index=30,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_position_name', full_name='mobile_transfer.TransferCollectDetailed.receiving_position_name', index=31,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position', full_name='mobile_transfer.TransferCollectDetailed.shipping_position', index=32,
      number=43, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_code', full_name='mobile_transfer.TransferCollectDetailed.shipping_position_code', index=33,
      number=44, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shipping_position_name', full_name='mobile_transfer.TransferCollectDetailed.shipping_position_name', index=34,
      number=45, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8720,
  serialized_end=9622,
)


_GETTRANSFERCOLLECTDETAILEDRESPONSE = _descriptor.Descriptor(
  name='GetTransferCollectDetailedResponse',
  full_name='mobile_transfer.GetTransferCollectDetailedResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_transfer.GetTransferCollectDetailedResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_transfer.GetTransferCollectDetailedResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9625,
  serialized_end=9771,
)


_GETTRANSFERLOGREQUEST = _descriptor.Descriptor(
  name='GetTransferLogRequest',
  full_name='mobile_transfer.GetTransferLogRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transfer_id', full_name='mobile_transfer.GetTransferLogRequest.transfer_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9773,
  serialized_end=9817,
)


_GETTRANSFERLOGRESPONSE = _descriptor.Descriptor(
  name='GetTransferLogResponse',
  full_name='mobile_transfer.GetTransferLogResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='mobile_transfer.GetTransferLogResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='mobile_transfer.GetTransferLogResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9819,
  serialized_end=9894,
)


_LOG = _descriptor.Descriptor(
  name='Log',
  full_name='mobile_transfer.Log',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='mobile_transfer.Log.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='mobile_transfer.Log.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='mobile_transfer.Log.created_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='mobile_transfer.Log.created_at', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='mobile_transfer.Log.reason', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='mobile_transfer.Log.created_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9897,
  serialized_end=10036,
)

_GETTRANSFERREQUEST.fields_by_name['status'].enum_type = _GETTRANSFERREQUEST_STATUS
_GETTRANSFERREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERREQUEST_STATUS.containing_type = _GETTRANSFERREQUEST
_TRANSFER.fields_by_name['status'].enum_type = _TRANSFER_STATUS
_TRANSFER.fields_by_name['process_status'].enum_type = _P_STATUS
_TRANSFER.fields_by_name['transfer_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFER.fields_by_name['receiving_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFER.fields_by_name['shipping_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFER.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFER.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFER_STATUS.containing_type = _TRANSFER
_GETTRANSFERRESPONSE.fields_by_name['rows'].message_type = _TRANSFER
_GETTRANSFERBYIDRESPONSE.fields_by_name['status'].enum_type = _GETTRANSFERBYIDRESPONSE_STATUS
_GETTRANSFERBYIDRESPONSE.fields_by_name['process_status'].enum_type = _P_STATUS
_GETTRANSFERBYIDRESPONSE.fields_by_name['transfer_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERBYIDRESPONSE.fields_by_name['receiving_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERBYIDRESPONSE.fields_by_name['shipping_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERBYIDRESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERBYIDRESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERBYIDRESPONSE_STATUS.containing_type = _GETTRANSFERBYIDRESPONSE
_SELECTTRANSFERPRODUCT.fields_by_name['unit'].message_type = _TRANSFERPRODUCTUNIT
_GETTRANSFERPRODUCTBYBRANCHIDRESPONSE.fields_by_name['rows'].message_type = _SELECTTRANSFERPRODUCT
_GETTRANSFERPRODUCTBYBRANCHIDRESPONSE.fields_by_name['inventory_unchanged_rows'].message_type = _SELECTTRANSFERPRODUCT
_GETTRANSFERREGIONBYBRANCHIDRESPONSE.fields_by_name['rows'].message_type = _REGIONBRANCH
_TRANSFERPRODUCT.fields_by_name['transfer_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFERPRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFERPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERPRODUCTBYTRANSFERIDRESPONSE.fields_by_name['rows'].message_type = _TRANSFERPRODUCT
_CREATETRANSFERREQUEST.fields_by_name['products'].message_type = _POSTTRANSFERPRODUCT
_CREATETRANSFERREQUEST.fields_by_name['shipping_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATETRANSFERREQUEST.fields_by_name['transfer_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATETRANSFERREQUEST.fields_by_name['products'].message_type = _POSTTRANSFERPRODUCT
_UPDATETRANSFERREQUEST.fields_by_name['transfer_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CONFIRMTRANSFERREQUEST.fields_by_name['products'].message_type = _CONFIRMPOSTTRANSFERPRODUCT
_SUBMITTRANSFERREQUEST.fields_by_name['products'].message_type = _POSTTRANSFERPRODUCT
_SUBMITTRANSFERRECEIVINGREQUEST.fields_by_name['products'].message_type = _POSTTRANSFERPRODUCT
_GETTRANSFERCOLLECTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERCOLLECTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFERCOLLECT.fields_by_name['category_parent'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_GETTRANSFERCOLLECTRESPONSE.fields_by_name['rows'].message_type = _TRANSFERCOLLECT
_GETTRANSFERCOLLECTRESPONSE.fields_by_name['total'].message_type = _TRANSFERCOLLECTTOTAL
_GETTRANSFERCOLLECTDETAILEDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERCOLLECTDETAILEDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRANSFERCOLLECTDETAILED.fields_by_name['transfer_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSFERCOLLECTDETAILEDRESPONSE.fields_by_name['rows'].message_type = _TRANSFERCOLLECTDETAILED
_GETTRANSFERCOLLECTDETAILEDRESPONSE.fields_by_name['total'].message_type = _TRANSFERCOLLECTTOTAL
_GETTRANSFERLOGRESPONSE.fields_by_name['rows'].message_type = _LOG
_LOG.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['Pong'] = _PONG
DESCRIPTOR.message_types_by_name['GetTransferRequest'] = _GETTRANSFERREQUEST
DESCRIPTOR.message_types_by_name['Transfer'] = _TRANSFER
DESCRIPTOR.message_types_by_name['GetTransferResponse'] = _GETTRANSFERRESPONSE
DESCRIPTOR.message_types_by_name['GetTransferByIDRequest'] = _GETTRANSFERBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetTransferByIDResponse'] = _GETTRANSFERBYIDRESPONSE
DESCRIPTOR.message_types_by_name['GetTransferProductByBranchIDRequest'] = _GETTRANSFERPRODUCTBYBRANCHIDREQUEST
DESCRIPTOR.message_types_by_name['TransferProductUnit'] = _TRANSFERPRODUCTUNIT
DESCRIPTOR.message_types_by_name['SelectTransferProduct'] = _SELECTTRANSFERPRODUCT
DESCRIPTOR.message_types_by_name['GetTransferProductByBranchIDResponse'] = _GETTRANSFERPRODUCTBYBRANCHIDRESPONSE
DESCRIPTOR.message_types_by_name['GetTransferRegionByBranchIDRequest'] = _GETTRANSFERREGIONBYBRANCHIDREQUEST
DESCRIPTOR.message_types_by_name['RegionBranch'] = _REGIONBRANCH
DESCRIPTOR.message_types_by_name['GetTransferRegionByBranchIDResponse'] = _GETTRANSFERREGIONBYBRANCHIDRESPONSE
DESCRIPTOR.message_types_by_name['GetTransferProductByTransferIDRequest'] = _GETTRANSFERPRODUCTBYTRANSFERIDREQUEST
DESCRIPTOR.message_types_by_name['TransferProduct'] = _TRANSFERPRODUCT
DESCRIPTOR.message_types_by_name['GetTransferProductByTransferIDResponse'] = _GETTRANSFERPRODUCTBYTRANSFERIDRESPONSE
DESCRIPTOR.message_types_by_name['PostTransferProduct'] = _POSTTRANSFERPRODUCT
DESCRIPTOR.message_types_by_name['CreateTransferRequest'] = _CREATETRANSFERREQUEST
DESCRIPTOR.message_types_by_name['UpdateTransferRequest'] = _UPDATETRANSFERREQUEST
DESCRIPTOR.message_types_by_name['UpdateTransferResponse'] = _UPDATETRANSFERRESPONSE
DESCRIPTOR.message_types_by_name['ConfirmPostTransferProduct'] = _CONFIRMPOSTTRANSFERPRODUCT
DESCRIPTOR.message_types_by_name['ConfirmTransferRequest'] = _CONFIRMTRANSFERREQUEST
DESCRIPTOR.message_types_by_name['DeleteTransferProductRequest'] = _DELETETRANSFERPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['DeleteTransferProductResponse'] = _DELETETRANSFERPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['DeleteTransferRequest'] = _DELETETRANSFERREQUEST
DESCRIPTOR.message_types_by_name['DeleteTransferResponse'] = _DELETETRANSFERRESPONSE
DESCRIPTOR.message_types_by_name['SubmitTransferRequest'] = _SUBMITTRANSFERREQUEST
DESCRIPTOR.message_types_by_name['SubmitTransferReceivingRequest'] = _SUBMITTRANSFERRECEIVINGREQUEST
DESCRIPTOR.message_types_by_name['CancelTransferRequest'] = _CANCELTRANSFERREQUEST
DESCRIPTOR.message_types_by_name['GetTransferCollectRequest'] = _GETTRANSFERCOLLECTREQUEST
DESCRIPTOR.message_types_by_name['TransferCollect'] = _TRANSFERCOLLECT
DESCRIPTOR.message_types_by_name['TransferCollectTotal'] = _TRANSFERCOLLECTTOTAL
DESCRIPTOR.message_types_by_name['GetTransferCollectResponse'] = _GETTRANSFERCOLLECTRESPONSE
DESCRIPTOR.message_types_by_name['GetTransferCollectDetailedRequest'] = _GETTRANSFERCOLLECTDETAILEDREQUEST
DESCRIPTOR.message_types_by_name['TransferCollectDetailed'] = _TRANSFERCOLLECTDETAILED
DESCRIPTOR.message_types_by_name['GetTransferCollectDetailedResponse'] = _GETTRANSFERCOLLECTDETAILEDRESPONSE
DESCRIPTOR.message_types_by_name['GetTransferLogRequest'] = _GETTRANSFERLOGREQUEST
DESCRIPTOR.message_types_by_name['GetTransferLogResponse'] = _GETTRANSFERLOGRESPONSE
DESCRIPTOR.message_types_by_name['Log'] = _LOG
DESCRIPTOR.enum_types_by_name['P_STATUS'] = _P_STATUS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Pong = _reflection.GeneratedProtocolMessageType('Pong', (_message.Message,), dict(
  DESCRIPTOR = _PONG,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.Pong)
  ))
_sym_db.RegisterMessage(Pong)

GetTransferRequest = _reflection.GeneratedProtocolMessageType('GetTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERREQUEST,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.GetTransferRequest)
  ))
_sym_db.RegisterMessage(GetTransferRequest)

Transfer = _reflection.GeneratedProtocolMessageType('Transfer', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFER,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.Transfer)
  ))
_sym_db.RegisterMessage(Transfer)

GetTransferResponse = _reflection.GeneratedProtocolMessageType('GetTransferResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERRESPONSE,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.GetTransferResponse)
  ))
_sym_db.RegisterMessage(GetTransferResponse)

GetTransferByIDRequest = _reflection.GeneratedProtocolMessageType('GetTransferByIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERBYIDREQUEST,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.GetTransferByIDRequest)
  ))
_sym_db.RegisterMessage(GetTransferByIDRequest)

GetTransferByIDResponse = _reflection.GeneratedProtocolMessageType('GetTransferByIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERBYIDRESPONSE,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.GetTransferByIDResponse)
  ))
_sym_db.RegisterMessage(GetTransferByIDResponse)

GetTransferProductByBranchIDRequest = _reflection.GeneratedProtocolMessageType('GetTransferProductByBranchIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERPRODUCTBYBRANCHIDREQUEST,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.GetTransferProductByBranchIDRequest)
  ))
_sym_db.RegisterMessage(GetTransferProductByBranchIDRequest)

TransferProductUnit = _reflection.GeneratedProtocolMessageType('TransferProductUnit', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFERPRODUCTUNIT,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.TransferProductUnit)
  ))
_sym_db.RegisterMessage(TransferProductUnit)

SelectTransferProduct = _reflection.GeneratedProtocolMessageType('SelectTransferProduct', (_message.Message,), dict(
  DESCRIPTOR = _SELECTTRANSFERPRODUCT,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.SelectTransferProduct)
  ))
_sym_db.RegisterMessage(SelectTransferProduct)

GetTransferProductByBranchIDResponse = _reflection.GeneratedProtocolMessageType('GetTransferProductByBranchIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERPRODUCTBYBRANCHIDRESPONSE,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.GetTransferProductByBranchIDResponse)
  ))
_sym_db.RegisterMessage(GetTransferProductByBranchIDResponse)

GetTransferRegionByBranchIDRequest = _reflection.GeneratedProtocolMessageType('GetTransferRegionByBranchIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERREGIONBYBRANCHIDREQUEST,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.GetTransferRegionByBranchIDRequest)
  ))
_sym_db.RegisterMessage(GetTransferRegionByBranchIDRequest)

RegionBranch = _reflection.GeneratedProtocolMessageType('RegionBranch', (_message.Message,), dict(
  DESCRIPTOR = _REGIONBRANCH,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.RegionBranch)
  ))
_sym_db.RegisterMessage(RegionBranch)

GetTransferRegionByBranchIDResponse = _reflection.GeneratedProtocolMessageType('GetTransferRegionByBranchIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERREGIONBYBRANCHIDRESPONSE,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.GetTransferRegionByBranchIDResponse)
  ))
_sym_db.RegisterMessage(GetTransferRegionByBranchIDResponse)

GetTransferProductByTransferIDRequest = _reflection.GeneratedProtocolMessageType('GetTransferProductByTransferIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERPRODUCTBYTRANSFERIDREQUEST,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.GetTransferProductByTransferIDRequest)
  ))
_sym_db.RegisterMessage(GetTransferProductByTransferIDRequest)

TransferProduct = _reflection.GeneratedProtocolMessageType('TransferProduct', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFERPRODUCT,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.TransferProduct)
  ))
_sym_db.RegisterMessage(TransferProduct)

GetTransferProductByTransferIDResponse = _reflection.GeneratedProtocolMessageType('GetTransferProductByTransferIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERPRODUCTBYTRANSFERIDRESPONSE,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.GetTransferProductByTransferIDResponse)
  ))
_sym_db.RegisterMessage(GetTransferProductByTransferIDResponse)

PostTransferProduct = _reflection.GeneratedProtocolMessageType('PostTransferProduct', (_message.Message,), dict(
  DESCRIPTOR = _POSTTRANSFERPRODUCT,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.PostTransferProduct)
  ))
_sym_db.RegisterMessage(PostTransferProduct)

CreateTransferRequest = _reflection.GeneratedProtocolMessageType('CreateTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATETRANSFERREQUEST,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.CreateTransferRequest)
  ))
_sym_db.RegisterMessage(CreateTransferRequest)

UpdateTransferRequest = _reflection.GeneratedProtocolMessageType('UpdateTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATETRANSFERREQUEST,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.UpdateTransferRequest)
  ))
_sym_db.RegisterMessage(UpdateTransferRequest)

UpdateTransferResponse = _reflection.GeneratedProtocolMessageType('UpdateTransferResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATETRANSFERRESPONSE,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.UpdateTransferResponse)
  ))
_sym_db.RegisterMessage(UpdateTransferResponse)

ConfirmPostTransferProduct = _reflection.GeneratedProtocolMessageType('ConfirmPostTransferProduct', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMPOSTTRANSFERPRODUCT,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.ConfirmPostTransferProduct)
  ))
_sym_db.RegisterMessage(ConfirmPostTransferProduct)

ConfirmTransferRequest = _reflection.GeneratedProtocolMessageType('ConfirmTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMTRANSFERREQUEST,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.ConfirmTransferRequest)
  ))
_sym_db.RegisterMessage(ConfirmTransferRequest)

DeleteTransferProductRequest = _reflection.GeneratedProtocolMessageType('DeleteTransferProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETETRANSFERPRODUCTREQUEST,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.DeleteTransferProductRequest)
  ))
_sym_db.RegisterMessage(DeleteTransferProductRequest)

DeleteTransferProductResponse = _reflection.GeneratedProtocolMessageType('DeleteTransferProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETETRANSFERPRODUCTRESPONSE,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.DeleteTransferProductResponse)
  ))
_sym_db.RegisterMessage(DeleteTransferProductResponse)

DeleteTransferRequest = _reflection.GeneratedProtocolMessageType('DeleteTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETETRANSFERREQUEST,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.DeleteTransferRequest)
  ))
_sym_db.RegisterMessage(DeleteTransferRequest)

DeleteTransferResponse = _reflection.GeneratedProtocolMessageType('DeleteTransferResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETETRANSFERRESPONSE,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.DeleteTransferResponse)
  ))
_sym_db.RegisterMessage(DeleteTransferResponse)

SubmitTransferRequest = _reflection.GeneratedProtocolMessageType('SubmitTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITTRANSFERREQUEST,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.SubmitTransferRequest)
  ))
_sym_db.RegisterMessage(SubmitTransferRequest)

SubmitTransferReceivingRequest = _reflection.GeneratedProtocolMessageType('SubmitTransferReceivingRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITTRANSFERRECEIVINGREQUEST,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.SubmitTransferReceivingRequest)
  ))
_sym_db.RegisterMessage(SubmitTransferReceivingRequest)

CancelTransferRequest = _reflection.GeneratedProtocolMessageType('CancelTransferRequest', (_message.Message,), dict(
  DESCRIPTOR = _CANCELTRANSFERREQUEST,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.CancelTransferRequest)
  ))
_sym_db.RegisterMessage(CancelTransferRequest)

GetTransferCollectRequest = _reflection.GeneratedProtocolMessageType('GetTransferCollectRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERCOLLECTREQUEST,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.GetTransferCollectRequest)
  ))
_sym_db.RegisterMessage(GetTransferCollectRequest)

TransferCollect = _reflection.GeneratedProtocolMessageType('TransferCollect', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFERCOLLECT,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.TransferCollect)
  ))
_sym_db.RegisterMessage(TransferCollect)

TransferCollectTotal = _reflection.GeneratedProtocolMessageType('TransferCollectTotal', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFERCOLLECTTOTAL,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.TransferCollectTotal)
  ))
_sym_db.RegisterMessage(TransferCollectTotal)

GetTransferCollectResponse = _reflection.GeneratedProtocolMessageType('GetTransferCollectResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERCOLLECTRESPONSE,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.GetTransferCollectResponse)
  ))
_sym_db.RegisterMessage(GetTransferCollectResponse)

GetTransferCollectDetailedRequest = _reflection.GeneratedProtocolMessageType('GetTransferCollectDetailedRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERCOLLECTDETAILEDREQUEST,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.GetTransferCollectDetailedRequest)
  ))
_sym_db.RegisterMessage(GetTransferCollectDetailedRequest)

TransferCollectDetailed = _reflection.GeneratedProtocolMessageType('TransferCollectDetailed', (_message.Message,), dict(
  DESCRIPTOR = _TRANSFERCOLLECTDETAILED,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.TransferCollectDetailed)
  ))
_sym_db.RegisterMessage(TransferCollectDetailed)

GetTransferCollectDetailedResponse = _reflection.GeneratedProtocolMessageType('GetTransferCollectDetailedResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERCOLLECTDETAILEDRESPONSE,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.GetTransferCollectDetailedResponse)
  ))
_sym_db.RegisterMessage(GetTransferCollectDetailedResponse)

GetTransferLogRequest = _reflection.GeneratedProtocolMessageType('GetTransferLogRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERLOGREQUEST,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.GetTransferLogRequest)
  ))
_sym_db.RegisterMessage(GetTransferLogRequest)

GetTransferLogResponse = _reflection.GeneratedProtocolMessageType('GetTransferLogResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSFERLOGRESPONSE,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.GetTransferLogResponse)
  ))
_sym_db.RegisterMessage(GetTransferLogResponse)

Log = _reflection.GeneratedProtocolMessageType('Log', (_message.Message,), dict(
  DESCRIPTOR = _LOG,
  __module__ = 'mobile.mobile_transfer_pb2'
  # @@protoc_insertion_point(class_scope:mobile_transfer.Log)
  ))
_sym_db.RegisterMessage(Log)


DESCRIPTOR._options = None

_MOBILETRANSFER = _descriptor.ServiceDescriptor(
  name='mobileTransfer',
  full_name='mobile_transfer.mobileTransfer',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=10116,
  serialized_end=12686,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateTransfer',
    full_name='mobile_transfer.mobileTransfer.CreateTransfer',
    index=0,
    containing_service=None,
    input_type=_CREATETRANSFERREQUEST,
    output_type=_TRANSFER,
    serialized_options=_b('\202\323\344\223\002(\"#/api/v2/supply/mobile/transfer/main:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateTransfer',
    full_name='mobile_transfer.mobileTransfer.UpdateTransfer',
    index=1,
    containing_service=None,
    input_type=_UPDATETRANSFERREQUEST,
    output_type=_UPDATETRANSFERRESPONSE,
    serialized_options=_b('\202\323\344\223\002=\"8/api/v2/supply/mobile/transfer/main/{transfer_id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransfer',
    full_name='mobile_transfer.mobileTransfer.GetTransfer',
    index=2,
    containing_service=None,
    input_type=_GETTRANSFERREQUEST,
    output_type=_GETTRANSFERRESPONSE,
    serialized_options=_b('\202\323\344\223\002 \022\036/api/v2/supply/mobile/transfer'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferByID',
    full_name='mobile_transfer.mobileTransfer.GetTransferByID',
    index=3,
    containing_service=None,
    input_type=_GETTRANSFERBYIDREQUEST,
    output_type=_GETTRANSFERBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\022,/api/v2/supply/mobile/transfer/{transfer_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferProductByBranchID',
    full_name='mobile_transfer.mobileTransfer.GetTransferProductByBranchID',
    index=4,
    containing_service=None,
    input_type=_GETTRANSFERPRODUCTBYBRANCHIDREQUEST,
    output_type=_GETTRANSFERPRODUCTBYBRANCHIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\0227/api/v2/supply/mobile/transfer/store/{store_id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferRegionByBranchID',
    full_name='mobile_transfer.mobileTransfer.GetTransferRegionByBranchID',
    index=5,
    containing_service=None,
    input_type=_GETTRANSFERREGIONBYBRANCHIDREQUEST,
    output_type=_GETTRANSFERREGIONBYBRANCHIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\0226/api/v2/supply/mobile/transfer/store/{store_id}/region'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferProductByTransferID',
    full_name='mobile_transfer.mobileTransfer.GetTransferProductByTransferID',
    index=6,
    containing_service=None,
    input_type=_GETTRANSFERPRODUCTBYTRANSFERIDREQUEST,
    output_type=_GETTRANSFERPRODUCTBYTRANSFERIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0026\0224/api/v2/supply/mobile/transfer/{transfer_id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmTransfer',
    full_name='mobile_transfer.mobileTransfer.ConfirmTransfer',
    index=7,
    containing_service=None,
    input_type=_CONFIRMTRANSFERREQUEST,
    output_type=_TRANSFER,
    serialized_options=_b('\202\323\344\223\002>\"9/api/v2/supply/mobile/transfer/main/{transfer_id}/confirm:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteTransferProduct',
    full_name='mobile_transfer.mobileTransfer.DeleteTransferProduct',
    index=8,
    containing_service=None,
    input_type=_DELETETRANSFERPRODUCTREQUEST,
    output_type=_DELETETRANSFERPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\002@\";/api/v2/supply/mobile/transfer/product/{transfer_id}/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteTransfer',
    full_name='mobile_transfer.mobileTransfer.DeleteTransfer',
    index=9,
    containing_service=None,
    input_type=_DELETETRANSFERREQUEST,
    output_type=_DELETETRANSFERRESPONSE,
    serialized_options=_b('\202\323\344\223\002=\"8/api/v2/supply/mobile/transfer/main/{transfer_id}/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='SubmitTransfer',
    full_name='mobile_transfer.mobileTransfer.SubmitTransfer',
    index=10,
    containing_service=None,
    input_type=_SUBMITTRANSFERREQUEST,
    output_type=_TRANSFER,
    serialized_options=_b('\202\323\344\223\002=\"8/api/v2/supply/mobile/transfer/main/{transfer_id}/submit:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CancelTransfer',
    full_name='mobile_transfer.mobileTransfer.CancelTransfer',
    index=11,
    containing_service=None,
    input_type=_CANCELTRANSFERREQUEST,
    output_type=_TRANSFER,
    serialized_options=_b('\202\323\344\223\002=\"8/api/v2/supply/mobile/transfer/main/{transfer_id}/cancel:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferCollect',
    full_name='mobile_transfer.mobileTransfer.GetTransferCollect',
    index=12,
    containing_service=None,
    input_type=_GETTRANSFERCOLLECTREQUEST,
    output_type=_GETTRANSFERCOLLECTRESPONSE,
    serialized_options=_b('\202\323\344\223\002/\022-/api/v2/supply/mobile/transfer/collect/report'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferCollectDetailed',
    full_name='mobile_transfer.mobileTransfer.GetTransferCollectDetailed',
    index=13,
    containing_service=None,
    input_type=_GETTRANSFERCOLLECTDETAILEDREQUEST,
    output_type=_GETTRANSFERCOLLECTDETAILEDRESPONSE,
    serialized_options=_b('\202\323\344\223\002,\022*/api/v2/supply/mobile/transfer/bi/detailed'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransferLog',
    full_name='mobile_transfer.mobileTransfer.GetTransferLog',
    index=14,
    containing_service=None,
    input_type=_GETTRANSFERLOGREQUEST,
    output_type=_GETTRANSFERLOGRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\0220/api/v2/supply/mobile/transfer/{transfer_id}/log'),
  ),
])
_sym_db.RegisterServiceDescriptor(_MOBILETRANSFER)

DESCRIPTOR.services_by_name['mobileTransfer'] = _MOBILETRANSFER

# @@protoc_insertion_point(module_scope)
