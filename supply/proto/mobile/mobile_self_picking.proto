syntax = "proto3";

package mobile_self_picking;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

service MobileStoreSelfPicking{
    // 门店自采移动端接口定义
    // 创建门店自采单
    rpc CreateStoreSelfPicking (CreateStoreSelfPickingRequest) returns (CreateStoreSelfPickingResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/mobile/self/picking/create"
        body: "*"
        };
    }
    // 门店自采单列表查询
    rpc ListStoreSelfPicking (ListStoreSelfPickingRequest) returns (ListStoreSelfPickingResponse){
        option (google.api.http) = {
        get: "/api/v2/supply/mobile/self/picking/list"
        };
    }
    // 查询门店自采单详情
    rpc GetStoreSelfPickingDetail (GetStoreSelfPickingDetailRequest) returns (GetStoreSelfPickingDetailResponse){
        option (google.api.http) = {
        get: "/api/v2/supply/mobile/self/picking/{receipt_id}/detail"
        };
    }
    // 更新门店自采单
    rpc UpdateStoreSelfPicking (UpdateStoreSelfPickingRequest) returns (UpdateStoreSelfPickingResponse){
        option (google.api.http) = {
        post: "/api/v2/supply/mobile/self/picking/update"
        body: "*"
        };
    }
    // 门店自采单变更状态
    rpc ChangeSelfPickingStatus (ChangeSelfPickingStatusRequest) returns (ChangeSelfPickingStatusResponse){
        option (google.api.http) = {
        post: "/api/v2/supply/mobile/self/picking/{status}/{receipt_id}"
        body: "*"
        };
    }
    // 取得门店可自采商品（相同属性区域）
    rpc GetPickingProductByStoreId (GetPickingProductByStoreIdRequest) returns (GetPickingProductByStoreIdResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/mobile/self/picking/store/{store_id}/product"
        };
    }
    // 自采历史记录
    rpc GetSelfPickingLog (GetSelfPickingLogRequest) returns (GetSelfPickingLogResponse){
        option (google.api.http) = {
        get: "/api/v2/supply/mobile/self/picking/{receipt_id}/log"
        };
    }
}

// 创建门店自采单请求参数
message CreateStoreSelfPickingRequest{
    // 自采日期
    google.protobuf.Timestamp order_date = 2;
    // 门店id
    uint64 branch_id = 3;
    // 组织类型STORE/
    string branch_type = 6;
    // 备注
    string remark = 7;
    // 原因
    string reason = 8;
    // 幂等性校验请求id
    uint64 request_id = 10;
    // 商品详情
    repeated Product items = 26;
    repeated string attachments = 11;
}

message Product {
    uint64 id = 1;
    // 关联的门店自采单id
    uint64 main_id = 2;
    // 物料/商品id
    uint64 product_id = 3;
    // 商品code
    string product_code = 4;
    // 商品名称
    string product_name = 5;
    // 单位id
    uint64 unit_id = 7;
    // 单位名称
    string unit_name = 8;
    // 规格编号
    string model_code = 9;
    // 规格名称
    string model_name = 10;
    // 采购数量
    double quantity = 12;
    // 单价
    double price = 13;
    // 金额
    double amount = 14;
    uint64 partner_id = 16;
    string created_name = 18;
    string updated_name = 20;
    google.protobuf.Timestamp created_at = 21;
    google.protobuf.Timestamp updated_at = 22;
    repeated Unit units = 23;
}

// 创建门店自采单返回
message CreateStoreSelfPickingResponse{
    // 门店自采单id
    uint64 receipt_id = 1;
    string result = 2;
}

// 门店自采单列表查询请求参数
message ListStoreSelfPickingRequest{
    // 开始日期
    google.protobuf.Timestamp start_date = 1;
    // 结束日期
    google.protobuf.Timestamp end_date = 2;
    // 门店ID列表
    repeated uint64 branch_ids = 3;
    // 组织类型STORE/
    string branch_type = 4;
    // 单号
    string code = 5;
    // 原因
    string reason = 6;
    // 商品ids
    repeated uint64 product_ids = 7;
    // 订单状态(多个传列表)
    // INITED = 'INITED'           # 新建
    // SUBMITTED = 'SUBMITTED'     # 已提交
    // APPROVED = 'APPROVED'       # 已审核
    // REJECTED = 'REJECTED'       # 已驳回
    repeated string status = 9;
    int64 limit = 10;
    uint64 offset = 11;
    bool include_total = 12;
    // 排序方式  asc or desc
    string order = 13;
    // 排序字段
    string sort = 14;
    // 单据id列表查询
    repeated uint64 ids = 15;
}

message SelfPicking{
    uint64 id = 1;
    uint64 partner_id = 2;
    google.protobuf.Timestamp created_at = 7;
    google.protobuf.Timestamp updated_at = 8;
    string status = 10;
    string code = 11;
    // 自采日期
    google.protobuf.Timestamp order_date = 12;
    // 门店id
    uint64 branch_id = 14;
    // 门店code
    string branch_code = 15;
    //门店名称
    string branch_name = 16;
    // 组织类型 STORE/
    string branch_type = 18;
    // 原因
    string reason = 19;
    // 备注
    string remark = 20;
    // 合计金额
    double total_amount = 21;
    // 原因名称
    string reason_name = 23;
    // 扩展字段
    string extends = 24;
    // 附件
    repeated string attachments = 25;
}

// 门店自采单列表查询返回参数
message ListStoreSelfPickingResponse{
    repeated SelfPicking rows = 1;
    uint64 total = 2;
}

// 查询门店自采单详情请求参数
message GetStoreSelfPickingDetailRequest{
    uint64 receipt_id = 1;
}

message GetStoreSelfPickingDetailResponse{
    uint64 id = 1;
    uint64 partner_id = 2;
    string created_name = 4;
    string updated_name = 6;
    google.protobuf.Timestamp created_at = 7;
    google.protobuf.Timestamp updated_at = 8;
    string status = 10;
    string code = 11;
    // 自采日期
    google.protobuf.Timestamp order_date = 12;
    // 门店id
    uint64 branch_id = 14;
    // 门店code
    string branch_code = 15;
    //门店名称
    string branch_name = 16;
    // 组织类型 STORE/
    string branch_type = 18;
    // 原因
    string reason = 19;
    // 备注
    string remark = 20;
    // 合计金额
    double total_amount = 21;
    repeated Product items = 34;
    // 附件
    repeated string attachments = 22;
    // 原因名称
    string reason_name = 23;
}

// 更新门店自采单请求参数
message UpdateStoreSelfPickingRequest{
    // 自采单id
    uint64 receipt_id = 1;
    // 自采日期
    google.protobuf.Timestamp order_date = 6;
    // 门店id
    uint64 branch_id = 14;
    // 门店code
    string branch_code = 15;
    //门店名称
    string branch_name = 16;
    // 组织类型 STORE/
    string branch_type = 18;
    // 原因
    string reason = 19;
    // 备注
    string remark = 20;
    // 合计金额
    double total_amount = 21;
    // 附件
    repeated string attachments = 22;
    repeated Product items = 34;
}

message UpdateStoreSelfPickingResponse{
    uint64 receipt_id = 1;
    string result = 2;
}

message GetPickingProductByStoreIdRequest {
    //门店id
    uint64 store_id = 1;
    //是否包含总数
    bool include_total = 2;
    //排序方式
    string order = 3;
    //分页开始处
    uint32 offset = 4;
    //返回条数
    uint32 limit = 5;
    // 排序字段
    string sort = 10;
    //模糊查询方式
    string search_fields = 6;
    //模糊查询条件
    string search = 7;
    repeated uint64 category_ids = 8;
}

message GetPickingProductByStoreIdResponse {
    repeated AllowPickingProduct rows = 1;
    uint32 total = 2;
}

message AllowPickingProduct {
    //商品ID
    uint64 product_id = 1;
    //商品编码
    string product_code = 2;
    //商品名字
    string product_name = 3;
    //单位分类
    uint64 category_id = 4;
    string category_name = 5;
    repeated Unit units = 6;
    repeated string barcode = 7;
    string spec = 8;
    string product_type = 9;
    string model_code = 18;
    string model_name = 19;
}

message Unit {
    // 数据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    string name = 4;
    string code = 5;
    string tp_code = 6;
    string updated = 7;
    double rate = 8;
    bool default = 9;
    bool order = 10;
    bool purchase = 11;
    bool sales = 12;
    bool stocktake = 13;
    bool bom = 14;
    bool default_stocktake = 15;
    bool transfer = 16;
}

message ChangeSelfPickingStatusRequest {
    // 自采单id
    uint64 receipt_id = 1;
    // 'SUBMITTED'已提交 'APPROVED'已审核 'REJECTED'已驳回
    string status = 2;
    // 备注(驳回原因)
    string remark = 3;
}

message ChangeSelfPickingStatusResponse{
    uint64 receipt_id = 1;
    string result = 2;
}

message GetSelfPickingLogRequest{
    uint64 receipt_id = 1;
}


message GetSelfPickingLogResponse{
    repeated Log rows = 1;
    uint64 total = 2;
}
message Log{
    // 历史记录id
    uint64 id = 1;
    // 状态
    string status = 2;
    // 操作人
    uint64 created_by = 3;
    // 操作时间
    google.protobuf.Timestamp created_at = 4;
    // 原因
    string reason = 5;
    // 操作人名称
    string created_name = 6;
}