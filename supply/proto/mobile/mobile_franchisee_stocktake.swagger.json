{"swagger": "2.0", "info": {"title": "mobile/mobile_franchisee_stocktake.proto", "version": "version not set"}, "tags": [{"name": "MobileFranchiseeStockTake"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/mobile/franchisee/recreate_stocktake_doc": {"post": {"summary": "RecreateStocktakeDoc  重盘功能，重新生成盘点单35", "operationId": "MobileFranchiseeStockTake_RecreateStocktakeDoc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeRecreateStocktakeDocResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeRecreateStocktakeDocRequest"}}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake": {"get": {"summary": "GetStocktake 查询盘点单20", "operationId": "MobileFranchiseeStockTake_GetStocktake", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeGetStocktakeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "include_total", "description": "是否包含总数", "in": "query", "required": false, "type": "boolean"}, {"name": "store_ids", "description": "门店ID", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "branch_ids", "description": "营运区域id", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "store_status", "description": "门店状态", "in": "query", "required": false, "type": "array", "items": {"type": "string", "enum": ["OPENED", "CLOSED"]}, "collectionFormat": "multi"}, {"name": "_type", "description": "盘点单类型", "in": "query", "required": false, "type": "array", "items": {"type": "string", "enum": ["W", "D", "M"]}, "collectionFormat": "multi"}, {"name": "status", "description": "盘点单状态\n\n - STARTED: 计划生成初始状态\n - INITED: 创建状态\n - CANCELLED: 取消状态\n - SUBMITTED: 提交\n - REJECTED: 驳回\n - APPROVED: 财务核算检查后状态\n - CONFIRMED: 录入盘点单位确定状态\n - SUBMIT_0: 月盘全盘0提交", "in": "query", "required": false, "type": "array", "items": {"type": "string", "enum": ["STARTED", "INITED", "CANCELLED", "SUBMITTED", "REJECTED", "APPROVED", "CONFIRMED", "SUBMIT_0", "APPROVE_0"]}, "collectionFormat": "multi"}, {"name": "schedule_code", "in": "query", "required": false, "type": "string"}, {"name": "product_ids", "description": "商品ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "offset", "description": "分页开始处", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "description": "分页限制数", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "start_date", "description": "查询开始时间", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "查询结束时间", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "target_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "ids", "description": "单据id列表查询", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "is_create", "in": "query", "required": false, "type": "boolean"}, {"name": "stocktake_type", "description": "盘点属性(不定期-R、计划生成-PLAN)", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序(默认asc)", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "branch_type", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "schedule_name", "description": "盘点计划名称", "in": "query", "required": false, "type": "string"}], "tags": ["MobileFranchiseeStockTake"]}, "post": {"summary": "手动创建盘点单", "operationId": "MobileFranchiseeStockTake_ManuallyCreateStocktake", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeManuallyCreateStocktakeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeManuallyCreateStocktakeRequest"}}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/balance/product/group": {"get": {"summary": "StocktakeBalanceRegion区域盘点31", "operationId": "MobileFranchiseeStockTake_StocktakeBalanceRegion", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeBalanceRegionResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "include_total", "in": "query", "required": false, "type": "boolean"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "start", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "region_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "product_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "type", "in": "query", "required": false, "type": "string"}, {"name": "branch_type", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/balance/{doc_id}/product/group": {"get": {"summary": "GetStocktakeBalanceProductGroup门店盘点单损益汇总报表29", "operationId": "MobileFranchiseeStockTake_GetStocktakeBalanceProductGroup", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeGetStocktakeBalanceProductGroupResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/bi/balance": {"get": {"summary": "GetStocktakeBalance盘点单损益报表27", "operationId": "MobileFranchiseeStockTake_GetStocktakeBalance", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeGetStocktakeBalanceResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "store_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "target_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "status", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean"}, {"name": "schedule_code", "in": "query", "required": false, "type": "string"}, {"name": "stocktake_type", "in": "query", "required": false, "type": "string"}, {"name": "is_wms_store", "in": "query", "required": false, "type": "boolean"}, {"name": "branch_type", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/bi/detail": {"get": {"summary": "StocktakeBiDetailed盘点单报表30", "operationId": "MobileFranchiseeStockTake_StocktakeBiDetailed", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeBiDetailedResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "category_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "type", "in": "query", "required": false, "type": "string"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean"}, {"name": "product_name", "in": "query", "required": false, "type": "string"}, {"name": "store_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "order", "description": "排序(默认asc)", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "jde_code", "in": "query", "required": false, "type": "string"}, {"name": "is_wms_store", "in": "query", "required": false, "type": "boolean"}, {"name": "branch_type", "description": "区分门店还是仓库/加工中心 STORE/WAREHOUSE/MACHINING_CENTER", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/by/ids": {"get": {"summary": "根据盘点单ID查询盘点单移动端用", "operationId": "MobileFranchiseeStockTake_GetStocktakeByIds", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeGetStocktakeByIdsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/diff/report": {"get": {"summary": "StocktakeDiffReport  盘点差异表33", "operationId": "MobileFranchiseeStockTake_StocktakeDiffReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeDiffReportResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "store_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "product_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "target_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "stocktake_type", "in": "query", "required": false, "type": "string"}, {"name": "jde_diff_code", "in": "query", "required": false, "type": "string"}, {"name": "schedule_code", "in": "query", "required": false, "type": "string"}, {"name": "status", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序(默认asc)", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "upper_limit", "description": "上限 区分0，传字符串", "in": "query", "required": false, "type": "string"}, {"name": "lower_limit", "description": "下限 区分0，传字符串", "in": "query", "required": false, "type": "string"}, {"name": "is_wms_store", "in": "query", "required": false, "type": "boolean"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/manual/newid": {"post": {"summary": "获取一个新盘点单id", "operationId": "MobileFranchiseeStockTake_GetNewStocktakeId", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeGetNewStocktakeIdResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeGetNewStocktakeIdRequest"}}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/product/tags": {"get": {"summary": "GetStocktakeTags获取盘点标签24", "operationId": "MobileFranchiseeStockTake_GetStocktakeTags", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeGetStocktakeTagsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "branch_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "tag_name", "in": "query", "required": false, "type": "string"}, {"name": "branch_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/product/tags/action": {"post": {"summary": "ActionStocktakeTags增加，删除，更新,获取盘点标签25", "operationId": "MobileFranchiseeStockTake_ActionStocktakeTags", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeActionStocktakeTagsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeActionStocktakeTagsRequest"}}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/product/tags/clean": {"post": {"summary": "DeleteStocktakeProductTags删除盘点商品标签条目26", "operationId": "MobileFranchiseeStockTake_DeleteStocktakeProductTags", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeDeleteStocktakeProductTagsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeDeleteStocktakeProductTagsRequest"}}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/tags/{tag_id}": {"get": {"summary": "获取一个盘点标签", "operationId": "MobileFranchiseeStockTake_GetStocktakeTagsById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeTags"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "tag_id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}": {"get": {"summary": "GetStocktakeByDocID 获取一个盘点单19", "operationId": "MobileFranchiseeStockTake_GetStocktakeByDocID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktake"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["MobileFranchiseeStockTake"]}, "post": {"summary": "PutStocktakeByDocID 提交盘点单明细商品数量，更新盘点单, 22", "operationId": "MobileFranchiseeStockTake_PutStocktakeByDocID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakePutStocktakeByDocIDResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"products": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakePutStocktakeProducts"}}, "lan": {"type": "string"}, "all_zero": {"type": "boolean", "title": "月盘单全盘零"}}}}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/advance": {"get": {"summary": "AdvanceStocktakeDiff  提前查看盘点损益32", "operationId": "MobileFranchiseeStockTake_AdvanceStocktakeDiff", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeAdvanceStocktakeDiffResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/approve": {"post": {"summary": "APPROVEDStocktakeByDocID  财务确认盘点单（status=APPROVED最终状态，）16", "operationId": "MobileFranchiseeStockTake_ApproveStocktakeByDocID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeApproveStocktakeByDocIDResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"type": {"type": "string"}, "approve_name": {"type": "string"}, "lan": {"type": "string"}, "all_zero": {"type": "boolean", "title": "是否全盘零"}, "source": {"type": "string"}}}}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/cancel": {"post": {"summary": "CancelStocktakeByDocID  作废盘点单（status=CANCELED）18", "operationId": "MobileFranchiseeStockTake_CancelStocktakeByDocID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeCancelStocktakeByDocIDResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/confirm": {"post": {"summary": "备注:盘点商品在INITED和REJECTED状态可以填写数量，CONFITRMED，不能。\nConfirmStocktakeByDocID 确认盘点单（status=CONFITRMED，核算完库存后'FINALIZED'）15", "operationId": "MobileFranchiseeStockTake_ConfirmStocktakeByDocID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeConfirmStocktakeByDocIDResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "check", "in": "query", "required": false, "type": "boolean"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/confirm/check": {"post": {"operationId": "MobileFranchiseeStockTake_CheckStocktakeByDocID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "check", "description": "是否已经检查过", "in": "query", "required": false, "type": "boolean"}, {"name": "branch_type", "description": "区分门店仓库 WAREHOUSE/STORE", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/init/check": {"post": {"summary": "CheckedStocktakeByDocID 检查完成盘点单23", "operationId": "MobileFranchiseeStockTake_CheckedStocktakeByDocID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckedStocktakeByDocIDResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"branch_type": {"type": "string", "title": "区分门店/仓库 WAREHOUSE/STORE"}}}}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/log": {"get": {"summary": "盘点历史记录", "operationId": "MobileFranchiseeStockTake_GetStocktakeLog", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeGetStocktakeLogResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/product": {"get": {"summary": "GetStocktakeProduct 查询盘点单明细商品21", "operationId": "MobileFranchiseeStockTake_GetStocktakeProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeGetStocktakeProductResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "include_unit", "description": "是否包含核算单位", "in": "query", "required": false, "type": "boolean"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean"}, {"name": "category_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "storage_type", "in": "query", "required": false, "type": "string"}, {"name": "product_name", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "include_barcode", "in": "query", "required": false, "type": "boolean"}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/reject": {"post": {"summary": "RejectStocktakeProduct 财务驳回提交后的部分盘点单明细商品（status=REJECTED，部分商品status=REJECTED）17", "operationId": "MobileFranchiseeStockTake_RejectStocktakeProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeRejectStocktakeProductResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"reason": {"type": "string"}, "lan": {"type": "string"}}}}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/stocktake/{doc_id}/submit": {"post": {"summary": "SubmitStocktakeByDocID 提交盘点单28", "operationId": "MobileFranchiseeStockTake_SubmitStocktakeByDocID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeSubmitStocktakeByDocIDResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "properties": {"submit_name": {"type": "string"}, "lan": {"type": "string"}, "all_zero": {"type": "boolean", "title": "是否全盘零"}, "source": {"type": "string"}}}}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/store/query/stocktake/category": {"get": {"summary": "GetStocktakeProductCategoryByStoreID 查询门店可盘点商品分类", "operationId": "MobileFranchiseeStockTake_GetStocktakeProductCategoryByStoreID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeGetStocktakeProductCategoryByStoreIDResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "store_id", "description": "门店id", "in": "query", "required": false, "type": "string", "format": "uint64"}], "tags": ["MobileFranchiseeStockTake"]}}, "/api/v2/supply/mobile/franchisee/store/query/stocktake/products": {"get": {"summary": "GetStocktakeProductByStoreID 查询门店可盘点商品", "operationId": "MobileFranchiseeStockTake_GetStocktakeProductByStoreID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/mobile_franchisee_stocktakeGetStocktakeProductByStoreIDResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "store_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean"}, {"name": "search", "in": "query", "required": false, "type": "string"}, {"name": "search_fields", "in": "query", "required": false, "type": "string"}, {"name": "category_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}, "collectionFormat": "multi"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "attach_price", "in": "query", "required": false, "type": "boolean"}, {"name": "exc_codes", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "in_codes", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "exc_upcs", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "in_upcs", "in": "query", "required": false, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "order_by", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}], "tags": ["MobileFranchiseeStockTake"]}}}, "definitions": {"ActionStocktakeTagsRequestAction": {"type": "string", "enum": ["get", "create", "delete", "update", "copy"], "default": "get"}, "GetStocktakeRequestS_STATUS": {"type": "string", "enum": ["OPENED", "CLOSED"], "default": "OPENED"}, "GetStocktakeRequestS_TYPE": {"type": "string", "enum": ["W", "D", "M"], "default": "W"}, "mobile_franchisee_stocktakeActionStocktakeTagsRequest": {"type": "object", "properties": {"tag_id": {"type": "string", "format": "uint64"}, "action": {"$ref": "#/definitions/ActionStocktakeTagsRequestAction"}, "name": {"type": "string"}, "branch_id": {"type": "string", "format": "uint64", "title": "复制新增操作对应原门店"}, "lan": {"type": "string"}, "branch_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "门店id列表"}, "region_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "区域id列表"}, "add_dimension": {"type": "string", "title": "添加维度：全市场/管理区域/门店->all/region/store"}, "tag_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "标签id-批量删除用"}, "origin_name": {"type": "string", "title": "批量修改中原标签名称"}, "copy_branch": {"type": "string", "format": "uint64", "title": "需要copy的门店"}}}, "mobile_franchisee_stocktakeActionStocktakeTagsResponse": {"type": "object", "properties": {"result": {"type": "boolean"}, "id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "user_id": {"type": "string", "format": "uint64"}, "created_by": {"type": "string", "format": "uint64"}, "updated_by": {"type": "string", "format": "uint64"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "branch_id": {"type": "string", "format": "uint64"}}}, "mobile_franchisee_stocktakeAdvanceStocktakeDiffResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeProduct"}}, "total": {"type": "string", "format": "uint64"}, "position_rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakePositionProducts"}}}}, "mobile_franchisee_stocktakeApproveStocktakeByDocIDResponse": {"type": "object", "properties": {"result": {"type": "boolean"}, "handler": {"type": "boolean"}, "adjust": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}, "receiving_diff": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}, "transfer": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}, "receiving": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}, "return": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}, "direct_receiving": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}, "direct_receiving_diff": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}, "direct_return": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}, "self_picking": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}}}, "mobile_franchisee_stocktakeCancelStocktakeByDocIDResponse": {"type": "object", "properties": {"result": {"type": "boolean"}}}, "mobile_franchisee_stocktakeCategoryItem": {"type": "object", "properties": {"category_id": {"type": "string", "format": "uint64"}, "category_name": {"type": "string"}, "product_count": {"type": "string", "format": "uint64"}}}, "mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "code": {"type": "string"}, "status": {"type": "string"}}}, "mobile_franchisee_stocktakeCheckStocktakeByDocIDResponse": {"type": "object", "properties": {"handler": {"type": "boolean"}, "adjust": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}, "receiving_diff": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}, "transfer": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}, "receiving": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}, "return": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}, "direct_receiving": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}, "direct_receiving_diff": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}, "direct_return": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}, "stocktake": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}, "demand": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}, "self_picking": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCheckStocktakeByDocIDDetail"}}}}, "mobile_franchisee_stocktakeCheckedStocktakeByDocIDResponse": {"type": "object", "properties": {"result": {"type": "boolean"}}, "title": "赶任务木想写注释了"}, "mobile_franchisee_stocktakeConfirmStocktakeByDocIDResponse": {"type": "object", "properties": {"result": {"type": "boolean"}}}, "mobile_franchisee_stocktakeDeleteStocktakeProductTagsRequest": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "mobile_franchisee_stocktakeDeleteStocktakeProductTagsResponse": {"type": "object", "properties": {"result": {"type": "boolean"}}}, "mobile_franchisee_stocktakeGetNewStocktakeIdRequest": {"type": "object"}, "mobile_franchisee_stocktakeGetNewStocktakeIdResponse": {"type": "object", "properties": {"new_doc_id": {"type": "string", "format": "uint64"}}}, "mobile_franchisee_stocktakeGetStocktakeBalanceProductGroupResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeBalanceProductGroup"}}}}, "mobile_franchisee_stocktakeGetStocktakeBalanceResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeBalance"}}, "total": {"type": "string", "format": "uint64"}}}, "mobile_franchisee_stocktakeGetStocktakeByIdsResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktake"}}, "total": {"type": "integer", "format": "int64"}}}, "mobile_franchisee_stocktakeGetStocktakeLogResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeLog"}}, "total": {"type": "string", "format": "uint64"}}}, "mobile_franchisee_stocktakeGetStocktakeProductByStoreIDResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeProductLine"}}, "total": {"type": "integer", "format": "int64"}, "inventory_unchanged_rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeProductLine"}}}}, "mobile_franchisee_stocktakeGetStocktakeProductCategoryByStoreIDResponse": {"type": "object", "properties": {"category_items": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeCategoryItem"}, "title": "门店id"}}}, "mobile_franchisee_stocktakeGetStocktakeProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeProduct"}}, "total": {"type": "string", "format": "uint64"}, "position_rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakePositionProducts"}}}}, "mobile_franchisee_stocktakeGetStocktakeRequestSTATUS": {"type": "string", "enum": ["STARTED", "INITED", "CANCELLED", "SUBMITTED", "REJECTED", "APPROVED", "CONFIRMED", "SUBMIT_0", "APPROVE_0"], "default": "STARTED", "title": "- STARTED: 计划生成初始状态\n - INITED: 创建状态\n - CANCELLED: 取消状态\n - SUBMITTED: 提交\n - REJECTED: 驳回\n - APPROVED: 财务核算检查后状态\n - CONFIRMED: 录入盘点单位确定状态\n - SUBMIT_0: 月盘全盘0提交"}, "mobile_franchisee_stocktakeGetStocktakeResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktake"}}, "total": {"type": "integer", "format": "int64"}}}, "mobile_franchisee_stocktakeGetStocktakeTagsResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeTags"}}}}, "mobile_franchisee_stocktakeLog": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "历史记录id"}, "status": {"type": "string", "title": "状态"}, "created_by": {"type": "string", "format": "uint64", "title": "操作人"}, "created_at": {"type": "string", "format": "date-time", "title": "操作时间"}, "reason": {"type": "string", "title": "原因"}, "created_name": {"type": "string", "title": "操作人名称"}}}, "mobile_franchisee_stocktakeManuallyCreateStocktakeProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "position_id": {"type": "string", "format": "uint64"}, "quantity": {"type": "number", "format": "double"}, "unit_id": {"type": "string", "format": "uint64"}, "tax_price": {"type": "number", "format": "double"}, "tax_rate": {"type": "number", "format": "double"}, "amount": {"type": "number", "format": "double"}, "diff_amount": {"type": "number", "format": "double"}, "sales_price": {"type": "number", "format": "double"}, "sales_amount": {"type": "number", "format": "double"}}}, "mobile_franchisee_stocktakeManuallyCreateStocktakeRequest": {"type": "object", "properties": {"target_date": {"type": "string", "format": "date-time"}, "branch_id": {"type": "string", "format": "uint64"}, "new_doc_id": {"type": "string", "format": "uint64"}, "calculate_inventory": {"type": "boolean"}, "product_list": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeManuallyCreateStocktakeProduct"}}, "source": {"type": "string"}}}, "mobile_franchisee_stocktakeManuallyCreateStocktakeResponse": {"type": "object", "properties": {"result": {"type": "boolean"}}}, "mobile_franchisee_stocktakeProductTagBi": {"type": "object", "properties": {"tag_unit_id": {"type": "string", "format": "uint64"}, "tag_unit_name": {"type": "string"}, "tag_quantity": {"type": "number", "format": "double"}, "tag_name": {"type": "string"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "accounting_quantity": {"type": "number", "format": "double"}, "id": {"type": "string", "format": "uint64"}}}, "mobile_franchisee_stocktakePutStocktakeByDocIDResponse": {"type": "object", "properties": {"result": {"type": "boolean"}}}, "mobile_franchisee_stocktakePutStocktakeProducts": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "quantity": {"type": "number", "format": "double", "title": "总数"}, "unit_id": {"type": "string", "format": "uint64"}, "tag_products": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeTagQuantity"}}, "is_pda": {"type": "boolean", "title": "是否pda盘点"}, "is_empty": {"type": "boolean", "title": "是否置0"}, "is_null": {"type": "boolean", "title": "PC盘点是否置空"}, "del_tag_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "删除的商品下标签id列表"}}}, "mobile_franchisee_stocktakeRecreateStocktakeDocRequest": {"type": "object", "properties": {"doc_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "calculate_inventory": {"type": "boolean"}, "schedule_name": {"type": "string"}, "remark": {"type": "string"}, "schedule_code": {"type": "string"}, "schedule_id": {"type": "string", "format": "uint64"}, "lan": {"type": "string"}}}, "mobile_franchisee_stocktakeRecreateStocktakeDocResponse": {"type": "object", "properties": {"result": {"type": "boolean"}, "restocktake_doc_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "has_recreate_doc_id_no_confirm": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "mobile_franchisee_stocktakeRejectStocktakeProductResponse": {"type": "object", "properties": {"result": {"type": "boolean"}}}, "mobile_franchisee_stocktakeST_total": {"type": "object", "properties": {"count": {"type": "string", "format": "uint64"}, "sum_quantity": {"type": "number", "format": "double"}, "sum_accounting_quantity": {"type": "number", "format": "double"}}}, "mobile_franchisee_stocktakeScheduleStatusPlan": {"type": "object", "properties": {"time": {"type": "string", "format": "date-time", "title": "报废单自动变更时间"}, "start_status": {"type": "array", "items": {"type": "string"}, "title": "状态变更开始状态"}, "end_status": {"type": "string", "title": "状态变更结束状态"}, "time_around": {"type": "string", "title": "状态自动变更时间周期"}, "doc_filter": {"type": "string", "title": "单据过滤条件"}}}, "mobile_franchisee_stocktakeStocktake": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "盘点单id"}, "partner_id": {"type": "string", "format": "uint64"}, "branch_batch_id": {"type": "string", "format": "uint64", "title": "盘点批次id"}, "branch_id": {"type": "string", "format": "uint64", "title": "盘点单门店id"}, "schedule_id": {"type": "string", "format": "uint64", "title": "盘点计划id"}, "branch_type": {"type": "string", "title": "盘点单门店类型"}, "calculate_inventory": {"type": "boolean", "title": "是否计算库存"}, "code": {"type": "string", "title": "盘点单编码"}, "remark": {"type": "string", "title": "标识符"}, "result_type": {"type": "string", "title": "结果类型"}, "schedule_code": {"type": "string", "title": "盘点计划编码"}, "st_diff_flag": {"type": "integer", "format": "int32", "title": "盘点差异标识"}, "status": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeSTATUS", "title": "盘点单状态"}, "process_status": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeSTATUS", "title": "过程状态"}, "store_secondary_id": {"type": "string", "format": "uint64", "title": "门店us_id"}, "type": {"type": "string", "title": "盘点单类型"}, "created_by": {"type": "string", "format": "uint64", "title": "创建者"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新者"}, "review_by": {"type": "string", "format": "uint64", "title": "校验者"}, "target_date": {"type": "string", "format": "date-time", "title": "盘点日期"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "user_id": {"type": "string", "format": "uint64"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "schedule_name": {"type": "string", "title": "盘点计划名称"}, "diff_err_message": {"type": "string"}, "month_err_message": {"type": "string"}, "original_code": {"type": "string"}, "original_doc_id": {"type": "string", "format": "uint64"}, "is_recreate": {"type": "boolean"}, "recreate_code": {"type": "string"}, "recreate_doc_id": {"type": "string", "format": "uint64"}, "submit_name": {"type": "string"}, "approve_name": {"type": "string"}, "stocktake_type": {"type": "string"}, "reject_reason": {"type": "string"}, "auto_invalid_time": {"type": "string", "format": "date-time", "title": "自动作废时间(utc)"}, "auto_approve_time": {"type": "string", "format": "date-time", "title": "自动审核时间(utc)"}, "is_empty": {"type": "boolean"}, "status_plan": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeScheduleStatusPlan"}, "title": "计划单据状态自动变更设置"}, "total_amount": {"type": "number", "format": "double", "title": "盘点单金额合计"}, "total_diff_amount": {"type": "number", "format": "double", "title": "盘点单差异金额合计"}, "total_sales_amount": {"type": "number", "format": "double", "title": "盘点单零售金额合计"}, "total_diff_sales_amount": {"type": "number", "format": "double", "title": "盘点单差异金额合计"}}}, "mobile_franchisee_stocktakeStocktakeBalance": {"type": "object", "properties": {"branch_batch_id": {"type": "string", "format": "uint64"}, "branch_id": {"type": "string", "format": "uint64"}, "branch_type": {"type": "string"}, "calculate_inventory": {"type": "boolean"}, "code": {"type": "string"}, "forecasting": {"type": "boolean"}, "forecasting_time": {"type": "string", "format": "date-time"}, "id": {"type": "string", "format": "uint64"}, "process_status": {"type": "string"}, "remark": {"type": "string"}, "result_type": {"type": "string"}, "review_by": {"type": "string", "format": "uint64"}, "schedule_code": {"type": "string"}, "schedule_id": {"type": "string", "format": "uint64"}, "st_diff_flag": {"type": "integer", "format": "int32"}, "status": {"type": "string"}, "store_secondary_id": {"type": "string", "format": "uint64"}, "target_date": {"type": "string", "format": "date-time"}, "type": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "user_id": {"type": "string", "format": "uint64"}, "created_by": {"type": "string", "format": "uint64"}, "updated_by": {"type": "string", "format": "uint64"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "schedule_name": {"type": "string"}, "diff_err_message": {"type": "string"}, "diff_jde_status": {"type": "string"}, "month_err_message": {"type": "string"}, "month_jde_status": {"type": "string"}, "jde_diff_code": {"type": "string"}, "jde_month_code": {"type": "string"}, "branch_name": {"type": "string"}, "branch_code": {"type": "string"}, "original_code": {"type": "string"}, "original_doc_id": {"type": "string", "format": "uint64"}, "is_recreate": {"type": "boolean"}, "recreate_code": {"type": "string"}, "recreate_doc_id": {"type": "string", "format": "uint64"}, "submit_name": {"type": "string"}, "approve_name": {"type": "string"}, "stocktake_type": {"type": "string"}, "request_id": {"type": "string", "format": "uint64"}, "total_amount": {"type": "number", "format": "double"}, "total_diff_amount": {"type": "number", "format": "double"}, "total_sales_amount": {"type": "number", "format": "double"}, "total_diff_sales_amount": {"type": "number", "format": "double", "title": "盘点单差异金额合计"}}}, "mobile_franchisee_stocktakeStocktakeBalanceProductGroup": {"type": "object", "properties": {"accounting_quantity": {"type": "number", "format": "double"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "accounting_unit_spec": {"type": "string"}, "diff_quantity": {"type": "number", "format": "double"}, "inventory_quantity": {"type": "number", "format": "double"}, "is_system": {"type": "boolean"}, "material_number": {"type": "string"}, "product_code": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "storage_type": {"type": "string"}, "tag_code": {"type": "string"}, "tag_name": {"type": "string"}, "unit_diff_quantity": {"type": "number", "format": "double"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "unit_rate": {"type": "number", "format": "double"}, "unit_spec": {"type": "string"}, "tag_details": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeTagProductBi"}}, "accounting_inventory_quantity": {"type": "number", "format": "double"}, "accounting_diff_quantity": {"type": "number", "format": "double"}, "position_id": {"type": "string", "format": "uint64"}, "position_code": {"type": "string"}, "position_name": {"type": "string"}}}, "mobile_franchisee_stocktakeStocktakeBalanceRegion": {"type": "object", "properties": {"accounting_inventory_quantity": {"type": "number", "format": "double"}, "accounting_quantity": {"type": "number", "format": "double"}, "accounting_unit_name": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeBalanceRegionDetails"}}, "unit_name": {"type": "string"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "inventory_quantity": {"type": "number", "format": "double"}, "accoinventory_quantity": {"type": "number", "format": "double"}, "diff_quantity": {"type": "number", "format": "double"}, "unit_diff_quantity": {"type": "number", "format": "double"}, "unit_rate": {"type": "number", "format": "double"}, "doc_type": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "store_id": {"type": "string", "format": "uint64"}, "store_name": {"type": "string"}, "store_code": {"type": "string"}, "product_code": {"type": "string"}, "accounting_diff_quantity": {"type": "number", "format": "double"}}}, "mobile_franchisee_stocktakeStocktakeBalanceRegionDetails": {"type": "object", "properties": {"accounting_inventory_quantity": {"type": "number", "format": "double"}, "accounting_quantity": {"type": "number", "format": "double"}, "accounting_unit_name": {"type": "string"}, "branch_name": {"type": "string"}, "diff_quantity": {"type": "number", "format": "double"}, "doc_type": {"type": "string"}, "inventory_quantity": {"type": "number", "format": "double"}, "material_number": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "store_id": {"type": "string", "format": "uint64"}, "store_name": {"type": "string"}, "tag_1": {"type": "string", "format": "uint64"}, "tag_1_name_pinyin": {"type": "string"}, "unit_diff_quantity": {"type": "number", "format": "double"}, "unit_name": {"type": "string"}, "unit_rate": {"type": "number", "format": "double"}}}, "mobile_franchisee_stocktakeStocktakeBalanceRegionResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeBalanceRegion"}}, "total": {"type": "string", "format": "uint64"}}}, "mobile_franchisee_stocktakeStocktakeBiDetailed": {"type": "object", "properties": {"accounting_quantity": {"type": "number", "format": "double"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "accounting_unit_spec": {"type": "string"}, "branch_1_name": {"type": "string"}, "branch_2_name": {"type": "string"}, "branch_id": {"type": "string", "format": "uint64"}, "category_code": {"type": "string"}, "category_id": {"type": "string", "format": "uint64"}, "category_name": {"type": "string"}, "code": {"type": "string", "title": "\"category_parent\""}, "deleted": {"type": "boolean"}, "diff_quantity": {"type": "number", "format": "double"}, "display_order": {"type": "string"}, "doc_id": {"type": "string", "format": "uint64"}, "extends": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "ignored": {"type": "boolean"}, "inventory_quantity": {"type": "number", "format": "double"}, "is_system": {"type": "boolean"}, "item_number": {"type": "string", "format": "uint64"}, "material_number": {"type": "string"}, "product_code": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "type": {"type": "string"}, "storage_type": {"type": "string"}, "store_code": {"type": "string"}, "store_name": {"type": "string"}, "target_date": {"type": "string", "format": "date-time"}, "unit_diff_quantity": {"type": "number", "format": "double"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "unit_rate": {"type": "number", "format": "double"}, "unit_spec": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "created_by": {"type": "string", "format": "uint64"}, "updated_by": {"type": "string", "format": "uint64"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "is_empty": {"type": "boolean"}, "is_pda": {"type": "boolean"}, "status": {"type": "string"}, "units": {"type": "string"}, "user_id": {"type": "string", "format": "uint64"}, "is_null": {"type": "boolean"}, "tag_quantity": {"type": "number", "format": "double"}, "product_tags": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeProductTagBi"}}, "is_enable": {"type": "boolean", "title": "商品状态、JDE盘点单号、规格、理论数量（加在盘点数量旁边）、盘点差异（百分比）、新建时间、操作人，提交时间、操作人。"}, "jde_code": {"type": "string"}, "spec": {"type": "string"}, "diff_percentage": {"type": "number", "format": "double"}, "created_time": {"type": "string", "format": "date-time"}, "created_user_name": {"type": "string"}, "submitted_time": {"type": "string", "format": "date-time"}, "submitted_user_name": {"type": "string"}, "branch_type": {"type": "string"}, "position_id": {"type": "string", "format": "uint64"}, "position_code": {"type": "string"}, "position_name": {"type": "string"}}}, "mobile_franchisee_stocktakeStocktakeBiDetailedResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeBiDetailed"}}, "total": {"$ref": "#/definitions/mobile_franchisee_stocktakeST_total"}}}, "mobile_franchisee_stocktakeStocktakeDiffReportResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeDiffReportRow"}}, "total": {"type": "string", "format": "uint64"}}}, "mobile_franchisee_stocktakeStocktakeDiffReportRow": {"type": "object", "properties": {"code": {"type": "string"}, "store_id": {"type": "string", "format": "uint64"}, "store_code": {"type": "string"}, "store_name": {"type": "string"}, "company_code": {"type": "string"}, "product_code": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "type": {"type": "string"}, "diff_quantity": {"type": "number", "format": "double"}, "quantity": {"type": "number", "format": "double"}, "accounting_quantity": {"type": "number", "format": "double"}, "inventory_quantity": {"type": "number", "format": "double"}, "diff_quantity_percentage": {"type": "number", "format": "double"}, "target_date": {"type": "string", "format": "date-time"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "unit_rate": {"type": "number", "format": "double"}, "unit_code": {"type": "string"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "accounting_unit_code": {"type": "string"}, "jde_diff_code": {"type": "string"}, "company_name": {"type": "string"}, "accounting_inventory_quantity": {"type": "number", "format": "double"}, "accounting_diff_quantity": {"type": "number", "format": "double"}, "unit_spec": {"type": "string"}, "status": {"type": "string"}, "schedule_code": {"type": "string"}}}, "mobile_franchisee_stocktakeStocktakePositionProducts": {"type": "object", "properties": {"position_id": {"type": "string", "format": "uint64"}, "position_code": {"type": "string"}, "position_name": {"type": "string"}, "products": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeProduct"}}, "total": {"type": "string", "format": "uint64"}}, "title": "position"}, "mobile_franchisee_stocktakeStocktakeProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "盘点单详情id"}, "partner_id": {"type": "string", "format": "uint64"}, "doc_id": {"type": "string", "format": "uint64", "title": "盘点单id"}, "branch_id": {"type": "string", "format": "uint64", "title": "门店id"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "accounting_quantity": {"type": "number", "format": "double", "title": "核算数量"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "核算单位id"}, "accounting_unit_name": {"type": "string", "title": "核算单位名字"}, "accounting_unit_spec": {"type": "string", "title": "核算单位分类"}, "code": {"type": "string", "title": "盘点单编号"}, "deleted": {"type": "boolean"}, "diff_quantity": {"type": "number", "format": "double", "title": "盘点差异量"}, "display_order": {"type": "string", "title": "盘点排序码"}, "extends": {"type": "string"}, "ignored": {"type": "boolean"}, "inventory_quantity": {"type": "number", "format": "double", "title": "理论库存"}, "is_system": {"type": "boolean"}, "item_number": {"type": "string", "format": "uint64"}, "material_number": {"type": "string", "title": "物料编码"}, "product_code": {"type": "string", "title": "商品编码"}, "product_name": {"type": "string", "title": "商品名字"}, "quantity": {"type": "number", "format": "double", "title": "盘点数量"}, "st_type": {"type": "string", "title": "盘点单类型"}, "storage_type": {"type": "string", "title": "储藏类型"}, "unit_diff_quantity": {"type": "number", "format": "double", "title": "核算差异量"}, "unit_id": {"type": "string", "format": "uint64", "title": "盘点单位id"}, "unit_name": {"type": "string", "title": "盘点单位名字"}, "unit_rate": {"type": "number", "format": "double", "title": "盘点单位比率"}, "unit_spec": {"type": "string", "title": "盘点单位类型"}, "units": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeProductUnits"}, "title": "盘点单位列表"}, "created_by": {"type": "string", "format": "uint64", "title": "创建者"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新者"}, "target_date": {"type": "string", "format": "date-time", "title": "盘点单日期"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "user_id": {"type": "string", "format": "uint64"}, "status": {"type": "string", "title": "盘点商品状态'INITED','REJECTED','CONFIRMED','FINALIZED','APPROVED'"}, "is_pda": {"type": "boolean"}, "product_tags": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeProductTagName"}, "title": "商品标签明细"}, "is_empty": {"type": "boolean"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "is_null": {"type": "boolean"}, "tag_quantity": {"type": "number", "format": "double"}, "convert_accounting_quantity": {"type": "number", "format": "double"}, "is_bom": {"type": "boolean"}, "allow_stocktake_edit": {"type": "boolean"}, "spec": {"type": "string", "title": "商品规格"}, "diff_price": {"type": "number", "format": "double", "title": "损益金额"}, "barcode": {"type": "array", "items": {"type": "string"}}, "category_id": {"type": "string", "format": "uint64", "title": "商品类别id"}, "position_id": {"type": "string", "format": "uint64"}, "category_name": {"type": "string"}, "tax_rate": {"type": "number", "format": "double", "title": "税率"}, "tax_price": {"type": "number", "format": "double", "title": "含税单价"}, "amount": {"type": "number", "format": "double", "title": "含税金额"}, "diff_amount": {"type": "number", "format": "double", "title": "差异金额"}, "cost_price": {"type": "number", "format": "double", "title": "成本价"}, "sales_amount": {"type": "number", "format": "double", "title": "零售金额"}, "sales_price": {"type": "number", "format": "double", "title": "零售价"}, "diff_sales_amount": {"type": "number", "format": "double"}}}, "mobile_franchisee_stocktakeStocktakeProductLine": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "loss_report_order": {"type": "string"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "model_name": {"type": "string"}, "storage_type": {"type": "string"}, "category_id": {"type": "string", "format": "uint64", "title": "单位分类"}, "category_name": {"type": "string"}, "units": {"type": "array", "items": {"$ref": "#/definitions/mobile_franchisee_stocktakeStocktakeProductUint"}}, "barcode": {"type": "array", "items": {"type": "string"}}, "tax_price": {"type": "number", "format": "double", "title": "含税单价"}, "sales_price": {"type": "number", "format": "double", "title": "零售价"}, "real_inventory_qty": {"type": "number", "format": "double", "title": "实时库存数量"}}}, "mobile_franchisee_stocktakeStocktakeProductTagName": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "doc_id": {"type": "string", "format": "uint64"}, "stp_id": {"type": "string", "format": "uint64"}, "product_id": {"type": "string", "format": "uint64"}, "tag_id": {"type": "string", "format": "uint64"}, "tag_name": {"type": "string"}, "tag_quantity": {"type": "number", "format": "double"}, "accounting_quantity": {"type": "number", "format": "double"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_spec": {"type": "string"}, "unit_name": {"type": "string"}, "unit_rate": {"type": "number", "format": "double"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "accounting_unit_spec": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "user_id": {"type": "string", "format": "uint64"}, "created_by": {"type": "string", "format": "uint64"}, "updated_by": {"type": "string", "format": "uint64"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "tax_rate": {"type": "number", "format": "double"}, "tax_price": {"type": "number", "format": "double"}, "cost_price": {"type": "number", "format": "double"}, "sales_price": {"type": "number", "format": "double"}}}, "mobile_franchisee_stocktakeStocktakeProductUint": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "数据id"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "scope_id": {"type": "string", "format": "uint64", "title": "scope_id"}, "name": {"type": "string"}, "code": {"type": "string"}, "updated": {"type": "string"}, "rate": {"type": "number", "format": "double"}, "default": {"type": "boolean"}, "order": {"type": "boolean"}, "purchase": {"type": "boolean"}, "sales": {"type": "boolean"}, "stocktake": {"type": "boolean"}, "bom": {"type": "boolean"}, "default_stocktake": {"type": "boolean"}, "transfer": {"type": "boolean"}, "tax_rate": {"type": "number", "format": "double"}, "tax_price": {"type": "number", "format": "double"}, "cost_price": {"type": "number", "format": "double"}, "sales_price": {"type": "number", "format": "double"}}}, "mobile_franchisee_stocktakeStocktakeProductUnits": {"type": "object", "properties": {"unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "unit_spec": {"type": "string"}, "unit_rate": {"type": "number", "format": "double"}, "tax_rate": {"type": "number", "format": "double"}, "tax_price": {"type": "number", "format": "double"}, "cost_price": {"type": "number", "format": "double"}, "sales_price": {"type": "number", "format": "double"}, "default": {"type": "boolean"}, "stocktake": {"type": "boolean"}}}, "mobile_franchisee_stocktakeStocktakeSTATUS": {"type": "string", "enum": ["STARTED", "INITED", "CANCELLED", "SUBMITTED", "REJECTED", "APPROVED", "CONFIRMED", "SUBMIT_0", "APPROVE_0"], "default": "STARTED", "title": "- STARTED: 计划生成初始状态\n - INITED: 新建状态\n - CANCELLED: 取消状态\n - SUBMITTED: 提交\n - REJECTED: 驳回\n - APPROVED: 财务核算检查后状态\n - CONFIRMED: 录入盘点单位确定状态\n - SUBMIT_0: 月盘全盘0提交"}, "mobile_franchisee_stocktakeStocktakeTags": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "user_id": {"type": "string", "format": "uint64"}, "created_by": {"type": "string", "format": "uint64"}, "updated_by": {"type": "string", "format": "uint64"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "branch_id": {"type": "string", "format": "uint64"}, "branch_name": {"type": "string"}}}, "mobile_franchisee_stocktakeSubmitStocktakeByDocIDResponse": {"type": "object", "properties": {"result": {"type": "boolean"}}}, "mobile_franchisee_stocktakeTagProductBi": {"type": "object", "properties": {"tag_name": {"type": "string"}, "tag_quantity": {"type": "number", "format": "double"}, "tag_unit_name": {"type": "string"}, "tag_uint_rate": {"type": "number", "format": "double"}}}, "mobile_franchisee_stocktakeTagQuantity": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "标签id"}, "tag_id": {"type": "string", "format": "uint64"}, "tag_name": {"type": "string", "title": "标签名"}, "tag_quantity": {"type": "number", "format": "double", "title": "标签名对应数量"}, "unit_id": {"type": "string", "format": "uint64", "title": "标签盘点单位"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}}}