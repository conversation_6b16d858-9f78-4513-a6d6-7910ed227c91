# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import attachments_pb2 as attachments__pb2


class AttachmentsServiceStub(object):
  """附件管理相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateAttachments = channel.unary_unary(
        '/attachments.AttachmentsService/CreateAttachments',
        request_serializer=attachments__pb2.NewAttachmentsList.SerializeToString,
        response_deserializer=attachments__pb2.Response.FromString,
        )
    self.GetAttachmentsByDocId = channel.unary_unary(
        '/attachments.AttachmentsService/GetAttachmentsByDocId',
        request_serializer=attachments__pb2.DocIdRequest.SerializeToString,
        response_deserializer=attachments__pb2.AttachmentsList.FromString,
        )
    self.UpdateAttachmentsByDocIdMobile = channel.unary_unary(
        '/attachments.AttachmentsService/UpdateAttachmentsByDocIdMobile',
        request_serializer=attachments__pb2.NewAttachmentsList.SerializeToString,
        response_deserializer=attachments__pb2.Response.FromString,
        )
    self.UpdateAttachmentsByDocId = channel.unary_unary(
        '/attachments.AttachmentsService/UpdateAttachmentsByDocId',
        request_serializer=attachments__pb2.NewAttachmentsList.SerializeToString,
        response_deserializer=attachments__pb2.Response.FromString,
        )


class AttachmentsServiceServicer(object):
  """附件管理相关服务
  """

  def CreateAttachments(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetAttachmentsByDocId(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateAttachmentsByDocIdMobile(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateAttachmentsByDocId(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_AttachmentsServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateAttachments': grpc.unary_unary_rpc_method_handler(
          servicer.CreateAttachments,
          request_deserializer=attachments__pb2.NewAttachmentsList.FromString,
          response_serializer=attachments__pb2.Response.SerializeToString,
      ),
      'GetAttachmentsByDocId': grpc.unary_unary_rpc_method_handler(
          servicer.GetAttachmentsByDocId,
          request_deserializer=attachments__pb2.DocIdRequest.FromString,
          response_serializer=attachments__pb2.AttachmentsList.SerializeToString,
      ),
      'UpdateAttachmentsByDocIdMobile': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateAttachmentsByDocIdMobile,
          request_deserializer=attachments__pb2.NewAttachmentsList.FromString,
          response_serializer=attachments__pb2.Response.SerializeToString,
      ),
      'UpdateAttachmentsByDocId': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateAttachmentsByDocId,
          request_deserializer=attachments__pb2.NewAttachmentsList.FromString,
          response_serializer=attachments__pb2.Response.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'attachments.AttachmentsService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
