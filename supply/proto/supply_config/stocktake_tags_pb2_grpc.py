# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from supply_config import stocktake_tags_pb2 as supply__config_dot_stocktake__tags__pb2


class StockTakeTagsStub(object):
  """运营设置模块-盘点标签服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetStocktakeTags = channel.unary_unary(
        '/supply_config.StockTakeTags/GetStocktakeTags',
        request_serializer=supply__config_dot_stocktake__tags__pb2.GetStocktakeTagsRequest.SerializeToString,
        response_deserializer=supply__config_dot_stocktake__tags__pb2.GetStocktakeTagsResponse.FromString,
        )
    self.ActionStocktakeTags = channel.unary_unary(
        '/supply_config.StockTakeTags/ActionStocktakeTags',
        request_serializer=supply__config_dot_stocktake__tags__pb2.ActionStocktakeTagsRequest.SerializeToString,
        response_deserializer=supply__config_dot_stocktake__tags__pb2.ActionStocktakeTagsResponse.FromString,
        )
    self.GetStocktakeTagsById = channel.unary_unary(
        '/supply_config.StockTakeTags/GetStocktakeTagsById',
        request_serializer=supply__config_dot_stocktake__tags__pb2.GetStocktakeTagsByIdRequest.SerializeToString,
        response_deserializer=supply__config_dot_stocktake__tags__pb2.StocktakeTags.FromString,
        )


class StockTakeTagsServicer(object):
  """运营设置模块-盘点标签服务
  """

  def GetStocktakeTags(self, request, context):
    """获取盘点标签
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ActionStocktakeTags(self, request, context):
    """增加，删除，更新,获取盘点标签
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStocktakeTagsById(self, request, context):
    """获取一个盘点标签
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_StockTakeTagsServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetStocktakeTags': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktakeTags,
          request_deserializer=supply__config_dot_stocktake__tags__pb2.GetStocktakeTagsRequest.FromString,
          response_serializer=supply__config_dot_stocktake__tags__pb2.GetStocktakeTagsResponse.SerializeToString,
      ),
      'ActionStocktakeTags': grpc.unary_unary_rpc_method_handler(
          servicer.ActionStocktakeTags,
          request_deserializer=supply__config_dot_stocktake__tags__pb2.ActionStocktakeTagsRequest.FromString,
          response_serializer=supply__config_dot_stocktake__tags__pb2.ActionStocktakeTagsResponse.SerializeToString,
      ),
      'GetStocktakeTagsById': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktakeTagsById,
          request_deserializer=supply__config_dot_stocktake__tags__pb2.GetStocktakeTagsByIdRequest.FromString,
          response_serializer=supply__config_dot_stocktake__tags__pb2.StocktakeTags.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'supply_config.StockTakeTags', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
