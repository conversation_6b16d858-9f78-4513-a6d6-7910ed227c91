syntax = "proto3";

package supply_config;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// 运营设置模块-盘点标签服务
service StockTakeTags {

    // 获取盘点标签
    rpc GetStocktakeTags (GetStocktakeTagsRequest) returns (GetStocktakeTagsResponse) {
        option (google.api.http) = { get: "/api/v2/supply/config/stocktake/tags"};
    }
    // 增加，删除，更新,获取盘点标签
    rpc ActionStocktakeTags (ActionStocktakeTagsRequest) returns (ActionStocktakeTagsResponse) {
        option (google.api.http) = { put: "/api/v2/supply/config/stocktake/tags/action" body: "*"};
    }
    // 获取一个盘点标签
    rpc GetStocktakeTagsById (GetStocktakeTagsByIdRequest) returns (StocktakeTags) {
        option (google.api.http) = { get: "/api/v2/supply/config/stocktake/tags/{tag_id}" body: "*"};
    }
}

message GetStocktakeTagsRequest {
    uint64 branch_id = 1;
    string lan = 2;
    string tag_name = 3;
    repeated uint64 branch_ids = 4;
}
message StocktakeTags {
    uint64 id = 1;
    string name = 2;
    uint64 partner_id = 3;
    uint64 user_id = 4;
    uint64 created_by = 5;
    uint64 updated_by = 6;
    google.protobuf.Timestamp created_at = 7;
    google.protobuf.Timestamp updated_at = 8;
    string created_name = 9;
    string updated_name = 10;
    uint64 branch_id = 11;
    string branch_name = 12;
}
message GetStocktakeTagsResponse {
    repeated StocktakeTags rows = 1;
}
message ActionStocktakeTagsRequest {
    uint64 tag_id = 1;
    enum Action {
        get = 0;     // 弃用，查询接口拆开了
        create = 1;
        delete = 2;
        update = 3;
        copy = 4;
    }
    Action action = 2;
    string name = 3;
    // 复制新增操作对应原门店
    uint64 branch_id = 4;
    string lan = 5;
    // 门店id列表
    repeated uint64 branch_ids = 6;
    // 区域id列表
    repeated uint64 region_ids = 7;
    // 添加维度：全市场/管理区域/门店->all/region/store
    string add_dimension = 8;
    // 标签id-批量删除用
    repeated uint64 tag_ids = 9;
    // 批量修改中原标签名称
    string origin_name = 10;
    // 需要copy的门店
    uint64 copy_branch = 11;
}

message ActionStocktakeTagsResponse {
    bool result = 1;
    uint64 id = 2;
    string name = 3;
    uint64 partner_id = 4;
    uint64 user_id = 5;
    uint64 created_by = 6;
    uint64 updated_by = 7;
    google.protobuf.Timestamp created_at = 8;
    google.protobuf.Timestamp updated_at = 9;
    string created_name = 10;
    string updated_name = 11;
    uint64 branch_id = 12;
}

message GetStocktakeTagsByIdRequest {
    uint64 tag_id = 1;
}