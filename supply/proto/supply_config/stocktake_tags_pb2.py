# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: supply_config/stocktake_tags.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='supply_config/stocktake_tags.proto',
  package='supply_config',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\"supply_config/stocktake_tags.proto\x12\rsupply_config\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"_\n\x17GetStocktakeTagsRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\x12\x10\n\x08tag_name\x18\x03 \x01(\t\x12\x12\n\nbranch_ids\x18\x04 \x03(\x04\"\xaa\x02\n\rStocktakeTags\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x12\n\npartner_id\x18\x03 \x01(\x04\x12\x0f\n\x07user_id\x18\x04 \x01(\x04\x12\x12\n\ncreated_by\x18\x05 \x01(\x04\x12\x12\n\nupdated_by\x18\x06 \x01(\x04\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\t \x01(\t\x12\x14\n\x0cupdated_name\x18\n \x01(\t\x12\x11\n\tbranch_id\x18\x0b \x01(\x04\x12\x13\n\x0b\x62ranch_name\x18\x0c \x01(\t\"F\n\x18GetStocktakeTagsResponse\x12*\n\x04rows\x18\x01 \x03(\x0b\x32\x1c.supply_config.StocktakeTags\"\xd7\x02\n\x1a\x41\x63tionStocktakeTagsRequest\x12\x0e\n\x06tag_id\x18\x01 \x01(\x04\x12@\n\x06\x61\x63tion\x18\x02 \x01(\x0e\x32\x30.supply_config.ActionStocktakeTagsRequest.Action\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x11\n\tbranch_id\x18\x04 \x01(\x04\x12\x0b\n\x03lan\x18\x05 \x01(\t\x12\x12\n\nbranch_ids\x18\x06 \x03(\x04\x12\x12\n\nregion_ids\x18\x07 \x03(\x04\x12\x15\n\radd_dimension\x18\x08 \x01(\t\x12\x0f\n\x07tag_ids\x18\t \x03(\x04\x12\x13\n\x0borigin_name\x18\n \x01(\t\x12\x13\n\x0b\x63opy_branch\x18\x0b \x01(\x04\"?\n\x06\x41\x63tion\x12\x07\n\x03get\x10\x00\x12\n\n\x06\x63reate\x10\x01\x12\n\n\x06\x64\x65lete\x10\x02\x12\n\n\x06update\x10\x03\x12\x08\n\x04\x63opy\x10\x04\"\xb3\x02\n\x1b\x41\x63tionStocktakeTagsResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\n\n\x02id\x18\x02 \x01(\x04\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x12\n\npartner_id\x18\x04 \x01(\x04\x12\x0f\n\x07user_id\x18\x05 \x01(\x04\x12\x12\n\ncreated_by\x18\x06 \x01(\x04\x12\x12\n\nupdated_by\x18\x07 \x01(\x04\x12.\n\ncreated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\n \x01(\t\x12\x14\n\x0cupdated_name\x18\x0b \x01(\t\x12\x11\n\tbranch_id\x18\x0c \x01(\x04\"-\n\x1bGetStocktakeTagsByIdRequest\x12\x0e\n\x06tag_id\x18\x01 \x01(\x04\x32\xe7\x03\n\rStockTakeTags\x12\x91\x01\n\x10GetStocktakeTags\x12&.supply_config.GetStocktakeTagsRequest\x1a\'.supply_config.GetStocktakeTagsResponse\",\x82\xd3\xe4\x93\x02&\x12$/api/v2/supply/config/stocktake/tags\x12\xa4\x01\n\x13\x41\x63tionStocktakeTags\x12).supply_config.ActionStocktakeTagsRequest\x1a*.supply_config.ActionStocktakeTagsResponse\"6\x82\xd3\xe4\x93\x02\x30\x1a+/api/v2/supply/config/stocktake/tags/action:\x01*\x12\x9a\x01\n\x14GetStocktakeTagsById\x12*.supply_config.GetStocktakeTagsByIdRequest\x1a\x1c.supply_config.StocktakeTags\"8\x82\xd3\xe4\x93\x02\x32\x12-/api/v2/supply/config/stocktake/tags/{tag_id}:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])



_ACTIONSTOCKTAKETAGSREQUEST_ACTION = _descriptor.EnumDescriptor(
  name='Action',
  full_name='supply_config.ActionStocktakeTagsRequest.Action',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='get', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='create', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='delete', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='update', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='copy', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=867,
  serialized_end=930,
)
_sym_db.RegisterEnumDescriptor(_ACTIONSTOCKTAKETAGSREQUEST_ACTION)


_GETSTOCKTAKETAGSREQUEST = _descriptor.Descriptor(
  name='GetStocktakeTagsRequest',
  full_name='supply_config.GetStocktakeTagsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='supply_config.GetStocktakeTagsRequest.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='supply_config.GetStocktakeTagsRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='supply_config.GetStocktakeTagsRequest.tag_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='supply_config.GetStocktakeTagsRequest.branch_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=116,
  serialized_end=211,
)


_STOCKTAKETAGS = _descriptor.Descriptor(
  name='StocktakeTags',
  full_name='supply_config.StocktakeTags',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='supply_config.StocktakeTags.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='supply_config.StocktakeTags.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='supply_config.StocktakeTags.partner_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='supply_config.StocktakeTags.user_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='supply_config.StocktakeTags.created_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='supply_config.StocktakeTags.updated_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='supply_config.StocktakeTags.created_at', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='supply_config.StocktakeTags.updated_at', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='supply_config.StocktakeTags.created_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='supply_config.StocktakeTags.updated_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='supply_config.StocktakeTags.branch_id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='supply_config.StocktakeTags.branch_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=214,
  serialized_end=512,
)


_GETSTOCKTAKETAGSRESPONSE = _descriptor.Descriptor(
  name='GetStocktakeTagsResponse',
  full_name='supply_config.GetStocktakeTagsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='supply_config.GetStocktakeTagsResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=514,
  serialized_end=584,
)


_ACTIONSTOCKTAKETAGSREQUEST = _descriptor.Descriptor(
  name='ActionStocktakeTagsRequest',
  full_name='supply_config.ActionStocktakeTagsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_id', full_name='supply_config.ActionStocktakeTagsRequest.tag_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='supply_config.ActionStocktakeTagsRequest.action', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='supply_config.ActionStocktakeTagsRequest.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='supply_config.ActionStocktakeTagsRequest.branch_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='supply_config.ActionStocktakeTagsRequest.lan', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='supply_config.ActionStocktakeTagsRequest.branch_ids', index=5,
      number=6, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_ids', full_name='supply_config.ActionStocktakeTagsRequest.region_ids', index=6,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='add_dimension', full_name='supply_config.ActionStocktakeTagsRequest.add_dimension', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_ids', full_name='supply_config.ActionStocktakeTagsRequest.tag_ids', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='origin_name', full_name='supply_config.ActionStocktakeTagsRequest.origin_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='copy_branch', full_name='supply_config.ActionStocktakeTagsRequest.copy_branch', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _ACTIONSTOCKTAKETAGSREQUEST_ACTION,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=587,
  serialized_end=930,
)


_ACTIONSTOCKTAKETAGSRESPONSE = _descriptor.Descriptor(
  name='ActionStocktakeTagsResponse',
  full_name='supply_config.ActionStocktakeTagsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='supply_config.ActionStocktakeTagsResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='supply_config.ActionStocktakeTagsResponse.id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='supply_config.ActionStocktakeTagsResponse.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='supply_config.ActionStocktakeTagsResponse.partner_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='supply_config.ActionStocktakeTagsResponse.user_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='supply_config.ActionStocktakeTagsResponse.created_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='supply_config.ActionStocktakeTagsResponse.updated_by', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='supply_config.ActionStocktakeTagsResponse.created_at', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='supply_config.ActionStocktakeTagsResponse.updated_at', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='supply_config.ActionStocktakeTagsResponse.created_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='supply_config.ActionStocktakeTagsResponse.updated_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='supply_config.ActionStocktakeTagsResponse.branch_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=933,
  serialized_end=1240,
)


_GETSTOCKTAKETAGSBYIDREQUEST = _descriptor.Descriptor(
  name='GetStocktakeTagsByIdRequest',
  full_name='supply_config.GetStocktakeTagsByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_id', full_name='supply_config.GetStocktakeTagsByIdRequest.tag_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1242,
  serialized_end=1287,
)

_STOCKTAKETAGS.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKETAGS.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKETAGSRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKETAGS
_ACTIONSTOCKTAKETAGSREQUEST.fields_by_name['action'].enum_type = _ACTIONSTOCKTAKETAGSREQUEST_ACTION
_ACTIONSTOCKTAKETAGSREQUEST_ACTION.containing_type = _ACTIONSTOCKTAKETAGSREQUEST
_ACTIONSTOCKTAKETAGSRESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ACTIONSTOCKTAKETAGSRESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['GetStocktakeTagsRequest'] = _GETSTOCKTAKETAGSREQUEST
DESCRIPTOR.message_types_by_name['StocktakeTags'] = _STOCKTAKETAGS
DESCRIPTOR.message_types_by_name['GetStocktakeTagsResponse'] = _GETSTOCKTAKETAGSRESPONSE
DESCRIPTOR.message_types_by_name['ActionStocktakeTagsRequest'] = _ACTIONSTOCKTAKETAGSREQUEST
DESCRIPTOR.message_types_by_name['ActionStocktakeTagsResponse'] = _ACTIONSTOCKTAKETAGSRESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeTagsByIdRequest'] = _GETSTOCKTAKETAGSBYIDREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetStocktakeTagsRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeTagsRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKETAGSREQUEST,
  __module__ = 'supply_config.stocktake_tags_pb2'
  # @@protoc_insertion_point(class_scope:supply_config.GetStocktakeTagsRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeTagsRequest)

StocktakeTags = _reflection.GeneratedProtocolMessageType('StocktakeTags', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKETAGS,
  __module__ = 'supply_config.stocktake_tags_pb2'
  # @@protoc_insertion_point(class_scope:supply_config.StocktakeTags)
  ))
_sym_db.RegisterMessage(StocktakeTags)

GetStocktakeTagsResponse = _reflection.GeneratedProtocolMessageType('GetStocktakeTagsResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKETAGSRESPONSE,
  __module__ = 'supply_config.stocktake_tags_pb2'
  # @@protoc_insertion_point(class_scope:supply_config.GetStocktakeTagsResponse)
  ))
_sym_db.RegisterMessage(GetStocktakeTagsResponse)

ActionStocktakeTagsRequest = _reflection.GeneratedProtocolMessageType('ActionStocktakeTagsRequest', (_message.Message,), dict(
  DESCRIPTOR = _ACTIONSTOCKTAKETAGSREQUEST,
  __module__ = 'supply_config.stocktake_tags_pb2'
  # @@protoc_insertion_point(class_scope:supply_config.ActionStocktakeTagsRequest)
  ))
_sym_db.RegisterMessage(ActionStocktakeTagsRequest)

ActionStocktakeTagsResponse = _reflection.GeneratedProtocolMessageType('ActionStocktakeTagsResponse', (_message.Message,), dict(
  DESCRIPTOR = _ACTIONSTOCKTAKETAGSRESPONSE,
  __module__ = 'supply_config.stocktake_tags_pb2'
  # @@protoc_insertion_point(class_scope:supply_config.ActionStocktakeTagsResponse)
  ))
_sym_db.RegisterMessage(ActionStocktakeTagsResponse)

GetStocktakeTagsByIdRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeTagsByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKETAGSBYIDREQUEST,
  __module__ = 'supply_config.stocktake_tags_pb2'
  # @@protoc_insertion_point(class_scope:supply_config.GetStocktakeTagsByIdRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeTagsByIdRequest)



_STOCKTAKETAGS = _descriptor.ServiceDescriptor(
  name='StockTakeTags',
  full_name='supply_config.StockTakeTags',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=1290,
  serialized_end=1777,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetStocktakeTags',
    full_name='supply_config.StockTakeTags.GetStocktakeTags',
    index=0,
    containing_service=None,
    input_type=_GETSTOCKTAKETAGSREQUEST,
    output_type=_GETSTOCKTAKETAGSRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\022$/api/v2/supply/config/stocktake/tags'),
  ),
  _descriptor.MethodDescriptor(
    name='ActionStocktakeTags',
    full_name='supply_config.StockTakeTags.ActionStocktakeTags',
    index=1,
    containing_service=None,
    input_type=_ACTIONSTOCKTAKETAGSREQUEST,
    output_type=_ACTIONSTOCKTAKETAGSRESPONSE,
    serialized_options=_b('\202\323\344\223\0020\032+/api/v2/supply/config/stocktake/tags/action:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktakeTagsById',
    full_name='supply_config.StockTakeTags.GetStocktakeTagsById',
    index=2,
    containing_service=None,
    input_type=_GETSTOCKTAKETAGSBYIDREQUEST,
    output_type=_STOCKTAKETAGS,
    serialized_options=_b('\202\323\344\223\0022\022-/api/v2/supply/config/stocktake/tags/{tag_id}:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_STOCKTAKETAGS)

DESCRIPTOR.services_by_name['StockTakeTags'] = _STOCKTAKETAGS

# @@protoc_insertion_point(module_scope)
