# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: uuid.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='uuid.proto',
  package='rpc',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\nuuid.proto\x12\x03rpc\"\x06\n\x04Null\"\x13\n\x04Pong\x12\x0b\n\x03msg\x18\x01 \x01(\t\"\x1d\n\x0eMutiNewRequest\x12\x0b\n\x03len\x18\x01 \x01(\x05\"\x1b\n\x08NewReply\x12\x0f\n\x07payload\x18\x01 \x01(\x03\"\x1f\n\x0cMutiNewReply\x12\x0f\n\x07payload\x18\x01 \x03(\x03\x32~\n\x04Uuid\x12\x1e\n\x04Ping\x12\t.rpc.Null\x1a\t.rpc.Pong\"\x00\x12!\n\x03New\x12\t.rpc.Null\x1a\r.rpc.NewReply\"\x00\x12\x33\n\x07MutiNew\x12\x13.rpc.MutiNewRequest\x1a\x11.rpc.MutiNewReply\"\x00\x62\x06proto3')
)




_NULL = _descriptor.Descriptor(
  name='Null',
  full_name='rpc.Null',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=19,
  serialized_end=25,
)


_PONG = _descriptor.Descriptor(
  name='Pong',
  full_name='rpc.Pong',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='msg', full_name='rpc.Pong.msg', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=27,
  serialized_end=46,
)


_MUTINEWREQUEST = _descriptor.Descriptor(
  name='MutiNewRequest',
  full_name='rpc.MutiNewRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='len', full_name='rpc.MutiNewRequest.len', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=48,
  serialized_end=77,
)


_NEWREPLY = _descriptor.Descriptor(
  name='NewReply',
  full_name='rpc.NewReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='rpc.NewReply.payload', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=79,
  serialized_end=106,
)


_MUTINEWREPLY = _descriptor.Descriptor(
  name='MutiNewReply',
  full_name='rpc.MutiNewReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='rpc.MutiNewReply.payload', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=108,
  serialized_end=139,
)

DESCRIPTOR.message_types_by_name['Null'] = _NULL
DESCRIPTOR.message_types_by_name['Pong'] = _PONG
DESCRIPTOR.message_types_by_name['MutiNewRequest'] = _MUTINEWREQUEST
DESCRIPTOR.message_types_by_name['NewReply'] = _NEWREPLY
DESCRIPTOR.message_types_by_name['MutiNewReply'] = _MUTINEWREPLY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Null = _reflection.GeneratedProtocolMessageType('Null', (_message.Message,), dict(
  DESCRIPTOR = _NULL,
  __module__ = 'uuid_pb2'
  # @@protoc_insertion_point(class_scope:rpc.Null)
  ))
_sym_db.RegisterMessage(Null)

Pong = _reflection.GeneratedProtocolMessageType('Pong', (_message.Message,), dict(
  DESCRIPTOR = _PONG,
  __module__ = 'uuid_pb2'
  # @@protoc_insertion_point(class_scope:rpc.Pong)
  ))
_sym_db.RegisterMessage(Pong)

MutiNewRequest = _reflection.GeneratedProtocolMessageType('MutiNewRequest', (_message.Message,), dict(
  DESCRIPTOR = _MUTINEWREQUEST,
  __module__ = 'uuid_pb2'
  # @@protoc_insertion_point(class_scope:rpc.MutiNewRequest)
  ))
_sym_db.RegisterMessage(MutiNewRequest)

NewReply = _reflection.GeneratedProtocolMessageType('NewReply', (_message.Message,), dict(
  DESCRIPTOR = _NEWREPLY,
  __module__ = 'uuid_pb2'
  # @@protoc_insertion_point(class_scope:rpc.NewReply)
  ))
_sym_db.RegisterMessage(NewReply)

MutiNewReply = _reflection.GeneratedProtocolMessageType('MutiNewReply', (_message.Message,), dict(
  DESCRIPTOR = _MUTINEWREPLY,
  __module__ = 'uuid_pb2'
  # @@protoc_insertion_point(class_scope:rpc.MutiNewReply)
  ))
_sym_db.RegisterMessage(MutiNewReply)



_UUID = _descriptor.ServiceDescriptor(
  name='Uuid',
  full_name='rpc.Uuid',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=141,
  serialized_end=267,
  methods=[
  _descriptor.MethodDescriptor(
    name='Ping',
    full_name='rpc.Uuid.Ping',
    index=0,
    containing_service=None,
    input_type=_NULL,
    output_type=_PONG,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='New',
    full_name='rpc.Uuid.New',
    index=1,
    containing_service=None,
    input_type=_NULL,
    output_type=_NEWREPLY,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='MutiNew',
    full_name='rpc.Uuid.MutiNew',
    index=2,
    containing_service=None,
    input_type=_MUTINEWREQUEST,
    output_type=_MUTINEWREPLY,
    serialized_options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_UUID)

DESCRIPTOR.services_by_name['Uuid'] = _UUID

# @@protoc_insertion_point(module_scope)
