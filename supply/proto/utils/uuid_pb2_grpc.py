# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

# import uuid_pb2 as uuid__pb2
from supply.proto.utils import uuid_pb2 as uuid__pb2

class UuidStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.Ping = channel.unary_unary(
        '/rpc.Uuid/Ping',
        request_serializer=uuid__pb2.Null.SerializeToString,
        response_deserializer=uuid__pb2.Pong.FromString,
        )
    self.New = channel.unary_unary(
        '/rpc.Uuid/New',
        request_serializer=uuid__pb2.Null.SerializeToString,
        response_deserializer=uuid__pb2.NewReply.FromString,
        )
    self.MutiNew = channel.unary_unary(
        '/rpc.Uuid/MutiNew',
        request_serializer=uuid__pb2.MutiNewRequest.SerializeToString,
        response_deserializer=uuid__pb2.MutiNewReply.FromString,
        )


class UuidServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def Ping(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def New(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def MutiNew(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_UuidServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'Ping': grpc.unary_unary_rpc_method_handler(
          servicer.Ping,
          request_deserializer=uuid__pb2.Null.FromString,
          response_serializer=uuid__pb2.Pong.SerializeToString,
      ),
      'New': grpc.unary_unary_rpc_method_handler(
          servicer.New,
          request_deserializer=uuid__pb2.Null.FromString,
          response_serializer=uuid__pb2.NewReply.SerializeToString,
      ),
      'MutiNew': grpc.unary_unary_rpc_method_handler(
          servicer.MutiNew,
          request_deserializer=uuid__pb2.MutiNewRequest.FromString,
          response_serializer=uuid__pb2.MutiNewReply.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'rpc.Uuid', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
