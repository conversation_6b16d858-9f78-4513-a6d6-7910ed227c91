{"swagger": "2.0", "info": {"title": "ianvs/user.proto", "version": "version not set"}, "tags": [{"name": "User"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "userGetAllPartnerResp": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/userPartnerInfo"}}, "total": {"type": "string", "format": "uint64"}}}, "userListUserInfoResp": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/userUserInfo"}}, "total": {"type": "string", "format": "int64"}}}, "userListUserResp": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/userUserAccount"}}, "total": {"type": "string", "format": "int64"}}}, "userPartnerInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "short_code": {"type": "string"}, "status": {"type": "string"}, "name": {"type": "string"}, "short_name": {"type": "string"}, "domain": {"type": "string"}, "hosts": {"type": "array", "items": {"type": "string"}}, "created_at": {"type": "string", "format": "uint64"}, "updated_at": {"type": "string", "format": "uint64"}, "industry": {"type": "string"}, "scale": {"type": "string"}, "store_num": {"type": "string", "format": "uint64"}, "location": {"type": "string"}, "address": {"type": "string"}, "customize_url": {"type": "string"}, "ding_crop_id": {"type": "string"}}}, "userUserAccount": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "partner_id": {"type": "string", "format": "uint64"}, "status": {"type": "string"}, "nick": {"type": "string"}, "lan": {"type": "string"}, "account": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}, "phone_status": {"type": "string"}, "admin": {"type": "boolean"}, "created_at": {"type": "string", "format": "uint64"}}}, "userUserInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "nick": {"type": "string"}}}}}