# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: ianvs/user.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='ianvs/user.proto',
  package='user',
  syntax='proto3',
  serialized_options=_b('Z\022ui/grpc/user/proto'),
  serialized_pb=_b('\n\x10ianvs/user.proto\x12\x04user\"\x07\n\x05\x65mpty\"C\n\x11GetAllPartnerResp\x12\x1f\n\x04rows\x18\x02 \x03(\x0b\x32\x11.user.PartnerInfo\x12\r\n\x05total\x18\x01 \x01(\x04\"\xaa\x02\n\x0bPartnerInfo\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nshort_code\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x12\n\nshort_name\x18\x05 \x01(\t\x12\x0e\n\x06\x64omain\x18\x06 \x01(\t\x12\r\n\x05hosts\x18\x07 \x03(\t\x12\x12\n\ncreated_at\x18\x08 \x01(\x04\x12\x12\n\nupdated_at\x18\t \x01(\x04\x12\x10\n\x08industry\x18\n \x01(\t\x12\r\n\x05scale\x18\x0b \x01(\t\x12\x11\n\tstore_num\x18\x0c \x01(\x04\x12\x10\n\x08location\x18\r \x01(\t\x12\x0f\n\x07\x61\x64\x64ress\x18\x0e \x01(\t\x12\x15\n\rcustomize_url\x18\x0f \x01(\t\x12\x14\n\x0c\x64ing_crop_id\x18\x10 \x01(\t\"X\n\x0bListUserReq\x12\r\n\x05limit\x18\x01 \x01(\x03\x12\x0e\n\x06offset\x18\x02 \x01(\x04\x12\r\n\x05\x61\x64min\x18\x03 \x01(\x08\x12\x0b\n\x03key\x18\x04 \x01(\t\x12\x0e\n\x06status\x18\x05 \x03(\t\">\n\x0cListUserResp\x12\x1f\n\x04rows\x18\x01 \x03(\x0b\x32\x11.user.UserAccount\x12\r\n\x05total\x18\x02 \x01(\x03\"\xc0\x01\n\x0bUserAccount\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x0c\n\x04nick\x18\x04 \x01(\t\x12\x0b\n\x03lan\x18\x05 \x01(\t\x12\x0f\n\x07\x61\x63\x63ount\x18\x06 \x01(\t\x12\r\n\x05\x65mail\x18\x07 \x01(\t\x12\r\n\x05phone\x18\x08 \x01(\t\x12\x14\n\x0cphone_status\x18\t \x01(\t\x12\r\n\x05\x61\x64min\x18\n \x01(\x08\x12\x12\n\ncreated_at\x18\x0b \x01(\x04\"\x1e\n\x0fListUserInfoReq\x12\x0b\n\x03ids\x18\x01 \x03(\x04\"$\n\x08UserInfo\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04nick\x18\x02 \x01(\t\"?\n\x10ListUserInfoResp\x12\x1c\n\x04rows\x18\x01 \x03(\x0b\x32\x0e.user.UserInfo\x12\r\n\x05total\x18\x02 \x01(\x03\x32\xb0\x01\n\x04User\x12\x31\n\x08ListUser\x12\x11.user.ListUserReq\x1a\x12.user.ListUserResp\x12=\n\x0cListUserInfo\x12\x15.user.ListUserInfoReq\x1a\x16.user.ListUserInfoResp\x12\x36\n\x0eListAllPartner\x12\x0b.user.empty\x1a\x17.user.GetAllPartnerRespB\x14Z\x12ui/grpc/user/protob\x06proto3')
)




_EMPTY = _descriptor.Descriptor(
  name='empty',
  full_name='user.empty',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=26,
  serialized_end=33,
)


_GETALLPARTNERRESP = _descriptor.Descriptor(
  name='GetAllPartnerResp',
  full_name='user.GetAllPartnerResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='user.GetAllPartnerResp.rows', index=0,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='user.GetAllPartnerResp.total', index=1,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=35,
  serialized_end=102,
)


_PARTNERINFO = _descriptor.Descriptor(
  name='PartnerInfo',
  full_name='user.PartnerInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='user.PartnerInfo.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='short_code', full_name='user.PartnerInfo.short_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='user.PartnerInfo.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='user.PartnerInfo.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='short_name', full_name='user.PartnerInfo.short_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='user.PartnerInfo.domain', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hosts', full_name='user.PartnerInfo.hosts', index=6,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='user.PartnerInfo.created_at', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='user.PartnerInfo.updated_at', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='industry', full_name='user.PartnerInfo.industry', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scale', full_name='user.PartnerInfo.scale', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_num', full_name='user.PartnerInfo.store_num', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='location', full_name='user.PartnerInfo.location', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='address', full_name='user.PartnerInfo.address', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='customize_url', full_name='user.PartnerInfo.customize_url', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ding_crop_id', full_name='user.PartnerInfo.ding_crop_id', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=105,
  serialized_end=403,
)


_LISTUSERREQ = _descriptor.Descriptor(
  name='ListUserReq',
  full_name='user.ListUserReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='limit', full_name='user.ListUserReq.limit', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='user.ListUserReq.offset', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='admin', full_name='user.ListUserReq.admin', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='key', full_name='user.ListUserReq.key', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='user.ListUserReq.status', index=4,
      number=5, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=405,
  serialized_end=493,
)


_LISTUSERRESP = _descriptor.Descriptor(
  name='ListUserResp',
  full_name='user.ListUserResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='user.ListUserResp.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='user.ListUserResp.total', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=495,
  serialized_end=557,
)


_USERACCOUNT = _descriptor.Descriptor(
  name='UserAccount',
  full_name='user.UserAccount',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='user.UserAccount.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='user.UserAccount.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='user.UserAccount.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='nick', full_name='user.UserAccount.nick', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='user.UserAccount.lan', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account', full_name='user.UserAccount.account', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='email', full_name='user.UserAccount.email', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='phone', full_name='user.UserAccount.phone', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='phone_status', full_name='user.UserAccount.phone_status', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='admin', full_name='user.UserAccount.admin', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='user.UserAccount.created_at', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=560,
  serialized_end=752,
)


_LISTUSERINFOREQ = _descriptor.Descriptor(
  name='ListUserInfoReq',
  full_name='user.ListUserInfoReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ids', full_name='user.ListUserInfoReq.ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=754,
  serialized_end=784,
)


_USERINFO = _descriptor.Descriptor(
  name='UserInfo',
  full_name='user.UserInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='user.UserInfo.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='nick', full_name='user.UserInfo.nick', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=786,
  serialized_end=822,
)


_LISTUSERINFORESP = _descriptor.Descriptor(
  name='ListUserInfoResp',
  full_name='user.ListUserInfoResp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='user.ListUserInfoResp.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='user.ListUserInfoResp.total', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=824,
  serialized_end=887,
)

_GETALLPARTNERRESP.fields_by_name['rows'].message_type = _PARTNERINFO
_LISTUSERRESP.fields_by_name['rows'].message_type = _USERACCOUNT
_LISTUSERINFORESP.fields_by_name['rows'].message_type = _USERINFO
DESCRIPTOR.message_types_by_name['empty'] = _EMPTY
DESCRIPTOR.message_types_by_name['GetAllPartnerResp'] = _GETALLPARTNERRESP
DESCRIPTOR.message_types_by_name['PartnerInfo'] = _PARTNERINFO
DESCRIPTOR.message_types_by_name['ListUserReq'] = _LISTUSERREQ
DESCRIPTOR.message_types_by_name['ListUserResp'] = _LISTUSERRESP
DESCRIPTOR.message_types_by_name['UserAccount'] = _USERACCOUNT
DESCRIPTOR.message_types_by_name['ListUserInfoReq'] = _LISTUSERINFOREQ
DESCRIPTOR.message_types_by_name['UserInfo'] = _USERINFO
DESCRIPTOR.message_types_by_name['ListUserInfoResp'] = _LISTUSERINFORESP
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

empty = _reflection.GeneratedProtocolMessageType('empty', (_message.Message,), dict(
  DESCRIPTOR = _EMPTY,
  __module__ = 'ianvs.user_pb2'
  # @@protoc_insertion_point(class_scope:user.empty)
  ))
_sym_db.RegisterMessage(empty)

GetAllPartnerResp = _reflection.GeneratedProtocolMessageType('GetAllPartnerResp', (_message.Message,), dict(
  DESCRIPTOR = _GETALLPARTNERRESP,
  __module__ = 'ianvs.user_pb2'
  # @@protoc_insertion_point(class_scope:user.GetAllPartnerResp)
  ))
_sym_db.RegisterMessage(GetAllPartnerResp)

PartnerInfo = _reflection.GeneratedProtocolMessageType('PartnerInfo', (_message.Message,), dict(
  DESCRIPTOR = _PARTNERINFO,
  __module__ = 'ianvs.user_pb2'
  # @@protoc_insertion_point(class_scope:user.PartnerInfo)
  ))
_sym_db.RegisterMessage(PartnerInfo)

ListUserReq = _reflection.GeneratedProtocolMessageType('ListUserReq', (_message.Message,), dict(
  DESCRIPTOR = _LISTUSERREQ,
  __module__ = 'ianvs.user_pb2'
  # @@protoc_insertion_point(class_scope:user.ListUserReq)
  ))
_sym_db.RegisterMessage(ListUserReq)

ListUserResp = _reflection.GeneratedProtocolMessageType('ListUserResp', (_message.Message,), dict(
  DESCRIPTOR = _LISTUSERRESP,
  __module__ = 'ianvs.user_pb2'
  # @@protoc_insertion_point(class_scope:user.ListUserResp)
  ))
_sym_db.RegisterMessage(ListUserResp)

UserAccount = _reflection.GeneratedProtocolMessageType('UserAccount', (_message.Message,), dict(
  DESCRIPTOR = _USERACCOUNT,
  __module__ = 'ianvs.user_pb2'
  # @@protoc_insertion_point(class_scope:user.UserAccount)
  ))
_sym_db.RegisterMessage(UserAccount)

ListUserInfoReq = _reflection.GeneratedProtocolMessageType('ListUserInfoReq', (_message.Message,), dict(
  DESCRIPTOR = _LISTUSERINFOREQ,
  __module__ = 'ianvs.user_pb2'
  # @@protoc_insertion_point(class_scope:user.ListUserInfoReq)
  ))
_sym_db.RegisterMessage(ListUserInfoReq)

UserInfo = _reflection.GeneratedProtocolMessageType('UserInfo', (_message.Message,), dict(
  DESCRIPTOR = _USERINFO,
  __module__ = 'ianvs.user_pb2'
  # @@protoc_insertion_point(class_scope:user.UserInfo)
  ))
_sym_db.RegisterMessage(UserInfo)

ListUserInfoResp = _reflection.GeneratedProtocolMessageType('ListUserInfoResp', (_message.Message,), dict(
  DESCRIPTOR = _LISTUSERINFORESP,
  __module__ = 'ianvs.user_pb2'
  # @@protoc_insertion_point(class_scope:user.ListUserInfoResp)
  ))
_sym_db.RegisterMessage(ListUserInfoResp)


DESCRIPTOR._options = None

_USER = _descriptor.ServiceDescriptor(
  name='User',
  full_name='user.User',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=890,
  serialized_end=1066,
  methods=[
  _descriptor.MethodDescriptor(
    name='ListUser',
    full_name='user.User.ListUser',
    index=0,
    containing_service=None,
    input_type=_LISTUSERREQ,
    output_type=_LISTUSERRESP,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='ListUserInfo',
    full_name='user.User.ListUserInfo',
    index=1,
    containing_service=None,
    input_type=_LISTUSERINFOREQ,
    output_type=_LISTUSERINFORESP,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='ListAllPartner',
    full_name='user.User.ListAllPartner',
    index=2,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_GETALLPARTNERRESP,
    serialized_options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_USER)

DESCRIPTOR.services_by_name['User'] = _USER

# @@protoc_insertion_point(module_scope)
