# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from bom import explain_pb2 as bom_dot_explain__pb2


class BomServiceStub(object):
  """BomService 物料服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.Explain = channel.unary_unary(
        '/explain.BomService/Explain',
        request_serializer=bom_dot_explain__pb2.ProductBomReqeust.SerializeToString,
        response_deserializer=bom_dot_explain__pb2.ProductBomResponse.FromString,
        )
    self.ExplainBatch = channel.unary_unary(
        '/explain.BomService/ExplainBatch',
        request_serializer=bom_dot_explain__pb2.BatchRequest.SerializeToString,
        response_deserializer=bom_dot_explain__pb2.BatchResponse.FromString,
        )
    self.SpeedUp = channel.unary_unary(
        '/explain.BomService/SpeedUp',
        request_serializer=bom_dot_explain__pb2.SpeedUpRequest.SerializeToString,
        response_deserializer=bom_dot_explain__pb2.SpeedUpResponse.FromString,
        )
    self.ExperimentalExplain = channel.unary_unary(
        '/explain.BomService/ExperimentalExplain',
        request_serializer=bom_dot_explain__pb2.ProductBomReqeust.SerializeToString,
        response_deserializer=bom_dot_explain__pb2.ProductBomResponse.FromString,
        )
    self.ExperimentalExplainBatch = channel.unary_unary(
        '/explain.BomService/ExperimentalExplainBatch',
        request_serializer=bom_dot_explain__pb2.BatchRequest.SerializeToString,
        response_deserializer=bom_dot_explain__pb2.BatchResponse.FromString,
        )


class BomServiceServicer(object):
  """BomService 物料服务
  """

  def Explain(self, request, context):
    """Explain 物料分解方法
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ExplainBatch(self, request, context):
    """ExplainBatch 批量分解
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SpeedUp(self, request, context):
    """SpeedUp 加速分解
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ExperimentalExplain(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ExperimentalExplainBatch(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_BomServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'Explain': grpc.unary_unary_rpc_method_handler(
          servicer.Explain,
          request_deserializer=bom_dot_explain__pb2.ProductBomReqeust.FromString,
          response_serializer=bom_dot_explain__pb2.ProductBomResponse.SerializeToString,
      ),
      'ExplainBatch': grpc.unary_unary_rpc_method_handler(
          servicer.ExplainBatch,
          request_deserializer=bom_dot_explain__pb2.BatchRequest.FromString,
          response_serializer=bom_dot_explain__pb2.BatchResponse.SerializeToString,
      ),
      'SpeedUp': grpc.unary_unary_rpc_method_handler(
          servicer.SpeedUp,
          request_deserializer=bom_dot_explain__pb2.SpeedUpRequest.FromString,
          response_serializer=bom_dot_explain__pb2.SpeedUpResponse.SerializeToString,
      ),
      'ExperimentalExplain': grpc.unary_unary_rpc_method_handler(
          servicer.ExperimentalExplain,
          request_deserializer=bom_dot_explain__pb2.ProductBomReqeust.FromString,
          response_serializer=bom_dot_explain__pb2.ProductBomResponse.SerializeToString,
      ),
      'ExperimentalExplainBatch': grpc.unary_unary_rpc_method_handler(
          servicer.ExperimentalExplainBatch,
          request_deserializer=bom_dot_explain__pb2.BatchRequest.FromString,
          response_serializer=bom_dot_explain__pb2.BatchResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'explain.BomService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
