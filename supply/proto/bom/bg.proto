syntax = "proto3";
package rule;

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// 配方报表数据查询服务
service BigDataService {
    // 查询配方分解数据（到区域、店到商品）
    rpc QueryTransactionBomIn(QueryRequest) returns (QueryTransactionBomInResponse) {
        option (google.api.http) = {
            post: "/api/v1/bom-bg/bomin/query"
            body: "*"
        };
    }

    rpc AggregateTransactionBomIn (AggregateRequest) returns (QueryTransactionBomInResponse){
        option (google.api.http) = {
            post: "/api/v1/bom-bg/bomin/aggregate"
            body: "*"
        };
    }

    rpc AggregateTransactionBomInStream (AggregateRequest) returns (stream TransactionBomIn) {
        option (google.api.http) = {
            post: "/api/v1/bom-bg/bomin/aggregate/stream"
            body: "*"
        };
    }
}

// 查询请求
message QueryRequest {
    // 条数
    uint64 limit = 1;
    // 偏移量
    uint64 offset = 2;
    // 区域id
    repeated int64 branch_ids = 3;
    // 商品id列表
    repeated int64 product_ids = 4;
    // 门店id列表
    repeated int64 store_ids = 5;
     // 营业开始日期
     string start_date = 6;
     // 营业结束日期
     string end_date = 7;
}

// 获取配方分解数据列表响应
message QueryTransactionBomInResponse {
    // 配方分解数据
    repeated TransactionBomIn rows = 1;
    // 总条数
    int32 total = 2;
}

message AggregateRequest{
     // 条数
     uint64 limit = 1;
     // 偏移量
     uint64 offset = 2;
     // 区域id
     repeated int64 branch_ids = 3;
     // 商品id列表
     repeated int64 product_ids = 4;
     // 门店id列表
     repeated int64 store_ids = 5;
     // 营业开始日期
     string start_date = 6;
     // 营业结束日期
     string end_date = 7;
     // 区域等级
     int32 region_level = 8;
     // 区域类型
     string region_type = 9;
     // 商品等级
     int32 product_level = 10;
     //日期等级
     int32 date_level = 11;

}

message TransactionBomIn {
    // 区域（门店）id
    int64 branch_id = 1;
    // 区域（门店）名称
    string branch_name = 2;
    // 区域（门店）编码
    string branch_code = 3;
    // 商品id
    int64 product_id = 4;
    // 商品名称
    string product_name = 5;
    // 商品编码
    string product_code = 6;
    // 商品物料清单id
    int64 bom_in_id = 7;
    // 物料清单名称
    string bom_in_name = 8;
    // 物料清单编码
    string bom_in_code = 9;
    // 物料清单单位编码
    string bom_in_uom_code = 10;
    // 物料清单单位名称
    string bom_in_uom_name = 11;
    // 数量
    double qty = 12;
    // 成本总额
    double cost_amount = 13;
    // 销售单价
    double sales_price = 14;
    // 成本单价
    double cost_price = 15;
    // 营业日
    google.protobuf.Timestamp sales_date = 16;
    // 日期
    google.protobuf.Timestamp biz_date = 17;
    // 商户id
    int64 partner_id = 18;
}
