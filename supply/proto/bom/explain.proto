syntax = "proto3";
package explain;

import "google/api/annotations.proto";

// BomService 物料服务
service BomService {
    // Explain 物料分解方法
    rpc Explain(ProductBomReqeust) returns (ProductBomResponse){
        option (google.api.http) = {
            post: "/api/v2/bom/explain"
            body: "*"
        };
    }

    // ExplainBatch 批量分解
    rpc ExplainBatch(BatchRequest) returns (BatchResponse) {
        option (google.api.http) = {
            post: "/api/v2/bom/explain/batch"
            body: "*"
        };
    }


    // SpeedUp 加速分解
    rpc SpeedUp (SpeedUpRequest) returns (SpeedUpResponse){
        // option (google.api.http).get = "/api/v2/bom/explain/speedup";
        option (google.api.http) = {
            post: "/api/v2/bom/explain/speedup"
            body: "*"
        };
    }

    rpc ExperimentalExplain(ProductBomReqeust) returns (ProductBomResponse){
        option (google.api.http) = {
            post: "/api/v3/bom/explain"
            body: "*"
        };
    }

    rpc ExperimentalExplainBatch(BatchRequest) returns (BatchResponse) {
        option (google.api.http) = {
            post: "/api/v3/bom/explain/batch"
            body: "*"
        };
    }
}

message SpeedUpRequest {
    repeated string schema_list = 1;
}

message SpeedUpResponse {
    int32 res = 1;
}

message ProductBomReqeust {
    //请求id 唯一
    uint64 request_id = 1;
    //商品id
    uint64 product_id = 2;
    //商品编码
    string product_code = 3;
    // 门店id
    uint64 store_id = 4;
    // 门店编码
    string store_code = 5;
    // 营业日
    string sales_date = 6;
    // 业务编码(自定义)
    string biz_code = 7;
    // 业务单号(自定义)
    string biz_no  = 8;
    // 商品可选项明细
    repeated TProductOption options = 9;
    // 商品数量
    double product_qty = 10;
}

message ProductBomResponse {
    //请求id
    uint64 request_id = 1;
    //商品id
    uint64 product_id = 2;
    //门店id
    uint64 store_id = 3;
    // 营业日
    string sales_date = 5;
    //商品物料清单
    repeated TProdoctBom bom = 6;
    // 业务编码(自定义)
    string biz_code = 7;
    // 业务单号(自定义)
    string biz_no  = 8;
    // 商品数量
    double product_qty = 9;
}

message BatchRequest {
    //请求id 唯一
    uint64 request_id = 1;
    //门店id
    uint64 store_id = 2;
    // 营业日
    string sales_date = 3;
    // 业务编码(自定义)
    string biz_code = 4;
    // 业务单号(自定义)
    string biz_no  = 5;
    // 商品明细
    repeated TProduct products = 6;
}

message BatchResponse {
    repeated ProductBomResponse response = 1;
}


// 商品可选项配置
message TProduct {
    //商品id
    uint64 product_id = 1;
    //商品可选项明细
    repeated TProductOption options = 2;
    //商品数量
    double product_qty = 3;
}

/* Entity */
message TProductOption {
    //商品可选项id
    int64 id = 1;      //商品可选项id
    //商品可选项编码
    string code = 2;    //商品可选项编码
    // int64 tag_id = 3;  //商品可选项标签id
    // string tag_code = 4; //商品可选项标签编码
    // 商品可选项列表
    repeated TProductOptionTag tags = 3;
}

message TProductOptionTag{
    //商品可选项标签id
    int64 id = 1;
    //商品可选项标签编码
    string code = 2;
}

message TProdoctBom {
    // 商品id
    uint64 product_id =1;
    // 单位（配方）
    uint64 uom_id =2;
    // 数量
    double qty = 3;
    // 是否已经是最终商品
    bool  is_product = 4;
    //是否被代入过
    bool  has_plugged = 5;
}

