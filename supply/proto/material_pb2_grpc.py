# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import material_pb2 as material__pb2


class materialStub(object):
  """物料服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetMaterialDifference = channel.unary_unary(
        '/material.material/GetMaterialDifference',
        request_serializer=material__pb2.GetMaterialDifferenceRequest.SerializeToString,
        response_deserializer=material__pb2.GetMaterialDifferenceResponse.FromString,
        )


class materialServicer(object):
  """物料服务
  """

  def GetMaterialDifference(self, request, context):
    """物料分差报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_materialServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetMaterialDifference': grpc.unary_unary_rpc_method_handler(
          servicer.GetMaterialDifference,
          request_deserializer=material__pb2.GetMaterialDifferenceRequest.FromString,
          response_serializer=material__pb2.GetMaterialDifferenceResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'material.material', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
