syntax = "proto3";

package supply_message;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";


service SupplyMessage {
    rpc HandleMessage (MessageRequest) returns (MessageResponse) {
        option (google.api.http) = {};
    }

}
message MessageRequest {
    string topic = 1;
    string tags = 2;
    string body = 3;
}

message MessageResponse {
    bool result = 1;
}
