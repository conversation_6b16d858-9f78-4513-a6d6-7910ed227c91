# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: supply_message/supply_message.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='supply_message/supply_message.proto',
  package='supply_message',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n#supply_message/supply_message.proto\x12\x0esupply_message\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\";\n\x0eMessageRequest\x12\r\n\x05topic\x18\x01 \x01(\t\x12\x0c\n\x04tags\x18\x02 \x01(\t\x12\x0c\n\x04\x62ody\x18\x03 \x01(\t\"!\n\x0fMessageResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x32i\n\rSupplyMessage\x12X\n\rHandleMessage\x12\x1e.supply_message.MessageRequest\x1a\x1f.supply_message.MessageResponse\"\x06\x82\xd3\xe4\x93\x02\x00\x62\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_empty__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_MESSAGEREQUEST = _descriptor.Descriptor(
  name='MessageRequest',
  full_name='supply_message.MessageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='topic', full_name='supply_message.MessageRequest.topic', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tags', full_name='supply_message.MessageRequest.tags', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='body', full_name='supply_message.MessageRequest.body', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=147,
  serialized_end=206,
)


_MESSAGERESPONSE = _descriptor.Descriptor(
  name='MessageResponse',
  full_name='supply_message.MessageResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='supply_message.MessageResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=208,
  serialized_end=241,
)

DESCRIPTOR.message_types_by_name['MessageRequest'] = _MESSAGEREQUEST
DESCRIPTOR.message_types_by_name['MessageResponse'] = _MESSAGERESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

MessageRequest = _reflection.GeneratedProtocolMessageType('MessageRequest', (_message.Message,), dict(
  DESCRIPTOR = _MESSAGEREQUEST,
  __module__ = 'supply_message.supply_message_pb2'
  # @@protoc_insertion_point(class_scope:supply_message.MessageRequest)
  ))
_sym_db.RegisterMessage(MessageRequest)

MessageResponse = _reflection.GeneratedProtocolMessageType('MessageResponse', (_message.Message,), dict(
  DESCRIPTOR = _MESSAGERESPONSE,
  __module__ = 'supply_message.supply_message_pb2'
  # @@protoc_insertion_point(class_scope:supply_message.MessageResponse)
  ))
_sym_db.RegisterMessage(MessageResponse)



_SUPPLYMESSAGE = _descriptor.ServiceDescriptor(
  name='SupplyMessage',
  full_name='supply_message.SupplyMessage',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=243,
  serialized_end=348,
  methods=[
  _descriptor.MethodDescriptor(
    name='HandleMessage',
    full_name='supply_message.SupplyMessage.HandleMessage',
    index=0,
    containing_service=None,
    input_type=_MESSAGEREQUEST,
    output_type=_MESSAGERESPONSE,
    serialized_options=_b('\202\323\344\223\002\000'),
  ),
])
_sym_db.RegisterServiceDescriptor(_SUPPLYMESSAGE)

DESCRIPTOR.services_by_name['SupplyMessage'] = _SUPPLYMESSAGE

# @@protoc_insertion_point(module_scope)
