# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from supply_message import supply_message_pb2 as supply__message_dot_supply__message__pb2


class SupplyMessageStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.HandleMessage = channel.unary_unary(
        '/supply_message.SupplyMessage/HandleMessage',
        request_serializer=supply__message_dot_supply__message__pb2.MessageRequest.SerializeToString,
        response_deserializer=supply__message_dot_supply__message__pb2.MessageResponse.FromString,
        )


class SupplyMessageServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def HandleMessage(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_SupplyMessageServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'HandleMessage': grpc.unary_unary_rpc_method_handler(
          servicer.HandleMessage,
          request_deserializer=supply__message_dot_supply__message__pb2.MessageRequest.FromString,
          response_serializer=supply__message_dot_supply__message__pb2.MessageResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'supply_message.SupplyMessage', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
