# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import inventory_bi_pb2 as inventory__bi__pb2


class InventoryBiServiceStub(object):
  """库存报表相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.RealtimeInventory = channel.unary_unary(
        '/inventory_bi.InventoryBiService/RealtimeInventory',
        request_serializer=inventory__bi__pb2.RealtimeInventoryRequest.SerializeToString,
        response_deserializer=inventory__bi__pb2.RealtimeInventoryResponse.FromString,
        )
    self.DailyInventory = channel.unary_unary(
        '/inventory_bi.InventoryBiService/DailyInventory',
        request_serializer=inventory__bi__pb2.DailyInventoryRequest.SerializeToString,
        response_deserializer=inventory__bi__pb2.DailyInventoryResponse.FromString,
        )
    self.QueryAccouting = channel.unary_unary(
        '/inventory_bi.InventoryBiService/QueryAccouting',
        request_serializer=inventory__bi__pb2.QueryAccoutingRequest.SerializeToString,
        response_deserializer=inventory__bi__pb2.QueryAccoutingResponse.FromString,
        )
    self.QueryInventoryLog = channel.unary_unary(
        '/inventory_bi.InventoryBiService/QueryInventoryLog',
        request_serializer=inventory__bi__pb2.QueryInventoryLogRequest.SerializeToString,
        response_deserializer=inventory__bi__pb2.QueryInventoryLogResponse.FromString,
        )
    self.QueryMcuRealtimeInventory = channel.unary_unary(
        '/inventory_bi.InventoryBiService/QueryMcuRealtimeInventory',
        request_serializer=inventory__bi__pb2.QueryMcuRealtimeInventoryRequest.SerializeToString,
        response_deserializer=inventory__bi__pb2.QueryMcuRealtimeInventoryResponse.FromString,
        )
    self.BatchQuery = channel.unary_unary(
        '/inventory_bi.InventoryBiService/BatchQuery',
        request_serializer=inventory__bi__pb2.BatchQueryRequest.SerializeToString,
        response_deserializer=inventory__bi__pb2.BatchQueryResponse.FromString,
        )
    self.QueryInventoryLogInTotal = channel.unary_unary(
        '/inventory_bi.InventoryBiService/QueryInventoryLogInTotal',
        request_serializer=inventory__bi__pb2.QueryInventoryLogInTotalRequest.SerializeToString,
        response_deserializer=inventory__bi__pb2.QueryInventoryLogInTotalResponse.FromString,
        )
    self.QuerySnapshotForSales = channel.unary_unary(
        '/inventory_bi.InventoryBiService/QuerySnapshotForSales',
        request_serializer=inventory__bi__pb2.QuerySnapshotForSalesRequest.SerializeToString,
        response_deserializer=inventory__bi__pb2.QuerySnapshotForSalesResponse.FromString,
        )
    self.QueryBohJdeInventoryDiff = channel.unary_unary(
        '/inventory_bi.InventoryBiService/QueryBohJdeInventoryDiff',
        request_serializer=inventory__bi__pb2.QueryBohJdeInventoryDiffRequest.SerializeToString,
        response_deserializer=inventory__bi__pb2.QueryBohJdeInventoryDiffResponse.FromString,
        )
    self.RealtimeInventoryByAccounts = channel.unary_unary(
        '/inventory_bi.InventoryBiService/RealtimeInventoryByAccounts',
        request_serializer=inventory__bi__pb2.RealtimeInventoryByAccountsRequest.SerializeToString,
        response_deserializer=inventory__bi__pb2.RealtimeInventoryByAccountsResponse.FromString,
        )


class InventoryBiServiceServicer(object):
  """库存报表相关服务
  """

  def RealtimeInventory(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DailyInventory(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryAccouting(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryInventoryLog(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryMcuRealtimeInventory(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def BatchQuery(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryInventoryLogInTotal(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QuerySnapshotForSales(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryBohJdeInventoryDiff(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RealtimeInventoryByAccounts(self, request, context):
    """按账户(目前只兼容仓库/加工中心查询发货仓位库存)和指定商品查询实时库存，包装给前端用
    查询库存前校验当前账户是否有子账户，若有返回子账户的库存
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_InventoryBiServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'RealtimeInventory': grpc.unary_unary_rpc_method_handler(
          servicer.RealtimeInventory,
          request_deserializer=inventory__bi__pb2.RealtimeInventoryRequest.FromString,
          response_serializer=inventory__bi__pb2.RealtimeInventoryResponse.SerializeToString,
      ),
      'DailyInventory': grpc.unary_unary_rpc_method_handler(
          servicer.DailyInventory,
          request_deserializer=inventory__bi__pb2.DailyInventoryRequest.FromString,
          response_serializer=inventory__bi__pb2.DailyInventoryResponse.SerializeToString,
      ),
      'QueryAccouting': grpc.unary_unary_rpc_method_handler(
          servicer.QueryAccouting,
          request_deserializer=inventory__bi__pb2.QueryAccoutingRequest.FromString,
          response_serializer=inventory__bi__pb2.QueryAccoutingResponse.SerializeToString,
      ),
      'QueryInventoryLog': grpc.unary_unary_rpc_method_handler(
          servicer.QueryInventoryLog,
          request_deserializer=inventory__bi__pb2.QueryInventoryLogRequest.FromString,
          response_serializer=inventory__bi__pb2.QueryInventoryLogResponse.SerializeToString,
      ),
      'QueryMcuRealtimeInventory': grpc.unary_unary_rpc_method_handler(
          servicer.QueryMcuRealtimeInventory,
          request_deserializer=inventory__bi__pb2.QueryMcuRealtimeInventoryRequest.FromString,
          response_serializer=inventory__bi__pb2.QueryMcuRealtimeInventoryResponse.SerializeToString,
      ),
      'BatchQuery': grpc.unary_unary_rpc_method_handler(
          servicer.BatchQuery,
          request_deserializer=inventory__bi__pb2.BatchQueryRequest.FromString,
          response_serializer=inventory__bi__pb2.BatchQueryResponse.SerializeToString,
      ),
      'QueryInventoryLogInTotal': grpc.unary_unary_rpc_method_handler(
          servicer.QueryInventoryLogInTotal,
          request_deserializer=inventory__bi__pb2.QueryInventoryLogInTotalRequest.FromString,
          response_serializer=inventory__bi__pb2.QueryInventoryLogInTotalResponse.SerializeToString,
      ),
      'QuerySnapshotForSales': grpc.unary_unary_rpc_method_handler(
          servicer.QuerySnapshotForSales,
          request_deserializer=inventory__bi__pb2.QuerySnapshotForSalesRequest.FromString,
          response_serializer=inventory__bi__pb2.QuerySnapshotForSalesResponse.SerializeToString,
      ),
      'QueryBohJdeInventoryDiff': grpc.unary_unary_rpc_method_handler(
          servicer.QueryBohJdeInventoryDiff,
          request_deserializer=inventory__bi__pb2.QueryBohJdeInventoryDiffRequest.FromString,
          response_serializer=inventory__bi__pb2.QueryBohJdeInventoryDiffResponse.SerializeToString,
      ),
      'RealtimeInventoryByAccounts': grpc.unary_unary_rpc_method_handler(
          servicer.RealtimeInventoryByAccounts,
          request_deserializer=inventory__bi__pb2.RealtimeInventoryByAccountsRequest.FromString,
          response_serializer=inventory__bi__pb2.RealtimeInventoryByAccountsResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'inventory_bi.InventoryBiService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
