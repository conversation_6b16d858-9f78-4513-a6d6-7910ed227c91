# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import reduction_augmentation_demand_pb2 as reduction__augmentation__demand__pb2


class ReductionAugmentationDemandStub(object):
  """物料服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.ListReductionAugmentationDemand = channel.unary_unary(
        '/reduction_augmentation_demand.ReductionAugmentationDemand/ListReductionAugmentationDemand',
        request_serializer=reduction__augmentation__demand__pb2.ListReductionAugmentationDemandRequest.SerializeToString,
        response_deserializer=reduction__augmentation__demand__pb2.ListReductionAugmentationDemandResponse.FromString,
        )
    self.RetrieveReductionAugmentationDemand = channel.unary_unary(
        '/reduction_augmentation_demand.ReductionAugmentationDemand/RetrieveReductionAugmentationDemand',
        request_serializer=reduction__augmentation__demand__pb2.RetrieveReductionAugmentationDemandRequest.SerializeToString,
        response_deserializer=reduction__augmentation__demand__pb2.RetrieveReductionAugmentationDemandResponse.FromString,
        )
    self.CreateReductionAugmentationDemand = channel.unary_unary(
        '/reduction_augmentation_demand.ReductionAugmentationDemand/CreateReductionAugmentationDemand',
        request_serializer=reduction__augmentation__demand__pb2.CreateReductionAugmentationDemandRequest.SerializeToString,
        response_deserializer=reduction__augmentation__demand__pb2.CreateReductionAugmentationDemandResponse.FromString,
        )
    self.UpdateReductionAugmentationDemand = channel.unary_unary(
        '/reduction_augmentation_demand.ReductionAugmentationDemand/UpdateReductionAugmentationDemand',
        request_serializer=reduction__augmentation__demand__pb2.UpdateReductionAugmentationDemandRequest.SerializeToString,
        response_deserializer=reduction__augmentation__demand__pb2.UpdateReductionAugmentationDemandResponse.FromString,
        )
    self.ListReductionAugmentationDemandProduct = channel.unary_unary(
        '/reduction_augmentation_demand.ReductionAugmentationDemand/ListReductionAugmentationDemandProduct',
        request_serializer=reduction__augmentation__demand__pb2.ListReductionAugmentationDemandProductRequest.SerializeToString,
        response_deserializer=reduction__augmentation__demand__pb2.ListReductionAugmentationDemandProductResponse.FromString,
        )
    self.ListReductionAugmentationDemandStore = channel.unary_unary(
        '/reduction_augmentation_demand.ReductionAugmentationDemand/ListReductionAugmentationDemandStore',
        request_serializer=reduction__augmentation__demand__pb2.ListReductionAugmentationDemandStoreRequest.SerializeToString,
        response_deserializer=reduction__augmentation__demand__pb2.ListReductionAugmentationDemandStoreResponse.FromString,
        )
    self.SumDemandQuantity = channel.unary_unary(
        '/reduction_augmentation_demand.ReductionAugmentationDemand/SumDemandQuantity',
        request_serializer=reduction__augmentation__demand__pb2.SumDemandQuantityRequest.SerializeToString,
        response_deserializer=reduction__augmentation__demand__pb2.SumDemandQuantityResponse.FromString,
        )
    self.CalculateAllocation = channel.unary_unary(
        '/reduction_augmentation_demand.ReductionAugmentationDemand/CalculateAllocation',
        request_serializer=reduction__augmentation__demand__pb2.CalculateAllocationRequest.SerializeToString,
        response_deserializer=reduction__augmentation__demand__pb2.CalculateAllocationResponse.FromString,
        )
    self.ListCalculateAllocationResult = channel.unary_unary(
        '/reduction_augmentation_demand.ReductionAugmentationDemand/ListCalculateAllocationResult',
        request_serializer=reduction__augmentation__demand__pb2.ListCalculateAllocationResultRequest.SerializeToString,
        response_deserializer=reduction__augmentation__demand__pb2.ListCalculateAllocationResultResponse.FromString,
        )
    self.DestroyCalculateAllocation = channel.unary_unary(
        '/reduction_augmentation_demand.ReductionAugmentationDemand/DestroyCalculateAllocation',
        request_serializer=reduction__augmentation__demand__pb2.DestroyCalculateAllocationRequest.SerializeToString,
        response_deserializer=reduction__augmentation__demand__pb2.DestroyCalculateAllocationResponse.FromString,
        )
    self.ListReductionAugmentationDemandExcludedStore = channel.unary_unary(
        '/reduction_augmentation_demand.ReductionAugmentationDemand/ListReductionAugmentationDemandExcludedStore',
        request_serializer=reduction__augmentation__demand__pb2.ListReductionAugmentationDemandExcludedStoreRequest.SerializeToString,
        response_deserializer=reduction__augmentation__demand__pb2.ListReductionAugmentationDemandExcludedStoreResponse.FromString,
        )
    self.CreateReductionAugmentationDemandExcludedStore = channel.unary_unary(
        '/reduction_augmentation_demand.ReductionAugmentationDemand/CreateReductionAugmentationDemandExcludedStore',
        request_serializer=reduction__augmentation__demand__pb2.CreateReductionAugmentationDemandExcludedStoreRequest.SerializeToString,
        response_deserializer=reduction__augmentation__demand__pb2.CreateReductionAugmentationDemandExcludedStoreResponse.FromString,
        )
    self.DestroyReductionAugmentationDemandExcludedStore = channel.unary_unary(
        '/reduction_augmentation_demand.ReductionAugmentationDemand/DestroyReductionAugmentationDemandExcludedStore',
        request_serializer=reduction__augmentation__demand__pb2.DestroyReductionAugmentationDemandExcludedStoreRequest.SerializeToString,
        response_deserializer=reduction__augmentation__demand__pb2.DestroyReductionAugmentationDemandExcludedStoreResponse.FromString,
        )
    self.ReductionAugmentationDemandProductAgg = channel.unary_unary(
        '/reduction_augmentation_demand.ReductionAugmentationDemand/ReductionAugmentationDemandProductAgg',
        request_serializer=reduction__augmentation__demand__pb2.ReductionAugmentationDemandProductAggRequest.SerializeToString,
        response_deserializer=reduction__augmentation__demand__pb2.ReductionAugmentationDemandProductAggResponse.FromString,
        )


class ReductionAugmentationDemandServicer(object):
  """物料服务
  """

  def ListReductionAugmentationDemand(self, request, context):
    """减配货列表页面
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RetrieveReductionAugmentationDemand(self, request, context):
    """减配货详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateReductionAugmentationDemand(self, request, context):
    """创建减配货
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateReductionAugmentationDemand(self, request, context):
    """更新减配货
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListReductionAugmentationDemandProduct(self, request, context):
    """按规则id查询商品列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListReductionAugmentationDemandStore(self, request, context):
    """按规则id查询门店列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SumDemandQuantity(self, request, context):
    """聚合商品数量（分配商品预估与门店订货明细处, 商品id与门店id不能都多传，必须有一个为单一id）
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CalculateAllocation(self, request, context):
    """计算商品分配
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListCalculateAllocationResult(self, request, context):
    """获取计算商品分配列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DestroyCalculateAllocation(self, request, context):
    """// 获取计算商品分配的门店
    rpc ListCalculateAllocationStore (ListCalculateAllocationStoreRequest) returns (ListCalculateAllocationStoreResponse) {
    option (google.api.http) = {
    get: "/api/v2/supply/reduction_augmentation_demand/calculate/store/{rule_id}"
    };
    };
    删除计算商品分配
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListReductionAugmentationDemandExcludedStore(self, request, context):
    """列表排除门店
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateReductionAugmentationDemandExcludedStore(self, request, context):
    """新建排除门店
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DestroyReductionAugmentationDemandExcludedStore(self, request, context):
    """按id删除排除门店
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ReductionAugmentationDemandProductAgg(self, request, context):
    """按
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_ReductionAugmentationDemandServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'ListReductionAugmentationDemand': grpc.unary_unary_rpc_method_handler(
          servicer.ListReductionAugmentationDemand,
          request_deserializer=reduction__augmentation__demand__pb2.ListReductionAugmentationDemandRequest.FromString,
          response_serializer=reduction__augmentation__demand__pb2.ListReductionAugmentationDemandResponse.SerializeToString,
      ),
      'RetrieveReductionAugmentationDemand': grpc.unary_unary_rpc_method_handler(
          servicer.RetrieveReductionAugmentationDemand,
          request_deserializer=reduction__augmentation__demand__pb2.RetrieveReductionAugmentationDemandRequest.FromString,
          response_serializer=reduction__augmentation__demand__pb2.RetrieveReductionAugmentationDemandResponse.SerializeToString,
      ),
      'CreateReductionAugmentationDemand': grpc.unary_unary_rpc_method_handler(
          servicer.CreateReductionAugmentationDemand,
          request_deserializer=reduction__augmentation__demand__pb2.CreateReductionAugmentationDemandRequest.FromString,
          response_serializer=reduction__augmentation__demand__pb2.CreateReductionAugmentationDemandResponse.SerializeToString,
      ),
      'UpdateReductionAugmentationDemand': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateReductionAugmentationDemand,
          request_deserializer=reduction__augmentation__demand__pb2.UpdateReductionAugmentationDemandRequest.FromString,
          response_serializer=reduction__augmentation__demand__pb2.UpdateReductionAugmentationDemandResponse.SerializeToString,
      ),
      'ListReductionAugmentationDemandProduct': grpc.unary_unary_rpc_method_handler(
          servicer.ListReductionAugmentationDemandProduct,
          request_deserializer=reduction__augmentation__demand__pb2.ListReductionAugmentationDemandProductRequest.FromString,
          response_serializer=reduction__augmentation__demand__pb2.ListReductionAugmentationDemandProductResponse.SerializeToString,
      ),
      'ListReductionAugmentationDemandStore': grpc.unary_unary_rpc_method_handler(
          servicer.ListReductionAugmentationDemandStore,
          request_deserializer=reduction__augmentation__demand__pb2.ListReductionAugmentationDemandStoreRequest.FromString,
          response_serializer=reduction__augmentation__demand__pb2.ListReductionAugmentationDemandStoreResponse.SerializeToString,
      ),
      'SumDemandQuantity': grpc.unary_unary_rpc_method_handler(
          servicer.SumDemandQuantity,
          request_deserializer=reduction__augmentation__demand__pb2.SumDemandQuantityRequest.FromString,
          response_serializer=reduction__augmentation__demand__pb2.SumDemandQuantityResponse.SerializeToString,
      ),
      'CalculateAllocation': grpc.unary_unary_rpc_method_handler(
          servicer.CalculateAllocation,
          request_deserializer=reduction__augmentation__demand__pb2.CalculateAllocationRequest.FromString,
          response_serializer=reduction__augmentation__demand__pb2.CalculateAllocationResponse.SerializeToString,
      ),
      'ListCalculateAllocationResult': grpc.unary_unary_rpc_method_handler(
          servicer.ListCalculateAllocationResult,
          request_deserializer=reduction__augmentation__demand__pb2.ListCalculateAllocationResultRequest.FromString,
          response_serializer=reduction__augmentation__demand__pb2.ListCalculateAllocationResultResponse.SerializeToString,
      ),
      'DestroyCalculateAllocation': grpc.unary_unary_rpc_method_handler(
          servicer.DestroyCalculateAllocation,
          request_deserializer=reduction__augmentation__demand__pb2.DestroyCalculateAllocationRequest.FromString,
          response_serializer=reduction__augmentation__demand__pb2.DestroyCalculateAllocationResponse.SerializeToString,
      ),
      'ListReductionAugmentationDemandExcludedStore': grpc.unary_unary_rpc_method_handler(
          servicer.ListReductionAugmentationDemandExcludedStore,
          request_deserializer=reduction__augmentation__demand__pb2.ListReductionAugmentationDemandExcludedStoreRequest.FromString,
          response_serializer=reduction__augmentation__demand__pb2.ListReductionAugmentationDemandExcludedStoreResponse.SerializeToString,
      ),
      'CreateReductionAugmentationDemandExcludedStore': grpc.unary_unary_rpc_method_handler(
          servicer.CreateReductionAugmentationDemandExcludedStore,
          request_deserializer=reduction__augmentation__demand__pb2.CreateReductionAugmentationDemandExcludedStoreRequest.FromString,
          response_serializer=reduction__augmentation__demand__pb2.CreateReductionAugmentationDemandExcludedStoreResponse.SerializeToString,
      ),
      'DestroyReductionAugmentationDemandExcludedStore': grpc.unary_unary_rpc_method_handler(
          servicer.DestroyReductionAugmentationDemandExcludedStore,
          request_deserializer=reduction__augmentation__demand__pb2.DestroyReductionAugmentationDemandExcludedStoreRequest.FromString,
          response_serializer=reduction__augmentation__demand__pb2.DestroyReductionAugmentationDemandExcludedStoreResponse.SerializeToString,
      ),
      'ReductionAugmentationDemandProductAgg': grpc.unary_unary_rpc_method_handler(
          servicer.ReductionAugmentationDemandProductAgg,
          request_deserializer=reduction__augmentation__demand__pb2.ReductionAugmentationDemandProductAggRequest.FromString,
          response_serializer=reduction__augmentation__demand__pb2.ReductionAugmentationDemandProductAggResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'reduction_augmentation_demand.ReductionAugmentationDemand', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
