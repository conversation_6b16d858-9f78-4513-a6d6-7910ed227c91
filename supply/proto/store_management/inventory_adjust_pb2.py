# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: store_management/inventory_adjust.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='store_management/inventory_adjust.proto',
  package='store_management',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\'store_management/inventory_adjust.proto\x12\x10store_management\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xc9\x01\n&GetDemandAdjustProductByStoreIdRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x0c\n\x04type\x18\x02 \x01(\t\x12.\n\norder_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x19\n\x11\x64istribution_type\x18\x04 \x01(\t\x12\x11\n\tvendor_id\x18\x05 \x01(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x06 \x03(\x04\x12\x0b\n\x03lan\x18\x07 \x01(\t\"c\n\x17\x44\x65mandAdjustProductUnit\x12\x0f\n\x07unit_id\x18\x01 \x01(\x04\x12\x11\n\tunit_name\x18\x02 \x01(\t\x12\x11\n\tunit_code\x18\x03 \x01(\t\x12\x11\n\tunit_rate\x18\x04 \x01(\x01\"\xe5\x05\n\x13\x44\x65mandAdjustProduct\x12\x14\n\x0c\x61rrival_days\x18\x01 \x01(\x05\x12\x15\n\rdistribute_by\x18\x02 \x01(\x04\x12\x19\n\x11\x64istribution_type\x18\x03 \x01(\t\x12\x1a\n\x12increment_quantity\x18\x04 \x01(\x02\x12\x14\n\x0cmax_quantity\x18\x05 \x01(\x02\x12\x14\n\x0cmin_quantity\x18\x06 \x01(\x02\x12\x1b\n\x13product_category_id\x18\x07 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x08 \x01(\t\x12\x12\n\nproduct_id\x18\t \x01(\x04\x12\x14\n\x0cproduct_name\x18\n \x01(\t\x12\x10\n\x08quantity\x18\x0b \x01(\x01\x12\x0c\n\x04spec\x18\x0c \x01(\t\x12\x14\n\x0cstorage_type\x18\r \x01(\t\x12\x38\n\x05units\x18\x0e \x03(\x0b\x32).store_management.DemandAdjustProductUnit\x12\x11\n\tsale_type\x18\x11 \x01(\t\x12\x14\n\x0cproduct_type\x18\x12 \x01(\t\x12\x18\n\x10suggest_quantity\x18\x14 \x01(\x02\x12\x19\n\x11\x66inished_quantity\x18\x15 \x01(\x02\x12\x16\n\x0e\x62read_quantity\x18\x16 \x01(\x02\x12\x14\n\x0ctea_quantity\x18\x17 \x01(\x02\x12\x14\n\x0craw_quantity\x18\x18 \x01(\x02\x12\x16\n\x0epurchase_price\x18\x19 \x01(\x02\x12\x14\n\x0cpurchase_tax\x18\x1a \x01(\x02\x12\n\n\x02id\x18\x1b \x01(\x04\x12\x1b\n\x13\x64istribution_circle\x18\x1c \x01(\t\x12\x1e\n\x16\x64istribution_center_id\x18\x1d \x01(\x04\x12\x11\n\tvendor_id\x18\x1e \x01(\x04\x12\x15\n\rcycle_extends\x18\x1f \x01(\t\x12\r\n\x05price\x18  \x01(\t\x12\r\n\x05image\x18! \x01(\t\x12\x10\n\x08\x63urrency\x18\" \x01(\t\"m\n\'GetDemandAdjustProductByStoreIdResponse\x12\x33\n\x04rows\x18\x01 \x03(\x0b\x32%.store_management.DemandAdjustProduct\x12\r\n\x05total\x18\x02 \x01(\x04\"\xf9\x02\n\x19\x43reateDemandAdjustRequest\x12/\n\x0b\x64\x65mand_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06remark\x18\x03 \x01(\t\x12\x0c\n\x04type\x18\x04 \x01(\t\x12\x10\n\x08sub_type\x18\x05 \x01(\t\x12\x10\n\x08store_id\x18\x06 \x01(\x04\x12\x46\n\x08products\x18\x07 \x03(\x0b\x32\x34.store_management.CreateDemandAdjustRequest.Products\x1ao\n\x08Products\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x10\n\x08quantity\x18\x02 \x01(\x01\x12\x19\n\x11\x64istribution_type\x18\x03 \x01(\t\x12\x11\n\tcentre_id\x18\x04 \x01(\x04\x12\x0f\n\x07unit_id\x18\x05 \x01(\x04\"c\n\x1b\x44\x65\x61lDemandAdjustByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06\x61\x63tion\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x04 \x01(\t\"\x1f\n\x08Response\x12\x13\n\x0b\x64\x65scription\x18\x01 \x01(\t\"\x98\x03\n UpdateDemandAdjustProductRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12L\n\x07product\x18\x02 \x03(\x0b\x32;.store_management.UpdateDemandAdjustProductRequest.products\x12.\n\norder_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x61ttachments\x18\x04 \x01(\t\x1a\xcd\x01\n\x08products\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x10\n\x08quantity\x18\x03 \x01(\x01\x12\x10\n\x08tag_type\x18\x04 \x01(\t\x12\x19\n\x11\x64istribution_type\x18\x05 \x01(\t\x12\x17\n\x0f\x64istribution_by\x18\x06 \x01(\x04\x12\x0f\n\x07unit_id\x18\x07 \x01(\t\x12\x11\n\tunit_rate\x18\x08 \x01(\x01\x12\x11\n\tvendor_id\x18\t \x01(\x04\x12\x12\n\norder_type\x18\n \x01(\t\"Q\n DeleteDemandAdjustProductRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12\x1a\n\x12\x64\x65mand_product_ids\x18\x02 \x03(\x04\"\x82\x01\n\x1dUpdateDemandAdjustInfoRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06remark\x18\x02 \x01(\t\x12\x30\n\x0c\x61rrival_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x61ttachments\x18\x04 \x01(\t\" \n\x05IdReq\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\xd1\x03\n\x18QueryDemandAdjustRequest\x12\x11\n\tstore_ids\x18\x01 \x03(\x04\x12\x13\n\x0bhas_product\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x10\n\x08sub_type\x18\x04 \x01(\t\x12\x12\n\nstore_type\x18\x05 \x03(\t\x12.\n\nstart_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x08 \x03(\t\x12\x0e\n\x06offset\x18\n \x01(\x04\x12\r\n\x05limit\x18\x0b \x01(\x04\x12\r\n\x05\x63odes\x18\x0c \x03(\t\x12\x0f\n\x07is_plan\x18\r \x01(\x08\x12\x11\n\tis_adjust\x18\x0e \x01(\x08\x12\x11\n\tsend_type\x18\x0f \x01(\t\x12\r\n\x05order\x18\x10 \x01(\t\x12\x0c\n\x04sort\x18\x11 \x01(\t\x12\x11\n\thavi_code\x18\x12 \x01(\t\x12\x15\n\rpre_havi_code\x18\x13 \x01(\t\x12\x10\n\x08\x62us_type\x18\x14 \x01(\t\x12\x11\n\tplan_name\x18\x15 \x01(\t\x12\x0b\n\x03lan\x18\x16 \x01(\t\x12\r\n\x05types\x18\x17 \x03(\t\"^\n\x19QueryDemandAdjustResponse\x12\x32\n\x04rows\x18\x01 \x03(\x0b\x32$.store_management.DemandAdjustEntity\x12\r\n\x05total\x18\x02 \x01(\x04\"q\n QueryDemandAdjustProductResponse\x12>\n\x04rows\x18\x01 \x03(\x0b\x32\x30.store_management.QueryDemandAdjustProductEntity\x12\r\n\x05total\x18\x02 \x01(\x05\"\xe0\x07\n\x1eQueryDemandAdjustProductEntity\x12\x14\n\x0c\x61rrival_days\x18\x01 \x01(\x05\x12\x15\n\rdistribute_by\x18\x02 \x01(\x04\x12\x19\n\x11\x64istribution_type\x18\x03 \x01(\t\x12\x1a\n\x12increment_quantity\x18\x04 \x01(\x02\x12\x14\n\x0cmax_quantity\x18\x05 \x01(\x02\x12\x14\n\x0cmin_quantity\x18\x06 \x01(\x02\x12\x1b\n\x13product_category_id\x18\x07 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x08 \x01(\t\x12\x12\n\nproduct_id\x18\t \x01(\x04\x12\x14\n\x0cproduct_name\x18\n \x01(\t\x12\x10\n\x08quantity\x18\x0b \x01(\x01\x12\x0c\n\x04spec\x18\x0c \x01(\t\x12\x14\n\x0cstorage_type\x18\r \x01(\t\x12\x0f\n\x07unit_id\x18\x0e \x01(\x04\x12\x11\n\tunit_name\x18\x0f \x01(\t\x12\x11\n\tunit_spec\x18\x10 \x01(\t\x12\x11\n\tsale_type\x18\x11 \x01(\t\x12\x14\n\x0cproduct_type\x18\x12 \x01(\t\x12\x18\n\x10suggest_quantity\x18\x14 \x01(\x02\x12\x19\n\x11\x66inished_quantity\x18\x15 \x01(\x02\x12\x16\n\x0e\x62read_quantity\x18\x16 \x01(\x02\x12\x14\n\x0ctea_quantity\x18\x17 \x01(\x02\x12\x14\n\x0craw_quantity\x18\x18 \x01(\x02\x12\x16\n\x0epurchase_price\x18\x19 \x01(\x02\x12\x14\n\x0cpurchase_tax\x18\x1a \x01(\x02\x12\n\n\x02id\x18\x1b \x01(\x04\x12\x1b\n\x13\x64istribution_circle\x18\x1c \x01(\t\x12\x1e\n\x16\x64istribution_center_id\x18\x1d \x01(\x04\x12\x11\n\tvendor_id\x18\x1e \x01(\x04\x12\x15\n\rcycle_extends\x18\x1f \x01(\t\x12\x19\n\x11is_first_delivery\x18  \x01(\t\x12\x17\n\x0fyesterday_sales\x18! \x01(\x01\x12\x1a\n\x12\x61verage_week_sales\x18\" \x01(\x01\x12\x1b\n\x13yesterday_order_qty\x18# \x01(\x01\x12\x11\n\tunit_rate\x18$ \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18% \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18& \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\' \x01(\t\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18( \x01(\x01\x12\x1b\n\x13has_new_suggest_qty\x18) \x01(\x08\x12\x0f\n\x07\x62\x61rcode\x18* \x03(\t\"\xf8\x06\n\x12\x44\x65mandAdjustEntity\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08\x62\x61tch_id\x18\x02 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x0c\n\x04type\x18\x04 \x01(\t\x12\x13\n\x0breason_type\x18\x05 \x01(\t\x12\x12\n\nreceive_by\x18\x06 \x01(\x04\x12\x14\n\x0creceive_name\x18\x07 \x01(\t\x12\x15\n\rdistribute_by\x18\x08 \x01(\x04\x12\x1a\n\x12store_secondary_id\x18\t \x01(\t\x12\x12\n\nstore_type\x18\n \x01(\t\x12\x19\n\x11\x64istribution_type\x18\x0b \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\treview_by\x18\r \x01(\x04\x12\x30\n\x0c\x61rrival_date\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x64\x65scription\x18\x0f \x01(\t\x12\x12\n\npartner_id\x18\x10 \x01(\x04\x12\x10\n\x08sub_type\x18\x11 \x01(\t\x12\x0e\n\x06remark\x18\x12 \x01(\t\x12\x13\n\x0bhas_product\x18\x13 \x01(\x05\x12\x0e\n\x06is_sys\x18\x14 \x01(\x05\x12\x0e\n\x06status\x18\x15 \x01(\t\x12\x16\n\x0eprocess_status\x18\x16 \x01(\t\x12.\n\nupdated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\x18 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x19 \x01(\t\x12\x14\n\x0cupdated_name\x18\x1a \x01(\t\x12.\n\ncreated_at\x18\x1b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x1c \x01(\x04\x12\x15\n\rproduct_count\x18\x1d \x01(\x05\x12\x0f\n\x07is_plan\x18\x1e \x01(\x08\x12\x11\n\tis_adjust\x18\x1f \x01(\x08\x12\x11\n\tsend_type\x18  \x01(\t\x12\x11\n\thavi_code\x18! \x01(\t\x12\x15\n\rpre_havi_code\x18\" \x01(\t\x12\x10\n\x08\x62us_type\x18# \x01(\t\x12\x13\n\x0b\x61ttachments\x18$ \x01(\t\x12\x13\n\x0bplan_method\x18% \x01(\t\x12\x11\n\tplan_name\x18& \x01(\t\"\xb5\x08\n\x07Returns\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x11\n\treturn_by\x18\x03 \x01(\x04\x12\x15\n\rreturn_number\x18\x04 \x01(\t\x12\x0e\n\x06status\x18\x05 \x01(\t\x12\x11\n\treview_by\x18\x06 \x01(\x04\x12\x1e\n\x16return_delivery_number\x18\x08 \x01(\t\x12/\n\x0breturn_date\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x38\n\x14return_delivery_date\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rreturn_reason\x18\x0b \x01(\t\x12\x0c\n\x04type\x18\x0c \x01(\t\x12\x10\n\x08sub_type\x18\r \x01(\t\x12\x0e\n\x06remark\x18\x0e \x01(\t\x12\x1a\n\x12store_secondary_id\x18\x0f \x01(\t\x12.\n\ncreated_at\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x12 \x01(\x04\x12\x12\n\nupdated_by\x18\x13 \x01(\x04\x12\x11\n\treturn_to\x18\x14 \x01(\x04\x12\x18\n\x10inventory_status\x18\x15 \x01(\t\x12\x18\n\x10inventory_req_id\x18\x16 \x01(\x04\x12\x12\n\npartner_id\x18\x17 \x01(\x04\x12\x15\n\rreject_reason\x18\x19 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x1a \x03(\t\x12\x14\n\x0c\x63reated_name\x18\x1b \x01(\t\x12\x14\n\x0cupdated_name\x18\x1c \x01(\t\x12\x14\n\x0cproduct_nums\x18\x1d \x01(\x04\x12\x16\n\x0elogistics_type\x18\x1f \x01(\t\x12\x12\n\ntrans_type\x18# \x01(\t\x12\x11\n\tsource_id\x18% \x01(\x04\x12\x13\n\x0bsource_code\x18& \x01(\t\x12\x31\n\rdelivery_date\x18\' \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nrequest_id\x18( \x01(\x04\x12\x16\n\x0ereturn_to_code\x18) \x01(\t\x12\x16\n\x0ereturn_to_name\x18* \x01(\t\x12\x16\n\x0ereturn_by_code\x18+ \x01(\t\x12\x16\n\x0ereturn_by_name\x18, \x01(\t\x12\x15\n\rfranchisee_id\x18\x37 \x01(\x04\x12\x17\n\x0f\x66ranchisee_code\x18\x38 \x01(\t\x12\x17\n\x0f\x66ranchisee_name\x18\x39 \x01(\t\x12\x13\n\x0bpayment_way\x18: \x01(\t\x12\x15\n\rdelivery_code\x18; \x01(\t\x12\x14\n\x0creceive_code\x18< \x01(\t\"z\n\x0cValidProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12\x0c\n\x04spec\x18\x04 \x01(\t\x12$\n\x04unit\x18\x05 \x01(\x0b\x32\x16.store_management.Unit\"2\n\x04Unit\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08quantity\x18\x02 \x01(\x05\x12\x0c\n\x04rate\x18\x03 \x01(\x01\"\x9e\x07\n\rProductDetail\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x11\n\treturn_id\x18\x02 \x01(\x04\x12\x11\n\treturn_by\x18\x03 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x05 \x01(\t\x12\x12\n\nproduct_id\x18\x06 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x10\n\x08quantity\x18\x08 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\t \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\n \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x0b \x01(\t\x12\x0f\n\x07unit_id\x18\x0c \x01(\x04\x12\x11\n\tunit_name\x18\r \x01(\t\x12\x11\n\tunit_rate\x18\x0e \x01(\x01\x12\x11\n\tunit_spec\x18\x0f \x01(\t\x12%\n\x1d\x61\x63\x63ounting_confirmed_quantity\x18\x10 \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x11 \x01(\x01\x12$\n\x1c\x61\x63\x63ounting_returned_quantity\x18\x12 \x01(\x01\x12\x1a\n\x12\x63onfirmed_quantity\x18\x13 \x01(\x01\x12\x19\n\x11returned_quantity\x18\x14 \x01(\x01\x12\x14\n\x0cis_confirmed\x18\x15 \x01(\x08\x12/\n\x0breturn_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\treturn_to\x18\x17 \x01(\x04\x12\x18\n\x10inventory_status\x18\x18 \x01(\t\x12\x18\n\x10inventory_req_id\x18\x19 \x01(\x04\x12.\n\ncreated_at\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x1b \x01(\x04\x12.\n\nupdated_at\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\x1d \x01(\x04\x12\x12\n\npartner_id\x18\x1e \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1f \x01(\t\x12\x14\n\x0cupdated_name\x18  \x01(\t\x12\x14\n\x0cstorage_type\x18# \x01(\t\x12\r\n\x05price\x18$ \x01(\x01\x12\x10\n\x08tax_rate\x18% \x01(\x02\x12\x11\n\tprice_tax\x18& \x01(\x01\x12\x11\n\tsum_price\x18\' \x01(\x01\x12\x13\n\x0b\x61ttachments\x18- \x03(\t\"\xb2\x02\n\rReturnProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x10\n\x08quantity\x18\x03 \x01(\x01\x12\x1a\n\x12\x63onfirmed_quantity\x18\x04 \x01(\x01\x12\x14\n\x0cproduct_code\x18\x05 \x01(\t\x12\x14\n\x0cproduct_name\x18\x06 \x01(\t\x12\x0f\n\x07unit_id\x18\x07 \x01(\x04\x12\x11\n\tunit_name\x18\x08 \x01(\t\x12\x11\n\tunit_spec\x18\t \x01(\t\x12\x11\n\tunit_rate\x18\n \x01(\x02\x12\x10\n\x08tax_rate\x18\x0b \x01(\x02\x12\r\n\x05price\x18\x0c \x01(\x02\x12\x11\n\tprice_tax\x18\r \x01(\x02\x12\x11\n\treturn_to\x18\x0e \x01(\x04\x12\x16\n\x0elogistics_type\x18\x0f \x01(\t\"\xd2\x02\n\x13\x43reateReturnRequest\x12\x11\n\treturn_by\x18\x01 \x01(\x04\x12\x16\n\x0elogistics_type\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x10\n\x08sub_type\x18\x04 \x01(\t\x12\x38\n\x14return_delivery_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tsource_id\x18\x06 \x01(\x04\x12\x13\n\x0bsource_code\x18\x07 \x01(\t\x12\x15\n\rreturn_reason\x18\x08 \x01(\t\x12\x0e\n\x06remark\x18\t \x01(\t\x12\x13\n\x0b\x61ttachments\x18\n \x03(\t\x12\x12\n\nrequest_id\x18\x0b \x01(\x04\x12\x31\n\x08products\x18\x0c \x03(\x0b\x32\x1f.store_management.ReturnProduct\x12\x0b\n\x03lan\x18\x14 \x01(\t\":\n\x14\x43reateReturnResponse\x12\x11\n\treturn_id\x18\x01 \x03(\x04\x12\x0f\n\x07payload\x18\x02 \x01(\x08\"\xec\x02\n\x1b\x43heckReturnAvailableRequest\x12\x11\n\treturn_by\x18\x01 \x01(\x04\x12\x38\n\x14return_delivery_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x10\n\x08sub_type\x18\x04 \x01(\t\x12\x15\n\rreturn_reason\x18\x05 \x01(\t\x12\x0e\n\x06remark\x18\x06 \x01(\t\x12\x31\n\x08products\x18\x07 \x03(\x0b\x32\x1f.store_management.ReturnProduct\x12\x11\n\treturn_to\x18\t \x01(\x04\x12\x13\n\x0b\x61ttachments\x18\n \x03(\t\x12\x16\n\x0elogistics_type\x18\x0b \x01(\t\x12\x11\n\tsource_id\x18\x0c \x01(\x04\x12\x13\n\x0bsource_code\x18\r \x01(\t\x12\x11\n\treturn_id\x18\x0e \x01(\x04\x12\x0b\n\x03lan\x18\x0f \x01(\t\"}\n\x16GetValidProductRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12\x14\n\x0cproduct_name\x18\x05 \x01(\t\x12\x0b\n\x03lan\x18\x06 \x01(\t\"*\n\x17GetValidProductResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"\xd8\x03\n\x11ListReturnRequest\x12\x11\n\treturn_by\x18\x01 \x03(\x04\x12\x0e\n\x06status\x18\x02 \x03(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x11\n\tsource_id\x18\x04 \x01(\x04\x12\x13\n\x0bsource_code\x18\x05 \x01(\t\x12\x16\n\x0elogistics_type\x18\x06 \x01(\t\x12\x0c\n\x04type\x18\x07 \x01(\t\x12\x10\n\x08sub_type\x18\x08 \x01(\t\x12\x34\n\x10return_date_from\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0ereturn_date_to\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x36\n\x12\x64\x65livery_date_from\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x10\x64\x65livery_date_to\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x14 \x01(\x05\x12\x0e\n\x06offset\x18\x15 \x01(\x05\x12\r\n\x05order\x18\x16 \x01(\t\x12\x0c\n\x04sort\x18\x17 \x01(\t\x12\x0b\n\x03lan\x18\x18 \x01(\t\x12\x11\n\treturn_to\x18\x19 \x03(\x04\"L\n\x12ListReturnResponse\x12\'\n\x04rows\x18\x01 \x03(\x0b\x32\x19.store_management.Returns\x12\r\n\x05total\x18\x02 \x01(\x04\"/\n\x14GetReturnByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"r\n\x1bGetReturnProductByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x0c\n\x04sort\x18\x04 \x01(\t\x12\r\n\x05order\x18\x05 \x01(\t\x12\x0b\n\x03lan\x18\x06 \x01(\t\"\\\n\x1cGetReturnProductByIdResponse\x12-\n\x04rows\x18\x01 \x03(\x0b\x32\x1f.store_management.ProductDetail\x12\r\n\x05total\x18\x02 \x01(\x04\".\n\x13SubmitReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"E\n\x13RejectReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x15\n\rreject_reason\x18\x02 \x01(\t\x12\x0b\n\x03lan\x18\x03 \x01(\t\"C\n\x14\x41pproveReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\ntrans_type\x18\x02 \x01(\t\x12\x0b\n\x03lan\x18\x03 \x01(\t\"/\n\x14\x43onfirmReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"0\n\x15\x44\x65liveryReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\x82\x02\n\x13UpdateReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x31\n\x08products\x18\x02 \x03(\x0b\x32\x1f.store_management.ReturnProduct\x12\x15\n\rreturn_reason\x18\x03 \x01(\t\x12\x0e\n\x06remark\x18\x04 \x01(\t\x12\x38\n\x14return_delivery_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\treturn_to\x18\x06 \x01(\x04\x12\x13\n\x0b\x61ttachments\x18\x07 \x03(\t\x12\x0b\n\x03lan\x18\x08 \x01(\t\x12\x16\n\x0elogistics_type\x18\t \x01(\t\".\n\x13\x44\x65leteReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\'\n\x14ReturnCommonResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\x32\xf3\x1c\n\x1bStoreInventoryAdjustService\x12\xde\x01\n\x1fGetDemandAdjustProductByStoreId\x12\x38.store_management.GetDemandAdjustProductByStoreIdRequest\x1a\x39.store_management.GetDemandAdjustProductByStoreIdResponse\"F\x82\xd3\xe4\x93\x02@\x12>/api/v2/supply/store/inventory_adjust/valid_product/{store_id}\x12\x8f\x01\n\x12\x43reateDemandAdjust\x12+.store_management.CreateDemandAdjustRequest\x1a\x1a.store_management.Response\"0\x82\xd3\xe4\x93\x02*\"%/api/v2/supply/store/inventory_adjust:\x01*\x12\xa1\x01\n\x14\x44\x65\x61lDemandAdjustById\x12-.store_management.DealDemandAdjustByIdRequest\x1a\x1a.store_management.Response\">\x82\xd3\xe4\x93\x02\x38\x1a\x33/api/v2/supply/store/inventory_adjust/{id}/{action}:\x01*\x12\x84\x01\n\x12\x44\x65leteDemandAdjust\x12\x17.store_management.IdReq\x1a\x1a.store_management.Response\"9\x82\xd3\xe4\x93\x02\x33\x1a\x31/api/v2/supply/store/inventory_adjust/delete/{id}\x12\xbd\x01\n\x19UpdateDemandAdjustProduct\x12\x32.store_management.UpdateDemandAdjustProductRequest\x1a\x32.store_management.UpdateDemandAdjustProductRequest\"8\x82\xd3\xe4\x93\x02\x32\x1a-/api/v2/supply/store/inventory_adjust/product:\x01*\x12\xb8\x01\n\x19\x44\x65leteDemandAdjustProduct\x12\x32.store_management.DeleteDemandAdjustProductRequest\x1a\x1a.store_management.Response\"K\x82\xd3\xe4\x93\x02\x45\x1a@/api/v2/supply/store/inventory_adjust/{demand_id}/product/delete:\x01*\x12\x9c\x01\n\x16UpdateDemandAdjustInfo\x12/.store_management.UpdateDemandAdjustInfoRequest\x1a\x1a.store_management.Response\"5\x82\xd3\xe4\x93\x02/\"*/api/v2/supply/store/inventory_adjust/{id}:\x01*\x12\x9a\x01\n\x10ListDemandAdjust\x12*.store_management.QueryDemandAdjustRequest\x1a+.store_management.QueryDemandAdjustResponse\"-\x82\xd3\xe4\x93\x02\'\x12%/api/v2/supply/store/inventory_adjust\x12\x8a\x01\n\x15GetDemandAdjustDetail\x12\x17.store_management.IdReq\x1a$.store_management.DemandAdjustEntity\"2\x82\xd3\xe4\x93\x02,\x12*/api/v2/supply/store/inventory_adjust/{id}\x12\xa7\x01\n\x1cGetDemandAdjustProductDetail\x12\x17.store_management.IdReq\x1a\x32.store_management.QueryDemandAdjustProductResponse\":\x82\xd3\xe4\x93\x02\x34\x12\x32/api/v2/supply/store/inventory_adjust/{id}/product\x12\x8b\x01\n\x0c\x43reateReturn\x12%.store_management.CreateReturnRequest\x1a&.store_management.CreateReturnResponse\",\x82\xd3\xe4\x93\x02&\"!/api/v2/supply/store_mgmt/returns:\x01*\x12\x94\x01\n\x0fGetValidProduct\x12(.store_management.GetValidProductRequest\x1a\x1e.store_management.ValidProduct\"7\x82\xd3\xe4\x93\x02\x31\x12//api/v2/supply/store_mgmt/returns/valid_product\x12\x82\x01\n\nListReturn\x12#.store_management.ListReturnRequest\x1a$.store_management.ListReturnResponse\")\x82\xd3\xe4\x93\x02#\x12!/api/v2/supply/store_mgmt/returns\x12\x82\x01\n\rGetReturnById\x12&.store_management.GetReturnByIdRequest\x1a\x19.store_management.Returns\".\x82\xd3\xe4\x93\x02(\x12&/api/v2/supply/store_mgmt/returns/{id}\x12\xad\x01\n\x14GetReturnProductById\x12-.store_management.GetReturnProductByIdRequest\x1a..store_management.GetReturnProductByIdResponse\"6\x82\xd3\xe4\x93\x02\x30\x12./api/v2/supply/store_mgmt/returns/{id}/product\x12\x97\x01\n\x0cSubmitReturn\x12%.store_management.SubmitReturnRequest\x1a&.store_management.ReturnCommonResponse\"8\x82\xd3\xe4\x93\x02\x32\x1a-/api/v2/supply/store_mgmt/returns/{id}/submit:\x01*\x12\x97\x01\n\x0cRejectReturn\x12%.store_management.RejectReturnRequest\x1a&.store_management.ReturnCommonResponse\"8\x82\xd3\xe4\x93\x02\x32\x1a-/api/v2/supply/store_mgmt/returns/{id}/reject:\x01*\x12\x9a\x01\n\rApproveReturn\x12&.store_management.ApproveReturnRequest\x1a&.store_management.ReturnCommonResponse\"9\x82\xd3\xe4\x93\x02\x33\x1a./api/v2/supply/store_mgmt/returns/{id}/approve:\x01*\x12\x9a\x01\n\rConfirmReturn\x12&.store_management.ConfirmReturnRequest\x1a&.store_management.ReturnCommonResponse\"9\x82\xd3\xe4\x93\x02\x33\x1a./api/v2/supply/store_mgmt/returns/{id}/confirm:\x01*\x12\x9d\x01\n\x0e\x44\x65liveryReturn\x12\'.store_management.DeliveryReturnRequest\x1a&.store_management.ReturnCommonResponse\":\x82\xd3\xe4\x93\x02\x34\x1a//api/v2/supply/store_mgmt/returns/{id}/delivery:\x01*\x12\x97\x01\n\x0cUpdateReturn\x12%.store_management.UpdateReturnRequest\x1a&.store_management.ReturnCommonResponse\"8\x82\xd3\xe4\x93\x02\x32\x1a-/api/v2/supply/store_mgmt/returns/{id}/update:\x01*\x12\x97\x01\n\x0c\x44\x65leteReturn\x12%.store_management.DeleteReturnRequest\x1a&.store_management.ReturnCommonResponse\"8\x82\xd3\xe4\x93\x02\x32\x1a-/api/v2/supply/store_mgmt/returns/{id}/delete:\x01*\x12\xa6\x01\n\x19\x43heckReturnAvailableByrec\x12-.store_management.CheckReturnAvailableRequest\x1a&.store_management.ReturnCommonResponse\"2\x82\xd3\xe4\x93\x02,\"\'/api/v2/supply/store_mgmt/returns/check:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_GETDEMANDADJUSTPRODUCTBYSTOREIDREQUEST = _descriptor.Descriptor(
  name='GetDemandAdjustProductByStoreIdRequest',
  full_name='store_management.GetDemandAdjustProductByStoreIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='store_management.GetDemandAdjustProductByStoreIdRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store_management.GetDemandAdjustProductByStoreIdRequest.type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date', full_name='store_management.GetDemandAdjustProductByStoreIdRequest.order_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='store_management.GetDemandAdjustProductByStoreIdRequest.distribution_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_id', full_name='store_management.GetDemandAdjustProductByStoreIdRequest.vendor_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='store_management.GetDemandAdjustProductByStoreIdRequest.category_ids', index=5,
      number=6, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.GetDemandAdjustProductByStoreIdRequest.lan', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=125,
  serialized_end=326,
)


_DEMANDADJUSTPRODUCTUNIT = _descriptor.Descriptor(
  name='DemandAdjustProductUnit',
  full_name='store_management.DemandAdjustProductUnit',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='store_management.DemandAdjustProductUnit.unit_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='store_management.DemandAdjustProductUnit.unit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_code', full_name='store_management.DemandAdjustProductUnit.unit_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='store_management.DemandAdjustProductUnit.unit_rate', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=328,
  serialized_end=427,
)


_DEMANDADJUSTPRODUCT = _descriptor.Descriptor(
  name='DemandAdjustProduct',
  full_name='store_management.DemandAdjustProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='arrival_days', full_name='store_management.DemandAdjustProduct.arrival_days', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_by', full_name='store_management.DemandAdjustProduct.distribute_by', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='store_management.DemandAdjustProduct.distribution_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_quantity', full_name='store_management.DemandAdjustProduct.increment_quantity', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_quantity', full_name='store_management.DemandAdjustProduct.max_quantity', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_quantity', full_name='store_management.DemandAdjustProduct.min_quantity', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_category_id', full_name='store_management.DemandAdjustProduct.product_category_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='store_management.DemandAdjustProduct.product_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store_management.DemandAdjustProduct.product_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store_management.DemandAdjustProduct.product_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='store_management.DemandAdjustProduct.quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='store_management.DemandAdjustProduct.spec', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='store_management.DemandAdjustProduct.storage_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='store_management.DemandAdjustProduct.units', index=13,
      number=14, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sale_type', full_name='store_management.DemandAdjustProduct.sale_type', index=14,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='store_management.DemandAdjustProduct.product_type', index=15,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='suggest_quantity', full_name='store_management.DemandAdjustProduct.suggest_quantity', index=16,
      number=20, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='finished_quantity', full_name='store_management.DemandAdjustProduct.finished_quantity', index=17,
      number=21, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bread_quantity', full_name='store_management.DemandAdjustProduct.bread_quantity', index=18,
      number=22, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tea_quantity', full_name='store_management.DemandAdjustProduct.tea_quantity', index=19,
      number=23, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='raw_quantity', full_name='store_management.DemandAdjustProduct.raw_quantity', index=20,
      number=24, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_price', full_name='store_management.DemandAdjustProduct.purchase_price', index=21,
      number=25, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_tax', full_name='store_management.DemandAdjustProduct.purchase_tax', index=22,
      number=26, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.DemandAdjustProduct.id', index=23,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_circle', full_name='store_management.DemandAdjustProduct.distribution_circle', index=24,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_center_id', full_name='store_management.DemandAdjustProduct.distribution_center_id', index=25,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_id', full_name='store_management.DemandAdjustProduct.vendor_id', index=26,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cycle_extends', full_name='store_management.DemandAdjustProduct.cycle_extends', index=27,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='store_management.DemandAdjustProduct.price', index=28,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='image', full_name='store_management.DemandAdjustProduct.image', index=29,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='store_management.DemandAdjustProduct.currency', index=30,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=430,
  serialized_end=1171,
)


_GETDEMANDADJUSTPRODUCTBYSTOREIDRESPONSE = _descriptor.Descriptor(
  name='GetDemandAdjustProductByStoreIdResponse',
  full_name='store_management.GetDemandAdjustProductByStoreIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_management.GetDemandAdjustProductByStoreIdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store_management.GetDemandAdjustProductByStoreIdResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1173,
  serialized_end=1282,
)


_CREATEDEMANDADJUSTREQUEST_PRODUCTS = _descriptor.Descriptor(
  name='Products',
  full_name='store_management.CreateDemandAdjustRequest.Products',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store_management.CreateDemandAdjustRequest.Products.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='store_management.CreateDemandAdjustRequest.Products.quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='store_management.CreateDemandAdjustRequest.Products.distribution_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='centre_id', full_name='store_management.CreateDemandAdjustRequest.Products.centre_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='store_management.CreateDemandAdjustRequest.Products.unit_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1551,
  serialized_end=1662,
)

_CREATEDEMANDADJUSTREQUEST = _descriptor.Descriptor(
  name='CreateDemandAdjustRequest',
  full_name='store_management.CreateDemandAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='store_management.CreateDemandAdjustRequest.demand_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='store_management.CreateDemandAdjustRequest.arrival_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_management.CreateDemandAdjustRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store_management.CreateDemandAdjustRequest.type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='store_management.CreateDemandAdjustRequest.sub_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='store_management.CreateDemandAdjustRequest.store_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='store_management.CreateDemandAdjustRequest.products', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_CREATEDEMANDADJUSTREQUEST_PRODUCTS, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1285,
  serialized_end=1662,
)


_DEALDEMANDADJUSTBYIDREQUEST = _descriptor.Descriptor(
  name='DealDemandAdjustByIdRequest',
  full_name='store_management.DealDemandAdjustByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.DealDemandAdjustByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='store_management.DealDemandAdjustByIdRequest.action', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='store_management.DealDemandAdjustByIdRequest.description', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_management.DealDemandAdjustByIdRequest.attachments', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1664,
  serialized_end=1763,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='store_management.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='description', full_name='store_management.Response.description', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1765,
  serialized_end=1796,
)


_UPDATEDEMANDADJUSTPRODUCTREQUEST_PRODUCTS = _descriptor.Descriptor(
  name='products',
  full_name='store_management.UpdateDemandAdjustProductRequest.products',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.UpdateDemandAdjustProductRequest.products.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store_management.UpdateDemandAdjustProductRequest.products.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='store_management.UpdateDemandAdjustProductRequest.products.quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_type', full_name='store_management.UpdateDemandAdjustProductRequest.products.tag_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='store_management.UpdateDemandAdjustProductRequest.products.distribution_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_by', full_name='store_management.UpdateDemandAdjustProductRequest.products.distribution_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='store_management.UpdateDemandAdjustProductRequest.products.unit_id', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='store_management.UpdateDemandAdjustProductRequest.products.unit_rate', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_id', full_name='store_management.UpdateDemandAdjustProductRequest.products.vendor_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='store_management.UpdateDemandAdjustProductRequest.products.order_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2002,
  serialized_end=2207,
)

_UPDATEDEMANDADJUSTPRODUCTREQUEST = _descriptor.Descriptor(
  name='UpdateDemandAdjustProductRequest',
  full_name='store_management.UpdateDemandAdjustProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='store_management.UpdateDemandAdjustProductRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product', full_name='store_management.UpdateDemandAdjustProductRequest.product', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date', full_name='store_management.UpdateDemandAdjustProductRequest.order_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_management.UpdateDemandAdjustProductRequest.attachments', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_UPDATEDEMANDADJUSTPRODUCTREQUEST_PRODUCTS, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1799,
  serialized_end=2207,
)


_DELETEDEMANDADJUSTPRODUCTREQUEST = _descriptor.Descriptor(
  name='DeleteDemandAdjustProductRequest',
  full_name='store_management.DeleteDemandAdjustProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='store_management.DeleteDemandAdjustProductRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_product_ids', full_name='store_management.DeleteDemandAdjustProductRequest.demand_product_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2209,
  serialized_end=2290,
)


_UPDATEDEMANDADJUSTINFOREQUEST = _descriptor.Descriptor(
  name='UpdateDemandAdjustInfoRequest',
  full_name='store_management.UpdateDemandAdjustInfoRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.UpdateDemandAdjustInfoRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_management.UpdateDemandAdjustInfoRequest.remark', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='store_management.UpdateDemandAdjustInfoRequest.arrival_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_management.UpdateDemandAdjustInfoRequest.attachments', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2293,
  serialized_end=2423,
)


_IDREQ = _descriptor.Descriptor(
  name='IdReq',
  full_name='store_management.IdReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.IdReq.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.IdReq.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2425,
  serialized_end=2457,
)


_QUERYDEMANDADJUSTREQUEST = _descriptor.Descriptor(
  name='QueryDemandAdjustRequest',
  full_name='store_management.QueryDemandAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='store_management.QueryDemandAdjustRequest.store_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='has_product', full_name='store_management.QueryDemandAdjustRequest.has_product', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store_management.QueryDemandAdjustRequest.type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='store_management.QueryDemandAdjustRequest.sub_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='store_management.QueryDemandAdjustRequest.store_type', index=4,
      number=5, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='store_management.QueryDemandAdjustRequest.start_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='store_management.QueryDemandAdjustRequest.end_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store_management.QueryDemandAdjustRequest.status', index=7,
      number=8, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store_management.QueryDemandAdjustRequest.offset', index=8,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store_management.QueryDemandAdjustRequest.limit', index=9,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='codes', full_name='store_management.QueryDemandAdjustRequest.codes', index=10,
      number=12, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_plan', full_name='store_management.QueryDemandAdjustRequest.is_plan', index=11,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='store_management.QueryDemandAdjustRequest.is_adjust', index=12,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='send_type', full_name='store_management.QueryDemandAdjustRequest.send_type', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='store_management.QueryDemandAdjustRequest.order', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='store_management.QueryDemandAdjustRequest.sort', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='havi_code', full_name='store_management.QueryDemandAdjustRequest.havi_code', index=16,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_havi_code', full_name='store_management.QueryDemandAdjustRequest.pre_havi_code', index=17,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bus_type', full_name='store_management.QueryDemandAdjustRequest.bus_type', index=18,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_name', full_name='store_management.QueryDemandAdjustRequest.plan_name', index=19,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.QueryDemandAdjustRequest.lan', index=20,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='types', full_name='store_management.QueryDemandAdjustRequest.types', index=21,
      number=23, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2460,
  serialized_end=2925,
)


_QUERYDEMANDADJUSTRESPONSE = _descriptor.Descriptor(
  name='QueryDemandAdjustResponse',
  full_name='store_management.QueryDemandAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_management.QueryDemandAdjustResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store_management.QueryDemandAdjustResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2927,
  serialized_end=3021,
)


_QUERYDEMANDADJUSTPRODUCTRESPONSE = _descriptor.Descriptor(
  name='QueryDemandAdjustProductResponse',
  full_name='store_management.QueryDemandAdjustProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_management.QueryDemandAdjustProductResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store_management.QueryDemandAdjustProductResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3023,
  serialized_end=3136,
)


_QUERYDEMANDADJUSTPRODUCTENTITY = _descriptor.Descriptor(
  name='QueryDemandAdjustProductEntity',
  full_name='store_management.QueryDemandAdjustProductEntity',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='arrival_days', full_name='store_management.QueryDemandAdjustProductEntity.arrival_days', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_by', full_name='store_management.QueryDemandAdjustProductEntity.distribute_by', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='store_management.QueryDemandAdjustProductEntity.distribution_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_quantity', full_name='store_management.QueryDemandAdjustProductEntity.increment_quantity', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_quantity', full_name='store_management.QueryDemandAdjustProductEntity.max_quantity', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_quantity', full_name='store_management.QueryDemandAdjustProductEntity.min_quantity', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_category_id', full_name='store_management.QueryDemandAdjustProductEntity.product_category_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='store_management.QueryDemandAdjustProductEntity.product_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store_management.QueryDemandAdjustProductEntity.product_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store_management.QueryDemandAdjustProductEntity.product_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='store_management.QueryDemandAdjustProductEntity.quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='store_management.QueryDemandAdjustProductEntity.spec', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='store_management.QueryDemandAdjustProductEntity.storage_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='store_management.QueryDemandAdjustProductEntity.unit_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='store_management.QueryDemandAdjustProductEntity.unit_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='store_management.QueryDemandAdjustProductEntity.unit_spec', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sale_type', full_name='store_management.QueryDemandAdjustProductEntity.sale_type', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='store_management.QueryDemandAdjustProductEntity.product_type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='suggest_quantity', full_name='store_management.QueryDemandAdjustProductEntity.suggest_quantity', index=18,
      number=20, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='finished_quantity', full_name='store_management.QueryDemandAdjustProductEntity.finished_quantity', index=19,
      number=21, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bread_quantity', full_name='store_management.QueryDemandAdjustProductEntity.bread_quantity', index=20,
      number=22, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tea_quantity', full_name='store_management.QueryDemandAdjustProductEntity.tea_quantity', index=21,
      number=23, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='raw_quantity', full_name='store_management.QueryDemandAdjustProductEntity.raw_quantity', index=22,
      number=24, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_price', full_name='store_management.QueryDemandAdjustProductEntity.purchase_price', index=23,
      number=25, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_tax', full_name='store_management.QueryDemandAdjustProductEntity.purchase_tax', index=24,
      number=26, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.QueryDemandAdjustProductEntity.id', index=25,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_circle', full_name='store_management.QueryDemandAdjustProductEntity.distribution_circle', index=26,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_center_id', full_name='store_management.QueryDemandAdjustProductEntity.distribution_center_id', index=27,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_id', full_name='store_management.QueryDemandAdjustProductEntity.vendor_id', index=28,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cycle_extends', full_name='store_management.QueryDemandAdjustProductEntity.cycle_extends', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_first_delivery', full_name='store_management.QueryDemandAdjustProductEntity.is_first_delivery', index=30,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='yesterday_sales', full_name='store_management.QueryDemandAdjustProductEntity.yesterday_sales', index=31,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='average_week_sales', full_name='store_management.QueryDemandAdjustProductEntity.average_week_sales', index=32,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='yesterday_order_qty', full_name='store_management.QueryDemandAdjustProductEntity.yesterday_order_qty', index=33,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='store_management.QueryDemandAdjustProductEntity.unit_rate', index=34,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='store_management.QueryDemandAdjustProductEntity.accounting_unit_id', index=35,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='store_management.QueryDemandAdjustProductEntity.accounting_unit_name', index=36,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='store_management.QueryDemandAdjustProductEntity.accounting_unit_spec', index=37,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='store_management.QueryDemandAdjustProductEntity.accounting_quantity', index=38,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='has_new_suggest_qty', full_name='store_management.QueryDemandAdjustProductEntity.has_new_suggest_qty', index=39,
      number=41, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='barcode', full_name='store_management.QueryDemandAdjustProductEntity.barcode', index=40,
      number=42, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3139,
  serialized_end=4131,
)


_DEMANDADJUSTENTITY = _descriptor.Descriptor(
  name='DemandAdjustEntity',
  full_name='store_management.DemandAdjustEntity',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.DemandAdjustEntity.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='store_management.DemandAdjustEntity.batch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store_management.DemandAdjustEntity.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store_management.DemandAdjustEntity.type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='store_management.DemandAdjustEntity.reason_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='store_management.DemandAdjustEntity.receive_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_name', full_name='store_management.DemandAdjustEntity.receive_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_by', full_name='store_management.DemandAdjustEntity.distribute_by', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='store_management.DemandAdjustEntity.store_secondary_id', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='store_management.DemandAdjustEntity.store_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='store_management.DemandAdjustEntity.distribution_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='store_management.DemandAdjustEntity.demand_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='store_management.DemandAdjustEntity.review_by', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='store_management.DemandAdjustEntity.arrival_date', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='store_management.DemandAdjustEntity.description', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='store_management.DemandAdjustEntity.partner_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='store_management.DemandAdjustEntity.sub_type', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_management.DemandAdjustEntity.remark', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='has_product', full_name='store_management.DemandAdjustEntity.has_product', index=18,
      number=19, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_sys', full_name='store_management.DemandAdjustEntity.is_sys', index=19,
      number=20, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store_management.DemandAdjustEntity.status', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='store_management.DemandAdjustEntity.process_status', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='store_management.DemandAdjustEntity.updated_at', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='store_management.DemandAdjustEntity.updated_by', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='store_management.DemandAdjustEntity.created_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='store_management.DemandAdjustEntity.updated_name', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='store_management.DemandAdjustEntity.created_at', index=26,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='store_management.DemandAdjustEntity.created_by', index=27,
      number=28, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_count', full_name='store_management.DemandAdjustEntity.product_count', index=28,
      number=29, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_plan', full_name='store_management.DemandAdjustEntity.is_plan', index=29,
      number=30, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='store_management.DemandAdjustEntity.is_adjust', index=30,
      number=31, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='send_type', full_name='store_management.DemandAdjustEntity.send_type', index=31,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='havi_code', full_name='store_management.DemandAdjustEntity.havi_code', index=32,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_havi_code', full_name='store_management.DemandAdjustEntity.pre_havi_code', index=33,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bus_type', full_name='store_management.DemandAdjustEntity.bus_type', index=34,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_management.DemandAdjustEntity.attachments', index=35,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_method', full_name='store_management.DemandAdjustEntity.plan_method', index=36,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_name', full_name='store_management.DemandAdjustEntity.plan_name', index=37,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4134,
  serialized_end=5022,
)


_RETURNS = _descriptor.Descriptor(
  name='Returns',
  full_name='store_management.Returns',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.Returns.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store_management.Returns.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_by', full_name='store_management.Returns.return_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_number', full_name='store_management.Returns.return_number', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store_management.Returns.status', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='store_management.Returns.review_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_number', full_name='store_management.Returns.return_delivery_number', index=6,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date', full_name='store_management.Returns.return_date', index=7,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_date', full_name='store_management.Returns.return_delivery_date', index=8,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_reason', full_name='store_management.Returns.return_reason', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store_management.Returns.type', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='store_management.Returns.sub_type', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_management.Returns.remark', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='store_management.Returns.store_secondary_id', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='store_management.Returns.created_at', index=14,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='store_management.Returns.updated_at', index=15,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='store_management.Returns.created_by', index=16,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='store_management.Returns.updated_by', index=17,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='store_management.Returns.return_to', index=18,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_status', full_name='store_management.Returns.inventory_status', index=19,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_req_id', full_name='store_management.Returns.inventory_req_id', index=20,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='store_management.Returns.partner_id', index=21,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='store_management.Returns.reject_reason', index=22,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_management.Returns.attachments', index=23,
      number=26, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='store_management.Returns.created_name', index=24,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='store_management.Returns.updated_name', index=25,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_nums', full_name='store_management.Returns.product_nums', index=26,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='store_management.Returns.logistics_type', index=27,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_type', full_name='store_management.Returns.trans_type', index=28,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_id', full_name='store_management.Returns.source_id', index=29,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_code', full_name='store_management.Returns.source_code', index=30,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='store_management.Returns.delivery_date', index=31,
      number=39, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='store_management.Returns.request_id', index=32,
      number=40, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to_code', full_name='store_management.Returns.return_to_code', index=33,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to_name', full_name='store_management.Returns.return_to_name', index=34,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_by_code', full_name='store_management.Returns.return_by_code', index=35,
      number=43, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_by_name', full_name='store_management.Returns.return_by_name', index=36,
      number=44, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='store_management.Returns.franchisee_id', index=37,
      number=55, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_code', full_name='store_management.Returns.franchisee_code', index=38,
      number=56, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_name', full_name='store_management.Returns.franchisee_name', index=39,
      number=57, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_way', full_name='store_management.Returns.payment_way', index=40,
      number=58, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_code', full_name='store_management.Returns.delivery_code', index=41,
      number=59, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_code', full_name='store_management.Returns.receive_code', index=42,
      number=60, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5025,
  serialized_end=6102,
)


_VALIDPRODUCT = _descriptor.Descriptor(
  name='ValidProduct',
  full_name='store_management.ValidProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.ValidProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='store_management.ValidProduct.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store_management.ValidProduct.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='store_management.ValidProduct.spec', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit', full_name='store_management.ValidProduct.unit', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6104,
  serialized_end=6226,
)


_UNIT = _descriptor.Descriptor(
  name='Unit',
  full_name='store_management.Unit',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.Unit.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='store_management.Unit.quantity', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='store_management.Unit.rate', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6228,
  serialized_end=6278,
)


_PRODUCTDETAIL = _descriptor.Descriptor(
  name='ProductDetail',
  full_name='store_management.ProductDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.ProductDetail.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_id', full_name='store_management.ProductDetail.return_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_by', full_name='store_management.ProductDetail.return_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='store_management.ProductDetail.product_code', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store_management.ProductDetail.product_id', index=4,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store_management.ProductDetail.product_name', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='store_management.ProductDetail.quantity', index=6,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='store_management.ProductDetail.accounting_unit_id', index=7,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='store_management.ProductDetail.accounting_unit_name', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='store_management.ProductDetail.accounting_unit_spec', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='store_management.ProductDetail.unit_id', index=10,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='store_management.ProductDetail.unit_name', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='store_management.ProductDetail.unit_rate', index=12,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='store_management.ProductDetail.unit_spec', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_confirmed_quantity', full_name='store_management.ProductDetail.accounting_confirmed_quantity', index=14,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='store_management.ProductDetail.accounting_quantity', index=15,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_returned_quantity', full_name='store_management.ProductDetail.accounting_returned_quantity', index=16,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='store_management.ProductDetail.confirmed_quantity', index=17,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='returned_quantity', full_name='store_management.ProductDetail.returned_quantity', index=18,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_confirmed', full_name='store_management.ProductDetail.is_confirmed', index=19,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date', full_name='store_management.ProductDetail.return_date', index=20,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='store_management.ProductDetail.return_to', index=21,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_status', full_name='store_management.ProductDetail.inventory_status', index=22,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_req_id', full_name='store_management.ProductDetail.inventory_req_id', index=23,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='store_management.ProductDetail.created_at', index=24,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='store_management.ProductDetail.created_by', index=25,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='store_management.ProductDetail.updated_at', index=26,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='store_management.ProductDetail.updated_by', index=27,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='store_management.ProductDetail.partner_id', index=28,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='store_management.ProductDetail.created_name', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='store_management.ProductDetail.updated_name', index=30,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='store_management.ProductDetail.storage_type', index=31,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='store_management.ProductDetail.price', index=32,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='store_management.ProductDetail.tax_rate', index=33,
      number=37, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_tax', full_name='store_management.ProductDetail.price_tax', index=34,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price', full_name='store_management.ProductDetail.sum_price', index=35,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_management.ProductDetail.attachments', index=36,
      number=45, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6281,
  serialized_end=7207,
)


_RETURNPRODUCT = _descriptor.Descriptor(
  name='ReturnProduct',
  full_name='store_management.ReturnProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.ReturnProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store_management.ReturnProduct.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='store_management.ReturnProduct.quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='store_management.ReturnProduct.confirmed_quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='store_management.ReturnProduct.product_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store_management.ReturnProduct.product_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='store_management.ReturnProduct.unit_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='store_management.ReturnProduct.unit_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='store_management.ReturnProduct.unit_spec', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='store_management.ReturnProduct.unit_rate', index=9,
      number=10, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='store_management.ReturnProduct.tax_rate', index=10,
      number=11, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='store_management.ReturnProduct.price', index=11,
      number=12, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_tax', full_name='store_management.ReturnProduct.price_tax', index=12,
      number=13, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='store_management.ReturnProduct.return_to', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='store_management.ReturnProduct.logistics_type', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7210,
  serialized_end=7516,
)


_CREATERETURNREQUEST = _descriptor.Descriptor(
  name='CreateReturnRequest',
  full_name='store_management.CreateReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='return_by', full_name='store_management.CreateReturnRequest.return_by', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='store_management.CreateReturnRequest.logistics_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store_management.CreateReturnRequest.type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='store_management.CreateReturnRequest.sub_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_date', full_name='store_management.CreateReturnRequest.return_delivery_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_id', full_name='store_management.CreateReturnRequest.source_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_code', full_name='store_management.CreateReturnRequest.source_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_reason', full_name='store_management.CreateReturnRequest.return_reason', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_management.CreateReturnRequest.remark', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_management.CreateReturnRequest.attachments', index=9,
      number=10, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='store_management.CreateReturnRequest.request_id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='store_management.CreateReturnRequest.products', index=11,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.CreateReturnRequest.lan', index=12,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7519,
  serialized_end=7857,
)


_CREATERETURNRESPONSE = _descriptor.Descriptor(
  name='CreateReturnResponse',
  full_name='store_management.CreateReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='return_id', full_name='store_management.CreateReturnResponse.return_id', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payload', full_name='store_management.CreateReturnResponse.payload', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7859,
  serialized_end=7917,
)


_CHECKRETURNAVAILABLEREQUEST = _descriptor.Descriptor(
  name='CheckReturnAvailableRequest',
  full_name='store_management.CheckReturnAvailableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='return_by', full_name='store_management.CheckReturnAvailableRequest.return_by', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_date', full_name='store_management.CheckReturnAvailableRequest.return_delivery_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store_management.CheckReturnAvailableRequest.type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='store_management.CheckReturnAvailableRequest.sub_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_reason', full_name='store_management.CheckReturnAvailableRequest.return_reason', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_management.CheckReturnAvailableRequest.remark', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='store_management.CheckReturnAvailableRequest.products', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='store_management.CheckReturnAvailableRequest.return_to', index=7,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_management.CheckReturnAvailableRequest.attachments', index=8,
      number=10, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='store_management.CheckReturnAvailableRequest.logistics_type', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_id', full_name='store_management.CheckReturnAvailableRequest.source_id', index=10,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_code', full_name='store_management.CheckReturnAvailableRequest.source_code', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_id', full_name='store_management.CheckReturnAvailableRequest.return_id', index=12,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.CheckReturnAvailableRequest.lan', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7920,
  serialized_end=8284,
)


_GETVALIDPRODUCTREQUEST = _descriptor.Descriptor(
  name='GetValidProductRequest',
  full_name='store_management.GetValidProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.GetValidProductRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store_management.GetValidProductRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store_management.GetValidProductRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='store_management.GetValidProductRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store_management.GetValidProductRequest.product_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.GetValidProductRequest.lan', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8286,
  serialized_end=8411,
)


_GETVALIDPRODUCTRESPONSE = _descriptor.Descriptor(
  name='GetValidProductResponse',
  full_name='store_management.GetValidProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='store_management.GetValidProductResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8413,
  serialized_end=8455,
)


_LISTRETURNREQUEST = _descriptor.Descriptor(
  name='ListReturnRequest',
  full_name='store_management.ListReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='return_by', full_name='store_management.ListReturnRequest.return_by', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store_management.ListReturnRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store_management.ListReturnRequest.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_id', full_name='store_management.ListReturnRequest.source_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_code', full_name='store_management.ListReturnRequest.source_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='store_management.ListReturnRequest.logistics_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store_management.ListReturnRequest.type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='store_management.ListReturnRequest.sub_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_from', full_name='store_management.ListReturnRequest.return_date_from', index=8,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_to', full_name='store_management.ListReturnRequest.return_date_to', index=9,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_from', full_name='store_management.ListReturnRequest.delivery_date_from', index=10,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_to', full_name='store_management.ListReturnRequest.delivery_date_to', index=11,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store_management.ListReturnRequest.limit', index=12,
      number=20, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store_management.ListReturnRequest.offset', index=13,
      number=21, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='store_management.ListReturnRequest.order', index=14,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='store_management.ListReturnRequest.sort', index=15,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.ListReturnRequest.lan', index=16,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='store_management.ListReturnRequest.return_to', index=17,
      number=25, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8458,
  serialized_end=8930,
)


_LISTRETURNRESPONSE = _descriptor.Descriptor(
  name='ListReturnResponse',
  full_name='store_management.ListReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_management.ListReturnResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store_management.ListReturnResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8932,
  serialized_end=9008,
)


_GETRETURNBYIDREQUEST = _descriptor.Descriptor(
  name='GetReturnByIdRequest',
  full_name='store_management.GetReturnByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.GetReturnByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.GetReturnByIdRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9010,
  serialized_end=9057,
)


_GETRETURNPRODUCTBYIDREQUEST = _descriptor.Descriptor(
  name='GetReturnProductByIdRequest',
  full_name='store_management.GetReturnProductByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.GetReturnProductByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store_management.GetReturnProductByIdRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store_management.GetReturnProductByIdRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='store_management.GetReturnProductByIdRequest.sort', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='store_management.GetReturnProductByIdRequest.order', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.GetReturnProductByIdRequest.lan', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9059,
  serialized_end=9173,
)


_GETRETURNPRODUCTBYIDRESPONSE = _descriptor.Descriptor(
  name='GetReturnProductByIdResponse',
  full_name='store_management.GetReturnProductByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_management.GetReturnProductByIdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store_management.GetReturnProductByIdResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9175,
  serialized_end=9267,
)


_SUBMITRETURNREQUEST = _descriptor.Descriptor(
  name='SubmitReturnRequest',
  full_name='store_management.SubmitReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.SubmitReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.SubmitReturnRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9269,
  serialized_end=9315,
)


_REJECTRETURNREQUEST = _descriptor.Descriptor(
  name='RejectReturnRequest',
  full_name='store_management.RejectReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.RejectReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='store_management.RejectReturnRequest.reject_reason', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.RejectReturnRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9317,
  serialized_end=9386,
)


_APPROVERETURNREQUEST = _descriptor.Descriptor(
  name='ApproveReturnRequest',
  full_name='store_management.ApproveReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.ApproveReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_type', full_name='store_management.ApproveReturnRequest.trans_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.ApproveReturnRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9388,
  serialized_end=9455,
)


_CONFIRMRETURNREQUEST = _descriptor.Descriptor(
  name='ConfirmReturnRequest',
  full_name='store_management.ConfirmReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.ConfirmReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.ConfirmReturnRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9457,
  serialized_end=9504,
)


_DELIVERYRETURNREQUEST = _descriptor.Descriptor(
  name='DeliveryReturnRequest',
  full_name='store_management.DeliveryReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.DeliveryReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.DeliveryReturnRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9506,
  serialized_end=9554,
)


_UPDATERETURNREQUEST = _descriptor.Descriptor(
  name='UpdateReturnRequest',
  full_name='store_management.UpdateReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.UpdateReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='store_management.UpdateReturnRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_reason', full_name='store_management.UpdateReturnRequest.return_reason', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_management.UpdateReturnRequest.remark', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_date', full_name='store_management.UpdateReturnRequest.return_delivery_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='store_management.UpdateReturnRequest.return_to', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_management.UpdateReturnRequest.attachments', index=6,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.UpdateReturnRequest.lan', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='store_management.UpdateReturnRequest.logistics_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9557,
  serialized_end=9815,
)


_DELETERETURNREQUEST = _descriptor.Descriptor(
  name='DeleteReturnRequest',
  full_name='store_management.DeleteReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.DeleteReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.DeleteReturnRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9817,
  serialized_end=9863,
)


_RETURNCOMMONRESPONSE = _descriptor.Descriptor(
  name='ReturnCommonResponse',
  full_name='store_management.ReturnCommonResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='store_management.ReturnCommonResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9865,
  serialized_end=9904,
)

_GETDEMANDADJUSTPRODUCTBYSTOREIDREQUEST.fields_by_name['order_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMANDADJUSTPRODUCT.fields_by_name['units'].message_type = _DEMANDADJUSTPRODUCTUNIT
_GETDEMANDADJUSTPRODUCTBYSTOREIDRESPONSE.fields_by_name['rows'].message_type = _DEMANDADJUSTPRODUCT
_CREATEDEMANDADJUSTREQUEST_PRODUCTS.containing_type = _CREATEDEMANDADJUSTREQUEST
_CREATEDEMANDADJUSTREQUEST.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEDEMANDADJUSTREQUEST.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEDEMANDADJUSTREQUEST.fields_by_name['products'].message_type = _CREATEDEMANDADJUSTREQUEST_PRODUCTS
_UPDATEDEMANDADJUSTPRODUCTREQUEST_PRODUCTS.containing_type = _UPDATEDEMANDADJUSTPRODUCTREQUEST
_UPDATEDEMANDADJUSTPRODUCTREQUEST.fields_by_name['product'].message_type = _UPDATEDEMANDADJUSTPRODUCTREQUEST_PRODUCTS
_UPDATEDEMANDADJUSTPRODUCTREQUEST.fields_by_name['order_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATEDEMANDADJUSTINFOREQUEST.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYDEMANDADJUSTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYDEMANDADJUSTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYDEMANDADJUSTRESPONSE.fields_by_name['rows'].message_type = _DEMANDADJUSTENTITY
_QUERYDEMANDADJUSTPRODUCTRESPONSE.fields_by_name['rows'].message_type = _QUERYDEMANDADJUSTPRODUCTENTITY
_DEMANDADJUSTENTITY.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMANDADJUSTENTITY.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMANDADJUSTENTITY.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMANDADJUSTENTITY.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RETURNS.fields_by_name['return_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RETURNS.fields_by_name['return_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RETURNS.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RETURNS.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RETURNS.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_VALIDPRODUCT.fields_by_name['unit'].message_type = _UNIT
_PRODUCTDETAIL.fields_by_name['return_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCTDETAIL.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCTDETAIL.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATERETURNREQUEST.fields_by_name['return_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATERETURNREQUEST.fields_by_name['products'].message_type = _RETURNPRODUCT
_CHECKRETURNAVAILABLEREQUEST.fields_by_name['return_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CHECKRETURNAVAILABLEREQUEST.fields_by_name['products'].message_type = _RETURNPRODUCT
_LISTRETURNREQUEST.fields_by_name['return_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNREQUEST.fields_by_name['return_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNREQUEST.fields_by_name['delivery_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNREQUEST.fields_by_name['delivery_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNRESPONSE.fields_by_name['rows'].message_type = _RETURNS
_GETRETURNPRODUCTBYIDRESPONSE.fields_by_name['rows'].message_type = _PRODUCTDETAIL
_UPDATERETURNREQUEST.fields_by_name['products'].message_type = _RETURNPRODUCT
_UPDATERETURNREQUEST.fields_by_name['return_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['GetDemandAdjustProductByStoreIdRequest'] = _GETDEMANDADJUSTPRODUCTBYSTOREIDREQUEST
DESCRIPTOR.message_types_by_name['DemandAdjustProductUnit'] = _DEMANDADJUSTPRODUCTUNIT
DESCRIPTOR.message_types_by_name['DemandAdjustProduct'] = _DEMANDADJUSTPRODUCT
DESCRIPTOR.message_types_by_name['GetDemandAdjustProductByStoreIdResponse'] = _GETDEMANDADJUSTPRODUCTBYSTOREIDRESPONSE
DESCRIPTOR.message_types_by_name['CreateDemandAdjustRequest'] = _CREATEDEMANDADJUSTREQUEST
DESCRIPTOR.message_types_by_name['DealDemandAdjustByIdRequest'] = _DEALDEMANDADJUSTBYIDREQUEST
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
DESCRIPTOR.message_types_by_name['UpdateDemandAdjustProductRequest'] = _UPDATEDEMANDADJUSTPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['DeleteDemandAdjustProductRequest'] = _DELETEDEMANDADJUSTPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['UpdateDemandAdjustInfoRequest'] = _UPDATEDEMANDADJUSTINFOREQUEST
DESCRIPTOR.message_types_by_name['IdReq'] = _IDREQ
DESCRIPTOR.message_types_by_name['QueryDemandAdjustRequest'] = _QUERYDEMANDADJUSTREQUEST
DESCRIPTOR.message_types_by_name['QueryDemandAdjustResponse'] = _QUERYDEMANDADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['QueryDemandAdjustProductResponse'] = _QUERYDEMANDADJUSTPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['QueryDemandAdjustProductEntity'] = _QUERYDEMANDADJUSTPRODUCTENTITY
DESCRIPTOR.message_types_by_name['DemandAdjustEntity'] = _DEMANDADJUSTENTITY
DESCRIPTOR.message_types_by_name['Returns'] = _RETURNS
DESCRIPTOR.message_types_by_name['ValidProduct'] = _VALIDPRODUCT
DESCRIPTOR.message_types_by_name['Unit'] = _UNIT
DESCRIPTOR.message_types_by_name['ProductDetail'] = _PRODUCTDETAIL
DESCRIPTOR.message_types_by_name['ReturnProduct'] = _RETURNPRODUCT
DESCRIPTOR.message_types_by_name['CreateReturnRequest'] = _CREATERETURNREQUEST
DESCRIPTOR.message_types_by_name['CreateReturnResponse'] = _CREATERETURNRESPONSE
DESCRIPTOR.message_types_by_name['CheckReturnAvailableRequest'] = _CHECKRETURNAVAILABLEREQUEST
DESCRIPTOR.message_types_by_name['GetValidProductRequest'] = _GETVALIDPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['GetValidProductResponse'] = _GETVALIDPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['ListReturnRequest'] = _LISTRETURNREQUEST
DESCRIPTOR.message_types_by_name['ListReturnResponse'] = _LISTRETURNRESPONSE
DESCRIPTOR.message_types_by_name['GetReturnByIdRequest'] = _GETRETURNBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetReturnProductByIdRequest'] = _GETRETURNPRODUCTBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetReturnProductByIdResponse'] = _GETRETURNPRODUCTBYIDRESPONSE
DESCRIPTOR.message_types_by_name['SubmitReturnRequest'] = _SUBMITRETURNREQUEST
DESCRIPTOR.message_types_by_name['RejectReturnRequest'] = _REJECTRETURNREQUEST
DESCRIPTOR.message_types_by_name['ApproveReturnRequest'] = _APPROVERETURNREQUEST
DESCRIPTOR.message_types_by_name['ConfirmReturnRequest'] = _CONFIRMRETURNREQUEST
DESCRIPTOR.message_types_by_name['DeliveryReturnRequest'] = _DELIVERYRETURNREQUEST
DESCRIPTOR.message_types_by_name['UpdateReturnRequest'] = _UPDATERETURNREQUEST
DESCRIPTOR.message_types_by_name['DeleteReturnRequest'] = _DELETERETURNREQUEST
DESCRIPTOR.message_types_by_name['ReturnCommonResponse'] = _RETURNCOMMONRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetDemandAdjustProductByStoreIdRequest = _reflection.GeneratedProtocolMessageType('GetDemandAdjustProductByStoreIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDEMANDADJUSTPRODUCTBYSTOREIDREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.GetDemandAdjustProductByStoreIdRequest)
  ))
_sym_db.RegisterMessage(GetDemandAdjustProductByStoreIdRequest)

DemandAdjustProductUnit = _reflection.GeneratedProtocolMessageType('DemandAdjustProductUnit', (_message.Message,), dict(
  DESCRIPTOR = _DEMANDADJUSTPRODUCTUNIT,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.DemandAdjustProductUnit)
  ))
_sym_db.RegisterMessage(DemandAdjustProductUnit)

DemandAdjustProduct = _reflection.GeneratedProtocolMessageType('DemandAdjustProduct', (_message.Message,), dict(
  DESCRIPTOR = _DEMANDADJUSTPRODUCT,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.DemandAdjustProduct)
  ))
_sym_db.RegisterMessage(DemandAdjustProduct)

GetDemandAdjustProductByStoreIdResponse = _reflection.GeneratedProtocolMessageType('GetDemandAdjustProductByStoreIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETDEMANDADJUSTPRODUCTBYSTOREIDRESPONSE,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.GetDemandAdjustProductByStoreIdResponse)
  ))
_sym_db.RegisterMessage(GetDemandAdjustProductByStoreIdResponse)

CreateDemandAdjustRequest = _reflection.GeneratedProtocolMessageType('CreateDemandAdjustRequest', (_message.Message,), dict(

  Products = _reflection.GeneratedProtocolMessageType('Products', (_message.Message,), dict(
    DESCRIPTOR = _CREATEDEMANDADJUSTREQUEST_PRODUCTS,
    __module__ = 'store_management.inventory_adjust_pb2'
    # @@protoc_insertion_point(class_scope:store_management.CreateDemandAdjustRequest.Products)
    ))
  ,
  DESCRIPTOR = _CREATEDEMANDADJUSTREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.CreateDemandAdjustRequest)
  ))
_sym_db.RegisterMessage(CreateDemandAdjustRequest)
_sym_db.RegisterMessage(CreateDemandAdjustRequest.Products)

DealDemandAdjustByIdRequest = _reflection.GeneratedProtocolMessageType('DealDemandAdjustByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _DEALDEMANDADJUSTBYIDREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.DealDemandAdjustByIdRequest)
  ))
_sym_db.RegisterMessage(DealDemandAdjustByIdRequest)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.Response)
  ))
_sym_db.RegisterMessage(Response)

UpdateDemandAdjustProductRequest = _reflection.GeneratedProtocolMessageType('UpdateDemandAdjustProductRequest', (_message.Message,), dict(

  products = _reflection.GeneratedProtocolMessageType('products', (_message.Message,), dict(
    DESCRIPTOR = _UPDATEDEMANDADJUSTPRODUCTREQUEST_PRODUCTS,
    __module__ = 'store_management.inventory_adjust_pb2'
    # @@protoc_insertion_point(class_scope:store_management.UpdateDemandAdjustProductRequest.products)
    ))
  ,
  DESCRIPTOR = _UPDATEDEMANDADJUSTPRODUCTREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.UpdateDemandAdjustProductRequest)
  ))
_sym_db.RegisterMessage(UpdateDemandAdjustProductRequest)
_sym_db.RegisterMessage(UpdateDemandAdjustProductRequest.products)

DeleteDemandAdjustProductRequest = _reflection.GeneratedProtocolMessageType('DeleteDemandAdjustProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEDEMANDADJUSTPRODUCTREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.DeleteDemandAdjustProductRequest)
  ))
_sym_db.RegisterMessage(DeleteDemandAdjustProductRequest)

UpdateDemandAdjustInfoRequest = _reflection.GeneratedProtocolMessageType('UpdateDemandAdjustInfoRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEDEMANDADJUSTINFOREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.UpdateDemandAdjustInfoRequest)
  ))
_sym_db.RegisterMessage(UpdateDemandAdjustInfoRequest)

IdReq = _reflection.GeneratedProtocolMessageType('IdReq', (_message.Message,), dict(
  DESCRIPTOR = _IDREQ,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.IdReq)
  ))
_sym_db.RegisterMessage(IdReq)

QueryDemandAdjustRequest = _reflection.GeneratedProtocolMessageType('QueryDemandAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYDEMANDADJUSTREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.QueryDemandAdjustRequest)
  ))
_sym_db.RegisterMessage(QueryDemandAdjustRequest)

QueryDemandAdjustResponse = _reflection.GeneratedProtocolMessageType('QueryDemandAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYDEMANDADJUSTRESPONSE,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.QueryDemandAdjustResponse)
  ))
_sym_db.RegisterMessage(QueryDemandAdjustResponse)

QueryDemandAdjustProductResponse = _reflection.GeneratedProtocolMessageType('QueryDemandAdjustProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYDEMANDADJUSTPRODUCTRESPONSE,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.QueryDemandAdjustProductResponse)
  ))
_sym_db.RegisterMessage(QueryDemandAdjustProductResponse)

QueryDemandAdjustProductEntity = _reflection.GeneratedProtocolMessageType('QueryDemandAdjustProductEntity', (_message.Message,), dict(
  DESCRIPTOR = _QUERYDEMANDADJUSTPRODUCTENTITY,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.QueryDemandAdjustProductEntity)
  ))
_sym_db.RegisterMessage(QueryDemandAdjustProductEntity)

DemandAdjustEntity = _reflection.GeneratedProtocolMessageType('DemandAdjustEntity', (_message.Message,), dict(
  DESCRIPTOR = _DEMANDADJUSTENTITY,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.DemandAdjustEntity)
  ))
_sym_db.RegisterMessage(DemandAdjustEntity)

Returns = _reflection.GeneratedProtocolMessageType('Returns', (_message.Message,), dict(
  DESCRIPTOR = _RETURNS,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.Returns)
  ))
_sym_db.RegisterMessage(Returns)

ValidProduct = _reflection.GeneratedProtocolMessageType('ValidProduct', (_message.Message,), dict(
  DESCRIPTOR = _VALIDPRODUCT,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.ValidProduct)
  ))
_sym_db.RegisterMessage(ValidProduct)

Unit = _reflection.GeneratedProtocolMessageType('Unit', (_message.Message,), dict(
  DESCRIPTOR = _UNIT,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.Unit)
  ))
_sym_db.RegisterMessage(Unit)

ProductDetail = _reflection.GeneratedProtocolMessageType('ProductDetail', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTDETAIL,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.ProductDetail)
  ))
_sym_db.RegisterMessage(ProductDetail)

ReturnProduct = _reflection.GeneratedProtocolMessageType('ReturnProduct', (_message.Message,), dict(
  DESCRIPTOR = _RETURNPRODUCT,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.ReturnProduct)
  ))
_sym_db.RegisterMessage(ReturnProduct)

CreateReturnRequest = _reflection.GeneratedProtocolMessageType('CreateReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATERETURNREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.CreateReturnRequest)
  ))
_sym_db.RegisterMessage(CreateReturnRequest)

CreateReturnResponse = _reflection.GeneratedProtocolMessageType('CreateReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATERETURNRESPONSE,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.CreateReturnResponse)
  ))
_sym_db.RegisterMessage(CreateReturnResponse)

CheckReturnAvailableRequest = _reflection.GeneratedProtocolMessageType('CheckReturnAvailableRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHECKRETURNAVAILABLEREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.CheckReturnAvailableRequest)
  ))
_sym_db.RegisterMessage(CheckReturnAvailableRequest)

GetValidProductRequest = _reflection.GeneratedProtocolMessageType('GetValidProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETVALIDPRODUCTREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.GetValidProductRequest)
  ))
_sym_db.RegisterMessage(GetValidProductRequest)

GetValidProductResponse = _reflection.GeneratedProtocolMessageType('GetValidProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETVALIDPRODUCTRESPONSE,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.GetValidProductResponse)
  ))
_sym_db.RegisterMessage(GetValidProductResponse)

ListReturnRequest = _reflection.GeneratedProtocolMessageType('ListReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTRETURNREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.ListReturnRequest)
  ))
_sym_db.RegisterMessage(ListReturnRequest)

ListReturnResponse = _reflection.GeneratedProtocolMessageType('ListReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTRETURNRESPONSE,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.ListReturnResponse)
  ))
_sym_db.RegisterMessage(ListReturnResponse)

GetReturnByIdRequest = _reflection.GeneratedProtocolMessageType('GetReturnByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRETURNBYIDREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.GetReturnByIdRequest)
  ))
_sym_db.RegisterMessage(GetReturnByIdRequest)

GetReturnProductByIdRequest = _reflection.GeneratedProtocolMessageType('GetReturnProductByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRETURNPRODUCTBYIDREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.GetReturnProductByIdRequest)
  ))
_sym_db.RegisterMessage(GetReturnProductByIdRequest)

GetReturnProductByIdResponse = _reflection.GeneratedProtocolMessageType('GetReturnProductByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRETURNPRODUCTBYIDRESPONSE,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.GetReturnProductByIdResponse)
  ))
_sym_db.RegisterMessage(GetReturnProductByIdResponse)

SubmitReturnRequest = _reflection.GeneratedProtocolMessageType('SubmitReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITRETURNREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.SubmitReturnRequest)
  ))
_sym_db.RegisterMessage(SubmitReturnRequest)

RejectReturnRequest = _reflection.GeneratedProtocolMessageType('RejectReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _REJECTRETURNREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.RejectReturnRequest)
  ))
_sym_db.RegisterMessage(RejectReturnRequest)

ApproveReturnRequest = _reflection.GeneratedProtocolMessageType('ApproveReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _APPROVERETURNREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.ApproveReturnRequest)
  ))
_sym_db.RegisterMessage(ApproveReturnRequest)

ConfirmReturnRequest = _reflection.GeneratedProtocolMessageType('ConfirmReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMRETURNREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.ConfirmReturnRequest)
  ))
_sym_db.RegisterMessage(ConfirmReturnRequest)

DeliveryReturnRequest = _reflection.GeneratedProtocolMessageType('DeliveryReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELIVERYRETURNREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.DeliveryReturnRequest)
  ))
_sym_db.RegisterMessage(DeliveryReturnRequest)

UpdateReturnRequest = _reflection.GeneratedProtocolMessageType('UpdateReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERETURNREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.UpdateReturnRequest)
  ))
_sym_db.RegisterMessage(UpdateReturnRequest)

DeleteReturnRequest = _reflection.GeneratedProtocolMessageType('DeleteReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETERETURNREQUEST,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.DeleteReturnRequest)
  ))
_sym_db.RegisterMessage(DeleteReturnRequest)

ReturnCommonResponse = _reflection.GeneratedProtocolMessageType('ReturnCommonResponse', (_message.Message,), dict(
  DESCRIPTOR = _RETURNCOMMONRESPONSE,
  __module__ = 'store_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:store_management.ReturnCommonResponse)
  ))
_sym_db.RegisterMessage(ReturnCommonResponse)



_STOREINVENTORYADJUSTSERVICE = _descriptor.ServiceDescriptor(
  name='StoreInventoryAdjustService',
  full_name='store_management.StoreInventoryAdjustService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=9907,
  serialized_end=13606,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetDemandAdjustProductByStoreId',
    full_name='store_management.StoreInventoryAdjustService.GetDemandAdjustProductByStoreId',
    index=0,
    containing_service=None,
    input_type=_GETDEMANDADJUSTPRODUCTBYSTOREIDREQUEST,
    output_type=_GETDEMANDADJUSTPRODUCTBYSTOREIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002@\022>/api/v2/supply/store/inventory_adjust/valid_product/{store_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='CreateDemandAdjust',
    full_name='store_management.StoreInventoryAdjustService.CreateDemandAdjust',
    index=1,
    containing_service=None,
    input_type=_CREATEDEMANDADJUSTREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002*\"%/api/v2/supply/store/inventory_adjust:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DealDemandAdjustById',
    full_name='store_management.StoreInventoryAdjustService.DealDemandAdjustById',
    index=2,
    containing_service=None,
    input_type=_DEALDEMANDADJUSTBYIDREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\0028\0323/api/v2/supply/store/inventory_adjust/{id}/{action}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteDemandAdjust',
    full_name='store_management.StoreInventoryAdjustService.DeleteDemandAdjust',
    index=3,
    containing_service=None,
    input_type=_IDREQ,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\0023\0321/api/v2/supply/store/inventory_adjust/delete/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateDemandAdjustProduct',
    full_name='store_management.StoreInventoryAdjustService.UpdateDemandAdjustProduct',
    index=4,
    containing_service=None,
    input_type=_UPDATEDEMANDADJUSTPRODUCTREQUEST,
    output_type=_UPDATEDEMANDADJUSTPRODUCTREQUEST,
    serialized_options=_b('\202\323\344\223\0022\032-/api/v2/supply/store/inventory_adjust/product:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteDemandAdjustProduct',
    full_name='store_management.StoreInventoryAdjustService.DeleteDemandAdjustProduct',
    index=5,
    containing_service=None,
    input_type=_DELETEDEMANDADJUSTPRODUCTREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002E\032@/api/v2/supply/store/inventory_adjust/{demand_id}/product/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateDemandAdjustInfo',
    full_name='store_management.StoreInventoryAdjustService.UpdateDemandAdjustInfo',
    index=6,
    containing_service=None,
    input_type=_UPDATEDEMANDADJUSTINFOREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002/\"*/api/v2/supply/store/inventory_adjust/{id}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListDemandAdjust',
    full_name='store_management.StoreInventoryAdjustService.ListDemandAdjust',
    index=7,
    containing_service=None,
    input_type=_QUERYDEMANDADJUSTREQUEST,
    output_type=_QUERYDEMANDADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\022%/api/v2/supply/store/inventory_adjust'),
  ),
  _descriptor.MethodDescriptor(
    name='GetDemandAdjustDetail',
    full_name='store_management.StoreInventoryAdjustService.GetDemandAdjustDetail',
    index=8,
    containing_service=None,
    input_type=_IDREQ,
    output_type=_DEMANDADJUSTENTITY,
    serialized_options=_b('\202\323\344\223\002,\022*/api/v2/supply/store/inventory_adjust/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetDemandAdjustProductDetail',
    full_name='store_management.StoreInventoryAdjustService.GetDemandAdjustProductDetail',
    index=9,
    containing_service=None,
    input_type=_IDREQ,
    output_type=_QUERYDEMANDADJUSTPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\0024\0222/api/v2/supply/store/inventory_adjust/{id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='CreateReturn',
    full_name='store_management.StoreInventoryAdjustService.CreateReturn',
    index=10,
    containing_service=None,
    input_type=_CREATERETURNREQUEST,
    output_type=_CREATERETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\"!/api/v2/supply/store_mgmt/returns:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetValidProduct',
    full_name='store_management.StoreInventoryAdjustService.GetValidProduct',
    index=11,
    containing_service=None,
    input_type=_GETVALIDPRODUCTREQUEST,
    output_type=_VALIDPRODUCT,
    serialized_options=_b('\202\323\344\223\0021\022//api/v2/supply/store_mgmt/returns/valid_product'),
  ),
  _descriptor.MethodDescriptor(
    name='ListReturn',
    full_name='store_management.StoreInventoryAdjustService.ListReturn',
    index=12,
    containing_service=None,
    input_type=_LISTRETURNREQUEST,
    output_type=_LISTRETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002#\022!/api/v2/supply/store_mgmt/returns'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReturnById',
    full_name='store_management.StoreInventoryAdjustService.GetReturnById',
    index=13,
    containing_service=None,
    input_type=_GETRETURNBYIDREQUEST,
    output_type=_RETURNS,
    serialized_options=_b('\202\323\344\223\002(\022&/api/v2/supply/store_mgmt/returns/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReturnProductById',
    full_name='store_management.StoreInventoryAdjustService.GetReturnProductById',
    index=14,
    containing_service=None,
    input_type=_GETRETURNPRODUCTBYIDREQUEST,
    output_type=_GETRETURNPRODUCTBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0020\022./api/v2/supply/store_mgmt/returns/{id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='SubmitReturn',
    full_name='store_management.StoreInventoryAdjustService.SubmitReturn',
    index=15,
    containing_service=None,
    input_type=_SUBMITRETURNREQUEST,
    output_type=_RETURNCOMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\032-/api/v2/supply/store_mgmt/returns/{id}/submit:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='RejectReturn',
    full_name='store_management.StoreInventoryAdjustService.RejectReturn',
    index=16,
    containing_service=None,
    input_type=_REJECTRETURNREQUEST,
    output_type=_RETURNCOMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\032-/api/v2/supply/store_mgmt/returns/{id}/reject:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ApproveReturn',
    full_name='store_management.StoreInventoryAdjustService.ApproveReturn',
    index=17,
    containing_service=None,
    input_type=_APPROVERETURNREQUEST,
    output_type=_RETURNCOMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0023\032./api/v2/supply/store_mgmt/returns/{id}/approve:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmReturn',
    full_name='store_management.StoreInventoryAdjustService.ConfirmReturn',
    index=18,
    containing_service=None,
    input_type=_CONFIRMRETURNREQUEST,
    output_type=_RETURNCOMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0023\032./api/v2/supply/store_mgmt/returns/{id}/confirm:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeliveryReturn',
    full_name='store_management.StoreInventoryAdjustService.DeliveryReturn',
    index=19,
    containing_service=None,
    input_type=_DELIVERYRETURNREQUEST,
    output_type=_RETURNCOMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0024\032//api/v2/supply/store_mgmt/returns/{id}/delivery:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateReturn',
    full_name='store_management.StoreInventoryAdjustService.UpdateReturn',
    index=20,
    containing_service=None,
    input_type=_UPDATERETURNREQUEST,
    output_type=_RETURNCOMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\032-/api/v2/supply/store_mgmt/returns/{id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteReturn',
    full_name='store_management.StoreInventoryAdjustService.DeleteReturn',
    index=21,
    containing_service=None,
    input_type=_DELETERETURNREQUEST,
    output_type=_RETURNCOMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\032-/api/v2/supply/store_mgmt/returns/{id}/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CheckReturnAvailableByrec',
    full_name='store_management.StoreInventoryAdjustService.CheckReturnAvailableByrec',
    index=22,
    containing_service=None,
    input_type=_CHECKRETURNAVAILABLEREQUEST,
    output_type=_RETURNCOMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\002,\"\'/api/v2/supply/store_mgmt/returns/check:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_STOREINVENTORYADJUSTSERVICE)

DESCRIPTOR.services_by_name['StoreInventoryAdjustService'] = _STOREINVENTORYADJUSTSERVICE

# @@protoc_insertion_point(module_scope)
