# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from store_management import hd_assignment_pb2 as store__management_dot_hd__assignment__pb2


class StoreManagementHdAssignmentStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateProductMain = channel.unary_unary(
        '/store_management.StoreManagementHdAssignment/CreateProductMain',
        request_serializer=store__management_dot_hd__assignment__pb2.ProductMainRequest.SerializeToString,
        response_deserializer=store__management_dot_hd__assignment__pb2.IdRequest.FromString,
        )
    self.UploadDemandMaster = channel.unary_unary(
        '/store_management.StoreManagementHdAssignment/UploadDemandMaster',
        request_serializer=store__management_dot_hd__assignment__pb2.UploadDemandMasterRequest.SerializeToString,
        response_deserializer=store__management_dot_hd__assignment__pb2.UploadDemandMasterResponse.FromString,
        )
    self.GetDemandMasterUploadByBatchId = channel.unary_unary(
        '/store_management.StoreManagementHdAssignment/GetDemandMasterUploadByBatchId',
        request_serializer=store__management_dot_hd__assignment__pb2.GetDemandMasterUploadByBatchIdRequest.SerializeToString,
        response_deserializer=store__management_dot_hd__assignment__pb2.GetDemandMasterUploadByBatchIdResponse.FromString,
        )
    self.GetDemandMasterUpload = channel.unary_unary(
        '/store_management.StoreManagementHdAssignment/GetDemandMasterUpload',
        request_serializer=store__management_dot_hd__assignment__pb2.GetDemandMasterUploadRequest.SerializeToString,
        response_deserializer=store__management_dot_hd__assignment__pb2.GetDemandMasterUploadResponse.FromString,
        )
    self.ApproveDemandMasterUpload = channel.unary_unary(
        '/store_management.StoreManagementHdAssignment/ApproveDemandMasterUpload',
        request_serializer=store__management_dot_hd__assignment__pb2.ApproveDemandMasterUploadRequest.SerializeToString,
        response_deserializer=store__management_dot_hd__assignment__pb2.ApproveDemandMasterUploadResponse.FromString,
        )
    self.CancelDemandMasterUpload = channel.unary_unary(
        '/store_management.StoreManagementHdAssignment/CancelDemandMasterUpload',
        request_serializer=store__management_dot_hd__assignment__pb2.CancelDemandMasterUploadRequest.SerializeToString,
        response_deserializer=store__management_dot_hd__assignment__pb2.CancelDemandMasterUploadResponse.FromString,
        )
    self.GetValidStoreByProductId = channel.unary_unary(
        '/store_management.StoreManagementHdAssignment/GetValidStoreByProductId',
        request_serializer=store__management_dot_hd__assignment__pb2.GetValidStoreByProductIdRequest.SerializeToString,
        response_deserializer=store__management_dot_hd__assignment__pb2.GetValidStoreByProductIdResponse.FromString,
        )
    self.ListDemandMaster = channel.unary_unary(
        '/store_management.StoreManagementHdAssignment/ListDemandMaster',
        request_serializer=store__management_dot_hd__assignment__pb2.QueryDemandMasterRequest.SerializeToString,
        response_deserializer=store__management_dot_hd__assignment__pb2.QueryDemandMasterResponse.FromString,
        )
    self.GetDemandMasterDetail = channel.unary_unary(
        '/store_management.StoreManagementHdAssignment/GetDemandMasterDetail',
        request_serializer=store__management_dot_hd__assignment__pb2.IdRequest.SerializeToString,
        response_deserializer=store__management_dot_hd__assignment__pb2.DemandMasterEntity.FromString,
        )
    self.UpdateDemandMasterInfo = channel.unary_unary(
        '/store_management.StoreManagementHdAssignment/UpdateDemandMasterInfo',
        request_serializer=store__management_dot_hd__assignment__pb2.UpdateDemandMasterInfoRequest.SerializeToString,
        response_deserializer=store__management_dot_hd__assignment__pb2.CommonResponse.FromString,
        )
    self.GetDemandMasterProductDetail = channel.unary_unary(
        '/store_management.StoreManagementHdAssignment/GetDemandMasterProductDetail',
        request_serializer=store__management_dot_hd__assignment__pb2.IdRequest.SerializeToString,
        response_deserializer=store__management_dot_hd__assignment__pb2.QueryDemandMasterProductResponse.FromString,
        )
    self.UpdateDemandMasterProduct = channel.unary_unary(
        '/store_management.StoreManagementHdAssignment/UpdateDemandMasterProduct',
        request_serializer=store__management_dot_hd__assignment__pb2.UpdateDemandMasterProductRequest.SerializeToString,
        response_deserializer=store__management_dot_hd__assignment__pb2.UpdateDemandMasterProductRequest.FromString,
        )
    self.DeleteDemandMasterProduct = channel.unary_unary(
        '/store_management.StoreManagementHdAssignment/DeleteDemandMasterProduct',
        request_serializer=store__management_dot_hd__assignment__pb2.DeleteDemandMasterProductRequest.SerializeToString,
        response_deserializer=store__management_dot_hd__assignment__pb2.CommonResponse.FromString,
        )
    self.DealDemandMasterById = channel.unary_unary(
        '/store_management.StoreManagementHdAssignment/DealDemandMasterById',
        request_serializer=store__management_dot_hd__assignment__pb2.DealDemandMasterByIdRequest.SerializeToString,
        response_deserializer=store__management_dot_hd__assignment__pb2.CommonResponse.FromString,
        )


class StoreManagementHdAssignmentServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def CreateProductMain(self, request, context):
    """创建主配单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UploadDemandMaster(self, request, context):
    """导入主配单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDemandMasterUploadByBatchId(self, request, context):
    """查询导入明细
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDemandMasterUpload(self, request, context):
    """查询导入记录
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ApproveDemandMasterUpload(self, request, context):
    """审核主配导入
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CancelDemandMasterUpload(self, request, context):
    """取消主配导入
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetValidStoreByProductId(self, request, context):
    """根据商品id获取可订货门店(此接口会很慢)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListDemandMaster(self, request, context):
    """查询门店主配单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDemandMasterDetail(self, request, context):
    """获取订货单详细
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateDemandMasterInfo(self, request, context):
    """更新订货单信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDemandMasterProductDetail(self, request, context):
    """获取订货商品信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateDemandMasterProduct(self, request, context):
    """更新主配单商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteDemandMasterProduct(self, request, context):
    """删除主配单订货商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DealDemandMasterById(self, request, context):
    """修改订单状态统一入口
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_StoreManagementHdAssignmentServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateProductMain': grpc.unary_unary_rpc_method_handler(
          servicer.CreateProductMain,
          request_deserializer=store__management_dot_hd__assignment__pb2.ProductMainRequest.FromString,
          response_serializer=store__management_dot_hd__assignment__pb2.IdRequest.SerializeToString,
      ),
      'UploadDemandMaster': grpc.unary_unary_rpc_method_handler(
          servicer.UploadDemandMaster,
          request_deserializer=store__management_dot_hd__assignment__pb2.UploadDemandMasterRequest.FromString,
          response_serializer=store__management_dot_hd__assignment__pb2.UploadDemandMasterResponse.SerializeToString,
      ),
      'GetDemandMasterUploadByBatchId': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandMasterUploadByBatchId,
          request_deserializer=store__management_dot_hd__assignment__pb2.GetDemandMasterUploadByBatchIdRequest.FromString,
          response_serializer=store__management_dot_hd__assignment__pb2.GetDemandMasterUploadByBatchIdResponse.SerializeToString,
      ),
      'GetDemandMasterUpload': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandMasterUpload,
          request_deserializer=store__management_dot_hd__assignment__pb2.GetDemandMasterUploadRequest.FromString,
          response_serializer=store__management_dot_hd__assignment__pb2.GetDemandMasterUploadResponse.SerializeToString,
      ),
      'ApproveDemandMasterUpload': grpc.unary_unary_rpc_method_handler(
          servicer.ApproveDemandMasterUpload,
          request_deserializer=store__management_dot_hd__assignment__pb2.ApproveDemandMasterUploadRequest.FromString,
          response_serializer=store__management_dot_hd__assignment__pb2.ApproveDemandMasterUploadResponse.SerializeToString,
      ),
      'CancelDemandMasterUpload': grpc.unary_unary_rpc_method_handler(
          servicer.CancelDemandMasterUpload,
          request_deserializer=store__management_dot_hd__assignment__pb2.CancelDemandMasterUploadRequest.FromString,
          response_serializer=store__management_dot_hd__assignment__pb2.CancelDemandMasterUploadResponse.SerializeToString,
      ),
      'GetValidStoreByProductId': grpc.unary_unary_rpc_method_handler(
          servicer.GetValidStoreByProductId,
          request_deserializer=store__management_dot_hd__assignment__pb2.GetValidStoreByProductIdRequest.FromString,
          response_serializer=store__management_dot_hd__assignment__pb2.GetValidStoreByProductIdResponse.SerializeToString,
      ),
      'ListDemandMaster': grpc.unary_unary_rpc_method_handler(
          servicer.ListDemandMaster,
          request_deserializer=store__management_dot_hd__assignment__pb2.QueryDemandMasterRequest.FromString,
          response_serializer=store__management_dot_hd__assignment__pb2.QueryDemandMasterResponse.SerializeToString,
      ),
      'GetDemandMasterDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandMasterDetail,
          request_deserializer=store__management_dot_hd__assignment__pb2.IdRequest.FromString,
          response_serializer=store__management_dot_hd__assignment__pb2.DemandMasterEntity.SerializeToString,
      ),
      'UpdateDemandMasterInfo': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateDemandMasterInfo,
          request_deserializer=store__management_dot_hd__assignment__pb2.UpdateDemandMasterInfoRequest.FromString,
          response_serializer=store__management_dot_hd__assignment__pb2.CommonResponse.SerializeToString,
      ),
      'GetDemandMasterProductDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandMasterProductDetail,
          request_deserializer=store__management_dot_hd__assignment__pb2.IdRequest.FromString,
          response_serializer=store__management_dot_hd__assignment__pb2.QueryDemandMasterProductResponse.SerializeToString,
      ),
      'UpdateDemandMasterProduct': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateDemandMasterProduct,
          request_deserializer=store__management_dot_hd__assignment__pb2.UpdateDemandMasterProductRequest.FromString,
          response_serializer=store__management_dot_hd__assignment__pb2.UpdateDemandMasterProductRequest.SerializeToString,
      ),
      'DeleteDemandMasterProduct': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteDemandMasterProduct,
          request_deserializer=store__management_dot_hd__assignment__pb2.DeleteDemandMasterProductRequest.FromString,
          response_serializer=store__management_dot_hd__assignment__pb2.CommonResponse.SerializeToString,
      ),
      'DealDemandMasterById': grpc.unary_unary_rpc_method_handler(
          servicer.DealDemandMasterById,
          request_deserializer=store__management_dot_hd__assignment__pb2.DealDemandMasterByIdRequest.FromString,
          response_serializer=store__management_dot_hd__assignment__pb2.CommonResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'store_management.StoreManagementHdAssignment', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
