syntax = "proto3";

package store_management;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

service StoreInventoryAdjustService {

    // 根据门店id查询可调整订货商品
    rpc GetDemandAdjustProductByStoreId (GetDemandAdjustProductByStoreIdRequest) returns (GetDemandAdjustProductByStoreIdResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/store/inventory_adjust/valid_product/{store_id}"
        };
    }

    // 创建订货调整单
    rpc CreateDemandAdjust (CreateDemandAdjustRequest) returns (Response) {
        option (google.api.http) = {
        post: "/api/v2/supply/store/inventory_adjust"
        body: "*"
        };
    }

    // 修改订单状态统一入口
    rpc DealDemandAdjustById (DealDemandAdjustByIdRequest) returns (Response) {
        option (google.api.http) = {
        put: "/api/v2/supply/store/inventory_adjust/{id}/{action}"
        body: "*"
        };
    };

    // 删除库存调整单
    rpc DeleteDemandAdjust (IdReq) returns (Response) {
        option (google.api.http) = {
        put: "/api/v2/supply/store/inventory_adjust/delete/{id}"
        };
    }

    // 更新库存调整单商品
    rpc UpdateDemandAdjustProduct (UpdateDemandAdjustProductRequest) returns (UpdateDemandAdjustProductRequest) {
        option (google.api.http) = {
        put: "/api/v2/supply/store/inventory_adjust/product"
        body: "*"
        };
    }

    // 删除库存调整单订货商品
    rpc DeleteDemandAdjustProduct (DeleteDemandAdjustProductRequest) returns (Response) {
        option (google.api.http) = {
        put: "/api/v2/supply/store/inventory_adjust/{demand_id}/product/delete"
        body: "*"
        };
    }

    // 更新库存调整单信息
    rpc UpdateDemandAdjustInfo (UpdateDemandAdjustInfoRequest) returns (Response) {
        option (google.api.http) = {
        post: "/api/v2/supply/store/inventory_adjust/{id}"
        body: "*"
        };
    }

    // 枚举库存调整单
    rpc ListDemandAdjust (QueryDemandAdjustRequest) returns (QueryDemandAdjustResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/store/inventory_adjust"
        };
    }

    // 查询库存调整单
    rpc GetDemandAdjustDetail (IdReq) returns (DemandAdjustEntity) {
        option (google.api.http) = {
        get: "/api/v2/supply/store/inventory_adjust/{id}"
        };
    };

    // 获取库存调整单商品信息
    rpc GetDemandAdjustProductDetail (IdReq) returns (QueryDemandAdjustProductResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/store/inventory_adjust/{id}/product"
        };
    }

    // 创建退货单
    rpc CreateReturn (CreateReturnRequest) returns (CreateReturnResponse) {
        option (google.api.http) = {
            post:"/api/v2/supply/store_mgmt/returns"
            body:"*"
        };
    }

    // 查询可退货商品
    rpc GetValidProduct (GetValidProductRequest) returns (ValidProduct) {
        option (google.api.http) = {
            get:"/api/v2/supply/store_mgmt/returns/valid_product"
        };
    }

    // 查询退货单
    rpc ListReturn (ListReturnRequest) returns (ListReturnResponse) {
        option (google.api.http) = {
            get:"/api/v2/supply/store_mgmt/returns"
        };
    }

    // 根据id查询退货单详情
    rpc GetReturnById (GetReturnByIdRequest) returns (Returns) {
        option (google.api.http) = {
            get:"/api/v2/supply/store_mgmt/returns/{id}"
        };
    }

    // 根据id查询退货单商品详情
    // 查询退货单详情时需调用
    rpc GetReturnProductById (GetReturnProductByIdRequest) returns (GetReturnProductByIdResponse) {
        option (google.api.http) = {
            get:"/api/v2/supply/store_mgmt/returns/{id}/product"
        };
    }

    // 提交退货单
    rpc SubmitReturn (SubmitReturnRequest) returns (ReturnCommonResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/store_mgmt/returns/{id}/submit"
            body:"*"
        };
    }

    // 驳回退货单
    rpc RejectReturn (RejectReturnRequest) returns (ReturnCommonResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/store_mgmt/returns/{id}/reject"
            body:"*"
        };
    }

    // 审核退货单
    rpc ApproveReturn (ApproveReturnRequest) returns (ReturnCommonResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/store_mgmt/returns/{id}/approve"
            body:"*"
        };
    }

    // 确认退货
    rpc ConfirmReturn (ConfirmReturnRequest) returns (ReturnCommonResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/store_mgmt/returns/{id}/confirm"
            body:"*"
        };
    }

    // 确认提货
    rpc DeliveryReturn (DeliveryReturnRequest) returns (ReturnCommonResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/store_mgmt/returns/{id}/delivery"
            body:"*"
        };
    }

    // 更新退货单
    rpc UpdateReturn (UpdateReturnRequest) returns (ReturnCommonResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/store_mgmt/returns/{id}/update"
            body:"*"
        };
    }

    // 删除新建的退货单
    rpc DeleteReturn (DeleteReturnRequest) returns (ReturnCommonResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/store_mgmt/returns/{id}/delete"
            body:"*"
        };
    }

    // 校验按收货原单退货是否超退
    rpc CheckReturnAvailableByrec (CheckReturnAvailableRequest) returns (ReturnCommonResponse) {
        option (google.api.http) = {
            post:"/api/v2/supply/store_mgmt/returns/check"
            body:"*"
        };
    }


}

// 查询可订货商品请求参数
message GetDemandAdjustProductByStoreIdRequest {
    // 门店id
    uint64 store_id = 1;
    // 订货类型:门市订货(SD)、紧急订货(HD)、主配订货(MD)
    string type = 2;
    // 订货日期
    google.protobuf.Timestamp order_date = 3;
    // 物流模式: 配送-NMD, 直送-PUR
    string distribution_type = 4;
    // 配送中心
    uint64 vendor_id = 5;
    repeated uint64 category_ids = 6;
    string lan = 7;
}

message DemandAdjustProductUnit {
    // 订货单位id
    uint64 unit_id = 1;
    // 订货单位名称
    string unit_name = 2;
    // 订货code
    string unit_code = 3;
    // 比率
    double unit_rate = 4;
}

message DemandAdjustProduct {
    // 到货天数
    int32 arrival_days = 1;
    // 配送/供应商中心
    uint64 distribute_by = 2;
    // 配送(NMD)、采购(PUR)
    string distribution_type = 3;
    // 递增订量
    float increment_quantity = 4;
    // 最大订货量
    float max_quantity = 5;
    // 最小订货量
    float min_quantity = 6;
    // 商品类别id
    uint64 product_category_id = 7;
    // 商品编码
    string product_code = 8;
    // 商品id
    uint64 product_id = 9;
    // 商品名称
    string product_name = 10;
    // 订货数量
    double quantity = 11;
    // 规格
    string spec = 12;
    // 商品储藏类型编码
    string storage_type = 13;
    //多单位调整
    repeated DemandAdjustProductUnit units = 14;
    // 销售类型
    string sale_type = 17;
    // 商品属性
    string product_type = 18;
    // 建议订货量
    float suggest_quantity = 20;
    // 成品订货量
    float finished_quantity = 21;
    // 热麦订货量
    float bread_quantity = 22;
    // 茶饮订货量
    float tea_quantity = 23;
    // 原料订货量
    float raw_quantity = 24;
    // 直采价格
    float purchase_price = 25;
    // 直采税率
    float purchase_tax = 26;
    // 订货记录id(如果有此id则要传此id)
    uint64 id = 27;
    // 配送周期类型
    string distribution_circle = 28;
    // 配送中心
    uint64 distribution_center_id = 29;
    // 供应商
    uint64 vendor_id = 30;
    //
    string cycle_extends = 31;
    string price = 32;
    string image = 33;
    string currency = 34;
}

message GetDemandAdjustProductByStoreIdResponse {
    repeated DemandAdjustProduct rows = 1;
    uint64 total = 2;
}

message CreateDemandAdjustRequest {
    // 订货日期
    google.protobuf.Timestamp demand_date = 1;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 2;
    // 备注
    string remark = 3;
    // 库存调整单-AD
    string type = 4;
    // 主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT)(主配单才有的字段)
    string sub_type = 5;
    // 门店id
    uint64 store_id = 6;
    message Products {
        // 门店id或者商品id
        uint64 product_id = 1;
        // 订货数量
        double quantity = 2;
        // 商品订货物流模式(NMD:配送, PUR:直送)
        string distribution_type = 3;
        uint64 centre_id = 4;
        uint64 unit_id = 5;
    }
    // 商品
    repeated Products products = 7;
}

// 处理单据
message DealDemandAdjustByIdRequest {
    uint64 id = 1;
    // 单据业务状态(INITED,F_COMMIT(成品订货提交),COMMIT(提交), CAL_DONE(原料计算完成, 这步不能通过前端修改), CAL_FAILED(原料计算失败，这步也不能通过前端修改),REJECTED(驳回订货单),APPROVED(审核订货单通过),FREEZE(冻结订货单),CANCELLED
    string action = 2;
    string description = 3;
    // 附件
    string attachments = 4;
}

// 统一返回对象
message Response {
    string description = 1;
}

// 更新订货商品参数
message UpdateDemandAdjustProductRequest {
    message products {
        // 订货记录id(如果有此id则要传此id)
        uint64 id = 1;
        // 商品id
        uint64 product_id = 2;
        // 订货数量
        double quantity = 3;
        // 订货热麦类型("FINISHED","成品订货"),("TEA", "茶饮订货"),("BREAD", "面包订货")("RAW", "原料订货")
        string tag_type = 4;
        // 商品订货物流模式(配送:NMD, 直送:PUR)， 成品订货不用传
        string distribution_type = 5;
        // 配送/供应商中心(不修改就不需要传)
        uint64 distribution_by = 6;
        string unit_id = 7;
        double unit_rate = 8;
        // 供应商id
        uint64 vendor_id = 9;
        // 订货方式
        string order_type = 10;
    }
    // 主配单id
    uint64 demand_id = 1;
    // 更新的商品信息
    repeated products product = 2;
    // 订货日期(不传默认当天)
    google.protobuf.Timestamp order_date = 3;
    // 附件
    string attachments = 4;
}

// 删除订货商品请求
message DeleteDemandAdjustProductRequest {
    // 订货单id
    uint64 demand_id = 1;
    // 订货单订货记录id
    repeated uint64 demand_product_ids = 2;
}

// 更新订货单信息参数
message UpdateDemandAdjustInfoRequest {
    uint64 id = 1;
    // 备注
    string remark = 2;
    // 到货日期(只有主配单和紧急订货单才有的参数，门市订货单请不要传)
    google.protobuf.Timestamp arrival_date = 3;
    string attachments = 4;
}


message IdReq {
    // 对象id
    uint64 id = 1;
    string lan = 2;
}

//查询门店订货单参数
message QueryDemandAdjustRequest {
    // 门店ids（,分割）
    repeated uint64 store_ids = 1;
    // 是否包含商品('0'是不含，'1'是含, 不传查询所有, 主意这里是字符0, 1)
    string has_product = 2;
    // 门市订货(SD)、紧急订货(HD)、主配订货(MD)
    string type = 3;
    // 主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT, 总部帮订:MASTER)(主配单才有的字段)
    string sub_type = 4;
    // 门店类型(热麦:MIX, 如果要查询非的字段使用neq_开头，例如:["neq_MIX", "TEA"], 不传查询所有)
    repeated string store_type = 5;
    // 订货日期起始时间
    google.protobuf.Timestamp start_date = 6;
    // 订货日期结束时间
    google.protobuf.Timestamp end_date = 7;
    // 订单状态(INITED,F_COMMIT, CAL_DONE, CAL_FAILED, COMMIT, REJECTED, APPROVED, FREEZE, CANCELLED)
    repeated string status = 8;
    // 偏移(默认0)
    uint64 offset = 10;
    // 查询的每页数量
    uint64 limit = 11;
    // 订货单号
    repeated string codes = 12;
    bool is_plan = 13;
    bool is_adjust = 14;
    string send_type = 15;
    // 排序(默认asc)
    string order = 16;
    string sort = 17;
    string havi_code = 18;
    string pre_havi_code = 19;
    string bus_type = 20;
    // 计划名称模糊查询
    string plan_name = 21;
    string lan = 22;
    repeated string types = 23;
}

// 查询门店订货单
message QueryDemandAdjustResponse {
    repeated DemandAdjustEntity rows = 1;
    uint64 total = 2;
}

// 获取订货商品返回参数
message QueryDemandAdjustProductResponse {
    repeated QueryDemandAdjustProductEntity rows = 1;
    int32 total = 2;
}

// 可订货商品实体
message QueryDemandAdjustProductEntity {
    // 到货天数
    int32 arrival_days = 1;
    // 配送/供应商中心
    uint64 distribute_by = 2;
    // 配送(NMD)、采购(PUR)
    string distribution_type = 3;
    // 递增订量
    float increment_quantity = 4;
    // 最大订货量
    float max_quantity = 5;
    // 最小订货量
    float min_quantity = 6;
    // 商品类别id
    uint64 product_category_id = 7;
    // 商品编码
    string product_code = 8;
    // 商品id
    uint64 product_id = 9;
    // 商品名称
    string product_name = 10;
    // 订货数量
    double quantity = 11;
    // 规格
    string spec = 12;
    // 商品储藏类型编码
    string storage_type = 13;
    // 订货单位id
    uint64 unit_id = 14;
    // 订货单位名称
    string unit_name = 15;
    // 订货规格
    string unit_spec = 16;
    // 销售类型
    string sale_type = 17;
    // 商品属性
    string product_type = 18;
    // 建议订货量
    float suggest_quantity = 20;
    // 成品订货量
    float finished_quantity = 21;
    // 热麦订货量
    float bread_quantity = 22;
    // 茶饮订货量
    float tea_quantity = 23;
    // 原料订货量
    float raw_quantity = 24;
    // 直采价格
    float purchase_price = 25;
    // 直采税率
    float purchase_tax = 26;
    // 订货记录id(如果有此id则要传此id)
    uint64 id = 27;
    // 配送周期类型
    string distribution_circle = 28;
    // 配送中心
    uint64 distribution_center_id = 29;
    // 供应商
    uint64 vendor_id = 30;
    //
    string cycle_extends = 31;
    //
    string is_first_delivery = 32;
    // 昨日销售
    double yesterday_sales = 33;
    // 一周平均销量
    double average_week_sales = 34;
    // 昨日订货量
    double yesterday_order_qty = 35;
    // 单位转换率
    double unit_rate = 36;
    // 核算订货单位id
    uint64 accounting_unit_id = 37;
    // 核算订货单位名称
    string accounting_unit_name = 38;
    // 核算订货规格
    string accounting_unit_spec = 39;
    // 核算订货数量
    double accounting_quantity = 40;
    bool has_new_suggest_qty = 41;
    repeated string barcode = 42;
}


message DemandAdjustEntity {
    // 单据id
    uint64 id = 1;
    // 生成的批次id
    uint64 batch_id = 2;
    // 编码
    string code = 3;
    // 门市订货(SD)、紧急订货(HD)、主配订货(MD)
    string type = 4;
    // 原因类型
    string reason_type = 5;
    // 收获门店id
    uint64 receive_by = 6;
    // 收获门店名称
    string receive_name = 7;
    // 供应商id
    uint64 distribute_by = 8;
    // 门店编码
    string store_secondary_id = 9;
    // 门店类型
    string store_type = 10;
    // 供应商类别
    string distribution_type = 11;
    // 订货日期
    google.protobuf.Timestamp demand_date = 12;
    // 审核人id
    uint64 review_by = 13;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 14;
    // 描述
    string description = 15;
    // 商户id
    uint64 partner_id = 16;
    // 主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT, 总部帮订:MASTER)(主配单才有的字段)
    string sub_type = 17;
    // 备注
    string remark = 18;
    // 是否有商品
    int32 has_product = 19;
    // 是否是系统操作(1是，0不是)
    int32 is_sys = 20;
    // 单据业务状态(INITED,F_COMMIT, CAL_DONE, CAL_FAILED, COMMIT, REJECTED, APPROVED, FREEZE, CANCELLED)
    string status = 21;
    // 生成要货单处理状态(INITED,PROCESSING,SUCCESS,FAILED)
    string process_status = 22;
    // 更新时间
    google.protobuf.Timestamp updated_at = 23;
    // 更新人id
    uint64 updated_by = 24;
    string created_name = 25;
    string updated_name = 26;
    // 创建日期
    google.protobuf.Timestamp created_at = 27;
    // 创建人id
    uint64 created_by = 28;
    // 订货品相
    int32 product_count = 29;
    bool is_plan = 30;
    bool is_adjust = 31;
    string send_type = 32;
    string havi_code = 33;
    string pre_havi_code = 34;
    string bus_type = 35;
    string attachments = 36;
    // 计划相关
    string plan_method = 37;
    string plan_name = 38;
    string amount  = 39;
    string currency  = 40;
}

message Returns {
    // id
    uint64 id = 1;
    // 退货单单号
    string code = 2;
    // 退货门店
    uint64 return_by = 3;
    // 退货编号
    string return_number = 4;
    // 退货单状态
    string status = 5;
    // 审核人
    uint64 review_by = 6;
    // 退货提货单单单号
    string return_delivery_number = 8;
    // 退货日期
    google.protobuf.Timestamp return_date = 9;
    // 预计退货提货时间
    google.protobuf.Timestamp return_delivery_date = 10;
    // 退货原因
    string return_reason = 11;
    // 退货类型: 原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD
    string type = 12;
    // 退货方类型： store/ warehouse/ machining
    string sub_type = 13;
    // 备注
    string remark = 14;
    // 预留门店号
    string store_secondary_id = 15;
    // 创建时间
    google.protobuf.Timestamp created_at = 16;
    // 更新时间
    google.protobuf.Timestamp updated_at = 17;
    // 创建人id
    uint64 created_by = 18;
    // 更新人id
    uint64 updated_by = 19;
    // 退货接受方 门店id
    uint64 return_to = 20;
    // // 库存引擎调用
    // enum inventoryStatus {
    //     SENT = 0; // 已发送
    //     SENT_SUC = 2; //调用成功
    //     SENT_FAIL = 3; //调用失败
    // }
    string inventory_status = 21;
    // 库存id预留字段
    uint64 inventory_req_id = 22;
    // 商户id
    uint64 partner_id = 23;
    // 驳回原因
    string reject_reason = 25;
    // 附件
    repeated string attachments = 26;
    // 创建人
    string created_name = 27;
    // 更新人
    string updated_name = 28;
    // 商品数量
    uint64 product_nums = 29;
    // 单据物流来源类型:PUR 直送/ NMD 配送
    string logistics_type = 31;
    // 提货方式
    string trans_type = 35;
    // 来源
    uint64 source_id = 37;
    string source_code = 38;
    // 实际提货日期
    google.protobuf.Timestamp delivery_date = 39;
    uint64 request_id = 40;
    // 接收方名字
    string return_to_code = 41;
    string return_to_name = 42;
    // 配送方名字
    string return_by_code = 43;
    string return_by_name = 44;
    uint64 franchisee_id = 55;
    string franchisee_code = 56;
    string franchisee_name = 57;
    string payment_way = 58;
    string delivery_code = 59;
    string receive_code = 60;
    string warehouse_type = 61;
    // 是否长效
    string long_effect = 62;
    string amount  = 63;
    string currency  = 64;
    string currencyuuuu  = 65;
}

message ValidProduct {
    // id
    uint64 id = 1;
    // 商品编号
    string product_code = 2;
    // 商品名称
    string product_name = 3;
    // 规格
    string spec = 4;
    // 单位
    Unit unit = 5;
}

message Unit {
    // id
    uint64 id = 1;
    // 单位数量
    int32 quantity = 2;
    //
    double rate = 3;
}

message ProductDetail {
    // id
    uint64 id = 1;
    // 退货单id
    uint64 return_id = 2;
    // 退货门店
    uint64 return_by = 3;

    // 商品编码
    string product_code = 5;
    // 商品id
    uint64 product_id = 6;
    // 商品名称
    string product_name = 7;
    // 数量
    double quantity = 8;

    // 单位id
    uint64 accounting_unit_id = 9;
    // 单位名称
    string accounting_unit_name = 10;
    // 单位规格
    string accounting_unit_spec = 11;
    // 单位id
    uint64 unit_id = 12;
    // 单位名称
    string unit_name = 13;
    // 单位换算比例
    double unit_rate = 14;
    // 单位规格
    string unit_spec = 15;
    // 确认退货数量
    double accounting_confirmed_quantity = 16;
    //
    double accounting_quantity = 17;
    //
    double accounting_returned_quantity = 18;
    // 确认收货数量
    double confirmed_quantity = 19;
    // 退货数量
    double returned_quantity = 20;

    // 状态
    bool is_confirmed = 21;
    // 退货日期
    google.protobuf.Timestamp return_date = 22;
    // 接收方
    uint64 return_to = 23;
    // 订货日期
    // google.protobuf.Timestamp demand_date = 23;

    // 库存引擎调用
    // enum inventoryStatus {
    //     SENT = 1; // 已发送
    //     SENT_SUC = 2; //调用成功
    //     SENT_FAIL = 3; //调用失败
    // }
    string inventory_status = 24;
    // 库存id预留字段
    uint64 inventory_req_id = 25;
    // 创建时间
    google.protobuf.Timestamp created_at = 26;
    // 创建人id
    uint64 created_by = 27;
    // 更新日期
    google.protobuf.Timestamp updated_at = 28;
    // 更新人id
    uint64 updated_by = 29;
    // 商户id
    uint64 partner_id = 30;
    // 创建人
    string created_name = 31;
    // 更新人
    string updated_name = 32;
    string storage_type = 35;
    // 未税单价
    double price = 36;
    // 税率
    float tax_rate = 37;
    // 含税单价
    double price_tax = 38;
    // 采购总价
    double sum_price = 39;
    // 附件
    repeated string attachments = 45;
}

message ReturnProduct {
    // 退货单商品主键id
    uint64 id = 1;
    // 商品主档id
    uint64 product_id = 2;
    // 数量
    double quantity = 3;
    // 收货数量
    double confirmed_quantity = 4;
    //
    string product_code = 5;
    string product_name = 6;
    // 订货单位id
    uint64 unit_id = 7;
    string unit_name = 8;
    string unit_spec = 9;
    // 单位换算比例
    float unit_rate = 10;
    // 税相关
    float tax_rate = 11;
    float price = 12;
    float price_tax = 13;
    // 退货接收方 - vendor_id/warehouse_id
    uint64 return_to = 14;
    string logistics_type = 15;
}

message CreateReturnRequest {
    // 退货方id
    uint64 return_by = 1;
    // 单据物流来源类型:PUR 直送/ NMD 配送
    string logistics_type = 2;
    // 退货类型: 原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD
    string type = 3;
    // 退货方类型： store/ warehouse/ machining
    string sub_type = 4;
    // 交货日期
    google.protobuf.Timestamp return_delivery_date = 5;
    // 退货单来源id
    uint64 source_id = 6;
    string source_code = 7;
    // 退货原因
    string return_reason = 8;
    // 备注
    string remark = 9;
    // 附件
    repeated string attachments = 10;
    // 唯一请求号，保证不重复请求
    uint64 request_id = 11;
    // 商品
    repeated ReturnProduct products = 12;
    // 语言
    string lan = 20;
}

message CreateReturnResponse {
    repeated uint64 return_id = 1;
    bool payload = 2;
}

message CheckReturnAvailableRequest {
    // 退货方id
    uint64 return_by = 1;
    // 交货日期
    google.protobuf.Timestamp return_delivery_date = 2;
    // 退货类型: 原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD
    string type = 3;
    // 退货方类型： store/ warehouse/ machining
    string sub_type = 4;
    // 退货原因
    string return_reason = 5;
    // 备注
    string remark = 6;
    // 商品
    repeated ReturnProduct products = 7;
    // 退货接收方 - vendor_id/warehouse_id
    uint64 return_to = 9;
    // 附件
    repeated string attachments = 10;
    // 单据物流来源类型:PUR 直送/ NMD 配送
    string logistics_type = 11;
    // 退货单来源id
    uint64 source_id = 12;
    string source_code = 13;
    // 退货单id
    uint64 return_id = 14;
    string lan = 15;
}

message GetValidProductRequest {
    // id
    uint64 id = 1;
    // 分页大小
    int32 limit = 2;
    // 跳过行数
    int32 offset = 3;
    // 返回总条数
    bool include_total = 4;
    // 商品名称
    string product_name = 5;
    string lan = 6;

}

message GetValidProductResponse {
    bool payload = 1;
}

message ListReturnRequest {
    // 退货方id
    repeated uint64 return_by = 1;
    // 订单状态
    repeated string status = 2;
    // 退货单号
    string code = 3;
    // 来源id
    uint64 source_id = 4;
    string source_code = 5;
    // 单据物流来源类型:PUR 直送/ NMD 配送
    string logistics_type = 6;
    // 退货类型: 原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD
    string type = 7;
    // 退货方类型： store/ warehouse/ machining
    string sub_type = 8;
    // 退货开始日期
    google.protobuf.Timestamp return_date_from = 10;
    // 退货结束日期
    google.protobuf.Timestamp return_date_to = 11;
    // 提货开始日期
    google.protobuf.Timestamp delivery_date_from = 12;
    // 提货结束日期
    google.protobuf.Timestamp delivery_date_to = 13;
    // 分页大小
    int32 limit = 20;
    // 跳过行数
    int32 offset = 21;
    // 排序
    string order = 22;
    // 排序字段
    string sort = 23;

    string lan = 24;
    // 退货方接收方
    repeated uint64 return_to = 25;

}

message ListReturnResponse {
    repeated Returns rows = 1;
    uint64 total = 2;
}

message GetReturnByIdRequest {
    // 退货单id
    uint64 id = 1;
    string lan = 2;
}


message GetReturnProductByIdRequest {
    // id
    uint64 id = 1;
    // 分页大小
    int32 limit = 2;
    // 跳过行数
    int32 offset = 3;
    // 排序字段
    string sort = 4;
    // 排序顺序
    string order = 5;
    string lan = 6;

}

message GetReturnProductByIdResponse {
    repeated ProductDetail rows = 1;
    uint64 total = 2;
}

message SubmitReturnRequest {
    // id
    uint64 id = 1;
    string lan = 2;
}


message RejectReturnRequest {
    // id
    uint64 id = 1;
    // 驳回原因
    string reject_reason = 2;
    string lan = 3;
}


message ApproveReturnRequest {
    // id
    uint64 id = 1;
    // 提货方式
    string trans_type = 2;
    string lan = 3;
}


message ConfirmReturnRequest {
    // id
    uint64 id = 1;
    string lan = 2;
}


message DeliveryReturnRequest {
    uint64 id = 1;
    string lan = 2;
}


message UpdateReturnRequest {
    //
    uint64 id = 1;
    //
    repeated ReturnProduct products = 2;
    // 退货原因
    string return_reason = 3;
    // 备注
    string remark = 4;
    // 退货交货时间
    google.protobuf.Timestamp return_delivery_date = 5;
    // 供应商/配送中心
    uint64 return_to = 6;
    // 附件
    repeated string attachments = 7;
    string lan = 8;
    // 单据物流来源类型:PUR 直送/ NMD 配送
    string logistics_type = 9;
}


message DeleteReturnRequest {
    //
    uint64 id = 1;
    string lan = 2;
}


message ReturnCommonResponse {
    bool payload = 1;
    // wrapper.Status status = 2;
}
