syntax = "proto3";

package store_management;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

service StoreManagementHdAssignment {

    // 创建主配单
    rpc CreateProductMain (ProductMainRequest) returns (IdRequest) {
        option (google.api.http) = {
        post: "/api/v2/supply/store/hd-assignment"
        body: "*"
        };
    }

    // 导入主配单
    rpc UploadDemandMaster (UploadDemandMasterRequest) returns (UploadDemandMasterResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/store/hd-assignment/upload/batch"
        body: "*"
        };
    };

    // 查询导入明细
    rpc GetDemandMasterUploadByBatchId (GetDemandMasterUploadByBatchIdRequest) returns (GetDemandMasterUploadByBatchIdResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/store/hd-assignment/upload/batch/{batch_id}"
        };
    }

    // 查询导入记录
    rpc GetDemandMasterUpload (GetDemandMasterUploadRequest) returns (GetDemandMasterUploadResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/store/hd-assignment/upload/batch/log"
        };
    }

    // 审核主配导入
    rpc ApproveDemandMasterUpload (ApproveDemandMasterUploadRequest) returns (ApproveDemandMasterUploadResponse) {
        option (google.api.http) = {
        put: "/api/v2/supply/store/hd-assignment/upload/batch/{batch_id}/approve"
        };
    }

    // 取消主配导入
    rpc CancelDemandMasterUpload (CancelDemandMasterUploadRequest) returns (CancelDemandMasterUploadResponse) {
        option (google.api.http) = {
        put: "/api/v2/supply/store/hd-assignment/upload/batch/{batch_id}/cancel"
        };
    }

    // 根据商品id获取可订货门店(此接口会很慢)
    rpc GetValidStoreByProductId (GetValidStoreByProductIdRequest) returns (GetValidStoreByProductIdResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/store/hd-assignment/valid/store"
        body: "*"
        };
    }

    // 查询门店主配单
    rpc ListDemandMaster (QueryDemandMasterRequest) returns (QueryDemandMasterResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/store/hd-assignment"
        };
    }

    // 获取订货单详细
    rpc GetDemandMasterDetail (IdRequest) returns (DemandMasterEntity) {
        option (google.api.http) = {
        get: "/api/v2/supply/store/hd-assignment/{id}"
        };
    };

    // 更新订货单信息
    rpc UpdateDemandMasterInfo (UpdateDemandMasterInfoRequest) returns (CommonResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/store/hd-assignment/{id}"
        body: "*"
        };
    }

    // 获取订货商品信息
    rpc GetDemandMasterProductDetail (IdRequest) returns (QueryDemandMasterProductResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/store/hd-assignment/{id}/product"
        };
    }


    // 更新主配单商品
    rpc UpdateDemandMasterProduct (UpdateDemandMasterProductRequest) returns (UpdateDemandMasterProductRequest) {
        option (google.api.http) = {
        put: "/api/v2/supply/store/hd-assignment/product"
        body: "*"
        };
    }

    // 删除主配单订货商品
    rpc DeleteDemandMasterProduct (DeleteDemandMasterProductRequest) returns (CommonResponse) {
        option (google.api.http) = {
        put: "/api/v2/supply/store/hd-assignment/{demand_id}/product/delete"
        body: "*"
        };
    }

    // 修改订单状态统一入口
    rpc DealDemandMasterById (DealDemandMasterByIdRequest) returns (CommonResponse) {
        option (google.api.http) = {
        put: "/api/v2/supply/store/hd-assignment/{id}/{action}"
        body: "*"
        };
    };

}

// 统一返回对象
message CommonResponse {
    string description = 1;
}

// 根据商品id获得可订货门店请求参数
message GetValidStoreByProductIdRequest {
    // 商品id
    repeated uint64 product_ids = 1;
    // 订单类型:MD(只有MD)
    string type = 2;
    // 订货日期
    google.protobuf.Timestamp order_date = 3;
    // 区域id
    repeated uint64 branch_ids = 4;
    // 分类id
    repeated uint64 category_ids = 5;
    string lan = 6;
}

// 根据商品id获得可订货门店返回参数
message GetValidStoreByProductIdResponse {
    message ValidStore {
        // 门店id
        uint64 store_id = 1;
        // 门店code
        string store_code = 2;
        // 门店名称
        string store_name = 3;
        // 商品信息
        repeated StoreProduct store_products = 4;
    }

    message StoreProduct {
        // 到货天数
        int32 arrival_days = 1;
        // 配送/供应商中心
        uint64 distribute_by = 2;
        // 配送(NMD)、采购(PUR)
        string distribution_type = 3;
        // 递增订量
        float increment_quantity = 4;
        // 最大订货量
        float max_quantity = 5;
        // 最小订货量
        float min_quantity = 6;
        // 订货单位id
        uint64 unit_id = 7;
        // 订货单位名称
        string unit_name = 8;
        // 订货规格
        string unit_spec = 9;
        // 直采价格
        float purchase_price = 10;
        // 直采税率
        float purchase_tax = 11;
        // 商品id
        uint64 product_id = 12;
        // 商品编码
        string product_code = 13;
        // 商品名称
        string product_name = 14;
    }

    repeated ValidStore rows = 1;
    string description = 2;

}

// 创建商品主配单请求参数
message ProductMainRequest {
    // 订货日期
    google.protobuf.Timestamp demand_date = 1;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 2;
    // 备注
    string remark = 3;
    // 主配单:MD
    string type = 4;
    // 商品主配:PRODUCT
    string sub_type = 5;
    // 商品主配单的参数
    repeated StoreItem items = 6;

    message StoreItem {
        // 门店id
        uint64 store_id = 1;
        // 订货的详细信息
        repeated ProductItem product_items = 2;
    }
    message ProductItem {
        // 商品id
        uint64 product_id = 1;
        // 订货数量
        double quantity = 2;
        // 商品订货物流模式(NMD:配送, PUR:直送)
        string distribution_type = 3;
        // 配送中心(不修改则可以不传)
        uint64 distribution_by = 4;
        // 供应商id
        uint64 vendor_id = 5;
        // 订货方式
        string order_type = 6;
    }
    // 唯一请求id
    uint64 batch_id = 7;

}

message IdRequest {
    // 对象id
    uint64 id = 1;
    string lan = 2;
}

// 导入主配单参数
message UploadDemandMasterRequest {
    // 文件名称
    string file_name = 1;
    // 导入类型
    string file_type = 2;
    // 文件流
    string file = 3;
    string lan = 4;
}

message UploadDemandMaster {
    string store_code = 1;
    string store_name = 2;
    string product_code = 3;
    string product_name = 4;
    double quantity = 5;
    string start_date = 6;
    string end_date = 7;
    uint64 row_num = 8;
    uint64 store_id = 9;
    uint64 product_id = 10;
    uint64 id = 11;
    string status = 12;
    uint64 partner_id = 13;
    string error = 14;
    uint64 batch_id = 15;
    string store_type = 16;
    uint64 centre_id = 17;
    string centre_name = 18;
    string centre_code = 19;
    string remark = 20;
}

message UploadDemandMasterResponse {
    bool result = 1;
    repeated UploadDemandMaster rows = 2;
    string file_name = 3;
}

message GetDemandMasterUploadByBatchIdRequest {
    uint64 batch_id = 1;
    uint64 offset = 2;
    uint64 limit = 3;
    bool include_total = 4;
}

message DemandMasterUploadByBatchDetail {
    string store_code = 1;
    string store_name = 2;
    string product_code = 3;
    string product_name = 4;
    double quantity = 5;
    google.protobuf.Timestamp start_date = 6;
    google.protobuf.Timestamp end_date = 7;
    uint64 row_num = 8;
    uint64 store_id = 9;
    uint64 product_id = 10;
    uint64 id = 11;
    string status = 12;
    uint64 partner_id = 13;
    string error = 14;
    uint64 batch_id = 15;
    google.protobuf.Timestamp created_at = 16;
    google.protobuf.Timestamp updated_at = 17;
    uint64 created_by = 18;
    uint64 updated_by = 19;
    uint64 master_id = 20;
    string master_code = 21;
    uint64 centre_id = 22;
    string centre_name = 23;
    string centre_code = 24;
    string remark = 25;
}

message GetDemandMasterUploadByBatchIdResponse {
    repeated DemandMasterUploadByBatchDetail rows = 1;
    uint64 total = 2;
    string file_name = 3;
    string status = 4;
    string updated_name = 5;
}

message GetDemandMasterUploadRequest {
    google.protobuf.Timestamp start_date = 1;
    google.protobuf.Timestamp end_date = 2;
    uint64 offset = 3;
    uint64 limit = 4;
    string file_name = 5;
    bool include_total = 6;
    string file_type = 7;
}

message DemandMasterUploadSummary {
    uint64 id = 1;
    uint64 partner_id = 2;
    string status = 3;
    string file_name = 4;
    uint64 created_by = 5;
    uint64 updated_by = 6;
    google.protobuf.Timestamp created_at = 7;
    google.protobuf.Timestamp updated_at = 8;
    string created_name = 9;
    string updated_name = 10;
    google.protobuf.Timestamp upload_date = 11;
    string file_type = 12;
}

message GetDemandMasterUploadResponse {
    repeated DemandMasterUploadSummary rows = 1;
    uint64 total = 2;
}

message ApproveDemandMasterUploadRequest {
    uint64 batch_id = 1;
}

message ApproveDemandMasterUploadResponse {
    bool result = 1;
}

message CancelDemandMasterUploadRequest {
    uint64 batch_id = 1;
}

message CancelDemandMasterUploadResponse {
    bool result = 1;
}


//查询门店订货单参数
message QueryDemandMasterRequest {
    // 门店ids（,分割）
    repeated uint64 store_ids = 1;
    // 是否包含商品('0'是不含，'1'是含, 不传查询所有, 主意这里是字符0, 1)
    string has_product = 2;
    // 门市订货(SD)、紧急订货(HD)、主配订货(MD)
    string type = 3;
    // 主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT, 总部帮订:MASTER)(主配单才有的字段)
    string sub_type = 4;
    // 门店类型(热麦:MIX, 如果要查询非的字段使用neq_开头，例如:["neq_MIX", "TEA"], 不传查询所有)
    repeated string store_type = 5;
    // 订货日期起始时间
    google.protobuf.Timestamp start_date = 6;
    // 订货日期结束时间
    google.protobuf.Timestamp end_date = 7;
    // 订单状态(INITED,F_COMMIT, CAL_DONE, CAL_FAILED, COMMIT, REJECTED, APPROVED, FREEZE, CANCELLED)
    repeated string status = 8;
    // 偏移(默认0)
    uint64 offset = 10;
    // 查询的每页数量
    uint64 limit = 11;
    // 订货单号
    repeated string codes = 12;
    bool is_plan = 13;
    bool is_adjust = 14;
    string send_type = 15;
    // 排序(默认asc)
    string order = 16;
    string sort = 17;
    string havi_code = 18;
    string pre_havi_code = 19;
    string bus_type = 20;
    // 计划名称模糊查询
    string plan_name = 21;
    string lan = 22;
    repeated string types = 23;
}

// 查询门店订货单
message QueryDemandMasterResponse {
    repeated DemandMasterEntity rows = 1;
    uint64 total = 2;
}

// 获取订货商品返回参数
message QueryDemandMasterProductResponse {
    repeated QueryDemandMasterProductEntity rows = 1;
    int32 total = 2;
}

// 可订货商品实体
message QueryDemandMasterProductEntity {
    // 到货天数
    int32 arrival_days = 1;
    // 配送/供应商中心
    uint64 distribute_by = 2;
    // 配送(NMD)、采购(PUR)
    string distribution_type = 3;
    // 递增订量
    float increment_quantity = 4;
    // 最大订货量
    float max_quantity = 5;
    // 最小订货量
    float min_quantity = 6;
    // 商品类别id
    uint64 product_category_id = 7;
    // 商品编码
    string product_code = 8;
    // 商品id
    uint64 product_id = 9;
    // 商品名称
    string product_name = 10;
    // 订货数量
    double quantity = 11;
    // 规格
    string spec = 12;
    // 商品储藏类型编码
    string storage_type = 13;
    // 订货单位id
    uint64 unit_id = 14;
    // 订货单位名称
    string unit_name = 15;
    // 订货规格
    string unit_spec = 16;
    // 销售类型
    string sale_type = 17;
    // 商品属性
    string product_type = 18;
    // 建议订货量
    float suggest_quantity = 20;
    // 成品订货量
    float finished_quantity = 21;
    // 热麦订货量
    float bread_quantity = 22;
    // 茶饮订货量
    float tea_quantity = 23;
    // 原料订货量
    float raw_quantity = 24;
    // 直采价格
    float purchase_price = 25;
    // 直采税率
    float purchase_tax = 26;
    // 订货记录id(如果有此id则要传此id)
    uint64 id = 27;
    // 配送周期类型
    string distribution_circle = 28;
    // 配送中心
    uint64 distribution_center_id = 29;
    // 供应商
    uint64 vendor_id = 30;
    //
    string cycle_extends = 31;
    //
    string is_first_delivery = 32;
    // 昨日销售
    double yesterday_sales = 33;
    // 一周平均销量
    double average_week_sales = 34;
    // 昨日订货量
    double yesterday_order_qty = 35;
    // 单位转换率
    double unit_rate = 36;
    // 核算订货单位id
    uint64 accounting_unit_id = 37;
    // 核算订货单位名称
    string accounting_unit_name = 38;
    // 核算订货规格
    string accounting_unit_spec = 39;
    // 核算订货数量
    double accounting_quantity = 40;
    bool has_new_suggest_qty = 41;
    repeated string barcode = 42;
}


message DemandMasterEntity {
    // 单据id
    uint64 id = 1;
    // 生成的批次id
    uint64 batch_id = 2;
    // 编码
    string code = 3;
    // 门市订货(SD)、紧急订货(HD)、主配订货(MD)
    string type = 4;
    // 原因类型
    string reason_type = 5;
    // 收获门店id
    uint64 receive_by = 6;
    // 收获门店名称
    string receive_name = 7;
    // 供应商id
    uint64 distribute_by = 8;
    // 门店编码
    string store_secondary_id = 9;
    // 门店类型
    string store_type = 10;
    // 供应商类别
    string distribution_type = 11;
    // 订货日期
    google.protobuf.Timestamp demand_date = 12;
    // 审核人id
    uint64 review_by = 13;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 14;
    // 描述
    string description = 15;
    // 商户id
    uint64 partner_id = 16;
    // 主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT, 总部帮订:MASTER)(主配单才有的字段)
    string sub_type = 17;
    // 备注
    string remark = 18;
    // 是否有商品
    int32 has_product = 19;
    // 是否是系统操作(1是，0不是)
    int32 is_sys = 20;
    // 单据业务状态(INITED,F_COMMIT, CAL_DONE, CAL_FAILED, COMMIT, REJECTED, APPROVED, FREEZE, CANCELLED)
    string status = 21;
    // 生成要货单处理状态(INITED,PROCESSING,SUCCESS,FAILED)
    string process_status = 22;
    // 更新时间
    google.protobuf.Timestamp updated_at = 23;
    // 更新人id
    uint64 updated_by = 24;
    string created_name = 25;
    string updated_name = 26;
    // 创建日期
    google.protobuf.Timestamp created_at = 27;
    // 创建人id
    uint64 created_by = 28;
    // 订货品相
    int32 product_count = 29;
    bool is_plan = 30;
    bool is_adjust = 31;
    string send_type = 32;
    string havi_code = 33;
    string pre_havi_code = 34;
    string bus_type = 35;
    string attachments = 36;
    // 计划相关
    string plan_method = 37;
    string plan_name = 38;
}

// 更新订货商品参数
message UpdateDemandMasterProductRequest {
    message products {
        // 订货记录id(如果有此id则要传此id)
        uint64 id = 1;
        // 商品id
        uint64 product_id = 2;
        // 订货数量
        double quantity = 3;
        // 订货热麦类型("FINISHED","成品订货"),("TEA", "茶饮订货"),("BREAD", "面包订货")("RAW", "原料订货")
        string tag_type = 4;
        // 商品订货物流模式(配送:NMD, 直送:PUR)， 成品订货不用传
        string distribution_type = 5;
        // 配送/供应商中心(不修改就不需要传)
        uint64 distribution_by = 6;
        string unit_id = 7;
        double unit_rate = 8;
        // 供应商id
        uint64 vendor_id = 9;
        // 订货方式
        string order_type = 10;
    }
    // 主配单id
    uint64 demand_id = 1;
    // 更新的商品信息
    repeated products product = 2;
    // 订货日期(不传默认当天)
    google.protobuf.Timestamp order_date = 3;
    // 附件
    string attachments = 4;
}


// 删除订货商品请求
message DeleteDemandMasterProductRequest {
    // 订货单id
    uint64 demand_id = 1;
    // 订货单订货记录id
    repeated uint64 demand_product_ids = 2;
}

// 处理单据
message DealDemandMasterByIdRequest {
    uint64 id = 1;
    // 单据业务状态(INITED,F_COMMIT(成品订货提交),COMMIT(提交), CAL_DONE(原料计算完成, 这步不能通过前端修改), CAL_FAILED(原料计算失败，这步也不能通过前端修改),REJECTED(驳回订货单),APPROVED(审核订货单通过),FREEZE(冻结订货单),CANCELLED
    string action = 2;
    string description = 3;
    // 附件
    string attachments = 4;
}


// 更新订货单信息参数
message UpdateDemandMasterInfoRequest {
    uint64 id = 1;
    // 备注
    string remark = 2;
    // 到货日期(只有主配单和紧急订货单才有的参数，门市订货单请不要传)
    google.protobuf.Timestamp arrival_date = 3;
    string attachments = 4;
}