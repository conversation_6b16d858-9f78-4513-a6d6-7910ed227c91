# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: store_management/hd_assignment.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='store_management/hd_assignment.proto',
  package='store_management',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n$store_management/hd_assignment.proto\x12\x10store_management\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"%\n\x0e\x43ommonResponse\x12\x13\n\x0b\x64\x65scription\x18\x01 \x01(\t\"\xab\x01\n\x1fGetValidStoreByProductIdRequest\x12\x13\n\x0bproduct_ids\x18\x01 \x03(\x04\x12\x0c\n\x04type\x18\x02 \x01(\t\x12.\n\norder_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nbranch_ids\x18\x04 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x05 \x03(\x04\x12\x0b\n\x03lan\x18\x06 \x01(\t\"\xec\x04\n GetValidStoreByProductIdResponse\x12K\n\x04rows\x18\x01 \x03(\x0b\x32=.store_management.GetValidStoreByProductIdResponse.ValidStore\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x1a\x9f\x01\n\nValidStore\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x12\n\nstore_code\x18\x02 \x01(\t\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12W\n\x0estore_products\x18\x04 \x03(\x0b\x32?.store_management.GetValidStoreByProductIdResponse.StoreProduct\x1a\xc3\x02\n\x0cStoreProduct\x12\x14\n\x0c\x61rrival_days\x18\x01 \x01(\x05\x12\x15\n\rdistribute_by\x18\x02 \x01(\x04\x12\x19\n\x11\x64istribution_type\x18\x03 \x01(\t\x12\x1a\n\x12increment_quantity\x18\x04 \x01(\x02\x12\x14\n\x0cmax_quantity\x18\x05 \x01(\x02\x12\x14\n\x0cmin_quantity\x18\x06 \x01(\x02\x12\x0f\n\x07unit_id\x18\x07 \x01(\x04\x12\x11\n\tunit_name\x18\x08 \x01(\t\x12\x11\n\tunit_spec\x18\t \x01(\t\x12\x16\n\x0epurchase_price\x18\n \x01(\x02\x12\x14\n\x0cpurchase_tax\x18\x0b \x01(\x02\x12\x12\n\nproduct_id\x18\x0c \x01(\x04\x12\x14\n\x0cproduct_code\x18\r \x01(\t\x12\x14\n\x0cproduct_name\x18\x0e \x01(\t\"\xf1\x03\n\x12ProductMainRequest\x12/\n\x0b\x64\x65mand_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06remark\x18\x03 \x01(\t\x12\x0c\n\x04type\x18\x04 \x01(\t\x12\x10\n\x08sub_type\x18\x05 \x01(\t\x12=\n\x05items\x18\x06 \x03(\x0b\x32..store_management.ProductMainRequest.StoreItem\x12\x10\n\x08\x62\x61tch_id\x18\x07 \x01(\x04\x1a\x66\n\tStoreItem\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12G\n\rproduct_items\x18\x02 \x03(\x0b\x32\x30.store_management.ProductMainRequest.ProductItem\x1a\x8e\x01\n\x0bProductItem\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x10\n\x08quantity\x18\x02 \x01(\x01\x12\x19\n\x11\x64istribution_type\x18\x03 \x01(\t\x12\x17\n\x0f\x64istribution_by\x18\x04 \x01(\x04\x12\x11\n\tvendor_id\x18\x05 \x01(\x04\x12\x12\n\norder_type\x18\x06 \x01(\t\"$\n\tIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\\\n\x19UploadDemandMasterRequest\x12\x11\n\tfile_name\x18\x01 \x01(\t\x12\x11\n\tfile_type\x18\x02 \x01(\t\x12\x0c\n\x04\x66ile\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\"\x89\x03\n\x12UploadDemandMaster\x12\x12\n\nstore_code\x18\x01 \x01(\t\x12\x12\n\nstore_name\x18\x02 \x01(\t\x12\x14\n\x0cproduct_code\x18\x03 \x01(\t\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12\x10\n\x08quantity\x18\x05 \x01(\x01\x12\x12\n\nstart_date\x18\x06 \x01(\t\x12\x10\n\x08\x65nd_date\x18\x07 \x01(\t\x12\x0f\n\x07row_num\x18\x08 \x01(\x04\x12\x10\n\x08store_id\x18\t \x01(\x04\x12\x12\n\nproduct_id\x18\n \x01(\x04\x12\n\n\x02id\x18\x0b \x01(\x04\x12\x0e\n\x06status\x18\x0c \x01(\t\x12\x12\n\npartner_id\x18\r \x01(\x04\x12\r\n\x05\x65rror\x18\x0e \x01(\t\x12\x10\n\x08\x62\x61tch_id\x18\x0f \x01(\x04\x12\x12\n\nstore_type\x18\x10 \x01(\t\x12\x11\n\tcentre_id\x18\x11 \x01(\x04\x12\x13\n\x0b\x63\x65ntre_name\x18\x12 \x01(\t\x12\x13\n\x0b\x63\x65ntre_code\x18\x13 \x01(\t\x12\x0e\n\x06remark\x18\x14 \x01(\t\"s\n\x1aUploadDemandMasterResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x32\n\x04rows\x18\x02 \x03(\x0b\x32$.store_management.UploadDemandMaster\x12\x11\n\tfile_name\x18\x03 \x01(\t\"o\n%GetDemandMasterUploadByBatchIdRequest\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\x12\x0e\n\x06offset\x18\x02 \x01(\x04\x12\r\n\x05limit\x18\x03 \x01(\x04\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\"\xea\x04\n\x1f\x44\x65mandMasterUploadByBatchDetail\x12\x12\n\nstore_code\x18\x01 \x01(\t\x12\x12\n\nstore_name\x18\x02 \x01(\t\x12\x14\n\x0cproduct_code\x18\x03 \x01(\t\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12\x10\n\x08quantity\x18\x05 \x01(\x01\x12.\n\nstart_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07row_num\x18\x08 \x01(\x04\x12\x10\n\x08store_id\x18\t \x01(\x04\x12\x12\n\nproduct_id\x18\n \x01(\x04\x12\n\n\x02id\x18\x0b \x01(\x04\x12\x0e\n\x06status\x18\x0c \x01(\t\x12\x12\n\npartner_id\x18\r \x01(\x04\x12\r\n\x05\x65rror\x18\x0e \x01(\t\x12\x10\n\x08\x62\x61tch_id\x18\x0f \x01(\x04\x12.\n\ncreated_at\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x12 \x01(\x04\x12\x12\n\nupdated_by\x18\x13 \x01(\x04\x12\x11\n\tmaster_id\x18\x14 \x01(\x04\x12\x13\n\x0bmaster_code\x18\x15 \x01(\t\x12\x11\n\tcentre_id\x18\x16 \x01(\x04\x12\x13\n\x0b\x63\x65ntre_name\x18\x17 \x01(\t\x12\x13\n\x0b\x63\x65ntre_code\x18\x18 \x01(\t\x12\x0e\n\x06remark\x18\x19 \x01(\t\"\xb1\x01\n&GetDemandMasterUploadByBatchIdResponse\x12?\n\x04rows\x18\x01 \x03(\x0b\x32\x31.store_management.DemandMasterUploadByBatchDetail\x12\r\n\x05total\x18\x02 \x01(\x04\x12\x11\n\tfile_name\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x14\n\x0cupdated_name\x18\x05 \x01(\t\"\xd8\x01\n\x1cGetDemandMasterUploadRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06offset\x18\x03 \x01(\x04\x12\r\n\x05limit\x18\x04 \x01(\x04\x12\x11\n\tfile_name\x18\x05 \x01(\t\x12\x15\n\rinclude_total\x18\x06 \x01(\x08\x12\x11\n\tfile_type\x18\x07 \x01(\t\"\xd6\x02\n\x19\x44\x65mandMasterUploadSummary\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x11\n\tfile_name\x18\x04 \x01(\t\x12\x12\n\ncreated_by\x18\x05 \x01(\x04\x12\x12\n\nupdated_by\x18\x06 \x01(\x04\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\t \x01(\t\x12\x14\n\x0cupdated_name\x18\n \x01(\t\x12/\n\x0bupload_date\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tfile_type\x18\x0c \x01(\t\"i\n\x1dGetDemandMasterUploadResponse\x12\x39\n\x04rows\x18\x01 \x03(\x0b\x32+.store_management.DemandMasterUploadSummary\x12\r\n\x05total\x18\x02 \x01(\x04\"4\n ApproveDemandMasterUploadRequest\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\"3\n!ApproveDemandMasterUploadResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"3\n\x1f\x43\x61ncelDemandMasterUploadRequest\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\"2\n CancelDemandMasterUploadResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\xd1\x03\n\x18QueryDemandMasterRequest\x12\x11\n\tstore_ids\x18\x01 \x03(\x04\x12\x13\n\x0bhas_product\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x10\n\x08sub_type\x18\x04 \x01(\t\x12\x12\n\nstore_type\x18\x05 \x03(\t\x12.\n\nstart_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x08 \x03(\t\x12\x0e\n\x06offset\x18\n \x01(\x04\x12\r\n\x05limit\x18\x0b \x01(\x04\x12\r\n\x05\x63odes\x18\x0c \x03(\t\x12\x0f\n\x07is_plan\x18\r \x01(\x08\x12\x11\n\tis_adjust\x18\x0e \x01(\x08\x12\x11\n\tsend_type\x18\x0f \x01(\t\x12\r\n\x05order\x18\x10 \x01(\t\x12\x0c\n\x04sort\x18\x11 \x01(\t\x12\x11\n\thavi_code\x18\x12 \x01(\t\x12\x15\n\rpre_havi_code\x18\x13 \x01(\t\x12\x10\n\x08\x62us_type\x18\x14 \x01(\t\x12\x11\n\tplan_name\x18\x15 \x01(\t\x12\x0b\n\x03lan\x18\x16 \x01(\t\x12\r\n\x05types\x18\x17 \x03(\t\"^\n\x19QueryDemandMasterResponse\x12\x32\n\x04rows\x18\x01 \x03(\x0b\x32$.store_management.DemandMasterEntity\x12\r\n\x05total\x18\x02 \x01(\x04\"q\n QueryDemandMasterProductResponse\x12>\n\x04rows\x18\x01 \x03(\x0b\x32\x30.store_management.QueryDemandMasterProductEntity\x12\r\n\x05total\x18\x02 \x01(\x05\"\xe0\x07\n\x1eQueryDemandMasterProductEntity\x12\x14\n\x0c\x61rrival_days\x18\x01 \x01(\x05\x12\x15\n\rdistribute_by\x18\x02 \x01(\x04\x12\x19\n\x11\x64istribution_type\x18\x03 \x01(\t\x12\x1a\n\x12increment_quantity\x18\x04 \x01(\x02\x12\x14\n\x0cmax_quantity\x18\x05 \x01(\x02\x12\x14\n\x0cmin_quantity\x18\x06 \x01(\x02\x12\x1b\n\x13product_category_id\x18\x07 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x08 \x01(\t\x12\x12\n\nproduct_id\x18\t \x01(\x04\x12\x14\n\x0cproduct_name\x18\n \x01(\t\x12\x10\n\x08quantity\x18\x0b \x01(\x01\x12\x0c\n\x04spec\x18\x0c \x01(\t\x12\x14\n\x0cstorage_type\x18\r \x01(\t\x12\x0f\n\x07unit_id\x18\x0e \x01(\x04\x12\x11\n\tunit_name\x18\x0f \x01(\t\x12\x11\n\tunit_spec\x18\x10 \x01(\t\x12\x11\n\tsale_type\x18\x11 \x01(\t\x12\x14\n\x0cproduct_type\x18\x12 \x01(\t\x12\x18\n\x10suggest_quantity\x18\x14 \x01(\x02\x12\x19\n\x11\x66inished_quantity\x18\x15 \x01(\x02\x12\x16\n\x0e\x62read_quantity\x18\x16 \x01(\x02\x12\x14\n\x0ctea_quantity\x18\x17 \x01(\x02\x12\x14\n\x0craw_quantity\x18\x18 \x01(\x02\x12\x16\n\x0epurchase_price\x18\x19 \x01(\x02\x12\x14\n\x0cpurchase_tax\x18\x1a \x01(\x02\x12\n\n\x02id\x18\x1b \x01(\x04\x12\x1b\n\x13\x64istribution_circle\x18\x1c \x01(\t\x12\x1e\n\x16\x64istribution_center_id\x18\x1d \x01(\x04\x12\x11\n\tvendor_id\x18\x1e \x01(\x04\x12\x15\n\rcycle_extends\x18\x1f \x01(\t\x12\x19\n\x11is_first_delivery\x18  \x01(\t\x12\x17\n\x0fyesterday_sales\x18! \x01(\x01\x12\x1a\n\x12\x61verage_week_sales\x18\" \x01(\x01\x12\x1b\n\x13yesterday_order_qty\x18# \x01(\x01\x12\x11\n\tunit_rate\x18$ \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18% \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18& \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\' \x01(\t\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18( \x01(\x01\x12\x1b\n\x13has_new_suggest_qty\x18) \x01(\x08\x12\x0f\n\x07\x62\x61rcode\x18* \x03(\t\"\xf8\x06\n\x12\x44\x65mandMasterEntity\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08\x62\x61tch_id\x18\x02 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x0c\n\x04type\x18\x04 \x01(\t\x12\x13\n\x0breason_type\x18\x05 \x01(\t\x12\x12\n\nreceive_by\x18\x06 \x01(\x04\x12\x14\n\x0creceive_name\x18\x07 \x01(\t\x12\x15\n\rdistribute_by\x18\x08 \x01(\x04\x12\x1a\n\x12store_secondary_id\x18\t \x01(\t\x12\x12\n\nstore_type\x18\n \x01(\t\x12\x19\n\x11\x64istribution_type\x18\x0b \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\treview_by\x18\r \x01(\x04\x12\x30\n\x0c\x61rrival_date\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x64\x65scription\x18\x0f \x01(\t\x12\x12\n\npartner_id\x18\x10 \x01(\x04\x12\x10\n\x08sub_type\x18\x11 \x01(\t\x12\x0e\n\x06remark\x18\x12 \x01(\t\x12\x13\n\x0bhas_product\x18\x13 \x01(\x05\x12\x0e\n\x06is_sys\x18\x14 \x01(\x05\x12\x0e\n\x06status\x18\x15 \x01(\t\x12\x16\n\x0eprocess_status\x18\x16 \x01(\t\x12.\n\nupdated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\x18 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x19 \x01(\t\x12\x14\n\x0cupdated_name\x18\x1a \x01(\t\x12.\n\ncreated_at\x18\x1b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x1c \x01(\x04\x12\x15\n\rproduct_count\x18\x1d \x01(\x05\x12\x0f\n\x07is_plan\x18\x1e \x01(\x08\x12\x11\n\tis_adjust\x18\x1f \x01(\x08\x12\x11\n\tsend_type\x18  \x01(\t\x12\x11\n\thavi_code\x18! \x01(\t\x12\x15\n\rpre_havi_code\x18\" \x01(\t\x12\x10\n\x08\x62us_type\x18# \x01(\t\x12\x13\n\x0b\x61ttachments\x18$ \x01(\t\x12\x13\n\x0bplan_method\x18% \x01(\t\x12\x11\n\tplan_name\x18& \x01(\t\"\x98\x03\n UpdateDemandMasterProductRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12L\n\x07product\x18\x02 \x03(\x0b\x32;.store_management.UpdateDemandMasterProductRequest.products\x12.\n\norder_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x61ttachments\x18\x04 \x01(\t\x1a\xcd\x01\n\x08products\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x10\n\x08quantity\x18\x03 \x01(\x01\x12\x10\n\x08tag_type\x18\x04 \x01(\t\x12\x19\n\x11\x64istribution_type\x18\x05 \x01(\t\x12\x17\n\x0f\x64istribution_by\x18\x06 \x01(\x04\x12\x0f\n\x07unit_id\x18\x07 \x01(\t\x12\x11\n\tunit_rate\x18\x08 \x01(\x01\x12\x11\n\tvendor_id\x18\t \x01(\x04\x12\x12\n\norder_type\x18\n \x01(\t\"Q\n DeleteDemandMasterProductRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12\x1a\n\x12\x64\x65mand_product_ids\x18\x02 \x03(\x04\"c\n\x1b\x44\x65\x61lDemandMasterByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06\x61\x63tion\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x04 \x01(\t\"\x82\x01\n\x1dUpdateDemandMasterInfoRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06remark\x18\x02 \x01(\t\x12\x30\n\x0c\x61rrival_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x61ttachments\x18\x04 \x01(\t2\xdd\x13\n\x1bStoreManagementHdAssignment\x12\x85\x01\n\x11\x43reateProductMain\x12$.store_management.ProductMainRequest\x1a\x1b.store_management.IdRequest\"-\x82\xd3\xe4\x93\x02\'\"\"/api/v2/supply/store/hd-assignment:\x01*\x12\xab\x01\n\x12UploadDemandMaster\x12+.store_management.UploadDemandMasterRequest\x1a,.store_management.UploadDemandMasterResponse\":\x82\xd3\xe4\x93\x02\x34\"//api/v2/supply/store/hd-assignment/upload/batch:\x01*\x12\xd7\x01\n\x1eGetDemandMasterUploadByBatchId\x12\x37.store_management.GetDemandMasterUploadByBatchIdRequest\x1a\x38.store_management.GetDemandMasterUploadByBatchIdResponse\"B\x82\xd3\xe4\x93\x02<\x12:/api/v2/supply/store/hd-assignment/upload/batch/{batch_id}\x12\xb5\x01\n\x15GetDemandMasterUpload\x12..store_management.GetDemandMasterUploadRequest\x1a/.store_management.GetDemandMasterUploadResponse\";\x82\xd3\xe4\x93\x02\x35\x12\x33/api/v2/supply/store/hd-assignment/upload/batch/log\x12\xd0\x01\n\x19\x41pproveDemandMasterUpload\x12\x32.store_management.ApproveDemandMasterUploadRequest\x1a\x33.store_management.ApproveDemandMasterUploadResponse\"J\x82\xd3\xe4\x93\x02\x44\x1a\x42/api/v2/supply/store/hd-assignment/upload/batch/{batch_id}/approve\x12\xcc\x01\n\x18\x43\x61ncelDemandMasterUpload\x12\x31.store_management.CancelDemandMasterUploadRequest\x1a\x32.store_management.CancelDemandMasterUploadResponse\"I\x82\xd3\xe4\x93\x02\x43\x1a\x41/api/v2/supply/store/hd-assignment/upload/batch/{batch_id}/cancel\x12\xbc\x01\n\x18GetValidStoreByProductId\x12\x31.store_management.GetValidStoreByProductIdRequest\x1a\x32.store_management.GetValidStoreByProductIdResponse\"9\x82\xd3\xe4\x93\x02\x33\"./api/v2/supply/store/hd-assignment/valid/store:\x01*\x12\x97\x01\n\x10ListDemandMaster\x12*.store_management.QueryDemandMasterRequest\x1a+.store_management.QueryDemandMasterResponse\"*\x82\xd3\xe4\x93\x02$\x12\"/api/v2/supply/store/hd-assignment\x12\x8b\x01\n\x15GetDemandMasterDetail\x12\x1b.store_management.IdRequest\x1a$.store_management.DemandMasterEntity\"/\x82\xd3\xe4\x93\x02)\x12\'/api/v2/supply/store/hd-assignment/{id}\x12\x9f\x01\n\x16UpdateDemandMasterInfo\x12/.store_management.UpdateDemandMasterInfoRequest\x1a .store_management.CommonResponse\"2\x82\xd3\xe4\x93\x02,\"\'/api/v2/supply/store/hd-assignment/{id}:\x01*\x12\xa8\x01\n\x1cGetDemandMasterProductDetail\x12\x1b.store_management.IdRequest\x1a\x32.store_management.QueryDemandMasterProductResponse\"7\x82\xd3\xe4\x93\x02\x31\x12//api/v2/supply/store/hd-assignment/{id}/product\x12\xba\x01\n\x19UpdateDemandMasterProduct\x12\x32.store_management.UpdateDemandMasterProductRequest\x1a\x32.store_management.UpdateDemandMasterProductRequest\"5\x82\xd3\xe4\x93\x02/\x1a*/api/v2/supply/store/hd-assignment/product:\x01*\x12\xbb\x01\n\x19\x44\x65leteDemandMasterProduct\x12\x32.store_management.DeleteDemandMasterProductRequest\x1a .store_management.CommonResponse\"H\x82\xd3\xe4\x93\x02\x42\x1a=/api/v2/supply/store/hd-assignment/{demand_id}/product/delete:\x01*\x12\xa4\x01\n\x14\x44\x65\x61lDemandMasterById\x12-.store_management.DealDemandMasterByIdRequest\x1a .store_management.CommonResponse\";\x82\xd3\xe4\x93\x02\x35\x1a\x30/api/v2/supply/store/hd-assignment/{id}/{action}:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_COMMONRESPONSE = _descriptor.Descriptor(
  name='CommonResponse',
  full_name='store_management.CommonResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='description', full_name='store_management.CommonResponse.description', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=121,
  serialized_end=158,
)


_GETVALIDSTOREBYPRODUCTIDREQUEST = _descriptor.Descriptor(
  name='GetValidStoreByProductIdRequest',
  full_name='store_management.GetValidStoreByProductIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='store_management.GetValidStoreByProductIdRequest.product_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store_management.GetValidStoreByProductIdRequest.type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date', full_name='store_management.GetValidStoreByProductIdRequest.order_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='store_management.GetValidStoreByProductIdRequest.branch_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='store_management.GetValidStoreByProductIdRequest.category_ids', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.GetValidStoreByProductIdRequest.lan', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=161,
  serialized_end=332,
)


_GETVALIDSTOREBYPRODUCTIDRESPONSE_VALIDSTORE = _descriptor.Descriptor(
  name='ValidStore',
  full_name='store_management.GetValidStoreByProductIdResponse.ValidStore',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='store_management.GetValidStoreByProductIdResponse.ValidStore.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='store_management.GetValidStoreByProductIdResponse.ValidStore.store_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='store_management.GetValidStoreByProductIdResponse.ValidStore.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_products', full_name='store_management.GetValidStoreByProductIdResponse.ValidStore.store_products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=470,
  serialized_end=629,
)

_GETVALIDSTOREBYPRODUCTIDRESPONSE_STOREPRODUCT = _descriptor.Descriptor(
  name='StoreProduct',
  full_name='store_management.GetValidStoreByProductIdResponse.StoreProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='arrival_days', full_name='store_management.GetValidStoreByProductIdResponse.StoreProduct.arrival_days', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_by', full_name='store_management.GetValidStoreByProductIdResponse.StoreProduct.distribute_by', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='store_management.GetValidStoreByProductIdResponse.StoreProduct.distribution_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_quantity', full_name='store_management.GetValidStoreByProductIdResponse.StoreProduct.increment_quantity', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_quantity', full_name='store_management.GetValidStoreByProductIdResponse.StoreProduct.max_quantity', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_quantity', full_name='store_management.GetValidStoreByProductIdResponse.StoreProduct.min_quantity', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='store_management.GetValidStoreByProductIdResponse.StoreProduct.unit_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='store_management.GetValidStoreByProductIdResponse.StoreProduct.unit_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='store_management.GetValidStoreByProductIdResponse.StoreProduct.unit_spec', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_price', full_name='store_management.GetValidStoreByProductIdResponse.StoreProduct.purchase_price', index=9,
      number=10, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_tax', full_name='store_management.GetValidStoreByProductIdResponse.StoreProduct.purchase_tax', index=10,
      number=11, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store_management.GetValidStoreByProductIdResponse.StoreProduct.product_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='store_management.GetValidStoreByProductIdResponse.StoreProduct.product_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store_management.GetValidStoreByProductIdResponse.StoreProduct.product_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=632,
  serialized_end=955,
)

_GETVALIDSTOREBYPRODUCTIDRESPONSE = _descriptor.Descriptor(
  name='GetValidStoreByProductIdResponse',
  full_name='store_management.GetValidStoreByProductIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_management.GetValidStoreByProductIdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='store_management.GetValidStoreByProductIdResponse.description', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_GETVALIDSTOREBYPRODUCTIDRESPONSE_VALIDSTORE, _GETVALIDSTOREBYPRODUCTIDRESPONSE_STOREPRODUCT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=335,
  serialized_end=955,
)


_PRODUCTMAINREQUEST_STOREITEM = _descriptor.Descriptor(
  name='StoreItem',
  full_name='store_management.ProductMainRequest.StoreItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='store_management.ProductMainRequest.StoreItem.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_items', full_name='store_management.ProductMainRequest.StoreItem.product_items', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1208,
  serialized_end=1310,
)

_PRODUCTMAINREQUEST_PRODUCTITEM = _descriptor.Descriptor(
  name='ProductItem',
  full_name='store_management.ProductMainRequest.ProductItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store_management.ProductMainRequest.ProductItem.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='store_management.ProductMainRequest.ProductItem.quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='store_management.ProductMainRequest.ProductItem.distribution_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_by', full_name='store_management.ProductMainRequest.ProductItem.distribution_by', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_id', full_name='store_management.ProductMainRequest.ProductItem.vendor_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='store_management.ProductMainRequest.ProductItem.order_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1313,
  serialized_end=1455,
)

_PRODUCTMAINREQUEST = _descriptor.Descriptor(
  name='ProductMainRequest',
  full_name='store_management.ProductMainRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='store_management.ProductMainRequest.demand_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='store_management.ProductMainRequest.arrival_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_management.ProductMainRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store_management.ProductMainRequest.type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='store_management.ProductMainRequest.sub_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='items', full_name='store_management.ProductMainRequest.items', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='store_management.ProductMainRequest.batch_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_PRODUCTMAINREQUEST_STOREITEM, _PRODUCTMAINREQUEST_PRODUCTITEM, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=958,
  serialized_end=1455,
)


_IDREQUEST = _descriptor.Descriptor(
  name='IdRequest',
  full_name='store_management.IdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.IdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.IdRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1457,
  serialized_end=1493,
)


_UPLOADDEMANDMASTERREQUEST = _descriptor.Descriptor(
  name='UploadDemandMasterRequest',
  full_name='store_management.UploadDemandMasterRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='file_name', full_name='store_management.UploadDemandMasterRequest.file_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_type', full_name='store_management.UploadDemandMasterRequest.file_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file', full_name='store_management.UploadDemandMasterRequest.file', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.UploadDemandMasterRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1495,
  serialized_end=1587,
)


_UPLOADDEMANDMASTER = _descriptor.Descriptor(
  name='UploadDemandMaster',
  full_name='store_management.UploadDemandMaster',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_code', full_name='store_management.UploadDemandMaster.store_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='store_management.UploadDemandMaster.store_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='store_management.UploadDemandMaster.product_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store_management.UploadDemandMaster.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='store_management.UploadDemandMaster.quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='store_management.UploadDemandMaster.start_date', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='store_management.UploadDemandMaster.end_date', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='row_num', full_name='store_management.UploadDemandMaster.row_num', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='store_management.UploadDemandMaster.store_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store_management.UploadDemandMaster.product_id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.UploadDemandMaster.id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store_management.UploadDemandMaster.status', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='store_management.UploadDemandMaster.partner_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error', full_name='store_management.UploadDemandMaster.error', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='store_management.UploadDemandMaster.batch_id', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='store_management.UploadDemandMaster.store_type', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='centre_id', full_name='store_management.UploadDemandMaster.centre_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='centre_name', full_name='store_management.UploadDemandMaster.centre_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='centre_code', full_name='store_management.UploadDemandMaster.centre_code', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_management.UploadDemandMaster.remark', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1590,
  serialized_end=1983,
)


_UPLOADDEMANDMASTERRESPONSE = _descriptor.Descriptor(
  name='UploadDemandMasterResponse',
  full_name='store_management.UploadDemandMasterResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='store_management.UploadDemandMasterResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_management.UploadDemandMasterResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_name', full_name='store_management.UploadDemandMasterResponse.file_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1985,
  serialized_end=2100,
)


_GETDEMANDMASTERUPLOADBYBATCHIDREQUEST = _descriptor.Descriptor(
  name='GetDemandMasterUploadByBatchIdRequest',
  full_name='store_management.GetDemandMasterUploadByBatchIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='store_management.GetDemandMasterUploadByBatchIdRequest.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store_management.GetDemandMasterUploadByBatchIdRequest.offset', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store_management.GetDemandMasterUploadByBatchIdRequest.limit', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='store_management.GetDemandMasterUploadByBatchIdRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2102,
  serialized_end=2213,
)


_DEMANDMASTERUPLOADBYBATCHDETAIL = _descriptor.Descriptor(
  name='DemandMasterUploadByBatchDetail',
  full_name='store_management.DemandMasterUploadByBatchDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_code', full_name='store_management.DemandMasterUploadByBatchDetail.store_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='store_management.DemandMasterUploadByBatchDetail.store_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='store_management.DemandMasterUploadByBatchDetail.product_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store_management.DemandMasterUploadByBatchDetail.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='store_management.DemandMasterUploadByBatchDetail.quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='store_management.DemandMasterUploadByBatchDetail.start_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='store_management.DemandMasterUploadByBatchDetail.end_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='row_num', full_name='store_management.DemandMasterUploadByBatchDetail.row_num', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='store_management.DemandMasterUploadByBatchDetail.store_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store_management.DemandMasterUploadByBatchDetail.product_id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.DemandMasterUploadByBatchDetail.id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store_management.DemandMasterUploadByBatchDetail.status', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='store_management.DemandMasterUploadByBatchDetail.partner_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error', full_name='store_management.DemandMasterUploadByBatchDetail.error', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='store_management.DemandMasterUploadByBatchDetail.batch_id', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='store_management.DemandMasterUploadByBatchDetail.created_at', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='store_management.DemandMasterUploadByBatchDetail.updated_at', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='store_management.DemandMasterUploadByBatchDetail.created_by', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='store_management.DemandMasterUploadByBatchDetail.updated_by', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='master_id', full_name='store_management.DemandMasterUploadByBatchDetail.master_id', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='master_code', full_name='store_management.DemandMasterUploadByBatchDetail.master_code', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='centre_id', full_name='store_management.DemandMasterUploadByBatchDetail.centre_id', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='centre_name', full_name='store_management.DemandMasterUploadByBatchDetail.centre_name', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='centre_code', full_name='store_management.DemandMasterUploadByBatchDetail.centre_code', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_management.DemandMasterUploadByBatchDetail.remark', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2216,
  serialized_end=2834,
)


_GETDEMANDMASTERUPLOADBYBATCHIDRESPONSE = _descriptor.Descriptor(
  name='GetDemandMasterUploadByBatchIdResponse',
  full_name='store_management.GetDemandMasterUploadByBatchIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_management.GetDemandMasterUploadByBatchIdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store_management.GetDemandMasterUploadByBatchIdResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_name', full_name='store_management.GetDemandMasterUploadByBatchIdResponse.file_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store_management.GetDemandMasterUploadByBatchIdResponse.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='store_management.GetDemandMasterUploadByBatchIdResponse.updated_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2837,
  serialized_end=3014,
)


_GETDEMANDMASTERUPLOADREQUEST = _descriptor.Descriptor(
  name='GetDemandMasterUploadRequest',
  full_name='store_management.GetDemandMasterUploadRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='store_management.GetDemandMasterUploadRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='store_management.GetDemandMasterUploadRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store_management.GetDemandMasterUploadRequest.offset', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store_management.GetDemandMasterUploadRequest.limit', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_name', full_name='store_management.GetDemandMasterUploadRequest.file_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='store_management.GetDemandMasterUploadRequest.include_total', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_type', full_name='store_management.GetDemandMasterUploadRequest.file_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3017,
  serialized_end=3233,
)


_DEMANDMASTERUPLOADSUMMARY = _descriptor.Descriptor(
  name='DemandMasterUploadSummary',
  full_name='store_management.DemandMasterUploadSummary',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.DemandMasterUploadSummary.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='store_management.DemandMasterUploadSummary.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store_management.DemandMasterUploadSummary.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_name', full_name='store_management.DemandMasterUploadSummary.file_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='store_management.DemandMasterUploadSummary.created_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='store_management.DemandMasterUploadSummary.updated_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='store_management.DemandMasterUploadSummary.created_at', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='store_management.DemandMasterUploadSummary.updated_at', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='store_management.DemandMasterUploadSummary.created_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='store_management.DemandMasterUploadSummary.updated_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='upload_date', full_name='store_management.DemandMasterUploadSummary.upload_date', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_type', full_name='store_management.DemandMasterUploadSummary.file_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3236,
  serialized_end=3578,
)


_GETDEMANDMASTERUPLOADRESPONSE = _descriptor.Descriptor(
  name='GetDemandMasterUploadResponse',
  full_name='store_management.GetDemandMasterUploadResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_management.GetDemandMasterUploadResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store_management.GetDemandMasterUploadResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3580,
  serialized_end=3685,
)


_APPROVEDEMANDMASTERUPLOADREQUEST = _descriptor.Descriptor(
  name='ApproveDemandMasterUploadRequest',
  full_name='store_management.ApproveDemandMasterUploadRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='store_management.ApproveDemandMasterUploadRequest.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3687,
  serialized_end=3739,
)


_APPROVEDEMANDMASTERUPLOADRESPONSE = _descriptor.Descriptor(
  name='ApproveDemandMasterUploadResponse',
  full_name='store_management.ApproveDemandMasterUploadResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='store_management.ApproveDemandMasterUploadResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3741,
  serialized_end=3792,
)


_CANCELDEMANDMASTERUPLOADREQUEST = _descriptor.Descriptor(
  name='CancelDemandMasterUploadRequest',
  full_name='store_management.CancelDemandMasterUploadRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='store_management.CancelDemandMasterUploadRequest.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3794,
  serialized_end=3845,
)


_CANCELDEMANDMASTERUPLOADRESPONSE = _descriptor.Descriptor(
  name='CancelDemandMasterUploadResponse',
  full_name='store_management.CancelDemandMasterUploadResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='store_management.CancelDemandMasterUploadResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3847,
  serialized_end=3897,
)


_QUERYDEMANDMASTERREQUEST = _descriptor.Descriptor(
  name='QueryDemandMasterRequest',
  full_name='store_management.QueryDemandMasterRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='store_management.QueryDemandMasterRequest.store_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='has_product', full_name='store_management.QueryDemandMasterRequest.has_product', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store_management.QueryDemandMasterRequest.type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='store_management.QueryDemandMasterRequest.sub_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='store_management.QueryDemandMasterRequest.store_type', index=4,
      number=5, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='store_management.QueryDemandMasterRequest.start_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='store_management.QueryDemandMasterRequest.end_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store_management.QueryDemandMasterRequest.status', index=7,
      number=8, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store_management.QueryDemandMasterRequest.offset', index=8,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store_management.QueryDemandMasterRequest.limit', index=9,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='codes', full_name='store_management.QueryDemandMasterRequest.codes', index=10,
      number=12, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_plan', full_name='store_management.QueryDemandMasterRequest.is_plan', index=11,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='store_management.QueryDemandMasterRequest.is_adjust', index=12,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='send_type', full_name='store_management.QueryDemandMasterRequest.send_type', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='store_management.QueryDemandMasterRequest.order', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='store_management.QueryDemandMasterRequest.sort', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='havi_code', full_name='store_management.QueryDemandMasterRequest.havi_code', index=16,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_havi_code', full_name='store_management.QueryDemandMasterRequest.pre_havi_code', index=17,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bus_type', full_name='store_management.QueryDemandMasterRequest.bus_type', index=18,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_name', full_name='store_management.QueryDemandMasterRequest.plan_name', index=19,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_management.QueryDemandMasterRequest.lan', index=20,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='types', full_name='store_management.QueryDemandMasterRequest.types', index=21,
      number=23, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3900,
  serialized_end=4365,
)


_QUERYDEMANDMASTERRESPONSE = _descriptor.Descriptor(
  name='QueryDemandMasterResponse',
  full_name='store_management.QueryDemandMasterResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_management.QueryDemandMasterResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store_management.QueryDemandMasterResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4367,
  serialized_end=4461,
)


_QUERYDEMANDMASTERPRODUCTRESPONSE = _descriptor.Descriptor(
  name='QueryDemandMasterProductResponse',
  full_name='store_management.QueryDemandMasterProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_management.QueryDemandMasterProductResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store_management.QueryDemandMasterProductResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4463,
  serialized_end=4576,
)


_QUERYDEMANDMASTERPRODUCTENTITY = _descriptor.Descriptor(
  name='QueryDemandMasterProductEntity',
  full_name='store_management.QueryDemandMasterProductEntity',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='arrival_days', full_name='store_management.QueryDemandMasterProductEntity.arrival_days', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_by', full_name='store_management.QueryDemandMasterProductEntity.distribute_by', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='store_management.QueryDemandMasterProductEntity.distribution_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_quantity', full_name='store_management.QueryDemandMasterProductEntity.increment_quantity', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_quantity', full_name='store_management.QueryDemandMasterProductEntity.max_quantity', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_quantity', full_name='store_management.QueryDemandMasterProductEntity.min_quantity', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_category_id', full_name='store_management.QueryDemandMasterProductEntity.product_category_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='store_management.QueryDemandMasterProductEntity.product_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store_management.QueryDemandMasterProductEntity.product_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store_management.QueryDemandMasterProductEntity.product_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='store_management.QueryDemandMasterProductEntity.quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='store_management.QueryDemandMasterProductEntity.spec', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='store_management.QueryDemandMasterProductEntity.storage_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='store_management.QueryDemandMasterProductEntity.unit_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='store_management.QueryDemandMasterProductEntity.unit_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='store_management.QueryDemandMasterProductEntity.unit_spec', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sale_type', full_name='store_management.QueryDemandMasterProductEntity.sale_type', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='store_management.QueryDemandMasterProductEntity.product_type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='suggest_quantity', full_name='store_management.QueryDemandMasterProductEntity.suggest_quantity', index=18,
      number=20, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='finished_quantity', full_name='store_management.QueryDemandMasterProductEntity.finished_quantity', index=19,
      number=21, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bread_quantity', full_name='store_management.QueryDemandMasterProductEntity.bread_quantity', index=20,
      number=22, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tea_quantity', full_name='store_management.QueryDemandMasterProductEntity.tea_quantity', index=21,
      number=23, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='raw_quantity', full_name='store_management.QueryDemandMasterProductEntity.raw_quantity', index=22,
      number=24, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_price', full_name='store_management.QueryDemandMasterProductEntity.purchase_price', index=23,
      number=25, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_tax', full_name='store_management.QueryDemandMasterProductEntity.purchase_tax', index=24,
      number=26, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.QueryDemandMasterProductEntity.id', index=25,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_circle', full_name='store_management.QueryDemandMasterProductEntity.distribution_circle', index=26,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_center_id', full_name='store_management.QueryDemandMasterProductEntity.distribution_center_id', index=27,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_id', full_name='store_management.QueryDemandMasterProductEntity.vendor_id', index=28,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cycle_extends', full_name='store_management.QueryDemandMasterProductEntity.cycle_extends', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_first_delivery', full_name='store_management.QueryDemandMasterProductEntity.is_first_delivery', index=30,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='yesterday_sales', full_name='store_management.QueryDemandMasterProductEntity.yesterday_sales', index=31,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='average_week_sales', full_name='store_management.QueryDemandMasterProductEntity.average_week_sales', index=32,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='yesterday_order_qty', full_name='store_management.QueryDemandMasterProductEntity.yesterday_order_qty', index=33,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='store_management.QueryDemandMasterProductEntity.unit_rate', index=34,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='store_management.QueryDemandMasterProductEntity.accounting_unit_id', index=35,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='store_management.QueryDemandMasterProductEntity.accounting_unit_name', index=36,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='store_management.QueryDemandMasterProductEntity.accounting_unit_spec', index=37,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='store_management.QueryDemandMasterProductEntity.accounting_quantity', index=38,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='has_new_suggest_qty', full_name='store_management.QueryDemandMasterProductEntity.has_new_suggest_qty', index=39,
      number=41, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='barcode', full_name='store_management.QueryDemandMasterProductEntity.barcode', index=40,
      number=42, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4579,
  serialized_end=5571,
)


_DEMANDMASTERENTITY = _descriptor.Descriptor(
  name='DemandMasterEntity',
  full_name='store_management.DemandMasterEntity',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.DemandMasterEntity.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='store_management.DemandMasterEntity.batch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store_management.DemandMasterEntity.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store_management.DemandMasterEntity.type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='store_management.DemandMasterEntity.reason_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_by', full_name='store_management.DemandMasterEntity.receive_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_name', full_name='store_management.DemandMasterEntity.receive_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_by', full_name='store_management.DemandMasterEntity.distribute_by', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='store_management.DemandMasterEntity.store_secondary_id', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='store_management.DemandMasterEntity.store_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='store_management.DemandMasterEntity.distribution_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='store_management.DemandMasterEntity.demand_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='store_management.DemandMasterEntity.review_by', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='store_management.DemandMasterEntity.arrival_date', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='store_management.DemandMasterEntity.description', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='store_management.DemandMasterEntity.partner_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='store_management.DemandMasterEntity.sub_type', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_management.DemandMasterEntity.remark', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='has_product', full_name='store_management.DemandMasterEntity.has_product', index=18,
      number=19, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_sys', full_name='store_management.DemandMasterEntity.is_sys', index=19,
      number=20, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store_management.DemandMasterEntity.status', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='store_management.DemandMasterEntity.process_status', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='store_management.DemandMasterEntity.updated_at', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='store_management.DemandMasterEntity.updated_by', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='store_management.DemandMasterEntity.created_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='store_management.DemandMasterEntity.updated_name', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='store_management.DemandMasterEntity.created_at', index=26,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='store_management.DemandMasterEntity.created_by', index=27,
      number=28, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_count', full_name='store_management.DemandMasterEntity.product_count', index=28,
      number=29, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_plan', full_name='store_management.DemandMasterEntity.is_plan', index=29,
      number=30, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='store_management.DemandMasterEntity.is_adjust', index=30,
      number=31, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='send_type', full_name='store_management.DemandMasterEntity.send_type', index=31,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='havi_code', full_name='store_management.DemandMasterEntity.havi_code', index=32,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_havi_code', full_name='store_management.DemandMasterEntity.pre_havi_code', index=33,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bus_type', full_name='store_management.DemandMasterEntity.bus_type', index=34,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_management.DemandMasterEntity.attachments', index=35,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_method', full_name='store_management.DemandMasterEntity.plan_method', index=36,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='plan_name', full_name='store_management.DemandMasterEntity.plan_name', index=37,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5574,
  serialized_end=6462,
)


_UPDATEDEMANDMASTERPRODUCTREQUEST_PRODUCTS = _descriptor.Descriptor(
  name='products',
  full_name='store_management.UpdateDemandMasterProductRequest.products',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.UpdateDemandMasterProductRequest.products.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store_management.UpdateDemandMasterProductRequest.products.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='store_management.UpdateDemandMasterProductRequest.products.quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_type', full_name='store_management.UpdateDemandMasterProductRequest.products.tag_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='store_management.UpdateDemandMasterProductRequest.products.distribution_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_by', full_name='store_management.UpdateDemandMasterProductRequest.products.distribution_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='store_management.UpdateDemandMasterProductRequest.products.unit_id', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='store_management.UpdateDemandMasterProductRequest.products.unit_rate', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_id', full_name='store_management.UpdateDemandMasterProductRequest.products.vendor_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='store_management.UpdateDemandMasterProductRequest.products.order_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6668,
  serialized_end=6873,
)

_UPDATEDEMANDMASTERPRODUCTREQUEST = _descriptor.Descriptor(
  name='UpdateDemandMasterProductRequest',
  full_name='store_management.UpdateDemandMasterProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='store_management.UpdateDemandMasterProductRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product', full_name='store_management.UpdateDemandMasterProductRequest.product', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date', full_name='store_management.UpdateDemandMasterProductRequest.order_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_management.UpdateDemandMasterProductRequest.attachments', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_UPDATEDEMANDMASTERPRODUCTREQUEST_PRODUCTS, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6465,
  serialized_end=6873,
)


_DELETEDEMANDMASTERPRODUCTREQUEST = _descriptor.Descriptor(
  name='DeleteDemandMasterProductRequest',
  full_name='store_management.DeleteDemandMasterProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='store_management.DeleteDemandMasterProductRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_product_ids', full_name='store_management.DeleteDemandMasterProductRequest.demand_product_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6875,
  serialized_end=6956,
)


_DEALDEMANDMASTERBYIDREQUEST = _descriptor.Descriptor(
  name='DealDemandMasterByIdRequest',
  full_name='store_management.DealDemandMasterByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.DealDemandMasterByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='store_management.DealDemandMasterByIdRequest.action', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='store_management.DealDemandMasterByIdRequest.description', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_management.DealDemandMasterByIdRequest.attachments', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6958,
  serialized_end=7057,
)


_UPDATEDEMANDMASTERINFOREQUEST = _descriptor.Descriptor(
  name='UpdateDemandMasterInfoRequest',
  full_name='store_management.UpdateDemandMasterInfoRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_management.UpdateDemandMasterInfoRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_management.UpdateDemandMasterInfoRequest.remark', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='store_management.UpdateDemandMasterInfoRequest.arrival_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_management.UpdateDemandMasterInfoRequest.attachments', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7060,
  serialized_end=7190,
)

_GETVALIDSTOREBYPRODUCTIDREQUEST.fields_by_name['order_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETVALIDSTOREBYPRODUCTIDRESPONSE_VALIDSTORE.fields_by_name['store_products'].message_type = _GETVALIDSTOREBYPRODUCTIDRESPONSE_STOREPRODUCT
_GETVALIDSTOREBYPRODUCTIDRESPONSE_VALIDSTORE.containing_type = _GETVALIDSTOREBYPRODUCTIDRESPONSE
_GETVALIDSTOREBYPRODUCTIDRESPONSE_STOREPRODUCT.containing_type = _GETVALIDSTOREBYPRODUCTIDRESPONSE
_GETVALIDSTOREBYPRODUCTIDRESPONSE.fields_by_name['rows'].message_type = _GETVALIDSTOREBYPRODUCTIDRESPONSE_VALIDSTORE
_PRODUCTMAINREQUEST_STOREITEM.fields_by_name['product_items'].message_type = _PRODUCTMAINREQUEST_PRODUCTITEM
_PRODUCTMAINREQUEST_STOREITEM.containing_type = _PRODUCTMAINREQUEST
_PRODUCTMAINREQUEST_PRODUCTITEM.containing_type = _PRODUCTMAINREQUEST
_PRODUCTMAINREQUEST.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCTMAINREQUEST.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCTMAINREQUEST.fields_by_name['items'].message_type = _PRODUCTMAINREQUEST_STOREITEM
_UPLOADDEMANDMASTERRESPONSE.fields_by_name['rows'].message_type = _UPLOADDEMANDMASTER
_DEMANDMASTERUPLOADBYBATCHDETAIL.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMANDMASTERUPLOADBYBATCHDETAIL.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMANDMASTERUPLOADBYBATCHDETAIL.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMANDMASTERUPLOADBYBATCHDETAIL.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDEMANDMASTERUPLOADBYBATCHIDRESPONSE.fields_by_name['rows'].message_type = _DEMANDMASTERUPLOADBYBATCHDETAIL
_GETDEMANDMASTERUPLOADREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDEMANDMASTERUPLOADREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMANDMASTERUPLOADSUMMARY.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMANDMASTERUPLOADSUMMARY.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMANDMASTERUPLOADSUMMARY.fields_by_name['upload_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETDEMANDMASTERUPLOADRESPONSE.fields_by_name['rows'].message_type = _DEMANDMASTERUPLOADSUMMARY
_QUERYDEMANDMASTERREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYDEMANDMASTERREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYDEMANDMASTERRESPONSE.fields_by_name['rows'].message_type = _DEMANDMASTERENTITY
_QUERYDEMANDMASTERPRODUCTRESPONSE.fields_by_name['rows'].message_type = _QUERYDEMANDMASTERPRODUCTENTITY
_DEMANDMASTERENTITY.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMANDMASTERENTITY.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMANDMASTERENTITY.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMANDMASTERENTITY.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATEDEMANDMASTERPRODUCTREQUEST_PRODUCTS.containing_type = _UPDATEDEMANDMASTERPRODUCTREQUEST
_UPDATEDEMANDMASTERPRODUCTREQUEST.fields_by_name['product'].message_type = _UPDATEDEMANDMASTERPRODUCTREQUEST_PRODUCTS
_UPDATEDEMANDMASTERPRODUCTREQUEST.fields_by_name['order_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATEDEMANDMASTERINFOREQUEST.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['CommonResponse'] = _COMMONRESPONSE
DESCRIPTOR.message_types_by_name['GetValidStoreByProductIdRequest'] = _GETVALIDSTOREBYPRODUCTIDREQUEST
DESCRIPTOR.message_types_by_name['GetValidStoreByProductIdResponse'] = _GETVALIDSTOREBYPRODUCTIDRESPONSE
DESCRIPTOR.message_types_by_name['ProductMainRequest'] = _PRODUCTMAINREQUEST
DESCRIPTOR.message_types_by_name['IdRequest'] = _IDREQUEST
DESCRIPTOR.message_types_by_name['UploadDemandMasterRequest'] = _UPLOADDEMANDMASTERREQUEST
DESCRIPTOR.message_types_by_name['UploadDemandMaster'] = _UPLOADDEMANDMASTER
DESCRIPTOR.message_types_by_name['UploadDemandMasterResponse'] = _UPLOADDEMANDMASTERRESPONSE
DESCRIPTOR.message_types_by_name['GetDemandMasterUploadByBatchIdRequest'] = _GETDEMANDMASTERUPLOADBYBATCHIDREQUEST
DESCRIPTOR.message_types_by_name['DemandMasterUploadByBatchDetail'] = _DEMANDMASTERUPLOADBYBATCHDETAIL
DESCRIPTOR.message_types_by_name['GetDemandMasterUploadByBatchIdResponse'] = _GETDEMANDMASTERUPLOADBYBATCHIDRESPONSE
DESCRIPTOR.message_types_by_name['GetDemandMasterUploadRequest'] = _GETDEMANDMASTERUPLOADREQUEST
DESCRIPTOR.message_types_by_name['DemandMasterUploadSummary'] = _DEMANDMASTERUPLOADSUMMARY
DESCRIPTOR.message_types_by_name['GetDemandMasterUploadResponse'] = _GETDEMANDMASTERUPLOADRESPONSE
DESCRIPTOR.message_types_by_name['ApproveDemandMasterUploadRequest'] = _APPROVEDEMANDMASTERUPLOADREQUEST
DESCRIPTOR.message_types_by_name['ApproveDemandMasterUploadResponse'] = _APPROVEDEMANDMASTERUPLOADRESPONSE
DESCRIPTOR.message_types_by_name['CancelDemandMasterUploadRequest'] = _CANCELDEMANDMASTERUPLOADREQUEST
DESCRIPTOR.message_types_by_name['CancelDemandMasterUploadResponse'] = _CANCELDEMANDMASTERUPLOADRESPONSE
DESCRIPTOR.message_types_by_name['QueryDemandMasterRequest'] = _QUERYDEMANDMASTERREQUEST
DESCRIPTOR.message_types_by_name['QueryDemandMasterResponse'] = _QUERYDEMANDMASTERRESPONSE
DESCRIPTOR.message_types_by_name['QueryDemandMasterProductResponse'] = _QUERYDEMANDMASTERPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['QueryDemandMasterProductEntity'] = _QUERYDEMANDMASTERPRODUCTENTITY
DESCRIPTOR.message_types_by_name['DemandMasterEntity'] = _DEMANDMASTERENTITY
DESCRIPTOR.message_types_by_name['UpdateDemandMasterProductRequest'] = _UPDATEDEMANDMASTERPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['DeleteDemandMasterProductRequest'] = _DELETEDEMANDMASTERPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['DealDemandMasterByIdRequest'] = _DEALDEMANDMASTERBYIDREQUEST
DESCRIPTOR.message_types_by_name['UpdateDemandMasterInfoRequest'] = _UPDATEDEMANDMASTERINFOREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CommonResponse = _reflection.GeneratedProtocolMessageType('CommonResponse', (_message.Message,), dict(
  DESCRIPTOR = _COMMONRESPONSE,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.CommonResponse)
  ))
_sym_db.RegisterMessage(CommonResponse)

GetValidStoreByProductIdRequest = _reflection.GeneratedProtocolMessageType('GetValidStoreByProductIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETVALIDSTOREBYPRODUCTIDREQUEST,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.GetValidStoreByProductIdRequest)
  ))
_sym_db.RegisterMessage(GetValidStoreByProductIdRequest)

GetValidStoreByProductIdResponse = _reflection.GeneratedProtocolMessageType('GetValidStoreByProductIdResponse', (_message.Message,), dict(

  ValidStore = _reflection.GeneratedProtocolMessageType('ValidStore', (_message.Message,), dict(
    DESCRIPTOR = _GETVALIDSTOREBYPRODUCTIDRESPONSE_VALIDSTORE,
    __module__ = 'store_management.hd_assignment_pb2'
    # @@protoc_insertion_point(class_scope:store_management.GetValidStoreByProductIdResponse.ValidStore)
    ))
  ,

  StoreProduct = _reflection.GeneratedProtocolMessageType('StoreProduct', (_message.Message,), dict(
    DESCRIPTOR = _GETVALIDSTOREBYPRODUCTIDRESPONSE_STOREPRODUCT,
    __module__ = 'store_management.hd_assignment_pb2'
    # @@protoc_insertion_point(class_scope:store_management.GetValidStoreByProductIdResponse.StoreProduct)
    ))
  ,
  DESCRIPTOR = _GETVALIDSTOREBYPRODUCTIDRESPONSE,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.GetValidStoreByProductIdResponse)
  ))
_sym_db.RegisterMessage(GetValidStoreByProductIdResponse)
_sym_db.RegisterMessage(GetValidStoreByProductIdResponse.ValidStore)
_sym_db.RegisterMessage(GetValidStoreByProductIdResponse.StoreProduct)

ProductMainRequest = _reflection.GeneratedProtocolMessageType('ProductMainRequest', (_message.Message,), dict(

  StoreItem = _reflection.GeneratedProtocolMessageType('StoreItem', (_message.Message,), dict(
    DESCRIPTOR = _PRODUCTMAINREQUEST_STOREITEM,
    __module__ = 'store_management.hd_assignment_pb2'
    # @@protoc_insertion_point(class_scope:store_management.ProductMainRequest.StoreItem)
    ))
  ,

  ProductItem = _reflection.GeneratedProtocolMessageType('ProductItem', (_message.Message,), dict(
    DESCRIPTOR = _PRODUCTMAINREQUEST_PRODUCTITEM,
    __module__ = 'store_management.hd_assignment_pb2'
    # @@protoc_insertion_point(class_scope:store_management.ProductMainRequest.ProductItem)
    ))
  ,
  DESCRIPTOR = _PRODUCTMAINREQUEST,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.ProductMainRequest)
  ))
_sym_db.RegisterMessage(ProductMainRequest)
_sym_db.RegisterMessage(ProductMainRequest.StoreItem)
_sym_db.RegisterMessage(ProductMainRequest.ProductItem)

IdRequest = _reflection.GeneratedProtocolMessageType('IdRequest', (_message.Message,), dict(
  DESCRIPTOR = _IDREQUEST,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.IdRequest)
  ))
_sym_db.RegisterMessage(IdRequest)

UploadDemandMasterRequest = _reflection.GeneratedProtocolMessageType('UploadDemandMasterRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPLOADDEMANDMASTERREQUEST,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.UploadDemandMasterRequest)
  ))
_sym_db.RegisterMessage(UploadDemandMasterRequest)

UploadDemandMaster = _reflection.GeneratedProtocolMessageType('UploadDemandMaster', (_message.Message,), dict(
  DESCRIPTOR = _UPLOADDEMANDMASTER,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.UploadDemandMaster)
  ))
_sym_db.RegisterMessage(UploadDemandMaster)

UploadDemandMasterResponse = _reflection.GeneratedProtocolMessageType('UploadDemandMasterResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPLOADDEMANDMASTERRESPONSE,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.UploadDemandMasterResponse)
  ))
_sym_db.RegisterMessage(UploadDemandMasterResponse)

GetDemandMasterUploadByBatchIdRequest = _reflection.GeneratedProtocolMessageType('GetDemandMasterUploadByBatchIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDEMANDMASTERUPLOADBYBATCHIDREQUEST,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.GetDemandMasterUploadByBatchIdRequest)
  ))
_sym_db.RegisterMessage(GetDemandMasterUploadByBatchIdRequest)

DemandMasterUploadByBatchDetail = _reflection.GeneratedProtocolMessageType('DemandMasterUploadByBatchDetail', (_message.Message,), dict(
  DESCRIPTOR = _DEMANDMASTERUPLOADBYBATCHDETAIL,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.DemandMasterUploadByBatchDetail)
  ))
_sym_db.RegisterMessage(DemandMasterUploadByBatchDetail)

GetDemandMasterUploadByBatchIdResponse = _reflection.GeneratedProtocolMessageType('GetDemandMasterUploadByBatchIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETDEMANDMASTERUPLOADBYBATCHIDRESPONSE,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.GetDemandMasterUploadByBatchIdResponse)
  ))
_sym_db.RegisterMessage(GetDemandMasterUploadByBatchIdResponse)

GetDemandMasterUploadRequest = _reflection.GeneratedProtocolMessageType('GetDemandMasterUploadRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDEMANDMASTERUPLOADREQUEST,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.GetDemandMasterUploadRequest)
  ))
_sym_db.RegisterMessage(GetDemandMasterUploadRequest)

DemandMasterUploadSummary = _reflection.GeneratedProtocolMessageType('DemandMasterUploadSummary', (_message.Message,), dict(
  DESCRIPTOR = _DEMANDMASTERUPLOADSUMMARY,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.DemandMasterUploadSummary)
  ))
_sym_db.RegisterMessage(DemandMasterUploadSummary)

GetDemandMasterUploadResponse = _reflection.GeneratedProtocolMessageType('GetDemandMasterUploadResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETDEMANDMASTERUPLOADRESPONSE,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.GetDemandMasterUploadResponse)
  ))
_sym_db.RegisterMessage(GetDemandMasterUploadResponse)

ApproveDemandMasterUploadRequest = _reflection.GeneratedProtocolMessageType('ApproveDemandMasterUploadRequest', (_message.Message,), dict(
  DESCRIPTOR = _APPROVEDEMANDMASTERUPLOADREQUEST,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.ApproveDemandMasterUploadRequest)
  ))
_sym_db.RegisterMessage(ApproveDemandMasterUploadRequest)

ApproveDemandMasterUploadResponse = _reflection.GeneratedProtocolMessageType('ApproveDemandMasterUploadResponse', (_message.Message,), dict(
  DESCRIPTOR = _APPROVEDEMANDMASTERUPLOADRESPONSE,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.ApproveDemandMasterUploadResponse)
  ))
_sym_db.RegisterMessage(ApproveDemandMasterUploadResponse)

CancelDemandMasterUploadRequest = _reflection.GeneratedProtocolMessageType('CancelDemandMasterUploadRequest', (_message.Message,), dict(
  DESCRIPTOR = _CANCELDEMANDMASTERUPLOADREQUEST,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.CancelDemandMasterUploadRequest)
  ))
_sym_db.RegisterMessage(CancelDemandMasterUploadRequest)

CancelDemandMasterUploadResponse = _reflection.GeneratedProtocolMessageType('CancelDemandMasterUploadResponse', (_message.Message,), dict(
  DESCRIPTOR = _CANCELDEMANDMASTERUPLOADRESPONSE,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.CancelDemandMasterUploadResponse)
  ))
_sym_db.RegisterMessage(CancelDemandMasterUploadResponse)

QueryDemandMasterRequest = _reflection.GeneratedProtocolMessageType('QueryDemandMasterRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYDEMANDMASTERREQUEST,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.QueryDemandMasterRequest)
  ))
_sym_db.RegisterMessage(QueryDemandMasterRequest)

QueryDemandMasterResponse = _reflection.GeneratedProtocolMessageType('QueryDemandMasterResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYDEMANDMASTERRESPONSE,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.QueryDemandMasterResponse)
  ))
_sym_db.RegisterMessage(QueryDemandMasterResponse)

QueryDemandMasterProductResponse = _reflection.GeneratedProtocolMessageType('QueryDemandMasterProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYDEMANDMASTERPRODUCTRESPONSE,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.QueryDemandMasterProductResponse)
  ))
_sym_db.RegisterMessage(QueryDemandMasterProductResponse)

QueryDemandMasterProductEntity = _reflection.GeneratedProtocolMessageType('QueryDemandMasterProductEntity', (_message.Message,), dict(
  DESCRIPTOR = _QUERYDEMANDMASTERPRODUCTENTITY,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.QueryDemandMasterProductEntity)
  ))
_sym_db.RegisterMessage(QueryDemandMasterProductEntity)

DemandMasterEntity = _reflection.GeneratedProtocolMessageType('DemandMasterEntity', (_message.Message,), dict(
  DESCRIPTOR = _DEMANDMASTERENTITY,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.DemandMasterEntity)
  ))
_sym_db.RegisterMessage(DemandMasterEntity)

UpdateDemandMasterProductRequest = _reflection.GeneratedProtocolMessageType('UpdateDemandMasterProductRequest', (_message.Message,), dict(

  products = _reflection.GeneratedProtocolMessageType('products', (_message.Message,), dict(
    DESCRIPTOR = _UPDATEDEMANDMASTERPRODUCTREQUEST_PRODUCTS,
    __module__ = 'store_management.hd_assignment_pb2'
    # @@protoc_insertion_point(class_scope:store_management.UpdateDemandMasterProductRequest.products)
    ))
  ,
  DESCRIPTOR = _UPDATEDEMANDMASTERPRODUCTREQUEST,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.UpdateDemandMasterProductRequest)
  ))
_sym_db.RegisterMessage(UpdateDemandMasterProductRequest)
_sym_db.RegisterMessage(UpdateDemandMasterProductRequest.products)

DeleteDemandMasterProductRequest = _reflection.GeneratedProtocolMessageType('DeleteDemandMasterProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEDEMANDMASTERPRODUCTREQUEST,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.DeleteDemandMasterProductRequest)
  ))
_sym_db.RegisterMessage(DeleteDemandMasterProductRequest)

DealDemandMasterByIdRequest = _reflection.GeneratedProtocolMessageType('DealDemandMasterByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _DEALDEMANDMASTERBYIDREQUEST,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.DealDemandMasterByIdRequest)
  ))
_sym_db.RegisterMessage(DealDemandMasterByIdRequest)

UpdateDemandMasterInfoRequest = _reflection.GeneratedProtocolMessageType('UpdateDemandMasterInfoRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEDEMANDMASTERINFOREQUEST,
  __module__ = 'store_management.hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:store_management.UpdateDemandMasterInfoRequest)
  ))
_sym_db.RegisterMessage(UpdateDemandMasterInfoRequest)



_STOREMANAGEMENTHDASSIGNMENT = _descriptor.ServiceDescriptor(
  name='StoreManagementHdAssignment',
  full_name='store_management.StoreManagementHdAssignment',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=7193,
  serialized_end=9718,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateProductMain',
    full_name='store_management.StoreManagementHdAssignment.CreateProductMain',
    index=0,
    containing_service=None,
    input_type=_PRODUCTMAINREQUEST,
    output_type=_IDREQUEST,
    serialized_options=_b('\202\323\344\223\002\'\"\"/api/v2/supply/store/hd-assignment:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UploadDemandMaster',
    full_name='store_management.StoreManagementHdAssignment.UploadDemandMaster',
    index=1,
    containing_service=None,
    input_type=_UPLOADDEMANDMASTERREQUEST,
    output_type=_UPLOADDEMANDMASTERRESPONSE,
    serialized_options=_b('\202\323\344\223\0024\"//api/v2/supply/store/hd-assignment/upload/batch:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetDemandMasterUploadByBatchId',
    full_name='store_management.StoreManagementHdAssignment.GetDemandMasterUploadByBatchId',
    index=2,
    containing_service=None,
    input_type=_GETDEMANDMASTERUPLOADBYBATCHIDREQUEST,
    output_type=_GETDEMANDMASTERUPLOADBYBATCHIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002<\022:/api/v2/supply/store/hd-assignment/upload/batch/{batch_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetDemandMasterUpload',
    full_name='store_management.StoreManagementHdAssignment.GetDemandMasterUpload',
    index=3,
    containing_service=None,
    input_type=_GETDEMANDMASTERUPLOADREQUEST,
    output_type=_GETDEMANDMASTERUPLOADRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\0223/api/v2/supply/store/hd-assignment/upload/batch/log'),
  ),
  _descriptor.MethodDescriptor(
    name='ApproveDemandMasterUpload',
    full_name='store_management.StoreManagementHdAssignment.ApproveDemandMasterUpload',
    index=4,
    containing_service=None,
    input_type=_APPROVEDEMANDMASTERUPLOADREQUEST,
    output_type=_APPROVEDEMANDMASTERUPLOADRESPONSE,
    serialized_options=_b('\202\323\344\223\002D\032B/api/v2/supply/store/hd-assignment/upload/batch/{batch_id}/approve'),
  ),
  _descriptor.MethodDescriptor(
    name='CancelDemandMasterUpload',
    full_name='store_management.StoreManagementHdAssignment.CancelDemandMasterUpload',
    index=5,
    containing_service=None,
    input_type=_CANCELDEMANDMASTERUPLOADREQUEST,
    output_type=_CANCELDEMANDMASTERUPLOADRESPONSE,
    serialized_options=_b('\202\323\344\223\002C\032A/api/v2/supply/store/hd-assignment/upload/batch/{batch_id}/cancel'),
  ),
  _descriptor.MethodDescriptor(
    name='GetValidStoreByProductId',
    full_name='store_management.StoreManagementHdAssignment.GetValidStoreByProductId',
    index=6,
    containing_service=None,
    input_type=_GETVALIDSTOREBYPRODUCTIDREQUEST,
    output_type=_GETVALIDSTOREBYPRODUCTIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0023\"./api/v2/supply/store/hd-assignment/valid/store:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListDemandMaster',
    full_name='store_management.StoreManagementHdAssignment.ListDemandMaster',
    index=7,
    containing_service=None,
    input_type=_QUERYDEMANDMASTERREQUEST,
    output_type=_QUERYDEMANDMASTERRESPONSE,
    serialized_options=_b('\202\323\344\223\002$\022\"/api/v2/supply/store/hd-assignment'),
  ),
  _descriptor.MethodDescriptor(
    name='GetDemandMasterDetail',
    full_name='store_management.StoreManagementHdAssignment.GetDemandMasterDetail',
    index=8,
    containing_service=None,
    input_type=_IDREQUEST,
    output_type=_DEMANDMASTERENTITY,
    serialized_options=_b('\202\323\344\223\002)\022\'/api/v2/supply/store/hd-assignment/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateDemandMasterInfo',
    full_name='store_management.StoreManagementHdAssignment.UpdateDemandMasterInfo',
    index=9,
    containing_service=None,
    input_type=_UPDATEDEMANDMASTERINFOREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\002,\"\'/api/v2/supply/store/hd-assignment/{id}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetDemandMasterProductDetail',
    full_name='store_management.StoreManagementHdAssignment.GetDemandMasterProductDetail',
    index=10,
    containing_service=None,
    input_type=_IDREQUEST,
    output_type=_QUERYDEMANDMASTERPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\0021\022//api/v2/supply/store/hd-assignment/{id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateDemandMasterProduct',
    full_name='store_management.StoreManagementHdAssignment.UpdateDemandMasterProduct',
    index=11,
    containing_service=None,
    input_type=_UPDATEDEMANDMASTERPRODUCTREQUEST,
    output_type=_UPDATEDEMANDMASTERPRODUCTREQUEST,
    serialized_options=_b('\202\323\344\223\002/\032*/api/v2/supply/store/hd-assignment/product:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteDemandMasterProduct',
    full_name='store_management.StoreManagementHdAssignment.DeleteDemandMasterProduct',
    index=12,
    containing_service=None,
    input_type=_DELETEDEMANDMASTERPRODUCTREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\002B\032=/api/v2/supply/store/hd-assignment/{demand_id}/product/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DealDemandMasterById',
    full_name='store_management.StoreManagementHdAssignment.DealDemandMasterById',
    index=13,
    containing_service=None,
    input_type=_DEALDEMANDMASTERBYIDREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\0320/api/v2/supply/store/hd-assignment/{id}/{action}:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_STOREMANAGEMENTHDASSIGNMENT)

DESCRIPTOR.services_by_name['StoreManagementHdAssignment'] = _STOREMANAGEMENTHDASSIGNMENT

# @@protoc_insertion_point(module_scope)
