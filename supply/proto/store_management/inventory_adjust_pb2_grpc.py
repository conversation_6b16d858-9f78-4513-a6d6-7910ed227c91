# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from store_management import inventory_adjust_pb2 as store__management_dot_inventory__adjust__pb2


class StoreInventoryAdjustServiceStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetDemandAdjustProductByStoreId = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/GetDemandAdjustProductByStoreId',
        request_serializer=store__management_dot_inventory__adjust__pb2.GetDemandAdjustProductByStoreIdRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.GetDemandAdjustProductByStoreIdResponse.FromString,
        )
    self.CreateDemandAdjust = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/CreateDemandAdjust',
        request_serializer=store__management_dot_inventory__adjust__pb2.CreateDemandAdjustRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.Response.FromString,
        )
    self.DealDemandAdjustById = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/DealDemandAdjustById',
        request_serializer=store__management_dot_inventory__adjust__pb2.DealDemandAdjustByIdRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.Response.FromString,
        )
    self.DeleteDemandAdjust = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/DeleteDemandAdjust',
        request_serializer=store__management_dot_inventory__adjust__pb2.IdReq.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.Response.FromString,
        )
    self.UpdateDemandAdjustProduct = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/UpdateDemandAdjustProduct',
        request_serializer=store__management_dot_inventory__adjust__pb2.UpdateDemandAdjustProductRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.UpdateDemandAdjustProductRequest.FromString,
        )
    self.DeleteDemandAdjustProduct = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/DeleteDemandAdjustProduct',
        request_serializer=store__management_dot_inventory__adjust__pb2.DeleteDemandAdjustProductRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.Response.FromString,
        )
    self.UpdateDemandAdjustInfo = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/UpdateDemandAdjustInfo',
        request_serializer=store__management_dot_inventory__adjust__pb2.UpdateDemandAdjustInfoRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.Response.FromString,
        )
    self.ListDemandAdjust = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/ListDemandAdjust',
        request_serializer=store__management_dot_inventory__adjust__pb2.QueryDemandAdjustRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.QueryDemandAdjustResponse.FromString,
        )
    self.GetDemandAdjustDetail = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/GetDemandAdjustDetail',
        request_serializer=store__management_dot_inventory__adjust__pb2.IdReq.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.DemandAdjustEntity.FromString,
        )
    self.GetDemandAdjustProductDetail = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/GetDemandAdjustProductDetail',
        request_serializer=store__management_dot_inventory__adjust__pb2.IdReq.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.QueryDemandAdjustProductResponse.FromString,
        )
    self.CreateReturn = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/CreateReturn',
        request_serializer=store__management_dot_inventory__adjust__pb2.CreateReturnRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.CreateReturnResponse.FromString,
        )
    self.GetValidProduct = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/GetValidProduct',
        request_serializer=store__management_dot_inventory__adjust__pb2.GetValidProductRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.ValidProduct.FromString,
        )
    self.ListReturn = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/ListReturn',
        request_serializer=store__management_dot_inventory__adjust__pb2.ListReturnRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.ListReturnResponse.FromString,
        )
    self.GetReturnById = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/GetReturnById',
        request_serializer=store__management_dot_inventory__adjust__pb2.GetReturnByIdRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.Returns.FromString,
        )
    self.GetReturnProductById = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/GetReturnProductById',
        request_serializer=store__management_dot_inventory__adjust__pb2.GetReturnProductByIdRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.GetReturnProductByIdResponse.FromString,
        )
    self.SubmitReturn = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/SubmitReturn',
        request_serializer=store__management_dot_inventory__adjust__pb2.SubmitReturnRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.ReturnCommonResponse.FromString,
        )
    self.RejectReturn = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/RejectReturn',
        request_serializer=store__management_dot_inventory__adjust__pb2.RejectReturnRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.ReturnCommonResponse.FromString,
        )
    self.ApproveReturn = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/ApproveReturn',
        request_serializer=store__management_dot_inventory__adjust__pb2.ApproveReturnRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.ReturnCommonResponse.FromString,
        )
    self.ConfirmReturn = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/ConfirmReturn',
        request_serializer=store__management_dot_inventory__adjust__pb2.ConfirmReturnRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.ReturnCommonResponse.FromString,
        )
    self.DeliveryReturn = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/DeliveryReturn',
        request_serializer=store__management_dot_inventory__adjust__pb2.DeliveryReturnRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.ReturnCommonResponse.FromString,
        )
    self.UpdateReturn = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/UpdateReturn',
        request_serializer=store__management_dot_inventory__adjust__pb2.UpdateReturnRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.ReturnCommonResponse.FromString,
        )
    self.DeleteReturn = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/DeleteReturn',
        request_serializer=store__management_dot_inventory__adjust__pb2.DeleteReturnRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.ReturnCommonResponse.FromString,
        )
    self.CheckReturnAvailableByrec = channel.unary_unary(
        '/store_management.StoreInventoryAdjustService/CheckReturnAvailableByrec',
        request_serializer=store__management_dot_inventory__adjust__pb2.CheckReturnAvailableRequest.SerializeToString,
        response_deserializer=store__management_dot_inventory__adjust__pb2.ReturnCommonResponse.FromString,
        )


class StoreInventoryAdjustServiceServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def GetDemandAdjustProductByStoreId(self, request, context):
    """根据门店id查询可调整订货商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateDemandAdjust(self, request, context):
    """创建订货调整单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DealDemandAdjustById(self, request, context):
    """修改订单状态统一入口
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteDemandAdjust(self, request, context):
    """删除库存调整单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateDemandAdjustProduct(self, request, context):
    """更新库存调整单商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteDemandAdjustProduct(self, request, context):
    """删除库存调整单订货商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateDemandAdjustInfo(self, request, context):
    """更新库存调整单信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListDemandAdjust(self, request, context):
    """枚举库存调整单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDemandAdjustDetail(self, request, context):
    """查询库存调整单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDemandAdjustProductDetail(self, request, context):
    """获取库存调整单商品信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateReturn(self, request, context):
    """创建退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetValidProduct(self, request, context):
    """查询可退货商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListReturn(self, request, context):
    """查询退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReturnById(self, request, context):
    """根据id查询退货单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReturnProductById(self, request, context):
    """根据id查询退货单商品详情
    查询退货单详情时需调用
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SubmitReturn(self, request, context):
    """提交退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RejectReturn(self, request, context):
    """驳回退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ApproveReturn(self, request, context):
    """审核退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ConfirmReturn(self, request, context):
    """确认退货
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeliveryReturn(self, request, context):
    """确认提货
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateReturn(self, request, context):
    """更新退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteReturn(self, request, context):
    """删除新建的退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CheckReturnAvailableByrec(self, request, context):
    """校验按收货原单退货是否超退
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_StoreInventoryAdjustServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetDemandAdjustProductByStoreId': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandAdjustProductByStoreId,
          request_deserializer=store__management_dot_inventory__adjust__pb2.GetDemandAdjustProductByStoreIdRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.GetDemandAdjustProductByStoreIdResponse.SerializeToString,
      ),
      'CreateDemandAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.CreateDemandAdjust,
          request_deserializer=store__management_dot_inventory__adjust__pb2.CreateDemandAdjustRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.Response.SerializeToString,
      ),
      'DealDemandAdjustById': grpc.unary_unary_rpc_method_handler(
          servicer.DealDemandAdjustById,
          request_deserializer=store__management_dot_inventory__adjust__pb2.DealDemandAdjustByIdRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.Response.SerializeToString,
      ),
      'DeleteDemandAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteDemandAdjust,
          request_deserializer=store__management_dot_inventory__adjust__pb2.IdReq.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.Response.SerializeToString,
      ),
      'UpdateDemandAdjustProduct': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateDemandAdjustProduct,
          request_deserializer=store__management_dot_inventory__adjust__pb2.UpdateDemandAdjustProductRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.UpdateDemandAdjustProductRequest.SerializeToString,
      ),
      'DeleteDemandAdjustProduct': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteDemandAdjustProduct,
          request_deserializer=store__management_dot_inventory__adjust__pb2.DeleteDemandAdjustProductRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.Response.SerializeToString,
      ),
      'UpdateDemandAdjustInfo': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateDemandAdjustInfo,
          request_deserializer=store__management_dot_inventory__adjust__pb2.UpdateDemandAdjustInfoRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.Response.SerializeToString,
      ),
      'ListDemandAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.ListDemandAdjust,
          request_deserializer=store__management_dot_inventory__adjust__pb2.QueryDemandAdjustRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.QueryDemandAdjustResponse.SerializeToString,
      ),
      'GetDemandAdjustDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandAdjustDetail,
          request_deserializer=store__management_dot_inventory__adjust__pb2.IdReq.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.DemandAdjustEntity.SerializeToString,
      ),
      'GetDemandAdjustProductDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandAdjustProductDetail,
          request_deserializer=store__management_dot_inventory__adjust__pb2.IdReq.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.QueryDemandAdjustProductResponse.SerializeToString,
      ),
      'CreateReturn': grpc.unary_unary_rpc_method_handler(
          servicer.CreateReturn,
          request_deserializer=store__management_dot_inventory__adjust__pb2.CreateReturnRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.CreateReturnResponse.SerializeToString,
      ),
      'GetValidProduct': grpc.unary_unary_rpc_method_handler(
          servicer.GetValidProduct,
          request_deserializer=store__management_dot_inventory__adjust__pb2.GetValidProductRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.ValidProduct.SerializeToString,
      ),
      'ListReturn': grpc.unary_unary_rpc_method_handler(
          servicer.ListReturn,
          request_deserializer=store__management_dot_inventory__adjust__pb2.ListReturnRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.ListReturnResponse.SerializeToString,
      ),
      'GetReturnById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReturnById,
          request_deserializer=store__management_dot_inventory__adjust__pb2.GetReturnByIdRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.Returns.SerializeToString,
      ),
      'GetReturnProductById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReturnProductById,
          request_deserializer=store__management_dot_inventory__adjust__pb2.GetReturnProductByIdRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.GetReturnProductByIdResponse.SerializeToString,
      ),
      'SubmitReturn': grpc.unary_unary_rpc_method_handler(
          servicer.SubmitReturn,
          request_deserializer=store__management_dot_inventory__adjust__pb2.SubmitReturnRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.ReturnCommonResponse.SerializeToString,
      ),
      'RejectReturn': grpc.unary_unary_rpc_method_handler(
          servicer.RejectReturn,
          request_deserializer=store__management_dot_inventory__adjust__pb2.RejectReturnRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.ReturnCommonResponse.SerializeToString,
      ),
      'ApproveReturn': grpc.unary_unary_rpc_method_handler(
          servicer.ApproveReturn,
          request_deserializer=store__management_dot_inventory__adjust__pb2.ApproveReturnRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.ReturnCommonResponse.SerializeToString,
      ),
      'ConfirmReturn': grpc.unary_unary_rpc_method_handler(
          servicer.ConfirmReturn,
          request_deserializer=store__management_dot_inventory__adjust__pb2.ConfirmReturnRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.ReturnCommonResponse.SerializeToString,
      ),
      'DeliveryReturn': grpc.unary_unary_rpc_method_handler(
          servicer.DeliveryReturn,
          request_deserializer=store__management_dot_inventory__adjust__pb2.DeliveryReturnRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.ReturnCommonResponse.SerializeToString,
      ),
      'UpdateReturn': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateReturn,
          request_deserializer=store__management_dot_inventory__adjust__pb2.UpdateReturnRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.ReturnCommonResponse.SerializeToString,
      ),
      'DeleteReturn': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteReturn,
          request_deserializer=store__management_dot_inventory__adjust__pb2.DeleteReturnRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.ReturnCommonResponse.SerializeToString,
      ),
      'CheckReturnAvailableByrec': grpc.unary_unary_rpc_method_handler(
          servicer.CheckReturnAvailableByrec,
          request_deserializer=store__management_dot_inventory__adjust__pb2.CheckReturnAvailableRequest.FromString,
          response_serializer=store__management_dot_inventory__adjust__pb2.ReturnCommonResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'store_management.StoreInventoryAdjustService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
