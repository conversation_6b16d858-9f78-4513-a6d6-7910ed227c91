# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import low_cost_pb2 as low__cost__pb2


class LowCostServiceStub(object):
  """库存报表相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.ListLowCost = channel.unary_unary(
        '/low_cost.LowCostService/ListLowCost',
        request_serializer=low__cost__pb2.ListLowCostRequest.SerializeToString,
        response_deserializer=low__cost__pb2.ListLowCostResponse.FromString,
        )


class LowCostServiceServicer(object):
  """库存报表相关服务
  """

  def ListLowCost(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_LowCostServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'ListLowCost': grpc.unary_unary_rpc_method_handler(
          servicer.ListLowCost,
          request_deserializer=low__cost__pb2.ListLowCostRequest.FromString,
          response_serializer=low__cost__pb2.ListLowCostResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'low_cost.LowCostService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
