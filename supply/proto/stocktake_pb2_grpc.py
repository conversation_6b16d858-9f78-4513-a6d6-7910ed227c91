# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
import stocktake_pb2 as stocktake__pb2


class stocktakeStub(object):
  """Stocktake 盘点服务
  Create_request里加请求ID，幂等检查create的API
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetStoreScope = channel.unary_unary(
        '/stocktake.stocktake/GetStoreScope',
        request_serializer=stocktake__pb2.StoreDataScopeRequest.SerializeToString,
        response_deserializer=stocktake__pb2.StoreDataScope.FromString,
        )
    self.Ping = channel.unary_unary(
        '/stocktake.stocktake/Ping',
        request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
        response_deserializer=stocktake__pb2.Pong.FromString,
        )
    self.CheckStocktakeByDocID = channel.unary_unary(
        '/stocktake.stocktake/CheckStocktakeByDocID',
        request_serializer=stocktake__pb2.CheckStocktakeByDocIDRequest.SerializeToString,
        response_deserializer=stocktake__pb2.CheckStocktakeByDocIDResponse.FromString,
        )
    self.ConfirmStocktakeByDocID = channel.unary_unary(
        '/stocktake.stocktake/ConfirmStocktakeByDocID',
        request_serializer=stocktake__pb2.ConfirmStocktakeByDocIDRequest.SerializeToString,
        response_deserializer=stocktake__pb2.ConfirmStocktakeByDocIDResponse.FromString,
        )
    self.ApproveStocktakeByDocID = channel.unary_unary(
        '/stocktake.stocktake/ApproveStocktakeByDocID',
        request_serializer=stocktake__pb2.ApproveStocktakeByDocIDRequest.SerializeToString,
        response_deserializer=stocktake__pb2.ApproveStocktakeByDocIDResponse.FromString,
        )
    self.RejectStocktakeProduct = channel.unary_unary(
        '/stocktake.stocktake/RejectStocktakeProduct',
        request_serializer=stocktake__pb2.RejectStocktakeProductRequest.SerializeToString,
        response_deserializer=stocktake__pb2.RejectStocktakeProductResponse.FromString,
        )
    self.CancelStocktakeByDocID = channel.unary_unary(
        '/stocktake.stocktake/CancelStocktakeByDocID',
        request_serializer=stocktake__pb2.CancelStocktakeByDocIDRequest.SerializeToString,
        response_deserializer=stocktake__pb2.CancelStocktakeByDocIDResponse.FromString,
        )
    self.GetStocktakeByDocID = channel.unary_unary(
        '/stocktake.stocktake/GetStocktakeByDocID',
        request_serializer=stocktake__pb2.GetStocktakeByDocIDRequest.SerializeToString,
        response_deserializer=stocktake__pb2.Stocktake.FromString,
        )
    self.GetStocktake = channel.unary_unary(
        '/stocktake.stocktake/GetStocktake',
        request_serializer=stocktake__pb2.GetStocktakeRequest.SerializeToString,
        response_deserializer=stocktake__pb2.GetStocktakeResponse.FromString,
        )
    self.GetStocktakeProduct = channel.unary_unary(
        '/stocktake.stocktake/GetStocktakeProduct',
        request_serializer=stocktake__pb2.GetStocktakeProductRequest.SerializeToString,
        response_deserializer=stocktake__pb2.GetStocktakeProductResponse.FromString,
        )
    self.PutStocktakeByDocID = channel.unary_unary(
        '/stocktake.stocktake/PutStocktakeByDocID',
        request_serializer=stocktake__pb2.PutStocktakeByDocIDRequest.SerializeToString,
        response_deserializer=stocktake__pb2.PutStocktakeByDocIDResponse.FromString,
        )
    self.CheckedStocktakeByDocID = channel.unary_unary(
        '/stocktake.stocktake/CheckedStocktakeByDocID',
        request_serializer=stocktake__pb2.CheckedStocktakeByDocIDRequest.SerializeToString,
        response_deserializer=stocktake__pb2.CheckedStocktakeByDocIDResponse.FromString,
        )
    self.GetStocktakeTags = channel.unary_unary(
        '/stocktake.stocktake/GetStocktakeTags',
        request_serializer=stocktake__pb2.GetStocktakeTagsRequest.SerializeToString,
        response_deserializer=stocktake__pb2.GetStocktakeTagsResponse.FromString,
        )
    self.ActionStocktakeTags = channel.unary_unary(
        '/stocktake.stocktake/ActionStocktakeTags',
        request_serializer=stocktake__pb2.ActionStocktakeTagsRequest.SerializeToString,
        response_deserializer=stocktake__pb2.ActionStocktakeTagsResponse.FromString,
        )
    self.DeleteStocktakeProductTags = channel.unary_unary(
        '/stocktake.stocktake/DeleteStocktakeProductTags',
        request_serializer=stocktake__pb2.DeleteStocktakeProductTagsRequest.SerializeToString,
        response_deserializer=stocktake__pb2.DeleteStocktakeProductTagsResponse.FromString,
        )
    self.GetStocktakeBalance = channel.unary_unary(
        '/stocktake.stocktake/GetStocktakeBalance',
        request_serializer=stocktake__pb2.GetStocktakeBalanceRequest.SerializeToString,
        response_deserializer=stocktake__pb2.GetStocktakeBalanceResponse.FromString,
        )
    self.SubmitStocktakeByDocID = channel.unary_unary(
        '/stocktake.stocktake/SubmitStocktakeByDocID',
        request_serializer=stocktake__pb2.SubmitStocktakeByDocIDRequest.SerializeToString,
        response_deserializer=stocktake__pb2.SubmitStocktakeByDocIDResponse.FromString,
        )
    self.GetStocktakeBalanceProductGroup = channel.unary_unary(
        '/stocktake.stocktake/GetStocktakeBalanceProductGroup',
        request_serializer=stocktake__pb2.GetStocktakeBalanceProductGroupRequest.SerializeToString,
        response_deserializer=stocktake__pb2.GetStocktakeBalanceProductGroupResponse.FromString,
        )
    self.StocktakeBiDetailed = channel.unary_unary(
        '/stocktake.stocktake/StocktakeBiDetailed',
        request_serializer=stocktake__pb2.StocktakeBiDetailedRequest.SerializeToString,
        response_deserializer=stocktake__pb2.StocktakeBiDetailedResponse.FromString,
        )
    self.StocktakeBalanceRegion = channel.unary_unary(
        '/stocktake.stocktake/StocktakeBalanceRegion',
        request_serializer=stocktake__pb2.StocktakeBalanceRegionRequest.SerializeToString,
        response_deserializer=stocktake__pb2.StocktakeBalanceRegionResponse.FromString,
        )
    self.AdvanceStocktakeDiff = channel.unary_unary(
        '/stocktake.stocktake/AdvanceStocktakeDiff',
        request_serializer=stocktake__pb2.AdvanceStocktakeDiffRequest.SerializeToString,
        response_deserializer=stocktake__pb2.AdvanceStocktakeDiffResponse.FromString,
        )
    self.StocktakeDiffReport = channel.unary_unary(
        '/stocktake.stocktake/StocktakeDiffReport',
        request_serializer=stocktake__pb2.StocktakeDiffReportRequest.SerializeToString,
        response_deserializer=stocktake__pb2.StocktakeDiffReportResponse.FromString,
        )
    self.GetUncompleteDoc = channel.unary_unary(
        '/stocktake.stocktake/GetUncompleteDoc',
        request_serializer=stocktake__pb2.GetUncompleteDocRequest.SerializeToString,
        response_deserializer=stocktake__pb2.GetUncompleteDocResponse.FromString,
        )
    self.RecreateStocktakeDoc = channel.unary_unary(
        '/stocktake.stocktake/RecreateStocktakeDoc',
        request_serializer=stocktake__pb2.RecreateStocktakeDocRequest.SerializeToString,
        response_deserializer=stocktake__pb2.RecreateStocktakeDocResponse.FromString,
        )
    self.StocktakeDocStatistics = channel.unary_unary(
        '/stocktake.stocktake/StocktakeDocStatistics',
        request_serializer=stocktake__pb2.StocktakeDocStatisticsRequest.SerializeToString,
        response_deserializer=stocktake__pb2.StocktakeDocStatisticsResponse.FromString,
        )
    self.StocktakeDiffCollectReport = channel.unary_unary(
        '/stocktake.stocktake/StocktakeDiffCollectReport',
        request_serializer=stocktake__pb2.StocktakeDiffCollectReportRequest.SerializeToString,
        response_deserializer=stocktake__pb2.StocktakeDiffCollectReportResponse.FromString,
        )
    self.UncompleteDocReport = channel.unary_unary(
        '/stocktake.stocktake/UncompleteDocReport',
        request_serializer=stocktake__pb2.UncompleteDocReportRequest.SerializeToString,
        response_deserializer=stocktake__pb2.UncompleteDocReportResponse.FromString,
        )
    self.StocktakeProductImport = channel.unary_unary(
        '/stocktake.stocktake/StocktakeProductImport',
        request_serializer=stocktake__pb2.StocktakeProductImportRequest.SerializeToString,
        response_deserializer=stocktake__pb2.StocktakeProductImportResponse.FromString,
        )
    self.UpdateStocktakeImportBatch = channel.unary_unary(
        '/stocktake.stocktake/UpdateStocktakeImportBatch',
        request_serializer=stocktake__pb2.UpdateStocktakeImportBatchRequest.SerializeToString,
        response_deserializer=stocktake__pb2.UpdateStocktakeImportBatchResponse.FromString,
        )


class stocktakeServicer(object):
  """Stocktake 盘点服务
  Create_request里加请求ID，幂等检查create的API
  """

  def GetStoreScope(self, request, context):
    """scope权限
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def Ping(self, request, context):
    """Ping 健康检查
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CheckStocktakeByDocID(self, request, context):
    """备注:盘点商品在INITED和REJECTED状态可以填写数量，CONFITRMED，不能。
    CheckStocktakeByDocID 检查能否确认盘点单 14
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ConfirmStocktakeByDocID(self, request, context):
    """ConfirmStocktakeByDocID 确认盘点单（status=CONFITRMED，核算完库存后'FINALIZED'）15
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ApproveStocktakeByDocID(self, request, context):
    """APPROVEDStocktakeByDocID  财务确认盘点单（status=APPROVED最终状态，）16
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RejectStocktakeProduct(self, request, context):
    """RejectStocktakeProduct 财务驳回提交后的部分盘点单明细商品（status=REJECTED，部分商品status=REJECTED）17
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CancelStocktakeByDocID(self, request, context):
    """CancelStocktakeByDocID  作废盘点单（status=CANCELED）18
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStocktakeByDocID(self, request, context):
    """GetStocktakeByDocID 获取一个盘点单19
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStocktake(self, request, context):
    """GetStocktake 查询盘点单20
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStocktakeProduct(self, request, context):
    """GetStocktakeProduct 查询盘点单明细商品21
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def PutStocktakeByDocID(self, request, context):
    """PutStocktakeByDocID 提交盘点单明细商品数量，更新盘点单, 22
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CheckedStocktakeByDocID(self, request, context):
    """CheckedStocktakeByDocID 检查完成盘点单23
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStocktakeTags(self, request, context):
    """GetStocktakeTags获取盘点标签24
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ActionStocktakeTags(self, request, context):
    """ActionStocktakeTags增加，删除，更新,获取盘点标签25
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteStocktakeProductTags(self, request, context):
    """DeleteStocktakeProductTags删除盘点商品标签条目26
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStocktakeBalance(self, request, context):
    """GetStocktakeBalance盘点单损益报表27
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SubmitStocktakeByDocID(self, request, context):
    """SubmitStocktakeByDocID 提交盘点单28
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStocktakeBalanceProductGroup(self, request, context):
    """GetStocktakeBalanceProductGroup门店盘点单损益汇总报表29
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def StocktakeBiDetailed(self, request, context):
    """StocktakeBiDetailed盘点单报表30
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def StocktakeBalanceRegion(self, request, context):
    """StocktakeBalanceRegion区域盘点31
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AdvanceStocktakeDiff(self, request, context):
    """AdvanceStocktakeDiff  提前查看盘点损益32
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def StocktakeDiffReport(self, request, context):
    """StocktakeDiffReport  盘点差异表33
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetUncompleteDoc(self, request, context):
    """GetUncompleteDoc  首页未完成单据34
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RecreateStocktakeDoc(self, request, context):
    """RecreateStocktakeDoc  重盘功能，重新生成盘点单35
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def StocktakeDocStatistics(self, request, context):
    """StocktakeDocStatistics  盘点单统计报表36
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def StocktakeDiffCollectReport(self, request, context):
    """StocktakeDiffCollectReport  盘点差异表汇总查询37
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UncompleteDocReport(self, request, context):
    """UncompleteDocReport  未完成单据报表38
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def StocktakeProductImport(self, request, context):
    """仓库/门店盘点单商品明细导入
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateStocktakeImportBatch(self, request, context):
    """仓库/门店盘点单商品导入文件状态修改
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_stocktakeServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetStoreScope': grpc.unary_unary_rpc_method_handler(
          servicer.GetStoreScope,
          request_deserializer=stocktake__pb2.StoreDataScopeRequest.FromString,
          response_serializer=stocktake__pb2.StoreDataScope.SerializeToString,
      ),
      'Ping': grpc.unary_unary_rpc_method_handler(
          servicer.Ping,
          request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
          response_serializer=stocktake__pb2.Pong.SerializeToString,
      ),
      'CheckStocktakeByDocID': grpc.unary_unary_rpc_method_handler(
          servicer.CheckStocktakeByDocID,
          request_deserializer=stocktake__pb2.CheckStocktakeByDocIDRequest.FromString,
          response_serializer=stocktake__pb2.CheckStocktakeByDocIDResponse.SerializeToString,
      ),
      'ConfirmStocktakeByDocID': grpc.unary_unary_rpc_method_handler(
          servicer.ConfirmStocktakeByDocID,
          request_deserializer=stocktake__pb2.ConfirmStocktakeByDocIDRequest.FromString,
          response_serializer=stocktake__pb2.ConfirmStocktakeByDocIDResponse.SerializeToString,
      ),
      'ApproveStocktakeByDocID': grpc.unary_unary_rpc_method_handler(
          servicer.ApproveStocktakeByDocID,
          request_deserializer=stocktake__pb2.ApproveStocktakeByDocIDRequest.FromString,
          response_serializer=stocktake__pb2.ApproveStocktakeByDocIDResponse.SerializeToString,
      ),
      'RejectStocktakeProduct': grpc.unary_unary_rpc_method_handler(
          servicer.RejectStocktakeProduct,
          request_deserializer=stocktake__pb2.RejectStocktakeProductRequest.FromString,
          response_serializer=stocktake__pb2.RejectStocktakeProductResponse.SerializeToString,
      ),
      'CancelStocktakeByDocID': grpc.unary_unary_rpc_method_handler(
          servicer.CancelStocktakeByDocID,
          request_deserializer=stocktake__pb2.CancelStocktakeByDocIDRequest.FromString,
          response_serializer=stocktake__pb2.CancelStocktakeByDocIDResponse.SerializeToString,
      ),
      'GetStocktakeByDocID': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktakeByDocID,
          request_deserializer=stocktake__pb2.GetStocktakeByDocIDRequest.FromString,
          response_serializer=stocktake__pb2.Stocktake.SerializeToString,
      ),
      'GetStocktake': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktake,
          request_deserializer=stocktake__pb2.GetStocktakeRequest.FromString,
          response_serializer=stocktake__pb2.GetStocktakeResponse.SerializeToString,
      ),
      'GetStocktakeProduct': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktakeProduct,
          request_deserializer=stocktake__pb2.GetStocktakeProductRequest.FromString,
          response_serializer=stocktake__pb2.GetStocktakeProductResponse.SerializeToString,
      ),
      'PutStocktakeByDocID': grpc.unary_unary_rpc_method_handler(
          servicer.PutStocktakeByDocID,
          request_deserializer=stocktake__pb2.PutStocktakeByDocIDRequest.FromString,
          response_serializer=stocktake__pb2.PutStocktakeByDocIDResponse.SerializeToString,
      ),
      'CheckedStocktakeByDocID': grpc.unary_unary_rpc_method_handler(
          servicer.CheckedStocktakeByDocID,
          request_deserializer=stocktake__pb2.CheckedStocktakeByDocIDRequest.FromString,
          response_serializer=stocktake__pb2.CheckedStocktakeByDocIDResponse.SerializeToString,
      ),
      'GetStocktakeTags': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktakeTags,
          request_deserializer=stocktake__pb2.GetStocktakeTagsRequest.FromString,
          response_serializer=stocktake__pb2.GetStocktakeTagsResponse.SerializeToString,
      ),
      'ActionStocktakeTags': grpc.unary_unary_rpc_method_handler(
          servicer.ActionStocktakeTags,
          request_deserializer=stocktake__pb2.ActionStocktakeTagsRequest.FromString,
          response_serializer=stocktake__pb2.ActionStocktakeTagsResponse.SerializeToString,
      ),
      'DeleteStocktakeProductTags': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteStocktakeProductTags,
          request_deserializer=stocktake__pb2.DeleteStocktakeProductTagsRequest.FromString,
          response_serializer=stocktake__pb2.DeleteStocktakeProductTagsResponse.SerializeToString,
      ),
      'GetStocktakeBalance': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktakeBalance,
          request_deserializer=stocktake__pb2.GetStocktakeBalanceRequest.FromString,
          response_serializer=stocktake__pb2.GetStocktakeBalanceResponse.SerializeToString,
      ),
      'SubmitStocktakeByDocID': grpc.unary_unary_rpc_method_handler(
          servicer.SubmitStocktakeByDocID,
          request_deserializer=stocktake__pb2.SubmitStocktakeByDocIDRequest.FromString,
          response_serializer=stocktake__pb2.SubmitStocktakeByDocIDResponse.SerializeToString,
      ),
      'GetStocktakeBalanceProductGroup': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktakeBalanceProductGroup,
          request_deserializer=stocktake__pb2.GetStocktakeBalanceProductGroupRequest.FromString,
          response_serializer=stocktake__pb2.GetStocktakeBalanceProductGroupResponse.SerializeToString,
      ),
      'StocktakeBiDetailed': grpc.unary_unary_rpc_method_handler(
          servicer.StocktakeBiDetailed,
          request_deserializer=stocktake__pb2.StocktakeBiDetailedRequest.FromString,
          response_serializer=stocktake__pb2.StocktakeBiDetailedResponse.SerializeToString,
      ),
      'StocktakeBalanceRegion': grpc.unary_unary_rpc_method_handler(
          servicer.StocktakeBalanceRegion,
          request_deserializer=stocktake__pb2.StocktakeBalanceRegionRequest.FromString,
          response_serializer=stocktake__pb2.StocktakeBalanceRegionResponse.SerializeToString,
      ),
      'AdvanceStocktakeDiff': grpc.unary_unary_rpc_method_handler(
          servicer.AdvanceStocktakeDiff,
          request_deserializer=stocktake__pb2.AdvanceStocktakeDiffRequest.FromString,
          response_serializer=stocktake__pb2.AdvanceStocktakeDiffResponse.SerializeToString,
      ),
      'StocktakeDiffReport': grpc.unary_unary_rpc_method_handler(
          servicer.StocktakeDiffReport,
          request_deserializer=stocktake__pb2.StocktakeDiffReportRequest.FromString,
          response_serializer=stocktake__pb2.StocktakeDiffReportResponse.SerializeToString,
      ),
      'GetUncompleteDoc': grpc.unary_unary_rpc_method_handler(
          servicer.GetUncompleteDoc,
          request_deserializer=stocktake__pb2.GetUncompleteDocRequest.FromString,
          response_serializer=stocktake__pb2.GetUncompleteDocResponse.SerializeToString,
      ),
      'RecreateStocktakeDoc': grpc.unary_unary_rpc_method_handler(
          servicer.RecreateStocktakeDoc,
          request_deserializer=stocktake__pb2.RecreateStocktakeDocRequest.FromString,
          response_serializer=stocktake__pb2.RecreateStocktakeDocResponse.SerializeToString,
      ),
      'StocktakeDocStatistics': grpc.unary_unary_rpc_method_handler(
          servicer.StocktakeDocStatistics,
          request_deserializer=stocktake__pb2.StocktakeDocStatisticsRequest.FromString,
          response_serializer=stocktake__pb2.StocktakeDocStatisticsResponse.SerializeToString,
      ),
      'StocktakeDiffCollectReport': grpc.unary_unary_rpc_method_handler(
          servicer.StocktakeDiffCollectReport,
          request_deserializer=stocktake__pb2.StocktakeDiffCollectReportRequest.FromString,
          response_serializer=stocktake__pb2.StocktakeDiffCollectReportResponse.SerializeToString,
      ),
      'UncompleteDocReport': grpc.unary_unary_rpc_method_handler(
          servicer.UncompleteDocReport,
          request_deserializer=stocktake__pb2.UncompleteDocReportRequest.FromString,
          response_serializer=stocktake__pb2.UncompleteDocReportResponse.SerializeToString,
      ),
      'StocktakeProductImport': grpc.unary_unary_rpc_method_handler(
          servicer.StocktakeProductImport,
          request_deserializer=stocktake__pb2.StocktakeProductImportRequest.FromString,
          response_serializer=stocktake__pb2.StocktakeProductImportResponse.SerializeToString,
      ),
      'UpdateStocktakeImportBatch': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateStocktakeImportBatch,
          request_deserializer=stocktake__pb2.UpdateStocktakeImportBatchRequest.FromString,
          response_serializer=stocktake__pb2.UpdateStocktakeImportBatchResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'stocktake.stocktake', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
