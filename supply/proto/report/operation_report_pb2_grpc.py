# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from report import operation_report_pb2 as report_dot_operation__report__pb2


class OperationReportStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.Ping = channel.unary_unary(
        '/operation_report.OperationReport/Ping',
        request_serializer=report_dot_operation__report__pb2.Null.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.Pong.FromString,
        )
    self.GetETicketReport = channel.unary_unary(
        '/operation_report.OperationReport/GetETicketReport',
        request_serializer=report_dot_operation__report__pb2.GetETicketReportRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetETicketReportResponse.FromString,
        )
    self.GetStoreSalesReport = channel.unary_unary(
        '/operation_report.OperationReport/GetStoreSalesReport',
        request_serializer=report_dot_operation__report__pb2.GetStoreSalesReportRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetStoreSalesReportReponse.FromString,
        )
    self.GetProductSalesReport = channel.unary_unary(
        '/operation_report.OperationReport/GetProductSalesReport',
        request_serializer=report_dot_operation__report__pb2.GetProductSalesReportRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetProductSalesReportResponse.FromString,
        )
    self.GetDiscountReport = channel.unary_unary(
        '/operation_report.OperationReport/GetDiscountReport',
        request_serializer=report_dot_operation__report__pb2.GetDiscountReportRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetDiscountReportResponse.FromString,
        )
    self.GetPaymentReport = channel.unary_unary(
        '/operation_report.OperationReport/GetPaymentReport',
        request_serializer=report_dot_operation__report__pb2.GetPaymentReportRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetPaymentReportResponse.FromString,
        )
    self.GetProductPaymentReport = channel.unary_unary(
        '/operation_report.OperationReport/GetProductPaymentReport',
        request_serializer=report_dot_operation__report__pb2.GetProductPaymentReportRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetProductPaymentReportResponse.FromString,
        )
    self.GetStoreChannelReport = channel.unary_unary(
        '/operation_report.OperationReport/GetStoreChannelReport',
        request_serializer=report_dot_operation__report__pb2.GetStoreChannelReportRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetStoreChannelReportResponse.FromString,
        )
    self.GetProductChannelReport = channel.unary_unary(
        '/operation_report.OperationReport/GetProductChannelReport',
        request_serializer=report_dot_operation__report__pb2.GetProductChannelReportRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetProductChannelReportResponse.FromString,
        )
    self.GetStorePeriodReport = channel.unary_unary(
        '/operation_report.OperationReport/GetStorePeriodReport',
        request_serializer=report_dot_operation__report__pb2.GetStorePeriodReportRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetStorePeriodReportResponse.FromString,
        )
    self.GetStoreChannelPeriodReport = channel.unary_unary(
        '/operation_report.OperationReport/GetStoreChannelPeriodReport',
        request_serializer=report_dot_operation__report__pb2.GetStoreChannelPeriodReportRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetStoreChannelPeriodReportResponse.FromString,
        )
    self.GetPaymentPeriodReport = channel.unary_unary(
        '/operation_report.OperationReport/GetPaymentPeriodReport',
        request_serializer=report_dot_operation__report__pb2.GetPaymentPeriodReportRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetPaymentPeriodReportResponse.FromString,
        )
    self.GetProductPeriodReport = channel.unary_unary(
        '/operation_report.OperationReport/GetProductPeriodReport',
        request_serializer=report_dot_operation__report__pb2.GetProductPeriodReportRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetProductPeriodReportResponse.FromString,
        )
    self.GetParseFaliedETicketList = channel.unary_unary(
        '/operation_report.OperationReport/GetParseFaliedETicketList',
        request_serializer=report_dot_operation__report__pb2.GetParseFaliedETicketListRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetParseFaliedETicketListResponse.FromString,
        )
    self.ReParseFailedETickets = channel.unary_unary(
        '/operation_report.OperationReport/ReParseFailedETickets',
        request_serializer=report_dot_operation__report__pb2.Null.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.Result.FromString,
        )
    self.GetStoreSalesReportForPDA = channel.unary_unary(
        '/operation_report.OperationReport/GetStoreSalesReportForPDA',
        request_serializer=report_dot_operation__report__pb2.GetStoreSalesReportForPDARequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetStoreSalesReportForPDAResponse.FromString,
        )
    self.GetEticketTraceIds = channel.unary_unary(
        '/operation_report.OperationReport/GetEticketTraceIds',
        request_serializer=report_dot_operation__report__pb2.GetETicketTraceIdsRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetETicketTraceIdsResponse.FromString,
        )
    self.GetProductSalesOffDemand = channel.unary_unary(
        '/operation_report.OperationReport/GetProductSalesOffDemand',
        request_serializer=report_dot_operation__report__pb2.GetProductSalesOffDemandRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetProductSalesOffDemandResponse.FromString,
        )
    self.GetProductSalesOffDemand2 = channel.unary_unary(
        '/operation_report.OperationReport/GetProductSalesOffDemand2',
        request_serializer=report_dot_operation__report__pb2.GetProductSalesOffDemandRequest2.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetProductSalesOffDemandResponse2.FromString,
        )
    self.GetSalesExplainedReport = channel.unary_unary(
        '/operation_report.OperationReport/GetSalesExplainedReport',
        request_serializer=report_dot_operation__report__pb2.GetSalesExplainedReportRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetSalesExplainedReportResponse.FromString,
        )
    self.GetSalesExplainedSummaryReport = channel.unary_unary(
        '/operation_report.OperationReport/GetSalesExplainedSummaryReport',
        request_serializer=report_dot_operation__report__pb2.GetSalesExplainedSummaryReportRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetSalesExplainedSummaryReportResponse.FromString,
        )
    self.GetETicketDetail = channel.unary_unary(
        '/operation_report.OperationReport/GetETicketDetail',
        request_serializer=report_dot_operation__report__pb2.GetETicketDetailRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetETicketDetailResponse.FromString,
        )
    self.GetSalesExplainedErrorReport = channel.unary_unary(
        '/operation_report.OperationReport/GetSalesExplainedErrorReport',
        request_serializer=report_dot_operation__report__pb2.GetSalesExplainedErrorReportRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetSalesExplainedErrorReportResponse.FromString,
        )
    self.GetMaterialSupplies = channel.unary_unary(
        '/operation_report.OperationReport/GetMaterialSupplies',
        request_serializer=report_dot_operation__report__pb2.GetMaterialSuppliesRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetMaterialSuppliesResponse.FromString,
        )
    self.GetSalesExplainedSupplementReport = channel.unary_unary(
        '/operation_report.OperationReport/GetSalesExplainedSupplementReport',
        request_serializer=report_dot_operation__report__pb2.GetSalesExplainedReportRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetSalesExplainedReportResponse.FromString,
        )
    self.GetSalesExplainedSupplementSummaryReport = channel.unary_unary(
        '/operation_report.OperationReport/GetSalesExplainedSupplementSummaryReport',
        request_serializer=report_dot_operation__report__pb2.GetSalesExplainedSummaryReportRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetSalesExplainedSummaryReportResponse.FromString,
        )
    self.GetMaterialCostReport = channel.unary_unary(
        '/operation_report.OperationReport/GetMaterialCostReport',
        request_serializer=report_dot_operation__report__pb2.GetMaterialCostReportRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetMaterialCostReportResponse.FromString,
        )
    self.GetBalanceCostReport = channel.unary_unary(
        '/operation_report.OperationReport/GetBalanceCostReport',
        request_serializer=report_dot_operation__report__pb2.GetBalanceCostReportRequest.SerializeToString,
        response_deserializer=report_dot_operation__report__pb2.GetBalanceCostReportResponse.FromString,
        )


class OperationReportServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def Ping(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetETicketReport(self, request, context):
    """查询电子小票, 按门店, 营业日期
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStoreSalesReport(self, request, context):
    """查询门店销售报表, 按 日期/门店/区域/门店类型 筛选, 日/月/年进行汇总
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProductSalesReport(self, request, context):
    """查询单品销售报表, 按日期/门店/商品 筛选, 按照日期/门店/商品进行聚合
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDiscountReport(self, request, context):
    """查询折扣报表, 按日期/门店/折扣方式 筛选, 按照日期/门店/折扣方式进行聚合
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPaymentReport(self, request, context):
    """查询支付报表, 按日期/门店/支付方式/支付类别 筛选, 按照日期/门店/支付方式进行聚合
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProductPaymentReport(self, request, context):
    """查询单品支付报表, 按日期/门店/支付方式/商品 筛选, 按照日期/门店/商品/支付方式进行聚合
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStoreChannelReport(self, request, context):
    """查询门店渠道报表, 按日期/门店/渠道类型/交付方式 筛选, 按照日期/门店/渠道/交付方式进行聚合
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProductChannelReport(self, request, context):
    """查询单品渠道报表, 按日期/门店/产品/渠道类型/交付方式 筛选, 按照日期/门店/产品/渠道/交付方式进行聚合
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStorePeriodReport(self, request, context):
    """查询门店/区域时段报表, 按 日期/区域 筛选, 按照 时段 /区域/进行聚合
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStoreChannelPeriodReport(self, request, context):
    """查询门店/区域 渠道时段报表, 按 日期/区域/渠道类型/交付方式 筛选, 按照 时段 /区域/ 渠道类型/ 交付方式进行聚合
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPaymentPeriodReport(self, request, context):
    """查询门店/区域 渠道时段报表, 按 日期/区域/渠道类型/交付方式 筛选, 按照 时段 /区域/ 渠道类型/ 交付方式进行聚合
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProductPeriodReport(self, request, context):
    """查询门店/区域  单品时段报表, 按 日期/区域/产品/产品类别 筛选, 按照 时段 /区域/ 产品/ 产品类别进行聚合
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetParseFaliedETicketList(self, request, context):
    """查询解析失败的小票报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ReParseFailedETickets(self, request, context):
    """补偿重传小票信息(重建 cache)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStoreSalesReportForPDA(self, request, context):
    """pda 仪表盘当前门店营业信息(重建 cache)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetEticketTraceIds(self, request, context):
    """小票trace_id列表查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProductSalesOffDemand(self, request, context):
    """建议订货前置查询接口
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProductSalesOffDemand2(self, request, context):
    """建议订货前置查询接口
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetSalesExplainedReport(self, request, context):
    """查询小票分解报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetSalesExplainedSummaryReport(self, request, context):
    """查询小票分解报表汇总查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetETicketDetail(self, request, context):
    """查询电子小票详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetSalesExplainedErrorReport(self, request, context):
    """销售配方拆解异常表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetMaterialSupplies(self, request, context):
    """物料使用量报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetSalesExplainedSupplementReport(self, request, context):
    """查询补传小票分解报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetSalesExplainedSupplementSummaryReport(self, request, context):
    """查询补传小票分解报表汇总查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetMaterialCostReport(self, request, context):
    """物料(商品)成本报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetBalanceCostReport(self, request, context):
    """成本进销存报表(主要体现门店/仓库的商品总金额)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_OperationReportServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'Ping': grpc.unary_unary_rpc_method_handler(
          servicer.Ping,
          request_deserializer=report_dot_operation__report__pb2.Null.FromString,
          response_serializer=report_dot_operation__report__pb2.Pong.SerializeToString,
      ),
      'GetETicketReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetETicketReport,
          request_deserializer=report_dot_operation__report__pb2.GetETicketReportRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetETicketReportResponse.SerializeToString,
      ),
      'GetStoreSalesReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetStoreSalesReport,
          request_deserializer=report_dot_operation__report__pb2.GetStoreSalesReportRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetStoreSalesReportReponse.SerializeToString,
      ),
      'GetProductSalesReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetProductSalesReport,
          request_deserializer=report_dot_operation__report__pb2.GetProductSalesReportRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetProductSalesReportResponse.SerializeToString,
      ),
      'GetDiscountReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetDiscountReport,
          request_deserializer=report_dot_operation__report__pb2.GetDiscountReportRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetDiscountReportResponse.SerializeToString,
      ),
      'GetPaymentReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetPaymentReport,
          request_deserializer=report_dot_operation__report__pb2.GetPaymentReportRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetPaymentReportResponse.SerializeToString,
      ),
      'GetProductPaymentReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetProductPaymentReport,
          request_deserializer=report_dot_operation__report__pb2.GetProductPaymentReportRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetProductPaymentReportResponse.SerializeToString,
      ),
      'GetStoreChannelReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetStoreChannelReport,
          request_deserializer=report_dot_operation__report__pb2.GetStoreChannelReportRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetStoreChannelReportResponse.SerializeToString,
      ),
      'GetProductChannelReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetProductChannelReport,
          request_deserializer=report_dot_operation__report__pb2.GetProductChannelReportRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetProductChannelReportResponse.SerializeToString,
      ),
      'GetStorePeriodReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetStorePeriodReport,
          request_deserializer=report_dot_operation__report__pb2.GetStorePeriodReportRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetStorePeriodReportResponse.SerializeToString,
      ),
      'GetStoreChannelPeriodReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetStoreChannelPeriodReport,
          request_deserializer=report_dot_operation__report__pb2.GetStoreChannelPeriodReportRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetStoreChannelPeriodReportResponse.SerializeToString,
      ),
      'GetPaymentPeriodReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetPaymentPeriodReport,
          request_deserializer=report_dot_operation__report__pb2.GetPaymentPeriodReportRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetPaymentPeriodReportResponse.SerializeToString,
      ),
      'GetProductPeriodReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetProductPeriodReport,
          request_deserializer=report_dot_operation__report__pb2.GetProductPeriodReportRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetProductPeriodReportResponse.SerializeToString,
      ),
      'GetParseFaliedETicketList': grpc.unary_unary_rpc_method_handler(
          servicer.GetParseFaliedETicketList,
          request_deserializer=report_dot_operation__report__pb2.GetParseFaliedETicketListRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetParseFaliedETicketListResponse.SerializeToString,
      ),
      'ReParseFailedETickets': grpc.unary_unary_rpc_method_handler(
          servicer.ReParseFailedETickets,
          request_deserializer=report_dot_operation__report__pb2.Null.FromString,
          response_serializer=report_dot_operation__report__pb2.Result.SerializeToString,
      ),
      'GetStoreSalesReportForPDA': grpc.unary_unary_rpc_method_handler(
          servicer.GetStoreSalesReportForPDA,
          request_deserializer=report_dot_operation__report__pb2.GetStoreSalesReportForPDARequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetStoreSalesReportForPDAResponse.SerializeToString,
      ),
      'GetEticketTraceIds': grpc.unary_unary_rpc_method_handler(
          servicer.GetEticketTraceIds,
          request_deserializer=report_dot_operation__report__pb2.GetETicketTraceIdsRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetETicketTraceIdsResponse.SerializeToString,
      ),
      'GetProductSalesOffDemand': grpc.unary_unary_rpc_method_handler(
          servicer.GetProductSalesOffDemand,
          request_deserializer=report_dot_operation__report__pb2.GetProductSalesOffDemandRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetProductSalesOffDemandResponse.SerializeToString,
      ),
      'GetProductSalesOffDemand2': grpc.unary_unary_rpc_method_handler(
          servicer.GetProductSalesOffDemand2,
          request_deserializer=report_dot_operation__report__pb2.GetProductSalesOffDemandRequest2.FromString,
          response_serializer=report_dot_operation__report__pb2.GetProductSalesOffDemandResponse2.SerializeToString,
      ),
      'GetSalesExplainedReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetSalesExplainedReport,
          request_deserializer=report_dot_operation__report__pb2.GetSalesExplainedReportRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetSalesExplainedReportResponse.SerializeToString,
      ),
      'GetSalesExplainedSummaryReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetSalesExplainedSummaryReport,
          request_deserializer=report_dot_operation__report__pb2.GetSalesExplainedSummaryReportRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetSalesExplainedSummaryReportResponse.SerializeToString,
      ),
      'GetETicketDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetETicketDetail,
          request_deserializer=report_dot_operation__report__pb2.GetETicketDetailRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetETicketDetailResponse.SerializeToString,
      ),
      'GetSalesExplainedErrorReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetSalesExplainedErrorReport,
          request_deserializer=report_dot_operation__report__pb2.GetSalesExplainedErrorReportRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetSalesExplainedErrorReportResponse.SerializeToString,
      ),
      'GetMaterialSupplies': grpc.unary_unary_rpc_method_handler(
          servicer.GetMaterialSupplies,
          request_deserializer=report_dot_operation__report__pb2.GetMaterialSuppliesRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetMaterialSuppliesResponse.SerializeToString,
      ),
      'GetSalesExplainedSupplementReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetSalesExplainedSupplementReport,
          request_deserializer=report_dot_operation__report__pb2.GetSalesExplainedReportRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetSalesExplainedReportResponse.SerializeToString,
      ),
      'GetSalesExplainedSupplementSummaryReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetSalesExplainedSupplementSummaryReport,
          request_deserializer=report_dot_operation__report__pb2.GetSalesExplainedSummaryReportRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetSalesExplainedSummaryReportResponse.SerializeToString,
      ),
      'GetMaterialCostReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetMaterialCostReport,
          request_deserializer=report_dot_operation__report__pb2.GetMaterialCostReportRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetMaterialCostReportResponse.SerializeToString,
      ),
      'GetBalanceCostReport': grpc.unary_unary_rpc_method_handler(
          servicer.GetBalanceCostReport,
          request_deserializer=report_dot_operation__report__pb2.GetBalanceCostReportRequest.FromString,
          response_serializer=report_dot_operation__report__pb2.GetBalanceCostReportResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'operation_report.OperationReport', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
