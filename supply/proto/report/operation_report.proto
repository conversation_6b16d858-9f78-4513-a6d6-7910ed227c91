syntax = "proto3";
import "google/protobuf/timestamp.proto";
import "google/api/annotations.proto";


package operation_report;

service OperationReport {
    rpc Ping (Null) returns (Pong) {
        option (google.api.http) = {
        get: "/api/v2/report/ping"
        };
    }
    // 查询电子小票, 按门店, 营业日期
    rpc GetETicketReport (GetETicketReportRequest) returns (GetETicketReportResponse) {
        option (google.api.http) = {
            post: "/api/v2/report/e_ticket_report"
            body: "*"
        };
    }

    // 查询门店销售报表, 按 日期/门店/区域/门店类型 筛选, 日/月/年进行汇总
    rpc GetStoreSalesReport (GetStoreSalesReportRequest) returns (GetStoreSalesReportReponse) {
        option (google.api.http) = {
            post: "/api/v2/report/store_sales_report"
            body: "*"
        };
    }

    // 查询单品销售报表, 按日期/门店/商品 筛选, 按照日期/门店/商品进行聚合
    rpc GetProductSalesReport (GetProductSalesReportRequest) returns (GetProductSalesReportResponse) {
        option (google.api.http) = {
            post: "/api/v2/report/product_sales_report"
            body: "*"
        };
    };

    // 查询折扣报表, 按日期/门店/折扣方式 筛选, 按照日期/门店/折扣方式进行聚合
    rpc GetDiscountReport (GetDiscountReportRequest) returns (GetDiscountReportResponse) {
        option (google.api.http) = {
            post: "/api/v2/report/discount"
            body: "*"
        };
    };
    // 查询支付报表, 按日期/门店/支付方式/支付类别 筛选, 按照日期/门店/支付方式进行聚合
    rpc GetPaymentReport (GetPaymentReportRequest) returns (GetPaymentReportResponse) {
        option (google.api.http) = {
            post: "/api/v2/report/payment"
            body: "*"
        };
    };

    // 查询单品支付报表, 按日期/门店/支付方式/商品 筛选, 按照日期/门店/商品/支付方式进行聚合
    rpc GetProductPaymentReport (GetProductPaymentReportRequest) returns (GetProductPaymentReportResponse) {
        option (google.api.http) = {
            post: "/api/v2/report/product_payment"
            body: "*"
        };
    };

    // 查询门店渠道报表, 按日期/门店/渠道类型/交付方式 筛选, 按照日期/门店/渠道/交付方式进行聚合
    rpc GetStoreChannelReport (GetStoreChannelReportRequest) returns (GetStoreChannelReportResponse) {
        option (google.api.http) = {
            post: "/api/v2/report/store_channel"
            body: "*"
        };
    };

    // 查询单品渠道报表, 按日期/门店/产品/渠道类型/交付方式 筛选, 按照日期/门店/产品/渠道/交付方式进行聚合
    rpc GetProductChannelReport (GetProductChannelReportRequest) returns (GetProductChannelReportResponse) {
        option (google.api.http) = {
            post: "/api/v2/report/product_channel"
            body: "*"
        };
    };

    // 查询门店/区域时段报表, 按 日期/区域 筛选, 按照 时段 /区域/进行聚合
    rpc GetStorePeriodReport (GetStorePeriodReportRequest) returns (GetStorePeriodReportResponse) {
        option (google.api.http) = {
            post: "/api/v2/report/store_period"
            body: "*"
        };
    };

    // 查询门店/区域 渠道时段报表, 按 日期/区域/渠道类型/交付方式 筛选, 按照 时段 /区域/ 渠道类型/ 交付方式进行聚合
    rpc GetStoreChannelPeriodReport (GetStoreChannelPeriodReportRequest) returns (GetStoreChannelPeriodReportResponse) {
        option (google.api.http) = {
            post: "/api/v2/report/store_channel_period"
            body: "*"
        };
    };

    // 查询门店/区域 渠道时段报表, 按 日期/区域/渠道类型/交付方式 筛选, 按照 时段 /区域/ 渠道类型/ 交付方式进行聚合
    rpc GetPaymentPeriodReport (GetPaymentPeriodReportRequest) returns (GetPaymentPeriodReportResponse) {
        option (google.api.http) = {
            post: "/api/v2/report/payment_period"
            body: "*"
        };
    };

    // 查询门店/区域  单品时段报表, 按 日期/区域/产品/产品类别 筛选, 按照 时段 /区域/ 产品/ 产品类别进行聚合
    rpc GetProductPeriodReport (GetProductPeriodReportRequest) returns (GetProductPeriodReportResponse) {
        option (google.api.http) = {
            post: "/api/v2/report/product_period"
            body: "*"
        };
    };

    // 查询解析失败的小票报表
    rpc GetParseFaliedETicketList (GetParseFaliedETicketListRequest) returns (GetParseFaliedETicketListResponse) {
        option (google.api.http) = {
            post: "/api/v2/report/parse_failed_e_ticket_list"
            body: "*"
        };
    };

    // 补偿重传小票信息(重建 cache)
    rpc ReParseFailedETickets (Null) returns (Result) {
        option (google.api.http) = {
            post: "/api/v2/report/re_parse_failed_e_tickets"
            body: "*"
        };
    };

    // pda 仪表盘当前门店营业信息(重建 cache)
    rpc GetStoreSalesReportForPDA (GetStoreSalesReportForPDARequest) returns (GetStoreSalesReportForPDAResponse) {
        option (google.api.http) = {
            get: "/api/v2/report/store_sales_for_pda"
        };
    };
    // 小票trace_id列表查询
    rpc GetEticketTraceIds (GetETicketTraceIdsRequest) returns (GetETicketTraceIdsResponse) {
        option (google.api.http) = {
            get: "/api/v2/report/e_ticket_trace_ids"
        };
    }
    // 建议订货前置查询接口
    rpc GetProductSalesOffDemand (GetProductSalesOffDemandRequest) returns (GetProductSalesOffDemandResponse) {
        option (google.api.http) = {
            get: "/api/v2/report/product_sales_off_demand"
        };
    }

    // 建议订货前置查询接口
    rpc GetProductSalesOffDemand2 (GetProductSalesOffDemandRequest2) returns (GetProductSalesOffDemandResponse2) {
        option (google.api.http) = {
            get: "/api/v2/report/product_sales_off_demand_2"
        };
    }

    // 查询小票分解报表
    rpc GetSalesExplainedReport (GetSalesExplainedReportRequest) returns (GetSalesExplainedReportResponse) {
        option (google.api.http) = {
            get: "/api/v2/report/sales_explained_report"
        };
    }
    // 查询小票分解报表汇总查询
    rpc GetSalesExplainedSummaryReport (GetSalesExplainedSummaryReportRequest) returns (GetSalesExplainedSummaryReportResponse) {
        option (google.api.http) = {
            post: "/api/v2/report/sales_explained_summary_report"
            body: "*"
        };
    }
    // 查询电子小票详情
    rpc GetETicketDetail (GetETicketDetailRequest) returns (GetETicketDetailResponse) {
        option (google.api.http) = {
            post: "/api/v2/report/e_ticket_detail"
            body: "*"
        };
    }
    // 销售配方拆解异常表
    rpc GetSalesExplainedErrorReport (GetSalesExplainedErrorReportRequest) returns (GetSalesExplainedErrorReportResponse) {
        option (google.api.http) = {
            get: "/api/v2/report/sales_explained_error_report"
        };
    }
    // 物料使用量报表
    rpc GetMaterialSupplies (GetMaterialSuppliesRequest) returns (GetMaterialSuppliesResponse) {
        option (google.api.http) = {
        get: "/api/v2/report/material_loss"
        };
    }
    // 查询补传小票分解报表
    rpc GetSalesExplainedSupplementReport (GetSalesExplainedReportRequest) returns (GetSalesExplainedReportResponse) {
        option (google.api.http) = {
            get: "/api/v2/report/sales_explained_supplement_report"
        };
    }
    // 查询补传小票分解报表汇总查询
    rpc GetSalesExplainedSupplementSummaryReport (GetSalesExplainedSummaryReportRequest) returns (GetSalesExplainedSummaryReportResponse) {
        option (google.api.http) = {
            post: "/api/v2/report/sales_explained_supplement_summary_report"
            body: "*"
        };
    }
    // 物料(商品)成本报表
    rpc GetMaterialCostReport (GetMaterialCostReportRequest) returns (GetMaterialCostReportResponse) {
        option (google.api.http) = {
        get: "/api/v2/report/cost/material"
        };
    }
    // 成本进销存报表(主要体现门店/仓库的商品总金额)
    rpc GetBalanceCostReport (GetBalanceCostReportRequest) returns (GetBalanceCostReportResponse){
        option (google.api.http) = {
        get: "/api/v2/report/cost/balance"
        };
    }
}
message Null {
}
message Pong {
    string msg = 1;
}
message Result {
    // 运行结果
    bool action_result = 1;
    // 附带的消息
    string message = 2;
}
message GetETicketReportRequest {
    // 查询起始日期(此日期包含在结果中)
    google.protobuf.Timestamp start_date = 1;
    // 查询结束日期(此日期结果不包含)
    google.protobuf.Timestamp end_date = 2;
    // 门店 id
    uint64 store_id = 3;
    // 支付方式 id
    repeated uint64 payment_method_ids = 4;
    // 折扣方式 id
    repeated uint64 discount_method_ids = 5;
    // 产品 id
    uint64 product_id = 6;
    // 会员卡号
    string member_card_number = 7;
    // 客户 pos 机上的订单编号
    string pos_ticket_number = 8;
    // 单据类型, order/payin/payout
    TicketType ticket_type = 9;
    // 交付方式: 堂食/外带/自提/外送
    DeliveryType delivery_type = 10;
    // 返回数据的偏移, 默认是 0
    uint64 offset = 11;
    // 返回数据的数量, 默认是 10
    int32 limit = 12;
    //hash_code
    string trace_id = 13;
    repeated string amount_legal = 14;
    // 语言
    string lan = 15;
}

enum TicketType {
    //  全部类型 ticket
    ALL_TICKET_TYPE = 0;
    //  销售订单
    ORDER = 1;
    //  payin
    PAYIN = 2;
    //  payout
    PAYOUT = 3;
    //
    CANCEL = 4;
}

enum DeliveryType {
    //  全部类型
    ALL_DELIVERY_TYPE = 0;
    // 堂食
    IN_STORE = 1;
    // 外带
    TAKE_AWAY = 2;
    // 自提
    SELF_PICK = 3;
    // 外送
    DELIVER = 4;
}

message GetETicketReportResponse {
    // 查询到的 eticket 总数
    uint64 total = 1;
    //  eticket 的数据
    repeated ETicketReport rows = 2;
}
message ETicketReport {
    // 小票在 hex server 中的 id
    uint64 id = 1;
    // 营业日期
    google.protobuf.Timestamp operation_date = 2;
    // 交易时间
    google.protobuf.Timestamp transaction_time = 3;
    // 小票上传时间
    google.protobuf.Timestamp ticket_upload_time = 4;
    // 小票类型
    TicketType ticket_type = 5;
    // 小票编号(客户系统编号)
    string pos_ticket_number = 6;
    // 门店信息
    StoreInfo store_info = 7;
    // 会员信息
    repeated MemberInfo member_info = 8;
    // 产品交付方式(堂食/外带/外送/外卖)
    DeliveryType delivery_type = 9;
    // 商品
    repeated Item products = 10;
    // 折扣 只显示整单的折扣, 单品折扣信息显示在单品中
    repeated Discount discounts = 11;
    // 销售费用
    repeated Fee fees = 12;
    // 金额
    double amount = 13;
    // 收款
    repeated Payment payments = 14;
    // 渠道
    SalesChannel sales_channel = 15;
    //外卖
    Takeway takeway = 16;
    //订单类型
    bool is_cancelled_order = 17;
    bool net_amount_legal = 18;
    bool product_amount_legal = 19;
    bool payment_amount_legal = 20;
}
message Takeway {
    string order_method = 1;
    string order_time = 2;
    string tp_order_id = 3;
    double send_fee = 4;
    double package_fee = 5;
}
message StoreInfo {
    // 门店 id
    uint64 id = 1;
    // 门店名称
    string name = 2;
    // 门店 编号
    string code = 3;
}
message MemberInfo {
    // 会员id
    uint64 member_id = 1;
    // 会员卡号
    string member_card_number = 2;
    // 会员名称
    string member_card_name = 3;
}
message Item {
    // 序号(小票上的 sequence number)
    uint32 sequence_number = 1;
    // 产品 信息
    ProductInfo product_info = 2;
    // 数量
    double quantity = 3;
    // 单价
    double price = 4;
    // 总价(不含加料)
    double amount_without_mixtures = 5;
    // 加料信息
    repeated Mixture mixtures = 6;
    // 加料的总价
    double amount_of_mixtures = 7;
    // 总价(包含加料)
    double amount_with_mixtures = 8;
    // 折扣(只显示单品折扣)
    repeated Discount item_discounts = 9;
    // 总价(去除单品折扣后的价格)
    double item_subtotal = 10;
}
message Mixture {
    // mixture 的信息
    ProductInfo product_info = 1;
    // 单价
    double price = 2;
    // 数量
    double quantity = 3;
    // 总额
    double amount = 4;
}
message Payment {
    // 支付 id
    string payment_uuid = 1;
    // 支付方式 id
    uint64 payment_method_id = 2;
    // 支付方式 code
    string payment_method_code = 3;
    // 支付方式名称
    string payment_method_name = 4;
    // 支付金额
    double amount = 5;
}
message SalesChannel {
    // id
    uint64 id = 1;
    // id
    string code = 2;
    // name
    string name = 3;
    // 是否是线上销售
    bool is_online_channel = 4;
}
message Discount {
    // 折扣方式 id
    uint64 id = 1;
    // 折扣方式 code
    string code = 2;
    // 折扣方式名称
    string name = 3;
    // 折扣金额
    double amount = 4;
}
message ProductInfo {
    // id
    uint64 id = 1;
    // 产品 code(客户 code)
    string code = 2;
    // 产品名称
    string name = 3;
    // 产品类别
    string category = 4;
}
message Fee {
    // id 暂时不从主档获取
    string id = 1;
    // name
    string name = 2;
    // price
    double price = 3;
    // quantity
    double quantity = 4;
    // amount
    double amount = 5;
}
message GetStoreSalesReportRequest {
    // 时间筛选
    PeriodFilter period_filter = 1;
    // 区域筛选
    RegionFilter region_filter = 2;
    // 日期汇总方式
    PeriodGroupMethod period_group_by = 3;
    // 区域汇总方式
    RegionGroupMethod region_group_by = 4;
    // limit
    int32 limit = 7;
    // offset
    uint64 offset = 8;
    // 语言
    string lan = 9;
}
enum PeriodGroupMethod {
    // 按天汇总
    BY_DAY = 0;
    // 按月汇总
    BY_MONTH = 1;
    // 按年汇总
    BY_YEAR = 2;
    // 按周几汇总: 礼拜天是0, 星期一是1...
    BY_DAY_OF_WEEK = 3;
    // 按季度
    BY_QUARTER = 4;
    // 按季度
    BY_WEEK = 5;
    // 按季度
    BY_HOUR = 6;
    // 按分钟
    BY_MINUTE = 7;
}
message RegionGroupMethod {
    // 区域类型
    RegionType region_type = 1;
    // 区域等级
    uint32 region_level = 2;
}
enum RegionType {
    // 门店
    STORE = 0;
    // 地理区域
    GEO_REGION = 1;
    // 分支区域
    BRANCH_REGION = 2;
}


message RegionInfo {
    // region level
    uint32 region_level = 1;
    // region id
    uint64 region_id = 2;
    // region name
    string region_name = 3;
}
enum StoreType {
    // 全部
    ALL_STORE = 0;
    // 直营店
    DRS = 1;
    // 加盟店
    FRS = 2;
}
message GetStoreSalesReportReponse {
    // 报表总数
    uint64 total = 1;
    // 报表总计信息
    TotalStoreSales summary = 2;
    // 明细数据
    repeated StoreSalesRecord rows = 3;
}
message TotalStoreSales {
    // 总条数
    int64 total_record_count = 1;
    // 总销售总额
    double gross_sales_amount = 2;
    // 总销售净额
    double net_sales_amount = 3;
    // 总折扣金额
    double discount_amount = 4;
    // 总销售费用
    double fee_amount = 5;
    // 总销售次数(不包含退单)
    double sales_count = 6;
    // 平均每单净额
    double average_net_sales_amount = 7;
    //退单数量”、
    double return_quantity = 11;
    //“退单净额
    double return_amount = 12;
}
message StoreSalesRecord {
    // 时间汇总字段 如按年 2018, 按月2018-02
    string period_symbol = 1;
    // 如果是按照区域来聚合, 区域的 level, 例子: level=2, 有的纪录显示 全市场 ->华东 -> 上海, 有的纪录显示全市场 -> 新家坡 -> -
    uint32 region_group_level = 2;
    // 区域类型
    RegionType region_type = 3;
    // 区域汇总字段, 如按门店汇总, 是门店名字, 如果按区域汇总, 是区域名字
    repeated RegionMetaInfo region_meta_info = 4;
    // 销售总额
    double gross_sales_amount = 5;
    // 销售净额
    double net_sales_amount = 6;
    // 折扣金额
    double discount_amount = 7;
    // 销售费用
    double fee_amount = 8;
    // 销售次数
    double sales_count = 9;
    // 平均每单净额
    double average_net_sales_amount = 10;
    //退单数量”、
    double return_quantity = 11;
    //“退单净额
    double return_amount = 12;
}
message GetProductSalesReportRequest {
    // 时间筛选
    PeriodFilter period_filter = 1;
    // 区域筛选
    RegionFilter region_filter = 2;
    // 产品筛选
    repeated ProductFilter product_filters = 3;
    // 销售渠道筛选
    repeated SalesChannelFilter sales_channel_filters = 4;
    // 日期汇总方式
    PeriodGroupMethod period_group_by = 5;
    // 区域汇总方式
    RegionGroupMethod region_group_by = 6;
    // 产品汇总方式
    ProductGroupMethod product_group_by = 7;
    // 销售渠道汇总
    SalesChannelGroupMethod sales_channel_group_by = 8;
    // limit
    int64 limit = 9;
    // offset
    uint64 offset = 10;
    //语言
    string lan = 11;
}
message PeriodFilter {
    // 开始时间
    google.protobuf.Timestamp start_date = 1;
    // 查询结束日期(此日期结果不包含)
    google.protobuf.Timestamp end_date = 2;
}
message RegionFilter {
    // 区域类型
    RegionType type = 1;
    // 区域层级
    uint32 level = 2;
    // 区域 id
    repeated uint64 ids = 3;
}
message ProductFilter {
    // filter 类型
    ProductFilterType type = 1;
    // filter 的 ids
    repeated uint64 ids = 2;
    // category 的 level, filter_by_category 时候需要
    uint32 level = 3;
}
enum ProductFilterType {
    // 按产品 id 筛选
    FILTER_BY_PRODUCT = 0;
    // 按产品类别 id 筛选
    FILTER_BY_CATEGORY = 1;
}
message ProductGroupMethod {
    // 按照 产品还是 产品类别来聚合数据
    ProductGroupBy group_by = 1;
    // 如果是按照产品类别聚合, 须指明产品类别的等级(如0级是全品类, 1级是全品大类, 2级是饮料, 3级是热饮, 再下一级就是产品本身)
    uint32 category_level = 2;
}
message SalesChannelFilter {
    //暂时只能按照 id 来筛选(没有决定 sales_channel 的层级)
    uint64 sales_channel_id = 1;
}
enum SalesChannelGroupMethod {
    // 暂时只有一个层级
    // 不按照 SalesChannelGroup 来分组
    ALL_CHANNELS = 0;
    // 按照 sales_channel_id 来分组
    BY_CHANNEL_ID = 1;
}
enum ProductGroupBy {
    // 按照产品聚合
    GROUP_BY_PRODUCT = 0;
    // 按照类别聚合
    GROUP_BY_CATEGORY = 1;
}
message GetProductSalesReportResponse {
    // 总计信息
    ProductSalesTotal summary = 1;
    // 详细信息
    repeated ProductSalesRecord rows = 2;
    // 总记录条数
    uint64 total = 3;
}
message ProductSalesTotal {
    // 总记录数
    int64 record_count = 1;
    // 销量总数
    int64 total_sales_quantity = 2;
    // 销量净额
    double net_sales_amount = 3;
    // 折扣总额
    double discount_amount = 4;
    // 净均价
    double average_price_net = 5;
}
message ProductSalesRecord {
    // 时间汇总字段 如按年 2018, 按月2018-02
    string period_symbol = 1;
    // 如果是按照区域来聚合, 区域的 level, 例子: level=2, 有的纪录显示 全市场 ->华东 -> 上海, 有的纪录显示全市场 -> 新家坡 -> -
    uint32 region_group_level = 2;
    // 区域类型
    RegionType region_type = 3;
    // 区域汇总字段, 如按门店汇总, 是门店名字, 如果按区域汇总, 是区域名字
    repeated RegionMetaInfo region_meta_info = 4;
    // 如果是按照产品类别来聚合, 类别的 level, 例子: level=2, 有的纪录显示 全品类 ->饮料 -> 热饮, 有的纪录显示全市场 -> 面包 -> -
    uint32 product_group_level = 5;
    // 区域类型
    ProductGroupBy product_group_type = 6;
    // 区域汇总字段, 如按门店汇总, 是门店名字, 如果按区域汇总, 是区域名字
    repeated ProductMetaInfo product_meta_infos = 7;
    // 如果按照某个销售渠道聚合, 此字段显示销售渠道名字, 否则为空
    string sales_channel_symbol = 8;
    // 销量
    int32 quantity = 9;
    // 销量占比
    double quantity_percentage = 10;
    // 净销售额
    double net_amount = 11;
    // 净销售额占比
    double net_amount_percentage = 12;
    // 均价
    double average_net_price = 13;
    // 折扣金额
    double discount_amount = 14;
}

message GetDiscountReportRequest {
    // 时间筛选
    PeriodFilter period_filter = 1;
    // 区域筛选
    RegionFilter region_filter = 2;
    // 产品筛选
    repeated DiscountFilter discount_filters = 3;
    // 日期汇总方式
    PeriodGroupMethod period_group_by = 4;
    // 区域汇总方式
    RegionGroupMethod region_group_by = 5;
    // 折扣汇总方式
    DiscountGroupMethod discount_group_by = 6;
    // 如果按照折扣类别汇总, 折扣类别的 level
    uint32 category_group_level = 7;
    // limit
    int64 limit = 8;
    // offset
    uint64 offset = 9;
    // 语言
    string lan = 10;
}
message DiscountFilter {
    // 是按照 折扣类别还是折扣方式进行筛选
    DiscountFilterBy type = 1;
    // 折扣类别 id/ 折扣方式 id
    repeated uint64 ids = 2;
    // 如果是按照类别筛选, 类别的深度
    uint32 level = 3;
}
enum DiscountFilterBy {
    // 按照折扣方式筛选
    FILTER_BY_DISCOUNT_METHOD = 0;
    // 按照折扣类别筛选
    FILTER_BY_DISCOUNT_CATEGORY = 1;
    // 按照折扣 TYPE 筛选
    FILTER_BY_DISCOUNT_TYPE = 2;
}
enum DiscountGroupMethod {
    // 按照折扣方式聚合
    GROUP_BY_DISCOUNT_METHOD = 0;
    // 按照折扣类别聚合
    GROUP_BY_DISCOUNT_CATEGORY = 1;
    // 按照 折扣类型聚合
    GROUP_BY_DISCOUNT_TYPE = 2;
}
message GetDiscountReportResponse {
    // 总计信息
    DiscountTotal summary = 1;
    // 详细信息
    repeated DiscountRecord rows = 2;
    // 总条数
    uint64 total = 3;
}
message DiscountTotal {
    // 总记录数
    int64 record_count = 1;
    // 折扣总次数
    int64 quantity = 2;
    // 折扣总额
    double amount = 3;
    // 取消订单中的折扣总额
    double discount_amount_of_canceled_order = 4;
}
message DiscountRecord {
    // 时间汇总字段 如按年 2018, 按月2018-02
    string period_symbol = 1;
    // 如果是按照区域来聚合, 区域的 level, 例子: level=2, 有的纪录显示 全市场 ->华东 -> 上海, 有的纪录显示全市场 -> 新家坡 -> -
    uint32 region_group_level = 2;
    // 区域类型
    RegionType region_type = 3;
    // 区域汇总字段, 如按门店汇总, 是门店名字, 如果按区域汇总, 是区域名字
    repeated RegionMetaInfo region_meta_info = 4;
    // 折扣汇总字段, 如果是按折扣方式汇总, 是折扣方式的名字, 如果是按照折扣类别汇总, 是这折扣类别的名字
    string discount_symbol = 5;
    // 折扣方式/折扣类别 的编码
    string discount_symbol_code = 6;
    // 折扣次数
    int64 quantity = 7;
    // 净折扣额
    double amount = 8;
    // 取消订单中的折扣金额
    double discount_amount_of_canceled_order = 9;
}
message GetPaymentReportRequest {
    // 时间筛选
    PeriodFilter period_filter = 1;
    // 区域筛选
    RegionFilter region_filter = 2;
    // 支付方式筛选
    repeated PaymentFilter payment_filters = 3;
    // 日期汇总方式
    PeriodGroupMethod period_group_by = 4;
    // 区域汇总方式
    RegionGroupMethod region_group_by = 5;
    // 支付汇总方式
    PaymentGroupMethod payment_group_by = 6;
    // limit
    int64 limit = 7;
    // offset
    uint64 offset = 8;
    // 语言
    string lan = 9;
}
message PaymentFilter {
    // 是按照 支付类别还是支付方式进行筛选
    PaymentFilterBy type = 1;
    // 支付类别 id/ 支付方式 id
    repeated uint64 ids = 2;
    // 如果是按照类别聚合, 类别的层级
    uint32 level = 3;
}
enum PaymentFilterBy {
    // 按照支付方式来筛选
    FILTER_BY_PAYMENT_METHOD = 0;
    // 按照支付类别筛选
    FILTER_BY_PAYMENT_CATEGORY = 1;
}
message PaymentGroupMethod {
    // 是按照类别还是方式来聚合
    PaymentGroupBy by = 1;
    // 如果按照支付类别聚合, 支付类别的level
    uint32 level = 2;
}
enum PaymentGroupBy {
    // 按照支付方式聚合
    GROUP_BY_PAYMENT_METHOD = 0;
    // 按照支付类别聚合
    GROUP_BY_PAYMENT_CATEGORY = 1;
}
message GetPaymentReportResponse {
    // 总计信息
    PaymentTotal summary = 1;
    // 详细信息
    repeated PaymentRecord rows = 2;
    // 记录条数
    uint64 total = 3;
}
message PaymentTotal {
    // 总记录数
    int64 record_count = 1;
    // 支付总次数
    int64 quantity = 2;
    // 支付总额
    double amount = 3;
    // 支付费用总计
    double fee = 4;
    // 实际收款总计
    double net_amount = 5;
}
message PaymentRecord {
    // 时间汇总字段 如按年 2018, 按月2018-02
    string period_symbol = 1;
    // 如果是按照区域来聚合, 区域的 level, 例子: level=2, 有的纪录显示 全市场 ->华东 -> 上海, 有的纪录显示全市场 -> 新家坡 -> -
    uint32 region_group_level = 2;
    // 区域类型
    RegionType region_type = 3;
    // 区域汇总字段, 如按门店汇总, 是门店名字, 如果按区域汇总, 是区域名字
    repeated RegionMetaInfo region_meta_info = 4;
    // 如果是按照支付类别聚合, 聚合类别的 level, 前端依此决定来决定支付类别显示多少级, 例子:level=3, 有的纪录显示 现金-> - -> - , 有的纪录显示 第三方 -> 微信 -> 微信公众号
    uint32 payment_group_level = 5;
    // 使用支付方式聚合还是支付类别聚合
    PaymentGroupBy payment_group_by = 6;
    // 支付汇总字段, 如果是按支付方式汇总, 是支付方式的名字, 如果是按照支付类别汇总, 是支付类别的名字
    repeated PaymentMetaInfo payment_meta_infos = 7;
    // 支付次数
    int64 quantity = 8;
    // 支付总额
    double amount = 9;

    double amount_percentage = 10;
    // 支付费用/支付方式绑定的折扣(实际收到的钱少了)
    double fee = 11;
    // 收款总额
    double net_amount = 12;
    double net_amount_percentage = 13;
}
message PaymentMetaInfo {
    // 按照支付类别聚合时类别的深度, 例: 0: 全支付方式, 1: 第三方支付, 2: 微信支付, 3: 微信小程序支付/公众号支付
    uint32 level = 2;
    // 支付方式/类别的名字
    string name = 3;
    // 支付方式/类别的编码
    string code = 4;
}
message GetProductPaymentReportRequest {
    // 时间筛选
    PeriodFilter period_filter = 1;
    // 区域筛选
    RegionFilter region_filter = 2;
    // 支付方式筛选
    repeated PaymentFilter payment_filters = 3;
    // 商品筛选
    repeated ProductFilter product_filters = 4;
    // 日期汇总方式
    PeriodGroupMethod period_group_by = 5;
    // 区域汇总方式
    RegionGroupMethod region_group_by = 6;
    // 支付汇总方式
    PaymentGroupMethod payment_group_by = 7;
    // 产品汇总方式
    ProductGroupMethod product_group_by = 8;
    // limit
    int64 limit = 9;
    // offset
    uint64 offset = 10;
    // 语言
    string lan = 11;
}
message GetProductPaymentReportResponse {
    // 纪录条数
    uint64 total = 1;
    // 纪录明细
    repeated ProductPaymentRecord rows = 2;
    //
    TotalStoreSales summary = 3;
}
message ProductPaymentRecord {
    // 时间汇总字段 如按年 2018, 按月2018-02
    string period_symbol = 1;
    // 区域聚合的层级(例如0是全区域, 1是华东, 2是上海, 3是浦东)
    uint32 region_group_level = 2;
    // 区域聚合方式
    RegionGroupMethod region_group_by = 3;
    // 区域汇总字段, 如按门店汇总, 是门店名字, 如果按区域汇总, 是区域及其祖先区域的名字
    repeated RegionMetaInfo region_meta_info = 4;
    // 如果是按照支付类别聚合, 聚合类别的 level, 前端依此决定来决定支付类别显示多少级, 例子:level=3, 有的纪录显示 现金-> - -> - , 有的纪录显示 第三方 -> 微信 -> 微信公众号
    uint32 payment_group_level = 5;
    // 使用支付方式聚合还是支付类别聚合
    PaymentGroupBy payment_group_by = 6;
    // 支付汇总字段, 如果是按支付方式汇总, 是支付方式的名字, 如果是按照支付类别汇总, 是支付类别及其全部先类的名字
    repeated PaymentMetaInfo payment_meta_infos = 7;
    // 如果是按照产品类别来聚合, 类别的 level, 例子: level=2, 有的纪录显示 全品类 ->饮料 -> 热饮, 有的纪录显示全市场 -> 面包 -> -
    uint32 product_group_level = 8;
    // 区域类型
    ProductGroupBy product_group_type = 9;
    // 区域汇总字段, 如按门店汇总, 是门店名字, 如果按区域汇总, 是区域名字
    repeated ProductMetaInfo product_meta_infos = 10;
    // 销售总净额
    double net_amount = 11;
    // 销售总数量
    int64 sales_quantity = 12;
    // 销售笔数
    int64 transaction_count = 13;
    // 平均每笔金额
    double average_amount_per_transaction = 14;
    // 净额占比
    double net_amount_percentage = 15;
    // 销量占比
    double quantity_percentage = 16;
}
message RegionMetaInfo {
    // 区域的层级 0 代表全市场
    uint32 level = 2;
    // 区域的名字
    string name = 3;
    // 区域的 code
    string code = 4;
}
message ProductMetaInfo {
    // 产品的层级 0 代表全品类
    uint32 level = 2;
    // 产品 / 类别的名字
    string name = 3;
    // 产品 / 类别的 code
    string code = 4;
}
message GetStoreChannelReportRequest {
    // 时间筛选
    PeriodFilter period_filter = 1;
    // 区域筛选
    RegionFilter region_filter = 2;
    // 支付方式筛选
    repeated SalesChannelFilter sales_channel_filters = 3;
    // 商品筛选
    repeated DeliveryType delivery_type_filters = 4;
    // 日期汇总方式
    PeriodGroupMethod period_group_by = 5;
    // 区域汇总方式
    RegionGroupMethod region_group_by = 6;
    // 是否按渠道分组
    bool partition_by_sales_channel = 7;
    // 是否按交付方式分组
    bool partition_by_delivery_type = 8;
    // limit
    int64 limit = 9;
    // offset
    uint64 offset = 10;
    // 语言
    string lan = 11;

}
enum DeliveryTypeGroupMethod {
    // 暂时只有一个层级 按 delivery tiype 聚合还是不按照 delivery type 聚合
    // 不按照 delivery type 来分组
    ALL_DELIVERY_TYPES = 0;
    // 按照 sales_channel_id 来分组
    BY_DELIVERY_TYPE_ID = 1;

}
message GetStoreChannelReportResponse {
    // 总计信息
    StoreChannelSalesTotal summary = 1;
    // 详细信息
    repeated StoreChanneSalesRecord rows = 2;
    //纪录总数
    int64 total = 3;
}

message StoreChannelSalesTotal {
    // 总记录数
    int64 record_count = 1;
    // 总交易笔数
    int64 transaction_count = 2;
    // 总销售净额
    double net_amount = 3;
    // 平均每单销售净额
    double average_amount_per_transaction = 4;
}
message StoreChanneSalesRecord {
    // 时间汇总字段 如按年 2018, 按月2018-02
    string period_symbol = 1;
    // 区域聚合的层级(例如0是全区域, 1是华东, 2是上海, 3是浦东)
    uint32 region_group_level = 2;
    // 区域聚合方式
    RegionGroupMethod region_group_by = 3;
    // 区域汇总字段, 如按门店汇总, 是门店名字, 如果按区域汇总, 是区域及其祖先区域的名字
    repeated RegionMetaInfo region_meta_info = 4;
    // 渠道名字,如果是按照渠道分组, 则为渠道名字, 否则为空
    string sales_channel_symbol = 5;
    // 交付方式的名字, 如果按照交付方式分组, 则为 堂食/外送/外卖/外带.. 否则为空
    string delivery_type_symbol = 6;
    // 销售笔数
    int64 transaction_count = 7;
    // 销售笔数在该区域/当前时间周期占比
    double transaction_count_percentage = 8;
    // 销售额
    double net_amount = 9;
    // 销售额在该区域/当前时间周期占比
    double net_amount_percentage = 10;
    // 平均每单销售额
    double average_amount_per_transaction = 11;
}
message GetProductChannelReportRequest {
    // 时间筛选
    PeriodFilter period_filter = 1;
    // 区域筛选
    RegionFilter region_filter = 2;
    // 产品筛选
    repeated ProductFilter product_filters = 3;
    // 销售渠道筛选
    repeated SalesChannelFilter sales_channel_filters = 4;
    // 交付方式筛选
    repeated DeliveryType delivery_type_filters = 5;
    // 日期汇总方式
    PeriodGroupMethod period_group_by = 6;
    // 区域汇总方式
    RegionGroupMethod region_group_by = 7;
    // 产品汇总方式
    ProductGroupMethod product_group_by = 8;
    // 是否按渠道分组
    bool partition_by_sales_channel = 9;
    // 是否按交付方式分组
    bool partition_by_delivery_type = 10;
    // limit
    int64 limit = 11;
    // offset
    uint64 offset = 12;
    // 语言
    string lan = 13;

}
message GetProductChannelReportResponse {
    // 总计信息
    ProductChannelSalesTotal summary = 1;
    // 详细信息
    repeated ProductChanneSalesRecord rows = 2;
    // 纪录总数
    uint64 total = 3;
}
message ProductChannelSalesTotal {
    // 总记录数
    int64 record_count = 1;
    // 总销量
    int64 quantity = 2;
    // 总销售净额
    double net_amount = 3;
    // 平均单价
    double average_net_price = 4;
}
message ProductChanneSalesRecord {
    // 时间汇总字段 如按年 2018, 按月2018-02
    string period_symbol = 1;
    // 区域聚合的层级(例如0是全区域, 1是华东, 2是上海, 3是浦东)
    uint32 region_group_level = 2;
    // 区域聚合方式
    RegionGroupMethod region_group_by = 3;
    // 区域汇总字段, 如按门店汇总, 是门店名字, 如果按区域汇总, 是区域及其祖先区域的名字
    repeated RegionMetaInfo region_meta_info = 4;
    // 如果是按照产品类别来聚合, 类别的 level, 例子: level=2, 有的纪录显示 全品类 ->饮料 -> 热饮, 有的纪录显示全市场 -> 面包 -> -
    uint32 product_group_level = 5;
    // 产品聚合类型
    ProductGroupBy product_group_type = 6;
    // 产品汇总字段, 如按单品汇总, 是单品名字, 如果按类别汇总, 是类别名字
    repeated ProductMetaInfo product_meta_infos = 7;
    // 渠道名字,如果是按照渠道分组, 则为渠道名字, 否则为空
    string sales_channel_symbol = 8;
    // 交付方式的名字, 如果按照交付方式分组, 则为 堂食/外送/外卖/外带.. 否则为空
    string delivery_type_symbol = 9;
    // 销量
    int64 quantity = 10;
    // 销量在该区域/当前时间周期/当前产品占比
    double quantity_percentage = 11;
    // 销售额
    double net_amount = 12;
    // 销售额在该区域/当前时间周期/当前产品占比(不同渠道占比)
    double net_amount_percentage = 13;
    // 平均单价
    double average_net_price = 14;
}
message GetStorePeriodReportRequest {
    // 时间筛选
    PeriodFilter period_filter = 1;
    // 区域筛选
    RegionFilter region_filter = 2;
    // 日期汇总方式
    PeriodGroupMethod period_group_by = 3;
    // 区域汇总方式
    RegionGroupMethod region_group_by = 4;
    // limit
    int64 limit = 5;
    // offset
    uint64 offset = 6;
    google.protobuf.Timestamp end_time = 7;
    // 语言
    string lan = 8;
}
message GetStorePeriodReportResponse {
    // 总计信息
    PeriodTotal summary = 1;
    // 详细信息
    repeated StorePeriodRecord rows = 2;
    // 纪录条数
    int64 total = 3;
}
message PeriodTotal {
    // 总计纪录数
    int64 total_count = 1;
    // 总交易笔数
    int64 transaction_count = 2;
    // 总销售额
    double net_income = 3;
    // 每个时段的销售笔数, 返回json格式字典, 键: 如果是按小时聚合, 则是'1', '2'.., 如果是按照周几聚合, 则'0': 周日, '1': 周一...以此类推, 值是该时段笔数
    string period_transaction_count = 4;
    // 每个时段的销售额, 返回json格式字典, 键: 如果是按小时聚合, 则是'1', '2'.., 如果是按照周几聚合, 则'0': 周日, '1': 周一...以此类推, 值是该时段金额
    string period_net_income = 5;
}
message StorePeriodRecord {
    // 区域聚合的层级(例如0是全区域, 1是华东, 2是上海, 3是浦东)
    uint32 region_group_level = 1;
    // 区域聚合方式
    RegionGroupMethod region_group_by = 2;
    // 区域汇总字段, 如按门店汇总, 是门店名字, 如果按区域汇总, 是区域及其祖先区域的名字
    repeated RegionMetaInfo region_meta_info = 3;
    // 区域总计净额
    double net_income = 4;
    // 区域总计笔数
    double transaction_count = 5;
    // 每个时段的销售净额, 返回json格式字典, 键: 如果是按小时聚合, 则是'1', '2'.., 如果是按照周几聚合, 则'0': 周日, '1': 周一...以此类推, 值是该时段金额
    string period_net_income = 6;
    // 每个时段的销售笔数, 返回json格式字典, 键: 如果是按小时聚合, 则是'1', '2'.., 如果是按照周几聚合, 则'0': 周日, '1': 周一...以此类推, 值是该时段笔数
    string period_transaction_count = 7;
}
message GetStoreChannelPeriodReportRequest {
    // 时间筛选
    PeriodFilter period_filter = 1;
    // 区域筛选
    RegionFilter region_filter = 2;
    // 销售渠道筛选
    repeated SalesChannelFilter sales_channel_filters = 3;
    // 交付方式筛选
    repeated DeliveryType delivery_type_filters = 4;
    // 日期汇总方式
    PeriodGroupMethod period_group_by = 5;
    // 区域汇总方式
    RegionGroupMethod region_group_by = 6;
    // 是否按渠道分组
    bool partition_by_sales_channel = 7;
    // 是否按交付方式分组
    bool partition_by_delivery_type = 8;
    // limit
    int64 limit = 9;
    // offset
    uint64 offset = 10;
    // 语言
    string lan = 11;
}
message GetStoreChannelPeriodReportResponse {
    // 总计信息
    PeriodTotal summary = 1;
    // 详细信息
    repeated StoreChannelPeriodRecord rows = 2;
    // 纪录条数
    int64 total = 3;
}
message StoreChannelPeriodRecord {
    // 区域聚合的层级(例如0是全区域, 1是华东, 2是上海, 3是浦东)
    uint32 region_group_level = 2;
    // 区域聚合方式
    RegionGroupMethod region_group_by = 3;
    // 区域汇总字段, 如按门店汇总, 是门店名字, 如果按区域汇总, 是区域及其祖先区域的名字
    repeated RegionMetaInfo region_meta_info = 4;
    // 渠道名字,如果是按照渠道分组, 则为渠道名字, 否则为空
    string sales_channel_symbol = 5;
    // 交付方式的名字, 如果按照交付方式分组, 则为 堂食/外送/外卖/外带.. 否则为空
    string delivery_type_symbol = 6;
    // 区域总计净额
    double net_income = 7;
    // 区域总计笔数
    int64 transaction_count = 8;
    // 每个时段的销售笔数, 返回json格式字典, 键: 如果是按小时聚合, 则是'1', '2'.., 如果是按照周几聚合, 则'0': 周日, '1': 周一...以此类推, 值是该时段金额
    string period_net_income = 9;
    // 每个时段的销售笔数, 返回json格式字典, 键: 如果是按小时聚合, 则是'1', '2'.., 如果是按照周几聚合, 则'0': 周日, '1': 周一...以此类推, 值是该时段笔数
    string period_transaction_count = 10;
}
message GetPaymentPeriodReportRequest {
    // 时间筛选
    PeriodFilter period_filter = 1;
    // 区域筛选
    RegionFilter region_filter = 2;
    // 支付方式筛选
    repeated PaymentFilter payment_filters = 3;
    // 日期汇总方式
    PeriodGroupMethod period_group_by = 4;
    // 区域汇总方式
    RegionGroupMethod region_group_by = 5;
    // 支付汇总方式
    PaymentGroupMethod payment_group_by = 6;
    // limit
    int64 limit = 7;
    // offset
    uint64 offset = 8;
    // 语言
    string lan = 9;
}
message GetPaymentPeriodReportResponse {
    // 总计信息
    PeriodTotal summary = 1;
    // 详细信息
    repeated PaymentPeriodRecord rows = 2;
    // 总计条目数
    int64 total = 3;
}
message PaymentPeriodRecord {
    // 区域聚合的层级(例如0是全区域, 1是华东, 2是上海, 3是浦东)
    uint32 region_group_level = 1;
    // 区域聚合方式
    RegionGroupMethod region_group_by = 2;
    // 区域汇总字段, 如按门店汇总, 是门店名字, 如果按区域汇总, 是区域及其祖先区域的名字
    repeated RegionMetaInfo region_meta_info = 3;
    // 如果是按照支付类别聚合, 聚合类别的 level, 前端依此决定来决定支付类别显示多少级, 例子:level=3, 有的纪录显示 现金-> - -> - , 有的纪录显示 第三方 -> 微信 -> 微信公众号
    uint32 payment_group_level = 4;
    // 使用支付方式聚合还是支付类别聚合
    PaymentGroupBy payment_group_by = 5;
    // 支付汇总字段, 如果是按支付方式汇总, 是支付方式的名字, 如果是按照支付类别汇总, 是支付类别的名字
    repeated PaymentMetaInfo payment_meta_infos = 6;
    // 区域总计净额
    double net_income = 7;
    // 区域总计笔数
    int64 transaction_count = 8;
    // 每个时段的销售笔数, 返回json格式字典, 键: 如果是按小时聚合, 则是'1', '2'.., 如果是按照周几聚合, 则'0': 周日, '1': 周一...以此类推, 值是该时段金额
    string period_net_income = 9;
    // 每个时段的销售笔数, 返回json格式字典, 键: 如果是按小时聚合, 则是'1', '2'.., 如果是按照周几聚合, 则'0': 周日, '1': 周一...以此类推, 值是该时段笔数
    string period_transaction_count = 10;
}
message GetProductPeriodReportRequest {
    // 时间筛选
    PeriodFilter period_filter = 1;
    // 区域筛选
    RegionFilter region_filter = 2;
    // 支付方式筛选
    repeated ProductFilter product_filters = 3;
    // 日期汇总方式
    PeriodGroupMethod period_group_by = 4;
    // 区域汇总方式
    RegionGroupMethod region_group_by = 5;
    // 支付汇总方式
    ProductGroupMethod product_group_by = 6;
    // limit
    int64 limit = 7;
    // offset
    uint64 offset = 8;
    // 语言
    string lan = 9;

}
message GetProductPeriodReportResponse {
    // 总计信息
    PeriodTotal summary = 1;
    // 详细信息
    repeated ProductPeriodRecord rows = 2;
    // 纪录总数
    int64 total = 3;
}
message ProductPeriodRecord {
    // 区域聚合的层级(例如0是全区域, 1是华东, 2是上海, 3是浦东)
    uint32 region_group_level = 1;
    // 区域聚合方式
    RegionGroupMethod region_group_by = 2;
    // 区域汇总字段, 如按门店汇总, 是门店名字, 如果按区域汇总, 是区域及其祖先区域的名字
    repeated RegionMetaInfo region_meta_info = 3;
    // 如果是按照产品类别来聚合, 类别的 level, 例子: level=2, 有的纪录显示 全品类 ->饮料 -> 热饮, 有的纪录显示全市场 -> 面包 -> -
    uint32 product_group_level = 5;
    // 产品聚合类型
    ProductGroupBy product_group_type = 6;
    // 产品汇总字段, 如按单品汇总, 是单品名字, 如果按类别汇总, 是类别名字
    repeated ProductMetaInfo product_meta_infos = 7;
    // 区域总计净额
    double net_income = 8;
    // 区域总计笔数
    int64 transaction_count = 9;
    // 每个时段的销售笔数, 返回json格式字典, 键: 如果是按小时聚合, 则是'1', '2'.., 如果是按照周几聚合, 则'0': 周日, '1': 周一...以此类推, 值是该时段金额
    string period_net_income = 10;
    // 每个时段的销售笔数, 返回json格式字典, 键: 如果是按小时聚合, 则是'1', '2'.., 如果是按照周几聚合, 则'0': 周日, '1': 周一...以此类推, 值是该时段笔数
    string period_transaction_count = 11;
}
message GetParseFaliedETicketListRequest {
    // 时间筛选
    PeriodFilter period_filter = 1;
    // 区域筛选
    RegionFilter region_filter = 2;
    // limit
    int64 limit = 9;
    // offset
    uint64 offset = 10;
    // 语言
    string lan = 11;
}
message GetParseFaliedETicketListResponse {
    // 解析失败的小票总条数
    int64 total_count = 1;
    // 小票的纪录
    repeated ParseFailedETicket tickets = 2;
}
message ParseFailedETicket {
    // 在 wrong pos 表中的纪录 id
    uint64 id = 1;
    // 客户的订单标识 id
    string customer_order_id = 2;
    // 门店名称
    string store_name = 3;
    // 净额
    double net_amount = 4;
    // 解析失败原因
    string reason_of_failure = 5;
}
message GetStoreSalesReportForPDARequest {
    // 门店 id
    uint64 store_id = 1;
    // 日期
    google.protobuf.Timestamp operation_date = 2;
}
message GetStoreSalesReportForPDAResponse {
    // 门店 id
    uint64 store_id = 1;
    // 营业日期
    google.protobuf.Timestamp operation_date = 2;
    // 门店当天销售毛额
    double gross_sales_amount = 3;
    // 门店当天销售净额
    double net_sales_amount = 4;
    // 门店当天退货金额
    double refund_amount = 5;
    // 门店当天折扣金额
    double discount_amount = 6;
    // 交易笔数
    double transaction_count = 7;
    // 平均每单净额
    double average_amount_per_transaction = 8;
    // 平均每单数量
    double average_quantity_per_transaction = 9;
    // 销售渠道金额
    repeated ChannelSalesAmount channel_sales = 10;
}
message ChannelSalesAmount {
    // 渠道名字
    string channel_name = 1;
    // 销售额
    double net_amount = 2;
}
message GetETicketTraceIdsRequest {
    string pos_ticket_number = 1;
    uint64 branch_id = 2;
    repeated uint64 product_ids = 3;
    google.protobuf.Timestamp start_date = 5;
    // 查询结束日期(此日期结果不包含)
    google.protobuf.Timestamp end_date = 6;
    uint64 offset = 7;
    // 返回数据的数量, 默认是 10
    int32 limit = 8;
    repeated uint64 trace_ids = 9;
    // 语言
    string lan = 10;
}
message PosTicketNumber {
    string pos_ticket_number = 1;
    uint64 trace_id = 2;
}
message GetETicketTraceIdsResponse {
    repeated string trace_ids = 3;
    repeated PosTicketNumber pos_info = 4;
}
message GetProductSalesOffDemandRequest {
    google.protobuf.Timestamp start_date = 1;
    google.protobuf.Timestamp end_date = 2;
    repeated uint64 product_ids = 3;
    uint64 store_id = 4;
    string lan = 5;
}
message GetProductSalesOffDemandRequest2 {
    uint64 store_id = 1;
    repeated uint64 timestamps = 2;
    repeated uint64 product_ids = 3;
}

message GetProductSalesOffDemandResponse2 {
    message SaleInfo {
        uint64 product_id = 1;
        uint64 timestamp = 2;
        int64 quantity = 3;
    }
    GetProductSalesOffDemandRequest2 request = 1;
    repeated SaleInfo sale_infos = 2;
}


message ProductsSalesInfo {
    uint64 product_id = 1;
    // 时间段内总销量
    double quantity = 2;
    // 该单品对应end_date最后一笔时间
    google.protobuf.Timestamp sales_end_date = 3;
    // 时间段内平均销量
    double average_quantity = 4;
}
message GetProductSalesOffDemandResponse {
    uint64 store_id = 1;
    repeated ProductsSalesInfo products_sales_info = 2;
}
message GetSalesExplainedReportRequest {
    //门店|商品|日期|销售流水号|原料
    repeated uint64 store_id = 1;
    repeated uint64 product_id = 2;
    google.protobuf.Timestamp start_date = 3;
    google.protobuf.Timestamp end_date = 4;
    string pos_ticket_number = 5;
    repeated uint64 bom_product_id = 7;
    uint64 offset = 8;
    // 返回数据的数量, 默认是 10
    int64 limit = 9;
    string is_unable = 10;
    //语言
    string lan = 11;
}
message SalesExplainedReportProperties {
    uint64 property_key_id = 1;
    string property_key_code = 2;
    string property_key_name = 3;
}
message SalesExplainedReport {
    //门店编码|门店名称|商品名称|商品编码|销售数量|销售流水号|原料名称|原料编码|拆解数量|核算单位
    uint64 batch_id = 1;
    uint64 e_ticket_id = 2;
    string store_code = 3;
    string store_name = 4;
    uint64 store_id = 5;
    uint64 product_id = 6;
    string product_code = 7;
    string product_name = 8;
    //核算单位
    uint64 accounting_unit_id = 9;
    string accounting_unit_code = 10;
    string accounting_unit_name = 11;
    double product_qty = 12;
    string pos_ticket_number = 13;
    uint64 bom_product_id = 14;
    string bom_product_code = 15;
    string bom_product_name = 16;
    //bom核算单位
    uint64 bom_accounting_unit_id = 17;
    string bom_accounting_unit_code = 18;
    string bom_accounting_unit_name = 19;
    double qty = 20;
    google.protobuf.Timestamp sales_date = 21;
    bool is_unable = 22;
    repeated SalesExplainedReportProperties properties = 23;
    double price = 24;
    double cost = 25;
}
message GetSalesExplainedReportResponse {
    repeated SalesExplainedReport rows = 1;
    uint64 total = 2;
}
message GetSalesExplainedSummaryReportRequest {
    // 时间筛选
    PeriodFilter period_filter = 1;
    // 区域筛选
    RegionFilter region_filter = 2;
    // 产品筛选
    repeated ProductFilter product_filters = 3;
    // 产品筛选
    repeated ProductFilter bom_product_filters = 4;
    // 日期汇总方式
    PeriodGroupMethod period_group_by = 5;
    // 区域汇总方式
    RegionGroupMethod region_group_by = 6;
    // 产品汇总方式
    ProductGroupMethod product_group_by = 7;
    // bom产品汇总方式
    ProductGroupMethod bom_product_group_by = 8;
    // limit
    int64 limit = 9;
    // offset
    uint64 offset = 10;
    string is_unable = 11;
    string lan = 12;
}
message SalesExplainedSummaryReport {
    //门店编码|门店名称|商品名称|商品编码|销售数量|销售流水号|原料名称|原料编码|拆解数量|核算单位
    uint64 batch_id = 1;
    uint64 e_ticket_id = 2;
    string entity_code = 3;
    string entity_name = 4;
    uint64 entity_id = 5;
    uint64 product_id = 6;
    string product_code = 7;
    string product_name = 8;
    //核算单位
    uint64 accounting_unit_id = 9;
    string accounting_unit_code = 10;
    string accounting_unit_name = 11;
    double product_qty = 12;
    string pos_ticket_number = 13;
    uint64 bom_product_id = 14;
    string bom_product_code = 15;
    string bom_product_name = 16;
    //bom核算单位
    uint64 bom_accounting_unit_id = 17;
    string bom_accounting_unit_code = 18;
    string bom_accounting_unit_name = 19;
    double qty = 20;
    google.protobuf.Timestamp sales_date = 21;
    bool is_unable = 22;
    repeated SalesExplainedReportProperties properties = 23;
    double price = 24;
    string period_symbol = 25;
    double cost = 26;
    string cost_date = 27;
}
message GetSalesExplainedSummaryReportResponse {
    repeated SalesExplainedSummaryReport rows = 1;
    uint64 total = 2;
    double summary_product_qty = 3;
    double summary_qty = 4;
}
message GetETicketDetailRequest {
    uint64 e_ticket_id = 1;
    string lan = 2;
}
message EticketDetailMaterialInfo {
    uint64 bom_product_id = 1;
    string bom_product_code = 2;
    string bom_product_name = 3;
    //bom核算单位
    uint64 bom_accounting_unit_id = 4;
    string bom_accounting_unit_code = 5;
    string bom_accounting_unit_name = 6;
    double qty = 7;
    bool is_unable = 8;
    double price = 9;
}
message EticketDetailMakeUpInfo {
    string code = 1;
    string name = 2;
    double qty = 3;
    string unit_name = 4;
    string type = 5;
    bool is_replace = 6;
}
message EticketDetailMakeUpRule {
    string code = 1;
    string name = 2;
    double priority = 3;
    google.protobuf.Timestamp start_date = 4;
    google.protobuf.Timestamp end_date = 5;
}
message EticketDetailProductInfo {
    string code = 1;
    // 产品名称
    string name = 2;
    double quantity = 3;
    double price = 4;
    double amount = 5;
    // 口味
    repeated SalesExplainedReportProperties properties = 6;
    //物料扣减
    repeated EticketDetailMaterialInfo material_info = 7;
    //标准配方
    repeated EticketDetailMakeUpInfo make_up_info = 8;
    //配方规则
    repeated EticketDetailMakeUpRule make_up_rule = 9;
    uint64 id = 10;
}
message GetETicketDetailResponse {
    // 小票在 hex server 中的 id
    uint64 id = 1;
    // 营业日期
    google.protobuf.Timestamp operation_date = 2;
    // 交易时间
    google.protobuf.Timestamp transaction_time = 3;
    // 小票上传时间
    google.protobuf.Timestamp ticket_upload_time = 4;
    // 小票类型
    TicketType ticket_type = 5;
    // 小票编号(客户系统编号)
    string pos_ticket_number = 6;
    // 门店信息
    StoreInfo store_info = 7;
    // 会员信息
    repeated MemberInfo member_info = 8;
    // 产品交付方式(堂食/外带/外送/外卖)
    DeliveryType delivery_type = 9;
    // 折扣 只显示整单的折扣, 单品折扣信息显示在单品中
    repeated Discount discounts = 10;
    // 销售费用
    repeated Fee fees = 11;
    // 金额
    double amount = 12;
    // 收款
    repeated Payment payments = 13;
    // 渠道
    SalesChannel sales_channel = 14;
    //外卖
    Takeway takeway = 15;
    //订单类型
    bool is_cancelled_order = 16;
    //商品详情
    repeated EticketDetailProductInfo products = 17;
}
message GetSalesExplainedErrorReportRequest{
    repeated uint64 store_ids = 1;
    repeated uint64 product_ids = 2;
    google.protobuf.Timestamp start_date = 3;
    google.protobuf.Timestamp end_date = 4;
    string pos_ticket_number = 5;
    string error_type = 6;
    uint64 offset = 8;
    // 返回数据的数量, 默认是 10
    int64 limit = 9;
    // 语言
    string lan = 10;
}
message BomProductExplained {
    uint64 bom_product_id = 1;
    string bom_product_code = 2;
    string bom_product_name = 3;
    //bom核算单位
    uint64 bom_accounting_unit_id = 4;
    string bom_accounting_unit_code = 5;
    string bom_accounting_unit_name = 6;
    double qty = 7;
}
message SalesExplainedErrorReport {
    string store_code = 1;
    string store_name = 2;
    uint64 store_id = 3;
    uint64 product_id = 4;
    string product_code = 5;
    string product_name = 6;
    string pos_ticket_number = 7;
    repeated BomProductExplained bom_products=8;
    google.protobuf.Timestamp sales_date = 9;
    google.protobuf.Timestamp operation_date = 10;
    string error_type = 11;
    string error_detail = 12;
    double product_qty = 13;
}
message GetSalesExplainedErrorReportResponse{
    repeated SalesExplainedErrorReport rows = 1;
    uint64 total = 2;
}

message GetMaterialSuppliesRequest{
    string start_date = 1;
    string end_date = 2;
    repeated uint64 store_ids = 3;
    repeated uint64 product_ids = 4;
    int64 limit = 5;
    int64 offset = 6;
    string lan = 7;
    repeated string ledger_class = 8;
    repeated uint64 category_ids = 9;
    bool exclude_empty = 10;
}
message GetMaterialSuppliesResponse{
    //["营业日", "管理区域", "门店编码", "门店名称", "物料SKU编码", "物料SKU名称", "单位","单价",
    //"期初库存","来货数量","退货数量", "报废数量","调入数量","调出数量","期末库存","实际库存使用数",
    // "成本（实际库存数*单价）","理论销售使用数","理论销售金额","差异数量（实际-理论）","差异金额","差异百分比（%）"]
    message MaterialSupplies{
        string bus_date = 1;
        string branch_region_name = 2;
        string store_code = 3;
        string store_name = 4;
        string product_code = 5;
        string product_name = 6;
        string unit_code = 7;
        string unit_name = 8;
        string product_category_id = 29;
        string category_code = 30;
        string category_name = 31;
        double start_qty = 11;
        double receive_qty = 12;
        double return_qty = 16;
        double adjust_qty = 15;
        double transfer_in_qty = 13;
        double transfer_out_qty = 14;
        double end_qty = 17;
        double calculate_use_qty = 18;
        double calculate_cost = 21;
        double actual_use_qty = 19;
        double actual_cost = 20;
        double diff_qty = 22;
        double diff_cost = 23;
        double diff_percentage = 24;
        uint64 store_id = 25;
        uint64 product_id = 26;
        double price = 27;
        double jde_end_qty = 28;
        double material_trans_deposit = 32;
        double material_trans_withdraw = 33;
    }
    repeated MaterialSupplies rows = 1;
    uint64 total = 2;
}

// 物料成本报表请求参数
message GetMaterialCostReportRequest{
    // 开始时间
    google.protobuf.Timestamp start_date = 16;
    // 查询结束日期(此日期结果不包含)
    google.protobuf.Timestamp end_date = 17;
    // 账期id
    uint64 period_id = 1;
    // 账期名称
    string period_name = 18;
    // 成本中心id
    uint64 cost_center_id = 2;
    // 商品id
    repeated uint64 product_ids = 4;
    // 排序(默认asc)
    string order = 10;
    // 排序字段
    string sort = 11;
    // 偏移
    uint64 offset = 12;
    // 每页数量
    uint64 limit = 13;
    // 是否过滤空数据
    bool filter_empty = 15;
    // 商品分类id列表 用于筛选
    repeated uint64 product_category_ids = 21;
}
message CostData{
    // 所属账期
    uint64 period_id = 1;
    // 账期名称
    string period_name = 2;
    // 成本中心id
    uint64 cost_center_id = 3;
    // 成本中心名称
    string center_name = 4;
    // 成本中心编号
    string center_code = 5;
    // 商品id
    uint64 product_id = 6;
    // 商品名称
    string product_name = 7;
    // 商品编号
    string product_code = 8;
    // 商品属性
    string product_type = 9;
    // 商品SKU状态 启用/停用
    string product_status = 10;
    // 商品类别id(商品直接挂的分类)
    uint64 category_id = 11;
    // 商品类别名称
    string category_name = 12;
    // 父商品类别id(二级/一级)
    uint64 parent_category_id = 13;
    // 父商品类别名称
    string parent_category_name = 14;
    // 单位id(核算)
    uint64 unit_id = 15;
    // 单位名称
    string unit_name = 16;
    // 单位编号
    string unit_code = 17;
    // 期初数量
    double start_quantity = 18;
    // 期初成本单价
    double start_cost = 19;
    // 期初金额 期初成本单价*期初数量
    double start_amount = 20;
    // 当期成本单价
    double current_cost = 21;
    // 采购收货数量
    double receive_quantity = 22;
    // 采购收货金额
    double receive_amount = 23;
    // 采购退货数量
    double return_quantity = 24;
    // 采购退货金额
    double return_amount = 25;
    // 调整金额
    double adjust_amount = 26;
    // 本期产成数量
    double product_qty = 27;
    // 本期产成金额
    double product_amount = 28;
    // 开始时间
    google.protobuf.Timestamp start_date = 29;
    // 查询结束日期(此日期结果不包含)
    google.protobuf.Timestamp end_date = 30;
    // 最后计算时间
    google.protobuf.Timestamp last_date = 31;
    // 理论成本
    string cost = 32;
    // 数据异常情况 0：没有错误，除了0就是有错误
    string error_code = 33;
    // 一级商品类别
    string category1 = 34;
    // 二级商品类别
    string category2 = 35;
    // 三级商品类别
    string category3 = 36;
    // 四级商品类别
    string category4 = 37;
    // 五级商品类别
    string category5 = 38;
    // 六级商品类别
    string category6 = 39;
    // 七级商品类别
    string category7 = 40;
    // 八级商品类别
    string category8 = 41;
    // 九级商品类别
    string category9 = 42;
    // 十级商品类别
    string category10 = 43;
}
// 物料成本报表返回参数
message GetMaterialCostReportResponse{
    repeated CostData rows = 1;
    uint64 total = 2;
    string status = 3;  // 0.当前任务未计算显示上次结果 1.正在计算 2.未找到任务执行记录，可以点击试算 3.已计算 4.当前账期无数据 5.计算失败/显示上次计算结果
    string description = 4;  // 状态描述信息
    // 最后计算时间
    google.protobuf.Timestamp end_time = 5;
}

// 成本进销存报表请求参数
message GetBalanceCostReportRequest{
    //账期id
    uint64 period_id = 1;
    // 账期名称
    string period_name = 23;
    // 成本中心id
    uint64 cost_center_id = 2;
    // 门店或仓库id列表
    repeated uint64 branch_ids = 3;
    // 商品id列表
    repeated uint64 product_ids = 5;
    // 排序(默认asc)
    string order = 11;
    // 排序字段
    string sort = 12;
    // 偏移
    uint64 offset = 13;
    // 每页数量
    uint64 limit = 14;
    // 管理区域ids
    repeated uint64 branch_regions = 15;
    // 报表查询纬度
    //  COST_CENTER        // 成本中心
    //  STORE              // 门店
    //  WAREHOUSE          // 仓库
    //  MANUFACTORY        // 加工中心
    //  STORE_COLLECT      // 门店汇总
    string branch_type = 16;
    // 是否过滤空数据
    bool filter_empty = 19;
    // 商品分类id列表  用于过滤筛选
    repeated uint64 product_category_ids = 20;
    // 开始时间
    google.protobuf.Timestamp start_date = 24;
    // 查询结束日期
    google.protobuf.Timestamp end_date = 25;
}

// 成本进销存报表返回参数
message GetBalanceCostReportResponse{
    repeated BalanceData rows = 1;
    uint64 total = 2;
}
message BalanceData{
    // 成本中心id
    uint64 cost_center_id = 1;
    // 成本中心名称
    string center_name = 2;
    // 成本中心编号
    string center_code = 3;
    // 账期id
    uint64 periodID = 4;
    // 账期状态
    string period_status = 5;
    // 账期名称
    string period_name = 6;
    // 门店/仓库/加工中心id
    uint64 branch_id = 7;
    // 门店/仓库/加工中心编号
    string branch_code = 8;
    // 门店/仓库/加工中心名称
    string branch_name = 9;
    // 商品id
    uint64 product_id = 10;
    // 商品名称
    string product_name = 11;
    // 商品编号
    string product_code = 12;
    // 商品SKU状态 启用/停用
    string product_status = 13;
    // 商品类别id
    uint64 category_id = 14;
    // 商品类别名称
    string category_name = 15;
    // 父商品类别id
    uint64 parent_category_id = 16;
    // 父商品类别名称
    string parent_category_name = 17;
    // 商品规格
    string spec = 18;
    // 单位id(核算)
    uint64 unit_id = 19;
    // 单位名称
    string unit_name = 20;
    // 单位编号
    string unit_code = 21;
    // 期初数量
    double start_quantity = 22;
    // 期初成本单价
    double start_cost = 23;
    // 期初成本  期初数量✖️期初成本单价
    double start_amount = 24;
    // 期末数量
    double end_quantity = 25;
    // 当期成本单价
    double current_cost = 26;
    // 期末成本 期末数量✖️期末成本单价
    double end_amount = 27;
    // 自采入库数量
    double self_in_qty = 28;
    // 自采入库成本
    double self_in_amount = 29;
    // 采购/直送入库数量
    double receive_quantity = 30;
    // 采购/直送入库成本（¥）
    double receive_cost = 31;
    // 采购/直送出库数量
    double return_quantity = 32;
    // 采购/直送出库成本（¥）
    double return_cost = 33;
    // 配送入库数量
    double delivery_in = 34;
    // 配送入库成本
    double delivery_in_cost = 35;
    // 配送出库数量
    double delivery_out = 36;
    // 配送出库成本
    double delivery_out_cost = 37;
    // 调拨出库数量
    double transfer_out_qty = 38;
    // 调拨出库成本
    double transfer_out_cost = 39;
    // 调拨入库数量
    double transfer_in_qty = 40;
    // 调拨入库金额
    double transfer_in_cost = 41;
    // 在途数量
    double broker_qty = 42;
    // 在途成本
    double broker_cost = 43;
    // 报废数量
    double scrap_quantity = 44;
    // 报废成本 报废数量✖️当期成本单价
    double scrap_amount = 45;
    // 销售数量
    double sale_quantity = 46;
    // 销售成本
    double sale_cost = 47;
    // 盘点损益 (当前账期内该商品盘点损益情况汇总，= 盘盈数量的绝对值-盘亏数量的绝对值)
    double inventory_diff = 48;
    // 盘点损益成本
    double inventory_diff_cost   = 49;
    // 开始时间
    google.protobuf.Timestamp start_date = 50;
    // 结束日期
    google.protobuf.Timestamp end_date = 51;
    // 最后计算时间
    google.protobuf.Timestamp last_date = 52;
    // 一级商品类别
    string category1 = 64;
    // 二级商品类别
    string category2 = 65;
    // 三级商品类别
    string category3 = 66;
    // 四级商品类别
    string category4 = 67;
    // 五级商品类别
    string category5 = 68;
    // 六级商品类别
    string category6 = 69;
    // 七级商品类别
    string category7 = 70;
    // 八级商品类别
    string category8 = 71;
    // 九级商品类别
    string category9 = 72;
    // 十级商品类别
    string category10 = 73;
}
