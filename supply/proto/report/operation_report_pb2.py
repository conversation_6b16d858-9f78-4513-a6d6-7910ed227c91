# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: report/operation_report.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='report/operation_report.proto',
  package='operation_report',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1dreport/operation_report.proto\x12\x10operation_report\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1cgoogle/api/annotations.proto\"\x06\n\x04Null\"\x13\n\x04Pong\x12\x0b\n\x03msg\x18\x01 \x01(\t\"0\n\x06Result\x12\x15\n\raction_result\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"\xcb\x03\n\x17GetETicketReportRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x10\n\x08store_id\x18\x03 \x01(\x04\x12\x1a\n\x12payment_method_ids\x18\x04 \x03(\x04\x12\x1b\n\x13\x64iscount_method_ids\x18\x05 \x03(\x04\x12\x12\n\nproduct_id\x18\x06 \x01(\x04\x12\x1a\n\x12member_card_number\x18\x07 \x01(\t\x12\x19\n\x11pos_ticket_number\x18\x08 \x01(\t\x12\x31\n\x0bticket_type\x18\t \x01(\x0e\x32\x1c.operation_report.TicketType\x12\x35\n\rdelivery_type\x18\n \x01(\x0e\x32\x1e.operation_report.DeliveryType\x12\x0e\n\x06offset\x18\x0b \x01(\x04\x12\r\n\x05limit\x18\x0c \x01(\x05\x12\x10\n\x08trace_id\x18\r \x01(\t\x12\x14\n\x0c\x61mount_legal\x18\x0e \x03(\t\x12\x0b\n\x03lan\x18\x0f \x01(\t\"X\n\x18GetETicketReportResponse\x12\r\n\x05total\x18\x01 \x01(\x04\x12-\n\x04rows\x18\x02 \x03(\x0b\x32\x1f.operation_report.ETicketReport\"\xb6\x06\n\rETicketReport\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x32\n\x0eoperation_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x10transaction_time\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x36\n\x12ticket_upload_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\x0bticket_type\x18\x05 \x01(\x0e\x32\x1c.operation_report.TicketType\x12\x19\n\x11pos_ticket_number\x18\x06 \x01(\t\x12/\n\nstore_info\x18\x07 \x01(\x0b\x32\x1b.operation_report.StoreInfo\x12\x31\n\x0bmember_info\x18\x08 \x03(\x0b\x32\x1c.operation_report.MemberInfo\x12\x35\n\rdelivery_type\x18\t \x01(\x0e\x32\x1e.operation_report.DeliveryType\x12(\n\x08products\x18\n \x03(\x0b\x32\x16.operation_report.Item\x12-\n\tdiscounts\x18\x0b \x03(\x0b\x32\x1a.operation_report.Discount\x12#\n\x04\x66\x65\x65s\x18\x0c \x03(\x0b\x32\x15.operation_report.Fee\x12\x0e\n\x06\x61mount\x18\r \x01(\x01\x12+\n\x08payments\x18\x0e \x03(\x0b\x32\x19.operation_report.Payment\x12\x35\n\rsales_channel\x18\x0f \x01(\x0b\x32\x1e.operation_report.SalesChannel\x12*\n\x07takeway\x18\x10 \x01(\x0b\x32\x19.operation_report.Takeway\x12\x1a\n\x12is_cancelled_order\x18\x11 \x01(\x08\x12\x18\n\x10net_amount_legal\x18\x12 \x01(\x08\x12\x1c\n\x14product_amount_legal\x18\x13 \x01(\x08\x12\x1c\n\x14payment_amount_legal\x18\x14 \x01(\x08\"o\n\x07Takeway\x12\x14\n\x0corder_method\x18\x01 \x01(\t\x12\x12\n\norder_time\x18\x02 \x01(\t\x12\x13\n\x0btp_order_id\x18\x03 \x01(\t\x12\x10\n\x08send_fee\x18\x04 \x01(\x01\x12\x13\n\x0bpackage_fee\x18\x05 \x01(\x01\"3\n\tStoreInfo\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\"U\n\nMemberInfo\x12\x11\n\tmember_id\x18\x01 \x01(\x04\x12\x1a\n\x12member_card_number\x18\x02 \x01(\t\x12\x18\n\x10member_card_name\x18\x03 \x01(\t\"\xc8\x02\n\x04Item\x12\x17\n\x0fsequence_number\x18\x01 \x01(\r\x12\x33\n\x0cproduct_info\x18\x02 \x01(\x0b\x32\x1d.operation_report.ProductInfo\x12\x10\n\x08quantity\x18\x03 \x01(\x01\x12\r\n\x05price\x18\x04 \x01(\x01\x12\x1f\n\x17\x61mount_without_mixtures\x18\x05 \x01(\x01\x12+\n\x08mixtures\x18\x06 \x03(\x0b\x32\x19.operation_report.Mixture\x12\x1a\n\x12\x61mount_of_mixtures\x18\x07 \x01(\x01\x12\x1c\n\x14\x61mount_with_mixtures\x18\x08 \x01(\x01\x12\x32\n\x0eitem_discounts\x18\t \x03(\x0b\x32\x1a.operation_report.Discount\x12\x15\n\ritem_subtotal\x18\n \x01(\x01\"o\n\x07Mixture\x12\x33\n\x0cproduct_info\x18\x01 \x01(\x0b\x32\x1d.operation_report.ProductInfo\x12\r\n\x05price\x18\x02 \x01(\x01\x12\x10\n\x08quantity\x18\x03 \x01(\x01\x12\x0e\n\x06\x61mount\x18\x04 \x01(\x01\"\x84\x01\n\x07Payment\x12\x14\n\x0cpayment_uuid\x18\x01 \x01(\t\x12\x19\n\x11payment_method_id\x18\x02 \x01(\x04\x12\x1b\n\x13payment_method_code\x18\x03 \x01(\t\x12\x1b\n\x13payment_method_name\x18\x04 \x01(\t\x12\x0e\n\x06\x61mount\x18\x05 \x01(\x01\"Q\n\x0cSalesChannel\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x19\n\x11is_online_channel\x18\x04 \x01(\x08\"B\n\x08\x44iscount\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x0e\n\x06\x61mount\x18\x04 \x01(\x01\"G\n\x0bProductInfo\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x10\n\x08\x63\x61tegory\x18\x04 \x01(\t\"P\n\x03\x46\x65\x65\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\r\n\x05price\x18\x03 \x01(\x01\x12\x10\n\x08quantity\x18\x04 \x01(\x01\x12\x0e\n\x06\x61mount\x18\x05 \x01(\x01\"\xb2\x02\n\x1aGetStoreSalesReportRequest\x12\x35\n\rperiod_filter\x18\x01 \x01(\x0b\x32\x1e.operation_report.PeriodFilter\x12\x35\n\rregion_filter\x18\x02 \x01(\x0b\x32\x1e.operation_report.RegionFilter\x12<\n\x0fperiod_group_by\x18\x03 \x01(\x0e\x32#.operation_report.PeriodGroupMethod\x12<\n\x0fregion_group_by\x18\x04 \x01(\x0b\x32#.operation_report.RegionGroupMethod\x12\r\n\x05limit\x18\x07 \x01(\x05\x12\x0e\n\x06offset\x18\x08 \x01(\x04\x12\x0b\n\x03lan\x18\t \x01(\t\"\\\n\x11RegionGroupMethod\x12\x31\n\x0bregion_type\x18\x01 \x01(\x0e\x32\x1c.operation_report.RegionType\x12\x14\n\x0cregion_level\x18\x02 \x01(\r\"J\n\nRegionInfo\x12\x14\n\x0cregion_level\x18\x01 \x01(\r\x12\x11\n\tregion_id\x18\x02 \x01(\x04\x12\x13\n\x0bregion_name\x18\x03 \x01(\t\"\x91\x01\n\x1aGetStoreSalesReportReponse\x12\r\n\x05total\x18\x01 \x01(\x04\x12\x32\n\x07summary\x18\x02 \x01(\x0b\x32!.operation_report.TotalStoreSales\x12\x30\n\x04rows\x18\x03 \x03(\x0b\x32\".operation_report.StoreSalesRecord\"\xf7\x01\n\x0fTotalStoreSales\x12\x1a\n\x12total_record_count\x18\x01 \x01(\x03\x12\x1a\n\x12gross_sales_amount\x18\x02 \x01(\x01\x12\x18\n\x10net_sales_amount\x18\x03 \x01(\x01\x12\x17\n\x0f\x64iscount_amount\x18\x04 \x01(\x01\x12\x12\n\nfee_amount\x18\x05 \x01(\x01\x12\x13\n\x0bsales_count\x18\x06 \x01(\x01\x12 \n\x18\x61verage_net_sales_amount\x18\x07 \x01(\x01\x12\x17\n\x0freturn_quantity\x18\x0b \x01(\x01\x12\x15\n\rreturn_amount\x18\x0c \x01(\x01\"\xfe\x02\n\x10StoreSalesRecord\x12\x15\n\rperiod_symbol\x18\x01 \x01(\t\x12\x1a\n\x12region_group_level\x18\x02 \x01(\r\x12\x31\n\x0bregion_type\x18\x03 \x01(\x0e\x32\x1c.operation_report.RegionType\x12:\n\x10region_meta_info\x18\x04 \x03(\x0b\x32 .operation_report.RegionMetaInfo\x12\x1a\n\x12gross_sales_amount\x18\x05 \x01(\x01\x12\x18\n\x10net_sales_amount\x18\x06 \x01(\x01\x12\x17\n\x0f\x64iscount_amount\x18\x07 \x01(\x01\x12\x12\n\nfee_amount\x18\x08 \x01(\x01\x12\x13\n\x0bsales_count\x18\t \x01(\x01\x12 \n\x18\x61verage_net_sales_amount\x18\n \x01(\x01\x12\x17\n\x0freturn_quantity\x18\x0b \x01(\x01\x12\x15\n\rreturn_amount\x18\x0c \x01(\x01\"\xbe\x04\n\x1cGetProductSalesReportRequest\x12\x35\n\rperiod_filter\x18\x01 \x01(\x0b\x32\x1e.operation_report.PeriodFilter\x12\x35\n\rregion_filter\x18\x02 \x01(\x0b\x32\x1e.operation_report.RegionFilter\x12\x38\n\x0fproduct_filters\x18\x03 \x03(\x0b\x32\x1f.operation_report.ProductFilter\x12\x43\n\x15sales_channel_filters\x18\x04 \x03(\x0b\x32$.operation_report.SalesChannelFilter\x12<\n\x0fperiod_group_by\x18\x05 \x01(\x0e\x32#.operation_report.PeriodGroupMethod\x12<\n\x0fregion_group_by\x18\x06 \x01(\x0b\x32#.operation_report.RegionGroupMethod\x12>\n\x10product_group_by\x18\x07 \x01(\x0b\x32$.operation_report.ProductGroupMethod\x12I\n\x16sales_channel_group_by\x18\x08 \x01(\x0e\x32).operation_report.SalesChannelGroupMethod\x12\r\n\x05limit\x18\t \x01(\x03\x12\x0e\n\x06offset\x18\n \x01(\x04\x12\x0b\n\x03lan\x18\x0b \x01(\t\"l\n\x0cPeriodFilter\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"V\n\x0cRegionFilter\x12*\n\x04type\x18\x01 \x01(\x0e\x32\x1c.operation_report.RegionType\x12\r\n\x05level\x18\x02 \x01(\r\x12\x0b\n\x03ids\x18\x03 \x03(\x04\"^\n\rProductFilter\x12\x31\n\x04type\x18\x01 \x01(\x0e\x32#.operation_report.ProductFilterType\x12\x0b\n\x03ids\x18\x02 \x03(\x04\x12\r\n\x05level\x18\x03 \x01(\r\"`\n\x12ProductGroupMethod\x12\x32\n\x08group_by\x18\x01 \x01(\x0e\x32 .operation_report.ProductGroupBy\x12\x16\n\x0e\x63\x61tegory_level\x18\x02 \x01(\r\".\n\x12SalesChannelFilter\x12\x18\n\x10sales_channel_id\x18\x01 \x01(\x04\"\x98\x01\n\x1dGetProductSalesReportResponse\x12\x34\n\x07summary\x18\x01 \x01(\x0b\x32#.operation_report.ProductSalesTotal\x12\x32\n\x04rows\x18\x02 \x03(\x0b\x32$.operation_report.ProductSalesRecord\x12\r\n\x05total\x18\x03 \x01(\x04\"\x95\x01\n\x11ProductSalesTotal\x12\x14\n\x0crecord_count\x18\x01 \x01(\x03\x12\x1c\n\x14total_sales_quantity\x18\x02 \x01(\x03\x12\x18\n\x10net_sales_amount\x18\x03 \x01(\x01\x12\x17\n\x0f\x64iscount_amount\x18\x04 \x01(\x01\x12\x19\n\x11\x61verage_price_net\x18\x05 \x01(\x01\"\x84\x04\n\x12ProductSalesRecord\x12\x15\n\rperiod_symbol\x18\x01 \x01(\t\x12\x1a\n\x12region_group_level\x18\x02 \x01(\r\x12\x31\n\x0bregion_type\x18\x03 \x01(\x0e\x32\x1c.operation_report.RegionType\x12:\n\x10region_meta_info\x18\x04 \x03(\x0b\x32 .operation_report.RegionMetaInfo\x12\x1b\n\x13product_group_level\x18\x05 \x01(\r\x12<\n\x12product_group_type\x18\x06 \x01(\x0e\x32 .operation_report.ProductGroupBy\x12=\n\x12product_meta_infos\x18\x07 \x03(\x0b\x32!.operation_report.ProductMetaInfo\x12\x1c\n\x14sales_channel_symbol\x18\x08 \x01(\t\x12\x10\n\x08quantity\x18\t \x01(\x05\x12\x1b\n\x13quantity_percentage\x18\n \x01(\x01\x12\x12\n\nnet_amount\x18\x0b \x01(\x01\x12\x1d\n\x15net_amount_percentage\x18\x0c \x01(\x01\x12\x19\n\x11\x61verage_net_price\x18\r \x01(\x01\x12\x17\n\x0f\x64iscount_amount\x18\x0e \x01(\x01\"\xcc\x03\n\x18GetDiscountReportRequest\x12\x35\n\rperiod_filter\x18\x01 \x01(\x0b\x32\x1e.operation_report.PeriodFilter\x12\x35\n\rregion_filter\x18\x02 \x01(\x0b\x32\x1e.operation_report.RegionFilter\x12:\n\x10\x64iscount_filters\x18\x03 \x03(\x0b\x32 .operation_report.DiscountFilter\x12<\n\x0fperiod_group_by\x18\x04 \x01(\x0e\x32#.operation_report.PeriodGroupMethod\x12<\n\x0fregion_group_by\x18\x05 \x01(\x0b\x32#.operation_report.RegionGroupMethod\x12@\n\x11\x64iscount_group_by\x18\x06 \x01(\x0e\x32%.operation_report.DiscountGroupMethod\x12\x1c\n\x14\x63\x61tegory_group_level\x18\x07 \x01(\r\x12\r\n\x05limit\x18\x08 \x01(\x03\x12\x0e\n\x06offset\x18\t \x01(\x04\x12\x0b\n\x03lan\x18\n \x01(\t\"^\n\x0e\x44iscountFilter\x12\x30\n\x04type\x18\x01 \x01(\x0e\x32\".operation_report.DiscountFilterBy\x12\x0b\n\x03ids\x18\x02 \x03(\x04\x12\r\n\x05level\x18\x03 \x01(\r\"\x8c\x01\n\x19GetDiscountReportResponse\x12\x30\n\x07summary\x18\x01 \x01(\x0b\x32\x1f.operation_report.DiscountTotal\x12.\n\x04rows\x18\x02 \x03(\x0b\x32 .operation_report.DiscountRecord\x12\r\n\x05total\x18\x03 \x01(\x04\"r\n\rDiscountTotal\x12\x14\n\x0crecord_count\x18\x01 \x01(\x03\x12\x10\n\x08quantity\x18\x02 \x01(\x03\x12\x0e\n\x06\x61mount\x18\x03 \x01(\x01\x12)\n!discount_amount_of_canceled_order\x18\x04 \x01(\x01\"\xb6\x02\n\x0e\x44iscountRecord\x12\x15\n\rperiod_symbol\x18\x01 \x01(\t\x12\x1a\n\x12region_group_level\x18\x02 \x01(\r\x12\x31\n\x0bregion_type\x18\x03 \x01(\x0e\x32\x1c.operation_report.RegionType\x12:\n\x10region_meta_info\x18\x04 \x03(\x0b\x32 .operation_report.RegionMetaInfo\x12\x17\n\x0f\x64iscount_symbol\x18\x05 \x01(\t\x12\x1c\n\x14\x64iscount_symbol_code\x18\x06 \x01(\t\x12\x10\n\x08quantity\x18\x07 \x01(\x03\x12\x0e\n\x06\x61mount\x18\x08 \x01(\x01\x12)\n!discount_amount_of_canceled_order\x18\t \x01(\x01\"\xa9\x03\n\x17GetPaymentReportRequest\x12\x35\n\rperiod_filter\x18\x01 \x01(\x0b\x32\x1e.operation_report.PeriodFilter\x12\x35\n\rregion_filter\x18\x02 \x01(\x0b\x32\x1e.operation_report.RegionFilter\x12\x38\n\x0fpayment_filters\x18\x03 \x03(\x0b\x32\x1f.operation_report.PaymentFilter\x12<\n\x0fperiod_group_by\x18\x04 \x01(\x0e\x32#.operation_report.PeriodGroupMethod\x12<\n\x0fregion_group_by\x18\x05 \x01(\x0b\x32#.operation_report.RegionGroupMethod\x12>\n\x10payment_group_by\x18\x06 \x01(\x0b\x32$.operation_report.PaymentGroupMethod\x12\r\n\x05limit\x18\x07 \x01(\x03\x12\x0e\n\x06offset\x18\x08 \x01(\x04\x12\x0b\n\x03lan\x18\t \x01(\t\"\\\n\rPaymentFilter\x12/\n\x04type\x18\x01 \x01(\x0e\x32!.operation_report.PaymentFilterBy\x12\x0b\n\x03ids\x18\x02 \x03(\x04\x12\r\n\x05level\x18\x03 \x01(\r\"Q\n\x12PaymentGroupMethod\x12,\n\x02\x62y\x18\x01 \x01(\x0e\x32 .operation_report.PaymentGroupBy\x12\r\n\x05level\x18\x02 \x01(\r\"\x89\x01\n\x18GetPaymentReportResponse\x12/\n\x07summary\x18\x01 \x01(\x0b\x32\x1e.operation_report.PaymentTotal\x12-\n\x04rows\x18\x02 \x03(\x0b\x32\x1f.operation_report.PaymentRecord\x12\r\n\x05total\x18\x03 \x01(\x04\"g\n\x0cPaymentTotal\x12\x14\n\x0crecord_count\x18\x01 \x01(\x03\x12\x10\n\x08quantity\x18\x02 \x01(\x03\x12\x0e\n\x06\x61mount\x18\x03 \x01(\x01\x12\x0b\n\x03\x66\x65\x65\x18\x04 \x01(\x01\x12\x12\n\nnet_amount\x18\x05 \x01(\x01\"\xc6\x03\n\rPaymentRecord\x12\x15\n\rperiod_symbol\x18\x01 \x01(\t\x12\x1a\n\x12region_group_level\x18\x02 \x01(\r\x12\x31\n\x0bregion_type\x18\x03 \x01(\x0e\x32\x1c.operation_report.RegionType\x12:\n\x10region_meta_info\x18\x04 \x03(\x0b\x32 .operation_report.RegionMetaInfo\x12\x1b\n\x13payment_group_level\x18\x05 \x01(\r\x12:\n\x10payment_group_by\x18\x06 \x01(\x0e\x32 .operation_report.PaymentGroupBy\x12=\n\x12payment_meta_infos\x18\x07 \x03(\x0b\x32!.operation_report.PaymentMetaInfo\x12\x10\n\x08quantity\x18\x08 \x01(\x03\x12\x0e\n\x06\x61mount\x18\t \x01(\x01\x12\x19\n\x11\x61mount_percentage\x18\n \x01(\x01\x12\x0b\n\x03\x66\x65\x65\x18\x0b \x01(\x01\x12\x12\n\nnet_amount\x18\x0c \x01(\x01\x12\x1d\n\x15net_amount_percentage\x18\r \x01(\x01\"<\n\x0fPaymentMetaInfo\x12\r\n\x05level\x18\x02 \x01(\r\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\"\xaa\x04\n\x1eGetProductPaymentReportRequest\x12\x35\n\rperiod_filter\x18\x01 \x01(\x0b\x32\x1e.operation_report.PeriodFilter\x12\x35\n\rregion_filter\x18\x02 \x01(\x0b\x32\x1e.operation_report.RegionFilter\x12\x38\n\x0fpayment_filters\x18\x03 \x03(\x0b\x32\x1f.operation_report.PaymentFilter\x12\x38\n\x0fproduct_filters\x18\x04 \x03(\x0b\x32\x1f.operation_report.ProductFilter\x12<\n\x0fperiod_group_by\x18\x05 \x01(\x0e\x32#.operation_report.PeriodGroupMethod\x12<\n\x0fregion_group_by\x18\x06 \x01(\x0b\x32#.operation_report.RegionGroupMethod\x12>\n\x10payment_group_by\x18\x07 \x01(\x0b\x32$.operation_report.PaymentGroupMethod\x12>\n\x10product_group_by\x18\x08 \x01(\x0b\x32$.operation_report.ProductGroupMethod\x12\r\n\x05limit\x18\t \x01(\x03\x12\x0e\n\x06offset\x18\n \x01(\x04\x12\x0b\n\x03lan\x18\x0b \x01(\t\"\x9a\x01\n\x1fGetProductPaymentReportResponse\x12\r\n\x05total\x18\x01 \x01(\x04\x12\x34\n\x04rows\x18\x02 \x03(\x0b\x32&.operation_report.ProductPaymentRecord\x12\x32\n\x07summary\x18\x03 \x01(\x0b\x32!.operation_report.TotalStoreSales\"\xa0\x05\n\x14ProductPaymentRecord\x12\x15\n\rperiod_symbol\x18\x01 \x01(\t\x12\x1a\n\x12region_group_level\x18\x02 \x01(\r\x12<\n\x0fregion_group_by\x18\x03 \x01(\x0b\x32#.operation_report.RegionGroupMethod\x12:\n\x10region_meta_info\x18\x04 \x03(\x0b\x32 .operation_report.RegionMetaInfo\x12\x1b\n\x13payment_group_level\x18\x05 \x01(\r\x12:\n\x10payment_group_by\x18\x06 \x01(\x0e\x32 .operation_report.PaymentGroupBy\x12=\n\x12payment_meta_infos\x18\x07 \x03(\x0b\x32!.operation_report.PaymentMetaInfo\x12\x1b\n\x13product_group_level\x18\x08 \x01(\r\x12<\n\x12product_group_type\x18\t \x01(\x0e\x32 .operation_report.ProductGroupBy\x12=\n\x12product_meta_infos\x18\n \x03(\x0b\x32!.operation_report.ProductMetaInfo\x12\x12\n\nnet_amount\x18\x0b \x01(\x01\x12\x16\n\x0esales_quantity\x18\x0c \x01(\x03\x12\x19\n\x11transaction_count\x18\r \x01(\x03\x12&\n\x1e\x61verage_amount_per_transaction\x18\x0e \x01(\x01\x12\x1d\n\x15net_amount_percentage\x18\x0f \x01(\x01\x12\x1b\n\x13quantity_percentage\x18\x10 \x01(\x01\";\n\x0eRegionMetaInfo\x12\r\n\x05level\x18\x02 \x01(\r\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\"<\n\x0fProductMetaInfo\x12\r\n\x05level\x18\x02 \x01(\r\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\"\x80\x04\n\x1cGetStoreChannelReportRequest\x12\x35\n\rperiod_filter\x18\x01 \x01(\x0b\x32\x1e.operation_report.PeriodFilter\x12\x35\n\rregion_filter\x18\x02 \x01(\x0b\x32\x1e.operation_report.RegionFilter\x12\x43\n\x15sales_channel_filters\x18\x03 \x03(\x0b\x32$.operation_report.SalesChannelFilter\x12=\n\x15\x64\x65livery_type_filters\x18\x04 \x03(\x0e\x32\x1e.operation_report.DeliveryType\x12<\n\x0fperiod_group_by\x18\x05 \x01(\x0e\x32#.operation_report.PeriodGroupMethod\x12<\n\x0fregion_group_by\x18\x06 \x01(\x0b\x32#.operation_report.RegionGroupMethod\x12\"\n\x1apartition_by_sales_channel\x18\x07 \x01(\x08\x12\"\n\x1apartition_by_delivery_type\x18\x08 \x01(\x08\x12\r\n\x05limit\x18\t \x01(\x03\x12\x0e\n\x06offset\x18\n \x01(\x04\x12\x0b\n\x03lan\x18\x0b \x01(\t\"\xa1\x01\n\x1dGetStoreChannelReportResponse\x12\x39\n\x07summary\x18\x01 \x01(\x0b\x32(.operation_report.StoreChannelSalesTotal\x12\x36\n\x04rows\x18\x02 \x03(\x0b\x32(.operation_report.StoreChanneSalesRecord\x12\r\n\x05total\x18\x03 \x01(\x03\"\x85\x01\n\x16StoreChannelSalesTotal\x12\x14\n\x0crecord_count\x18\x01 \x01(\x03\x12\x19\n\x11transaction_count\x18\x02 \x01(\x03\x12\x12\n\nnet_amount\x18\x03 \x01(\x01\x12&\n\x1e\x61verage_amount_per_transaction\x18\x04 \x01(\x01\"\x9d\x03\n\x16StoreChanneSalesRecord\x12\x15\n\rperiod_symbol\x18\x01 \x01(\t\x12\x1a\n\x12region_group_level\x18\x02 \x01(\r\x12<\n\x0fregion_group_by\x18\x03 \x01(\x0b\x32#.operation_report.RegionGroupMethod\x12:\n\x10region_meta_info\x18\x04 \x03(\x0b\x32 .operation_report.RegionMetaInfo\x12\x1c\n\x14sales_channel_symbol\x18\x05 \x01(\t\x12\x1c\n\x14\x64\x65livery_type_symbol\x18\x06 \x01(\t\x12\x19\n\x11transaction_count\x18\x07 \x01(\x03\x12$\n\x1ctransaction_count_percentage\x18\x08 \x01(\x01\x12\x12\n\nnet_amount\x18\t \x01(\x01\x12\x1d\n\x15net_amount_percentage\x18\n \x01(\x01\x12&\n\x1e\x61verage_amount_per_transaction\x18\x0b \x01(\x01\"\xfc\x04\n\x1eGetProductChannelReportRequest\x12\x35\n\rperiod_filter\x18\x01 \x01(\x0b\x32\x1e.operation_report.PeriodFilter\x12\x35\n\rregion_filter\x18\x02 \x01(\x0b\x32\x1e.operation_report.RegionFilter\x12\x38\n\x0fproduct_filters\x18\x03 \x03(\x0b\x32\x1f.operation_report.ProductFilter\x12\x43\n\x15sales_channel_filters\x18\x04 \x03(\x0b\x32$.operation_report.SalesChannelFilter\x12=\n\x15\x64\x65livery_type_filters\x18\x05 \x03(\x0e\x32\x1e.operation_report.DeliveryType\x12<\n\x0fperiod_group_by\x18\x06 \x01(\x0e\x32#.operation_report.PeriodGroupMethod\x12<\n\x0fregion_group_by\x18\x07 \x01(\x0b\x32#.operation_report.RegionGroupMethod\x12>\n\x10product_group_by\x18\x08 \x01(\x0b\x32$.operation_report.ProductGroupMethod\x12\"\n\x1apartition_by_sales_channel\x18\t \x01(\x08\x12\"\n\x1apartition_by_delivery_type\x18\n \x01(\x08\x12\r\n\x05limit\x18\x0b \x01(\x03\x12\x0e\n\x06offset\x18\x0c \x01(\x04\x12\x0b\n\x03lan\x18\r \x01(\t\"\xa7\x01\n\x1fGetProductChannelReportResponse\x12;\n\x07summary\x18\x01 \x01(\x0b\x32*.operation_report.ProductChannelSalesTotal\x12\x38\n\x04rows\x18\x02 \x03(\x0b\x32*.operation_report.ProductChanneSalesRecord\x12\r\n\x05total\x18\x03 \x01(\x04\"q\n\x18ProductChannelSalesTotal\x12\x14\n\x0crecord_count\x18\x01 \x01(\x03\x12\x10\n\x08quantity\x18\x02 \x01(\x03\x12\x12\n\nnet_amount\x18\x03 \x01(\x01\x12\x19\n\x11\x61verage_net_price\x18\x04 \x01(\x01\"\x9a\x04\n\x18ProductChanneSalesRecord\x12\x15\n\rperiod_symbol\x18\x01 \x01(\t\x12\x1a\n\x12region_group_level\x18\x02 \x01(\r\x12<\n\x0fregion_group_by\x18\x03 \x01(\x0b\x32#.operation_report.RegionGroupMethod\x12:\n\x10region_meta_info\x18\x04 \x03(\x0b\x32 .operation_report.RegionMetaInfo\x12\x1b\n\x13product_group_level\x18\x05 \x01(\r\x12<\n\x12product_group_type\x18\x06 \x01(\x0e\x32 .operation_report.ProductGroupBy\x12=\n\x12product_meta_infos\x18\x07 \x03(\x0b\x32!.operation_report.ProductMetaInfo\x12\x1c\n\x14sales_channel_symbol\x18\x08 \x01(\t\x12\x1c\n\x14\x64\x65livery_type_symbol\x18\t \x01(\t\x12\x10\n\x08quantity\x18\n \x01(\x03\x12\x1b\n\x13quantity_percentage\x18\x0b \x01(\x01\x12\x12\n\nnet_amount\x18\x0c \x01(\x01\x12\x1d\n\x15net_amount_percentage\x18\r \x01(\x01\x12\x19\n\x11\x61verage_net_price\x18\x0e \x01(\x01\"\xe1\x02\n\x1bGetStorePeriodReportRequest\x12\x35\n\rperiod_filter\x18\x01 \x01(\x0b\x32\x1e.operation_report.PeriodFilter\x12\x35\n\rregion_filter\x18\x02 \x01(\x0b\x32\x1e.operation_report.RegionFilter\x12<\n\x0fperiod_group_by\x18\x03 \x01(\x0e\x32#.operation_report.PeriodGroupMethod\x12<\n\x0fregion_group_by\x18\x04 \x01(\x0b\x32#.operation_report.RegionGroupMethod\x12\r\n\x05limit\x18\x05 \x01(\x03\x12\x0e\n\x06offset\x18\x06 \x01(\x04\x12,\n\x08\x65nd_time\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0b\n\x03lan\x18\x08 \x01(\t\"\x90\x01\n\x1cGetStorePeriodReportResponse\x12.\n\x07summary\x18\x01 \x01(\x0b\x32\x1d.operation_report.PeriodTotal\x12\x31\n\x04rows\x18\x02 \x03(\x0b\x32#.operation_report.StorePeriodRecord\x12\r\n\x05total\x18\x03 \x01(\x03\"\x8e\x01\n\x0bPeriodTotal\x12\x13\n\x0btotal_count\x18\x01 \x01(\x03\x12\x19\n\x11transaction_count\x18\x02 \x01(\x03\x12\x12\n\nnet_income\x18\x03 \x01(\x01\x12 \n\x18period_transaction_count\x18\x04 \x01(\t\x12\x19\n\x11period_net_income\x18\x05 \x01(\t\"\x95\x02\n\x11StorePeriodRecord\x12\x1a\n\x12region_group_level\x18\x01 \x01(\r\x12<\n\x0fregion_group_by\x18\x02 \x01(\x0b\x32#.operation_report.RegionGroupMethod\x12:\n\x10region_meta_info\x18\x03 \x03(\x0b\x32 .operation_report.RegionMetaInfo\x12\x12\n\nnet_income\x18\x04 \x01(\x01\x12\x19\n\x11transaction_count\x18\x05 \x01(\x01\x12\x19\n\x11period_net_income\x18\x06 \x01(\t\x12 \n\x18period_transaction_count\x18\x07 \x01(\t\"\x86\x04\n\"GetStoreChannelPeriodReportRequest\x12\x35\n\rperiod_filter\x18\x01 \x01(\x0b\x32\x1e.operation_report.PeriodFilter\x12\x35\n\rregion_filter\x18\x02 \x01(\x0b\x32\x1e.operation_report.RegionFilter\x12\x43\n\x15sales_channel_filters\x18\x03 \x03(\x0b\x32$.operation_report.SalesChannelFilter\x12=\n\x15\x64\x65livery_type_filters\x18\x04 \x03(\x0e\x32\x1e.operation_report.DeliveryType\x12<\n\x0fperiod_group_by\x18\x05 \x01(\x0e\x32#.operation_report.PeriodGroupMethod\x12<\n\x0fregion_group_by\x18\x06 \x01(\x0b\x32#.operation_report.RegionGroupMethod\x12\"\n\x1apartition_by_sales_channel\x18\x07 \x01(\x08\x12\"\n\x1apartition_by_delivery_type\x18\x08 \x01(\x08\x12\r\n\x05limit\x18\t \x01(\x03\x12\x0e\n\x06offset\x18\n \x01(\x04\x12\x0b\n\x03lan\x18\x0b \x01(\t\"\x9e\x01\n#GetStoreChannelPeriodReportResponse\x12.\n\x07summary\x18\x01 \x01(\x0b\x32\x1d.operation_report.PeriodTotal\x12\x38\n\x04rows\x18\x02 \x03(\x0b\x32*.operation_report.StoreChannelPeriodRecord\x12\r\n\x05total\x18\x03 \x01(\x03\"\xd8\x02\n\x18StoreChannelPeriodRecord\x12\x1a\n\x12region_group_level\x18\x02 \x01(\r\x12<\n\x0fregion_group_by\x18\x03 \x01(\x0b\x32#.operation_report.RegionGroupMethod\x12:\n\x10region_meta_info\x18\x04 \x03(\x0b\x32 .operation_report.RegionMetaInfo\x12\x1c\n\x14sales_channel_symbol\x18\x05 \x01(\t\x12\x1c\n\x14\x64\x65livery_type_symbol\x18\x06 \x01(\t\x12\x12\n\nnet_income\x18\x07 \x01(\x01\x12\x19\n\x11transaction_count\x18\x08 \x01(\x03\x12\x19\n\x11period_net_income\x18\t \x01(\t\x12 \n\x18period_transaction_count\x18\n \x01(\t\"\xaf\x03\n\x1dGetPaymentPeriodReportRequest\x12\x35\n\rperiod_filter\x18\x01 \x01(\x0b\x32\x1e.operation_report.PeriodFilter\x12\x35\n\rregion_filter\x18\x02 \x01(\x0b\x32\x1e.operation_report.RegionFilter\x12\x38\n\x0fpayment_filters\x18\x03 \x03(\x0b\x32\x1f.operation_report.PaymentFilter\x12<\n\x0fperiod_group_by\x18\x04 \x01(\x0e\x32#.operation_report.PeriodGroupMethod\x12<\n\x0fregion_group_by\x18\x05 \x01(\x0b\x32#.operation_report.RegionGroupMethod\x12>\n\x10payment_group_by\x18\x06 \x01(\x0b\x32$.operation_report.PaymentGroupMethod\x12\r\n\x05limit\x18\x07 \x01(\x03\x12\x0e\n\x06offset\x18\x08 \x01(\x04\x12\x0b\n\x03lan\x18\t \x01(\t\"\x94\x01\n\x1eGetPaymentPeriodReportResponse\x12.\n\x07summary\x18\x01 \x01(\x0b\x32\x1d.operation_report.PeriodTotal\x12\x33\n\x04rows\x18\x02 \x03(\x0b\x32%.operation_report.PaymentPeriodRecord\x12\r\n\x05total\x18\x03 \x01(\x03\"\xaf\x03\n\x13PaymentPeriodRecord\x12\x1a\n\x12region_group_level\x18\x01 \x01(\r\x12<\n\x0fregion_group_by\x18\x02 \x01(\x0b\x32#.operation_report.RegionGroupMethod\x12:\n\x10region_meta_info\x18\x03 \x03(\x0b\x32 .operation_report.RegionMetaInfo\x12\x1b\n\x13payment_group_level\x18\x04 \x01(\r\x12:\n\x10payment_group_by\x18\x05 \x01(\x0e\x32 .operation_report.PaymentGroupBy\x12=\n\x12payment_meta_infos\x18\x06 \x03(\x0b\x32!.operation_report.PaymentMetaInfo\x12\x12\n\nnet_income\x18\x07 \x01(\x01\x12\x19\n\x11transaction_count\x18\x08 \x01(\x03\x12\x19\n\x11period_net_income\x18\t \x01(\t\x12 \n\x18period_transaction_count\x18\n \x01(\t\"\xaf\x03\n\x1dGetProductPeriodReportRequest\x12\x35\n\rperiod_filter\x18\x01 \x01(\x0b\x32\x1e.operation_report.PeriodFilter\x12\x35\n\rregion_filter\x18\x02 \x01(\x0b\x32\x1e.operation_report.RegionFilter\x12\x38\n\x0fproduct_filters\x18\x03 \x03(\x0b\x32\x1f.operation_report.ProductFilter\x12<\n\x0fperiod_group_by\x18\x04 \x01(\x0e\x32#.operation_report.PeriodGroupMethod\x12<\n\x0fregion_group_by\x18\x05 \x01(\x0b\x32#.operation_report.RegionGroupMethod\x12>\n\x10product_group_by\x18\x06 \x01(\x0b\x32$.operation_report.ProductGroupMethod\x12\r\n\x05limit\x18\x07 \x01(\x03\x12\x0e\n\x06offset\x18\x08 \x01(\x04\x12\x0b\n\x03lan\x18\t \x01(\t\"\x94\x01\n\x1eGetProductPeriodReportResponse\x12.\n\x07summary\x18\x01 \x01(\x0b\x32\x1d.operation_report.PeriodTotal\x12\x33\n\x04rows\x18\x02 \x03(\x0b\x32%.operation_report.ProductPeriodRecord\x12\r\n\x05total\x18\x03 \x01(\x03\"\xb1\x03\n\x13ProductPeriodRecord\x12\x1a\n\x12region_group_level\x18\x01 \x01(\r\x12<\n\x0fregion_group_by\x18\x02 \x01(\x0b\x32#.operation_report.RegionGroupMethod\x12:\n\x10region_meta_info\x18\x03 \x03(\x0b\x32 .operation_report.RegionMetaInfo\x12\x1b\n\x13product_group_level\x18\x05 \x01(\r\x12<\n\x12product_group_type\x18\x06 \x01(\x0e\x32 .operation_report.ProductGroupBy\x12=\n\x12product_meta_infos\x18\x07 \x03(\x0b\x32!.operation_report.ProductMetaInfo\x12\x12\n\nnet_income\x18\x08 \x01(\x01\x12\x19\n\x11transaction_count\x18\t \x01(\x03\x12\x19\n\x11period_net_income\x18\n \x01(\t\x12 \n\x18period_transaction_count\x18\x0b \x01(\t\"\xbc\x01\n GetParseFaliedETicketListRequest\x12\x35\n\rperiod_filter\x18\x01 \x01(\x0b\x32\x1e.operation_report.PeriodFilter\x12\x35\n\rregion_filter\x18\x02 \x01(\x0b\x32\x1e.operation_report.RegionFilter\x12\r\n\x05limit\x18\t \x01(\x03\x12\x0e\n\x06offset\x18\n \x01(\x04\x12\x0b\n\x03lan\x18\x0b \x01(\t\"o\n!GetParseFaliedETicketListResponse\x12\x13\n\x0btotal_count\x18\x01 \x01(\x03\x12\x35\n\x07tickets\x18\x02 \x03(\x0b\x32$.operation_report.ParseFailedETicket\"~\n\x12ParseFailedETicket\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x19\n\x11\x63ustomer_order_id\x18\x02 \x01(\t\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x12\n\nnet_amount\x18\x04 \x01(\x01\x12\x19\n\x11reason_of_failure\x18\x05 \x01(\t\"h\n GetStoreSalesReportForPDARequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x32\n\x0eoperation_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xf9\x02\n!GetStoreSalesReportForPDAResponse\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x32\n\x0eoperation_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1a\n\x12gross_sales_amount\x18\x03 \x01(\x01\x12\x18\n\x10net_sales_amount\x18\x04 \x01(\x01\x12\x15\n\rrefund_amount\x18\x05 \x01(\x01\x12\x17\n\x0f\x64iscount_amount\x18\x06 \x01(\x01\x12\x19\n\x11transaction_count\x18\x07 \x01(\x01\x12&\n\x1e\x61verage_amount_per_transaction\x18\x08 \x01(\x01\x12(\n average_quantity_per_transaction\x18\t \x01(\x01\x12;\n\rchannel_sales\x18\n \x03(\x0b\x32$.operation_report.ChannelSalesAmount\">\n\x12\x43hannelSalesAmount\x12\x14\n\x0c\x63hannel_name\x18\x01 \x01(\t\x12\x12\n\nnet_amount\x18\x02 \x01(\x01\"\xfb\x01\n\x19GetETicketTraceIdsRequest\x12\x19\n\x11pos_ticket_number\x18\x01 \x01(\t\x12\x11\n\tbranch_id\x18\x02 \x01(\x04\x12\x13\n\x0bproduct_ids\x18\x03 \x03(\x04\x12.\n\nstart_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06offset\x18\x07 \x01(\x04\x12\r\n\x05limit\x18\x08 \x01(\x05\x12\x11\n\ttrace_ids\x18\t \x03(\x04\x12\x0b\n\x03lan\x18\n \x01(\t\">\n\x0fPosTicketNumber\x12\x19\n\x11pos_ticket_number\x18\x01 \x01(\t\x12\x10\n\x08trace_id\x18\x02 \x01(\x04\"d\n\x1aGetETicketTraceIdsResponse\x12\x11\n\ttrace_ids\x18\x03 \x03(\t\x12\x33\n\x08pos_info\x18\x04 \x03(\x0b\x32!.operation_report.PosTicketNumber\"\xb3\x01\n\x1fGetProductSalesOffDemandRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0bproduct_ids\x18\x03 \x03(\x04\x12\x10\n\x08store_id\x18\x04 \x01(\x04\x12\x0b\n\x03lan\x18\x05 \x01(\t\"]\n GetProductSalesOffDemandRequest2\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x12\n\ntimestamps\x18\x02 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x03 \x03(\x04\"\xff\x01\n!GetProductSalesOffDemandResponse2\x12\x43\n\x07request\x18\x01 \x01(\x0b\x32\x32.operation_report.GetProductSalesOffDemandRequest2\x12P\n\nsale_infos\x18\x02 \x03(\x0b\x32<.operation_report.GetProductSalesOffDemandResponse2.SaleInfo\x1a\x43\n\x08SaleInfo\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x11\n\ttimestamp\x18\x02 \x01(\x04\x12\x10\n\x08quantity\x18\x03 \x01(\x03\"\x87\x01\n\x11ProductsSalesInfo\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x10\n\x08quantity\x18\x02 \x01(\x01\x12\x32\n\x0esales_end_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x18\n\x10\x61verage_quantity\x18\x04 \x01(\x01\"v\n GetProductSalesOffDemandResponse\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12@\n\x13products_sales_info\x18\x02 \x03(\x0b\x32#.operation_report.ProductsSalesInfo\"\x96\x02\n\x1eGetSalesExplainedReportRequest\x12\x10\n\x08store_id\x18\x01 \x03(\x04\x12\x12\n\nproduct_id\x18\x02 \x03(\x04\x12.\n\nstart_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x19\n\x11pos_ticket_number\x18\x05 \x01(\t\x12\x16\n\x0e\x62om_product_id\x18\x07 \x03(\x04\x12\x0e\n\x06offset\x18\x08 \x01(\x04\x12\r\n\x05limit\x18\t \x01(\x03\x12\x11\n\tis_unable\x18\n \x01(\t\x12\x0b\n\x03lan\x18\x0b \x01(\t\"o\n\x1eSalesExplainedReportProperties\x12\x17\n\x0fproperty_key_id\x18\x01 \x01(\x04\x12\x19\n\x11property_key_code\x18\x02 \x01(\t\x12\x19\n\x11property_key_name\x18\x03 \x01(\t\"\xa2\x05\n\x14SalesExplainedReport\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\x12\x13\n\x0b\x65_ticket_id\x18\x02 \x01(\x04\x12\x12\n\nstore_code\x18\x03 \x01(\t\x12\x12\n\nstore_name\x18\x04 \x01(\t\x12\x10\n\x08store_id\x18\x05 \x01(\x04\x12\x12\n\nproduct_id\x18\x06 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x07 \x01(\t\x12\x14\n\x0cproduct_name\x18\x08 \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\t \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\n \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x0b \x01(\t\x12\x13\n\x0bproduct_qty\x18\x0c \x01(\x01\x12\x19\n\x11pos_ticket_number\x18\r \x01(\t\x12\x16\n\x0e\x62om_product_id\x18\x0e \x01(\x04\x12\x18\n\x10\x62om_product_code\x18\x0f \x01(\t\x12\x18\n\x10\x62om_product_name\x18\x10 \x01(\t\x12\x1e\n\x16\x62om_accounting_unit_id\x18\x11 \x01(\x04\x12 \n\x18\x62om_accounting_unit_code\x18\x12 \x01(\t\x12 \n\x18\x62om_accounting_unit_name\x18\x13 \x01(\t\x12\x0b\n\x03qty\x18\x14 \x01(\x01\x12.\n\nsales_date\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tis_unable\x18\x16 \x01(\x08\x12\x44\n\nproperties\x18\x17 \x03(\x0b\x32\x30.operation_report.SalesExplainedReportProperties\x12\r\n\x05price\x18\x18 \x01(\x01\x12\x0c\n\x04\x63ost\x18\x19 \x01(\x01\"f\n\x1fGetSalesExplainedReportResponse\x12\x34\n\x04rows\x18\x01 \x03(\x0b\x32&.operation_report.SalesExplainedReport\x12\r\n\x05total\x18\x02 \x01(\x04\"\xcc\x04\n%GetSalesExplainedSummaryReportRequest\x12\x35\n\rperiod_filter\x18\x01 \x01(\x0b\x32\x1e.operation_report.PeriodFilter\x12\x35\n\rregion_filter\x18\x02 \x01(\x0b\x32\x1e.operation_report.RegionFilter\x12\x38\n\x0fproduct_filters\x18\x03 \x03(\x0b\x32\x1f.operation_report.ProductFilter\x12<\n\x13\x62om_product_filters\x18\x04 \x03(\x0b\x32\x1f.operation_report.ProductFilter\x12<\n\x0fperiod_group_by\x18\x05 \x01(\x0e\x32#.operation_report.PeriodGroupMethod\x12<\n\x0fregion_group_by\x18\x06 \x01(\x0b\x32#.operation_report.RegionGroupMethod\x12>\n\x10product_group_by\x18\x07 \x01(\x0b\x32$.operation_report.ProductGroupMethod\x12\x42\n\x14\x62om_product_group_by\x18\x08 \x01(\x0b\x32$.operation_report.ProductGroupMethod\x12\r\n\x05limit\x18\t \x01(\x03\x12\x0e\n\x06offset\x18\n \x01(\x04\x12\x11\n\tis_unable\x18\x0b \x01(\t\x12\x0b\n\x03lan\x18\x0c \x01(\t\"\xd6\x05\n\x1bSalesExplainedSummaryReport\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\x12\x13\n\x0b\x65_ticket_id\x18\x02 \x01(\x04\x12\x13\n\x0b\x65ntity_code\x18\x03 \x01(\t\x12\x13\n\x0b\x65ntity_name\x18\x04 \x01(\t\x12\x11\n\tentity_id\x18\x05 \x01(\x04\x12\x12\n\nproduct_id\x18\x06 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x07 \x01(\t\x12\x14\n\x0cproduct_name\x18\x08 \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\t \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\n \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x0b \x01(\t\x12\x13\n\x0bproduct_qty\x18\x0c \x01(\x01\x12\x19\n\x11pos_ticket_number\x18\r \x01(\t\x12\x16\n\x0e\x62om_product_id\x18\x0e \x01(\x04\x12\x18\n\x10\x62om_product_code\x18\x0f \x01(\t\x12\x18\n\x10\x62om_product_name\x18\x10 \x01(\t\x12\x1e\n\x16\x62om_accounting_unit_id\x18\x11 \x01(\x04\x12 \n\x18\x62om_accounting_unit_code\x18\x12 \x01(\t\x12 \n\x18\x62om_accounting_unit_name\x18\x13 \x01(\t\x12\x0b\n\x03qty\x18\x14 \x01(\x01\x12.\n\nsales_date\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tis_unable\x18\x16 \x01(\x08\x12\x44\n\nproperties\x18\x17 \x03(\x0b\x32\x30.operation_report.SalesExplainedReportProperties\x12\r\n\x05price\x18\x18 \x01(\x01\x12\x15\n\rperiod_symbol\x18\x19 \x01(\t\x12\x0c\n\x04\x63ost\x18\x1a \x01(\x01\x12\x11\n\tcost_date\x18\x1b \x01(\t\"\xa6\x01\n&GetSalesExplainedSummaryReportResponse\x12;\n\x04rows\x18\x01 \x03(\x0b\x32-.operation_report.SalesExplainedSummaryReport\x12\r\n\x05total\x18\x02 \x01(\x04\x12\x1b\n\x13summary_product_qty\x18\x03 \x01(\x01\x12\x13\n\x0bsummary_qty\x18\x04 \x01(\x01\";\n\x17GetETicketDetailRequest\x12\x13\n\x0b\x65_ticket_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\xfa\x01\n\x19\x45ticketDetailMaterialInfo\x12\x16\n\x0e\x62om_product_id\x18\x01 \x01(\x04\x12\x18\n\x10\x62om_product_code\x18\x02 \x01(\t\x12\x18\n\x10\x62om_product_name\x18\x03 \x01(\t\x12\x1e\n\x16\x62om_accounting_unit_id\x18\x04 \x01(\x04\x12 \n\x18\x62om_accounting_unit_code\x18\x05 \x01(\t\x12 \n\x18\x62om_accounting_unit_name\x18\x06 \x01(\t\x12\x0b\n\x03qty\x18\x07 \x01(\x01\x12\x11\n\tis_unable\x18\x08 \x01(\x08\x12\r\n\x05price\x18\t \x01(\x01\"w\n\x17\x45ticketDetailMakeUpInfo\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0b\n\x03qty\x18\x03 \x01(\x01\x12\x11\n\tunit_name\x18\x04 \x01(\t\x12\x0c\n\x04type\x18\x05 \x01(\t\x12\x12\n\nis_replace\x18\x06 \x01(\x08\"\xa5\x01\n\x17\x45ticketDetailMakeUpRule\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x10\n\x08priority\x18\x03 \x01(\x01\x12.\n\nstart_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xff\x02\n\x18\x45ticketDetailProductInfo\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x10\n\x08quantity\x18\x03 \x01(\x01\x12\r\n\x05price\x18\x04 \x01(\x01\x12\x0e\n\x06\x61mount\x18\x05 \x01(\x01\x12\x44\n\nproperties\x18\x06 \x03(\x0b\x32\x30.operation_report.SalesExplainedReportProperties\x12\x42\n\rmaterial_info\x18\x07 \x03(\x0b\x32+.operation_report.EticketDetailMaterialInfo\x12?\n\x0cmake_up_info\x18\x08 \x03(\x0b\x32).operation_report.EticketDetailMakeUpInfo\x12?\n\x0cmake_up_rule\x18\t \x03(\x0b\x32).operation_report.EticketDetailMakeUpRule\x12\n\n\x02id\x18\n \x01(\x04\"\xff\x05\n\x18GetETicketDetailResponse\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x32\n\x0eoperation_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x10transaction_time\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x36\n\x12ticket_upload_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x31\n\x0bticket_type\x18\x05 \x01(\x0e\x32\x1c.operation_report.TicketType\x12\x19\n\x11pos_ticket_number\x18\x06 \x01(\t\x12/\n\nstore_info\x18\x07 \x01(\x0b\x32\x1b.operation_report.StoreInfo\x12\x31\n\x0bmember_info\x18\x08 \x03(\x0b\x32\x1c.operation_report.MemberInfo\x12\x35\n\rdelivery_type\x18\t \x01(\x0e\x32\x1e.operation_report.DeliveryType\x12-\n\tdiscounts\x18\n \x03(\x0b\x32\x1a.operation_report.Discount\x12#\n\x04\x66\x65\x65s\x18\x0b \x03(\x0b\x32\x15.operation_report.Fee\x12\x0e\n\x06\x61mount\x18\x0c \x01(\x01\x12+\n\x08payments\x18\r \x03(\x0b\x32\x19.operation_report.Payment\x12\x35\n\rsales_channel\x18\x0e \x01(\x0b\x32\x1e.operation_report.SalesChannel\x12*\n\x07takeway\x18\x0f \x01(\x0b\x32\x19.operation_report.Takeway\x12\x1a\n\x12is_cancelled_order\x18\x10 \x01(\x08\x12<\n\x08products\x18\x11 \x03(\x0b\x32*.operation_report.EticketDetailProductInfo\"\x86\x02\n#GetSalesExplainedErrorReportRequest\x12\x11\n\tstore_ids\x18\x01 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x02 \x03(\x04\x12.\n\nstart_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x19\n\x11pos_ticket_number\x18\x05 \x01(\t\x12\x12\n\nerror_type\x18\x06 \x01(\t\x12\x0e\n\x06offset\x18\x08 \x01(\x04\x12\r\n\x05limit\x18\t \x01(\x03\x12\x0b\n\x03lan\x18\n \x01(\t\"\xd2\x01\n\x13\x42omProductExplained\x12\x16\n\x0e\x62om_product_id\x18\x01 \x01(\x04\x12\x18\n\x10\x62om_product_code\x18\x02 \x01(\t\x12\x18\n\x10\x62om_product_name\x18\x03 \x01(\t\x12\x1e\n\x16\x62om_accounting_unit_id\x18\x04 \x01(\x04\x12 \n\x18\x62om_accounting_unit_code\x18\x05 \x01(\t\x12 \n\x18\x62om_accounting_unit_name\x18\x06 \x01(\t\x12\x0b\n\x03qty\x18\x07 \x01(\x01\"\x90\x03\n\x19SalesExplainedErrorReport\x12\x12\n\nstore_code\x18\x01 \x01(\t\x12\x12\n\nstore_name\x18\x02 \x01(\t\x12\x10\n\x08store_id\x18\x03 \x01(\x04\x12\x12\n\nproduct_id\x18\x04 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x05 \x01(\t\x12\x14\n\x0cproduct_name\x18\x06 \x01(\t\x12\x19\n\x11pos_ticket_number\x18\x07 \x01(\t\x12;\n\x0c\x62om_products\x18\x08 \x03(\x0b\x32%.operation_report.BomProductExplained\x12.\n\nsales_date\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0eoperation_date\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nerror_type\x18\x0b \x01(\t\x12\x14\n\x0c\x65rror_detail\x18\x0c \x01(\t\x12\x13\n\x0bproduct_qty\x18\r \x01(\x01\"p\n$GetSalesExplainedErrorReportResponse\x12\x39\n\x04rows\x18\x01 \x03(\x0b\x32+.operation_report.SalesExplainedErrorReport\x12\r\n\x05total\x18\x02 \x01(\x04\"\xd9\x01\n\x1aGetMaterialSuppliesRequest\x12\x12\n\nstart_date\x18\x01 \x01(\t\x12\x10\n\x08\x65nd_date\x18\x02 \x01(\t\x12\x11\n\tstore_ids\x18\x03 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x04 \x03(\x04\x12\r\n\x05limit\x18\x05 \x01(\x03\x12\x0e\n\x06offset\x18\x06 \x01(\x03\x12\x0b\n\x03lan\x18\x07 \x01(\t\x12\x14\n\x0cledger_class\x18\x08 \x03(\t\x12\x14\n\x0c\x63\x61tegory_ids\x18\t \x03(\x04\x12\x15\n\rexclude_empty\x18\n \x01(\x08\"\xbf\x06\n\x1bGetMaterialSuppliesResponse\x12L\n\x04rows\x18\x01 \x03(\x0b\x32>.operation_report.GetMaterialSuppliesResponse.MaterialSupplies\x12\r\n\x05total\x18\x02 \x01(\x04\x1a\xc2\x05\n\x10MaterialSupplies\x12\x10\n\x08\x62us_date\x18\x01 \x01(\t\x12\x1a\n\x12\x62ranch_region_name\x18\x02 \x01(\t\x12\x12\n\nstore_code\x18\x03 \x01(\t\x12\x12\n\nstore_name\x18\x04 \x01(\t\x12\x14\n\x0cproduct_code\x18\x05 \x01(\t\x12\x14\n\x0cproduct_name\x18\x06 \x01(\t\x12\x11\n\tunit_code\x18\x07 \x01(\t\x12\x11\n\tunit_name\x18\x08 \x01(\t\x12\x1b\n\x13product_category_id\x18\x1d \x01(\t\x12\x15\n\rcategory_code\x18\x1e \x01(\t\x12\x15\n\rcategory_name\x18\x1f \x01(\t\x12\x11\n\tstart_qty\x18\x0b \x01(\x01\x12\x13\n\x0breceive_qty\x18\x0c \x01(\x01\x12\x12\n\nreturn_qty\x18\x10 \x01(\x01\x12\x12\n\nadjust_qty\x18\x0f \x01(\x01\x12\x17\n\x0ftransfer_in_qty\x18\r \x01(\x01\x12\x18\n\x10transfer_out_qty\x18\x0e \x01(\x01\x12\x0f\n\x07\x65nd_qty\x18\x11 \x01(\x01\x12\x19\n\x11\x63\x61lculate_use_qty\x18\x12 \x01(\x01\x12\x16\n\x0e\x63\x61lculate_cost\x18\x15 \x01(\x01\x12\x16\n\x0e\x61\x63tual_use_qty\x18\x13 \x01(\x01\x12\x13\n\x0b\x61\x63tual_cost\x18\x14 \x01(\x01\x12\x10\n\x08\x64iff_qty\x18\x16 \x01(\x01\x12\x11\n\tdiff_cost\x18\x17 \x01(\x01\x12\x17\n\x0f\x64iff_percentage\x18\x18 \x01(\x01\x12\x10\n\x08store_id\x18\x19 \x01(\x04\x12\x12\n\nproduct_id\x18\x1a \x01(\x04\x12\r\n\x05price\x18\x1b \x01(\x01\x12\x13\n\x0bjde_end_qty\x18\x1c \x01(\x01\x12\x1e\n\x16material_trans_deposit\x18  \x01(\x01\x12\x1f\n\x17material_trans_withdraw\x18! \x01(\x01\"\xc1\x02\n\x1cGetMaterialCostReportRequest\x12.\n\nstart_date\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tperiod_id\x18\x01 \x01(\x04\x12\x13\n\x0bperiod_name\x18\x12 \x01(\t\x12\x16\n\x0e\x63ost_center_id\x18\x02 \x01(\x04\x12\x13\n\x0bproduct_ids\x18\x04 \x03(\x04\x12\r\n\x05order\x18\n \x01(\t\x12\x0c\n\x04sort\x18\x0b \x01(\t\x12\x0e\n\x06offset\x18\x0c \x01(\x04\x12\r\n\x05limit\x18\r \x01(\x04\x12\x14\n\x0c\x66ilter_empty\x18\x0f \x01(\x08\x12\x1c\n\x14product_category_ids\x18\x15 \x03(\x04\"\xeb\x07\n\x08\x43ostData\x12\x11\n\tperiod_id\x18\x01 \x01(\x04\x12\x13\n\x0bperiod_name\x18\x02 \x01(\t\x12\x16\n\x0e\x63ost_center_id\x18\x03 \x01(\x04\x12\x13\n\x0b\x63\x65nter_name\x18\x04 \x01(\t\x12\x13\n\x0b\x63\x65nter_code\x18\x05 \x01(\t\x12\x12\n\nproduct_id\x18\x06 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x14\n\x0cproduct_code\x18\x08 \x01(\t\x12\x14\n\x0cproduct_type\x18\t \x01(\t\x12\x16\n\x0eproduct_status\x18\n \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x0b \x01(\x04\x12\x15\n\rcategory_name\x18\x0c \x01(\t\x12\x1a\n\x12parent_category_id\x18\r \x01(\x04\x12\x1c\n\x14parent_category_name\x18\x0e \x01(\t\x12\x0f\n\x07unit_id\x18\x0f \x01(\x04\x12\x11\n\tunit_name\x18\x10 \x01(\t\x12\x11\n\tunit_code\x18\x11 \x01(\t\x12\x16\n\x0estart_quantity\x18\x12 \x01(\x01\x12\x12\n\nstart_cost\x18\x13 \x01(\x01\x12\x14\n\x0cstart_amount\x18\x14 \x01(\x01\x12\x14\n\x0c\x63urrent_cost\x18\x15 \x01(\x01\x12\x18\n\x10receive_quantity\x18\x16 \x01(\x01\x12\x16\n\x0ereceive_amount\x18\x17 \x01(\x01\x12\x17\n\x0freturn_quantity\x18\x18 \x01(\x01\x12\x15\n\rreturn_amount\x18\x19 \x01(\x01\x12\x15\n\radjust_amount\x18\x1a \x01(\x01\x12\x13\n\x0bproduct_qty\x18\x1b \x01(\x01\x12\x16\n\x0eproduct_amount\x18\x1c \x01(\x01\x12.\n\nstart_date\x18\x1d \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x1e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12-\n\tlast_date\x18\x1f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x63ost\x18  \x01(\t\x12\x12\n\nerror_code\x18! \x01(\t\x12\x11\n\tcategory1\x18\" \x01(\t\x12\x11\n\tcategory2\x18# \x01(\t\x12\x11\n\tcategory3\x18$ \x01(\t\x12\x11\n\tcategory4\x18% \x01(\t\x12\x11\n\tcategory5\x18& \x01(\t\x12\x11\n\tcategory6\x18\' \x01(\t\x12\x11\n\tcategory7\x18( \x01(\t\x12\x11\n\tcategory8\x18) \x01(\t\x12\x11\n\tcategory9\x18* \x01(\t\x12\x12\n\ncategory10\x18+ \x01(\t\"\xab\x01\n\x1dGetMaterialCostReportResponse\x12(\n\x04rows\x18\x01 \x03(\x0b\x32\x1a.operation_report.CostData\x12\r\n\x05total\x18\x02 \x01(\x04\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12,\n\x08\x65nd_time\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\x81\x03\n\x1bGetBalanceCostReportRequest\x12\x11\n\tperiod_id\x18\x01 \x01(\x04\x12\x13\n\x0bperiod_name\x18\x17 \x01(\t\x12\x16\n\x0e\x63ost_center_id\x18\x02 \x01(\x04\x12\x12\n\nbranch_ids\x18\x03 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x05 \x03(\x04\x12\r\n\x05order\x18\x0b \x01(\t\x12\x0c\n\x04sort\x18\x0c \x01(\t\x12\x0e\n\x06offset\x18\r \x01(\x04\x12\r\n\x05limit\x18\x0e \x01(\x04\x12\x16\n\x0e\x62ranch_regions\x18\x0f \x03(\x04\x12\x13\n\x0b\x62ranch_type\x18\x10 \x01(\t\x12\x14\n\x0c\x66ilter_empty\x18\x13 \x01(\x08\x12\x1c\n\x14product_category_ids\x18\x14 \x03(\x04\x12.\n\nstart_date\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"Z\n\x1cGetBalanceCostReportResponse\x12+\n\x04rows\x18\x01 \x03(\x0b\x32\x1d.operation_report.BalanceData\x12\r\n\x05total\x18\x02 \x01(\x04\"\xa4\x0b\n\x0b\x42\x61lanceData\x12\x16\n\x0e\x63ost_center_id\x18\x01 \x01(\x04\x12\x13\n\x0b\x63\x65nter_name\x18\x02 \x01(\t\x12\x13\n\x0b\x63\x65nter_code\x18\x03 \x01(\t\x12\x10\n\x08periodID\x18\x04 \x01(\x04\x12\x15\n\rperiod_status\x18\x05 \x01(\t\x12\x13\n\x0bperiod_name\x18\x06 \x01(\t\x12\x11\n\tbranch_id\x18\x07 \x01(\x04\x12\x13\n\x0b\x62ranch_code\x18\x08 \x01(\t\x12\x13\n\x0b\x62ranch_name\x18\t \x01(\t\x12\x12\n\nproduct_id\x18\n \x01(\x04\x12\x14\n\x0cproduct_name\x18\x0b \x01(\t\x12\x14\n\x0cproduct_code\x18\x0c \x01(\t\x12\x16\n\x0eproduct_status\x18\r \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x0e \x01(\x04\x12\x15\n\rcategory_name\x18\x0f \x01(\t\x12\x1a\n\x12parent_category_id\x18\x10 \x01(\x04\x12\x1c\n\x14parent_category_name\x18\x11 \x01(\t\x12\x0c\n\x04spec\x18\x12 \x01(\t\x12\x0f\n\x07unit_id\x18\x13 \x01(\x04\x12\x11\n\tunit_name\x18\x14 \x01(\t\x12\x11\n\tunit_code\x18\x15 \x01(\t\x12\x16\n\x0estart_quantity\x18\x16 \x01(\x01\x12\x12\n\nstart_cost\x18\x17 \x01(\x01\x12\x14\n\x0cstart_amount\x18\x18 \x01(\x01\x12\x14\n\x0c\x65nd_quantity\x18\x19 \x01(\x01\x12\x14\n\x0c\x63urrent_cost\x18\x1a \x01(\x01\x12\x12\n\nend_amount\x18\x1b \x01(\x01\x12\x13\n\x0bself_in_qty\x18\x1c \x01(\x01\x12\x16\n\x0eself_in_amount\x18\x1d \x01(\x01\x12\x18\n\x10receive_quantity\x18\x1e \x01(\x01\x12\x14\n\x0creceive_cost\x18\x1f \x01(\x01\x12\x17\n\x0freturn_quantity\x18  \x01(\x01\x12\x13\n\x0breturn_cost\x18! \x01(\x01\x12\x13\n\x0b\x64\x65livery_in\x18\" \x01(\x01\x12\x18\n\x10\x64\x65livery_in_cost\x18# \x01(\x01\x12\x14\n\x0c\x64\x65livery_out\x18$ \x01(\x01\x12\x19\n\x11\x64\x65livery_out_cost\x18% \x01(\x01\x12\x18\n\x10transfer_out_qty\x18& \x01(\x01\x12\x19\n\x11transfer_out_cost\x18\' \x01(\x01\x12\x17\n\x0ftransfer_in_qty\x18( \x01(\x01\x12\x18\n\x10transfer_in_cost\x18) \x01(\x01\x12\x12\n\nbroker_qty\x18* \x01(\x01\x12\x13\n\x0b\x62roker_cost\x18+ \x01(\x01\x12\x16\n\x0escrap_quantity\x18, \x01(\x01\x12\x14\n\x0cscrap_amount\x18- \x01(\x01\x12\x15\n\rsale_quantity\x18. \x01(\x01\x12\x11\n\tsale_cost\x18/ \x01(\x01\x12\x16\n\x0einventory_diff\x18\x30 \x01(\x01\x12\x1b\n\x13inventory_diff_cost\x18\x31 \x01(\x01\x12.\n\nstart_date\x18\x32 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x33 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12-\n\tlast_date\x18\x34 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tcategory1\x18@ \x01(\t\x12\x11\n\tcategory2\x18\x41 \x01(\t\x12\x11\n\tcategory3\x18\x42 \x01(\t\x12\x11\n\tcategory4\x18\x43 \x01(\t\x12\x11\n\tcategory5\x18\x44 \x01(\t\x12\x11\n\tcategory6\x18\x45 \x01(\t\x12\x11\n\tcategory7\x18\x46 \x01(\t\x12\x11\n\tcategory8\x18G \x01(\t\x12\x11\n\tcategory9\x18H \x01(\t\x12\x12\n\ncategory10\x18I \x01(\t*O\n\nTicketType\x12\x13\n\x0f\x41LL_TICKET_TYPE\x10\x00\x12\t\n\x05ORDER\x10\x01\x12\t\n\x05PAYIN\x10\x02\x12\n\n\x06PAYOUT\x10\x03\x12\n\n\x06\x43\x41NCEL\x10\x04*^\n\x0c\x44\x65liveryType\x12\x15\n\x11\x41LL_DELIVERY_TYPE\x10\x00\x12\x0c\n\x08IN_STORE\x10\x01\x12\r\n\tTAKE_AWAY\x10\x02\x12\r\n\tSELF_PICK\x10\x03\x12\x0b\n\x07\x44\x45LIVER\x10\x04*\x87\x01\n\x11PeriodGroupMethod\x12\n\n\x06\x42Y_DAY\x10\x00\x12\x0c\n\x08\x42Y_MONTH\x10\x01\x12\x0b\n\x07\x42Y_YEAR\x10\x02\x12\x12\n\x0e\x42Y_DAY_OF_WEEK\x10\x03\x12\x0e\n\nBY_QUARTER\x10\x04\x12\x0b\n\x07\x42Y_WEEK\x10\x05\x12\x0b\n\x07\x42Y_HOUR\x10\x06\x12\r\n\tBY_MINUTE\x10\x07*:\n\nRegionType\x12\t\n\x05STORE\x10\x00\x12\x0e\n\nGEO_REGION\x10\x01\x12\x11\n\rBRANCH_REGION\x10\x02*,\n\tStoreType\x12\r\n\tALL_STORE\x10\x00\x12\x07\n\x03\x44RS\x10\x01\x12\x07\n\x03\x46RS\x10\x02*B\n\x11ProductFilterType\x12\x15\n\x11\x46ILTER_BY_PRODUCT\x10\x00\x12\x16\n\x12\x46ILTER_BY_CATEGORY\x10\x01*>\n\x17SalesChannelGroupMethod\x12\x10\n\x0c\x41LL_CHANNELS\x10\x00\x12\x11\n\rBY_CHANNEL_ID\x10\x01*=\n\x0eProductGroupBy\x12\x14\n\x10GROUP_BY_PRODUCT\x10\x00\x12\x15\n\x11GROUP_BY_CATEGORY\x10\x01*o\n\x10\x44iscountFilterBy\x12\x1d\n\x19\x46ILTER_BY_DISCOUNT_METHOD\x10\x00\x12\x1f\n\x1b\x46ILTER_BY_DISCOUNT_CATEGORY\x10\x01\x12\x1b\n\x17\x46ILTER_BY_DISCOUNT_TYPE\x10\x02*o\n\x13\x44iscountGroupMethod\x12\x1c\n\x18GROUP_BY_DISCOUNT_METHOD\x10\x00\x12\x1e\n\x1aGROUP_BY_DISCOUNT_CATEGORY\x10\x01\x12\x1a\n\x16GROUP_BY_DISCOUNT_TYPE\x10\x02*O\n\x0fPaymentFilterBy\x12\x1c\n\x18\x46ILTER_BY_PAYMENT_METHOD\x10\x00\x12\x1e\n\x1a\x46ILTER_BY_PAYMENT_CATEGORY\x10\x01*L\n\x0ePaymentGroupBy\x12\x1b\n\x17GROUP_BY_PAYMENT_METHOD\x10\x00\x12\x1d\n\x19GROUP_BY_PAYMENT_CATEGORY\x10\x01*J\n\x17\x44\x65liveryTypeGroupMethod\x12\x16\n\x12\x41LL_DELIVERY_TYPES\x10\x00\x12\x17\n\x13\x42Y_DELIVERY_TYPE_ID\x10\x01\x32\xf0$\n\x0fOperationReport\x12S\n\x04Ping\x12\x16.operation_report.Null\x1a\x16.operation_report.Pong\"\x1b\x82\xd3\xe4\x93\x02\x15\x12\x13/api/v2/report/ping\x12\x94\x01\n\x10GetETicketReport\x12).operation_report.GetETicketReportRequest\x1a*.operation_report.GetETicketReportResponse\")\x82\xd3\xe4\x93\x02#\"\x1e/api/v2/report/e_ticket_report:\x01*\x12\x9f\x01\n\x13GetStoreSalesReport\x12,.operation_report.GetStoreSalesReportRequest\x1a,.operation_report.GetStoreSalesReportReponse\",\x82\xd3\xe4\x93\x02&\"!/api/v2/report/store_sales_report:\x01*\x12\xa8\x01\n\x15GetProductSalesReport\x12..operation_report.GetProductSalesReportRequest\x1a/.operation_report.GetProductSalesReportResponse\".\x82\xd3\xe4\x93\x02(\"#/api/v2/report/product_sales_report:\x01*\x12\x90\x01\n\x11GetDiscountReport\x12*.operation_report.GetDiscountReportRequest\x1a+.operation_report.GetDiscountReportResponse\"\"\x82\xd3\xe4\x93\x02\x1c\"\x17/api/v2/report/discount:\x01*\x12\x8c\x01\n\x10GetPaymentReport\x12).operation_report.GetPaymentReportRequest\x1a*.operation_report.GetPaymentReportResponse\"!\x82\xd3\xe4\x93\x02\x1b\"\x16/api/v2/report/payment:\x01*\x12\xa9\x01\n\x17GetProductPaymentReport\x12\x30.operation_report.GetProductPaymentReportRequest\x1a\x31.operation_report.GetProductPaymentReportResponse\")\x82\xd3\xe4\x93\x02#\"\x1e/api/v2/report/product_payment:\x01*\x12\xa1\x01\n\x15GetStoreChannelReport\x12..operation_report.GetStoreChannelReportRequest\x1a/.operation_report.GetStoreChannelReportResponse\"\'\x82\xd3\xe4\x93\x02!\"\x1c/api/v2/report/store_channel:\x01*\x12\xa9\x01\n\x17GetProductChannelReport\x12\x30.operation_report.GetProductChannelReportRequest\x1a\x31.operation_report.GetProductChannelReportResponse\")\x82\xd3\xe4\x93\x02#\"\x1e/api/v2/report/product_channel:\x01*\x12\x9d\x01\n\x14GetStorePeriodReport\x12-.operation_report.GetStorePeriodReportRequest\x1a..operation_report.GetStorePeriodReportResponse\"&\x82\xd3\xe4\x93\x02 \"\x1b/api/v2/report/store_period:\x01*\x12\xba\x01\n\x1bGetStoreChannelPeriodReport\x12\x34.operation_report.GetStoreChannelPeriodReportRequest\x1a\x35.operation_report.GetStoreChannelPeriodReportResponse\".\x82\xd3\xe4\x93\x02(\"#/api/v2/report/store_channel_period:\x01*\x12\xa5\x01\n\x16GetPaymentPeriodReport\x12/.operation_report.GetPaymentPeriodReportRequest\x1a\x30.operation_report.GetPaymentPeriodReportResponse\"(\x82\xd3\xe4\x93\x02\"\"\x1d/api/v2/report/payment_period:\x01*\x12\xa5\x01\n\x16GetProductPeriodReport\x12/.operation_report.GetProductPeriodReportRequest\x1a\x30.operation_report.GetProductPeriodReportResponse\"(\x82\xd3\xe4\x93\x02\"\"\x1d/api/v2/report/product_period:\x01*\x12\xba\x01\n\x19GetParseFaliedETicketList\x12\x32.operation_report.GetParseFaliedETicketListRequest\x1a\x33.operation_report.GetParseFaliedETicketListResponse\"4\x82\xd3\xe4\x93\x02.\")/api/v2/report/parse_failed_e_ticket_list:\x01*\x12~\n\x15ReParseFailedETickets\x12\x16.operation_report.Null\x1a\x18.operation_report.Result\"3\x82\xd3\xe4\x93\x02-\"(/api/v2/report/re_parse_failed_e_tickets:\x01*\x12\xb0\x01\n\x19GetStoreSalesReportForPDA\x12\x32.operation_report.GetStoreSalesReportForPDARequest\x1a\x33.operation_report.GetStoreSalesReportForPDAResponse\"*\x82\xd3\xe4\x93\x02$\x12\"/api/v2/report/store_sales_for_pda\x12\x9a\x01\n\x12GetEticketTraceIds\x12+.operation_report.GetETicketTraceIdsRequest\x1a,.operation_report.GetETicketTraceIdsResponse\")\x82\xd3\xe4\x93\x02#\x12!/api/v2/report/e_ticket_trace_ids\x12\xb2\x01\n\x18GetProductSalesOffDemand\x12\x31.operation_report.GetProductSalesOffDemandRequest\x1a\x32.operation_report.GetProductSalesOffDemandResponse\"/\x82\xd3\xe4\x93\x02)\x12\'/api/v2/report/product_sales_off_demand\x12\xb7\x01\n\x19GetProductSalesOffDemand2\x12\x32.operation_report.GetProductSalesOffDemandRequest2\x1a\x33.operation_report.GetProductSalesOffDemandResponse2\"1\x82\xd3\xe4\x93\x02+\x12)/api/v2/report/product_sales_off_demand_2\x12\xad\x01\n\x17GetSalesExplainedReport\x12\x30.operation_report.GetSalesExplainedReportRequest\x1a\x31.operation_report.GetSalesExplainedReportResponse\"-\x82\xd3\xe4\x93\x02\'\x12%/api/v2/report/sales_explained_report\x12\xcd\x01\n\x1eGetSalesExplainedSummaryReport\x12\x37.operation_report.GetSalesExplainedSummaryReportRequest\x1a\x38.operation_report.GetSalesExplainedSummaryReportResponse\"8\x82\xd3\xe4\x93\x02\x32\"-/api/v2/report/sales_explained_summary_report:\x01*\x12\x94\x01\n\x10GetETicketDetail\x12).operation_report.GetETicketDetailRequest\x1a*.operation_report.GetETicketDetailResponse\")\x82\xd3\xe4\x93\x02#\"\x1e/api/v2/report/e_ticket_detail:\x01*\x12\xc2\x01\n\x1cGetSalesExplainedErrorReport\x12\x35.operation_report.GetSalesExplainedErrorReportRequest\x1a\x36.operation_report.GetSalesExplainedErrorReportResponse\"3\x82\xd3\xe4\x93\x02-\x12+/api/v2/report/sales_explained_error_report\x12\x98\x01\n\x13GetMaterialSupplies\x12,.operation_report.GetMaterialSuppliesRequest\x1a-.operation_report.GetMaterialSuppliesResponse\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/api/v2/report/material_loss\x12\xc2\x01\n!GetSalesExplainedSupplementReport\x12\x30.operation_report.GetSalesExplainedReportRequest\x1a\x31.operation_report.GetSalesExplainedReportResponse\"8\x82\xd3\xe4\x93\x02\x32\x12\x30/api/v2/report/sales_explained_supplement_report\x12\xe2\x01\n(GetSalesExplainedSupplementSummaryReport\x12\x37.operation_report.GetSalesExplainedSummaryReportRequest\x1a\x38.operation_report.GetSalesExplainedSummaryReportResponse\"C\x82\xd3\xe4\x93\x02=\"8/api/v2/report/sales_explained_supplement_summary_report:\x01*\x12\x9e\x01\n\x15GetMaterialCostReport\x12..operation_report.GetMaterialCostReportRequest\x1a/.operation_report.GetMaterialCostReportResponse\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/api/v2/report/cost/material\x12\x9a\x01\n\x14GetBalanceCostReport\x12-.operation_report.GetBalanceCostReportRequest\x1a..operation_report.GetBalanceCostReportResponse\"#\x82\xd3\xe4\x93\x02\x1d\x12\x1b/api/v2/report/cost/balanceb\x06proto3')
  ,
  dependencies=[google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,google_dot_api_dot_annotations__pb2.DESCRIPTOR,])

_TICKETTYPE = _descriptor.EnumDescriptor(
  name='TicketType',
  full_name='operation_report.TicketType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ALL_TICKET_TYPE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ORDER', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PAYIN', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PAYOUT', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCEL', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=28616,
  serialized_end=28695,
)
_sym_db.RegisterEnumDescriptor(_TICKETTYPE)

TicketType = enum_type_wrapper.EnumTypeWrapper(_TICKETTYPE)
_DELIVERYTYPE = _descriptor.EnumDescriptor(
  name='DeliveryType',
  full_name='operation_report.DeliveryType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ALL_DELIVERY_TYPE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='IN_STORE', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TAKE_AWAY', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SELF_PICK', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DELIVER', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=28697,
  serialized_end=28791,
)
_sym_db.RegisterEnumDescriptor(_DELIVERYTYPE)

DeliveryType = enum_type_wrapper.EnumTypeWrapper(_DELIVERYTYPE)
_PERIODGROUPMETHOD = _descriptor.EnumDescriptor(
  name='PeriodGroupMethod',
  full_name='operation_report.PeriodGroupMethod',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='BY_DAY', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BY_MONTH', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BY_YEAR', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BY_DAY_OF_WEEK', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BY_QUARTER', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BY_WEEK', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BY_HOUR', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BY_MINUTE', index=7, number=7,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=28794,
  serialized_end=28929,
)
_sym_db.RegisterEnumDescriptor(_PERIODGROUPMETHOD)

PeriodGroupMethod = enum_type_wrapper.EnumTypeWrapper(_PERIODGROUPMETHOD)
_REGIONTYPE = _descriptor.EnumDescriptor(
  name='RegionType',
  full_name='operation_report.RegionType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='STORE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GEO_REGION', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BRANCH_REGION', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=28931,
  serialized_end=28989,
)
_sym_db.RegisterEnumDescriptor(_REGIONTYPE)

RegionType = enum_type_wrapper.EnumTypeWrapper(_REGIONTYPE)
_STORETYPE = _descriptor.EnumDescriptor(
  name='StoreType',
  full_name='operation_report.StoreType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ALL_STORE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DRS', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FRS', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=28991,
  serialized_end=29035,
)
_sym_db.RegisterEnumDescriptor(_STORETYPE)

StoreType = enum_type_wrapper.EnumTypeWrapper(_STORETYPE)
_PRODUCTFILTERTYPE = _descriptor.EnumDescriptor(
  name='ProductFilterType',
  full_name='operation_report.ProductFilterType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='FILTER_BY_PRODUCT', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FILTER_BY_CATEGORY', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=29037,
  serialized_end=29103,
)
_sym_db.RegisterEnumDescriptor(_PRODUCTFILTERTYPE)

ProductFilterType = enum_type_wrapper.EnumTypeWrapper(_PRODUCTFILTERTYPE)
_SALESCHANNELGROUPMETHOD = _descriptor.EnumDescriptor(
  name='SalesChannelGroupMethod',
  full_name='operation_report.SalesChannelGroupMethod',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ALL_CHANNELS', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BY_CHANNEL_ID', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=29105,
  serialized_end=29167,
)
_sym_db.RegisterEnumDescriptor(_SALESCHANNELGROUPMETHOD)

SalesChannelGroupMethod = enum_type_wrapper.EnumTypeWrapper(_SALESCHANNELGROUPMETHOD)
_PRODUCTGROUPBY = _descriptor.EnumDescriptor(
  name='ProductGroupBy',
  full_name='operation_report.ProductGroupBy',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='GROUP_BY_PRODUCT', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GROUP_BY_CATEGORY', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=29169,
  serialized_end=29230,
)
_sym_db.RegisterEnumDescriptor(_PRODUCTGROUPBY)

ProductGroupBy = enum_type_wrapper.EnumTypeWrapper(_PRODUCTGROUPBY)
_DISCOUNTFILTERBY = _descriptor.EnumDescriptor(
  name='DiscountFilterBy',
  full_name='operation_report.DiscountFilterBy',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='FILTER_BY_DISCOUNT_METHOD', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FILTER_BY_DISCOUNT_CATEGORY', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FILTER_BY_DISCOUNT_TYPE', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=29232,
  serialized_end=29343,
)
_sym_db.RegisterEnumDescriptor(_DISCOUNTFILTERBY)

DiscountFilterBy = enum_type_wrapper.EnumTypeWrapper(_DISCOUNTFILTERBY)
_DISCOUNTGROUPMETHOD = _descriptor.EnumDescriptor(
  name='DiscountGroupMethod',
  full_name='operation_report.DiscountGroupMethod',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='GROUP_BY_DISCOUNT_METHOD', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GROUP_BY_DISCOUNT_CATEGORY', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GROUP_BY_DISCOUNT_TYPE', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=29345,
  serialized_end=29456,
)
_sym_db.RegisterEnumDescriptor(_DISCOUNTGROUPMETHOD)

DiscountGroupMethod = enum_type_wrapper.EnumTypeWrapper(_DISCOUNTGROUPMETHOD)
_PAYMENTFILTERBY = _descriptor.EnumDescriptor(
  name='PaymentFilterBy',
  full_name='operation_report.PaymentFilterBy',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='FILTER_BY_PAYMENT_METHOD', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FILTER_BY_PAYMENT_CATEGORY', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=29458,
  serialized_end=29537,
)
_sym_db.RegisterEnumDescriptor(_PAYMENTFILTERBY)

PaymentFilterBy = enum_type_wrapper.EnumTypeWrapper(_PAYMENTFILTERBY)
_PAYMENTGROUPBY = _descriptor.EnumDescriptor(
  name='PaymentGroupBy',
  full_name='operation_report.PaymentGroupBy',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='GROUP_BY_PAYMENT_METHOD', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='GROUP_BY_PAYMENT_CATEGORY', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=29539,
  serialized_end=29615,
)
_sym_db.RegisterEnumDescriptor(_PAYMENTGROUPBY)

PaymentGroupBy = enum_type_wrapper.EnumTypeWrapper(_PAYMENTGROUPBY)
_DELIVERYTYPEGROUPMETHOD = _descriptor.EnumDescriptor(
  name='DeliveryTypeGroupMethod',
  full_name='operation_report.DeliveryTypeGroupMethod',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ALL_DELIVERY_TYPES', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BY_DELIVERY_TYPE_ID', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=29617,
  serialized_end=29691,
)
_sym_db.RegisterEnumDescriptor(_DELIVERYTYPEGROUPMETHOD)

DeliveryTypeGroupMethod = enum_type_wrapper.EnumTypeWrapper(_DELIVERYTYPEGROUPMETHOD)
ALL_TICKET_TYPE = 0
ORDER = 1
PAYIN = 2
PAYOUT = 3
CANCEL = 4
ALL_DELIVERY_TYPE = 0
IN_STORE = 1
TAKE_AWAY = 2
SELF_PICK = 3
DELIVER = 4
BY_DAY = 0
BY_MONTH = 1
BY_YEAR = 2
BY_DAY_OF_WEEK = 3
BY_QUARTER = 4
BY_WEEK = 5
BY_HOUR = 6
BY_MINUTE = 7
STORE = 0
GEO_REGION = 1
BRANCH_REGION = 2
ALL_STORE = 0
DRS = 1
FRS = 2
FILTER_BY_PRODUCT = 0
FILTER_BY_CATEGORY = 1
ALL_CHANNELS = 0
BY_CHANNEL_ID = 1
GROUP_BY_PRODUCT = 0
GROUP_BY_CATEGORY = 1
FILTER_BY_DISCOUNT_METHOD = 0
FILTER_BY_DISCOUNT_CATEGORY = 1
FILTER_BY_DISCOUNT_TYPE = 2
GROUP_BY_DISCOUNT_METHOD = 0
GROUP_BY_DISCOUNT_CATEGORY = 1
GROUP_BY_DISCOUNT_TYPE = 2
FILTER_BY_PAYMENT_METHOD = 0
FILTER_BY_PAYMENT_CATEGORY = 1
GROUP_BY_PAYMENT_METHOD = 0
GROUP_BY_PAYMENT_CATEGORY = 1
ALL_DELIVERY_TYPES = 0
BY_DELIVERY_TYPE_ID = 1



_NULL = _descriptor.Descriptor(
  name='Null',
  full_name='operation_report.Null',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=114,
  serialized_end=120,
)


_PONG = _descriptor.Descriptor(
  name='Pong',
  full_name='operation_report.Pong',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='msg', full_name='operation_report.Pong.msg', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=122,
  serialized_end=141,
)


_RESULT = _descriptor.Descriptor(
  name='Result',
  full_name='operation_report.Result',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='action_result', full_name='operation_report.Result.action_result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='message', full_name='operation_report.Result.message', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=143,
  serialized_end=191,
)


_GETETICKETREPORTREQUEST = _descriptor.Descriptor(
  name='GetETicketReportRequest',
  full_name='operation_report.GetETicketReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='operation_report.GetETicketReportRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='operation_report.GetETicketReportRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='operation_report.GetETicketReportRequest.store_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_method_ids', full_name='operation_report.GetETicketReportRequest.payment_method_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='discount_method_ids', full_name='operation_report.GetETicketReportRequest.discount_method_ids', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='operation_report.GetETicketReportRequest.product_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='member_card_number', full_name='operation_report.GetETicketReportRequest.member_card_number', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pos_ticket_number', full_name='operation_report.GetETicketReportRequest.pos_ticket_number', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ticket_type', full_name='operation_report.GetETicketReportRequest.ticket_type', index=8,
      number=9, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_type', full_name='operation_report.GetETicketReportRequest.delivery_type', index=9,
      number=10, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetETicketReportRequest.offset', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetETicketReportRequest.limit', index=11,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trace_id', full_name='operation_report.GetETicketReportRequest.trace_id', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount_legal', full_name='operation_report.GetETicketReportRequest.amount_legal', index=13,
      number=14, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetETicketReportRequest.lan', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=194,
  serialized_end=653,
)


_GETETICKETREPORTRESPONSE = _descriptor.Descriptor(
  name='GetETicketReportResponse',
  full_name='operation_report.GetETicketReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='operation_report.GetETicketReportResponse.total', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='operation_report.GetETicketReportResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=655,
  serialized_end=743,
)


_ETICKETREPORT = _descriptor.Descriptor(
  name='ETicketReport',
  full_name='operation_report.ETicketReport',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='operation_report.ETicketReport.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='operation_date', full_name='operation_report.ETicketReport.operation_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transaction_time', full_name='operation_report.ETicketReport.transaction_time', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ticket_upload_time', full_name='operation_report.ETicketReport.ticket_upload_time', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ticket_type', full_name='operation_report.ETicketReport.ticket_type', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pos_ticket_number', full_name='operation_report.ETicketReport.pos_ticket_number', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_info', full_name='operation_report.ETicketReport.store_info', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='member_info', full_name='operation_report.ETicketReport.member_info', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_type', full_name='operation_report.ETicketReport.delivery_type', index=8,
      number=9, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='operation_report.ETicketReport.products', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='discounts', full_name='operation_report.ETicketReport.discounts', index=10,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fees', full_name='operation_report.ETicketReport.fees', index=11,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='operation_report.ETicketReport.amount', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payments', full_name='operation_report.ETicketReport.payments', index=13,
      number=14, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_channel', full_name='operation_report.ETicketReport.sales_channel', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='takeway', full_name='operation_report.ETicketReport.takeway', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_cancelled_order', full_name='operation_report.ETicketReport.is_cancelled_order', index=16,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_amount_legal', full_name='operation_report.ETicketReport.net_amount_legal', index=17,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_amount_legal', full_name='operation_report.ETicketReport.product_amount_legal', index=18,
      number=19, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_amount_legal', full_name='operation_report.ETicketReport.payment_amount_legal', index=19,
      number=20, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=746,
  serialized_end=1568,
)


_TAKEWAY = _descriptor.Descriptor(
  name='Takeway',
  full_name='operation_report.Takeway',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_method', full_name='operation_report.Takeway.order_method', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_time', full_name='operation_report.Takeway.order_time', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tp_order_id', full_name='operation_report.Takeway.tp_order_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='send_fee', full_name='operation_report.Takeway.send_fee', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='package_fee', full_name='operation_report.Takeway.package_fee', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1570,
  serialized_end=1681,
)


_STOREINFO = _descriptor.Descriptor(
  name='StoreInfo',
  full_name='operation_report.StoreInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='operation_report.StoreInfo.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='operation_report.StoreInfo.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='operation_report.StoreInfo.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1683,
  serialized_end=1734,
)


_MEMBERINFO = _descriptor.Descriptor(
  name='MemberInfo',
  full_name='operation_report.MemberInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='member_id', full_name='operation_report.MemberInfo.member_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='member_card_number', full_name='operation_report.MemberInfo.member_card_number', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='member_card_name', full_name='operation_report.MemberInfo.member_card_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1736,
  serialized_end=1821,
)


_ITEM = _descriptor.Descriptor(
  name='Item',
  full_name='operation_report.Item',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sequence_number', full_name='operation_report.Item.sequence_number', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_info', full_name='operation_report.Item.product_info', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='operation_report.Item.quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='operation_report.Item.price', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount_without_mixtures', full_name='operation_report.Item.amount_without_mixtures', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mixtures', full_name='operation_report.Item.mixtures', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount_of_mixtures', full_name='operation_report.Item.amount_of_mixtures', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount_with_mixtures', full_name='operation_report.Item.amount_with_mixtures', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_discounts', full_name='operation_report.Item.item_discounts', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_subtotal', full_name='operation_report.Item.item_subtotal', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1824,
  serialized_end=2152,
)


_MIXTURE = _descriptor.Descriptor(
  name='Mixture',
  full_name='operation_report.Mixture',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_info', full_name='operation_report.Mixture.product_info', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='operation_report.Mixture.price', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='operation_report.Mixture.quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='operation_report.Mixture.amount', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2154,
  serialized_end=2265,
)


_PAYMENT = _descriptor.Descriptor(
  name='Payment',
  full_name='operation_report.Payment',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payment_uuid', full_name='operation_report.Payment.payment_uuid', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_method_id', full_name='operation_report.Payment.payment_method_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_method_code', full_name='operation_report.Payment.payment_method_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_method_name', full_name='operation_report.Payment.payment_method_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='operation_report.Payment.amount', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2268,
  serialized_end=2400,
)


_SALESCHANNEL = _descriptor.Descriptor(
  name='SalesChannel',
  full_name='operation_report.SalesChannel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='operation_report.SalesChannel.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='operation_report.SalesChannel.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='operation_report.SalesChannel.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_online_channel', full_name='operation_report.SalesChannel.is_online_channel', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2402,
  serialized_end=2483,
)


_DISCOUNT = _descriptor.Descriptor(
  name='Discount',
  full_name='operation_report.Discount',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='operation_report.Discount.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='operation_report.Discount.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='operation_report.Discount.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='operation_report.Discount.amount', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2485,
  serialized_end=2551,
)


_PRODUCTINFO = _descriptor.Descriptor(
  name='ProductInfo',
  full_name='operation_report.ProductInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='operation_report.ProductInfo.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='operation_report.ProductInfo.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='operation_report.ProductInfo.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category', full_name='operation_report.ProductInfo.category', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2553,
  serialized_end=2624,
)


_FEE = _descriptor.Descriptor(
  name='Fee',
  full_name='operation_report.Fee',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='operation_report.Fee.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='operation_report.Fee.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='operation_report.Fee.price', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='operation_report.Fee.quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='operation_report.Fee.amount', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2626,
  serialized_end=2706,
)


_GETSTORESALESREPORTREQUEST = _descriptor.Descriptor(
  name='GetStoreSalesReportRequest',
  full_name='operation_report.GetStoreSalesReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_filter', full_name='operation_report.GetStoreSalesReportRequest.period_filter', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_filter', full_name='operation_report.GetStoreSalesReportRequest.region_filter', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_group_by', full_name='operation_report.GetStoreSalesReportRequest.period_group_by', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_by', full_name='operation_report.GetStoreSalesReportRequest.region_group_by', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetStoreSalesReportRequest.limit', index=4,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetStoreSalesReportRequest.offset', index=5,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetStoreSalesReportRequest.lan', index=6,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2709,
  serialized_end=3015,
)


_REGIONGROUPMETHOD = _descriptor.Descriptor(
  name='RegionGroupMethod',
  full_name='operation_report.RegionGroupMethod',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='region_type', full_name='operation_report.RegionGroupMethod.region_type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_level', full_name='operation_report.RegionGroupMethod.region_level', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3017,
  serialized_end=3109,
)


_REGIONINFO = _descriptor.Descriptor(
  name='RegionInfo',
  full_name='operation_report.RegionInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='region_level', full_name='operation_report.RegionInfo.region_level', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_id', full_name='operation_report.RegionInfo.region_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_name', full_name='operation_report.RegionInfo.region_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3111,
  serialized_end=3185,
)


_GETSTORESALESREPORTREPONSE = _descriptor.Descriptor(
  name='GetStoreSalesReportReponse',
  full_name='operation_report.GetStoreSalesReportReponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='operation_report.GetStoreSalesReportReponse.total', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='summary', full_name='operation_report.GetStoreSalesReportReponse.summary', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='operation_report.GetStoreSalesReportReponse.rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3188,
  serialized_end=3333,
)


_TOTALSTORESALES = _descriptor.Descriptor(
  name='TotalStoreSales',
  full_name='operation_report.TotalStoreSales',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total_record_count', full_name='operation_report.TotalStoreSales.total_record_count', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='gross_sales_amount', full_name='operation_report.TotalStoreSales.gross_sales_amount', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_sales_amount', full_name='operation_report.TotalStoreSales.net_sales_amount', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='discount_amount', full_name='operation_report.TotalStoreSales.discount_amount', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fee_amount', full_name='operation_report.TotalStoreSales.fee_amount', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_count', full_name='operation_report.TotalStoreSales.sales_count', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='average_net_sales_amount', full_name='operation_report.TotalStoreSales.average_net_sales_amount', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_quantity', full_name='operation_report.TotalStoreSales.return_quantity', index=7,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_amount', full_name='operation_report.TotalStoreSales.return_amount', index=8,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3336,
  serialized_end=3583,
)


_STORESALESRECORD = _descriptor.Descriptor(
  name='StoreSalesRecord',
  full_name='operation_report.StoreSalesRecord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='operation_report.StoreSalesRecord.period_symbol', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_level', full_name='operation_report.StoreSalesRecord.region_group_level', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_type', full_name='operation_report.StoreSalesRecord.region_type', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_meta_info', full_name='operation_report.StoreSalesRecord.region_meta_info', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='gross_sales_amount', full_name='operation_report.StoreSalesRecord.gross_sales_amount', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_sales_amount', full_name='operation_report.StoreSalesRecord.net_sales_amount', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='discount_amount', full_name='operation_report.StoreSalesRecord.discount_amount', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fee_amount', full_name='operation_report.StoreSalesRecord.fee_amount', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_count', full_name='operation_report.StoreSalesRecord.sales_count', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='average_net_sales_amount', full_name='operation_report.StoreSalesRecord.average_net_sales_amount', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_quantity', full_name='operation_report.StoreSalesRecord.return_quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_amount', full_name='operation_report.StoreSalesRecord.return_amount', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3586,
  serialized_end=3968,
)


_GETPRODUCTSALESREPORTREQUEST = _descriptor.Descriptor(
  name='GetProductSalesReportRequest',
  full_name='operation_report.GetProductSalesReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_filter', full_name='operation_report.GetProductSalesReportRequest.period_filter', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_filter', full_name='operation_report.GetProductSalesReportRequest.region_filter', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_filters', full_name='operation_report.GetProductSalesReportRequest.product_filters', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_channel_filters', full_name='operation_report.GetProductSalesReportRequest.sales_channel_filters', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_group_by', full_name='operation_report.GetProductSalesReportRequest.period_group_by', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_by', full_name='operation_report.GetProductSalesReportRequest.region_group_by', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_group_by', full_name='operation_report.GetProductSalesReportRequest.product_group_by', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_channel_group_by', full_name='operation_report.GetProductSalesReportRequest.sales_channel_group_by', index=7,
      number=8, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetProductSalesReportRequest.limit', index=8,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetProductSalesReportRequest.offset', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetProductSalesReportRequest.lan', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3971,
  serialized_end=4545,
)


_PERIODFILTER = _descriptor.Descriptor(
  name='PeriodFilter',
  full_name='operation_report.PeriodFilter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='operation_report.PeriodFilter.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='operation_report.PeriodFilter.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4547,
  serialized_end=4655,
)


_REGIONFILTER = _descriptor.Descriptor(
  name='RegionFilter',
  full_name='operation_report.RegionFilter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='operation_report.RegionFilter.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='level', full_name='operation_report.RegionFilter.level', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='operation_report.RegionFilter.ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4657,
  serialized_end=4743,
)


_PRODUCTFILTER = _descriptor.Descriptor(
  name='ProductFilter',
  full_name='operation_report.ProductFilter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='operation_report.ProductFilter.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='operation_report.ProductFilter.ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='level', full_name='operation_report.ProductFilter.level', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4745,
  serialized_end=4839,
)


_PRODUCTGROUPMETHOD = _descriptor.Descriptor(
  name='ProductGroupMethod',
  full_name='operation_report.ProductGroupMethod',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='group_by', full_name='operation_report.ProductGroupMethod.group_by', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_level', full_name='operation_report.ProductGroupMethod.category_level', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4841,
  serialized_end=4937,
)


_SALESCHANNELFILTER = _descriptor.Descriptor(
  name='SalesChannelFilter',
  full_name='operation_report.SalesChannelFilter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sales_channel_id', full_name='operation_report.SalesChannelFilter.sales_channel_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4939,
  serialized_end=4985,
)


_GETPRODUCTSALESREPORTRESPONSE = _descriptor.Descriptor(
  name='GetProductSalesReportResponse',
  full_name='operation_report.GetProductSalesReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='summary', full_name='operation_report.GetProductSalesReportResponse.summary', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='operation_report.GetProductSalesReportResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='operation_report.GetProductSalesReportResponse.total', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4988,
  serialized_end=5140,
)


_PRODUCTSALESTOTAL = _descriptor.Descriptor(
  name='ProductSalesTotal',
  full_name='operation_report.ProductSalesTotal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='record_count', full_name='operation_report.ProductSalesTotal.record_count', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_sales_quantity', full_name='operation_report.ProductSalesTotal.total_sales_quantity', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_sales_amount', full_name='operation_report.ProductSalesTotal.net_sales_amount', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='discount_amount', full_name='operation_report.ProductSalesTotal.discount_amount', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='average_price_net', full_name='operation_report.ProductSalesTotal.average_price_net', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5143,
  serialized_end=5292,
)


_PRODUCTSALESRECORD = _descriptor.Descriptor(
  name='ProductSalesRecord',
  full_name='operation_report.ProductSalesRecord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='operation_report.ProductSalesRecord.period_symbol', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_level', full_name='operation_report.ProductSalesRecord.region_group_level', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_type', full_name='operation_report.ProductSalesRecord.region_type', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_meta_info', full_name='operation_report.ProductSalesRecord.region_meta_info', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_group_level', full_name='operation_report.ProductSalesRecord.product_group_level', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_group_type', full_name='operation_report.ProductSalesRecord.product_group_type', index=5,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_meta_infos', full_name='operation_report.ProductSalesRecord.product_meta_infos', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_channel_symbol', full_name='operation_report.ProductSalesRecord.sales_channel_symbol', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='operation_report.ProductSalesRecord.quantity', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity_percentage', full_name='operation_report.ProductSalesRecord.quantity_percentage', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_amount', full_name='operation_report.ProductSalesRecord.net_amount', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_amount_percentage', full_name='operation_report.ProductSalesRecord.net_amount_percentage', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='average_net_price', full_name='operation_report.ProductSalesRecord.average_net_price', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='discount_amount', full_name='operation_report.ProductSalesRecord.discount_amount', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5295,
  serialized_end=5811,
)


_GETDISCOUNTREPORTREQUEST = _descriptor.Descriptor(
  name='GetDiscountReportRequest',
  full_name='operation_report.GetDiscountReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_filter', full_name='operation_report.GetDiscountReportRequest.period_filter', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_filter', full_name='operation_report.GetDiscountReportRequest.region_filter', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='discount_filters', full_name='operation_report.GetDiscountReportRequest.discount_filters', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_group_by', full_name='operation_report.GetDiscountReportRequest.period_group_by', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_by', full_name='operation_report.GetDiscountReportRequest.region_group_by', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='discount_group_by', full_name='operation_report.GetDiscountReportRequest.discount_group_by', index=5,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_group_level', full_name='operation_report.GetDiscountReportRequest.category_group_level', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetDiscountReportRequest.limit', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetDiscountReportRequest.offset', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetDiscountReportRequest.lan', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5814,
  serialized_end=6274,
)


_DISCOUNTFILTER = _descriptor.Descriptor(
  name='DiscountFilter',
  full_name='operation_report.DiscountFilter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='operation_report.DiscountFilter.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='operation_report.DiscountFilter.ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='level', full_name='operation_report.DiscountFilter.level', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6276,
  serialized_end=6370,
)


_GETDISCOUNTREPORTRESPONSE = _descriptor.Descriptor(
  name='GetDiscountReportResponse',
  full_name='operation_report.GetDiscountReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='summary', full_name='operation_report.GetDiscountReportResponse.summary', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='operation_report.GetDiscountReportResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='operation_report.GetDiscountReportResponse.total', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6373,
  serialized_end=6513,
)


_DISCOUNTTOTAL = _descriptor.Descriptor(
  name='DiscountTotal',
  full_name='operation_report.DiscountTotal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='record_count', full_name='operation_report.DiscountTotal.record_count', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='operation_report.DiscountTotal.quantity', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='operation_report.DiscountTotal.amount', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='discount_amount_of_canceled_order', full_name='operation_report.DiscountTotal.discount_amount_of_canceled_order', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6515,
  serialized_end=6629,
)


_DISCOUNTRECORD = _descriptor.Descriptor(
  name='DiscountRecord',
  full_name='operation_report.DiscountRecord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='operation_report.DiscountRecord.period_symbol', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_level', full_name='operation_report.DiscountRecord.region_group_level', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_type', full_name='operation_report.DiscountRecord.region_type', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_meta_info', full_name='operation_report.DiscountRecord.region_meta_info', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='discount_symbol', full_name='operation_report.DiscountRecord.discount_symbol', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='discount_symbol_code', full_name='operation_report.DiscountRecord.discount_symbol_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='operation_report.DiscountRecord.quantity', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='operation_report.DiscountRecord.amount', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='discount_amount_of_canceled_order', full_name='operation_report.DiscountRecord.discount_amount_of_canceled_order', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6632,
  serialized_end=6942,
)


_GETPAYMENTREPORTREQUEST = _descriptor.Descriptor(
  name='GetPaymentReportRequest',
  full_name='operation_report.GetPaymentReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_filter', full_name='operation_report.GetPaymentReportRequest.period_filter', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_filter', full_name='operation_report.GetPaymentReportRequest.region_filter', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_filters', full_name='operation_report.GetPaymentReportRequest.payment_filters', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_group_by', full_name='operation_report.GetPaymentReportRequest.period_group_by', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_by', full_name='operation_report.GetPaymentReportRequest.region_group_by', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_group_by', full_name='operation_report.GetPaymentReportRequest.payment_group_by', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetPaymentReportRequest.limit', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetPaymentReportRequest.offset', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetPaymentReportRequest.lan', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6945,
  serialized_end=7370,
)


_PAYMENTFILTER = _descriptor.Descriptor(
  name='PaymentFilter',
  full_name='operation_report.PaymentFilter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='operation_report.PaymentFilter.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='operation_report.PaymentFilter.ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='level', full_name='operation_report.PaymentFilter.level', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7372,
  serialized_end=7464,
)


_PAYMENTGROUPMETHOD = _descriptor.Descriptor(
  name='PaymentGroupMethod',
  full_name='operation_report.PaymentGroupMethod',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='by', full_name='operation_report.PaymentGroupMethod.by', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='level', full_name='operation_report.PaymentGroupMethod.level', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7466,
  serialized_end=7547,
)


_GETPAYMENTREPORTRESPONSE = _descriptor.Descriptor(
  name='GetPaymentReportResponse',
  full_name='operation_report.GetPaymentReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='summary', full_name='operation_report.GetPaymentReportResponse.summary', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='operation_report.GetPaymentReportResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='operation_report.GetPaymentReportResponse.total', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7550,
  serialized_end=7687,
)


_PAYMENTTOTAL = _descriptor.Descriptor(
  name='PaymentTotal',
  full_name='operation_report.PaymentTotal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='record_count', full_name='operation_report.PaymentTotal.record_count', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='operation_report.PaymentTotal.quantity', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='operation_report.PaymentTotal.amount', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fee', full_name='operation_report.PaymentTotal.fee', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_amount', full_name='operation_report.PaymentTotal.net_amount', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7689,
  serialized_end=7792,
)


_PAYMENTRECORD = _descriptor.Descriptor(
  name='PaymentRecord',
  full_name='operation_report.PaymentRecord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='operation_report.PaymentRecord.period_symbol', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_level', full_name='operation_report.PaymentRecord.region_group_level', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_type', full_name='operation_report.PaymentRecord.region_type', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_meta_info', full_name='operation_report.PaymentRecord.region_meta_info', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_group_level', full_name='operation_report.PaymentRecord.payment_group_level', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_group_by', full_name='operation_report.PaymentRecord.payment_group_by', index=5,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_meta_infos', full_name='operation_report.PaymentRecord.payment_meta_infos', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='operation_report.PaymentRecord.quantity', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='operation_report.PaymentRecord.amount', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount_percentage', full_name='operation_report.PaymentRecord.amount_percentage', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fee', full_name='operation_report.PaymentRecord.fee', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_amount', full_name='operation_report.PaymentRecord.net_amount', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_amount_percentage', full_name='operation_report.PaymentRecord.net_amount_percentage', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7795,
  serialized_end=8249,
)


_PAYMENTMETAINFO = _descriptor.Descriptor(
  name='PaymentMetaInfo',
  full_name='operation_report.PaymentMetaInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='level', full_name='operation_report.PaymentMetaInfo.level', index=0,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='operation_report.PaymentMetaInfo.name', index=1,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='operation_report.PaymentMetaInfo.code', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8251,
  serialized_end=8311,
)


_GETPRODUCTPAYMENTREPORTREQUEST = _descriptor.Descriptor(
  name='GetProductPaymentReportRequest',
  full_name='operation_report.GetProductPaymentReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_filter', full_name='operation_report.GetProductPaymentReportRequest.period_filter', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_filter', full_name='operation_report.GetProductPaymentReportRequest.region_filter', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_filters', full_name='operation_report.GetProductPaymentReportRequest.payment_filters', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_filters', full_name='operation_report.GetProductPaymentReportRequest.product_filters', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_group_by', full_name='operation_report.GetProductPaymentReportRequest.period_group_by', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_by', full_name='operation_report.GetProductPaymentReportRequest.region_group_by', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_group_by', full_name='operation_report.GetProductPaymentReportRequest.payment_group_by', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_group_by', full_name='operation_report.GetProductPaymentReportRequest.product_group_by', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetProductPaymentReportRequest.limit', index=8,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetProductPaymentReportRequest.offset', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetProductPaymentReportRequest.lan', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8314,
  serialized_end=8868,
)


_GETPRODUCTPAYMENTREPORTRESPONSE = _descriptor.Descriptor(
  name='GetProductPaymentReportResponse',
  full_name='operation_report.GetProductPaymentReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='operation_report.GetProductPaymentReportResponse.total', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='operation_report.GetProductPaymentReportResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='summary', full_name='operation_report.GetProductPaymentReportResponse.summary', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8871,
  serialized_end=9025,
)


_PRODUCTPAYMENTRECORD = _descriptor.Descriptor(
  name='ProductPaymentRecord',
  full_name='operation_report.ProductPaymentRecord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='operation_report.ProductPaymentRecord.period_symbol', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_level', full_name='operation_report.ProductPaymentRecord.region_group_level', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_by', full_name='operation_report.ProductPaymentRecord.region_group_by', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_meta_info', full_name='operation_report.ProductPaymentRecord.region_meta_info', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_group_level', full_name='operation_report.ProductPaymentRecord.payment_group_level', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_group_by', full_name='operation_report.ProductPaymentRecord.payment_group_by', index=5,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_meta_infos', full_name='operation_report.ProductPaymentRecord.payment_meta_infos', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_group_level', full_name='operation_report.ProductPaymentRecord.product_group_level', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_group_type', full_name='operation_report.ProductPaymentRecord.product_group_type', index=8,
      number=9, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_meta_infos', full_name='operation_report.ProductPaymentRecord.product_meta_infos', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_amount', full_name='operation_report.ProductPaymentRecord.net_amount', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_quantity', full_name='operation_report.ProductPaymentRecord.sales_quantity', index=11,
      number=12, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transaction_count', full_name='operation_report.ProductPaymentRecord.transaction_count', index=12,
      number=13, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='average_amount_per_transaction', full_name='operation_report.ProductPaymentRecord.average_amount_per_transaction', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_amount_percentage', full_name='operation_report.ProductPaymentRecord.net_amount_percentage', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity_percentage', full_name='operation_report.ProductPaymentRecord.quantity_percentage', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9028,
  serialized_end=9700,
)


_REGIONMETAINFO = _descriptor.Descriptor(
  name='RegionMetaInfo',
  full_name='operation_report.RegionMetaInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='level', full_name='operation_report.RegionMetaInfo.level', index=0,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='operation_report.RegionMetaInfo.name', index=1,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='operation_report.RegionMetaInfo.code', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9702,
  serialized_end=9761,
)


_PRODUCTMETAINFO = _descriptor.Descriptor(
  name='ProductMetaInfo',
  full_name='operation_report.ProductMetaInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='level', full_name='operation_report.ProductMetaInfo.level', index=0,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='operation_report.ProductMetaInfo.name', index=1,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='operation_report.ProductMetaInfo.code', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9763,
  serialized_end=9823,
)


_GETSTORECHANNELREPORTREQUEST = _descriptor.Descriptor(
  name='GetStoreChannelReportRequest',
  full_name='operation_report.GetStoreChannelReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_filter', full_name='operation_report.GetStoreChannelReportRequest.period_filter', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_filter', full_name='operation_report.GetStoreChannelReportRequest.region_filter', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_channel_filters', full_name='operation_report.GetStoreChannelReportRequest.sales_channel_filters', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_type_filters', full_name='operation_report.GetStoreChannelReportRequest.delivery_type_filters', index=3,
      number=4, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_group_by', full_name='operation_report.GetStoreChannelReportRequest.period_group_by', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_by', full_name='operation_report.GetStoreChannelReportRequest.region_group_by', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partition_by_sales_channel', full_name='operation_report.GetStoreChannelReportRequest.partition_by_sales_channel', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partition_by_delivery_type', full_name='operation_report.GetStoreChannelReportRequest.partition_by_delivery_type', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetStoreChannelReportRequest.limit', index=8,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetStoreChannelReportRequest.offset', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetStoreChannelReportRequest.lan', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9826,
  serialized_end=10338,
)


_GETSTORECHANNELREPORTRESPONSE = _descriptor.Descriptor(
  name='GetStoreChannelReportResponse',
  full_name='operation_report.GetStoreChannelReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='summary', full_name='operation_report.GetStoreChannelReportResponse.summary', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='operation_report.GetStoreChannelReportResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='operation_report.GetStoreChannelReportResponse.total', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10341,
  serialized_end=10502,
)


_STORECHANNELSALESTOTAL = _descriptor.Descriptor(
  name='StoreChannelSalesTotal',
  full_name='operation_report.StoreChannelSalesTotal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='record_count', full_name='operation_report.StoreChannelSalesTotal.record_count', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transaction_count', full_name='operation_report.StoreChannelSalesTotal.transaction_count', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_amount', full_name='operation_report.StoreChannelSalesTotal.net_amount', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='average_amount_per_transaction', full_name='operation_report.StoreChannelSalesTotal.average_amount_per_transaction', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10505,
  serialized_end=10638,
)


_STORECHANNESALESRECORD = _descriptor.Descriptor(
  name='StoreChanneSalesRecord',
  full_name='operation_report.StoreChanneSalesRecord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='operation_report.StoreChanneSalesRecord.period_symbol', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_level', full_name='operation_report.StoreChanneSalesRecord.region_group_level', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_by', full_name='operation_report.StoreChanneSalesRecord.region_group_by', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_meta_info', full_name='operation_report.StoreChanneSalesRecord.region_meta_info', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_channel_symbol', full_name='operation_report.StoreChanneSalesRecord.sales_channel_symbol', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_type_symbol', full_name='operation_report.StoreChanneSalesRecord.delivery_type_symbol', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transaction_count', full_name='operation_report.StoreChanneSalesRecord.transaction_count', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transaction_count_percentage', full_name='operation_report.StoreChanneSalesRecord.transaction_count_percentage', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_amount', full_name='operation_report.StoreChanneSalesRecord.net_amount', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_amount_percentage', full_name='operation_report.StoreChanneSalesRecord.net_amount_percentage', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='average_amount_per_transaction', full_name='operation_report.StoreChanneSalesRecord.average_amount_per_transaction', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10641,
  serialized_end=11054,
)


_GETPRODUCTCHANNELREPORTREQUEST = _descriptor.Descriptor(
  name='GetProductChannelReportRequest',
  full_name='operation_report.GetProductChannelReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_filter', full_name='operation_report.GetProductChannelReportRequest.period_filter', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_filter', full_name='operation_report.GetProductChannelReportRequest.region_filter', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_filters', full_name='operation_report.GetProductChannelReportRequest.product_filters', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_channel_filters', full_name='operation_report.GetProductChannelReportRequest.sales_channel_filters', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_type_filters', full_name='operation_report.GetProductChannelReportRequest.delivery_type_filters', index=4,
      number=5, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_group_by', full_name='operation_report.GetProductChannelReportRequest.period_group_by', index=5,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_by', full_name='operation_report.GetProductChannelReportRequest.region_group_by', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_group_by', full_name='operation_report.GetProductChannelReportRequest.product_group_by', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partition_by_sales_channel', full_name='operation_report.GetProductChannelReportRequest.partition_by_sales_channel', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partition_by_delivery_type', full_name='operation_report.GetProductChannelReportRequest.partition_by_delivery_type', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetProductChannelReportRequest.limit', index=10,
      number=11, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetProductChannelReportRequest.offset', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetProductChannelReportRequest.lan', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11057,
  serialized_end=11693,
)


_GETPRODUCTCHANNELREPORTRESPONSE = _descriptor.Descriptor(
  name='GetProductChannelReportResponse',
  full_name='operation_report.GetProductChannelReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='summary', full_name='operation_report.GetProductChannelReportResponse.summary', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='operation_report.GetProductChannelReportResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='operation_report.GetProductChannelReportResponse.total', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11696,
  serialized_end=11863,
)


_PRODUCTCHANNELSALESTOTAL = _descriptor.Descriptor(
  name='ProductChannelSalesTotal',
  full_name='operation_report.ProductChannelSalesTotal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='record_count', full_name='operation_report.ProductChannelSalesTotal.record_count', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='operation_report.ProductChannelSalesTotal.quantity', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_amount', full_name='operation_report.ProductChannelSalesTotal.net_amount', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='average_net_price', full_name='operation_report.ProductChannelSalesTotal.average_net_price', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11865,
  serialized_end=11978,
)


_PRODUCTCHANNESALESRECORD = _descriptor.Descriptor(
  name='ProductChanneSalesRecord',
  full_name='operation_report.ProductChanneSalesRecord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='operation_report.ProductChanneSalesRecord.period_symbol', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_level', full_name='operation_report.ProductChanneSalesRecord.region_group_level', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_by', full_name='operation_report.ProductChanneSalesRecord.region_group_by', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_meta_info', full_name='operation_report.ProductChanneSalesRecord.region_meta_info', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_group_level', full_name='operation_report.ProductChanneSalesRecord.product_group_level', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_group_type', full_name='operation_report.ProductChanneSalesRecord.product_group_type', index=5,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_meta_infos', full_name='operation_report.ProductChanneSalesRecord.product_meta_infos', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_channel_symbol', full_name='operation_report.ProductChanneSalesRecord.sales_channel_symbol', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_type_symbol', full_name='operation_report.ProductChanneSalesRecord.delivery_type_symbol', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='operation_report.ProductChanneSalesRecord.quantity', index=9,
      number=10, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity_percentage', full_name='operation_report.ProductChanneSalesRecord.quantity_percentage', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_amount', full_name='operation_report.ProductChanneSalesRecord.net_amount', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_amount_percentage', full_name='operation_report.ProductChanneSalesRecord.net_amount_percentage', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='average_net_price', full_name='operation_report.ProductChanneSalesRecord.average_net_price', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11981,
  serialized_end=12519,
)


_GETSTOREPERIODREPORTREQUEST = _descriptor.Descriptor(
  name='GetStorePeriodReportRequest',
  full_name='operation_report.GetStorePeriodReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_filter', full_name='operation_report.GetStorePeriodReportRequest.period_filter', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_filter', full_name='operation_report.GetStorePeriodReportRequest.region_filter', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_group_by', full_name='operation_report.GetStorePeriodReportRequest.period_group_by', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_by', full_name='operation_report.GetStorePeriodReportRequest.region_group_by', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetStorePeriodReportRequest.limit', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetStorePeriodReportRequest.offset', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='operation_report.GetStorePeriodReportRequest.end_time', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetStorePeriodReportRequest.lan', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12522,
  serialized_end=12875,
)


_GETSTOREPERIODREPORTRESPONSE = _descriptor.Descriptor(
  name='GetStorePeriodReportResponse',
  full_name='operation_report.GetStorePeriodReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='summary', full_name='operation_report.GetStorePeriodReportResponse.summary', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='operation_report.GetStorePeriodReportResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='operation_report.GetStorePeriodReportResponse.total', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12878,
  serialized_end=13022,
)


_PERIODTOTAL = _descriptor.Descriptor(
  name='PeriodTotal',
  full_name='operation_report.PeriodTotal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total_count', full_name='operation_report.PeriodTotal.total_count', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transaction_count', full_name='operation_report.PeriodTotal.transaction_count', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_income', full_name='operation_report.PeriodTotal.net_income', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_transaction_count', full_name='operation_report.PeriodTotal.period_transaction_count', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_net_income', full_name='operation_report.PeriodTotal.period_net_income', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13025,
  serialized_end=13167,
)


_STOREPERIODRECORD = _descriptor.Descriptor(
  name='StorePeriodRecord',
  full_name='operation_report.StorePeriodRecord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='region_group_level', full_name='operation_report.StorePeriodRecord.region_group_level', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_by', full_name='operation_report.StorePeriodRecord.region_group_by', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_meta_info', full_name='operation_report.StorePeriodRecord.region_meta_info', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_income', full_name='operation_report.StorePeriodRecord.net_income', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transaction_count', full_name='operation_report.StorePeriodRecord.transaction_count', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_net_income', full_name='operation_report.StorePeriodRecord.period_net_income', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_transaction_count', full_name='operation_report.StorePeriodRecord.period_transaction_count', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13170,
  serialized_end=13447,
)


_GETSTORECHANNELPERIODREPORTREQUEST = _descriptor.Descriptor(
  name='GetStoreChannelPeriodReportRequest',
  full_name='operation_report.GetStoreChannelPeriodReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_filter', full_name='operation_report.GetStoreChannelPeriodReportRequest.period_filter', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_filter', full_name='operation_report.GetStoreChannelPeriodReportRequest.region_filter', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_channel_filters', full_name='operation_report.GetStoreChannelPeriodReportRequest.sales_channel_filters', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_type_filters', full_name='operation_report.GetStoreChannelPeriodReportRequest.delivery_type_filters', index=3,
      number=4, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_group_by', full_name='operation_report.GetStoreChannelPeriodReportRequest.period_group_by', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_by', full_name='operation_report.GetStoreChannelPeriodReportRequest.region_group_by', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partition_by_sales_channel', full_name='operation_report.GetStoreChannelPeriodReportRequest.partition_by_sales_channel', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partition_by_delivery_type', full_name='operation_report.GetStoreChannelPeriodReportRequest.partition_by_delivery_type', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetStoreChannelPeriodReportRequest.limit', index=8,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetStoreChannelPeriodReportRequest.offset', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetStoreChannelPeriodReportRequest.lan', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13450,
  serialized_end=13968,
)


_GETSTORECHANNELPERIODREPORTRESPONSE = _descriptor.Descriptor(
  name='GetStoreChannelPeriodReportResponse',
  full_name='operation_report.GetStoreChannelPeriodReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='summary', full_name='operation_report.GetStoreChannelPeriodReportResponse.summary', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='operation_report.GetStoreChannelPeriodReportResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='operation_report.GetStoreChannelPeriodReportResponse.total', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13971,
  serialized_end=14129,
)


_STORECHANNELPERIODRECORD = _descriptor.Descriptor(
  name='StoreChannelPeriodRecord',
  full_name='operation_report.StoreChannelPeriodRecord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='region_group_level', full_name='operation_report.StoreChannelPeriodRecord.region_group_level', index=0,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_by', full_name='operation_report.StoreChannelPeriodRecord.region_group_by', index=1,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_meta_info', full_name='operation_report.StoreChannelPeriodRecord.region_meta_info', index=2,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_channel_symbol', full_name='operation_report.StoreChannelPeriodRecord.sales_channel_symbol', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_type_symbol', full_name='operation_report.StoreChannelPeriodRecord.delivery_type_symbol', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_income', full_name='operation_report.StoreChannelPeriodRecord.net_income', index=5,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transaction_count', full_name='operation_report.StoreChannelPeriodRecord.transaction_count', index=6,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_net_income', full_name='operation_report.StoreChannelPeriodRecord.period_net_income', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_transaction_count', full_name='operation_report.StoreChannelPeriodRecord.period_transaction_count', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14132,
  serialized_end=14476,
)


_GETPAYMENTPERIODREPORTREQUEST = _descriptor.Descriptor(
  name='GetPaymentPeriodReportRequest',
  full_name='operation_report.GetPaymentPeriodReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_filter', full_name='operation_report.GetPaymentPeriodReportRequest.period_filter', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_filter', full_name='operation_report.GetPaymentPeriodReportRequest.region_filter', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_filters', full_name='operation_report.GetPaymentPeriodReportRequest.payment_filters', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_group_by', full_name='operation_report.GetPaymentPeriodReportRequest.period_group_by', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_by', full_name='operation_report.GetPaymentPeriodReportRequest.region_group_by', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_group_by', full_name='operation_report.GetPaymentPeriodReportRequest.payment_group_by', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetPaymentPeriodReportRequest.limit', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetPaymentPeriodReportRequest.offset', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetPaymentPeriodReportRequest.lan', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14479,
  serialized_end=14910,
)


_GETPAYMENTPERIODREPORTRESPONSE = _descriptor.Descriptor(
  name='GetPaymentPeriodReportResponse',
  full_name='operation_report.GetPaymentPeriodReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='summary', full_name='operation_report.GetPaymentPeriodReportResponse.summary', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='operation_report.GetPaymentPeriodReportResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='operation_report.GetPaymentPeriodReportResponse.total', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=14913,
  serialized_end=15061,
)


_PAYMENTPERIODRECORD = _descriptor.Descriptor(
  name='PaymentPeriodRecord',
  full_name='operation_report.PaymentPeriodRecord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='region_group_level', full_name='operation_report.PaymentPeriodRecord.region_group_level', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_by', full_name='operation_report.PaymentPeriodRecord.region_group_by', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_meta_info', full_name='operation_report.PaymentPeriodRecord.region_meta_info', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_group_level', full_name='operation_report.PaymentPeriodRecord.payment_group_level', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_group_by', full_name='operation_report.PaymentPeriodRecord.payment_group_by', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_meta_infos', full_name='operation_report.PaymentPeriodRecord.payment_meta_infos', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_income', full_name='operation_report.PaymentPeriodRecord.net_income', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transaction_count', full_name='operation_report.PaymentPeriodRecord.transaction_count', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_net_income', full_name='operation_report.PaymentPeriodRecord.period_net_income', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_transaction_count', full_name='operation_report.PaymentPeriodRecord.period_transaction_count', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15064,
  serialized_end=15495,
)


_GETPRODUCTPERIODREPORTREQUEST = _descriptor.Descriptor(
  name='GetProductPeriodReportRequest',
  full_name='operation_report.GetProductPeriodReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_filter', full_name='operation_report.GetProductPeriodReportRequest.period_filter', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_filter', full_name='operation_report.GetProductPeriodReportRequest.region_filter', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_filters', full_name='operation_report.GetProductPeriodReportRequest.product_filters', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_group_by', full_name='operation_report.GetProductPeriodReportRequest.period_group_by', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_by', full_name='operation_report.GetProductPeriodReportRequest.region_group_by', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_group_by', full_name='operation_report.GetProductPeriodReportRequest.product_group_by', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetProductPeriodReportRequest.limit', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetProductPeriodReportRequest.offset', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetProductPeriodReportRequest.lan', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15498,
  serialized_end=15929,
)


_GETPRODUCTPERIODREPORTRESPONSE = _descriptor.Descriptor(
  name='GetProductPeriodReportResponse',
  full_name='operation_report.GetProductPeriodReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='summary', full_name='operation_report.GetProductPeriodReportResponse.summary', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='operation_report.GetProductPeriodReportResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='operation_report.GetProductPeriodReportResponse.total', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15932,
  serialized_end=16080,
)


_PRODUCTPERIODRECORD = _descriptor.Descriptor(
  name='ProductPeriodRecord',
  full_name='operation_report.ProductPeriodRecord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='region_group_level', full_name='operation_report.ProductPeriodRecord.region_group_level', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_by', full_name='operation_report.ProductPeriodRecord.region_group_by', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_meta_info', full_name='operation_report.ProductPeriodRecord.region_meta_info', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_group_level', full_name='operation_report.ProductPeriodRecord.product_group_level', index=3,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_group_type', full_name='operation_report.ProductPeriodRecord.product_group_type', index=4,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_meta_infos', full_name='operation_report.ProductPeriodRecord.product_meta_infos', index=5,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_income', full_name='operation_report.ProductPeriodRecord.net_income', index=6,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transaction_count', full_name='operation_report.ProductPeriodRecord.transaction_count', index=7,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_net_income', full_name='operation_report.ProductPeriodRecord.period_net_income', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_transaction_count', full_name='operation_report.ProductPeriodRecord.period_transaction_count', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=16083,
  serialized_end=16516,
)


_GETPARSEFALIEDETICKETLISTREQUEST = _descriptor.Descriptor(
  name='GetParseFaliedETicketListRequest',
  full_name='operation_report.GetParseFaliedETicketListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_filter', full_name='operation_report.GetParseFaliedETicketListRequest.period_filter', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_filter', full_name='operation_report.GetParseFaliedETicketListRequest.region_filter', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetParseFaliedETicketListRequest.limit', index=2,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetParseFaliedETicketListRequest.offset', index=3,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetParseFaliedETicketListRequest.lan', index=4,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=16519,
  serialized_end=16707,
)


_GETPARSEFALIEDETICKETLISTRESPONSE = _descriptor.Descriptor(
  name='GetParseFaliedETicketListResponse',
  full_name='operation_report.GetParseFaliedETicketListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total_count', full_name='operation_report.GetParseFaliedETicketListResponse.total_count', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tickets', full_name='operation_report.GetParseFaliedETicketListResponse.tickets', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=16709,
  serialized_end=16820,
)


_PARSEFAILEDETICKET = _descriptor.Descriptor(
  name='ParseFailedETicket',
  full_name='operation_report.ParseFailedETicket',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='operation_report.ParseFailedETicket.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='customer_order_id', full_name='operation_report.ParseFailedETicket.customer_order_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='operation_report.ParseFailedETicket.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_amount', full_name='operation_report.ParseFailedETicket.net_amount', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_of_failure', full_name='operation_report.ParseFailedETicket.reason_of_failure', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=16822,
  serialized_end=16948,
)


_GETSTORESALESREPORTFORPDAREQUEST = _descriptor.Descriptor(
  name='GetStoreSalesReportForPDARequest',
  full_name='operation_report.GetStoreSalesReportForPDARequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='operation_report.GetStoreSalesReportForPDARequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='operation_date', full_name='operation_report.GetStoreSalesReportForPDARequest.operation_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=16950,
  serialized_end=17054,
)


_GETSTORESALESREPORTFORPDARESPONSE = _descriptor.Descriptor(
  name='GetStoreSalesReportForPDAResponse',
  full_name='operation_report.GetStoreSalesReportForPDAResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='operation_report.GetStoreSalesReportForPDAResponse.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='operation_date', full_name='operation_report.GetStoreSalesReportForPDAResponse.operation_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='gross_sales_amount', full_name='operation_report.GetStoreSalesReportForPDAResponse.gross_sales_amount', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_sales_amount', full_name='operation_report.GetStoreSalesReportForPDAResponse.net_sales_amount', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_amount', full_name='operation_report.GetStoreSalesReportForPDAResponse.refund_amount', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='discount_amount', full_name='operation_report.GetStoreSalesReportForPDAResponse.discount_amount', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transaction_count', full_name='operation_report.GetStoreSalesReportForPDAResponse.transaction_count', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='average_amount_per_transaction', full_name='operation_report.GetStoreSalesReportForPDAResponse.average_amount_per_transaction', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='average_quantity_per_transaction', full_name='operation_report.GetStoreSalesReportForPDAResponse.average_quantity_per_transaction', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='channel_sales', full_name='operation_report.GetStoreSalesReportForPDAResponse.channel_sales', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17057,
  serialized_end=17434,
)


_CHANNELSALESAMOUNT = _descriptor.Descriptor(
  name='ChannelSalesAmount',
  full_name='operation_report.ChannelSalesAmount',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='channel_name', full_name='operation_report.ChannelSalesAmount.channel_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='net_amount', full_name='operation_report.ChannelSalesAmount.net_amount', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17436,
  serialized_end=17498,
)


_GETETICKETTRACEIDSREQUEST = _descriptor.Descriptor(
  name='GetETicketTraceIdsRequest',
  full_name='operation_report.GetETicketTraceIdsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='pos_ticket_number', full_name='operation_report.GetETicketTraceIdsRequest.pos_ticket_number', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='operation_report.GetETicketTraceIdsRequest.branch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='operation_report.GetETicketTraceIdsRequest.product_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='operation_report.GetETicketTraceIdsRequest.start_date', index=3,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='operation_report.GetETicketTraceIdsRequest.end_date', index=4,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetETicketTraceIdsRequest.offset', index=5,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetETicketTraceIdsRequest.limit', index=6,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trace_ids', full_name='operation_report.GetETicketTraceIdsRequest.trace_ids', index=7,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetETicketTraceIdsRequest.lan', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17501,
  serialized_end=17752,
)


_POSTICKETNUMBER = _descriptor.Descriptor(
  name='PosTicketNumber',
  full_name='operation_report.PosTicketNumber',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='pos_ticket_number', full_name='operation_report.PosTicketNumber.pos_ticket_number', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trace_id', full_name='operation_report.PosTicketNumber.trace_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17754,
  serialized_end=17816,
)


_GETETICKETTRACEIDSRESPONSE = _descriptor.Descriptor(
  name='GetETicketTraceIdsResponse',
  full_name='operation_report.GetETicketTraceIdsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='trace_ids', full_name='operation_report.GetETicketTraceIdsResponse.trace_ids', index=0,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pos_info', full_name='operation_report.GetETicketTraceIdsResponse.pos_info', index=1,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17818,
  serialized_end=17918,
)


_GETPRODUCTSALESOFFDEMANDREQUEST = _descriptor.Descriptor(
  name='GetProductSalesOffDemandRequest',
  full_name='operation_report.GetProductSalesOffDemandRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='operation_report.GetProductSalesOffDemandRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='operation_report.GetProductSalesOffDemandRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='operation_report.GetProductSalesOffDemandRequest.product_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='operation_report.GetProductSalesOffDemandRequest.store_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetProductSalesOffDemandRequest.lan', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17921,
  serialized_end=18100,
)


_GETPRODUCTSALESOFFDEMANDREQUEST2 = _descriptor.Descriptor(
  name='GetProductSalesOffDemandRequest2',
  full_name='operation_report.GetProductSalesOffDemandRequest2',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='operation_report.GetProductSalesOffDemandRequest2.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='timestamps', full_name='operation_report.GetProductSalesOffDemandRequest2.timestamps', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='operation_report.GetProductSalesOffDemandRequest2.product_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=18102,
  serialized_end=18195,
)


_GETPRODUCTSALESOFFDEMANDRESPONSE2_SALEINFO = _descriptor.Descriptor(
  name='SaleInfo',
  full_name='operation_report.GetProductSalesOffDemandResponse2.SaleInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='operation_report.GetProductSalesOffDemandResponse2.SaleInfo.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='operation_report.GetProductSalesOffDemandResponse2.SaleInfo.timestamp', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='operation_report.GetProductSalesOffDemandResponse2.SaleInfo.quantity', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=18386,
  serialized_end=18453,
)

_GETPRODUCTSALESOFFDEMANDRESPONSE2 = _descriptor.Descriptor(
  name='GetProductSalesOffDemandResponse2',
  full_name='operation_report.GetProductSalesOffDemandResponse2',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request', full_name='operation_report.GetProductSalesOffDemandResponse2.request', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sale_infos', full_name='operation_report.GetProductSalesOffDemandResponse2.sale_infos', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_GETPRODUCTSALESOFFDEMANDRESPONSE2_SALEINFO, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=18198,
  serialized_end=18453,
)


_PRODUCTSSALESINFO = _descriptor.Descriptor(
  name='ProductsSalesInfo',
  full_name='operation_report.ProductsSalesInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='operation_report.ProductsSalesInfo.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='operation_report.ProductsSalesInfo.quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_end_date', full_name='operation_report.ProductsSalesInfo.sales_end_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='average_quantity', full_name='operation_report.ProductsSalesInfo.average_quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=18456,
  serialized_end=18591,
)


_GETPRODUCTSALESOFFDEMANDRESPONSE = _descriptor.Descriptor(
  name='GetProductSalesOffDemandResponse',
  full_name='operation_report.GetProductSalesOffDemandResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='operation_report.GetProductSalesOffDemandResponse.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products_sales_info', full_name='operation_report.GetProductSalesOffDemandResponse.products_sales_info', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=18593,
  serialized_end=18711,
)


_GETSALESEXPLAINEDREPORTREQUEST = _descriptor.Descriptor(
  name='GetSalesExplainedReportRequest',
  full_name='operation_report.GetSalesExplainedReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='operation_report.GetSalesExplainedReportRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='operation_report.GetSalesExplainedReportRequest.product_id', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='operation_report.GetSalesExplainedReportRequest.start_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='operation_report.GetSalesExplainedReportRequest.end_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pos_ticket_number', full_name='operation_report.GetSalesExplainedReportRequest.pos_ticket_number', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='operation_report.GetSalesExplainedReportRequest.bom_product_id', index=5,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetSalesExplainedReportRequest.offset', index=6,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetSalesExplainedReportRequest.limit', index=7,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_unable', full_name='operation_report.GetSalesExplainedReportRequest.is_unable', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetSalesExplainedReportRequest.lan', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=18714,
  serialized_end=18992,
)


_SALESEXPLAINEDREPORTPROPERTIES = _descriptor.Descriptor(
  name='SalesExplainedReportProperties',
  full_name='operation_report.SalesExplainedReportProperties',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='property_key_id', full_name='operation_report.SalesExplainedReportProperties.property_key_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='property_key_code', full_name='operation_report.SalesExplainedReportProperties.property_key_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='property_key_name', full_name='operation_report.SalesExplainedReportProperties.property_key_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=18994,
  serialized_end=19105,
)


_SALESEXPLAINEDREPORT = _descriptor.Descriptor(
  name='SalesExplainedReport',
  full_name='operation_report.SalesExplainedReport',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='operation_report.SalesExplainedReport.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='e_ticket_id', full_name='operation_report.SalesExplainedReport.e_ticket_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='operation_report.SalesExplainedReport.store_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='operation_report.SalesExplainedReport.store_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='operation_report.SalesExplainedReport.store_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='operation_report.SalesExplainedReport.product_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='operation_report.SalesExplainedReport.product_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='operation_report.SalesExplainedReport.product_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='operation_report.SalesExplainedReport.accounting_unit_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='operation_report.SalesExplainedReport.accounting_unit_code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='operation_report.SalesExplainedReport.accounting_unit_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_qty', full_name='operation_report.SalesExplainedReport.product_qty', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pos_ticket_number', full_name='operation_report.SalesExplainedReport.pos_ticket_number', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='operation_report.SalesExplainedReport.bom_product_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_code', full_name='operation_report.SalesExplainedReport.bom_product_code', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_name', full_name='operation_report.SalesExplainedReport.bom_product_name', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_id', full_name='operation_report.SalesExplainedReport.bom_accounting_unit_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_code', full_name='operation_report.SalesExplainedReport.bom_accounting_unit_code', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_name', full_name='operation_report.SalesExplainedReport.bom_accounting_unit_name', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='operation_report.SalesExplainedReport.qty', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_date', full_name='operation_report.SalesExplainedReport.sales_date', index=20,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_unable', full_name='operation_report.SalesExplainedReport.is_unable', index=21,
      number=22, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='properties', full_name='operation_report.SalesExplainedReport.properties', index=22,
      number=23, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='operation_report.SalesExplainedReport.price', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost', full_name='operation_report.SalesExplainedReport.cost', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=19108,
  serialized_end=19782,
)


_GETSALESEXPLAINEDREPORTRESPONSE = _descriptor.Descriptor(
  name='GetSalesExplainedReportResponse',
  full_name='operation_report.GetSalesExplainedReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='operation_report.GetSalesExplainedReportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='operation_report.GetSalesExplainedReportResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=19784,
  serialized_end=19886,
)


_GETSALESEXPLAINEDSUMMARYREPORTREQUEST = _descriptor.Descriptor(
  name='GetSalesExplainedSummaryReportRequest',
  full_name='operation_report.GetSalesExplainedSummaryReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_filter', full_name='operation_report.GetSalesExplainedSummaryReportRequest.period_filter', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_filter', full_name='operation_report.GetSalesExplainedSummaryReportRequest.region_filter', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_filters', full_name='operation_report.GetSalesExplainedSummaryReportRequest.product_filters', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_filters', full_name='operation_report.GetSalesExplainedSummaryReportRequest.bom_product_filters', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_group_by', full_name='operation_report.GetSalesExplainedSummaryReportRequest.period_group_by', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_group_by', full_name='operation_report.GetSalesExplainedSummaryReportRequest.region_group_by', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_group_by', full_name='operation_report.GetSalesExplainedSummaryReportRequest.product_group_by', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_group_by', full_name='operation_report.GetSalesExplainedSummaryReportRequest.bom_product_group_by', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetSalesExplainedSummaryReportRequest.limit', index=8,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetSalesExplainedSummaryReportRequest.offset', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_unable', full_name='operation_report.GetSalesExplainedSummaryReportRequest.is_unable', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetSalesExplainedSummaryReportRequest.lan', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=19889,
  serialized_end=20477,
)


_SALESEXPLAINEDSUMMARYREPORT = _descriptor.Descriptor(
  name='SalesExplainedSummaryReport',
  full_name='operation_report.SalesExplainedSummaryReport',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='operation_report.SalesExplainedSummaryReport.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='e_ticket_id', full_name='operation_report.SalesExplainedSummaryReport.e_ticket_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='entity_code', full_name='operation_report.SalesExplainedSummaryReport.entity_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='entity_name', full_name='operation_report.SalesExplainedSummaryReport.entity_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='entity_id', full_name='operation_report.SalesExplainedSummaryReport.entity_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='operation_report.SalesExplainedSummaryReport.product_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='operation_report.SalesExplainedSummaryReport.product_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='operation_report.SalesExplainedSummaryReport.product_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='operation_report.SalesExplainedSummaryReport.accounting_unit_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='operation_report.SalesExplainedSummaryReport.accounting_unit_code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='operation_report.SalesExplainedSummaryReport.accounting_unit_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_qty', full_name='operation_report.SalesExplainedSummaryReport.product_qty', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pos_ticket_number', full_name='operation_report.SalesExplainedSummaryReport.pos_ticket_number', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='operation_report.SalesExplainedSummaryReport.bom_product_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_code', full_name='operation_report.SalesExplainedSummaryReport.bom_product_code', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_name', full_name='operation_report.SalesExplainedSummaryReport.bom_product_name', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_id', full_name='operation_report.SalesExplainedSummaryReport.bom_accounting_unit_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_code', full_name='operation_report.SalesExplainedSummaryReport.bom_accounting_unit_code', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_name', full_name='operation_report.SalesExplainedSummaryReport.bom_accounting_unit_name', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='operation_report.SalesExplainedSummaryReport.qty', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_date', full_name='operation_report.SalesExplainedSummaryReport.sales_date', index=20,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_unable', full_name='operation_report.SalesExplainedSummaryReport.is_unable', index=21,
      number=22, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='properties', full_name='operation_report.SalesExplainedSummaryReport.properties', index=22,
      number=23, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='operation_report.SalesExplainedSummaryReport.price', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='operation_report.SalesExplainedSummaryReport.period_symbol', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost', full_name='operation_report.SalesExplainedSummaryReport.cost', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_date', full_name='operation_report.SalesExplainedSummaryReport.cost_date', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=20480,
  serialized_end=21206,
)


_GETSALESEXPLAINEDSUMMARYREPORTRESPONSE = _descriptor.Descriptor(
  name='GetSalesExplainedSummaryReportResponse',
  full_name='operation_report.GetSalesExplainedSummaryReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='operation_report.GetSalesExplainedSummaryReportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='operation_report.GetSalesExplainedSummaryReportResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='summary_product_qty', full_name='operation_report.GetSalesExplainedSummaryReportResponse.summary_product_qty', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='summary_qty', full_name='operation_report.GetSalesExplainedSummaryReportResponse.summary_qty', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=21209,
  serialized_end=21375,
)


_GETETICKETDETAILREQUEST = _descriptor.Descriptor(
  name='GetETicketDetailRequest',
  full_name='operation_report.GetETicketDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='e_ticket_id', full_name='operation_report.GetETicketDetailRequest.e_ticket_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetETicketDetailRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=21377,
  serialized_end=21436,
)


_ETICKETDETAILMATERIALINFO = _descriptor.Descriptor(
  name='EticketDetailMaterialInfo',
  full_name='operation_report.EticketDetailMaterialInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='operation_report.EticketDetailMaterialInfo.bom_product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_code', full_name='operation_report.EticketDetailMaterialInfo.bom_product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_name', full_name='operation_report.EticketDetailMaterialInfo.bom_product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_id', full_name='operation_report.EticketDetailMaterialInfo.bom_accounting_unit_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_code', full_name='operation_report.EticketDetailMaterialInfo.bom_accounting_unit_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_name', full_name='operation_report.EticketDetailMaterialInfo.bom_accounting_unit_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='operation_report.EticketDetailMaterialInfo.qty', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_unable', full_name='operation_report.EticketDetailMaterialInfo.is_unable', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='operation_report.EticketDetailMaterialInfo.price', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=21439,
  serialized_end=21689,
)


_ETICKETDETAILMAKEUPINFO = _descriptor.Descriptor(
  name='EticketDetailMakeUpInfo',
  full_name='operation_report.EticketDetailMakeUpInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='operation_report.EticketDetailMakeUpInfo.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='operation_report.EticketDetailMakeUpInfo.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='operation_report.EticketDetailMakeUpInfo.qty', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='operation_report.EticketDetailMakeUpInfo.unit_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='operation_report.EticketDetailMakeUpInfo.type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_replace', full_name='operation_report.EticketDetailMakeUpInfo.is_replace', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=21691,
  serialized_end=21810,
)


_ETICKETDETAILMAKEUPRULE = _descriptor.Descriptor(
  name='EticketDetailMakeUpRule',
  full_name='operation_report.EticketDetailMakeUpRule',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='operation_report.EticketDetailMakeUpRule.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='operation_report.EticketDetailMakeUpRule.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='priority', full_name='operation_report.EticketDetailMakeUpRule.priority', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='operation_report.EticketDetailMakeUpRule.start_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='operation_report.EticketDetailMakeUpRule.end_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=21813,
  serialized_end=21978,
)


_ETICKETDETAILPRODUCTINFO = _descriptor.Descriptor(
  name='EticketDetailProductInfo',
  full_name='operation_report.EticketDetailProductInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='operation_report.EticketDetailProductInfo.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='operation_report.EticketDetailProductInfo.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='operation_report.EticketDetailProductInfo.quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='operation_report.EticketDetailProductInfo.price', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='operation_report.EticketDetailProductInfo.amount', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='properties', full_name='operation_report.EticketDetailProductInfo.properties', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_info', full_name='operation_report.EticketDetailProductInfo.material_info', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='make_up_info', full_name='operation_report.EticketDetailProductInfo.make_up_info', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='make_up_rule', full_name='operation_report.EticketDetailProductInfo.make_up_rule', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='operation_report.EticketDetailProductInfo.id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=21981,
  serialized_end=22364,
)


_GETETICKETDETAILRESPONSE = _descriptor.Descriptor(
  name='GetETicketDetailResponse',
  full_name='operation_report.GetETicketDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='operation_report.GetETicketDetailResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='operation_date', full_name='operation_report.GetETicketDetailResponse.operation_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transaction_time', full_name='operation_report.GetETicketDetailResponse.transaction_time', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ticket_upload_time', full_name='operation_report.GetETicketDetailResponse.ticket_upload_time', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ticket_type', full_name='operation_report.GetETicketDetailResponse.ticket_type', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pos_ticket_number', full_name='operation_report.GetETicketDetailResponse.pos_ticket_number', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_info', full_name='operation_report.GetETicketDetailResponse.store_info', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='member_info', full_name='operation_report.GetETicketDetailResponse.member_info', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_type', full_name='operation_report.GetETicketDetailResponse.delivery_type', index=8,
      number=9, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='discounts', full_name='operation_report.GetETicketDetailResponse.discounts', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fees', full_name='operation_report.GetETicketDetailResponse.fees', index=10,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='operation_report.GetETicketDetailResponse.amount', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payments', full_name='operation_report.GetETicketDetailResponse.payments', index=12,
      number=13, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_channel', full_name='operation_report.GetETicketDetailResponse.sales_channel', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='takeway', full_name='operation_report.GetETicketDetailResponse.takeway', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_cancelled_order', full_name='operation_report.GetETicketDetailResponse.is_cancelled_order', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='operation_report.GetETicketDetailResponse.products', index=16,
      number=17, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22367,
  serialized_end=23134,
)


_GETSALESEXPLAINEDERRORREPORTREQUEST = _descriptor.Descriptor(
  name='GetSalesExplainedErrorReportRequest',
  full_name='operation_report.GetSalesExplainedErrorReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='operation_report.GetSalesExplainedErrorReportRequest.store_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='operation_report.GetSalesExplainedErrorReportRequest.product_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='operation_report.GetSalesExplainedErrorReportRequest.start_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='operation_report.GetSalesExplainedErrorReportRequest.end_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pos_ticket_number', full_name='operation_report.GetSalesExplainedErrorReportRequest.pos_ticket_number', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error_type', full_name='operation_report.GetSalesExplainedErrorReportRequest.error_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetSalesExplainedErrorReportRequest.offset', index=6,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetSalesExplainedErrorReportRequest.limit', index=7,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetSalesExplainedErrorReportRequest.lan', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=23137,
  serialized_end=23399,
)


_BOMPRODUCTEXPLAINED = _descriptor.Descriptor(
  name='BomProductExplained',
  full_name='operation_report.BomProductExplained',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='operation_report.BomProductExplained.bom_product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_code', full_name='operation_report.BomProductExplained.bom_product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_name', full_name='operation_report.BomProductExplained.bom_product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_id', full_name='operation_report.BomProductExplained.bom_accounting_unit_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_code', full_name='operation_report.BomProductExplained.bom_accounting_unit_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_name', full_name='operation_report.BomProductExplained.bom_accounting_unit_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='operation_report.BomProductExplained.qty', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=23402,
  serialized_end=23612,
)


_SALESEXPLAINEDERRORREPORT = _descriptor.Descriptor(
  name='SalesExplainedErrorReport',
  full_name='operation_report.SalesExplainedErrorReport',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_code', full_name='operation_report.SalesExplainedErrorReport.store_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='operation_report.SalesExplainedErrorReport.store_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='operation_report.SalesExplainedErrorReport.store_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='operation_report.SalesExplainedErrorReport.product_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='operation_report.SalesExplainedErrorReport.product_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='operation_report.SalesExplainedErrorReport.product_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pos_ticket_number', full_name='operation_report.SalesExplainedErrorReport.pos_ticket_number', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_products', full_name='operation_report.SalesExplainedErrorReport.bom_products', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_date', full_name='operation_report.SalesExplainedErrorReport.sales_date', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='operation_date', full_name='operation_report.SalesExplainedErrorReport.operation_date', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error_type', full_name='operation_report.SalesExplainedErrorReport.error_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error_detail', full_name='operation_report.SalesExplainedErrorReport.error_detail', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_qty', full_name='operation_report.SalesExplainedErrorReport.product_qty', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=23615,
  serialized_end=24015,
)


_GETSALESEXPLAINEDERRORREPORTRESPONSE = _descriptor.Descriptor(
  name='GetSalesExplainedErrorReportResponse',
  full_name='operation_report.GetSalesExplainedErrorReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='operation_report.GetSalesExplainedErrorReportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='operation_report.GetSalesExplainedErrorReportResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=24017,
  serialized_end=24129,
)


_GETMATERIALSUPPLIESREQUEST = _descriptor.Descriptor(
  name='GetMaterialSuppliesRequest',
  full_name='operation_report.GetMaterialSuppliesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='operation_report.GetMaterialSuppliesRequest.start_date', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='operation_report.GetMaterialSuppliesRequest.end_date', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='operation_report.GetMaterialSuppliesRequest.store_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='operation_report.GetMaterialSuppliesRequest.product_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetMaterialSuppliesRequest.limit', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetMaterialSuppliesRequest.offset', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='operation_report.GetMaterialSuppliesRequest.lan', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ledger_class', full_name='operation_report.GetMaterialSuppliesRequest.ledger_class', index=7,
      number=8, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='operation_report.GetMaterialSuppliesRequest.category_ids', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exclude_empty', full_name='operation_report.GetMaterialSuppliesRequest.exclude_empty', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=24132,
  serialized_end=24349,
)


_GETMATERIALSUPPLIESRESPONSE_MATERIALSUPPLIES = _descriptor.Descriptor(
  name='MaterialSupplies',
  full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bus_date', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.bus_date', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_region_name', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.branch_region_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.store_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.store_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.product_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.product_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_code', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.unit_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.unit_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_category_id', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.product_category_id', index=8,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.category_code', index=9,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.category_name', index=10,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_qty', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.start_qty', index=11,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_qty', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.receive_qty', index=12,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_qty', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.return_qty', index=13,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_qty', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.adjust_qty', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_in_qty', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.transfer_in_qty', index=15,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_out_qty', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.transfer_out_qty', index=16,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_qty', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.end_qty', index=17,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_use_qty', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.calculate_use_qty', index=18,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_cost', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.calculate_cost', index=19,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actual_use_qty', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.actual_use_qty', index=20,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actual_cost', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.actual_cost', index=21,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_qty', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.diff_qty', index=22,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_cost', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.diff_cost', index=23,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_percentage', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.diff_percentage', index=24,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.store_id', index=25,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.product_id', index=26,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.price', index=27,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_end_qty', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.jde_end_qty', index=28,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_trans_deposit', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.material_trans_deposit', index=29,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_trans_withdraw', full_name='operation_report.GetMaterialSuppliesResponse.MaterialSupplies.material_trans_withdraw', index=30,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=24477,
  serialized_end=25183,
)

_GETMATERIALSUPPLIESRESPONSE = _descriptor.Descriptor(
  name='GetMaterialSuppliesResponse',
  full_name='operation_report.GetMaterialSuppliesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='operation_report.GetMaterialSuppliesResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='operation_report.GetMaterialSuppliesResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_GETMATERIALSUPPLIESRESPONSE_MATERIALSUPPLIES, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=24352,
  serialized_end=25183,
)


_GETMATERIALCOSTREPORTREQUEST = _descriptor.Descriptor(
  name='GetMaterialCostReportRequest',
  full_name='operation_report.GetMaterialCostReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='operation_report.GetMaterialCostReportRequest.start_date', index=0,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='operation_report.GetMaterialCostReportRequest.end_date', index=1,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_id', full_name='operation_report.GetMaterialCostReportRequest.period_id', index=2,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_name', full_name='operation_report.GetMaterialCostReportRequest.period_name', index=3,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='operation_report.GetMaterialCostReportRequest.cost_center_id', index=4,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='operation_report.GetMaterialCostReportRequest.product_ids', index=5,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='operation_report.GetMaterialCostReportRequest.order', index=6,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='operation_report.GetMaterialCostReportRequest.sort', index=7,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetMaterialCostReportRequest.offset', index=8,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetMaterialCostReportRequest.limit', index=9,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filter_empty', full_name='operation_report.GetMaterialCostReportRequest.filter_empty', index=10,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_category_ids', full_name='operation_report.GetMaterialCostReportRequest.product_category_ids', index=11,
      number=21, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=25186,
  serialized_end=25507,
)


_COSTDATA = _descriptor.Descriptor(
  name='CostData',
  full_name='operation_report.CostData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_id', full_name='operation_report.CostData.period_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_name', full_name='operation_report.CostData.period_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='operation_report.CostData.cost_center_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='center_name', full_name='operation_report.CostData.center_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='center_code', full_name='operation_report.CostData.center_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='operation_report.CostData.product_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='operation_report.CostData.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='operation_report.CostData.product_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='operation_report.CostData.product_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_status', full_name='operation_report.CostData.product_status', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='operation_report.CostData.category_id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='operation_report.CostData.category_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='parent_category_id', full_name='operation_report.CostData.parent_category_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='parent_category_name', full_name='operation_report.CostData.parent_category_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='operation_report.CostData.unit_id', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='operation_report.CostData.unit_name', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_code', full_name='operation_report.CostData.unit_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_quantity', full_name='operation_report.CostData.start_quantity', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_cost', full_name='operation_report.CostData.start_cost', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_amount', full_name='operation_report.CostData.start_amount', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='current_cost', full_name='operation_report.CostData.current_cost', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_quantity', full_name='operation_report.CostData.receive_quantity', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_amount', full_name='operation_report.CostData.receive_amount', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_quantity', full_name='operation_report.CostData.return_quantity', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_amount', full_name='operation_report.CostData.return_amount', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_amount', full_name='operation_report.CostData.adjust_amount', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_qty', full_name='operation_report.CostData.product_qty', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_amount', full_name='operation_report.CostData.product_amount', index=27,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='operation_report.CostData.start_date', index=28,
      number=29, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='operation_report.CostData.end_date', index=29,
      number=30, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='last_date', full_name='operation_report.CostData.last_date', index=30,
      number=31, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost', full_name='operation_report.CostData.cost', index=31,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error_code', full_name='operation_report.CostData.error_code', index=32,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category1', full_name='operation_report.CostData.category1', index=33,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category2', full_name='operation_report.CostData.category2', index=34,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category3', full_name='operation_report.CostData.category3', index=35,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category4', full_name='operation_report.CostData.category4', index=36,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category5', full_name='operation_report.CostData.category5', index=37,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category6', full_name='operation_report.CostData.category6', index=38,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category7', full_name='operation_report.CostData.category7', index=39,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category8', full_name='operation_report.CostData.category8', index=40,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category9', full_name='operation_report.CostData.category9', index=41,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category10', full_name='operation_report.CostData.category10', index=42,
      number=43, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=25510,
  serialized_end=26513,
)


_GETMATERIALCOSTREPORTRESPONSE = _descriptor.Descriptor(
  name='GetMaterialCostReportResponse',
  full_name='operation_report.GetMaterialCostReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='operation_report.GetMaterialCostReportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='operation_report.GetMaterialCostReportResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='operation_report.GetMaterialCostReportResponse.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='operation_report.GetMaterialCostReportResponse.description', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='operation_report.GetMaterialCostReportResponse.end_time', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=26516,
  serialized_end=26687,
)


_GETBALANCECOSTREPORTREQUEST = _descriptor.Descriptor(
  name='GetBalanceCostReportRequest',
  full_name='operation_report.GetBalanceCostReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_id', full_name='operation_report.GetBalanceCostReportRequest.period_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_name', full_name='operation_report.GetBalanceCostReportRequest.period_name', index=1,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='operation_report.GetBalanceCostReportRequest.cost_center_id', index=2,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='operation_report.GetBalanceCostReportRequest.branch_ids', index=3,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='operation_report.GetBalanceCostReportRequest.product_ids', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='operation_report.GetBalanceCostReportRequest.order', index=5,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='operation_report.GetBalanceCostReportRequest.sort', index=6,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='operation_report.GetBalanceCostReportRequest.offset', index=7,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='operation_report.GetBalanceCostReportRequest.limit', index=8,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_regions', full_name='operation_report.GetBalanceCostReportRequest.branch_regions', index=9,
      number=15, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='operation_report.GetBalanceCostReportRequest.branch_type', index=10,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filter_empty', full_name='operation_report.GetBalanceCostReportRequest.filter_empty', index=11,
      number=19, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_category_ids', full_name='operation_report.GetBalanceCostReportRequest.product_category_ids', index=12,
      number=20, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='operation_report.GetBalanceCostReportRequest.start_date', index=13,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='operation_report.GetBalanceCostReportRequest.end_date', index=14,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=26690,
  serialized_end=27075,
)


_GETBALANCECOSTREPORTRESPONSE = _descriptor.Descriptor(
  name='GetBalanceCostReportResponse',
  full_name='operation_report.GetBalanceCostReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='operation_report.GetBalanceCostReportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='operation_report.GetBalanceCostReportResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=27077,
  serialized_end=27167,
)


_BALANCEDATA = _descriptor.Descriptor(
  name='BalanceData',
  full_name='operation_report.BalanceData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='operation_report.BalanceData.cost_center_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='center_name', full_name='operation_report.BalanceData.center_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='center_code', full_name='operation_report.BalanceData.center_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='periodID', full_name='operation_report.BalanceData.periodID', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_status', full_name='operation_report.BalanceData.period_status', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_name', full_name='operation_report.BalanceData.period_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='operation_report.BalanceData.branch_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_code', full_name='operation_report.BalanceData.branch_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='operation_report.BalanceData.branch_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='operation_report.BalanceData.product_id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='operation_report.BalanceData.product_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='operation_report.BalanceData.product_code', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_status', full_name='operation_report.BalanceData.product_status', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='operation_report.BalanceData.category_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='operation_report.BalanceData.category_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='parent_category_id', full_name='operation_report.BalanceData.parent_category_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='parent_category_name', full_name='operation_report.BalanceData.parent_category_name', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='operation_report.BalanceData.spec', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='operation_report.BalanceData.unit_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='operation_report.BalanceData.unit_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_code', full_name='operation_report.BalanceData.unit_code', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_quantity', full_name='operation_report.BalanceData.start_quantity', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_cost', full_name='operation_report.BalanceData.start_cost', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_amount', full_name='operation_report.BalanceData.start_amount', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_quantity', full_name='operation_report.BalanceData.end_quantity', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='current_cost', full_name='operation_report.BalanceData.current_cost', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_amount', full_name='operation_report.BalanceData.end_amount', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='self_in_qty', full_name='operation_report.BalanceData.self_in_qty', index=27,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='self_in_amount', full_name='operation_report.BalanceData.self_in_amount', index=28,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_quantity', full_name='operation_report.BalanceData.receive_quantity', index=29,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_cost', full_name='operation_report.BalanceData.receive_cost', index=30,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_quantity', full_name='operation_report.BalanceData.return_quantity', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_cost', full_name='operation_report.BalanceData.return_cost', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_in', full_name='operation_report.BalanceData.delivery_in', index=33,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_in_cost', full_name='operation_report.BalanceData.delivery_in_cost', index=34,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_out', full_name='operation_report.BalanceData.delivery_out', index=35,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_out_cost', full_name='operation_report.BalanceData.delivery_out_cost', index=36,
      number=37, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_out_qty', full_name='operation_report.BalanceData.transfer_out_qty', index=37,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_out_cost', full_name='operation_report.BalanceData.transfer_out_cost', index=38,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_in_qty', full_name='operation_report.BalanceData.transfer_in_qty', index=39,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_in_cost', full_name='operation_report.BalanceData.transfer_in_cost', index=40,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='broker_qty', full_name='operation_report.BalanceData.broker_qty', index=41,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='broker_cost', full_name='operation_report.BalanceData.broker_cost', index=42,
      number=43, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scrap_quantity', full_name='operation_report.BalanceData.scrap_quantity', index=43,
      number=44, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scrap_amount', full_name='operation_report.BalanceData.scrap_amount', index=44,
      number=45, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sale_quantity', full_name='operation_report.BalanceData.sale_quantity', index=45,
      number=46, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sale_cost', full_name='operation_report.BalanceData.sale_cost', index=46,
      number=47, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_diff', full_name='operation_report.BalanceData.inventory_diff', index=47,
      number=48, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_diff_cost', full_name='operation_report.BalanceData.inventory_diff_cost', index=48,
      number=49, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='operation_report.BalanceData.start_date', index=49,
      number=50, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='operation_report.BalanceData.end_date', index=50,
      number=51, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='last_date', full_name='operation_report.BalanceData.last_date', index=51,
      number=52, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category1', full_name='operation_report.BalanceData.category1', index=52,
      number=64, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category2', full_name='operation_report.BalanceData.category2', index=53,
      number=65, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category3', full_name='operation_report.BalanceData.category3', index=54,
      number=66, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category4', full_name='operation_report.BalanceData.category4', index=55,
      number=67, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category5', full_name='operation_report.BalanceData.category5', index=56,
      number=68, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category6', full_name='operation_report.BalanceData.category6', index=57,
      number=69, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category7', full_name='operation_report.BalanceData.category7', index=58,
      number=70, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category8', full_name='operation_report.BalanceData.category8', index=59,
      number=71, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category9', full_name='operation_report.BalanceData.category9', index=60,
      number=72, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category10', full_name='operation_report.BalanceData.category10', index=61,
      number=73, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=27170,
  serialized_end=28614,
)

_GETETICKETREPORTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETETICKETREPORTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETETICKETREPORTREQUEST.fields_by_name['ticket_type'].enum_type = _TICKETTYPE
_GETETICKETREPORTREQUEST.fields_by_name['delivery_type'].enum_type = _DELIVERYTYPE
_GETETICKETREPORTRESPONSE.fields_by_name['rows'].message_type = _ETICKETREPORT
_ETICKETREPORT.fields_by_name['operation_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ETICKETREPORT.fields_by_name['transaction_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ETICKETREPORT.fields_by_name['ticket_upload_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ETICKETREPORT.fields_by_name['ticket_type'].enum_type = _TICKETTYPE
_ETICKETREPORT.fields_by_name['store_info'].message_type = _STOREINFO
_ETICKETREPORT.fields_by_name['member_info'].message_type = _MEMBERINFO
_ETICKETREPORT.fields_by_name['delivery_type'].enum_type = _DELIVERYTYPE
_ETICKETREPORT.fields_by_name['products'].message_type = _ITEM
_ETICKETREPORT.fields_by_name['discounts'].message_type = _DISCOUNT
_ETICKETREPORT.fields_by_name['fees'].message_type = _FEE
_ETICKETREPORT.fields_by_name['payments'].message_type = _PAYMENT
_ETICKETREPORT.fields_by_name['sales_channel'].message_type = _SALESCHANNEL
_ETICKETREPORT.fields_by_name['takeway'].message_type = _TAKEWAY
_ITEM.fields_by_name['product_info'].message_type = _PRODUCTINFO
_ITEM.fields_by_name['mixtures'].message_type = _MIXTURE
_ITEM.fields_by_name['item_discounts'].message_type = _DISCOUNT
_MIXTURE.fields_by_name['product_info'].message_type = _PRODUCTINFO
_GETSTORESALESREPORTREQUEST.fields_by_name['period_filter'].message_type = _PERIODFILTER
_GETSTORESALESREPORTREQUEST.fields_by_name['region_filter'].message_type = _REGIONFILTER
_GETSTORESALESREPORTREQUEST.fields_by_name['period_group_by'].enum_type = _PERIODGROUPMETHOD
_GETSTORESALESREPORTREQUEST.fields_by_name['region_group_by'].message_type = _REGIONGROUPMETHOD
_REGIONGROUPMETHOD.fields_by_name['region_type'].enum_type = _REGIONTYPE
_GETSTORESALESREPORTREPONSE.fields_by_name['summary'].message_type = _TOTALSTORESALES
_GETSTORESALESREPORTREPONSE.fields_by_name['rows'].message_type = _STORESALESRECORD
_STORESALESRECORD.fields_by_name['region_type'].enum_type = _REGIONTYPE
_STORESALESRECORD.fields_by_name['region_meta_info'].message_type = _REGIONMETAINFO
_GETPRODUCTSALESREPORTREQUEST.fields_by_name['period_filter'].message_type = _PERIODFILTER
_GETPRODUCTSALESREPORTREQUEST.fields_by_name['region_filter'].message_type = _REGIONFILTER
_GETPRODUCTSALESREPORTREQUEST.fields_by_name['product_filters'].message_type = _PRODUCTFILTER
_GETPRODUCTSALESREPORTREQUEST.fields_by_name['sales_channel_filters'].message_type = _SALESCHANNELFILTER
_GETPRODUCTSALESREPORTREQUEST.fields_by_name['period_group_by'].enum_type = _PERIODGROUPMETHOD
_GETPRODUCTSALESREPORTREQUEST.fields_by_name['region_group_by'].message_type = _REGIONGROUPMETHOD
_GETPRODUCTSALESREPORTREQUEST.fields_by_name['product_group_by'].message_type = _PRODUCTGROUPMETHOD
_GETPRODUCTSALESREPORTREQUEST.fields_by_name['sales_channel_group_by'].enum_type = _SALESCHANNELGROUPMETHOD
_PERIODFILTER.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PERIODFILTER.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_REGIONFILTER.fields_by_name['type'].enum_type = _REGIONTYPE
_PRODUCTFILTER.fields_by_name['type'].enum_type = _PRODUCTFILTERTYPE
_PRODUCTGROUPMETHOD.fields_by_name['group_by'].enum_type = _PRODUCTGROUPBY
_GETPRODUCTSALESREPORTRESPONSE.fields_by_name['summary'].message_type = _PRODUCTSALESTOTAL
_GETPRODUCTSALESREPORTRESPONSE.fields_by_name['rows'].message_type = _PRODUCTSALESRECORD
_PRODUCTSALESRECORD.fields_by_name['region_type'].enum_type = _REGIONTYPE
_PRODUCTSALESRECORD.fields_by_name['region_meta_info'].message_type = _REGIONMETAINFO
_PRODUCTSALESRECORD.fields_by_name['product_group_type'].enum_type = _PRODUCTGROUPBY
_PRODUCTSALESRECORD.fields_by_name['product_meta_infos'].message_type = _PRODUCTMETAINFO
_GETDISCOUNTREPORTREQUEST.fields_by_name['period_filter'].message_type = _PERIODFILTER
_GETDISCOUNTREPORTREQUEST.fields_by_name['region_filter'].message_type = _REGIONFILTER
_GETDISCOUNTREPORTREQUEST.fields_by_name['discount_filters'].message_type = _DISCOUNTFILTER
_GETDISCOUNTREPORTREQUEST.fields_by_name['period_group_by'].enum_type = _PERIODGROUPMETHOD
_GETDISCOUNTREPORTREQUEST.fields_by_name['region_group_by'].message_type = _REGIONGROUPMETHOD
_GETDISCOUNTREPORTREQUEST.fields_by_name['discount_group_by'].enum_type = _DISCOUNTGROUPMETHOD
_DISCOUNTFILTER.fields_by_name['type'].enum_type = _DISCOUNTFILTERBY
_GETDISCOUNTREPORTRESPONSE.fields_by_name['summary'].message_type = _DISCOUNTTOTAL
_GETDISCOUNTREPORTRESPONSE.fields_by_name['rows'].message_type = _DISCOUNTRECORD
_DISCOUNTRECORD.fields_by_name['region_type'].enum_type = _REGIONTYPE
_DISCOUNTRECORD.fields_by_name['region_meta_info'].message_type = _REGIONMETAINFO
_GETPAYMENTREPORTREQUEST.fields_by_name['period_filter'].message_type = _PERIODFILTER
_GETPAYMENTREPORTREQUEST.fields_by_name['region_filter'].message_type = _REGIONFILTER
_GETPAYMENTREPORTREQUEST.fields_by_name['payment_filters'].message_type = _PAYMENTFILTER
_GETPAYMENTREPORTREQUEST.fields_by_name['period_group_by'].enum_type = _PERIODGROUPMETHOD
_GETPAYMENTREPORTREQUEST.fields_by_name['region_group_by'].message_type = _REGIONGROUPMETHOD
_GETPAYMENTREPORTREQUEST.fields_by_name['payment_group_by'].message_type = _PAYMENTGROUPMETHOD
_PAYMENTFILTER.fields_by_name['type'].enum_type = _PAYMENTFILTERBY
_PAYMENTGROUPMETHOD.fields_by_name['by'].enum_type = _PAYMENTGROUPBY
_GETPAYMENTREPORTRESPONSE.fields_by_name['summary'].message_type = _PAYMENTTOTAL
_GETPAYMENTREPORTRESPONSE.fields_by_name['rows'].message_type = _PAYMENTRECORD
_PAYMENTRECORD.fields_by_name['region_type'].enum_type = _REGIONTYPE
_PAYMENTRECORD.fields_by_name['region_meta_info'].message_type = _REGIONMETAINFO
_PAYMENTRECORD.fields_by_name['payment_group_by'].enum_type = _PAYMENTGROUPBY
_PAYMENTRECORD.fields_by_name['payment_meta_infos'].message_type = _PAYMENTMETAINFO
_GETPRODUCTPAYMENTREPORTREQUEST.fields_by_name['period_filter'].message_type = _PERIODFILTER
_GETPRODUCTPAYMENTREPORTREQUEST.fields_by_name['region_filter'].message_type = _REGIONFILTER
_GETPRODUCTPAYMENTREPORTREQUEST.fields_by_name['payment_filters'].message_type = _PAYMENTFILTER
_GETPRODUCTPAYMENTREPORTREQUEST.fields_by_name['product_filters'].message_type = _PRODUCTFILTER
_GETPRODUCTPAYMENTREPORTREQUEST.fields_by_name['period_group_by'].enum_type = _PERIODGROUPMETHOD
_GETPRODUCTPAYMENTREPORTREQUEST.fields_by_name['region_group_by'].message_type = _REGIONGROUPMETHOD
_GETPRODUCTPAYMENTREPORTREQUEST.fields_by_name['payment_group_by'].message_type = _PAYMENTGROUPMETHOD
_GETPRODUCTPAYMENTREPORTREQUEST.fields_by_name['product_group_by'].message_type = _PRODUCTGROUPMETHOD
_GETPRODUCTPAYMENTREPORTRESPONSE.fields_by_name['rows'].message_type = _PRODUCTPAYMENTRECORD
_GETPRODUCTPAYMENTREPORTRESPONSE.fields_by_name['summary'].message_type = _TOTALSTORESALES
_PRODUCTPAYMENTRECORD.fields_by_name['region_group_by'].message_type = _REGIONGROUPMETHOD
_PRODUCTPAYMENTRECORD.fields_by_name['region_meta_info'].message_type = _REGIONMETAINFO
_PRODUCTPAYMENTRECORD.fields_by_name['payment_group_by'].enum_type = _PAYMENTGROUPBY
_PRODUCTPAYMENTRECORD.fields_by_name['payment_meta_infos'].message_type = _PAYMENTMETAINFO
_PRODUCTPAYMENTRECORD.fields_by_name['product_group_type'].enum_type = _PRODUCTGROUPBY
_PRODUCTPAYMENTRECORD.fields_by_name['product_meta_infos'].message_type = _PRODUCTMETAINFO
_GETSTORECHANNELREPORTREQUEST.fields_by_name['period_filter'].message_type = _PERIODFILTER
_GETSTORECHANNELREPORTREQUEST.fields_by_name['region_filter'].message_type = _REGIONFILTER
_GETSTORECHANNELREPORTREQUEST.fields_by_name['sales_channel_filters'].message_type = _SALESCHANNELFILTER
_GETSTORECHANNELREPORTREQUEST.fields_by_name['delivery_type_filters'].enum_type = _DELIVERYTYPE
_GETSTORECHANNELREPORTREQUEST.fields_by_name['period_group_by'].enum_type = _PERIODGROUPMETHOD
_GETSTORECHANNELREPORTREQUEST.fields_by_name['region_group_by'].message_type = _REGIONGROUPMETHOD
_GETSTORECHANNELREPORTRESPONSE.fields_by_name['summary'].message_type = _STORECHANNELSALESTOTAL
_GETSTORECHANNELREPORTRESPONSE.fields_by_name['rows'].message_type = _STORECHANNESALESRECORD
_STORECHANNESALESRECORD.fields_by_name['region_group_by'].message_type = _REGIONGROUPMETHOD
_STORECHANNESALESRECORD.fields_by_name['region_meta_info'].message_type = _REGIONMETAINFO
_GETPRODUCTCHANNELREPORTREQUEST.fields_by_name['period_filter'].message_type = _PERIODFILTER
_GETPRODUCTCHANNELREPORTREQUEST.fields_by_name['region_filter'].message_type = _REGIONFILTER
_GETPRODUCTCHANNELREPORTREQUEST.fields_by_name['product_filters'].message_type = _PRODUCTFILTER
_GETPRODUCTCHANNELREPORTREQUEST.fields_by_name['sales_channel_filters'].message_type = _SALESCHANNELFILTER
_GETPRODUCTCHANNELREPORTREQUEST.fields_by_name['delivery_type_filters'].enum_type = _DELIVERYTYPE
_GETPRODUCTCHANNELREPORTREQUEST.fields_by_name['period_group_by'].enum_type = _PERIODGROUPMETHOD
_GETPRODUCTCHANNELREPORTREQUEST.fields_by_name['region_group_by'].message_type = _REGIONGROUPMETHOD
_GETPRODUCTCHANNELREPORTREQUEST.fields_by_name['product_group_by'].message_type = _PRODUCTGROUPMETHOD
_GETPRODUCTCHANNELREPORTRESPONSE.fields_by_name['summary'].message_type = _PRODUCTCHANNELSALESTOTAL
_GETPRODUCTCHANNELREPORTRESPONSE.fields_by_name['rows'].message_type = _PRODUCTCHANNESALESRECORD
_PRODUCTCHANNESALESRECORD.fields_by_name['region_group_by'].message_type = _REGIONGROUPMETHOD
_PRODUCTCHANNESALESRECORD.fields_by_name['region_meta_info'].message_type = _REGIONMETAINFO
_PRODUCTCHANNESALESRECORD.fields_by_name['product_group_type'].enum_type = _PRODUCTGROUPBY
_PRODUCTCHANNESALESRECORD.fields_by_name['product_meta_infos'].message_type = _PRODUCTMETAINFO
_GETSTOREPERIODREPORTREQUEST.fields_by_name['period_filter'].message_type = _PERIODFILTER
_GETSTOREPERIODREPORTREQUEST.fields_by_name['region_filter'].message_type = _REGIONFILTER
_GETSTOREPERIODREPORTREQUEST.fields_by_name['period_group_by'].enum_type = _PERIODGROUPMETHOD
_GETSTOREPERIODREPORTREQUEST.fields_by_name['region_group_by'].message_type = _REGIONGROUPMETHOD
_GETSTOREPERIODREPORTREQUEST.fields_by_name['end_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOREPERIODREPORTRESPONSE.fields_by_name['summary'].message_type = _PERIODTOTAL
_GETSTOREPERIODREPORTRESPONSE.fields_by_name['rows'].message_type = _STOREPERIODRECORD
_STOREPERIODRECORD.fields_by_name['region_group_by'].message_type = _REGIONGROUPMETHOD
_STOREPERIODRECORD.fields_by_name['region_meta_info'].message_type = _REGIONMETAINFO
_GETSTORECHANNELPERIODREPORTREQUEST.fields_by_name['period_filter'].message_type = _PERIODFILTER
_GETSTORECHANNELPERIODREPORTREQUEST.fields_by_name['region_filter'].message_type = _REGIONFILTER
_GETSTORECHANNELPERIODREPORTREQUEST.fields_by_name['sales_channel_filters'].message_type = _SALESCHANNELFILTER
_GETSTORECHANNELPERIODREPORTREQUEST.fields_by_name['delivery_type_filters'].enum_type = _DELIVERYTYPE
_GETSTORECHANNELPERIODREPORTREQUEST.fields_by_name['period_group_by'].enum_type = _PERIODGROUPMETHOD
_GETSTORECHANNELPERIODREPORTREQUEST.fields_by_name['region_group_by'].message_type = _REGIONGROUPMETHOD
_GETSTORECHANNELPERIODREPORTRESPONSE.fields_by_name['summary'].message_type = _PERIODTOTAL
_GETSTORECHANNELPERIODREPORTRESPONSE.fields_by_name['rows'].message_type = _STORECHANNELPERIODRECORD
_STORECHANNELPERIODRECORD.fields_by_name['region_group_by'].message_type = _REGIONGROUPMETHOD
_STORECHANNELPERIODRECORD.fields_by_name['region_meta_info'].message_type = _REGIONMETAINFO
_GETPAYMENTPERIODREPORTREQUEST.fields_by_name['period_filter'].message_type = _PERIODFILTER
_GETPAYMENTPERIODREPORTREQUEST.fields_by_name['region_filter'].message_type = _REGIONFILTER
_GETPAYMENTPERIODREPORTREQUEST.fields_by_name['payment_filters'].message_type = _PAYMENTFILTER
_GETPAYMENTPERIODREPORTREQUEST.fields_by_name['period_group_by'].enum_type = _PERIODGROUPMETHOD
_GETPAYMENTPERIODREPORTREQUEST.fields_by_name['region_group_by'].message_type = _REGIONGROUPMETHOD
_GETPAYMENTPERIODREPORTREQUEST.fields_by_name['payment_group_by'].message_type = _PAYMENTGROUPMETHOD
_GETPAYMENTPERIODREPORTRESPONSE.fields_by_name['summary'].message_type = _PERIODTOTAL
_GETPAYMENTPERIODREPORTRESPONSE.fields_by_name['rows'].message_type = _PAYMENTPERIODRECORD
_PAYMENTPERIODRECORD.fields_by_name['region_group_by'].message_type = _REGIONGROUPMETHOD
_PAYMENTPERIODRECORD.fields_by_name['region_meta_info'].message_type = _REGIONMETAINFO
_PAYMENTPERIODRECORD.fields_by_name['payment_group_by'].enum_type = _PAYMENTGROUPBY
_PAYMENTPERIODRECORD.fields_by_name['payment_meta_infos'].message_type = _PAYMENTMETAINFO
_GETPRODUCTPERIODREPORTREQUEST.fields_by_name['period_filter'].message_type = _PERIODFILTER
_GETPRODUCTPERIODREPORTREQUEST.fields_by_name['region_filter'].message_type = _REGIONFILTER
_GETPRODUCTPERIODREPORTREQUEST.fields_by_name['product_filters'].message_type = _PRODUCTFILTER
_GETPRODUCTPERIODREPORTREQUEST.fields_by_name['period_group_by'].enum_type = _PERIODGROUPMETHOD
_GETPRODUCTPERIODREPORTREQUEST.fields_by_name['region_group_by'].message_type = _REGIONGROUPMETHOD
_GETPRODUCTPERIODREPORTREQUEST.fields_by_name['product_group_by'].message_type = _PRODUCTGROUPMETHOD
_GETPRODUCTPERIODREPORTRESPONSE.fields_by_name['summary'].message_type = _PERIODTOTAL
_GETPRODUCTPERIODREPORTRESPONSE.fields_by_name['rows'].message_type = _PRODUCTPERIODRECORD
_PRODUCTPERIODRECORD.fields_by_name['region_group_by'].message_type = _REGIONGROUPMETHOD
_PRODUCTPERIODRECORD.fields_by_name['region_meta_info'].message_type = _REGIONMETAINFO
_PRODUCTPERIODRECORD.fields_by_name['product_group_type'].enum_type = _PRODUCTGROUPBY
_PRODUCTPERIODRECORD.fields_by_name['product_meta_infos'].message_type = _PRODUCTMETAINFO
_GETPARSEFALIEDETICKETLISTREQUEST.fields_by_name['period_filter'].message_type = _PERIODFILTER
_GETPARSEFALIEDETICKETLISTREQUEST.fields_by_name['region_filter'].message_type = _REGIONFILTER
_GETPARSEFALIEDETICKETLISTRESPONSE.fields_by_name['tickets'].message_type = _PARSEFAILEDETICKET
_GETSTORESALESREPORTFORPDAREQUEST.fields_by_name['operation_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTORESALESREPORTFORPDARESPONSE.fields_by_name['operation_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTORESALESREPORTFORPDARESPONSE.fields_by_name['channel_sales'].message_type = _CHANNELSALESAMOUNT
_GETETICKETTRACEIDSREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETETICKETTRACEIDSREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETETICKETTRACEIDSRESPONSE.fields_by_name['pos_info'].message_type = _POSTICKETNUMBER
_GETPRODUCTSALESOFFDEMANDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPRODUCTSALESOFFDEMANDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPRODUCTSALESOFFDEMANDRESPONSE2_SALEINFO.containing_type = _GETPRODUCTSALESOFFDEMANDRESPONSE2
_GETPRODUCTSALESOFFDEMANDRESPONSE2.fields_by_name['request'].message_type = _GETPRODUCTSALESOFFDEMANDREQUEST2
_GETPRODUCTSALESOFFDEMANDRESPONSE2.fields_by_name['sale_infos'].message_type = _GETPRODUCTSALESOFFDEMANDRESPONSE2_SALEINFO
_PRODUCTSSALESINFO.fields_by_name['sales_end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPRODUCTSALESOFFDEMANDRESPONSE.fields_by_name['products_sales_info'].message_type = _PRODUCTSSALESINFO
_GETSALESEXPLAINEDREPORTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSALESEXPLAINEDREPORTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SALESEXPLAINEDREPORT.fields_by_name['sales_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SALESEXPLAINEDREPORT.fields_by_name['properties'].message_type = _SALESEXPLAINEDREPORTPROPERTIES
_GETSALESEXPLAINEDREPORTRESPONSE.fields_by_name['rows'].message_type = _SALESEXPLAINEDREPORT
_GETSALESEXPLAINEDSUMMARYREPORTREQUEST.fields_by_name['period_filter'].message_type = _PERIODFILTER
_GETSALESEXPLAINEDSUMMARYREPORTREQUEST.fields_by_name['region_filter'].message_type = _REGIONFILTER
_GETSALESEXPLAINEDSUMMARYREPORTREQUEST.fields_by_name['product_filters'].message_type = _PRODUCTFILTER
_GETSALESEXPLAINEDSUMMARYREPORTREQUEST.fields_by_name['bom_product_filters'].message_type = _PRODUCTFILTER
_GETSALESEXPLAINEDSUMMARYREPORTREQUEST.fields_by_name['period_group_by'].enum_type = _PERIODGROUPMETHOD
_GETSALESEXPLAINEDSUMMARYREPORTREQUEST.fields_by_name['region_group_by'].message_type = _REGIONGROUPMETHOD
_GETSALESEXPLAINEDSUMMARYREPORTREQUEST.fields_by_name['product_group_by'].message_type = _PRODUCTGROUPMETHOD
_GETSALESEXPLAINEDSUMMARYREPORTREQUEST.fields_by_name['bom_product_group_by'].message_type = _PRODUCTGROUPMETHOD
_SALESEXPLAINEDSUMMARYREPORT.fields_by_name['sales_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SALESEXPLAINEDSUMMARYREPORT.fields_by_name['properties'].message_type = _SALESEXPLAINEDREPORTPROPERTIES
_GETSALESEXPLAINEDSUMMARYREPORTRESPONSE.fields_by_name['rows'].message_type = _SALESEXPLAINEDSUMMARYREPORT
_ETICKETDETAILMAKEUPRULE.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ETICKETDETAILMAKEUPRULE.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ETICKETDETAILPRODUCTINFO.fields_by_name['properties'].message_type = _SALESEXPLAINEDREPORTPROPERTIES
_ETICKETDETAILPRODUCTINFO.fields_by_name['material_info'].message_type = _ETICKETDETAILMATERIALINFO
_ETICKETDETAILPRODUCTINFO.fields_by_name['make_up_info'].message_type = _ETICKETDETAILMAKEUPINFO
_ETICKETDETAILPRODUCTINFO.fields_by_name['make_up_rule'].message_type = _ETICKETDETAILMAKEUPRULE
_GETETICKETDETAILRESPONSE.fields_by_name['operation_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETETICKETDETAILRESPONSE.fields_by_name['transaction_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETETICKETDETAILRESPONSE.fields_by_name['ticket_upload_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETETICKETDETAILRESPONSE.fields_by_name['ticket_type'].enum_type = _TICKETTYPE
_GETETICKETDETAILRESPONSE.fields_by_name['store_info'].message_type = _STOREINFO
_GETETICKETDETAILRESPONSE.fields_by_name['member_info'].message_type = _MEMBERINFO
_GETETICKETDETAILRESPONSE.fields_by_name['delivery_type'].enum_type = _DELIVERYTYPE
_GETETICKETDETAILRESPONSE.fields_by_name['discounts'].message_type = _DISCOUNT
_GETETICKETDETAILRESPONSE.fields_by_name['fees'].message_type = _FEE
_GETETICKETDETAILRESPONSE.fields_by_name['payments'].message_type = _PAYMENT
_GETETICKETDETAILRESPONSE.fields_by_name['sales_channel'].message_type = _SALESCHANNEL
_GETETICKETDETAILRESPONSE.fields_by_name['takeway'].message_type = _TAKEWAY
_GETETICKETDETAILRESPONSE.fields_by_name['products'].message_type = _ETICKETDETAILPRODUCTINFO
_GETSALESEXPLAINEDERRORREPORTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSALESEXPLAINEDERRORREPORTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SALESEXPLAINEDERRORREPORT.fields_by_name['bom_products'].message_type = _BOMPRODUCTEXPLAINED
_SALESEXPLAINEDERRORREPORT.fields_by_name['sales_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SALESEXPLAINEDERRORREPORT.fields_by_name['operation_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSALESEXPLAINEDERRORREPORTRESPONSE.fields_by_name['rows'].message_type = _SALESEXPLAINEDERRORREPORT
_GETMATERIALSUPPLIESRESPONSE_MATERIALSUPPLIES.containing_type = _GETMATERIALSUPPLIESRESPONSE
_GETMATERIALSUPPLIESRESPONSE.fields_by_name['rows'].message_type = _GETMATERIALSUPPLIESRESPONSE_MATERIALSUPPLIES
_GETMATERIALCOSTREPORTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETMATERIALCOSTREPORTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_COSTDATA.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_COSTDATA.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_COSTDATA.fields_by_name['last_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETMATERIALCOSTREPORTRESPONSE.fields_by_name['rows'].message_type = _COSTDATA
_GETMATERIALCOSTREPORTRESPONSE.fields_by_name['end_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETBALANCECOSTREPORTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETBALANCECOSTREPORTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETBALANCECOSTREPORTRESPONSE.fields_by_name['rows'].message_type = _BALANCEDATA
_BALANCEDATA.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_BALANCEDATA.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_BALANCEDATA.fields_by_name['last_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['Null'] = _NULL
DESCRIPTOR.message_types_by_name['Pong'] = _PONG
DESCRIPTOR.message_types_by_name['Result'] = _RESULT
DESCRIPTOR.message_types_by_name['GetETicketReportRequest'] = _GETETICKETREPORTREQUEST
DESCRIPTOR.message_types_by_name['GetETicketReportResponse'] = _GETETICKETREPORTRESPONSE
DESCRIPTOR.message_types_by_name['ETicketReport'] = _ETICKETREPORT
DESCRIPTOR.message_types_by_name['Takeway'] = _TAKEWAY
DESCRIPTOR.message_types_by_name['StoreInfo'] = _STOREINFO
DESCRIPTOR.message_types_by_name['MemberInfo'] = _MEMBERINFO
DESCRIPTOR.message_types_by_name['Item'] = _ITEM
DESCRIPTOR.message_types_by_name['Mixture'] = _MIXTURE
DESCRIPTOR.message_types_by_name['Payment'] = _PAYMENT
DESCRIPTOR.message_types_by_name['SalesChannel'] = _SALESCHANNEL
DESCRIPTOR.message_types_by_name['Discount'] = _DISCOUNT
DESCRIPTOR.message_types_by_name['ProductInfo'] = _PRODUCTINFO
DESCRIPTOR.message_types_by_name['Fee'] = _FEE
DESCRIPTOR.message_types_by_name['GetStoreSalesReportRequest'] = _GETSTORESALESREPORTREQUEST
DESCRIPTOR.message_types_by_name['RegionGroupMethod'] = _REGIONGROUPMETHOD
DESCRIPTOR.message_types_by_name['RegionInfo'] = _REGIONINFO
DESCRIPTOR.message_types_by_name['GetStoreSalesReportReponse'] = _GETSTORESALESREPORTREPONSE
DESCRIPTOR.message_types_by_name['TotalStoreSales'] = _TOTALSTORESALES
DESCRIPTOR.message_types_by_name['StoreSalesRecord'] = _STORESALESRECORD
DESCRIPTOR.message_types_by_name['GetProductSalesReportRequest'] = _GETPRODUCTSALESREPORTREQUEST
DESCRIPTOR.message_types_by_name['PeriodFilter'] = _PERIODFILTER
DESCRIPTOR.message_types_by_name['RegionFilter'] = _REGIONFILTER
DESCRIPTOR.message_types_by_name['ProductFilter'] = _PRODUCTFILTER
DESCRIPTOR.message_types_by_name['ProductGroupMethod'] = _PRODUCTGROUPMETHOD
DESCRIPTOR.message_types_by_name['SalesChannelFilter'] = _SALESCHANNELFILTER
DESCRIPTOR.message_types_by_name['GetProductSalesReportResponse'] = _GETPRODUCTSALESREPORTRESPONSE
DESCRIPTOR.message_types_by_name['ProductSalesTotal'] = _PRODUCTSALESTOTAL
DESCRIPTOR.message_types_by_name['ProductSalesRecord'] = _PRODUCTSALESRECORD
DESCRIPTOR.message_types_by_name['GetDiscountReportRequest'] = _GETDISCOUNTREPORTREQUEST
DESCRIPTOR.message_types_by_name['DiscountFilter'] = _DISCOUNTFILTER
DESCRIPTOR.message_types_by_name['GetDiscountReportResponse'] = _GETDISCOUNTREPORTRESPONSE
DESCRIPTOR.message_types_by_name['DiscountTotal'] = _DISCOUNTTOTAL
DESCRIPTOR.message_types_by_name['DiscountRecord'] = _DISCOUNTRECORD
DESCRIPTOR.message_types_by_name['GetPaymentReportRequest'] = _GETPAYMENTREPORTREQUEST
DESCRIPTOR.message_types_by_name['PaymentFilter'] = _PAYMENTFILTER
DESCRIPTOR.message_types_by_name['PaymentGroupMethod'] = _PAYMENTGROUPMETHOD
DESCRIPTOR.message_types_by_name['GetPaymentReportResponse'] = _GETPAYMENTREPORTRESPONSE
DESCRIPTOR.message_types_by_name['PaymentTotal'] = _PAYMENTTOTAL
DESCRIPTOR.message_types_by_name['PaymentRecord'] = _PAYMENTRECORD
DESCRIPTOR.message_types_by_name['PaymentMetaInfo'] = _PAYMENTMETAINFO
DESCRIPTOR.message_types_by_name['GetProductPaymentReportRequest'] = _GETPRODUCTPAYMENTREPORTREQUEST
DESCRIPTOR.message_types_by_name['GetProductPaymentReportResponse'] = _GETPRODUCTPAYMENTREPORTRESPONSE
DESCRIPTOR.message_types_by_name['ProductPaymentRecord'] = _PRODUCTPAYMENTRECORD
DESCRIPTOR.message_types_by_name['RegionMetaInfo'] = _REGIONMETAINFO
DESCRIPTOR.message_types_by_name['ProductMetaInfo'] = _PRODUCTMETAINFO
DESCRIPTOR.message_types_by_name['GetStoreChannelReportRequest'] = _GETSTORECHANNELREPORTREQUEST
DESCRIPTOR.message_types_by_name['GetStoreChannelReportResponse'] = _GETSTORECHANNELREPORTRESPONSE
DESCRIPTOR.message_types_by_name['StoreChannelSalesTotal'] = _STORECHANNELSALESTOTAL
DESCRIPTOR.message_types_by_name['StoreChanneSalesRecord'] = _STORECHANNESALESRECORD
DESCRIPTOR.message_types_by_name['GetProductChannelReportRequest'] = _GETPRODUCTCHANNELREPORTREQUEST
DESCRIPTOR.message_types_by_name['GetProductChannelReportResponse'] = _GETPRODUCTCHANNELREPORTRESPONSE
DESCRIPTOR.message_types_by_name['ProductChannelSalesTotal'] = _PRODUCTCHANNELSALESTOTAL
DESCRIPTOR.message_types_by_name['ProductChanneSalesRecord'] = _PRODUCTCHANNESALESRECORD
DESCRIPTOR.message_types_by_name['GetStorePeriodReportRequest'] = _GETSTOREPERIODREPORTREQUEST
DESCRIPTOR.message_types_by_name['GetStorePeriodReportResponse'] = _GETSTOREPERIODREPORTRESPONSE
DESCRIPTOR.message_types_by_name['PeriodTotal'] = _PERIODTOTAL
DESCRIPTOR.message_types_by_name['StorePeriodRecord'] = _STOREPERIODRECORD
DESCRIPTOR.message_types_by_name['GetStoreChannelPeriodReportRequest'] = _GETSTORECHANNELPERIODREPORTREQUEST
DESCRIPTOR.message_types_by_name['GetStoreChannelPeriodReportResponse'] = _GETSTORECHANNELPERIODREPORTRESPONSE
DESCRIPTOR.message_types_by_name['StoreChannelPeriodRecord'] = _STORECHANNELPERIODRECORD
DESCRIPTOR.message_types_by_name['GetPaymentPeriodReportRequest'] = _GETPAYMENTPERIODREPORTREQUEST
DESCRIPTOR.message_types_by_name['GetPaymentPeriodReportResponse'] = _GETPAYMENTPERIODREPORTRESPONSE
DESCRIPTOR.message_types_by_name['PaymentPeriodRecord'] = _PAYMENTPERIODRECORD
DESCRIPTOR.message_types_by_name['GetProductPeriodReportRequest'] = _GETPRODUCTPERIODREPORTREQUEST
DESCRIPTOR.message_types_by_name['GetProductPeriodReportResponse'] = _GETPRODUCTPERIODREPORTRESPONSE
DESCRIPTOR.message_types_by_name['ProductPeriodRecord'] = _PRODUCTPERIODRECORD
DESCRIPTOR.message_types_by_name['GetParseFaliedETicketListRequest'] = _GETPARSEFALIEDETICKETLISTREQUEST
DESCRIPTOR.message_types_by_name['GetParseFaliedETicketListResponse'] = _GETPARSEFALIEDETICKETLISTRESPONSE
DESCRIPTOR.message_types_by_name['ParseFailedETicket'] = _PARSEFAILEDETICKET
DESCRIPTOR.message_types_by_name['GetStoreSalesReportForPDARequest'] = _GETSTORESALESREPORTFORPDAREQUEST
DESCRIPTOR.message_types_by_name['GetStoreSalesReportForPDAResponse'] = _GETSTORESALESREPORTFORPDARESPONSE
DESCRIPTOR.message_types_by_name['ChannelSalesAmount'] = _CHANNELSALESAMOUNT
DESCRIPTOR.message_types_by_name['GetETicketTraceIdsRequest'] = _GETETICKETTRACEIDSREQUEST
DESCRIPTOR.message_types_by_name['PosTicketNumber'] = _POSTICKETNUMBER
DESCRIPTOR.message_types_by_name['GetETicketTraceIdsResponse'] = _GETETICKETTRACEIDSRESPONSE
DESCRIPTOR.message_types_by_name['GetProductSalesOffDemandRequest'] = _GETPRODUCTSALESOFFDEMANDREQUEST
DESCRIPTOR.message_types_by_name['GetProductSalesOffDemandRequest2'] = _GETPRODUCTSALESOFFDEMANDREQUEST2
DESCRIPTOR.message_types_by_name['GetProductSalesOffDemandResponse2'] = _GETPRODUCTSALESOFFDEMANDRESPONSE2
DESCRIPTOR.message_types_by_name['ProductsSalesInfo'] = _PRODUCTSSALESINFO
DESCRIPTOR.message_types_by_name['GetProductSalesOffDemandResponse'] = _GETPRODUCTSALESOFFDEMANDRESPONSE
DESCRIPTOR.message_types_by_name['GetSalesExplainedReportRequest'] = _GETSALESEXPLAINEDREPORTREQUEST
DESCRIPTOR.message_types_by_name['SalesExplainedReportProperties'] = _SALESEXPLAINEDREPORTPROPERTIES
DESCRIPTOR.message_types_by_name['SalesExplainedReport'] = _SALESEXPLAINEDREPORT
DESCRIPTOR.message_types_by_name['GetSalesExplainedReportResponse'] = _GETSALESEXPLAINEDREPORTRESPONSE
DESCRIPTOR.message_types_by_name['GetSalesExplainedSummaryReportRequest'] = _GETSALESEXPLAINEDSUMMARYREPORTREQUEST
DESCRIPTOR.message_types_by_name['SalesExplainedSummaryReport'] = _SALESEXPLAINEDSUMMARYREPORT
DESCRIPTOR.message_types_by_name['GetSalesExplainedSummaryReportResponse'] = _GETSALESEXPLAINEDSUMMARYREPORTRESPONSE
DESCRIPTOR.message_types_by_name['GetETicketDetailRequest'] = _GETETICKETDETAILREQUEST
DESCRIPTOR.message_types_by_name['EticketDetailMaterialInfo'] = _ETICKETDETAILMATERIALINFO
DESCRIPTOR.message_types_by_name['EticketDetailMakeUpInfo'] = _ETICKETDETAILMAKEUPINFO
DESCRIPTOR.message_types_by_name['EticketDetailMakeUpRule'] = _ETICKETDETAILMAKEUPRULE
DESCRIPTOR.message_types_by_name['EticketDetailProductInfo'] = _ETICKETDETAILPRODUCTINFO
DESCRIPTOR.message_types_by_name['GetETicketDetailResponse'] = _GETETICKETDETAILRESPONSE
DESCRIPTOR.message_types_by_name['GetSalesExplainedErrorReportRequest'] = _GETSALESEXPLAINEDERRORREPORTREQUEST
DESCRIPTOR.message_types_by_name['BomProductExplained'] = _BOMPRODUCTEXPLAINED
DESCRIPTOR.message_types_by_name['SalesExplainedErrorReport'] = _SALESEXPLAINEDERRORREPORT
DESCRIPTOR.message_types_by_name['GetSalesExplainedErrorReportResponse'] = _GETSALESEXPLAINEDERRORREPORTRESPONSE
DESCRIPTOR.message_types_by_name['GetMaterialSuppliesRequest'] = _GETMATERIALSUPPLIESREQUEST
DESCRIPTOR.message_types_by_name['GetMaterialSuppliesResponse'] = _GETMATERIALSUPPLIESRESPONSE
DESCRIPTOR.message_types_by_name['GetMaterialCostReportRequest'] = _GETMATERIALCOSTREPORTREQUEST
DESCRIPTOR.message_types_by_name['CostData'] = _COSTDATA
DESCRIPTOR.message_types_by_name['GetMaterialCostReportResponse'] = _GETMATERIALCOSTREPORTRESPONSE
DESCRIPTOR.message_types_by_name['GetBalanceCostReportRequest'] = _GETBALANCECOSTREPORTREQUEST
DESCRIPTOR.message_types_by_name['GetBalanceCostReportResponse'] = _GETBALANCECOSTREPORTRESPONSE
DESCRIPTOR.message_types_by_name['BalanceData'] = _BALANCEDATA
DESCRIPTOR.enum_types_by_name['TicketType'] = _TICKETTYPE
DESCRIPTOR.enum_types_by_name['DeliveryType'] = _DELIVERYTYPE
DESCRIPTOR.enum_types_by_name['PeriodGroupMethod'] = _PERIODGROUPMETHOD
DESCRIPTOR.enum_types_by_name['RegionType'] = _REGIONTYPE
DESCRIPTOR.enum_types_by_name['StoreType'] = _STORETYPE
DESCRIPTOR.enum_types_by_name['ProductFilterType'] = _PRODUCTFILTERTYPE
DESCRIPTOR.enum_types_by_name['SalesChannelGroupMethod'] = _SALESCHANNELGROUPMETHOD
DESCRIPTOR.enum_types_by_name['ProductGroupBy'] = _PRODUCTGROUPBY
DESCRIPTOR.enum_types_by_name['DiscountFilterBy'] = _DISCOUNTFILTERBY
DESCRIPTOR.enum_types_by_name['DiscountGroupMethod'] = _DISCOUNTGROUPMETHOD
DESCRIPTOR.enum_types_by_name['PaymentFilterBy'] = _PAYMENTFILTERBY
DESCRIPTOR.enum_types_by_name['PaymentGroupBy'] = _PAYMENTGROUPBY
DESCRIPTOR.enum_types_by_name['DeliveryTypeGroupMethod'] = _DELIVERYTYPEGROUPMETHOD
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Null = _reflection.GeneratedProtocolMessageType('Null', (_message.Message,), dict(
  DESCRIPTOR = _NULL,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.Null)
  ))
_sym_db.RegisterMessage(Null)

Pong = _reflection.GeneratedProtocolMessageType('Pong', (_message.Message,), dict(
  DESCRIPTOR = _PONG,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.Pong)
  ))
_sym_db.RegisterMessage(Pong)

Result = _reflection.GeneratedProtocolMessageType('Result', (_message.Message,), dict(
  DESCRIPTOR = _RESULT,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.Result)
  ))
_sym_db.RegisterMessage(Result)

GetETicketReportRequest = _reflection.GeneratedProtocolMessageType('GetETicketReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETETICKETREPORTREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetETicketReportRequest)
  ))
_sym_db.RegisterMessage(GetETicketReportRequest)

GetETicketReportResponse = _reflection.GeneratedProtocolMessageType('GetETicketReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETETICKETREPORTRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetETicketReportResponse)
  ))
_sym_db.RegisterMessage(GetETicketReportResponse)

ETicketReport = _reflection.GeneratedProtocolMessageType('ETicketReport', (_message.Message,), dict(
  DESCRIPTOR = _ETICKETREPORT,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.ETicketReport)
  ))
_sym_db.RegisterMessage(ETicketReport)

Takeway = _reflection.GeneratedProtocolMessageType('Takeway', (_message.Message,), dict(
  DESCRIPTOR = _TAKEWAY,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.Takeway)
  ))
_sym_db.RegisterMessage(Takeway)

StoreInfo = _reflection.GeneratedProtocolMessageType('StoreInfo', (_message.Message,), dict(
  DESCRIPTOR = _STOREINFO,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.StoreInfo)
  ))
_sym_db.RegisterMessage(StoreInfo)

MemberInfo = _reflection.GeneratedProtocolMessageType('MemberInfo', (_message.Message,), dict(
  DESCRIPTOR = _MEMBERINFO,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.MemberInfo)
  ))
_sym_db.RegisterMessage(MemberInfo)

Item = _reflection.GeneratedProtocolMessageType('Item', (_message.Message,), dict(
  DESCRIPTOR = _ITEM,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.Item)
  ))
_sym_db.RegisterMessage(Item)

Mixture = _reflection.GeneratedProtocolMessageType('Mixture', (_message.Message,), dict(
  DESCRIPTOR = _MIXTURE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.Mixture)
  ))
_sym_db.RegisterMessage(Mixture)

Payment = _reflection.GeneratedProtocolMessageType('Payment', (_message.Message,), dict(
  DESCRIPTOR = _PAYMENT,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.Payment)
  ))
_sym_db.RegisterMessage(Payment)

SalesChannel = _reflection.GeneratedProtocolMessageType('SalesChannel', (_message.Message,), dict(
  DESCRIPTOR = _SALESCHANNEL,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.SalesChannel)
  ))
_sym_db.RegisterMessage(SalesChannel)

Discount = _reflection.GeneratedProtocolMessageType('Discount', (_message.Message,), dict(
  DESCRIPTOR = _DISCOUNT,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.Discount)
  ))
_sym_db.RegisterMessage(Discount)

ProductInfo = _reflection.GeneratedProtocolMessageType('ProductInfo', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTINFO,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.ProductInfo)
  ))
_sym_db.RegisterMessage(ProductInfo)

Fee = _reflection.GeneratedProtocolMessageType('Fee', (_message.Message,), dict(
  DESCRIPTOR = _FEE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.Fee)
  ))
_sym_db.RegisterMessage(Fee)

GetStoreSalesReportRequest = _reflection.GeneratedProtocolMessageType('GetStoreSalesReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTORESALESREPORTREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetStoreSalesReportRequest)
  ))
_sym_db.RegisterMessage(GetStoreSalesReportRequest)

RegionGroupMethod = _reflection.GeneratedProtocolMessageType('RegionGroupMethod', (_message.Message,), dict(
  DESCRIPTOR = _REGIONGROUPMETHOD,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.RegionGroupMethod)
  ))
_sym_db.RegisterMessage(RegionGroupMethod)

RegionInfo = _reflection.GeneratedProtocolMessageType('RegionInfo', (_message.Message,), dict(
  DESCRIPTOR = _REGIONINFO,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.RegionInfo)
  ))
_sym_db.RegisterMessage(RegionInfo)

GetStoreSalesReportReponse = _reflection.GeneratedProtocolMessageType('GetStoreSalesReportReponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTORESALESREPORTREPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetStoreSalesReportReponse)
  ))
_sym_db.RegisterMessage(GetStoreSalesReportReponse)

TotalStoreSales = _reflection.GeneratedProtocolMessageType('TotalStoreSales', (_message.Message,), dict(
  DESCRIPTOR = _TOTALSTORESALES,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.TotalStoreSales)
  ))
_sym_db.RegisterMessage(TotalStoreSales)

StoreSalesRecord = _reflection.GeneratedProtocolMessageType('StoreSalesRecord', (_message.Message,), dict(
  DESCRIPTOR = _STORESALESRECORD,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.StoreSalesRecord)
  ))
_sym_db.RegisterMessage(StoreSalesRecord)

GetProductSalesReportRequest = _reflection.GeneratedProtocolMessageType('GetProductSalesReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTSALESREPORTREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetProductSalesReportRequest)
  ))
_sym_db.RegisterMessage(GetProductSalesReportRequest)

PeriodFilter = _reflection.GeneratedProtocolMessageType('PeriodFilter', (_message.Message,), dict(
  DESCRIPTOR = _PERIODFILTER,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.PeriodFilter)
  ))
_sym_db.RegisterMessage(PeriodFilter)

RegionFilter = _reflection.GeneratedProtocolMessageType('RegionFilter', (_message.Message,), dict(
  DESCRIPTOR = _REGIONFILTER,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.RegionFilter)
  ))
_sym_db.RegisterMessage(RegionFilter)

ProductFilter = _reflection.GeneratedProtocolMessageType('ProductFilter', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTFILTER,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.ProductFilter)
  ))
_sym_db.RegisterMessage(ProductFilter)

ProductGroupMethod = _reflection.GeneratedProtocolMessageType('ProductGroupMethod', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTGROUPMETHOD,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.ProductGroupMethod)
  ))
_sym_db.RegisterMessage(ProductGroupMethod)

SalesChannelFilter = _reflection.GeneratedProtocolMessageType('SalesChannelFilter', (_message.Message,), dict(
  DESCRIPTOR = _SALESCHANNELFILTER,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.SalesChannelFilter)
  ))
_sym_db.RegisterMessage(SalesChannelFilter)

GetProductSalesReportResponse = _reflection.GeneratedProtocolMessageType('GetProductSalesReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTSALESREPORTRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetProductSalesReportResponse)
  ))
_sym_db.RegisterMessage(GetProductSalesReportResponse)

ProductSalesTotal = _reflection.GeneratedProtocolMessageType('ProductSalesTotal', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTSALESTOTAL,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.ProductSalesTotal)
  ))
_sym_db.RegisterMessage(ProductSalesTotal)

ProductSalesRecord = _reflection.GeneratedProtocolMessageType('ProductSalesRecord', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTSALESRECORD,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.ProductSalesRecord)
  ))
_sym_db.RegisterMessage(ProductSalesRecord)

GetDiscountReportRequest = _reflection.GeneratedProtocolMessageType('GetDiscountReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDISCOUNTREPORTREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetDiscountReportRequest)
  ))
_sym_db.RegisterMessage(GetDiscountReportRequest)

DiscountFilter = _reflection.GeneratedProtocolMessageType('DiscountFilter', (_message.Message,), dict(
  DESCRIPTOR = _DISCOUNTFILTER,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.DiscountFilter)
  ))
_sym_db.RegisterMessage(DiscountFilter)

GetDiscountReportResponse = _reflection.GeneratedProtocolMessageType('GetDiscountReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETDISCOUNTREPORTRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetDiscountReportResponse)
  ))
_sym_db.RegisterMessage(GetDiscountReportResponse)

DiscountTotal = _reflection.GeneratedProtocolMessageType('DiscountTotal', (_message.Message,), dict(
  DESCRIPTOR = _DISCOUNTTOTAL,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.DiscountTotal)
  ))
_sym_db.RegisterMessage(DiscountTotal)

DiscountRecord = _reflection.GeneratedProtocolMessageType('DiscountRecord', (_message.Message,), dict(
  DESCRIPTOR = _DISCOUNTRECORD,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.DiscountRecord)
  ))
_sym_db.RegisterMessage(DiscountRecord)

GetPaymentReportRequest = _reflection.GeneratedProtocolMessageType('GetPaymentReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPAYMENTREPORTREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetPaymentReportRequest)
  ))
_sym_db.RegisterMessage(GetPaymentReportRequest)

PaymentFilter = _reflection.GeneratedProtocolMessageType('PaymentFilter', (_message.Message,), dict(
  DESCRIPTOR = _PAYMENTFILTER,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.PaymentFilter)
  ))
_sym_db.RegisterMessage(PaymentFilter)

PaymentGroupMethod = _reflection.GeneratedProtocolMessageType('PaymentGroupMethod', (_message.Message,), dict(
  DESCRIPTOR = _PAYMENTGROUPMETHOD,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.PaymentGroupMethod)
  ))
_sym_db.RegisterMessage(PaymentGroupMethod)

GetPaymentReportResponse = _reflection.GeneratedProtocolMessageType('GetPaymentReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPAYMENTREPORTRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetPaymentReportResponse)
  ))
_sym_db.RegisterMessage(GetPaymentReportResponse)

PaymentTotal = _reflection.GeneratedProtocolMessageType('PaymentTotal', (_message.Message,), dict(
  DESCRIPTOR = _PAYMENTTOTAL,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.PaymentTotal)
  ))
_sym_db.RegisterMessage(PaymentTotal)

PaymentRecord = _reflection.GeneratedProtocolMessageType('PaymentRecord', (_message.Message,), dict(
  DESCRIPTOR = _PAYMENTRECORD,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.PaymentRecord)
  ))
_sym_db.RegisterMessage(PaymentRecord)

PaymentMetaInfo = _reflection.GeneratedProtocolMessageType('PaymentMetaInfo', (_message.Message,), dict(
  DESCRIPTOR = _PAYMENTMETAINFO,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.PaymentMetaInfo)
  ))
_sym_db.RegisterMessage(PaymentMetaInfo)

GetProductPaymentReportRequest = _reflection.GeneratedProtocolMessageType('GetProductPaymentReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTPAYMENTREPORTREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetProductPaymentReportRequest)
  ))
_sym_db.RegisterMessage(GetProductPaymentReportRequest)

GetProductPaymentReportResponse = _reflection.GeneratedProtocolMessageType('GetProductPaymentReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTPAYMENTREPORTRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetProductPaymentReportResponse)
  ))
_sym_db.RegisterMessage(GetProductPaymentReportResponse)

ProductPaymentRecord = _reflection.GeneratedProtocolMessageType('ProductPaymentRecord', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTPAYMENTRECORD,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.ProductPaymentRecord)
  ))
_sym_db.RegisterMessage(ProductPaymentRecord)

RegionMetaInfo = _reflection.GeneratedProtocolMessageType('RegionMetaInfo', (_message.Message,), dict(
  DESCRIPTOR = _REGIONMETAINFO,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.RegionMetaInfo)
  ))
_sym_db.RegisterMessage(RegionMetaInfo)

ProductMetaInfo = _reflection.GeneratedProtocolMessageType('ProductMetaInfo', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTMETAINFO,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.ProductMetaInfo)
  ))
_sym_db.RegisterMessage(ProductMetaInfo)

GetStoreChannelReportRequest = _reflection.GeneratedProtocolMessageType('GetStoreChannelReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTORECHANNELREPORTREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetStoreChannelReportRequest)
  ))
_sym_db.RegisterMessage(GetStoreChannelReportRequest)

GetStoreChannelReportResponse = _reflection.GeneratedProtocolMessageType('GetStoreChannelReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTORECHANNELREPORTRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetStoreChannelReportResponse)
  ))
_sym_db.RegisterMessage(GetStoreChannelReportResponse)

StoreChannelSalesTotal = _reflection.GeneratedProtocolMessageType('StoreChannelSalesTotal', (_message.Message,), dict(
  DESCRIPTOR = _STORECHANNELSALESTOTAL,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.StoreChannelSalesTotal)
  ))
_sym_db.RegisterMessage(StoreChannelSalesTotal)

StoreChanneSalesRecord = _reflection.GeneratedProtocolMessageType('StoreChanneSalesRecord', (_message.Message,), dict(
  DESCRIPTOR = _STORECHANNESALESRECORD,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.StoreChanneSalesRecord)
  ))
_sym_db.RegisterMessage(StoreChanneSalesRecord)

GetProductChannelReportRequest = _reflection.GeneratedProtocolMessageType('GetProductChannelReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTCHANNELREPORTREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetProductChannelReportRequest)
  ))
_sym_db.RegisterMessage(GetProductChannelReportRequest)

GetProductChannelReportResponse = _reflection.GeneratedProtocolMessageType('GetProductChannelReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTCHANNELREPORTRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetProductChannelReportResponse)
  ))
_sym_db.RegisterMessage(GetProductChannelReportResponse)

ProductChannelSalesTotal = _reflection.GeneratedProtocolMessageType('ProductChannelSalesTotal', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTCHANNELSALESTOTAL,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.ProductChannelSalesTotal)
  ))
_sym_db.RegisterMessage(ProductChannelSalesTotal)

ProductChanneSalesRecord = _reflection.GeneratedProtocolMessageType('ProductChanneSalesRecord', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTCHANNESALESRECORD,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.ProductChanneSalesRecord)
  ))
_sym_db.RegisterMessage(ProductChanneSalesRecord)

GetStorePeriodReportRequest = _reflection.GeneratedProtocolMessageType('GetStorePeriodReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOREPERIODREPORTREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetStorePeriodReportRequest)
  ))
_sym_db.RegisterMessage(GetStorePeriodReportRequest)

GetStorePeriodReportResponse = _reflection.GeneratedProtocolMessageType('GetStorePeriodReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOREPERIODREPORTRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetStorePeriodReportResponse)
  ))
_sym_db.RegisterMessage(GetStorePeriodReportResponse)

PeriodTotal = _reflection.GeneratedProtocolMessageType('PeriodTotal', (_message.Message,), dict(
  DESCRIPTOR = _PERIODTOTAL,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.PeriodTotal)
  ))
_sym_db.RegisterMessage(PeriodTotal)

StorePeriodRecord = _reflection.GeneratedProtocolMessageType('StorePeriodRecord', (_message.Message,), dict(
  DESCRIPTOR = _STOREPERIODRECORD,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.StorePeriodRecord)
  ))
_sym_db.RegisterMessage(StorePeriodRecord)

GetStoreChannelPeriodReportRequest = _reflection.GeneratedProtocolMessageType('GetStoreChannelPeriodReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTORECHANNELPERIODREPORTREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetStoreChannelPeriodReportRequest)
  ))
_sym_db.RegisterMessage(GetStoreChannelPeriodReportRequest)

GetStoreChannelPeriodReportResponse = _reflection.GeneratedProtocolMessageType('GetStoreChannelPeriodReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTORECHANNELPERIODREPORTRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetStoreChannelPeriodReportResponse)
  ))
_sym_db.RegisterMessage(GetStoreChannelPeriodReportResponse)

StoreChannelPeriodRecord = _reflection.GeneratedProtocolMessageType('StoreChannelPeriodRecord', (_message.Message,), dict(
  DESCRIPTOR = _STORECHANNELPERIODRECORD,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.StoreChannelPeriodRecord)
  ))
_sym_db.RegisterMessage(StoreChannelPeriodRecord)

GetPaymentPeriodReportRequest = _reflection.GeneratedProtocolMessageType('GetPaymentPeriodReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPAYMENTPERIODREPORTREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetPaymentPeriodReportRequest)
  ))
_sym_db.RegisterMessage(GetPaymentPeriodReportRequest)

GetPaymentPeriodReportResponse = _reflection.GeneratedProtocolMessageType('GetPaymentPeriodReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPAYMENTPERIODREPORTRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetPaymentPeriodReportResponse)
  ))
_sym_db.RegisterMessage(GetPaymentPeriodReportResponse)

PaymentPeriodRecord = _reflection.GeneratedProtocolMessageType('PaymentPeriodRecord', (_message.Message,), dict(
  DESCRIPTOR = _PAYMENTPERIODRECORD,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.PaymentPeriodRecord)
  ))
_sym_db.RegisterMessage(PaymentPeriodRecord)

GetProductPeriodReportRequest = _reflection.GeneratedProtocolMessageType('GetProductPeriodReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTPERIODREPORTREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetProductPeriodReportRequest)
  ))
_sym_db.RegisterMessage(GetProductPeriodReportRequest)

GetProductPeriodReportResponse = _reflection.GeneratedProtocolMessageType('GetProductPeriodReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTPERIODREPORTRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetProductPeriodReportResponse)
  ))
_sym_db.RegisterMessage(GetProductPeriodReportResponse)

ProductPeriodRecord = _reflection.GeneratedProtocolMessageType('ProductPeriodRecord', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTPERIODRECORD,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.ProductPeriodRecord)
  ))
_sym_db.RegisterMessage(ProductPeriodRecord)

GetParseFaliedETicketListRequest = _reflection.GeneratedProtocolMessageType('GetParseFaliedETicketListRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPARSEFALIEDETICKETLISTREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetParseFaliedETicketListRequest)
  ))
_sym_db.RegisterMessage(GetParseFaliedETicketListRequest)

GetParseFaliedETicketListResponse = _reflection.GeneratedProtocolMessageType('GetParseFaliedETicketListResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPARSEFALIEDETICKETLISTRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetParseFaliedETicketListResponse)
  ))
_sym_db.RegisterMessage(GetParseFaliedETicketListResponse)

ParseFailedETicket = _reflection.GeneratedProtocolMessageType('ParseFailedETicket', (_message.Message,), dict(
  DESCRIPTOR = _PARSEFAILEDETICKET,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.ParseFailedETicket)
  ))
_sym_db.RegisterMessage(ParseFailedETicket)

GetStoreSalesReportForPDARequest = _reflection.GeneratedProtocolMessageType('GetStoreSalesReportForPDARequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTORESALESREPORTFORPDAREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetStoreSalesReportForPDARequest)
  ))
_sym_db.RegisterMessage(GetStoreSalesReportForPDARequest)

GetStoreSalesReportForPDAResponse = _reflection.GeneratedProtocolMessageType('GetStoreSalesReportForPDAResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTORESALESREPORTFORPDARESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetStoreSalesReportForPDAResponse)
  ))
_sym_db.RegisterMessage(GetStoreSalesReportForPDAResponse)

ChannelSalesAmount = _reflection.GeneratedProtocolMessageType('ChannelSalesAmount', (_message.Message,), dict(
  DESCRIPTOR = _CHANNELSALESAMOUNT,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.ChannelSalesAmount)
  ))
_sym_db.RegisterMessage(ChannelSalesAmount)

GetETicketTraceIdsRequest = _reflection.GeneratedProtocolMessageType('GetETicketTraceIdsRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETETICKETTRACEIDSREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetETicketTraceIdsRequest)
  ))
_sym_db.RegisterMessage(GetETicketTraceIdsRequest)

PosTicketNumber = _reflection.GeneratedProtocolMessageType('PosTicketNumber', (_message.Message,), dict(
  DESCRIPTOR = _POSTICKETNUMBER,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.PosTicketNumber)
  ))
_sym_db.RegisterMessage(PosTicketNumber)

GetETicketTraceIdsResponse = _reflection.GeneratedProtocolMessageType('GetETicketTraceIdsResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETETICKETTRACEIDSRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetETicketTraceIdsResponse)
  ))
_sym_db.RegisterMessage(GetETicketTraceIdsResponse)

GetProductSalesOffDemandRequest = _reflection.GeneratedProtocolMessageType('GetProductSalesOffDemandRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTSALESOFFDEMANDREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetProductSalesOffDemandRequest)
  ))
_sym_db.RegisterMessage(GetProductSalesOffDemandRequest)

GetProductSalesOffDemandRequest2 = _reflection.GeneratedProtocolMessageType('GetProductSalesOffDemandRequest2', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTSALESOFFDEMANDREQUEST2,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetProductSalesOffDemandRequest2)
  ))
_sym_db.RegisterMessage(GetProductSalesOffDemandRequest2)

GetProductSalesOffDemandResponse2 = _reflection.GeneratedProtocolMessageType('GetProductSalesOffDemandResponse2', (_message.Message,), dict(

  SaleInfo = _reflection.GeneratedProtocolMessageType('SaleInfo', (_message.Message,), dict(
    DESCRIPTOR = _GETPRODUCTSALESOFFDEMANDRESPONSE2_SALEINFO,
    __module__ = 'report.operation_report_pb2'
    # @@protoc_insertion_point(class_scope:operation_report.GetProductSalesOffDemandResponse2.SaleInfo)
    ))
  ,
  DESCRIPTOR = _GETPRODUCTSALESOFFDEMANDRESPONSE2,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetProductSalesOffDemandResponse2)
  ))
_sym_db.RegisterMessage(GetProductSalesOffDemandResponse2)
_sym_db.RegisterMessage(GetProductSalesOffDemandResponse2.SaleInfo)

ProductsSalesInfo = _reflection.GeneratedProtocolMessageType('ProductsSalesInfo', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTSSALESINFO,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.ProductsSalesInfo)
  ))
_sym_db.RegisterMessage(ProductsSalesInfo)

GetProductSalesOffDemandResponse = _reflection.GeneratedProtocolMessageType('GetProductSalesOffDemandResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTSALESOFFDEMANDRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetProductSalesOffDemandResponse)
  ))
_sym_db.RegisterMessage(GetProductSalesOffDemandResponse)

GetSalesExplainedReportRequest = _reflection.GeneratedProtocolMessageType('GetSalesExplainedReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSALESEXPLAINEDREPORTREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetSalesExplainedReportRequest)
  ))
_sym_db.RegisterMessage(GetSalesExplainedReportRequest)

SalesExplainedReportProperties = _reflection.GeneratedProtocolMessageType('SalesExplainedReportProperties', (_message.Message,), dict(
  DESCRIPTOR = _SALESEXPLAINEDREPORTPROPERTIES,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.SalesExplainedReportProperties)
  ))
_sym_db.RegisterMessage(SalesExplainedReportProperties)

SalesExplainedReport = _reflection.GeneratedProtocolMessageType('SalesExplainedReport', (_message.Message,), dict(
  DESCRIPTOR = _SALESEXPLAINEDREPORT,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.SalesExplainedReport)
  ))
_sym_db.RegisterMessage(SalesExplainedReport)

GetSalesExplainedReportResponse = _reflection.GeneratedProtocolMessageType('GetSalesExplainedReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSALESEXPLAINEDREPORTRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetSalesExplainedReportResponse)
  ))
_sym_db.RegisterMessage(GetSalesExplainedReportResponse)

GetSalesExplainedSummaryReportRequest = _reflection.GeneratedProtocolMessageType('GetSalesExplainedSummaryReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSALESEXPLAINEDSUMMARYREPORTREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetSalesExplainedSummaryReportRequest)
  ))
_sym_db.RegisterMessage(GetSalesExplainedSummaryReportRequest)

SalesExplainedSummaryReport = _reflection.GeneratedProtocolMessageType('SalesExplainedSummaryReport', (_message.Message,), dict(
  DESCRIPTOR = _SALESEXPLAINEDSUMMARYREPORT,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.SalesExplainedSummaryReport)
  ))
_sym_db.RegisterMessage(SalesExplainedSummaryReport)

GetSalesExplainedSummaryReportResponse = _reflection.GeneratedProtocolMessageType('GetSalesExplainedSummaryReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSALESEXPLAINEDSUMMARYREPORTRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetSalesExplainedSummaryReportResponse)
  ))
_sym_db.RegisterMessage(GetSalesExplainedSummaryReportResponse)

GetETicketDetailRequest = _reflection.GeneratedProtocolMessageType('GetETicketDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETETICKETDETAILREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetETicketDetailRequest)
  ))
_sym_db.RegisterMessage(GetETicketDetailRequest)

EticketDetailMaterialInfo = _reflection.GeneratedProtocolMessageType('EticketDetailMaterialInfo', (_message.Message,), dict(
  DESCRIPTOR = _ETICKETDETAILMATERIALINFO,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.EticketDetailMaterialInfo)
  ))
_sym_db.RegisterMessage(EticketDetailMaterialInfo)

EticketDetailMakeUpInfo = _reflection.GeneratedProtocolMessageType('EticketDetailMakeUpInfo', (_message.Message,), dict(
  DESCRIPTOR = _ETICKETDETAILMAKEUPINFO,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.EticketDetailMakeUpInfo)
  ))
_sym_db.RegisterMessage(EticketDetailMakeUpInfo)

EticketDetailMakeUpRule = _reflection.GeneratedProtocolMessageType('EticketDetailMakeUpRule', (_message.Message,), dict(
  DESCRIPTOR = _ETICKETDETAILMAKEUPRULE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.EticketDetailMakeUpRule)
  ))
_sym_db.RegisterMessage(EticketDetailMakeUpRule)

EticketDetailProductInfo = _reflection.GeneratedProtocolMessageType('EticketDetailProductInfo', (_message.Message,), dict(
  DESCRIPTOR = _ETICKETDETAILPRODUCTINFO,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.EticketDetailProductInfo)
  ))
_sym_db.RegisterMessage(EticketDetailProductInfo)

GetETicketDetailResponse = _reflection.GeneratedProtocolMessageType('GetETicketDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETETICKETDETAILRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetETicketDetailResponse)
  ))
_sym_db.RegisterMessage(GetETicketDetailResponse)

GetSalesExplainedErrorReportRequest = _reflection.GeneratedProtocolMessageType('GetSalesExplainedErrorReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSALESEXPLAINEDERRORREPORTREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetSalesExplainedErrorReportRequest)
  ))
_sym_db.RegisterMessage(GetSalesExplainedErrorReportRequest)

BomProductExplained = _reflection.GeneratedProtocolMessageType('BomProductExplained', (_message.Message,), dict(
  DESCRIPTOR = _BOMPRODUCTEXPLAINED,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.BomProductExplained)
  ))
_sym_db.RegisterMessage(BomProductExplained)

SalesExplainedErrorReport = _reflection.GeneratedProtocolMessageType('SalesExplainedErrorReport', (_message.Message,), dict(
  DESCRIPTOR = _SALESEXPLAINEDERRORREPORT,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.SalesExplainedErrorReport)
  ))
_sym_db.RegisterMessage(SalesExplainedErrorReport)

GetSalesExplainedErrorReportResponse = _reflection.GeneratedProtocolMessageType('GetSalesExplainedErrorReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSALESEXPLAINEDERRORREPORTRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetSalesExplainedErrorReportResponse)
  ))
_sym_db.RegisterMessage(GetSalesExplainedErrorReportResponse)

GetMaterialSuppliesRequest = _reflection.GeneratedProtocolMessageType('GetMaterialSuppliesRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETMATERIALSUPPLIESREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetMaterialSuppliesRequest)
  ))
_sym_db.RegisterMessage(GetMaterialSuppliesRequest)

GetMaterialSuppliesResponse = _reflection.GeneratedProtocolMessageType('GetMaterialSuppliesResponse', (_message.Message,), dict(

  MaterialSupplies = _reflection.GeneratedProtocolMessageType('MaterialSupplies', (_message.Message,), dict(
    DESCRIPTOR = _GETMATERIALSUPPLIESRESPONSE_MATERIALSUPPLIES,
    __module__ = 'report.operation_report_pb2'
    # @@protoc_insertion_point(class_scope:operation_report.GetMaterialSuppliesResponse.MaterialSupplies)
    ))
  ,
  DESCRIPTOR = _GETMATERIALSUPPLIESRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetMaterialSuppliesResponse)
  ))
_sym_db.RegisterMessage(GetMaterialSuppliesResponse)
_sym_db.RegisterMessage(GetMaterialSuppliesResponse.MaterialSupplies)

GetMaterialCostReportRequest = _reflection.GeneratedProtocolMessageType('GetMaterialCostReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETMATERIALCOSTREPORTREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetMaterialCostReportRequest)
  ))
_sym_db.RegisterMessage(GetMaterialCostReportRequest)

CostData = _reflection.GeneratedProtocolMessageType('CostData', (_message.Message,), dict(
  DESCRIPTOR = _COSTDATA,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.CostData)
  ))
_sym_db.RegisterMessage(CostData)

GetMaterialCostReportResponse = _reflection.GeneratedProtocolMessageType('GetMaterialCostReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETMATERIALCOSTREPORTRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetMaterialCostReportResponse)
  ))
_sym_db.RegisterMessage(GetMaterialCostReportResponse)

GetBalanceCostReportRequest = _reflection.GeneratedProtocolMessageType('GetBalanceCostReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETBALANCECOSTREPORTREQUEST,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetBalanceCostReportRequest)
  ))
_sym_db.RegisterMessage(GetBalanceCostReportRequest)

GetBalanceCostReportResponse = _reflection.GeneratedProtocolMessageType('GetBalanceCostReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETBALANCECOSTREPORTRESPONSE,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.GetBalanceCostReportResponse)
  ))
_sym_db.RegisterMessage(GetBalanceCostReportResponse)

BalanceData = _reflection.GeneratedProtocolMessageType('BalanceData', (_message.Message,), dict(
  DESCRIPTOR = _BALANCEDATA,
  __module__ = 'report.operation_report_pb2'
  # @@protoc_insertion_point(class_scope:operation_report.BalanceData)
  ))
_sym_db.RegisterMessage(BalanceData)



_OPERATIONREPORT = _descriptor.ServiceDescriptor(
  name='OperationReport',
  full_name='operation_report.OperationReport',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=29694,
  serialized_end=34414,
  methods=[
  _descriptor.MethodDescriptor(
    name='Ping',
    full_name='operation_report.OperationReport.Ping',
    index=0,
    containing_service=None,
    input_type=_NULL,
    output_type=_PONG,
    serialized_options=_b('\202\323\344\223\002\025\022\023/api/v2/report/ping'),
  ),
  _descriptor.MethodDescriptor(
    name='GetETicketReport',
    full_name='operation_report.OperationReport.GetETicketReport',
    index=1,
    containing_service=None,
    input_type=_GETETICKETREPORTREQUEST,
    output_type=_GETETICKETREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002#\"\036/api/v2/report/e_ticket_report:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStoreSalesReport',
    full_name='operation_report.OperationReport.GetStoreSalesReport',
    index=2,
    containing_service=None,
    input_type=_GETSTORESALESREPORTREQUEST,
    output_type=_GETSTORESALESREPORTREPONSE,
    serialized_options=_b('\202\323\344\223\002&\"!/api/v2/report/store_sales_report:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetProductSalesReport',
    full_name='operation_report.OperationReport.GetProductSalesReport',
    index=3,
    containing_service=None,
    input_type=_GETPRODUCTSALESREPORTREQUEST,
    output_type=_GETPRODUCTSALESREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002(\"#/api/v2/report/product_sales_report:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetDiscountReport',
    full_name='operation_report.OperationReport.GetDiscountReport',
    index=4,
    containing_service=None,
    input_type=_GETDISCOUNTREPORTREQUEST,
    output_type=_GETDISCOUNTREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002\034\"\027/api/v2/report/discount:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPaymentReport',
    full_name='operation_report.OperationReport.GetPaymentReport',
    index=5,
    containing_service=None,
    input_type=_GETPAYMENTREPORTREQUEST,
    output_type=_GETPAYMENTREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002\033\"\026/api/v2/report/payment:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetProductPaymentReport',
    full_name='operation_report.OperationReport.GetProductPaymentReport',
    index=6,
    containing_service=None,
    input_type=_GETPRODUCTPAYMENTREPORTREQUEST,
    output_type=_GETPRODUCTPAYMENTREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002#\"\036/api/v2/report/product_payment:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStoreChannelReport',
    full_name='operation_report.OperationReport.GetStoreChannelReport',
    index=7,
    containing_service=None,
    input_type=_GETSTORECHANNELREPORTREQUEST,
    output_type=_GETSTORECHANNELREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002!\"\034/api/v2/report/store_channel:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetProductChannelReport',
    full_name='operation_report.OperationReport.GetProductChannelReport',
    index=8,
    containing_service=None,
    input_type=_GETPRODUCTCHANNELREPORTREQUEST,
    output_type=_GETPRODUCTCHANNELREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002#\"\036/api/v2/report/product_channel:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStorePeriodReport',
    full_name='operation_report.OperationReport.GetStorePeriodReport',
    index=9,
    containing_service=None,
    input_type=_GETSTOREPERIODREPORTREQUEST,
    output_type=_GETSTOREPERIODREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002 \"\033/api/v2/report/store_period:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStoreChannelPeriodReport',
    full_name='operation_report.OperationReport.GetStoreChannelPeriodReport',
    index=10,
    containing_service=None,
    input_type=_GETSTORECHANNELPERIODREPORTREQUEST,
    output_type=_GETSTORECHANNELPERIODREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002(\"#/api/v2/report/store_channel_period:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPaymentPeriodReport',
    full_name='operation_report.OperationReport.GetPaymentPeriodReport',
    index=11,
    containing_service=None,
    input_type=_GETPAYMENTPERIODREPORTREQUEST,
    output_type=_GETPAYMENTPERIODREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002\"\"\035/api/v2/report/payment_period:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetProductPeriodReport',
    full_name='operation_report.OperationReport.GetProductPeriodReport',
    index=12,
    containing_service=None,
    input_type=_GETPRODUCTPERIODREPORTREQUEST,
    output_type=_GETPRODUCTPERIODREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002\"\"\035/api/v2/report/product_period:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetParseFaliedETicketList',
    full_name='operation_report.OperationReport.GetParseFaliedETicketList',
    index=13,
    containing_service=None,
    input_type=_GETPARSEFALIEDETICKETLISTREQUEST,
    output_type=_GETPARSEFALIEDETICKETLISTRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\")/api/v2/report/parse_failed_e_ticket_list:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ReParseFailedETickets',
    full_name='operation_report.OperationReport.ReParseFailedETickets',
    index=14,
    containing_service=None,
    input_type=_NULL,
    output_type=_RESULT,
    serialized_options=_b('\202\323\344\223\002-\"(/api/v2/report/re_parse_failed_e_tickets:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStoreSalesReportForPDA',
    full_name='operation_report.OperationReport.GetStoreSalesReportForPDA',
    index=15,
    containing_service=None,
    input_type=_GETSTORESALESREPORTFORPDAREQUEST,
    output_type=_GETSTORESALESREPORTFORPDARESPONSE,
    serialized_options=_b('\202\323\344\223\002$\022\"/api/v2/report/store_sales_for_pda'),
  ),
  _descriptor.MethodDescriptor(
    name='GetEticketTraceIds',
    full_name='operation_report.OperationReport.GetEticketTraceIds',
    index=16,
    containing_service=None,
    input_type=_GETETICKETTRACEIDSREQUEST,
    output_type=_GETETICKETTRACEIDSRESPONSE,
    serialized_options=_b('\202\323\344\223\002#\022!/api/v2/report/e_ticket_trace_ids'),
  ),
  _descriptor.MethodDescriptor(
    name='GetProductSalesOffDemand',
    full_name='operation_report.OperationReport.GetProductSalesOffDemand',
    index=17,
    containing_service=None,
    input_type=_GETPRODUCTSALESOFFDEMANDREQUEST,
    output_type=_GETPRODUCTSALESOFFDEMANDRESPONSE,
    serialized_options=_b('\202\323\344\223\002)\022\'/api/v2/report/product_sales_off_demand'),
  ),
  _descriptor.MethodDescriptor(
    name='GetProductSalesOffDemand2',
    full_name='operation_report.OperationReport.GetProductSalesOffDemand2',
    index=18,
    containing_service=None,
    input_type=_GETPRODUCTSALESOFFDEMANDREQUEST2,
    output_type=_GETPRODUCTSALESOFFDEMANDRESPONSE2,
    serialized_options=_b('\202\323\344\223\002+\022)/api/v2/report/product_sales_off_demand_2'),
  ),
  _descriptor.MethodDescriptor(
    name='GetSalesExplainedReport',
    full_name='operation_report.OperationReport.GetSalesExplainedReport',
    index=19,
    containing_service=None,
    input_type=_GETSALESEXPLAINEDREPORTREQUEST,
    output_type=_GETSALESEXPLAINEDREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\022%/api/v2/report/sales_explained_report'),
  ),
  _descriptor.MethodDescriptor(
    name='GetSalesExplainedSummaryReport',
    full_name='operation_report.OperationReport.GetSalesExplainedSummaryReport',
    index=20,
    containing_service=None,
    input_type=_GETSALESEXPLAINEDSUMMARYREPORTREQUEST,
    output_type=_GETSALESEXPLAINEDSUMMARYREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\"-/api/v2/report/sales_explained_summary_report:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetETicketDetail',
    full_name='operation_report.OperationReport.GetETicketDetail',
    index=21,
    containing_service=None,
    input_type=_GETETICKETDETAILREQUEST,
    output_type=_GETETICKETDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\002#\"\036/api/v2/report/e_ticket_detail:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetSalesExplainedErrorReport',
    full_name='operation_report.OperationReport.GetSalesExplainedErrorReport',
    index=22,
    containing_service=None,
    input_type=_GETSALESEXPLAINEDERRORREPORTREQUEST,
    output_type=_GETSALESEXPLAINEDERRORREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002-\022+/api/v2/report/sales_explained_error_report'),
  ),
  _descriptor.MethodDescriptor(
    name='GetMaterialSupplies',
    full_name='operation_report.OperationReport.GetMaterialSupplies',
    index=23,
    containing_service=None,
    input_type=_GETMATERIALSUPPLIESREQUEST,
    output_type=_GETMATERIALSUPPLIESRESPONSE,
    serialized_options=_b('\202\323\344\223\002\036\022\034/api/v2/report/material_loss'),
  ),
  _descriptor.MethodDescriptor(
    name='GetSalesExplainedSupplementReport',
    full_name='operation_report.OperationReport.GetSalesExplainedSupplementReport',
    index=24,
    containing_service=None,
    input_type=_GETSALESEXPLAINEDREPORTREQUEST,
    output_type=_GETSALESEXPLAINEDREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\0220/api/v2/report/sales_explained_supplement_report'),
  ),
  _descriptor.MethodDescriptor(
    name='GetSalesExplainedSupplementSummaryReport',
    full_name='operation_report.OperationReport.GetSalesExplainedSupplementSummaryReport',
    index=25,
    containing_service=None,
    input_type=_GETSALESEXPLAINEDSUMMARYREPORTREQUEST,
    output_type=_GETSALESEXPLAINEDSUMMARYREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002=\"8/api/v2/report/sales_explained_supplement_summary_report:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetMaterialCostReport',
    full_name='operation_report.OperationReport.GetMaterialCostReport',
    index=26,
    containing_service=None,
    input_type=_GETMATERIALCOSTREPORTREQUEST,
    output_type=_GETMATERIALCOSTREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002\036\022\034/api/v2/report/cost/material'),
  ),
  _descriptor.MethodDescriptor(
    name='GetBalanceCostReport',
    full_name='operation_report.OperationReport.GetBalanceCostReport',
    index=27,
    containing_service=None,
    input_type=_GETBALANCECOSTREPORTREQUEST,
    output_type=_GETBALANCECOSTREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002\035\022\033/api/v2/report/cost/balance'),
  ),
])
_sym_db.RegisterServiceDescriptor(_OPERATIONREPORT)

DESCRIPTOR.services_by_name['OperationReport'] = _OPERATIONREPORT

# @@protoc_insertion_point(module_scope)
