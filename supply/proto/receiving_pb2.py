# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: receiving.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='receiving.proto',
  package='receiving',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x0freceiving.proto\x12\treceiving\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x8a\x01\n\x17SuspendReceivingRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06remark\x18\x02 \x01(\t\x12\x14\n\x0c\x64\x65lay_reason\x18\x03 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x04 \x01(\t\x12\x15\n\rnosign_reason\x18\x05 \x01(\t\x12\x11\n\tsignature\x18\x06 \x01(\t\"*\n\x18SuspendReceivingResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\xf7\x04\n\x16\x43reateReceivingRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x11\n\tmaster_id\x18\x03 \x01(\x04\x12\x13\n\x0bmaster_code\x18\x04 \x01(\t\x12\x17\n\x0f\x64\x65mand_order_id\x18\x05 \x01(\x04\x12\x19\n\x11\x64\x65mand_order_code\x18\x06 \x01(\t\x12\x14\n\x0cjde_order_id\x18\x07 \x01(\x04\x12\x14\n\x0cstorage_type\x18\x08 \x01(\t\x12\x13\n\x0breceived_by\x18\t \x01(\x04\x12\x1a\n\x12store_secondary_id\x18\n \x01(\t\x12\x1c\n\x14\x64\x65livery_note_number\x18\x0c \x01(\t\x12\x13\n\x0b\x64\x65livery_by\x18\r \x01(\t\x12\x31\n\rdelivery_date\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0b\x64\x65mand_date\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tis_direct\x18\x10 \x01(\x08\x12\x16\n\x0elogistics_type\x18\x11 \x01(\t\x12$\n\x08products\x18\x12 \x03(\x0b\x32\x12.receiving.Product\x12\x13\n\x0bmaster_type\x18\x13 \x01(\t\x12\x39\n\x15\x65xpected_arrival_date\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x37\n\x13\x61\x63tual_arrival_date\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tis_adjust\x18\x16 \x01(\x08\"/\n\x17\x43reateReceivingResponse\x12\x14\n\x0creceiving_id\x18\x01 \x01(\x04\"\xe5\x07\n\x14ListReceivingRequest\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x10\n\x08\x62\x61tch_id\x18\x02 \x01(\x04\x12\x13\n\x0b\x64\x65livery_id\x18\x03 \x01(\x04\x12\x12\n\nbatch_code\x18\x04 \x01(\t\x12\x15\n\rdelivery_code\x18\x05 \x01(\t\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\x12\n\norder_code\x18\x07 \x01(\t\x12\x13\n\x0breceive_bys\x18\x08 \x03(\x04\x12\x14\n\x0c\x64\x65livery_bys\x18\t \x03(\x04\x12\x0e\n\x06status\x18\n \x03(\t\x12\x16\n\x0eprocess_status\x18\x0b \x03(\t\x12.\n\nstart_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ndistr_type\x18\x0e \x01(\t\x12\x12\n\nbatch_type\x18\x0f \x03(\t\x12\r\n\x05limit\x18\x10 \x01(\x05\x12\x0e\n\x06offset\x18\x11 \x01(\x05\x12\x0c\n\x04sort\x18\x12 \x01(\t\x12\r\n\x05order\x18\x13 \x01(\t\x12\x18\n\x10main_branch_type\x18\x14 \x01(\t\x12\x37\n\x13\x64\x65livery_start_date\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x11\x64\x65livery_end_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x19\n\x11\x63ost_trans_status\x18\x17 \x01(\t\x12\x37\n\x13received_start_date\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x11received_end_date\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x18\n\x10sub_account_type\x18\x1a \x01(\t\x12\x17\n\x0fsub_receive_bys\x18\x1b \x03(\x04\x12\x18\n\x10sub_delivery_bys\x18\x1c \x03(\x04\x12\x13\n\x0bgeo_regions\x18\x1d \x03(\x04\x12\x13\n\x0b\x64\x65mand_type\x18\x1e \x01(\t\x12\x13\n\x0bproduct_ids\x18\x1f \x03(\x04\x12\x18\n\x10include_products\x18  \x01(\t\x12\x0b\n\x03ids\x18! \x03(\x04\x12\x35\n\x11\x65xpect_start_date\x18\" \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x33\n\x0f\x65xpect_end_date\x18# \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"J\n\x15ListReceivingResponse\x12\"\n\x04rows\x18\x01 \x03(\x0b\x32\x14.receiving.Receiving\x12\r\n\x05total\x18\x02 \x01(\x04\"\x8a\x03\n\x18ListReceivingColdRequest\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x11\n\tstore_ids\x18\x02 \x03(\x04\x12\x0e\n\x06status\x18\x03 \x03(\t\x12\x11\n\thas_diffs\x18\x04 \x01(\x08\x12.\n\nstart_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x07 \x01(\x05\x12\x0e\n\x06offset\x18\x08 \x01(\x05\x12\x15\n\rinclude_total\x18\t \x01(\x08\x12\x11\n\tis_direct\x18\n \x01(\x08\x12\x14\n\x0cjde_order_id\x18\x0b \x01(\x04\x12\x0c\n\x04\x63ode\x18\x0c \x01(\t\x12\x13\n\x0bgeo_regions\x18\r \x03(\x04\x12\x16\n\x0elogistics_type\x18\x0e \x01(\t\x12\x11\n\tis_adjust\x18\x0f \x01(\x08\x12\x0c\n\x04sort\x18\x10 \x01(\t\x12\r\n\x05order\x18\x11 \x01(\t\"N\n\x19ListReceivingColdResponse\x12\"\n\x04rows\x18\x01 \x03(\x0b\x32\x14.receiving.Receiving\x12\r\n\x05total\x18\x02 \x01(\x04\"%\n\x17GetReceivingByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\"C\n\x18GetReceivingByIdResponse\x12\'\n\treceiving\x18\x01 \x01(\x0b\x32\x14.receiving.Receiving\"\x7f\n\x1eGetReceivingProductByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12\x0c\n\x04sort\x18\x05 \x01(\t\x12\r\n\x05order\x18\x06 \x01(\t\"R\n\x1fGetReceivingProductByIdResponse\x12 \n\x04rows\x18\x01 \x03(\x0b\x32\x12.receiving.Product\x12\r\n\x05total\x18\x02 \x01(\x04\"\xcf\x01\n\x17\x43onfirmReceivingRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12.\n\x12\x63onfirmed_products\x18\x02 \x03(\x0b\x32\x12.receiving.Product\x12\x13\n\x0bhas_checked\x18\x03 \x01(\x08\x12\x0e\n\x06remark\x18\x04 \x01(\t\x12\x14\n\x0c\x64\x65lay_reason\x18\x05 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x06 \x01(\t\x12\x15\n\rnosign_reason\x18\x07 \x01(\t\x12\x11\n\tsignature\x18\x08 \x01(\t\"*\n\x18\x43onfirmReceivingResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"I\n\x16UpdateReceivingRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12#\n\x07product\x18\x02 \x03(\x0b\x32\x12.receiving.Product\"P\n\x17UpdateReceivingResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12%\n\x07payload\x18\x02 \x01(\x0b\x32\x14.receiving.Receiving\"\xc3\x07\n\x07Product\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x14\n\x0creceiving_id\x18\x02 \x01(\x04\x12\x13\n\x0breceived_by\x18\x03 \x01(\x04\x12\x12\n\nproduct_id\x18\x04 \x01(\x04\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x05 \x01(\x04\x12\x0f\n\x07unit_id\x18\x06 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x07 \x01(\t\x12\x14\n\x0cproduct_name\x18\x08 \x01(\t\x12\x17\n\x0fmaterial_number\x18\t \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\n \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x0b \x01(\t\x12\x11\n\tunit_name\x18\x0c \x01(\t\x12\x11\n\tunit_spec\x18\r \x01(\t\x12\x11\n\tunit_rate\x18\x0e \x01(\x01\x12\x14\n\x0cstorage_type\x18\x0f \x01(\t\x12$\n\x1crequired_accounting_quantity\x18\x10 \x01(\x01\x12\x19\n\x11required_quantity\x18\x11 \x01(\x01\x12$\n\x1creceived_accounting_quantity\x18\x12 \x01(\x01\x12\x19\n\x11received_quantity\x18\x13 \x01(\x01\x12%\n\x1d\x63onfirmed_accounting_quantity\x18\x14 \x01(\x01\x12\x1a\n\x12\x63onfirmed_quantity\x18\x15 \x01(\x01\x12\x14\n\x0cis_confirmed\x18\x16 \x01(\x08\x12.\n\ncreated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x18 \x01(\x04\x12.\n\nupdated_at\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\x1a \x01(\x04\x12\x16\n\x0ejde_product_id\x18\x1b \x01(\x04\x12/\n\x0b\x64\x65mand_date\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\npartner_id\x18\x1d \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1e \x01(\t\x12\x14\n\x0cupdated_name\x18\x1f \x01(\t\x12\x10\n\x08jde_line\x18  \x01(\x01\x12\x10\n\x08hws_line\x18! \x01(\r\x12\x19\n\x11is_first_delivery\x18\" \x01(\t\x12\r\n\x05price\x18# \x01(\x01\x12\x11\n\ttax_price\x18$ \x01(\x01\x12\x10\n\x08tax_rate\x18% \x01(\x01\x12\x11\n\tsale_type\x18\' \x01(\t\"\xbd\x08\n\tReceiving\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x11\n\tmaster_id\x18\x03 \x01(\x04\x12\x13\n\x0bmaster_code\x18\x04 \x01(\t\x12\x17\n\x0f\x64\x65mand_order_id\x18\x05 \x01(\x04\x12\x19\n\x11\x64\x65mand_order_code\x18\x06 \x01(\t\x12\x19\n\x11receiving_diff_id\x18\x07 \x01(\x04\x12\x14\n\x0cjde_order_id\x18\x08 \x01(\x04\x12\x13\n\x0breceived_by\x18\t \x01(\x04\x12\x1a\n\x12store_secondary_id\x18\n \x01(\t\x12\x0e\n\x06status\x18\x0b \x01(\t\x12\x1c\n\x14\x64\x65livery_note_number\x18\x0c \x01(\t\x12\x13\n\x0b\x64\x65livery_by\x18\r \x01(\t\x12\x31\n\rdelivery_date\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tis_direct\x18\x0f \x01(\x08\x12\x1b\n\x13\x63\x61lculate_inventory\x18\x10 \x01(\x08\x12\x14\n\x0cstorage_type\x18\x11 \x01(\t\x12\x0e\n\x06remark\x18\x12 \x01(\t\x12\x18\n\x10inventory_status\x18\x13 \x01(\t\x12\x18\n\x10inventory_req_id\x18\x14 \x01(\x04\x12\x16\n\x0eprocess_status\x18\x15 \x01(\t\x12.\n\ncreated_at\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x18 \x01(\x03\x12\x12\n\nupdated_by\x18\x19 \x01(\x03\x12/\n\x0b\x64\x65mand_date\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rreceived_type\x18\x1b \x01(\t\x12\x13\n\x0bhas_checked\x18\x1c \x01(\x08\x12\x12\n\npartner_id\x18\x1d \x01(\x04\x12\x14\n\x0c\x64\x65lay_reason\x18\x1e \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x1f \x01(\t\x12\x14\n\x0cproduct_nums\x18  \x01(\x04\x12\x14\n\x0c\x63reated_name\x18! \x01(\t\x12\x14\n\x0cupdated_name\x18\" \x01(\t\x12\x39\n\x15\x65xpected_arrival_date\x18# \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x37\n\x13\x61\x63tual_arrival_date\x18$ \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rnosign_reason\x18% \x01(\t\x12\x11\n\tsignature\x18& \x01(\t\x12\x16\n\x0elogistics_type\x18\' \x01(\t\x12\x11\n\tis_adjust\x18( \x01(\x08\x12\x11\n\tsend_type\x18) \x01(\t2\xd2\x08\n\x10ReceivingService\x12}\n\x0f\x43reateReceiving\x12!.receiving.CreateReceivingRequest\x1a\".receiving.CreateReceivingResponse\"#\x82\xd3\xe4\x93\x02\x1d\"\x18/api/v2/supply/receiving:\x01*\x12t\n\rListReceiving\x12\x1f.receiving.ListReceivingRequest\x1a .receiving.ListReceivingResponse\" \x82\xd3\xe4\x93\x02\x1a\x12\x18/api/v2/supply/receiving\x12\x85\x01\n\x11ListReceivingCold\x12#.receiving.ListReceivingColdRequest\x1a$.receiving.ListReceivingColdResponse\"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/api/v2/supply/receiving/cold\x12s\n\x10GetReceivingById\x12\".receiving.GetReceivingByIdRequest\x1a\x14.receiving.Receiving\"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/api/v2/supply/receiving/{id}\x12\x9f\x01\n\x17GetReceivingProductById\x12).receiving.GetReceivingProductByIdRequest\x1a*.receiving.GetReceivingProductByIdResponse\"-\x82\xd3\xe4\x93\x02\'\x12%/api/v2/supply/receiving/{id}/product\x12\x8d\x01\n\x10\x43onfirmReceiving\x12\".receiving.ConfirmReceivingRequest\x1a#.receiving.ConfirmReceivingResponse\"0\x82\xd3\xe4\x93\x02*\x1a%/api/v2/supply/receiving/{id}/confirm:\x01*\x12\x89\x01\n\x0fUpdateReceiving\x12!.receiving.UpdateReceivingRequest\x1a\".receiving.UpdateReceivingResponse\"/\x82\xd3\xe4\x93\x02)\x1a$/api/v2/supply/receiving/{id}/update:\x01*\x12\x8d\x01\n\x10SuspendReceiving\x12\".receiving.SuspendReceivingRequest\x1a#.receiving.SuspendReceivingResponse\"0\x82\xd3\xe4\x93\x02*\x1a%/api/v2/supply/receiving/{id}/suspend:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_SUSPENDRECEIVINGREQUEST = _descriptor.Descriptor(
  name='SuspendReceivingRequest',
  full_name='receiving.SuspendReceivingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receiving.SuspendReceivingRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='receiving.SuspendReceivingRequest.remark', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delay_reason', full_name='receiving.SuspendReceivingRequest.delay_reason', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='receiving.SuspendReceivingRequest.attachments', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='nosign_reason', full_name='receiving.SuspendReceivingRequest.nosign_reason', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='signature', full_name='receiving.SuspendReceivingRequest.signature', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=94,
  serialized_end=232,
)


_SUSPENDRECEIVINGRESPONSE = _descriptor.Descriptor(
  name='SuspendReceivingResponse',
  full_name='receiving.SuspendReceivingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='receiving.SuspendReceivingResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=234,
  serialized_end=276,
)


_CREATERECEIVINGREQUEST = _descriptor.Descriptor(
  name='CreateReceivingRequest',
  full_name='receiving.CreateReceivingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='receiving.CreateReceivingRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='receiving.CreateReceivingRequest.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='master_id', full_name='receiving.CreateReceivingRequest.master_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='master_code', full_name='receiving.CreateReceivingRequest.master_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_order_id', full_name='receiving.CreateReceivingRequest.demand_order_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_order_code', full_name='receiving.CreateReceivingRequest.demand_order_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_order_id', full_name='receiving.CreateReceivingRequest.jde_order_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='receiving.CreateReceivingRequest.storage_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='receiving.CreateReceivingRequest.received_by', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='receiving.CreateReceivingRequest.store_secondary_id', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_note_number', full_name='receiving.CreateReceivingRequest.delivery_note_number', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='receiving.CreateReceivingRequest.delivery_by', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='receiving.CreateReceivingRequest.delivery_date', index=12,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='receiving.CreateReceivingRequest.demand_date', index=13,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='receiving.CreateReceivingRequest.is_direct', index=14,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='receiving.CreateReceivingRequest.logistics_type', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='receiving.CreateReceivingRequest.products', index=16,
      number=18, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='master_type', full_name='receiving.CreateReceivingRequest.master_type', index=17,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expected_arrival_date', full_name='receiving.CreateReceivingRequest.expected_arrival_date', index=18,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actual_arrival_date', full_name='receiving.CreateReceivingRequest.actual_arrival_date', index=19,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='receiving.CreateReceivingRequest.is_adjust', index=20,
      number=22, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=279,
  serialized_end=910,
)


_CREATERECEIVINGRESPONSE = _descriptor.Descriptor(
  name='CreateReceivingResponse',
  full_name='receiving.CreateReceivingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receiving_id', full_name='receiving.CreateReceivingResponse.receiving_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=912,
  serialized_end=959,
)


_LISTRECEIVINGREQUEST = _descriptor.Descriptor(
  name='ListReceivingRequest',
  full_name='receiving.ListReceivingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='receiving.ListReceivingRequest.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='receiving.ListReceivingRequest.batch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_id', full_name='receiving.ListReceivingRequest.delivery_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_code', full_name='receiving.ListReceivingRequest.batch_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_code', full_name='receiving.ListReceivingRequest.delivery_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='receiving.ListReceivingRequest.code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='receiving.ListReceivingRequest.order_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_bys', full_name='receiving.ListReceivingRequest.receive_bys', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_bys', full_name='receiving.ListReceivingRequest.delivery_bys', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='receiving.ListReceivingRequest.status', index=9,
      number=10, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='receiving.ListReceivingRequest.process_status', index=10,
      number=11, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='receiving.ListReceivingRequest.start_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='receiving.ListReceivingRequest.end_date', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='receiving.ListReceivingRequest.distr_type', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_type', full_name='receiving.ListReceivingRequest.batch_type', index=14,
      number=15, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='receiving.ListReceivingRequest.limit', index=15,
      number=16, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='receiving.ListReceivingRequest.offset', index=16,
      number=17, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='receiving.ListReceivingRequest.sort', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='receiving.ListReceivingRequest.order', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_branch_type', full_name='receiving.ListReceivingRequest.main_branch_type', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_start_date', full_name='receiving.ListReceivingRequest.delivery_start_date', index=20,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_end_date', full_name='receiving.ListReceivingRequest.delivery_end_date', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_trans_status', full_name='receiving.ListReceivingRequest.cost_trans_status', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_start_date', full_name='receiving.ListReceivingRequest.received_start_date', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_end_date', full_name='receiving.ListReceivingRequest.received_end_date', index=24,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_type', full_name='receiving.ListReceivingRequest.sub_account_type', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_bys', full_name='receiving.ListReceivingRequest.sub_receive_bys', index=26,
      number=27, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_delivery_bys', full_name='receiving.ListReceivingRequest.sub_delivery_bys', index=27,
      number=28, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='geo_regions', full_name='receiving.ListReceivingRequest.geo_regions', index=28,
      number=29, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_type', full_name='receiving.ListReceivingRequest.demand_type', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='receiving.ListReceivingRequest.product_ids', index=30,
      number=31, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_products', full_name='receiving.ListReceivingRequest.include_products', index=31,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='receiving.ListReceivingRequest.ids', index=32,
      number=33, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_start_date', full_name='receiving.ListReceivingRequest.expect_start_date', index=33,
      number=34, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_end_date', full_name='receiving.ListReceivingRequest.expect_end_date', index=34,
      number=35, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=962,
  serialized_end=1959,
)


_LISTRECEIVINGRESPONSE = _descriptor.Descriptor(
  name='ListReceivingResponse',
  full_name='receiving.ListReceivingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='receiving.ListReceivingResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='receiving.ListReceivingResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1961,
  serialized_end=2035,
)


_LISTRECEIVINGCOLDREQUEST = _descriptor.Descriptor(
  name='ListReceivingColdRequest',
  full_name='receiving.ListReceivingColdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='receiving.ListReceivingColdRequest.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='receiving.ListReceivingColdRequest.store_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='receiving.ListReceivingColdRequest.status', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='has_diffs', full_name='receiving.ListReceivingColdRequest.has_diffs', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='receiving.ListReceivingColdRequest.start_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='receiving.ListReceivingColdRequest.end_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='receiving.ListReceivingColdRequest.limit', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='receiving.ListReceivingColdRequest.offset', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='receiving.ListReceivingColdRequest.include_total', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='receiving.ListReceivingColdRequest.is_direct', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_order_id', full_name='receiving.ListReceivingColdRequest.jde_order_id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='receiving.ListReceivingColdRequest.code', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='geo_regions', full_name='receiving.ListReceivingColdRequest.geo_regions', index=12,
      number=13, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='receiving.ListReceivingColdRequest.logistics_type', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='receiving.ListReceivingColdRequest.is_adjust', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='receiving.ListReceivingColdRequest.sort', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='receiving.ListReceivingColdRequest.order', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2038,
  serialized_end=2432,
)


_LISTRECEIVINGCOLDRESPONSE = _descriptor.Descriptor(
  name='ListReceivingColdResponse',
  full_name='receiving.ListReceivingColdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='receiving.ListReceivingColdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='receiving.ListReceivingColdResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2434,
  serialized_end=2512,
)


_GETRECEIVINGBYIDREQUEST = _descriptor.Descriptor(
  name='GetReceivingByIdRequest',
  full_name='receiving.GetReceivingByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receiving.GetReceivingByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2514,
  serialized_end=2551,
)


_GETRECEIVINGBYIDRESPONSE = _descriptor.Descriptor(
  name='GetReceivingByIdResponse',
  full_name='receiving.GetReceivingByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receiving', full_name='receiving.GetReceivingByIdResponse.receiving', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2553,
  serialized_end=2620,
)


_GETRECEIVINGPRODUCTBYIDREQUEST = _descriptor.Descriptor(
  name='GetReceivingProductByIdRequest',
  full_name='receiving.GetReceivingProductByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receiving.GetReceivingProductByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='receiving.GetReceivingProductByIdRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='receiving.GetReceivingProductByIdRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='receiving.GetReceivingProductByIdRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='receiving.GetReceivingProductByIdRequest.sort', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='receiving.GetReceivingProductByIdRequest.order', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2622,
  serialized_end=2749,
)


_GETRECEIVINGPRODUCTBYIDRESPONSE = _descriptor.Descriptor(
  name='GetReceivingProductByIdResponse',
  full_name='receiving.GetReceivingProductByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='receiving.GetReceivingProductByIdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='receiving.GetReceivingProductByIdResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2751,
  serialized_end=2833,
)


_CONFIRMRECEIVINGREQUEST = _descriptor.Descriptor(
  name='ConfirmReceivingRequest',
  full_name='receiving.ConfirmReceivingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receiving.ConfirmReceivingRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_products', full_name='receiving.ConfirmReceivingRequest.confirmed_products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='has_checked', full_name='receiving.ConfirmReceivingRequest.has_checked', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='receiving.ConfirmReceivingRequest.remark', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delay_reason', full_name='receiving.ConfirmReceivingRequest.delay_reason', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='receiving.ConfirmReceivingRequest.attachments', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='nosign_reason', full_name='receiving.ConfirmReceivingRequest.nosign_reason', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='signature', full_name='receiving.ConfirmReceivingRequest.signature', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2836,
  serialized_end=3043,
)


_CONFIRMRECEIVINGRESPONSE = _descriptor.Descriptor(
  name='ConfirmReceivingResponse',
  full_name='receiving.ConfirmReceivingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='receiving.ConfirmReceivingResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3045,
  serialized_end=3087,
)


_UPDATERECEIVINGREQUEST = _descriptor.Descriptor(
  name='UpdateReceivingRequest',
  full_name='receiving.UpdateReceivingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receiving.UpdateReceivingRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product', full_name='receiving.UpdateReceivingRequest.product', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3089,
  serialized_end=3162,
)


_UPDATERECEIVINGRESPONSE = _descriptor.Descriptor(
  name='UpdateReceivingResponse',
  full_name='receiving.UpdateReceivingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='receiving.UpdateReceivingResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payload', full_name='receiving.UpdateReceivingResponse.payload', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3164,
  serialized_end=3244,
)


_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='receiving.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receiving.Product.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_id', full_name='receiving.Product.receiving_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='receiving.Product.received_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='receiving.Product.product_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='receiving.Product.accounting_unit_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='receiving.Product.unit_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='receiving.Product.product_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='receiving.Product.product_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='receiving.Product.material_number', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='receiving.Product.accounting_unit_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='receiving.Product.accounting_unit_spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='receiving.Product.unit_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='receiving.Product.unit_spec', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='receiving.Product.unit_rate', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='receiving.Product.storage_type', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='required_accounting_quantity', full_name='receiving.Product.required_accounting_quantity', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='required_quantity', full_name='receiving.Product.required_quantity', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_accounting_quantity', full_name='receiving.Product.received_accounting_quantity', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_quantity', full_name='receiving.Product.received_quantity', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_accounting_quantity', full_name='receiving.Product.confirmed_accounting_quantity', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='receiving.Product.confirmed_quantity', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_confirmed', full_name='receiving.Product.is_confirmed', index=21,
      number=22, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='receiving.Product.created_at', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='receiving.Product.created_by', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='receiving.Product.updated_at', index=24,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='receiving.Product.updated_by', index=25,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_product_id', full_name='receiving.Product.jde_product_id', index=26,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='receiving.Product.demand_date', index=27,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='receiving.Product.partner_id', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='receiving.Product.created_name', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='receiving.Product.updated_name', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_line', full_name='receiving.Product.jde_line', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hws_line', full_name='receiving.Product.hws_line', index=32,
      number=33, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_first_delivery', full_name='receiving.Product.is_first_delivery', index=33,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='receiving.Product.price', index=34,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='receiving.Product.tax_price', index=35,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='receiving.Product.tax_rate', index=36,
      number=37, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sale_type', full_name='receiving.Product.sale_type', index=37,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3247,
  serialized_end=4210,
)


_RECEIVING = _descriptor.Descriptor(
  name='Receiving',
  full_name='receiving.Receiving',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='receiving.Receiving.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='receiving.Receiving.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='master_id', full_name='receiving.Receiving.master_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='master_code', full_name='receiving.Receiving.master_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_order_id', full_name='receiving.Receiving.demand_order_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_order_code', full_name='receiving.Receiving.demand_order_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_diff_id', full_name='receiving.Receiving.receiving_diff_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_order_id', full_name='receiving.Receiving.jde_order_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='receiving.Receiving.received_by', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='receiving.Receiving.store_secondary_id', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='receiving.Receiving.status', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_note_number', full_name='receiving.Receiving.delivery_note_number', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='receiving.Receiving.delivery_by', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='receiving.Receiving.delivery_date', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='receiving.Receiving.is_direct', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_inventory', full_name='receiving.Receiving.calculate_inventory', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='receiving.Receiving.storage_type', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='receiving.Receiving.remark', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_status', full_name='receiving.Receiving.inventory_status', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_req_id', full_name='receiving.Receiving.inventory_req_id', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='receiving.Receiving.process_status', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='receiving.Receiving.created_at', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='receiving.Receiving.updated_at', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='receiving.Receiving.created_by', index=23,
      number=24, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='receiving.Receiving.updated_by', index=24,
      number=25, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='receiving.Receiving.demand_date', index=25,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_type', full_name='receiving.Receiving.received_type', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='has_checked', full_name='receiving.Receiving.has_checked', index=27,
      number=28, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='receiving.Receiving.partner_id', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delay_reason', full_name='receiving.Receiving.delay_reason', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='receiving.Receiving.attachments', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_nums', full_name='receiving.Receiving.product_nums', index=31,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='receiving.Receiving.created_name', index=32,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='receiving.Receiving.updated_name', index=33,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expected_arrival_date', full_name='receiving.Receiving.expected_arrival_date', index=34,
      number=35, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actual_arrival_date', full_name='receiving.Receiving.actual_arrival_date', index=35,
      number=36, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='nosign_reason', full_name='receiving.Receiving.nosign_reason', index=36,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='signature', full_name='receiving.Receiving.signature', index=37,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='receiving.Receiving.logistics_type', index=38,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='receiving.Receiving.is_adjust', index=39,
      number=40, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='send_type', full_name='receiving.Receiving.send_type', index=40,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4213,
  serialized_end=5298,
)

_CREATERECEIVINGREQUEST.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATERECEIVINGREQUEST.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATERECEIVINGREQUEST.fields_by_name['products'].message_type = _PRODUCT
_CREATERECEIVINGREQUEST.fields_by_name['expected_arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATERECEIVINGREQUEST.fields_by_name['actual_arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGREQUEST.fields_by_name['delivery_start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGREQUEST.fields_by_name['delivery_end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGREQUEST.fields_by_name['received_start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGREQUEST.fields_by_name['received_end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGREQUEST.fields_by_name['expect_start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGREQUEST.fields_by_name['expect_end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGRESPONSE.fields_by_name['rows'].message_type = _RECEIVING
_LISTRECEIVINGCOLDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGCOLDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGCOLDRESPONSE.fields_by_name['rows'].message_type = _RECEIVING
_GETRECEIVINGBYIDRESPONSE.fields_by_name['receiving'].message_type = _RECEIVING
_GETRECEIVINGPRODUCTBYIDRESPONSE.fields_by_name['rows'].message_type = _PRODUCT
_CONFIRMRECEIVINGREQUEST.fields_by_name['confirmed_products'].message_type = _PRODUCT
_UPDATERECEIVINGREQUEST.fields_by_name['product'].message_type = _PRODUCT
_UPDATERECEIVINGRESPONSE.fields_by_name['payload'].message_type = _RECEIVING
_PRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCT.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVING.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVING.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVING.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVING.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVING.fields_by_name['expected_arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVING.fields_by_name['actual_arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['SuspendReceivingRequest'] = _SUSPENDRECEIVINGREQUEST
DESCRIPTOR.message_types_by_name['SuspendReceivingResponse'] = _SUSPENDRECEIVINGRESPONSE
DESCRIPTOR.message_types_by_name['CreateReceivingRequest'] = _CREATERECEIVINGREQUEST
DESCRIPTOR.message_types_by_name['CreateReceivingResponse'] = _CREATERECEIVINGRESPONSE
DESCRIPTOR.message_types_by_name['ListReceivingRequest'] = _LISTRECEIVINGREQUEST
DESCRIPTOR.message_types_by_name['ListReceivingResponse'] = _LISTRECEIVINGRESPONSE
DESCRIPTOR.message_types_by_name['ListReceivingColdRequest'] = _LISTRECEIVINGCOLDREQUEST
DESCRIPTOR.message_types_by_name['ListReceivingColdResponse'] = _LISTRECEIVINGCOLDRESPONSE
DESCRIPTOR.message_types_by_name['GetReceivingByIdRequest'] = _GETRECEIVINGBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetReceivingByIdResponse'] = _GETRECEIVINGBYIDRESPONSE
DESCRIPTOR.message_types_by_name['GetReceivingProductByIdRequest'] = _GETRECEIVINGPRODUCTBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetReceivingProductByIdResponse'] = _GETRECEIVINGPRODUCTBYIDRESPONSE
DESCRIPTOR.message_types_by_name['ConfirmReceivingRequest'] = _CONFIRMRECEIVINGREQUEST
DESCRIPTOR.message_types_by_name['ConfirmReceivingResponse'] = _CONFIRMRECEIVINGRESPONSE
DESCRIPTOR.message_types_by_name['UpdateReceivingRequest'] = _UPDATERECEIVINGREQUEST
DESCRIPTOR.message_types_by_name['UpdateReceivingResponse'] = _UPDATERECEIVINGRESPONSE
DESCRIPTOR.message_types_by_name['Product'] = _PRODUCT
DESCRIPTOR.message_types_by_name['Receiving'] = _RECEIVING
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

SuspendReceivingRequest = _reflection.GeneratedProtocolMessageType('SuspendReceivingRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUSPENDRECEIVINGREQUEST,
  __module__ = 'receiving_pb2'
  # @@protoc_insertion_point(class_scope:receiving.SuspendReceivingRequest)
  ))
_sym_db.RegisterMessage(SuspendReceivingRequest)

SuspendReceivingResponse = _reflection.GeneratedProtocolMessageType('SuspendReceivingResponse', (_message.Message,), dict(
  DESCRIPTOR = _SUSPENDRECEIVINGRESPONSE,
  __module__ = 'receiving_pb2'
  # @@protoc_insertion_point(class_scope:receiving.SuspendReceivingResponse)
  ))
_sym_db.RegisterMessage(SuspendReceivingResponse)

CreateReceivingRequest = _reflection.GeneratedProtocolMessageType('CreateReceivingRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATERECEIVINGREQUEST,
  __module__ = 'receiving_pb2'
  # @@protoc_insertion_point(class_scope:receiving.CreateReceivingRequest)
  ))
_sym_db.RegisterMessage(CreateReceivingRequest)

CreateReceivingResponse = _reflection.GeneratedProtocolMessageType('CreateReceivingResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATERECEIVINGRESPONSE,
  __module__ = 'receiving_pb2'
  # @@protoc_insertion_point(class_scope:receiving.CreateReceivingResponse)
  ))
_sym_db.RegisterMessage(CreateReceivingResponse)

ListReceivingRequest = _reflection.GeneratedProtocolMessageType('ListReceivingRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTRECEIVINGREQUEST,
  __module__ = 'receiving_pb2'
  # @@protoc_insertion_point(class_scope:receiving.ListReceivingRequest)
  ))
_sym_db.RegisterMessage(ListReceivingRequest)

ListReceivingResponse = _reflection.GeneratedProtocolMessageType('ListReceivingResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTRECEIVINGRESPONSE,
  __module__ = 'receiving_pb2'
  # @@protoc_insertion_point(class_scope:receiving.ListReceivingResponse)
  ))
_sym_db.RegisterMessage(ListReceivingResponse)

ListReceivingColdRequest = _reflection.GeneratedProtocolMessageType('ListReceivingColdRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTRECEIVINGCOLDREQUEST,
  __module__ = 'receiving_pb2'
  # @@protoc_insertion_point(class_scope:receiving.ListReceivingColdRequest)
  ))
_sym_db.RegisterMessage(ListReceivingColdRequest)

ListReceivingColdResponse = _reflection.GeneratedProtocolMessageType('ListReceivingColdResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTRECEIVINGCOLDRESPONSE,
  __module__ = 'receiving_pb2'
  # @@protoc_insertion_point(class_scope:receiving.ListReceivingColdResponse)
  ))
_sym_db.RegisterMessage(ListReceivingColdResponse)

GetReceivingByIdRequest = _reflection.GeneratedProtocolMessageType('GetReceivingByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGBYIDREQUEST,
  __module__ = 'receiving_pb2'
  # @@protoc_insertion_point(class_scope:receiving.GetReceivingByIdRequest)
  ))
_sym_db.RegisterMessage(GetReceivingByIdRequest)

GetReceivingByIdResponse = _reflection.GeneratedProtocolMessageType('GetReceivingByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGBYIDRESPONSE,
  __module__ = 'receiving_pb2'
  # @@protoc_insertion_point(class_scope:receiving.GetReceivingByIdResponse)
  ))
_sym_db.RegisterMessage(GetReceivingByIdResponse)

GetReceivingProductByIdRequest = _reflection.GeneratedProtocolMessageType('GetReceivingProductByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGPRODUCTBYIDREQUEST,
  __module__ = 'receiving_pb2'
  # @@protoc_insertion_point(class_scope:receiving.GetReceivingProductByIdRequest)
  ))
_sym_db.RegisterMessage(GetReceivingProductByIdRequest)

GetReceivingProductByIdResponse = _reflection.GeneratedProtocolMessageType('GetReceivingProductByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGPRODUCTBYIDRESPONSE,
  __module__ = 'receiving_pb2'
  # @@protoc_insertion_point(class_scope:receiving.GetReceivingProductByIdResponse)
  ))
_sym_db.RegisterMessage(GetReceivingProductByIdResponse)

ConfirmReceivingRequest = _reflection.GeneratedProtocolMessageType('ConfirmReceivingRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMRECEIVINGREQUEST,
  __module__ = 'receiving_pb2'
  # @@protoc_insertion_point(class_scope:receiving.ConfirmReceivingRequest)
  ))
_sym_db.RegisterMessage(ConfirmReceivingRequest)

ConfirmReceivingResponse = _reflection.GeneratedProtocolMessageType('ConfirmReceivingResponse', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMRECEIVINGRESPONSE,
  __module__ = 'receiving_pb2'
  # @@protoc_insertion_point(class_scope:receiving.ConfirmReceivingResponse)
  ))
_sym_db.RegisterMessage(ConfirmReceivingResponse)

UpdateReceivingRequest = _reflection.GeneratedProtocolMessageType('UpdateReceivingRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERECEIVINGREQUEST,
  __module__ = 'receiving_pb2'
  # @@protoc_insertion_point(class_scope:receiving.UpdateReceivingRequest)
  ))
_sym_db.RegisterMessage(UpdateReceivingRequest)

UpdateReceivingResponse = _reflection.GeneratedProtocolMessageType('UpdateReceivingResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERECEIVINGRESPONSE,
  __module__ = 'receiving_pb2'
  # @@protoc_insertion_point(class_scope:receiving.UpdateReceivingResponse)
  ))
_sym_db.RegisterMessage(UpdateReceivingResponse)

Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCT,
  __module__ = 'receiving_pb2'
  # @@protoc_insertion_point(class_scope:receiving.Product)
  ))
_sym_db.RegisterMessage(Product)

Receiving = _reflection.GeneratedProtocolMessageType('Receiving', (_message.Message,), dict(
  DESCRIPTOR = _RECEIVING,
  __module__ = 'receiving_pb2'
  # @@protoc_insertion_point(class_scope:receiving.Receiving)
  ))
_sym_db.RegisterMessage(Receiving)



_RECEIVINGSERVICE = _descriptor.ServiceDescriptor(
  name='ReceivingService',
  full_name='receiving.ReceivingService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=5301,
  serialized_end=6407,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateReceiving',
    full_name='receiving.ReceivingService.CreateReceiving',
    index=0,
    containing_service=None,
    input_type=_CREATERECEIVINGREQUEST,
    output_type=_CREATERECEIVINGRESPONSE,
    serialized_options=_b('\202\323\344\223\002\035\"\030/api/v2/supply/receiving:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListReceiving',
    full_name='receiving.ReceivingService.ListReceiving',
    index=1,
    containing_service=None,
    input_type=_LISTRECEIVINGREQUEST,
    output_type=_LISTRECEIVINGRESPONSE,
    serialized_options=_b('\202\323\344\223\002\032\022\030/api/v2/supply/receiving'),
  ),
  _descriptor.MethodDescriptor(
    name='ListReceivingCold',
    full_name='receiving.ReceivingService.ListReceivingCold',
    index=2,
    containing_service=None,
    input_type=_LISTRECEIVINGCOLDREQUEST,
    output_type=_LISTRECEIVINGCOLDRESPONSE,
    serialized_options=_b('\202\323\344\223\002\037\022\035/api/v2/supply/receiving/cold'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReceivingById',
    full_name='receiving.ReceivingService.GetReceivingById',
    index=3,
    containing_service=None,
    input_type=_GETRECEIVINGBYIDREQUEST,
    output_type=_RECEIVING,
    serialized_options=_b('\202\323\344\223\002\037\022\035/api/v2/supply/receiving/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReceivingProductById',
    full_name='receiving.ReceivingService.GetReceivingProductById',
    index=4,
    containing_service=None,
    input_type=_GETRECEIVINGPRODUCTBYIDREQUEST,
    output_type=_GETRECEIVINGPRODUCTBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\022%/api/v2/supply/receiving/{id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmReceiving',
    full_name='receiving.ReceivingService.ConfirmReceiving',
    index=5,
    containing_service=None,
    input_type=_CONFIRMRECEIVINGREQUEST,
    output_type=_CONFIRMRECEIVINGRESPONSE,
    serialized_options=_b('\202\323\344\223\002*\032%/api/v2/supply/receiving/{id}/confirm:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateReceiving',
    full_name='receiving.ReceivingService.UpdateReceiving',
    index=6,
    containing_service=None,
    input_type=_UPDATERECEIVINGREQUEST,
    output_type=_UPDATERECEIVINGRESPONSE,
    serialized_options=_b('\202\323\344\223\002)\032$/api/v2/supply/receiving/{id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='SuspendReceiving',
    full_name='receiving.ReceivingService.SuspendReceiving',
    index=7,
    containing_service=None,
    input_type=_SUSPENDRECEIVINGREQUEST,
    output_type=_SUSPENDRECEIVINGRESPONSE,
    serialized_options=_b('\202\323\344\223\002*\032%/api/v2/supply/receiving/{id}/suspend:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_RECEIVINGSERVICE)

DESCRIPTOR.services_by_name['ReceivingService'] = _RECEIVINGSERVICE

# @@protoc_insertion_point(module_scope)
