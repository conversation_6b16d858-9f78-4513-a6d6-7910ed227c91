syntax = "proto3";
package attachments;
import "google/api/annotations.proto";
// import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

// 附件管理相关服务
service AttachmentsService{
    rpc CreateAttachments(NewAttachmentsList) returns (Response){
        option (google.api.http) = {
            post:"/api/v2/supply/attachments"
            body:"*"
        };
    }

    rpc GetAttachmentsByDocId(DocIdRequest) returns (AttachmentsList){
        option (google.api.http) = {
            get:"/api/v2/supply/attachments/{doc_type}/{doc_id}"
        };
    }

    rpc UpdateAttachmentsByDocIdMobile(NewAttachmentsList) returns (Response){
        option (google.api.http) = {
            post:"/api/v2/supply/attachments/{doc_type}/{doc_id}"
            body:"*"
        };
    }

    rpc UpdateAttachmentsByDocId(NewAttachmentsList) returns (Response){
        option (google.api.http) = {
            put:"/api/v2/supply/attachments/{doc_type}/{doc_id}"
            body:"*"
        };
    }

}

message Response{
    uint64 id = 1;
    string status = 2;
    string msg = 3;
}

message NewAttachmentsList{
    // 
    uint64 doc_id = 1;
    string doc_type = 2;
    repeated string attachments = 3;
    string signature = 4;
    string nosign_reason = 5;
}

message AttachmentsList{
    // 
    uint64 doc_id = 1;
    string doc_type = 2;
    repeated string attachments = 3;
    string signature = 4;
    string nosign_reason = 5;

    // 创建时间
    google.protobuf.Timestamp created_at = 11;
    // 创建人id
    uint64 created_by= 12;
    // 更新日期
    google.protobuf.Timestamp updated_at = 13;
    // 更新人id
    uint64 updated_by = 14;
    // 商户id
    uint64 partner_id = 15;
}

message DocIdRequest{
    uint64 doc_id = 1;
    string doc_type = 2;
}

