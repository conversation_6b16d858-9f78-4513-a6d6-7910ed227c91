{"swagger": "2.0", "info": {"title": "reduction_augmentation_demand.proto", "version": "version not set"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/reduction_augmentation_demand": {"get": {"summary": "减配货列表页面", "operationId": "ListReductionAugmentationDemand", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/reduction_augmentation_demandListReductionAugmentationDemandResponse"}}}, "parameters": [{"name": "status", "description": "暂时不支持模糊搜索.", "in": "query", "required": false, "type": "string"}, {"name": "company", "in": "query", "required": false, "type": "string"}, {"name": "allocation_type", "in": "query", "required": false, "type": "string"}, {"name": "name", "in": "query", "required": false, "type": "string"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int32"}], "tags": ["ReductionAugmentationDemand"]}, "post": {"summary": "创建减配货", "operationId": "CreateReductionAugmentationDemand", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/reduction_augmentation_demandCreateReductionAugmentationDemandResponse"}}}, "tags": ["ReductionAugmentationDemand"]}}, "/api/v2/supply/reduction_augmentation_demand/calculate": {"post": {"summary": "计算商品分配", "operationId": "CalculateAllocation", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/reduction_augmentation_demandCalculateAllocationResponse"}}}, "tags": ["ReductionAugmentationDemand"]}}, "/api/v2/supply/reduction_augmentation_demand/calculate/{rule_id}": {"get": {"summary": "获取计算商品分配列表", "operationId": "ListCalculateAllocationResult", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/reduction_augmentation_demandListCalculateAllocationResultResponse"}}}, "parameters": [{"name": "rule_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int32"}], "tags": ["ReductionAugmentationDemand"]}, "delete": {"summary": "// 获取计算商品分配的门店\n   rpc ListCalculateAllocationStore (ListCalculateAllocationStoreRequest) returns (ListCalculateAllocationStoreResponse) {\n       option (google.api.http) = {\n       get: \"/api/v2/supply/reduction_augmentation_demand/calculate/store/{rule_id}\"\n       };\n   };\n删除计算商品分配", "operationId": "DestroyCalculateAllocation", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/reduction_augmentation_demandDestroyCalculateAllocationResponse"}}}, "parameters": [{"name": "rule_id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["ReductionAugmentationDemand"]}}, "/api/v2/supply/reduction_augmentation_demand/excluded/store": {"get": {"summary": "列表排除门店", "operationId": "ListReductionAugmentationDemandExcludedStore", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/reduction_augmentation_demandListReductionAugmentationDemandExcludedStoreResponse"}}}, "parameters": [{"name": "allocation_type", "description": "分配类型 INCREASE: 增加， REDUCE: 减少.", "in": "query", "required": false, "type": "string"}, {"name": "store_id", "description": "名字.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int32"}], "tags": ["ReductionAugmentationDemand"]}, "post": {"summary": "新建排除门店", "operationId": "CreateReductionAugmentationDemandExcludedStore", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/reduction_augmentation_demandCreateReductionAugmentationDemandExcludedStoreResponse"}}}, "tags": ["ReductionAugmentationDemand"]}}, "/api/v2/supply/reduction_augmentation_demand/excluded/store/{id}": {"delete": {"summary": "按id删除排除门店", "operationId": "DestroyReductionAugmentationDemandExcludedStore", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/reduction_augmentation_demandDestroyReductionAugmentationDemandExcludedStoreResponse"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["ReductionAugmentationDemand"]}}, "/api/v2/supply/reduction_augmentation_demand/product": {"get": {"summary": "按规则id查询商品列表", "operationId": "ListReductionAugmentationDemandProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/reduction_augmentation_demandListReductionAugmentationDemandProductResponse"}}}, "parameters": [{"name": "rule_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int32"}], "tags": ["ReductionAugmentationDemand"]}}, "/api/v2/supply/reduction_augmentation_demand/product/agg": {"get": {"summary": "按", "operationId": "ReductionAugmentationDemandProductAgg", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/reduction_augmentation_demandReductionAugmentationDemandProductAggResponse"}}}, "parameters": [{"name": "rule_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int32"}], "tags": ["ReductionAugmentationDemand"]}}, "/api/v2/supply/reduction_augmentation_demand/store": {"get": {"summary": "按规则id查询门店列表", "operationId": "ListReductionAugmentationDemandStore", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/reduction_augmentation_demandListReductionAugmentationDemandStoreResponse"}}}, "parameters": [{"name": "rule_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int32"}], "tags": ["ReductionAugmentationDemand"]}}, "/api/v2/supply/reduction_augmentation_demand/sum": {"post": {"summary": "聚合商品数量（分配商品预估与门店订货明细处, 商品id与门店id不能都多传，必须有一个为单一id）", "operationId": "SumDemandQuantity", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/reduction_augmentation_demandSumDemandQuantityResponse"}}}, "tags": ["ReductionAugmentationDemand"]}}, "/api/v2/supply/reduction_augmentation_demand/with/id/{id}": {"get": {"summary": "减配货详情", "operationId": "RetrieveReductionAugmentationDemand", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/reduction_augmentation_demandRetrieveReductionAugmentationDemandResponse"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["ReductionAugmentationDemand"]}}, "/api/v2/supply/reduction_augmentation_demand/{id}": {"patch": {"summary": "更新减配货", "operationId": "UpdateReductionAugmentationDemand", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/reduction_augmentation_demandUpdateReductionAugmentationDemandResponse"}}}, "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["ReductionAugmentationDemand"]}}}, "definitions": {"CalculateAllocationResponseRemainProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "remain_quantity": {"type": "number", "format": "double"}}}, "ListCalculateAllocationResultResponseAllocationDetail": {"type": "object", "properties": {"product_code": {"type": "string"}, "product_name": {"type": "string"}, "store_code": {"type": "string"}, "store_name": {"type": "string"}, "demand_quantity": {"type": "number", "format": "double"}, "allocation_quantity": {"type": "number", "format": "double"}, "final_quantity": {"type": "number", "format": "double"}, "order_type": {"type": "string"}, "order_source": {"type": "string"}, "order_code": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "store_id": {"type": "string", "format": "uint64"}}}, "ListReductionAugmentationDemandProductResponseAllocationProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "allocation_quantity": {"type": "number", "format": "double"}, "remain_quantity": {"type": "number", "format": "double"}, "rate": {"type": "number", "format": "double"}}}, "ListReductionAugmentationDemandResponseDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "status": {"type": "string"}, "company": {"type": "string", "format": "uint64"}, "allocation_type": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}, "start_demand_date": {"type": "string"}, "end_demand_date": {"type": "string"}, "rate": {"type": "number", "format": "double"}, "updated_at": {"type": "string"}}}, "ListReductionAugmentationDemandStoreResponseAllocationStore": {"type": "object", "properties": {"store_id": {"type": "string", "format": "uint64"}, "store_code": {"type": "string"}, "store_name": {"type": "string"}}}, "SumDemandQuantityResponseSumDemandProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}}}, "reduction_augmentation_demandCalculateAllocationRequestProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "allocation_quantity": {"type": "number", "format": "double"}, "rate": {"type": "number", "format": "double"}}}, "reduction_augmentation_demandCalculateAllocationResponse": {"type": "object", "properties": {"remain_products": {"type": "array", "items": {"$ref": "#/definitions/CalculateAllocationResponseRemainProduct"}}, "allocation_result": {"type": "array", "items": {"$ref": "#/definitions/reduction_augmentation_demandCalculateAllocationResponseAllocationResult"}}, "rule_id": {"type": "string", "format": "uint64"}}}, "reduction_augmentation_demandCalculateAllocationResponseAllocationResult": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "store_count": {"type": "integer", "format": "int32"}, "allocation_quantity": {"type": "number", "format": "double"}}}, "reduction_augmentation_demandCreateReductionAugmentationDemandExcludedStoreRequestStore": {"type": "object", "properties": {"store_id": {"type": "string", "format": "uint64", "title": "门店id"}, "store_code": {"type": "string", "title": "门店编号"}}}, "reduction_augmentation_demandCreateReductionAugmentationDemandExcludedStoreResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean", "title": "id"}}}, "reduction_augmentation_demandCreateReductionAugmentationDemandRequestProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "allocation_quantity": {"type": "number", "format": "double"}, "rate": {"type": "number", "format": "double"}, "remain_quantity": {"type": "number", "format": "double"}}}, "reduction_augmentation_demandCreateReductionAugmentationDemandResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}}}, "reduction_augmentation_demandDestroyCalculateAllocationResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "reduction_augmentation_demandDestroyReductionAugmentationDemandExcludedStoreResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "reduction_augmentation_demandListCalculateAllocationResultResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ListCalculateAllocationResultResponseAllocationDetail"}}, "count": {"type": "integer", "format": "int32"}}}, "reduction_augmentation_demandListReductionAugmentationDemandExcludedStoreResponse": {"type": "object", "properties": {"stores": {"type": "array", "items": {"$ref": "#/definitions/reduction_augmentation_demandListReductionAugmentationDemandExcludedStoreResponseStore"}}, "count": {"type": "integer", "format": "int32", "title": "总条数"}}}, "reduction_augmentation_demandListReductionAugmentationDemandExcludedStoreResponseStore": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "store_id": {"type": "string", "format": "uint64", "title": "门店id"}, "store_code": {"type": "string", "title": "门店编号"}, "store_name": {"type": "string", "title": "门店名称"}, "allocation_type": {"type": "string", "title": "分配类型 INCREASE: 增加， REDUCE: 减少"}}}, "reduction_augmentation_demandListReductionAugmentationDemandProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ListReductionAugmentationDemandProductResponseAllocationProduct"}}, "count": {"type": "integer", "format": "int32"}}}, "reduction_augmentation_demandListReductionAugmentationDemandResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ListReductionAugmentationDemandResponseDetail"}}, "count": {"type": "integer", "format": "int32"}}}, "reduction_augmentation_demandListReductionAugmentationDemandStoreResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/ListReductionAugmentationDemandStoreResponseAllocationStore"}}, "count": {"type": "integer", "format": "int32"}}}, "reduction_augmentation_demandReductionAugmentationDemandProductAggResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/reduction_augmentation_demandReductionAugmentationDemandProductAggResponseAllocationResult"}}, "count": {"type": "integer", "format": "int32"}}}, "reduction_augmentation_demandReductionAugmentationDemandProductAggResponseAllocationResult": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "store_count": {"type": "integer", "format": "int32"}, "allocation_quantity": {"type": "number", "format": "double"}}}, "reduction_augmentation_demandRetrieveReductionAugmentationDemandResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "status": {"type": "string"}, "company": {"type": "string", "format": "uint64"}, "allocation_type": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}, "start_demand_date": {"type": "string"}, "end_demand_date": {"type": "string"}, "rate": {"type": "number", "format": "double"}, "updated_at": {"type": "string"}, "order_by_demand_day": {"type": "integer", "format": "int32", "title": "double lower_limit = 11; // 分配下限或下限"}, "quantity_per_time": {"type": "string"}, "allow_reduce_to_zero": {"type": "boolean", "format": "boolean"}, "order_types": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "store_sum": {"type": "integer", "format": "int32"}, "product_sum": {"type": "integer", "format": "int32"}, "allocation_quantity": {"type": "number", "format": "double"}}}, "reduction_augmentation_demandSumDemandQuantityResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/SumDemandQuantityResponseSumDemandProduct"}}, "count": {"type": "integer", "format": "int32"}}}, "reduction_augmentation_demandUpdateReductionAugmentationDemandRequestProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "allocation_quantity": {"type": "number", "format": "double"}, "rate": {"type": "number", "format": "double"}, "remain_quantity": {"type": "number", "format": "double"}}}, "reduction_augmentation_demandUpdateReductionAugmentationDemandResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}}}