# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from manufactory_management import inventory_adjust_pb2 as manufactory__management_dot_inventory__adjust__pb2


class ManufactoryInventoryAdjustServiceStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetDemandAdjustProductByBranchId = channel.unary_unary(
        '/manufactory_management.ManufactoryInventoryAdjustService/GetDemandAdjustProductByBranchId',
        request_serializer=manufactory__management_dot_inventory__adjust__pb2.GetDemandAdjustProductByBranchIdRequest.SerializeToString,
        response_deserializer=manufactory__management_dot_inventory__adjust__pb2.GetDemandAdjustProductByBranchIdResponse.FromString,
        )
    self.CreateDemandAdjust = channel.unary_unary(
        '/manufactory_management.ManufactoryInventoryAdjustService/CreateDemandAdjust',
        request_serializer=manufactory__management_dot_inventory__adjust__pb2.CreateDemandAdjustRequest.SerializeToString,
        response_deserializer=manufactory__management_dot_inventory__adjust__pb2.Response.FromString,
        )
    self.DealDemandAdjustById = channel.unary_unary(
        '/manufactory_management.ManufactoryInventoryAdjustService/DealDemandAdjustById',
        request_serializer=manufactory__management_dot_inventory__adjust__pb2.DealDemandAdjustByIdRequest.SerializeToString,
        response_deserializer=manufactory__management_dot_inventory__adjust__pb2.Response.FromString,
        )


class ManufactoryInventoryAdjustServiceServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def GetDemandAdjustProductByBranchId(self, request, context):
    """根据仓库id查询可调整订货商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateDemandAdjust(self, request, context):
    """创建订货调整单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DealDemandAdjustById(self, request, context):
    """修改订单状态统一入口
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_ManufactoryInventoryAdjustServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetDemandAdjustProductByBranchId': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandAdjustProductByBranchId,
          request_deserializer=manufactory__management_dot_inventory__adjust__pb2.GetDemandAdjustProductByBranchIdRequest.FromString,
          response_serializer=manufactory__management_dot_inventory__adjust__pb2.GetDemandAdjustProductByBranchIdResponse.SerializeToString,
      ),
      'CreateDemandAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.CreateDemandAdjust,
          request_deserializer=manufactory__management_dot_inventory__adjust__pb2.CreateDemandAdjustRequest.FromString,
          response_serializer=manufactory__management_dot_inventory__adjust__pb2.Response.SerializeToString,
      ),
      'DealDemandAdjustById': grpc.unary_unary_rpc_method_handler(
          servicer.DealDemandAdjustById,
          request_deserializer=manufactory__management_dot_inventory__adjust__pb2.DealDemandAdjustByIdRequest.FromString,
          response_serializer=manufactory__management_dot_inventory__adjust__pb2.Response.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'manufactory_management.ManufactoryInventoryAdjustService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
