syntax = "proto3";
package franchisee_demand;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// 加盟商订货 -- 加盟商总部入口
service FranchiseeDemandService {
    // 批量创建订单(总部分配导入使用)
    rpc CreateFDemandBatch (CreateFDemandBatchRequest) returns (CreateFDemandBatchResponse) {
        option (google.api.http) = {
            post: "/api/v2/supply/frs-management/demand/create/batch"
            body: "*"
        };
    }
    // 查询订货单列表
    rpc ListFDemand (ListFDemandRequest) returns (ListFDemandResponse) {
        option (google.api.http) = {
            get:"/api/v2/supply/frs-management/frs-demand"
        };
    }
    // 根据id查询单个订货单
    rpc GetFDemandById (GetFDemandByIdRequest) returns (Demand) {
        option (google.api.http) = {
            get:"/api/v2/supply/frs-management/frs-demand/{demand_id}"
        };
    }
    // 根据id查询单据历史记录
    rpc GetHistoryById (GetHistoryByIdRequest) returns (HistoryResponse) {
        option (google.api.http) = {
            get:"/api/v2/supply/frs-management/frs-demand/{demand_id}/history"
        };
    }
    // 根据id查询单据内的商品列表
    rpc GetProductsById (GetProductsByIdRequest) returns (ProductsResponse) {
        option (google.api.http) = {
            get:"/api/v2/supply/frs-management/frs-demand/{demand_id}/product"
        };
    }
    // 更新订货单商品
    rpc UpdateProducts (UpdateProductRequest) returns (UpdateProductsResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/frs-management/frs-demand/{demand_id}/product"
            body:"*"
        };
    }
    // 修改订单状态统一入口
    rpc DealFDemandById (DealFDemandByIdRequest) returns (DealFDemandByIdResponse) {
        option (google.api.http) = {
            put: "/api/v2/supply/frs-management/frs-demand/deal/{action}"
            body: "*"
        };
    }
    // 获取订货商品可分配仓库(包含实时库存)
    rpc GetProductDistribution (GetProductDistributionRequest) returns (GetProductDistributionResponse) {
        option (google.api.http) = {
            get: "/api/v2/supply/frs-management/product/distribution"
        };
    }
    // 订单新增商品
    rpc AddFDemandProduct (AddFDemandProductRequest) returns (AddFDemandProductResponse) {
        option (google.api.http) = {
            post: "/api/v2/supply/frs-management/frs-demand/add/product"
            body: "*"
        };
    }
    // 导入订货单(改单)
    rpc ImportFDemandProducts (ImportFDemandProductsRequest) returns (ImportFDemandProductsResponse) {
        option (google.api.http) = {
            post: "/api/v2/supply/frs-management/frs-demand/import/products"
            body: "*"
        };
    }

    // 批量还原订单(批量还原订单导入数量)
    rpc BatchResetFDemand (BatchDealFDemandRequest) returns (BatchDealFDemandResponse) {
        option (google.api.http) = {
            put: "/api/v2/supply/frs-management/frs-demand/batch/reset"
            body: "*"
        };
    }

    //门店未订货提醒列表（WEB端）
    rpc ListFDemandToRemind (ListFDemandToRemindRequest) returns (ListFDemandToRemindResponse) {
        option (google.api.http) = {
            post:"/api/v2/supply/frs-management/franchisee/demand/remind"
        };
    }
    // 按条件批量确认订单
    rpc BatchConfirmFDemand (BatchDealFDemandRequest) returns (BatchDealFDemandResponse) {
        option (google.api.http) = {
            put: "/api/v2/supply/frs-management/frs-demand/batch/confirm"
            body: "*"
        };
    }
    // 检查订货待办能否订货
    rpc CheckFDemandTodo (CheckFDemandTodoRequest) returns (CheckFDemandTodoResponse) {
        option (google.api.http) = {
            get: "/api/v2/supply/frs-management/frs-demand/{demand_id}/check"
        };
    }
}

// 订单状态枚举
enum Status {
    INITED = 0;      // 新建(总部)
    PREPARE = 1;     // 待付款(门店)-已提交(总部)
    P_SUBMIT = 2;    // 付款提交凭证-> 付款待确认(门店/仓库)
    SUBMITTED = 4;   // 确认收款 -> 待审核(门店/仓库)
    R_APPROVE = 3;   // 区经审核 -> 已审核/待确认(门店/仓库)
    REJECTED = 5;    // 已驳回(门店/仓库)
    CANCELLED = 6;   // 已取消(门店)
    CONFIRMED = 7;   // 确认订单->已确认(门店)-待分配(仓库)
    APPROVING = 10;   // 确认分配->已确认(门店)-部分分配(仓库)
    APPROVED = 11;    // 确认分配->已确认(门店)-完全分配(仓库)
}

// 订货单类型枚举
enum Type {
    FSD = 0; // 加盟商订货单
    FMD = 1; // 加盟门店主配单
}

message CreateFDemandBatchRequest {
    // 唯一请求ID 批量创建保证一个批次唯一请求即可
    uint64 batch_id = 1;
    repeated CreateFDemandRequest batches = 2;
}

message CreateFDemandRequest {
    // 到货日期
    google.protobuf.Timestamp arrival_date = 1;
    // 订货日期
    google.protobuf.Timestamp demand_date = 2;
    // 订货门店
    uint64 received_by = 3;
    // 配送方
    uint64 distribute_by = 4;
    // 状态
    string status = 5;
    // 订货商品
    repeated Product products = 6;
    // 加盟商ID
    uint64 franchisee_id = 7;
    // 订货类型id
    uint64 order_type_id = 8;
    // 备注
    string remark = 10;
    // 订单类型：加盟门店订货(FSD)
    string type = 11;
    // 原因
    string reason = 12;
    // 业务类型(来源)：手动创建(MANUAL)、总部分配(HD_ASSIGN)、计划创建(PLAN)
    string bus_type = 13;
    // 付款方式：
    // "BohCreditPay"  BOH信用付
    // "WXMPay"        微信小程序
    // "BohOfflinePay" 线下支付
    // "BohVoucherPay" 代金券支付
    string payment_way = 14;
    // 贸易公司
    uint64 trade_company = 15;
    // 扩展信息 eg: {"payment_info": {"payee_name": "收款人名称", "payee_account": "收款方帐号", "deposit_bank": "收款方开户行名称"})
    string extends = 16;
}

message CreateFDemandBatchResponse {
    // True / False
    bool result = 1;
}

message ListFDemandRequest {
    // 订货开始日期
    google.protobuf.Timestamp start_date = 1;
    // 订货结束日期
    google.protobuf.Timestamp end_date = 2;
    // 订货单状态
    repeated string status = 3;
    // 接收方ids
    repeated uint64 received_bys = 4;
    // 订货单号
    string code = 5;
    // 订货单类型：加盟门店订货单(FSD)、加盟门店主配单(FMD)
    repeated string types = 6;
    // 主配类型枚举：门店主配(STORE)、商品主配(PRODUCT)、总部帮订(MASTER) (主配单才有的字段)
    string sub_type = 7;
    // 门店连锁类型：加盟(FRS)、直营(DRS)
    string chain_type = 8;
    // 业务类型(来源)：手动创建(MANUAL)、总部分配(HD_ASSIGN)、计划创建(PLAN)
    repeated string bus_types = 9;
    // 偏移量(默认0)
    uint64 offset = 10;
    // 查询的每页数量
    uint64 limit = 11;
    // 排序(asc or desc 默认asc)
    string order = 12;
    // 排序字段
    string sort = 13;
    bool include_total = 14;
    // 订货单ID列表
    repeated uint64 ids = 15;
    // 商品ids
    repeated uint64 product_ids = 16;
    // 加盟商ids
    repeated uint64 franchisee_ids = 17;
    // 支付方式
    // "BohCreditPay"  BOH信用付
    // "WXMPay"        微信小程序
    // "BohOfflinePay" 线下支付
    // "BohVoucherPay" 代金券支付
    repeated string payment_ways = 18;
    // 订货类型ids
    repeated uint64 order_type_ids = 19;
    // 线路ID
    uint64 line_doc_id = 21;
    string lan = 20;
}

message ListFDemandResponse {
    repeated Demand rows = 1;
    uint64 total = 2;
}

message Demand {
    // 单据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // 生成的批次id
    uint64 batch_id = 3;
    // 编码
    string code = 4;
    // 订货单类型
    string type = 5;
    // 主配类型(主配单才有的字段)
    string sub_type = 6;
    // 业务类型(来源)：手动创建(MANUAL)、总部分配(HD_ASSIGN)、计划创建(PLAN)
    string bus_type = 7;
    // 收货门店id
    uint64 received_by = 8;
    // 收货门店编码
    string received_code = 9;
    // 收货门店名称
    string received_name = 10;
    // 配送方id
    uint64 distribute_by = 11;
    // 门店类型
    string store_type = 12;
    // 加盟商id
    uint64 franchisee_id = 13;
    // 加盟商code
    string franchisee_code = 14;
    // 加盟商名称
    string franchisee_name = 15;
    // 订货日期
    google.protobuf.Timestamp demand_date = 16;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 17;
    // 单据业务状态
    string status = 18;
    // 处理状态
    string process_status = 19;
    // 驳回原因
    string reject_reason = 20;
    // 备注
    string remark = 21;
    // 原因
    string reason = 22;
    // 创建日期
    google.protobuf.Timestamp created_at = 23;
    // 更新时间
    google.protobuf.Timestamp updated_at = 24;
    // 创建人
    uint64 created_by = 25;
    string created_name = 26;
    // 更新人id
    uint64 updated_by = 27;
    string updated_name = 28;
    // 审核人id
    uint64 review_by = 29;
    // 是否有商品
    int32 has_product = 30;
    // 合计含税金额
    double sum_price_tax = 31;
    // 合计税额
    double sum_tax = 32;
    // 实际付款金额
    double pay_amount = 33;
    // 付款凭证（附件）
    repeated string attachments = 34;
    // 扩展信息:(eg: {"product_names": [xxx]})
    string extends = 35;
    message Order {
        uint64 order_id = 1;
        string order_code = 2;
    }
    // 要货单们
    repeated Order orders = 36;
    // 付款方式
    // "BohCreditPay"  BOH信用付
    // "WXMPay"        微信小程序
    // "BohOfflinePay" 线下支付
    // "BohVoucherPay" 代金券支付
    string payment_way = 37;
    // 门店连锁类型
    string chain_type = 38;
    // 是否调整了订货数量
    bool is_adjust = 39;
    // 确认金额
    double confirm_amount = 40;
    // 订货类型id
    uint64 order_type_id = 41;
    // 订货类型名称
    string order_type_name = 42;
    // 订货类型编码
    string order_type_code = 43;
    // 零售金额
    double sales_amount = 44;
    // 确认零售金额
    double confirm_sales_amount = 45;
    message Refund {
        uint64 refund_id = 1;
        string refund_code = 2;
    }
    // 退款单们
    repeated Refund refunds = 46;
    // 审核金额
    double approve_amount = 47;
    // 审核零售金额
    double approve_sales_amount = 48;
    // 订货类型关联时间组
    message OrderTypeTime {
        repeated string order_time = 1;
        repeated string audit_time = 2;
    }
    OrderTypeTime order_type_time = 49;
}

message GetFDemandByIdRequest {
    // 订货单id
    uint64 demand_id = 1;
}
message GetHistoryByIdRequest {
    // 订货单id
    uint64 demand_id = 1;
}
message HistoryResponse {
    repeated History rows = 1;
    uint64 total = 2;
}

message ProductHistory {
    // 数据id
    uint64 id = 1;
    // 租户ID
    uint64 partner_id = 2;
    // 订货单id
    uint64 demand_id = 3;
    // 订单操作traceID(对应订单log表ID)
    uint64 trace_id = 4;
    // 操作code
    string action = 5;
    // 操作名称
    string action_name = 6;
    // 商品id
    uint64 product_id = 7;
    // 商品编号
    string product_code = 8;
    // 商品名称
    string product_name = 9;
    // 单位ID
    uint64 unit_id = 10;
    // 单位名称
    string unit_name = 11;
    // 订货价
    string tax_price = 12;
    // 零售价
    string sales_price = 13;
    // 订货数量
    string quantity = 15;
    // 单项合计金额
    string amount = 16;
    // 零售金额
    string sales_amount = 17;
    // 审核数量
    string approve_quantity = 18;
    // 审核金额
    string approve_amount = 19;
    // 审核零售金额
    string approve_sales_amount = 20;
    // 确认数量
    string confirm_quantity = 21;
    // 确认金额
    string confirm_amount = 22;
    // 确认零售金额
    string confirm_sales_amount = 23;
    // 预计到货天数
    string arrival_days = 24;
    // 配送方式
    string distribution_type = 25;
    // 配送方
    uint64 distribute_by = 26;
    // 配送方名称
    string distribute_name = 27;
    // 操作人名称或者系统自动操作
    uint64 created_by = 28;
    // 操作时间
    google.protobuf.Timestamp created_at = 29;
    // 操作人名称
    string created_name = 30;
}

message History {
    // 历史记录id
    uint64 id = 1;
    // 租户ID
    uint64 partner_id = 2;
    // 订单ID
    uint64 demand_id = 3;
    // 操作code
    // INITED = "总部新建"
    // PREPARE = "手动新建"
    // INVALID = "总部作废"
    // SUBMITTED = "付款"
    // R_APPROVE = "审核"
    // CANCELLED = "取消"
    // CONFIRMED = "确认"
    // APPROVED = "分配"
    // ADD_PRODUCT = "添加商品"
    // UPDATE = "更新订单"
    // RESET = "还原订单"
    // IMPORT = "导入订单"
    string action = 4;
    // 操作名称
    string action_name = 5;
    // 操作人名称或者系统自动操作
    uint64 created_by = 6;
    // 操作时间
    google.protobuf.Timestamp created_at = 7;
    // 操作人名称
    string created_name = 8;
    // 操作平台
    // HEX_MOBILE   移动端
    // HEX_WEB      WEB后台
    string platform = 9;
    // 起始状态
    string start_status = 10;
    // 目标状态
    string end_status = 11;
    // 链路追踪ID
    uint64 trace_id = 12;
    // 操作平台名称
    string platform_name = 13;
    // 变更商品详情
    repeated ProductHistory details = 15;
}
message GetProductsByIdRequest {
    // 订货单id
    uint64 demand_id = 1;
    // 分页限制
    int64 limit = 2;
    // 偏移量
    string offset = 3;
    bool include_total = 4;
    // 排序方式  asc or desc
    string order = 5;
    // 排序字段
    string sort = 6;
    // 是否需要把捆绑商品展开
    bool expand = 7;
}

message ProductsResponse {
    repeated Product rows = 1;
    uint64 total = 2;
}

message Product {
    // 数据id
    uint64 id = 1;
    // 商品id(新增必传)
    uint64 product_id = 2;
    // 商品编号
    string product_code = 3;
    // 商品名称
    string product_name = 4;
    // 原商品ID
    uint64 org_product_id = 5;
    // 原商品编号
    string org_product_code = 6;
    // 原商品名称
    string org_product_name = 7;
    // 商品分类id
    uint64 category_id = 8;
    // 商品分类名称
    string category_name = 9;
    // 商品规格
    string product_spec = 10;
    // 单位ID
    uint64 unit_id = 11;
    // 单位名称
    string unit_name = 12;
    // 单位规格
    string unit_spec = 13;
    // 核算单位ID
    uint64 accounting_unit_id = 14;
    // 核算单位名称
    string accounting_unit_name = 15;
    // 核算单位规格
    string accounting_unit_spec = 16;
    // 单位转换率
    double unit_rate = 17;
    // 订货数量
    double quantity = 18;
    // 核算数量
    double accounting_quantity = 19;
    // 最小订量
    double min_quantity = 20;
    // 最大订量
    double max_quantity = 21;
    // 递增订量
    double increment_quantity = 22;
    // 含税单价
    double tax_price = 23;
    // 成本单价
    double cost_price = 24;
    // 税率
    double tax_rate = 25;
    // 单项合计金额
    double amount = 26;
    // 预计到货天数
    int32 arrival_days = 27;
    // 配送方式
    string distribution_type = 28;
    // 配送方
    uint64 distribute_by = 29;
    // 配送方名称
    string distribute_name = 30;
    // 分配状态
    // INITED = "INITED"           # 未分配
    // PROCESS = "PROCESS"         # 已分配
    // SUCCESS = "SUCCESS"         # 已生单
    string status = 31;
    // 昨日销量
    double yesterday_sales = 32;
    // 实时库存
    double inventory_qty = 33;
    // 在途(待收)数量
    double on_way_qty = 34;
    //0未删除 1已删除
    uint32 is_delete = 35;
    // 创建日期
    google.protobuf.Timestamp created_at = 36;
    // 更新时间
    google.protobuf.Timestamp updated_at = 37;
    // 创建人
    uint64 created_by = 38;
    string created_name = 39;
    // 更新人id
    uint64 updated_by = 40;
    string updated_name = 41;
    // 订单ID
    uint64 demand_id = 42;
    // 租户ID
    uint64 partner_id = 43;
    // 是否可订货
    bool allow_order = 44;
    // 确认数量
    double confirm_quantity = 45;
    // 确认金额
    double confirm_amount = 46;
    // 扩展信息:(eg: {"product_price": [xxx]})
    string extends = 47;
    // 储藏类型
    string storage_type = 48;
    // 零售单价
    double sales_price = 49;
    // 零售金额
    double sales_amount = 50;
    // 确认零售金额
    double confirm_sales_amount = 51;
    // 审核数量
    double approve_quantity = 52;
    // 审核金额
    double approve_amount = 53;
    // 审核零售金额
    double approve_sales_amount = 54;
    // 是否调整了确认订货数量
    bool is_confirm_qty = 55;
    // 是否调整了审核订货数量
    bool is_approve_qty = 56;
    // 捆绑商品或者组合商品
    repeated Product relation_products = 57;
    // 商品组合类型
    string product_type = 58;
    // 关联商品的比例
    string ratio = 59;
    // 关联商品数量取值配置
    int32 configure = 60;
}

message UpdateProductRequest {
    // 订货单id
    uint64 demand_id = 1;
    // 备注
    string remark = 2;
    // 单据状态，对应上方Status枚举, 替换商品/分配时同时支持确认分配
    string status = 3;
    // 商品
    repeated UpdateProduct products = 4;
}
message UpdateProduct {
    // 数据id
    uint64 id = 1;
    // 商品id(新增必传)
    uint64 product_id = 2;
    // 商品编号
    string product_code = 3;
    // 商品名称
    string product_name = 4;
    // 原商品ID
    uint64 org_product_id = 5;
    // 原商品编号
    string org_product_code = 6;
    // 原商品名称
    string org_product_name = 7;
    // 商品分类id
    uint64 category_id = 8;
    // 商品分类名称
    string category_name = 9;
    // 商品规格
    string product_spec = 10;
    // 单位ID
    uint64 unit_id = 11;
    // 单位名称
    string unit_name = 12;
    // 单位规格
    string unit_spec = 13;
    // 核算单位ID
    uint64 accounting_unit_id = 14;
    // 核算单位名称
    string accounting_unit_name = 15;
    // 核算单位规格
    string accounting_unit_spec = 16;
    // 单位转换率
    double unit_rate = 17;
    // 订货数量
    double quantity = 18;
    // 核算数量
    double accounting_quantity = 19;
    // 最小订量
    double min_quantity = 20;
    // 最大订量
    double max_quantity = 21;
    // 递增订量
    double increment_quantity = 22;
    // 含税单价
    double tax_price = 23;
    // 成本单价
    double cost_price = 24;
    // 税率
    double tax_rate = 25;
    // 单项合计金额
    double amount = 26;
    // 预计到货天数
    string arrival_days = 27;
    // 配送方式
    string distribution_type = 28;
    // 配送方
    uint64 distribute_by = 29;
    // 配送方名称
    string distribute_name = 30;
    // 分配状态
    // INITED = "INITED"           # 未分配
    // PROCESS = "PROCESS"         # 已分配
    // SUCCESS = "SUCCESS"         # 已生单
    string status = 31;
    // 昨日销量
    double yesterday_sales = 32;
    // 实时库存
    double inventory_qty = 33;
    // 在途(待收)数量
    double on_way_qty = 34;
    //0未删除 1已删除
    uint32 is_delete = 35;
    // 创建日期
    google.protobuf.Timestamp created_at = 36;
    // 更新时间
    google.protobuf.Timestamp updated_at = 37;
    // 创建人
    uint64 created_by = 38;
    string created_name = 39;
    // 更新人id
    uint64 updated_by = 40;
    string updated_name = 41;
    // 订单ID
    uint64 demand_id = 42;
    // 租户ID
    uint64 partner_id = 43;
    // 是否可订货
    bool allow_order = 44;
    // 确认数量
    double confirm_quantity = 45;
    // 确认金额
    double confirm_amount = 46;
    // 扩展信息:(eg: {"product_price": [xxx]})
    string extends = 47;
    // 储藏类型
    string storage_type = 48;
    // 零售单价
    double sales_price = 49;
    // 零售金额
    double sales_amount = 50;
    // 确认零售金额
    double confirm_sales_amount = 51;
    // 审核数量
    double approve_quantity = 52;
    // 审核金额
    double approve_amount = 53;
    // 审核零售金额
    double approve_sales_amount = 54;
    // 是否调整了确认订货数量
    bool is_confirm_qty = 55;
    // 是否调整了审核订货数量
    bool is_approve_qty = 56;
    // 商品
    repeated UpdateProduct relation_products = 57;
    // 关联商品的比例
    string ratio = 58;
    // 关联商品数量取值配置
    int32 configure = 59;
}
message UpdateProductsResponse {
    // 订货单id
    uint64 demand_id = 1;
    repeated CheckProductMessage msgs = 2;
    int32 not_allocate_products = 3;
}

// 处理单据
message DealFDemandByIdRequest {
    // 批量只支持'确认收款'和'驳回'操作
    repeated uint64 demand_ids = 1;
    // 单据业务状态，对应上方Status枚举
    string action = 2;
    // 备注
    string remark = 3;
    // 附件
    repeated string attachments = 4;
    // 驳回原因
    string reject_reason = 5;
}

// 统一返回对象
message DealFDemandByIdResponse {
    // "success" 成功
    // "failed"  失败
    string result = 1;
    repeated CheckOrderMessage msgs = 2;
}

message GetProductDistributionRequest {
    // 订货单id
    uint64 demand_id = 1;
    // 商品ids
    repeated uint64 product_ids = 2;
}

message GetProductDistributionResponse {
    repeated ProDetail rows = 1;
    repeated DtrDetail distribute_centers = 2;
    int32 total = 3;
    message ProDetail {
        // 商品ID
        uint64 product_id = 1;
        // 可分配仓库信息
        repeated DtrDetail dists = 2;
    }
    message DtrDetail {
        // 配送方ID
        uint64 distribute_id = 1;
        // 配送方名称
        string distribute_name = 2;
        // 到货天数
        string arrival_days = 3;
        // 实时库存(核算单位)
        double qty = 4;
        // 实时库存(订货单位)
        double order_qty = 5;
        // 配送方式
        string distribution_type = 6;
    }

}

message AddFDemandProductRequest {
    // 订货单id
    uint64 demand_id = 1;
    // 商品
    repeated AddProduct products = 2;
    // 添加商品结构
    message AddProduct {
        // 商品ID(必填)
        uint64 product_id = 1;
        // PC添加商品更新到确认数量(必填)
        double confirm_quantity = 2;
        // 含税订货价(必填)
        double tax_price = 3;
        // 税率(必填)
        double tax_rate = 4;
        // 零售价(必填)
        double sales_price = 5;
        // 到货天数(必填)
        string arrival_days = 6;
        // 最小订量(必填)
        double min_quantity = 7;
        // 最大订量(必填)
        double max_quantity = 8;
        // 递增订量(必填)
        double increment_quantity = 9;
        // 扩展商品信息(eg: {"product_price": [xxx]})(必填)
        string extends = 10;
        // 捆绑商品结构(必填)
//        repeated AddProduct bind_products = 11;
        repeated AddProduct relation_products = 11;
         // 关联商品的比例
        string ratio = 12;
        // 关联商品数量取值配置
        int32 configure = 13;
    }
}

message AddFDemandProductResponse {
    // 订货单id
    uint64 demand_id = 1;
}

// 导入订货单参数
message ImportFDemandProductsRequest {
    // 导入类型
    // 导入订单（最大量）Overwrite
    // 导入订单（增加值）Increase
    string import_type = 1;
    uint64 batch_id = 2;
    repeated ImportDetail rows = 3;
}

message ImportDetail {
    uint64 demand_id = 1;
    repeated ImportProduct products = 5;
}

message ImportProduct {
    uint64 product_id = 1;
    // 商品分类id
    uint64 category_id = 2;
    // 单位ID
    uint64 unit_id = 3;
    // 核算单位ID
    uint64 accounting_unit_id = 4;
    // 单位转换率
    double unit_rate = 5;
    // 商品数量
    double quantity = 6;
    // 含税单价(订货价)
    double order_tax_price = 12;
    // 含税单价(零售价)
    double sale_tax_price = 13;
    // 税率
    double tax_rate = 14;
    // 最小订量
    double min_quantity = 15;
    // 最大订量
    double max_quantity = 16;
    // 递增订量
    double increment_quantity = 17;
    // 预计到货天数
    int32 arrival_days = 18;
    // 捆绑商品或者组合商品
    repeated ImportProduct relation_products = 57;
    // 商品组合类型
    string product_type = 58;
    // 关联商品的比例
    string ratio = 59;
    // 关联商品数量取值配置
    int32 configure = 60;
}

message ImportFDemandProductsResponse {
    bool result = 1;
    string description = 2;
}

message GetFDemandImportDetailRequest {
    uint64 batch_id = 1;
    uint64 offset = 2;
    uint64 limit = 3;
    bool include_total = 4;
}

message FDemandImportDetail {
    uint64 id = 1;
    uint64 partner_id = 2;
    uint64 store_id = 3;
    string store_code = 4;
    string store_name = 5;
    uint64 product_id = 6;
    string product_code = 7;
    string product_name = 8;
    double quantity = 9;
    string status = 10;
    string remark = 11;
    uint64 batch_id = 12;
    uint64 row_num = 13;
    string error_msg = 14;
    google.protobuf.Timestamp demand_date = 15;
    google.protobuf.Timestamp created_at = 17;
    google.protobuf.Timestamp updated_at = 18;
    uint64 created_by = 19;
    string created_name = 20;
    uint64 updated_by = 21;
    string updated_name = 22;
    uint64 demand_id = 23;
    string demand_code = 24;
    string order_type_code = 25;
    string order_type_name = 26;
}

message GetFDemandImportDetailResponse {
    repeated FDemandImportDetail rows = 1;
    uint64 total = 2;
    string file_name = 3;
    string status = 4;
    string updated_name = 5;
}

message GetFDemandImportRequest {
    google.protobuf.Timestamp start_date = 1;
    google.protobuf.Timestamp end_date = 2;
    uint64 offset = 3;
    uint64 limit = 4;
    string file_name = 5;
    bool include_total = 6;
    string import_type = 7;
}

message FDemandImportLog {
    uint64 id = 1;
    uint64 partner_id = 2;
    string status = 3;
    string file_name = 4;
    uint64 created_by = 5;
    uint64 updated_by = 6;
    google.protobuf.Timestamp created_at = 7;
    google.protobuf.Timestamp updated_at = 8;
    string created_name = 9;
    string updated_name = 10;
    google.protobuf.Timestamp import_date = 11;
    string import_type = 12;
}

message GetFDemandImportResponse {
    repeated FDemandImportLog rows = 1;
    uint64 total = 2;
}

message ApproveFDemandImportRequest {
    uint64 batch_id = 1;
}

message ApproveFDemandImportResponse {
    bool result = 1;
}

message CancelFDemandImportRequest {
    uint64 batch_id = 1;
}

message CancelFDemandImportResponse {
    bool result = 1;
}

message ListFDemandToRemindRequest {
    //地理区域
    repeated uint64 geo_region_ids = 1;
    //管理区域
    repeated uint64 branch_region_ids = 2;
    //加盟区域
    repeated uint64 franchise_region_ids = 3;
    //门店
    repeated uint64 store_ids = 4;
    //订货状态
    string status = 5;
    // 订货日期
    google.protobuf.Timestamp demand_date = 6;
    //订货类型
    repeated uint64 type_ids = 7;
    // 偏移量(默认0)
    uint64 offset = 8;
    // 查询的每页数量
    uint64 limit = 9;
    // 开始订货日期
    google.protobuf.Timestamp start_demand_date = 10;
    // 结束订货日期
    google.protobuf.Timestamp end_demand_date = 11;

}
message DemandToRemind {
    // 门店编码
    string store_code = 1;
    // 门店名称
    string store_name = 2;
    string geo_region_name = 3;
    string branch_region_name = 4;
    string franchise_region_name = 5;
    //订货类型
    string type_name = 6;
    // 订货日期
    google.protobuf.Timestamp demand_date = 7;
    // 单据业务状态
    string status = 8;
}
message ListFDemandToRemindResponse {
    repeated DemandToRemind rows = 1;
    uint64 total = 2;
}

message BatchDealFDemandRequest {
    // 订货开始日期
    google.protobuf.Timestamp start_date = 1;
    // 订货结束日期
    google.protobuf.Timestamp end_date = 2;
    // 接收方ids
    repeated uint64 received_bys = 3;
    // 业务类型(来源)：手动创建(MANUAL)、总部分配(HD_ASSIGN)、计划创建(PLAN)
    repeated string bus_types = 4;
    // 订货类型ids
    repeated uint64 order_type_ids = 5;
    // 线路ID
    uint64 line_doc_id = 6;
    // 批量更新方式:
    // "ALL"  全量按条件(支持排除部分订单：详见参数exclude_ids)
    // "SELF" 部分按订单(选择部分订单批量提交：详见参数include_ids)
    string batch_method = 8;
    // 排除订单ID列表(batch_type="ALL"时使用)
    repeated uint64 exclude_ids = 9;
    // 提交订单ID列表(batch_type="SELF"时必传)
    repeated uint64 include_ids = 10;
    repeated uint64 company_ids = 11;
}

message BatchDealFDemandResponse {
    bool result = 1;
    string description = 2;
    repeated CheckOrderMessage msgs = 3;
}

message CheckFDemandTodoRequest {
    // 订货单ID
    uint64 demand_id = 1;
}


message TodoReceipt {
    // 单据id
    uint64 id = 1;
    // 单号
    string code = 2;
    // 单据状态
    string status = 4;
    // 单据类型
    string type = 7;
}

message CheckFDemandTodoResponse {
    bool handler = 1;
    repeated TodoReceipt receiving = 2;
    repeated TodoReceipt stocktake = 3;
}


message CheckProductMessage {
    // 商品名称
    string product_name = 1;
    // 商品编号
    string product_code = 2;
    // 规格
    string spec = 3;
    // 订货数量
    double quantity = 4;
    // 起订量
    double min_quantity = 5;
    // 递增定量
    double increment_quantity = 6;
    // 最大定量
    double max_quantity = 7;
    // 订货单位
    string unit = 8;
    // 单价
    double tax_price = 9;
    // 订货金额
    double amount = 10;
    // 储藏类型
    string storage_type = 11;
    // id
    uint64 id = 12;
    // 商品id
    uint64 product_id = 13;
}

message CheckOrderMessage {
    // 订单状态
    string status = 1;
    // 订单编号
    string code = 2;
    // 订货门店
    string store_name = 3;
    // 订货类型
    uint64 order_type_id = 4;
    // 加盟商
    string franchisee_name = 5;
    // 订货金额
    double amount = 6;
    // 订货日期
    google.protobuf.Timestamp demand_date = 7;
    // 预计到货日期
    google.protobuf.Timestamp arrival_date = 8;
    // msg 返回check信息
    string msg = 9;
}