{"swagger": "2.0", "info": {"title": "frs_management/franchisee_refund.proto", "version": "version not set"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/frs-management/frs-refund": {"get": {"summary": "查询退款单列表", "operationId": "ListFRefund", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_refundListFRefundResponse"}}}, "parameters": [{"name": "refund_start_date", "description": "退款申请开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "refund_end_date", "description": "退款申请结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "status", "description": "退款单状态.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "received_bys", "description": "接收方ids.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "franchisee_ids", "description": "加盟商ids.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "code", "description": "退款单号.", "in": "query", "required": false, "type": "string"}, {"name": "limit", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "order", "description": "排序方式  asc or desc.", "in": "query", "required": false, "type": "string"}, {"name": "sort", "description": "排序字段.", "in": "query", "required": false, "type": "string"}, {"name": "approved_start_date", "description": "退款审核开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "approved_end_date", "description": "退款审核结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "types", "description": "退款单类型(来源)\n仅退款: ORDER_REFUND\n缺货退款: OOS_REFUND\n退货退款: RETURN_REFUND\n差异退款: DIFF_REFUND.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "main_type", "description": "关联主单类型(单号筛选时使用)\n订货单FRS_DEMAND\n要货单FRS_WHS_DEMAND\n退货单FRS_RETURN\n收货差异单FRS_REC_DIFF.", "in": "query", "required": false, "type": "string"}, {"name": "main_code", "description": "关联主单号.", "in": "query", "required": false, "type": "string"}, {"name": "payment_ways", "description": "退款支付方式\n\"BohCreditPay\"  BOH信用付\n\"WXMPay\"        微信小程序\n\"BohOfflinePay\" 线下支付\n\"BohVoucherPay\" 代金券支付.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "ids", "description": "退款单id.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}], "tags": ["FranchiseeRefundService"]}}, "/api/v2/supply/frs-management/frs-refund/create": {"post": {"summary": "创建退款单", "operationId": "CreateFRefund", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_refundCreateFRefundResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/franchisee_refundCreateFRefundRequest"}}], "tags": ["FranchiseeRefundService"]}}, "/api/v2/supply/frs-management/frs-refund/deal": {"put": {"summary": "修改退款单状态统一入口", "operationId": "DealFRefundById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_refundDealFRefundByIdResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/franchisee_refundDealFRefundByIdRequest"}}], "tags": ["FranchiseeRefundService"]}}, "/api/v2/supply/frs-management/frs-refund/{refund_id}": {"get": {"summary": "根据id查询单个退款单", "operationId": "GetFRefundById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_refundGetFRefundByIdResponse"}}}, "parameters": [{"name": "refund_id", "description": "退款单id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["FranchiseeRefundService"]}}, "/api/v2/supply/frs-management/frs-refund/{refund_id}/history": {"get": {"summary": "根据id查询单据历史记录", "operationId": "GetFRefundHistoryById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_refundRefundHistoryResponse"}}}, "parameters": [{"name": "refund_id", "description": "退款单id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["FranchiseeRefundService"]}}}, "definitions": {"franchisee_refundCreateFRefundRequest": {"type": "object", "properties": {"main_id": {"type": "string", "format": "uint64", "title": "关联主单ID(订货单/退货单/收货单/要货单等)"}, "main_code": {"type": "string", "title": "关联主单单号"}, "main_type": {"type": "string", "title": "关联主单类型：订货单FRS_DEMAND、要货单FRS_WHS_DEMAND、退货单FRS_RETURN、收货差异单FRS_REC_DIFF"}, "received_by": {"type": "string", "format": "uint64", "title": "收货门店id"}, "franchisee_id": {"type": "string", "format": "uint64", "title": "加盟商id"}, "refund_date": {"type": "string", "format": "date-time", "title": "退款申请日期"}, "remark": {"type": "string", "title": "备注"}, "reason": {"type": "string", "title": "原因"}, "refund_amount": {"type": "number", "format": "double", "title": "退款金额"}, "pay_amount": {"type": "number", "format": "double", "title": "实际付款金额"}, "payment_way": {"type": "string", "title": "退款付款方式\n\"BohCreditPay\"  BOH信用付\n\"WXMPay\"        微信小程序\n\"BohOfflinePay\" 线下支付\n\"BohVoucherPay\" 代金券支付"}, "type": {"type": "string", "title": "退款单类型：订货退款ORDER_REFUND、缺货退款OOS_REFUND、退货退款RETURN_REFUND、收货差异退款DIFF_REFUND"}, "batch_id": {"type": "string", "format": "uint64", "title": "订货单ID"}, "products": {"type": "array", "items": {"$ref": "#/definitions/franchisee_refundRefundProduct"}, "title": "商品详情"}}}, "franchisee_refundCreateFRefundResponse": {"type": "object", "properties": {"refund_id": {"type": "string", "format": "uint64", "title": "退款单ID"}}}, "franchisee_refundDealFRefundByIdRequest": {"type": "object", "properties": {"refund_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "批量只支持'审核通过'和'驳回'操作"}, "action": {"type": "string", "title": "单据业务状态，对应上方Status枚举"}, "remark": {"type": "string", "title": "备注"}, "attachments": {"type": "array", "items": {"type": "string"}, "title": "附件"}, "reject_reason": {"type": "string", "title": "驳回原因"}, "payment_way": {"type": "string", "title": "退款支付方式"}}, "title": "处理单据"}, "franchisee_refundDealFRefundByIdResponse": {"type": "object", "properties": {"result": {"type": "string"}}, "title": "统一返回对象"}, "franchisee_refundGetFRefundByIdResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "单据id"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "main_id": {"type": "string", "format": "uint64", "title": "关联主单ID(订货单/退货单/收货单等)"}, "main_code": {"type": "string", "title": "关联主单单号"}, "main_type": {"type": "string", "title": "关联主单类型"}, "code": {"type": "string", "title": "单号"}, "type": {"type": "string", "title": "退款单类型：订货退款ORDER_REFUND、缺货退款OOS_REFUND、退货退款RETURN_REFUND、收货差异退款DIFF_REFUND"}, "received_by": {"type": "string", "format": "uint64", "title": "收货门店id"}, "received_code": {"type": "string", "title": "收货门店编码"}, "received_name": {"type": "string", "title": "收货门店名称"}, "franchisee_id": {"type": "string", "format": "uint64", "title": "加盟商id"}, "franchisee_code": {"type": "string", "title": "加盟商code"}, "franchisee_name": {"type": "string", "title": "加盟商名称"}, "refund_date": {"type": "string", "format": "date-time", "title": "退款申请日期"}, "status": {"type": "string", "title": "单据业务状态"}, "process_status": {"type": "string", "title": "处理状态"}, "reject_reason": {"type": "string", "title": "驳回原因"}, "remark": {"type": "string", "title": "备注"}, "reason": {"type": "string", "title": "原因"}, "created_at": {"type": "string", "format": "date-time", "title": "创建日期"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人"}, "created_name": {"type": "string"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "updated_name": {"type": "string"}, "review_by": {"type": "string", "format": "uint64", "title": "审核人id"}, "refund_amount": {"type": "number", "format": "double", "title": "退款金额"}, "pay_amount": {"type": "number", "format": "double", "title": "实际付款金额"}, "attachments": {"type": "array", "items": {"type": "string"}, "title": "付款凭证（附件）"}, "extends": {"type": "string", "title": "扩展信息"}, "approved_date": {"type": "string", "format": "date-time", "title": "退款审核日期"}, "payment_way": {"type": "string", "title": "退款付款方式\n\"BohCreditPay\"  BOH信用付\n\"WXMPay\"        微信小程序\n\"BohOfflinePay\" 线下支付\n\"BohVoucherPay\" 代金券支付"}, "trade_company": {"type": "string", "format": "uint64", "title": "贸易公司"}, "company_name": {"type": "string", "title": "公司名称"}, "company_code": {"type": "string", "title": "公司编号"}, "batch_id": {"type": "string", "format": "uint64", "title": "订货单ID"}, "products": {"type": "array", "items": {"$ref": "#/definitions/franchisee_refundRefundProduct"}, "title": "商品详情"}}}, "franchisee_refundListFRefundResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/franchisee_refundRefund"}}, "total": {"type": "string", "format": "uint64"}}}, "franchisee_refundRefund": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "单据id"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "main_id": {"type": "string", "format": "uint64", "title": "关联主单ID(订货单/退货单)"}, "main_code": {"type": "string", "title": "关联主单单号"}, "main_type": {"type": "string", "title": "关联主单类型"}, "code": {"type": "string", "title": "单号"}, "type": {"type": "string", "title": "退款单类型：订货退款ORDER_REFUND、缺货退款OOS_REFUND、退货退款RETURN_REFUND、收货差异退款DIFF_REFUND"}, "received_by": {"type": "string", "format": "uint64", "title": "收货门店id"}, "received_code": {"type": "string", "title": "收货门店编码"}, "received_name": {"type": "string", "title": "收货门店名称"}, "franchisee_id": {"type": "string", "format": "uint64", "title": "加盟商id"}, "franchisee_code": {"type": "string", "title": "加盟商code"}, "franchisee_name": {"type": "string", "title": "加盟商名称"}, "refund_date": {"type": "string", "format": "date-time", "title": "退款申请日期"}, "status": {"type": "string", "title": "单据业务状态"}, "process_status": {"type": "string", "title": "处理状态"}, "reject_reason": {"type": "string", "title": "驳回原因"}, "remark": {"type": "string", "title": "备注"}, "reason": {"type": "string", "title": "原因"}, "created_at": {"type": "string", "format": "date-time", "title": "创建日期"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人"}, "created_name": {"type": "string"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "updated_name": {"type": "string"}, "review_by": {"type": "string", "format": "uint64", "title": "审核人id"}, "refund_amount": {"type": "number", "format": "double", "title": "退款金额"}, "pay_amount": {"type": "number", "format": "double", "title": "实际付款金额"}, "attachments": {"type": "array", "items": {"type": "string"}, "title": "付款凭证（附件）"}, "extends": {"type": "string", "title": "扩展信息"}, "approved_date": {"type": "string", "format": "date-time", "title": "退款审核日期"}, "payment_way": {"type": "string", "title": "退款付款方式\n\"BohCreditPay\"  BOH信用付\n\"WXMPay\"        微信小程序\n\"BohOfflinePay\" 线下支付\n\"BohVoucherPay\" 代金券支付"}, "trade_company": {"type": "string", "format": "uint64", "title": "贸易公司"}, "company_name": {"type": "string", "title": "公司名称"}, "company_code": {"type": "string", "title": "公司编号"}, "batch_id": {"type": "string", "format": "uint64", "title": "订货单ID"}}}, "franchisee_refundRefundHistory": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "历史记录id"}, "status": {"type": "string", "title": "退款单状态"}, "created_by": {"type": "string", "format": "uint64", "title": "操作人名称或者系统自动操作"}, "created_at": {"type": "string", "format": "date-time", "title": "操作时间"}, "created_name": {"type": "string", "title": "操作人名称"}}}, "franchisee_refundRefundHistoryResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/franchisee_refundRefundHistory"}}, "total": {"type": "string", "format": "uint64"}}}, "franchisee_refundRefundProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "数据id"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_code": {"type": "string", "title": "商品编号"}, "product_name": {"type": "string", "title": "商品名称"}, "refund_id": {"type": "string", "format": "uint64", "title": "退款单ID"}, "partner_id": {"type": "string", "format": "uint64"}, "category_id": {"type": "string", "format": "uint64", "title": "商品分类id"}, "category_name": {"type": "string", "title": "商品分类名称"}, "product_spec": {"type": "string", "title": "商品规格"}, "unit_id": {"type": "string", "format": "uint64", "title": "单位ID"}, "unit_name": {"type": "string", "title": "单位名称"}, "unit_spec": {"type": "string", "title": "单位规格"}, "unit_rate": {"type": "number", "format": "double", "title": "单位转换率"}, "quantity": {"type": "number", "format": "double", "title": "退货数量"}, "tax_price": {"type": "number", "format": "double", "title": "含税单价"}, "cost_price": {"type": "number", "format": "double", "title": "成本价"}, "tax_rate": {"type": "number", "format": "double", "title": "税率"}, "amount": {"type": "number", "format": "double", "title": "单项合计金额"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人"}, "created_name": {"type": "string"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "updated_name": {"type": "string"}, "created_at": {"type": "string", "format": "date-time", "title": "创建日期"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}}}}}