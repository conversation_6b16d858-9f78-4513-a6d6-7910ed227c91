# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frs_management/franchisee_demand.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frs_management/franchisee_demand.proto',
  package='franchisee_demand',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n&frs_management/franchisee_demand.proto\x12\x11\x66ranchisee_demand\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"g\n\x19\x43reateFDemandBatchRequest\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\x12\x38\n\x07\x62\x61tches\x18\x02 \x03(\x0b\x32\'.franchisee_demand.CreateFDemandRequest\"\x8e\x03\n\x14\x43reateFDemandRequest\x12\x30\n\x0c\x61rrival_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0b\x64\x65mand_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0breceived_by\x18\x03 \x01(\x04\x12\x15\n\rdistribute_by\x18\x04 \x01(\x04\x12\x0e\n\x06status\x18\x05 \x01(\t\x12,\n\x08products\x18\x06 \x03(\x0b\x32\x1a.franchisee_demand.Product\x12\x15\n\rfranchisee_id\x18\x07 \x01(\x04\x12\x15\n\rorder_type_id\x18\x08 \x01(\x04\x12\x0e\n\x06remark\x18\n \x01(\t\x12\x0c\n\x04type\x18\x0b \x01(\t\x12\x0e\n\x06reason\x18\x0c \x01(\t\x12\x10\n\x08\x62us_type\x18\r \x01(\t\x12\x13\n\x0bpayment_way\x18\x0e \x01(\t\x12\x15\n\rtrade_company\x18\x0f \x01(\x04\x12\x0f\n\x07\x65xtends\x18\x10 \x01(\t\",\n\x1a\x43reateFDemandBatchResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\xcb\x03\n\x12ListFDemandRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x03 \x03(\t\x12\x14\n\x0creceived_bys\x18\x04 \x03(\x04\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\r\n\x05types\x18\x06 \x03(\t\x12\x10\n\x08sub_type\x18\x07 \x01(\t\x12\x12\n\nchain_type\x18\x08 \x01(\t\x12\x11\n\tbus_types\x18\t \x03(\t\x12\x0e\n\x06offset\x18\n \x01(\x04\x12\r\n\x05limit\x18\x0b \x01(\x04\x12\r\n\x05order\x18\x0c \x01(\t\x12\x0c\n\x04sort\x18\r \x01(\t\x12\x15\n\rinclude_total\x18\x0e \x01(\x08\x12\x0b\n\x03ids\x18\x0f \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x10 \x03(\x04\x12\x16\n\x0e\x66ranchisee_ids\x18\x11 \x03(\x04\x12\x14\n\x0cpayment_ways\x18\x12 \x03(\t\x12\x16\n\x0eorder_type_ids\x18\x13 \x03(\x04\x12\x13\n\x0bline_doc_id\x18\x15 \x01(\x04\x12\x0b\n\x03lan\x18\x14 \x01(\t\"M\n\x13ListFDemandResponse\x12\'\n\x04rows\x18\x01 \x03(\x0b\x32\x19.franchisee_demand.Demand\x12\r\n\x05total\x18\x02 \x01(\x04\"\xf8\n\n\x06\x44\x65mand\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08\x62\x61tch_id\x18\x03 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\x0c\n\x04type\x18\x05 \x01(\t\x12\x10\n\x08sub_type\x18\x06 \x01(\t\x12\x10\n\x08\x62us_type\x18\x07 \x01(\t\x12\x13\n\x0breceived_by\x18\x08 \x01(\x04\x12\x15\n\rreceived_code\x18\t \x01(\t\x12\x15\n\rreceived_name\x18\n \x01(\t\x12\x15\n\rdistribute_by\x18\x0b \x01(\x04\x12\x12\n\nstore_type\x18\x0c \x01(\t\x12\x15\n\rfranchisee_id\x18\r \x01(\x04\x12\x17\n\x0f\x66ranchisee_code\x18\x0e \x01(\t\x12\x17\n\x0f\x66ranchisee_name\x18\x0f \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x12 \x01(\t\x12\x16\n\x0eprocess_status\x18\x13 \x01(\t\x12\x15\n\rreject_reason\x18\x14 \x01(\t\x12\x0e\n\x06remark\x18\x15 \x01(\t\x12\x0e\n\x06reason\x18\x16 \x01(\t\x12.\n\ncreated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x19 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1a \x01(\t\x12\x12\n\nupdated_by\x18\x1b \x01(\x04\x12\x14\n\x0cupdated_name\x18\x1c \x01(\t\x12\x11\n\treview_by\x18\x1d \x01(\x04\x12\x13\n\x0bhas_product\x18\x1e \x01(\x05\x12\x15\n\rsum_price_tax\x18\x1f \x01(\x01\x12\x0f\n\x07sum_tax\x18  \x01(\x01\x12\x12\n\npay_amount\x18! \x01(\x01\x12\x13\n\x0b\x61ttachments\x18\" \x03(\t\x12\x0f\n\x07\x65xtends\x18# \x01(\t\x12/\n\x06orders\x18$ \x03(\x0b\x32\x1f.franchisee_demand.Demand.Order\x12\x13\n\x0bpayment_way\x18% \x01(\t\x12\x12\n\nchain_type\x18& \x01(\t\x12\x11\n\tis_adjust\x18\' \x01(\x08\x12\x16\n\x0e\x63onfirm_amount\x18( \x01(\x01\x12\x15\n\rorder_type_id\x18) \x01(\x04\x12\x17\n\x0forder_type_name\x18* \x01(\t\x12\x17\n\x0forder_type_code\x18+ \x01(\t\x12\x14\n\x0csales_amount\x18, \x01(\x01\x12\x1c\n\x14\x63onfirm_sales_amount\x18- \x01(\x01\x12\x31\n\x07refunds\x18. \x03(\x0b\x32 .franchisee_demand.Demand.Refund\x12\x16\n\x0e\x61pprove_amount\x18/ \x01(\x01\x12\x1c\n\x14\x61pprove_sales_amount\x18\x30 \x01(\x01\x12@\n\x0forder_type_time\x18\x31 \x01(\x0b\x32\'.franchisee_demand.Demand.OrderTypeTime\x1a-\n\x05Order\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x12\n\norder_code\x18\x02 \x01(\t\x1a\x30\n\x06Refund\x12\x11\n\trefund_id\x18\x01 \x01(\x04\x12\x13\n\x0brefund_code\x18\x02 \x01(\t\x1a\x37\n\rOrderTypeTime\x12\x12\n\norder_time\x18\x01 \x03(\t\x12\x12\n\naudit_time\x18\x02 \x03(\t\"*\n\x15GetFDemandByIdRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\"*\n\x15GetHistoryByIdRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\"J\n\x0fHistoryResponse\x12(\n\x04rows\x18\x01 \x03(\x0b\x32\x1a.franchisee_demand.History\x12\r\n\x05total\x18\x02 \x01(\x04\"\x99\x05\n\x0eProductHistory\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x11\n\tdemand_id\x18\x03 \x01(\x04\x12\x10\n\x08trace_id\x18\x04 \x01(\x04\x12\x0e\n\x06\x61\x63tion\x18\x05 \x01(\t\x12\x13\n\x0b\x61\x63tion_name\x18\x06 \x01(\t\x12\x12\n\nproduct_id\x18\x07 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x08 \x01(\t\x12\x14\n\x0cproduct_name\x18\t \x01(\t\x12\x0f\n\x07unit_id\x18\n \x01(\x04\x12\x11\n\tunit_name\x18\x0b \x01(\t\x12\x11\n\ttax_price\x18\x0c \x01(\t\x12\x13\n\x0bsales_price\x18\r \x01(\t\x12\x10\n\x08quantity\x18\x0f \x01(\t\x12\x0e\n\x06\x61mount\x18\x10 \x01(\t\x12\x14\n\x0csales_amount\x18\x11 \x01(\t\x12\x18\n\x10\x61pprove_quantity\x18\x12 \x01(\t\x12\x16\n\x0e\x61pprove_amount\x18\x13 \x01(\t\x12\x1c\n\x14\x61pprove_sales_amount\x18\x14 \x01(\t\x12\x18\n\x10\x63onfirm_quantity\x18\x15 \x01(\t\x12\x16\n\x0e\x63onfirm_amount\x18\x16 \x01(\t\x12\x1c\n\x14\x63onfirm_sales_amount\x18\x17 \x01(\t\x12\x14\n\x0c\x61rrival_days\x18\x18 \x01(\t\x12\x19\n\x11\x64istribution_type\x18\x19 \x01(\t\x12\x15\n\rdistribute_by\x18\x1a \x01(\x04\x12\x17\n\x0f\x64istribute_name\x18\x1b \x01(\t\x12\x12\n\ncreated_by\x18\x1c \x01(\x04\x12.\n\ncreated_at\x18\x1d \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\x1e \x01(\t\"\xd4\x02\n\x07History\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x11\n\tdemand_id\x18\x03 \x01(\x04\x12\x0e\n\x06\x61\x63tion\x18\x04 \x01(\t\x12\x13\n\x0b\x61\x63tion_name\x18\x05 \x01(\t\x12\x12\n\ncreated_by\x18\x06 \x01(\x04\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\x08 \x01(\t\x12\x10\n\x08platform\x18\t \x01(\t\x12\x14\n\x0cstart_status\x18\n \x01(\t\x12\x12\n\nend_status\x18\x0b \x01(\t\x12\x10\n\x08trace_id\x18\x0c \x01(\x04\x12\x15\n\rplatform_name\x18\r \x01(\t\x12\x32\n\x07\x64\x65tails\x18\x0f \x03(\x0b\x32!.franchisee_demand.ProductHistory\"\x8e\x01\n\x16GetProductsByIdRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x03\x12\x0e\n\x06offset\x18\x03 \x01(\t\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12\r\n\x05order\x18\x05 \x01(\t\x12\x0c\n\x04sort\x18\x06 \x01(\t\x12\x0e\n\x06\x65xpand\x18\x07 \x01(\x08\"K\n\x10ProductsResponse\x12(\n\x04rows\x18\x01 \x03(\x0b\x32\x1a.franchisee_demand.Product\x12\r\n\x05total\x18\x02 \x01(\x04\"\x8e\x0b\n\x07Product\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x03 \x01(\t\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12\x16\n\x0eorg_product_id\x18\x05 \x01(\x04\x12\x18\n\x10org_product_code\x18\x06 \x01(\t\x12\x18\n\x10org_product_name\x18\x07 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x08 \x01(\x04\x12\x15\n\rcategory_name\x18\t \x01(\t\x12\x14\n\x0cproduct_spec\x18\n \x01(\t\x12\x0f\n\x07unit_id\x18\x0b \x01(\x04\x12\x11\n\tunit_name\x18\x0c \x01(\t\x12\x11\n\tunit_spec\x18\r \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x0e \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x0f \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x10 \x01(\t\x12\x11\n\tunit_rate\x18\x11 \x01(\x01\x12\x10\n\x08quantity\x18\x12 \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x13 \x01(\x01\x12\x14\n\x0cmin_quantity\x18\x14 \x01(\x01\x12\x14\n\x0cmax_quantity\x18\x15 \x01(\x01\x12\x1a\n\x12increment_quantity\x18\x16 \x01(\x01\x12\x11\n\ttax_price\x18\x17 \x01(\x01\x12\x12\n\ncost_price\x18\x18 \x01(\x01\x12\x10\n\x08tax_rate\x18\x19 \x01(\x01\x12\x0e\n\x06\x61mount\x18\x1a \x01(\x01\x12\x14\n\x0c\x61rrival_days\x18\x1b \x01(\x05\x12\x19\n\x11\x64istribution_type\x18\x1c \x01(\t\x12\x15\n\rdistribute_by\x18\x1d \x01(\x04\x12\x17\n\x0f\x64istribute_name\x18\x1e \x01(\t\x12\x0e\n\x06status\x18\x1f \x01(\t\x12\x17\n\x0fyesterday_sales\x18  \x01(\x01\x12\x15\n\rinventory_qty\x18! \x01(\x01\x12\x12\n\non_way_qty\x18\" \x01(\x01\x12\x11\n\tis_delete\x18# \x01(\r\x12.\n\ncreated_at\x18$ \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18% \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18& \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\' \x01(\t\x12\x12\n\nupdated_by\x18( \x01(\x04\x12\x14\n\x0cupdated_name\x18) \x01(\t\x12\x11\n\tdemand_id\x18* \x01(\x04\x12\x12\n\npartner_id\x18+ \x01(\x04\x12\x13\n\x0b\x61llow_order\x18, \x01(\x08\x12\x18\n\x10\x63onfirm_quantity\x18- \x01(\x01\x12\x16\n\x0e\x63onfirm_amount\x18. \x01(\x01\x12\x0f\n\x07\x65xtends\x18/ \x01(\t\x12\x14\n\x0cstorage_type\x18\x30 \x01(\t\x12\x13\n\x0bsales_price\x18\x31 \x01(\x01\x12\x14\n\x0csales_amount\x18\x32 \x01(\x01\x12\x1c\n\x14\x63onfirm_sales_amount\x18\x33 \x01(\x01\x12\x18\n\x10\x61pprove_quantity\x18\x34 \x01(\x01\x12\x16\n\x0e\x61pprove_amount\x18\x35 \x01(\x01\x12\x1c\n\x14\x61pprove_sales_amount\x18\x36 \x01(\x01\x12\x16\n\x0eis_confirm_qty\x18\x37 \x01(\x08\x12\x16\n\x0eis_approve_qty\x18\x38 \x01(\x08\x12\x35\n\x11relation_products\x18\x39 \x03(\x0b\x32\x1a.franchisee_demand.Product\x12\x14\n\x0cproduct_type\x18: \x01(\t\x12\r\n\x05ratio\x18; \x01(\t\x12\x11\n\tconfigure\x18< \x01(\x05\"}\n\x14UpdateProductRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12\x0e\n\x06remark\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x32\n\x08products\x18\x04 \x03(\x0b\x32 .franchisee_demand.UpdateProduct\"\x84\x0b\n\rUpdateProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x03 \x01(\t\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12\x16\n\x0eorg_product_id\x18\x05 \x01(\x04\x12\x18\n\x10org_product_code\x18\x06 \x01(\t\x12\x18\n\x10org_product_name\x18\x07 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x08 \x01(\x04\x12\x15\n\rcategory_name\x18\t \x01(\t\x12\x14\n\x0cproduct_spec\x18\n \x01(\t\x12\x0f\n\x07unit_id\x18\x0b \x01(\x04\x12\x11\n\tunit_name\x18\x0c \x01(\t\x12\x11\n\tunit_spec\x18\r \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x0e \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x0f \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x10 \x01(\t\x12\x11\n\tunit_rate\x18\x11 \x01(\x01\x12\x10\n\x08quantity\x18\x12 \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x13 \x01(\x01\x12\x14\n\x0cmin_quantity\x18\x14 \x01(\x01\x12\x14\n\x0cmax_quantity\x18\x15 \x01(\x01\x12\x1a\n\x12increment_quantity\x18\x16 \x01(\x01\x12\x11\n\ttax_price\x18\x17 \x01(\x01\x12\x12\n\ncost_price\x18\x18 \x01(\x01\x12\x10\n\x08tax_rate\x18\x19 \x01(\x01\x12\x0e\n\x06\x61mount\x18\x1a \x01(\x01\x12\x14\n\x0c\x61rrival_days\x18\x1b \x01(\t\x12\x19\n\x11\x64istribution_type\x18\x1c \x01(\t\x12\x15\n\rdistribute_by\x18\x1d \x01(\x04\x12\x17\n\x0f\x64istribute_name\x18\x1e \x01(\t\x12\x0e\n\x06status\x18\x1f \x01(\t\x12\x17\n\x0fyesterday_sales\x18  \x01(\x01\x12\x15\n\rinventory_qty\x18! \x01(\x01\x12\x12\n\non_way_qty\x18\" \x01(\x01\x12\x11\n\tis_delete\x18# \x01(\r\x12.\n\ncreated_at\x18$ \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18% \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18& \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\' \x01(\t\x12\x12\n\nupdated_by\x18( \x01(\x04\x12\x14\n\x0cupdated_name\x18) \x01(\t\x12\x11\n\tdemand_id\x18* \x01(\x04\x12\x12\n\npartner_id\x18+ \x01(\x04\x12\x13\n\x0b\x61llow_order\x18, \x01(\x08\x12\x18\n\x10\x63onfirm_quantity\x18- \x01(\x01\x12\x16\n\x0e\x63onfirm_amount\x18. \x01(\x01\x12\x0f\n\x07\x65xtends\x18/ \x01(\t\x12\x14\n\x0cstorage_type\x18\x30 \x01(\t\x12\x13\n\x0bsales_price\x18\x31 \x01(\x01\x12\x14\n\x0csales_amount\x18\x32 \x01(\x01\x12\x1c\n\x14\x63onfirm_sales_amount\x18\x33 \x01(\x01\x12\x18\n\x10\x61pprove_quantity\x18\x34 \x01(\x01\x12\x16\n\x0e\x61pprove_amount\x18\x35 \x01(\x01\x12\x1c\n\x14\x61pprove_sales_amount\x18\x36 \x01(\x01\x12\x16\n\x0eis_confirm_qty\x18\x37 \x01(\x08\x12\x16\n\x0eis_approve_qty\x18\x38 \x01(\x08\x12;\n\x11relation_products\x18\x39 \x03(\x0b\x32 .franchisee_demand.UpdateProduct\x12\r\n\x05ratio\x18: \x01(\t\x12\x11\n\tconfigure\x18; \x01(\x05\"\x80\x01\n\x16UpdateProductsResponse\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12\x34\n\x04msgs\x18\x02 \x03(\x0b\x32&.franchisee_demand.CheckProductMessage\x12\x1d\n\x15not_allocate_products\x18\x03 \x01(\x05\"x\n\x16\x44\x65\x61lFDemandByIdRequest\x12\x12\n\ndemand_ids\x18\x01 \x03(\x04\x12\x0e\n\x06\x61\x63tion\x18\x02 \x01(\t\x12\x0e\n\x06remark\x18\x03 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x04 \x03(\t\x12\x15\n\rreject_reason\x18\x05 \x01(\t\"]\n\x17\x44\x65\x61lFDemandByIdResponse\x12\x0e\n\x06result\x18\x01 \x01(\t\x12\x32\n\x04msgs\x18\x02 \x03(\x0b\x32$.franchisee_demand.CheckOrderMessage\"G\n\x1dGetProductDistributionRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12\x13\n\x0bproduct_ids\x18\x02 \x03(\x04\"\xcf\x03\n\x1eGetProductDistributionResponse\x12I\n\x04rows\x18\x01 \x03(\x0b\x32;.franchisee_demand.GetProductDistributionResponse.ProDetail\x12W\n\x12\x64istribute_centers\x18\x02 \x03(\x0b\x32;.franchisee_demand.GetProductDistributionResponse.DtrDetail\x12\r\n\x05total\x18\x03 \x01(\x05\x1ak\n\tProDetail\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12J\n\x05\x64ists\x18\x02 \x03(\x0b\x32;.franchisee_demand.GetProductDistributionResponse.DtrDetail\x1a\x8c\x01\n\tDtrDetail\x12\x15\n\rdistribute_id\x18\x01 \x01(\x04\x12\x17\n\x0f\x64istribute_name\x18\x02 \x01(\t\x12\x14\n\x0c\x61rrival_days\x18\x03 \x01(\t\x12\x0b\n\x03qty\x18\x04 \x01(\x01\x12\x11\n\torder_qty\x18\x05 \x01(\x01\x12\x19\n\x11\x64istribution_type\x18\x06 \x01(\t\"\xd2\x03\n\x18\x41\x64\x64\x46\x44\x65mandProductRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12H\n\x08products\x18\x02 \x03(\x0b\x32\x36.franchisee_demand.AddFDemandProductRequest.AddProduct\x1a\xd8\x02\n\nAddProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x18\n\x10\x63onfirm_quantity\x18\x02 \x01(\x01\x12\x11\n\ttax_price\x18\x03 \x01(\x01\x12\x10\n\x08tax_rate\x18\x04 \x01(\x01\x12\x13\n\x0bsales_price\x18\x05 \x01(\x01\x12\x14\n\x0c\x61rrival_days\x18\x06 \x01(\t\x12\x14\n\x0cmin_quantity\x18\x07 \x01(\x01\x12\x14\n\x0cmax_quantity\x18\x08 \x01(\x01\x12\x1a\n\x12increment_quantity\x18\t \x01(\x01\x12\x0f\n\x07\x65xtends\x18\n \x01(\t\x12Q\n\x11relation_products\x18\x0b \x03(\x0b\x32\x36.franchisee_demand.AddFDemandProductRequest.AddProduct\x12\r\n\x05ratio\x18\x0c \x01(\t\x12\x11\n\tconfigure\x18\r \x01(\x05\".\n\x19\x41\x64\x64\x46\x44\x65mandProductResponse\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\"t\n\x1cImportFDemandProductsRequest\x12\x13\n\x0bimport_type\x18\x01 \x01(\t\x12\x10\n\x08\x62\x61tch_id\x18\x02 \x01(\x04\x12-\n\x04rows\x18\x03 \x03(\x0b\x32\x1f.franchisee_demand.ImportDetail\"U\n\x0cImportDetail\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12\x32\n\x08products\x18\x05 \x03(\x0b\x32 .franchisee_demand.ImportProduct\"\xa0\x03\n\rImportProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x13\n\x0b\x63\x61tegory_id\x18\x02 \x01(\x04\x12\x0f\n\x07unit_id\x18\x03 \x01(\x04\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x04 \x01(\x04\x12\x11\n\tunit_rate\x18\x05 \x01(\x01\x12\x10\n\x08quantity\x18\x06 \x01(\x01\x12\x17\n\x0forder_tax_price\x18\x0c \x01(\x01\x12\x16\n\x0esale_tax_price\x18\r \x01(\x01\x12\x10\n\x08tax_rate\x18\x0e \x01(\x01\x12\x14\n\x0cmin_quantity\x18\x0f \x01(\x01\x12\x14\n\x0cmax_quantity\x18\x10 \x01(\x01\x12\x1a\n\x12increment_quantity\x18\x11 \x01(\x01\x12\x14\n\x0c\x61rrival_days\x18\x12 \x01(\x05\x12;\n\x11relation_products\x18\x39 \x03(\x0b\x32 .franchisee_demand.ImportProduct\x12\x14\n\x0cproduct_type\x18: \x01(\t\x12\r\n\x05ratio\x18; \x01(\t\x12\x11\n\tconfigure\x18< \x01(\x05\"D\n\x1dImportFDemandProductsResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\"g\n\x1dGetFDemandImportDetailRequest\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\x12\x0e\n\x06offset\x18\x02 \x01(\x04\x12\r\n\x05limit\x18\x03 \x01(\x04\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\"\xd6\x04\n\x13\x46\x44\x65mandImportDetail\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08store_id\x18\x03 \x01(\x04\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nstore_name\x18\x05 \x01(\t\x12\x12\n\nproduct_id\x18\x06 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x07 \x01(\t\x12\x14\n\x0cproduct_name\x18\x08 \x01(\t\x12\x10\n\x08quantity\x18\t \x01(\x01\x12\x0e\n\x06status\x18\n \x01(\t\x12\x0e\n\x06remark\x18\x0b \x01(\t\x12\x10\n\x08\x62\x61tch_id\x18\x0c \x01(\x04\x12\x0f\n\x07row_num\x18\r \x01(\x04\x12\x11\n\terror_msg\x18\x0e \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x12 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x13 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x14 \x01(\t\x12\x12\n\nupdated_by\x18\x15 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x16 \x01(\t\x12\x11\n\tdemand_id\x18\x17 \x01(\x04\x12\x13\n\x0b\x64\x65mand_code\x18\x18 \x01(\t\x12\x17\n\x0forder_type_code\x18\x19 \x01(\t\x12\x17\n\x0forder_type_name\x18\x1a \x01(\t\"\x9e\x01\n\x1eGetFDemandImportDetailResponse\x12\x34\n\x04rows\x18\x01 \x03(\x0b\x32&.franchisee_demand.FDemandImportDetail\x12\r\n\x05total\x18\x02 \x01(\x04\x12\x11\n\tfile_name\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x14\n\x0cupdated_name\x18\x05 \x01(\t\"\xd5\x01\n\x17GetFDemandImportRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06offset\x18\x03 \x01(\x04\x12\r\n\x05limit\x18\x04 \x01(\x04\x12\x11\n\tfile_name\x18\x05 \x01(\t\x12\x15\n\rinclude_total\x18\x06 \x01(\x08\x12\x13\n\x0bimport_type\x18\x07 \x01(\t\"\xcf\x02\n\x10\x46\x44\x65mandImportLog\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x11\n\tfile_name\x18\x04 \x01(\t\x12\x12\n\ncreated_by\x18\x05 \x01(\x04\x12\x12\n\nupdated_by\x18\x06 \x01(\x04\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\t \x01(\t\x12\x14\n\x0cupdated_name\x18\n \x01(\t\x12/\n\x0bimport_date\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0bimport_type\x18\x0c \x01(\t\"\\\n\x18GetFDemandImportResponse\x12\x31\n\x04rows\x18\x01 \x03(\x0b\x32#.franchisee_demand.FDemandImportLog\x12\r\n\x05total\x18\x02 \x01(\x04\"/\n\x1b\x41pproveFDemandImportRequest\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\".\n\x1c\x41pproveFDemandImportResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\".\n\x1a\x43\x61ncelFDemandImportRequest\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\"-\n\x1b\x43\x61ncelFDemandImportResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\xde\x02\n\x1aListFDemandToRemindRequest\x12\x16\n\x0egeo_region_ids\x18\x01 \x03(\x04\x12\x19\n\x11\x62ranch_region_ids\x18\x02 \x03(\x04\x12\x1c\n\x14\x66ranchise_region_ids\x18\x03 \x03(\x04\x12\x11\n\tstore_ids\x18\x04 \x03(\x04\x12\x0e\n\x06status\x18\x05 \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x10\n\x08type_ids\x18\x07 \x03(\x04\x12\x0e\n\x06offset\x18\x08 \x01(\x04\x12\r\n\x05limit\x18\t \x01(\x04\x12\x35\n\x11start_demand_date\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x33\n\x0f\x65nd_demand_date\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xe0\x01\n\x0e\x44\x65mandToRemind\x12\x12\n\nstore_code\x18\x01 \x01(\t\x12\x12\n\nstore_name\x18\x02 \x01(\t\x12\x17\n\x0fgeo_region_name\x18\x03 \x01(\t\x12\x1a\n\x12\x62ranch_region_name\x18\x04 \x01(\t\x12\x1d\n\x15\x66ranchise_region_name\x18\x05 \x01(\t\x12\x11\n\ttype_name\x18\x06 \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x08 \x01(\t\"]\n\x1bListFDemandToRemindResponse\x12/\n\x04rows\x18\x01 \x03(\x0b\x32!.franchisee_demand.DemandToRemind\x12\r\n\x05total\x18\x02 \x01(\x04\"\xa2\x02\n\x17\x42\x61tchDealFDemandRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0creceived_bys\x18\x03 \x03(\x04\x12\x11\n\tbus_types\x18\x04 \x03(\t\x12\x16\n\x0eorder_type_ids\x18\x05 \x03(\x04\x12\x13\n\x0bline_doc_id\x18\x06 \x01(\x04\x12\x14\n\x0c\x62\x61tch_method\x18\x08 \x01(\t\x12\x13\n\x0b\x65xclude_ids\x18\t \x03(\x04\x12\x13\n\x0binclude_ids\x18\n \x03(\x04\x12\x13\n\x0b\x63ompany_ids\x18\x0b \x03(\x04\"s\n\x18\x42\x61tchDealFDemandResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x32\n\x04msgs\x18\x03 \x03(\x0b\x32$.franchisee_demand.CheckOrderMessage\",\n\x17\x43heckFDemandTodoRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\"E\n\x0bTodoReceipt\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x0c\n\x04type\x18\x07 \x01(\t\"\x91\x01\n\x18\x43heckFDemandTodoResponse\x12\x0f\n\x07handler\x18\x01 \x01(\x08\x12\x31\n\treceiving\x18\x02 \x03(\x0b\x32\x1e.franchisee_demand.TodoReceipt\x12\x31\n\tstocktake\x18\x03 \x03(\x0b\x32\x1e.franchisee_demand.TodoReceipt\"\x90\x02\n\x13\x43heckProductMessage\x12\x14\n\x0cproduct_name\x18\x01 \x01(\t\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x0c\n\x04spec\x18\x03 \x01(\t\x12\x10\n\x08quantity\x18\x04 \x01(\x01\x12\x14\n\x0cmin_quantity\x18\x05 \x01(\x01\x12\x1a\n\x12increment_quantity\x18\x06 \x01(\x01\x12\x14\n\x0cmax_quantity\x18\x07 \x01(\x01\x12\x0c\n\x04unit\x18\x08 \x01(\t\x12\x11\n\ttax_price\x18\t \x01(\x01\x12\x0e\n\x06\x61mount\x18\n \x01(\x01\x12\x14\n\x0cstorage_type\x18\x0b \x01(\t\x12\n\n\x02id\x18\x0c \x01(\x04\x12\x12\n\nproduct_id\x18\r \x01(\x04\"\xf5\x01\n\x11\x43heckOrderMessage\x12\x0e\n\x06status\x18\x01 \x01(\t\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x15\n\rorder_type_id\x18\x04 \x01(\x04\x12\x17\n\x0f\x66ranchisee_name\x18\x05 \x01(\t\x12\x0e\n\x06\x61mount\x18\x06 \x01(\x01\x12/\n\x0b\x64\x65mand_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0b\n\x03msg\x18\t \x01(\t*\x96\x01\n\x06Status\x12\n\n\x06INITED\x10\x00\x12\x0b\n\x07PREPARE\x10\x01\x12\x0c\n\x08P_SUBMIT\x10\x02\x12\r\n\tSUBMITTED\x10\x04\x12\r\n\tR_APPROVE\x10\x03\x12\x0c\n\x08REJECTED\x10\x05\x12\r\n\tCANCELLED\x10\x06\x12\r\n\tCONFIRMED\x10\x07\x12\r\n\tAPPROVING\x10\n\x12\x0c\n\x08\x41PPROVED\x10\x0b*\x18\n\x04Type\x12\x07\n\x03\x46SD\x10\x00\x12\x07\n\x03\x46MD\x10\x01\x32\x9e\x13\n\x17\x46ranchiseeDemandService\x12\xaf\x01\n\x12\x43reateFDemandBatch\x12,.franchisee_demand.CreateFDemandBatchRequest\x1a-.franchisee_demand.CreateFDemandBatchResponse\"<\x82\xd3\xe4\x93\x02\x36\"1/api/v2/supply/frs-management/demand/create/batch:\x01*\x12\x8e\x01\n\x0bListFDemand\x12%.franchisee_demand.ListFDemandRequest\x1a&.franchisee_demand.ListFDemandResponse\"0\x82\xd3\xe4\x93\x02*\x12(/api/v2/supply/frs-management/frs-demand\x12\x93\x01\n\x0eGetFDemandById\x12(.franchisee_demand.GetFDemandByIdRequest\x1a\x19.franchisee_demand.Demand\"<\x82\xd3\xe4\x93\x02\x36\x12\x34/api/v2/supply/frs-management/frs-demand/{demand_id}\x12\xa4\x01\n\x0eGetHistoryById\x12(.franchisee_demand.GetHistoryByIdRequest\x1a\".franchisee_demand.HistoryResponse\"D\x82\xd3\xe4\x93\x02>\x12</api/v2/supply/frs-management/frs-demand/{demand_id}/history\x12\xa7\x01\n\x0fGetProductsById\x12).franchisee_demand.GetProductsByIdRequest\x1a#.franchisee_demand.ProductsResponse\"D\x82\xd3\xe4\x93\x02>\x12</api/v2/supply/frs-management/frs-demand/{demand_id}/product\x12\xad\x01\n\x0eUpdateProducts\x12\'.franchisee_demand.UpdateProductRequest\x1a).franchisee_demand.UpdateProductsResponse\"G\x82\xd3\xe4\x93\x02\x41\x1a</api/v2/supply/frs-management/frs-demand/{demand_id}/product:\x01*\x12\xab\x01\n\x0f\x44\x65\x61lFDemandById\x12).franchisee_demand.DealFDemandByIdRequest\x1a*.franchisee_demand.DealFDemandByIdResponse\"A\x82\xd3\xe4\x93\x02;\x1a\x36/api/v2/supply/frs-management/frs-demand/deal/{action}:\x01*\x12\xb9\x01\n\x16GetProductDistribution\x12\x30.franchisee_demand.GetProductDistributionRequest\x1a\x31.franchisee_demand.GetProductDistributionResponse\":\x82\xd3\xe4\x93\x02\x34\x12\x32/api/v2/supply/frs-management/product/distribution\x12\xaf\x01\n\x11\x41\x64\x64\x46\x44\x65mandProduct\x12+.franchisee_demand.AddFDemandProductRequest\x1a,.franchisee_demand.AddFDemandProductResponse\"?\x82\xd3\xe4\x93\x02\x39\"4/api/v2/supply/frs-management/frs-demand/add/product:\x01*\x12\xbf\x01\n\x15ImportFDemandProducts\x12/.franchisee_demand.ImportFDemandProductsRequest\x1a\x30.franchisee_demand.ImportFDemandProductsResponse\"C\x82\xd3\xe4\x93\x02=\"8/api/v2/supply/frs-management/frs-demand/import/products:\x01*\x12\xad\x01\n\x11\x42\x61tchResetFDemand\x12*.franchisee_demand.BatchDealFDemandRequest\x1a+.franchisee_demand.BatchDealFDemandResponse\"?\x82\xd3\xe4\x93\x02\x39\x1a\x34/api/v2/supply/frs-management/frs-demand/batch/reset:\x01*\x12\xb4\x01\n\x13ListFDemandToRemind\x12-.franchisee_demand.ListFDemandToRemindRequest\x1a..franchisee_demand.ListFDemandToRemindResponse\">\x82\xd3\xe4\x93\x02\x38\"6/api/v2/supply/frs-management/franchisee/demand/remind\x12\xb1\x01\n\x13\x42\x61tchConfirmFDemand\x12*.franchisee_demand.BatchDealFDemandRequest\x1a+.franchisee_demand.BatchDealFDemandResponse\"A\x82\xd3\xe4\x93\x02;\x1a\x36/api/v2/supply/frs-management/frs-demand/batch/confirm:\x01*\x12\xaf\x01\n\x10\x43heckFDemandTodo\x12*.franchisee_demand.CheckFDemandTodoRequest\x1a+.franchisee_demand.CheckFDemandTodoResponse\"B\x82\xd3\xe4\x93\x02<\x12:/api/v2/supply/frs-management/frs-demand/{demand_id}/checkb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])

_STATUS = _descriptor.EnumDescriptor(
  name='Status',
  full_name='franchisee_demand.Status',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='INITED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PREPARE', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='P_SUBMIT', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=3, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='R_APPROVE', index=4, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REJECTED', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=7, number=7,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVING', index=8, number=10,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVED', index=9, number=11,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=12663,
  serialized_end=12813,
)
_sym_db.RegisterEnumDescriptor(_STATUS)

Status = enum_type_wrapper.EnumTypeWrapper(_STATUS)
_TYPE = _descriptor.EnumDescriptor(
  name='Type',
  full_name='franchisee_demand.Type',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='FSD', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='FMD', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=12815,
  serialized_end=12839,
)
_sym_db.RegisterEnumDescriptor(_TYPE)

Type = enum_type_wrapper.EnumTypeWrapper(_TYPE)
INITED = 0
PREPARE = 1
P_SUBMIT = 2
SUBMITTED = 4
R_APPROVE = 3
REJECTED = 5
CANCELLED = 6
CONFIRMED = 7
APPROVING = 10
APPROVED = 11
FSD = 0
FMD = 1



_CREATEFDEMANDBATCHREQUEST = _descriptor.Descriptor(
  name='CreateFDemandBatchRequest',
  full_name='franchisee_demand.CreateFDemandBatchRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='franchisee_demand.CreateFDemandBatchRequest.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batches', full_name='franchisee_demand.CreateFDemandBatchRequest.batches', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=124,
  serialized_end=227,
)


_CREATEFDEMANDREQUEST = _descriptor.Descriptor(
  name='CreateFDemandRequest',
  full_name='franchisee_demand.CreateFDemandRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='franchisee_demand.CreateFDemandRequest.arrival_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='franchisee_demand.CreateFDemandRequest.demand_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='franchisee_demand.CreateFDemandRequest.received_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_by', full_name='franchisee_demand.CreateFDemandRequest.distribute_by', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_demand.CreateFDemandRequest.status', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='franchisee_demand.CreateFDemandRequest.products', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='franchisee_demand.CreateFDemandRequest.franchisee_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='franchisee_demand.CreateFDemandRequest.order_type_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_demand.CreateFDemandRequest.remark', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='franchisee_demand.CreateFDemandRequest.type', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='franchisee_demand.CreateFDemandRequest.reason', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bus_type', full_name='franchisee_demand.CreateFDemandRequest.bus_type', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_way', full_name='franchisee_demand.CreateFDemandRequest.payment_way', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trade_company', full_name='franchisee_demand.CreateFDemandRequest.trade_company', index=13,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='franchisee_demand.CreateFDemandRequest.extends', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=230,
  serialized_end=628,
)


_CREATEFDEMANDBATCHRESPONSE = _descriptor.Descriptor(
  name='CreateFDemandBatchResponse',
  full_name='franchisee_demand.CreateFDemandBatchResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='franchisee_demand.CreateFDemandBatchResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=630,
  serialized_end=674,
)


_LISTFDEMANDREQUEST = _descriptor.Descriptor(
  name='ListFDemandRequest',
  full_name='franchisee_demand.ListFDemandRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='franchisee_demand.ListFDemandRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='franchisee_demand.ListFDemandRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_demand.ListFDemandRequest.status', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_bys', full_name='franchisee_demand.ListFDemandRequest.received_bys', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='franchisee_demand.ListFDemandRequest.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='types', full_name='franchisee_demand.ListFDemandRequest.types', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='franchisee_demand.ListFDemandRequest.sub_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='chain_type', full_name='franchisee_demand.ListFDemandRequest.chain_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bus_types', full_name='franchisee_demand.ListFDemandRequest.bus_types', index=8,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_demand.ListFDemandRequest.offset', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_demand.ListFDemandRequest.limit', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='franchisee_demand.ListFDemandRequest.order', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='franchisee_demand.ListFDemandRequest.sort', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='franchisee_demand.ListFDemandRequest.include_total', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='franchisee_demand.ListFDemandRequest.ids', index=14,
      number=15, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='franchisee_demand.ListFDemandRequest.product_ids', index=15,
      number=16, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_ids', full_name='franchisee_demand.ListFDemandRequest.franchisee_ids', index=16,
      number=17, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_ways', full_name='franchisee_demand.ListFDemandRequest.payment_ways', index=17,
      number=18, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_ids', full_name='franchisee_demand.ListFDemandRequest.order_type_ids', index=18,
      number=19, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='line_doc_id', full_name='franchisee_demand.ListFDemandRequest.line_doc_id', index=19,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_demand.ListFDemandRequest.lan', index=20,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=677,
  serialized_end=1136,
)


_LISTFDEMANDRESPONSE = _descriptor.Descriptor(
  name='ListFDemandResponse',
  full_name='franchisee_demand.ListFDemandResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_demand.ListFDemandResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_demand.ListFDemandResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1138,
  serialized_end=1215,
)


_DEMAND_ORDER = _descriptor.Descriptor(
  name='Order',
  full_name='franchisee_demand.Demand.Order',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='franchisee_demand.Demand.Order.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='franchisee_demand.Demand.Order.order_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2466,
  serialized_end=2511,
)

_DEMAND_REFUND = _descriptor.Descriptor(
  name='Refund',
  full_name='franchisee_demand.Demand.Refund',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='refund_id', full_name='franchisee_demand.Demand.Refund.refund_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_code', full_name='franchisee_demand.Demand.Refund.refund_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2513,
  serialized_end=2561,
)

_DEMAND_ORDERTYPETIME = _descriptor.Descriptor(
  name='OrderTypeTime',
  full_name='franchisee_demand.Demand.OrderTypeTime',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_time', full_name='franchisee_demand.Demand.OrderTypeTime.order_time', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='audit_time', full_name='franchisee_demand.Demand.OrderTypeTime.audit_time', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2563,
  serialized_end=2618,
)

_DEMAND = _descriptor.Descriptor(
  name='Demand',
  full_name='franchisee_demand.Demand',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_demand.Demand.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='franchisee_demand.Demand.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='franchisee_demand.Demand.batch_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='franchisee_demand.Demand.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='franchisee_demand.Demand.type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='franchisee_demand.Demand.sub_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bus_type', full_name='franchisee_demand.Demand.bus_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='franchisee_demand.Demand.received_by', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_code', full_name='franchisee_demand.Demand.received_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_name', full_name='franchisee_demand.Demand.received_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_by', full_name='franchisee_demand.Demand.distribute_by', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='franchisee_demand.Demand.store_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='franchisee_demand.Demand.franchisee_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_code', full_name='franchisee_demand.Demand.franchisee_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_name', full_name='franchisee_demand.Demand.franchisee_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='franchisee_demand.Demand.demand_date', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='franchisee_demand.Demand.arrival_date', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_demand.Demand.status', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='franchisee_demand.Demand.process_status', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='franchisee_demand.Demand.reject_reason', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_demand.Demand.remark', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='franchisee_demand.Demand.reason', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='franchisee_demand.Demand.created_at', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='franchisee_demand.Demand.updated_at', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_demand.Demand.created_by', index=24,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='franchisee_demand.Demand.created_name', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='franchisee_demand.Demand.updated_by', index=26,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='franchisee_demand.Demand.updated_name', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='franchisee_demand.Demand.review_by', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='has_product', full_name='franchisee_demand.Demand.has_product', index=29,
      number=30, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price_tax', full_name='franchisee_demand.Demand.sum_price_tax', index=30,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_tax', full_name='franchisee_demand.Demand.sum_tax', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pay_amount', full_name='franchisee_demand.Demand.pay_amount', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_demand.Demand.attachments', index=33,
      number=34, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='franchisee_demand.Demand.extends', index=34,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orders', full_name='franchisee_demand.Demand.orders', index=35,
      number=36, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_way', full_name='franchisee_demand.Demand.payment_way', index=36,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='chain_type', full_name='franchisee_demand.Demand.chain_type', index=37,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='franchisee_demand.Demand.is_adjust', index=38,
      number=39, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_amount', full_name='franchisee_demand.Demand.confirm_amount', index=39,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='franchisee_demand.Demand.order_type_id', index=40,
      number=41, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_name', full_name='franchisee_demand.Demand.order_type_name', index=41,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_code', full_name='franchisee_demand.Demand.order_type_code', index=42,
      number=43, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='franchisee_demand.Demand.sales_amount', index=43,
      number=44, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_sales_amount', full_name='franchisee_demand.Demand.confirm_sales_amount', index=44,
      number=45, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refunds', full_name='franchisee_demand.Demand.refunds', index=45,
      number=46, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_amount', full_name='franchisee_demand.Demand.approve_amount', index=46,
      number=47, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_sales_amount', full_name='franchisee_demand.Demand.approve_sales_amount', index=47,
      number=48, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_time', full_name='franchisee_demand.Demand.order_type_time', index=48,
      number=49, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_DEMAND_ORDER, _DEMAND_REFUND, _DEMAND_ORDERTYPETIME, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1218,
  serialized_end=2618,
)


_GETFDEMANDBYIDREQUEST = _descriptor.Descriptor(
  name='GetFDemandByIdRequest',
  full_name='franchisee_demand.GetFDemandByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='franchisee_demand.GetFDemandByIdRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2620,
  serialized_end=2662,
)


_GETHISTORYBYIDREQUEST = _descriptor.Descriptor(
  name='GetHistoryByIdRequest',
  full_name='franchisee_demand.GetHistoryByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='franchisee_demand.GetHistoryByIdRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2664,
  serialized_end=2706,
)


_HISTORYRESPONSE = _descriptor.Descriptor(
  name='HistoryResponse',
  full_name='franchisee_demand.HistoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_demand.HistoryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_demand.HistoryResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2708,
  serialized_end=2782,
)


_PRODUCTHISTORY = _descriptor.Descriptor(
  name='ProductHistory',
  full_name='franchisee_demand.ProductHistory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_demand.ProductHistory.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='franchisee_demand.ProductHistory.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='franchisee_demand.ProductHistory.demand_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trace_id', full_name='franchisee_demand.ProductHistory.trace_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='franchisee_demand.ProductHistory.action', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action_name', full_name='franchisee_demand.ProductHistory.action_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='franchisee_demand.ProductHistory.product_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='franchisee_demand.ProductHistory.product_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='franchisee_demand.ProductHistory.product_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='franchisee_demand.ProductHistory.unit_id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='franchisee_demand.ProductHistory.unit_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='franchisee_demand.ProductHistory.tax_price', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='franchisee_demand.ProductHistory.sales_price', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='franchisee_demand.ProductHistory.quantity', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='franchisee_demand.ProductHistory.amount', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='franchisee_demand.ProductHistory.sales_amount', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_quantity', full_name='franchisee_demand.ProductHistory.approve_quantity', index=16,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_amount', full_name='franchisee_demand.ProductHistory.approve_amount', index=17,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_sales_amount', full_name='franchisee_demand.ProductHistory.approve_sales_amount', index=18,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_quantity', full_name='franchisee_demand.ProductHistory.confirm_quantity', index=19,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_amount', full_name='franchisee_demand.ProductHistory.confirm_amount', index=20,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_sales_amount', full_name='franchisee_demand.ProductHistory.confirm_sales_amount', index=21,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_days', full_name='franchisee_demand.ProductHistory.arrival_days', index=22,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='franchisee_demand.ProductHistory.distribution_type', index=23,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_by', full_name='franchisee_demand.ProductHistory.distribute_by', index=24,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_name', full_name='franchisee_demand.ProductHistory.distribute_name', index=25,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_demand.ProductHistory.created_by', index=26,
      number=28, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='franchisee_demand.ProductHistory.created_at', index=27,
      number=29, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='franchisee_demand.ProductHistory.created_name', index=28,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2785,
  serialized_end=3450,
)


_HISTORY = _descriptor.Descriptor(
  name='History',
  full_name='franchisee_demand.History',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_demand.History.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='franchisee_demand.History.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='franchisee_demand.History.demand_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='franchisee_demand.History.action', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action_name', full_name='franchisee_demand.History.action_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_demand.History.created_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='franchisee_demand.History.created_at', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='franchisee_demand.History.created_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='platform', full_name='franchisee_demand.History.platform', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_status', full_name='franchisee_demand.History.start_status', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_status', full_name='franchisee_demand.History.end_status', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trace_id', full_name='franchisee_demand.History.trace_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='platform_name', full_name='franchisee_demand.History.platform_name', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='details', full_name='franchisee_demand.History.details', index=13,
      number=15, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3453,
  serialized_end=3793,
)


_GETPRODUCTSBYIDREQUEST = _descriptor.Descriptor(
  name='GetProductsByIdRequest',
  full_name='franchisee_demand.GetProductsByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='franchisee_demand.GetProductsByIdRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_demand.GetProductsByIdRequest.limit', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_demand.GetProductsByIdRequest.offset', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='franchisee_demand.GetProductsByIdRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='franchisee_demand.GetProductsByIdRequest.order', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='franchisee_demand.GetProductsByIdRequest.sort', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expand', full_name='franchisee_demand.GetProductsByIdRequest.expand', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3796,
  serialized_end=3938,
)


_PRODUCTSRESPONSE = _descriptor.Descriptor(
  name='ProductsResponse',
  full_name='franchisee_demand.ProductsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_demand.ProductsResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_demand.ProductsResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3940,
  serialized_end=4015,
)


_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='franchisee_demand.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_demand.Product.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='franchisee_demand.Product.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='franchisee_demand.Product.product_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='franchisee_demand.Product.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='org_product_id', full_name='franchisee_demand.Product.org_product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='org_product_code', full_name='franchisee_demand.Product.org_product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='org_product_name', full_name='franchisee_demand.Product.org_product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='franchisee_demand.Product.category_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='franchisee_demand.Product.category_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='franchisee_demand.Product.product_spec', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='franchisee_demand.Product.unit_id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='franchisee_demand.Product.unit_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='franchisee_demand.Product.unit_spec', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='franchisee_demand.Product.accounting_unit_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='franchisee_demand.Product.accounting_unit_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='franchisee_demand.Product.accounting_unit_spec', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='franchisee_demand.Product.unit_rate', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='franchisee_demand.Product.quantity', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='franchisee_demand.Product.accounting_quantity', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_quantity', full_name='franchisee_demand.Product.min_quantity', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_quantity', full_name='franchisee_demand.Product.max_quantity', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_quantity', full_name='franchisee_demand.Product.increment_quantity', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='franchisee_demand.Product.tax_price', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='franchisee_demand.Product.cost_price', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='franchisee_demand.Product.tax_rate', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='franchisee_demand.Product.amount', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_days', full_name='franchisee_demand.Product.arrival_days', index=26,
      number=27, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='franchisee_demand.Product.distribution_type', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_by', full_name='franchisee_demand.Product.distribute_by', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_name', full_name='franchisee_demand.Product.distribute_name', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_demand.Product.status', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='yesterday_sales', full_name='franchisee_demand.Product.yesterday_sales', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_qty', full_name='franchisee_demand.Product.inventory_qty', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='on_way_qty', full_name='franchisee_demand.Product.on_way_qty', index=33,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_delete', full_name='franchisee_demand.Product.is_delete', index=34,
      number=35, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='franchisee_demand.Product.created_at', index=35,
      number=36, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='franchisee_demand.Product.updated_at', index=36,
      number=37, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_demand.Product.created_by', index=37,
      number=38, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='franchisee_demand.Product.created_name', index=38,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='franchisee_demand.Product.updated_by', index=39,
      number=40, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='franchisee_demand.Product.updated_name', index=40,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='franchisee_demand.Product.demand_id', index=41,
      number=42, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='franchisee_demand.Product.partner_id', index=42,
      number=43, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_order', full_name='franchisee_demand.Product.allow_order', index=43,
      number=44, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_quantity', full_name='franchisee_demand.Product.confirm_quantity', index=44,
      number=45, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_amount', full_name='franchisee_demand.Product.confirm_amount', index=45,
      number=46, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='franchisee_demand.Product.extends', index=46,
      number=47, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='franchisee_demand.Product.storage_type', index=47,
      number=48, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='franchisee_demand.Product.sales_price', index=48,
      number=49, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='franchisee_demand.Product.sales_amount', index=49,
      number=50, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_sales_amount', full_name='franchisee_demand.Product.confirm_sales_amount', index=50,
      number=51, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_quantity', full_name='franchisee_demand.Product.approve_quantity', index=51,
      number=52, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_amount', full_name='franchisee_demand.Product.approve_amount', index=52,
      number=53, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_sales_amount', full_name='franchisee_demand.Product.approve_sales_amount', index=53,
      number=54, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_confirm_qty', full_name='franchisee_demand.Product.is_confirm_qty', index=54,
      number=55, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_approve_qty', full_name='franchisee_demand.Product.is_approve_qty', index=55,
      number=56, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation_products', full_name='franchisee_demand.Product.relation_products', index=56,
      number=57, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='franchisee_demand.Product.product_type', index=57,
      number=58, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ratio', full_name='franchisee_demand.Product.ratio', index=58,
      number=59, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='configure', full_name='franchisee_demand.Product.configure', index=59,
      number=60, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4018,
  serialized_end=5440,
)


_UPDATEPRODUCTREQUEST = _descriptor.Descriptor(
  name='UpdateProductRequest',
  full_name='franchisee_demand.UpdateProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='franchisee_demand.UpdateProductRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_demand.UpdateProductRequest.remark', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_demand.UpdateProductRequest.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='franchisee_demand.UpdateProductRequest.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5442,
  serialized_end=5567,
)


_UPDATEPRODUCT = _descriptor.Descriptor(
  name='UpdateProduct',
  full_name='franchisee_demand.UpdateProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_demand.UpdateProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='franchisee_demand.UpdateProduct.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='franchisee_demand.UpdateProduct.product_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='franchisee_demand.UpdateProduct.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='org_product_id', full_name='franchisee_demand.UpdateProduct.org_product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='org_product_code', full_name='franchisee_demand.UpdateProduct.org_product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='org_product_name', full_name='franchisee_demand.UpdateProduct.org_product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='franchisee_demand.UpdateProduct.category_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='franchisee_demand.UpdateProduct.category_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='franchisee_demand.UpdateProduct.product_spec', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='franchisee_demand.UpdateProduct.unit_id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='franchisee_demand.UpdateProduct.unit_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='franchisee_demand.UpdateProduct.unit_spec', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='franchisee_demand.UpdateProduct.accounting_unit_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='franchisee_demand.UpdateProduct.accounting_unit_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='franchisee_demand.UpdateProduct.accounting_unit_spec', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='franchisee_demand.UpdateProduct.unit_rate', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='franchisee_demand.UpdateProduct.quantity', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='franchisee_demand.UpdateProduct.accounting_quantity', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_quantity', full_name='franchisee_demand.UpdateProduct.min_quantity', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_quantity', full_name='franchisee_demand.UpdateProduct.max_quantity', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_quantity', full_name='franchisee_demand.UpdateProduct.increment_quantity', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='franchisee_demand.UpdateProduct.tax_price', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='franchisee_demand.UpdateProduct.cost_price', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='franchisee_demand.UpdateProduct.tax_rate', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='franchisee_demand.UpdateProduct.amount', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_days', full_name='franchisee_demand.UpdateProduct.arrival_days', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='franchisee_demand.UpdateProduct.distribution_type', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_by', full_name='franchisee_demand.UpdateProduct.distribute_by', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_name', full_name='franchisee_demand.UpdateProduct.distribute_name', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_demand.UpdateProduct.status', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='yesterday_sales', full_name='franchisee_demand.UpdateProduct.yesterday_sales', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_qty', full_name='franchisee_demand.UpdateProduct.inventory_qty', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='on_way_qty', full_name='franchisee_demand.UpdateProduct.on_way_qty', index=33,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_delete', full_name='franchisee_demand.UpdateProduct.is_delete', index=34,
      number=35, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='franchisee_demand.UpdateProduct.created_at', index=35,
      number=36, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='franchisee_demand.UpdateProduct.updated_at', index=36,
      number=37, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_demand.UpdateProduct.created_by', index=37,
      number=38, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='franchisee_demand.UpdateProduct.created_name', index=38,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='franchisee_demand.UpdateProduct.updated_by', index=39,
      number=40, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='franchisee_demand.UpdateProduct.updated_name', index=40,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='franchisee_demand.UpdateProduct.demand_id', index=41,
      number=42, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='franchisee_demand.UpdateProduct.partner_id', index=42,
      number=43, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_order', full_name='franchisee_demand.UpdateProduct.allow_order', index=43,
      number=44, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_quantity', full_name='franchisee_demand.UpdateProduct.confirm_quantity', index=44,
      number=45, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_amount', full_name='franchisee_demand.UpdateProduct.confirm_amount', index=45,
      number=46, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='franchisee_demand.UpdateProduct.extends', index=46,
      number=47, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='franchisee_demand.UpdateProduct.storage_type', index=47,
      number=48, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='franchisee_demand.UpdateProduct.sales_price', index=48,
      number=49, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='franchisee_demand.UpdateProduct.sales_amount', index=49,
      number=50, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_sales_amount', full_name='franchisee_demand.UpdateProduct.confirm_sales_amount', index=50,
      number=51, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_quantity', full_name='franchisee_demand.UpdateProduct.approve_quantity', index=51,
      number=52, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_amount', full_name='franchisee_demand.UpdateProduct.approve_amount', index=52,
      number=53, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_sales_amount', full_name='franchisee_demand.UpdateProduct.approve_sales_amount', index=53,
      number=54, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_confirm_qty', full_name='franchisee_demand.UpdateProduct.is_confirm_qty', index=54,
      number=55, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_approve_qty', full_name='franchisee_demand.UpdateProduct.is_approve_qty', index=55,
      number=56, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation_products', full_name='franchisee_demand.UpdateProduct.relation_products', index=56,
      number=57, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ratio', full_name='franchisee_demand.UpdateProduct.ratio', index=57,
      number=58, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='configure', full_name='franchisee_demand.UpdateProduct.configure', index=58,
      number=59, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5570,
  serialized_end=6982,
)


_UPDATEPRODUCTSRESPONSE = _descriptor.Descriptor(
  name='UpdateProductsResponse',
  full_name='franchisee_demand.UpdateProductsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='franchisee_demand.UpdateProductsResponse.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msgs', full_name='franchisee_demand.UpdateProductsResponse.msgs', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='not_allocate_products', full_name='franchisee_demand.UpdateProductsResponse.not_allocate_products', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6985,
  serialized_end=7113,
)


_DEALFDEMANDBYIDREQUEST = _descriptor.Descriptor(
  name='DealFDemandByIdRequest',
  full_name='franchisee_demand.DealFDemandByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_ids', full_name='franchisee_demand.DealFDemandByIdRequest.demand_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='franchisee_demand.DealFDemandByIdRequest.action', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_demand.DealFDemandByIdRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_demand.DealFDemandByIdRequest.attachments', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='franchisee_demand.DealFDemandByIdRequest.reject_reason', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7115,
  serialized_end=7235,
)


_DEALFDEMANDBYIDRESPONSE = _descriptor.Descriptor(
  name='DealFDemandByIdResponse',
  full_name='franchisee_demand.DealFDemandByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='franchisee_demand.DealFDemandByIdResponse.result', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msgs', full_name='franchisee_demand.DealFDemandByIdResponse.msgs', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7237,
  serialized_end=7330,
)


_GETPRODUCTDISTRIBUTIONREQUEST = _descriptor.Descriptor(
  name='GetProductDistributionRequest',
  full_name='franchisee_demand.GetProductDistributionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='franchisee_demand.GetProductDistributionRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='franchisee_demand.GetProductDistributionRequest.product_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7332,
  serialized_end=7403,
)


_GETPRODUCTDISTRIBUTIONRESPONSE_PRODETAIL = _descriptor.Descriptor(
  name='ProDetail',
  full_name='franchisee_demand.GetProductDistributionResponse.ProDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='franchisee_demand.GetProductDistributionResponse.ProDetail.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dists', full_name='franchisee_demand.GetProductDistributionResponse.ProDetail.dists', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7619,
  serialized_end=7726,
)

_GETPRODUCTDISTRIBUTIONRESPONSE_DTRDETAIL = _descriptor.Descriptor(
  name='DtrDetail',
  full_name='franchisee_demand.GetProductDistributionResponse.DtrDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='distribute_id', full_name='franchisee_demand.GetProductDistributionResponse.DtrDetail.distribute_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_name', full_name='franchisee_demand.GetProductDistributionResponse.DtrDetail.distribute_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_days', full_name='franchisee_demand.GetProductDistributionResponse.DtrDetail.arrival_days', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='franchisee_demand.GetProductDistributionResponse.DtrDetail.qty', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_qty', full_name='franchisee_demand.GetProductDistributionResponse.DtrDetail.order_qty', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='franchisee_demand.GetProductDistributionResponse.DtrDetail.distribution_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7729,
  serialized_end=7869,
)

_GETPRODUCTDISTRIBUTIONRESPONSE = _descriptor.Descriptor(
  name='GetProductDistributionResponse',
  full_name='franchisee_demand.GetProductDistributionResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_demand.GetProductDistributionResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_centers', full_name='franchisee_demand.GetProductDistributionResponse.distribute_centers', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_demand.GetProductDistributionResponse.total', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_GETPRODUCTDISTRIBUTIONRESPONSE_PRODETAIL, _GETPRODUCTDISTRIBUTIONRESPONSE_DTRDETAIL, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7406,
  serialized_end=7869,
)


_ADDFDEMANDPRODUCTREQUEST_ADDPRODUCT = _descriptor.Descriptor(
  name='AddProduct',
  full_name='franchisee_demand.AddFDemandProductRequest.AddProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='franchisee_demand.AddFDemandProductRequest.AddProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_quantity', full_name='franchisee_demand.AddFDemandProductRequest.AddProduct.confirm_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='franchisee_demand.AddFDemandProductRequest.AddProduct.tax_price', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='franchisee_demand.AddFDemandProductRequest.AddProduct.tax_rate', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='franchisee_demand.AddFDemandProductRequest.AddProduct.sales_price', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_days', full_name='franchisee_demand.AddFDemandProductRequest.AddProduct.arrival_days', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_quantity', full_name='franchisee_demand.AddFDemandProductRequest.AddProduct.min_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_quantity', full_name='franchisee_demand.AddFDemandProductRequest.AddProduct.max_quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_quantity', full_name='franchisee_demand.AddFDemandProductRequest.AddProduct.increment_quantity', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='franchisee_demand.AddFDemandProductRequest.AddProduct.extends', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation_products', full_name='franchisee_demand.AddFDemandProductRequest.AddProduct.relation_products', index=10,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ratio', full_name='franchisee_demand.AddFDemandProductRequest.AddProduct.ratio', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='configure', full_name='franchisee_demand.AddFDemandProductRequest.AddProduct.configure', index=12,
      number=13, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7994,
  serialized_end=8338,
)

_ADDFDEMANDPRODUCTREQUEST = _descriptor.Descriptor(
  name='AddFDemandProductRequest',
  full_name='franchisee_demand.AddFDemandProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='franchisee_demand.AddFDemandProductRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='franchisee_demand.AddFDemandProductRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_ADDFDEMANDPRODUCTREQUEST_ADDPRODUCT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7872,
  serialized_end=8338,
)


_ADDFDEMANDPRODUCTRESPONSE = _descriptor.Descriptor(
  name='AddFDemandProductResponse',
  full_name='franchisee_demand.AddFDemandProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='franchisee_demand.AddFDemandProductResponse.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8340,
  serialized_end=8386,
)


_IMPORTFDEMANDPRODUCTSREQUEST = _descriptor.Descriptor(
  name='ImportFDemandProductsRequest',
  full_name='franchisee_demand.ImportFDemandProductsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='import_type', full_name='franchisee_demand.ImportFDemandProductsRequest.import_type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='franchisee_demand.ImportFDemandProductsRequest.batch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_demand.ImportFDemandProductsRequest.rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8388,
  serialized_end=8504,
)


_IMPORTDETAIL = _descriptor.Descriptor(
  name='ImportDetail',
  full_name='franchisee_demand.ImportDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='franchisee_demand.ImportDetail.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='franchisee_demand.ImportDetail.products', index=1,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8506,
  serialized_end=8591,
)


_IMPORTPRODUCT = _descriptor.Descriptor(
  name='ImportProduct',
  full_name='franchisee_demand.ImportProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='franchisee_demand.ImportProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='franchisee_demand.ImportProduct.category_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='franchisee_demand.ImportProduct.unit_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='franchisee_demand.ImportProduct.accounting_unit_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='franchisee_demand.ImportProduct.unit_rate', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='franchisee_demand.ImportProduct.quantity', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_tax_price', full_name='franchisee_demand.ImportProduct.order_tax_price', index=6,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sale_tax_price', full_name='franchisee_demand.ImportProduct.sale_tax_price', index=7,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='franchisee_demand.ImportProduct.tax_rate', index=8,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_quantity', full_name='franchisee_demand.ImportProduct.min_quantity', index=9,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_quantity', full_name='franchisee_demand.ImportProduct.max_quantity', index=10,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_quantity', full_name='franchisee_demand.ImportProduct.increment_quantity', index=11,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_days', full_name='franchisee_demand.ImportProduct.arrival_days', index=12,
      number=18, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation_products', full_name='franchisee_demand.ImportProduct.relation_products', index=13,
      number=57, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='franchisee_demand.ImportProduct.product_type', index=14,
      number=58, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ratio', full_name='franchisee_demand.ImportProduct.ratio', index=15,
      number=59, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='configure', full_name='franchisee_demand.ImportProduct.configure', index=16,
      number=60, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8594,
  serialized_end=9010,
)


_IMPORTFDEMANDPRODUCTSRESPONSE = _descriptor.Descriptor(
  name='ImportFDemandProductsResponse',
  full_name='franchisee_demand.ImportFDemandProductsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='franchisee_demand.ImportFDemandProductsResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='franchisee_demand.ImportFDemandProductsResponse.description', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9012,
  serialized_end=9080,
)


_GETFDEMANDIMPORTDETAILREQUEST = _descriptor.Descriptor(
  name='GetFDemandImportDetailRequest',
  full_name='franchisee_demand.GetFDemandImportDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='franchisee_demand.GetFDemandImportDetailRequest.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_demand.GetFDemandImportDetailRequest.offset', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_demand.GetFDemandImportDetailRequest.limit', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='franchisee_demand.GetFDemandImportDetailRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9082,
  serialized_end=9185,
)


_FDEMANDIMPORTDETAIL = _descriptor.Descriptor(
  name='FDemandImportDetail',
  full_name='franchisee_demand.FDemandImportDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_demand.FDemandImportDetail.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='franchisee_demand.FDemandImportDetail.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='franchisee_demand.FDemandImportDetail.store_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='franchisee_demand.FDemandImportDetail.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='franchisee_demand.FDemandImportDetail.store_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='franchisee_demand.FDemandImportDetail.product_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='franchisee_demand.FDemandImportDetail.product_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='franchisee_demand.FDemandImportDetail.product_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='franchisee_demand.FDemandImportDetail.quantity', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_demand.FDemandImportDetail.status', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_demand.FDemandImportDetail.remark', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='franchisee_demand.FDemandImportDetail.batch_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='row_num', full_name='franchisee_demand.FDemandImportDetail.row_num', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error_msg', full_name='franchisee_demand.FDemandImportDetail.error_msg', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='franchisee_demand.FDemandImportDetail.demand_date', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='franchisee_demand.FDemandImportDetail.created_at', index=15,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='franchisee_demand.FDemandImportDetail.updated_at', index=16,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_demand.FDemandImportDetail.created_by', index=17,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='franchisee_demand.FDemandImportDetail.created_name', index=18,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='franchisee_demand.FDemandImportDetail.updated_by', index=19,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='franchisee_demand.FDemandImportDetail.updated_name', index=20,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='franchisee_demand.FDemandImportDetail.demand_id', index=21,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_code', full_name='franchisee_demand.FDemandImportDetail.demand_code', index=22,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_code', full_name='franchisee_demand.FDemandImportDetail.order_type_code', index=23,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_name', full_name='franchisee_demand.FDemandImportDetail.order_type_name', index=24,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9188,
  serialized_end=9786,
)


_GETFDEMANDIMPORTDETAILRESPONSE = _descriptor.Descriptor(
  name='GetFDemandImportDetailResponse',
  full_name='franchisee_demand.GetFDemandImportDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_demand.GetFDemandImportDetailResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_demand.GetFDemandImportDetailResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_name', full_name='franchisee_demand.GetFDemandImportDetailResponse.file_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_demand.GetFDemandImportDetailResponse.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='franchisee_demand.GetFDemandImportDetailResponse.updated_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9789,
  serialized_end=9947,
)


_GETFDEMANDIMPORTREQUEST = _descriptor.Descriptor(
  name='GetFDemandImportRequest',
  full_name='franchisee_demand.GetFDemandImportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='franchisee_demand.GetFDemandImportRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='franchisee_demand.GetFDemandImportRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_demand.GetFDemandImportRequest.offset', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_demand.GetFDemandImportRequest.limit', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_name', full_name='franchisee_demand.GetFDemandImportRequest.file_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='franchisee_demand.GetFDemandImportRequest.include_total', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='import_type', full_name='franchisee_demand.GetFDemandImportRequest.import_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9950,
  serialized_end=10163,
)


_FDEMANDIMPORTLOG = _descriptor.Descriptor(
  name='FDemandImportLog',
  full_name='franchisee_demand.FDemandImportLog',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_demand.FDemandImportLog.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='franchisee_demand.FDemandImportLog.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_demand.FDemandImportLog.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_name', full_name='franchisee_demand.FDemandImportLog.file_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_demand.FDemandImportLog.created_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='franchisee_demand.FDemandImportLog.updated_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='franchisee_demand.FDemandImportLog.created_at', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='franchisee_demand.FDemandImportLog.updated_at', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='franchisee_demand.FDemandImportLog.created_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='franchisee_demand.FDemandImportLog.updated_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='import_date', full_name='franchisee_demand.FDemandImportLog.import_date', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='import_type', full_name='franchisee_demand.FDemandImportLog.import_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10166,
  serialized_end=10501,
)


_GETFDEMANDIMPORTRESPONSE = _descriptor.Descriptor(
  name='GetFDemandImportResponse',
  full_name='franchisee_demand.GetFDemandImportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_demand.GetFDemandImportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_demand.GetFDemandImportResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10503,
  serialized_end=10595,
)


_APPROVEFDEMANDIMPORTREQUEST = _descriptor.Descriptor(
  name='ApproveFDemandImportRequest',
  full_name='franchisee_demand.ApproveFDemandImportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='franchisee_demand.ApproveFDemandImportRequest.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10597,
  serialized_end=10644,
)


_APPROVEFDEMANDIMPORTRESPONSE = _descriptor.Descriptor(
  name='ApproveFDemandImportResponse',
  full_name='franchisee_demand.ApproveFDemandImportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='franchisee_demand.ApproveFDemandImportResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10646,
  serialized_end=10692,
)


_CANCELFDEMANDIMPORTREQUEST = _descriptor.Descriptor(
  name='CancelFDemandImportRequest',
  full_name='franchisee_demand.CancelFDemandImportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='franchisee_demand.CancelFDemandImportRequest.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10694,
  serialized_end=10740,
)


_CANCELFDEMANDIMPORTRESPONSE = _descriptor.Descriptor(
  name='CancelFDemandImportResponse',
  full_name='franchisee_demand.CancelFDemandImportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='franchisee_demand.CancelFDemandImportResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10742,
  serialized_end=10787,
)


_LISTFDEMANDTOREMINDREQUEST = _descriptor.Descriptor(
  name='ListFDemandToRemindRequest',
  full_name='franchisee_demand.ListFDemandToRemindRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='geo_region_ids', full_name='franchisee_demand.ListFDemandToRemindRequest.geo_region_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_region_ids', full_name='franchisee_demand.ListFDemandToRemindRequest.branch_region_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchise_region_ids', full_name='franchisee_demand.ListFDemandToRemindRequest.franchise_region_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='franchisee_demand.ListFDemandToRemindRequest.store_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_demand.ListFDemandToRemindRequest.status', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='franchisee_demand.ListFDemandToRemindRequest.demand_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type_ids', full_name='franchisee_demand.ListFDemandToRemindRequest.type_ids', index=6,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_demand.ListFDemandToRemindRequest.offset', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_demand.ListFDemandToRemindRequest.limit', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_demand_date', full_name='franchisee_demand.ListFDemandToRemindRequest.start_demand_date', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_demand_date', full_name='franchisee_demand.ListFDemandToRemindRequest.end_demand_date', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10790,
  serialized_end=11140,
)


_DEMANDTOREMIND = _descriptor.Descriptor(
  name='DemandToRemind',
  full_name='franchisee_demand.DemandToRemind',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_code', full_name='franchisee_demand.DemandToRemind.store_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='franchisee_demand.DemandToRemind.store_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='geo_region_name', full_name='franchisee_demand.DemandToRemind.geo_region_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_region_name', full_name='franchisee_demand.DemandToRemind.branch_region_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchise_region_name', full_name='franchisee_demand.DemandToRemind.franchise_region_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type_name', full_name='franchisee_demand.DemandToRemind.type_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='franchisee_demand.DemandToRemind.demand_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_demand.DemandToRemind.status', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11143,
  serialized_end=11367,
)


_LISTFDEMANDTOREMINDRESPONSE = _descriptor.Descriptor(
  name='ListFDemandToRemindResponse',
  full_name='franchisee_demand.ListFDemandToRemindResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_demand.ListFDemandToRemindResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_demand.ListFDemandToRemindResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11369,
  serialized_end=11462,
)


_BATCHDEALFDEMANDREQUEST = _descriptor.Descriptor(
  name='BatchDealFDemandRequest',
  full_name='franchisee_demand.BatchDealFDemandRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='franchisee_demand.BatchDealFDemandRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='franchisee_demand.BatchDealFDemandRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_bys', full_name='franchisee_demand.BatchDealFDemandRequest.received_bys', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bus_types', full_name='franchisee_demand.BatchDealFDemandRequest.bus_types', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_ids', full_name='franchisee_demand.BatchDealFDemandRequest.order_type_ids', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='line_doc_id', full_name='franchisee_demand.BatchDealFDemandRequest.line_doc_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_method', full_name='franchisee_demand.BatchDealFDemandRequest.batch_method', index=6,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exclude_ids', full_name='franchisee_demand.BatchDealFDemandRequest.exclude_ids', index=7,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_ids', full_name='franchisee_demand.BatchDealFDemandRequest.include_ids', index=8,
      number=10, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_ids', full_name='franchisee_demand.BatchDealFDemandRequest.company_ids', index=9,
      number=11, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11465,
  serialized_end=11755,
)


_BATCHDEALFDEMANDRESPONSE = _descriptor.Descriptor(
  name='BatchDealFDemandResponse',
  full_name='franchisee_demand.BatchDealFDemandResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='franchisee_demand.BatchDealFDemandResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='franchisee_demand.BatchDealFDemandResponse.description', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msgs', full_name='franchisee_demand.BatchDealFDemandResponse.msgs', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11757,
  serialized_end=11872,
)


_CHECKFDEMANDTODOREQUEST = _descriptor.Descriptor(
  name='CheckFDemandTodoRequest',
  full_name='franchisee_demand.CheckFDemandTodoRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='franchisee_demand.CheckFDemandTodoRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11874,
  serialized_end=11918,
)


_TODORECEIPT = _descriptor.Descriptor(
  name='TodoReceipt',
  full_name='franchisee_demand.TodoReceipt',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_demand.TodoReceipt.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='franchisee_demand.TodoReceipt.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_demand.TodoReceipt.status', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='franchisee_demand.TodoReceipt.type', index=3,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11920,
  serialized_end=11989,
)


_CHECKFDEMANDTODORESPONSE = _descriptor.Descriptor(
  name='CheckFDemandTodoResponse',
  full_name='franchisee_demand.CheckFDemandTodoResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='handler', full_name='franchisee_demand.CheckFDemandTodoResponse.handler', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving', full_name='franchisee_demand.CheckFDemandTodoResponse.receiving', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='franchisee_demand.CheckFDemandTodoResponse.stocktake', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11992,
  serialized_end=12137,
)


_CHECKPRODUCTMESSAGE = _descriptor.Descriptor(
  name='CheckProductMessage',
  full_name='franchisee_demand.CheckProductMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_name', full_name='franchisee_demand.CheckProductMessage.product_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='franchisee_demand.CheckProductMessage.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='franchisee_demand.CheckProductMessage.spec', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='franchisee_demand.CheckProductMessage.quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_quantity', full_name='franchisee_demand.CheckProductMessage.min_quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_quantity', full_name='franchisee_demand.CheckProductMessage.increment_quantity', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_quantity', full_name='franchisee_demand.CheckProductMessage.max_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit', full_name='franchisee_demand.CheckProductMessage.unit', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='franchisee_demand.CheckProductMessage.tax_price', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='franchisee_demand.CheckProductMessage.amount', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='franchisee_demand.CheckProductMessage.storage_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_demand.CheckProductMessage.id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='franchisee_demand.CheckProductMessage.product_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12140,
  serialized_end=12412,
)


_CHECKORDERMESSAGE = _descriptor.Descriptor(
  name='CheckOrderMessage',
  full_name='franchisee_demand.CheckOrderMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_demand.CheckOrderMessage.status', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='franchisee_demand.CheckOrderMessage.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='franchisee_demand.CheckOrderMessage.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='franchisee_demand.CheckOrderMessage.order_type_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_name', full_name='franchisee_demand.CheckOrderMessage.franchisee_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='franchisee_demand.CheckOrderMessage.amount', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='franchisee_demand.CheckOrderMessage.demand_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='franchisee_demand.CheckOrderMessage.arrival_date', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msg', full_name='franchisee_demand.CheckOrderMessage.msg', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12415,
  serialized_end=12660,
)

_CREATEFDEMANDBATCHREQUEST.fields_by_name['batches'].message_type = _CREATEFDEMANDREQUEST
_CREATEFDEMANDREQUEST.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEFDEMANDREQUEST.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEFDEMANDREQUEST.fields_by_name['products'].message_type = _PRODUCT
_LISTFDEMANDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTFDEMANDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTFDEMANDRESPONSE.fields_by_name['rows'].message_type = _DEMAND
_DEMAND_ORDER.containing_type = _DEMAND
_DEMAND_REFUND.containing_type = _DEMAND
_DEMAND_ORDERTYPETIME.containing_type = _DEMAND
_DEMAND.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMAND.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMAND.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMAND.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMAND.fields_by_name['orders'].message_type = _DEMAND_ORDER
_DEMAND.fields_by_name['refunds'].message_type = _DEMAND_REFUND
_DEMAND.fields_by_name['order_type_time'].message_type = _DEMAND_ORDERTYPETIME
_HISTORYRESPONSE.fields_by_name['rows'].message_type = _HISTORY
_PRODUCTHISTORY.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_HISTORY.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_HISTORY.fields_by_name['details'].message_type = _PRODUCTHISTORY
_PRODUCTSRESPONSE.fields_by_name['rows'].message_type = _PRODUCT
_PRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCT.fields_by_name['relation_products'].message_type = _PRODUCT
_UPDATEPRODUCTREQUEST.fields_by_name['products'].message_type = _UPDATEPRODUCT
_UPDATEPRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATEPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATEPRODUCT.fields_by_name['relation_products'].message_type = _UPDATEPRODUCT
_UPDATEPRODUCTSRESPONSE.fields_by_name['msgs'].message_type = _CHECKPRODUCTMESSAGE
_DEALFDEMANDBYIDRESPONSE.fields_by_name['msgs'].message_type = _CHECKORDERMESSAGE
_GETPRODUCTDISTRIBUTIONRESPONSE_PRODETAIL.fields_by_name['dists'].message_type = _GETPRODUCTDISTRIBUTIONRESPONSE_DTRDETAIL
_GETPRODUCTDISTRIBUTIONRESPONSE_PRODETAIL.containing_type = _GETPRODUCTDISTRIBUTIONRESPONSE
_GETPRODUCTDISTRIBUTIONRESPONSE_DTRDETAIL.containing_type = _GETPRODUCTDISTRIBUTIONRESPONSE
_GETPRODUCTDISTRIBUTIONRESPONSE.fields_by_name['rows'].message_type = _GETPRODUCTDISTRIBUTIONRESPONSE_PRODETAIL
_GETPRODUCTDISTRIBUTIONRESPONSE.fields_by_name['distribute_centers'].message_type = _GETPRODUCTDISTRIBUTIONRESPONSE_DTRDETAIL
_ADDFDEMANDPRODUCTREQUEST_ADDPRODUCT.fields_by_name['relation_products'].message_type = _ADDFDEMANDPRODUCTREQUEST_ADDPRODUCT
_ADDFDEMANDPRODUCTREQUEST_ADDPRODUCT.containing_type = _ADDFDEMANDPRODUCTREQUEST
_ADDFDEMANDPRODUCTREQUEST.fields_by_name['products'].message_type = _ADDFDEMANDPRODUCTREQUEST_ADDPRODUCT
_IMPORTFDEMANDPRODUCTSREQUEST.fields_by_name['rows'].message_type = _IMPORTDETAIL
_IMPORTDETAIL.fields_by_name['products'].message_type = _IMPORTPRODUCT
_IMPORTPRODUCT.fields_by_name['relation_products'].message_type = _IMPORTPRODUCT
_FDEMANDIMPORTDETAIL.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_FDEMANDIMPORTDETAIL.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_FDEMANDIMPORTDETAIL.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETFDEMANDIMPORTDETAILRESPONSE.fields_by_name['rows'].message_type = _FDEMANDIMPORTDETAIL
_GETFDEMANDIMPORTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETFDEMANDIMPORTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_FDEMANDIMPORTLOG.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_FDEMANDIMPORTLOG.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_FDEMANDIMPORTLOG.fields_by_name['import_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETFDEMANDIMPORTRESPONSE.fields_by_name['rows'].message_type = _FDEMANDIMPORTLOG
_LISTFDEMANDTOREMINDREQUEST.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTFDEMANDTOREMINDREQUEST.fields_by_name['start_demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTFDEMANDTOREMINDREQUEST.fields_by_name['end_demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMANDTOREMIND.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTFDEMANDTOREMINDRESPONSE.fields_by_name['rows'].message_type = _DEMANDTOREMIND
_BATCHDEALFDEMANDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_BATCHDEALFDEMANDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_BATCHDEALFDEMANDRESPONSE.fields_by_name['msgs'].message_type = _CHECKORDERMESSAGE
_CHECKFDEMANDTODORESPONSE.fields_by_name['receiving'].message_type = _TODORECEIPT
_CHECKFDEMANDTODORESPONSE.fields_by_name['stocktake'].message_type = _TODORECEIPT
_CHECKORDERMESSAGE.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CHECKORDERMESSAGE.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['CreateFDemandBatchRequest'] = _CREATEFDEMANDBATCHREQUEST
DESCRIPTOR.message_types_by_name['CreateFDemandRequest'] = _CREATEFDEMANDREQUEST
DESCRIPTOR.message_types_by_name['CreateFDemandBatchResponse'] = _CREATEFDEMANDBATCHRESPONSE
DESCRIPTOR.message_types_by_name['ListFDemandRequest'] = _LISTFDEMANDREQUEST
DESCRIPTOR.message_types_by_name['ListFDemandResponse'] = _LISTFDEMANDRESPONSE
DESCRIPTOR.message_types_by_name['Demand'] = _DEMAND
DESCRIPTOR.message_types_by_name['GetFDemandByIdRequest'] = _GETFDEMANDBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetHistoryByIdRequest'] = _GETHISTORYBYIDREQUEST
DESCRIPTOR.message_types_by_name['HistoryResponse'] = _HISTORYRESPONSE
DESCRIPTOR.message_types_by_name['ProductHistory'] = _PRODUCTHISTORY
DESCRIPTOR.message_types_by_name['History'] = _HISTORY
DESCRIPTOR.message_types_by_name['GetProductsByIdRequest'] = _GETPRODUCTSBYIDREQUEST
DESCRIPTOR.message_types_by_name['ProductsResponse'] = _PRODUCTSRESPONSE
DESCRIPTOR.message_types_by_name['Product'] = _PRODUCT
DESCRIPTOR.message_types_by_name['UpdateProductRequest'] = _UPDATEPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['UpdateProduct'] = _UPDATEPRODUCT
DESCRIPTOR.message_types_by_name['UpdateProductsResponse'] = _UPDATEPRODUCTSRESPONSE
DESCRIPTOR.message_types_by_name['DealFDemandByIdRequest'] = _DEALFDEMANDBYIDREQUEST
DESCRIPTOR.message_types_by_name['DealFDemandByIdResponse'] = _DEALFDEMANDBYIDRESPONSE
DESCRIPTOR.message_types_by_name['GetProductDistributionRequest'] = _GETPRODUCTDISTRIBUTIONREQUEST
DESCRIPTOR.message_types_by_name['GetProductDistributionResponse'] = _GETPRODUCTDISTRIBUTIONRESPONSE
DESCRIPTOR.message_types_by_name['AddFDemandProductRequest'] = _ADDFDEMANDPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['AddFDemandProductResponse'] = _ADDFDEMANDPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['ImportFDemandProductsRequest'] = _IMPORTFDEMANDPRODUCTSREQUEST
DESCRIPTOR.message_types_by_name['ImportDetail'] = _IMPORTDETAIL
DESCRIPTOR.message_types_by_name['ImportProduct'] = _IMPORTPRODUCT
DESCRIPTOR.message_types_by_name['ImportFDemandProductsResponse'] = _IMPORTFDEMANDPRODUCTSRESPONSE
DESCRIPTOR.message_types_by_name['GetFDemandImportDetailRequest'] = _GETFDEMANDIMPORTDETAILREQUEST
DESCRIPTOR.message_types_by_name['FDemandImportDetail'] = _FDEMANDIMPORTDETAIL
DESCRIPTOR.message_types_by_name['GetFDemandImportDetailResponse'] = _GETFDEMANDIMPORTDETAILRESPONSE
DESCRIPTOR.message_types_by_name['GetFDemandImportRequest'] = _GETFDEMANDIMPORTREQUEST
DESCRIPTOR.message_types_by_name['FDemandImportLog'] = _FDEMANDIMPORTLOG
DESCRIPTOR.message_types_by_name['GetFDemandImportResponse'] = _GETFDEMANDIMPORTRESPONSE
DESCRIPTOR.message_types_by_name['ApproveFDemandImportRequest'] = _APPROVEFDEMANDIMPORTREQUEST
DESCRIPTOR.message_types_by_name['ApproveFDemandImportResponse'] = _APPROVEFDEMANDIMPORTRESPONSE
DESCRIPTOR.message_types_by_name['CancelFDemandImportRequest'] = _CANCELFDEMANDIMPORTREQUEST
DESCRIPTOR.message_types_by_name['CancelFDemandImportResponse'] = _CANCELFDEMANDIMPORTRESPONSE
DESCRIPTOR.message_types_by_name['ListFDemandToRemindRequest'] = _LISTFDEMANDTOREMINDREQUEST
DESCRIPTOR.message_types_by_name['DemandToRemind'] = _DEMANDTOREMIND
DESCRIPTOR.message_types_by_name['ListFDemandToRemindResponse'] = _LISTFDEMANDTOREMINDRESPONSE
DESCRIPTOR.message_types_by_name['BatchDealFDemandRequest'] = _BATCHDEALFDEMANDREQUEST
DESCRIPTOR.message_types_by_name['BatchDealFDemandResponse'] = _BATCHDEALFDEMANDRESPONSE
DESCRIPTOR.message_types_by_name['CheckFDemandTodoRequest'] = _CHECKFDEMANDTODOREQUEST
DESCRIPTOR.message_types_by_name['TodoReceipt'] = _TODORECEIPT
DESCRIPTOR.message_types_by_name['CheckFDemandTodoResponse'] = _CHECKFDEMANDTODORESPONSE
DESCRIPTOR.message_types_by_name['CheckProductMessage'] = _CHECKPRODUCTMESSAGE
DESCRIPTOR.message_types_by_name['CheckOrderMessage'] = _CHECKORDERMESSAGE
DESCRIPTOR.enum_types_by_name['Status'] = _STATUS
DESCRIPTOR.enum_types_by_name['Type'] = _TYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CreateFDemandBatchRequest = _reflection.GeneratedProtocolMessageType('CreateFDemandBatchRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEFDEMANDBATCHREQUEST,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.CreateFDemandBatchRequest)
  ))
_sym_db.RegisterMessage(CreateFDemandBatchRequest)

CreateFDemandRequest = _reflection.GeneratedProtocolMessageType('CreateFDemandRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEFDEMANDREQUEST,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.CreateFDemandRequest)
  ))
_sym_db.RegisterMessage(CreateFDemandRequest)

CreateFDemandBatchResponse = _reflection.GeneratedProtocolMessageType('CreateFDemandBatchResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEFDEMANDBATCHRESPONSE,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.CreateFDemandBatchResponse)
  ))
_sym_db.RegisterMessage(CreateFDemandBatchResponse)

ListFDemandRequest = _reflection.GeneratedProtocolMessageType('ListFDemandRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTFDEMANDREQUEST,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.ListFDemandRequest)
  ))
_sym_db.RegisterMessage(ListFDemandRequest)

ListFDemandResponse = _reflection.GeneratedProtocolMessageType('ListFDemandResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTFDEMANDRESPONSE,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.ListFDemandResponse)
  ))
_sym_db.RegisterMessage(ListFDemandResponse)

Demand = _reflection.GeneratedProtocolMessageType('Demand', (_message.Message,), dict(

  Order = _reflection.GeneratedProtocolMessageType('Order', (_message.Message,), dict(
    DESCRIPTOR = _DEMAND_ORDER,
    __module__ = 'frs_management.franchisee_demand_pb2'
    # @@protoc_insertion_point(class_scope:franchisee_demand.Demand.Order)
    ))
  ,

  Refund = _reflection.GeneratedProtocolMessageType('Refund', (_message.Message,), dict(
    DESCRIPTOR = _DEMAND_REFUND,
    __module__ = 'frs_management.franchisee_demand_pb2'
    # @@protoc_insertion_point(class_scope:franchisee_demand.Demand.Refund)
    ))
  ,

  OrderTypeTime = _reflection.GeneratedProtocolMessageType('OrderTypeTime', (_message.Message,), dict(
    DESCRIPTOR = _DEMAND_ORDERTYPETIME,
    __module__ = 'frs_management.franchisee_demand_pb2'
    # @@protoc_insertion_point(class_scope:franchisee_demand.Demand.OrderTypeTime)
    ))
  ,
  DESCRIPTOR = _DEMAND,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.Demand)
  ))
_sym_db.RegisterMessage(Demand)
_sym_db.RegisterMessage(Demand.Order)
_sym_db.RegisterMessage(Demand.Refund)
_sym_db.RegisterMessage(Demand.OrderTypeTime)

GetFDemandByIdRequest = _reflection.GeneratedProtocolMessageType('GetFDemandByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETFDEMANDBYIDREQUEST,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.GetFDemandByIdRequest)
  ))
_sym_db.RegisterMessage(GetFDemandByIdRequest)

GetHistoryByIdRequest = _reflection.GeneratedProtocolMessageType('GetHistoryByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETHISTORYBYIDREQUEST,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.GetHistoryByIdRequest)
  ))
_sym_db.RegisterMessage(GetHistoryByIdRequest)

HistoryResponse = _reflection.GeneratedProtocolMessageType('HistoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _HISTORYRESPONSE,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.HistoryResponse)
  ))
_sym_db.RegisterMessage(HistoryResponse)

ProductHistory = _reflection.GeneratedProtocolMessageType('ProductHistory', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTHISTORY,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.ProductHistory)
  ))
_sym_db.RegisterMessage(ProductHistory)

History = _reflection.GeneratedProtocolMessageType('History', (_message.Message,), dict(
  DESCRIPTOR = _HISTORY,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.History)
  ))
_sym_db.RegisterMessage(History)

GetProductsByIdRequest = _reflection.GeneratedProtocolMessageType('GetProductsByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTSBYIDREQUEST,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.GetProductsByIdRequest)
  ))
_sym_db.RegisterMessage(GetProductsByIdRequest)

ProductsResponse = _reflection.GeneratedProtocolMessageType('ProductsResponse', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTSRESPONSE,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.ProductsResponse)
  ))
_sym_db.RegisterMessage(ProductsResponse)

Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCT,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.Product)
  ))
_sym_db.RegisterMessage(Product)

UpdateProductRequest = _reflection.GeneratedProtocolMessageType('UpdateProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPRODUCTREQUEST,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.UpdateProductRequest)
  ))
_sym_db.RegisterMessage(UpdateProductRequest)

UpdateProduct = _reflection.GeneratedProtocolMessageType('UpdateProduct', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPRODUCT,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.UpdateProduct)
  ))
_sym_db.RegisterMessage(UpdateProduct)

UpdateProductsResponse = _reflection.GeneratedProtocolMessageType('UpdateProductsResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPRODUCTSRESPONSE,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.UpdateProductsResponse)
  ))
_sym_db.RegisterMessage(UpdateProductsResponse)

DealFDemandByIdRequest = _reflection.GeneratedProtocolMessageType('DealFDemandByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _DEALFDEMANDBYIDREQUEST,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.DealFDemandByIdRequest)
  ))
_sym_db.RegisterMessage(DealFDemandByIdRequest)

DealFDemandByIdResponse = _reflection.GeneratedProtocolMessageType('DealFDemandByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _DEALFDEMANDBYIDRESPONSE,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.DealFDemandByIdResponse)
  ))
_sym_db.RegisterMessage(DealFDemandByIdResponse)

GetProductDistributionRequest = _reflection.GeneratedProtocolMessageType('GetProductDistributionRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTDISTRIBUTIONREQUEST,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.GetProductDistributionRequest)
  ))
_sym_db.RegisterMessage(GetProductDistributionRequest)

GetProductDistributionResponse = _reflection.GeneratedProtocolMessageType('GetProductDistributionResponse', (_message.Message,), dict(

  ProDetail = _reflection.GeneratedProtocolMessageType('ProDetail', (_message.Message,), dict(
    DESCRIPTOR = _GETPRODUCTDISTRIBUTIONRESPONSE_PRODETAIL,
    __module__ = 'frs_management.franchisee_demand_pb2'
    # @@protoc_insertion_point(class_scope:franchisee_demand.GetProductDistributionResponse.ProDetail)
    ))
  ,

  DtrDetail = _reflection.GeneratedProtocolMessageType('DtrDetail', (_message.Message,), dict(
    DESCRIPTOR = _GETPRODUCTDISTRIBUTIONRESPONSE_DTRDETAIL,
    __module__ = 'frs_management.franchisee_demand_pb2'
    # @@protoc_insertion_point(class_scope:franchisee_demand.GetProductDistributionResponse.DtrDetail)
    ))
  ,
  DESCRIPTOR = _GETPRODUCTDISTRIBUTIONRESPONSE,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.GetProductDistributionResponse)
  ))
_sym_db.RegisterMessage(GetProductDistributionResponse)
_sym_db.RegisterMessage(GetProductDistributionResponse.ProDetail)
_sym_db.RegisterMessage(GetProductDistributionResponse.DtrDetail)

AddFDemandProductRequest = _reflection.GeneratedProtocolMessageType('AddFDemandProductRequest', (_message.Message,), dict(

  AddProduct = _reflection.GeneratedProtocolMessageType('AddProduct', (_message.Message,), dict(
    DESCRIPTOR = _ADDFDEMANDPRODUCTREQUEST_ADDPRODUCT,
    __module__ = 'frs_management.franchisee_demand_pb2'
    # @@protoc_insertion_point(class_scope:franchisee_demand.AddFDemandProductRequest.AddProduct)
    ))
  ,
  DESCRIPTOR = _ADDFDEMANDPRODUCTREQUEST,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.AddFDemandProductRequest)
  ))
_sym_db.RegisterMessage(AddFDemandProductRequest)
_sym_db.RegisterMessage(AddFDemandProductRequest.AddProduct)

AddFDemandProductResponse = _reflection.GeneratedProtocolMessageType('AddFDemandProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _ADDFDEMANDPRODUCTRESPONSE,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.AddFDemandProductResponse)
  ))
_sym_db.RegisterMessage(AddFDemandProductResponse)

ImportFDemandProductsRequest = _reflection.GeneratedProtocolMessageType('ImportFDemandProductsRequest', (_message.Message,), dict(
  DESCRIPTOR = _IMPORTFDEMANDPRODUCTSREQUEST,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.ImportFDemandProductsRequest)
  ))
_sym_db.RegisterMessage(ImportFDemandProductsRequest)

ImportDetail = _reflection.GeneratedProtocolMessageType('ImportDetail', (_message.Message,), dict(
  DESCRIPTOR = _IMPORTDETAIL,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.ImportDetail)
  ))
_sym_db.RegisterMessage(ImportDetail)

ImportProduct = _reflection.GeneratedProtocolMessageType('ImportProduct', (_message.Message,), dict(
  DESCRIPTOR = _IMPORTPRODUCT,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.ImportProduct)
  ))
_sym_db.RegisterMessage(ImportProduct)

ImportFDemandProductsResponse = _reflection.GeneratedProtocolMessageType('ImportFDemandProductsResponse', (_message.Message,), dict(
  DESCRIPTOR = _IMPORTFDEMANDPRODUCTSRESPONSE,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.ImportFDemandProductsResponse)
  ))
_sym_db.RegisterMessage(ImportFDemandProductsResponse)

GetFDemandImportDetailRequest = _reflection.GeneratedProtocolMessageType('GetFDemandImportDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETFDEMANDIMPORTDETAILREQUEST,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.GetFDemandImportDetailRequest)
  ))
_sym_db.RegisterMessage(GetFDemandImportDetailRequest)

FDemandImportDetail = _reflection.GeneratedProtocolMessageType('FDemandImportDetail', (_message.Message,), dict(
  DESCRIPTOR = _FDEMANDIMPORTDETAIL,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.FDemandImportDetail)
  ))
_sym_db.RegisterMessage(FDemandImportDetail)

GetFDemandImportDetailResponse = _reflection.GeneratedProtocolMessageType('GetFDemandImportDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETFDEMANDIMPORTDETAILRESPONSE,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.GetFDemandImportDetailResponse)
  ))
_sym_db.RegisterMessage(GetFDemandImportDetailResponse)

GetFDemandImportRequest = _reflection.GeneratedProtocolMessageType('GetFDemandImportRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETFDEMANDIMPORTREQUEST,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.GetFDemandImportRequest)
  ))
_sym_db.RegisterMessage(GetFDemandImportRequest)

FDemandImportLog = _reflection.GeneratedProtocolMessageType('FDemandImportLog', (_message.Message,), dict(
  DESCRIPTOR = _FDEMANDIMPORTLOG,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.FDemandImportLog)
  ))
_sym_db.RegisterMessage(FDemandImportLog)

GetFDemandImportResponse = _reflection.GeneratedProtocolMessageType('GetFDemandImportResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETFDEMANDIMPORTRESPONSE,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.GetFDemandImportResponse)
  ))
_sym_db.RegisterMessage(GetFDemandImportResponse)

ApproveFDemandImportRequest = _reflection.GeneratedProtocolMessageType('ApproveFDemandImportRequest', (_message.Message,), dict(
  DESCRIPTOR = _APPROVEFDEMANDIMPORTREQUEST,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.ApproveFDemandImportRequest)
  ))
_sym_db.RegisterMessage(ApproveFDemandImportRequest)

ApproveFDemandImportResponse = _reflection.GeneratedProtocolMessageType('ApproveFDemandImportResponse', (_message.Message,), dict(
  DESCRIPTOR = _APPROVEFDEMANDIMPORTRESPONSE,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.ApproveFDemandImportResponse)
  ))
_sym_db.RegisterMessage(ApproveFDemandImportResponse)

CancelFDemandImportRequest = _reflection.GeneratedProtocolMessageType('CancelFDemandImportRequest', (_message.Message,), dict(
  DESCRIPTOR = _CANCELFDEMANDIMPORTREQUEST,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.CancelFDemandImportRequest)
  ))
_sym_db.RegisterMessage(CancelFDemandImportRequest)

CancelFDemandImportResponse = _reflection.GeneratedProtocolMessageType('CancelFDemandImportResponse', (_message.Message,), dict(
  DESCRIPTOR = _CANCELFDEMANDIMPORTRESPONSE,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.CancelFDemandImportResponse)
  ))
_sym_db.RegisterMessage(CancelFDemandImportResponse)

ListFDemandToRemindRequest = _reflection.GeneratedProtocolMessageType('ListFDemandToRemindRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTFDEMANDTOREMINDREQUEST,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.ListFDemandToRemindRequest)
  ))
_sym_db.RegisterMessage(ListFDemandToRemindRequest)

DemandToRemind = _reflection.GeneratedProtocolMessageType('DemandToRemind', (_message.Message,), dict(
  DESCRIPTOR = _DEMANDTOREMIND,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.DemandToRemind)
  ))
_sym_db.RegisterMessage(DemandToRemind)

ListFDemandToRemindResponse = _reflection.GeneratedProtocolMessageType('ListFDemandToRemindResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTFDEMANDTOREMINDRESPONSE,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.ListFDemandToRemindResponse)
  ))
_sym_db.RegisterMessage(ListFDemandToRemindResponse)

BatchDealFDemandRequest = _reflection.GeneratedProtocolMessageType('BatchDealFDemandRequest', (_message.Message,), dict(
  DESCRIPTOR = _BATCHDEALFDEMANDREQUEST,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.BatchDealFDemandRequest)
  ))
_sym_db.RegisterMessage(BatchDealFDemandRequest)

BatchDealFDemandResponse = _reflection.GeneratedProtocolMessageType('BatchDealFDemandResponse', (_message.Message,), dict(
  DESCRIPTOR = _BATCHDEALFDEMANDRESPONSE,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.BatchDealFDemandResponse)
  ))
_sym_db.RegisterMessage(BatchDealFDemandResponse)

CheckFDemandTodoRequest = _reflection.GeneratedProtocolMessageType('CheckFDemandTodoRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHECKFDEMANDTODOREQUEST,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.CheckFDemandTodoRequest)
  ))
_sym_db.RegisterMessage(CheckFDemandTodoRequest)

TodoReceipt = _reflection.GeneratedProtocolMessageType('TodoReceipt', (_message.Message,), dict(
  DESCRIPTOR = _TODORECEIPT,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.TodoReceipt)
  ))
_sym_db.RegisterMessage(TodoReceipt)

CheckFDemandTodoResponse = _reflection.GeneratedProtocolMessageType('CheckFDemandTodoResponse', (_message.Message,), dict(
  DESCRIPTOR = _CHECKFDEMANDTODORESPONSE,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.CheckFDemandTodoResponse)
  ))
_sym_db.RegisterMessage(CheckFDemandTodoResponse)

CheckProductMessage = _reflection.GeneratedProtocolMessageType('CheckProductMessage', (_message.Message,), dict(
  DESCRIPTOR = _CHECKPRODUCTMESSAGE,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.CheckProductMessage)
  ))
_sym_db.RegisterMessage(CheckProductMessage)

CheckOrderMessage = _reflection.GeneratedProtocolMessageType('CheckOrderMessage', (_message.Message,), dict(
  DESCRIPTOR = _CHECKORDERMESSAGE,
  __module__ = 'frs_management.franchisee_demand_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_demand.CheckOrderMessage)
  ))
_sym_db.RegisterMessage(CheckOrderMessage)



_FRANCHISEEDEMANDSERVICE = _descriptor.ServiceDescriptor(
  name='FranchiseeDemandService',
  full_name='franchisee_demand.FranchiseeDemandService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=12842,
  serialized_end=15304,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateFDemandBatch',
    full_name='franchisee_demand.FranchiseeDemandService.CreateFDemandBatch',
    index=0,
    containing_service=None,
    input_type=_CREATEFDEMANDBATCHREQUEST,
    output_type=_CREATEFDEMANDBATCHRESPONSE,
    serialized_options=_b('\202\323\344\223\0026\"1/api/v2/supply/frs-management/demand/create/batch:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListFDemand',
    full_name='franchisee_demand.FranchiseeDemandService.ListFDemand',
    index=1,
    containing_service=None,
    input_type=_LISTFDEMANDREQUEST,
    output_type=_LISTFDEMANDRESPONSE,
    serialized_options=_b('\202\323\344\223\002*\022(/api/v2/supply/frs-management/frs-demand'),
  ),
  _descriptor.MethodDescriptor(
    name='GetFDemandById',
    full_name='franchisee_demand.FranchiseeDemandService.GetFDemandById',
    index=2,
    containing_service=None,
    input_type=_GETFDEMANDBYIDREQUEST,
    output_type=_DEMAND,
    serialized_options=_b('\202\323\344\223\0026\0224/api/v2/supply/frs-management/frs-demand/{demand_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetHistoryById',
    full_name='franchisee_demand.FranchiseeDemandService.GetHistoryById',
    index=3,
    containing_service=None,
    input_type=_GETHISTORYBYIDREQUEST,
    output_type=_HISTORYRESPONSE,
    serialized_options=_b('\202\323\344\223\002>\022</api/v2/supply/frs-management/frs-demand/{demand_id}/history'),
  ),
  _descriptor.MethodDescriptor(
    name='GetProductsById',
    full_name='franchisee_demand.FranchiseeDemandService.GetProductsById',
    index=4,
    containing_service=None,
    input_type=_GETPRODUCTSBYIDREQUEST,
    output_type=_PRODUCTSRESPONSE,
    serialized_options=_b('\202\323\344\223\002>\022</api/v2/supply/frs-management/frs-demand/{demand_id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateProducts',
    full_name='franchisee_demand.FranchiseeDemandService.UpdateProducts',
    index=5,
    containing_service=None,
    input_type=_UPDATEPRODUCTREQUEST,
    output_type=_UPDATEPRODUCTSRESPONSE,
    serialized_options=_b('\202\323\344\223\002A\032</api/v2/supply/frs-management/frs-demand/{demand_id}/product:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DealFDemandById',
    full_name='franchisee_demand.FranchiseeDemandService.DealFDemandById',
    index=6,
    containing_service=None,
    input_type=_DEALFDEMANDBYIDREQUEST,
    output_type=_DEALFDEMANDBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002;\0326/api/v2/supply/frs-management/frs-demand/deal/{action}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetProductDistribution',
    full_name='franchisee_demand.FranchiseeDemandService.GetProductDistribution',
    index=7,
    containing_service=None,
    input_type=_GETPRODUCTDISTRIBUTIONREQUEST,
    output_type=_GETPRODUCTDISTRIBUTIONRESPONSE,
    serialized_options=_b('\202\323\344\223\0024\0222/api/v2/supply/frs-management/product/distribution'),
  ),
  _descriptor.MethodDescriptor(
    name='AddFDemandProduct',
    full_name='franchisee_demand.FranchiseeDemandService.AddFDemandProduct',
    index=8,
    containing_service=None,
    input_type=_ADDFDEMANDPRODUCTREQUEST,
    output_type=_ADDFDEMANDPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\"4/api/v2/supply/frs-management/frs-demand/add/product:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ImportFDemandProducts',
    full_name='franchisee_demand.FranchiseeDemandService.ImportFDemandProducts',
    index=9,
    containing_service=None,
    input_type=_IMPORTFDEMANDPRODUCTSREQUEST,
    output_type=_IMPORTFDEMANDPRODUCTSRESPONSE,
    serialized_options=_b('\202\323\344\223\002=\"8/api/v2/supply/frs-management/frs-demand/import/products:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='BatchResetFDemand',
    full_name='franchisee_demand.FranchiseeDemandService.BatchResetFDemand',
    index=10,
    containing_service=None,
    input_type=_BATCHDEALFDEMANDREQUEST,
    output_type=_BATCHDEALFDEMANDRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\0324/api/v2/supply/frs-management/frs-demand/batch/reset:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListFDemandToRemind',
    full_name='franchisee_demand.FranchiseeDemandService.ListFDemandToRemind',
    index=11,
    containing_service=None,
    input_type=_LISTFDEMANDTOREMINDREQUEST,
    output_type=_LISTFDEMANDTOREMINDRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\"6/api/v2/supply/frs-management/franchisee/demand/remind'),
  ),
  _descriptor.MethodDescriptor(
    name='BatchConfirmFDemand',
    full_name='franchisee_demand.FranchiseeDemandService.BatchConfirmFDemand',
    index=12,
    containing_service=None,
    input_type=_BATCHDEALFDEMANDREQUEST,
    output_type=_BATCHDEALFDEMANDRESPONSE,
    serialized_options=_b('\202\323\344\223\002;\0326/api/v2/supply/frs-management/frs-demand/batch/confirm:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CheckFDemandTodo',
    full_name='franchisee_demand.FranchiseeDemandService.CheckFDemandTodo',
    index=13,
    containing_service=None,
    input_type=_CHECKFDEMANDTODOREQUEST,
    output_type=_CHECKFDEMANDTODORESPONSE,
    serialized_options=_b('\202\323\344\223\002<\022:/api/v2/supply/frs-management/frs-demand/{demand_id}/check'),
  ),
])
_sym_db.RegisterServiceDescriptor(_FRANCHISEEDEMANDSERVICE)

DESCRIPTOR.services_by_name['FranchiseeDemandService'] = _FRANCHISEEDEMANDSERVICE

# @@protoc_insertion_point(module_scope)
