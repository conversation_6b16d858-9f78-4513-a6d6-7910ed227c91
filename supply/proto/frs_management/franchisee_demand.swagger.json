{"swagger": "2.0", "info": {"title": "frs_management/franchisee_demand.proto", "version": "version not set"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/frs-management/demand/create/batch": {"post": {"summary": "批量创建订单(总部分配导入使用)", "operationId": "CreateFDemandBatch", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_demandCreateFDemandBatchResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/franchisee_demandCreateFDemandBatchRequest"}}], "tags": ["FranchiseeDemandService"]}}, "/api/v2/supply/frs-management/franchisee/demand/remind": {"post": {"summary": "门店未订货提醒列表（WEB端）", "operationId": "ListFDemandToRemind", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_demandListFDemandToRemindResponse"}}}, "tags": ["FranchiseeDemandService"]}}, "/api/v2/supply/frs-management/frs-demand": {"get": {"summary": "查询订货单列表", "operationId": "ListFDemand", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_demandListFDemandResponse"}}}, "parameters": [{"name": "start_date", "description": "订货开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "订货结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "status", "description": "订货单状态.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "received_bys", "description": "接收方ids.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "code", "description": "订货单号.", "in": "query", "required": false, "type": "string"}, {"name": "types", "description": "订货单类型：加盟门店订货单(FSD)、加盟门店主配单(FMD).", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "sub_type", "description": "主配类型枚举：门店主配(STORE)、商品主配(PRODUCT)、总部帮订(MASTER) (主配单才有的字段).", "in": "query", "required": false, "type": "string"}, {"name": "chain_type", "description": "门店连锁类型：加盟(FRS)、直营(DRS).", "in": "query", "required": false, "type": "string"}, {"name": "bus_types", "description": "业务类型(来源)：手动创建(MANUAL)、总部分配(HD_ASSIGN)、计划创建(PLAN).", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "offset", "description": "偏移量(默认0).", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "description": "查询的每页数量.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "order", "description": "排序(asc or desc 默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "description": "排序字段.", "in": "query", "required": false, "type": "string"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "ids", "description": "订货单ID列表.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "product_ids", "description": "商品ids.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "franchisee_ids", "description": "加盟商ids.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "payment_ways", "description": "支付方式\n\"BohCreditPay\"  BOH信用付\n\"WXMPay\"        微信小程序\n\"BohOfflinePay\" 线下支付\n\"BohVoucherPay\" 代金券支付.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "order_type_ids", "description": "订货类型ids.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "line_doc_id", "description": "线路ID.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["FranchiseeDemandService"]}}, "/api/v2/supply/frs-management/frs-demand/add/product": {"post": {"summary": "订单新增商品", "operationId": "AddFDemandProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_demandAddFDemandProductResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/franchisee_demandAddFDemandProductRequest"}}], "tags": ["FranchiseeDemandService"]}}, "/api/v2/supply/frs-management/frs-demand/batch/confirm": {"put": {"summary": "按条件批量确认订单", "operationId": "BatchConfirmFDemand", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_demandBatchDealFDemandResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/franchisee_demandBatchDealFDemandRequest"}}], "tags": ["FranchiseeDemandService"]}}, "/api/v2/supply/frs-management/frs-demand/batch/reset": {"put": {"summary": "批量还原订单(批量还原订单导入数量)", "operationId": "BatchResetFDemand", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_demandBatchDealFDemandResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/franchisee_demandBatchDealFDemandRequest"}}], "tags": ["FranchiseeDemandService"]}}, "/api/v2/supply/frs-management/frs-demand/deal/{action}": {"put": {"summary": "修改订单状态统一入口", "operationId": "DealFDemandById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_demandDealFDemandByIdResponse"}}}, "parameters": [{"name": "action", "description": "单据业务状态，对应上方Status枚举", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/franchisee_demandDealFDemandByIdRequest"}}], "tags": ["FranchiseeDemandService"]}}, "/api/v2/supply/frs-management/frs-demand/import/products": {"post": {"summary": "导入订货单(改单)", "operationId": "ImportFDemandProducts", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_demandImportFDemandProductsResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/franchisee_demandImportFDemandProductsRequest"}}], "tags": ["FranchiseeDemandService"]}}, "/api/v2/supply/frs-management/frs-demand/{demand_id}": {"get": {"summary": "根据id查询单个订货单", "operationId": "GetFDemandById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_demandDemand"}}}, "parameters": [{"name": "demand_id", "description": "订货单id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["FranchiseeDemandService"]}}, "/api/v2/supply/frs-management/frs-demand/{demand_id}/check": {"get": {"summary": "检查订货待办能否订货", "operationId": "CheckFDemandTodo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_demandCheckFDemandTodoResponse"}}}, "parameters": [{"name": "demand_id", "description": "订货单ID", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["FranchiseeDemandService"]}}, "/api/v2/supply/frs-management/frs-demand/{demand_id}/history": {"get": {"summary": "根据id查询单据历史记录", "operationId": "GetHistoryById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_demandHistoryResponse"}}}, "parameters": [{"name": "demand_id", "description": "订货单id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["FranchiseeDemandService"]}}, "/api/v2/supply/frs-management/frs-demand/{demand_id}/product": {"get": {"summary": "根据id查询单据内的商品列表", "operationId": "GetProductsById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_demandProductsResponse"}}}, "parameters": [{"name": "demand_id", "description": "订货单id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "limit", "description": "分页限制.", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "offset", "description": "偏移量.", "in": "query", "required": false, "type": "string"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "order", "description": "排序方式  asc or desc.", "in": "query", "required": false, "type": "string"}, {"name": "sort", "description": "排序字段.", "in": "query", "required": false, "type": "string"}], "tags": ["FranchiseeDemandService"]}, "put": {"summary": "更新订货单商品", "operationId": "UpdateProducts", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_demandUpdateProductsResponse"}}}, "parameters": [{"name": "demand_id", "description": "订货单id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/franchisee_demandUpdateProductRequest"}}], "tags": ["FranchiseeDemandService"]}}, "/api/v2/supply/frs-management/product/distribution": {"get": {"summary": "获取订货商品可分配仓库(包含实时库存)", "operationId": "GetProductDistribution", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/franchisee_demandGetProductDistributionResponse"}}}, "parameters": [{"name": "demand_id", "description": "订货单id.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "product_ids", "description": "商品ids.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}], "tags": ["FranchiseeDemandService"]}}}, "definitions": {"AddFDemandProductRequestAddProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64", "title": "商品ID(必填)"}, "confirm_quantity": {"type": "number", "format": "double", "title": "PC添加商品更新到确认数量(必填)"}, "tax_price": {"type": "number", "format": "double", "title": "含税订货价(必填)"}, "tax_rate": {"type": "number", "format": "double", "title": "税率(必填)"}, "sales_price": {"type": "number", "format": "double", "title": "零售价(必填)"}, "arrival_days": {"type": "integer", "format": "int64", "title": "到货天数(必填)"}, "min_quantity": {"type": "number", "format": "double", "title": "最小订量(必填)"}, "max_quantity": {"type": "number", "format": "double", "title": "最大订量(必填)"}, "increment_quantity": {"type": "number", "format": "double", "title": "递增订量(必填)"}, "extends": {"type": "string", "title": "扩展商品信息(eg: {\"product_price\": [xxx]})(必填)"}, "bind_products": {"type": "array", "items": {"$ref": "#/definitions/AddFDemandProductRequestAddProduct"}, "title": "捆绑商品结构(必填)"}}, "title": "添加商品结构"}, "DemandOrder": {"type": "object", "properties": {"order_id": {"type": "string", "format": "uint64"}, "order_code": {"type": "string"}}}, "DemandOrderTypeTime": {"type": "object", "properties": {"order_time": {"type": "array", "items": {"type": "string"}}, "audit_time": {"type": "array", "items": {"type": "string"}}}, "title": "订货类型关联时间组"}, "DemandRefund": {"type": "object", "properties": {"refund_id": {"type": "string", "format": "uint64"}, "refund_code": {"type": "string"}}}, "GetProductDistributionResponseDtrDetail": {"type": "object", "properties": {"distribute_id": {"type": "string", "format": "uint64", "title": "配送方ID"}, "distribute_name": {"type": "string", "title": "配送方名称"}, "arrival_days": {"type": "integer", "format": "int32", "title": "到货天数"}, "qty": {"type": "number", "format": "double", "title": "实时库存(核算单位)"}, "order_qty": {"type": "number", "format": "double", "title": "实时库存(订货单位)"}, "distribution_type": {"type": "string", "title": "配送方式"}}}, "GetProductDistributionResponseProDetail": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64", "title": "商品ID"}, "dists": {"type": "array", "items": {"$ref": "#/definitions/GetProductDistributionResponseDtrDetail"}, "title": "可分配仓库信息"}}}, "franchisee_demandAddFDemandProductRequest": {"type": "object", "properties": {"demand_id": {"type": "string", "format": "uint64", "title": "订货单id"}, "products": {"type": "array", "items": {"$ref": "#/definitions/AddFDemandProductRequestAddProduct"}, "title": "商品"}}}, "franchisee_demandAddFDemandProductResponse": {"type": "object", "properties": {"demand_id": {"type": "string", "format": "uint64", "title": "订货单id"}}}, "franchisee_demandBatchDealFDemandRequest": {"type": "object", "properties": {"start_date": {"type": "string", "format": "date-time", "title": "订货开始日期"}, "end_date": {"type": "string", "format": "date-time", "title": "订货结束日期"}, "received_bys": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "接收方ids"}, "bus_types": {"type": "array", "items": {"type": "string"}, "title": "业务类型(来源)：手动创建(MANUAL)、总部分配(HD_ASSIGN)、计划创建(PLAN)"}, "order_type_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "订货类型ids"}, "line_doc_id": {"type": "string", "format": "uint64", "title": "线路ID"}, "batch_method": {"type": "string", "title": "批量更新方式:\n\"ALL\"  全量按条件(支持排除部分订单：详见参数exclude_ids)\n\"SELF\" 部分按订单(选择部分订单批量提交：详见参数include_ids)"}, "exclude_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "排除订单ID列表(batch_type=\"ALL\"时使用)"}, "include_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "提交订单ID列表(batch_type=\"SELF\"时必传)"}}}, "franchisee_demandBatchDealFDemandResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}, "description": {"type": "string"}}}, "franchisee_demandCheckFDemandTodoResponse": {"type": "object", "properties": {"handler": {"type": "boolean", "format": "boolean"}, "receiving": {"type": "array", "items": {"$ref": "#/definitions/franchisee_demandTodoReceipt"}}, "stocktake": {"type": "array", "items": {"$ref": "#/definitions/franchisee_demandTodoReceipt"}}}}, "franchisee_demandCreateFDemandBatchRequest": {"type": "object", "properties": {"batch_id": {"type": "string", "format": "uint64", "title": "唯一请求ID 批量创建保证一个批次唯一请求即可"}, "batches": {"type": "array", "items": {"$ref": "#/definitions/franchisee_demandCreateFDemandRequest"}}}}, "franchisee_demandCreateFDemandBatchResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean", "title": "True / False"}}}, "franchisee_demandCreateFDemandRequest": {"type": "object", "properties": {"arrival_date": {"type": "string", "format": "date-time", "title": "到货日期"}, "demand_date": {"type": "string", "format": "date-time", "title": "订货日期"}, "received_by": {"type": "string", "format": "uint64", "title": "订货门店"}, "distribute_by": {"type": "string", "format": "uint64", "title": "配送方"}, "status": {"type": "string", "title": "状态"}, "products": {"type": "array", "items": {"$ref": "#/definitions/franchisee_demandProduct"}, "title": "订货商品"}, "franchisee_id": {"type": "string", "format": "uint64", "title": "加盟商ID"}, "order_type_id": {"type": "string", "format": "uint64", "title": "订货类型id"}, "remark": {"type": "string", "title": "备注"}, "type": {"type": "string", "title": "订单类型：加盟门店订货(FSD)"}, "reason": {"type": "string", "title": "原因"}, "bus_type": {"type": "string", "title": "业务类型(来源)：手动创建(MANUAL)、总部分配(HD_ASSIGN)、计划创建(PLAN)"}, "payment_way": {"type": "string", "title": "付款方式：\n\"BohCreditPay\"  BOH信用付\n\"WXMPay\"        微信小程序\n\"BohOfflinePay\" 线下支付\n\"BohVoucherPay\" 代金券支付"}, "trade_company": {"type": "string", "format": "uint64", "title": "贸易公司"}, "extends": {"type": "string", "title": "扩展信息 eg: {\"payment_info\": {\"payee_name\": \"收款人名称\", \"payee_account\": \"收款方帐号\", \"deposit_bank\": \"收款方开户行名称\"})"}}}, "franchisee_demandDealFDemandByIdRequest": {"type": "object", "properties": {"demand_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "批量只支持'确认收款'和'驳回'操作"}, "action": {"type": "string", "title": "单据业务状态，对应上方Status枚举"}, "remark": {"type": "string", "title": "备注"}, "attachments": {"type": "array", "items": {"type": "string"}, "title": "附件"}, "reject_reason": {"type": "string", "title": "驳回原因"}}, "title": "处理单据"}, "franchisee_demandDealFDemandByIdResponse": {"type": "object", "properties": {"result": {"type": "string", "title": "\"success\" 成功\n\"failed\"  失败"}}, "title": "统一返回对象"}, "franchisee_demandDemand": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "单据id"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "batch_id": {"type": "string", "format": "uint64", "title": "生成的批次id"}, "code": {"type": "string", "title": "编码"}, "type": {"type": "string", "title": "订货单类型"}, "sub_type": {"type": "string", "title": "主配类型(主配单才有的字段)"}, "bus_type": {"type": "string", "title": "业务类型(来源)：手动创建(MANUAL)、总部分配(HD_ASSIGN)、计划创建(PLAN)"}, "received_by": {"type": "string", "format": "uint64", "title": "收货门店id"}, "received_code": {"type": "string", "title": "收货门店编码"}, "received_name": {"type": "string", "title": "收货门店名称"}, "distribute_by": {"type": "string", "format": "uint64", "title": "配送方id"}, "store_type": {"type": "string", "title": "门店类型"}, "franchisee_id": {"type": "string", "format": "uint64", "title": "加盟商id"}, "franchisee_code": {"type": "string", "title": "加盟商code"}, "franchisee_name": {"type": "string", "title": "加盟商名称"}, "demand_date": {"type": "string", "format": "date-time", "title": "订货日期"}, "arrival_date": {"type": "string", "format": "date-time", "title": "到货日期"}, "status": {"type": "string", "title": "单据业务状态"}, "process_status": {"type": "string", "title": "处理状态"}, "reject_reason": {"type": "string", "title": "驳回原因"}, "remark": {"type": "string", "title": "备注"}, "reason": {"type": "string", "title": "原因"}, "created_at": {"type": "string", "format": "date-time", "title": "创建日期"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人"}, "created_name": {"type": "string"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "updated_name": {"type": "string"}, "review_by": {"type": "string", "format": "uint64", "title": "审核人id"}, "has_product": {"type": "integer", "format": "int32", "title": "是否有商品"}, "sum_price_tax": {"type": "number", "format": "double", "title": "合计含税金额"}, "sum_tax": {"type": "number", "format": "double", "title": "合计税额"}, "pay_amount": {"type": "number", "format": "double", "title": "实际付款金额"}, "attachments": {"type": "array", "items": {"type": "string"}, "title": "付款凭证（附件）"}, "extends": {"type": "string", "title": "扩展信息:(eg: {\"product_names\": [xxx]})"}, "orders": {"type": "array", "items": {"$ref": "#/definitions/DemandOrder"}, "title": "要货单们"}, "payment_way": {"type": "string", "title": "付款方式\n\"BohCreditPay\"  BOH信用付\n\"WXMPay\"        微信小程序\n\"BohOfflinePay\" 线下支付\n\"BohVoucherPay\" 代金券支付"}, "chain_type": {"type": "string", "title": "门店连锁类型"}, "is_adjust": {"type": "boolean", "format": "boolean", "title": "是否调整了订货数量"}, "confirm_amount": {"type": "number", "format": "double", "title": "确认金额"}, "order_type_id": {"type": "string", "format": "uint64", "title": "订货类型id"}, "order_type_name": {"type": "string", "title": "订货类型名称"}, "order_type_code": {"type": "string", "title": "订货类型编码"}, "sales_amount": {"type": "number", "format": "double", "title": "零售金额"}, "confirm_sales_amount": {"type": "number", "format": "double", "title": "确认零售金额"}, "refunds": {"type": "array", "items": {"$ref": "#/definitions/DemandRefund"}, "title": "退款单们"}, "approve_amount": {"type": "number", "format": "double", "title": "审核金额"}, "approve_sales_amount": {"type": "number", "format": "double", "title": "审核零售金额"}, "order_type_time": {"$ref": "#/definitions/DemandOrderTypeTime"}}}, "franchisee_demandDemandToRemind": {"type": "object", "properties": {"store_code": {"type": "string", "title": "门店编码"}, "store_name": {"type": "string", "title": "门店名称"}, "geo_region_name": {"type": "string"}, "branch_region_name": {"type": "string"}, "franchise_region_name": {"type": "string"}, "type_name": {"type": "string", "title": "订货类型"}, "demand_date": {"type": "string", "format": "date-time", "title": "订货日期"}, "status": {"type": "string", "title": "单据业务状态"}}}, "franchisee_demandGetProductDistributionResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/GetProductDistributionResponseProDetail"}}, "distribute_centers": {"type": "array", "items": {"$ref": "#/definitions/GetProductDistributionResponseDtrDetail"}}, "total": {"type": "integer", "format": "int32"}}}, "franchisee_demandHistory": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "历史记录id"}, "partner_id": {"type": "string", "format": "uint64", "title": "租户ID"}, "demand_id": {"type": "string", "format": "uint64", "title": "订单ID"}, "action": {"type": "string", "title": "操作code\nINITED = \"总部新建\"\nPREPARE = \"手动新建\"\nINVALID = \"总部作废\"\nSUBMITTED = \"付款\"\nR_APPROVE = \"审核\"\nCANCELLED = \"取消\"\nCONFIRMED = \"确认\"\nAPPROVED = \"分配\"\nADD_PRODUCT = \"添加商品\"\nUPDATE = \"更新订单\"\nRESET = \"还原订单\"\nIMPORT = \"导入订单\""}, "action_name": {"type": "string", "title": "操作名称"}, "created_by": {"type": "string", "format": "uint64", "title": "操作人名称或者系统自动操作"}, "created_at": {"type": "string", "format": "date-time", "title": "操作时间"}, "created_name": {"type": "string", "title": "操作人名称"}, "platform": {"type": "string", "title": "操作平台\nHEX_MOBILE   移动端\nHEX_WEB      WEB后台"}, "start_status": {"type": "string", "title": "起始状态"}, "end_status": {"type": "string", "title": "目标状态"}, "trace_id": {"type": "string", "format": "uint64", "title": "链路追踪ID"}, "platform_name": {"type": "string", "title": "操作平台名称"}, "details": {"type": "array", "items": {"$ref": "#/definitions/franchisee_demandProductHistory"}, "title": "变更商品详情"}}}, "franchisee_demandHistoryResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/franchisee_demandHistory"}}, "total": {"type": "string", "format": "uint64"}}}, "franchisee_demandImportDetail": {"type": "object", "properties": {"demand_id": {"type": "string", "format": "uint64"}, "products": {"type": "array", "items": {"$ref": "#/definitions/franchisee_demandImportProduct"}}}}, "franchisee_demandImportFDemandProductsRequest": {"type": "object", "properties": {"import_type": {"type": "string", "title": "导入类型\n导入订单（最大量）Overwrite\n导入订单（增加值）Increase"}, "batch_id": {"type": "string", "format": "uint64"}, "rows": {"type": "array", "items": {"$ref": "#/definitions/franchisee_demandImportDetail"}}}, "title": "导入订货单参数"}, "franchisee_demandImportFDemandProductsResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}, "description": {"type": "string"}}}, "franchisee_demandImportProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "category_id": {"type": "string", "format": "uint64", "title": "商品分类id"}, "unit_id": {"type": "string", "format": "uint64", "title": "单位ID"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "核算单位ID"}, "unit_rate": {"type": "number", "format": "double", "title": "单位转换率"}, "quantity": {"type": "number", "format": "double", "title": "商品数量"}, "order_tax_price": {"type": "number", "format": "double", "title": "含税单价(订货价)"}, "sale_tax_price": {"type": "number", "format": "double", "title": "含税单价(零售价)"}, "tax_rate": {"type": "number", "format": "double", "title": "税率"}, "min_quantity": {"type": "number", "format": "double", "title": "最小订量"}, "max_quantity": {"type": "number", "format": "double", "title": "最大订量"}, "increment_quantity": {"type": "number", "format": "double", "title": "递增订量"}, "arrival_days": {"type": "integer", "format": "int32", "title": "预计到货天数"}}}, "franchisee_demandListFDemandResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/franchisee_demandDemand"}}, "total": {"type": "string", "format": "uint64"}}}, "franchisee_demandListFDemandToRemindResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/franchisee_demandDemandToRemind"}}, "total": {"type": "string", "format": "uint64"}}}, "franchisee_demandProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "数据id"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id(新增必传)"}, "product_code": {"type": "string", "title": "商品编号"}, "product_name": {"type": "string", "title": "商品名称"}, "org_product_id": {"type": "string", "format": "uint64", "title": "原商品ID"}, "org_product_code": {"type": "string", "title": "原商品编号"}, "org_product_name": {"type": "string", "title": "原商品名称"}, "category_id": {"type": "string", "format": "uint64", "title": "商品分类id"}, "category_name": {"type": "string", "title": "商品分类名称"}, "product_spec": {"type": "string", "title": "商品规格"}, "unit_id": {"type": "string", "format": "uint64", "title": "单位ID"}, "unit_name": {"type": "string", "title": "单位名称"}, "unit_spec": {"type": "string", "title": "单位规格"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "核算单位ID"}, "accounting_unit_name": {"type": "string", "title": "核算单位名称"}, "accounting_unit_spec": {"type": "string", "title": "核算单位规格"}, "unit_rate": {"type": "number", "format": "double", "title": "单位转换率"}, "quantity": {"type": "number", "format": "double", "title": "订货数量"}, "accounting_quantity": {"type": "number", "format": "double", "title": "核算数量"}, "min_quantity": {"type": "number", "format": "double", "title": "最小订量"}, "max_quantity": {"type": "number", "format": "double", "title": "最大订量"}, "increment_quantity": {"type": "number", "format": "double", "title": "递增订量"}, "tax_price": {"type": "number", "format": "double", "title": "含税单价"}, "cost_price": {"type": "number", "format": "double", "title": "成本单价"}, "tax_rate": {"type": "number", "format": "double", "title": "税率"}, "amount": {"type": "number", "format": "double", "title": "单项合计金额"}, "arrival_days": {"type": "integer", "format": "int32", "title": "预计到货天数"}, "distribution_type": {"type": "string", "title": "配送方式"}, "distribute_by": {"type": "string", "format": "uint64", "title": "配送方"}, "distribute_name": {"type": "string", "title": "配送方名称"}, "status": {"type": "string", "title": "分配状态\nINITED = \"INITED\"           # 未分配\nPROCESS = \"PROCESS\"         # 已分配\nSUCCESS = \"SUCCESS\"         # 已生单"}, "yesterday_sales": {"type": "number", "format": "double", "title": "昨日销量"}, "inventory_qty": {"type": "number", "format": "double", "title": "实时库存"}, "on_way_qty": {"type": "number", "format": "double", "title": "在途(待收)数量"}, "is_delete": {"type": "integer", "format": "int64", "title": "0未删除 1已删除"}, "created_at": {"type": "string", "format": "date-time", "title": "创建日期"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人"}, "created_name": {"type": "string"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "updated_name": {"type": "string"}, "demand_id": {"type": "string", "format": "uint64", "title": "订单ID"}, "partner_id": {"type": "string", "format": "uint64", "title": "租户ID"}, "allow_order": {"type": "boolean", "format": "boolean", "title": "是否可订货"}, "confirm_quantity": {"type": "number", "format": "double", "title": "确认数量"}, "confirm_amount": {"type": "number", "format": "double", "title": "确认金额"}, "extends": {"type": "string", "title": "扩展信息:(eg: {\"product_price\": [xxx]})"}, "storage_type": {"type": "string", "title": "储藏类型"}, "sales_price": {"type": "number", "format": "double", "title": "零售单价"}, "sales_amount": {"type": "number", "format": "double", "title": "零售金额"}, "confirm_sales_amount": {"type": "number", "format": "double", "title": "确认零售金额"}, "approve_quantity": {"type": "number", "format": "double", "title": "审核数量"}, "approve_amount": {"type": "number", "format": "double", "title": "审核金额"}, "approve_sales_amount": {"type": "number", "format": "double", "title": "审核零售金额"}, "is_confirm_qty": {"type": "boolean", "format": "boolean", "title": "是否调整了确认订货数量"}, "is_approve_qty": {"type": "boolean", "format": "boolean", "title": "是否调整了审核订货数量"}}}, "franchisee_demandProductHistory": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "数据id"}, "partner_id": {"type": "string", "format": "uint64", "title": "租户ID"}, "demand_id": {"type": "string", "format": "uint64", "title": "订货单id"}, "trace_id": {"type": "string", "format": "uint64", "title": "订单操作traceID(对应订单log表ID)"}, "action": {"type": "string", "title": "操作code"}, "action_name": {"type": "string", "title": "操作名称"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_code": {"type": "string", "title": "商品编号"}, "product_name": {"type": "string", "title": "商品名称"}, "unit_id": {"type": "string", "format": "uint64", "title": "单位ID"}, "unit_name": {"type": "string", "title": "单位名称"}, "tax_price": {"type": "string", "title": "订货价"}, "sales_price": {"type": "string", "title": "零售价"}, "quantity": {"type": "string", "title": "订货数量"}, "amount": {"type": "string", "title": "单项合计金额"}, "sales_amount": {"type": "string", "title": "零售金额"}, "approve_quantity": {"type": "string", "title": "审核数量"}, "approve_amount": {"type": "string", "title": "审核金额"}, "approve_sales_amount": {"type": "string", "title": "审核零售金额"}, "confirm_quantity": {"type": "string", "title": "确认数量"}, "confirm_amount": {"type": "string", "title": "确认金额"}, "confirm_sales_amount": {"type": "string", "title": "确认零售金额"}, "arrival_days": {"type": "string", "title": "预计到货天数"}, "distribution_type": {"type": "string", "title": "配送方式"}, "distribute_by": {"type": "string", "format": "uint64", "title": "配送方"}, "distribute_name": {"type": "string", "title": "配送方名称"}, "created_by": {"type": "string", "format": "uint64", "title": "操作人名称或者系统自动操作"}, "created_at": {"type": "string", "format": "date-time", "title": "操作时间"}, "created_name": {"type": "string", "title": "操作人名称"}}}, "franchisee_demandProductsResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/franchisee_demandProduct"}}, "total": {"type": "string", "format": "uint64"}}}, "franchisee_demandTodoReceipt": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "单据id"}, "code": {"type": "string", "title": "单号"}, "status": {"type": "string", "title": "单据状态"}, "type": {"type": "string", "title": "单据类型"}}}, "franchisee_demandUpdateProductRequest": {"type": "object", "properties": {"demand_id": {"type": "string", "format": "uint64", "title": "订货单id"}, "remark": {"type": "string", "title": "备注"}, "status": {"type": "string", "title": "单据状态，对应上方Status枚举, 替换商品/分配时同时支持确认分配"}, "products": {"type": "array", "items": {"$ref": "#/definitions/franchisee_demandProduct"}, "title": "商品"}}}, "franchisee_demandUpdateProductsResponse": {"type": "object", "properties": {"demand_id": {"type": "string", "format": "uint64", "title": "订货单id"}}}}}