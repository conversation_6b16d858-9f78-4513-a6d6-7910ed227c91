{"swagger": "2.0", "info": {"title": "frs_management/franchise_hd_assignment.proto", "version": "version not set"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/frs-management/hd-assignment/demand": {"get": {"summary": "查询门店主配单", "operationId": "ListHdDemand", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frachisee_hd_assignmentListHdDemandResponse"}}}, "parameters": [{"name": "start_date", "description": "订货开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "订货结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "status", "description": "订单状态\n INITED = 新建(总部)\n PREPARE = 待付款(门店)-已提交(总部)\n INVALID = 已作废(总部).", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "received_bys", "description": "门店ids.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "code", "description": "订货单号.", "in": "query", "required": false, "type": "string"}, {"name": "types", "description": "订货单类型：加盟门店订货单(FSD)、加盟门店主配单(FMD).", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "sub_type", "description": "主配类型枚举：按门店主配(STORE)、按商品主配(PRODUCT).", "in": "query", "required": false, "type": "string"}, {"name": "chain_type", "description": "门店连锁类型：加盟(FRS)、直营(DRS).", "in": "query", "required": false, "type": "string"}, {"name": "bus_types", "description": "业务类型(来源)：手动创建(MANUAL)、总部分配(HD_ASSIGN)、计划创建(PLAN).", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "offset", "description": "偏移(默认0).", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "description": "查询的每页数量.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "order", "description": "排序(默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "ids", "description": "订货单ID列表.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "product_ids", "description": "商品ids.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "franchisee_ids", "description": "加盟商ids.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "payment_ways", "description": "支付方式\n\"BohCreditPay\"  BOH信用付\n\"WXMPay\"        微信小程序\n\"BohOfflinePay\" 线下支付\n\"BohVoucherPay\" 代金券支付.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "order_type_ids", "description": "订货类型ids.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["FranchiseeHdAssignment"]}, "post": {"summary": "创建主配单", "operationId": "CreateHdDemand", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frachisee_hd_assignmentCommonResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/frachisee_hd_assignmentCreateHdDemandRequest"}}], "tags": ["FranchiseeHdAssignment"]}}, "/api/v2/supply/frs-management/hd-assignment/demand/product/update": {"put": {"summary": "更新主配单商品信息", "operationId": "UpdateHdDemandProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frachisee_hd_assignmentUpdateHdDemandProductResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/frachisee_hd_assignmentUpdateHdDemandProductRequest"}}], "tags": ["FranchiseeHdAssignment"]}}, "/api/v2/supply/frs-management/hd-assignment/demand/{action}": {"put": {"summary": "修改订单状态统一入口", "operationId": "DealHdDemandById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frachisee_hd_assignmentCommonResponse"}}}, "parameters": [{"name": "action", "description": "单据业务状态，对应Status枚举\n PREPARE = 已提交(总部)\n INVALID = 已作废(总部)", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/frachisee_hd_assignmentDealHdDemandByIdRequest"}}], "tags": ["FranchiseeHdAssignment"]}}, "/api/v2/supply/frs-management/hd-assignment/demand/{demand_id}": {"get": {"summary": "获取主配单", "operationId": "GetHdDemandDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frachisee_hd_assignmentDemand"}}}, "parameters": [{"name": "demand_id", "description": "订货单id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["FranchiseeHdAssignment"]}}, "/api/v2/supply/frs-management/hd-assignment/demand/{demand_id}/product": {"get": {"summary": "获取主配单商品", "operationId": "GetHdDemandProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frachisee_hd_assignmentGetHdDemandProductResponse"}}}, "parameters": [{"name": "demand_id", "description": "订货单id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "limit", "description": "分页限制.", "in": "query", "required": false, "type": "string", "format": "int64"}, {"name": "offset", "description": "偏移量.", "in": "query", "required": false, "type": "string"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "order", "description": "排序方式  asc or desc.", "in": "query", "required": false, "type": "string"}, {"name": "sort", "description": "排序字段.", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["FranchiseeHdAssignment"]}}, "/api/v2/supply/frs-management/hd-assignment/valid/product": {"get": {"summary": "根据门店id获取可主配商品", "operationId": "GetValidProductByStore", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frachisee_hd_assignmentGetValidProductByStoreResponse"}}}, "parameters": [{"name": "store_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "order_type_id", "description": "订货类型id.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["FranchiseeHdAssignment"]}}, "/api/v2/supply/frs-management/hd-assignment/valid/store": {"get": {"summary": "根据商品id获取可订货门店", "operationId": "GetValidStoreByProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/frachisee_hd_assignmentGetValidStoreByProductResponse"}}}, "parameters": [{"name": "product_ids", "description": "商品id.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "order_type_id", "description": "订货类型id.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "demand_date", "description": "订货日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "category_ids", "description": "分类id.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["FranchiseeHdAssignment"]}}}, "definitions": {"CreateHdDemandRequestByProductParams": {"type": "object", "properties": {"received_by": {"type": "string", "format": "uint64", "title": "收货方id"}, "products": {"type": "array", "items": {"$ref": "#/definitions/frachisee_hd_assignmentStoreValidProduct"}, "title": "订货的详细信息"}}}, "DemandOrder": {"type": "object", "properties": {"order_id": {"type": "string", "format": "uint64"}, "order_code": {"type": "string"}}}, "DemandOrderTypeTime": {"type": "object", "properties": {"order_time": {"type": "array", "items": {"type": "string"}}, "audit_time": {"type": "array", "items": {"type": "string"}}}, "title": "订货类型关联时间组"}, "DemandRefund": {"type": "object", "properties": {"refund_id": {"type": "string", "format": "uint64"}, "refund_code": {"type": "string"}}}, "UpdateHdDemandProductRequestUpdateProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "category_id": {"type": "string", "format": "uint64", "title": "商品分类ID"}, "quantity": {"type": "number", "format": "double", "title": "订货数量"}, "unit_id": {"type": "string", "format": "uint64", "title": "订货单位"}, "tax_price": {"type": "number", "format": "double", "title": "含税单价"}, "tax_rate": {"type": "number", "format": "double", "title": "税率"}, "arrival_days": {"type": "number", "format": "float", "title": "预计到货天数"}, "sales_price": {"type": "number", "format": "double", "title": "零售单价"}, "min_quantity": {"type": "number", "format": "double", "title": "最小订量"}, "max_quantity": {"type": "number", "format": "double", "title": "最大订量"}, "increment_quantity": {"type": "number", "format": "double", "title": "递增订量"}}}, "frachisee_hd_assignmentCommonResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}, "title": "统一返回对象"}, "frachisee_hd_assignmentCreateHdDemandRequest": {"type": "object", "properties": {"demand_date": {"type": "string", "format": "date-time", "title": "订货日期"}, "arrival_date": {"type": "string", "format": "date-time", "title": "到货日期"}, "remark": {"type": "string", "title": "备注"}, "type": {"type": "string", "title": "加盟门店主配单:FMD"}, "sub_type": {"type": "string", "title": "主配类型：按门店主配(STORE)、按商品主配(PRODUCT)"}, "by_product_params": {"$ref": "#/definitions/CreateHdDemandRequestByProductParams", "title": "按商品分配：多商品、单门店"}, "by_store_params": {"$ref": "#/definitions/frachisee_hd_assignmentProductValidStore", "title": "按门店主配：单商品、多门店"}, "bus_type": {"type": "string", "title": "业务类型(来源)：手动创建(MANUAL)、总部分配(HD_ASSIGN)、计划创建(PLAN)"}, "batch_id": {"type": "string", "format": "uint64", "title": "唯一请求id"}, "order_type_id": {"type": "string", "format": "uint64", "title": "订货类型id"}}, "title": "创建商品主配单请求参数"}, "frachisee_hd_assignmentDealHdDemandByIdRequest": {"type": "object", "properties": {"demand_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "批量只支持'提交'和'作废'操作"}, "action": {"type": "string", "title": "单据业务状态，对应Status枚举\n PREPARE = 已提交(总部)\n INVALID = 已作废(总部)"}, "remark": {"type": "string", "title": "备注"}, "attachments": {"type": "array", "items": {"type": "string"}, "title": "附件"}}}, "frachisee_hd_assignmentDemand": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "单据id"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "batch_id": {"type": "string", "format": "uint64", "title": "生成的批次id"}, "code": {"type": "string", "title": "编码"}, "type": {"type": "string", "title": "订货单类型"}, "sub_type": {"type": "string", "title": "主配类型(主配单才有的字段)"}, "bus_type": {"type": "string", "title": "业务类型(来源)：手动创建(MANUAL)、总部分配(HD_ASSIGN)、计划创建(PLAN)"}, "received_by": {"type": "string", "format": "uint64", "title": "收货门店id"}, "received_code": {"type": "string", "title": "收货门店编码"}, "received_name": {"type": "string", "title": "收货门店名称"}, "distribute_by": {"type": "string", "format": "uint64", "title": "配送方id"}, "store_type": {"type": "string", "title": "门店类型"}, "franchisee_id": {"type": "string", "format": "uint64", "title": "加盟商id"}, "franchisee_code": {"type": "string", "title": "加盟商code"}, "franchisee_name": {"type": "string", "title": "加盟商名称"}, "demand_date": {"type": "string", "format": "date-time", "title": "订货日期"}, "arrival_date": {"type": "string", "format": "date-time", "title": "到货日期"}, "status": {"type": "string", "title": "单据业务状态"}, "process_status": {"type": "string", "title": "处理状态"}, "reject_reason": {"type": "string", "title": "驳回原因"}, "remark": {"type": "string", "title": "备注"}, "reason": {"type": "string", "title": "原因"}, "created_at": {"type": "string", "format": "date-time", "title": "创建日期"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人"}, "created_name": {"type": "string"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "updated_name": {"type": "string"}, "review_by": {"type": "string", "format": "uint64", "title": "审核人id"}, "has_product": {"type": "integer", "format": "int32", "title": "是否有商品"}, "sum_price_tax": {"type": "number", "format": "double", "title": "合计含税金额"}, "sum_tax": {"type": "number", "format": "double", "title": "合计税额"}, "pay_amount": {"type": "number", "format": "double", "title": "实际付款金额"}, "attachments": {"type": "array", "items": {"type": "string"}, "title": "付款凭证（附件）"}, "extends": {"type": "string", "title": "扩展信息:(eg: {\"product_names\": [xxx]})"}, "orders": {"type": "array", "items": {"$ref": "#/definitions/DemandOrder"}, "title": "要货单们"}, "payment_way": {"type": "string", "title": "付款方式\n\"BohCreditPay\"  BOH信用付\n\"WXMPay\"        微信小程序\n\"BohOfflinePay\" 线下支付\n\"BohVoucherPay\" 代金券支付"}, "chain_type": {"type": "string", "title": "门店连锁类型"}, "is_adjust": {"type": "boolean", "format": "boolean", "title": "是否调整了订货数量"}, "confirm_amount": {"type": "number", "format": "double", "title": "确认金额"}, "order_type_id": {"type": "string", "format": "uint64", "title": "订货类型id"}, "order_type_name": {"type": "string", "title": "订货类型名称"}, "order_type_code": {"type": "string", "title": "订货类型编码"}, "sales_amount": {"type": "number", "format": "double", "title": "零售金额"}, "confirm_sales_amount": {"type": "number", "format": "double", "title": "确认零售金额"}, "refunds": {"type": "array", "items": {"$ref": "#/definitions/DemandRefund"}, "title": "退款单们"}, "approve_amount": {"type": "number", "format": "double", "title": "审核金额"}, "approve_sales_amount": {"type": "number", "format": "double", "title": "审核零售金额"}, "order_type_time": {"$ref": "#/definitions/DemandOrderTypeTime"}}}, "frachisee_hd_assignmentGetHdDemandProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/frachisee_hd_assignmentProduct"}}, "total": {"type": "string", "format": "uint64"}}, "title": "获取订货商品返回参数"}, "frachisee_hd_assignmentGetValidProductByStoreResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/frachisee_hd_assignmentStoreValidProduct"}}, "total": {"type": "integer", "format": "int64"}}}, "frachisee_hd_assignmentGetValidStoreByProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/frachisee_hd_assignmentProductValidStore"}}, "total": {"type": "integer", "format": "int64"}}, "title": "根据商品id获得可订货门店返回参数"}, "frachisee_hd_assignmentListHdDemandResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/frachisee_hd_assignmentDemand"}}, "total": {"type": "string", "format": "uint64"}}, "title": "查询门店订货单"}, "frachisee_hd_assignmentProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "数据id"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_code": {"type": "string", "title": "商品编号"}, "product_name": {"type": "string", "title": "商品名称"}, "org_product_id": {"type": "string", "format": "uint64", "title": "原商品ID"}, "org_product_code": {"type": "string", "title": "原商品编号"}, "org_product_name": {"type": "string", "title": "原商品名称"}, "category_id": {"type": "string", "format": "uint64", "title": "商品分类id"}, "category_name": {"type": "string", "title": "商品分类名称"}, "product_spec": {"type": "string", "title": "商品规格"}, "unit_id": {"type": "string", "format": "uint64", "title": "单位ID"}, "unit_name": {"type": "string", "title": "单位名称"}, "unit_spec": {"type": "string", "title": "单位规格"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "核算单位ID"}, "accounting_unit_name": {"type": "string", "title": "核算单位名称"}, "accounting_unit_spec": {"type": "string", "title": "核算单位规格"}, "unit_rate": {"type": "number", "format": "double", "title": "单位转换率"}, "quantity": {"type": "number", "format": "double", "title": "订货数量"}, "accounting_quantity": {"type": "number", "format": "double", "title": "核算数量"}, "min_quantity": {"type": "number", "format": "double", "title": "最小订量"}, "max_quantity": {"type": "number", "format": "double", "title": "最大订量"}, "increment_quantity": {"type": "number", "format": "double", "title": "递增订量"}, "tax_price": {"type": "number", "format": "double", "title": "含税单价"}, "cost_price": {"type": "number", "format": "double", "title": "成本单价"}, "tax_rate": {"type": "number", "format": "double", "title": "税率"}, "amount": {"type": "number", "format": "double", "title": "单项合计金额"}, "arrival_days": {"type": "number", "format": "float", "title": "预计到货天数"}, "distribution_type": {"type": "string", "title": "配送方式"}, "distribute_by": {"type": "string", "format": "uint64", "title": "配送方"}, "distribute_name": {"type": "string", "title": "配送方名称"}, "status": {"type": "string", "title": "分配状态"}, "yesterday_sales": {"type": "number", "format": "double", "title": "昨日销量"}, "inventory_qty": {"type": "number", "format": "double", "title": "实时库存"}, "on_way_qty": {"type": "number", "format": "double", "title": "在途(待收)数量"}, "is_delete": {"type": "integer", "format": "int64", "title": "0未删除 1已删除"}, "created_at": {"type": "string", "format": "date-time", "title": "创建日期"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人"}, "created_name": {"type": "string"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "updated_name": {"type": "string"}, "demand_id": {"type": "string", "format": "uint64", "title": "订单ID"}, "partner_id": {"type": "string", "format": "uint64", "title": "租户ID"}, "allow_order": {"type": "boolean", "format": "boolean", "title": "是否可订货"}, "confirm_quantity": {"type": "number", "format": "double", "title": "确认数量"}, "confirm_amount": {"type": "number", "format": "double", "title": "确认金额"}, "extends": {"type": "string", "title": "扩展信息:(eg: {\"product_price\": [xxx]})"}, "storage_type": {"type": "string", "title": "储藏类型"}, "sales_price": {"type": "number", "format": "double", "title": "零售单价"}, "sales_amount": {"type": "number", "format": "double", "title": "零售金额"}, "confirm_sales_amount": {"type": "number", "format": "double", "title": "确认零售金额"}, "approve_quantity": {"type": "number", "format": "double", "title": "审核数量"}, "approve_amount": {"type": "number", "format": "double", "title": "审核金额"}, "approve_sales_amount": {"type": "number", "format": "double", "title": "审核零售金额"}, "is_confirm_qty": {"type": "boolean", "format": "boolean", "title": "是否调整了确认订货数量"}, "is_approve_qty": {"type": "boolean", "format": "boolean", "title": "是否调整了审核订货数量"}}}, "frachisee_hd_assignmentProductValidStore": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64", "title": "商品id(新建时必传)"}, "product_code": {"type": "string", "title": "商品编码"}, "product_name": {"type": "string", "title": "商品名称"}, "unit_id": {"type": "string", "format": "uint64", "title": "订货单位id(新建时必传)"}, "unit_name": {"type": "string", "title": "订货单位名称"}, "unit_spec": {"type": "string", "title": "订货单位规格"}, "quantity": {"type": "number", "format": "double", "title": "订货数量"}, "stores": {"type": "array", "items": {"$ref": "#/definitions/frachisee_hd_assignmentValidStore"}, "title": "可主配门店"}}}, "frachisee_hd_assignmentStoreValidProduct": {"type": "object", "properties": {"arrival_days": {"type": "integer", "format": "int32", "title": "到货天数"}, "distribute_by": {"type": "string", "format": "uint64", "title": "配送/供应商中心"}, "distribution_type": {"type": "string", "title": "配送(NMD)、采购(PUR)"}, "increment_quantity": {"type": "number", "format": "double", "title": "递增订量"}, "max_quantity": {"type": "number", "format": "double", "title": "最大订货量"}, "min_quantity": {"type": "number", "format": "double", "title": "最小订货量"}, "unit_id": {"type": "string", "format": "uint64", "title": "订货单位id"}, "unit_name": {"type": "string", "title": "订货单位名称"}, "unit_spec": {"type": "string", "title": "订货单位规格"}, "tax_price": {"type": "number", "format": "double", "title": "含税价"}, "tax_rate": {"type": "number", "format": "double", "title": "税率"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "product_code": {"type": "string", "title": "商品编码"}, "product_name": {"type": "string", "title": "商品名称"}, "category_id": {"type": "string", "format": "uint64", "title": "商品分类ID"}, "category_name": {"type": "string", "title": "商品分类ID"}, "sales_price": {"type": "number", "format": "double", "title": "零售价"}, "extends": {"type": "string", "title": "扩展信息（商品价格等）"}, "quantity": {"type": "number", "format": "double", "title": "订货数量"}}}, "frachisee_hd_assignmentUpdateHdDemandProductRequest": {"type": "object", "properties": {"demand_id": {"type": "string", "format": "uint64", "title": "订货单id"}, "remark": {"type": "string", "title": "备注"}, "products": {"type": "array", "items": {"$ref": "#/definitions/UpdateHdDemandProductRequestUpdateProduct"}, "title": "商品"}}, "title": "更新订货单信息参数"}, "frachisee_hd_assignmentUpdateHdDemandProductResponse": {"type": "object", "properties": {"demand_id": {"type": "string", "format": "uint64", "title": "订货单id"}, "description": {"type": "string", "title": "描述"}, "product_names": {"type": "array", "items": {"type": "string"}, "title": "不合法商品列表"}}}, "frachisee_hd_assignmentValidStore": {"type": "object", "properties": {"store_id": {"type": "string", "format": "uint64", "title": "门店id(新建时必传)"}, "store_code": {"type": "string", "title": "门店code"}, "store_name": {"type": "string", "title": "门店名称"}, "geo_region": {"type": "string", "format": "uint64", "title": "地理区域"}, "tax_price": {"type": "number", "format": "double", "title": "含税价(新建时必传)"}, "tax_rate": {"type": "number", "format": "double", "title": "税率(新建时必传)"}, "arrival_days": {"type": "integer", "format": "int32", "title": "到货天数(新建时必传)"}, "distribute_by": {"type": "string", "format": "uint64", "title": "配送/供应商中心(新建时必传)"}, "distribution_type": {"type": "string", "title": "配送(NMD)、采购(PUR)(新建时必传)"}, "increment_quantity": {"type": "number", "format": "double", "title": "递增订量(新建时必传)"}, "max_quantity": {"type": "number", "format": "double", "title": "最大订货量(新建时必传)"}, "min_quantity": {"type": "number", "format": "double", "title": "最小订货量(新建时必传)"}, "sales_price": {"type": "number", "format": "double", "title": "零售价(新建时必传)"}, "extends": {"type": "string", "title": "扩展信息（商品价格等）(新建时必传)"}}}}}