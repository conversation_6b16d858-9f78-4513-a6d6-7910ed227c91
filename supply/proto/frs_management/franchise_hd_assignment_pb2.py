# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frs_management/franchise_hd_assignment.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frs_management/franchise_hd_assignment.proto',
  package='frachisee_hd_assignment',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n,frs_management/franchise_hd_assignment.proto\x12\x17\x66rachisee_hd_assignment\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x97\x01\n\x0e\x43ommonResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x38\n\x04msgs\x18\x02 \x03(\x0b\x32*.frachisee_hd_assignment.CheckOrderMessage\x12;\n\x05pmsgs\x18\x03 \x03(\x0b\x32,.frachisee_hd_assignment.CheckProductMessage\"\x9f\x01\n\x1dGetValidStoreByProductRequest\x12\x13\n\x0bproduct_ids\x18\x01 \x03(\x04\x12\x15\n\rorder_type_id\x18\x02 \x01(\x04\x12/\n\x0b\x64\x65mand_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63\x61tegory_ids\x18\x05 \x03(\x04\x12\x0b\n\x03lan\x18\x06 \x01(\t\"i\n\x1eGetValidStoreByProductResponse\x12\x38\n\x04rows\x18\x01 \x03(\x0b\x32*.frachisee_hd_assignment.ProductValidStore\x12\r\n\x05total\x18\x02 \x01(\r\"\xb5\x02\n\nValidStore\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x12\n\nstore_code\x18\x02 \x01(\t\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x12\n\ngeo_region\x18\x04 \x01(\x04\x12\x11\n\ttax_price\x18\x05 \x01(\x01\x12\x10\n\x08tax_rate\x18\x06 \x01(\x01\x12\x14\n\x0c\x61rrival_days\x18\x07 \x01(\t\x12\x15\n\rdistribute_by\x18\x08 \x01(\x04\x12\x19\n\x11\x64istribution_type\x18\t \x01(\t\x12\x1a\n\x12increment_quantity\x18\n \x01(\t\x12\x14\n\x0cmax_quantity\x18\x0b \x01(\t\x12\x14\n\x0cmin_quantity\x18\x0c \x01(\t\x12\x13\n\x0bsales_price\x18\r \x01(\x01\x12\x0f\n\x07\x65xtends\x18\x0e \x01(\t\"\xd1\x01\n\x11ProductValidStore\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12\x0f\n\x07unit_id\x18\x04 \x01(\x04\x12\x11\n\tunit_name\x18\x05 \x01(\t\x12\x11\n\tunit_spec\x18\x06 \x01(\t\x12\x10\n\x08quantity\x18\x07 \x01(\x01\x12\x33\n\x06stores\x18\n \x03(\x0b\x32#.frachisee_hd_assignment.ValidStore\"\xba\x04\n\x11StoreValidProduct\x12\x14\n\x0c\x61rrival_days\x18\x01 \x01(\t\x12\x15\n\rdistribute_by\x18\x02 \x01(\x04\x12\x19\n\x11\x64istribution_type\x18\x03 \x01(\t\x12\x1a\n\x12increment_quantity\x18\x04 \x01(\t\x12\x14\n\x0cmax_quantity\x18\x05 \x01(\t\x12\x14\n\x0cmin_quantity\x18\x06 \x01(\t\x12\x0f\n\x07unit_id\x18\x07 \x01(\x04\x12\x11\n\tunit_name\x18\x08 \x01(\t\x12\x11\n\tunit_spec\x18\t \x01(\t\x12\x11\n\ttax_price\x18\n \x01(\x01\x12\x10\n\x08tax_rate\x18\x0b \x01(\x01\x12\x12\n\nproduct_id\x18\x0c \x01(\x04\x12\x14\n\x0cproduct_code\x18\r \x01(\t\x12\x14\n\x0cproduct_name\x18\x0e \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x0f \x01(\x04\x12\x15\n\rcategory_name\x18\x10 \x01(\t\x12\x13\n\x0bsales_price\x18\x11 \x01(\x01\x12\x0f\n\x07\x65xtends\x18\x12 \x01(\t\x12\x10\n\x08quantity\x18\x13 \x01(\x01\x12\x45\n\x11relation_products\x18\x14 \x03(\x0b\x32*.frachisee_hd_assignment.StoreValidProduct\x12\r\n\x05ratio\x18\x15 \x01(\t\x12\x11\n\tconfigure\x18\x16 \x01(\x05\x12\x10\n\x08store_id\x18\x17 \x01(\x04\x12\x1a\n\x12real_inventory_qty\x18\x18 \x01(\x01\"\xbb\x01\n\x1dGetValidProductByStoreRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x15\n\rorder_type_id\x18\x02 \x01(\x04\x12\x0b\n\x03lan\x18\x05 \x01(\t\x12\x12\n\nproduct_id\x18\x06 \x01(\x04\x12\x11\n\tstore_ids\x18\x07 \x03(\x04\x12\x10\n\x08order_by\x18\x08 \x01(\t\x12\x0c\n\x04sort\x18\t \x01(\t\x12\x0e\n\x06offset\x18\n \x01(\x04\x12\r\n\x05limit\x18\x0b \x01(\x04\"\xb7\x01\n\x1eGetValidProductByStoreResponse\x12\x38\n\x04rows\x18\x01 \x03(\x0b\x32*.frachisee_hd_assignment.StoreValidProduct\x12\r\n\x05total\x18\x02 \x01(\r\x12L\n\x18inventory_unchanged_rows\x18\x03 \x03(\x0b\x32*.frachisee_hd_assignment.StoreValidProduct\"\xeb\x03\n\x15\x43reateHdDemandRequest\x12/\n\x0b\x64\x65mand_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06remark\x18\x03 \x01(\t\x12\x0c\n\x04type\x18\x04 \x01(\t\x12\x10\n\x08sub_type\x18\x05 \x01(\t\x12Y\n\x11\x62y_product_params\x18\x06 \x01(\x0b\x32>.frachisee_hd_assignment.CreateHdDemandRequest.ByProductParams\x12\x43\n\x0f\x62y_store_params\x18\x07 \x01(\x0b\x32*.frachisee_hd_assignment.ProductValidStore\x12\x10\n\x08\x62us_type\x18\x08 \x01(\t\x12\x10\n\x08\x62\x61tch_id\x18\t \x01(\x04\x12\x15\n\rorder_type_id\x18\n \x01(\x04\x1a\x64\n\x0f\x42yProductParams\x12\x13\n\x0breceived_by\x18\x01 \x01(\x04\x12<\n\x08products\x18\x02 \x03(\x0b\x32*.frachisee_hd_assignment.StoreValidProduct\"+\n\tIdRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\x1f\n\nIdResponse\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\"\xb7\x03\n\x13ListHdDemandRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x03 \x03(\t\x12\x14\n\x0creceived_bys\x18\x04 \x03(\x04\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\r\n\x05types\x18\x06 \x03(\t\x12\x10\n\x08sub_type\x18\x07 \x01(\t\x12\x12\n\nchain_type\x18\x08 \x01(\t\x12\x11\n\tbus_types\x18\t \x03(\t\x12\x0e\n\x06offset\x18\n \x01(\x04\x12\r\n\x05limit\x18\x0b \x01(\x04\x12\r\n\x05order\x18\x0c \x01(\t\x12\x0c\n\x04sort\x18\r \x01(\t\x12\x15\n\rinclude_total\x18\x0e \x01(\x08\x12\x0b\n\x03ids\x18\x0f \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x10 \x03(\x04\x12\x16\n\x0e\x66ranchisee_ids\x18\x11 \x03(\x04\x12\x14\n\x0cpayment_ways\x18\x12 \x03(\t\x12\x16\n\x0eorder_type_ids\x18\x13 \x03(\x04\x12\x0b\n\x03lan\x18\x14 \x01(\t\"T\n\x14ListHdDemandResponse\x12-\n\x04rows\x18\x01 \x03(\x0b\x32\x1f.frachisee_hd_assignment.Demand\x12\r\n\x05total\x18\x02 \x01(\x04\"\x9e\x01\n\x19GetHdDemandProductRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x03\x12\x0e\n\x06offset\x18\x03 \x01(\t\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12\r\n\x05order\x18\x05 \x01(\t\x12\x0c\n\x04sort\x18\x06 \x01(\t\x12\x0b\n\x03lan\x18\x07 \x01(\t\x12\x0e\n\x06\x65xpand\x18\x08 \x01(\x08\"[\n\x1aGetHdDemandProductResponse\x12.\n\x04rows\x18\x01 \x03(\x0b\x32 .frachisee_hd_assignment.Product\x12\r\n\x05total\x18\x02 \x01(\x04\"\xfe\n\n\x07Product\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x03 \x01(\t\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12\x16\n\x0eorg_product_id\x18\x05 \x01(\x04\x12\x18\n\x10org_product_code\x18\x06 \x01(\t\x12\x18\n\x10org_product_name\x18\x07 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x08 \x01(\x04\x12\x15\n\rcategory_name\x18\t \x01(\t\x12\x14\n\x0cproduct_spec\x18\n \x01(\t\x12\x0f\n\x07unit_id\x18\x0b \x01(\x04\x12\x11\n\tunit_name\x18\x0c \x01(\t\x12\x11\n\tunit_spec\x18\r \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x0e \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x0f \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x10 \x01(\t\x12\x11\n\tunit_rate\x18\x11 \x01(\x01\x12\x10\n\x08quantity\x18\x12 \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x13 \x01(\x01\x12\x14\n\x0cmin_quantity\x18\x14 \x01(\x01\x12\x14\n\x0cmax_quantity\x18\x15 \x01(\x01\x12\x1a\n\x12increment_quantity\x18\x16 \x01(\x01\x12\x11\n\ttax_price\x18\x17 \x01(\x01\x12\x12\n\ncost_price\x18\x18 \x01(\x01\x12\x10\n\x08tax_rate\x18\x19 \x01(\x01\x12\x0e\n\x06\x61mount\x18\x1a \x01(\x01\x12\x14\n\x0c\x61rrival_days\x18\x1b \x01(\x02\x12\x19\n\x11\x64istribution_type\x18\x1c \x01(\t\x12\x15\n\rdistribute_by\x18\x1d \x01(\x04\x12\x17\n\x0f\x64istribute_name\x18\x1e \x01(\t\x12\x0e\n\x06status\x18\x1f \x01(\t\x12\x17\n\x0fyesterday_sales\x18  \x01(\x01\x12\x15\n\rinventory_qty\x18! \x01(\x01\x12\x12\n\non_way_qty\x18\" \x01(\x01\x12\x11\n\tis_delete\x18# \x01(\r\x12.\n\ncreated_at\x18$ \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18% \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18& \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\' \x01(\t\x12\x12\n\nupdated_by\x18( \x01(\x04\x12\x14\n\x0cupdated_name\x18) \x01(\t\x12\x11\n\tdemand_id\x18* \x01(\x04\x12\x12\n\npartner_id\x18+ \x01(\x04\x12\x13\n\x0b\x61llow_order\x18, \x01(\x08\x12\x18\n\x10\x63onfirm_quantity\x18- \x01(\x01\x12\x16\n\x0e\x63onfirm_amount\x18. \x01(\x01\x12\x0f\n\x07\x65xtends\x18/ \x01(\t\x12\x14\n\x0cstorage_type\x18\x30 \x01(\t\x12\x13\n\x0bsales_price\x18\x31 \x01(\x01\x12\x14\n\x0csales_amount\x18\x32 \x01(\x01\x12\x1c\n\x14\x63onfirm_sales_amount\x18\x33 \x01(\x01\x12\x18\n\x10\x61pprove_quantity\x18\x34 \x01(\x01\x12\x16\n\x0e\x61pprove_amount\x18\x35 \x01(\x01\x12\x1c\n\x14\x61pprove_sales_amount\x18\x36 \x01(\x01\x12\x16\n\x0eis_confirm_qty\x18\x37 \x01(\x08\x12\x16\n\x0eis_approve_qty\x18\x38 \x01(\x08\x12\r\n\x05ratio\x18\x39 \x01(\t\x12\x11\n\tconfigure\x18: \x01(\x05\x12;\n\x11relation_products\x18; \x03(\x0b\x32 .frachisee_hd_assignment.Product\"\x8a\x0b\n\x06\x44\x65mand\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08\x62\x61tch_id\x18\x03 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\x0c\n\x04type\x18\x05 \x01(\t\x12\x10\n\x08sub_type\x18\x06 \x01(\t\x12\x10\n\x08\x62us_type\x18\x07 \x01(\t\x12\x13\n\x0breceived_by\x18\x08 \x01(\x04\x12\x15\n\rreceived_code\x18\t \x01(\t\x12\x15\n\rreceived_name\x18\n \x01(\t\x12\x15\n\rdistribute_by\x18\x0b \x01(\x04\x12\x12\n\nstore_type\x18\x0c \x01(\t\x12\x15\n\rfranchisee_id\x18\r \x01(\x04\x12\x17\n\x0f\x66ranchisee_code\x18\x0e \x01(\t\x12\x17\n\x0f\x66ranchisee_name\x18\x0f \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x12 \x01(\t\x12\x16\n\x0eprocess_status\x18\x13 \x01(\t\x12\x15\n\rreject_reason\x18\x14 \x01(\t\x12\x0e\n\x06remark\x18\x15 \x01(\t\x12\x0e\n\x06reason\x18\x16 \x01(\t\x12.\n\ncreated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x19 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1a \x01(\t\x12\x12\n\nupdated_by\x18\x1b \x01(\x04\x12\x14\n\x0cupdated_name\x18\x1c \x01(\t\x12\x11\n\treview_by\x18\x1d \x01(\x04\x12\x13\n\x0bhas_product\x18\x1e \x01(\x05\x12\x15\n\rsum_price_tax\x18\x1f \x01(\x01\x12\x0f\n\x07sum_tax\x18  \x01(\x01\x12\x12\n\npay_amount\x18! \x01(\x01\x12\x13\n\x0b\x61ttachments\x18\" \x03(\t\x12\x0f\n\x07\x65xtends\x18# \x01(\t\x12\x35\n\x06orders\x18$ \x03(\x0b\x32%.frachisee_hd_assignment.Demand.Order\x12\x13\n\x0bpayment_way\x18% \x01(\t\x12\x12\n\nchain_type\x18& \x01(\t\x12\x11\n\tis_adjust\x18\' \x01(\x08\x12\x16\n\x0e\x63onfirm_amount\x18( \x01(\x01\x12\x15\n\rorder_type_id\x18) \x01(\x04\x12\x17\n\x0forder_type_name\x18* \x01(\t\x12\x17\n\x0forder_type_code\x18+ \x01(\t\x12\x14\n\x0csales_amount\x18, \x01(\x01\x12\x1c\n\x14\x63onfirm_sales_amount\x18- \x01(\x01\x12\x37\n\x07refunds\x18. \x03(\x0b\x32&.frachisee_hd_assignment.Demand.Refund\x12\x16\n\x0e\x61pprove_amount\x18/ \x01(\x01\x12\x1c\n\x14\x61pprove_sales_amount\x18\x30 \x01(\x01\x12\x46\n\x0forder_type_time\x18\x31 \x01(\x0b\x32-.frachisee_hd_assignment.Demand.OrderTypeTime\x1a-\n\x05Order\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x12\n\norder_code\x18\x02 \x01(\t\x1a\x30\n\x06Refund\x12\x11\n\trefund_id\x18\x01 \x01(\x04\x12\x13\n\x0brefund_code\x18\x02 \x01(\t\x1a\x37\n\rOrderTypeTime\x12\x12\n\norder_time\x18\x01 \x03(\t\x12\x12\n\naudit_time\x18\x02 \x03(\t\"\x90\x04\n\x1cUpdateHdDemandProductRequest\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12\x0e\n\x06remark\x18\x02 \x01(\t\x12U\n\x08products\x18\x04 \x03(\x0b\x32\x43.frachisee_hd_assignment.UpdateHdDemandProductRequest.UpdateProduct\x1a\xf5\x02\n\rUpdateProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x13\n\x0b\x63\x61tegory_id\x18\x02 \x01(\x04\x12\x10\n\x08quantity\x18\x03 \x01(\x01\x12\x0f\n\x07unit_id\x18\x04 \x01(\x04\x12\x11\n\ttax_price\x18\x05 \x01(\x01\x12\x10\n\x08tax_rate\x18\x06 \x01(\x01\x12\x14\n\x0c\x61rrival_days\x18\x07 \x01(\x02\x12\x13\n\x0bsales_price\x18\x08 \x01(\x01\x12\x14\n\x0cmin_quantity\x18\x0c \x01(\x01\x12\x14\n\x0cmax_quantity\x18\r \x01(\x01\x12\x1a\n\x12increment_quantity\x18\x0e \x01(\x01\x12^\n\x11relation_products\x18\x0f \x03(\x0b\x32\x43.frachisee_hd_assignment.UpdateHdDemandProductRequest.UpdateProduct\x12\r\n\x05ratio\x18\x10 \x01(\t\x12\x11\n\tconfigure\x18\x11 \x01(\x05\"^\n\x1dUpdateHdDemandProductResponse\x12\x11\n\tdemand_id\x18\x01 \x01(\x04\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x15\n\rproduct_names\x18\x03 \x03(\t\"w\n\x17\x44\x65\x61lHdDemandByIdRequest\x12\x12\n\ndemand_ids\x18\x01 \x03(\x04\x12\x0e\n\x06\x61\x63tion\x18\x02 \x01(\t\x12\x0e\n\x06remark\x18\x03 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x04 \x03(\t\x12\x13\n\x0bis_detailed\x18\x05 \x01(\x08\"\x90\x02\n\x13\x43heckProductMessage\x12\x14\n\x0cproduct_name\x18\x01 \x01(\t\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x0c\n\x04spec\x18\x03 \x01(\t\x12\x10\n\x08quantity\x18\x04 \x01(\x01\x12\x14\n\x0cmin_quantity\x18\x05 \x01(\x01\x12\x1a\n\x12increment_quantity\x18\x06 \x01(\x01\x12\x14\n\x0cmax_quantity\x18\x07 \x01(\x01\x12\x0c\n\x04unit\x18\x08 \x01(\t\x12\x11\n\ttax_price\x18\t \x01(\x01\x12\x0e\n\x06\x61mount\x18\n \x01(\x01\x12\x14\n\x0cstorage_type\x18\x0b \x01(\t\x12\n\n\x02id\x18\x0c \x01(\x04\x12\x12\n\nproduct_id\x18\r \x01(\x04\"\xf5\x01\n\x11\x43heckOrderMessage\x12\x0e\n\x06status\x18\x01 \x01(\t\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x15\n\rorder_type_id\x18\x04 \x01(\x04\x12\x17\n\x0f\x66ranchisee_name\x18\x05 \x01(\t\x12\x0e\n\x06\x61mount\x18\x06 \x01(\x01\x12/\n\x0b\x64\x65mand_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0b\n\x03msg\x18\t \x01(\t\"\x9e\x02\n\x18\x42\x61tchDealHdDemandRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0creceived_bys\x18\x03 \x03(\x04\x12\x11\n\tbus_types\x18\x04 \x03(\t\x12\x16\n\x0eorder_type_ids\x18\x05 \x03(\x04\x12\x13\n\x0bline_doc_id\x18\x06 \x01(\x04\x12\x14\n\x0c\x62\x61tch_method\x18\x08 \x01(\t\x12\x13\n\x0b\x65xclude_ids\x18\t \x03(\x04\x12\x13\n\x0binclude_ids\x18\n \x03(\x04\x12\x0e\n\x06\x61\x63tion\x18\x0b \x01(\t\"z\n\x19\x42\x61tchDealHdDemandResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x38\n\x04msgs\x18\x03 \x03(\x0b\x32*.frachisee_hd_assignment.CheckOrderMessage2\xd1\r\n\x16\x46ranchiseeHdAssignment\x12\xa8\x01\n\x0e\x43reateHdDemand\x12..frachisee_hd_assignment.CreateHdDemandRequest\x1a\'.frachisee_hd_assignment.CommonResponse\"=\x82\xd3\xe4\x93\x02\x37\"2/api/v2/supply/frs-management/hd-assignment/demand:\x01*\x12\xca\x01\n\x16GetValidStoreByProduct\x12\x36.frachisee_hd_assignment.GetValidStoreByProductRequest\x1a\x37.frachisee_hd_assignment.GetValidStoreByProductResponse\"?\x82\xd3\xe4\x93\x02\x39\x12\x37/api/v2/supply/frs-management/hd-assignment/valid/store\x12\xcc\x01\n\x16GetValidProductByStore\x12\x36.frachisee_hd_assignment.GetValidProductByStoreRequest\x1a\x37.frachisee_hd_assignment.GetValidProductByStoreResponse\"A\x82\xd3\xe4\x93\x02;\x12\x39/api/v2/supply/frs-management/hd-assignment/valid/product\x12\xa7\x01\n\x0cListHdDemand\x12,.frachisee_hd_assignment.ListHdDemandRequest\x1a-.frachisee_hd_assignment.ListHdDemandResponse\":\x82\xd3\xe4\x93\x02\x34\x12\x32/api/v2/supply/frs-management/hd-assignment/demand\x12\xa0\x01\n\x11GetHdDemandDetail\x12\".frachisee_hd_assignment.IdRequest\x1a\x1f.frachisee_hd_assignment.Demand\"F\x82\xd3\xe4\x93\x02@\x12>/api/v2/supply/frs-management/hd-assignment/demand/{demand_id}\x12\xcd\x01\n\x12GetHdDemandProduct\x12\x32.frachisee_hd_assignment.GetHdDemandProductRequest\x1a\x33.frachisee_hd_assignment.GetHdDemandProductResponse\"N\x82\xd3\xe4\x93\x02H\x12\x46/api/v2/supply/frs-management/hd-assignment/demand/{demand_id}/product\x12\xd4\x01\n\x15UpdateHdDemandProduct\x12\x35.frachisee_hd_assignment.UpdateHdDemandProductRequest\x1a\x36.frachisee_hd_assignment.UpdateHdDemandProductResponse\"L\x82\xd3\xe4\x93\x02\x46\x1a\x41/api/v2/supply/frs-management/hd-assignment/demand/product/update:\x01*\x12\xb5\x01\n\x10\x44\x65\x61lHdDemandById\x12\x30.frachisee_hd_assignment.DealHdDemandByIdRequest\x1a\'.frachisee_hd_assignment.CommonResponse\"F\x82\xd3\xe4\x93\x02@\x1a;/api/v2/supply/frs-management/hd-assignment/demand/{action}:\x01*\x12\xc3\x01\n\x14\x42\x61tchConfirmHdDemand\x12\x31.frachisee_hd_assignment.BatchDealHdDemandRequest\x1a\x32.frachisee_hd_assignment.BatchDealHdDemandResponse\"D\x82\xd3\xe4\x93\x02>\x1a\x39/api/v2/supply/frs-management/hd-assignment/batch/confirm:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_COMMONRESPONSE = _descriptor.Descriptor(
  name='CommonResponse',
  full_name='frachisee_hd_assignment.CommonResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='frachisee_hd_assignment.CommonResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msgs', full_name='frachisee_hd_assignment.CommonResponse.msgs', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pmsgs', full_name='frachisee_hd_assignment.CommonResponse.pmsgs', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=137,
  serialized_end=288,
)


_GETVALIDSTOREBYPRODUCTREQUEST = _descriptor.Descriptor(
  name='GetValidStoreByProductRequest',
  full_name='frachisee_hd_assignment.GetValidStoreByProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='frachisee_hd_assignment.GetValidStoreByProductRequest.product_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='frachisee_hd_assignment.GetValidStoreByProductRequest.order_type_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='frachisee_hd_assignment.GetValidStoreByProductRequest.demand_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='frachisee_hd_assignment.GetValidStoreByProductRequest.category_ids', index=3,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='frachisee_hd_assignment.GetValidStoreByProductRequest.lan', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=291,
  serialized_end=450,
)


_GETVALIDSTOREBYPRODUCTRESPONSE = _descriptor.Descriptor(
  name='GetValidStoreByProductResponse',
  full_name='frachisee_hd_assignment.GetValidStoreByProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='frachisee_hd_assignment.GetValidStoreByProductResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='frachisee_hd_assignment.GetValidStoreByProductResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=452,
  serialized_end=557,
)


_VALIDSTORE = _descriptor.Descriptor(
  name='ValidStore',
  full_name='frachisee_hd_assignment.ValidStore',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='frachisee_hd_assignment.ValidStore.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='frachisee_hd_assignment.ValidStore.store_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='frachisee_hd_assignment.ValidStore.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='geo_region', full_name='frachisee_hd_assignment.ValidStore.geo_region', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='frachisee_hd_assignment.ValidStore.tax_price', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='frachisee_hd_assignment.ValidStore.tax_rate', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_days', full_name='frachisee_hd_assignment.ValidStore.arrival_days', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_by', full_name='frachisee_hd_assignment.ValidStore.distribute_by', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='frachisee_hd_assignment.ValidStore.distribution_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_quantity', full_name='frachisee_hd_assignment.ValidStore.increment_quantity', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_quantity', full_name='frachisee_hd_assignment.ValidStore.max_quantity', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_quantity', full_name='frachisee_hd_assignment.ValidStore.min_quantity', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='frachisee_hd_assignment.ValidStore.sales_price', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='frachisee_hd_assignment.ValidStore.extends', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=560,
  serialized_end=869,
)


_PRODUCTVALIDSTORE = _descriptor.Descriptor(
  name='ProductValidStore',
  full_name='frachisee_hd_assignment.ProductValidStore',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='frachisee_hd_assignment.ProductValidStore.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='frachisee_hd_assignment.ProductValidStore.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='frachisee_hd_assignment.ProductValidStore.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='frachisee_hd_assignment.ProductValidStore.unit_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='frachisee_hd_assignment.ProductValidStore.unit_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='frachisee_hd_assignment.ProductValidStore.unit_spec', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='frachisee_hd_assignment.ProductValidStore.quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stores', full_name='frachisee_hd_assignment.ProductValidStore.stores', index=7,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=872,
  serialized_end=1081,
)


_STOREVALIDPRODUCT = _descriptor.Descriptor(
  name='StoreValidProduct',
  full_name='frachisee_hd_assignment.StoreValidProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='arrival_days', full_name='frachisee_hd_assignment.StoreValidProduct.arrival_days', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_by', full_name='frachisee_hd_assignment.StoreValidProduct.distribute_by', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='frachisee_hd_assignment.StoreValidProduct.distribution_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_quantity', full_name='frachisee_hd_assignment.StoreValidProduct.increment_quantity', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_quantity', full_name='frachisee_hd_assignment.StoreValidProduct.max_quantity', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_quantity', full_name='frachisee_hd_assignment.StoreValidProduct.min_quantity', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='frachisee_hd_assignment.StoreValidProduct.unit_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='frachisee_hd_assignment.StoreValidProduct.unit_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='frachisee_hd_assignment.StoreValidProduct.unit_spec', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='frachisee_hd_assignment.StoreValidProduct.tax_price', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='frachisee_hd_assignment.StoreValidProduct.tax_rate', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='frachisee_hd_assignment.StoreValidProduct.product_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='frachisee_hd_assignment.StoreValidProduct.product_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='frachisee_hd_assignment.StoreValidProduct.product_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='frachisee_hd_assignment.StoreValidProduct.category_id', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='frachisee_hd_assignment.StoreValidProduct.category_name', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='frachisee_hd_assignment.StoreValidProduct.sales_price', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='frachisee_hd_assignment.StoreValidProduct.extends', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='frachisee_hd_assignment.StoreValidProduct.quantity', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation_products', full_name='frachisee_hd_assignment.StoreValidProduct.relation_products', index=19,
      number=20, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ratio', full_name='frachisee_hd_assignment.StoreValidProduct.ratio', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='configure', full_name='frachisee_hd_assignment.StoreValidProduct.configure', index=21,
      number=22, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='frachisee_hd_assignment.StoreValidProduct.store_id', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='real_inventory_qty', full_name='frachisee_hd_assignment.StoreValidProduct.real_inventory_qty', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1084,
  serialized_end=1654,
)


_GETVALIDPRODUCTBYSTOREREQUEST = _descriptor.Descriptor(
  name='GetValidProductByStoreRequest',
  full_name='frachisee_hd_assignment.GetValidProductByStoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='frachisee_hd_assignment.GetValidProductByStoreRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='frachisee_hd_assignment.GetValidProductByStoreRequest.order_type_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='frachisee_hd_assignment.GetValidProductByStoreRequest.lan', index=2,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='frachisee_hd_assignment.GetValidProductByStoreRequest.product_id', index=3,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='frachisee_hd_assignment.GetValidProductByStoreRequest.store_ids', index=4,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by', full_name='frachisee_hd_assignment.GetValidProductByStoreRequest.order_by', index=5,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='frachisee_hd_assignment.GetValidProductByStoreRequest.sort', index=6,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='frachisee_hd_assignment.GetValidProductByStoreRequest.offset', index=7,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='frachisee_hd_assignment.GetValidProductByStoreRequest.limit', index=8,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1657,
  serialized_end=1844,
)


_GETVALIDPRODUCTBYSTORERESPONSE = _descriptor.Descriptor(
  name='GetValidProductByStoreResponse',
  full_name='frachisee_hd_assignment.GetValidProductByStoreResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='frachisee_hd_assignment.GetValidProductByStoreResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='frachisee_hd_assignment.GetValidProductByStoreResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_unchanged_rows', full_name='frachisee_hd_assignment.GetValidProductByStoreResponse.inventory_unchanged_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1847,
  serialized_end=2030,
)


_CREATEHDDEMANDREQUEST_BYPRODUCTPARAMS = _descriptor.Descriptor(
  name='ByProductParams',
  full_name='frachisee_hd_assignment.CreateHdDemandRequest.ByProductParams',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='received_by', full_name='frachisee_hd_assignment.CreateHdDemandRequest.ByProductParams.received_by', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='frachisee_hd_assignment.CreateHdDemandRequest.ByProductParams.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2424,
  serialized_end=2524,
)

_CREATEHDDEMANDREQUEST = _descriptor.Descriptor(
  name='CreateHdDemandRequest',
  full_name='frachisee_hd_assignment.CreateHdDemandRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='frachisee_hd_assignment.CreateHdDemandRequest.demand_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='frachisee_hd_assignment.CreateHdDemandRequest.arrival_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='frachisee_hd_assignment.CreateHdDemandRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='frachisee_hd_assignment.CreateHdDemandRequest.type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='frachisee_hd_assignment.CreateHdDemandRequest.sub_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='by_product_params', full_name='frachisee_hd_assignment.CreateHdDemandRequest.by_product_params', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='by_store_params', full_name='frachisee_hd_assignment.CreateHdDemandRequest.by_store_params', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bus_type', full_name='frachisee_hd_assignment.CreateHdDemandRequest.bus_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='frachisee_hd_assignment.CreateHdDemandRequest.batch_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='frachisee_hd_assignment.CreateHdDemandRequest.order_type_id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_CREATEHDDEMANDREQUEST_BYPRODUCTPARAMS, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2033,
  serialized_end=2524,
)


_IDREQUEST = _descriptor.Descriptor(
  name='IdRequest',
  full_name='frachisee_hd_assignment.IdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='frachisee_hd_assignment.IdRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='frachisee_hd_assignment.IdRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2526,
  serialized_end=2569,
)


_IDRESPONSE = _descriptor.Descriptor(
  name='IdResponse',
  full_name='frachisee_hd_assignment.IdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='frachisee_hd_assignment.IdResponse.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2571,
  serialized_end=2602,
)


_LISTHDDEMANDREQUEST = _descriptor.Descriptor(
  name='ListHdDemandRequest',
  full_name='frachisee_hd_assignment.ListHdDemandRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='frachisee_hd_assignment.ListHdDemandRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='frachisee_hd_assignment.ListHdDemandRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='frachisee_hd_assignment.ListHdDemandRequest.status', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_bys', full_name='frachisee_hd_assignment.ListHdDemandRequest.received_bys', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='frachisee_hd_assignment.ListHdDemandRequest.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='types', full_name='frachisee_hd_assignment.ListHdDemandRequest.types', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='frachisee_hd_assignment.ListHdDemandRequest.sub_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='chain_type', full_name='frachisee_hd_assignment.ListHdDemandRequest.chain_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bus_types', full_name='frachisee_hd_assignment.ListHdDemandRequest.bus_types', index=8,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='frachisee_hd_assignment.ListHdDemandRequest.offset', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='frachisee_hd_assignment.ListHdDemandRequest.limit', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='frachisee_hd_assignment.ListHdDemandRequest.order', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='frachisee_hd_assignment.ListHdDemandRequest.sort', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='frachisee_hd_assignment.ListHdDemandRequest.include_total', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='frachisee_hd_assignment.ListHdDemandRequest.ids', index=14,
      number=15, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='frachisee_hd_assignment.ListHdDemandRequest.product_ids', index=15,
      number=16, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_ids', full_name='frachisee_hd_assignment.ListHdDemandRequest.franchisee_ids', index=16,
      number=17, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_ways', full_name='frachisee_hd_assignment.ListHdDemandRequest.payment_ways', index=17,
      number=18, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_ids', full_name='frachisee_hd_assignment.ListHdDemandRequest.order_type_ids', index=18,
      number=19, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='frachisee_hd_assignment.ListHdDemandRequest.lan', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2605,
  serialized_end=3044,
)


_LISTHDDEMANDRESPONSE = _descriptor.Descriptor(
  name='ListHdDemandResponse',
  full_name='frachisee_hd_assignment.ListHdDemandResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='frachisee_hd_assignment.ListHdDemandResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='frachisee_hd_assignment.ListHdDemandResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3046,
  serialized_end=3130,
)


_GETHDDEMANDPRODUCTREQUEST = _descriptor.Descriptor(
  name='GetHdDemandProductRequest',
  full_name='frachisee_hd_assignment.GetHdDemandProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='frachisee_hd_assignment.GetHdDemandProductRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='frachisee_hd_assignment.GetHdDemandProductRequest.limit', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='frachisee_hd_assignment.GetHdDemandProductRequest.offset', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='frachisee_hd_assignment.GetHdDemandProductRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='frachisee_hd_assignment.GetHdDemandProductRequest.order', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='frachisee_hd_assignment.GetHdDemandProductRequest.sort', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='frachisee_hd_assignment.GetHdDemandProductRequest.lan', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expand', full_name='frachisee_hd_assignment.GetHdDemandProductRequest.expand', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3133,
  serialized_end=3291,
)


_GETHDDEMANDPRODUCTRESPONSE = _descriptor.Descriptor(
  name='GetHdDemandProductResponse',
  full_name='frachisee_hd_assignment.GetHdDemandProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='frachisee_hd_assignment.GetHdDemandProductResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='frachisee_hd_assignment.GetHdDemandProductResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3293,
  serialized_end=3384,
)


_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='frachisee_hd_assignment.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='frachisee_hd_assignment.Product.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='frachisee_hd_assignment.Product.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='frachisee_hd_assignment.Product.product_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='frachisee_hd_assignment.Product.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='org_product_id', full_name='frachisee_hd_assignment.Product.org_product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='org_product_code', full_name='frachisee_hd_assignment.Product.org_product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='org_product_name', full_name='frachisee_hd_assignment.Product.org_product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='frachisee_hd_assignment.Product.category_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='frachisee_hd_assignment.Product.category_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='frachisee_hd_assignment.Product.product_spec', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='frachisee_hd_assignment.Product.unit_id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='frachisee_hd_assignment.Product.unit_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='frachisee_hd_assignment.Product.unit_spec', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='frachisee_hd_assignment.Product.accounting_unit_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='frachisee_hd_assignment.Product.accounting_unit_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='frachisee_hd_assignment.Product.accounting_unit_spec', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='frachisee_hd_assignment.Product.unit_rate', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='frachisee_hd_assignment.Product.quantity', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='frachisee_hd_assignment.Product.accounting_quantity', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_quantity', full_name='frachisee_hd_assignment.Product.min_quantity', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_quantity', full_name='frachisee_hd_assignment.Product.max_quantity', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_quantity', full_name='frachisee_hd_assignment.Product.increment_quantity', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='frachisee_hd_assignment.Product.tax_price', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='frachisee_hd_assignment.Product.cost_price', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='frachisee_hd_assignment.Product.tax_rate', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='frachisee_hd_assignment.Product.amount', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_days', full_name='frachisee_hd_assignment.Product.arrival_days', index=26,
      number=27, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='frachisee_hd_assignment.Product.distribution_type', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_by', full_name='frachisee_hd_assignment.Product.distribute_by', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_name', full_name='frachisee_hd_assignment.Product.distribute_name', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='frachisee_hd_assignment.Product.status', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='yesterday_sales', full_name='frachisee_hd_assignment.Product.yesterday_sales', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_qty', full_name='frachisee_hd_assignment.Product.inventory_qty', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='on_way_qty', full_name='frachisee_hd_assignment.Product.on_way_qty', index=33,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_delete', full_name='frachisee_hd_assignment.Product.is_delete', index=34,
      number=35, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='frachisee_hd_assignment.Product.created_at', index=35,
      number=36, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='frachisee_hd_assignment.Product.updated_at', index=36,
      number=37, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='frachisee_hd_assignment.Product.created_by', index=37,
      number=38, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='frachisee_hd_assignment.Product.created_name', index=38,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='frachisee_hd_assignment.Product.updated_by', index=39,
      number=40, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='frachisee_hd_assignment.Product.updated_name', index=40,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='frachisee_hd_assignment.Product.demand_id', index=41,
      number=42, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='frachisee_hd_assignment.Product.partner_id', index=42,
      number=43, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_order', full_name='frachisee_hd_assignment.Product.allow_order', index=43,
      number=44, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_quantity', full_name='frachisee_hd_assignment.Product.confirm_quantity', index=44,
      number=45, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_amount', full_name='frachisee_hd_assignment.Product.confirm_amount', index=45,
      number=46, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='frachisee_hd_assignment.Product.extends', index=46,
      number=47, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='frachisee_hd_assignment.Product.storage_type', index=47,
      number=48, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='frachisee_hd_assignment.Product.sales_price', index=48,
      number=49, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='frachisee_hd_assignment.Product.sales_amount', index=49,
      number=50, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_sales_amount', full_name='frachisee_hd_assignment.Product.confirm_sales_amount', index=50,
      number=51, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_quantity', full_name='frachisee_hd_assignment.Product.approve_quantity', index=51,
      number=52, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_amount', full_name='frachisee_hd_assignment.Product.approve_amount', index=52,
      number=53, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_sales_amount', full_name='frachisee_hd_assignment.Product.approve_sales_amount', index=53,
      number=54, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_confirm_qty', full_name='frachisee_hd_assignment.Product.is_confirm_qty', index=54,
      number=55, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_approve_qty', full_name='frachisee_hd_assignment.Product.is_approve_qty', index=55,
      number=56, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ratio', full_name='frachisee_hd_assignment.Product.ratio', index=56,
      number=57, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='configure', full_name='frachisee_hd_assignment.Product.configure', index=57,
      number=58, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation_products', full_name='frachisee_hd_assignment.Product.relation_products', index=58,
      number=59, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3387,
  serialized_end=4793,
)


_DEMAND_ORDER = _descriptor.Descriptor(
  name='Order',
  full_name='frachisee_hd_assignment.Demand.Order',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='frachisee_hd_assignment.Demand.Order.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='frachisee_hd_assignment.Demand.Order.order_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6062,
  serialized_end=6107,
)

_DEMAND_REFUND = _descriptor.Descriptor(
  name='Refund',
  full_name='frachisee_hd_assignment.Demand.Refund',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='refund_id', full_name='frachisee_hd_assignment.Demand.Refund.refund_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_code', full_name='frachisee_hd_assignment.Demand.Refund.refund_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6109,
  serialized_end=6157,
)

_DEMAND_ORDERTYPETIME = _descriptor.Descriptor(
  name='OrderTypeTime',
  full_name='frachisee_hd_assignment.Demand.OrderTypeTime',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_time', full_name='frachisee_hd_assignment.Demand.OrderTypeTime.order_time', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='audit_time', full_name='frachisee_hd_assignment.Demand.OrderTypeTime.audit_time', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6159,
  serialized_end=6214,
)

_DEMAND = _descriptor.Descriptor(
  name='Demand',
  full_name='frachisee_hd_assignment.Demand',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='frachisee_hd_assignment.Demand.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='frachisee_hd_assignment.Demand.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='frachisee_hd_assignment.Demand.batch_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='frachisee_hd_assignment.Demand.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='frachisee_hd_assignment.Demand.type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='frachisee_hd_assignment.Demand.sub_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bus_type', full_name='frachisee_hd_assignment.Demand.bus_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='frachisee_hd_assignment.Demand.received_by', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_code', full_name='frachisee_hd_assignment.Demand.received_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_name', full_name='frachisee_hd_assignment.Demand.received_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_by', full_name='frachisee_hd_assignment.Demand.distribute_by', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='frachisee_hd_assignment.Demand.store_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='frachisee_hd_assignment.Demand.franchisee_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_code', full_name='frachisee_hd_assignment.Demand.franchisee_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_name', full_name='frachisee_hd_assignment.Demand.franchisee_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='frachisee_hd_assignment.Demand.demand_date', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='frachisee_hd_assignment.Demand.arrival_date', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='frachisee_hd_assignment.Demand.status', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='frachisee_hd_assignment.Demand.process_status', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='frachisee_hd_assignment.Demand.reject_reason', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='frachisee_hd_assignment.Demand.remark', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='frachisee_hd_assignment.Demand.reason', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='frachisee_hd_assignment.Demand.created_at', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='frachisee_hd_assignment.Demand.updated_at', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='frachisee_hd_assignment.Demand.created_by', index=24,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='frachisee_hd_assignment.Demand.created_name', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='frachisee_hd_assignment.Demand.updated_by', index=26,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='frachisee_hd_assignment.Demand.updated_name', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='frachisee_hd_assignment.Demand.review_by', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='has_product', full_name='frachisee_hd_assignment.Demand.has_product', index=29,
      number=30, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price_tax', full_name='frachisee_hd_assignment.Demand.sum_price_tax', index=30,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_tax', full_name='frachisee_hd_assignment.Demand.sum_tax', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pay_amount', full_name='frachisee_hd_assignment.Demand.pay_amount', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='frachisee_hd_assignment.Demand.attachments', index=33,
      number=34, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='frachisee_hd_assignment.Demand.extends', index=34,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orders', full_name='frachisee_hd_assignment.Demand.orders', index=35,
      number=36, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_way', full_name='frachisee_hd_assignment.Demand.payment_way', index=36,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='chain_type', full_name='frachisee_hd_assignment.Demand.chain_type', index=37,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='frachisee_hd_assignment.Demand.is_adjust', index=38,
      number=39, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_amount', full_name='frachisee_hd_assignment.Demand.confirm_amount', index=39,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='frachisee_hd_assignment.Demand.order_type_id', index=40,
      number=41, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_name', full_name='frachisee_hd_assignment.Demand.order_type_name', index=41,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_code', full_name='frachisee_hd_assignment.Demand.order_type_code', index=42,
      number=43, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='frachisee_hd_assignment.Demand.sales_amount', index=43,
      number=44, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirm_sales_amount', full_name='frachisee_hd_assignment.Demand.confirm_sales_amount', index=44,
      number=45, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refunds', full_name='frachisee_hd_assignment.Demand.refunds', index=45,
      number=46, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_amount', full_name='frachisee_hd_assignment.Demand.approve_amount', index=46,
      number=47, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_sales_amount', full_name='frachisee_hd_assignment.Demand.approve_sales_amount', index=47,
      number=48, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_time', full_name='frachisee_hd_assignment.Demand.order_type_time', index=48,
      number=49, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_DEMAND_ORDER, _DEMAND_REFUND, _DEMAND_ORDERTYPETIME, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4796,
  serialized_end=6214,
)


_UPDATEHDDEMANDPRODUCTREQUEST_UPDATEPRODUCT = _descriptor.Descriptor(
  name='UpdateProduct',
  full_name='frachisee_hd_assignment.UpdateHdDemandProductRequest.UpdateProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='frachisee_hd_assignment.UpdateHdDemandProductRequest.UpdateProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='frachisee_hd_assignment.UpdateHdDemandProductRequest.UpdateProduct.category_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='frachisee_hd_assignment.UpdateHdDemandProductRequest.UpdateProduct.quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='frachisee_hd_assignment.UpdateHdDemandProductRequest.UpdateProduct.unit_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='frachisee_hd_assignment.UpdateHdDemandProductRequest.UpdateProduct.tax_price', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='frachisee_hd_assignment.UpdateHdDemandProductRequest.UpdateProduct.tax_rate', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_days', full_name='frachisee_hd_assignment.UpdateHdDemandProductRequest.UpdateProduct.arrival_days', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='frachisee_hd_assignment.UpdateHdDemandProductRequest.UpdateProduct.sales_price', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_quantity', full_name='frachisee_hd_assignment.UpdateHdDemandProductRequest.UpdateProduct.min_quantity', index=8,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_quantity', full_name='frachisee_hd_assignment.UpdateHdDemandProductRequest.UpdateProduct.max_quantity', index=9,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_quantity', full_name='frachisee_hd_assignment.UpdateHdDemandProductRequest.UpdateProduct.increment_quantity', index=10,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation_products', full_name='frachisee_hd_assignment.UpdateHdDemandProductRequest.UpdateProduct.relation_products', index=11,
      number=15, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ratio', full_name='frachisee_hd_assignment.UpdateHdDemandProductRequest.UpdateProduct.ratio', index=12,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='configure', full_name='frachisee_hd_assignment.UpdateHdDemandProductRequest.UpdateProduct.configure', index=13,
      number=17, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6372,
  serialized_end=6745,
)

_UPDATEHDDEMANDPRODUCTREQUEST = _descriptor.Descriptor(
  name='UpdateHdDemandProductRequest',
  full_name='frachisee_hd_assignment.UpdateHdDemandProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='frachisee_hd_assignment.UpdateHdDemandProductRequest.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='frachisee_hd_assignment.UpdateHdDemandProductRequest.remark', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='frachisee_hd_assignment.UpdateHdDemandProductRequest.products', index=2,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_UPDATEHDDEMANDPRODUCTREQUEST_UPDATEPRODUCT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6217,
  serialized_end=6745,
)


_UPDATEHDDEMANDPRODUCTRESPONSE = _descriptor.Descriptor(
  name='UpdateHdDemandProductResponse',
  full_name='frachisee_hd_assignment.UpdateHdDemandProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_id', full_name='frachisee_hd_assignment.UpdateHdDemandProductResponse.demand_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='frachisee_hd_assignment.UpdateHdDemandProductResponse.description', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_names', full_name='frachisee_hd_assignment.UpdateHdDemandProductResponse.product_names', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6747,
  serialized_end=6841,
)


_DEALHDDEMANDBYIDREQUEST = _descriptor.Descriptor(
  name='DealHdDemandByIdRequest',
  full_name='frachisee_hd_assignment.DealHdDemandByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_ids', full_name='frachisee_hd_assignment.DealHdDemandByIdRequest.demand_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='frachisee_hd_assignment.DealHdDemandByIdRequest.action', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='frachisee_hd_assignment.DealHdDemandByIdRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='frachisee_hd_assignment.DealHdDemandByIdRequest.attachments', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_detailed', full_name='frachisee_hd_assignment.DealHdDemandByIdRequest.is_detailed', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6843,
  serialized_end=6962,
)


_CHECKPRODUCTMESSAGE = _descriptor.Descriptor(
  name='CheckProductMessage',
  full_name='frachisee_hd_assignment.CheckProductMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_name', full_name='frachisee_hd_assignment.CheckProductMessage.product_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='frachisee_hd_assignment.CheckProductMessage.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='frachisee_hd_assignment.CheckProductMessage.spec', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='frachisee_hd_assignment.CheckProductMessage.quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_quantity', full_name='frachisee_hd_assignment.CheckProductMessage.min_quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_quantity', full_name='frachisee_hd_assignment.CheckProductMessage.increment_quantity', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_quantity', full_name='frachisee_hd_assignment.CheckProductMessage.max_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit', full_name='frachisee_hd_assignment.CheckProductMessage.unit', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='frachisee_hd_assignment.CheckProductMessage.tax_price', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='frachisee_hd_assignment.CheckProductMessage.amount', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='frachisee_hd_assignment.CheckProductMessage.storage_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='frachisee_hd_assignment.CheckProductMessage.id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='frachisee_hd_assignment.CheckProductMessage.product_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6965,
  serialized_end=7237,
)


_CHECKORDERMESSAGE = _descriptor.Descriptor(
  name='CheckOrderMessage',
  full_name='frachisee_hd_assignment.CheckOrderMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='frachisee_hd_assignment.CheckOrderMessage.status', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='frachisee_hd_assignment.CheckOrderMessage.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='frachisee_hd_assignment.CheckOrderMessage.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='frachisee_hd_assignment.CheckOrderMessage.order_type_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_name', full_name='frachisee_hd_assignment.CheckOrderMessage.franchisee_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='frachisee_hd_assignment.CheckOrderMessage.amount', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='frachisee_hd_assignment.CheckOrderMessage.demand_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='frachisee_hd_assignment.CheckOrderMessage.arrival_date', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msg', full_name='frachisee_hd_assignment.CheckOrderMessage.msg', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7240,
  serialized_end=7485,
)


_BATCHDEALHDDEMANDREQUEST = _descriptor.Descriptor(
  name='BatchDealHdDemandRequest',
  full_name='frachisee_hd_assignment.BatchDealHdDemandRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='frachisee_hd_assignment.BatchDealHdDemandRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='frachisee_hd_assignment.BatchDealHdDemandRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_bys', full_name='frachisee_hd_assignment.BatchDealHdDemandRequest.received_bys', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bus_types', full_name='frachisee_hd_assignment.BatchDealHdDemandRequest.bus_types', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_ids', full_name='frachisee_hd_assignment.BatchDealHdDemandRequest.order_type_ids', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='line_doc_id', full_name='frachisee_hd_assignment.BatchDealHdDemandRequest.line_doc_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_method', full_name='frachisee_hd_assignment.BatchDealHdDemandRequest.batch_method', index=6,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exclude_ids', full_name='frachisee_hd_assignment.BatchDealHdDemandRequest.exclude_ids', index=7,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_ids', full_name='frachisee_hd_assignment.BatchDealHdDemandRequest.include_ids', index=8,
      number=10, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='frachisee_hd_assignment.BatchDealHdDemandRequest.action', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7488,
  serialized_end=7774,
)


_BATCHDEALHDDEMANDRESPONSE = _descriptor.Descriptor(
  name='BatchDealHdDemandResponse',
  full_name='frachisee_hd_assignment.BatchDealHdDemandResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='frachisee_hd_assignment.BatchDealHdDemandResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='frachisee_hd_assignment.BatchDealHdDemandResponse.description', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msgs', full_name='frachisee_hd_assignment.BatchDealHdDemandResponse.msgs', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7776,
  serialized_end=7898,
)

_COMMONRESPONSE.fields_by_name['msgs'].message_type = _CHECKORDERMESSAGE
_COMMONRESPONSE.fields_by_name['pmsgs'].message_type = _CHECKPRODUCTMESSAGE
_GETVALIDSTOREBYPRODUCTREQUEST.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETVALIDSTOREBYPRODUCTRESPONSE.fields_by_name['rows'].message_type = _PRODUCTVALIDSTORE
_PRODUCTVALIDSTORE.fields_by_name['stores'].message_type = _VALIDSTORE
_STOREVALIDPRODUCT.fields_by_name['relation_products'].message_type = _STOREVALIDPRODUCT
_GETVALIDPRODUCTBYSTORERESPONSE.fields_by_name['rows'].message_type = _STOREVALIDPRODUCT
_GETVALIDPRODUCTBYSTORERESPONSE.fields_by_name['inventory_unchanged_rows'].message_type = _STOREVALIDPRODUCT
_CREATEHDDEMANDREQUEST_BYPRODUCTPARAMS.fields_by_name['products'].message_type = _STOREVALIDPRODUCT
_CREATEHDDEMANDREQUEST_BYPRODUCTPARAMS.containing_type = _CREATEHDDEMANDREQUEST
_CREATEHDDEMANDREQUEST.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEHDDEMANDREQUEST.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEHDDEMANDREQUEST.fields_by_name['by_product_params'].message_type = _CREATEHDDEMANDREQUEST_BYPRODUCTPARAMS
_CREATEHDDEMANDREQUEST.fields_by_name['by_store_params'].message_type = _PRODUCTVALIDSTORE
_LISTHDDEMANDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTHDDEMANDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTHDDEMANDRESPONSE.fields_by_name['rows'].message_type = _DEMAND
_GETHDDEMANDPRODUCTRESPONSE.fields_by_name['rows'].message_type = _PRODUCT
_PRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCT.fields_by_name['relation_products'].message_type = _PRODUCT
_DEMAND_ORDER.containing_type = _DEMAND
_DEMAND_REFUND.containing_type = _DEMAND
_DEMAND_ORDERTYPETIME.containing_type = _DEMAND
_DEMAND.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMAND.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMAND.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMAND.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMAND.fields_by_name['orders'].message_type = _DEMAND_ORDER
_DEMAND.fields_by_name['refunds'].message_type = _DEMAND_REFUND
_DEMAND.fields_by_name['order_type_time'].message_type = _DEMAND_ORDERTYPETIME
_UPDATEHDDEMANDPRODUCTREQUEST_UPDATEPRODUCT.fields_by_name['relation_products'].message_type = _UPDATEHDDEMANDPRODUCTREQUEST_UPDATEPRODUCT
_UPDATEHDDEMANDPRODUCTREQUEST_UPDATEPRODUCT.containing_type = _UPDATEHDDEMANDPRODUCTREQUEST
_UPDATEHDDEMANDPRODUCTREQUEST.fields_by_name['products'].message_type = _UPDATEHDDEMANDPRODUCTREQUEST_UPDATEPRODUCT
_CHECKORDERMESSAGE.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CHECKORDERMESSAGE.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_BATCHDEALHDDEMANDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_BATCHDEALHDDEMANDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_BATCHDEALHDDEMANDRESPONSE.fields_by_name['msgs'].message_type = _CHECKORDERMESSAGE
DESCRIPTOR.message_types_by_name['CommonResponse'] = _COMMONRESPONSE
DESCRIPTOR.message_types_by_name['GetValidStoreByProductRequest'] = _GETVALIDSTOREBYPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['GetValidStoreByProductResponse'] = _GETVALIDSTOREBYPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['ValidStore'] = _VALIDSTORE
DESCRIPTOR.message_types_by_name['ProductValidStore'] = _PRODUCTVALIDSTORE
DESCRIPTOR.message_types_by_name['StoreValidProduct'] = _STOREVALIDPRODUCT
DESCRIPTOR.message_types_by_name['GetValidProductByStoreRequest'] = _GETVALIDPRODUCTBYSTOREREQUEST
DESCRIPTOR.message_types_by_name['GetValidProductByStoreResponse'] = _GETVALIDPRODUCTBYSTORERESPONSE
DESCRIPTOR.message_types_by_name['CreateHdDemandRequest'] = _CREATEHDDEMANDREQUEST
DESCRIPTOR.message_types_by_name['IdRequest'] = _IDREQUEST
DESCRIPTOR.message_types_by_name['IdResponse'] = _IDRESPONSE
DESCRIPTOR.message_types_by_name['ListHdDemandRequest'] = _LISTHDDEMANDREQUEST
DESCRIPTOR.message_types_by_name['ListHdDemandResponse'] = _LISTHDDEMANDRESPONSE
DESCRIPTOR.message_types_by_name['GetHdDemandProductRequest'] = _GETHDDEMANDPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['GetHdDemandProductResponse'] = _GETHDDEMANDPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['Product'] = _PRODUCT
DESCRIPTOR.message_types_by_name['Demand'] = _DEMAND
DESCRIPTOR.message_types_by_name['UpdateHdDemandProductRequest'] = _UPDATEHDDEMANDPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['UpdateHdDemandProductResponse'] = _UPDATEHDDEMANDPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['DealHdDemandByIdRequest'] = _DEALHDDEMANDBYIDREQUEST
DESCRIPTOR.message_types_by_name['CheckProductMessage'] = _CHECKPRODUCTMESSAGE
DESCRIPTOR.message_types_by_name['CheckOrderMessage'] = _CHECKORDERMESSAGE
DESCRIPTOR.message_types_by_name['BatchDealHdDemandRequest'] = _BATCHDEALHDDEMANDREQUEST
DESCRIPTOR.message_types_by_name['BatchDealHdDemandResponse'] = _BATCHDEALHDDEMANDRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CommonResponse = _reflection.GeneratedProtocolMessageType('CommonResponse', (_message.Message,), dict(
  DESCRIPTOR = _COMMONRESPONSE,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.CommonResponse)
  ))
_sym_db.RegisterMessage(CommonResponse)

GetValidStoreByProductRequest = _reflection.GeneratedProtocolMessageType('GetValidStoreByProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETVALIDSTOREBYPRODUCTREQUEST,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.GetValidStoreByProductRequest)
  ))
_sym_db.RegisterMessage(GetValidStoreByProductRequest)

GetValidStoreByProductResponse = _reflection.GeneratedProtocolMessageType('GetValidStoreByProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETVALIDSTOREBYPRODUCTRESPONSE,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.GetValidStoreByProductResponse)
  ))
_sym_db.RegisterMessage(GetValidStoreByProductResponse)

ValidStore = _reflection.GeneratedProtocolMessageType('ValidStore', (_message.Message,), dict(
  DESCRIPTOR = _VALIDSTORE,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.ValidStore)
  ))
_sym_db.RegisterMessage(ValidStore)

ProductValidStore = _reflection.GeneratedProtocolMessageType('ProductValidStore', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTVALIDSTORE,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.ProductValidStore)
  ))
_sym_db.RegisterMessage(ProductValidStore)

StoreValidProduct = _reflection.GeneratedProtocolMessageType('StoreValidProduct', (_message.Message,), dict(
  DESCRIPTOR = _STOREVALIDPRODUCT,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.StoreValidProduct)
  ))
_sym_db.RegisterMessage(StoreValidProduct)

GetValidProductByStoreRequest = _reflection.GeneratedProtocolMessageType('GetValidProductByStoreRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETVALIDPRODUCTBYSTOREREQUEST,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.GetValidProductByStoreRequest)
  ))
_sym_db.RegisterMessage(GetValidProductByStoreRequest)

GetValidProductByStoreResponse = _reflection.GeneratedProtocolMessageType('GetValidProductByStoreResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETVALIDPRODUCTBYSTORERESPONSE,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.GetValidProductByStoreResponse)
  ))
_sym_db.RegisterMessage(GetValidProductByStoreResponse)

CreateHdDemandRequest = _reflection.GeneratedProtocolMessageType('CreateHdDemandRequest', (_message.Message,), dict(

  ByProductParams = _reflection.GeneratedProtocolMessageType('ByProductParams', (_message.Message,), dict(
    DESCRIPTOR = _CREATEHDDEMANDREQUEST_BYPRODUCTPARAMS,
    __module__ = 'frs_management.franchise_hd_assignment_pb2'
    # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.CreateHdDemandRequest.ByProductParams)
    ))
  ,
  DESCRIPTOR = _CREATEHDDEMANDREQUEST,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.CreateHdDemandRequest)
  ))
_sym_db.RegisterMessage(CreateHdDemandRequest)
_sym_db.RegisterMessage(CreateHdDemandRequest.ByProductParams)

IdRequest = _reflection.GeneratedProtocolMessageType('IdRequest', (_message.Message,), dict(
  DESCRIPTOR = _IDREQUEST,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.IdRequest)
  ))
_sym_db.RegisterMessage(IdRequest)

IdResponse = _reflection.GeneratedProtocolMessageType('IdResponse', (_message.Message,), dict(
  DESCRIPTOR = _IDRESPONSE,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.IdResponse)
  ))
_sym_db.RegisterMessage(IdResponse)

ListHdDemandRequest = _reflection.GeneratedProtocolMessageType('ListHdDemandRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTHDDEMANDREQUEST,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.ListHdDemandRequest)
  ))
_sym_db.RegisterMessage(ListHdDemandRequest)

ListHdDemandResponse = _reflection.GeneratedProtocolMessageType('ListHdDemandResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTHDDEMANDRESPONSE,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.ListHdDemandResponse)
  ))
_sym_db.RegisterMessage(ListHdDemandResponse)

GetHdDemandProductRequest = _reflection.GeneratedProtocolMessageType('GetHdDemandProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETHDDEMANDPRODUCTREQUEST,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.GetHdDemandProductRequest)
  ))
_sym_db.RegisterMessage(GetHdDemandProductRequest)

GetHdDemandProductResponse = _reflection.GeneratedProtocolMessageType('GetHdDemandProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETHDDEMANDPRODUCTRESPONSE,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.GetHdDemandProductResponse)
  ))
_sym_db.RegisterMessage(GetHdDemandProductResponse)

Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCT,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.Product)
  ))
_sym_db.RegisterMessage(Product)

Demand = _reflection.GeneratedProtocolMessageType('Demand', (_message.Message,), dict(

  Order = _reflection.GeneratedProtocolMessageType('Order', (_message.Message,), dict(
    DESCRIPTOR = _DEMAND_ORDER,
    __module__ = 'frs_management.franchise_hd_assignment_pb2'
    # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.Demand.Order)
    ))
  ,

  Refund = _reflection.GeneratedProtocolMessageType('Refund', (_message.Message,), dict(
    DESCRIPTOR = _DEMAND_REFUND,
    __module__ = 'frs_management.franchise_hd_assignment_pb2'
    # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.Demand.Refund)
    ))
  ,

  OrderTypeTime = _reflection.GeneratedProtocolMessageType('OrderTypeTime', (_message.Message,), dict(
    DESCRIPTOR = _DEMAND_ORDERTYPETIME,
    __module__ = 'frs_management.franchise_hd_assignment_pb2'
    # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.Demand.OrderTypeTime)
    ))
  ,
  DESCRIPTOR = _DEMAND,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.Demand)
  ))
_sym_db.RegisterMessage(Demand)
_sym_db.RegisterMessage(Demand.Order)
_sym_db.RegisterMessage(Demand.Refund)
_sym_db.RegisterMessage(Demand.OrderTypeTime)

UpdateHdDemandProductRequest = _reflection.GeneratedProtocolMessageType('UpdateHdDemandProductRequest', (_message.Message,), dict(

  UpdateProduct = _reflection.GeneratedProtocolMessageType('UpdateProduct', (_message.Message,), dict(
    DESCRIPTOR = _UPDATEHDDEMANDPRODUCTREQUEST_UPDATEPRODUCT,
    __module__ = 'frs_management.franchise_hd_assignment_pb2'
    # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.UpdateHdDemandProductRequest.UpdateProduct)
    ))
  ,
  DESCRIPTOR = _UPDATEHDDEMANDPRODUCTREQUEST,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.UpdateHdDemandProductRequest)
  ))
_sym_db.RegisterMessage(UpdateHdDemandProductRequest)
_sym_db.RegisterMessage(UpdateHdDemandProductRequest.UpdateProduct)

UpdateHdDemandProductResponse = _reflection.GeneratedProtocolMessageType('UpdateHdDemandProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEHDDEMANDPRODUCTRESPONSE,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.UpdateHdDemandProductResponse)
  ))
_sym_db.RegisterMessage(UpdateHdDemandProductResponse)

DealHdDemandByIdRequest = _reflection.GeneratedProtocolMessageType('DealHdDemandByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _DEALHDDEMANDBYIDREQUEST,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.DealHdDemandByIdRequest)
  ))
_sym_db.RegisterMessage(DealHdDemandByIdRequest)

CheckProductMessage = _reflection.GeneratedProtocolMessageType('CheckProductMessage', (_message.Message,), dict(
  DESCRIPTOR = _CHECKPRODUCTMESSAGE,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.CheckProductMessage)
  ))
_sym_db.RegisterMessage(CheckProductMessage)

CheckOrderMessage = _reflection.GeneratedProtocolMessageType('CheckOrderMessage', (_message.Message,), dict(
  DESCRIPTOR = _CHECKORDERMESSAGE,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.CheckOrderMessage)
  ))
_sym_db.RegisterMessage(CheckOrderMessage)

BatchDealHdDemandRequest = _reflection.GeneratedProtocolMessageType('BatchDealHdDemandRequest', (_message.Message,), dict(
  DESCRIPTOR = _BATCHDEALHDDEMANDREQUEST,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.BatchDealHdDemandRequest)
  ))
_sym_db.RegisterMessage(BatchDealHdDemandRequest)

BatchDealHdDemandResponse = _reflection.GeneratedProtocolMessageType('BatchDealHdDemandResponse', (_message.Message,), dict(
  DESCRIPTOR = _BATCHDEALHDDEMANDRESPONSE,
  __module__ = 'frs_management.franchise_hd_assignment_pb2'
  # @@protoc_insertion_point(class_scope:frachisee_hd_assignment.BatchDealHdDemandResponse)
  ))
_sym_db.RegisterMessage(BatchDealHdDemandResponse)



_FRANCHISEEHDASSIGNMENT = _descriptor.ServiceDescriptor(
  name='FranchiseeHdAssignment',
  full_name='frachisee_hd_assignment.FranchiseeHdAssignment',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=7901,
  serialized_end=9646,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateHdDemand',
    full_name='frachisee_hd_assignment.FranchiseeHdAssignment.CreateHdDemand',
    index=0,
    containing_service=None,
    input_type=_CREATEHDDEMANDREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\"2/api/v2/supply/frs-management/hd-assignment/demand:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetValidStoreByProduct',
    full_name='frachisee_hd_assignment.FranchiseeHdAssignment.GetValidStoreByProduct',
    index=1,
    containing_service=None,
    input_type=_GETVALIDSTOREBYPRODUCTREQUEST,
    output_type=_GETVALIDSTOREBYPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\0227/api/v2/supply/frs-management/hd-assignment/valid/store'),
  ),
  _descriptor.MethodDescriptor(
    name='GetValidProductByStore',
    full_name='frachisee_hd_assignment.FranchiseeHdAssignment.GetValidProductByStore',
    index=2,
    containing_service=None,
    input_type=_GETVALIDPRODUCTBYSTOREREQUEST,
    output_type=_GETVALIDPRODUCTBYSTORERESPONSE,
    serialized_options=_b('\202\323\344\223\002;\0229/api/v2/supply/frs-management/hd-assignment/valid/product'),
  ),
  _descriptor.MethodDescriptor(
    name='ListHdDemand',
    full_name='frachisee_hd_assignment.FranchiseeHdAssignment.ListHdDemand',
    index=3,
    containing_service=None,
    input_type=_LISTHDDEMANDREQUEST,
    output_type=_LISTHDDEMANDRESPONSE,
    serialized_options=_b('\202\323\344\223\0024\0222/api/v2/supply/frs-management/hd-assignment/demand'),
  ),
  _descriptor.MethodDescriptor(
    name='GetHdDemandDetail',
    full_name='frachisee_hd_assignment.FranchiseeHdAssignment.GetHdDemandDetail',
    index=4,
    containing_service=None,
    input_type=_IDREQUEST,
    output_type=_DEMAND,
    serialized_options=_b('\202\323\344\223\002@\022>/api/v2/supply/frs-management/hd-assignment/demand/{demand_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetHdDemandProduct',
    full_name='frachisee_hd_assignment.FranchiseeHdAssignment.GetHdDemandProduct',
    index=5,
    containing_service=None,
    input_type=_GETHDDEMANDPRODUCTREQUEST,
    output_type=_GETHDDEMANDPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\002H\022F/api/v2/supply/frs-management/hd-assignment/demand/{demand_id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateHdDemandProduct',
    full_name='frachisee_hd_assignment.FranchiseeHdAssignment.UpdateHdDemandProduct',
    index=6,
    containing_service=None,
    input_type=_UPDATEHDDEMANDPRODUCTREQUEST,
    output_type=_UPDATEHDDEMANDPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\002F\032A/api/v2/supply/frs-management/hd-assignment/demand/product/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DealHdDemandById',
    full_name='frachisee_hd_assignment.FranchiseeHdAssignment.DealHdDemandById',
    index=7,
    containing_service=None,
    input_type=_DEALHDDEMANDBYIDREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\002@\032;/api/v2/supply/frs-management/hd-assignment/demand/{action}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='BatchConfirmHdDemand',
    full_name='frachisee_hd_assignment.FranchiseeHdAssignment.BatchConfirmHdDemand',
    index=8,
    containing_service=None,
    input_type=_BATCHDEALHDDEMANDREQUEST,
    output_type=_BATCHDEALHDDEMANDRESPONSE,
    serialized_options=_b('\202\323\344\223\002>\0329/api/v2/supply/frs-management/hd-assignment/batch/confirm:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_FRANCHISEEHDASSIGNMENT)

DESCRIPTOR.services_by_name['FranchiseeHdAssignment'] = _FRANCHISEEHDASSIGNMENT

# @@protoc_insertion_point(module_scope)
