# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from frs_management import franchise_hd_assignment_pb2 as frs__management_dot_franchise__hd__assignment__pb2


class FranchiseeHdAssignmentStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateHdDemand = channel.unary_unary(
        '/frachisee_hd_assignment.FranchiseeHdAssignment/CreateHdDemand',
        request_serializer=frs__management_dot_franchise__hd__assignment__pb2.CreateHdDemandRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchise__hd__assignment__pb2.CommonResponse.FromString,
        )
    self.GetValidStoreByProduct = channel.unary_unary(
        '/frachisee_hd_assignment.FranchiseeHdAssignment/GetValidStoreByProduct',
        request_serializer=frs__management_dot_franchise__hd__assignment__pb2.GetValidStoreByProductRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchise__hd__assignment__pb2.GetValidStoreByProductResponse.FromString,
        )
    self.GetValidProductByStore = channel.unary_unary(
        '/frachisee_hd_assignment.FranchiseeHdAssignment/GetValidProductByStore',
        request_serializer=frs__management_dot_franchise__hd__assignment__pb2.GetValidProductByStoreRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchise__hd__assignment__pb2.GetValidProductByStoreResponse.FromString,
        )
    self.ListHdDemand = channel.unary_unary(
        '/frachisee_hd_assignment.FranchiseeHdAssignment/ListHdDemand',
        request_serializer=frs__management_dot_franchise__hd__assignment__pb2.ListHdDemandRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchise__hd__assignment__pb2.ListHdDemandResponse.FromString,
        )
    self.GetHdDemandDetail = channel.unary_unary(
        '/frachisee_hd_assignment.FranchiseeHdAssignment/GetHdDemandDetail',
        request_serializer=frs__management_dot_franchise__hd__assignment__pb2.IdRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchise__hd__assignment__pb2.Demand.FromString,
        )
    self.GetHdDemandProduct = channel.unary_unary(
        '/frachisee_hd_assignment.FranchiseeHdAssignment/GetHdDemandProduct',
        request_serializer=frs__management_dot_franchise__hd__assignment__pb2.GetHdDemandProductRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchise__hd__assignment__pb2.GetHdDemandProductResponse.FromString,
        )
    self.UpdateHdDemandProduct = channel.unary_unary(
        '/frachisee_hd_assignment.FranchiseeHdAssignment/UpdateHdDemandProduct',
        request_serializer=frs__management_dot_franchise__hd__assignment__pb2.UpdateHdDemandProductRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchise__hd__assignment__pb2.UpdateHdDemandProductResponse.FromString,
        )
    self.DealHdDemandById = channel.unary_unary(
        '/frachisee_hd_assignment.FranchiseeHdAssignment/DealHdDemandById',
        request_serializer=frs__management_dot_franchise__hd__assignment__pb2.DealHdDemandByIdRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchise__hd__assignment__pb2.CommonResponse.FromString,
        )
    self.BatchConfirmHdDemand = channel.unary_unary(
        '/frachisee_hd_assignment.FranchiseeHdAssignment/BatchConfirmHdDemand',
        request_serializer=frs__management_dot_franchise__hd__assignment__pb2.BatchDealHdDemandRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchise__hd__assignment__pb2.BatchDealHdDemandResponse.FromString,
        )


class FranchiseeHdAssignmentServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def CreateHdDemand(self, request, context):
    """创建主配单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetValidStoreByProduct(self, request, context):
    """根据商品id获取可订货门店
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetValidProductByStore(self, request, context):
    """根据门店id获取可主配商品, 新增多门店筛选
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListHdDemand(self, request, context):
    """查询门店主配单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetHdDemandDetail(self, request, context):
    """获取主配单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetHdDemandProduct(self, request, context):
    """获取主配单商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateHdDemandProduct(self, request, context):
    """更新主配单商品信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DealHdDemandById(self, request, context):
    """修改订单状态统一入口
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def BatchConfirmHdDemand(self, request, context):
    """按条件批量确认订单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_FranchiseeHdAssignmentServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateHdDemand': grpc.unary_unary_rpc_method_handler(
          servicer.CreateHdDemand,
          request_deserializer=frs__management_dot_franchise__hd__assignment__pb2.CreateHdDemandRequest.FromString,
          response_serializer=frs__management_dot_franchise__hd__assignment__pb2.CommonResponse.SerializeToString,
      ),
      'GetValidStoreByProduct': grpc.unary_unary_rpc_method_handler(
          servicer.GetValidStoreByProduct,
          request_deserializer=frs__management_dot_franchise__hd__assignment__pb2.GetValidStoreByProductRequest.FromString,
          response_serializer=frs__management_dot_franchise__hd__assignment__pb2.GetValidStoreByProductResponse.SerializeToString,
      ),
      'GetValidProductByStore': grpc.unary_unary_rpc_method_handler(
          servicer.GetValidProductByStore,
          request_deserializer=frs__management_dot_franchise__hd__assignment__pb2.GetValidProductByStoreRequest.FromString,
          response_serializer=frs__management_dot_franchise__hd__assignment__pb2.GetValidProductByStoreResponse.SerializeToString,
      ),
      'ListHdDemand': grpc.unary_unary_rpc_method_handler(
          servicer.ListHdDemand,
          request_deserializer=frs__management_dot_franchise__hd__assignment__pb2.ListHdDemandRequest.FromString,
          response_serializer=frs__management_dot_franchise__hd__assignment__pb2.ListHdDemandResponse.SerializeToString,
      ),
      'GetHdDemandDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetHdDemandDetail,
          request_deserializer=frs__management_dot_franchise__hd__assignment__pb2.IdRequest.FromString,
          response_serializer=frs__management_dot_franchise__hd__assignment__pb2.Demand.SerializeToString,
      ),
      'GetHdDemandProduct': grpc.unary_unary_rpc_method_handler(
          servicer.GetHdDemandProduct,
          request_deserializer=frs__management_dot_franchise__hd__assignment__pb2.GetHdDemandProductRequest.FromString,
          response_serializer=frs__management_dot_franchise__hd__assignment__pb2.GetHdDemandProductResponse.SerializeToString,
      ),
      'UpdateHdDemandProduct': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateHdDemandProduct,
          request_deserializer=frs__management_dot_franchise__hd__assignment__pb2.UpdateHdDemandProductRequest.FromString,
          response_serializer=frs__management_dot_franchise__hd__assignment__pb2.UpdateHdDemandProductResponse.SerializeToString,
      ),
      'DealHdDemandById': grpc.unary_unary_rpc_method_handler(
          servicer.DealHdDemandById,
          request_deserializer=frs__management_dot_franchise__hd__assignment__pb2.DealHdDemandByIdRequest.FromString,
          response_serializer=frs__management_dot_franchise__hd__assignment__pb2.CommonResponse.SerializeToString,
      ),
      'BatchConfirmHdDemand': grpc.unary_unary_rpc_method_handler(
          servicer.BatchConfirmHdDemand,
          request_deserializer=frs__management_dot_franchise__hd__assignment__pb2.BatchDealHdDemandRequest.FromString,
          response_serializer=frs__management_dot_franchise__hd__assignment__pb2.BatchDealHdDemandResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'frachisee_hd_assignment.FranchiseeHdAssignment', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
