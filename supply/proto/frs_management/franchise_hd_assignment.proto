syntax = "proto3";

package frachisee_hd_assignment;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";


service FranchiseeHdAssignment {

    // 创建主配单
    rpc CreateHdDemand (CreateHdDemandRequest) returns (CommonResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/frs-management/hd-assignment/demand"
        body: "*"
        };
    }

    // 根据商品id获取可订货门店
    rpc GetValidStoreByProduct (GetValidStoreByProductRequest) returns (GetValidStoreByProductResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/frs-management/hd-assignment/valid/store"
        };
    }

    // 根据门店id获取可主配商品, 新增多门店筛选
    rpc GetValidProductByStore (GetValidProductByStoreRequest) returns (GetValidProductByStoreResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/frs-management/hd-assignment/valid/product"
        };
    }

    // 查询门店主配单
    rpc ListHdDemand (ListHdDemandRequest) returns (ListHdDemandResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/frs-management/hd-assignment/demand"
        };
    }

    // 获取主配单
    rpc GetHdDemandDetail (IdRequest) returns (Demand) {
        option (google.api.http) = {
        get: "/api/v2/supply/frs-management/hd-assignment/demand/{demand_id}"
        };
    };

    // 获取主配单商品
    rpc GetHdDemandProduct (GetHdDemandProductRequest) returns (GetHdDemandProductResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/frs-management/hd-assignment/demand/{demand_id}/product"
        };
    }

    // 更新主配单商品信息
    rpc UpdateHdDemandProduct (UpdateHdDemandProductRequest) returns (UpdateHdDemandProductResponse) {
        option (google.api.http) = {
        put: "/api/v2/supply/frs-management/hd-assignment/demand/product/update"
        body: "*"
        };
    }

    // 修改订单状态统一入口
    rpc DealHdDemandById (DealHdDemandByIdRequest) returns (CommonResponse) {
        option (google.api.http) = {
            put: "/api/v2/supply/frs-management/hd-assignment/demand/{action}"
            body: "*"
        };
    }
        // 按条件批量确认订单
    rpc BatchConfirmHdDemand (BatchDealHdDemandRequest) returns (BatchDealHdDemandResponse) {
        option (google.api.http) = {
            put: "/api/v2/supply/frs-management/hd-assignment/batch/confirm"
            body: "*"
        };
    }
}

// 统一返回对象
message CommonResponse {
    bool result = 1;
    repeated CheckOrderMessage msgs = 2;
    repeated CheckProductMessage pmsgs = 3;
}

// 根据商品id获得可订货门店请求参数
message GetValidStoreByProductRequest {
    // 商品id
    repeated uint64 product_ids = 1;
    // 订货类型id
    uint64 order_type_id = 2;
    // 订货日期
    google.protobuf.Timestamp demand_date = 3;
    // 分类id
    repeated uint64 category_ids = 5;
    string lan = 6;
}

// 根据商品id获得可订货门店返回参数
message GetValidStoreByProductResponse {
    repeated ProductValidStore rows = 1;
    uint32 total = 2;
}

message ValidStore {
    // 门店id(新建时必传)
    uint64 store_id = 1;
    // 门店code
    string store_code = 2;
    // 门店名称
    string store_name = 3;
    // 地理区域
    uint64 geo_region = 4;
    // 含税价(新建时必传)
    double tax_price = 5;
    // 税率(新建时必传)
    double tax_rate = 6;
    // 到货天数(新建时必传)
    string arrival_days = 7;
    // 配送/供应商中心(新建时必传)
    uint64 distribute_by = 8;
    // 配送(NMD)、采购(PUR)(新建时必传)
    string distribution_type = 9;
    // 递增订量(新建时必传)
    string increment_quantity = 10;
    // 最大订货量(新建时必传)
    string max_quantity = 11;
    // 最小订货量(新建时必传)
    string min_quantity = 12;
    // 零售价(新建时必传)
    double sales_price = 13;
    // 扩展信息（商品价格等）(新建时必传)
    string extends = 14;
}

message ProductValidStore {
    // 商品id(新建时必传)
    uint64 product_id = 1;
    // 商品编码
    string product_code = 2;
    // 商品名称
    string product_name = 3;
    // 订货单位id(新建时必传)
    uint64 unit_id = 4;
    // 订货单位名称
    string unit_name = 5;
    // 订货单位规格
    string unit_spec = 6;
    // 订货数量
    double quantity = 7;
    // 可主配门店
    repeated ValidStore stores = 10;
}

message StoreValidProduct {
    // 到货天数
    string arrival_days = 1;
    // 配送/供应商中心
    uint64 distribute_by = 2;
    // 配送(NMD)、采购(PUR)
    string distribution_type = 3;
    // 递增订量
    string increment_quantity = 4;
    // 最大订货量
    string max_quantity = 5;
    // 最小订货量
    string min_quantity = 6;
    // 订货单位id
    uint64 unit_id = 7;
    // 订货单位名称
    string unit_name = 8;
    // 订货单位规格
    string unit_spec = 9;
    // 含税价
    double tax_price = 10;
    // 税率
    double tax_rate = 11;
    // 商品id
    uint64 product_id = 12;
    // 商品编码
    string product_code = 13;
    // 商品名称
    string product_name = 14;
    // 商品分类ID
    uint64 category_id = 15;
    // 商品分类ID
    string category_name = 16;
    // 零售价
    double sales_price = 17;
    // 扩展信息（商品价格等）
    string extends = 18;
    // 订货数量
    double quantity = 19;

    repeated StoreValidProduct relation_products = 20;

    // 关联商品的比例
    string ratio = 21;
    // 关联商品数量取值配置
    int32 configure = 22;
    uint64 store_id = 23;
    // 实时库存数量
    double real_inventory_qty = 24;
}

message GetValidProductByStoreRequest {
    uint64 store_id = 1;
    // 订货类型id
    uint64 order_type_id = 2;
    string lan = 5;
    uint64 product_id = 6;
    repeated uint64 store_ids = 7;
    // 排序条件
    string order_by = 8;
    // asc or desc, 暂时未使用
    string sort = 9;
     // 偏移(默认0)
    uint64 offset = 10;
    // 查询的每页数量
    uint64 limit = 11;
}

message GetValidProductByStoreResponse {
    repeated StoreValidProduct rows = 1;
    uint32 total = 2;
    // 库存未异动的商品
    repeated StoreValidProduct inventory_unchanged_rows = 3;
}

// 创建商品主配单请求参数
message CreateHdDemandRequest {
    // 订货日期
    google.protobuf.Timestamp demand_date = 1;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 2;
    // 备注
    string remark = 3;
    // 加盟门店主配单:FMD
    string type = 4;
    // 主配类型：按门店主配(STORE)、按商品主配(PRODUCT)
    string sub_type = 5;
    // 按商品分配：多商品、单门店
    ByProductParams by_product_params = 6;
    // 按门店主配：单商品、多门店
    ProductValidStore by_store_params = 7;
    // 业务类型(来源)：手动创建(MANUAL)、总部分配(HD_ASSIGN)、计划创建(PLAN)
    string bus_type = 8;
    message ByProductParams {
        // 收货方id
        uint64 received_by = 1;
        // 订货的详细信息
        repeated StoreValidProduct products = 2;
    }
    // 唯一请求id
    uint64 batch_id = 9;
    // 订货类型id
    uint64 order_type_id = 10;
}

message IdRequest {
    // 订货单id
    uint64 demand_id = 1;
    string lan = 2;
}

message IdResponse {
    // 订货单id
    uint64 demand_id = 1;
}

// 查询总部订货单参数
message ListHdDemandRequest {
    // 订货开始日期
    google.protobuf.Timestamp start_date = 1;
    // 订货结束日期
    google.protobuf.Timestamp end_date = 2;
    // 订单状态
    //  INITED = 新建(总部)
    //  PREPARE = 待付款(门店)-已提交(总部)
    //  INVALID = 已作废(总部)
    repeated string status = 3;
    // 门店ids
    repeated uint64 received_bys = 4;
    // 订货单号
    string code = 5;
    // 订货单类型：加盟门店订货单(FSD)、加盟门店主配单(FMD)
    repeated string types = 6;
    // 主配类型枚举：按门店主配(STORE)、按商品主配(PRODUCT)
    string sub_type = 7;
    // 门店连锁类型：加盟(FRS)、直营(DRS)
    string chain_type = 8;
    // 业务类型(来源)：手动创建(MANUAL)、总部分配(HD_ASSIGN)、计划创建(PLAN)
    repeated string bus_types = 9;
    // 偏移(默认0)
    uint64 offset = 10;
    // 查询的每页数量
    uint64 limit = 11;
    // 排序(默认asc)
    string order = 12;
    string sort = 13;
    bool include_total = 14;
    // 订货单ID列表
    repeated uint64 ids = 15;
    // 商品ids
    repeated uint64 product_ids = 16;
    // 加盟商ids
    repeated uint64 franchisee_ids = 17;
    // 支付方式
    // "BohCreditPay"  BOH信用付
    // "WXMPay"        微信小程序
    // "BohOfflinePay" 线下支付
    // "BohVoucherPay" 代金券支付
    repeated string payment_ways = 18;
    // 订货类型ids
    repeated uint64 order_type_ids = 19;
    string lan = 20;
}

// 查询门店订货单
message ListHdDemandResponse {
    repeated Demand rows = 1;
    uint64 total = 2;
}

message GetHdDemandProductRequest {
    // 订货单id
    uint64 demand_id = 1;
    // 分页限制
    int64 limit = 2;
    // 偏移量
    string offset = 3;
    bool include_total = 4;
    // 排序方式  asc or desc
    string order = 5;
    // 排序字段
    string sort = 6;
    string lan = 7;

    bool expand = 8;
}

// 获取订货商品返回参数
message GetHdDemandProductResponse {
    repeated Product rows = 1;
    uint64 total = 2;
}

message Product {
    // 数据id
    uint64 id = 1;
    // 商品id
    uint64 product_id = 2;
    // 商品编号
    string product_code = 3;
    // 商品名称
    string product_name = 4;
    // 原商品ID
    uint64 org_product_id = 5;
    // 原商品编号
    string org_product_code = 6;
    // 原商品名称
    string org_product_name = 7;
    // 商品分类id
    uint64 category_id = 8;
    // 商品分类名称
    string category_name = 9;
    // 商品规格
    string product_spec = 10;
    // 单位ID
    uint64 unit_id = 11;
    // 单位名称
    string unit_name = 12;
    // 单位规格
    string unit_spec = 13;
    // 核算单位ID
    uint64 accounting_unit_id = 14;
    // 核算单位名称
    string accounting_unit_name = 15;
    // 核算单位规格
    string accounting_unit_spec = 16;
    // 单位转换率
    double unit_rate = 17;
    // 订货数量
    double quantity = 18;
    // 核算数量
    double accounting_quantity = 19;
    // 最小订量
    double min_quantity = 20;
    // 最大订量
    double max_quantity = 21;
    // 递增订量
    double increment_quantity = 22;
    // 含税单价
    double tax_price = 23;
    // 成本单价
    double cost_price = 24;
    // 税率
    double tax_rate = 25;
    // 单项合计金额
    double amount = 26;
    // 预计到货天数
    float arrival_days = 27;
    // 配送方式
    string distribution_type = 28;
    // 配送方
    uint64 distribute_by = 29;
    // 配送方名称
    string distribute_name = 30;
    // 分配状态
    string status = 31;
    // 昨日销量
    double yesterday_sales = 32;
    // 实时库存
    double inventory_qty = 33;
    // 在途(待收)数量
    double on_way_qty = 34;
    //0未删除 1已删除
    uint32 is_delete = 35;
    // 创建日期
    google.protobuf.Timestamp created_at = 36;
    // 更新时间
    google.protobuf.Timestamp updated_at = 37;
    // 创建人
    uint64 created_by = 38;
    string created_name = 39;
    // 更新人id
    uint64 updated_by = 40;
    string updated_name = 41;
    // 订单ID
    uint64 demand_id = 42;
    // 租户ID
    uint64 partner_id = 43;
    // 是否可订货
    bool allow_order = 44;
    // 确认数量
    double confirm_quantity = 45;
    // 确认金额
    double confirm_amount = 46;
    // 扩展信息:(eg: {"product_price": [xxx]})
    string extends = 47;
    // 储藏类型
    string storage_type = 48;
    // 零售单价
    double sales_price = 49;
    // 零售金额
    double sales_amount = 50;
    // 确认零售金额
    double confirm_sales_amount = 51;
    // 审核数量
    double approve_quantity = 52;
    // 审核金额
    double approve_amount = 53;
    // 审核零售金额
    double approve_sales_amount = 54;
    // 是否调整了确认订货数量
    bool is_confirm_qty = 55;
    // 是否调整了审核订货数量
    bool is_approve_qty = 56;
    // 关联商品的比例
    string ratio = 57;
    // 关联商品数量取值配置
    int32 configure = 58;
    // 捆绑商品或者组合商品
    repeated Product relation_products = 59;
}

message Demand {
    // 单据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // 生成的批次id
    uint64 batch_id = 3;
    // 编码
    string code = 4;
    // 订货单类型
    string type = 5;
    // 主配类型(主配单才有的字段)
    string sub_type = 6;
    // 业务类型(来源)：手动创建(MANUAL)、总部分配(HD_ASSIGN)、计划创建(PLAN)
    string bus_type = 7;
    // 收货门店id
    uint64 received_by = 8;
    // 收货门店编码
    string received_code = 9;
    // 收货门店名称
    string received_name = 10;
    // 配送方id
    uint64 distribute_by = 11;
    // 门店类型
    string store_type = 12;
    // 加盟商id
    uint64 franchisee_id = 13;
    // 加盟商code
    string franchisee_code = 14;
    // 加盟商名称
    string franchisee_name = 15;
    // 订货日期
    google.protobuf.Timestamp demand_date = 16;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 17;
    // 单据业务状态
    string status = 18;
    // 处理状态
    string process_status = 19;
    // 驳回原因
    string reject_reason = 20;
    // 备注
    string remark = 21;
    // 原因
    string reason = 22;
    // 创建日期
    google.protobuf.Timestamp created_at = 23;
    // 更新时间
    google.protobuf.Timestamp updated_at = 24;
    // 创建人
    uint64 created_by = 25;
    string created_name = 26;
    // 更新人id
    uint64 updated_by = 27;
    string updated_name = 28;
    // 审核人id
    uint64 review_by = 29;
    // 是否有商品
    int32 has_product = 30;
    // 合计含税金额
    double sum_price_tax = 31;
    // 合计税额
    double sum_tax = 32;
    // 实际付款金额
    double pay_amount = 33;
    // 付款凭证（附件）
    repeated string attachments = 34;
    // 扩展信息:(eg: {"product_names": [xxx]})
    string extends = 35;
    message Order {
        uint64 order_id = 1;
        string order_code = 2;
    }
    // 要货单们
    repeated Order orders = 36;
    // 付款方式
    // "BohCreditPay"  BOH信用付
    // "WXMPay"        微信小程序
    // "BohOfflinePay" 线下支付
    // "BohVoucherPay" 代金券支付
    string payment_way = 37;
    // 门店连锁类型
    string chain_type = 38;
    // 是否调整了订货数量
    bool is_adjust = 39;
    // 确认金额
    double confirm_amount = 40;
    // 订货类型id
    uint64 order_type_id = 41;
    // 订货类型名称
    string order_type_name = 42;
    // 订货类型编码
    string order_type_code = 43;
    // 零售金额
    double sales_amount = 44;
    // 确认零售金额
    double confirm_sales_amount = 45;
    message Refund {
        uint64 refund_id = 1;
        string refund_code = 2;
    }
    // 退款单们
    repeated Refund refunds = 46;
    // 审核金额
    double approve_amount = 47;
    // 审核零售金额
    double approve_sales_amount = 48;
    // 订货类型关联时间组
    message OrderTypeTime {
        repeated string order_time = 1;
        repeated string audit_time = 2;
    }
    OrderTypeTime order_type_time = 49;
}

// 更新订货单信息参数
message UpdateHdDemandProductRequest {
    // 订货单id
    uint64 demand_id = 1;
    // 备注
    string remark = 2;
    // 商品
    repeated UpdateProduct products = 4;
    message UpdateProduct {
        // 商品id
        uint64 product_id = 1;
        // 商品分类ID
        uint64 category_id = 2;
        // 订货数量
        double quantity = 3;
        // 订货单位
        uint64 unit_id = 4;
        // 含税单价
        double tax_price = 5;
        // 税率
        double tax_rate = 6;
        // 预计到货天数
        float arrival_days = 7;
        // 零售单价
        double sales_price = 8;
        // 最小订量
        double min_quantity = 12;
        // 最大订量
        double max_quantity = 13;
        // 递增订量
        double increment_quantity = 14;

        repeated UpdateProduct relation_products = 15;
        // 关联商品的比例
        string ratio = 16;
        // 关联商品数量取值配置
        int32 configure = 17;

    }
}

message UpdateHdDemandProductResponse {
    // 订货单id
    uint64 demand_id = 1;
    // 描述
    string description = 2;
    // 不合法商品列表
    repeated string product_names = 3;
}

message DealHdDemandByIdRequest {
    // 批量只支持'提交'和'作废'操作
    repeated uint64 demand_ids = 1;
    // 单据业务状态，对应Status枚举
    //  PREPARE = 已提交(总部)
    //  INVALID = 已作废(总部)
    string action = 2;
    // 备注
    string remark = 3;
    // 附件
    repeated string attachments = 4;
    // 是明细操作还是列表操作
    bool is_detailed = 5;
}



message CheckProductMessage {
    // 商品名称
    string product_name = 1;
    // 商品编号
    string product_code = 2;
    // 规格
    string spec = 3;
    // 订货数量
    double quantity = 4;
    // 起订量
    double min_quantity = 5;
    // 递增定量
    double increment_quantity = 6;
    // 最大定量
    double max_quantity = 7;
    // 订货单位
    string unit = 8;
    // 单价
    double tax_price = 9;
    // 订货金额
    double amount = 10;
    // 储藏类型
    string storage_type = 11;
    // id
    uint64 id = 12;
    // 商品id
    uint64 product_id = 13;
}

message CheckOrderMessage {
    // 订单状态
    string status = 1;
    // 订单编号
    string code = 2;
    // 订货门店
    string store_name = 3;
    // 订货类型
    uint64 order_type_id = 4;
    // 加盟商
    string franchisee_name = 5;
    // 订货金额
    double amount = 6;
    // 订货日期
    google.protobuf.Timestamp demand_date = 7;
    // 预计到货日期
    google.protobuf.Timestamp arrival_date = 8;
    // msg 返回check信息
    string msg = 9;
}
message BatchDealHdDemandRequest{
    // 订货开始日期
    google.protobuf.Timestamp start_date = 1;
    // 订货结束日期
    google.protobuf.Timestamp end_date = 2;
    // 接收方ids
    repeated uint64 received_bys = 3;
    // 业务类型(来源)：手动创建(MANUAL)、总部分配(HD_ASSIGN)、计划创建(PLAN)
    repeated string bus_types = 4;
    // 订货类型ids
    repeated uint64 order_type_ids = 5;
    // 线路ID
    uint64 line_doc_id = 6;
    // 批量更新方式:
    // "ALL"  全量按条件(支持排除部分订单：详见参数exclude_ids)
    // "SELF" 部分按订单(选择部分订单批量提交：详见参数include_ids)
    string batch_method = 8;
    // 排除订单ID列表(batch_type="ALL"时使用)
    repeated uint64 exclude_ids = 9;
    // 提交订单ID列表(batch_type="SELF"时必传)
    repeated uint64 include_ids = 10;
    string action = 11;
}
message BatchDealHdDemandResponse{
    bool result = 1;
    string description = 2;
    repeated CheckOrderMessage msgs = 3;
}