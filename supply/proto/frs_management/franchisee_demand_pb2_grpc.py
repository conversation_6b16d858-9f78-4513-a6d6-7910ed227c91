# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from frs_management import franchisee_demand_pb2 as frs__management_dot_franchisee__demand__pb2


class FranchiseeDemandServiceStub(object):
  """加盟商订货 -- 加盟商总部入口
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateFDemandBatch = channel.unary_unary(
        '/franchisee_demand.FranchiseeDemandService/CreateFDemandBatch',
        request_serializer=frs__management_dot_franchisee__demand__pb2.CreateFDemandBatchRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchisee__demand__pb2.CreateFDemandBatchResponse.FromString,
        )
    self.ListFDemand = channel.unary_unary(
        '/franchisee_demand.FranchiseeDemandService/ListFDemand',
        request_serializer=frs__management_dot_franchisee__demand__pb2.ListFDemandRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchisee__demand__pb2.ListFDemandResponse.FromString,
        )
    self.GetFDemandById = channel.unary_unary(
        '/franchisee_demand.FranchiseeDemandService/GetFDemandById',
        request_serializer=frs__management_dot_franchisee__demand__pb2.GetFDemandByIdRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchisee__demand__pb2.Demand.FromString,
        )
    self.GetHistoryById = channel.unary_unary(
        '/franchisee_demand.FranchiseeDemandService/GetHistoryById',
        request_serializer=frs__management_dot_franchisee__demand__pb2.GetHistoryByIdRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchisee__demand__pb2.HistoryResponse.FromString,
        )
    self.GetProductsById = channel.unary_unary(
        '/franchisee_demand.FranchiseeDemandService/GetProductsById',
        request_serializer=frs__management_dot_franchisee__demand__pb2.GetProductsByIdRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchisee__demand__pb2.ProductsResponse.FromString,
        )
    self.UpdateProducts = channel.unary_unary(
        '/franchisee_demand.FranchiseeDemandService/UpdateProducts',
        request_serializer=frs__management_dot_franchisee__demand__pb2.UpdateProductRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchisee__demand__pb2.UpdateProductsResponse.FromString,
        )
    self.DealFDemandById = channel.unary_unary(
        '/franchisee_demand.FranchiseeDemandService/DealFDemandById',
        request_serializer=frs__management_dot_franchisee__demand__pb2.DealFDemandByIdRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchisee__demand__pb2.DealFDemandByIdResponse.FromString,
        )
    self.GetProductDistribution = channel.unary_unary(
        '/franchisee_demand.FranchiseeDemandService/GetProductDistribution',
        request_serializer=frs__management_dot_franchisee__demand__pb2.GetProductDistributionRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchisee__demand__pb2.GetProductDistributionResponse.FromString,
        )
    self.AddFDemandProduct = channel.unary_unary(
        '/franchisee_demand.FranchiseeDemandService/AddFDemandProduct',
        request_serializer=frs__management_dot_franchisee__demand__pb2.AddFDemandProductRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchisee__demand__pb2.AddFDemandProductResponse.FromString,
        )
    self.ImportFDemandProducts = channel.unary_unary(
        '/franchisee_demand.FranchiseeDemandService/ImportFDemandProducts',
        request_serializer=frs__management_dot_franchisee__demand__pb2.ImportFDemandProductsRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchisee__demand__pb2.ImportFDemandProductsResponse.FromString,
        )
    self.BatchResetFDemand = channel.unary_unary(
        '/franchisee_demand.FranchiseeDemandService/BatchResetFDemand',
        request_serializer=frs__management_dot_franchisee__demand__pb2.BatchDealFDemandRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchisee__demand__pb2.BatchDealFDemandResponse.FromString,
        )
    self.ListFDemandToRemind = channel.unary_unary(
        '/franchisee_demand.FranchiseeDemandService/ListFDemandToRemind',
        request_serializer=frs__management_dot_franchisee__demand__pb2.ListFDemandToRemindRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchisee__demand__pb2.ListFDemandToRemindResponse.FromString,
        )
    self.BatchConfirmFDemand = channel.unary_unary(
        '/franchisee_demand.FranchiseeDemandService/BatchConfirmFDemand',
        request_serializer=frs__management_dot_franchisee__demand__pb2.BatchDealFDemandRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchisee__demand__pb2.BatchDealFDemandResponse.FromString,
        )
    self.CheckFDemandTodo = channel.unary_unary(
        '/franchisee_demand.FranchiseeDemandService/CheckFDemandTodo',
        request_serializer=frs__management_dot_franchisee__demand__pb2.CheckFDemandTodoRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchisee__demand__pb2.CheckFDemandTodoResponse.FromString,
        )


class FranchiseeDemandServiceServicer(object):
  """加盟商订货 -- 加盟商总部入口
  """

  def CreateFDemandBatch(self, request, context):
    """批量创建订单(总部分配导入使用)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListFDemand(self, request, context):
    """查询订货单列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetFDemandById(self, request, context):
    """根据id查询单个订货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetHistoryById(self, request, context):
    """根据id查询单据历史记录
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProductsById(self, request, context):
    """根据id查询单据内的商品列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateProducts(self, request, context):
    """更新订货单商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DealFDemandById(self, request, context):
    """修改订单状态统一入口
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProductDistribution(self, request, context):
    """获取订货商品可分配仓库(包含实时库存)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddFDemandProduct(self, request, context):
    """订单新增商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ImportFDemandProducts(self, request, context):
    """导入订货单(改单)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def BatchResetFDemand(self, request, context):
    """批量还原订单(批量还原订单导入数量)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListFDemandToRemind(self, request, context):
    """门店未订货提醒列表（WEB端）
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def BatchConfirmFDemand(self, request, context):
    """按条件批量确认订单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CheckFDemandTodo(self, request, context):
    """检查订货待办能否订货
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_FranchiseeDemandServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateFDemandBatch': grpc.unary_unary_rpc_method_handler(
          servicer.CreateFDemandBatch,
          request_deserializer=frs__management_dot_franchisee__demand__pb2.CreateFDemandBatchRequest.FromString,
          response_serializer=frs__management_dot_franchisee__demand__pb2.CreateFDemandBatchResponse.SerializeToString,
      ),
      'ListFDemand': grpc.unary_unary_rpc_method_handler(
          servicer.ListFDemand,
          request_deserializer=frs__management_dot_franchisee__demand__pb2.ListFDemandRequest.FromString,
          response_serializer=frs__management_dot_franchisee__demand__pb2.ListFDemandResponse.SerializeToString,
      ),
      'GetFDemandById': grpc.unary_unary_rpc_method_handler(
          servicer.GetFDemandById,
          request_deserializer=frs__management_dot_franchisee__demand__pb2.GetFDemandByIdRequest.FromString,
          response_serializer=frs__management_dot_franchisee__demand__pb2.Demand.SerializeToString,
      ),
      'GetHistoryById': grpc.unary_unary_rpc_method_handler(
          servicer.GetHistoryById,
          request_deserializer=frs__management_dot_franchisee__demand__pb2.GetHistoryByIdRequest.FromString,
          response_serializer=frs__management_dot_franchisee__demand__pb2.HistoryResponse.SerializeToString,
      ),
      'GetProductsById': grpc.unary_unary_rpc_method_handler(
          servicer.GetProductsById,
          request_deserializer=frs__management_dot_franchisee__demand__pb2.GetProductsByIdRequest.FromString,
          response_serializer=frs__management_dot_franchisee__demand__pb2.ProductsResponse.SerializeToString,
      ),
      'UpdateProducts': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateProducts,
          request_deserializer=frs__management_dot_franchisee__demand__pb2.UpdateProductRequest.FromString,
          response_serializer=frs__management_dot_franchisee__demand__pb2.UpdateProductsResponse.SerializeToString,
      ),
      'DealFDemandById': grpc.unary_unary_rpc_method_handler(
          servicer.DealFDemandById,
          request_deserializer=frs__management_dot_franchisee__demand__pb2.DealFDemandByIdRequest.FromString,
          response_serializer=frs__management_dot_franchisee__demand__pb2.DealFDemandByIdResponse.SerializeToString,
      ),
      'GetProductDistribution': grpc.unary_unary_rpc_method_handler(
          servicer.GetProductDistribution,
          request_deserializer=frs__management_dot_franchisee__demand__pb2.GetProductDistributionRequest.FromString,
          response_serializer=frs__management_dot_franchisee__demand__pb2.GetProductDistributionResponse.SerializeToString,
      ),
      'AddFDemandProduct': grpc.unary_unary_rpc_method_handler(
          servicer.AddFDemandProduct,
          request_deserializer=frs__management_dot_franchisee__demand__pb2.AddFDemandProductRequest.FromString,
          response_serializer=frs__management_dot_franchisee__demand__pb2.AddFDemandProductResponse.SerializeToString,
      ),
      'ImportFDemandProducts': grpc.unary_unary_rpc_method_handler(
          servicer.ImportFDemandProducts,
          request_deserializer=frs__management_dot_franchisee__demand__pb2.ImportFDemandProductsRequest.FromString,
          response_serializer=frs__management_dot_franchisee__demand__pb2.ImportFDemandProductsResponse.SerializeToString,
      ),
      'BatchResetFDemand': grpc.unary_unary_rpc_method_handler(
          servicer.BatchResetFDemand,
          request_deserializer=frs__management_dot_franchisee__demand__pb2.BatchDealFDemandRequest.FromString,
          response_serializer=frs__management_dot_franchisee__demand__pb2.BatchDealFDemandResponse.SerializeToString,
      ),
      'ListFDemandToRemind': grpc.unary_unary_rpc_method_handler(
          servicer.ListFDemandToRemind,
          request_deserializer=frs__management_dot_franchisee__demand__pb2.ListFDemandToRemindRequest.FromString,
          response_serializer=frs__management_dot_franchisee__demand__pb2.ListFDemandToRemindResponse.SerializeToString,
      ),
      'BatchConfirmFDemand': grpc.unary_unary_rpc_method_handler(
          servicer.BatchConfirmFDemand,
          request_deserializer=frs__management_dot_franchisee__demand__pb2.BatchDealFDemandRequest.FromString,
          response_serializer=frs__management_dot_franchisee__demand__pb2.BatchDealFDemandResponse.SerializeToString,
      ),
      'CheckFDemandTodo': grpc.unary_unary_rpc_method_handler(
          servicer.CheckFDemandTodo,
          request_deserializer=frs__management_dot_franchisee__demand__pb2.CheckFDemandTodoRequest.FromString,
          response_serializer=frs__management_dot_franchisee__demand__pb2.CheckFDemandTodoResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'franchisee_demand.FranchiseeDemandService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
