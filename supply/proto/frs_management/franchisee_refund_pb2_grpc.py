# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from frs_management import franchisee_refund_pb2 as frs__management_dot_franchisee__refund__pb2


class FranchiseeRefundServiceStub(object):
  """加盟商订货退款 -- 加盟商运营总部入口
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateFRefund = channel.unary_unary(
        '/franchisee_refund.FranchiseeRefundService/CreateFRefund',
        request_serializer=frs__management_dot_franchisee__refund__pb2.CreateFRefundRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchisee__refund__pb2.CreateFRefundResponse.FromString,
        )
    self.ListFRefund = channel.unary_unary(
        '/franchisee_refund.FranchiseeRefundService/ListFRefund',
        request_serializer=frs__management_dot_franchisee__refund__pb2.ListFRefundRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchisee__refund__pb2.ListFRefundResponse.FromString,
        )
    self.GetFRefundById = channel.unary_unary(
        '/franchisee_refund.FranchiseeRefundService/GetFRefundById',
        request_serializer=frs__management_dot_franchisee__refund__pb2.GetFRefundByIdRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchisee__refund__pb2.GetFRefundByIdResponse.FromString,
        )
    self.GetFRefundHistoryById = channel.unary_unary(
        '/franchisee_refund.FranchiseeRefundService/GetFRefundHistoryById',
        request_serializer=frs__management_dot_franchisee__refund__pb2.GetHistoryByIdRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchisee__refund__pb2.RefundHistoryResponse.FromString,
        )
    self.DealFRefundById = channel.unary_unary(
        '/franchisee_refund.FranchiseeRefundService/DealFRefundById',
        request_serializer=frs__management_dot_franchisee__refund__pb2.DealFRefundByIdRequest.SerializeToString,
        response_deserializer=frs__management_dot_franchisee__refund__pb2.DealFRefundByIdResponse.FromString,
        )


class FranchiseeRefundServiceServicer(object):
  """加盟商订货退款 -- 加盟商运营总部入口
  """

  def CreateFRefund(self, request, context):
    """创建退款单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListFRefund(self, request, context):
    """查询退款单列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetFRefundById(self, request, context):
    """根据id查询单个退款单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetFRefundHistoryById(self, request, context):
    """根据id查询单据历史记录
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DealFRefundById(self, request, context):
    """修改退款单状态统一入口
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_FranchiseeRefundServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateFRefund': grpc.unary_unary_rpc_method_handler(
          servicer.CreateFRefund,
          request_deserializer=frs__management_dot_franchisee__refund__pb2.CreateFRefundRequest.FromString,
          response_serializer=frs__management_dot_franchisee__refund__pb2.CreateFRefundResponse.SerializeToString,
      ),
      'ListFRefund': grpc.unary_unary_rpc_method_handler(
          servicer.ListFRefund,
          request_deserializer=frs__management_dot_franchisee__refund__pb2.ListFRefundRequest.FromString,
          response_serializer=frs__management_dot_franchisee__refund__pb2.ListFRefundResponse.SerializeToString,
      ),
      'GetFRefundById': grpc.unary_unary_rpc_method_handler(
          servicer.GetFRefundById,
          request_deserializer=frs__management_dot_franchisee__refund__pb2.GetFRefundByIdRequest.FromString,
          response_serializer=frs__management_dot_franchisee__refund__pb2.GetFRefundByIdResponse.SerializeToString,
      ),
      'GetFRefundHistoryById': grpc.unary_unary_rpc_method_handler(
          servicer.GetFRefundHistoryById,
          request_deserializer=frs__management_dot_franchisee__refund__pb2.GetHistoryByIdRequest.FromString,
          response_serializer=frs__management_dot_franchisee__refund__pb2.RefundHistoryResponse.SerializeToString,
      ),
      'DealFRefundById': grpc.unary_unary_rpc_method_handler(
          servicer.DealFRefundById,
          request_deserializer=frs__management_dot_franchisee__refund__pb2.DealFRefundByIdRequest.FromString,
          response_serializer=frs__management_dot_franchisee__refund__pb2.DealFRefundByIdResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'franchisee_refund.FranchiseeRefundService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
