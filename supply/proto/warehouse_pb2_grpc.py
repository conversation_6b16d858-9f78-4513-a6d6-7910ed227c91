# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import warehouse_pb2 as warehouse__pb2


class warehouseStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreatePurchaseOrder = channel.unary_unary(
        '/warehouse.warehouse/CreatePurchaseOrder',
        request_serializer=warehouse__pb2.CreatePurchaseOrderRequest.SerializeToString,
        response_deserializer=warehouse__pb2.CreatePurchaseOrderResponse.FromString,
        )
    self.ListPurchaseOrder = channel.unary_unary(
        '/warehouse.warehouse/ListPurchaseOrder',
        request_serializer=warehouse__pb2.ListPurchaseOrderRequest.SerializeToString,
        response_deserializer=warehouse__pb2.ListPurchaseOrderResponse.FromString,
        )
    self.ChangeOrderStatus = channel.unary_unary(
        '/warehouse.warehouse/ChangeOrderStatus',
        request_serializer=warehouse__pb2.ChangeOrderStatusRequest.SerializeToString,
        response_deserializer=warehouse__pb2.ChangeOrderStatusResponse.FromString,
        )
    self.UpdatePurchaseOrder = channel.unary_unary(
        '/warehouse.warehouse/UpdatePurchaseOrder',
        request_serializer=warehouse__pb2.UpdatePurchaseOrderRequest.SerializeToString,
        response_deserializer=warehouse__pb2.UpdatePurchaseOrderResponse.FromString,
        )
    self.GetOrderDetailById = channel.unary_unary(
        '/warehouse.warehouse/GetOrderDetailById',
        request_serializer=warehouse__pb2.GetOrderDetailByIdRequest.SerializeToString,
        response_deserializer=warehouse__pb2.GetOrderDetailByIdResponse.FromString,
        )
    self.GetProductListByWHId = channel.unary_unary(
        '/warehouse.warehouse/GetProductListByWHId',
        request_serializer=warehouse__pb2.GetProductListByWHIdRequest.SerializeToString,
        response_deserializer=warehouse__pb2.GetProductListByWHIdResponse.FromString,
        )
    self.GetPurchaseBi = channel.unary_unary(
        '/warehouse.warehouse/GetPurchaseBi',
        request_serializer=warehouse__pb2.GetPurchaseBiRequest.SerializeToString,
        response_deserializer=warehouse__pb2.GetPurchaseBiResponse.FromString,
        )


class warehouseServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def CreatePurchaseOrder(self, request, context):
    """新建仓库采购订单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListPurchaseOrder(self, request, context):
    """仓库采购订单列表查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ChangeOrderStatus(self, request, context):
    """修改订单状态
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdatePurchaseOrder(self, request, context):
    """更新商品信息：已驳回状态和新建状态下可以修改采购单信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetOrderDetailById(self, request, context):
    """查询订单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProductListByWHId(self, request, context):
    """根据仓库id拉商品列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPurchaseBi(self, request, context):
    """仓库采购报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_warehouseServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreatePurchaseOrder': grpc.unary_unary_rpc_method_handler(
          servicer.CreatePurchaseOrder,
          request_deserializer=warehouse__pb2.CreatePurchaseOrderRequest.FromString,
          response_serializer=warehouse__pb2.CreatePurchaseOrderResponse.SerializeToString,
      ),
      'ListPurchaseOrder': grpc.unary_unary_rpc_method_handler(
          servicer.ListPurchaseOrder,
          request_deserializer=warehouse__pb2.ListPurchaseOrderRequest.FromString,
          response_serializer=warehouse__pb2.ListPurchaseOrderResponse.SerializeToString,
      ),
      'ChangeOrderStatus': grpc.unary_unary_rpc_method_handler(
          servicer.ChangeOrderStatus,
          request_deserializer=warehouse__pb2.ChangeOrderStatusRequest.FromString,
          response_serializer=warehouse__pb2.ChangeOrderStatusResponse.SerializeToString,
      ),
      'UpdatePurchaseOrder': grpc.unary_unary_rpc_method_handler(
          servicer.UpdatePurchaseOrder,
          request_deserializer=warehouse__pb2.UpdatePurchaseOrderRequest.FromString,
          response_serializer=warehouse__pb2.UpdatePurchaseOrderResponse.SerializeToString,
      ),
      'GetOrderDetailById': grpc.unary_unary_rpc_method_handler(
          servicer.GetOrderDetailById,
          request_deserializer=warehouse__pb2.GetOrderDetailByIdRequest.FromString,
          response_serializer=warehouse__pb2.GetOrderDetailByIdResponse.SerializeToString,
      ),
      'GetProductListByWHId': grpc.unary_unary_rpc_method_handler(
          servicer.GetProductListByWHId,
          request_deserializer=warehouse__pb2.GetProductListByWHIdRequest.FromString,
          response_serializer=warehouse__pb2.GetProductListByWHIdResponse.SerializeToString,
      ),
      'GetPurchaseBi': grpc.unary_unary_rpc_method_handler(
          servicer.GetPurchaseBi,
          request_deserializer=warehouse__pb2.GetPurchaseBiRequest.FromString,
          response_serializer=warehouse__pb2.GetPurchaseBiResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'warehouse.warehouse', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
