syntax = "proto3";

package transfer;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
option   go_package = "./transfer";

//Transfer 调拨服务
service transfer {
    //Ping 健康检查
    rpc Ping (google.protobuf.Empty) returns (Pong) {
        option (google.api.http) = { get: "/ping" };
    }
    //GetTransfer 查询调拨单1
    rpc GetTransfer (GetTransferRequest) returns (GetTransferResponse) {
        option (google.api.http) = { get: "/api/v2/supply/transfer" };
    }
    //GetTransferByID 查询一个调拨单2
    rpc GetTransferByID (GetTransferByIDRequest) returns (GetTransferByIDResponse) {
        option (google.api.http) = { get: "/api/v2/supply/transfer/{transfer_id}" };
    }
    //GetTransferProductByBranchID 取得门店可调拨商品3
    rpc GetTransferProductByBranchID (GetTransferProductByBranchIDRequest) returns (GetTransferProductByBranchIDResponse) {
        option (google.api.http) = { get: "/api/v2/supply/transfer/store/{store_id}/product" };
    }
    //GetTransferRegionByID 查询相同属性区域门店4
    rpc GetTransferRegionByBranchID (GetTransferRegionByBranchIDRequest) returns (GetTransferRegionByBranchIDResponse) {
        option (google.api.http) = { get: "/api/v2/supply/transfer/store/{store_id}/region" };
    }
    //GetTransferProductByTransferID 获取一个调拨单商品5
    rpc GetTransferProductByTransferID (GetTransferProductByTransferIDRequest) returns (GetTransferProductByTransferIDResponse) {
        option (google.api.http) = { get: "/api/v2/supply/transfer/{transfer_id}/product" };
    }
    //CreateTransfer 创建调拨单6
    rpc CreateTransfer (CreateTransferRequest) returns (Transfer) {
        option (google.api.http) = { post: "/api/v2/supply/transfer/main" body: "*"};
    }
    //UpdateTransfer 修改调拨单7
    rpc UpdateTransfer (UpdateTransferRequest) returns (UpdateTransferResponse) {
        option (google.api.http) = { put: "/api/v2/supply/transfer/main/{transfer_id}/update" body: "*"};
    }
    //ConfirmTransfer 确认调拨单8
    rpc ConfirmTransfer (ConfirmTransferRequest) returns (Transfer) {
        option (google.api.http) = { put: "/api/v2/supply/transfer/main/{transfer_id}/confirm" body: "*"};
    }
    //DeleteTransferProduct 删除调拨单商品10
    rpc DeleteTransferProduct (DeleteTransferProductRequest) returns (DeleteTransferProductResponse) {
        option (google.api.http) = { put: "/api/v2/supply/transfer/product/{transfer_product_id}/delete" body: "*"};
    }
    //DeleteTransfer 删除调拨单11
    rpc DeleteTransfer (DeleteTransferRequest) returns (DeleteTransferResponse) {
        option (google.api.http) = { put: "/api/v2/supply/transfer/main/{transfer_id}/delete" body: "*"};
    }
    //SubmitTransfer 提交调拨单12
    rpc SubmitTransfer (SubmitTransferRequest) returns (Transfer) {
        option (google.api.http) = { put: "/api/v2/supply/transfer/main/{transfer_id}/submit" body: "*"};
    }
    // CancelTransfer 取消调拨单13
    rpc CancelTransfer (CancelTransferRequest) returns (Transfer) {
        option (google.api.http) = { put: "/api/v2/supply/transfer/main/{transfer_id}/cancel" body: "*"};
    }
    //SubmitTransferReceiving 提交调拨单收货数量13
    //    rpc SubmitTransferReceiving (SubmitTransferReceivingRequest) returns (Transfer) {
    //        option (google.api.http) = { put: "/api/v2/supply/transfer/main/{transfer_id}/submit/receiving" body: "*"};
    //    }
    //FinalizedTransfer 核算完成调拨单13
    //    rpc FinalizedTransfer (FinalizedTransferRequest) returns (FinalizedTransferResponse) {
    //        option (google.api.http) = { put: "/api/v2/supply/transfer/main/{transfer_id}/finalize"};
    //    }
    //GetTransferCollect调拨单汇总报表14
    rpc GetTransferCollect (GetTransferCollectRequest) returns (GetTransferCollectResponse) {
        option (google.api.http) = { get: "/api/v2/supply/transfer/collect/report"};
    }
    //GetTransferCollectDetailed调拨单明细汇总报表15
    rpc GetTransferCollectDetailed (GetTransferCollectDetailedRequest) returns (GetTransferCollectDetailedResponse) {
        option (google.api.http) = { get: "/api/v2/supply/transfer/bi/detailed"};
    }
    //CreateTransfer 自动创建内部调拨单接口(暂时仅内部rpc调用)
    rpc CreateInnerTransfer (CreateInnerTransferRequest) returns (CreateInnerTransferResponse) {
        option (google.api.http) = { post: "/api/v2/supply/inner/transfer" body: "*"};
    }

    // 三方确认调拨单
    rpc ConfirmTransferByCode (ConfirmTransferByCodeRequest) returns (ConfirmTransferByCodeResponse) {
        option (google.api.http) = { 
            put: "/api/v2/supply/tp/transfer/{transfer_id}/confirm" 
            body: "*"
        };
    }
}

message Pong {
    string msg = 1;
}
// A account resource.
message GetTransferRequest {
    //是否包含总数
    bool include_total = 1;
    enum STATUS {
        NONE = 0;
        INITED = 1;
        SUBMITTED = 2;
        CONFIRMED = 3;
        CANCELLED = 4;
    }
    //状态
    repeated STATUS status = 2;
    //调出门店
    repeated uint64 shipping_stores = 3;
    //调入门店
    repeated uint64 receiving_stores = 4;
    // 商品ids
    repeated uint64 product_ids = 5;
    //分页开始处
    uint32 offset = 6;
    //返回条数
    uint32 limit = 7;
    //查询开始时间
    google.protobuf.Timestamp start_date = 9;
    //查询结束时间
    google.protobuf.Timestamp end_date = 10;
    string code =11;
    bool no_jde_code = 12;
    bool auto_confirm = 13;
    string jde_code = 14;
        // 排序(默认asc)
    string order = 15;
    string sort = 16;
    // 录入方式，区分门店/仓库/加工中心 STORE/WAREHOUSE/MACHINING_CENTER
    string branch_type = 17;
    string lan = 18;
    // 调拨类型(自动AUTO/手动MANUAL)
    repeated string types = 19;
    // 区分内部`INTERNAL`/外部`EXTERNAL`调拨
    string sub_type = 20;
    // 接收仓位
    repeated uint64 receiving_positions = 21;
    // 调出仓位
    repeated uint64 shipping_positions = 22;
}
enum P_STATUS {
    NONE = 0;
    INITED = 1;
    PROCESSING = 2;
    SUCCESSED = 3;
    FAILED = 4;
}
message Transfer {
    //调拨单ID
    uint64 id = 1;
    uint64 master_id = 2;
    uint64 partner_id = 3;
    //调拨单号
    uint64 transfer_order_number = 4;
    //调拨门店ID
    uint64 shipping_store = 5;
    //调拨门店名称
    string shipping_store_name = 6;
    //收货门店名称
    string receiving_store_name = 7;
    //收货门店ID
    uint64 receiving_store = 8;
    //调拨单编码
    string code = 9;
    //描述
    string description = 10;
    //调拨原因类型
    string reason_type = 11;
    //接收人
    uint64 receiver = 12;
    //备注
    string remark = 13;
    //调拨者
    uint64 shipper = 14;
    string extends = 15;
    // 调拨类型(自动AUTO/手动MANUAL)
    string type = 16;
    //调拨单状态
    enum STATUS {
        NONE = 0;
        INITED = 1;
        SUBMITTED = 2;
        CONFIRMED = 3;
        CANCELLED = 4;
    }
    STATUS status = 17;
    //操作状态
    P_STATUS process_status = 18;
    // 区分内部`INTERNAL`/外部`EXTERNAL`调拨
    string sub_type = 19;
    //创建者
    uint64 created_by = 20;
    //更新者
    uint64 updated_by = 21;
    //调拨单日期
    google.protobuf.Timestamp transfer_date = 22;
    //调拨单收货日
    google.protobuf.Timestamp receiving_date = 23;
    //调拨时间
    google.protobuf.Timestamp shipping_date = 24;
    //调拨单创建时间
    google.protobuf.Timestamp created_at = 25;
    //更新时间
    google.protobuf.Timestamp updated_at = 26;
    uint64 request_id = 27;
    uint64 user_id = 28;
    string created_name = 29;
    string updated_name = 30;
    string jde_order_id = 31;
    bool auto_confirm =32;
    // 录入方式，区分门店还是仓库STORE/WAREHOUSE
    string branch_type = 33;
    string attachments = 34;
    // 接收仓位id
    uint64 receiving_position = 35;
    // 接收仓位名称
    string receiving_position_name = 36;
    // 调出仓位id
    uint64 shipping_position = 37;
    // 调出仓位名称
    string shipping_position_name = 38;
    // 子账户类型，如果开启了多仓位值为"position"没配置多仓位为空
    string sub_account_type = 39;
    // 是否跨公司调拨
    string cross_company = 40;
    // 调拨总金额
    double total_amount = 41;
    // 调拨零售总金额
    double total_sales_amount = 42;
}
message GetTransferResponse {
    repeated Transfer rows = 1;
    uint32 total = 2;
}
message GetTransferByIDRequest {
    //调拨单id
    uint64 transfer_id = 1;
    string lan = 2;
}
message GetTransferByIDResponse {
    //调拨单ID
    uint64 id = 1;
    uint64 master_id = 2;
    uint64 partner_id = 3;
    //调拨单号
    uint64 transfer_order_number = 4;
    //调拨门店
    uint64 shipping_store = 5;
    //调拨门店副ID
    string shipping_store_name = 6;
    //收货门店us_ID
    string receiving_store_name = 7;
    //收货门店
    uint64 receiving_store = 8;
    //调拨单编码
    string code = 9;
    //描述
    string description = 10;
    //调拨单
    string reason_type = 11;
    //接收者
    uint64 receiver = 12;
    //备注
    string remark = 13;
    //调拨人
    uint64 shipper = 14;
    string extends = 15;
    string type = 16;
    //调拨单状态
    enum STATUS {
        NONE = 0;
        INITED = 1;
        SUBMITTED = 2;
        CONFIRMED = 3;
        CANCELLED = 4;
    }
    STATUS status = 17;
    //操作状态
    P_STATUS process_status = 18;
    string sub_type = 19;
    //创建人
    uint64 created_by = 20;
    //更新人
    uint64 updated_by = 21;
    //调拨单日期
    google.protobuf.Timestamp transfer_date = 22;
    //调拨单收货日
    google.protobuf.Timestamp receiving_date = 23;
    //调拨时间
    google.protobuf.Timestamp shipping_date = 24;
    //调拨单创建时间
    google.protobuf.Timestamp created_at = 25;
    //更新时间
    google.protobuf.Timestamp updated_at = 26;
    uint64 user_id = 27;
    string created_name = 28;
    string updated_name = 29;
    string jde_order_id = 30;
    bool auto_confirm =31;
    string branch_type = 32;
    string attachments = 33;
    // 接收仓位id
    uint64 receiving_position = 35;
    // 接收仓位名称
    string receiving_position_name = 36;
    // 调出仓位id
    uint64 shipping_position = 37;
    // 调出仓位名称
    string shipping_position_name = 38;
    // 子账户类型，如果开启了多仓位值为"position"没配置多仓位为空
    string sub_account_type = 39;
    // 是否跨公司调拨
    string cross_company = 40;
    // 调拨总金额
    double total_amount = 41;
    // 调拨零售总金额
    double total_sales_amount = 42;
}
message GetTransferProductByBranchIDRequest {
    //门店id
    uint64 store_id = 1;
    //是否包含总数
    bool include_total = 2;
    //排序方式
    string order = 3;
    //分页开始处
    uint32 offset = 4;
    //返回条数
    uint32 limit = 5;
    //模糊查询方式
    string search_fields = 6;
    //模糊查询条件
    string search = 7;
    repeated uint64 category_ids = 8;
    string lan = 9;
    // 排序条件
    string order_by = 10;
    // asc or desc，暂时未使用
    string sort = 11;
}
message TransferProductUnit {
    //调拨单位ID
    uint64 id = 1;
    //调拨数量
    double quantity = 2;
    string name = 3;
    double tax_rate = 4;   // 税率
    double tax_price = 5;  // 含税价格
    double cost_price = 6; // 成本价
    double sales_price = 7; // 零售价
    double unit_rate = 8;
}
message SelectTransferProduct {
    //商品ID
    uint64 id = 1;
    //商品编码
    string product_code = 2;
    //单位分类
    uint64 product_category_id = 3;
    //商品名字
    string product_name = 4;
    repeated TransferProductUnit unit = 5;
    repeated string barcode = 6;
    // 实时库存数量
    double real_inventory_qty = 7;
}
message GetTransferProductByBranchIDResponse {
    repeated SelectTransferProduct rows = 1;
    uint32 total = 2;
    // 库存未异动的商品
    repeated SelectTransferProduct inventory_unchanged_rows = 3;
}
message GetTransferRegionByBranchIDRequest {
    //门店ID
    uint64 store_id = 1;
    string lan = 2;
}
message RegionBranch {
    //门店地址
    string address = 1;
    //门店ID
    uint64 id = 2;
    //门店名字
    string name = 3;
    string code = 4;
}
message GetTransferRegionByBranchIDResponse {
    repeated RegionBranch rows = 1;
}
message GetTransferProductByTransferIDRequest {
    //是否包含总数
    bool include_total = 1;
    //排序方式
    string order = 2;
    //分页开始处
    uint32 offset = 3;
    //返回条数
    uint32 limit = 4;
    uint64 transfer_id = 5;
    string lan = 6;
}
message TransferProduct {
    //调拨商品条目ID
    uint64 id = 1;
    //商品ID
    uint64 product_id = 2;
    //调拨单ID
    uint64 transfer_id = 3;
    //调拨单位ID
    uint64 unit_id = 4;
    //调拨单位名字
    string unit_name = 5;
    //调拨单位分类
    string unit_spec = 6;
    //核算数量
    double accounting_quantity = 7;
    //核算单位ID
    uint64 accounting_unit_id = 8;
    //核算单位名字
    string accounting_unit_name = 9;
    //核算单位分类
    string accounting_unit_spec = 10;
    //确认数量
    double accounting_received_quantity = 11;
    //描述
    string description = 12;
    //是否确认
    bool is_confirmed = 13;
    //条目数量
    uint32 item_number = 14;
    //物料编码
    string material_number = 15;
    //商品编码
    string product_code = 16;
    //商品名字
    string product_name = 17;
    //数量
    double quantity = 18;
    //接收门店
    uint64 receiving_store = 19;
    //调出门店
    uint64 shipping_store = 20;
    //创建者
    uint64 created_by = 21;
    //更新者
    uint64 updated_by = 22;
    //调拨单日期
    google.protobuf.Timestamp transfer_date = 23;
    //创建时间
    google.protobuf.Timestamp created_at = 24;
    //更新时间
    google.protobuf.Timestamp updated_at = 25;
    string extends = 26;
    uint64 partner_id = 27;
    uint64 user_id = 28;
    //确认收货数量
    double confirmed_received_quantity = 29;
    string created_name = 30;
    string updated_name = 31;
    double tax_rate = 35;
    double tax_price = 36;
    double cost_price = 37;
    double amount = 38;
    double sales_price = 39;
    double sales_amount = 40;
    double unit_rate = 41;
}
message GetTransferProductByTransferIDResponse {
    repeated TransferProduct rows = 1;
    uint32 total = 2;
}
message PostTransferProduct {
    //商品ID
    uint64 product_id = 1;
    //调拨单位ID
    uint64 unit_id = 2;
    //调拨数量
    double quantity = 3;
    double tax_rate = 4;   //税率
    double tax_price = 5;  //含税价格
    double cost_price = 6; //成本价
    double amount = 7;     //金额=含税价格*数量
    double sales_price = 8; //零售价
    double sales_amount = 9;//零售金额=零售价格*数量
}
message CreateTransferRequest {
    //请求ID
    uint64 request_id = 1;
    //调出门店
    uint64 shipping_store = 2;
    //接收门店
    uint64 receiving_store = 3;
    repeated PostTransferProduct products = 4;
    //备注
    string remark = 5;
    //调出时间
    google.protobuf.Timestamp shipping_date = 6;
    //调拨单日期
    google.protobuf.Timestamp transfer_date = 7;
    // 录入方式，区分门店/仓库/加工中心STORE/WAREHOUSE/MACHINING_CENTER
    string branch_type = 8;
    string attachments = 9;
    // 调拨类型(自动AUTO/手动MANUAL)
    string type = 10;
    // 区分内部`INTERNAL`/外部`EXTERNAL`调拨
    string sub_type = 11;
    // 接收仓位
    uint64 receiving_position = 12;
    // 调出仓位
    uint64 shipping_position = 13;
    // 接收仓位名称
    string receiving_position_name = 14;
    // 调出仓位名称
    string shipping_position_name = 15;
    // 子账户类型，如果开启了多仓位值为"position"没配置多仓位为空
    string sub_account_type = 16;
}

message CreateInnerTransferRequest{
    //请求ID
    uint64 request_id = 1;
    //调出门店（内部调拨只传调出门店）
    uint64 shipping_store = 2;
    //接收门店
    uint64 receiving_store = 3;
    repeated PostTransferProduct products = 4;
    //备注
    string remark = 5;
    //调出时间
    google.protobuf.Timestamp shipping_date = 6;
    //调拨单日期
    google.protobuf.Timestamp transfer_date = 7;
    // 录入方式，区分门店/仓库/加工中心STORE/WAREHOUSE/MACHINING_CENTER
    string branch_type = 8;
    string attachments = 9;
    // 调拨类型(自动AUTO/手动MANUAL)
    string type = 10;
    // 区分内部`INTERNAL`/外部`EXTERNAL`调拨
    string sub_type = 11;
    // 接收仓位id
    uint64 receiving_position = 12;
    // 调出仓位id
    uint64 shipping_position = 13;
    // 接收仓位名称
    string receiving_position_name = 14;
    // 调出仓位名称
    string shipping_position_name = 15;
    // 子账户类型，如果开启了多仓位值为"position"没配置多仓位为空
    string sub_account_type = 16;
}
message CreateInnerTransferResponse{
    uint64 transfer_id = 1;
}

message UpdateTransferRequest {
    uint64 transfer_id = 1;
    repeated PostTransferProduct products = 2;
    //备注
    string remark = 3;
    //调拨单日期
    google.protobuf.Timestamp transfer_date = 4;
    // 录入方式，区分门店还是仓库STORE/WAREHOUSE
    string branch_type = 5;
    string attachments = 6;
    // 调拨类型(自动AUTO/手动MANUAL)
    string type = 10;
    // 区分内部`INTERNAL`/外部`EXTERNAL`调拨
    string sub_type = 11;
}
message UpdateTransferResponse {
    bool result = 1;
}

message ConfirmPostTransferProduct {
    //商品ID
    uint64 product_id = 1;
    //调拨单位ID
    uint64 unit_id = 2;
    //收货数量
    double confirmed_received_quantity = 3;
}
message ConfirmTransferRequest {
    //调拨单ID
    uint64 transfer_id = 1;
    uint64 receiver = 2;
    uint64 receiving_store = 3;
    repeated ConfirmPostTransferProduct products = 4;
    // 录入方式，区分门店还是仓库STORE/WAREHOUSE
    string branch_type = 5;
}
message DeleteTransferProductRequest {
    //调拨商品条目ID
    uint64 transfer_product_id = 1;

}
message DeleteTransferProductResponse {
    bool result = 1;
}
message DeleteTransferRequest {
    //调拨单ID
    uint64 transfer_id = 1;
}
message DeleteTransferResponse {
    bool result = 1;
}
message SubmitTransferRequest {
    //调拨单ID
    uint64 transfer_id = 1;
    uint64 receiver = 2;
    uint64 receiving_store = 3;
    repeated PostTransferProduct products = 4;
    // 录入方式，区分门店还是仓库STORE/WAREHOUSE
    string branch_type = 5;
}
message SubmitTransferReceivingRequest {
    //调拨单ID
    uint64 transfer_id = 1;
    uint64 receiver = 2;
    uint64 receiving_store = 3;
    repeated PostTransferProduct products = 4;
}

message CancelTransferRequest {
    //调拨单ID
    uint64 transfer_id = 1;
}

//报表
message GetTransferCollectRequest {
    //门店id列表
    repeated uint64 st_ids = 1;
    //掉入调出门店id，不用
    repeated uint64 store_ids = 2;
    //商品类别列表
    repeated uint64 category_ids = 3;
    string product_name = 4;
    google.protobuf.Timestamp start_date = 5;
    google.protobuf.Timestamp end_date = 6;
    uint32 limit = 7;
    uint32 offset = 8;
    bool include_total = 9;
    //true时返回收货门店，false返回调出门店
    bool is_in = 10;
    // 排序(默认asc)
    string order = 11;
    string sort = 12;
    string code = 13;
    string jde_code = 14;
    bool is_wms_store = 15;
    // 区分门店还是仓库
    string branch_type = 16;
    string lan = 17;
    // 调拨类型(自动AUTO/手动MANUAL)
    string type = 18;
    // 区分内部`INTERNAL`/外部`EXTERNAL`调拨(必传)
    string sub_type = 19;
    // 是否跨公司调拨: "0"否/"1"是/不传全部
    string cross_company = 20;
}
message TransferCollect {
    double accounting_quantity = 1;
    uint64 accounting_unit_id = 2;
    string accounting_unit_name = 3;
    string category_code = 4;
    uint64 category_id = 5;
    string category_name = 6;
    //商品标签层级
    google.protobuf.Struct category_parent = 11;
    string product_code = 12;
    uint64 product_id = 13;
    string product_name = 14;
    double product_price = 15;
    uint64 receiving_store = 16;
    string receiving_store_code = 17;
    string receiving_store_us_id = 18;
    string receiving_store_name = 19;
    double quantity = 20;
    uint64 shipping_store = 21;
    string shipping_store_code = 22;
    string shipping_store_name = 23;
    string shipping_store_us_id = 24;
    uint64 unit_id = 25;
    string unit_name = 26;
    string product_spec = 27;
    double price = 28;
    uint64 receiving_position = 29;
    string receiving_position_code = 30;
    string receiving_position_name = 31;
    uint64 shipping_position = 32;
    string shipping_position_code = 33;
    string shipping_position_name = 34;
    // 是否跨公司调拨
    string cross_company = 35;
}
message TransferCollectTotal {
    uint64 count = 1;
    double sum_accounting_quantity = 2;
    double sum_quantity = 3;
}
message GetTransferCollectResponse {
    repeated TransferCollect rows = 1;
    TransferCollectTotal total = 2;
}
message GetTransferCollectDetailedRequest {
    //门店id列表
    repeated uint64 st_ids = 1;
    //掉入调出门店id，不用
    repeated uint64 store_ids = 2;
    //商品类别列表
    repeated uint64 category_ids = 3;
    string product_name = 4;
    google.protobuf.Timestamp start_date = 5;
    google.protobuf.Timestamp end_date = 6;
    uint32 limit = 7;
    uint32 offset = 8;
    bool include_total = 9;
    //true时返回收货门店，false返回调出门店
    bool is_in = 10;
    string order = 11;
    string sort = 12;
    string code = 13;
    string jde_code = 14;
    bool is_wms_store = 15;
    // 区分门店还是仓库
    string branch_type = 16;
    string lan = 17;
    // 调拨类型(自动AUTO/手动MANUAL)
    string type = 18;
    // 区分内部`INTERNAL`/外部`EXTERNAL`调拨(必传)
    string sub_type = 19;
    // 是否跨公司调拨: "0"否/"1"是/不传全部
    string cross_company = 20;
}
message TransferCollectDetailed {
    double accounting_quantity = 1;
    uint64 accounting_unit_id = 2;
    string accounting_unit_name = 3;
    string category_code = 4;
    uint64 category_id = 5;
    string category_name = 6;
    uint64 id = 7;
    string product_code = 8;
    uint64 product_id = 9;
    string product_name = 10;
    string product_spec = 11;
    double quantity = 12;
    uint64 receiving_store = 13;
    string receiving_store_code = 14;
    string receiving_store_name = 15;
    uint64 shipping_store = 16;
    string shipping_store_code = 17;
    string shipping_store_name = 18;
    string status = 19;
    string transfer_code = 20;
    google.protobuf.Timestamp transfer_date = 21;
    uint64 transfer_id = 22;
    uint64 unit_id = 23;
    string unit_name = 24;
    string shipping_store_us_id=25;
    string receiving_store_us_id=26;
    double price=27;
    string jde_code = 33;
    string code = 34;
    uint64 receiving_position = 40;
    string receiving_position_code = 41;
    string receiving_position_name = 42;
    uint64 shipping_position = 43;
    string shipping_position_code = 44;
    string shipping_position_name = 45;
    // 是否跨公司调拨
    string cross_company = 35;
}
message GetTransferCollectDetailedResponse {
    repeated TransferCollectDetailed rows = 1;
    TransferCollectTotal total = 2;
}


message ConfirmTransferByCodeRequest {
    string transfer_code = 1;
}

message ConfirmTransferByCodeResponse {
    uint64 batch_id = 1;
    string status = 2;
    string msg = 3;
    string code = 4;
    string failed_code = 5;

}