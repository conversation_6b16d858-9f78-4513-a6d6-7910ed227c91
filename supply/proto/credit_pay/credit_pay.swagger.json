{"swagger": "2.0", "info": {"title": "credit_pay/credit_pay.proto", "version": "version not set"}, "tags": [{"name": "CreditPay"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/credit-pay/adjust-pay-account": {"post": {"summary": "同步信用付账户额度, 提供给第三方, 强制对准额度. 只支持信用付", "operationId": "CreditPay_AdjustPayAccount", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payAdjustPayAccountRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/change-pay-relation-status": {"post": {"summary": "关闭/开启支付关系", "operationId": "CreditPay_ChangePayRelationStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payChangePayRelationStatusRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/close-credit-pay": {"post": {"summary": "关闭/开启信用付", "operationId": "CreditPay_CloseCreditPay", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payCloseCreditPayRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/create-credit-pay": {"post": {"summary": "创建信用付", "operationId": "CreditPay_CreateCreditPay", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payCreateCreditPayRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/create-pay-relation": {"post": {"summary": "创建支付关系", "operationId": "CreditPay_CreatePayRelation", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payCreatePayRelationRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/delete-pay-relation": {"post": {"summary": "移除支付关系", "operationId": "CreditPay_DeletePayRelation", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payDeletePayRelationRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/force-pay": {"post": {"summary": "强制使用信用付支付 - 仅提供后台使用", "operationId": "CreditPay_ForcePay", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payForcePayRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/get-channels": {"post": {"summary": "获取支付方式列表", "operationId": "CreditPay_GetChannels", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payGetChannelsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payGetChannelsRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/get-credit-pay-detail": {"get": {"summary": "信用付账户详情", "operationId": "CreditPay_GetCreditPayDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payGetCreditPayDetailResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "credit_pay_id", "description": "信用付id", "in": "query", "required": false, "type": "string", "format": "uint64"}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/get-credit-pay-list": {"post": {"summary": "信用付账户列表", "operationId": "CreditPay_GetCreditPayList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payGetCreditPayListResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payGetCreditPayListRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/get-credit-pay-list-mini-wx": {"post": {"summary": "小程序获取信用付账户", "operationId": "CreditPay_GetCreditPayListMiniWX", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payGetCreditPayListMiniWXResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payGetCreditPayListMiniWXRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/get-log-list": {"post": {"summary": "获取日志数据", "operationId": "CreditPay_GetLogList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payGetLogListResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payGetLogListRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/get-pay-params": {"post": {"summary": "获取支付参数", "operationId": "CreditPay_GetPayParams", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payGetPayParamsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payGetPayParamsRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/get-pay-relation-detail": {"post": {"summary": "获取支付关系详情", "operationId": "CreditPay_GetPayRelationDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payGetPayRelationDetailResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payGetPayRelationDetailRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/get-pay-relation-list": {"post": {"summary": "获取支付关系列表", "operationId": "CreditPay_GetPayRelationList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payGetPayRelationListResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payGetPayRelationListRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/get-transaction-log-list": {"post": {"summary": "获取交易流水", "operationId": "CreditPay_GetTransactionLogList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payGetTransactionLogListResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payGetTransactionLogListRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/get-vouchers": {"post": {"summary": "获取代金券列表", "operationId": "CreditPay_GetVouchers", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payGetVouchersResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payGetVouchersRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/pay": {"post": {"summary": "信用付支付", "operationId": "CreditPay_Pay", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payPayRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/pay-notify": {"post": {"summary": "支付回调 - 微信等线上支付", "operationId": "CreditPay_PayNotify", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payPayNotifyRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/query-pay-status": {"post": {"summary": "支付结果查询", "operationId": "CreditPay_QueryPayStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payQueryPayStatusResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payQueryPayStatusRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/refresh-credit-pay": {"post": {"summary": "刷新信用额度", "operationId": "CreditPay_RefreshCreditPay", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payRefreshCreditPayRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/refund": {"post": {"summary": "退款 - 不支持原路退, 代金券退代金券, 其他退信用付", "operationId": "CreditPay_Refund", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payRefundRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/update-credit-pay": {"post": {"summary": "修改信用付", "operationId": "CreditPay_UpdateCreditPay", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payUpdateCreditPayRequest"}}], "tags": ["CreditPay"]}}, "/api/v1/credit-pay/voucher-pay": {"post": {"summary": "代金券支付", "operationId": "CreditPay_VoucherPay", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/credit_payCommonResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/credit_payVoucherPayRequest"}}], "tags": ["CreditPay"]}}}, "definitions": {"credit_payAdjustPayAccountDetail": {"type": "object", "properties": {"branch_id": {"type": "string", "format": "uint64", "title": "被授信方id"}, "company_id": {"type": "string", "format": "uint64", "title": "公司id"}, "limit": {"type": "string", "title": "额度"}, "request_id": {"type": "string", "title": "唯一id, 重复则不处理"}}}, "credit_payAdjustPayAccountRequest": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/credit_payAdjustPayAccountDetail"}, "title": "账户明细"}, "credit_way_type": {"$ref": "#/definitions/credit_payCreditWayType", "title": "授信方式"}}, "title": "同步信用付账户额度, 提供给第三方, 强制对准额度. 只支持信用付"}, "credit_payBindingItem": {"type": "object", "properties": {"channel_code": {"type": "string", "title": "（必传）渠道代码"}, "channel_name": {"type": "string", "title": "（必传）渠道名称"}, "business_code": {"type": "string", "title": "（必传）渠道业务代码，\",\"分割"}, "channel_type": {"type": "string", "title": "（必传）渠道性质，KA 品牌商；ISV 服务商"}, "channel_category": {"type": "string", "title": "（必传）渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送"}, "channel_sub_category": {"type": "string", "title": "（可选）渠道二级分类，AM 小程序&会员；COUPON 卡券"}, "channel_labels": {"type": "string", "title": "（可选）渠道标签，逗号分割"}, "enabled": {"type": "string", "title": "（必传）是否启用"}}, "title": "渠道绑定关系信息"}, "credit_payBindingListSection": {"type": "object", "properties": {"binding_item": {"type": "array", "items": {"$ref": "#/definitions/credit_payBindingItem"}, "title": "（必传）渠道绑定关系信息"}}, "title": "渠道绑定关系查询结果"}, "credit_payBranchCredit": {"type": "object", "properties": {"branch_id": {"type": "string", "format": "uint64", "title": "被授信方id, 必传"}, "limit": {"type": "string", "format": "int64", "title": "授信额度, 单位分, 必传"}, "is_over": {"type": "boolean", "title": "是否允许超额支付, 必传"}, "max_over_limit": {"type": "string", "format": "int64", "title": "超额支付额度上限, 单位分, is_over为true时必传"}, "remark": {"type": "string", "title": "备注, 必传"}, "status": {"$ref": "#/definitions/credit_payCreditStatus", "title": "状态"}}}, "credit_payChangePayRelationStatusRequest": {"type": "object", "properties": {"relation_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "信用付id, 必传"}, "status": {"$ref": "#/definitions/credit_payPayRelationStatus", "title": "状态, 必传"}}, "title": "关闭/开启信用付"}, "credit_payCloseCreditPayRequest": {"type": "object", "properties": {"credit_pay_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "信用付id, 必传"}, "status": {"$ref": "#/definitions/credit_payCreditStatus", "title": "状态, 必传"}}, "title": "关闭/开启信用付"}, "credit_payCommonResponse": {"type": "object", "properties": {"code": {"type": "integer", "format": "int64"}, "message": {"type": "string"}}}, "credit_payCreateCreditPayRequest": {"type": "object", "properties": {"company_id": {"type": "string", "format": "uint64", "title": "授信主体公司id, 必传"}, "credit_way": {"$ref": "#/definitions/credit_payCreditWayType", "title": "授信方式枚举, 必传"}, "credits": {"type": "array", "items": {"$ref": "#/definitions/credit_payBranchCredit"}, "title": "门店信用额度"}}, "title": "创建信用付"}, "credit_payCreatePayRelationRequest": {"type": "object", "properties": {"CompanyId": {"type": "string", "format": "uint64", "title": "支付公司id"}, "status": {"$ref": "#/definitions/credit_payPayRelationStatus", "title": "支付关系状态"}, "store_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "门店id"}}, "title": "创建支付关系"}, "credit_payCreditPayList": {"type": "object", "properties": {"credit_pay_id": {"type": "string", "format": "uint64", "title": "信用付id"}, "branch_id": {"type": "string", "format": "uint64", "title": "被授信方组织id, 目前是门店/加盟商"}, "branch_code": {"type": "string", "title": "被授信方组织code"}, "branch_name": {"type": "string", "title": "被授信方名称"}, "company_id": {"type": "string", "format": "uint64", "title": "授信主体公司id"}, "company_name": {"type": "string", "title": "授信主体名称"}, "limit": {"type": "string", "format": "int64", "title": "可用额度, 单位分"}, "is_over": {"type": "boolean", "title": "是否允许超额支付"}, "max_over_limit": {"type": "string", "format": "int64", "title": "超额支付额度, 单位分"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人id"}, "created_name": {"type": "string", "title": "创建人"}, "updated_name": {"type": "string", "title": "更新人;"}, "status": {"$ref": "#/definitions/credit_payCreditStatus", "title": "状态"}, "created": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated": {"type": "string", "format": "date-time", "title": "更新时间"}}, "title": "信用付列表数据"}, "credit_payCreditPayMiniWXDetail": {"type": "object", "properties": {"branch_code": {"type": "string", "title": "被授信方code"}, "branch_name": {"type": "string", "title": "被授信方名称"}, "boh_account_amount": {"type": "string", "title": "boh账户额度"}, "last_sync_time": {"type": "string", "format": "date-time", "title": "最后更新时间"}, "status": {"$ref": "#/definitions/credit_payCreditStatus", "title": "信用付状态"}, "extend_accounts": {"type": "array", "items": {"$ref": "#/definitions/credit_payThirdPartyCreditPayDetail"}, "title": "第三方账户信息"}}, "title": "小程序信用付账户明细"}, "credit_payCreditStatus": {"type": "string", "enum": ["CreditStatusInit", "CreditStatusOpen", "CreditStatusClosed"], "default": "CreditStatusInit", "description": "- CreditStatusInit: 初始值, 无意义\n - CreditStatusOpen: 开启\n - CreditStatusClosed: 关闭", "title": "信用付状态枚举"}, "credit_payCreditWayType": {"type": "string", "enum": ["CreditWayInit", "CreditWayStore", "CreditWayFranchisee"], "default": "CreditWayInit", "description": "- CreditWayInit: 初始值, 无意义\n - CreditWayStore: 门店\n - CreditWayFranchisee: 加盟商", "title": "授信方式枚举"}, "credit_payDeletePayRelationRequest": {"type": "object", "properties": {"relation_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "支付关系id"}}, "title": "移除支付关系"}, "credit_payForcePayRequest": {"type": "object", "properties": {"order_no": {"type": "string", "title": "业务系统订单号"}}, "title": "强制使用信用付支付"}, "credit_payGetChannelsRequest": {"type": "object", "properties": {"action": {"type": "string", "title": "（必传）业务操作"}, "store_id": {"type": "string", "format": "uint64", "title": "（必传）门店id"}, "platform": {"$ref": "#/definitions/credit_payPlatformType", "title": "必传）平台code"}, "order_type_id": {"type": "string", "format": "uint64", "title": "订货类型"}, "list_binding_section": {"$ref": "#/definitions/credit_payListBindingSection", "title": "（可选）渠道绑定关系查询请求对象"}}, "title": "获取支付列表"}, "credit_payGetChannelsResponse": {"type": "object", "properties": {"error_code": {"type": "string", "title": "（必传）异常编码"}, "error_message": {"type": "string", "title": "（必传）异常信息"}, "binding_list_section": {"$ref": "#/definitions/credit_payBindingListSection", "title": "（可选）渠道绑定关系信息"}}, "title": "渠道签约授权响应信息"}, "credit_payGetCreditPayDetailResponse": {"type": "object", "properties": {"credit_pay_id": {"type": "string", "format": "uint64", "title": "信用付id"}, "branch_id": {"type": "string", "format": "uint64", "title": "被授信方组织id, 目前是门店/加盟商"}, "branch_code": {"type": "string", "title": "被授信方组织code"}, "branch_name": {"type": "string", "title": "被授信方名称"}, "company_id": {"type": "string", "format": "uint64", "title": "授信主体公司id"}, "company_code": {"type": "string", "title": "授信主体公司code"}, "company_name": {"type": "string", "title": "授信主体名称"}, "limit": {"type": "string", "format": "int64", "title": "当前额度, 单位分"}, "is_over": {"type": "boolean", "title": "是否允许超额支付"}, "max_over_limit": {"type": "string", "format": "int64", "title": "超额支付额度, 单位分"}, "remark": {"type": "string", "title": "备注"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人"}, "status": {"$ref": "#/definitions/credit_payCreditStatus", "title": "状态"}, "credit_way": {"$ref": "#/definitions/credit_payCreditWayType", "title": "授信方式枚举"}, "created": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated": {"type": "string", "format": "date-time", "title": "更新时间"}}, "title": "信用付详情"}, "credit_payGetCreditPayListMiniWXRequest": {"type": "object", "properties": {"branch_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "(可选) 被授信方id"}, "credit_way": {"$ref": "#/definitions/credit_payCreditWayType", "title": "(必传) 授信方式"}, "status": {"$ref": "#/definitions/credit_payCreditStatus", "title": "(可选) 状态"}, "page": {"$ref": "#/definitions/credit_payPage", "title": "分页"}}, "title": "小程序获取信用付账户"}, "credit_payGetCreditPayListMiniWXResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/credit_payCreditPayMiniWXDetail"}, "title": "信用付账户列表"}, "total": {"type": "string", "format": "uint64", "title": "总页数"}}, "title": "小程序信用付账户列表"}, "credit_payGetCreditPayListRequest": {"type": "object", "properties": {"credit_way": {"$ref": "#/definitions/credit_payCreditWayType", "title": "授信方式, 必传"}, "branch_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "被授信方id, 可选"}, "company_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "授信主体公司id, 可选"}, "status": {"$ref": "#/definitions/credit_payCreditStatus", "title": "状态, 可选"}, "page": {"$ref": "#/definitions/credit_payPage", "title": "分页"}}, "title": "获取信用付列表"}, "credit_payGetCreditPayListResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/credit_payCreditPayList"}, "title": "列表数据"}, "total": {"type": "string", "format": "uint64", "title": "总页数"}}, "title": "信用付列表响应"}, "credit_payGetLogListRequest": {"type": "object", "properties": {"bus_name": {"type": "string", "title": "业务名称, 必传\ncredit_pay 信用付\npay_relation 支付关系"}, "data_id": {"type": "string", "format": "uint64", "title": "数据id, 必传"}, "page": {"$ref": "#/definitions/credit_payPage", "title": "分页"}}, "title": "获取日志列表"}, "credit_payGetLogListResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/credit_payLogList"}, "title": "列表数据"}, "total": {"type": "string", "format": "uint64", "title": "总页数"}}, "title": "日志列表"}, "credit_payGetPayParamsRequest": {"type": "object", "properties": {"order_no": {"type": "string", "title": "订单号"}, "channel": {"type": "string", "title": "支付渠道, 全渠道定义"}}, "title": "获取支付参数请求"}, "credit_payGetPayParamsResponse": {"type": "object", "properties": {"error_code": {"type": "string", "title": "错误码"}, "error_message": {"type": "string", "title": "错误信息"}, "ticket_id": {"type": "string", "title": "支付ticket_id"}, "status": {"type": "string", "title": "支付状态 WAITING， PAYING   等"}, "prepay_id": {"type": "string", "title": "预下单第三方订单号（微信、支付宝）"}, "prepay_sign": {"type": "string", "title": "预支付交易客户端唤起支付签名"}, "tp_transaction_id": {"type": "string", "title": "（可选）第三方流水号"}, "pack_str": {"type": "object", "additionalProperties": {"type": "string"}, "title": "（可选）预支付交易扩展字段"}, "sign_type": {"type": "string", "title": "签名算法"}}, "title": "支付参数响应"}, "credit_payGetPayRelationDetailRequest": {"type": "object", "properties": {"relation_id": {"type": "string", "format": "uint64", "title": "支付关系id"}}, "title": "获取支付关系详情"}, "credit_payGetPayRelationDetailResponse": {"type": "object", "properties": {"relation_id": {"type": "string", "format": "uint64", "title": "支付关系id"}, "store_id": {"type": "string", "format": "uint64", "title": "门店id"}, "store_code": {"type": "string", "title": "门店code"}, "store_name": {"type": "string", "title": "门店名称"}, "company_id": {"type": "string", "format": "uint64", "title": "授信主体公司id"}, "company_name": {"type": "string", "title": "授信主体名称"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人"}, "updated_name": {"type": "string", "title": "更新人名称"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人"}, "created_name": {"type": "string", "title": "创建人名称"}, "status": {"$ref": "#/definitions/credit_payPayRelationStatus", "title": "状态"}, "created": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated": {"type": "string", "format": "date-time", "title": "更新时间"}}, "title": "支付关系列表"}, "credit_payGetPayRelationListRequest": {"type": "object", "properties": {"geo_region_id": {"type": "string", "format": "uint64", "title": "地理区域id"}, "branch_region_id": {"type": "string", "format": "uint64", "title": "管理区域id"}, "store_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "门店id"}, "company_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "公司id"}, "status": {"$ref": "#/definitions/credit_payPayRelationStatus", "title": "支付关系状态"}, "page": {"$ref": "#/definitions/credit_payPage", "title": "分页"}}, "title": "获取支付关系列表"}, "credit_payGetPayRelationListResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/credit_payGetPayRelationDetailResponse"}, "title": "列表数据"}, "total": {"type": "string", "format": "uint64", "title": "总页数"}}, "title": "支付关系列表"}, "credit_payGetTransactionLogListRequest": {"type": "object", "properties": {"account_id": {"type": "string", "format": "uint64", "title": "账户id"}, "account_type": {"type": "string", "title": "账户类型:\n   BohCreditPay  BOH信用付\n   WXMPay        微信小程序\n   BohVoucherPay 代金券"}, "status": {"type": "string", "title": "交易流水状态:\n   Waiting 待处理\n   Success 成功\n   Failed  失败"}, "page": {"$ref": "#/definitions/credit_payPage", "title": "分页"}}, "title": "获取交易流水"}, "credit_payGetTransactionLogListResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/credit_payTransactionLogDetail"}, "title": "交易流水"}, "total": {"type": "string", "format": "uint64", "title": "总页数"}}, "title": "交易流水列表"}, "credit_payGetVouchersRequest": {"type": "object", "properties": {"store_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "门店id"}, "order_type_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "订货类型id"}, "voucher_status": {"type": "array", "items": {"$ref": "#/definitions/credit_payVoucherStatus"}, "title": "代金券状态"}, "voucher_name": {"type": "string", "title": "代金券名称"}, "voucher_code": {"type": "string", "title": "代金券code"}, "page": {"$ref": "#/definitions/credit_payPage", "title": "分页"}}, "title": "获取代金券列表"}, "credit_payGetVouchersResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/credit_payVouchersDetail"}, "title": "代金券数据"}, "total": {"type": "string", "format": "uint64", "title": "总页数"}}, "title": "代金券列表"}, "credit_payListBindingSection": {"type": "object", "properties": {"channel_category": {"type": "string", "title": "（可选）渠道分类，PAYMENT 支付；OPERATION 运营；TAKEOUT 外卖；DELIVERY 配送，多个渠道代码使用\",\"分隔"}, "enabled": {"type": "string", "title": "（可选）渠道是否启用"}, "channel_labels": {"type": "string", "title": "（可选）渠道标签，多个以\",\"分割"}, "business_code": {"type": "string", "title": "（可选）渠道业务代码，多个以\",\"分割"}}, "title": "渠道绑定关系查询对象"}, "credit_payLogInfo": {"type": "object", "properties": {"field_name": {"type": "string", "title": "字段名称"}, "old_val": {"type": "string", "title": "更新前的值"}, "new_val": {"type": "string", "title": "更新后的值"}, "is_change": {"type": "boolean", "title": "是否变更"}}, "title": "日志详情"}, "credit_payLogList": {"type": "object", "properties": {"log_id": {"type": "string", "format": "uint64", "title": "日志id"}, "log_data": {"type": "array", "items": {"$ref": "#/definitions/credit_payLogInfo"}, "title": "日志详情\n字段含义详见 https://gitlab.hexcloud.cn/saas-boh/credit-pay/blob/master/doc/saas_boh_credit_pay.sql"}, "operation_type": {"$ref": "#/definitions/credit_payLogOperationType", "title": "操作类型"}, "updated_by": {"type": "string", "format": "uint64", "title": "操作人id"}, "updated_name": {"type": "string", "title": "操作人名称"}, "updated": {"type": "string", "format": "date-time", "title": "操作时间"}}, "title": "日志列表数据"}, "credit_payLogOperationType": {"type": "string", "enum": ["LogOperationTypeInit", "LogOperationTypeCreate", "LogOperationTypeUpdate"], "default": "LogOperationTypeInit", "description": "- LogOperationTypeInit: 初始值, 无意义\n - LogOperationTypeCreate: 创建\n - LogOperationTypeUpdate: 更新", "title": "日志业务类型枚举"}, "credit_payOrderTypeInfo": {"type": "object", "properties": {"order_type_id": {"type": "string", "title": "代金券id"}, "order_type_code": {"type": "string", "title": "代金券code"}, "order_type_name": {"type": "string", "title": "代金券名称"}}, "title": "代金券支付"}, "credit_payPage": {"type": "object", "properties": {"order": {"type": "string"}, "sort": {"type": "string"}, "limit": {"type": "integer", "format": "int32"}, "offset": {"type": "integer", "format": "int32"}}}, "credit_payPayNotifyRequest": {"type": "object", "properties": {"order_no": {"type": "string", "title": "业务系统订单号"}, "tp_transaction_no": {"type": "string", "title": "第三方支付订单号，比如 微信订单号、支付宝订单号"}, "pay_status": {"type": "string", "title": "支付状态   PAID：支付成功，REFUNDED：退款成功，REF_FAIL:退款失败"}, "message": {"type": "string", "title": "附加信息，如果有失败的信息，可通过该字段带回。"}, "pay_channel": {"type": "string", "title": "支付渠道"}, "pay_user_id": {"type": "string", "title": "支付人id"}, "pay_user_name": {"type": "string", "title": "支付人名称"}}, "title": "支付回调"}, "credit_payPayRelationStatus": {"type": "string", "enum": ["PayRelationStatusInit", "PayRelationStatusOpen", "PayRelationStatusClosed"], "default": "PayRelationStatusInit", "description": "- PayRelationStatusInit: 初始值, 无意义\n - PayRelationStatusOpen: 开启\n - PayRelationStatusClosed: 关闭", "title": "支付关系状态枚举"}, "credit_payPayRequest": {"type": "object", "properties": {"order_no": {"type": "string", "title": "业务系统订单号"}}, "title": "信用付支付"}, "credit_payPingResponse": {"type": "object", "properties": {"pong": {"type": "string"}}}, "credit_payPlatformType": {"type": "string", "enum": ["PlatformTypeInit", "WXMiniProgram", "DingDingProgram"], "default": "PlatformTypeInit", "description": "- PlatformTypeInit: 初始值, 无意义\n - WXMiniProgram: 微信小程序\n - DingDingProgram: 钉钉", "title": "平台类型枚举"}, "credit_payQueryPayStatusRequest": {"type": "object", "properties": {"channel": {"type": "string", "title": "（必传）渠道编码，CreditPay BOH信用付 WXMPay 微信小程序 Cash 现金"}, "transaction_id": {"type": "string", "title": "（必传）支付时传给第三方接口的唯一标识id"}, "order_no": {"type": "string", "title": "必传）订单号"}}, "title": "支付结果查询"}, "credit_payQueryPayStatusResponse": {"type": "object", "properties": {"error_code": {"type": "string", "title": "异常编码 - 0为成功"}, "error_message": {"type": "string", "title": "异常描述 - 成功时为success"}, "transaction_state": {"type": "string", "title": "（必传）交易状态"}, "real_amount": {"type": "number", "format": "double", "title": "（必传）实际发生金额"}, "tp_transaction_id": {"type": "string", "title": "（必传）支付流水号"}, "pay_channel": {"type": "string", "title": "（必传）支付渠道"}}, "title": "支付结果"}, "credit_payRefreshCreditPayRequest": {"type": "object", "properties": {"store_code": {"type": "string", "title": "门店编码"}}, "title": "刷新信用额度"}, "credit_payRefundRequest": {"type": "object", "properties": {"refund_id": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "退款单id"}}, "title": "发起退款"}, "credit_payThirdPartyCreditPayDetail": {"type": "object", "properties": {"account_name": {"type": "string", "title": "第三方账户名称"}, "amount": {"type": "string", "title": "第三方账户余额"}}}, "credit_payTransactionLogDetail": {"type": "object", "properties": {"transaction_account": {"type": "string", "title": "变动账户id"}, "order_no": {"type": "string", "title": "单号id"}, "order_code": {"type": "string", "title": "单号编号"}, "sum_price": {"type": "string", "title": "单据变动金额"}, "transaction_amount": {"type": "string", "title": "实际变动金额"}, "remain_amount": {"type": "string", "title": "当前剩余额度"}, "transaction_time": {"type": "string", "format": "date-time", "title": "交易时间"}, "status": {"type": "string", "title": "交易流水状态:\n   Waiting 待处理\n   Success 成功\n   Failed  失败"}, "transaction_type": {"type": "string", "title": "交易类型:\n   Pay     支付\n   Refund  退款\n   Deposit 充值"}}, "title": "交易流水详情"}, "credit_payUpdateCreditPayRequest": {"type": "object", "properties": {"credit_pay_id": {"type": "string", "format": "uint64", "title": "信用付id, 必传"}, "increase": {"type": "string", "format": "uint64", "title": "增加授信额度, 单位分, 如果要变更额度, increase/reduce 必须二选一, 否则都为0"}, "reduce": {"type": "string", "format": "uint64", "title": "减少授信额度, 单位分, 如果要变更额度, increase/reduce 必须二选一, 否则都为0"}, "is_over": {"type": "boolean", "title": "是否允许超额支付, 必传"}, "max_over_limit": {"type": "string", "format": "int64", "title": "超额支付上限, 必传"}, "remark": {"type": "string", "title": "备注, 可选"}, "status": {"$ref": "#/definitions/credit_payCreditStatus", "title": "状态"}}, "title": "编辑信用付"}, "credit_payVoucherPayRequest": {"type": "object", "properties": {"order_no": {"type": "string", "title": "业务系统订单号"}, "voucher_id": {"type": "string", "title": "代金券id"}}, "title": "代金券支付"}, "credit_payVoucherStatus": {"type": "string", "enum": ["VoucherStatusInit", "NotStart", "UnderWay", "Expired"], "default": "VoucherStatusInit", "description": "- VoucherStatusInit: 初始值, 无意义\n - NotStart: 未开始\n - UnderWay: 活动中\n - Expired: 已过期", "title": "代金券状态枚举"}, "credit_payVouchersDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "代金券id"}, "partner_id": {"type": "string", "format": "uint64", "title": "租户id"}, "store_id": {"type": "string", "format": "uint64", "title": "门店id"}, "voucher_id": {"type": "string", "format": "uint64", "title": "代金券活动id"}, "voucher_code": {"type": "string", "title": "代金券活动code"}, "voucher_name": {"type": "string", "title": "代金券名称"}, "start_date": {"type": "string", "format": "date-time", "title": "开始时间"}, "end_date": {"type": "string", "format": "date-time", "title": "结束时间"}, "order_type": {"type": "array", "items": {"$ref": "#/definitions/credit_payOrderTypeInfo"}, "title": "支持的订货类型"}, "limit": {"type": "string", "title": "额度"}, "remark": {"type": "string", "title": "备注"}, "voucher_status": {"$ref": "#/definitions/credit_payVoucherStatus", "title": "代金券状态"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人"}, "created_name": {"type": "string", "title": "创建人名称"}, "updated_name": {"type": "string", "title": "更新人名称"}, "created": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated": {"type": "string", "format": "date-time", "title": "更新时间"}}, "title": "代金券详情"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}}}