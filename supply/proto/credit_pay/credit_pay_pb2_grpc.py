# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from credit_pay import credit_pay_pb2 as credit__pay_dot_credit__pay__pb2


class CreditPayStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.Ping = channel.unary_unary(
        '/credit_pay.CreditPay/Ping',
        request_serializer=credit__pay_dot_credit__pay__pb2.PingRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.PingResponse.FromString,
        )
    self.CreateCreditPay = channel.unary_unary(
        '/credit_pay.CreditPay/CreateCreditPay',
        request_serializer=credit__pay_dot_credit__pay__pb2.CreateCreditPayRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.CommonResponse.FromString,
        )
    self.UpdateCreditPay = channel.unary_unary(
        '/credit_pay.CreditPay/UpdateCreditPay',
        request_serializer=credit__pay_dot_credit__pay__pb2.UpdateCreditPayRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.CommonResponse.FromString,
        )
    self.CloseCreditPay = channel.unary_unary(
        '/credit_pay.CreditPay/CloseCreditPay',
        request_serializer=credit__pay_dot_credit__pay__pb2.CloseCreditPayRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.CommonResponse.FromString,
        )
    self.GetCreditPayList = channel.unary_unary(
        '/credit_pay.CreditPay/GetCreditPayList',
        request_serializer=credit__pay_dot_credit__pay__pb2.GetCreditPayListRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.GetCreditPayListResponse.FromString,
        )
    self.GetCreditPayDetail = channel.unary_unary(
        '/credit_pay.CreditPay/GetCreditPayDetail',
        request_serializer=credit__pay_dot_credit__pay__pb2.GetCreditPayDetailRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.GetCreditPayDetailResponse.FromString,
        )
    self.CreatePayRelation = channel.unary_unary(
        '/credit_pay.CreditPay/CreatePayRelation',
        request_serializer=credit__pay_dot_credit__pay__pb2.CreatePayRelationRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.CommonResponse.FromString,
        )
    self.GetPayRelationList = channel.unary_unary(
        '/credit_pay.CreditPay/GetPayRelationList',
        request_serializer=credit__pay_dot_credit__pay__pb2.GetPayRelationListRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.GetPayRelationListResponse.FromString,
        )
    self.GetPayRelationDetail = channel.unary_unary(
        '/credit_pay.CreditPay/GetPayRelationDetail',
        request_serializer=credit__pay_dot_credit__pay__pb2.GetPayRelationDetailRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.GetPayRelationDetailResponse.FromString,
        )
    self.DeletePayRelation = channel.unary_unary(
        '/credit_pay.CreditPay/DeletePayRelation',
        request_serializer=credit__pay_dot_credit__pay__pb2.DeletePayRelationRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.CommonResponse.FromString,
        )
    self.ChangePayRelationStatus = channel.unary_unary(
        '/credit_pay.CreditPay/ChangePayRelationStatus',
        request_serializer=credit__pay_dot_credit__pay__pb2.ChangePayRelationStatusRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.CommonResponse.FromString,
        )
    self.GetLogList = channel.unary_unary(
        '/credit_pay.CreditPay/GetLogList',
        request_serializer=credit__pay_dot_credit__pay__pb2.GetLogListRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.GetLogListResponse.FromString,
        )
    self.PayNotify = channel.unary_unary(
        '/credit_pay.CreditPay/PayNotify',
        request_serializer=credit__pay_dot_credit__pay__pb2.PayNotifyRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.CommonResponse.FromString,
        )
    self.QueryPayStatus = channel.unary_unary(
        '/credit_pay.CreditPay/QueryPayStatus',
        request_serializer=credit__pay_dot_credit__pay__pb2.QueryPayStatusRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.QueryPayStatusResponse.FromString,
        )
    self.GetPayParams = channel.unary_unary(
        '/credit_pay.CreditPay/GetPayParams',
        request_serializer=credit__pay_dot_credit__pay__pb2.GetPayParamsRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.GetPayParamsResponse.FromString,
        )
    self.ForcePay = channel.unary_unary(
        '/credit_pay.CreditPay/ForcePay',
        request_serializer=credit__pay_dot_credit__pay__pb2.ForcePayRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.CommonResponse.FromString,
        )
    self.GetChannels = channel.unary_unary(
        '/credit_pay.CreditPay/GetChannels',
        request_serializer=credit__pay_dot_credit__pay__pb2.GetChannelsRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.GetChannelsResponse.FromString,
        )
    self.GetVouchers = channel.unary_unary(
        '/credit_pay.CreditPay/GetVouchers',
        request_serializer=credit__pay_dot_credit__pay__pb2.GetVouchersRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.GetVouchersResponse.FromString,
        )
    self.Pay = channel.unary_unary(
        '/credit_pay.CreditPay/Pay',
        request_serializer=credit__pay_dot_credit__pay__pb2.PayRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.CommonResponse.FromString,
        )
    self.VoucherPay = channel.unary_unary(
        '/credit_pay.CreditPay/VoucherPay',
        request_serializer=credit__pay_dot_credit__pay__pb2.VoucherPayRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.CommonResponse.FromString,
        )
    self.Refund = channel.unary_unary(
        '/credit_pay.CreditPay/Refund',
        request_serializer=credit__pay_dot_credit__pay__pb2.RefundRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.CommonResponse.FromString,
        )
    self.GetTransactionLogList = channel.unary_unary(
        '/credit_pay.CreditPay/GetTransactionLogList',
        request_serializer=credit__pay_dot_credit__pay__pb2.GetTransactionLogListRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.GetTransactionLogListResponse.FromString,
        )
    self.GetCreditPayListMiniWX = channel.unary_unary(
        '/credit_pay.CreditPay/GetCreditPayListMiniWX',
        request_serializer=credit__pay_dot_credit__pay__pb2.GetCreditPayListMiniWXRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.GetCreditPayListMiniWXResponse.FromString,
        )
    self.AdjustPayAccount = channel.unary_unary(
        '/credit_pay.CreditPay/AdjustPayAccount',
        request_serializer=credit__pay_dot_credit__pay__pb2.AdjustPayAccountRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.CommonResponse.FromString,
        )
    self.RefreshCreditPay = channel.unary_unary(
        '/credit_pay.CreditPay/RefreshCreditPay',
        request_serializer=credit__pay_dot_credit__pay__pb2.RefreshCreditPayRequest.SerializeToString,
        response_deserializer=credit__pay_dot_credit__pay__pb2.CommonResponse.FromString,
        )


class CreditPayServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def Ping(self, request, context):
    """健康检查
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateCreditPay(self, request, context):
    """创建信用付
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateCreditPay(self, request, context):
    """修改信用付
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CloseCreditPay(self, request, context):
    """关闭/开启信用付
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetCreditPayList(self, request, context):
    """信用付账户列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetCreditPayDetail(self, request, context):
    """信用付账户详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreatePayRelation(self, request, context):
    """创建支付关系
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPayRelationList(self, request, context):
    """获取支付关系列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPayRelationDetail(self, request, context):
    """获取支付关系详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeletePayRelation(self, request, context):
    """移除支付关系
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ChangePayRelationStatus(self, request, context):
    """关闭/开启支付关系
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetLogList(self, request, context):
    """获取日志数据
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def PayNotify(self, request, context):
    """支付回调 - 微信等线上支付
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryPayStatus(self, request, context):
    """支付结果查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPayParams(self, request, context):
    """获取支付参数
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ForcePay(self, request, context):
    """强制使用信用付支付 - 仅提供后台使用
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetChannels(self, request, context):
    """获取支付方式列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetVouchers(self, request, context):
    """获取代金券列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def Pay(self, request, context):
    """信用付支付
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def VoucherPay(self, request, context):
    """代金券支付
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def Refund(self, request, context):
    """退款 - 不支持原路退, 代金券退代金券, 其他退信用付
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransactionLogList(self, request, context):
    """获取交易流水
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetCreditPayListMiniWX(self, request, context):
    """小程序获取信用付账户
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AdjustPayAccount(self, request, context):
    """同步信用付账户额度, 提供给第三方, 强制对准额度. 只支持信用付
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RefreshCreditPay(self, request, context):
    """刷新信用额度
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_CreditPayServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'Ping': grpc.unary_unary_rpc_method_handler(
          servicer.Ping,
          request_deserializer=credit__pay_dot_credit__pay__pb2.PingRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.PingResponse.SerializeToString,
      ),
      'CreateCreditPay': grpc.unary_unary_rpc_method_handler(
          servicer.CreateCreditPay,
          request_deserializer=credit__pay_dot_credit__pay__pb2.CreateCreditPayRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.CommonResponse.SerializeToString,
      ),
      'UpdateCreditPay': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateCreditPay,
          request_deserializer=credit__pay_dot_credit__pay__pb2.UpdateCreditPayRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.CommonResponse.SerializeToString,
      ),
      'CloseCreditPay': grpc.unary_unary_rpc_method_handler(
          servicer.CloseCreditPay,
          request_deserializer=credit__pay_dot_credit__pay__pb2.CloseCreditPayRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.CommonResponse.SerializeToString,
      ),
      'GetCreditPayList': grpc.unary_unary_rpc_method_handler(
          servicer.GetCreditPayList,
          request_deserializer=credit__pay_dot_credit__pay__pb2.GetCreditPayListRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.GetCreditPayListResponse.SerializeToString,
      ),
      'GetCreditPayDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetCreditPayDetail,
          request_deserializer=credit__pay_dot_credit__pay__pb2.GetCreditPayDetailRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.GetCreditPayDetailResponse.SerializeToString,
      ),
      'CreatePayRelation': grpc.unary_unary_rpc_method_handler(
          servicer.CreatePayRelation,
          request_deserializer=credit__pay_dot_credit__pay__pb2.CreatePayRelationRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.CommonResponse.SerializeToString,
      ),
      'GetPayRelationList': grpc.unary_unary_rpc_method_handler(
          servicer.GetPayRelationList,
          request_deserializer=credit__pay_dot_credit__pay__pb2.GetPayRelationListRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.GetPayRelationListResponse.SerializeToString,
      ),
      'GetPayRelationDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetPayRelationDetail,
          request_deserializer=credit__pay_dot_credit__pay__pb2.GetPayRelationDetailRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.GetPayRelationDetailResponse.SerializeToString,
      ),
      'DeletePayRelation': grpc.unary_unary_rpc_method_handler(
          servicer.DeletePayRelation,
          request_deserializer=credit__pay_dot_credit__pay__pb2.DeletePayRelationRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.CommonResponse.SerializeToString,
      ),
      'ChangePayRelationStatus': grpc.unary_unary_rpc_method_handler(
          servicer.ChangePayRelationStatus,
          request_deserializer=credit__pay_dot_credit__pay__pb2.ChangePayRelationStatusRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.CommonResponse.SerializeToString,
      ),
      'GetLogList': grpc.unary_unary_rpc_method_handler(
          servicer.GetLogList,
          request_deserializer=credit__pay_dot_credit__pay__pb2.GetLogListRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.GetLogListResponse.SerializeToString,
      ),
      'PayNotify': grpc.unary_unary_rpc_method_handler(
          servicer.PayNotify,
          request_deserializer=credit__pay_dot_credit__pay__pb2.PayNotifyRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.CommonResponse.SerializeToString,
      ),
      'QueryPayStatus': grpc.unary_unary_rpc_method_handler(
          servicer.QueryPayStatus,
          request_deserializer=credit__pay_dot_credit__pay__pb2.QueryPayStatusRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.QueryPayStatusResponse.SerializeToString,
      ),
      'GetPayParams': grpc.unary_unary_rpc_method_handler(
          servicer.GetPayParams,
          request_deserializer=credit__pay_dot_credit__pay__pb2.GetPayParamsRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.GetPayParamsResponse.SerializeToString,
      ),
      'ForcePay': grpc.unary_unary_rpc_method_handler(
          servicer.ForcePay,
          request_deserializer=credit__pay_dot_credit__pay__pb2.ForcePayRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.CommonResponse.SerializeToString,
      ),
      'GetChannels': grpc.unary_unary_rpc_method_handler(
          servicer.GetChannels,
          request_deserializer=credit__pay_dot_credit__pay__pb2.GetChannelsRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.GetChannelsResponse.SerializeToString,
      ),
      'GetVouchers': grpc.unary_unary_rpc_method_handler(
          servicer.GetVouchers,
          request_deserializer=credit__pay_dot_credit__pay__pb2.GetVouchersRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.GetVouchersResponse.SerializeToString,
      ),
      'Pay': grpc.unary_unary_rpc_method_handler(
          servicer.Pay,
          request_deserializer=credit__pay_dot_credit__pay__pb2.PayRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.CommonResponse.SerializeToString,
      ),
      'VoucherPay': grpc.unary_unary_rpc_method_handler(
          servicer.VoucherPay,
          request_deserializer=credit__pay_dot_credit__pay__pb2.VoucherPayRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.CommonResponse.SerializeToString,
      ),
      'Refund': grpc.unary_unary_rpc_method_handler(
          servicer.Refund,
          request_deserializer=credit__pay_dot_credit__pay__pb2.RefundRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.CommonResponse.SerializeToString,
      ),
      'GetTransactionLogList': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransactionLogList,
          request_deserializer=credit__pay_dot_credit__pay__pb2.GetTransactionLogListRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.GetTransactionLogListResponse.SerializeToString,
      ),
      'GetCreditPayListMiniWX': grpc.unary_unary_rpc_method_handler(
          servicer.GetCreditPayListMiniWX,
          request_deserializer=credit__pay_dot_credit__pay__pb2.GetCreditPayListMiniWXRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.GetCreditPayListMiniWXResponse.SerializeToString,
      ),
      'AdjustPayAccount': grpc.unary_unary_rpc_method_handler(
          servicer.AdjustPayAccount,
          request_deserializer=credit__pay_dot_credit__pay__pb2.AdjustPayAccountRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.CommonResponse.SerializeToString,
      ),
      'RefreshCreditPay': grpc.unary_unary_rpc_method_handler(
          servicer.RefreshCreditPay,
          request_deserializer=credit__pay_dot_credit__pay__pb2.RefreshCreditPayRequest.FromString,
          response_serializer=credit__pay_dot_credit__pay__pb2.CommonResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'credit_pay.CreditPay', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
