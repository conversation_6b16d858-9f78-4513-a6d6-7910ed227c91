# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: credit_pay/credit_pay.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='credit_pay/credit_pay.proto',
  package='credit_pay',
  syntax='proto3',
  serialized_options=_b('Z\014./credit-pay'),
  serialized_pb=_b('\n\x1b\x63redit_pay/credit_pay.proto\x12\ncredit_pay\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x1b\n\x0bPingRequest\x12\x0c\n\x04ping\x18\x01 \x01(\t\"\x1c\n\x0cPingResponse\x12\x0c\n\x04pong\x18\x01 \x01(\t\"\x86\x01\n\x16\x43reateCreditPayRequest\x12\x12\n\ncompany_id\x18\x01 \x01(\x04\x12-\n\ncredit_way\x18\x02 \x01(\x0e\x32\x19.credit_pay.CreditWayType\x12)\n\x07\x63redits\x18\x03 \x03(\x0b\x32\x18.credit_pay.BranchCredit\"\x93\x01\n\x0c\x42ranchCredit\x12\x11\n\tbranch_id\x18\x03 \x01(\x04\x12\r\n\x05limit\x18\x04 \x01(\x03\x12\x0f\n\x07is_over\x18\x05 \x01(\x08\x12\x16\n\x0emax_over_limit\x18\x06 \x01(\x03\x12\x0e\n\x06remark\x18\x07 \x01(\t\x12(\n\x06status\x18\x08 \x01(\x0e\x32\x18.credit_pay.CreditStatus\"\xb4\x01\n\x16UpdateCreditPayRequest\x12\x15\n\rcredit_pay_id\x18\x01 \x01(\x04\x12\x10\n\x08increase\x18\x02 \x01(\x04\x12\x0e\n\x06reduce\x18\x03 \x01(\x04\x12\x0f\n\x07is_over\x18\x04 \x01(\x08\x12\x16\n\x0emax_over_limit\x18\x05 \x01(\x03\x12\x0e\n\x06remark\x18\x06 \x01(\t\x12(\n\x06status\x18\x07 \x01(\x0e\x32\x18.credit_pay.CreditStatus\"Y\n\x15\x43loseCreditPayRequest\x12\x16\n\x0e\x63redit_pay_ids\x18\x01 \x03(\x04\x12(\n\x06status\x18\x02 \x01(\x0e\x32\x18.credit_pay.CreditStatus\"\xbb\x01\n\x17GetCreditPayListRequest\x12-\n\ncredit_way\x18\x01 \x01(\x0e\x32\x19.credit_pay.CreditWayType\x12\x12\n\nbranch_ids\x18\x02 \x03(\x04\x12\x13\n\x0b\x63ompany_ids\x18\x03 \x03(\x04\x12(\n\x06status\x18\x04 \x01(\x0e\x32\x18.credit_pay.CreditStatus\x12\x1e\n\x04page\x18\x05 \x01(\x0b\x32\x10.credit_pay.Page\"R\n\x18GetCreditPayListResponse\x12\'\n\x04rows\x18\x01 \x03(\x0b\x32\x19.credit_pay.CreditPayList\x12\r\n\x05total\x18\x02 \x01(\x04\"\x9d\x03\n\rCreditPayList\x12\x15\n\rcredit_pay_id\x18\x01 \x01(\x04\x12\x11\n\tbranch_id\x18\x02 \x01(\x04\x12\x13\n\x0b\x62ranch_code\x18\x03 \x01(\t\x12\x13\n\x0b\x62ranch_name\x18\x04 \x01(\t\x12\x12\n\ncompany_id\x18\x05 \x01(\x04\x12\x14\n\x0c\x63ompany_name\x18\x06 \x01(\t\x12\r\n\x05limit\x18\x07 \x01(\x03\x12\x0f\n\x07is_over\x18\x08 \x01(\x08\x12\x16\n\x0emax_over_limit\x18\t \x01(\x03\x12\x12\n\nupdated_by\x18\n \x01(\x04\x12\x12\n\ncreated_by\x18\x0b \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x0c \x01(\t\x12\x14\n\x0cupdated_name\x18\r \x01(\t\x12(\n\x06status\x18\x0e \x01(\x0e\x32\x18.credit_pay.CreditStatus\x12+\n\x07\x63reated\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12+\n\x07updated\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"2\n\x19GetCreditPayDetailRequest\x12\x15\n\rcredit_pay_id\x18\x01 \x01(\x04\"\xd3\x03\n\x1aGetCreditPayDetailResponse\x12\x15\n\rcredit_pay_id\x18\x01 \x01(\x04\x12\x11\n\tbranch_id\x18\x02 \x01(\x04\x12\x13\n\x0b\x62ranch_code\x18\x03 \x01(\t\x12\x13\n\x0b\x62ranch_name\x18\x04 \x01(\t\x12\x12\n\ncompany_id\x18\x05 \x01(\x04\x12\x14\n\x0c\x63ompany_code\x18\x06 \x01(\t\x12\x14\n\x0c\x63ompany_name\x18\x07 \x01(\t\x12\r\n\x05limit\x18\x08 \x01(\x03\x12\x0f\n\x07is_over\x18\t \x01(\x08\x12\x16\n\x0emax_over_limit\x18\n \x01(\x03\x12\x0e\n\x06remark\x18\x0b \x01(\t\x12\x12\n\nupdated_by\x18\x0c \x01(\x04\x12\x12\n\ncreated_by\x18\r \x01(\x04\x12(\n\x06status\x18\x0e \x01(\x0e\x32\x18.credit_pay.CreditStatus\x12-\n\ncredit_way\x18\x0f \x01(\x0e\x32\x19.credit_pay.CreditWayType\x12+\n\x07\x63reated\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12+\n\x07updated\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"o\n\x18\x43reatePayRelationRequest\x12\x11\n\tCompanyId\x18\x01 \x01(\x04\x12-\n\x06status\x18\x02 \x01(\x0e\x32\x1d.credit_pay.PayRelationStatus\x12\x11\n\tstore_ids\x18\x03 \x03(\x04\"\xc3\x01\n\x19GetPayRelationListRequest\x12\x15\n\rgeo_region_id\x18\x01 \x01(\x04\x12\x18\n\x10\x62ranch_region_id\x18\x02 \x01(\x04\x12\x11\n\tstore_ids\x18\x03 \x03(\x04\x12\x13\n\x0b\x63ompany_ids\x18\x04 \x03(\x04\x12-\n\x06status\x18\x05 \x01(\x0e\x32\x1d.credit_pay.PayRelationStatus\x12\x1e\n\x04page\x18\x06 \x01(\x0b\x32\x10.credit_pay.Page\"c\n\x1aGetPayRelationListResponse\x12\x36\n\x04rows\x18\x01 \x03(\x0b\x32(.credit_pay.GetPayRelationDetailResponse\x12\r\n\x05total\x18\x02 \x01(\x04\"\xf4\x02\n\x1cGetPayRelationDetailResponse\x12\x13\n\x0brelation_id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_code\x18\x03 \x01(\t\x12\x12\n\nstore_name\x18\x04 \x01(\t\x12\x12\n\ncompany_id\x18\x05 \x01(\x04\x12\x14\n\x0c\x63ompany_name\x18\x06 \x01(\t\x12\x12\n\nupdated_by\x18\x07 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x08 \x01(\t\x12\x12\n\ncreated_by\x18\t \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\n \x01(\t\x12-\n\x06status\x18\x0b \x01(\x0e\x32\x1d.credit_pay.PayRelationStatus\x12+\n\x07\x63reated\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12+\n\x07updated\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"2\n\x1bGetPayRelationDetailRequest\x12\x13\n\x0brelation_id\x18\x01 \x01(\x04\"/\n\x18\x44\x65letePayRelationRequest\x12\x13\n\x0brelation_id\x18\x01 \x03(\x04\"e\n\x1e\x43hangePayRelationStatusRequest\x12\x14\n\x0crelation_ids\x18\x01 \x03(\x04\x12-\n\x06status\x18\x02 \x01(\x0e\x32\x1d.credit_pay.PayRelationStatus\"V\n\x11GetLogListRequest\x12\x10\n\x08\x62us_name\x18\x01 \x01(\t\x12\x0f\n\x07\x64\x61ta_id\x18\x02 \x01(\x04\x12\x1e\n\x04page\x18\x03 \x01(\x0b\x32\x10.credit_pay.Page\"F\n\x12GetLogListResponse\x12!\n\x04rows\x18\x01 \x03(\x0b\x32\x13.credit_pay.LogList\x12\r\n\x05total\x18\x02 \x01(\x04\"\xcd\x01\n\x07LogList\x12\x0e\n\x06log_id\x18\x01 \x01(\x04\x12%\n\x08log_data\x18\x02 \x03(\x0b\x32\x13.credit_pay.LogInfo\x12\x34\n\x0eoperation_type\x18\x03 \x01(\x0e\x32\x1c.credit_pay.LogOperationType\x12\x12\n\nupdated_by\x18\x04 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x05 \x01(\t\x12+\n\x07updated\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"R\n\x07LogInfo\x12\x12\n\nfield_name\x18\x01 \x01(\t\x12\x0f\n\x07old_val\x18\x02 \x01(\t\x12\x0f\n\x07new_val\x18\x03 \x01(\t\x12\x11\n\tis_change\x18\x04 \x01(\x08\"\xa5\x01\n\x10PayNotifyRequest\x12\x10\n\x08order_no\x18\x01 \x01(\t\x12\x19\n\x11tp_transaction_no\x18\x02 \x01(\t\x12\x12\n\npay_status\x18\x03 \x01(\t\x12\x0f\n\x07message\x18\x04 \x01(\t\x12\x13\n\x0bpay_channel\x18\x05 \x01(\t\x12\x13\n\x0bpay_user_id\x18\x06 \x01(\t\x12\x15\n\rpay_user_name\x18\x07 \x01(\t\"\x1e\n\nPayRequest\x12\x10\n\x08order_no\x18\x01 \x01(\t\"R\n\x15QueryPayStatusRequest\x12\x0f\n\x07\x63hannel\x18\x01 \x01(\t\x12\x16\n\x0etransaction_id\x18\x02 \x01(\t\x12\x10\n\x08order_no\x18\x03 \x01(\t\"\xa3\x01\n\x16QueryPayStatusResponse\x12\x12\n\nerror_code\x18\x01 \x01(\t\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12\x19\n\x11transaction_state\x18\x03 \x01(\t\x12\x13\n\x0breal_amount\x18\x04 \x01(\x01\x12\x19\n\x11tp_transaction_id\x18\x05 \x01(\t\x12\x13\n\x0bpay_channel\x18\x06 \x01(\t\"8\n\x13GetPayParamsRequest\x12\x10\n\x08order_no\x18\x01 \x01(\t\x12\x0f\n\x07\x63hannel\x18\x02 \x01(\t\"\xab\x02\n\x14GetPayParamsResponse\x12\x12\n\nerror_code\x18\x01 \x01(\t\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12\x11\n\tticket_id\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x11\n\tprepay_id\x18\x05 \x01(\t\x12\x13\n\x0bprepay_sign\x18\x06 \x01(\t\x12\x19\n\x11tp_transaction_id\x18\x07 \x01(\t\x12?\n\x08pack_str\x18\x08 \x03(\x0b\x32-.credit_pay.GetPayParamsResponse.PackStrEntry\x12\x11\n\tsign_type\x18\t \x01(\t\x1a.\n\x0cPackStrEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"#\n\x0f\x46orcePayRequest\x12\x10\n\x08order_no\x18\x01 \x01(\t\"\xb7\x01\n\x12GetChannelsRequest\x12\x0e\n\x06\x61\x63tion\x18\x01 \x01(\t\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12*\n\x08platform\x18\x03 \x01(\x0e\x32\x18.credit_pay.PlatformType\x12\x15\n\rorder_type_id\x18\x04 \x01(\x04\x12<\n\x14list_binding_section\x18\x05 \x01(\x0b\x32\x1e.credit_pay.ListBindingSection\"n\n\x12ListBindingSection\x12\x18\n\x10\x63hannel_category\x18\x01 \x01(\t\x12\x0f\n\x07\x65nabled\x18\x02 \x01(\t\x12\x16\n\x0e\x63hannel_labels\x18\x03 \x01(\t\x12\x15\n\rbusiness_code\x18\x04 \x01(\t\"~\n\x13GetChannelsResponse\x12\x12\n\nerror_code\x18\x01 \x01(\t\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12<\n\x14\x62inding_list_section\x18\x03 \x01(\x0b\x32\x1e.credit_pay.BindingListSection\"C\n\x12\x42indingListSection\x12-\n\x0c\x62inding_item\x18\x01 \x03(\x0b\x32\x17.credit_pay.BindingItem\"\xc7\x01\n\x0b\x42indingItem\x12\x14\n\x0c\x63hannel_code\x18\x01 \x01(\t\x12\x14\n\x0c\x63hannel_name\x18\x02 \x01(\t\x12\x15\n\rbusiness_code\x18\x03 \x01(\t\x12\x14\n\x0c\x63hannel_type\x18\x04 \x01(\t\x12\x18\n\x10\x63hannel_category\x18\x05 \x01(\t\x12\x1c\n\x14\x63hannel_sub_category\x18\x06 \x01(\t\x12\x16\n\x0e\x63hannel_labels\x18\x07 \x01(\t\x12\x0f\n\x07\x65nabled\x18\x08 \x01(\t\"\xbc\x01\n\x12GetVouchersRequest\x12\x10\n\x08store_id\x18\x01 \x03(\x04\x12\x15\n\rorder_type_id\x18\x02 \x03(\x04\x12\x31\n\x0evoucher_status\x18\x03 \x03(\x0e\x32\x19.credit_pay.VoucherStatus\x12\x14\n\x0cvoucher_name\x18\x04 \x01(\t\x12\x14\n\x0cvoucher_code\x18\x05 \x01(\t\x12\x1e\n\x04page\x18\x06 \x01(\x0b\x32\x10.credit_pay.Page\"N\n\x13GetVouchersResponse\x12(\n\x04rows\x18\x01 \x03(\x0b\x32\x1a.credit_pay.VouchersDetail\x12\r\n\x05total\x18\x02 \x01(\x04\"\x8f\x04\n\x0eVouchersDetail\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08store_id\x18\x03 \x01(\x04\x12\x12\n\nvoucher_id\x18\x04 \x01(\x04\x12\x14\n\x0cvoucher_code\x18\x05 \x01(\t\x12\x14\n\x0cvoucher_name\x18\x06 \x01(\t\x12.\n\nstart_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12-\n\norder_type\x18\t \x03(\x0b\x32\x19.credit_pay.OrderTypeInfo\x12\r\n\x05limit\x18\n \x01(\t\x12\x0e\n\x06remark\x18\x0b \x01(\t\x12\x31\n\x0evoucher_status\x18\x0c \x01(\x0e\x32\x19.credit_pay.VoucherStatus\x12\x12\n\nupdated_by\x18\r \x01(\x04\x12\x12\n\ncreated_by\x18\x0e \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x0f \x01(\t\x12\x14\n\x0cupdated_name\x18\x10 \x01(\t\x12+\n\x07\x63reated\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12+\n\x07updated\x18\x12 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"X\n\rOrderTypeInfo\x12\x15\n\rorder_type_id\x18\x01 \x01(\t\x12\x17\n\x0forder_type_code\x18\x02 \x01(\t\x12\x17\n\x0forder_type_name\x18\x03 \x01(\t\"9\n\x11VoucherPayRequest\x12\x10\n\x08order_no\x18\x01 \x01(\t\x12\x12\n\nvoucher_id\x18\x02 \x01(\t\"\"\n\rRefundRequest\x12\x11\n\trefund_id\x18\x01 \x03(\x04\"x\n\x1cGetTransactionLogListRequest\x12\x12\n\naccount_id\x18\x01 \x01(\x04\x12\x14\n\x0c\x61\x63\x63ount_type\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x1e\n\x04page\x18\x04 \x01(\x0b\x32\x10.credit_pay.Page\"^\n\x1dGetTransactionLogListResponse\x12.\n\x04rows\x18\x01 \x03(\x0b\x32 .credit_pay.TransactionLogDetail\x12\r\n\x05total\x18\x02 \x01(\x04\"\xff\x01\n\x14TransactionLogDetail\x12\x1b\n\x13transaction_account\x18\x01 \x01(\t\x12\x10\n\x08order_no\x18\x02 \x01(\t\x12\x12\n\norder_code\x18\x03 \x01(\t\x12\x11\n\tsum_price\x18\x04 \x01(\t\x12\x1a\n\x12transaction_amount\x18\x05 \x01(\t\x12\x15\n\rremain_amount\x18\x06 \x01(\t\x12\x34\n\x10transaction_time\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x08 \x01(\t\x12\x18\n\x10transaction_type\x18\t \x01(\t\"\xac\x01\n\x1dGetCreditPayListMiniWXRequest\x12\x12\n\nbranch_ids\x18\x01 \x03(\x04\x12-\n\ncredit_way\x18\x02 \x01(\x0e\x32\x19.credit_pay.CreditWayType\x12(\n\x06status\x18\x03 \x01(\x0e\x32\x18.credit_pay.CreditStatus\x12\x1e\n\x04page\x18\x04 \x01(\x0b\x32\x10.credit_pay.Page\"`\n\x1eGetCreditPayListMiniWXResponse\x12/\n\x04rows\x18\x01 \x03(\x0b\x32!.credit_pay.CreditPayMiniWXDetail\x12\r\n\x05total\x18\x02 \x01(\x04\"\xfb\x01\n\x15\x43reditPayMiniWXDetail\x12\x13\n\x0b\x62ranch_code\x18\x01 \x01(\t\x12\x13\n\x0b\x62ranch_name\x18\x02 \x01(\t\x12\x1a\n\x12\x62oh_account_amount\x18\x03 \x01(\t\x12\x32\n\x0elast_sync_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12(\n\x06status\x18\x05 \x01(\x0e\x32\x18.credit_pay.CreditStatus\x12>\n\x0f\x65xtend_accounts\x18\x06 \x03(\x0b\x32%.credit_pay.ThirdPartyCreditPayDetail\"A\n\x19ThirdPartyCreditPayDetail\x12\x14\n\x0c\x61\x63\x63ount_name\x18\x01 \x01(\t\x12\x0e\n\x06\x61mount\x18\x02 \x01(\t\"\x7f\n\x17\x41\x64justPayAccountRequest\x12\x30\n\x04rows\x18\x01 \x03(\x0b\x32\".credit_pay.AdjustPayAccountDetail\x12\x32\n\x0f\x63redit_way_type\x18\x02 \x01(\x0e\x32\x19.credit_pay.CreditWayType\"b\n\x16\x41\x64justPayAccountDetail\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x12\n\ncompany_id\x18\x02 \x01(\x04\x12\r\n\x05limit\x18\x03 \x01(\t\x12\x12\n\nrequest_id\x18\x04 \x01(\t\"-\n\x17RefreshCreditPayRequest\x12\x12\n\nstore_code\x18\x01 \x01(\t\"/\n\x0e\x43ommonResponse\x12\x0c\n\x04\x63ode\x18\x01 \x01(\r\x12\x0f\n\x07message\x18\x02 \x01(\t\"B\n\x04Page\x12\r\n\x05order\x18\x01 \x01(\t\x12\x0c\n\x04sort\x18\x02 \x01(\t\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x0e\n\x06offset\x18\x04 \x01(\x05*O\n\rCreditWayType\x12\x11\n\rCreditWayInit\x10\x00\x12\x12\n\x0e\x43reditWayStore\x10\x01\x12\x17\n\x13\x43reditWayFranchisee\x10\x02*R\n\x0c\x43reditStatus\x12\x14\n\x10\x43reditStatusInit\x10\x00\x12\x14\n\x10\x43reditStatusOpen\x10\x01\x12\x16\n\x12\x43reditStatusClosed\x10\x02*f\n\x11PayRelationStatus\x12\x19\n\x15PayRelationStatusInit\x10\x00\x12\x19\n\x15PayRelationStatusOpen\x10\x01\x12\x1b\n\x17PayRelationStatusClosed\x10\x02*d\n\x10LogOperationType\x12\x18\n\x14LogOperationTypeInit\x10\x00\x12\x1a\n\x16LogOperationTypeCreate\x10\x01\x12\x1a\n\x16LogOperationTypeUpdate\x10\x02*L\n\x0cPlatformType\x12\x14\n\x10PlatformTypeInit\x10\x00\x12\x11\n\rWXMiniProgram\x10\x01\x12\x13\n\x0f\x44ingDingProgram\x10\x02*O\n\rVoucherStatus\x12\x15\n\x11VoucherStatusInit\x10\x00\x12\x0c\n\x08NotStart\x10\x01\x12\x0c\n\x08UnderWay\x10\x02\x12\x0b\n\x07\x45xpired\x10\x03\x32\xe6\x19\n\tCreditPay\x12\x39\n\x04Ping\x12\x17.credit_pay.PingRequest\x1a\x18.credit_pay.PingResponse\x12\x82\x01\n\x0f\x43reateCreditPay\x12\".credit_pay.CreateCreditPayRequest\x1a\x1a.credit_pay.CommonResponse\"/\x82\xd3\xe4\x93\x02)\"$/api/v1/credit-pay/create-credit-pay:\x01*\x12\x82\x01\n\x0fUpdateCreditPay\x12\".credit_pay.UpdateCreditPayRequest\x1a\x1a.credit_pay.CommonResponse\"/\x82\xd3\xe4\x93\x02)\"$/api/v1/credit-pay/update-credit-pay:\x01*\x12\x7f\n\x0e\x43loseCreditPay\x12!.credit_pay.CloseCreditPayRequest\x1a\x1a.credit_pay.CommonResponse\".\x82\xd3\xe4\x93\x02(\"#/api/v1/credit-pay/close-credit-pay:\x01*\x12\x90\x01\n\x10GetCreditPayList\x12#.credit_pay.GetCreditPayListRequest\x1a$.credit_pay.GetCreditPayListResponse\"1\x82\xd3\xe4\x93\x02+\"&/api/v1/credit-pay/get-credit-pay-list:\x01*\x12\x95\x01\n\x12GetCreditPayDetail\x12%.credit_pay.GetCreditPayDetailRequest\x1a&.credit_pay.GetCreditPayDetailResponse\"0\x82\xd3\xe4\x93\x02*\x12(/api/v1/credit-pay/get-credit-pay-detail\x12\x88\x01\n\x11\x43reatePayRelation\x12$.credit_pay.CreatePayRelationRequest\x1a\x1a.credit_pay.CommonResponse\"1\x82\xd3\xe4\x93\x02+\"&/api/v1/credit-pay/create-pay-relation:\x01*\x12\x98\x01\n\x12GetPayRelationList\x12%.credit_pay.GetPayRelationListRequest\x1a&.credit_pay.GetPayRelationListResponse\"3\x82\xd3\xe4\x93\x02-\"(/api/v1/credit-pay/get-pay-relation-list:\x01*\x12\xa0\x01\n\x14GetPayRelationDetail\x12\'.credit_pay.GetPayRelationDetailRequest\x1a(.credit_pay.GetPayRelationDetailResponse\"5\x82\xd3\xe4\x93\x02/\"*/api/v1/credit-pay/get-pay-relation-detail:\x01*\x12\x88\x01\n\x11\x44\x65letePayRelation\x12$.credit_pay.DeletePayRelationRequest\x1a\x1a.credit_pay.CommonResponse\"1\x82\xd3\xe4\x93\x02+\"&/api/v1/credit-pay/delete-pay-relation:\x01*\x12\x9b\x01\n\x17\x43hangePayRelationStatus\x12*.credit_pay.ChangePayRelationStatusRequest\x1a\x1a.credit_pay.CommonResponse\"8\x82\xd3\xe4\x93\x02\x32\"-/api/v1/credit-pay/change-pay-relation-status:\x01*\x12w\n\nGetLogList\x12\x1d.credit_pay.GetLogListRequest\x1a\x1e.credit_pay.GetLogListResponse\"*\x82\xd3\xe4\x93\x02$\"\x1f/api/v1/credit-pay/get-log-list:\x01*\x12o\n\tPayNotify\x12\x1c.credit_pay.PayNotifyRequest\x1a\x1a.credit_pay.CommonResponse\"(\x82\xd3\xe4\x93\x02\"\"\x1d/api/v1/credit-pay/pay-notify:\x01*\x12\x87\x01\n\x0eQueryPayStatus\x12!.credit_pay.QueryPayStatusRequest\x1a\".credit_pay.QueryPayStatusResponse\".\x82\xd3\xe4\x93\x02(\"#/api/v1/credit-pay/query-pay-status:\x01*\x12\x7f\n\x0cGetPayParams\x12\x1f.credit_pay.GetPayParamsRequest\x1a .credit_pay.GetPayParamsResponse\",\x82\xd3\xe4\x93\x02&\"!/api/v1/credit-pay/get-pay-params:\x01*\x12l\n\x08\x46orcePay\x12\x1b.credit_pay.ForcePayRequest\x1a\x1a.credit_pay.CommonResponse\"\'\x82\xd3\xe4\x93\x02!\"\x1c/api/v1/credit-pay/force-pay:\x01*\x12z\n\x0bGetChannels\x12\x1e.credit_pay.GetChannelsRequest\x1a\x1f.credit_pay.GetChannelsResponse\"*\x82\xd3\xe4\x93\x02$\"\x1f/api/v1/credit-pay/get-channels:\x01*\x12z\n\x0bGetVouchers\x12\x1e.credit_pay.GetVouchersRequest\x1a\x1f.credit_pay.GetVouchersResponse\"*\x82\xd3\xe4\x93\x02$\"\x1f/api/v1/credit-pay/get-vouchers:\x01*\x12\\\n\x03Pay\x12\x16.credit_pay.PayRequest\x1a\x1a.credit_pay.CommonResponse\"!\x82\xd3\xe4\x93\x02\x1b\"\x16/api/v1/credit-pay/pay:\x01*\x12r\n\nVoucherPay\x12\x1d.credit_pay.VoucherPayRequest\x1a\x1a.credit_pay.CommonResponse\")\x82\xd3\xe4\x93\x02#\"\x1e/api/v1/credit-pay/voucher-pay:\x01*\x12\x65\n\x06Refund\x12\x19.credit_pay.RefundRequest\x1a\x1a.credit_pay.CommonResponse\"$\x82\xd3\xe4\x93\x02\x1e\"\x19/api/v1/credit-pay/refund:\x01*\x12\xa4\x01\n\x15GetTransactionLogList\x12(.credit_pay.GetTransactionLogListRequest\x1a).credit_pay.GetTransactionLogListResponse\"6\x82\xd3\xe4\x93\x02\x30\"+/api/v1/credit-pay/get-transaction-log-list:\x01*\x12\xaa\x01\n\x16GetCreditPayListMiniWX\x12).credit_pay.GetCreditPayListMiniWXRequest\x1a*.credit_pay.GetCreditPayListMiniWXResponse\"9\x82\xd3\xe4\x93\x02\x33\"./api/v1/credit-pay/get-credit-pay-list-mini-wx:\x01*\x12\x85\x01\n\x10\x41\x64justPayAccount\x12#.credit_pay.AdjustPayAccountRequest\x1a\x1a.credit_pay.CommonResponse\"0\x82\xd3\xe4\x93\x02*\"%/api/v1/credit-pay/adjust-pay-account:\x01*\x12\x85\x01\n\x10RefreshCreditPay\x12#.credit_pay.RefreshCreditPayRequest\x1a\x1a.credit_pay.CommonResponse\"0\x82\xd3\xe4\x93\x02*\"%/api/v1/credit-pay/refresh-credit-pay:\x01*B\x0eZ\x0c./credit-payb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])

_CREDITWAYTYPE = _descriptor.EnumDescriptor(
  name='CreditWayType',
  full_name='credit_pay.CreditWayType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CreditWayInit', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CreditWayStore', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CreditWayFranchisee', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7374,
  serialized_end=7453,
)
_sym_db.RegisterEnumDescriptor(_CREDITWAYTYPE)

CreditWayType = enum_type_wrapper.EnumTypeWrapper(_CREDITWAYTYPE)
_CREDITSTATUS = _descriptor.EnumDescriptor(
  name='CreditStatus',
  full_name='credit_pay.CreditStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CreditStatusInit', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CreditStatusOpen', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CreditStatusClosed', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7455,
  serialized_end=7537,
)
_sym_db.RegisterEnumDescriptor(_CREDITSTATUS)

CreditStatus = enum_type_wrapper.EnumTypeWrapper(_CREDITSTATUS)
_PAYRELATIONSTATUS = _descriptor.EnumDescriptor(
  name='PayRelationStatus',
  full_name='credit_pay.PayRelationStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PayRelationStatusInit', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PayRelationStatusOpen', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PayRelationStatusClosed', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7539,
  serialized_end=7641,
)
_sym_db.RegisterEnumDescriptor(_PAYRELATIONSTATUS)

PayRelationStatus = enum_type_wrapper.EnumTypeWrapper(_PAYRELATIONSTATUS)
_LOGOPERATIONTYPE = _descriptor.EnumDescriptor(
  name='LogOperationType',
  full_name='credit_pay.LogOperationType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='LogOperationTypeInit', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LogOperationTypeCreate', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LogOperationTypeUpdate', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7643,
  serialized_end=7743,
)
_sym_db.RegisterEnumDescriptor(_LOGOPERATIONTYPE)

LogOperationType = enum_type_wrapper.EnumTypeWrapper(_LOGOPERATIONTYPE)
_PLATFORMTYPE = _descriptor.EnumDescriptor(
  name='PlatformType',
  full_name='credit_pay.PlatformType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='PlatformTypeInit', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='WXMiniProgram', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DingDingProgram', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7745,
  serialized_end=7821,
)
_sym_db.RegisterEnumDescriptor(_PLATFORMTYPE)

PlatformType = enum_type_wrapper.EnumTypeWrapper(_PLATFORMTYPE)
_VOUCHERSTATUS = _descriptor.EnumDescriptor(
  name='VoucherStatus',
  full_name='credit_pay.VoucherStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='VoucherStatusInit', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NotStart', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='UnderWay', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Expired', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7823,
  serialized_end=7902,
)
_sym_db.RegisterEnumDescriptor(_VOUCHERSTATUS)

VoucherStatus = enum_type_wrapper.EnumTypeWrapper(_VOUCHERSTATUS)
CreditWayInit = 0
CreditWayStore = 1
CreditWayFranchisee = 2
CreditStatusInit = 0
CreditStatusOpen = 1
CreditStatusClosed = 2
PayRelationStatusInit = 0
PayRelationStatusOpen = 1
PayRelationStatusClosed = 2
LogOperationTypeInit = 0
LogOperationTypeCreate = 1
LogOperationTypeUpdate = 2
PlatformTypeInit = 0
WXMiniProgram = 1
DingDingProgram = 2
VoucherStatusInit = 0
NotStart = 1
UnderWay = 2
Expired = 3



_PINGREQUEST = _descriptor.Descriptor(
  name='PingRequest',
  full_name='credit_pay.PingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ping', full_name='credit_pay.PingRequest.ping', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=106,
  serialized_end=133,
)


_PINGRESPONSE = _descriptor.Descriptor(
  name='PingResponse',
  full_name='credit_pay.PingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='pong', full_name='credit_pay.PingResponse.pong', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=135,
  serialized_end=163,
)


_CREATECREDITPAYREQUEST = _descriptor.Descriptor(
  name='CreateCreditPayRequest',
  full_name='credit_pay.CreateCreditPayRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='company_id', full_name='credit_pay.CreateCreditPayRequest.company_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='credit_way', full_name='credit_pay.CreateCreditPayRequest.credit_way', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='credits', full_name='credit_pay.CreateCreditPayRequest.credits', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=166,
  serialized_end=300,
)


_BRANCHCREDIT = _descriptor.Descriptor(
  name='BranchCredit',
  full_name='credit_pay.BranchCredit',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='credit_pay.BranchCredit.branch_id', index=0,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='credit_pay.BranchCredit.limit', index=1,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_over', full_name='credit_pay.BranchCredit.is_over', index=2,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_over_limit', full_name='credit_pay.BranchCredit.max_over_limit', index=3,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='credit_pay.BranchCredit.remark', index=4,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='credit_pay.BranchCredit.status', index=5,
      number=8, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=303,
  serialized_end=450,
)


_UPDATECREDITPAYREQUEST = _descriptor.Descriptor(
  name='UpdateCreditPayRequest',
  full_name='credit_pay.UpdateCreditPayRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='credit_pay_id', full_name='credit_pay.UpdateCreditPayRequest.credit_pay_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increase', full_name='credit_pay.UpdateCreditPayRequest.increase', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reduce', full_name='credit_pay.UpdateCreditPayRequest.reduce', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_over', full_name='credit_pay.UpdateCreditPayRequest.is_over', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_over_limit', full_name='credit_pay.UpdateCreditPayRequest.max_over_limit', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='credit_pay.UpdateCreditPayRequest.remark', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='credit_pay.UpdateCreditPayRequest.status', index=6,
      number=7, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=453,
  serialized_end=633,
)


_CLOSECREDITPAYREQUEST = _descriptor.Descriptor(
  name='CloseCreditPayRequest',
  full_name='credit_pay.CloseCreditPayRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='credit_pay_ids', full_name='credit_pay.CloseCreditPayRequest.credit_pay_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='credit_pay.CloseCreditPayRequest.status', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=635,
  serialized_end=724,
)


_GETCREDITPAYLISTREQUEST = _descriptor.Descriptor(
  name='GetCreditPayListRequest',
  full_name='credit_pay.GetCreditPayListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='credit_way', full_name='credit_pay.GetCreditPayListRequest.credit_way', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='credit_pay.GetCreditPayListRequest.branch_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_ids', full_name='credit_pay.GetCreditPayListRequest.company_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='credit_pay.GetCreditPayListRequest.status', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='credit_pay.GetCreditPayListRequest.page', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=727,
  serialized_end=914,
)


_GETCREDITPAYLISTRESPONSE = _descriptor.Descriptor(
  name='GetCreditPayListResponse',
  full_name='credit_pay.GetCreditPayListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='credit_pay.GetCreditPayListResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='credit_pay.GetCreditPayListResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=916,
  serialized_end=998,
)


_CREDITPAYLIST = _descriptor.Descriptor(
  name='CreditPayList',
  full_name='credit_pay.CreditPayList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='credit_pay_id', full_name='credit_pay.CreditPayList.credit_pay_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='credit_pay.CreditPayList.branch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_code', full_name='credit_pay.CreditPayList.branch_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='credit_pay.CreditPayList.branch_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_id', full_name='credit_pay.CreditPayList.company_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_name', full_name='credit_pay.CreditPayList.company_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='credit_pay.CreditPayList.limit', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_over', full_name='credit_pay.CreditPayList.is_over', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_over_limit', full_name='credit_pay.CreditPayList.max_over_limit', index=8,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='credit_pay.CreditPayList.updated_by', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='credit_pay.CreditPayList.created_by', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='credit_pay.CreditPayList.created_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='credit_pay.CreditPayList.updated_name', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='credit_pay.CreditPayList.status', index=13,
      number=14, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='credit_pay.CreditPayList.created', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='credit_pay.CreditPayList.updated', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1001,
  serialized_end=1414,
)


_GETCREDITPAYDETAILREQUEST = _descriptor.Descriptor(
  name='GetCreditPayDetailRequest',
  full_name='credit_pay.GetCreditPayDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='credit_pay_id', full_name='credit_pay.GetCreditPayDetailRequest.credit_pay_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1416,
  serialized_end=1466,
)


_GETCREDITPAYDETAILRESPONSE = _descriptor.Descriptor(
  name='GetCreditPayDetailResponse',
  full_name='credit_pay.GetCreditPayDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='credit_pay_id', full_name='credit_pay.GetCreditPayDetailResponse.credit_pay_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='credit_pay.GetCreditPayDetailResponse.branch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_code', full_name='credit_pay.GetCreditPayDetailResponse.branch_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='credit_pay.GetCreditPayDetailResponse.branch_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_id', full_name='credit_pay.GetCreditPayDetailResponse.company_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_code', full_name='credit_pay.GetCreditPayDetailResponse.company_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_name', full_name='credit_pay.GetCreditPayDetailResponse.company_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='credit_pay.GetCreditPayDetailResponse.limit', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_over', full_name='credit_pay.GetCreditPayDetailResponse.is_over', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_over_limit', full_name='credit_pay.GetCreditPayDetailResponse.max_over_limit', index=9,
      number=10, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='credit_pay.GetCreditPayDetailResponse.remark', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='credit_pay.GetCreditPayDetailResponse.updated_by', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='credit_pay.GetCreditPayDetailResponse.created_by', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='credit_pay.GetCreditPayDetailResponse.status', index=13,
      number=14, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='credit_way', full_name='credit_pay.GetCreditPayDetailResponse.credit_way', index=14,
      number=15, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='credit_pay.GetCreditPayDetailResponse.created', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='credit_pay.GetCreditPayDetailResponse.updated', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1469,
  serialized_end=1936,
)


_CREATEPAYRELATIONREQUEST = _descriptor.Descriptor(
  name='CreatePayRelationRequest',
  full_name='credit_pay.CreatePayRelationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='CompanyId', full_name='credit_pay.CreatePayRelationRequest.CompanyId', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='credit_pay.CreatePayRelationRequest.status', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='credit_pay.CreatePayRelationRequest.store_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1938,
  serialized_end=2049,
)


_GETPAYRELATIONLISTREQUEST = _descriptor.Descriptor(
  name='GetPayRelationListRequest',
  full_name='credit_pay.GetPayRelationListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='geo_region_id', full_name='credit_pay.GetPayRelationListRequest.geo_region_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_region_id', full_name='credit_pay.GetPayRelationListRequest.branch_region_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='credit_pay.GetPayRelationListRequest.store_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_ids', full_name='credit_pay.GetPayRelationListRequest.company_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='credit_pay.GetPayRelationListRequest.status', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='credit_pay.GetPayRelationListRequest.page', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2052,
  serialized_end=2247,
)


_GETPAYRELATIONLISTRESPONSE = _descriptor.Descriptor(
  name='GetPayRelationListResponse',
  full_name='credit_pay.GetPayRelationListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='credit_pay.GetPayRelationListResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='credit_pay.GetPayRelationListResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2249,
  serialized_end=2348,
)


_GETPAYRELATIONDETAILRESPONSE = _descriptor.Descriptor(
  name='GetPayRelationDetailResponse',
  full_name='credit_pay.GetPayRelationDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='relation_id', full_name='credit_pay.GetPayRelationDetailResponse.relation_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='credit_pay.GetPayRelationDetailResponse.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='credit_pay.GetPayRelationDetailResponse.store_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='credit_pay.GetPayRelationDetailResponse.store_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_id', full_name='credit_pay.GetPayRelationDetailResponse.company_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_name', full_name='credit_pay.GetPayRelationDetailResponse.company_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='credit_pay.GetPayRelationDetailResponse.updated_by', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='credit_pay.GetPayRelationDetailResponse.updated_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='credit_pay.GetPayRelationDetailResponse.created_by', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='credit_pay.GetPayRelationDetailResponse.created_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='credit_pay.GetPayRelationDetailResponse.status', index=10,
      number=11, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='credit_pay.GetPayRelationDetailResponse.created', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='credit_pay.GetPayRelationDetailResponse.updated', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2351,
  serialized_end=2723,
)


_GETPAYRELATIONDETAILREQUEST = _descriptor.Descriptor(
  name='GetPayRelationDetailRequest',
  full_name='credit_pay.GetPayRelationDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='relation_id', full_name='credit_pay.GetPayRelationDetailRequest.relation_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2725,
  serialized_end=2775,
)


_DELETEPAYRELATIONREQUEST = _descriptor.Descriptor(
  name='DeletePayRelationRequest',
  full_name='credit_pay.DeletePayRelationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='relation_id', full_name='credit_pay.DeletePayRelationRequest.relation_id', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2777,
  serialized_end=2824,
)


_CHANGEPAYRELATIONSTATUSREQUEST = _descriptor.Descriptor(
  name='ChangePayRelationStatusRequest',
  full_name='credit_pay.ChangePayRelationStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='relation_ids', full_name='credit_pay.ChangePayRelationStatusRequest.relation_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='credit_pay.ChangePayRelationStatusRequest.status', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2826,
  serialized_end=2927,
)


_GETLOGLISTREQUEST = _descriptor.Descriptor(
  name='GetLogListRequest',
  full_name='credit_pay.GetLogListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bus_name', full_name='credit_pay.GetLogListRequest.bus_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data_id', full_name='credit_pay.GetLogListRequest.data_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='credit_pay.GetLogListRequest.page', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2929,
  serialized_end=3015,
)


_GETLOGLISTRESPONSE = _descriptor.Descriptor(
  name='GetLogListResponse',
  full_name='credit_pay.GetLogListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='credit_pay.GetLogListResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='credit_pay.GetLogListResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3017,
  serialized_end=3087,
)


_LOGLIST = _descriptor.Descriptor(
  name='LogList',
  full_name='credit_pay.LogList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='log_id', full_name='credit_pay.LogList.log_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='log_data', full_name='credit_pay.LogList.log_data', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='operation_type', full_name='credit_pay.LogList.operation_type', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='credit_pay.LogList.updated_by', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='credit_pay.LogList.updated_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='credit_pay.LogList.updated', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3090,
  serialized_end=3295,
)


_LOGINFO = _descriptor.Descriptor(
  name='LogInfo',
  full_name='credit_pay.LogInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='field_name', full_name='credit_pay.LogInfo.field_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='old_val', full_name='credit_pay.LogInfo.old_val', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='new_val', full_name='credit_pay.LogInfo.new_val', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_change', full_name='credit_pay.LogInfo.is_change', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3297,
  serialized_end=3379,
)


_PAYNOTIFYREQUEST = _descriptor.Descriptor(
  name='PayNotifyRequest',
  full_name='credit_pay.PayNotifyRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_no', full_name='credit_pay.PayNotifyRequest.order_no', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tp_transaction_no', full_name='credit_pay.PayNotifyRequest.tp_transaction_no', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pay_status', full_name='credit_pay.PayNotifyRequest.pay_status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='message', full_name='credit_pay.PayNotifyRequest.message', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pay_channel', full_name='credit_pay.PayNotifyRequest.pay_channel', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pay_user_id', full_name='credit_pay.PayNotifyRequest.pay_user_id', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pay_user_name', full_name='credit_pay.PayNotifyRequest.pay_user_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3382,
  serialized_end=3547,
)


_PAYREQUEST = _descriptor.Descriptor(
  name='PayRequest',
  full_name='credit_pay.PayRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_no', full_name='credit_pay.PayRequest.order_no', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3549,
  serialized_end=3579,
)


_QUERYPAYSTATUSREQUEST = _descriptor.Descriptor(
  name='QueryPayStatusRequest',
  full_name='credit_pay.QueryPayStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='channel', full_name='credit_pay.QueryPayStatusRequest.channel', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transaction_id', full_name='credit_pay.QueryPayStatusRequest.transaction_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_no', full_name='credit_pay.QueryPayStatusRequest.order_no', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3581,
  serialized_end=3663,
)


_QUERYPAYSTATUSRESPONSE = _descriptor.Descriptor(
  name='QueryPayStatusResponse',
  full_name='credit_pay.QueryPayStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='error_code', full_name='credit_pay.QueryPayStatusResponse.error_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error_message', full_name='credit_pay.QueryPayStatusResponse.error_message', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transaction_state', full_name='credit_pay.QueryPayStatusResponse.transaction_state', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='real_amount', full_name='credit_pay.QueryPayStatusResponse.real_amount', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tp_transaction_id', full_name='credit_pay.QueryPayStatusResponse.tp_transaction_id', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pay_channel', full_name='credit_pay.QueryPayStatusResponse.pay_channel', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3666,
  serialized_end=3829,
)


_GETPAYPARAMSREQUEST = _descriptor.Descriptor(
  name='GetPayParamsRequest',
  full_name='credit_pay.GetPayParamsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_no', full_name='credit_pay.GetPayParamsRequest.order_no', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='channel', full_name='credit_pay.GetPayParamsRequest.channel', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3831,
  serialized_end=3887,
)


_GETPAYPARAMSRESPONSE_PACKSTRENTRY = _descriptor.Descriptor(
  name='PackStrEntry',
  full_name='credit_pay.GetPayParamsResponse.PackStrEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='credit_pay.GetPayParamsResponse.PackStrEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='credit_pay.GetPayParamsResponse.PackStrEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4143,
  serialized_end=4189,
)

_GETPAYPARAMSRESPONSE = _descriptor.Descriptor(
  name='GetPayParamsResponse',
  full_name='credit_pay.GetPayParamsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='error_code', full_name='credit_pay.GetPayParamsResponse.error_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error_message', full_name='credit_pay.GetPayParamsResponse.error_message', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ticket_id', full_name='credit_pay.GetPayParamsResponse.ticket_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='credit_pay.GetPayParamsResponse.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='prepay_id', full_name='credit_pay.GetPayParamsResponse.prepay_id', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='prepay_sign', full_name='credit_pay.GetPayParamsResponse.prepay_sign', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tp_transaction_id', full_name='credit_pay.GetPayParamsResponse.tp_transaction_id', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pack_str', full_name='credit_pay.GetPayParamsResponse.pack_str', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sign_type', full_name='credit_pay.GetPayParamsResponse.sign_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_GETPAYPARAMSRESPONSE_PACKSTRENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3890,
  serialized_end=4189,
)


_FORCEPAYREQUEST = _descriptor.Descriptor(
  name='ForcePayRequest',
  full_name='credit_pay.ForcePayRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_no', full_name='credit_pay.ForcePayRequest.order_no', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4191,
  serialized_end=4226,
)


_GETCHANNELSREQUEST = _descriptor.Descriptor(
  name='GetChannelsRequest',
  full_name='credit_pay.GetChannelsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='action', full_name='credit_pay.GetChannelsRequest.action', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='credit_pay.GetChannelsRequest.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='platform', full_name='credit_pay.GetChannelsRequest.platform', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='credit_pay.GetChannelsRequest.order_type_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='list_binding_section', full_name='credit_pay.GetChannelsRequest.list_binding_section', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4229,
  serialized_end=4412,
)


_LISTBINDINGSECTION = _descriptor.Descriptor(
  name='ListBindingSection',
  full_name='credit_pay.ListBindingSection',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='channel_category', full_name='credit_pay.ListBindingSection.channel_category', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='enabled', full_name='credit_pay.ListBindingSection.enabled', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='channel_labels', full_name='credit_pay.ListBindingSection.channel_labels', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='business_code', full_name='credit_pay.ListBindingSection.business_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4414,
  serialized_end=4524,
)


_GETCHANNELSRESPONSE = _descriptor.Descriptor(
  name='GetChannelsResponse',
  full_name='credit_pay.GetChannelsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='error_code', full_name='credit_pay.GetChannelsResponse.error_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error_message', full_name='credit_pay.GetChannelsResponse.error_message', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='binding_list_section', full_name='credit_pay.GetChannelsResponse.binding_list_section', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4526,
  serialized_end=4652,
)


_BINDINGLISTSECTION = _descriptor.Descriptor(
  name='BindingListSection',
  full_name='credit_pay.BindingListSection',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='binding_item', full_name='credit_pay.BindingListSection.binding_item', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4654,
  serialized_end=4721,
)


_BINDINGITEM = _descriptor.Descriptor(
  name='BindingItem',
  full_name='credit_pay.BindingItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='channel_code', full_name='credit_pay.BindingItem.channel_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='channel_name', full_name='credit_pay.BindingItem.channel_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='business_code', full_name='credit_pay.BindingItem.business_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='channel_type', full_name='credit_pay.BindingItem.channel_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='channel_category', full_name='credit_pay.BindingItem.channel_category', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='channel_sub_category', full_name='credit_pay.BindingItem.channel_sub_category', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='channel_labels', full_name='credit_pay.BindingItem.channel_labels', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='enabled', full_name='credit_pay.BindingItem.enabled', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4724,
  serialized_end=4923,
)


_GETVOUCHERSREQUEST = _descriptor.Descriptor(
  name='GetVouchersRequest',
  full_name='credit_pay.GetVouchersRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='credit_pay.GetVouchersRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='credit_pay.GetVouchersRequest.order_type_id', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='voucher_status', full_name='credit_pay.GetVouchersRequest.voucher_status', index=2,
      number=3, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='voucher_name', full_name='credit_pay.GetVouchersRequest.voucher_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='voucher_code', full_name='credit_pay.GetVouchersRequest.voucher_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='credit_pay.GetVouchersRequest.page', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4926,
  serialized_end=5114,
)


_GETVOUCHERSRESPONSE = _descriptor.Descriptor(
  name='GetVouchersResponse',
  full_name='credit_pay.GetVouchersResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='credit_pay.GetVouchersResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='credit_pay.GetVouchersResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5116,
  serialized_end=5194,
)


_VOUCHERSDETAIL = _descriptor.Descriptor(
  name='VouchersDetail',
  full_name='credit_pay.VouchersDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='credit_pay.VouchersDetail.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='credit_pay.VouchersDetail.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='credit_pay.VouchersDetail.store_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='voucher_id', full_name='credit_pay.VouchersDetail.voucher_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='voucher_code', full_name='credit_pay.VouchersDetail.voucher_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='voucher_name', full_name='credit_pay.VouchersDetail.voucher_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='credit_pay.VouchersDetail.start_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='credit_pay.VouchersDetail.end_date', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='credit_pay.VouchersDetail.order_type', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='credit_pay.VouchersDetail.limit', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='credit_pay.VouchersDetail.remark', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='voucher_status', full_name='credit_pay.VouchersDetail.voucher_status', index=11,
      number=12, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='credit_pay.VouchersDetail.updated_by', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='credit_pay.VouchersDetail.created_by', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='credit_pay.VouchersDetail.created_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='credit_pay.VouchersDetail.updated_name', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='credit_pay.VouchersDetail.created', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='credit_pay.VouchersDetail.updated', index=17,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5197,
  serialized_end=5724,
)


_ORDERTYPEINFO = _descriptor.Descriptor(
  name='OrderTypeInfo',
  full_name='credit_pay.OrderTypeInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='credit_pay.OrderTypeInfo.order_type_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_code', full_name='credit_pay.OrderTypeInfo.order_type_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_name', full_name='credit_pay.OrderTypeInfo.order_type_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5726,
  serialized_end=5814,
)


_VOUCHERPAYREQUEST = _descriptor.Descriptor(
  name='VoucherPayRequest',
  full_name='credit_pay.VoucherPayRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_no', full_name='credit_pay.VoucherPayRequest.order_no', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='voucher_id', full_name='credit_pay.VoucherPayRequest.voucher_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5816,
  serialized_end=5873,
)


_REFUNDREQUEST = _descriptor.Descriptor(
  name='RefundRequest',
  full_name='credit_pay.RefundRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='refund_id', full_name='credit_pay.RefundRequest.refund_id', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5875,
  serialized_end=5909,
)


_GETTRANSACTIONLOGLISTREQUEST = _descriptor.Descriptor(
  name='GetTransactionLogListRequest',
  full_name='credit_pay.GetTransactionLogListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_id', full_name='credit_pay.GetTransactionLogListRequest.account_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account_type', full_name='credit_pay.GetTransactionLogListRequest.account_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='credit_pay.GetTransactionLogListRequest.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='credit_pay.GetTransactionLogListRequest.page', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5911,
  serialized_end=6031,
)


_GETTRANSACTIONLOGLISTRESPONSE = _descriptor.Descriptor(
  name='GetTransactionLogListResponse',
  full_name='credit_pay.GetTransactionLogListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='credit_pay.GetTransactionLogListResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='credit_pay.GetTransactionLogListResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6033,
  serialized_end=6127,
)


_TRANSACTIONLOGDETAIL = _descriptor.Descriptor(
  name='TransactionLogDetail',
  full_name='credit_pay.TransactionLogDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='transaction_account', full_name='credit_pay.TransactionLogDetail.transaction_account', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_no', full_name='credit_pay.TransactionLogDetail.order_no', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='credit_pay.TransactionLogDetail.order_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price', full_name='credit_pay.TransactionLogDetail.sum_price', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transaction_amount', full_name='credit_pay.TransactionLogDetail.transaction_amount', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remain_amount', full_name='credit_pay.TransactionLogDetail.remain_amount', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transaction_time', full_name='credit_pay.TransactionLogDetail.transaction_time', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='credit_pay.TransactionLogDetail.status', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transaction_type', full_name='credit_pay.TransactionLogDetail.transaction_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6130,
  serialized_end=6385,
)


_GETCREDITPAYLISTMINIWXREQUEST = _descriptor.Descriptor(
  name='GetCreditPayListMiniWXRequest',
  full_name='credit_pay.GetCreditPayListMiniWXRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='credit_pay.GetCreditPayListMiniWXRequest.branch_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='credit_way', full_name='credit_pay.GetCreditPayListMiniWXRequest.credit_way', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='credit_pay.GetCreditPayListMiniWXRequest.status', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='credit_pay.GetCreditPayListMiniWXRequest.page', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6388,
  serialized_end=6560,
)


_GETCREDITPAYLISTMINIWXRESPONSE = _descriptor.Descriptor(
  name='GetCreditPayListMiniWXResponse',
  full_name='credit_pay.GetCreditPayListMiniWXResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='credit_pay.GetCreditPayListMiniWXResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='credit_pay.GetCreditPayListMiniWXResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6562,
  serialized_end=6658,
)


_CREDITPAYMINIWXDETAIL = _descriptor.Descriptor(
  name='CreditPayMiniWXDetail',
  full_name='credit_pay.CreditPayMiniWXDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_code', full_name='credit_pay.CreditPayMiniWXDetail.branch_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='credit_pay.CreditPayMiniWXDetail.branch_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='boh_account_amount', full_name='credit_pay.CreditPayMiniWXDetail.boh_account_amount', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='last_sync_time', full_name='credit_pay.CreditPayMiniWXDetail.last_sync_time', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='credit_pay.CreditPayMiniWXDetail.status', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extend_accounts', full_name='credit_pay.CreditPayMiniWXDetail.extend_accounts', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6661,
  serialized_end=6912,
)


_THIRDPARTYCREDITPAYDETAIL = _descriptor.Descriptor(
  name='ThirdPartyCreditPayDetail',
  full_name='credit_pay.ThirdPartyCreditPayDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_name', full_name='credit_pay.ThirdPartyCreditPayDetail.account_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='credit_pay.ThirdPartyCreditPayDetail.amount', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6914,
  serialized_end=6979,
)


_ADJUSTPAYACCOUNTREQUEST = _descriptor.Descriptor(
  name='AdjustPayAccountRequest',
  full_name='credit_pay.AdjustPayAccountRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='credit_pay.AdjustPayAccountRequest.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='credit_way_type', full_name='credit_pay.AdjustPayAccountRequest.credit_way_type', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6981,
  serialized_end=7108,
)


_ADJUSTPAYACCOUNTDETAIL = _descriptor.Descriptor(
  name='AdjustPayAccountDetail',
  full_name='credit_pay.AdjustPayAccountDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='credit_pay.AdjustPayAccountDetail.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_id', full_name='credit_pay.AdjustPayAccountDetail.company_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='credit_pay.AdjustPayAccountDetail.limit', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='credit_pay.AdjustPayAccountDetail.request_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7110,
  serialized_end=7208,
)


_REFRESHCREDITPAYREQUEST = _descriptor.Descriptor(
  name='RefreshCreditPayRequest',
  full_name='credit_pay.RefreshCreditPayRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_code', full_name='credit_pay.RefreshCreditPayRequest.store_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7210,
  serialized_end=7255,
)


_COMMONRESPONSE = _descriptor.Descriptor(
  name='CommonResponse',
  full_name='credit_pay.CommonResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='credit_pay.CommonResponse.code', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='message', full_name='credit_pay.CommonResponse.message', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7257,
  serialized_end=7304,
)


_PAGE = _descriptor.Descriptor(
  name='Page',
  full_name='credit_pay.Page',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order', full_name='credit_pay.Page.order', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='credit_pay.Page.sort', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='credit_pay.Page.limit', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='credit_pay.Page.offset', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7306,
  serialized_end=7372,
)

_CREATECREDITPAYREQUEST.fields_by_name['credit_way'].enum_type = _CREDITWAYTYPE
_CREATECREDITPAYREQUEST.fields_by_name['credits'].message_type = _BRANCHCREDIT
_BRANCHCREDIT.fields_by_name['status'].enum_type = _CREDITSTATUS
_UPDATECREDITPAYREQUEST.fields_by_name['status'].enum_type = _CREDITSTATUS
_CLOSECREDITPAYREQUEST.fields_by_name['status'].enum_type = _CREDITSTATUS
_GETCREDITPAYLISTREQUEST.fields_by_name['credit_way'].enum_type = _CREDITWAYTYPE
_GETCREDITPAYLISTREQUEST.fields_by_name['status'].enum_type = _CREDITSTATUS
_GETCREDITPAYLISTREQUEST.fields_by_name['page'].message_type = _PAGE
_GETCREDITPAYLISTRESPONSE.fields_by_name['rows'].message_type = _CREDITPAYLIST
_CREDITPAYLIST.fields_by_name['status'].enum_type = _CREDITSTATUS
_CREDITPAYLIST.fields_by_name['created'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREDITPAYLIST.fields_by_name['updated'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETCREDITPAYDETAILRESPONSE.fields_by_name['status'].enum_type = _CREDITSTATUS
_GETCREDITPAYDETAILRESPONSE.fields_by_name['credit_way'].enum_type = _CREDITWAYTYPE
_GETCREDITPAYDETAILRESPONSE.fields_by_name['created'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETCREDITPAYDETAILRESPONSE.fields_by_name['updated'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEPAYRELATIONREQUEST.fields_by_name['status'].enum_type = _PAYRELATIONSTATUS
_GETPAYRELATIONLISTREQUEST.fields_by_name['status'].enum_type = _PAYRELATIONSTATUS
_GETPAYRELATIONLISTREQUEST.fields_by_name['page'].message_type = _PAGE
_GETPAYRELATIONLISTRESPONSE.fields_by_name['rows'].message_type = _GETPAYRELATIONDETAILRESPONSE
_GETPAYRELATIONDETAILRESPONSE.fields_by_name['status'].enum_type = _PAYRELATIONSTATUS
_GETPAYRELATIONDETAILRESPONSE.fields_by_name['created'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPAYRELATIONDETAILRESPONSE.fields_by_name['updated'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CHANGEPAYRELATIONSTATUSREQUEST.fields_by_name['status'].enum_type = _PAYRELATIONSTATUS
_GETLOGLISTREQUEST.fields_by_name['page'].message_type = _PAGE
_GETLOGLISTRESPONSE.fields_by_name['rows'].message_type = _LOGLIST
_LOGLIST.fields_by_name['log_data'].message_type = _LOGINFO
_LOGLIST.fields_by_name['operation_type'].enum_type = _LOGOPERATIONTYPE
_LOGLIST.fields_by_name['updated'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPAYPARAMSRESPONSE_PACKSTRENTRY.containing_type = _GETPAYPARAMSRESPONSE
_GETPAYPARAMSRESPONSE.fields_by_name['pack_str'].message_type = _GETPAYPARAMSRESPONSE_PACKSTRENTRY
_GETCHANNELSREQUEST.fields_by_name['platform'].enum_type = _PLATFORMTYPE
_GETCHANNELSREQUEST.fields_by_name['list_binding_section'].message_type = _LISTBINDINGSECTION
_GETCHANNELSRESPONSE.fields_by_name['binding_list_section'].message_type = _BINDINGLISTSECTION
_BINDINGLISTSECTION.fields_by_name['binding_item'].message_type = _BINDINGITEM
_GETVOUCHERSREQUEST.fields_by_name['voucher_status'].enum_type = _VOUCHERSTATUS
_GETVOUCHERSREQUEST.fields_by_name['page'].message_type = _PAGE
_GETVOUCHERSRESPONSE.fields_by_name['rows'].message_type = _VOUCHERSDETAIL
_VOUCHERSDETAIL.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_VOUCHERSDETAIL.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_VOUCHERSDETAIL.fields_by_name['order_type'].message_type = _ORDERTYPEINFO
_VOUCHERSDETAIL.fields_by_name['voucher_status'].enum_type = _VOUCHERSTATUS
_VOUCHERSDETAIL.fields_by_name['created'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_VOUCHERSDETAIL.fields_by_name['updated'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETTRANSACTIONLOGLISTREQUEST.fields_by_name['page'].message_type = _PAGE
_GETTRANSACTIONLOGLISTRESPONSE.fields_by_name['rows'].message_type = _TRANSACTIONLOGDETAIL
_TRANSACTIONLOGDETAIL.fields_by_name['transaction_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETCREDITPAYLISTMINIWXREQUEST.fields_by_name['credit_way'].enum_type = _CREDITWAYTYPE
_GETCREDITPAYLISTMINIWXREQUEST.fields_by_name['status'].enum_type = _CREDITSTATUS
_GETCREDITPAYLISTMINIWXREQUEST.fields_by_name['page'].message_type = _PAGE
_GETCREDITPAYLISTMINIWXRESPONSE.fields_by_name['rows'].message_type = _CREDITPAYMINIWXDETAIL
_CREDITPAYMINIWXDETAIL.fields_by_name['last_sync_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREDITPAYMINIWXDETAIL.fields_by_name['status'].enum_type = _CREDITSTATUS
_CREDITPAYMINIWXDETAIL.fields_by_name['extend_accounts'].message_type = _THIRDPARTYCREDITPAYDETAIL
_ADJUSTPAYACCOUNTREQUEST.fields_by_name['rows'].message_type = _ADJUSTPAYACCOUNTDETAIL
_ADJUSTPAYACCOUNTREQUEST.fields_by_name['credit_way_type'].enum_type = _CREDITWAYTYPE
DESCRIPTOR.message_types_by_name['PingRequest'] = _PINGREQUEST
DESCRIPTOR.message_types_by_name['PingResponse'] = _PINGRESPONSE
DESCRIPTOR.message_types_by_name['CreateCreditPayRequest'] = _CREATECREDITPAYREQUEST
DESCRIPTOR.message_types_by_name['BranchCredit'] = _BRANCHCREDIT
DESCRIPTOR.message_types_by_name['UpdateCreditPayRequest'] = _UPDATECREDITPAYREQUEST
DESCRIPTOR.message_types_by_name['CloseCreditPayRequest'] = _CLOSECREDITPAYREQUEST
DESCRIPTOR.message_types_by_name['GetCreditPayListRequest'] = _GETCREDITPAYLISTREQUEST
DESCRIPTOR.message_types_by_name['GetCreditPayListResponse'] = _GETCREDITPAYLISTRESPONSE
DESCRIPTOR.message_types_by_name['CreditPayList'] = _CREDITPAYLIST
DESCRIPTOR.message_types_by_name['GetCreditPayDetailRequest'] = _GETCREDITPAYDETAILREQUEST
DESCRIPTOR.message_types_by_name['GetCreditPayDetailResponse'] = _GETCREDITPAYDETAILRESPONSE
DESCRIPTOR.message_types_by_name['CreatePayRelationRequest'] = _CREATEPAYRELATIONREQUEST
DESCRIPTOR.message_types_by_name['GetPayRelationListRequest'] = _GETPAYRELATIONLISTREQUEST
DESCRIPTOR.message_types_by_name['GetPayRelationListResponse'] = _GETPAYRELATIONLISTRESPONSE
DESCRIPTOR.message_types_by_name['GetPayRelationDetailResponse'] = _GETPAYRELATIONDETAILRESPONSE
DESCRIPTOR.message_types_by_name['GetPayRelationDetailRequest'] = _GETPAYRELATIONDETAILREQUEST
DESCRIPTOR.message_types_by_name['DeletePayRelationRequest'] = _DELETEPAYRELATIONREQUEST
DESCRIPTOR.message_types_by_name['ChangePayRelationStatusRequest'] = _CHANGEPAYRELATIONSTATUSREQUEST
DESCRIPTOR.message_types_by_name['GetLogListRequest'] = _GETLOGLISTREQUEST
DESCRIPTOR.message_types_by_name['GetLogListResponse'] = _GETLOGLISTRESPONSE
DESCRIPTOR.message_types_by_name['LogList'] = _LOGLIST
DESCRIPTOR.message_types_by_name['LogInfo'] = _LOGINFO
DESCRIPTOR.message_types_by_name['PayNotifyRequest'] = _PAYNOTIFYREQUEST
DESCRIPTOR.message_types_by_name['PayRequest'] = _PAYREQUEST
DESCRIPTOR.message_types_by_name['QueryPayStatusRequest'] = _QUERYPAYSTATUSREQUEST
DESCRIPTOR.message_types_by_name['QueryPayStatusResponse'] = _QUERYPAYSTATUSRESPONSE
DESCRIPTOR.message_types_by_name['GetPayParamsRequest'] = _GETPAYPARAMSREQUEST
DESCRIPTOR.message_types_by_name['GetPayParamsResponse'] = _GETPAYPARAMSRESPONSE
DESCRIPTOR.message_types_by_name['ForcePayRequest'] = _FORCEPAYREQUEST
DESCRIPTOR.message_types_by_name['GetChannelsRequest'] = _GETCHANNELSREQUEST
DESCRIPTOR.message_types_by_name['ListBindingSection'] = _LISTBINDINGSECTION
DESCRIPTOR.message_types_by_name['GetChannelsResponse'] = _GETCHANNELSRESPONSE
DESCRIPTOR.message_types_by_name['BindingListSection'] = _BINDINGLISTSECTION
DESCRIPTOR.message_types_by_name['BindingItem'] = _BINDINGITEM
DESCRIPTOR.message_types_by_name['GetVouchersRequest'] = _GETVOUCHERSREQUEST
DESCRIPTOR.message_types_by_name['GetVouchersResponse'] = _GETVOUCHERSRESPONSE
DESCRIPTOR.message_types_by_name['VouchersDetail'] = _VOUCHERSDETAIL
DESCRIPTOR.message_types_by_name['OrderTypeInfo'] = _ORDERTYPEINFO
DESCRIPTOR.message_types_by_name['VoucherPayRequest'] = _VOUCHERPAYREQUEST
DESCRIPTOR.message_types_by_name['RefundRequest'] = _REFUNDREQUEST
DESCRIPTOR.message_types_by_name['GetTransactionLogListRequest'] = _GETTRANSACTIONLOGLISTREQUEST
DESCRIPTOR.message_types_by_name['GetTransactionLogListResponse'] = _GETTRANSACTIONLOGLISTRESPONSE
DESCRIPTOR.message_types_by_name['TransactionLogDetail'] = _TRANSACTIONLOGDETAIL
DESCRIPTOR.message_types_by_name['GetCreditPayListMiniWXRequest'] = _GETCREDITPAYLISTMINIWXREQUEST
DESCRIPTOR.message_types_by_name['GetCreditPayListMiniWXResponse'] = _GETCREDITPAYLISTMINIWXRESPONSE
DESCRIPTOR.message_types_by_name['CreditPayMiniWXDetail'] = _CREDITPAYMINIWXDETAIL
DESCRIPTOR.message_types_by_name['ThirdPartyCreditPayDetail'] = _THIRDPARTYCREDITPAYDETAIL
DESCRIPTOR.message_types_by_name['AdjustPayAccountRequest'] = _ADJUSTPAYACCOUNTREQUEST
DESCRIPTOR.message_types_by_name['AdjustPayAccountDetail'] = _ADJUSTPAYACCOUNTDETAIL
DESCRIPTOR.message_types_by_name['RefreshCreditPayRequest'] = _REFRESHCREDITPAYREQUEST
DESCRIPTOR.message_types_by_name['CommonResponse'] = _COMMONRESPONSE
DESCRIPTOR.message_types_by_name['Page'] = _PAGE
DESCRIPTOR.enum_types_by_name['CreditWayType'] = _CREDITWAYTYPE
DESCRIPTOR.enum_types_by_name['CreditStatus'] = _CREDITSTATUS
DESCRIPTOR.enum_types_by_name['PayRelationStatus'] = _PAYRELATIONSTATUS
DESCRIPTOR.enum_types_by_name['LogOperationType'] = _LOGOPERATIONTYPE
DESCRIPTOR.enum_types_by_name['PlatformType'] = _PLATFORMTYPE
DESCRIPTOR.enum_types_by_name['VoucherStatus'] = _VOUCHERSTATUS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

PingRequest = _reflection.GeneratedProtocolMessageType('PingRequest', (_message.Message,), dict(
  DESCRIPTOR = _PINGREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.PingRequest)
  ))
_sym_db.RegisterMessage(PingRequest)

PingResponse = _reflection.GeneratedProtocolMessageType('PingResponse', (_message.Message,), dict(
  DESCRIPTOR = _PINGRESPONSE,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.PingResponse)
  ))
_sym_db.RegisterMessage(PingResponse)

CreateCreditPayRequest = _reflection.GeneratedProtocolMessageType('CreateCreditPayRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATECREDITPAYREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.CreateCreditPayRequest)
  ))
_sym_db.RegisterMessage(CreateCreditPayRequest)

BranchCredit = _reflection.GeneratedProtocolMessageType('BranchCredit', (_message.Message,), dict(
  DESCRIPTOR = _BRANCHCREDIT,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.BranchCredit)
  ))
_sym_db.RegisterMessage(BranchCredit)

UpdateCreditPayRequest = _reflection.GeneratedProtocolMessageType('UpdateCreditPayRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATECREDITPAYREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.UpdateCreditPayRequest)
  ))
_sym_db.RegisterMessage(UpdateCreditPayRequest)

CloseCreditPayRequest = _reflection.GeneratedProtocolMessageType('CloseCreditPayRequest', (_message.Message,), dict(
  DESCRIPTOR = _CLOSECREDITPAYREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.CloseCreditPayRequest)
  ))
_sym_db.RegisterMessage(CloseCreditPayRequest)

GetCreditPayListRequest = _reflection.GeneratedProtocolMessageType('GetCreditPayListRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETCREDITPAYLISTREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetCreditPayListRequest)
  ))
_sym_db.RegisterMessage(GetCreditPayListRequest)

GetCreditPayListResponse = _reflection.GeneratedProtocolMessageType('GetCreditPayListResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETCREDITPAYLISTRESPONSE,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetCreditPayListResponse)
  ))
_sym_db.RegisterMessage(GetCreditPayListResponse)

CreditPayList = _reflection.GeneratedProtocolMessageType('CreditPayList', (_message.Message,), dict(
  DESCRIPTOR = _CREDITPAYLIST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.CreditPayList)
  ))
_sym_db.RegisterMessage(CreditPayList)

GetCreditPayDetailRequest = _reflection.GeneratedProtocolMessageType('GetCreditPayDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETCREDITPAYDETAILREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetCreditPayDetailRequest)
  ))
_sym_db.RegisterMessage(GetCreditPayDetailRequest)

GetCreditPayDetailResponse = _reflection.GeneratedProtocolMessageType('GetCreditPayDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETCREDITPAYDETAILRESPONSE,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetCreditPayDetailResponse)
  ))
_sym_db.RegisterMessage(GetCreditPayDetailResponse)

CreatePayRelationRequest = _reflection.GeneratedProtocolMessageType('CreatePayRelationRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPAYRELATIONREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.CreatePayRelationRequest)
  ))
_sym_db.RegisterMessage(CreatePayRelationRequest)

GetPayRelationListRequest = _reflection.GeneratedProtocolMessageType('GetPayRelationListRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPAYRELATIONLISTREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetPayRelationListRequest)
  ))
_sym_db.RegisterMessage(GetPayRelationListRequest)

GetPayRelationListResponse = _reflection.GeneratedProtocolMessageType('GetPayRelationListResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPAYRELATIONLISTRESPONSE,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetPayRelationListResponse)
  ))
_sym_db.RegisterMessage(GetPayRelationListResponse)

GetPayRelationDetailResponse = _reflection.GeneratedProtocolMessageType('GetPayRelationDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPAYRELATIONDETAILRESPONSE,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetPayRelationDetailResponse)
  ))
_sym_db.RegisterMessage(GetPayRelationDetailResponse)

GetPayRelationDetailRequest = _reflection.GeneratedProtocolMessageType('GetPayRelationDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPAYRELATIONDETAILREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetPayRelationDetailRequest)
  ))
_sym_db.RegisterMessage(GetPayRelationDetailRequest)

DeletePayRelationRequest = _reflection.GeneratedProtocolMessageType('DeletePayRelationRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEPAYRELATIONREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.DeletePayRelationRequest)
  ))
_sym_db.RegisterMessage(DeletePayRelationRequest)

ChangePayRelationStatusRequest = _reflection.GeneratedProtocolMessageType('ChangePayRelationStatusRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHANGEPAYRELATIONSTATUSREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.ChangePayRelationStatusRequest)
  ))
_sym_db.RegisterMessage(ChangePayRelationStatusRequest)

GetLogListRequest = _reflection.GeneratedProtocolMessageType('GetLogListRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETLOGLISTREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetLogListRequest)
  ))
_sym_db.RegisterMessage(GetLogListRequest)

GetLogListResponse = _reflection.GeneratedProtocolMessageType('GetLogListResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETLOGLISTRESPONSE,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetLogListResponse)
  ))
_sym_db.RegisterMessage(GetLogListResponse)

LogList = _reflection.GeneratedProtocolMessageType('LogList', (_message.Message,), dict(
  DESCRIPTOR = _LOGLIST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.LogList)
  ))
_sym_db.RegisterMessage(LogList)

LogInfo = _reflection.GeneratedProtocolMessageType('LogInfo', (_message.Message,), dict(
  DESCRIPTOR = _LOGINFO,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.LogInfo)
  ))
_sym_db.RegisterMessage(LogInfo)

PayNotifyRequest = _reflection.GeneratedProtocolMessageType('PayNotifyRequest', (_message.Message,), dict(
  DESCRIPTOR = _PAYNOTIFYREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.PayNotifyRequest)
  ))
_sym_db.RegisterMessage(PayNotifyRequest)

PayRequest = _reflection.GeneratedProtocolMessageType('PayRequest', (_message.Message,), dict(
  DESCRIPTOR = _PAYREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.PayRequest)
  ))
_sym_db.RegisterMessage(PayRequest)

QueryPayStatusRequest = _reflection.GeneratedProtocolMessageType('QueryPayStatusRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYPAYSTATUSREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.QueryPayStatusRequest)
  ))
_sym_db.RegisterMessage(QueryPayStatusRequest)

QueryPayStatusResponse = _reflection.GeneratedProtocolMessageType('QueryPayStatusResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYPAYSTATUSRESPONSE,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.QueryPayStatusResponse)
  ))
_sym_db.RegisterMessage(QueryPayStatusResponse)

GetPayParamsRequest = _reflection.GeneratedProtocolMessageType('GetPayParamsRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPAYPARAMSREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetPayParamsRequest)
  ))
_sym_db.RegisterMessage(GetPayParamsRequest)

GetPayParamsResponse = _reflection.GeneratedProtocolMessageType('GetPayParamsResponse', (_message.Message,), dict(

  PackStrEntry = _reflection.GeneratedProtocolMessageType('PackStrEntry', (_message.Message,), dict(
    DESCRIPTOR = _GETPAYPARAMSRESPONSE_PACKSTRENTRY,
    __module__ = 'credit_pay.credit_pay_pb2'
    # @@protoc_insertion_point(class_scope:credit_pay.GetPayParamsResponse.PackStrEntry)
    ))
  ,
  DESCRIPTOR = _GETPAYPARAMSRESPONSE,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetPayParamsResponse)
  ))
_sym_db.RegisterMessage(GetPayParamsResponse)
_sym_db.RegisterMessage(GetPayParamsResponse.PackStrEntry)

ForcePayRequest = _reflection.GeneratedProtocolMessageType('ForcePayRequest', (_message.Message,), dict(
  DESCRIPTOR = _FORCEPAYREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.ForcePayRequest)
  ))
_sym_db.RegisterMessage(ForcePayRequest)

GetChannelsRequest = _reflection.GeneratedProtocolMessageType('GetChannelsRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETCHANNELSREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetChannelsRequest)
  ))
_sym_db.RegisterMessage(GetChannelsRequest)

ListBindingSection = _reflection.GeneratedProtocolMessageType('ListBindingSection', (_message.Message,), dict(
  DESCRIPTOR = _LISTBINDINGSECTION,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.ListBindingSection)
  ))
_sym_db.RegisterMessage(ListBindingSection)

GetChannelsResponse = _reflection.GeneratedProtocolMessageType('GetChannelsResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETCHANNELSRESPONSE,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetChannelsResponse)
  ))
_sym_db.RegisterMessage(GetChannelsResponse)

BindingListSection = _reflection.GeneratedProtocolMessageType('BindingListSection', (_message.Message,), dict(
  DESCRIPTOR = _BINDINGLISTSECTION,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.BindingListSection)
  ))
_sym_db.RegisterMessage(BindingListSection)

BindingItem = _reflection.GeneratedProtocolMessageType('BindingItem', (_message.Message,), dict(
  DESCRIPTOR = _BINDINGITEM,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.BindingItem)
  ))
_sym_db.RegisterMessage(BindingItem)

GetVouchersRequest = _reflection.GeneratedProtocolMessageType('GetVouchersRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETVOUCHERSREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetVouchersRequest)
  ))
_sym_db.RegisterMessage(GetVouchersRequest)

GetVouchersResponse = _reflection.GeneratedProtocolMessageType('GetVouchersResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETVOUCHERSRESPONSE,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetVouchersResponse)
  ))
_sym_db.RegisterMessage(GetVouchersResponse)

VouchersDetail = _reflection.GeneratedProtocolMessageType('VouchersDetail', (_message.Message,), dict(
  DESCRIPTOR = _VOUCHERSDETAIL,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.VouchersDetail)
  ))
_sym_db.RegisterMessage(VouchersDetail)

OrderTypeInfo = _reflection.GeneratedProtocolMessageType('OrderTypeInfo', (_message.Message,), dict(
  DESCRIPTOR = _ORDERTYPEINFO,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.OrderTypeInfo)
  ))
_sym_db.RegisterMessage(OrderTypeInfo)

VoucherPayRequest = _reflection.GeneratedProtocolMessageType('VoucherPayRequest', (_message.Message,), dict(
  DESCRIPTOR = _VOUCHERPAYREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.VoucherPayRequest)
  ))
_sym_db.RegisterMessage(VoucherPayRequest)

RefundRequest = _reflection.GeneratedProtocolMessageType('RefundRequest', (_message.Message,), dict(
  DESCRIPTOR = _REFUNDREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.RefundRequest)
  ))
_sym_db.RegisterMessage(RefundRequest)

GetTransactionLogListRequest = _reflection.GeneratedProtocolMessageType('GetTransactionLogListRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSACTIONLOGLISTREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetTransactionLogListRequest)
  ))
_sym_db.RegisterMessage(GetTransactionLogListRequest)

GetTransactionLogListResponse = _reflection.GeneratedProtocolMessageType('GetTransactionLogListResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETTRANSACTIONLOGLISTRESPONSE,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetTransactionLogListResponse)
  ))
_sym_db.RegisterMessage(GetTransactionLogListResponse)

TransactionLogDetail = _reflection.GeneratedProtocolMessageType('TransactionLogDetail', (_message.Message,), dict(
  DESCRIPTOR = _TRANSACTIONLOGDETAIL,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.TransactionLogDetail)
  ))
_sym_db.RegisterMessage(TransactionLogDetail)

GetCreditPayListMiniWXRequest = _reflection.GeneratedProtocolMessageType('GetCreditPayListMiniWXRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETCREDITPAYLISTMINIWXREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetCreditPayListMiniWXRequest)
  ))
_sym_db.RegisterMessage(GetCreditPayListMiniWXRequest)

GetCreditPayListMiniWXResponse = _reflection.GeneratedProtocolMessageType('GetCreditPayListMiniWXResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETCREDITPAYLISTMINIWXRESPONSE,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.GetCreditPayListMiniWXResponse)
  ))
_sym_db.RegisterMessage(GetCreditPayListMiniWXResponse)

CreditPayMiniWXDetail = _reflection.GeneratedProtocolMessageType('CreditPayMiniWXDetail', (_message.Message,), dict(
  DESCRIPTOR = _CREDITPAYMINIWXDETAIL,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.CreditPayMiniWXDetail)
  ))
_sym_db.RegisterMessage(CreditPayMiniWXDetail)

ThirdPartyCreditPayDetail = _reflection.GeneratedProtocolMessageType('ThirdPartyCreditPayDetail', (_message.Message,), dict(
  DESCRIPTOR = _THIRDPARTYCREDITPAYDETAIL,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.ThirdPartyCreditPayDetail)
  ))
_sym_db.RegisterMessage(ThirdPartyCreditPayDetail)

AdjustPayAccountRequest = _reflection.GeneratedProtocolMessageType('AdjustPayAccountRequest', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTPAYACCOUNTREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.AdjustPayAccountRequest)
  ))
_sym_db.RegisterMessage(AdjustPayAccountRequest)

AdjustPayAccountDetail = _reflection.GeneratedProtocolMessageType('AdjustPayAccountDetail', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTPAYACCOUNTDETAIL,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.AdjustPayAccountDetail)
  ))
_sym_db.RegisterMessage(AdjustPayAccountDetail)

RefreshCreditPayRequest = _reflection.GeneratedProtocolMessageType('RefreshCreditPayRequest', (_message.Message,), dict(
  DESCRIPTOR = _REFRESHCREDITPAYREQUEST,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.RefreshCreditPayRequest)
  ))
_sym_db.RegisterMessage(RefreshCreditPayRequest)

CommonResponse = _reflection.GeneratedProtocolMessageType('CommonResponse', (_message.Message,), dict(
  DESCRIPTOR = _COMMONRESPONSE,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.CommonResponse)
  ))
_sym_db.RegisterMessage(CommonResponse)

Page = _reflection.GeneratedProtocolMessageType('Page', (_message.Message,), dict(
  DESCRIPTOR = _PAGE,
  __module__ = 'credit_pay.credit_pay_pb2'
  # @@protoc_insertion_point(class_scope:credit_pay.Page)
  ))
_sym_db.RegisterMessage(Page)


DESCRIPTOR._options = None
_GETPAYPARAMSRESPONSE_PACKSTRENTRY._options = None

_CREDITPAY = _descriptor.ServiceDescriptor(
  name='CreditPay',
  full_name='credit_pay.CreditPay',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=7905,
  serialized_end=11207,
  methods=[
  _descriptor.MethodDescriptor(
    name='Ping',
    full_name='credit_pay.CreditPay.Ping',
    index=0,
    containing_service=None,
    input_type=_PINGREQUEST,
    output_type=_PINGRESPONSE,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='CreateCreditPay',
    full_name='credit_pay.CreditPay.CreateCreditPay',
    index=1,
    containing_service=None,
    input_type=_CREATECREDITPAYREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\002)\"$/api/v1/credit-pay/create-credit-pay:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateCreditPay',
    full_name='credit_pay.CreditPay.UpdateCreditPay',
    index=2,
    containing_service=None,
    input_type=_UPDATECREDITPAYREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\002)\"$/api/v1/credit-pay/update-credit-pay:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CloseCreditPay',
    full_name='credit_pay.CreditPay.CloseCreditPay',
    index=3,
    containing_service=None,
    input_type=_CLOSECREDITPAYREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\002(\"#/api/v1/credit-pay/close-credit-pay:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetCreditPayList',
    full_name='credit_pay.CreditPay.GetCreditPayList',
    index=4,
    containing_service=None,
    input_type=_GETCREDITPAYLISTREQUEST,
    output_type=_GETCREDITPAYLISTRESPONSE,
    serialized_options=_b('\202\323\344\223\002+\"&/api/v1/credit-pay/get-credit-pay-list:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetCreditPayDetail',
    full_name='credit_pay.CreditPay.GetCreditPayDetail',
    index=5,
    containing_service=None,
    input_type=_GETCREDITPAYDETAILREQUEST,
    output_type=_GETCREDITPAYDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\002*\022(/api/v1/credit-pay/get-credit-pay-detail'),
  ),
  _descriptor.MethodDescriptor(
    name='CreatePayRelation',
    full_name='credit_pay.CreditPay.CreatePayRelation',
    index=6,
    containing_service=None,
    input_type=_CREATEPAYRELATIONREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\002+\"&/api/v1/credit-pay/create-pay-relation:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPayRelationList',
    full_name='credit_pay.CreditPay.GetPayRelationList',
    index=7,
    containing_service=None,
    input_type=_GETPAYRELATIONLISTREQUEST,
    output_type=_GETPAYRELATIONLISTRESPONSE,
    serialized_options=_b('\202\323\344\223\002-\"(/api/v1/credit-pay/get-pay-relation-list:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPayRelationDetail',
    full_name='credit_pay.CreditPay.GetPayRelationDetail',
    index=8,
    containing_service=None,
    input_type=_GETPAYRELATIONDETAILREQUEST,
    output_type=_GETPAYRELATIONDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\002/\"*/api/v1/credit-pay/get-pay-relation-detail:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeletePayRelation',
    full_name='credit_pay.CreditPay.DeletePayRelation',
    index=9,
    containing_service=None,
    input_type=_DELETEPAYRELATIONREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\002+\"&/api/v1/credit-pay/delete-pay-relation:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ChangePayRelationStatus',
    full_name='credit_pay.CreditPay.ChangePayRelationStatus',
    index=10,
    containing_service=None,
    input_type=_CHANGEPAYRELATIONSTATUSREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\"-/api/v1/credit-pay/change-pay-relation-status:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetLogList',
    full_name='credit_pay.CreditPay.GetLogList',
    index=11,
    containing_service=None,
    input_type=_GETLOGLISTREQUEST,
    output_type=_GETLOGLISTRESPONSE,
    serialized_options=_b('\202\323\344\223\002$\"\037/api/v1/credit-pay/get-log-list:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='PayNotify',
    full_name='credit_pay.CreditPay.PayNotify',
    index=12,
    containing_service=None,
    input_type=_PAYNOTIFYREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\002\"\"\035/api/v1/credit-pay/pay-notify:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryPayStatus',
    full_name='credit_pay.CreditPay.QueryPayStatus',
    index=13,
    containing_service=None,
    input_type=_QUERYPAYSTATUSREQUEST,
    output_type=_QUERYPAYSTATUSRESPONSE,
    serialized_options=_b('\202\323\344\223\002(\"#/api/v1/credit-pay/query-pay-status:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPayParams',
    full_name='credit_pay.CreditPay.GetPayParams',
    index=14,
    containing_service=None,
    input_type=_GETPAYPARAMSREQUEST,
    output_type=_GETPAYPARAMSRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\"!/api/v1/credit-pay/get-pay-params:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ForcePay',
    full_name='credit_pay.CreditPay.ForcePay',
    index=15,
    containing_service=None,
    input_type=_FORCEPAYREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\002!\"\034/api/v1/credit-pay/force-pay:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetChannels',
    full_name='credit_pay.CreditPay.GetChannels',
    index=16,
    containing_service=None,
    input_type=_GETCHANNELSREQUEST,
    output_type=_GETCHANNELSRESPONSE,
    serialized_options=_b('\202\323\344\223\002$\"\037/api/v1/credit-pay/get-channels:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetVouchers',
    full_name='credit_pay.CreditPay.GetVouchers',
    index=17,
    containing_service=None,
    input_type=_GETVOUCHERSREQUEST,
    output_type=_GETVOUCHERSRESPONSE,
    serialized_options=_b('\202\323\344\223\002$\"\037/api/v1/credit-pay/get-vouchers:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='Pay',
    full_name='credit_pay.CreditPay.Pay',
    index=18,
    containing_service=None,
    input_type=_PAYREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\002\033\"\026/api/v1/credit-pay/pay:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='VoucherPay',
    full_name='credit_pay.CreditPay.VoucherPay',
    index=19,
    containing_service=None,
    input_type=_VOUCHERPAYREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\002#\"\036/api/v1/credit-pay/voucher-pay:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='Refund',
    full_name='credit_pay.CreditPay.Refund',
    index=20,
    containing_service=None,
    input_type=_REFUNDREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\002\036\"\031/api/v1/credit-pay/refund:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTransactionLogList',
    full_name='credit_pay.CreditPay.GetTransactionLogList',
    index=21,
    containing_service=None,
    input_type=_GETTRANSACTIONLOGLISTREQUEST,
    output_type=_GETTRANSACTIONLOGLISTRESPONSE,
    serialized_options=_b('\202\323\344\223\0020\"+/api/v1/credit-pay/get-transaction-log-list:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetCreditPayListMiniWX',
    full_name='credit_pay.CreditPay.GetCreditPayListMiniWX',
    index=22,
    containing_service=None,
    input_type=_GETCREDITPAYLISTMINIWXREQUEST,
    output_type=_GETCREDITPAYLISTMINIWXRESPONSE,
    serialized_options=_b('\202\323\344\223\0023\"./api/v1/credit-pay/get-credit-pay-list-mini-wx:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='AdjustPayAccount',
    full_name='credit_pay.CreditPay.AdjustPayAccount',
    index=23,
    containing_service=None,
    input_type=_ADJUSTPAYACCOUNTREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\002*\"%/api/v1/credit-pay/adjust-pay-account:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='RefreshCreditPay',
    full_name='credit_pay.CreditPay.RefreshCreditPay',
    index=24,
    containing_service=None,
    input_type=_REFRESHCREDITPAYREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\002*\"%/api/v1/credit-pay/refresh-credit-pay:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_CREDITPAY)

DESCRIPTOR.services_by_name['CreditPay'] = _CREDITPAY

# @@protoc_insertion_point(module_scope)
