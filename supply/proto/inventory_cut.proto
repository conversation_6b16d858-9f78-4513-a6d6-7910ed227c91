syntax = "proto3";
package inventory_cut;
import "google/api/annotations.proto";
// import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

// 库存报表相关服务
service InventoryCutService{
    rpc CutDailySnapshot(CutDailySnapshotRequest) returns (CutDailySnapshotResponse){
        option (google.api.http) = {
            post:"/api/v2/supply/inventory/daily/cut"
            body:"*"
        };
    }

    rpc TaskCallBack (TaskCallBackRequest) returns (CommonReply) {
        option (google.api.http) = {
            post:"/api/v2/supply/cost/trigger/callback"
            body:"*"
        };
    }

    rpc ListCostTriggerLog (ListCostTriggerLogRequest) returns (ListCostTriggerLogResponse) {
        option (google.api.http) = {
            post:"/api/v2/supply/cost/trigger/list"
            body:"*"
        };
    }
    
    rpc ListPeriod (ListPeriodRequest) returns (ListPeriodResponse) {
        option (google.api.http) = {
            post:"/api/v2/supply/cost/period/list"
            body:"*"
        };
    }

    rpc TriggerCostCount(TriggerCostCountRequest) returns (TriggerCostCountResponse){
        option (google.api.http) = {
            post:"/api/v2/supply/cost/trigger/count"
            body:"*"
        };
    }

  }
  
  //TaskCallback Request
  message TaskCallBackRequest{
    uint64 requestID = 5; //requestid
    uint32 category = 10; //任务类型 1:物料统计任务，2：bom商品统计任务
    bool success = 15; //是否成功
    string message = 20; //其他消息，如果失败，失败理由等
  }

  message CommonReply {
    string code = 5;//错误编码
    string message = 10;//错误消息
  }

message CutDailySnapshotRequest{
    uint64 branch_id = 1;
    repeated uint64 product_ids = 2;
    google.protobuf.Timestamp end_date = 3;
    
}

message CutDailySnapshotResponse{
    bool success = 1;
    string id = 2;
    string status = 3;
}

message ListCostTriggerLogRequest{
    // 计算开始时间
    google.protobuf.Timestamp start_time = 1;
    // 计算结束时间
    google.protobuf.Timestamp end_time = 2; 
    // 账期ids
    repeated uint64 period_ids = 3; 
    // 处理状态
    string status = 4;
    uint64 limit = 5;
    uint64 offset = 6;
    repeated string req_types = 7;
}

message ListCostTriggerLogResponse{
    repeated CostTriggerLog rows = 1;
    uint64 total = 2;

}


message CostTriggerLog{
    uint64 id = 1;
    // 批次请求编号
    uint64 batch_no = 2;
    // 分支id
    uint64 branch_id = 3;
    // 分支类型：COSTCENTER/ STORE
    string branch_type = 4;
    // 请求类型: material/ bom
    string req_type = 5;
    // 账期id
    uint64 period_id = 6;
    // 
    google.protobuf.Timestamp start_time = 7;
    // 
    google.protobuf.Timestamp end_time = 8;
    // 发送的detail数据
    string sent = 9;
    // 提交返回的detail数据
    string ret = 10;
    // 返回是否成功
    bool success = 11;
    // 返回msg
    string msg = 12;
    // 状态：INITED/ SUCCUSSED
    string status = 13;
    // 商户id
    uint64 partner_id = 14;
    uint64 created_by = 15;
    google.protobuf.Timestamp created_at = 16;
    google.protobuf.Timestamp updated_at = 17;

}

message ListPeriodRequest{
    // 计算开始时间
    google.protobuf.Timestamp start_time = 1;
    // 计算结束时间
    google.protobuf.Timestamp end_time = 2; 
    // 账期ids
    repeated uint64 period_ids = 3; 
    // 处理状态
    string status = 4;
    uint64 limit = 5;
    uint64 offset = 6;
    uint64 branch_id = 7;
}

message ListPeriodResponse{
    repeated Period rows = 1;
    uint64 total = 2;

}

message Period{
    uint64 id = 1;
    // 分支id
    uint64 branch_id = 2;
    // 分支类型：COSTCENTER/ STORE
    string branch_type = 3;
    // 请求类型: material/ bom
    string req_type = 4;
    // 账期code
    string code = 5;
    // 账期name
    string name = 6;
    // 
    google.protobuf.Timestamp start_time = 7;
    // 
    google.protobuf.Timestamp end_time = 8;
    // 状态：
    string status = 9;
    // 商户id
    uint64 partner_id = 14;
    uint64 created_by = 15;
    google.protobuf.Timestamp created_at = 16;
    google.protobuf.Timestamp updated_at = 17;
}

message TriggerCostCountRequest{
    uint64 branch_id = 1;
    repeated uint64 product_ids = 2;
    string branch_type = 3;
    google.protobuf.Timestamp start_date = 4;
    google.protobuf.Timestamp end_date = 5;
    uint64 period_id = 6;
    string task_type = 7;
    string report_type = 8;
    string extra_type = 9;
}

message TriggerCostCountResponse{
    bool success = 1;
    string id = 2;
    string status = 3;
}