// Code generated by protoc-gen-go. DO NOT EDIT.
// source: protoc-gen-swagger/options/openapiv2.proto

package options // import "github.com/grpc-ecosystem/grpc-gateway/protoc-gen-swagger/options"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import any "github.com/golang/protobuf/ptypes/any"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type Swagger_SwaggerScheme int32

const (
	Swagger_UNKNOWN Swagger_SwaggerScheme = 0
	Swagger_HTTP    Swagger_SwaggerScheme = 1
	Swagger_HTTPS   Swagger_SwaggerScheme = 2
	Swagger_WS      Swagger_SwaggerScheme = 3
	Swagger_WSS     Swagger_SwaggerScheme = 4
)

var Swagger_SwaggerScheme_name = map[int32]string{
	0: "UNKNOWN",
	1: "HTTP",
	2: "HTTPS",
	3: "WS",
	4: "WSS",
}
var Swagger_SwaggerScheme_value = map[string]int32{
	"UNKNOWN": 0,
	"HTTP":    1,
	"HTTPS":   2,
	"WS":      3,
	"WSS":     4,
}

func (x Swagger_SwaggerScheme) String() string {
	return proto.EnumName(Swagger_SwaggerScheme_name, int32(x))
}
func (Swagger_SwaggerScheme) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{0, 0}
}

type JSONSchema_JSONSchemaSimpleTypes int32

const (
	JSONSchema_UNKNOWN JSONSchema_JSONSchemaSimpleTypes = 0
	JSONSchema_ARRAY   JSONSchema_JSONSchemaSimpleTypes = 1
	JSONSchema_BOOLEAN JSONSchema_JSONSchemaSimpleTypes = 2
	JSONSchema_INTEGER JSONSchema_JSONSchemaSimpleTypes = 3
	JSONSchema_NULL    JSONSchema_JSONSchemaSimpleTypes = 4
	JSONSchema_NUMBER  JSONSchema_JSONSchemaSimpleTypes = 5
	JSONSchema_OBJECT  JSONSchema_JSONSchemaSimpleTypes = 6
	JSONSchema_STRING  JSONSchema_JSONSchemaSimpleTypes = 7
)

var JSONSchema_JSONSchemaSimpleTypes_name = map[int32]string{
	0: "UNKNOWN",
	1: "ARRAY",
	2: "BOOLEAN",
	3: "INTEGER",
	4: "NULL",
	5: "NUMBER",
	6: "OBJECT",
	7: "STRING",
}
var JSONSchema_JSONSchemaSimpleTypes_value = map[string]int32{
	"UNKNOWN": 0,
	"ARRAY":   1,
	"BOOLEAN": 2,
	"INTEGER": 3,
	"NULL":    4,
	"NUMBER":  5,
	"OBJECT":  6,
	"STRING":  7,
}

func (x JSONSchema_JSONSchemaSimpleTypes) String() string {
	return proto.EnumName(JSONSchema_JSONSchemaSimpleTypes_name, int32(x))
}
func (JSONSchema_JSONSchemaSimpleTypes) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{8, 0}
}

// Required. The type of the security scheme. Valid values are "basic",
// "apiKey" or "oauth2".
type SecurityScheme_Type int32

const (
	SecurityScheme_TYPE_INVALID SecurityScheme_Type = 0
	SecurityScheme_TYPE_BASIC   SecurityScheme_Type = 1
	SecurityScheme_TYPE_API_KEY SecurityScheme_Type = 2
	SecurityScheme_TYPE_OAUTH2  SecurityScheme_Type = 3
)

var SecurityScheme_Type_name = map[int32]string{
	0: "TYPE_INVALID",
	1: "TYPE_BASIC",
	2: "TYPE_API_KEY",
	3: "TYPE_OAUTH2",
}
var SecurityScheme_Type_value = map[string]int32{
	"TYPE_INVALID": 0,
	"TYPE_BASIC":   1,
	"TYPE_API_KEY": 2,
	"TYPE_OAUTH2":  3,
}

func (x SecurityScheme_Type) String() string {
	return proto.EnumName(SecurityScheme_Type_name, int32(x))
}
func (SecurityScheme_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{11, 0}
}

// Required. The location of the API key. Valid values are "query" or "header".
type SecurityScheme_In int32

const (
	SecurityScheme_IN_INVALID SecurityScheme_In = 0
	SecurityScheme_IN_QUERY   SecurityScheme_In = 1
	SecurityScheme_IN_HEADER  SecurityScheme_In = 2
)

var SecurityScheme_In_name = map[int32]string{
	0: "IN_INVALID",
	1: "IN_QUERY",
	2: "IN_HEADER",
}
var SecurityScheme_In_value = map[string]int32{
	"IN_INVALID": 0,
	"IN_QUERY":   1,
	"IN_HEADER":  2,
}

func (x SecurityScheme_In) String() string {
	return proto.EnumName(SecurityScheme_In_name, int32(x))
}
func (SecurityScheme_In) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{11, 1}
}

// Required. The flow used by the OAuth2 security scheme. Valid values are
// "implicit", "password", "application" or "accessCode".
type SecurityScheme_Flow int32

const (
	SecurityScheme_FLOW_INVALID     SecurityScheme_Flow = 0
	SecurityScheme_FLOW_IMPLICIT    SecurityScheme_Flow = 1
	SecurityScheme_FLOW_PASSWORD    SecurityScheme_Flow = 2
	SecurityScheme_FLOW_APPLICATION SecurityScheme_Flow = 3
	SecurityScheme_FLOW_ACCESS_CODE SecurityScheme_Flow = 4
)

var SecurityScheme_Flow_name = map[int32]string{
	0: "FLOW_INVALID",
	1: "FLOW_IMPLICIT",
	2: "FLOW_PASSWORD",
	3: "FLOW_APPLICATION",
	4: "FLOW_ACCESS_CODE",
}
var SecurityScheme_Flow_value = map[string]int32{
	"FLOW_INVALID":     0,
	"FLOW_IMPLICIT":    1,
	"FLOW_PASSWORD":    2,
	"FLOW_APPLICATION": 3,
	"FLOW_ACCESS_CODE": 4,
}

func (x SecurityScheme_Flow) String() string {
	return proto.EnumName(SecurityScheme_Flow_name, int32(x))
}
func (SecurityScheme_Flow) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{11, 2}
}

// `Swagger` is a representation of OpenAPI v2 specification's Swagger object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#swaggerObject
//
// TODO(ivucica): document fields
type Swagger struct {
	Swagger              string                  `protobuf:"bytes,1,opt,name=swagger,proto3" json:"swagger,omitempty"`
	Info                 *Info                   `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	Host                 string                  `protobuf:"bytes,3,opt,name=host,proto3" json:"host,omitempty"`
	BasePath             string                  `protobuf:"bytes,4,opt,name=base_path,json=basePath,proto3" json:"base_path,omitempty"`
	Schemes              []Swagger_SwaggerScheme `protobuf:"varint,5,rep,packed,name=schemes,proto3,enum=grpc.gateway.protoc_gen_swagger.options.Swagger_SwaggerScheme" json:"schemes,omitempty"`
	Consumes             []string                `protobuf:"bytes,6,rep,name=consumes,proto3" json:"consumes,omitempty"`
	Produces             []string                `protobuf:"bytes,7,rep,name=produces,proto3" json:"produces,omitempty"`
	Responses            map[string]*Response    `protobuf:"bytes,10,rep,name=responses,proto3" json:"responses,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	SecurityDefinitions  *SecurityDefinitions    `protobuf:"bytes,11,opt,name=security_definitions,json=securityDefinitions,proto3" json:"security_definitions,omitempty"`
	Security             []*SecurityRequirement  `protobuf:"bytes,12,rep,name=security,proto3" json:"security,omitempty"`
	ExternalDocs         *ExternalDocumentation  `protobuf:"bytes,14,opt,name=external_docs,json=externalDocs,proto3" json:"external_docs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *Swagger) Reset()         { *m = Swagger{} }
func (m *Swagger) String() string { return proto.CompactTextString(m) }
func (*Swagger) ProtoMessage()    {}
func (*Swagger) Descriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{0}
}
func (m *Swagger) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Swagger.Unmarshal(m, b)
}
func (m *Swagger) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Swagger.Marshal(b, m, deterministic)
}
func (dst *Swagger) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Swagger.Merge(dst, src)
}
func (m *Swagger) XXX_Size() int {
	return xxx_messageInfo_Swagger.Size(m)
}
func (m *Swagger) XXX_DiscardUnknown() {
	xxx_messageInfo_Swagger.DiscardUnknown(m)
}

var xxx_messageInfo_Swagger proto.InternalMessageInfo

func (m *Swagger) GetSwagger() string {
	if m != nil {
		return m.Swagger
	}
	return ""
}

func (m *Swagger) GetInfo() *Info {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *Swagger) GetHost() string {
	if m != nil {
		return m.Host
	}
	return ""
}

func (m *Swagger) GetBasePath() string {
	if m != nil {
		return m.BasePath
	}
	return ""
}

func (m *Swagger) GetSchemes() []Swagger_SwaggerScheme {
	if m != nil {
		return m.Schemes
	}
	return nil
}

func (m *Swagger) GetConsumes() []string {
	if m != nil {
		return m.Consumes
	}
	return nil
}

func (m *Swagger) GetProduces() []string {
	if m != nil {
		return m.Produces
	}
	return nil
}

func (m *Swagger) GetResponses() map[string]*Response {
	if m != nil {
		return m.Responses
	}
	return nil
}

func (m *Swagger) GetSecurityDefinitions() *SecurityDefinitions {
	if m != nil {
		return m.SecurityDefinitions
	}
	return nil
}

func (m *Swagger) GetSecurity() []*SecurityRequirement {
	if m != nil {
		return m.Security
	}
	return nil
}

func (m *Swagger) GetExternalDocs() *ExternalDocumentation {
	if m != nil {
		return m.ExternalDocs
	}
	return nil
}

// `Operation` is a representation of OpenAPI v2 specification's Operation object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#operationObject
//
// TODO(ivucica): document fields
type Operation struct {
	Tags                 []string               `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty"`
	Summary              string                 `protobuf:"bytes,2,opt,name=summary,proto3" json:"summary,omitempty"`
	Description          string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	ExternalDocs         *ExternalDocumentation `protobuf:"bytes,4,opt,name=external_docs,json=externalDocs,proto3" json:"external_docs,omitempty"`
	OperationId          string                 `protobuf:"bytes,5,opt,name=operation_id,json=operationId,proto3" json:"operation_id,omitempty"`
	Consumes             []string               `protobuf:"bytes,6,rep,name=consumes,proto3" json:"consumes,omitempty"`
	Produces             []string               `protobuf:"bytes,7,rep,name=produces,proto3" json:"produces,omitempty"`
	Responses            map[string]*Response   `protobuf:"bytes,9,rep,name=responses,proto3" json:"responses,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Schemes              []string               `protobuf:"bytes,10,rep,name=schemes,proto3" json:"schemes,omitempty"`
	Deprecated           bool                   `protobuf:"varint,11,opt,name=deprecated,proto3" json:"deprecated,omitempty"`
	Security             []*SecurityRequirement `protobuf:"bytes,12,rep,name=security,proto3" json:"security,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *Operation) Reset()         { *m = Operation{} }
func (m *Operation) String() string { return proto.CompactTextString(m) }
func (*Operation) ProtoMessage()    {}
func (*Operation) Descriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{1}
}
func (m *Operation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Operation.Unmarshal(m, b)
}
func (m *Operation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Operation.Marshal(b, m, deterministic)
}
func (dst *Operation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Operation.Merge(dst, src)
}
func (m *Operation) XXX_Size() int {
	return xxx_messageInfo_Operation.Size(m)
}
func (m *Operation) XXX_DiscardUnknown() {
	xxx_messageInfo_Operation.DiscardUnknown(m)
}

var xxx_messageInfo_Operation proto.InternalMessageInfo

func (m *Operation) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *Operation) GetSummary() string {
	if m != nil {
		return m.Summary
	}
	return ""
}

func (m *Operation) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *Operation) GetExternalDocs() *ExternalDocumentation {
	if m != nil {
		return m.ExternalDocs
	}
	return nil
}

func (m *Operation) GetOperationId() string {
	if m != nil {
		return m.OperationId
	}
	return ""
}

func (m *Operation) GetConsumes() []string {
	if m != nil {
		return m.Consumes
	}
	return nil
}

func (m *Operation) GetProduces() []string {
	if m != nil {
		return m.Produces
	}
	return nil
}

func (m *Operation) GetResponses() map[string]*Response {
	if m != nil {
		return m.Responses
	}
	return nil
}

func (m *Operation) GetSchemes() []string {
	if m != nil {
		return m.Schemes
	}
	return nil
}

func (m *Operation) GetDeprecated() bool {
	if m != nil {
		return m.Deprecated
	}
	return false
}

func (m *Operation) GetSecurity() []*SecurityRequirement {
	if m != nil {
		return m.Security
	}
	return nil
}

// `Response` is a representation of OpenAPI v2 specification's Response object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#responseObject
//
type Response struct {
	// `Description` is a short description of the response.
	// GFM syntax can be used for rich text representation.
	Description string `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	// `Schema` optionally defines the structure of the response.
	// If `Schema` is not provided, it means there is no content to the response.
	Schema               *Schema  `protobuf:"bytes,2,opt,name=schema,proto3" json:"schema,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Response) Reset()         { *m = Response{} }
func (m *Response) String() string { return proto.CompactTextString(m) }
func (*Response) ProtoMessage()    {}
func (*Response) Descriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{2}
}
func (m *Response) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Response.Unmarshal(m, b)
}
func (m *Response) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Response.Marshal(b, m, deterministic)
}
func (dst *Response) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Response.Merge(dst, src)
}
func (m *Response) XXX_Size() int {
	return xxx_messageInfo_Response.Size(m)
}
func (m *Response) XXX_DiscardUnknown() {
	xxx_messageInfo_Response.DiscardUnknown(m)
}

var xxx_messageInfo_Response proto.InternalMessageInfo

func (m *Response) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *Response) GetSchema() *Schema {
	if m != nil {
		return m.Schema
	}
	return nil
}

// `Info` is a representation of OpenAPI v2 specification's Info object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#infoObject
//
// TODO(ivucica): document fields
type Info struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Description          string   `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	TermsOfService       string   `protobuf:"bytes,3,opt,name=terms_of_service,json=termsOfService,proto3" json:"terms_of_service,omitempty"`
	Contact              *Contact `protobuf:"bytes,4,opt,name=contact,proto3" json:"contact,omitempty"`
	License              *License `protobuf:"bytes,5,opt,name=license,proto3" json:"license,omitempty"`
	Version              string   `protobuf:"bytes,6,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Info) Reset()         { *m = Info{} }
func (m *Info) String() string { return proto.CompactTextString(m) }
func (*Info) ProtoMessage()    {}
func (*Info) Descriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{3}
}
func (m *Info) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Info.Unmarshal(m, b)
}
func (m *Info) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Info.Marshal(b, m, deterministic)
}
func (dst *Info) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Info.Merge(dst, src)
}
func (m *Info) XXX_Size() int {
	return xxx_messageInfo_Info.Size(m)
}
func (m *Info) XXX_DiscardUnknown() {
	xxx_messageInfo_Info.DiscardUnknown(m)
}

var xxx_messageInfo_Info proto.InternalMessageInfo

func (m *Info) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *Info) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *Info) GetTermsOfService() string {
	if m != nil {
		return m.TermsOfService
	}
	return ""
}

func (m *Info) GetContact() *Contact {
	if m != nil {
		return m.Contact
	}
	return nil
}

func (m *Info) GetLicense() *License {
	if m != nil {
		return m.License
	}
	return nil
}

func (m *Info) GetVersion() string {
	if m != nil {
		return m.Version
	}
	return ""
}

// `Contact` is a representation of OpenAPI v2 specification's Contact object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#contactObject
//
// TODO(ivucica): document fields
type Contact struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Email                string   `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Contact) Reset()         { *m = Contact{} }
func (m *Contact) String() string { return proto.CompactTextString(m) }
func (*Contact) ProtoMessage()    {}
func (*Contact) Descriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{4}
}
func (m *Contact) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Contact.Unmarshal(m, b)
}
func (m *Contact) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Contact.Marshal(b, m, deterministic)
}
func (dst *Contact) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Contact.Merge(dst, src)
}
func (m *Contact) XXX_Size() int {
	return xxx_messageInfo_Contact.Size(m)
}
func (m *Contact) XXX_DiscardUnknown() {
	xxx_messageInfo_Contact.DiscardUnknown(m)
}

var xxx_messageInfo_Contact proto.InternalMessageInfo

func (m *Contact) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Contact) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *Contact) GetEmail() string {
	if m != nil {
		return m.Email
	}
	return ""
}

// `License` is a representation of OpenAPI v2 specification's License object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#licenseObject
//
type License struct {
	// Required. The license name used for the API.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// A URL to the license used for the API.
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *License) Reset()         { *m = License{} }
func (m *License) String() string { return proto.CompactTextString(m) }
func (*License) ProtoMessage()    {}
func (*License) Descriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{5}
}
func (m *License) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_License.Unmarshal(m, b)
}
func (m *License) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_License.Marshal(b, m, deterministic)
}
func (dst *License) XXX_Merge(src proto.Message) {
	xxx_messageInfo_License.Merge(dst, src)
}
func (m *License) XXX_Size() int {
	return xxx_messageInfo_License.Size(m)
}
func (m *License) XXX_DiscardUnknown() {
	xxx_messageInfo_License.DiscardUnknown(m)
}

var xxx_messageInfo_License proto.InternalMessageInfo

func (m *License) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *License) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

// `ExternalDocumentation` is a representation of OpenAPI v2 specification's
// ExternalDocumentation object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#externalDocumentationObject
//
// TODO(ivucica): document fields
type ExternalDocumentation struct {
	Description          string   `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExternalDocumentation) Reset()         { *m = ExternalDocumentation{} }
func (m *ExternalDocumentation) String() string { return proto.CompactTextString(m) }
func (*ExternalDocumentation) ProtoMessage()    {}
func (*ExternalDocumentation) Descriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{6}
}
func (m *ExternalDocumentation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExternalDocumentation.Unmarshal(m, b)
}
func (m *ExternalDocumentation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExternalDocumentation.Marshal(b, m, deterministic)
}
func (dst *ExternalDocumentation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExternalDocumentation.Merge(dst, src)
}
func (m *ExternalDocumentation) XXX_Size() int {
	return xxx_messageInfo_ExternalDocumentation.Size(m)
}
func (m *ExternalDocumentation) XXX_DiscardUnknown() {
	xxx_messageInfo_ExternalDocumentation.DiscardUnknown(m)
}

var xxx_messageInfo_ExternalDocumentation proto.InternalMessageInfo

func (m *ExternalDocumentation) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *ExternalDocumentation) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

// `Schema` is a representation of OpenAPI v2 specification's Schema object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#schemaObject
//
// TODO(ivucica): document fields
type Schema struct {
	JsonSchema           *JSONSchema            `protobuf:"bytes,1,opt,name=json_schema,json=jsonSchema,proto3" json:"json_schema,omitempty"`
	Discriminator        string                 `protobuf:"bytes,2,opt,name=discriminator,proto3" json:"discriminator,omitempty"`
	ReadOnly             bool                   `protobuf:"varint,3,opt,name=read_only,json=readOnly,proto3" json:"read_only,omitempty"`
	ExternalDocs         *ExternalDocumentation `protobuf:"bytes,5,opt,name=external_docs,json=externalDocs,proto3" json:"external_docs,omitempty"`
	Example              *any.Any               `protobuf:"bytes,6,opt,name=example,proto3" json:"example,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *Schema) Reset()         { *m = Schema{} }
func (m *Schema) String() string { return proto.CompactTextString(m) }
func (*Schema) ProtoMessage()    {}
func (*Schema) Descriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{7}
}
func (m *Schema) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Schema.Unmarshal(m, b)
}
func (m *Schema) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Schema.Marshal(b, m, deterministic)
}
func (dst *Schema) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Schema.Merge(dst, src)
}
func (m *Schema) XXX_Size() int {
	return xxx_messageInfo_Schema.Size(m)
}
func (m *Schema) XXX_DiscardUnknown() {
	xxx_messageInfo_Schema.DiscardUnknown(m)
}

var xxx_messageInfo_Schema proto.InternalMessageInfo

func (m *Schema) GetJsonSchema() *JSONSchema {
	if m != nil {
		return m.JsonSchema
	}
	return nil
}

func (m *Schema) GetDiscriminator() string {
	if m != nil {
		return m.Discriminator
	}
	return ""
}

func (m *Schema) GetReadOnly() bool {
	if m != nil {
		return m.ReadOnly
	}
	return false
}

func (m *Schema) GetExternalDocs() *ExternalDocumentation {
	if m != nil {
		return m.ExternalDocs
	}
	return nil
}

func (m *Schema) GetExample() *any.Any {
	if m != nil {
		return m.Example
	}
	return nil
}

// `JSONSchema` represents properties from JSON Schema taken, and as used, in
// the OpenAPI v2 spec.
//
// This includes changes made by OpenAPI v2.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#schemaObject
//
// See also: https://cswr.github.io/JsonSchema/spec/basic_types/,
// https://github.com/json-schema-org/json-schema-spec/blob/master/schema.json
//
// TODO(ivucica): document fields
type JSONSchema struct {
	// Ref is used to define an external reference to include in the message.
	// This could be a fully qualified proto message reference, and that type must be imported
	// into the protofile. If no message is identified, the Ref will be used verbatim in
	// the output.
	// For example:
	//  `ref: ".google.protobuf.Timestamp"`.
	Ref              string   `protobuf:"bytes,3,opt,name=ref,proto3" json:"ref,omitempty"`
	Title            string   `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	Description      string   `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	Default          string   `protobuf:"bytes,7,opt,name=default,proto3" json:"default,omitempty"`
	MultipleOf       float64  `protobuf:"fixed64,10,opt,name=multiple_of,json=multipleOf,proto3" json:"multiple_of,omitempty"`
	Maximum          float64  `protobuf:"fixed64,11,opt,name=maximum,proto3" json:"maximum,omitempty"`
	ExclusiveMaximum bool     `protobuf:"varint,12,opt,name=exclusive_maximum,json=exclusiveMaximum,proto3" json:"exclusive_maximum,omitempty"`
	Minimum          float64  `protobuf:"fixed64,13,opt,name=minimum,proto3" json:"minimum,omitempty"`
	ExclusiveMinimum bool     `protobuf:"varint,14,opt,name=exclusive_minimum,json=exclusiveMinimum,proto3" json:"exclusive_minimum,omitempty"`
	MaxLength        uint64   `protobuf:"varint,15,opt,name=max_length,json=maxLength,proto3" json:"max_length,omitempty"`
	MinLength        uint64   `protobuf:"varint,16,opt,name=min_length,json=minLength,proto3" json:"min_length,omitempty"`
	Pattern          string   `protobuf:"bytes,17,opt,name=pattern,proto3" json:"pattern,omitempty"`
	MaxItems         uint64   `protobuf:"varint,20,opt,name=max_items,json=maxItems,proto3" json:"max_items,omitempty"`
	MinItems         uint64   `protobuf:"varint,21,opt,name=min_items,json=minItems,proto3" json:"min_items,omitempty"`
	UniqueItems      bool     `protobuf:"varint,22,opt,name=unique_items,json=uniqueItems,proto3" json:"unique_items,omitempty"`
	MaxProperties    uint64   `protobuf:"varint,24,opt,name=max_properties,json=maxProperties,proto3" json:"max_properties,omitempty"`
	MinProperties    uint64   `protobuf:"varint,25,opt,name=min_properties,json=minProperties,proto3" json:"min_properties,omitempty"`
	Required         []string `protobuf:"bytes,26,rep,name=required,proto3" json:"required,omitempty"`
	// Items in 'array' must be unique.
	Array                []string                           `protobuf:"bytes,34,rep,name=array,proto3" json:"array,omitempty"`
	Type                 []JSONSchema_JSONSchemaSimpleTypes `protobuf:"varint,35,rep,packed,name=type,proto3,enum=grpc.gateway.protoc_gen_swagger.options.JSONSchema_JSONSchemaSimpleTypes" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *JSONSchema) Reset()         { *m = JSONSchema{} }
func (m *JSONSchema) String() string { return proto.CompactTextString(m) }
func (*JSONSchema) ProtoMessage()    {}
func (*JSONSchema) Descriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{8}
}
func (m *JSONSchema) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JSONSchema.Unmarshal(m, b)
}
func (m *JSONSchema) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JSONSchema.Marshal(b, m, deterministic)
}
func (dst *JSONSchema) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JSONSchema.Merge(dst, src)
}
func (m *JSONSchema) XXX_Size() int {
	return xxx_messageInfo_JSONSchema.Size(m)
}
func (m *JSONSchema) XXX_DiscardUnknown() {
	xxx_messageInfo_JSONSchema.DiscardUnknown(m)
}

var xxx_messageInfo_JSONSchema proto.InternalMessageInfo

func (m *JSONSchema) GetRef() string {
	if m != nil {
		return m.Ref
	}
	return ""
}

func (m *JSONSchema) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *JSONSchema) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *JSONSchema) GetDefault() string {
	if m != nil {
		return m.Default
	}
	return ""
}

func (m *JSONSchema) GetMultipleOf() float64 {
	if m != nil {
		return m.MultipleOf
	}
	return 0
}

func (m *JSONSchema) GetMaximum() float64 {
	if m != nil {
		return m.Maximum
	}
	return 0
}

func (m *JSONSchema) GetExclusiveMaximum() bool {
	if m != nil {
		return m.ExclusiveMaximum
	}
	return false
}

func (m *JSONSchema) GetMinimum() float64 {
	if m != nil {
		return m.Minimum
	}
	return 0
}

func (m *JSONSchema) GetExclusiveMinimum() bool {
	if m != nil {
		return m.ExclusiveMinimum
	}
	return false
}

func (m *JSONSchema) GetMaxLength() uint64 {
	if m != nil {
		return m.MaxLength
	}
	return 0
}

func (m *JSONSchema) GetMinLength() uint64 {
	if m != nil {
		return m.MinLength
	}
	return 0
}

func (m *JSONSchema) GetPattern() string {
	if m != nil {
		return m.Pattern
	}
	return ""
}

func (m *JSONSchema) GetMaxItems() uint64 {
	if m != nil {
		return m.MaxItems
	}
	return 0
}

func (m *JSONSchema) GetMinItems() uint64 {
	if m != nil {
		return m.MinItems
	}
	return 0
}

func (m *JSONSchema) GetUniqueItems() bool {
	if m != nil {
		return m.UniqueItems
	}
	return false
}

func (m *JSONSchema) GetMaxProperties() uint64 {
	if m != nil {
		return m.MaxProperties
	}
	return 0
}

func (m *JSONSchema) GetMinProperties() uint64 {
	if m != nil {
		return m.MinProperties
	}
	return 0
}

func (m *JSONSchema) GetRequired() []string {
	if m != nil {
		return m.Required
	}
	return nil
}

func (m *JSONSchema) GetArray() []string {
	if m != nil {
		return m.Array
	}
	return nil
}

func (m *JSONSchema) GetType() []JSONSchema_JSONSchemaSimpleTypes {
	if m != nil {
		return m.Type
	}
	return nil
}

// `Tag` is a representation of OpenAPI v2 specification's Tag object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#tagObject
//
// TODO(ivucica): document fields
type Tag struct {
	// TODO(ivucica): Description should be extracted from comments on the proto
	// service object.
	Description          string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	ExternalDocs         *ExternalDocumentation `protobuf:"bytes,3,opt,name=external_docs,json=externalDocs,proto3" json:"external_docs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *Tag) Reset()         { *m = Tag{} }
func (m *Tag) String() string { return proto.CompactTextString(m) }
func (*Tag) ProtoMessage()    {}
func (*Tag) Descriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{9}
}
func (m *Tag) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Tag.Unmarshal(m, b)
}
func (m *Tag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Tag.Marshal(b, m, deterministic)
}
func (dst *Tag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Tag.Merge(dst, src)
}
func (m *Tag) XXX_Size() int {
	return xxx_messageInfo_Tag.Size(m)
}
func (m *Tag) XXX_DiscardUnknown() {
	xxx_messageInfo_Tag.DiscardUnknown(m)
}

var xxx_messageInfo_Tag proto.InternalMessageInfo

func (m *Tag) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *Tag) GetExternalDocs() *ExternalDocumentation {
	if m != nil {
		return m.ExternalDocs
	}
	return nil
}

// `SecurityDefinitions` is a representation of OpenAPI v2 specification's
// Security Definitions object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#securityDefinitionsObject
//
// A declaration of the security schemes available to be used in the
// specification. This does not enforce the security schemes on the operations
// and only serves to provide the relevant details for each scheme.
type SecurityDefinitions struct {
	// A single security scheme definition, mapping a "name" to the scheme it defines.
	Security             map[string]*SecurityScheme `protobuf:"bytes,1,rep,name=security,proto3" json:"security,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *SecurityDefinitions) Reset()         { *m = SecurityDefinitions{} }
func (m *SecurityDefinitions) String() string { return proto.CompactTextString(m) }
func (*SecurityDefinitions) ProtoMessage()    {}
func (*SecurityDefinitions) Descriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{10}
}
func (m *SecurityDefinitions) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SecurityDefinitions.Unmarshal(m, b)
}
func (m *SecurityDefinitions) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SecurityDefinitions.Marshal(b, m, deterministic)
}
func (dst *SecurityDefinitions) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SecurityDefinitions.Merge(dst, src)
}
func (m *SecurityDefinitions) XXX_Size() int {
	return xxx_messageInfo_SecurityDefinitions.Size(m)
}
func (m *SecurityDefinitions) XXX_DiscardUnknown() {
	xxx_messageInfo_SecurityDefinitions.DiscardUnknown(m)
}

var xxx_messageInfo_SecurityDefinitions proto.InternalMessageInfo

func (m *SecurityDefinitions) GetSecurity() map[string]*SecurityScheme {
	if m != nil {
		return m.Security
	}
	return nil
}

// `SecurityScheme` is a representation of OpenAPI v2 specification's
// Security Scheme object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#securitySchemeObject
//
// Allows the definition of a security scheme that can be used by the
// operations. Supported schemes are basic authentication, an API key (either as
// a header or as a query parameter) and OAuth2's common flows (implicit,
// password, application and access code).
type SecurityScheme struct {
	// Required. The type of the security scheme. Valid values are "basic",
	// "apiKey" or "oauth2".
	Type SecurityScheme_Type `protobuf:"varint,1,opt,name=type,proto3,enum=grpc.gateway.protoc_gen_swagger.options.SecurityScheme_Type" json:"type,omitempty"`
	// A short description for security scheme.
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// Required. The name of the header or query parameter to be used.
	//
	// Valid for apiKey.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// Required. The location of the API key. Valid values are "query" or "header".
	//
	// Valid for apiKey.
	In SecurityScheme_In `protobuf:"varint,4,opt,name=in,proto3,enum=grpc.gateway.protoc_gen_swagger.options.SecurityScheme_In" json:"in,omitempty"`
	// Required. The flow used by the OAuth2 security scheme. Valid values are
	// "implicit", "password", "application" or "accessCode".
	//
	// Valid for oauth2.
	Flow SecurityScheme_Flow `protobuf:"varint,5,opt,name=flow,proto3,enum=grpc.gateway.protoc_gen_swagger.options.SecurityScheme_Flow" json:"flow,omitempty"`
	// Required. The authorization URL to be used for this flow. This SHOULD be in
	// the form of a URL.
	//
	// Valid for oauth2/implicit and oauth2/accessCode.
	AuthorizationUrl string `protobuf:"bytes,6,opt,name=authorization_url,json=authorizationUrl,proto3" json:"authorization_url,omitempty"`
	// Required. The token URL to be used for this flow. This SHOULD be in the
	// form of a URL.
	//
	// Valid for oauth2/password, oauth2/application and oauth2/accessCode.
	TokenUrl string `protobuf:"bytes,7,opt,name=token_url,json=tokenUrl,proto3" json:"token_url,omitempty"`
	// Required. The available scopes for the OAuth2 security scheme.
	//
	// Valid for oauth2.
	Scopes               *Scopes  `protobuf:"bytes,8,opt,name=scopes,proto3" json:"scopes,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SecurityScheme) Reset()         { *m = SecurityScheme{} }
func (m *SecurityScheme) String() string { return proto.CompactTextString(m) }
func (*SecurityScheme) ProtoMessage()    {}
func (*SecurityScheme) Descriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{11}
}
func (m *SecurityScheme) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SecurityScheme.Unmarshal(m, b)
}
func (m *SecurityScheme) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SecurityScheme.Marshal(b, m, deterministic)
}
func (dst *SecurityScheme) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SecurityScheme.Merge(dst, src)
}
func (m *SecurityScheme) XXX_Size() int {
	return xxx_messageInfo_SecurityScheme.Size(m)
}
func (m *SecurityScheme) XXX_DiscardUnknown() {
	xxx_messageInfo_SecurityScheme.DiscardUnknown(m)
}

var xxx_messageInfo_SecurityScheme proto.InternalMessageInfo

func (m *SecurityScheme) GetType() SecurityScheme_Type {
	if m != nil {
		return m.Type
	}
	return SecurityScheme_TYPE_INVALID
}

func (m *SecurityScheme) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *SecurityScheme) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SecurityScheme) GetIn() SecurityScheme_In {
	if m != nil {
		return m.In
	}
	return SecurityScheme_IN_INVALID
}

func (m *SecurityScheme) GetFlow() SecurityScheme_Flow {
	if m != nil {
		return m.Flow
	}
	return SecurityScheme_FLOW_INVALID
}

func (m *SecurityScheme) GetAuthorizationUrl() string {
	if m != nil {
		return m.AuthorizationUrl
	}
	return ""
}

func (m *SecurityScheme) GetTokenUrl() string {
	if m != nil {
		return m.TokenUrl
	}
	return ""
}

func (m *SecurityScheme) GetScopes() *Scopes {
	if m != nil {
		return m.Scopes
	}
	return nil
}

// `SecurityRequirement` is a representation of OpenAPI v2 specification's
// Security Requirement object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#securityRequirementObject
//
// Lists the required security schemes to execute this operation. The object can
// have multiple security schemes declared in it which are all required (that
// is, there is a logical AND between the schemes).
//
// The name used for each property MUST correspond to a security scheme
// declared in the Security Definitions.
type SecurityRequirement struct {
	// Each name must correspond to a security scheme which is declared in
	// the Security Definitions. If the security scheme is of type "oauth2",
	// then the value is a list of scope names required for the execution.
	// For other security scheme types, the array MUST be empty.
	SecurityRequirement  map[string]*SecurityRequirement_SecurityRequirementValue `protobuf:"bytes,1,rep,name=security_requirement,json=securityRequirement,proto3" json:"security_requirement,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                                 `json:"-"`
	XXX_unrecognized     []byte                                                   `json:"-"`
	XXX_sizecache        int32                                                    `json:"-"`
}

func (m *SecurityRequirement) Reset()         { *m = SecurityRequirement{} }
func (m *SecurityRequirement) String() string { return proto.CompactTextString(m) }
func (*SecurityRequirement) ProtoMessage()    {}
func (*SecurityRequirement) Descriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{12}
}
func (m *SecurityRequirement) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SecurityRequirement.Unmarshal(m, b)
}
func (m *SecurityRequirement) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SecurityRequirement.Marshal(b, m, deterministic)
}
func (dst *SecurityRequirement) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SecurityRequirement.Merge(dst, src)
}
func (m *SecurityRequirement) XXX_Size() int {
	return xxx_messageInfo_SecurityRequirement.Size(m)
}
func (m *SecurityRequirement) XXX_DiscardUnknown() {
	xxx_messageInfo_SecurityRequirement.DiscardUnknown(m)
}

var xxx_messageInfo_SecurityRequirement proto.InternalMessageInfo

func (m *SecurityRequirement) GetSecurityRequirement() map[string]*SecurityRequirement_SecurityRequirementValue {
	if m != nil {
		return m.SecurityRequirement
	}
	return nil
}

// If the security scheme is of type "oauth2", then the value is a list of
// scope names required for the execution. For other security scheme types,
// the array MUST be empty.
type SecurityRequirement_SecurityRequirementValue struct {
	Scope                []string `protobuf:"bytes,1,rep,name=scope,proto3" json:"scope,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SecurityRequirement_SecurityRequirementValue) Reset() {
	*m = SecurityRequirement_SecurityRequirementValue{}
}
func (m *SecurityRequirement_SecurityRequirementValue) String() string {
	return proto.CompactTextString(m)
}
func (*SecurityRequirement_SecurityRequirementValue) ProtoMessage() {}
func (*SecurityRequirement_SecurityRequirementValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{12, 0}
}
func (m *SecurityRequirement_SecurityRequirementValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SecurityRequirement_SecurityRequirementValue.Unmarshal(m, b)
}
func (m *SecurityRequirement_SecurityRequirementValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SecurityRequirement_SecurityRequirementValue.Marshal(b, m, deterministic)
}
func (dst *SecurityRequirement_SecurityRequirementValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SecurityRequirement_SecurityRequirementValue.Merge(dst, src)
}
func (m *SecurityRequirement_SecurityRequirementValue) XXX_Size() int {
	return xxx_messageInfo_SecurityRequirement_SecurityRequirementValue.Size(m)
}
func (m *SecurityRequirement_SecurityRequirementValue) XXX_DiscardUnknown() {
	xxx_messageInfo_SecurityRequirement_SecurityRequirementValue.DiscardUnknown(m)
}

var xxx_messageInfo_SecurityRequirement_SecurityRequirementValue proto.InternalMessageInfo

func (m *SecurityRequirement_SecurityRequirementValue) GetScope() []string {
	if m != nil {
		return m.Scope
	}
	return nil
}

// `Scopes` is a representation of OpenAPI v2 specification's Scopes object.
//
// See: https://github.com/OAI/OpenAPI-Specification/blob/3.0.0/versions/2.0.md#scopesObject
//
// Lists the available scopes for an OAuth2 security scheme.
type Scopes struct {
	// Maps between a name of a scope to a short description of it (as the value
	// of the property).
	Scope                map[string]string `protobuf:"bytes,1,rep,name=scope,proto3" json:"scope,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *Scopes) Reset()         { *m = Scopes{} }
func (m *Scopes) String() string { return proto.CompactTextString(m) }
func (*Scopes) ProtoMessage()    {}
func (*Scopes) Descriptor() ([]byte, []int) {
	return fileDescriptor_openapiv2_ae281b352364822e, []int{13}
}
func (m *Scopes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Scopes.Unmarshal(m, b)
}
func (m *Scopes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Scopes.Marshal(b, m, deterministic)
}
func (dst *Scopes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Scopes.Merge(dst, src)
}
func (m *Scopes) XXX_Size() int {
	return xxx_messageInfo_Scopes.Size(m)
}
func (m *Scopes) XXX_DiscardUnknown() {
	xxx_messageInfo_Scopes.DiscardUnknown(m)
}

var xxx_messageInfo_Scopes proto.InternalMessageInfo

func (m *Scopes) GetScope() map[string]string {
	if m != nil {
		return m.Scope
	}
	return nil
}

func init() {
	proto.RegisterType((*Swagger)(nil), "grpc.gateway.protoc_gen_swagger.options.Swagger")
	proto.RegisterMapType((map[string]*Response)(nil), "grpc.gateway.protoc_gen_swagger.options.Swagger.ResponsesEntry")
	proto.RegisterType((*Operation)(nil), "grpc.gateway.protoc_gen_swagger.options.Operation")
	proto.RegisterMapType((map[string]*Response)(nil), "grpc.gateway.protoc_gen_swagger.options.Operation.ResponsesEntry")
	proto.RegisterType((*Response)(nil), "grpc.gateway.protoc_gen_swagger.options.Response")
	proto.RegisterType((*Info)(nil), "grpc.gateway.protoc_gen_swagger.options.Info")
	proto.RegisterType((*Contact)(nil), "grpc.gateway.protoc_gen_swagger.options.Contact")
	proto.RegisterType((*License)(nil), "grpc.gateway.protoc_gen_swagger.options.License")
	proto.RegisterType((*ExternalDocumentation)(nil), "grpc.gateway.protoc_gen_swagger.options.ExternalDocumentation")
	proto.RegisterType((*Schema)(nil), "grpc.gateway.protoc_gen_swagger.options.Schema")
	proto.RegisterType((*JSONSchema)(nil), "grpc.gateway.protoc_gen_swagger.options.JSONSchema")
	proto.RegisterType((*Tag)(nil), "grpc.gateway.protoc_gen_swagger.options.Tag")
	proto.RegisterType((*SecurityDefinitions)(nil), "grpc.gateway.protoc_gen_swagger.options.SecurityDefinitions")
	proto.RegisterMapType((map[string]*SecurityScheme)(nil), "grpc.gateway.protoc_gen_swagger.options.SecurityDefinitions.SecurityEntry")
	proto.RegisterType((*SecurityScheme)(nil), "grpc.gateway.protoc_gen_swagger.options.SecurityScheme")
	proto.RegisterType((*SecurityRequirement)(nil), "grpc.gateway.protoc_gen_swagger.options.SecurityRequirement")
	proto.RegisterMapType((map[string]*SecurityRequirement_SecurityRequirementValue)(nil), "grpc.gateway.protoc_gen_swagger.options.SecurityRequirement.SecurityRequirementEntry")
	proto.RegisterType((*SecurityRequirement_SecurityRequirementValue)(nil), "grpc.gateway.protoc_gen_swagger.options.SecurityRequirement.SecurityRequirementValue")
	proto.RegisterType((*Scopes)(nil), "grpc.gateway.protoc_gen_swagger.options.Scopes")
	proto.RegisterMapType((map[string]string)(nil), "grpc.gateway.protoc_gen_swagger.options.Scopes.ScopeEntry")
	proto.RegisterEnum("grpc.gateway.protoc_gen_swagger.options.Swagger_SwaggerScheme", Swagger_SwaggerScheme_name, Swagger_SwaggerScheme_value)
	proto.RegisterEnum("grpc.gateway.protoc_gen_swagger.options.JSONSchema_JSONSchemaSimpleTypes", JSONSchema_JSONSchemaSimpleTypes_name, JSONSchema_JSONSchemaSimpleTypes_value)
	proto.RegisterEnum("grpc.gateway.protoc_gen_swagger.options.SecurityScheme_Type", SecurityScheme_Type_name, SecurityScheme_Type_value)
	proto.RegisterEnum("grpc.gateway.protoc_gen_swagger.options.SecurityScheme_In", SecurityScheme_In_name, SecurityScheme_In_value)
	proto.RegisterEnum("grpc.gateway.protoc_gen_swagger.options.SecurityScheme_Flow", SecurityScheme_Flow_name, SecurityScheme_Flow_value)
}

func init() {
	proto.RegisterFile("protoc-gen-swagger/options/openapiv2.proto", fileDescriptor_openapiv2_ae281b352364822e)
}

var fileDescriptor_openapiv2_ae281b352364822e = []byte{
	// 1773 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x58, 0xdd, 0x73, 0xdb, 0xc6,
	0x11, 0x0f, 0x48, 0x90, 0x04, 0x97, 0x22, 0x73, 0x3e, 0xcb, 0x2d, 0xa2, 0xc4, 0xae, 0xc2, 0xa6,
	0x53, 0x8d, 0x3d, 0xa6, 0x12, 0xe5, 0xa1, 0x99, 0x4c, 0xa7, 0x1d, 0x4a, 0x62, 0x6c, 0xc0, 0x32,
	0xc9, 0x82, 0x54, 0x14, 0x77, 0x26, 0x83, 0x81, 0xc1, 0x23, 0x85, 0x18, 0x38, 0x20, 0xf8, 0x90,
	0xc4, 0xbe, 0xf5, 0xa5, 0xed, 0x73, 0xa7, 0x4f, 0x9d, 0xe9, 0x9f, 0xd1, 0xb7, 0xfe, 0x55, 0xed,
	0x1f, 0xd0, 0xce, 0x7d, 0x80, 0x04, 0x25, 0x26, 0x43, 0xf9, 0xa3, 0x4f, 0xbc, 0xfd, 0xfa, 0xdd,
	0xee, 0xde, 0xee, 0xdd, 0x82, 0xf0, 0x30, 0x8a, 0xc3, 0x34, 0x74, 0x1f, 0xcf, 0x08, 0x7d, 0x9c,
	0x5c, 0x3a, 0xb3, 0x19, 0x89, 0xf7, 0xc3, 0x28, 0xf5, 0x42, 0x9a, 0xec, 0x87, 0x11, 0xa1, 0x4e,
	0xe4, 0x5d, 0x1c, 0x74, 0xb8, 0x12, 0xfe, 0xe5, 0x2c, 0x8e, 0xdc, 0xce, 0xcc, 0x49, 0xc9, 0xa5,
	0x33, 0x17, 0x3c, 0xd7, 0x9e, 0x11, 0x6a, 0x4b, 0xc3, 0x8e, 0x34, 0xdc, 0xf9, 0x60, 0x16, 0x86,
	0x33, 0x9f, 0xec, 0x73, 0x95, 0x97, 0xd9, 0x74, 0xdf, 0xa1, 0x52, 0xbf, 0xfd, 0xdf, 0x2a, 0xd4,
	0x46, 0x42, 0x1d, 0xeb, 0x50, 0x93, 0x96, 0xba, 0xb2, 0xab, 0xec, 0xd5, 0xad, 0x9c, 0xc4, 0x5d,
	0x50, 0x3d, 0x3a, 0x0d, 0xf5, 0xd2, 0xae, 0xb2, 0xd7, 0x38, 0x78, 0xdc, 0xd9, 0x70, 0xe3, 0x8e,
	0x41, 0xa7, 0xa1, 0xc5, 0x4d, 0x31, 0x06, 0xf5, 0x3c, 0x4c, 0x52, 0xbd, 0xcc, 0x91, 0xf9, 0x1a,
	0x7f, 0x08, 0xf5, 0x97, 0x4e, 0x42, 0xec, 0xc8, 0x49, 0xcf, 0x75, 0x95, 0x0b, 0x34, 0xc6, 0x18,
	0x3a, 0xe9, 0x39, 0xfe, 0x06, 0x6a, 0x89, 0x7b, 0x4e, 0x02, 0x92, 0xe8, 0x95, 0xdd, 0xf2, 0x5e,
	0xeb, 0xe0, 0x37, 0x1b, 0x6f, 0x2b, 0x03, 0xca, 0x7f, 0x47, 0x1c, 0xc6, 0xca, 0xe1, 0xf0, 0x0e,
	0x68, 0x6e, 0x48, 0x93, 0x8c, 0x41, 0x57, 0x77, 0xcb, 0x6c, 0xd7, 0x9c, 0x66, 0xb2, 0x28, 0x0e,
	0x27, 0x99, 0x4b, 0x12, 0xbd, 0x26, 0x64, 0x39, 0x8d, 0xbf, 0x85, 0x7a, 0x4c, 0x92, 0x28, 0xa4,
	0x09, 0x49, 0x74, 0xd8, 0x2d, 0xef, 0x35, 0x0e, 0x7e, 0x7b, 0x6b, 0x9f, 0xac, 0x1c, 0xa1, 0x47,
	0xd3, 0x78, 0x6e, 0x2d, 0x11, 0x71, 0x08, 0xdb, 0x09, 0x71, 0xb3, 0xd8, 0x4b, 0xe7, 0xf6, 0x84,
	0x4c, 0x3d, 0xea, 0x71, 0x4b, 0xbd, 0xc1, 0x93, 0xfe, 0xeb, 0xcd, 0x77, 0x92, 0x20, 0xc7, 0x4b,
	0x0c, 0xeb, 0x6e, 0x72, 0x93, 0x89, 0xbf, 0x01, 0x2d, 0x67, 0xeb, 0x5b, 0x3c, 0x9c, 0xdb, 0x6f,
	0x62, 0x91, 0xef, 0x33, 0x2f, 0x26, 0x01, 0xa1, 0xa9, 0xb5, 0x40, 0xc3, 0x2e, 0x34, 0xc9, 0x55,
	0x4a, 0x62, 0xea, 0xf8, 0xf6, 0x24, 0x74, 0x13, 0xbd, 0xc5, 0x63, 0xd8, 0xfc, 0x04, 0x7b, 0xd2,
	0xfa, 0x38, 0x74, 0x33, 0x86, 0xed, 0x30, 0xb6, 0xb5, 0x45, 0x96, 0xec, 0x64, 0x27, 0x84, 0xd6,
	0x6a, 0x32, 0x31, 0x82, 0xf2, 0x2b, 0x32, 0x97, 0xc5, 0xcb, 0x96, 0xf8, 0x09, 0x54, 0x2e, 0x1c,
	0x3f, 0x23, 0xb2, 0x72, 0x3f, 0xdb, 0xd8, 0x81, 0x1c, 0xd9, 0x12, 0xf6, 0x5f, 0x96, 0xbe, 0x50,
	0xda, 0x87, 0xd0, 0x5c, 0xa9, 0x28, 0xdc, 0x80, 0xda, 0x69, 0xff, 0x59, 0x7f, 0x70, 0xd6, 0x47,
	0xef, 0x61, 0x0d, 0xd4, 0xa7, 0xe3, 0xf1, 0x10, 0x29, 0xb8, 0x0e, 0x15, 0xb6, 0x1a, 0xa1, 0x12,
	0xae, 0x42, 0xe9, 0x6c, 0x84, 0xca, 0xb8, 0x06, 0xe5, 0xb3, 0xd1, 0x08, 0xa9, 0xa6, 0xaa, 0x69,
	0xa8, 0x6e, 0xaa, 0x5a, 0x1d, 0x81, 0xa9, 0x6a, 0x4d, 0xd4, 0x6a, 0xff, 0xb9, 0x02, 0xf5, 0x41,
	0x44, 0x62, 0x1e, 0x22, 0x6b, 0x93, 0xd4, 0x99, 0x25, 0xba, 0xc2, 0x6b, 0x8f, 0xaf, 0x79, 0x5f,
	0x66, 0x41, 0xe0, 0xc4, 0x73, 0x1e, 0x06, 0xeb, 0x4b, 0x41, 0xe2, 0x5d, 0x68, 0x4c, 0x48, 0xe2,
	0xc6, 0x1e, 0xf7, 0x5a, 0xf6, 0x56, 0x91, 0x75, 0xf3, 0x24, 0xd4, 0xb7, 0x7f, 0x12, 0xf8, 0x63,
	0xd8, 0x0a, 0xf3, 0x08, 0x6c, 0x6f, 0xa2, 0x57, 0x84, 0x1f, 0x0b, 0x9e, 0x31, 0x79, 0xed, 0x9e,
	0xb3, 0x8b, 0x3d, 0x57, 0xe7, 0x45, 0xda, 0xdd, 0xd8, 0xf7, 0x45, 0x5a, 0x7f, 0xa4, 0xeb, 0xf4,
	0xe5, 0x35, 0x03, 0x7c, 0xef, 0xc5, 0x35, 0xf1, 0x00, 0x60, 0x42, 0xa2, 0x98, 0xb8, 0x4e, 0x4a,
	0x26, 0xbc, 0x0b, 0x35, 0xab, 0xc0, 0x79, 0x77, 0xed, 0xf3, 0x7f, 0xaf, 0x6c, 0x51, 0x95, 0xed,
	0x3f, 0x2a, 0xa0, 0xe5, 0xd2, 0xeb, 0xa5, 0xa5, 0xdc, 0x2c, 0xad, 0x27, 0x50, 0xe5, 0xa9, 0x72,
	0xa4, 0x0b, 0xfb, 0x9b, 0x47, 0xcf, 0xcd, 0x2c, 0x69, 0x6e, 0xaa, 0x5a, 0x99, 0x77, 0x86, 0x8a,
	0x2a, 0xed, 0xbf, 0x97, 0x40, 0x65, 0xaf, 0x06, 0xde, 0x86, 0x4a, 0xea, 0xa5, 0x3e, 0x91, 0x3b,
	0x0b, 0xe2, 0xba, 0x57, 0xa5, 0x9b, 0x5e, 0xed, 0x01, 0x4a, 0x49, 0x1c, 0x24, 0x76, 0x38, 0xb5,
	0x13, 0x12, 0x5f, 0x78, 0x2e, 0x91, 0x7d, 0xd1, 0xe2, 0xfc, 0xc1, 0x74, 0x24, 0xb8, 0xd8, 0x84,
	0x9a, 0x1b, 0xd2, 0xd4, 0x71, 0x53, 0xd9, 0x14, 0x9f, 0x6e, 0x1c, 0xc0, 0x91, 0xb0, 0xb3, 0x72,
	0x00, 0x86, 0xe5, 0x7b, 0x2e, 0xa1, 0x09, 0xe1, 0xc5, 0x7f, 0x1b, 0xac, 0x13, 0x61, 0x67, 0xe5,
	0x00, 0xac, 0x22, 0x2f, 0x48, 0x9c, 0xb0, 0xf8, 0xaa, 0xa2, 0xdd, 0x25, 0xd9, 0xee, 0x41, 0x4d,
	0xee, 0xcc, 0xee, 0x09, 0xea, 0x04, 0x79, 0x76, 0xf8, 0x9a, 0x15, 0x49, 0x16, 0xfb, 0x32, 0x29,
	0x6c, 0xc9, 0x92, 0x48, 0x02, 0xc7, 0xf3, 0x65, 0x06, 0x04, 0xd1, 0xde, 0x87, 0x9a, 0xdc, 0x74,
	0x33, 0x98, 0xf6, 0x33, 0xb8, 0xb7, 0xf6, 0x1a, 0xd8, 0xa0, 0x48, 0x6e, 0x82, 0xfd, 0xab, 0x04,
	0x55, 0x51, 0x00, 0x78, 0x0c, 0x8d, 0xef, 0x92, 0x90, 0xda, 0xb2, 0x8c, 0x14, 0x9e, 0xb9, 0xcf,
	0x37, 0xce, 0x9c, 0x39, 0x1a, 0xf4, 0x65, 0x29, 0x01, 0xc3, 0x91, 0xa8, 0x9f, 0x40, 0x73, 0xe2,
	0x31, 0x0f, 0x02, 0x8f, 0x3a, 0x69, 0x18, 0xcb, 0xcd, 0x57, 0x99, 0x6c, 0xf6, 0x88, 0x89, 0x33,
	0xb1, 0x43, 0xea, 0xcf, 0x79, 0x7a, 0x34, 0x4b, 0x63, 0x8c, 0x01, 0xf5, 0xd7, 0xbc, 0x5f, 0x95,
	0x77, 0x70, 0x6b, 0x76, 0xa0, 0x46, 0xae, 0x9c, 0x20, 0xf2, 0x09, 0x3f, 0xe7, 0xc6, 0xc1, 0x76,
	0x47, 0xcc, 0x69, 0x9d, 0x7c, 0x4e, 0xeb, 0x74, 0xe9, 0xdc, 0xca, 0x95, 0x64, 0x83, 0xfc, 0xa9,
	0x06, 0xb0, 0x0c, 0x9c, 0xe5, 0x37, 0x26, 0x53, 0x79, 0xbe, 0x6c, 0xb9, 0x6c, 0x9c, 0xca, 0x8f,
	0x34, 0x4e, 0xf5, 0xe6, 0x49, 0xe9, 0x50, 0x9b, 0x90, 0xa9, 0x93, 0xf9, 0xa9, 0x5e, 0x13, 0x65,
	0x27, 0x49, 0xfc, 0x33, 0x68, 0x04, 0x99, 0x9f, 0x7a, 0x91, 0x4f, 0xec, 0x70, 0xaa, 0xc3, 0xae,
	0xb2, 0xa7, 0x58, 0x90, 0xb3, 0x06, 0x53, 0x66, 0x1a, 0x38, 0x57, 0x5e, 0x90, 0x05, 0xfc, 0x9a,
	0x54, 0xac, 0x9c, 0xc4, 0x8f, 0xe0, 0x0e, 0xb9, 0x72, 0xfd, 0x2c, 0xf1, 0x2e, 0x88, 0x9d, 0xeb,
	0x6c, 0xf1, 0x6c, 0xa3, 0x85, 0xe0, 0xb9, 0x54, 0x66, 0x30, 0x1e, 0xe5, 0x2a, 0x4d, 0x09, 0x23,
	0xc8, 0x6b, 0x30, 0x52, 0xa7, 0x75, 0x1d, 0x46, 0x2a, 0xdf, 0x07, 0x08, 0x9c, 0x2b, 0xdb, 0x27,
	0x74, 0x96, 0x9e, 0xeb, 0xef, 0xef, 0x2a, 0x7b, 0xaa, 0x55, 0x0f, 0x9c, 0xab, 0x13, 0xce, 0xe0,
	0x62, 0x8f, 0xe6, 0x62, 0x24, 0xc5, 0x1e, 0x95, 0x62, 0x1d, 0x6a, 0x91, 0x93, 0xb2, 0x63, 0xd2,
	0xef, 0x88, 0x34, 0x48, 0x92, 0x55, 0x0c, 0xc3, 0xf5, 0x52, 0x12, 0x24, 0xfa, 0x36, 0xb7, 0xd3,
	0x02, 0xe7, 0xca, 0x60, 0x34, 0x17, 0x7a, 0x54, 0x0a, 0xef, 0x49, 0xa1, 0x47, 0x85, 0xf0, 0x63,
	0xd8, 0xca, 0xa8, 0xf7, 0x7d, 0x46, 0xa4, 0xfc, 0x27, 0xdc, 0xf3, 0x86, 0xe0, 0x09, 0x95, 0x5f,
	0x40, 0x8b, 0x81, 0x47, 0x31, 0x7b, 0x34, 0x53, 0x8f, 0x24, 0xba, 0xce, 0x41, 0x9a, 0x81, 0x73,
	0x35, 0x5c, 0x30, 0xb9, 0x9a, 0x47, 0x8b, 0x6a, 0x1f, 0x48, 0x35, 0x8f, 0x16, 0xd4, 0x76, 0x40,
	0x8b, 0xc5, 0xcb, 0x32, 0xd1, 0x77, 0xc4, 0x8b, 0x9a, 0xd3, 0xac, 0x3e, 0x9c, 0x38, 0x76, 0xe6,
	0x7a, 0x9b, 0x0b, 0x04, 0x81, 0xbf, 0x05, 0x35, 0x9d, 0x47, 0x44, 0xff, 0x39, 0x1f, 0xb5, 0x8d,
	0xd7, 0xe8, 0xc1, 0xc2, 0x72, 0xe4, 0xb1, 0x82, 0x1d, 0xcf, 0x23, 0x92, 0x58, 0x1c, 0xb6, 0x7d,
	0x09, 0xf7, 0xd6, 0x8a, 0x57, 0x47, 0xa8, 0x3a, 0x54, 0xba, 0x96, 0xd5, 0x7d, 0x81, 0x14, 0xc6,
	0x3f, 0x1c, 0x0c, 0x4e, 0x7a, 0xdd, 0x3e, 0x2a, 0x31, 0xc2, 0xe8, 0x8f, 0x7b, 0x4f, 0x7a, 0x16,
	0x2a, 0xb3, 0x39, 0xab, 0x7f, 0x7a, 0x72, 0x82, 0x54, 0x0c, 0x50, 0xed, 0x9f, 0x3e, 0x3f, 0xec,
	0x59, 0xa8, 0xc2, 0xd6, 0x83, 0x43, 0xb3, 0x77, 0x34, 0x46, 0x55, 0xb6, 0x1e, 0x8d, 0x2d, 0xa3,
	0xff, 0x04, 0xd5, 0x4c, 0x55, 0x53, 0x50, 0xc9, 0x54, 0xb5, 0x12, 0x2a, 0x8b, 0x06, 0xba, 0x36,
	0x81, 0x61, 0x74, 0xd7, 0x54, 0xb5, 0xbb, 0x68, 0xdb, 0x54, 0xb5, 0x9f, 0x22, 0xdd, 0x54, 0xb5,
	0x0f, 0xd1, 0x47, 0xa6, 0xaa, 0x7d, 0x84, 0xee, 0x9b, 0xaa, 0x76, 0x1f, 0x3d, 0x30, 0x55, 0xed,
	0x01, 0x6a, 0x9b, 0xaa, 0xf6, 0x09, 0x7a, 0x68, 0xaa, 0xda, 0x43, 0xf4, 0xc8, 0x54, 0xb5, 0x47,
	0xa8, 0xd3, 0xfe, 0xab, 0x02, 0xe5, 0xb1, 0x33, 0xdb, 0xe0, 0x49, 0xba, 0x71, 0x9b, 0x94, 0xdf,
	0xfe, 0x6d, 0x22, 0x02, 0x6d, 0xff, 0x47, 0x81, 0xbb, 0x6b, 0xe6, 0x7f, 0x3c, 0x2d, 0xcc, 0x2a,
	0x0a, 0x9f, 0x55, 0xcc, 0x37, 0xf9, 0x9e, 0x58, 0xf0, 0xc4, 0x38, 0xb5, 0x9c, 0x5c, 0x52, 0x68,
	0xae, 0x88, 0xd6, 0x0c, 0x2e, 0xcf, 0x57, 0x07, 0x97, 0x5f, 0xdd, 0xda, 0x0f, 0xf9, 0x39, 0x57,
	0x18, 0xcc, 0xff, 0x59, 0x81, 0xd6, 0xaa, 0x14, 0x0f, 0x65, 0x3d, 0xb3, 0x8d, 0x5b, 0xaf, 0x31,
	0x98, 0x09, 0x98, 0x0e, 0x2b, 0x52, 0x51, 0xc2, 0x1b, 0x9c, 0x73, 0xfe, 0x98, 0x96, 0x0b, 0x8f,
	0xa9, 0x09, 0x25, 0x8f, 0xf2, 0xf9, 0xa2, 0x75, 0xf0, 0xe5, 0xeb, 0x7a, 0x61, 0x50, 0xab, 0xe4,
	0x51, 0x16, 0xd3, 0xd4, 0x0f, 0x2f, 0xf9, 0xc5, 0xfe, 0x06, 0x31, 0x7d, 0xe5, 0x87, 0x97, 0x16,
	0x47, 0x62, 0xf7, 0xaa, 0x93, 0xa5, 0xe7, 0x61, 0xec, 0xfd, 0x41, 0x0c, 0xef, 0xec, 0xad, 0x16,
	0x6f, 0x03, 0x5a, 0x11, 0x9c, 0xc6, 0x3e, 0xbb, 0xe2, 0xd2, 0xf0, 0x15, 0x11, 0x4a, 0xe2, 0x89,
	0xd0, 0x38, 0x83, 0x09, 0xf9, 0x30, 0x18, 0x46, 0x24, 0xd1, 0xb5, 0x5b, 0x0f, 0x83, 0xcc, 0xcc,
	0x92, 0xe6, 0xed, 0x67, 0xa0, 0xb2, 0xa4, 0x63, 0x04, 0x5b, 0xe3, 0x17, 0xc3, 0x9e, 0x6d, 0xf4,
	0xbf, 0xee, 0x9e, 0x18, 0xc7, 0xe8, 0x3d, 0xdc, 0x02, 0xe0, 0x9c, 0xc3, 0xee, 0xc8, 0x38, 0x42,
	0xca, 0x42, 0xa3, 0x3b, 0x34, 0xec, 0x67, 0xbd, 0x17, 0xa8, 0x84, 0xdf, 0x87, 0x06, 0xe7, 0x0c,
	0xba, 0xa7, 0xe3, 0xa7, 0x07, 0xa8, 0xdc, 0xfe, 0x0c, 0x4a, 0x06, 0x65, 0x86, 0x46, 0xbf, 0x00,
	0xb4, 0x05, 0x9a, 0xd1, 0xb7, 0x7f, 0x77, 0xda, 0xb3, 0xd8, 0x4d, 0xd3, 0x84, 0xba, 0xd1, 0xb7,
	0x9f, 0xf6, 0xba, 0xc7, 0x3d, 0x0b, 0x95, 0xda, 0xdf, 0x81, 0xca, 0x12, 0xc4, 0xd0, 0xbf, 0x3a,
	0x19, 0x9c, 0x15, 0xcc, 0xee, 0x40, 0x53, 0x70, 0x9e, 0x0f, 0x4f, 0x8c, 0x23, 0x63, 0x8c, 0x94,
	0x05, 0x6b, 0xd8, 0x1d, 0x8d, 0xce, 0x06, 0xd6, 0x31, 0x2a, 0xe1, 0x6d, 0x40, 0x9c, 0xd5, 0x1d,
	0x32, 0xad, 0xee, 0xd8, 0x18, 0xf4, 0x51, 0x79, 0xc9, 0x3d, 0x3a, 0xea, 0x8d, 0x46, 0xf6, 0xd1,
	0xe0, 0xb8, 0x87, 0xd4, 0xf6, 0xbf, 0x4b, 0xcb, 0x6e, 0x2d, 0x7c, 0x09, 0xe0, 0xbf, 0x28, 0x85,
	0xbf, 0x02, 0xe2, 0xa5, 0x40, 0xb6, 0xee, 0xe9, 0x9b, 0x7c, 0x66, 0xac, 0xe3, 0x89, 0x2e, 0x5e,
	0xfc, 0x47, 0x50, 0x90, 0xec, 0x7c, 0x0a, 0xfa, 0x1a, 0x83, 0xaf, 0x59, 0xeb, 0xb1, 0x97, 0x84,
	0x1f, 0x9a, 0xfc, 0x58, 0x15, 0xc4, 0xce, 0x3f, 0x94, 0xb5, 0x26, 0x3f, 0x74, 0x1d, 0xbc, 0x5a,
	0xbd, 0x0e, 0xde, 0x7a, 0x6c, 0xdc, 0xd5, 0xe2, 0x65, 0xf1, 0x37, 0x85, 0xcd, 0x9f, 0xac, 0xd6,
	0xf0, 0xb0, 0x18, 0x40, 0xe3, 0x36, 0xfd, 0xc9, 0xed, 0xc5, 0x8f, 0x48, 0x9e, 0x0c, 0xfe, 0x0b,
	0x80, 0x25, 0x73, 0x4d, 0xb4, 0xdb, 0xc5, 0x68, 0xeb, 0x05, 0xb7, 0x0e, 0x8f, 0x7e, 0xdf, 0x9d,
	0x79, 0xe9, 0x79, 0xf6, 0xb2, 0xe3, 0x86, 0xc1, 0x3e, 0x73, 0xe4, 0x31, 0x71, 0xc3, 0x64, 0x9e,
	0xa4, 0x44, 0x92, 0xd2, 0xaf, 0xfd, 0x1f, 0xfe, 0x87, 0xf0, 0x65, 0x95, 0xcb, 0x3e, 0xff, 0x5f,
	0x00, 0x00, 0x00, 0xff, 0xff, 0xf7, 0xc4, 0x0e, 0x83, 0x46, 0x14, 0x00, 0x00,
}
