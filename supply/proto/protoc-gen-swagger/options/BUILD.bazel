load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

package(default_visibility = ["//visibility:public"])

filegroup(
    name = "options_proto_files",
    srcs = [
        "annotations.proto",
        "openapiv2.proto",
    ],
)

go_library(
    name = "go_default_library",
    embed = [":options_go_proto"],
    importpath = "github.com/grpc-ecosystem/grpc-gateway/protoc-gen-swagger/options",
)

proto_library(
    name = "options_proto",
    srcs = [
        "annotations.proto",
        "openapiv2.proto",
    ],
    deps = [
        "@com_google_protobuf//:any_proto",
        "@com_google_protobuf//:descriptor_proto",
    ],
)

go_proto_library(
    name = "options_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "github.com/grpc-ecosystem/grpc-gateway/protoc-gen-swagger/options",
    proto = ":options_proto",
)
