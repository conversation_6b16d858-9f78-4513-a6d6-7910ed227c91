syntax = "proto3";

package vouchers;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";


service VouchersService {
    // 新建代金券
    rpc CreateVouchers (CreateVouchersRequest) returns (CommonMsg) {
        option (google.api.http) = {
            post: "/api/v2/supply/franchisee/vouchers/create"
            body: "*"
        };
    }
    // 查询代金券列表
    rpc ListVouchers (ListVouchersRequest) returns (ListVouchersResponse) {
        option (google.api.http) = {
            get: "/api/v2/supply/franchisee/vouchers"
        };
    }
    // 查询代金券详情
    rpc GetVouchersDetail (CommonMsg) returns (Vouchers) {
        option (google.api.http) = {
            get: "/api/v2/supply/franchisee/vouchers/{voucher_id}"
        };
    }
    // 更新代金券
    rpc UpdateVouchers (UpdateVouchersRequest) returns (CommonMsg) {
        option (google.api.http) = {
            put: "/api/v2/supply/franchisee/vouchers/update"
            body: "*"
        };
    }
    // 变更状态
    rpc DealVouchersById (DealVouchersByIdRequest) returns (DealVouchersByIdResponse) {
        option (google.api.http) = {
            put: "/api/v2/supply/franchisee/vouchers/deal/{action}"
            body: "*"
        };
    }

}

message CommonMsg {
    // 代金券ID
    uint64 voucher_id = 1;
}

message CreateVouchersRequest {
    // 唯一请求ID
    uint64 request_id = 1;
    // 名称
    string name = 2;
    // 订货类型(多选)
    repeated uint64 order_type_ids = 3;
    // 生效开始日期
    google.protobuf.Timestamp start_date = 4;
    // 生效结束日期
    google.protobuf.Timestamp end_date = 5;
    // 代金券金额
    double amount = 6;
    // 备注
    string remark = 7;
    // 多区域
    repeated Region regions = 8;
    // 多门店
    repeated uint64 store_ids = 9;
    // 生效门店
    repeated Store stores = 10;
}

message Region {
    // 区域类型(地理区域、管理区域等)跟主档枚举相同
    string region_type = 1;
    // 区域ID
    uint64 region_id = 2;
    // 区域编码
    string region_code = 3;
    // 区域名称
    string region_name = 4;
}

message Store {
    // 门店ID
    uint64 store_id = 1;
    // 门店编码
    string store_code = 2;
    // 门店名称
    string store_name = 3;
    // 门店类型
    string store_type = 4;
    // 代金券金额
    double amount = 5;
}

message OrderType {
    // 订货类型ID
    uint64 order_type_id = 1;
    // 订货类型编码
    string order_type_code = 2;
    // 订货类型名称
    string order_type_name = 3;
}


message ListVouchersRequest {
    // 代金券ids
    repeated uint64 voucher_ids = 1;
    // 编号
    string code = 2;
    // 名称
    string name = 3;
    // 状态：
    // INITED     新建
    // CONFIRMED  已确认
    // INVALID    已作废
    repeated string status = 4;
    // 订货类型ids
    repeated uint64 order_type_ids = 6;
    // 偏移量(默认0)
    uint64 offset = 10;
    // 每页数量
    uint64 limit = 11;
    // 排序(asc or desc 默认asc)
    string order = 12;
    // 排序字段
    string sort = 13;
    // 返回总数
    bool include_total = 14;
    // 语言
    string lan = 15;
}

message ListVouchersResponse {
    repeated Vouchers rows = 1;
    uint32 total = 2;
}

message UpdateVouchersRequest {
    // 代金券ID
    uint64 voucher_id = 1;
    // 名称
    string name = 2;
    // 订货类型(多选)
    repeated uint64 order_type_ids = 3;
    // 生效开始日期
    google.protobuf.Timestamp start_date = 4;
    // 生效结束日期
    google.protobuf.Timestamp end_date = 5;
    // 代金券金额
    double amount = 6;
    // 备注
    string remark = 7;
    // 多区域
    repeated Region regions = 8;
    // 多门店
    repeated uint64 store_ids = 9;
    // 生效门店
    repeated Store stores = 10;
}

message DealVouchersByIdRequest {
    // 批量支持'确认'和'作废'操作
    repeated uint64 voucher_ids = 1;
    // 单据操作状态
    // CONFIRMED  已确认
    // INVALID    已作废
    string action = 2;
    // 备注
    string remark = 3;
    // 原因
    string reason = 4;
}

message DealVouchersByIdResponse {
    // 处理结果
    bool result = 1;
    // 描述
    string description = 2;
}

message GetVouchersDetailRequest {
    // 代金券ID
    uint64 voucher_id = 1;
}

message Vouchers {
    // 代金券ID
    uint64 id = 1;
    // 名称
    string name = 2;
    // 编号
    string code = 3;
    // 状态
    string status = 4;
    // 处理状态
    string process_status = 5;
    // 订货类型
    repeated OrderType order_types = 6;
    // 生效开始日期
    google.protobuf.Timestamp start_date = 7;
    // 生效结束日期
    google.protobuf.Timestamp end_date = 8;
    // 代金券金额
    double amount = 9;
    // 原因
    string reason = 10;
    // 备注
    string remark = 13;
    // 区域
    repeated Region regions = 14;
    // 门店
    repeated Store stores = 15;
    // 商户id
    uint64 partner_id = 16;
    // 唯一请求ID
    uint64 request_id = 17;
    // 创建日期
    google.protobuf.Timestamp created_at = 18;
    // 更新时间
    google.protobuf.Timestamp updated_at = 19;
    // 创建人
    uint64 created_by = 20;
    string created_name = 21;
    // 更新人id
    uint64 updated_by = 22;
    string updated_name = 23;
}