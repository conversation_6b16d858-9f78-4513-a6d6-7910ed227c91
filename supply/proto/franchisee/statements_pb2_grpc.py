# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from franchisee import statements_pb2 as franchisee_dot_statements__pb2


class StatementsServiceStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.ListReconciliation = channel.unary_unary(
        '/franchisee.StatementsService/ListReconciliation',
        request_serializer=franchisee_dot_statements__pb2.ListReconciliationRequest.SerializeToString,
        response_deserializer=franchisee_dot_statements__pb2.ListReconciliationResponse.FromString,
        )


class StatementsServiceServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def ListReconciliation(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_StatementsServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'ListReconciliation': grpc.unary_unary_rpc_method_handler(
          servicer.ListReconciliation,
          request_deserializer=franchisee_dot_statements__pb2.ListReconciliationRequest.FromString,
          response_serializer=franchisee_dot_statements__pb2.ListReconciliationResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'franchisee.StatementsService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
