# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from franchisee import receiving_diff_pb2 as franchisee_dot_receiving__diff__pb2


class ReceivingDiffServiceStub(object):
  """ReceivingDiffService 收货差异相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateReceivingDiff = channel.unary_unary(
        '/franchisee_receiving_diff.ReceivingDiffService/CreateReceivingDiff',
        request_serializer=franchisee_dot_receiving__diff__pb2.CreateReceivingDiffRequest.SerializeToString,
        response_deserializer=franchisee_dot_receiving__diff__pb2.CreateReceivingDiffResponse.FromString,
        )
    self.ListReceivingDiff = channel.unary_unary(
        '/franchisee_receiving_diff.ReceivingDiffService/ListReceivingDiff',
        request_serializer=franchisee_dot_receiving__diff__pb2.ListReceivingDiffRequest.SerializeToString,
        response_deserializer=franchisee_dot_receiving__diff__pb2.ListReceivingDiffResponse.FromString,
        )
    self.ListReceivingDiffCold = channel.unary_unary(
        '/franchisee_receiving_diff.ReceivingDiffService/ListReceivingDiffCold',
        request_serializer=franchisee_dot_receiving__diff__pb2.ListReceivingDiffColdRequest.SerializeToString,
        response_deserializer=franchisee_dot_receiving__diff__pb2.ListReceivingDiffColdResponse.FromString,
        )
    self.GetReceivingDiffById = channel.unary_unary(
        '/franchisee_receiving_diff.ReceivingDiffService/GetReceivingDiffById',
        request_serializer=franchisee_dot_receiving__diff__pb2.GetReceivingDiffByIdRequest.SerializeToString,
        response_deserializer=franchisee_dot_receiving__diff__pb2.ReceivingDiff.FromString,
        )
    self.GetReceivingDiffByArgs = channel.unary_unary(
        '/franchisee_receiving_diff.ReceivingDiffService/GetReceivingDiffByArgs',
        request_serializer=franchisee_dot_receiving__diff__pb2.GetReceivingDiffByArgsRequest.SerializeToString,
        response_deserializer=franchisee_dot_receiving__diff__pb2.ListReceivingDiffResponse.FromString,
        )
    self.GetReceivingDiffProductById = channel.unary_unary(
        '/franchisee_receiving_diff.ReceivingDiffService/GetReceivingDiffProductById',
        request_serializer=franchisee_dot_receiving__diff__pb2.GetReceivingDiffProductByIdRequest.SerializeToString,
        response_deserializer=franchisee_dot_receiving__diff__pb2.GetReceivingDiffProductByIdResponse.FromString,
        )
    self.GetReceivingDiffProductByArgs = channel.unary_unary(
        '/franchisee_receiving_diff.ReceivingDiffService/GetReceivingDiffProductByArgs',
        request_serializer=franchisee_dot_receiving__diff__pb2.GetReceivingDiffProductByArgsRequest.SerializeToString,
        response_deserializer=franchisee_dot_receiving__diff__pb2.GetReceivingDiffProductByIdResponse.FromString,
        )
    self.SubmitReceivingDiff = channel.unary_unary(
        '/franchisee_receiving_diff.ReceivingDiffService/SubmitReceivingDiff',
        request_serializer=franchisee_dot_receiving__diff__pb2.SubmitReceivingDiffRequest.SerializeToString,
        response_deserializer=franchisee_dot_receiving__diff__pb2.SubmitReceivingDiffResponse.FromString,
        )
    self.ConfirmReceivingDiff = channel.unary_unary(
        '/franchisee_receiving_diff.ReceivingDiffService/ConfirmReceivingDiff',
        request_serializer=franchisee_dot_receiving__diff__pb2.ConfirmReceivingDiffRequet.SerializeToString,
        response_deserializer=franchisee_dot_receiving__diff__pb2.ConfirmReceivingDiffResponse.FromString,
        )
    self.RejectReceivingDiff = channel.unary_unary(
        '/franchisee_receiving_diff.ReceivingDiffService/RejectReceivingDiff',
        request_serializer=franchisee_dot_receiving__diff__pb2.RejectReceivingDiffRequest.SerializeToString,
        response_deserializer=franchisee_dot_receiving__diff__pb2.RejectReceivingDiffResponse.FromString,
        )
    self.UpdateReceivingDiff = channel.unary_unary(
        '/franchisee_receiving_diff.ReceivingDiffService/UpdateReceivingDiff',
        request_serializer=franchisee_dot_receiving__diff__pb2.UpdateReceivingDiffRequest.SerializeToString,
        response_deserializer=franchisee_dot_receiving__diff__pb2.UpdateReceivingDiffResponse.FromString,
        )
    self.UpdateReceivingDiffRemark = channel.unary_unary(
        '/franchisee_receiving_diff.ReceivingDiffService/UpdateReceivingDiffRemark',
        request_serializer=franchisee_dot_receiving__diff__pb2.UpdateReceivingDiffRemarkRequest.SerializeToString,
        response_deserializer=franchisee_dot_receiving__diff__pb2.UpdateReceivingDiffRemarkResponse.FromString,
        )
    self.UpdateReceivingDiffCold = channel.unary_unary(
        '/franchisee_receiving_diff.ReceivingDiffService/UpdateReceivingDiffCold',
        request_serializer=franchisee_dot_receiving__diff__pb2.UpdateReceivingDiffColdRequest.SerializeToString,
        response_deserializer=franchisee_dot_receiving__diff__pb2.UpdateReceivingDiffColdResponse.FromString,
        )
    self.DeleteReceivingDiff = channel.unary_unary(
        '/franchisee_receiving_diff.ReceivingDiffService/DeleteReceivingDiff',
        request_serializer=franchisee_dot_receiving__diff__pb2.DeleteReceivingDiffRequest.SerializeToString,
        response_deserializer=franchisee_dot_receiving__diff__pb2.DeleteReceivingDiffResponse.FromString,
        )


class ReceivingDiffServiceServicer(object):
  """ReceivingDiffService 收货差异相关服务
  """

  def CreateReceivingDiff(self, request, context):
    """创建收货差异单
    dq原先没有这个API，在收货时自动生成
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListReceivingDiff(self, request, context):
    """查询收货差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListReceivingDiffCold(self, request, context):
    """查询收货差异单--冷链
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceivingDiffById(self, request, context):
    """根据id查询收货差异单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceivingDiffByArgs(self, request, context):
    """根据一些参数查询收货差异单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceivingDiffProductById(self, request, context):
    """根据id查商品详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceivingDiffProductByArgs(self, request, context):
    """根据参数查商品详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SubmitReceivingDiff(self, request, context):
    """提交收货差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ConfirmReceivingDiff(self, request, context):
    """确认收货差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RejectReceivingDiff(self, request, context):
    """驳回收货差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateReceivingDiff(self, request, context):
    """更新收货单差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateReceivingDiffRemark(self, request, context):
    """修改差异原因和备注
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateReceivingDiffCold(self, request, context):
    """直送收货差异单更新商品明细
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteReceivingDiff(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_ReceivingDiffServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateReceivingDiff': grpc.unary_unary_rpc_method_handler(
          servicer.CreateReceivingDiff,
          request_deserializer=franchisee_dot_receiving__diff__pb2.CreateReceivingDiffRequest.FromString,
          response_serializer=franchisee_dot_receiving__diff__pb2.CreateReceivingDiffResponse.SerializeToString,
      ),
      'ListReceivingDiff': grpc.unary_unary_rpc_method_handler(
          servicer.ListReceivingDiff,
          request_deserializer=franchisee_dot_receiving__diff__pb2.ListReceivingDiffRequest.FromString,
          response_serializer=franchisee_dot_receiving__diff__pb2.ListReceivingDiffResponse.SerializeToString,
      ),
      'ListReceivingDiffCold': grpc.unary_unary_rpc_method_handler(
          servicer.ListReceivingDiffCold,
          request_deserializer=franchisee_dot_receiving__diff__pb2.ListReceivingDiffColdRequest.FromString,
          response_serializer=franchisee_dot_receiving__diff__pb2.ListReceivingDiffColdResponse.SerializeToString,
      ),
      'GetReceivingDiffById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceivingDiffById,
          request_deserializer=franchisee_dot_receiving__diff__pb2.GetReceivingDiffByIdRequest.FromString,
          response_serializer=franchisee_dot_receiving__diff__pb2.ReceivingDiff.SerializeToString,
      ),
      'GetReceivingDiffByArgs': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceivingDiffByArgs,
          request_deserializer=franchisee_dot_receiving__diff__pb2.GetReceivingDiffByArgsRequest.FromString,
          response_serializer=franchisee_dot_receiving__diff__pb2.ListReceivingDiffResponse.SerializeToString,
      ),
      'GetReceivingDiffProductById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceivingDiffProductById,
          request_deserializer=franchisee_dot_receiving__diff__pb2.GetReceivingDiffProductByIdRequest.FromString,
          response_serializer=franchisee_dot_receiving__diff__pb2.GetReceivingDiffProductByIdResponse.SerializeToString,
      ),
      'GetReceivingDiffProductByArgs': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceivingDiffProductByArgs,
          request_deserializer=franchisee_dot_receiving__diff__pb2.GetReceivingDiffProductByArgsRequest.FromString,
          response_serializer=franchisee_dot_receiving__diff__pb2.GetReceivingDiffProductByIdResponse.SerializeToString,
      ),
      'SubmitReceivingDiff': grpc.unary_unary_rpc_method_handler(
          servicer.SubmitReceivingDiff,
          request_deserializer=franchisee_dot_receiving__diff__pb2.SubmitReceivingDiffRequest.FromString,
          response_serializer=franchisee_dot_receiving__diff__pb2.SubmitReceivingDiffResponse.SerializeToString,
      ),
      'ConfirmReceivingDiff': grpc.unary_unary_rpc_method_handler(
          servicer.ConfirmReceivingDiff,
          request_deserializer=franchisee_dot_receiving__diff__pb2.ConfirmReceivingDiffRequet.FromString,
          response_serializer=franchisee_dot_receiving__diff__pb2.ConfirmReceivingDiffResponse.SerializeToString,
      ),
      'RejectReceivingDiff': grpc.unary_unary_rpc_method_handler(
          servicer.RejectReceivingDiff,
          request_deserializer=franchisee_dot_receiving__diff__pb2.RejectReceivingDiffRequest.FromString,
          response_serializer=franchisee_dot_receiving__diff__pb2.RejectReceivingDiffResponse.SerializeToString,
      ),
      'UpdateReceivingDiff': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateReceivingDiff,
          request_deserializer=franchisee_dot_receiving__diff__pb2.UpdateReceivingDiffRequest.FromString,
          response_serializer=franchisee_dot_receiving__diff__pb2.UpdateReceivingDiffResponse.SerializeToString,
      ),
      'UpdateReceivingDiffRemark': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateReceivingDiffRemark,
          request_deserializer=franchisee_dot_receiving__diff__pb2.UpdateReceivingDiffRemarkRequest.FromString,
          response_serializer=franchisee_dot_receiving__diff__pb2.UpdateReceivingDiffRemarkResponse.SerializeToString,
      ),
      'UpdateReceivingDiffCold': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateReceivingDiffCold,
          request_deserializer=franchisee_dot_receiving__diff__pb2.UpdateReceivingDiffColdRequest.FromString,
          response_serializer=franchisee_dot_receiving__diff__pb2.UpdateReceivingDiffColdResponse.SerializeToString,
      ),
      'DeleteReceivingDiff': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteReceivingDiff,
          request_deserializer=franchisee_dot_receiving__diff__pb2.DeleteReceivingDiffRequest.FromString,
          response_serializer=franchisee_dot_receiving__diff__pb2.DeleteReceivingDiffResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'franchisee_receiving_diff.ReceivingDiffService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
