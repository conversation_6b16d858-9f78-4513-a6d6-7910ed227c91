# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from franchisee import vouchers_pb2 as franchisee_dot_vouchers__pb2


class VouchersServiceStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateVouchers = channel.unary_unary(
        '/vouchers.VouchersService/CreateVouchers',
        request_serializer=franchisee_dot_vouchers__pb2.CreateVouchersRequest.SerializeToString,
        response_deserializer=franchisee_dot_vouchers__pb2.CommonMsg.FromString,
        )
    self.ListVouchers = channel.unary_unary(
        '/vouchers.VouchersService/ListVouchers',
        request_serializer=franchisee_dot_vouchers__pb2.ListVouchersRequest.SerializeToString,
        response_deserializer=franchisee_dot_vouchers__pb2.ListVouchersResponse.FromString,
        )
    self.GetVouchersDetail = channel.unary_unary(
        '/vouchers.VouchersService/GetVouchersDetail',
        request_serializer=franchisee_dot_vouchers__pb2.CommonMsg.SerializeToString,
        response_deserializer=franchisee_dot_vouchers__pb2.Vouchers.FromString,
        )
    self.UpdateVouchers = channel.unary_unary(
        '/vouchers.VouchersService/UpdateVouchers',
        request_serializer=franchisee_dot_vouchers__pb2.UpdateVouchersRequest.SerializeToString,
        response_deserializer=franchisee_dot_vouchers__pb2.CommonMsg.FromString,
        )
    self.DealVouchersById = channel.unary_unary(
        '/vouchers.VouchersService/DealVouchersById',
        request_serializer=franchisee_dot_vouchers__pb2.DealVouchersByIdRequest.SerializeToString,
        response_deserializer=franchisee_dot_vouchers__pb2.DealVouchersByIdResponse.FromString,
        )


class VouchersServiceServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def CreateVouchers(self, request, context):
    """新建代金券
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListVouchers(self, request, context):
    """查询代金券列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetVouchersDetail(self, request, context):
    """查询代金券详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateVouchers(self, request, context):
    """更新代金券
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DealVouchersById(self, request, context):
    """变更状态
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_VouchersServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateVouchers': grpc.unary_unary_rpc_method_handler(
          servicer.CreateVouchers,
          request_deserializer=franchisee_dot_vouchers__pb2.CreateVouchersRequest.FromString,
          response_serializer=franchisee_dot_vouchers__pb2.CommonMsg.SerializeToString,
      ),
      'ListVouchers': grpc.unary_unary_rpc_method_handler(
          servicer.ListVouchers,
          request_deserializer=franchisee_dot_vouchers__pb2.ListVouchersRequest.FromString,
          response_serializer=franchisee_dot_vouchers__pb2.ListVouchersResponse.SerializeToString,
      ),
      'GetVouchersDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetVouchersDetail,
          request_deserializer=franchisee_dot_vouchers__pb2.CommonMsg.FromString,
          response_serializer=franchisee_dot_vouchers__pb2.Vouchers.SerializeToString,
      ),
      'UpdateVouchers': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateVouchers,
          request_deserializer=franchisee_dot_vouchers__pb2.UpdateVouchersRequest.FromString,
          response_serializer=franchisee_dot_vouchers__pb2.CommonMsg.SerializeToString,
      ),
      'DealVouchersById': grpc.unary_unary_rpc_method_handler(
          servicer.DealVouchersById,
          request_deserializer=franchisee_dot_vouchers__pb2.DealVouchersByIdRequest.FromString,
          response_serializer=franchisee_dot_vouchers__pb2.DealVouchersByIdResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'vouchers.VouchersService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
