# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: franchisee/vouchers.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='franchisee/vouchers.proto',
  package='vouchers',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x19\x66ranchisee/vouchers.proto\x12\x08vouchers\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x1f\n\tCommonMsg\x12\x12\n\nvoucher_id\x18\x01 \x01(\x04\"\xa6\x02\n\x15\x43reateVouchersRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x16\n\x0eorder_type_ids\x18\x03 \x03(\x04\x12.\n\nstart_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06\x61mount\x18\x06 \x01(\x01\x12\x0e\n\x06remark\x18\x07 \x01(\t\x12!\n\x07regions\x18\x08 \x03(\x0b\x32\x10.vouchers.Region\x12\x11\n\tstore_ids\x18\t \x03(\x04\x12\x1f\n\x06stores\x18\n \x03(\x0b\x32\x0f.vouchers.Store\"Z\n\x06Region\x12\x13\n\x0bregion_type\x18\x01 \x01(\t\x12\x11\n\tregion_id\x18\x02 \x01(\x04\x12\x13\n\x0bregion_code\x18\x03 \x01(\t\x12\x13\n\x0bregion_name\x18\x04 \x01(\t\"e\n\x05Store\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x12\n\nstore_code\x18\x02 \x01(\t\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x12\n\nstore_type\x18\x04 \x01(\t\x12\x0e\n\x06\x61mount\x18\x05 \x01(\x01\"T\n\tOrderType\x12\x15\n\rorder_type_id\x18\x01 \x01(\x04\x12\x17\n\x0forder_type_code\x18\x02 \x01(\t\x12\x17\n\x0forder_type_name\x18\x03 \x01(\t\"\xce\x01\n\x13ListVouchersRequest\x12\x13\n\x0bvoucher_ids\x18\x01 \x03(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x03(\t\x12\x16\n\x0eorder_type_ids\x18\x06 \x03(\x04\x12\x0e\n\x06offset\x18\n \x01(\x04\x12\r\n\x05limit\x18\x0b \x01(\x04\x12\r\n\x05order\x18\x0c \x01(\t\x12\x0c\n\x04sort\x18\r \x01(\t\x12\x15\n\rinclude_total\x18\x0e \x01(\x08\x12\x0b\n\x03lan\x18\x0f \x01(\t\"G\n\x14ListVouchersResponse\x12 \n\x04rows\x18\x01 \x03(\x0b\x32\x12.vouchers.Vouchers\x12\r\n\x05total\x18\x02 \x01(\r\"\xa6\x02\n\x15UpdateVouchersRequest\x12\x12\n\nvoucher_id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x16\n\x0eorder_type_ids\x18\x03 \x03(\x04\x12.\n\nstart_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06\x61mount\x18\x06 \x01(\x01\x12\x0e\n\x06remark\x18\x07 \x01(\t\x12!\n\x07regions\x18\x08 \x03(\x0b\x32\x10.vouchers.Region\x12\x11\n\tstore_ids\x18\t \x03(\x04\x12\x1f\n\x06stores\x18\n \x03(\x0b\x32\x0f.vouchers.Store\"^\n\x17\x44\x65\x61lVouchersByIdRequest\x12\x13\n\x0bvoucher_ids\x18\x01 \x03(\x04\x12\x0e\n\x06\x61\x63tion\x18\x02 \x01(\t\x12\x0e\n\x06remark\x18\x03 \x01(\t\x12\x0e\n\x06reason\x18\x04 \x01(\t\"?\n\x18\x44\x65\x61lVouchersByIdResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\".\n\x18GetVouchersDetailRequest\x12\x12\n\nvoucher_id\x18\x01 \x01(\x04\"\xb2\x04\n\x08Vouchers\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x16\n\x0eprocess_status\x18\x05 \x01(\t\x12(\n\x0border_types\x18\x06 \x03(\x0b\x32\x13.vouchers.OrderType\x12.\n\nstart_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06\x61mount\x18\t \x01(\x01\x12\x0e\n\x06reason\x18\n \x01(\t\x12\x0e\n\x06remark\x18\r \x01(\t\x12!\n\x07regions\x18\x0e \x03(\x0b\x32\x10.vouchers.Region\x12\x1f\n\x06stores\x18\x0f \x03(\x0b\x32\x0f.vouchers.Store\x12\x12\n\npartner_id\x18\x10 \x01(\x04\x12\x12\n\nrequest_id\x18\x11 \x01(\x04\x12.\n\ncreated_at\x18\x12 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x13 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x14 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x15 \x01(\t\x12\x12\n\nupdated_by\x18\x16 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x17 \x01(\t2\x98\x05\n\x0fVouchersService\x12|\n\x0e\x43reateVouchers\x12\x1f.vouchers.CreateVouchersRequest\x1a\x13.vouchers.CommonMsg\"4\x82\xd3\xe4\x93\x02.\")/api/v2/supply/franchisee/vouchers/create:\x01*\x12y\n\x0cListVouchers\x12\x1d.vouchers.ListVouchersRequest\x1a\x1e.vouchers.ListVouchersResponse\"*\x82\xd3\xe4\x93\x02$\x12\"/api/v2/supply/franchisee/vouchers\x12u\n\x11GetVouchersDetail\x12\x13.vouchers.CommonMsg\x1a\x12.vouchers.Vouchers\"7\x82\xd3\xe4\x93\x02\x31\x12//api/v2/supply/franchisee/vouchers/{voucher_id}\x12|\n\x0eUpdateVouchers\x12\x1f.vouchers.UpdateVouchersRequest\x1a\x13.vouchers.CommonMsg\"4\x82\xd3\xe4\x93\x02.\x1a)/api/v2/supply/franchisee/vouchers/update:\x01*\x12\x96\x01\n\x10\x44\x65\x61lVouchersById\x12!.vouchers.DealVouchersByIdRequest\x1a\".vouchers.DealVouchersByIdResponse\";\x82\xd3\xe4\x93\x02\x35\x1a\x30/api/v2/supply/franchisee/vouchers/deal/{action}:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_COMMONMSG = _descriptor.Descriptor(
  name='CommonMsg',
  full_name='vouchers.CommonMsg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='voucher_id', full_name='vouchers.CommonMsg.voucher_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=102,
  serialized_end=133,
)


_CREATEVOUCHERSREQUEST = _descriptor.Descriptor(
  name='CreateVouchersRequest',
  full_name='vouchers.CreateVouchersRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='vouchers.CreateVouchersRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='vouchers.CreateVouchersRequest.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_ids', full_name='vouchers.CreateVouchersRequest.order_type_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='vouchers.CreateVouchersRequest.start_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='vouchers.CreateVouchersRequest.end_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='vouchers.CreateVouchersRequest.amount', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='vouchers.CreateVouchersRequest.remark', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='regions', full_name='vouchers.CreateVouchersRequest.regions', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='vouchers.CreateVouchersRequest.store_ids', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stores', full_name='vouchers.CreateVouchersRequest.stores', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=136,
  serialized_end=430,
)


_REGION = _descriptor.Descriptor(
  name='Region',
  full_name='vouchers.Region',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='region_type', full_name='vouchers.Region.region_type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_id', full_name='vouchers.Region.region_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_code', full_name='vouchers.Region.region_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_name', full_name='vouchers.Region.region_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=432,
  serialized_end=522,
)


_STORE = _descriptor.Descriptor(
  name='Store',
  full_name='vouchers.Store',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='vouchers.Store.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='vouchers.Store.store_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='vouchers.Store.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_type', full_name='vouchers.Store.store_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='vouchers.Store.amount', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=524,
  serialized_end=625,
)


_ORDERTYPE = _descriptor.Descriptor(
  name='OrderType',
  full_name='vouchers.OrderType',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_type_id', full_name='vouchers.OrderType.order_type_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_code', full_name='vouchers.OrderType.order_type_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_name', full_name='vouchers.OrderType.order_type_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=627,
  serialized_end=711,
)


_LISTVOUCHERSREQUEST = _descriptor.Descriptor(
  name='ListVouchersRequest',
  full_name='vouchers.ListVouchersRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='voucher_ids', full_name='vouchers.ListVouchersRequest.voucher_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='vouchers.ListVouchersRequest.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='vouchers.ListVouchersRequest.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='vouchers.ListVouchersRequest.status', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_ids', full_name='vouchers.ListVouchersRequest.order_type_ids', index=4,
      number=6, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='vouchers.ListVouchersRequest.offset', index=5,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='vouchers.ListVouchersRequest.limit', index=6,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='vouchers.ListVouchersRequest.order', index=7,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='vouchers.ListVouchersRequest.sort', index=8,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='vouchers.ListVouchersRequest.include_total', index=9,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='vouchers.ListVouchersRequest.lan', index=10,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=714,
  serialized_end=920,
)


_LISTVOUCHERSRESPONSE = _descriptor.Descriptor(
  name='ListVouchersResponse',
  full_name='vouchers.ListVouchersResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='vouchers.ListVouchersResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='vouchers.ListVouchersResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=922,
  serialized_end=993,
)


_UPDATEVOUCHERSREQUEST = _descriptor.Descriptor(
  name='UpdateVouchersRequest',
  full_name='vouchers.UpdateVouchersRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='voucher_id', full_name='vouchers.UpdateVouchersRequest.voucher_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='vouchers.UpdateVouchersRequest.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type_ids', full_name='vouchers.UpdateVouchersRequest.order_type_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='vouchers.UpdateVouchersRequest.start_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='vouchers.UpdateVouchersRequest.end_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='vouchers.UpdateVouchersRequest.amount', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='vouchers.UpdateVouchersRequest.remark', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='regions', full_name='vouchers.UpdateVouchersRequest.regions', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='vouchers.UpdateVouchersRequest.store_ids', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stores', full_name='vouchers.UpdateVouchersRequest.stores', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=996,
  serialized_end=1290,
)


_DEALVOUCHERSBYIDREQUEST = _descriptor.Descriptor(
  name='DealVouchersByIdRequest',
  full_name='vouchers.DealVouchersByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='voucher_ids', full_name='vouchers.DealVouchersByIdRequest.voucher_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='vouchers.DealVouchersByIdRequest.action', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='vouchers.DealVouchersByIdRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='vouchers.DealVouchersByIdRequest.reason', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1292,
  serialized_end=1386,
)


_DEALVOUCHERSBYIDRESPONSE = _descriptor.Descriptor(
  name='DealVouchersByIdResponse',
  full_name='vouchers.DealVouchersByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='vouchers.DealVouchersByIdResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='vouchers.DealVouchersByIdResponse.description', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1388,
  serialized_end=1451,
)


_GETVOUCHERSDETAILREQUEST = _descriptor.Descriptor(
  name='GetVouchersDetailRequest',
  full_name='vouchers.GetVouchersDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='voucher_id', full_name='vouchers.GetVouchersDetailRequest.voucher_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1453,
  serialized_end=1499,
)


_VOUCHERS = _descriptor.Descriptor(
  name='Vouchers',
  full_name='vouchers.Vouchers',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='vouchers.Vouchers.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='vouchers.Vouchers.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='vouchers.Vouchers.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='vouchers.Vouchers.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='vouchers.Vouchers.process_status', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_types', full_name='vouchers.Vouchers.order_types', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='vouchers.Vouchers.start_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='vouchers.Vouchers.end_date', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='vouchers.Vouchers.amount', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='vouchers.Vouchers.reason', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='vouchers.Vouchers.remark', index=10,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='regions', full_name='vouchers.Vouchers.regions', index=11,
      number=14, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stores', full_name='vouchers.Vouchers.stores', index=12,
      number=15, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='vouchers.Vouchers.partner_id', index=13,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='vouchers.Vouchers.request_id', index=14,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='vouchers.Vouchers.created_at', index=15,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='vouchers.Vouchers.updated_at', index=16,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='vouchers.Vouchers.created_by', index=17,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='vouchers.Vouchers.created_name', index=18,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='vouchers.Vouchers.updated_by', index=19,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='vouchers.Vouchers.updated_name', index=20,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1502,
  serialized_end=2064,
)

_CREATEVOUCHERSREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEVOUCHERSREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEVOUCHERSREQUEST.fields_by_name['regions'].message_type = _REGION
_CREATEVOUCHERSREQUEST.fields_by_name['stores'].message_type = _STORE
_LISTVOUCHERSRESPONSE.fields_by_name['rows'].message_type = _VOUCHERS
_UPDATEVOUCHERSREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATEVOUCHERSREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATEVOUCHERSREQUEST.fields_by_name['regions'].message_type = _REGION
_UPDATEVOUCHERSREQUEST.fields_by_name['stores'].message_type = _STORE
_VOUCHERS.fields_by_name['order_types'].message_type = _ORDERTYPE
_VOUCHERS.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_VOUCHERS.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_VOUCHERS.fields_by_name['regions'].message_type = _REGION
_VOUCHERS.fields_by_name['stores'].message_type = _STORE
_VOUCHERS.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_VOUCHERS.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['CommonMsg'] = _COMMONMSG
DESCRIPTOR.message_types_by_name['CreateVouchersRequest'] = _CREATEVOUCHERSREQUEST
DESCRIPTOR.message_types_by_name['Region'] = _REGION
DESCRIPTOR.message_types_by_name['Store'] = _STORE
DESCRIPTOR.message_types_by_name['OrderType'] = _ORDERTYPE
DESCRIPTOR.message_types_by_name['ListVouchersRequest'] = _LISTVOUCHERSREQUEST
DESCRIPTOR.message_types_by_name['ListVouchersResponse'] = _LISTVOUCHERSRESPONSE
DESCRIPTOR.message_types_by_name['UpdateVouchersRequest'] = _UPDATEVOUCHERSREQUEST
DESCRIPTOR.message_types_by_name['DealVouchersByIdRequest'] = _DEALVOUCHERSBYIDREQUEST
DESCRIPTOR.message_types_by_name['DealVouchersByIdResponse'] = _DEALVOUCHERSBYIDRESPONSE
DESCRIPTOR.message_types_by_name['GetVouchersDetailRequest'] = _GETVOUCHERSDETAILREQUEST
DESCRIPTOR.message_types_by_name['Vouchers'] = _VOUCHERS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CommonMsg = _reflection.GeneratedProtocolMessageType('CommonMsg', (_message.Message,), dict(
  DESCRIPTOR = _COMMONMSG,
  __module__ = 'franchisee.vouchers_pb2'
  # @@protoc_insertion_point(class_scope:vouchers.CommonMsg)
  ))
_sym_db.RegisterMessage(CommonMsg)

CreateVouchersRequest = _reflection.GeneratedProtocolMessageType('CreateVouchersRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEVOUCHERSREQUEST,
  __module__ = 'franchisee.vouchers_pb2'
  # @@protoc_insertion_point(class_scope:vouchers.CreateVouchersRequest)
  ))
_sym_db.RegisterMessage(CreateVouchersRequest)

Region = _reflection.GeneratedProtocolMessageType('Region', (_message.Message,), dict(
  DESCRIPTOR = _REGION,
  __module__ = 'franchisee.vouchers_pb2'
  # @@protoc_insertion_point(class_scope:vouchers.Region)
  ))
_sym_db.RegisterMessage(Region)

Store = _reflection.GeneratedProtocolMessageType('Store', (_message.Message,), dict(
  DESCRIPTOR = _STORE,
  __module__ = 'franchisee.vouchers_pb2'
  # @@protoc_insertion_point(class_scope:vouchers.Store)
  ))
_sym_db.RegisterMessage(Store)

OrderType = _reflection.GeneratedProtocolMessageType('OrderType', (_message.Message,), dict(
  DESCRIPTOR = _ORDERTYPE,
  __module__ = 'franchisee.vouchers_pb2'
  # @@protoc_insertion_point(class_scope:vouchers.OrderType)
  ))
_sym_db.RegisterMessage(OrderType)

ListVouchersRequest = _reflection.GeneratedProtocolMessageType('ListVouchersRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTVOUCHERSREQUEST,
  __module__ = 'franchisee.vouchers_pb2'
  # @@protoc_insertion_point(class_scope:vouchers.ListVouchersRequest)
  ))
_sym_db.RegisterMessage(ListVouchersRequest)

ListVouchersResponse = _reflection.GeneratedProtocolMessageType('ListVouchersResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTVOUCHERSRESPONSE,
  __module__ = 'franchisee.vouchers_pb2'
  # @@protoc_insertion_point(class_scope:vouchers.ListVouchersResponse)
  ))
_sym_db.RegisterMessage(ListVouchersResponse)

UpdateVouchersRequest = _reflection.GeneratedProtocolMessageType('UpdateVouchersRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEVOUCHERSREQUEST,
  __module__ = 'franchisee.vouchers_pb2'
  # @@protoc_insertion_point(class_scope:vouchers.UpdateVouchersRequest)
  ))
_sym_db.RegisterMessage(UpdateVouchersRequest)

DealVouchersByIdRequest = _reflection.GeneratedProtocolMessageType('DealVouchersByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _DEALVOUCHERSBYIDREQUEST,
  __module__ = 'franchisee.vouchers_pb2'
  # @@protoc_insertion_point(class_scope:vouchers.DealVouchersByIdRequest)
  ))
_sym_db.RegisterMessage(DealVouchersByIdRequest)

DealVouchersByIdResponse = _reflection.GeneratedProtocolMessageType('DealVouchersByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _DEALVOUCHERSBYIDRESPONSE,
  __module__ = 'franchisee.vouchers_pb2'
  # @@protoc_insertion_point(class_scope:vouchers.DealVouchersByIdResponse)
  ))
_sym_db.RegisterMessage(DealVouchersByIdResponse)

GetVouchersDetailRequest = _reflection.GeneratedProtocolMessageType('GetVouchersDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETVOUCHERSDETAILREQUEST,
  __module__ = 'franchisee.vouchers_pb2'
  # @@protoc_insertion_point(class_scope:vouchers.GetVouchersDetailRequest)
  ))
_sym_db.RegisterMessage(GetVouchersDetailRequest)

Vouchers = _reflection.GeneratedProtocolMessageType('Vouchers', (_message.Message,), dict(
  DESCRIPTOR = _VOUCHERS,
  __module__ = 'franchisee.vouchers_pb2'
  # @@protoc_insertion_point(class_scope:vouchers.Vouchers)
  ))
_sym_db.RegisterMessage(Vouchers)



_VOUCHERSSERVICE = _descriptor.ServiceDescriptor(
  name='VouchersService',
  full_name='vouchers.VouchersService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=2067,
  serialized_end=2731,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateVouchers',
    full_name='vouchers.VouchersService.CreateVouchers',
    index=0,
    containing_service=None,
    input_type=_CREATEVOUCHERSREQUEST,
    output_type=_COMMONMSG,
    serialized_options=_b('\202\323\344\223\002.\")/api/v2/supply/franchisee/vouchers/create:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListVouchers',
    full_name='vouchers.VouchersService.ListVouchers',
    index=1,
    containing_service=None,
    input_type=_LISTVOUCHERSREQUEST,
    output_type=_LISTVOUCHERSRESPONSE,
    serialized_options=_b('\202\323\344\223\002$\022\"/api/v2/supply/franchisee/vouchers'),
  ),
  _descriptor.MethodDescriptor(
    name='GetVouchersDetail',
    full_name='vouchers.VouchersService.GetVouchersDetail',
    index=2,
    containing_service=None,
    input_type=_COMMONMSG,
    output_type=_VOUCHERS,
    serialized_options=_b('\202\323\344\223\0021\022//api/v2/supply/franchisee/vouchers/{voucher_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateVouchers',
    full_name='vouchers.VouchersService.UpdateVouchers',
    index=3,
    containing_service=None,
    input_type=_UPDATEVOUCHERSREQUEST,
    output_type=_COMMONMSG,
    serialized_options=_b('\202\323\344\223\002.\032)/api/v2/supply/franchisee/vouchers/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DealVouchersById',
    full_name='vouchers.VouchersService.DealVouchersById',
    index=4,
    containing_service=None,
    input_type=_DEALVOUCHERSBYIDREQUEST,
    output_type=_DEALVOUCHERSBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\0320/api/v2/supply/franchisee/vouchers/deal/{action}:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_VOUCHERSSERVICE)

DESCRIPTOR.services_by_name['VouchersService'] = _VOUCHERSSERVICE

# @@protoc_insertion_point(module_scope)
