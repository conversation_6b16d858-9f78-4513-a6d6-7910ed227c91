{"swagger": "2.0", "info": {"title": "franchisee/vouchers.proto", "version": "version not set"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/franchisee/vouchers": {"get": {"summary": "查询代金券列表", "operationId": "ListVouchers", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/vouchersListVouchersResponse"}}}, "parameters": [{"name": "voucher_ids", "description": "代金券ids.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "code", "description": "编号.", "in": "query", "required": false, "type": "string"}, {"name": "name", "description": "名称.", "in": "query", "required": false, "type": "string"}, {"name": "status", "description": "状态：\nINITED     新建\nCONFIRMED  已确认\nINVALID    已作废.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "order_type_ids", "description": "订货类型ids.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "offset", "description": "偏移量(默认0).", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "description": "每页数量.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "order", "description": "排序(asc or desc 默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "description": "排序字段.", "in": "query", "required": false, "type": "string"}, {"name": "include_total", "description": "返回总数.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "lan", "description": "语言.", "in": "query", "required": false, "type": "string"}], "tags": ["VouchersService"]}}, "/api/v2/supply/franchisee/vouchers/create": {"post": {"summary": "新建代金券", "operationId": "CreateVouchers", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/vouchersCommonMsg"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/vouchersCreateVouchersRequest"}}], "tags": ["VouchersService"]}}, "/api/v2/supply/franchisee/vouchers/deal/{action}": {"put": {"summary": "变更状态", "operationId": "DealVouchersById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/vouchersDealVouchersByIdResponse"}}}, "parameters": [{"name": "action", "description": "单据操作状态\nCONFIRMED  已确认\nINVALID    已作废", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/vouchersDealVouchersByIdRequest"}}], "tags": ["VouchersService"]}}, "/api/v2/supply/franchisee/vouchers/update": {"put": {"summary": "更新代金券", "operationId": "UpdateVouchers", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/vouchersCommonMsg"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/vouchersUpdateVouchersRequest"}}], "tags": ["VouchersService"]}}, "/api/v2/supply/franchisee/vouchers/{voucher_id}": {"get": {"summary": "查询代金券详情", "operationId": "GetVouchersDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/vouchersVouchers"}}}, "parameters": [{"name": "voucher_id", "description": "代金券ID", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["VouchersService"]}}}, "definitions": {"vouchersCommonMsg": {"type": "object", "properties": {"voucher_id": {"type": "string", "format": "uint64", "title": "代金券ID"}}}, "vouchersCreateVouchersRequest": {"type": "object", "properties": {"request_id": {"type": "string", "format": "uint64", "title": "唯一请求ID"}, "name": {"type": "string", "title": "名称"}, "order_type_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "订货类型(多选)"}, "start_date": {"type": "string", "format": "date-time", "title": "生效开始日期"}, "end_date": {"type": "string", "format": "date-time", "title": "生效结束日期"}, "amount": {"type": "number", "format": "double", "title": "代金券金额"}, "remark": {"type": "string", "title": "备注"}, "regions": {"type": "array", "items": {"$ref": "#/definitions/vouchersRegion"}, "title": "多区域"}, "store_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "多门店"}, "stores": {"type": "array", "items": {"$ref": "#/definitions/vouchersStore"}, "title": "生效门店"}}}, "vouchersDealVouchersByIdRequest": {"type": "object", "properties": {"voucher_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "批量支持'确认'和'作废'操作"}, "action": {"type": "string", "title": "单据操作状态\nCONFIRMED  已确认\nINVALID    已作废"}, "remark": {"type": "string", "title": "备注"}, "reason": {"type": "string", "title": "原因"}}}, "vouchersDealVouchersByIdResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean", "title": "处理结果"}, "description": {"type": "string", "title": "描述"}}}, "vouchersListVouchersResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/vouchersVouchers"}}, "total": {"type": "integer", "format": "int64"}}}, "vouchersOrderType": {"type": "object", "properties": {"order_type_id": {"type": "string", "format": "uint64", "title": "订货类型ID"}, "order_type_code": {"type": "string", "title": "订货类型编码"}, "order_type_name": {"type": "string", "title": "订货类型名称"}}}, "vouchersRegion": {"type": "object", "properties": {"region_type": {"type": "string", "title": "区域类型(地理区域、管理区域等)跟主档枚举相同"}, "region_id": {"type": "string", "format": "uint64", "title": "区域ID"}, "region_code": {"type": "string", "title": "区域编码"}, "region_name": {"type": "string", "title": "区域名称"}}}, "vouchersStore": {"type": "object", "properties": {"store_id": {"type": "string", "format": "uint64", "title": "门店ID"}, "store_code": {"type": "string", "title": "门店编码"}, "store_name": {"type": "string", "title": "门店名称"}, "store_type": {"type": "string", "title": "门店类型"}, "amount": {"type": "number", "format": "double", "title": "代金券金额"}}}, "vouchersUpdateVouchersRequest": {"type": "object", "properties": {"voucher_id": {"type": "string", "format": "uint64", "title": "代金券ID"}, "name": {"type": "string", "title": "名称"}, "order_type_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "订货类型(多选)"}, "start_date": {"type": "string", "format": "date-time", "title": "生效开始日期"}, "end_date": {"type": "string", "format": "date-time", "title": "生效结束日期"}, "amount": {"type": "number", "format": "double", "title": "代金券金额"}, "remark": {"type": "string", "title": "备注"}, "regions": {"type": "array", "items": {"$ref": "#/definitions/vouchersRegion"}, "title": "多区域"}, "store_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "多门店"}, "stores": {"type": "array", "items": {"$ref": "#/definitions/vouchersStore"}, "title": "生效门店"}}}, "vouchersVouchers": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "代金券ID"}, "name": {"type": "string", "title": "名称"}, "code": {"type": "string", "title": "编号"}, "status": {"type": "string", "title": "状态"}, "process_status": {"type": "string", "title": "处理状态"}, "order_types": {"type": "array", "items": {"$ref": "#/definitions/vouchersOrderType"}, "title": "订货类型"}, "start_date": {"type": "string", "format": "date-time", "title": "生效开始日期"}, "end_date": {"type": "string", "format": "date-time", "title": "生效结束日期"}, "amount": {"type": "number", "format": "double", "title": "代金券金额"}, "reason": {"type": "string", "title": "原因"}, "remark": {"type": "string", "title": "备注"}, "regions": {"type": "array", "items": {"$ref": "#/definitions/vouchersRegion"}, "title": "区域"}, "stores": {"type": "array", "items": {"$ref": "#/definitions/vouchersStore"}, "title": "门店"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "request_id": {"type": "string", "format": "uint64", "title": "唯一请求ID"}, "created_at": {"type": "string", "format": "date-time", "title": "创建日期"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人"}, "created_name": {"type": "string"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "updated_name": {"type": "string"}}}}}