# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: franchisee/statements.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='franchisee/statements.proto',
  package='franchisee',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1b\x66ranchisee/statements.proto\x12\nfranchisee\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xc2\x01\n\x19ListReconciliationRequest\x12/\n\x0bquery_dates\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nstore_code\x18\x02 \x01(\t\x12\x0e\n\x06\x66ields\x18\x03 \x01(\t\x12\r\n\x05limit\x18\x04 \x01(\r\x12\x0e\n\x06offset\x18\x05 \x01(\r\x12\x12\n\norder_type\x18\x06 \x01(\t\x12\x0c\n\x04\x64\x65sc\x18\x07 \x01(\x08\x12\x0f\n\x07voucher\x18\x08 \x01(\t\"\xde\x01\n\x1aListReconciliationResponse\x12(\n\x04rows\x18\x01 \x03(\x0b\x32\x1a.franchisee.StatementsItem\x12\r\n\x05total\x18\x02 \x01(\x05\x12\x18\n\x10\x62\x65gining_balance\x18\x03 \x01(\x01\x12\x16\n\x0e\x65nding_balance\x18\x04 \x01(\x01\x12-\n\tcreate_at\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0b\n\x03msg\x18\x06 \x01(\t\x12\x0b\n\x03uid\x18\x07 \x01(\t\x12\x0c\n\x04\x63ode\x18\x08 \x01(\x05\"\xb2\x01\n\x0eStatementsItem\x12\x0b\n\x03uid\x18\x01 \x01(\t\x12\r\n\x05uname\x18\x02 \x01(\t\x12\x12\n\nvoucher_no\x18\x03 \x01(\t\x12\x1a\n\x12transcation_amount\x18\x05 \x01(\x01\x12\x0f\n\x07\x62\x61lance\x18\x06 \x01(\x01\x12\x16\n\x0e\x61\x63\x63ount_amount\x18\x07 \x01(\x01\x12\x0e\n\x06\x63redit\x18\x08 \x01(\t\x12\x0f\n\x07\x61\x64\x64ress\x18\t \x01(\t\x12\n\n\x02id\x18\n \x01(\x04\x32\xab\x01\n\x11StatementsService\x12\x95\x01\n\x12ListReconciliation\x12%.franchisee.ListReconciliationRequest\x1a&.franchisee.ListReconciliationResponse\"0\x82\xd3\xe4\x93\x02*\x12(/api/v2/supply/statements/reconciliationb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_LISTRECONCILIATIONREQUEST = _descriptor.Descriptor(
  name='ListReconciliationRequest',
  full_name='franchisee.ListReconciliationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='query_dates', full_name='franchisee.ListReconciliationRequest.query_dates', index=0,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='franchisee.ListReconciliationRequest.store_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='franchisee.ListReconciliationRequest.fields', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee.ListReconciliationRequest.limit', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee.ListReconciliationRequest.offset', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='franchisee.ListReconciliationRequest.order_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='desc', full_name='franchisee.ListReconciliationRequest.desc', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='voucher', full_name='franchisee.ListReconciliationRequest.voucher', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=107,
  serialized_end=301,
)


_LISTRECONCILIATIONRESPONSE = _descriptor.Descriptor(
  name='ListReconciliationResponse',
  full_name='franchisee.ListReconciliationResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee.ListReconciliationResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee.ListReconciliationResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='begining_balance', full_name='franchisee.ListReconciliationResponse.begining_balance', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ending_balance', full_name='franchisee.ListReconciliationResponse.ending_balance', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='create_at', full_name='franchisee.ListReconciliationResponse.create_at', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msg', full_name='franchisee.ListReconciliationResponse.msg', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='uid', full_name='franchisee.ListReconciliationResponse.uid', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='franchisee.ListReconciliationResponse.code', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=304,
  serialized_end=526,
)


_STATEMENTSITEM = _descriptor.Descriptor(
  name='StatementsItem',
  full_name='franchisee.StatementsItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='uid', full_name='franchisee.StatementsItem.uid', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='uname', full_name='franchisee.StatementsItem.uname', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='voucher_no', full_name='franchisee.StatementsItem.voucher_no', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transcation_amount', full_name='franchisee.StatementsItem.transcation_amount', index=3,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='balance', full_name='franchisee.StatementsItem.balance', index=4,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account_amount', full_name='franchisee.StatementsItem.account_amount', index=5,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='credit', full_name='franchisee.StatementsItem.credit', index=6,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='address', full_name='franchisee.StatementsItem.address', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee.StatementsItem.id', index=8,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=529,
  serialized_end=707,
)

_LISTRECONCILIATIONREQUEST.fields_by_name['query_dates'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECONCILIATIONRESPONSE.fields_by_name['rows'].message_type = _STATEMENTSITEM
_LISTRECONCILIATIONRESPONSE.fields_by_name['create_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['ListReconciliationRequest'] = _LISTRECONCILIATIONREQUEST
DESCRIPTOR.message_types_by_name['ListReconciliationResponse'] = _LISTRECONCILIATIONRESPONSE
DESCRIPTOR.message_types_by_name['StatementsItem'] = _STATEMENTSITEM
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ListReconciliationRequest = _reflection.GeneratedProtocolMessageType('ListReconciliationRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTRECONCILIATIONREQUEST,
  __module__ = 'franchisee.statements_pb2'
  # @@protoc_insertion_point(class_scope:franchisee.ListReconciliationRequest)
  ))
_sym_db.RegisterMessage(ListReconciliationRequest)

ListReconciliationResponse = _reflection.GeneratedProtocolMessageType('ListReconciliationResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTRECONCILIATIONRESPONSE,
  __module__ = 'franchisee.statements_pb2'
  # @@protoc_insertion_point(class_scope:franchisee.ListReconciliationResponse)
  ))
_sym_db.RegisterMessage(ListReconciliationResponse)

StatementsItem = _reflection.GeneratedProtocolMessageType('StatementsItem', (_message.Message,), dict(
  DESCRIPTOR = _STATEMENTSITEM,
  __module__ = 'franchisee.statements_pb2'
  # @@protoc_insertion_point(class_scope:franchisee.StatementsItem)
  ))
_sym_db.RegisterMessage(StatementsItem)



_STATEMENTSSERVICE = _descriptor.ServiceDescriptor(
  name='StatementsService',
  full_name='franchisee.StatementsService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=710,
  serialized_end=881,
  methods=[
  _descriptor.MethodDescriptor(
    name='ListReconciliation',
    full_name='franchisee.StatementsService.ListReconciliation',
    index=0,
    containing_service=None,
    input_type=_LISTRECONCILIATIONREQUEST,
    output_type=_LISTRECONCILIATIONRESPONSE,
    serialized_options=_b('\202\323\344\223\002*\022(/api/v2/supply/statements/reconciliation'),
  ),
])
_sym_db.RegisterServiceDescriptor(_STATEMENTSSERVICE)

DESCRIPTOR.services_by_name['StatementsService'] = _STATEMENTSSERVICE

# @@protoc_insertion_point(module_scope)
