syntax = "proto3";
package franchisee;

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
// import "protoc-gen-swagger/options/annotations.proto";

service StatementsService{

    rpc ListReconciliation (ListReconciliationRequest) returns (ListReconciliationResponse) {
        option (google.api.http) = {
            get:"/api/v2/supply/statements/reconciliation"
        };
    }
}

message ListReconciliationRequest{
    // 时间范围
    google.protobuf.Timestamp query_dates = 15;
    // 门店
    string store_code = 2;
    // 搜索摘要
    string fields = 3;
    uint32 limit = 4;
    uint32 offset = 5;
    string order_type = 6;
    bool desc = 7;
    // 搜索凭证号
    string voucher = 8;
  }
  
  message ListReconciliationResponse{
    repeated StatementsItem rows = 1;
    int32 total = 2;
    // 期初余额
    double begining_balance = 3;
    // 账户余额
    double ending_balance = 4;
    google.protobuf.Timestamp create_at = 5;
    string msg = 6;
    // 客户编号
    string uid = 7;
    // code
    int32 code = 8;
  }
  
  message StatementsItem{
    // 客户编号
    string uid = 1;
    // 客户名称
    string uname = 2;
    // 凭证号
    string voucher_no = 3;
    // 交易金额
    double transcation_amount = 5;
    // 余额
    double balance = 6;
    // 账户额度
    double account_amount = 7;
    // 账期
    string credit = 8;
    // 地址
    string address = 9;
    uint64 id = 10;
  }