# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: franchisee/receiving_diff.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='franchisee/receiving_diff.proto',
  package='franchisee_receiving_diff',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1f\x66ranchisee/receiving_diff.proto\x12\x19\x66ranchisee_receiving_diff\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x9e\x02\n\x1a\x43reateReceivingDiffRequest\x12\x14\n\x0creceiving_id\x18\x01 \x01(\x04\x12K\n\x12\x63onfirmed_products\x18\x02 \x03(\x0b\x32/.franchisee_receiving_diff.ReceivingDiffProduct\x12\x11\n\treview_by\x18\x03 \x01(\x04\x12\x0b\n\x03lan\x18\x04 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x05 \x01(\t\x12\x31\n\rdelivery_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x11pre_delivery_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"8\n\x1b\x43reateReceivingDiffResponse\x12\x19\n\x11receiving_diff_id\x18\x01 \x01(\x04\"\xce\x04\n\x18ListReceivingDiffRequest\x12\x11\n\tstore_ids\x18\x01 \x03(\x04\x12\x0e\n\x06status\x18\x02 \x03(\t\x12.\n\nstart_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x05 \x01(\x05\x12\x0e\n\x06offset\x18\x06 \x01(\x05\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12\x11\n\tis_direct\x18\x08 \x01(\x08\x12\x15\n\rreceiving_ids\x18\t \x01(\x04\x12\x16\n\x0ereceiving_code\x18\n \x01(\t\x12\x0c\n\x04\x63ode\x18\x0c \x01(\t\x12\x16\n\x0elogistics_type\x18\r \x01(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\r\n\x05order\x18\x0f \x01(\t\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x34\n\x10return_date_from\x18\x12 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0ereturn_date_to\x18\x13 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x36\n\x12\x64\x65livery_date_from\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x10\x64\x65livery_date_to\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tdiff_type\x18\x16 \x01(\t\"b\n\x19ListReceivingDiffResponse\x12\x36\n\x04rows\x18\x01 \x03(\x0b\x32(.franchisee_receiving_diff.ReceivingDiff\x12\r\n\x05total\x18\x02 \x01(\x04\"\xd2\x04\n\x1cListReceivingDiffColdRequest\x12\x11\n\tstore_ids\x18\x01 \x03(\x04\x12\x0e\n\x06status\x18\x02 \x03(\t\x12.\n\nstart_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x05 \x01(\x05\x12\x0e\n\x06offset\x18\x06 \x01(\x05\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12\x11\n\tis_direct\x18\x08 \x01(\x08\x12\x15\n\rreceiving_ids\x18\t \x01(\x04\x12\x16\n\x0ereceiving_code\x18\n \x01(\t\x12\x0c\n\x04\x63ode\x18\x0c \x01(\t\x12\x16\n\x0elogistics_type\x18\r \x01(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\r\n\x05order\x18\x0f \x01(\t\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x34\n\x10return_date_from\x18\x12 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0ereturn_date_to\x18\x13 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x36\n\x12\x64\x65livery_date_from\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x10\x64\x65livery_date_to\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tdiff_type\x18\x16 \x01(\t\"f\n\x1dListReceivingDiffColdResponse\x12\x36\n\x04rows\x18\x01 \x03(\x0b\x32(.franchisee_receiving_diff.ReceivingDiff\x12\r\n\x05total\x18\x02 \x01(\x04\"l\n\x1bGetReceivingDiffByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12\x0b\n\x03lan\x18\x05 \x01(\t\"3\n\x1dGetReceivingDiffByArgsRequest\x12\x12\n\nrequest_id\x18\x01 \x03(\x04\"Q\n$GetReceivingDiffProductByArgsRequest\x12\x13\n\x0b\x64\x65livery_id\x18\x01 \x01(\x04\x12\x14\n\x0creceiving_id\x18\x02 \x01(\x04\"\x90\x01\n\"GetReceivingDiffProductByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12\x0c\n\x04sort\x18\x05 \x01(\t\x12\r\n\x05order\x18\x06 \x01(\t\x12\x0b\n\x03lan\x18\x07 \x01(\t\"s\n#GetReceivingDiffProductByIdResponse\x12=\n\x04rows\x18\x01 \x03(\x0b\x32/.franchisee_receiving_diff.ReceivingDiffProduct\x12\r\n\x05total\x18\x02 \x01(\x04\"5\n\x1aSubmitReceivingDiffRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\".\n\x1bSubmitReceivingDiffResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"5\n\x1a\x43onfirmReceivingDiffRequet\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"/\n\x1c\x43onfirmReceivingDiffResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"a\n\x1aRejectReceivingDiffRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x15\n\rreject_reason\x18\x02 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\".\n\x1bRejectReceivingDiffResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"5\n\x1a\x44\x65leteReceivingDiffRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\".\n\x1b\x44\x65leteReceivingDiffResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"\x90\x01\n\x1aUpdateReceivingDiffRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x34\n\x08products\x18\x02 \x03(\x0b\x32\".franchisee_receiving_diff.Product\x12\x13\n\x0b\x61ttachments\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\x12\x0e\n\x06remark\x18\x05 \x01(\t\".\n\x1bUpdateReceivingDiffResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"\xb3\x01\n\x1eUpdateReceivingDiffColdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x41\n\x08products\x18\x02 \x03(\x0b\x32/.franchisee_receiving_diff.ReceivingDiffProduct\x12\x0b\n\x03lan\x18\x03 \x01(\t\x12\x35\n\x11pre_delivery_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"2\n\x1fUpdateReceivingDiffColdResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"h\n UpdateReceivingDiffRemarkRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x38\n\x08products\x18\x02 \x03(\x0b\x32&.franchisee_receiving_diff.DiffProduct\"4\n!UpdateReceivingDiffRemarkResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"\xef\x07\n\rReceivingDiff\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x13\n\x0breceived_by\x18\x03 \x01(\x04\x12\x14\n\x0creceiving_id\x18\x04 \x01(\x04\x12\x16\n\x0ereceiving_code\x18\x05 \x01(\t\x12\x11\n\tmaster_id\x18\x06 \x01(\x04\x12\x13\n\x0bmaster_code\x18\x07 \x01(\t\x12\x0e\n\x06status\x18\x08 \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1c\n\x14\x64\x65livery_note_number\x18\n \x01(\t\x12\x13\n\x0b\x64\x65livery_by\x18\x0b \x01(\x04\x12\x31\n\rdelivery_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0breason_type\x18\r \x01(\t\x12\x11\n\treview_by\x18\x0e \x01(\x04\x12\x0e\n\x06remark\x18\x0f \x01(\t\x12\x1a\n\x12store_secondary_id\x18\x10 \x01(\t\x12\x18\n\x10inventory_status\x18\x11 \x01(\t\x12\x18\n\x10inventory_req_id\x18\x12 \x01(\x04\x12.\n\ncreated_at\x18\x13 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x15 \x01(\x04\x12\x12\n\nupdated_by\x18\x16 \x01(\x04\x12\x12\n\npartner_id\x18\x17 \x01(\x04\x12\x11\n\tis_direct\x18\x18 \x01(\x08\x12\x14\n\x0c\x63reated_name\x18\x19 \x01(\t\x12\x14\n\x0cupdated_name\x18\x1a \x01(\t\x12\x15\n\rreject_reason\x18\x1b \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x1c \x01(\t\x12\x14\n\x0cproduct_nums\x18\x1d \x01(\x04\x12\x15\n\rreceived_type\x18\x1f \x01(\t\x12\x12\n\nrequest_id\x18  \x01(\x04\x12\x16\n\x0elogistics_type\x18! \x01(\t\x12\x11\n\tsend_type\x18\" \x01(\t\x12\x35\n\x11pre_delivery_date\x18$ \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\treturn_id\x18% \x01(\x04\x12-\n\tsend_date\x18& \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\' \x01(\t\x12\x16\n\x0esub_receive_by\x18( \x01(\x04\x12\x11\n\tdiff_type\x18) \x01(\t\"\xbd\x08\n\x14ReceivingDiffProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x14\n\x0creceiving_id\x18\x02 \x01(\x04\x12\x13\n\x0breceived_by\x18\x03 \x01(\x04\x12\x12\n\nproduct_id\x18\x04 \x01(\x04\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x05 \x01(\x04\x12\x0f\n\x07unit_id\x18\x06 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x07 \x01(\t\x12\x14\n\x0cproduct_name\x18\x08 \x01(\t\x12\x17\n\x0fmaterial_number\x18\t \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\n \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x0b \x01(\t\x12\x11\n\tunit_name\x18\x0c \x01(\t\x12\x11\n\tunit_spec\x18\r \x01(\t\x12\x11\n\tunit_rate\x18\x0e \x01(\x01\x12\x10\n\x08quantity\x18\x11 \x01(\x01\x12$\n\x1creceived_accounting_quantity\x18\x12 \x01(\x01\x12\x19\n\x11received_quantity\x18\x13 \x01(\x01\x12%\n\x1d\x63onfirmed_accounting_quantity\x18\x14 \x01(\x01\x12\x1a\n\x12\x63onfirmed_quantity\x18\x15 \x01(\x01\x12 \n\x18\x64iff_accounting_quantity\x18\x16 \x01(\x01\x12\x15\n\rdiff_quantity\x18\x17 \x01(\x01\x12\"\n\x1as_diff_accounting_quantity\x18\x18 \x01(\x01\x12\x17\n\x0fs_diff_quantity\x18\x19 \x01(\x01\x12\"\n\x1a\x64_diff_accounting_quantity\x18\x1a \x01(\x01\x12\x17\n\x0f\x64_diff_quantity\x18\x1b \x01(\x01\x12/\n\x0b\x64\x65mand_date\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x1d \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x1e \x01(\x04\x12.\n\nupdated_at\x18\x1f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18  \x01(\x04\x12\x12\n\npartner_id\x18! \x01(\x04\x12\x0f\n\x07\x64iff_id\x18\" \x01(\x04\x12\x13\n\x0breason_type\x18# \x01(\t\x12\x15\n\rreject_reason\x18$ \x01(\t\x12\x13\n\x0b\x61ttachments\x18% \x01(\t\x12\x14\n\x0c\x63reated_name\x18& \x01(\t\x12\x14\n\x0cupdated_name\x18\' \x01(\t\x12\x0e\n\x06remark\x18( \x01(\t\x12\x12\n\ncost_price\x18) \x01(\x01\x12\x11\n\ttax_price\x18* \x01(\x01\x12\x10\n\x08tax_rate\x18+ \x01(\x01\x12\x16\n\x0esub_receive_by\x18, \x01(\x04\"l\n\x07Product\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x17\n\x0fs_diff_quantity\x18\x02 \x01(\x01\x12\x17\n\x0f\x64_diff_quantity\x18\x03 \x01(\x01\x12\x13\n\x0breason_type\x18\x04 \x01(\t\x12\x0e\n\x06remark\x18\x05 \x01(\t\"R\n\x0b\x44iffProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06remark\x18\x02 \x01(\t\x12\x13\n\x0breason_type\x18\x03 \x01(\t\x12\x12\n\ncreated_by\x18\x04 \x01(\x04\x32\xa9\x16\n\x14ReceivingDiffService\x12\xb9\x01\n\x13\x43reateReceivingDiff\x12\x35.franchisee_receiving_diff.CreateReceivingDiffRequest\x1a\x36.franchisee_receiving_diff.CreateReceivingDiffResponse\"3\x82\xd3\xe4\x93\x02-\"(/api/v2/supply/franchisee/receiving/diff:\x01*\x12\xb0\x01\n\x11ListReceivingDiff\x12\x33.franchisee_receiving_diff.ListReceivingDiffRequest\x1a\x34.franchisee_receiving_diff.ListReceivingDiffResponse\"0\x82\xd3\xe4\x93\x02*\x12(/api/v2/supply/franchisee/receiving_diff\x12\xc1\x01\n\x15ListReceivingDiffCold\x12\x37.franchisee_receiving_diff.ListReceivingDiffColdRequest\x1a\x38.franchisee_receiving_diff.ListReceivingDiffColdResponse\"5\x82\xd3\xe4\x93\x02/\x12-/api/v2/supply/franchisee/receiving/diff/cold\x12\xaf\x01\n\x14GetReceivingDiffById\x12\x36.franchisee_receiving_diff.GetReceivingDiffByIdRequest\x1a(.franchisee_receiving_diff.ReceivingDiff\"5\x82\xd3\xe4\x93\x02/\x12-/api/v2/supply/franchisee/receiving/diff/{id}\x12\xc4\x01\n\x16GetReceivingDiffByArgs\x12\x38.franchisee_receiving_diff.GetReceivingDiffByArgsRequest\x1a\x34.franchisee_receiving_diff.ListReceivingDiffResponse\":\x82\xd3\xe4\x93\x02\x34\"//api/v2/supply/franchisee/receiving/diff/byargs:\x01*\x12\xdb\x01\n\x1bGetReceivingDiffProductById\x12=.franchisee_receiving_diff.GetReceivingDiffProductByIdRequest\x1a>.franchisee_receiving_diff.GetReceivingDiffProductByIdResponse\"=\x82\xd3\xe4\x93\x02\x37\x12\x35/api/v2/supply/franchisee/receiving/diff/{id}/product\x12\xe4\x01\n\x1dGetReceivingDiffProductByArgs\x12?.franchisee_receiving_diff.GetReceivingDiffProductByArgsRequest\x1a>.franchisee_receiving_diff.GetReceivingDiffProductByIdResponse\"B\x82\xd3\xe4\x93\x02<\"7/api/v2/supply/franchisee/receiving/diff/product/byargs:\x01*\x12\xc5\x01\n\x13SubmitReceivingDiff\x12\x35.franchisee_receiving_diff.SubmitReceivingDiffRequest\x1a\x36.franchisee_receiving_diff.SubmitReceivingDiffResponse\"?\x82\xd3\xe4\x93\x02\x39\x1a\x34/api/v2/supply/franchisee/receiving/diff/{id}/submit:\x01*\x12\xc8\x01\n\x14\x43onfirmReceivingDiff\x12\x35.franchisee_receiving_diff.ConfirmReceivingDiffRequet\x1a\x37.franchisee_receiving_diff.ConfirmReceivingDiffResponse\"@\x82\xd3\xe4\x93\x02:\x1a\x35/api/v2/supply/franchisee/receiving/diff/{id}/confirm:\x01*\x12\xc5\x01\n\x13RejectReceivingDiff\x12\x35.franchisee_receiving_diff.RejectReceivingDiffRequest\x1a\x36.franchisee_receiving_diff.RejectReceivingDiffResponse\"?\x82\xd3\xe4\x93\x02\x39\x1a\x34/api/v2/supply/franchisee/receiving/diff/{id}/reject:\x01*\x12\xc5\x01\n\x13UpdateReceivingDiff\x12\x35.franchisee_receiving_diff.UpdateReceivingDiffRequest\x1a\x36.franchisee_receiving_diff.UpdateReceivingDiffResponse\"?\x82\xd3\xe4\x93\x02\x39\x1a\x34/api/v2/supply/franchisee/receiving/diff/{id}/update:\x01*\x12\xde\x01\n\x19UpdateReceivingDiffRemark\x12;.franchisee_receiving_diff.UpdateReceivingDiffRemarkRequest\x1a<.franchisee_receiving_diff.UpdateReceivingDiffRemarkResponse\"F\x82\xd3\xe4\x93\x02@\x1a;/api/v2/supply/franchisee/receiving/diff/{id}/update/remark:\x01*\x12\xd6\x01\n\x17UpdateReceivingDiffCold\x12\x39.franchisee_receiving_diff.UpdateReceivingDiffColdRequest\x1a:.franchisee_receiving_diff.UpdateReceivingDiffColdResponse\"D\x82\xd3\xe4\x93\x02>\x1a\x39/api/v2/supply/franchisee/receiving/diff/cold/{id}/update:\x01*\x12\xc2\x01\n\x13\x44\x65leteReceivingDiff\x12\x35.franchisee_receiving_diff.DeleteReceivingDiffRequest\x1a\x36.franchisee_receiving_diff.DeleteReceivingDiffResponse\"<\x82\xd3\xe4\x93\x02\x36\x1a\x34/api/v2/supply/franchisee/receiving/diff/{id}/deleteb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_CREATERECEIVINGDIFFREQUEST = _descriptor.Descriptor(
  name='CreateReceivingDiffRequest',
  full_name='franchisee_receiving_diff.CreateReceivingDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receiving_id', full_name='franchisee_receiving_diff.CreateReceivingDiffRequest.receiving_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_products', full_name='franchisee_receiving_diff.CreateReceivingDiffRequest.confirmed_products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='franchisee_receiving_diff.CreateReceivingDiffRequest.review_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receiving_diff.CreateReceivingDiffRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_receiving_diff.CreateReceivingDiffRequest.attachments', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='franchisee_receiving_diff.CreateReceivingDiffRequest.delivery_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_delivery_date', full_name='franchisee_receiving_diff.CreateReceivingDiffRequest.pre_delivery_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=126,
  serialized_end=412,
)


_CREATERECEIVINGDIFFRESPONSE = _descriptor.Descriptor(
  name='CreateReceivingDiffResponse',
  full_name='franchisee_receiving_diff.CreateReceivingDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receiving_diff_id', full_name='franchisee_receiving_diff.CreateReceivingDiffResponse.receiving_diff_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=414,
  serialized_end=470,
)


_LISTRECEIVINGDIFFREQUEST = _descriptor.Descriptor(
  name='ListReceivingDiffRequest',
  full_name='franchisee_receiving_diff.ListReceivingDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.store_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.start_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.end_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.limit', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.offset', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.is_direct', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_ids', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.receiving_ids', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_code', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.receiving_code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.code', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.logistics_type', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.sort', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.order', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.lan', index=14,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_from', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.return_date_from', index=15,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_to', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.return_date_to', index=16,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_from', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.delivery_date_from', index=17,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_to', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.delivery_date_to', index=18,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_type', full_name='franchisee_receiving_diff.ListReceivingDiffRequest.diff_type', index=19,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=473,
  serialized_end=1063,
)


_LISTRECEIVINGDIFFRESPONSE = _descriptor.Descriptor(
  name='ListReceivingDiffResponse',
  full_name='franchisee_receiving_diff.ListReceivingDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_receiving_diff.ListReceivingDiffResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_receiving_diff.ListReceivingDiffResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1065,
  serialized_end=1163,
)


_LISTRECEIVINGDIFFCOLDREQUEST = _descriptor.Descriptor(
  name='ListReceivingDiffColdRequest',
  full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.store_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.start_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.end_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.limit', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.offset', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.is_direct', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_ids', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.receiving_ids', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_code', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.receiving_code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.code', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.logistics_type', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.sort', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.order', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.lan', index=14,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_from', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.return_date_from', index=15,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_to', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.return_date_to', index=16,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_from', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.delivery_date_from', index=17,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_to', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.delivery_date_to', index=18,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_type', full_name='franchisee_receiving_diff.ListReceivingDiffColdRequest.diff_type', index=19,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1166,
  serialized_end=1760,
)


_LISTRECEIVINGDIFFCOLDRESPONSE = _descriptor.Descriptor(
  name='ListReceivingDiffColdResponse',
  full_name='franchisee_receiving_diff.ListReceivingDiffColdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_receiving_diff.ListReceivingDiffColdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_receiving_diff.ListReceivingDiffColdResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1762,
  serialized_end=1864,
)


_GETRECEIVINGDIFFBYIDREQUEST = _descriptor.Descriptor(
  name='GetReceivingDiffByIdRequest',
  full_name='franchisee_receiving_diff.GetReceivingDiffByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receiving_diff.GetReceivingDiffByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_receiving_diff.GetReceivingDiffByIdRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_receiving_diff.GetReceivingDiffByIdRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='franchisee_receiving_diff.GetReceivingDiffByIdRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receiving_diff.GetReceivingDiffByIdRequest.lan', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1866,
  serialized_end=1974,
)


_GETRECEIVINGDIFFBYARGSREQUEST = _descriptor.Descriptor(
  name='GetReceivingDiffByArgsRequest',
  full_name='franchisee_receiving_diff.GetReceivingDiffByArgsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='franchisee_receiving_diff.GetReceivingDiffByArgsRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1976,
  serialized_end=2027,
)


_GETRECEIVINGDIFFPRODUCTBYARGSREQUEST = _descriptor.Descriptor(
  name='GetReceivingDiffProductByArgsRequest',
  full_name='franchisee_receiving_diff.GetReceivingDiffProductByArgsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='delivery_id', full_name='franchisee_receiving_diff.GetReceivingDiffProductByArgsRequest.delivery_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_id', full_name='franchisee_receiving_diff.GetReceivingDiffProductByArgsRequest.receiving_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2029,
  serialized_end=2110,
)


_GETRECEIVINGDIFFPRODUCTBYIDREQUEST = _descriptor.Descriptor(
  name='GetReceivingDiffProductByIdRequest',
  full_name='franchisee_receiving_diff.GetReceivingDiffProductByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receiving_diff.GetReceivingDiffProductByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_receiving_diff.GetReceivingDiffProductByIdRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_receiving_diff.GetReceivingDiffProductByIdRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='franchisee_receiving_diff.GetReceivingDiffProductByIdRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='franchisee_receiving_diff.GetReceivingDiffProductByIdRequest.sort', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='franchisee_receiving_diff.GetReceivingDiffProductByIdRequest.order', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receiving_diff.GetReceivingDiffProductByIdRequest.lan', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2113,
  serialized_end=2257,
)


_GETRECEIVINGDIFFPRODUCTBYIDRESPONSE = _descriptor.Descriptor(
  name='GetReceivingDiffProductByIdResponse',
  full_name='franchisee_receiving_diff.GetReceivingDiffProductByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_receiving_diff.GetReceivingDiffProductByIdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_receiving_diff.GetReceivingDiffProductByIdResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2259,
  serialized_end=2374,
)


_SUBMITRECEIVINGDIFFREQUEST = _descriptor.Descriptor(
  name='SubmitReceivingDiffRequest',
  full_name='franchisee_receiving_diff.SubmitReceivingDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receiving_diff.SubmitReceivingDiffRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receiving_diff.SubmitReceivingDiffRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2376,
  serialized_end=2429,
)


_SUBMITRECEIVINGDIFFRESPONSE = _descriptor.Descriptor(
  name='SubmitReceivingDiffResponse',
  full_name='franchisee_receiving_diff.SubmitReceivingDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_receiving_diff.SubmitReceivingDiffResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2431,
  serialized_end=2477,
)


_CONFIRMRECEIVINGDIFFREQUET = _descriptor.Descriptor(
  name='ConfirmReceivingDiffRequet',
  full_name='franchisee_receiving_diff.ConfirmReceivingDiffRequet',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receiving_diff.ConfirmReceivingDiffRequet.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receiving_diff.ConfirmReceivingDiffRequet.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2479,
  serialized_end=2532,
)


_CONFIRMRECEIVINGDIFFRESPONSE = _descriptor.Descriptor(
  name='ConfirmReceivingDiffResponse',
  full_name='franchisee_receiving_diff.ConfirmReceivingDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_receiving_diff.ConfirmReceivingDiffResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2534,
  serialized_end=2581,
)


_REJECTRECEIVINGDIFFREQUEST = _descriptor.Descriptor(
  name='RejectReceivingDiffRequest',
  full_name='franchisee_receiving_diff.RejectReceivingDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receiving_diff.RejectReceivingDiffRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='franchisee_receiving_diff.RejectReceivingDiffRequest.reject_reason', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_receiving_diff.RejectReceivingDiffRequest.attachments', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receiving_diff.RejectReceivingDiffRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2583,
  serialized_end=2680,
)


_REJECTRECEIVINGDIFFRESPONSE = _descriptor.Descriptor(
  name='RejectReceivingDiffResponse',
  full_name='franchisee_receiving_diff.RejectReceivingDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_receiving_diff.RejectReceivingDiffResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2682,
  serialized_end=2728,
)


_DELETERECEIVINGDIFFREQUEST = _descriptor.Descriptor(
  name='DeleteReceivingDiffRequest',
  full_name='franchisee_receiving_diff.DeleteReceivingDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receiving_diff.DeleteReceivingDiffRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receiving_diff.DeleteReceivingDiffRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2730,
  serialized_end=2783,
)


_DELETERECEIVINGDIFFRESPONSE = _descriptor.Descriptor(
  name='DeleteReceivingDiffResponse',
  full_name='franchisee_receiving_diff.DeleteReceivingDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_receiving_diff.DeleteReceivingDiffResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2785,
  serialized_end=2831,
)


_UPDATERECEIVINGDIFFREQUEST = _descriptor.Descriptor(
  name='UpdateReceivingDiffRequest',
  full_name='franchisee_receiving_diff.UpdateReceivingDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receiving_diff.UpdateReceivingDiffRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='franchisee_receiving_diff.UpdateReceivingDiffRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_receiving_diff.UpdateReceivingDiffRequest.attachments', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receiving_diff.UpdateReceivingDiffRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_receiving_diff.UpdateReceivingDiffRequest.remark', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2834,
  serialized_end=2978,
)


_UPDATERECEIVINGDIFFRESPONSE = _descriptor.Descriptor(
  name='UpdateReceivingDiffResponse',
  full_name='franchisee_receiving_diff.UpdateReceivingDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_receiving_diff.UpdateReceivingDiffResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2980,
  serialized_end=3026,
)


_UPDATERECEIVINGDIFFCOLDREQUEST = _descriptor.Descriptor(
  name='UpdateReceivingDiffColdRequest',
  full_name='franchisee_receiving_diff.UpdateReceivingDiffColdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receiving_diff.UpdateReceivingDiffColdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='franchisee_receiving_diff.UpdateReceivingDiffColdRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receiving_diff.UpdateReceivingDiffColdRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_delivery_date', full_name='franchisee_receiving_diff.UpdateReceivingDiffColdRequest.pre_delivery_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3029,
  serialized_end=3208,
)


_UPDATERECEIVINGDIFFCOLDRESPONSE = _descriptor.Descriptor(
  name='UpdateReceivingDiffColdResponse',
  full_name='franchisee_receiving_diff.UpdateReceivingDiffColdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_receiving_diff.UpdateReceivingDiffColdResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3210,
  serialized_end=3260,
)


_UPDATERECEIVINGDIFFREMARKREQUEST = _descriptor.Descriptor(
  name='UpdateReceivingDiffRemarkRequest',
  full_name='franchisee_receiving_diff.UpdateReceivingDiffRemarkRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receiving_diff.UpdateReceivingDiffRemarkRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='franchisee_receiving_diff.UpdateReceivingDiffRemarkRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3262,
  serialized_end=3366,
)


_UPDATERECEIVINGDIFFREMARKRESPONSE = _descriptor.Descriptor(
  name='UpdateReceivingDiffRemarkResponse',
  full_name='franchisee_receiving_diff.UpdateReceivingDiffRemarkResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_receiving_diff.UpdateReceivingDiffRemarkResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3368,
  serialized_end=3420,
)


_RECEIVINGDIFF = _descriptor.Descriptor(
  name='ReceivingDiff',
  full_name='franchisee_receiving_diff.ReceivingDiff',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receiving_diff.ReceivingDiff.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='franchisee_receiving_diff.ReceivingDiff.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='franchisee_receiving_diff.ReceivingDiff.received_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_id', full_name='franchisee_receiving_diff.ReceivingDiff.receiving_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_code', full_name='franchisee_receiving_diff.ReceivingDiff.receiving_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='master_id', full_name='franchisee_receiving_diff.ReceivingDiff.master_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='master_code', full_name='franchisee_receiving_diff.ReceivingDiff.master_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_receiving_diff.ReceivingDiff.status', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='franchisee_receiving_diff.ReceivingDiff.demand_date', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_note_number', full_name='franchisee_receiving_diff.ReceivingDiff.delivery_note_number', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='franchisee_receiving_diff.ReceivingDiff.delivery_by', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='franchisee_receiving_diff.ReceivingDiff.delivery_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='franchisee_receiving_diff.ReceivingDiff.reason_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='franchisee_receiving_diff.ReceivingDiff.review_by', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_receiving_diff.ReceivingDiff.remark', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='franchisee_receiving_diff.ReceivingDiff.store_secondary_id', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_status', full_name='franchisee_receiving_diff.ReceivingDiff.inventory_status', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_req_id', full_name='franchisee_receiving_diff.ReceivingDiff.inventory_req_id', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='franchisee_receiving_diff.ReceivingDiff.created_at', index=18,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='franchisee_receiving_diff.ReceivingDiff.updated_at', index=19,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_receiving_diff.ReceivingDiff.created_by', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='franchisee_receiving_diff.ReceivingDiff.updated_by', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='franchisee_receiving_diff.ReceivingDiff.partner_id', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='franchisee_receiving_diff.ReceivingDiff.is_direct', index=23,
      number=24, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='franchisee_receiving_diff.ReceivingDiff.created_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='franchisee_receiving_diff.ReceivingDiff.updated_name', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='franchisee_receiving_diff.ReceivingDiff.reject_reason', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_receiving_diff.ReceivingDiff.attachments', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_nums', full_name='franchisee_receiving_diff.ReceivingDiff.product_nums', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_type', full_name='franchisee_receiving_diff.ReceivingDiff.received_type', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='franchisee_receiving_diff.ReceivingDiff.request_id', index=30,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='franchisee_receiving_diff.ReceivingDiff.logistics_type', index=31,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='send_type', full_name='franchisee_receiving_diff.ReceivingDiff.send_type', index=32,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_delivery_date', full_name='franchisee_receiving_diff.ReceivingDiff.pre_delivery_date', index=33,
      number=36, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_id', full_name='franchisee_receiving_diff.ReceivingDiff.return_id', index=34,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='send_date', full_name='franchisee_receiving_diff.ReceivingDiff.send_date', index=35,
      number=38, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='franchisee_receiving_diff.ReceivingDiff.branch_type', index=36,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='franchisee_receiving_diff.ReceivingDiff.sub_receive_by', index=37,
      number=40, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_type', full_name='franchisee_receiving_diff.ReceivingDiff.diff_type', index=38,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3423,
  serialized_end=4430,
)


_RECEIVINGDIFFPRODUCT = _descriptor.Descriptor(
  name='ReceivingDiffProduct',
  full_name='franchisee_receiving_diff.ReceivingDiffProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receiving_diff.ReceivingDiffProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_id', full_name='franchisee_receiving_diff.ReceivingDiffProduct.receiving_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='franchisee_receiving_diff.ReceivingDiffProduct.received_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='franchisee_receiving_diff.ReceivingDiffProduct.product_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='franchisee_receiving_diff.ReceivingDiffProduct.accounting_unit_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='franchisee_receiving_diff.ReceivingDiffProduct.unit_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='franchisee_receiving_diff.ReceivingDiffProduct.product_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='franchisee_receiving_diff.ReceivingDiffProduct.product_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='franchisee_receiving_diff.ReceivingDiffProduct.material_number', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='franchisee_receiving_diff.ReceivingDiffProduct.accounting_unit_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='franchisee_receiving_diff.ReceivingDiffProduct.accounting_unit_spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='franchisee_receiving_diff.ReceivingDiffProduct.unit_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='franchisee_receiving_diff.ReceivingDiffProduct.unit_spec', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='franchisee_receiving_diff.ReceivingDiffProduct.unit_rate', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='franchisee_receiving_diff.ReceivingDiffProduct.quantity', index=14,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_accounting_quantity', full_name='franchisee_receiving_diff.ReceivingDiffProduct.received_accounting_quantity', index=15,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_quantity', full_name='franchisee_receiving_diff.ReceivingDiffProduct.received_quantity', index=16,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_accounting_quantity', full_name='franchisee_receiving_diff.ReceivingDiffProduct.confirmed_accounting_quantity', index=17,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='franchisee_receiving_diff.ReceivingDiffProduct.confirmed_quantity', index=18,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_accounting_quantity', full_name='franchisee_receiving_diff.ReceivingDiffProduct.diff_accounting_quantity', index=19,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='franchisee_receiving_diff.ReceivingDiffProduct.diff_quantity', index=20,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_accounting_quantity', full_name='franchisee_receiving_diff.ReceivingDiffProduct.s_diff_accounting_quantity', index=21,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_quantity', full_name='franchisee_receiving_diff.ReceivingDiffProduct.s_diff_quantity', index=22,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_accounting_quantity', full_name='franchisee_receiving_diff.ReceivingDiffProduct.d_diff_accounting_quantity', index=23,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_quantity', full_name='franchisee_receiving_diff.ReceivingDiffProduct.d_diff_quantity', index=24,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='franchisee_receiving_diff.ReceivingDiffProduct.demand_date', index=25,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='franchisee_receiving_diff.ReceivingDiffProduct.created_at', index=26,
      number=29, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_receiving_diff.ReceivingDiffProduct.created_by', index=27,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='franchisee_receiving_diff.ReceivingDiffProduct.updated_at', index=28,
      number=31, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='franchisee_receiving_diff.ReceivingDiffProduct.updated_by', index=29,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='franchisee_receiving_diff.ReceivingDiffProduct.partner_id', index=30,
      number=33, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_id', full_name='franchisee_receiving_diff.ReceivingDiffProduct.diff_id', index=31,
      number=34, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='franchisee_receiving_diff.ReceivingDiffProduct.reason_type', index=32,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='franchisee_receiving_diff.ReceivingDiffProduct.reject_reason', index=33,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_receiving_diff.ReceivingDiffProduct.attachments', index=34,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='franchisee_receiving_diff.ReceivingDiffProduct.created_name', index=35,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='franchisee_receiving_diff.ReceivingDiffProduct.updated_name', index=36,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_receiving_diff.ReceivingDiffProduct.remark', index=37,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='franchisee_receiving_diff.ReceivingDiffProduct.cost_price', index=38,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='franchisee_receiving_diff.ReceivingDiffProduct.tax_price', index=39,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='franchisee_receiving_diff.ReceivingDiffProduct.tax_rate', index=40,
      number=43, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='franchisee_receiving_diff.ReceivingDiffProduct.sub_receive_by', index=41,
      number=44, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4433,
  serialized_end=5518,
)


_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='franchisee_receiving_diff.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receiving_diff.Product.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_quantity', full_name='franchisee_receiving_diff.Product.s_diff_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_quantity', full_name='franchisee_receiving_diff.Product.d_diff_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='franchisee_receiving_diff.Product.reason_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_receiving_diff.Product.remark', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5520,
  serialized_end=5628,
)


_DIFFPRODUCT = _descriptor.Descriptor(
  name='DiffProduct',
  full_name='franchisee_receiving_diff.DiffProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receiving_diff.DiffProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_receiving_diff.DiffProduct.remark', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='franchisee_receiving_diff.DiffProduct.reason_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_receiving_diff.DiffProduct.created_by', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5630,
  serialized_end=5712,
)

_CREATERECEIVINGDIFFREQUEST.fields_by_name['confirmed_products'].message_type = _RECEIVINGDIFFPRODUCT
_CREATERECEIVINGDIFFREQUEST.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATERECEIVINGDIFFREQUEST.fields_by_name['pre_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGDIFFREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGDIFFREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGDIFFREQUEST.fields_by_name['return_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGDIFFREQUEST.fields_by_name['return_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGDIFFREQUEST.fields_by_name['delivery_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGDIFFREQUEST.fields_by_name['delivery_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGDIFFRESPONSE.fields_by_name['rows'].message_type = _RECEIVINGDIFF
_LISTRECEIVINGDIFFCOLDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGDIFFCOLDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGDIFFCOLDREQUEST.fields_by_name['return_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGDIFFCOLDREQUEST.fields_by_name['return_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGDIFFCOLDREQUEST.fields_by_name['delivery_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGDIFFCOLDREQUEST.fields_by_name['delivery_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVINGDIFFCOLDRESPONSE.fields_by_name['rows'].message_type = _RECEIVINGDIFF
_GETRECEIVINGDIFFPRODUCTBYIDRESPONSE.fields_by_name['rows'].message_type = _RECEIVINGDIFFPRODUCT
_UPDATERECEIVINGDIFFREQUEST.fields_by_name['products'].message_type = _PRODUCT
_UPDATERECEIVINGDIFFCOLDREQUEST.fields_by_name['products'].message_type = _RECEIVINGDIFFPRODUCT
_UPDATERECEIVINGDIFFCOLDREQUEST.fields_by_name['pre_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATERECEIVINGDIFFREMARKREQUEST.fields_by_name['products'].message_type = _DIFFPRODUCT
_RECEIVINGDIFF.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVINGDIFF.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVINGDIFF.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVINGDIFF.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVINGDIFF.fields_by_name['pre_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVINGDIFF.fields_by_name['send_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVINGDIFFPRODUCT.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVINGDIFFPRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVINGDIFFPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['CreateReceivingDiffRequest'] = _CREATERECEIVINGDIFFREQUEST
DESCRIPTOR.message_types_by_name['CreateReceivingDiffResponse'] = _CREATERECEIVINGDIFFRESPONSE
DESCRIPTOR.message_types_by_name['ListReceivingDiffRequest'] = _LISTRECEIVINGDIFFREQUEST
DESCRIPTOR.message_types_by_name['ListReceivingDiffResponse'] = _LISTRECEIVINGDIFFRESPONSE
DESCRIPTOR.message_types_by_name['ListReceivingDiffColdRequest'] = _LISTRECEIVINGDIFFCOLDREQUEST
DESCRIPTOR.message_types_by_name['ListReceivingDiffColdResponse'] = _LISTRECEIVINGDIFFCOLDRESPONSE
DESCRIPTOR.message_types_by_name['GetReceivingDiffByIdRequest'] = _GETRECEIVINGDIFFBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetReceivingDiffByArgsRequest'] = _GETRECEIVINGDIFFBYARGSREQUEST
DESCRIPTOR.message_types_by_name['GetReceivingDiffProductByArgsRequest'] = _GETRECEIVINGDIFFPRODUCTBYARGSREQUEST
DESCRIPTOR.message_types_by_name['GetReceivingDiffProductByIdRequest'] = _GETRECEIVINGDIFFPRODUCTBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetReceivingDiffProductByIdResponse'] = _GETRECEIVINGDIFFPRODUCTBYIDRESPONSE
DESCRIPTOR.message_types_by_name['SubmitReceivingDiffRequest'] = _SUBMITRECEIVINGDIFFREQUEST
DESCRIPTOR.message_types_by_name['SubmitReceivingDiffResponse'] = _SUBMITRECEIVINGDIFFRESPONSE
DESCRIPTOR.message_types_by_name['ConfirmReceivingDiffRequet'] = _CONFIRMRECEIVINGDIFFREQUET
DESCRIPTOR.message_types_by_name['ConfirmReceivingDiffResponse'] = _CONFIRMRECEIVINGDIFFRESPONSE
DESCRIPTOR.message_types_by_name['RejectReceivingDiffRequest'] = _REJECTRECEIVINGDIFFREQUEST
DESCRIPTOR.message_types_by_name['RejectReceivingDiffResponse'] = _REJECTRECEIVINGDIFFRESPONSE
DESCRIPTOR.message_types_by_name['DeleteReceivingDiffRequest'] = _DELETERECEIVINGDIFFREQUEST
DESCRIPTOR.message_types_by_name['DeleteReceivingDiffResponse'] = _DELETERECEIVINGDIFFRESPONSE
DESCRIPTOR.message_types_by_name['UpdateReceivingDiffRequest'] = _UPDATERECEIVINGDIFFREQUEST
DESCRIPTOR.message_types_by_name['UpdateReceivingDiffResponse'] = _UPDATERECEIVINGDIFFRESPONSE
DESCRIPTOR.message_types_by_name['UpdateReceivingDiffColdRequest'] = _UPDATERECEIVINGDIFFCOLDREQUEST
DESCRIPTOR.message_types_by_name['UpdateReceivingDiffColdResponse'] = _UPDATERECEIVINGDIFFCOLDRESPONSE
DESCRIPTOR.message_types_by_name['UpdateReceivingDiffRemarkRequest'] = _UPDATERECEIVINGDIFFREMARKREQUEST
DESCRIPTOR.message_types_by_name['UpdateReceivingDiffRemarkResponse'] = _UPDATERECEIVINGDIFFREMARKRESPONSE
DESCRIPTOR.message_types_by_name['ReceivingDiff'] = _RECEIVINGDIFF
DESCRIPTOR.message_types_by_name['ReceivingDiffProduct'] = _RECEIVINGDIFFPRODUCT
DESCRIPTOR.message_types_by_name['Product'] = _PRODUCT
DESCRIPTOR.message_types_by_name['DiffProduct'] = _DIFFPRODUCT
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CreateReceivingDiffRequest = _reflection.GeneratedProtocolMessageType('CreateReceivingDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATERECEIVINGDIFFREQUEST,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.CreateReceivingDiffRequest)
  ))
_sym_db.RegisterMessage(CreateReceivingDiffRequest)

CreateReceivingDiffResponse = _reflection.GeneratedProtocolMessageType('CreateReceivingDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATERECEIVINGDIFFRESPONSE,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.CreateReceivingDiffResponse)
  ))
_sym_db.RegisterMessage(CreateReceivingDiffResponse)

ListReceivingDiffRequest = _reflection.GeneratedProtocolMessageType('ListReceivingDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTRECEIVINGDIFFREQUEST,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.ListReceivingDiffRequest)
  ))
_sym_db.RegisterMessage(ListReceivingDiffRequest)

ListReceivingDiffResponse = _reflection.GeneratedProtocolMessageType('ListReceivingDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTRECEIVINGDIFFRESPONSE,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.ListReceivingDiffResponse)
  ))
_sym_db.RegisterMessage(ListReceivingDiffResponse)

ListReceivingDiffColdRequest = _reflection.GeneratedProtocolMessageType('ListReceivingDiffColdRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTRECEIVINGDIFFCOLDREQUEST,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.ListReceivingDiffColdRequest)
  ))
_sym_db.RegisterMessage(ListReceivingDiffColdRequest)

ListReceivingDiffColdResponse = _reflection.GeneratedProtocolMessageType('ListReceivingDiffColdResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTRECEIVINGDIFFCOLDRESPONSE,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.ListReceivingDiffColdResponse)
  ))
_sym_db.RegisterMessage(ListReceivingDiffColdResponse)

GetReceivingDiffByIdRequest = _reflection.GeneratedProtocolMessageType('GetReceivingDiffByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGDIFFBYIDREQUEST,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.GetReceivingDiffByIdRequest)
  ))
_sym_db.RegisterMessage(GetReceivingDiffByIdRequest)

GetReceivingDiffByArgsRequest = _reflection.GeneratedProtocolMessageType('GetReceivingDiffByArgsRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGDIFFBYARGSREQUEST,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.GetReceivingDiffByArgsRequest)
  ))
_sym_db.RegisterMessage(GetReceivingDiffByArgsRequest)

GetReceivingDiffProductByArgsRequest = _reflection.GeneratedProtocolMessageType('GetReceivingDiffProductByArgsRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGDIFFPRODUCTBYARGSREQUEST,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.GetReceivingDiffProductByArgsRequest)
  ))
_sym_db.RegisterMessage(GetReceivingDiffProductByArgsRequest)

GetReceivingDiffProductByIdRequest = _reflection.GeneratedProtocolMessageType('GetReceivingDiffProductByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGDIFFPRODUCTBYIDREQUEST,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.GetReceivingDiffProductByIdRequest)
  ))
_sym_db.RegisterMessage(GetReceivingDiffProductByIdRequest)

GetReceivingDiffProductByIdResponse = _reflection.GeneratedProtocolMessageType('GetReceivingDiffProductByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGDIFFPRODUCTBYIDRESPONSE,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.GetReceivingDiffProductByIdResponse)
  ))
_sym_db.RegisterMessage(GetReceivingDiffProductByIdResponse)

SubmitReceivingDiffRequest = _reflection.GeneratedProtocolMessageType('SubmitReceivingDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITRECEIVINGDIFFREQUEST,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.SubmitReceivingDiffRequest)
  ))
_sym_db.RegisterMessage(SubmitReceivingDiffRequest)

SubmitReceivingDiffResponse = _reflection.GeneratedProtocolMessageType('SubmitReceivingDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITRECEIVINGDIFFRESPONSE,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.SubmitReceivingDiffResponse)
  ))
_sym_db.RegisterMessage(SubmitReceivingDiffResponse)

ConfirmReceivingDiffRequet = _reflection.GeneratedProtocolMessageType('ConfirmReceivingDiffRequet', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMRECEIVINGDIFFREQUET,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.ConfirmReceivingDiffRequet)
  ))
_sym_db.RegisterMessage(ConfirmReceivingDiffRequet)

ConfirmReceivingDiffResponse = _reflection.GeneratedProtocolMessageType('ConfirmReceivingDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMRECEIVINGDIFFRESPONSE,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.ConfirmReceivingDiffResponse)
  ))
_sym_db.RegisterMessage(ConfirmReceivingDiffResponse)

RejectReceivingDiffRequest = _reflection.GeneratedProtocolMessageType('RejectReceivingDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _REJECTRECEIVINGDIFFREQUEST,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.RejectReceivingDiffRequest)
  ))
_sym_db.RegisterMessage(RejectReceivingDiffRequest)

RejectReceivingDiffResponse = _reflection.GeneratedProtocolMessageType('RejectReceivingDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _REJECTRECEIVINGDIFFRESPONSE,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.RejectReceivingDiffResponse)
  ))
_sym_db.RegisterMessage(RejectReceivingDiffResponse)

DeleteReceivingDiffRequest = _reflection.GeneratedProtocolMessageType('DeleteReceivingDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETERECEIVINGDIFFREQUEST,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.DeleteReceivingDiffRequest)
  ))
_sym_db.RegisterMessage(DeleteReceivingDiffRequest)

DeleteReceivingDiffResponse = _reflection.GeneratedProtocolMessageType('DeleteReceivingDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETERECEIVINGDIFFRESPONSE,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.DeleteReceivingDiffResponse)
  ))
_sym_db.RegisterMessage(DeleteReceivingDiffResponse)

UpdateReceivingDiffRequest = _reflection.GeneratedProtocolMessageType('UpdateReceivingDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERECEIVINGDIFFREQUEST,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.UpdateReceivingDiffRequest)
  ))
_sym_db.RegisterMessage(UpdateReceivingDiffRequest)

UpdateReceivingDiffResponse = _reflection.GeneratedProtocolMessageType('UpdateReceivingDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERECEIVINGDIFFRESPONSE,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.UpdateReceivingDiffResponse)
  ))
_sym_db.RegisterMessage(UpdateReceivingDiffResponse)

UpdateReceivingDiffColdRequest = _reflection.GeneratedProtocolMessageType('UpdateReceivingDiffColdRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERECEIVINGDIFFCOLDREQUEST,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.UpdateReceivingDiffColdRequest)
  ))
_sym_db.RegisterMessage(UpdateReceivingDiffColdRequest)

UpdateReceivingDiffColdResponse = _reflection.GeneratedProtocolMessageType('UpdateReceivingDiffColdResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERECEIVINGDIFFCOLDRESPONSE,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.UpdateReceivingDiffColdResponse)
  ))
_sym_db.RegisterMessage(UpdateReceivingDiffColdResponse)

UpdateReceivingDiffRemarkRequest = _reflection.GeneratedProtocolMessageType('UpdateReceivingDiffRemarkRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERECEIVINGDIFFREMARKREQUEST,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.UpdateReceivingDiffRemarkRequest)
  ))
_sym_db.RegisterMessage(UpdateReceivingDiffRemarkRequest)

UpdateReceivingDiffRemarkResponse = _reflection.GeneratedProtocolMessageType('UpdateReceivingDiffRemarkResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERECEIVINGDIFFREMARKRESPONSE,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.UpdateReceivingDiffRemarkResponse)
  ))
_sym_db.RegisterMessage(UpdateReceivingDiffRemarkResponse)

ReceivingDiff = _reflection.GeneratedProtocolMessageType('ReceivingDiff', (_message.Message,), dict(
  DESCRIPTOR = _RECEIVINGDIFF,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.ReceivingDiff)
  ))
_sym_db.RegisterMessage(ReceivingDiff)

ReceivingDiffProduct = _reflection.GeneratedProtocolMessageType('ReceivingDiffProduct', (_message.Message,), dict(
  DESCRIPTOR = _RECEIVINGDIFFPRODUCT,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.ReceivingDiffProduct)
  ))
_sym_db.RegisterMessage(ReceivingDiffProduct)

Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCT,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.Product)
  ))
_sym_db.RegisterMessage(Product)

DiffProduct = _reflection.GeneratedProtocolMessageType('DiffProduct', (_message.Message,), dict(
  DESCRIPTOR = _DIFFPRODUCT,
  __module__ = 'franchisee.receiving_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receiving_diff.DiffProduct)
  ))
_sym_db.RegisterMessage(DiffProduct)



_RECEIVINGDIFFSERVICE = _descriptor.ServiceDescriptor(
  name='ReceivingDiffService',
  full_name='franchisee_receiving_diff.ReceivingDiffService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=5715,
  serialized_end=8572,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateReceivingDiff',
    full_name='franchisee_receiving_diff.ReceivingDiffService.CreateReceivingDiff',
    index=0,
    containing_service=None,
    input_type=_CREATERECEIVINGDIFFREQUEST,
    output_type=_CREATERECEIVINGDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\002-\"(/api/v2/supply/franchisee/receiving/diff:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListReceivingDiff',
    full_name='franchisee_receiving_diff.ReceivingDiffService.ListReceivingDiff',
    index=1,
    containing_service=None,
    input_type=_LISTRECEIVINGDIFFREQUEST,
    output_type=_LISTRECEIVINGDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\002*\022(/api/v2/supply/franchisee/receiving_diff'),
  ),
  _descriptor.MethodDescriptor(
    name='ListReceivingDiffCold',
    full_name='franchisee_receiving_diff.ReceivingDiffService.ListReceivingDiffCold',
    index=2,
    containing_service=None,
    input_type=_LISTRECEIVINGDIFFCOLDREQUEST,
    output_type=_LISTRECEIVINGDIFFCOLDRESPONSE,
    serialized_options=_b('\202\323\344\223\002/\022-/api/v2/supply/franchisee/receiving/diff/cold'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReceivingDiffById',
    full_name='franchisee_receiving_diff.ReceivingDiffService.GetReceivingDiffById',
    index=3,
    containing_service=None,
    input_type=_GETRECEIVINGDIFFBYIDREQUEST,
    output_type=_RECEIVINGDIFF,
    serialized_options=_b('\202\323\344\223\002/\022-/api/v2/supply/franchisee/receiving/diff/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReceivingDiffByArgs',
    full_name='franchisee_receiving_diff.ReceivingDiffService.GetReceivingDiffByArgs',
    index=4,
    containing_service=None,
    input_type=_GETRECEIVINGDIFFBYARGSREQUEST,
    output_type=_LISTRECEIVINGDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\0024\"//api/v2/supply/franchisee/receiving/diff/byargs:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReceivingDiffProductById',
    full_name='franchisee_receiving_diff.ReceivingDiffService.GetReceivingDiffProductById',
    index=5,
    containing_service=None,
    input_type=_GETRECEIVINGDIFFPRODUCTBYIDREQUEST,
    output_type=_GETRECEIVINGDIFFPRODUCTBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\0225/api/v2/supply/franchisee/receiving/diff/{id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReceivingDiffProductByArgs',
    full_name='franchisee_receiving_diff.ReceivingDiffService.GetReceivingDiffProductByArgs',
    index=6,
    containing_service=None,
    input_type=_GETRECEIVINGDIFFPRODUCTBYARGSREQUEST,
    output_type=_GETRECEIVINGDIFFPRODUCTBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002<\"7/api/v2/supply/franchisee/receiving/diff/product/byargs:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='SubmitReceivingDiff',
    full_name='franchisee_receiving_diff.ReceivingDiffService.SubmitReceivingDiff',
    index=7,
    containing_service=None,
    input_type=_SUBMITRECEIVINGDIFFREQUEST,
    output_type=_SUBMITRECEIVINGDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\0324/api/v2/supply/franchisee/receiving/diff/{id}/submit:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmReceivingDiff',
    full_name='franchisee_receiving_diff.ReceivingDiffService.ConfirmReceivingDiff',
    index=8,
    containing_service=None,
    input_type=_CONFIRMRECEIVINGDIFFREQUET,
    output_type=_CONFIRMRECEIVINGDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\002:\0325/api/v2/supply/franchisee/receiving/diff/{id}/confirm:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='RejectReceivingDiff',
    full_name='franchisee_receiving_diff.ReceivingDiffService.RejectReceivingDiff',
    index=9,
    containing_service=None,
    input_type=_REJECTRECEIVINGDIFFREQUEST,
    output_type=_REJECTRECEIVINGDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\0324/api/v2/supply/franchisee/receiving/diff/{id}/reject:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateReceivingDiff',
    full_name='franchisee_receiving_diff.ReceivingDiffService.UpdateReceivingDiff',
    index=10,
    containing_service=None,
    input_type=_UPDATERECEIVINGDIFFREQUEST,
    output_type=_UPDATERECEIVINGDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\0324/api/v2/supply/franchisee/receiving/diff/{id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateReceivingDiffRemark',
    full_name='franchisee_receiving_diff.ReceivingDiffService.UpdateReceivingDiffRemark',
    index=11,
    containing_service=None,
    input_type=_UPDATERECEIVINGDIFFREMARKREQUEST,
    output_type=_UPDATERECEIVINGDIFFREMARKRESPONSE,
    serialized_options=_b('\202\323\344\223\002@\032;/api/v2/supply/franchisee/receiving/diff/{id}/update/remark:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateReceivingDiffCold',
    full_name='franchisee_receiving_diff.ReceivingDiffService.UpdateReceivingDiffCold',
    index=12,
    containing_service=None,
    input_type=_UPDATERECEIVINGDIFFCOLDREQUEST,
    output_type=_UPDATERECEIVINGDIFFCOLDRESPONSE,
    serialized_options=_b('\202\323\344\223\002>\0329/api/v2/supply/franchisee/receiving/diff/cold/{id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteReceivingDiff',
    full_name='franchisee_receiving_diff.ReceivingDiffService.DeleteReceivingDiff',
    index=13,
    containing_service=None,
    input_type=_DELETERECEIVINGDIFFREQUEST,
    output_type=_DELETERECEIVINGDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\0026\0324/api/v2/supply/franchisee/receiving/diff/{id}/delete'),
  ),
])
_sym_db.RegisterServiceDescriptor(_RECEIVINGDIFFSERVICE)

DESCRIPTOR.services_by_name['ReceivingDiffService'] = _RECEIVINGDIFFSERVICE

# @@protoc_insertion_point(module_scope)
