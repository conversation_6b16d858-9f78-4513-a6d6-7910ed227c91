# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: returns.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='returns.proto',
  package='returns',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\rreturns.proto\x12\x07returns\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x1e\n\x0b\x43ommonReply\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\xd9\x02\n\x13\x43reateReturnRequest\x12\x11\n\treturn_by\x18\x01 \x01(\x04\x12\x16\n\x0elogistics_type\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x10\n\x08sub_type\x18\x04 \x01(\t\x12\x38\n\x14return_delivery_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tsource_id\x18\x06 \x01(\x04\x12\x13\n\x0bsource_code\x18\x07 \x01(\t\x12\x15\n\rreturn_reason\x18\x08 \x01(\t\x12\x0e\n\x06remark\x18\t \x01(\t\x12)\n\x0b\x61ttachments\x18\n \x03(\x0b\x32\x14.returns.Attachments\x12\x12\n\nrequest_id\x18\x0b \x01(\x04\x12\"\n\x08products\x18\x0c \x03(\x0b\x32\x10.returns.Product\x12\x0b\n\x03lan\x18\x14 \x01(\t\"\x87\x03\n\x1b\x43heckReturnAvailableRequest\x12\x11\n\treturn_by\x18\x01 \x01(\x04\x12\x38\n\x14return_delivery_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x10\n\x08sub_type\x18\x04 \x01(\t\x12\x15\n\rreturn_reason\x18\x05 \x01(\t\x12\x0e\n\x06remark\x18\x06 \x01(\t\x12\"\n\x08products\x18\x07 \x03(\x0b\x32\x10.returns.Product\x12\x11\n\tis_direct\x18\x08 \x01(\x08\x12\x11\n\treturn_to\x18\t \x01(\x04\x12)\n\x0b\x61ttachments\x18\n \x03(\x0b\x32\x14.returns.Attachments\x12\x16\n\x0elogistics_type\x18\x0b \x01(\t\x12\x11\n\tis_adjust\x18\x0c \x01(\x08\x12\x14\n\x0creceiving_id\x18\r \x01(\x04\x12\x0b\n\x03lan\x18\x0e \x01(\t\x12\x11\n\treturn_id\x18\x0f \x01(\x04\":\n\x14\x43reateReturnResponse\x12\x11\n\treturn_id\x18\x01 \x03(\x04\x12\x0f\n\x07payload\x18\x02 \x01(\x08\"}\n\x16GetValidProductRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12\x14\n\x0cproduct_name\x18\x06 \x01(\t\x12\x0b\n\x03lan\x18\x07 \x01(\t\"*\n\x17GetValidProductResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"\x80\x06\n\x11ListReturnRequest\x12\x11\n\tstore_ids\x18\x01 \x03(\x04\x12\x0e\n\x06status\x18\x02 \x03(\t\x12.\n\nstart_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\r\n\x05limit\x18\x06 \x01(\x05\x12\x0e\n\x06offset\x18\x07 \x01(\x05\x12\x15\n\rinclude_total\x18\x08 \x01(\x08\x12\x11\n\tis_direct\x18\t \x01(\x08\x12\x10\n\x08order_by\x18\n \x01(\t\x12\x16\n\x0elogistics_type\x18\x0c \x01(\t\x12\x11\n\tis_adjust\x18\r \x01(\x08\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\r\n\x05order\x18\x0f \x01(\t\x12\x14\n\x0cstorage_type\x18\x11 \x01(\t\x12\x0c\n\x04type\x18\x12 \x01(\t\x12\x10\n\x08sub_type\x18\x13 \x01(\t\x12\x14\n\x0creceive_code\x18\x14 \x01(\t\x12\x0b\n\x03lan\x18\x15 \x01(\t\x12\x34\n\x10return_date_from\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0ereturn_date_to\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x36\n\x12\x64\x65livery_date_from\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x10\x64\x65livery_date_to\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rreturn_number\x18\x1a \x01(\t\x12\x13\n\x0bproduct_ids\x18\x1b \x03(\x04\x12\x11\n\treturn_by\x18\x1c \x03(\x04\x12\x11\n\treturn_to\x18\x1d \x03(\x04\x12\x11\n\tsource_id\x18\x1e \x03(\x04\x12\x13\n\x0bsource_code\x18\x1f \x01(\t\x12\x0e\n\x06is_frs\x18  \x01(\x08\"C\n\x12ListReturnResponse\x12\x1e\n\x04rows\x18\x01 \x03(\x0b\x32\x10.returns.Returns\x12\r\n\x05total\x18\x02 \x01(\x04\"\xb2\x04\n\x15ListReturnColdRequest\x12\x11\n\tstore_ids\x18\x01 \x03(\x04\x12\x0e\n\x06status\x18\x02 \x03(\t\x12.\n\nstart_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\r\n\x05limit\x18\x06 \x01(\x05\x12\x0e\n\x06offset\x18\x07 \x01(\x05\x12\x15\n\rinclude_total\x18\x08 \x01(\x08\x12\x11\n\tis_direct\x18\t \x01(\x08\x12\x16\n\x0elogistics_type\x18\x0c \x01(\t\x12\x11\n\tis_adjust\x18\r \x01(\x08\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\r\n\x05order\x18\x0f \x01(\t\x12\x14\n\x0cstorage_type\x18\x11 \x01(\t\x12\x0b\n\x03lan\x18\x12 \x01(\t\x12\x34\n\x10return_date_from\x18\x13 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0ereturn_date_to\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x36\n\x12\x64\x65livery_date_from\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x10\x64\x65livery_date_to\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"G\n\x16ListReturnColdResponse\x12\x1e\n\x04rows\x18\x01 \x03(\x0b\x32\x10.returns.Returns\x12\r\n\x05total\x18\x02 \x01(\x04\"/\n\x14GetReturnByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"@\n\x15GetReturnByIdResponse\x12\'\n\rreturn_detail\x18\x01 \x01(\x0b\x32\x10.returns.Returns\"\x89\x01\n\x1bGetReturnProductByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\r\n\x05order\x18\x0f \x01(\t\x12\x0b\n\x03lan\x18\x10 \x01(\t\"S\n\x1cGetReturnProductByIdResponse\x12$\n\x04rows\x18\x01 \x03(\x0b\x32\x16.returns.ProductDetail\x12\r\n\x05total\x18\x02 \x01(\x04\".\n\x13SubmitReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\'\n\x14SubmitReturnResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"E\n\x13RejectReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x15\n\rreject_reason\x18\x02 \x01(\t\x12\x0b\n\x03lan\x18\x03 \x01(\t\"\'\n\x14RejectReturnResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"[\n\x14\x41pproveReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\ntrans_type\x18\x02 \x01(\t\x12\x0b\n\x03lan\x18\x03 \x01(\t\x12\x16\n\x0ewarehouse_type\x18\x04 \x01(\t\"(\n\x15\x41pproveReturnResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"/\n\x14\x43onfirmReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"(\n\x15\x43onfirmReturnResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"A\n\x15\x44\x65liveryReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\x12\x0f\n\x07\x66s_code\x18\x03 \x01(\t\")\n\x16\x44\x65liveryReturnResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"\x89\x02\n\x13UpdateReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\"\n\x08products\x18\x02 \x03(\x0b\x32\x10.returns.Product\x12\x15\n\rreturn_reason\x18\x03 \x01(\t\x12\x0e\n\x06remark\x18\x04 \x01(\t\x12\x38\n\x14return_delivery_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\treturn_to\x18\x06 \x01(\x04\x12)\n\x0b\x61ttachments\x18\x07 \x03(\x0b\x32\x14.returns.Attachments\x12\x0b\n\x03lan\x18\x08 \x01(\t\x12\x16\n\x0elogistics_type\x18\t \x01(\t\"\'\n\x14UpdateReturnResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\">\n\x13UpdateRemarkRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06remark\x18\x02 \x01(\t\x12\x0b\n\x03lan\x18\x03 \x01(\t\"\'\n\x14UpdateRemarkResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\".\n\x13\x44\x65leteReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\'\n\x14\x44\x65leteReturnResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"\xee\x02\n\x07Product\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x0f\n\x07unit_id\x18\x02 \x01(\x04\x12\x10\n\x08quantity\x18\x03 \x01(\x01\x12\x1a\n\x12\x63onfirmed_quantity\x18\x04 \x01(\x01\x12\x14\n\x0cproduct_code\x18\x05 \x01(\t\x12\x14\n\x0cproduct_name\x18\x06 \x01(\t\x12\x11\n\tunit_name\x18\x07 \x01(\t\x12\x11\n\tunit_spec\x18\x08 \x01(\t\x12\x11\n\tunit_rate\x18\t \x01(\x02\x12\x10\n\x08tax_rate\x18\n \x01(\x02\x12\r\n\x05price\x18\x0b \x01(\x02\x12\x11\n\tprice_tax\x18\x0c \x01(\x02\x12\n\n\x02id\x18\r \x01(\x04\x12\x11\n\treturn_to\x18\x0e \x01(\x04\x12\x16\n\x0elogistics_type\x18\x0f \x01(\t\x12)\n\x0b\x61ttachments\x18\x10 \x03(\x0b\x32\x14.returns.Attachments\x12\x15\n\rrefund_amount\x18\x14 \x01(\x01\"\xda\n\n\x07Returns\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x11\n\treturn_by\x18\x03 \x01(\x04\x12\x15\n\rreturn_number\x18\x04 \x01(\t\x12\x0e\n\x06status\x18\x05 \x01(\t\x12\x11\n\treview_by\x18\x06 \x01(\x04\x12\x13\n\x0bis_returned\x18\x07 \x01(\x08\x12\x1e\n\x16return_delivery_number\x18\x08 \x01(\t\x12/\n\x0breturn_date\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x38\n\x14return_delivery_date\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rreturn_reason\x18\x0b \x01(\t\x12\x0c\n\x04type\x18\x0c \x01(\t\x12\x10\n\x08sub_type\x18\r \x01(\t\x12\x0e\n\x06remark\x18\x0e \x01(\t\x12\x1a\n\x12store_secondary_id\x18\x0f \x01(\t\x12.\n\ncreated_at\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x12 \x01(\x04\x12\x12\n\nupdated_by\x18\x13 \x01(\x04\x12\x11\n\treturn_to\x18\x14 \x01(\x04\x12\x18\n\x10inventory_status\x18\x15 \x01(\t\x12\x18\n\x10inventory_req_id\x18\x16 \x01(\x04\x12\x12\n\npartner_id\x18\x17 \x01(\x04\x12\x11\n\tis_direct\x18\x18 \x01(\x08\x12\x15\n\rreject_reason\x18\x19 \x01(\t\x12)\n\x0b\x61ttachments\x18\x1a \x03(\x0b\x32\x14.returns.Attachments\x12\x14\n\x0c\x63reated_name\x18\x1b \x01(\t\x12\x14\n\x0cupdated_name\x18\x1c \x01(\t\x12\x14\n\x0cproduct_nums\x18\x1d \x01(\x04\x12\x16\n\x0elogistics_type\x18\x1f \x01(\t\x12\x12\n\ntrans_type\x18# \x01(\t\x12\x11\n\tsource_id\x18% \x01(\x04\x12\x13\n\x0bsource_code\x18& \x01(\t\x12\x31\n\rdelivery_date\x18\' \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nrequest_id\x18( \x01(\x04\x12\x16\n\x0ereturn_to_code\x18) \x01(\t\x12\x16\n\x0ereturn_to_name\x18* \x01(\t\x12\x16\n\x0ereturn_by_code\x18+ \x01(\t\x12\x16\n\x0ereturn_by_name\x18, \x01(\t\x12\x15\n\rrefund_amount\x18\x32 \x01(\x01\x12\x15\n\rfranchisee_id\x18\x37 \x01(\x04\x12\x17\n\x0f\x66ranchisee_code\x18\x38 \x01(\t\x12\x17\n\x0f\x66ranchisee_name\x18\x39 \x01(\t\x12\x13\n\x0bpayment_way\x18: \x01(\t\x12\x15\n\rdelivery_code\x18; \x01(\t\x12\x14\n\x0creceive_code\x18< \x01(\t\x12\x15\n\rreturns_price\x18\x41 \x01(\x01\x12\x14\n\x0cproduct_code\x18\x42 \x01(\t\x12\x14\n\x0cproduct_name\x18\x43 \x01(\t\x12\x11\n\tunit_spec\x18\x44 \x01(\t\x12\x11\n\tprice_tax\x18\x45 \x01(\x01\x12\x10\n\x08quantity\x18\x46 \x01(\x01\x12\x11\n\trefund_id\x18\x33 \x01(\x04\x12\x13\n\x0brefund_code\x18\x34 \x01(\t\x12\x16\n\x0ewarehouse_type\x18\x35 \x01(\t\x12\x13\n\x0blong_effect\x18\x36 \x01(\t\"q\n\x0cValidProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12\x0c\n\x04spec\x18\x04 \x01(\t\x12\x1b\n\x04unit\x18\x05 \x01(\x0b\x32\r.returns.Unit\"2\n\x04Unit\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08quantity\x18\x02 \x01(\x05\x12\x0c\n\x04rate\x18\x03 \x01(\x01\"\xcd\x07\n\rProductDetail\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x11\n\treturn_id\x18\x02 \x01(\x04\x12\x11\n\treturn_by\x18\x03 \x01(\x04\x12\x17\n\x0fmaterial_number\x18\x04 \x01(\t\x12\x14\n\x0cproduct_code\x18\x05 \x01(\t\x12\x12\n\nproduct_id\x18\x06 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x10\n\x08quantity\x18\x08 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\t \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\n \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x0b \x01(\t\x12\x0f\n\x07unit_id\x18\x0c \x01(\x04\x12\x11\n\tunit_name\x18\r \x01(\t\x12\x11\n\tunit_rate\x18\x0e \x01(\x01\x12\x11\n\tunit_spec\x18\x0f \x01(\t\x12%\n\x1d\x61\x63\x63ounting_confirmed_quantity\x18\x10 \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x11 \x01(\x01\x12$\n\x1c\x61\x63\x63ounting_returned_quantity\x18\x12 \x01(\x01\x12\x1a\n\x12\x63onfirmed_quantity\x18\x13 \x01(\x01\x12\x19\n\x11returned_quantity\x18\x14 \x01(\x01\x12\x14\n\x0cis_confirmed\x18\x15 \x01(\x08\x12/\n\x0breturn_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\treturn_to\x18\x17 \x01(\x04\x12\x18\n\x10inventory_status\x18\x18 \x01(\t\x12\x18\n\x10inventory_req_id\x18\x19 \x01(\x04\x12.\n\ncreated_at\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x1b \x01(\x04\x12.\n\nupdated_at\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\x1d \x01(\x04\x12\x12\n\npartner_id\x18\x1e \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1f \x01(\t\x12\x14\n\x0cupdated_name\x18  \x01(\t\x12\x14\n\x0cstorage_type\x18# \x01(\t\x12\r\n\x05price\x18$ \x01(\x01\x12\x10\n\x08tax_rate\x18% \x01(\x02\x12\x11\n\tprice_tax\x18& \x01(\x01\x12\x11\n\tsum_price\x18\' \x01(\x01\x12)\n\x0b\x61ttachments\x18- \x03(\x0b\x32\x14.returns.Attachments\"0\n\x1a\x43reateAdjustReturnResponse\x12\x12\n\nreturn_ids\x18\x01 \x03(\x04\"(\n\x0b\x41ttachments\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t2\xd0\x0f\n\rReturnService\x12n\n\x0c\x43reateReturn\x12\x1c.returns.CreateReturnRequest\x1a\x1d.returns.CreateReturnResponse\"!\x82\xd3\xe4\x93\x02\x1b\"\x16/api/v2/supply/returns:\x01*\x12|\n\x0fGetValidProduct\x12\x1f.returns.GetValidProductRequest\x1a\x15.returns.ValidProduct\"1\x82\xd3\xe4\x93\x02+\x12)/api/v2/supply/return/store/product/valid\x12\x65\n\nListReturn\x12\x1a.returns.ListReturnRequest\x1a\x1b.returns.ListReturnResponse\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/api/v2/supply/returns\x12r\n\x0eListReturnCold\x12\x1e.returns.ListReturnColdRequest\x1a\x1b.returns.ListReturnResponse\"#\x82\xd3\xe4\x93\x02\x1d\x12\x1b/api/v2/supply/returns/cold\x12\x65\n\rGetReturnById\x12\x1d.returns.GetReturnByIdRequest\x1a\x10.returns.Returns\"#\x82\xd3\xe4\x93\x02\x1d\x12\x1b/api/v2/supply/returns/{id}\x12\x90\x01\n\x14GetReturnProductById\x12$.returns.GetReturnProductByIdRequest\x1a%.returns.GetReturnProductByIdResponse\"+\x82\xd3\xe4\x93\x02%\x12#/api/v2/supply/returns/{id}/product\x12z\n\x0cSubmitReturn\x12\x1c.returns.SubmitReturnRequest\x1a\x1d.returns.SubmitReturnResponse\"-\x82\xd3\xe4\x93\x02\'\x1a\"/api/v2/supply/returns/{id}/submit:\x01*\x12z\n\x0cRejectReturn\x12\x1c.returns.RejectReturnRequest\x1a\x1d.returns.RejectReturnResponse\"-\x82\xd3\xe4\x93\x02\'\x1a\"/api/v2/supply/returns/{id}/reject:\x01*\x12~\n\rApproveReturn\x12\x1d.returns.ApproveReturnRequest\x1a\x1e.returns.ApproveReturnResponse\".\x82\xd3\xe4\x93\x02(\x1a#/api/v2/supply/returns/{id}/approve:\x01*\x12~\n\rConfirmReturn\x12\x1d.returns.ConfirmReturnRequest\x1a\x1e.returns.ConfirmReturnResponse\".\x82\xd3\xe4\x93\x02(\x1a#/api/v2/supply/returns/{id}/confirm:\x01*\x12\x82\x01\n\x0e\x44\x65liveryReturn\x12\x1e.returns.DeliveryReturnRequest\x1a\x1f.returns.DeliveryReturnResponse\"/\x82\xd3\xe4\x93\x02)\x1a$/api/v2/supply/returns/{id}/delivery:\x01*\x12z\n\x0cUpdateReturn\x12\x1c.returns.UpdateReturnRequest\x1a\x1d.returns.UpdateReturnResponse\"-\x82\xd3\xe4\x93\x02\'\x1a\"/api/v2/supply/returns/{id}/update:\x01*\x12\x81\x01\n\x0cUpdateRemark\x12\x1c.returns.UpdateRemarkRequest\x1a\x1d.returns.UpdateRemarkResponse\"4\x82\xd3\xe4\x93\x02.\x1a)/api/v2/supply/returns/{id}/update/remark:\x01*\x12z\n\x0c\x44\x65leteReturn\x12\x1c.returns.DeleteReturnRequest\x1a\x1d.returns.DeleteReturnResponse\"-\x82\xd3\xe4\x93\x02\'\x1a\"/api/v2/supply/returns/{id}/delete:\x01*\x12\x81\x01\n\x12\x43reateAdjustReturn\x12\x1c.returns.CreateReturnRequest\x1a#.returns.CreateAdjustReturnResponse\"(\x82\xd3\xe4\x93\x02\"\"\x1d/api/v2/supply/adjust_returns:\x01*\x12\x7f\n\x19\x43heckReturnAvailableByrec\x12$.returns.CheckReturnAvailableRequest\x1a\x14.returns.CommonReply\"&\x82\xd3\xe4\x93\x02 \"\x1b/api/v2/supply/return/check:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_COMMONREPLY = _descriptor.Descriptor(
  name='CommonReply',
  full_name='returns.CommonReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='returns.CommonReply.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=89,
  serialized_end=119,
)


_CREATERETURNREQUEST = _descriptor.Descriptor(
  name='CreateReturnRequest',
  full_name='returns.CreateReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='return_by', full_name='returns.CreateReturnRequest.return_by', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='returns.CreateReturnRequest.logistics_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='returns.CreateReturnRequest.type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='returns.CreateReturnRequest.sub_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_date', full_name='returns.CreateReturnRequest.return_delivery_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_id', full_name='returns.CreateReturnRequest.source_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_code', full_name='returns.CreateReturnRequest.source_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_reason', full_name='returns.CreateReturnRequest.return_reason', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='returns.CreateReturnRequest.remark', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='returns.CreateReturnRequest.attachments', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='returns.CreateReturnRequest.request_id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='returns.CreateReturnRequest.products', index=11,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='returns.CreateReturnRequest.lan', index=12,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=122,
  serialized_end=467,
)


_CHECKRETURNAVAILABLEREQUEST = _descriptor.Descriptor(
  name='CheckReturnAvailableRequest',
  full_name='returns.CheckReturnAvailableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='return_by', full_name='returns.CheckReturnAvailableRequest.return_by', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_date', full_name='returns.CheckReturnAvailableRequest.return_delivery_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='returns.CheckReturnAvailableRequest.type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='returns.CheckReturnAvailableRequest.sub_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_reason', full_name='returns.CheckReturnAvailableRequest.return_reason', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='returns.CheckReturnAvailableRequest.remark', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='returns.CheckReturnAvailableRequest.products', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='returns.CheckReturnAvailableRequest.is_direct', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='returns.CheckReturnAvailableRequest.return_to', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='returns.CheckReturnAvailableRequest.attachments', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='returns.CheckReturnAvailableRequest.logistics_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='returns.CheckReturnAvailableRequest.is_adjust', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_id', full_name='returns.CheckReturnAvailableRequest.receiving_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='returns.CheckReturnAvailableRequest.lan', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_id', full_name='returns.CheckReturnAvailableRequest.return_id', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=470,
  serialized_end=861,
)


_CREATERETURNRESPONSE = _descriptor.Descriptor(
  name='CreateReturnResponse',
  full_name='returns.CreateReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='return_id', full_name='returns.CreateReturnResponse.return_id', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payload', full_name='returns.CreateReturnResponse.payload', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=863,
  serialized_end=921,
)


_GETVALIDPRODUCTREQUEST = _descriptor.Descriptor(
  name='GetValidProductRequest',
  full_name='returns.GetValidProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='returns.GetValidProductRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='returns.GetValidProductRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='returns.GetValidProductRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='returns.GetValidProductRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='returns.GetValidProductRequest.product_name', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='returns.GetValidProductRequest.lan', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=923,
  serialized_end=1048,
)


_GETVALIDPRODUCTRESPONSE = _descriptor.Descriptor(
  name='GetValidProductResponse',
  full_name='returns.GetValidProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='returns.GetValidProductResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1050,
  serialized_end=1092,
)


_LISTRETURNREQUEST = _descriptor.Descriptor(
  name='ListReturnRequest',
  full_name='returns.ListReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='returns.ListReturnRequest.store_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='returns.ListReturnRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='returns.ListReturnRequest.start_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='returns.ListReturnRequest.end_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='returns.ListReturnRequest.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='returns.ListReturnRequest.limit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='returns.ListReturnRequest.offset', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='returns.ListReturnRequest.include_total', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='returns.ListReturnRequest.is_direct', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by', full_name='returns.ListReturnRequest.order_by', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='returns.ListReturnRequest.logistics_type', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='returns.ListReturnRequest.is_adjust', index=11,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='returns.ListReturnRequest.sort', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='returns.ListReturnRequest.order', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='returns.ListReturnRequest.storage_type', index=14,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='returns.ListReturnRequest.type', index=15,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='returns.ListReturnRequest.sub_type', index=16,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_code', full_name='returns.ListReturnRequest.receive_code', index=17,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='returns.ListReturnRequest.lan', index=18,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_from', full_name='returns.ListReturnRequest.return_date_from', index=19,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_to', full_name='returns.ListReturnRequest.return_date_to', index=20,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_from', full_name='returns.ListReturnRequest.delivery_date_from', index=21,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_to', full_name='returns.ListReturnRequest.delivery_date_to', index=22,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_number', full_name='returns.ListReturnRequest.return_number', index=23,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='returns.ListReturnRequest.product_ids', index=24,
      number=27, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_by', full_name='returns.ListReturnRequest.return_by', index=25,
      number=28, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='returns.ListReturnRequest.return_to', index=26,
      number=29, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_id', full_name='returns.ListReturnRequest.source_id', index=27,
      number=30, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_code', full_name='returns.ListReturnRequest.source_code', index=28,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_frs', full_name='returns.ListReturnRequest.is_frs', index=29,
      number=32, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1095,
  serialized_end=1863,
)


_LISTRETURNRESPONSE = _descriptor.Descriptor(
  name='ListReturnResponse',
  full_name='returns.ListReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='returns.ListReturnResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='returns.ListReturnResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1865,
  serialized_end=1932,
)


_LISTRETURNCOLDREQUEST = _descriptor.Descriptor(
  name='ListReturnColdRequest',
  full_name='returns.ListReturnColdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='returns.ListReturnColdRequest.store_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='returns.ListReturnColdRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='returns.ListReturnColdRequest.start_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='returns.ListReturnColdRequest.end_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='returns.ListReturnColdRequest.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='returns.ListReturnColdRequest.limit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='returns.ListReturnColdRequest.offset', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='returns.ListReturnColdRequest.include_total', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='returns.ListReturnColdRequest.is_direct', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='returns.ListReturnColdRequest.logistics_type', index=9,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_adjust', full_name='returns.ListReturnColdRequest.is_adjust', index=10,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='returns.ListReturnColdRequest.sort', index=11,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='returns.ListReturnColdRequest.order', index=12,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='returns.ListReturnColdRequest.storage_type', index=13,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='returns.ListReturnColdRequest.lan', index=14,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_from', full_name='returns.ListReturnColdRequest.return_date_from', index=15,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_to', full_name='returns.ListReturnColdRequest.return_date_to', index=16,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_from', full_name='returns.ListReturnColdRequest.delivery_date_from', index=17,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_to', full_name='returns.ListReturnColdRequest.delivery_date_to', index=18,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1935,
  serialized_end=2497,
)


_LISTRETURNCOLDRESPONSE = _descriptor.Descriptor(
  name='ListReturnColdResponse',
  full_name='returns.ListReturnColdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='returns.ListReturnColdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='returns.ListReturnColdResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2499,
  serialized_end=2570,
)


_GETRETURNBYIDREQUEST = _descriptor.Descriptor(
  name='GetReturnByIdRequest',
  full_name='returns.GetReturnByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='returns.GetReturnByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='returns.GetReturnByIdRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2572,
  serialized_end=2619,
)


_GETRETURNBYIDRESPONSE = _descriptor.Descriptor(
  name='GetReturnByIdResponse',
  full_name='returns.GetReturnByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='return_detail', full_name='returns.GetReturnByIdResponse.return_detail', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2621,
  serialized_end=2685,
)


_GETRETURNPRODUCTBYIDREQUEST = _descriptor.Descriptor(
  name='GetReturnProductByIdRequest',
  full_name='returns.GetReturnProductByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='returns.GetReturnProductByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='returns.GetReturnProductByIdRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='returns.GetReturnProductByIdRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='returns.GetReturnProductByIdRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='returns.GetReturnProductByIdRequest.sort', index=4,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='returns.GetReturnProductByIdRequest.order', index=5,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='returns.GetReturnProductByIdRequest.lan', index=6,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2688,
  serialized_end=2825,
)


_GETRETURNPRODUCTBYIDRESPONSE = _descriptor.Descriptor(
  name='GetReturnProductByIdResponse',
  full_name='returns.GetReturnProductByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='returns.GetReturnProductByIdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='returns.GetReturnProductByIdResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2827,
  serialized_end=2910,
)


_SUBMITRETURNREQUEST = _descriptor.Descriptor(
  name='SubmitReturnRequest',
  full_name='returns.SubmitReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='returns.SubmitReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='returns.SubmitReturnRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2912,
  serialized_end=2958,
)


_SUBMITRETURNRESPONSE = _descriptor.Descriptor(
  name='SubmitReturnResponse',
  full_name='returns.SubmitReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='returns.SubmitReturnResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2960,
  serialized_end=2999,
)


_REJECTRETURNREQUEST = _descriptor.Descriptor(
  name='RejectReturnRequest',
  full_name='returns.RejectReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='returns.RejectReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='returns.RejectReturnRequest.reject_reason', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='returns.RejectReturnRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3001,
  serialized_end=3070,
)


_REJECTRETURNRESPONSE = _descriptor.Descriptor(
  name='RejectReturnResponse',
  full_name='returns.RejectReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='returns.RejectReturnResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3072,
  serialized_end=3111,
)


_APPROVERETURNREQUEST = _descriptor.Descriptor(
  name='ApproveReturnRequest',
  full_name='returns.ApproveReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='returns.ApproveReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_type', full_name='returns.ApproveReturnRequest.trans_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='returns.ApproveReturnRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='warehouse_type', full_name='returns.ApproveReturnRequest.warehouse_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3113,
  serialized_end=3204,
)


_APPROVERETURNRESPONSE = _descriptor.Descriptor(
  name='ApproveReturnResponse',
  full_name='returns.ApproveReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='returns.ApproveReturnResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3206,
  serialized_end=3246,
)


_CONFIRMRETURNREQUEST = _descriptor.Descriptor(
  name='ConfirmReturnRequest',
  full_name='returns.ConfirmReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='returns.ConfirmReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='returns.ConfirmReturnRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3248,
  serialized_end=3295,
)


_CONFIRMRETURNRESPONSE = _descriptor.Descriptor(
  name='ConfirmReturnResponse',
  full_name='returns.ConfirmReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='returns.ConfirmReturnResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3297,
  serialized_end=3337,
)


_DELIVERYRETURNREQUEST = _descriptor.Descriptor(
  name='DeliveryReturnRequest',
  full_name='returns.DeliveryReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='returns.DeliveryReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='returns.DeliveryReturnRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fs_code', full_name='returns.DeliveryReturnRequest.fs_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3339,
  serialized_end=3404,
)


_DELIVERYRETURNRESPONSE = _descriptor.Descriptor(
  name='DeliveryReturnResponse',
  full_name='returns.DeliveryReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='returns.DeliveryReturnResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3406,
  serialized_end=3447,
)


_UPDATERETURNREQUEST = _descriptor.Descriptor(
  name='UpdateReturnRequest',
  full_name='returns.UpdateReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='returns.UpdateReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='returns.UpdateReturnRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_reason', full_name='returns.UpdateReturnRequest.return_reason', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='returns.UpdateReturnRequest.remark', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_date', full_name='returns.UpdateReturnRequest.return_delivery_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='returns.UpdateReturnRequest.return_to', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='returns.UpdateReturnRequest.attachments', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='returns.UpdateReturnRequest.lan', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='returns.UpdateReturnRequest.logistics_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3450,
  serialized_end=3715,
)


_UPDATERETURNRESPONSE = _descriptor.Descriptor(
  name='UpdateReturnResponse',
  full_name='returns.UpdateReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='returns.UpdateReturnResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3717,
  serialized_end=3756,
)


_UPDATEREMARKREQUEST = _descriptor.Descriptor(
  name='UpdateRemarkRequest',
  full_name='returns.UpdateRemarkRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='returns.UpdateRemarkRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='returns.UpdateRemarkRequest.remark', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='returns.UpdateRemarkRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3758,
  serialized_end=3820,
)


_UPDATEREMARKRESPONSE = _descriptor.Descriptor(
  name='UpdateRemarkResponse',
  full_name='returns.UpdateRemarkResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='returns.UpdateRemarkResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3822,
  serialized_end=3861,
)


_DELETERETURNREQUEST = _descriptor.Descriptor(
  name='DeleteReturnRequest',
  full_name='returns.DeleteReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='returns.DeleteReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='returns.DeleteReturnRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3863,
  serialized_end=3909,
)


_DELETERETURNRESPONSE = _descriptor.Descriptor(
  name='DeleteReturnResponse',
  full_name='returns.DeleteReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='returns.DeleteReturnResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3911,
  serialized_end=3950,
)


_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='returns.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='returns.Product.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='returns.Product.unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='returns.Product.quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='returns.Product.confirmed_quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='returns.Product.product_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='returns.Product.product_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='returns.Product.unit_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='returns.Product.unit_spec', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='returns.Product.unit_rate', index=8,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='returns.Product.tax_rate', index=9,
      number=10, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='returns.Product.price', index=10,
      number=11, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_tax', full_name='returns.Product.price_tax', index=11,
      number=12, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='returns.Product.id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='returns.Product.return_to', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='returns.Product.logistics_type', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='returns.Product.attachments', index=15,
      number=16, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_amount', full_name='returns.Product.refund_amount', index=16,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3953,
  serialized_end=4319,
)


_RETURNS = _descriptor.Descriptor(
  name='Returns',
  full_name='returns.Returns',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='returns.Returns.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='returns.Returns.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_by', full_name='returns.Returns.return_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_number', full_name='returns.Returns.return_number', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='returns.Returns.status', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='returns.Returns.review_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_returned', full_name='returns.Returns.is_returned', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_number', full_name='returns.Returns.return_delivery_number', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date', full_name='returns.Returns.return_date', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_date', full_name='returns.Returns.return_delivery_date', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_reason', full_name='returns.Returns.return_reason', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='returns.Returns.type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='returns.Returns.sub_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='returns.Returns.remark', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='returns.Returns.store_secondary_id', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='returns.Returns.created_at', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='returns.Returns.updated_at', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='returns.Returns.created_by', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='returns.Returns.updated_by', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='returns.Returns.return_to', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_status', full_name='returns.Returns.inventory_status', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_req_id', full_name='returns.Returns.inventory_req_id', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='returns.Returns.partner_id', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='returns.Returns.is_direct', index=23,
      number=24, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='returns.Returns.reject_reason', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='returns.Returns.attachments', index=25,
      number=26, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='returns.Returns.created_name', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='returns.Returns.updated_name', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_nums', full_name='returns.Returns.product_nums', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='returns.Returns.logistics_type', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_type', full_name='returns.Returns.trans_type', index=30,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_id', full_name='returns.Returns.source_id', index=31,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_code', full_name='returns.Returns.source_code', index=32,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='returns.Returns.delivery_date', index=33,
      number=39, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='returns.Returns.request_id', index=34,
      number=40, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to_code', full_name='returns.Returns.return_to_code', index=35,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to_name', full_name='returns.Returns.return_to_name', index=36,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_by_code', full_name='returns.Returns.return_by_code', index=37,
      number=43, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_by_name', full_name='returns.Returns.return_by_name', index=38,
      number=44, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_amount', full_name='returns.Returns.refund_amount', index=39,
      number=50, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='returns.Returns.franchisee_id', index=40,
      number=55, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_code', full_name='returns.Returns.franchisee_code', index=41,
      number=56, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_name', full_name='returns.Returns.franchisee_name', index=42,
      number=57, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_way', full_name='returns.Returns.payment_way', index=43,
      number=58, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_code', full_name='returns.Returns.delivery_code', index=44,
      number=59, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_code', full_name='returns.Returns.receive_code', index=45,
      number=60, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='returns_price', full_name='returns.Returns.returns_price', index=46,
      number=65, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='returns.Returns.product_code', index=47,
      number=66, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='returns.Returns.product_name', index=48,
      number=67, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='returns.Returns.unit_spec', index=49,
      number=68, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_tax', full_name='returns.Returns.price_tax', index=50,
      number=69, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='returns.Returns.quantity', index=51,
      number=70, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_id', full_name='returns.Returns.refund_id', index=52,
      number=51, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_code', full_name='returns.Returns.refund_code', index=53,
      number=52, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='warehouse_type', full_name='returns.Returns.warehouse_type', index=54,
      number=53, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='long_effect', full_name='returns.Returns.long_effect', index=55,
      number=54, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4322,
  serialized_end=5692,
)


_VALIDPRODUCT = _descriptor.Descriptor(
  name='ValidProduct',
  full_name='returns.ValidProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='returns.ValidProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='returns.ValidProduct.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='returns.ValidProduct.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='returns.ValidProduct.spec', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit', full_name='returns.ValidProduct.unit', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5694,
  serialized_end=5807,
)


_UNIT = _descriptor.Descriptor(
  name='Unit',
  full_name='returns.Unit',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='returns.Unit.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='returns.Unit.quantity', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='returns.Unit.rate', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5809,
  serialized_end=5859,
)


_PRODUCTDETAIL = _descriptor.Descriptor(
  name='ProductDetail',
  full_name='returns.ProductDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='returns.ProductDetail.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_id', full_name='returns.ProductDetail.return_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_by', full_name='returns.ProductDetail.return_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='returns.ProductDetail.material_number', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='returns.ProductDetail.product_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='returns.ProductDetail.product_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='returns.ProductDetail.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='returns.ProductDetail.quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='returns.ProductDetail.accounting_unit_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='returns.ProductDetail.accounting_unit_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='returns.ProductDetail.accounting_unit_spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='returns.ProductDetail.unit_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='returns.ProductDetail.unit_name', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='returns.ProductDetail.unit_rate', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='returns.ProductDetail.unit_spec', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_confirmed_quantity', full_name='returns.ProductDetail.accounting_confirmed_quantity', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='returns.ProductDetail.accounting_quantity', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_returned_quantity', full_name='returns.ProductDetail.accounting_returned_quantity', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='returns.ProductDetail.confirmed_quantity', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='returned_quantity', full_name='returns.ProductDetail.returned_quantity', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_confirmed', full_name='returns.ProductDetail.is_confirmed', index=20,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date', full_name='returns.ProductDetail.return_date', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='returns.ProductDetail.return_to', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_status', full_name='returns.ProductDetail.inventory_status', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_req_id', full_name='returns.ProductDetail.inventory_req_id', index=24,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='returns.ProductDetail.created_at', index=25,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='returns.ProductDetail.created_by', index=26,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='returns.ProductDetail.updated_at', index=27,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='returns.ProductDetail.updated_by', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='returns.ProductDetail.partner_id', index=29,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='returns.ProductDetail.created_name', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='returns.ProductDetail.updated_name', index=31,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='returns.ProductDetail.storage_type', index=32,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='returns.ProductDetail.price', index=33,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='returns.ProductDetail.tax_rate', index=34,
      number=37, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_tax', full_name='returns.ProductDetail.price_tax', index=35,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price', full_name='returns.ProductDetail.sum_price', index=36,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='returns.ProductDetail.attachments', index=37,
      number=45, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5862,
  serialized_end=6835,
)


_CREATEADJUSTRETURNRESPONSE = _descriptor.Descriptor(
  name='CreateAdjustReturnResponse',
  full_name='returns.CreateAdjustReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='return_ids', full_name='returns.CreateAdjustReturnResponse.return_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6837,
  serialized_end=6885,
)


_ATTACHMENTS = _descriptor.Descriptor(
  name='Attachments',
  full_name='returns.Attachments',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='returns.Attachments.type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='returns.Attachments.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6887,
  serialized_end=6927,
)

_CREATERETURNREQUEST.fields_by_name['return_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATERETURNREQUEST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_CREATERETURNREQUEST.fields_by_name['products'].message_type = _PRODUCT
_CHECKRETURNAVAILABLEREQUEST.fields_by_name['return_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CHECKRETURNAVAILABLEREQUEST.fields_by_name['products'].message_type = _PRODUCT
_CHECKRETURNAVAILABLEREQUEST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_LISTRETURNREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNREQUEST.fields_by_name['return_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNREQUEST.fields_by_name['return_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNREQUEST.fields_by_name['delivery_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNREQUEST.fields_by_name['delivery_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNRESPONSE.fields_by_name['rows'].message_type = _RETURNS
_LISTRETURNCOLDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNCOLDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNCOLDREQUEST.fields_by_name['return_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNCOLDREQUEST.fields_by_name['return_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNCOLDREQUEST.fields_by_name['delivery_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNCOLDREQUEST.fields_by_name['delivery_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNCOLDRESPONSE.fields_by_name['rows'].message_type = _RETURNS
_GETRETURNBYIDRESPONSE.fields_by_name['return_detail'].message_type = _RETURNS
_GETRETURNPRODUCTBYIDRESPONSE.fields_by_name['rows'].message_type = _PRODUCTDETAIL
_UPDATERETURNREQUEST.fields_by_name['products'].message_type = _PRODUCT
_UPDATERETURNREQUEST.fields_by_name['return_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATERETURNREQUEST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_PRODUCT.fields_by_name['attachments'].message_type = _ATTACHMENTS
_RETURNS.fields_by_name['return_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RETURNS.fields_by_name['return_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RETURNS.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RETURNS.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RETURNS.fields_by_name['attachments'].message_type = _ATTACHMENTS
_RETURNS.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_VALIDPRODUCT.fields_by_name['unit'].message_type = _UNIT
_PRODUCTDETAIL.fields_by_name['return_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCTDETAIL.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCTDETAIL.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCTDETAIL.fields_by_name['attachments'].message_type = _ATTACHMENTS
DESCRIPTOR.message_types_by_name['CommonReply'] = _COMMONREPLY
DESCRIPTOR.message_types_by_name['CreateReturnRequest'] = _CREATERETURNREQUEST
DESCRIPTOR.message_types_by_name['CheckReturnAvailableRequest'] = _CHECKRETURNAVAILABLEREQUEST
DESCRIPTOR.message_types_by_name['CreateReturnResponse'] = _CREATERETURNRESPONSE
DESCRIPTOR.message_types_by_name['GetValidProductRequest'] = _GETVALIDPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['GetValidProductResponse'] = _GETVALIDPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['ListReturnRequest'] = _LISTRETURNREQUEST
DESCRIPTOR.message_types_by_name['ListReturnResponse'] = _LISTRETURNRESPONSE
DESCRIPTOR.message_types_by_name['ListReturnColdRequest'] = _LISTRETURNCOLDREQUEST
DESCRIPTOR.message_types_by_name['ListReturnColdResponse'] = _LISTRETURNCOLDRESPONSE
DESCRIPTOR.message_types_by_name['GetReturnByIdRequest'] = _GETRETURNBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetReturnByIdResponse'] = _GETRETURNBYIDRESPONSE
DESCRIPTOR.message_types_by_name['GetReturnProductByIdRequest'] = _GETRETURNPRODUCTBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetReturnProductByIdResponse'] = _GETRETURNPRODUCTBYIDRESPONSE
DESCRIPTOR.message_types_by_name['SubmitReturnRequest'] = _SUBMITRETURNREQUEST
DESCRIPTOR.message_types_by_name['SubmitReturnResponse'] = _SUBMITRETURNRESPONSE
DESCRIPTOR.message_types_by_name['RejectReturnRequest'] = _REJECTRETURNREQUEST
DESCRIPTOR.message_types_by_name['RejectReturnResponse'] = _REJECTRETURNRESPONSE
DESCRIPTOR.message_types_by_name['ApproveReturnRequest'] = _APPROVERETURNREQUEST
DESCRIPTOR.message_types_by_name['ApproveReturnResponse'] = _APPROVERETURNRESPONSE
DESCRIPTOR.message_types_by_name['ConfirmReturnRequest'] = _CONFIRMRETURNREQUEST
DESCRIPTOR.message_types_by_name['ConfirmReturnResponse'] = _CONFIRMRETURNRESPONSE
DESCRIPTOR.message_types_by_name['DeliveryReturnRequest'] = _DELIVERYRETURNREQUEST
DESCRIPTOR.message_types_by_name['DeliveryReturnResponse'] = _DELIVERYRETURNRESPONSE
DESCRIPTOR.message_types_by_name['UpdateReturnRequest'] = _UPDATERETURNREQUEST
DESCRIPTOR.message_types_by_name['UpdateReturnResponse'] = _UPDATERETURNRESPONSE
DESCRIPTOR.message_types_by_name['UpdateRemarkRequest'] = _UPDATEREMARKREQUEST
DESCRIPTOR.message_types_by_name['UpdateRemarkResponse'] = _UPDATEREMARKRESPONSE
DESCRIPTOR.message_types_by_name['DeleteReturnRequest'] = _DELETERETURNREQUEST
DESCRIPTOR.message_types_by_name['DeleteReturnResponse'] = _DELETERETURNRESPONSE
DESCRIPTOR.message_types_by_name['Product'] = _PRODUCT
DESCRIPTOR.message_types_by_name['Returns'] = _RETURNS
DESCRIPTOR.message_types_by_name['ValidProduct'] = _VALIDPRODUCT
DESCRIPTOR.message_types_by_name['Unit'] = _UNIT
DESCRIPTOR.message_types_by_name['ProductDetail'] = _PRODUCTDETAIL
DESCRIPTOR.message_types_by_name['CreateAdjustReturnResponse'] = _CREATEADJUSTRETURNRESPONSE
DESCRIPTOR.message_types_by_name['Attachments'] = _ATTACHMENTS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CommonReply = _reflection.GeneratedProtocolMessageType('CommonReply', (_message.Message,), dict(
  DESCRIPTOR = _COMMONREPLY,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.CommonReply)
  ))
_sym_db.RegisterMessage(CommonReply)

CreateReturnRequest = _reflection.GeneratedProtocolMessageType('CreateReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATERETURNREQUEST,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.CreateReturnRequest)
  ))
_sym_db.RegisterMessage(CreateReturnRequest)

CheckReturnAvailableRequest = _reflection.GeneratedProtocolMessageType('CheckReturnAvailableRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHECKRETURNAVAILABLEREQUEST,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.CheckReturnAvailableRequest)
  ))
_sym_db.RegisterMessage(CheckReturnAvailableRequest)

CreateReturnResponse = _reflection.GeneratedProtocolMessageType('CreateReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATERETURNRESPONSE,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.CreateReturnResponse)
  ))
_sym_db.RegisterMessage(CreateReturnResponse)

GetValidProductRequest = _reflection.GeneratedProtocolMessageType('GetValidProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETVALIDPRODUCTREQUEST,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.GetValidProductRequest)
  ))
_sym_db.RegisterMessage(GetValidProductRequest)

GetValidProductResponse = _reflection.GeneratedProtocolMessageType('GetValidProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETVALIDPRODUCTRESPONSE,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.GetValidProductResponse)
  ))
_sym_db.RegisterMessage(GetValidProductResponse)

ListReturnRequest = _reflection.GeneratedProtocolMessageType('ListReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTRETURNREQUEST,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.ListReturnRequest)
  ))
_sym_db.RegisterMessage(ListReturnRequest)

ListReturnResponse = _reflection.GeneratedProtocolMessageType('ListReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTRETURNRESPONSE,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.ListReturnResponse)
  ))
_sym_db.RegisterMessage(ListReturnResponse)

ListReturnColdRequest = _reflection.GeneratedProtocolMessageType('ListReturnColdRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTRETURNCOLDREQUEST,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.ListReturnColdRequest)
  ))
_sym_db.RegisterMessage(ListReturnColdRequest)

ListReturnColdResponse = _reflection.GeneratedProtocolMessageType('ListReturnColdResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTRETURNCOLDRESPONSE,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.ListReturnColdResponse)
  ))
_sym_db.RegisterMessage(ListReturnColdResponse)

GetReturnByIdRequest = _reflection.GeneratedProtocolMessageType('GetReturnByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRETURNBYIDREQUEST,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.GetReturnByIdRequest)
  ))
_sym_db.RegisterMessage(GetReturnByIdRequest)

GetReturnByIdResponse = _reflection.GeneratedProtocolMessageType('GetReturnByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRETURNBYIDRESPONSE,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.GetReturnByIdResponse)
  ))
_sym_db.RegisterMessage(GetReturnByIdResponse)

GetReturnProductByIdRequest = _reflection.GeneratedProtocolMessageType('GetReturnProductByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRETURNPRODUCTBYIDREQUEST,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.GetReturnProductByIdRequest)
  ))
_sym_db.RegisterMessage(GetReturnProductByIdRequest)

GetReturnProductByIdResponse = _reflection.GeneratedProtocolMessageType('GetReturnProductByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRETURNPRODUCTBYIDRESPONSE,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.GetReturnProductByIdResponse)
  ))
_sym_db.RegisterMessage(GetReturnProductByIdResponse)

SubmitReturnRequest = _reflection.GeneratedProtocolMessageType('SubmitReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITRETURNREQUEST,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.SubmitReturnRequest)
  ))
_sym_db.RegisterMessage(SubmitReturnRequest)

SubmitReturnResponse = _reflection.GeneratedProtocolMessageType('SubmitReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITRETURNRESPONSE,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.SubmitReturnResponse)
  ))
_sym_db.RegisterMessage(SubmitReturnResponse)

RejectReturnRequest = _reflection.GeneratedProtocolMessageType('RejectReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _REJECTRETURNREQUEST,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.RejectReturnRequest)
  ))
_sym_db.RegisterMessage(RejectReturnRequest)

RejectReturnResponse = _reflection.GeneratedProtocolMessageType('RejectReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _REJECTRETURNRESPONSE,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.RejectReturnResponse)
  ))
_sym_db.RegisterMessage(RejectReturnResponse)

ApproveReturnRequest = _reflection.GeneratedProtocolMessageType('ApproveReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _APPROVERETURNREQUEST,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.ApproveReturnRequest)
  ))
_sym_db.RegisterMessage(ApproveReturnRequest)

ApproveReturnResponse = _reflection.GeneratedProtocolMessageType('ApproveReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _APPROVERETURNRESPONSE,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.ApproveReturnResponse)
  ))
_sym_db.RegisterMessage(ApproveReturnResponse)

ConfirmReturnRequest = _reflection.GeneratedProtocolMessageType('ConfirmReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMRETURNREQUEST,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.ConfirmReturnRequest)
  ))
_sym_db.RegisterMessage(ConfirmReturnRequest)

ConfirmReturnResponse = _reflection.GeneratedProtocolMessageType('ConfirmReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMRETURNRESPONSE,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.ConfirmReturnResponse)
  ))
_sym_db.RegisterMessage(ConfirmReturnResponse)

DeliveryReturnRequest = _reflection.GeneratedProtocolMessageType('DeliveryReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELIVERYRETURNREQUEST,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.DeliveryReturnRequest)
  ))
_sym_db.RegisterMessage(DeliveryReturnRequest)

DeliveryReturnResponse = _reflection.GeneratedProtocolMessageType('DeliveryReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELIVERYRETURNRESPONSE,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.DeliveryReturnResponse)
  ))
_sym_db.RegisterMessage(DeliveryReturnResponse)

UpdateReturnRequest = _reflection.GeneratedProtocolMessageType('UpdateReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERETURNREQUEST,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.UpdateReturnRequest)
  ))
_sym_db.RegisterMessage(UpdateReturnRequest)

UpdateReturnResponse = _reflection.GeneratedProtocolMessageType('UpdateReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERETURNRESPONSE,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.UpdateReturnResponse)
  ))
_sym_db.RegisterMessage(UpdateReturnResponse)

UpdateRemarkRequest = _reflection.GeneratedProtocolMessageType('UpdateRemarkRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEREMARKREQUEST,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.UpdateRemarkRequest)
  ))
_sym_db.RegisterMessage(UpdateRemarkRequest)

UpdateRemarkResponse = _reflection.GeneratedProtocolMessageType('UpdateRemarkResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEREMARKRESPONSE,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.UpdateRemarkResponse)
  ))
_sym_db.RegisterMessage(UpdateRemarkResponse)

DeleteReturnRequest = _reflection.GeneratedProtocolMessageType('DeleteReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETERETURNREQUEST,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.DeleteReturnRequest)
  ))
_sym_db.RegisterMessage(DeleteReturnRequest)

DeleteReturnResponse = _reflection.GeneratedProtocolMessageType('DeleteReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETERETURNRESPONSE,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.DeleteReturnResponse)
  ))
_sym_db.RegisterMessage(DeleteReturnResponse)

Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCT,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.Product)
  ))
_sym_db.RegisterMessage(Product)

Returns = _reflection.GeneratedProtocolMessageType('Returns', (_message.Message,), dict(
  DESCRIPTOR = _RETURNS,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.Returns)
  ))
_sym_db.RegisterMessage(Returns)

ValidProduct = _reflection.GeneratedProtocolMessageType('ValidProduct', (_message.Message,), dict(
  DESCRIPTOR = _VALIDPRODUCT,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.ValidProduct)
  ))
_sym_db.RegisterMessage(ValidProduct)

Unit = _reflection.GeneratedProtocolMessageType('Unit', (_message.Message,), dict(
  DESCRIPTOR = _UNIT,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.Unit)
  ))
_sym_db.RegisterMessage(Unit)

ProductDetail = _reflection.GeneratedProtocolMessageType('ProductDetail', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTDETAIL,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.ProductDetail)
  ))
_sym_db.RegisterMessage(ProductDetail)

CreateAdjustReturnResponse = _reflection.GeneratedProtocolMessageType('CreateAdjustReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEADJUSTRETURNRESPONSE,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.CreateAdjustReturnResponse)
  ))
_sym_db.RegisterMessage(CreateAdjustReturnResponse)

Attachments = _reflection.GeneratedProtocolMessageType('Attachments', (_message.Message,), dict(
  DESCRIPTOR = _ATTACHMENTS,
  __module__ = 'returns_pb2'
  # @@protoc_insertion_point(class_scope:returns.Attachments)
  ))
_sym_db.RegisterMessage(Attachments)



_RETURNSERVICE = _descriptor.ServiceDescriptor(
  name='ReturnService',
  full_name='returns.ReturnService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=6930,
  serialized_end=8930,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateReturn',
    full_name='returns.ReturnService.CreateReturn',
    index=0,
    containing_service=None,
    input_type=_CREATERETURNREQUEST,
    output_type=_CREATERETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002\033\"\026/api/v2/supply/returns:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetValidProduct',
    full_name='returns.ReturnService.GetValidProduct',
    index=1,
    containing_service=None,
    input_type=_GETVALIDPRODUCTREQUEST,
    output_type=_VALIDPRODUCT,
    serialized_options=_b('\202\323\344\223\002+\022)/api/v2/supply/return/store/product/valid'),
  ),
  _descriptor.MethodDescriptor(
    name='ListReturn',
    full_name='returns.ReturnService.ListReturn',
    index=2,
    containing_service=None,
    input_type=_LISTRETURNREQUEST,
    output_type=_LISTRETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002\030\022\026/api/v2/supply/returns'),
  ),
  _descriptor.MethodDescriptor(
    name='ListReturnCold',
    full_name='returns.ReturnService.ListReturnCold',
    index=3,
    containing_service=None,
    input_type=_LISTRETURNCOLDREQUEST,
    output_type=_LISTRETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002\035\022\033/api/v2/supply/returns/cold'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReturnById',
    full_name='returns.ReturnService.GetReturnById',
    index=4,
    containing_service=None,
    input_type=_GETRETURNBYIDREQUEST,
    output_type=_RETURNS,
    serialized_options=_b('\202\323\344\223\002\035\022\033/api/v2/supply/returns/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReturnProductById',
    full_name='returns.ReturnService.GetReturnProductById',
    index=5,
    containing_service=None,
    input_type=_GETRETURNPRODUCTBYIDREQUEST,
    output_type=_GETRETURNPRODUCTBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002%\022#/api/v2/supply/returns/{id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='SubmitReturn',
    full_name='returns.ReturnService.SubmitReturn',
    index=6,
    containing_service=None,
    input_type=_SUBMITRETURNREQUEST,
    output_type=_SUBMITRETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\032\"/api/v2/supply/returns/{id}/submit:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='RejectReturn',
    full_name='returns.ReturnService.RejectReturn',
    index=7,
    containing_service=None,
    input_type=_REJECTRETURNREQUEST,
    output_type=_REJECTRETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\032\"/api/v2/supply/returns/{id}/reject:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ApproveReturn',
    full_name='returns.ReturnService.ApproveReturn',
    index=8,
    containing_service=None,
    input_type=_APPROVERETURNREQUEST,
    output_type=_APPROVERETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002(\032#/api/v2/supply/returns/{id}/approve:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmReturn',
    full_name='returns.ReturnService.ConfirmReturn',
    index=9,
    containing_service=None,
    input_type=_CONFIRMRETURNREQUEST,
    output_type=_CONFIRMRETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002(\032#/api/v2/supply/returns/{id}/confirm:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeliveryReturn',
    full_name='returns.ReturnService.DeliveryReturn',
    index=10,
    containing_service=None,
    input_type=_DELIVERYRETURNREQUEST,
    output_type=_DELIVERYRETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002)\032$/api/v2/supply/returns/{id}/delivery:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateReturn',
    full_name='returns.ReturnService.UpdateReturn',
    index=11,
    containing_service=None,
    input_type=_UPDATERETURNREQUEST,
    output_type=_UPDATERETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\032\"/api/v2/supply/returns/{id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateRemark',
    full_name='returns.ReturnService.UpdateRemark',
    index=12,
    containing_service=None,
    input_type=_UPDATEREMARKREQUEST,
    output_type=_UPDATEREMARKRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\032)/api/v2/supply/returns/{id}/update/remark:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteReturn',
    full_name='returns.ReturnService.DeleteReturn',
    index=13,
    containing_service=None,
    input_type=_DELETERETURNREQUEST,
    output_type=_DELETERETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\032\"/api/v2/supply/returns/{id}/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CreateAdjustReturn',
    full_name='returns.ReturnService.CreateAdjustReturn',
    index=14,
    containing_service=None,
    input_type=_CREATERETURNREQUEST,
    output_type=_CREATEADJUSTRETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002\"\"\035/api/v2/supply/adjust_returns:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CheckReturnAvailableByrec',
    full_name='returns.ReturnService.CheckReturnAvailableByrec',
    index=15,
    containing_service=None,
    input_type=_CHECKRETURNAVAILABLEREQUEST,
    output_type=_COMMONREPLY,
    serialized_options=_b('\202\323\344\223\002 \"\033/api/v2/supply/return/check:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_RETURNSERVICE)

DESCRIPTOR.services_by_name['ReturnService'] = _RETURNSERVICE

# @@protoc_insertion_point(module_scope)
