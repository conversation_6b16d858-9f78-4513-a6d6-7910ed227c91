# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: vendor_bi/receive_diff_bi.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='vendor_bi/receive_diff_bi.proto',
  package='vendor_bi',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1fvendor_bi/receive_diff_bi.proto\x12\tvendor_bi\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xc6\x02\n\x1cGetReceiveDiffCollectRequest\x12\x0e\n\x06st_ids\x18\x01 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x02 \x03(\x04\x12\x16\n\x0elogistics_type\x18\x03 \x03(\t\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12.\n\nstart_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x07 \x01(\x05\x12\x0e\n\x06offset\x18\x08 \x01(\x05\x12\x15\n\rinclude_total\x18\t \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x0b \x01(\t\x12\x14\n\x0c\x64\x65livery_bys\x18\x0c \x03(\x04\x12\x13\n\x0bproduct_ids\x18\r \x03(\x04\"q\n\x1dGetReceiveDiffCollectResponse\x12+\n\x04rows\x18\x01 \x03(\x0b\x32\x1d.vendor_bi.ReceiveDiffCollect\x12#\n\x05total\x18\x02 \x01(\x0b\x32\x14.vendor_bi.DiffTotal\"\xd3\x02\n\x1bGetReceiveDiffDetailRequest\x12\x0e\n\x06st_ids\x18\x01 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x02 \x03(\x04\x12\x16\n\x0elogistics_type\x18\x03 \x03(\t\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12.\n\nstart_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x07 \x01(\x05\x12\x0e\n\x06offset\x18\x08 \x01(\x05\x12\x15\n\rinclude_total\x18\t \x01(\x08\x12\x0c\n\x04\x63ode\x18\n \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x0b \x01(\t\x12\x14\n\x0c\x64\x65livery_bys\x18\x0c \x03(\x04\x12\x13\n\x0bproduct_ids\x18\r \x03(\x04\"q\n\x1cGetReceiveDiffDetailResponse\x12,\n\x04rows\x18\x01 \x03(\x0b\x32\x1e.vendor_bi.ReceiveDiffDetailed\x12#\n\x05total\x18\x02 \x01(\x0b\x32\x14.vendor_bi.DiffTotal\"\x8b\x01\n\tDiffTotal\x12\r\n\x05\x63ount\x18\x01 \x01(\x01\x12\x14\n\x0csum_quantity\x18\x02 \x01(\x01\x12\x1f\n\x17sum_accounting_quantity\x18\x03 \x01(\x01\x12\x1b\n\x13sum_d_diff_quantity\x18\x04 \x01(\x01\x12\x1b\n\x13sum_s_diff_quantity\x18\x05 \x01(\x01\"\x92\x06\n\x12ReceiveDiffCollect\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x01 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x02 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x03 \x01(\x04\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x15\n\rcategory_name\x18\x05 \x01(\t\x12\x17\n\x0f\x63\x61tegory_parent\x18\x06 \x01(\t\x12\x12\n\nproduct_id\x18\x07 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x08 \x01(\t\x12\x14\n\x0cproduct_name\x18\t \x01(\t\x12\x14\n\x0cproduct_spec\x18\n \x01(\t\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x0b \x01(\x01\x12\x10\n\x08quantity\x18\x0c \x01(\x01\x12\x10\n\x08store_id\x18\r \x01(\x04\x12\x12\n\nstore_code\x18\x0e \x01(\t\x12\x12\n\nstore_name\x18\x0f \x01(\t\x12\x0f\n\x07unit_id\x18\x10 \x01(\x04\x12\x11\n\tunit_name\x18\x11 \x01(\t\x12\n\n\x02id\x18\x12 \x01(\x04\x12\x13\n\x0bposition_id\x18\x19 \x01(\x04\x12\x15\n\rposition_code\x18\x1a \x01(\t\x12\x15\n\rposition_name\x18\x1b \x01(\t\x12\x13\n\x0b\x64\x65livery_by\x18\x1c \x01(\x04\x12\x18\n\x10\x64\x65livery_by_name\x18\x1d \x01(\t\x12\x18\n\x10\x64\x65livery_by_code\x18\x1e \x01(\t\x12\x16\n\x0elogistics_type\x18\x1f \x01(\t\x12\x17\n\x0fs_diff_quantity\x18  \x01(\x01\x12\x17\n\x0f\x64_diff_quantity\x18! \x01(\x01\x12\x15\n\rs_diff_amount\x18\" \x01(\x01\x12\x15\n\rd_diff_amount\x18# \x01(\x01\x12\x0e\n\x06\x61mount\x18$ \x01(\x01\x12\x15\n\rs_diff_netwrt\x18% \x01(\x01\x12\x15\n\rd_diff_netwrt\x18& \x01(\x01\x12\x0e\n\x06netwrt\x18\' \x01(\x01\x12\x11\n\ttax_price\x18( \x01(\x01\x12\x12\n\ncost_price\x18) \x01(\x01\"\xd8\x07\n\x13ReceiveDiffDetailed\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x01 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x02 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x03 \x01(\x04\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x15\n\rcategory_name\x18\x05 \x01(\t\x12\x17\n\x0f\x63\x61tegory_parent\x18\x06 \x01(\t\x12\x0f\n\x07\x64iff_id\x18\x07 \x01(\x04\x12\x11\n\tdiff_date\x18\x08 \x01(\t\x12\x11\n\tdiff_code\x18\t \x01(\t\x12\x12\n\nproduct_id\x18\n \x01(\x04\x12\x14\n\x0cproduct_code\x18\x0b \x01(\t\x12\x14\n\x0cproduct_name\x18\x0c \x01(\t\x12\x14\n\x0cproduct_spec\x18\r \x01(\t\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x0e \x01(\x01\x12\x10\n\x08quantity\x18\x0f \x01(\x01\x12\x10\n\x08store_id\x18\x10 \x01(\x04\x12\x12\n\nstore_code\x18\x11 \x01(\t\x12\x12\n\nstore_name\x18\x12 \x01(\t\x12\x0f\n\x07unit_id\x18\x13 \x01(\x04\x12\x11\n\tunit_name\x18\x14 \x01(\t\x12\n\n\x02id\x18\x15 \x01(\x04\x12\x13\n\x0breason_type\x18\x16 \x01(\t\x12\x13\n\x0bposition_id\x18\x19 \x01(\x04\x12\x15\n\rposition_code\x18\x1a \x01(\t\x12\x15\n\rposition_name\x18\x1b \x01(\t\x12\x13\n\x0b\x64\x65livery_by\x18\x1c \x01(\x04\x12\x18\n\x10\x64\x65livery_by_name\x18\x1d \x01(\t\x12\x18\n\x10\x64\x65livery_by_code\x18\x1e \x01(\t\x12\x16\n\x0elogistics_type\x18\x1f \x01(\t\x12/\n\x0b\x64\x65mand_date\x18  \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18! \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x16\n\x0ereceiving_code\x18\" \x01(\t\x12\x17\n\x0fs_diff_quantity\x18# \x01(\x01\x12\x17\n\x0f\x64_diff_quantity\x18$ \x01(\x01\x12\x15\n\rs_diff_amount\x18% \x01(\x01\x12\x15\n\rd_diff_amount\x18& \x01(\x01\x12\x0e\n\x06\x61mount\x18\' \x01(\x01\x12\x15\n\rs_diff_netwrt\x18( \x01(\x01\x12\x15\n\rd_diff_netwrt\x18) \x01(\x01\x12\x0e\n\x06netwrt\x18* \x01(\x01\x12\x11\n\ttax_price\x18+ \x01(\x01\x12\x12\n\ncost_price\x18, \x01(\x01\x32\xe2\x02\n\x1aVendorReceiveDiffBiService\x12\xa1\x01\n\x15GetReceiveDiffCollect\x12\'.vendor_bi.GetReceiveDiffCollectRequest\x1a(.vendor_bi.GetReceiveDiffCollectResponse\"5\x82\xd3\xe4\x93\x02/\x12-/api/v2/supply/vendor_bi/receive_diff/collect\x12\x9f\x01\n\x14GetReceiveDiffDetail\x12&.vendor_bi.GetReceiveDiffDetailRequest\x1a\'.vendor_bi.GetReceiveDiffDetailResponse\"6\x82\xd3\xe4\x93\x02\x30\x12./api/v2/supply/vendor_bi/receive_diff/detailedb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_GETRECEIVEDIFFCOLLECTREQUEST = _descriptor.Descriptor(
  name='GetReceiveDiffCollectRequest',
  full_name='vendor_bi.GetReceiveDiffCollectRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='st_ids', full_name='vendor_bi.GetReceiveDiffCollectRequest.st_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='vendor_bi.GetReceiveDiffCollectRequest.category_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='vendor_bi.GetReceiveDiffCollectRequest.logistics_type', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='vendor_bi.GetReceiveDiffCollectRequest.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='vendor_bi.GetReceiveDiffCollectRequest.start_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='vendor_bi.GetReceiveDiffCollectRequest.end_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='vendor_bi.GetReceiveDiffCollectRequest.limit', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='vendor_bi.GetReceiveDiffCollectRequest.offset', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='vendor_bi.GetReceiveDiffCollectRequest.include_total', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='vendor_bi.GetReceiveDiffCollectRequest.branch_type', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_bys', full_name='vendor_bi.GetReceiveDiffCollectRequest.delivery_bys', index=10,
      number=12, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='vendor_bi.GetReceiveDiffCollectRequest.product_ids', index=11,
      number=13, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=110,
  serialized_end=436,
)


_GETRECEIVEDIFFCOLLECTRESPONSE = _descriptor.Descriptor(
  name='GetReceiveDiffCollectResponse',
  full_name='vendor_bi.GetReceiveDiffCollectResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='vendor_bi.GetReceiveDiffCollectResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='vendor_bi.GetReceiveDiffCollectResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=438,
  serialized_end=551,
)


_GETRECEIVEDIFFDETAILREQUEST = _descriptor.Descriptor(
  name='GetReceiveDiffDetailRequest',
  full_name='vendor_bi.GetReceiveDiffDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='st_ids', full_name='vendor_bi.GetReceiveDiffDetailRequest.st_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='vendor_bi.GetReceiveDiffDetailRequest.category_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='vendor_bi.GetReceiveDiffDetailRequest.logistics_type', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='vendor_bi.GetReceiveDiffDetailRequest.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='vendor_bi.GetReceiveDiffDetailRequest.start_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='vendor_bi.GetReceiveDiffDetailRequest.end_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='vendor_bi.GetReceiveDiffDetailRequest.limit', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='vendor_bi.GetReceiveDiffDetailRequest.offset', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='vendor_bi.GetReceiveDiffDetailRequest.include_total', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='vendor_bi.GetReceiveDiffDetailRequest.code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='vendor_bi.GetReceiveDiffDetailRequest.branch_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_bys', full_name='vendor_bi.GetReceiveDiffDetailRequest.delivery_bys', index=11,
      number=12, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='vendor_bi.GetReceiveDiffDetailRequest.product_ids', index=12,
      number=13, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=554,
  serialized_end=893,
)


_GETRECEIVEDIFFDETAILRESPONSE = _descriptor.Descriptor(
  name='GetReceiveDiffDetailResponse',
  full_name='vendor_bi.GetReceiveDiffDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='vendor_bi.GetReceiveDiffDetailResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='vendor_bi.GetReceiveDiffDetailResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=895,
  serialized_end=1008,
)


_DIFFTOTAL = _descriptor.Descriptor(
  name='DiffTotal',
  full_name='vendor_bi.DiffTotal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='count', full_name='vendor_bi.DiffTotal.count', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_quantity', full_name='vendor_bi.DiffTotal.sum_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_accounting_quantity', full_name='vendor_bi.DiffTotal.sum_accounting_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_d_diff_quantity', full_name='vendor_bi.DiffTotal.sum_d_diff_quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_s_diff_quantity', full_name='vendor_bi.DiffTotal.sum_s_diff_quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1011,
  serialized_end=1150,
)


_RECEIVEDIFFCOLLECT = _descriptor.Descriptor(
  name='ReceiveDiffCollect',
  full_name='vendor_bi.ReceiveDiffCollect',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='vendor_bi.ReceiveDiffCollect.accounting_unit_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='vendor_bi.ReceiveDiffCollect.accounting_unit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='vendor_bi.ReceiveDiffCollect.category_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='vendor_bi.ReceiveDiffCollect.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='vendor_bi.ReceiveDiffCollect.category_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_parent', full_name='vendor_bi.ReceiveDiffCollect.category_parent', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='vendor_bi.ReceiveDiffCollect.product_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='vendor_bi.ReceiveDiffCollect.product_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='vendor_bi.ReceiveDiffCollect.product_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='vendor_bi.ReceiveDiffCollect.product_spec', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='vendor_bi.ReceiveDiffCollect.accounting_quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='vendor_bi.ReceiveDiffCollect.quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='vendor_bi.ReceiveDiffCollect.store_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='vendor_bi.ReceiveDiffCollect.store_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='vendor_bi.ReceiveDiffCollect.store_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='vendor_bi.ReceiveDiffCollect.unit_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='vendor_bi.ReceiveDiffCollect.unit_name', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='vendor_bi.ReceiveDiffCollect.id', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='vendor_bi.ReceiveDiffCollect.position_id', index=18,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='vendor_bi.ReceiveDiffCollect.position_code', index=19,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='vendor_bi.ReceiveDiffCollect.position_name', index=20,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='vendor_bi.ReceiveDiffCollect.delivery_by', index=21,
      number=28, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_name', full_name='vendor_bi.ReceiveDiffCollect.delivery_by_name', index=22,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_code', full_name='vendor_bi.ReceiveDiffCollect.delivery_by_code', index=23,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='vendor_bi.ReceiveDiffCollect.logistics_type', index=24,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_quantity', full_name='vendor_bi.ReceiveDiffCollect.s_diff_quantity', index=25,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_quantity', full_name='vendor_bi.ReceiveDiffCollect.d_diff_quantity', index=26,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_amount', full_name='vendor_bi.ReceiveDiffCollect.s_diff_amount', index=27,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_amount', full_name='vendor_bi.ReceiveDiffCollect.d_diff_amount', index=28,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='vendor_bi.ReceiveDiffCollect.amount', index=29,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_netwrt', full_name='vendor_bi.ReceiveDiffCollect.s_diff_netwrt', index=30,
      number=37, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_netwrt', full_name='vendor_bi.ReceiveDiffCollect.d_diff_netwrt', index=31,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='netwrt', full_name='vendor_bi.ReceiveDiffCollect.netwrt', index=32,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='vendor_bi.ReceiveDiffCollect.tax_price', index=33,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='vendor_bi.ReceiveDiffCollect.cost_price', index=34,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1153,
  serialized_end=1939,
)


_RECEIVEDIFFDETAILED = _descriptor.Descriptor(
  name='ReceiveDiffDetailed',
  full_name='vendor_bi.ReceiveDiffDetailed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='vendor_bi.ReceiveDiffDetailed.accounting_unit_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='vendor_bi.ReceiveDiffDetailed.accounting_unit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='vendor_bi.ReceiveDiffDetailed.category_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='vendor_bi.ReceiveDiffDetailed.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='vendor_bi.ReceiveDiffDetailed.category_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_parent', full_name='vendor_bi.ReceiveDiffDetailed.category_parent', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_id', full_name='vendor_bi.ReceiveDiffDetailed.diff_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_date', full_name='vendor_bi.ReceiveDiffDetailed.diff_date', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_code', full_name='vendor_bi.ReceiveDiffDetailed.diff_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='vendor_bi.ReceiveDiffDetailed.product_id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='vendor_bi.ReceiveDiffDetailed.product_code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='vendor_bi.ReceiveDiffDetailed.product_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='vendor_bi.ReceiveDiffDetailed.product_spec', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='vendor_bi.ReceiveDiffDetailed.accounting_quantity', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='vendor_bi.ReceiveDiffDetailed.quantity', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='vendor_bi.ReceiveDiffDetailed.store_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='vendor_bi.ReceiveDiffDetailed.store_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='vendor_bi.ReceiveDiffDetailed.store_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='vendor_bi.ReceiveDiffDetailed.unit_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='vendor_bi.ReceiveDiffDetailed.unit_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='vendor_bi.ReceiveDiffDetailed.id', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='vendor_bi.ReceiveDiffDetailed.reason_type', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='vendor_bi.ReceiveDiffDetailed.position_id', index=22,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='vendor_bi.ReceiveDiffDetailed.position_code', index=23,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='vendor_bi.ReceiveDiffDetailed.position_name', index=24,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='vendor_bi.ReceiveDiffDetailed.delivery_by', index=25,
      number=28, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_name', full_name='vendor_bi.ReceiveDiffDetailed.delivery_by_name', index=26,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_code', full_name='vendor_bi.ReceiveDiffDetailed.delivery_by_code', index=27,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='vendor_bi.ReceiveDiffDetailed.logistics_type', index=28,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='vendor_bi.ReceiveDiffDetailed.demand_date', index=29,
      number=32, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='vendor_bi.ReceiveDiffDetailed.created_at', index=30,
      number=33, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_code', full_name='vendor_bi.ReceiveDiffDetailed.receiving_code', index=31,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_quantity', full_name='vendor_bi.ReceiveDiffDetailed.s_diff_quantity', index=32,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_quantity', full_name='vendor_bi.ReceiveDiffDetailed.d_diff_quantity', index=33,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_amount', full_name='vendor_bi.ReceiveDiffDetailed.s_diff_amount', index=34,
      number=37, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_amount', full_name='vendor_bi.ReceiveDiffDetailed.d_diff_amount', index=35,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='vendor_bi.ReceiveDiffDetailed.amount', index=36,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_netwrt', full_name='vendor_bi.ReceiveDiffDetailed.s_diff_netwrt', index=37,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_netwrt', full_name='vendor_bi.ReceiveDiffDetailed.d_diff_netwrt', index=38,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='netwrt', full_name='vendor_bi.ReceiveDiffDetailed.netwrt', index=39,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='vendor_bi.ReceiveDiffDetailed.tax_price', index=40,
      number=43, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='vendor_bi.ReceiveDiffDetailed.cost_price', index=41,
      number=44, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1942,
  serialized_end=2926,
)

_GETRECEIVEDIFFCOLLECTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVEDIFFCOLLECTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVEDIFFCOLLECTRESPONSE.fields_by_name['rows'].message_type = _RECEIVEDIFFCOLLECT
_GETRECEIVEDIFFCOLLECTRESPONSE.fields_by_name['total'].message_type = _DIFFTOTAL
_GETRECEIVEDIFFDETAILREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVEDIFFDETAILREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVEDIFFDETAILRESPONSE.fields_by_name['rows'].message_type = _RECEIVEDIFFDETAILED
_GETRECEIVEDIFFDETAILRESPONSE.fields_by_name['total'].message_type = _DIFFTOTAL
_RECEIVEDIFFDETAILED.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFFDETAILED.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['GetReceiveDiffCollectRequest'] = _GETRECEIVEDIFFCOLLECTREQUEST
DESCRIPTOR.message_types_by_name['GetReceiveDiffCollectResponse'] = _GETRECEIVEDIFFCOLLECTRESPONSE
DESCRIPTOR.message_types_by_name['GetReceiveDiffDetailRequest'] = _GETRECEIVEDIFFDETAILREQUEST
DESCRIPTOR.message_types_by_name['GetReceiveDiffDetailResponse'] = _GETRECEIVEDIFFDETAILRESPONSE
DESCRIPTOR.message_types_by_name['DiffTotal'] = _DIFFTOTAL
DESCRIPTOR.message_types_by_name['ReceiveDiffCollect'] = _RECEIVEDIFFCOLLECT
DESCRIPTOR.message_types_by_name['ReceiveDiffDetailed'] = _RECEIVEDIFFDETAILED
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetReceiveDiffCollectRequest = _reflection.GeneratedProtocolMessageType('GetReceiveDiffCollectRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVEDIFFCOLLECTREQUEST,
  __module__ = 'vendor_bi.receive_diff_bi_pb2'
  # @@protoc_insertion_point(class_scope:vendor_bi.GetReceiveDiffCollectRequest)
  ))
_sym_db.RegisterMessage(GetReceiveDiffCollectRequest)

GetReceiveDiffCollectResponse = _reflection.GeneratedProtocolMessageType('GetReceiveDiffCollectResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVEDIFFCOLLECTRESPONSE,
  __module__ = 'vendor_bi.receive_diff_bi_pb2'
  # @@protoc_insertion_point(class_scope:vendor_bi.GetReceiveDiffCollectResponse)
  ))
_sym_db.RegisterMessage(GetReceiveDiffCollectResponse)

GetReceiveDiffDetailRequest = _reflection.GeneratedProtocolMessageType('GetReceiveDiffDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVEDIFFDETAILREQUEST,
  __module__ = 'vendor_bi.receive_diff_bi_pb2'
  # @@protoc_insertion_point(class_scope:vendor_bi.GetReceiveDiffDetailRequest)
  ))
_sym_db.RegisterMessage(GetReceiveDiffDetailRequest)

GetReceiveDiffDetailResponse = _reflection.GeneratedProtocolMessageType('GetReceiveDiffDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVEDIFFDETAILRESPONSE,
  __module__ = 'vendor_bi.receive_diff_bi_pb2'
  # @@protoc_insertion_point(class_scope:vendor_bi.GetReceiveDiffDetailResponse)
  ))
_sym_db.RegisterMessage(GetReceiveDiffDetailResponse)

DiffTotal = _reflection.GeneratedProtocolMessageType('DiffTotal', (_message.Message,), dict(
  DESCRIPTOR = _DIFFTOTAL,
  __module__ = 'vendor_bi.receive_diff_bi_pb2'
  # @@protoc_insertion_point(class_scope:vendor_bi.DiffTotal)
  ))
_sym_db.RegisterMessage(DiffTotal)

ReceiveDiffCollect = _reflection.GeneratedProtocolMessageType('ReceiveDiffCollect', (_message.Message,), dict(
  DESCRIPTOR = _RECEIVEDIFFCOLLECT,
  __module__ = 'vendor_bi.receive_diff_bi_pb2'
  # @@protoc_insertion_point(class_scope:vendor_bi.ReceiveDiffCollect)
  ))
_sym_db.RegisterMessage(ReceiveDiffCollect)

ReceiveDiffDetailed = _reflection.GeneratedProtocolMessageType('ReceiveDiffDetailed', (_message.Message,), dict(
  DESCRIPTOR = _RECEIVEDIFFDETAILED,
  __module__ = 'vendor_bi.receive_diff_bi_pb2'
  # @@protoc_insertion_point(class_scope:vendor_bi.ReceiveDiffDetailed)
  ))
_sym_db.RegisterMessage(ReceiveDiffDetailed)



_VENDORRECEIVEDIFFBISERVICE = _descriptor.ServiceDescriptor(
  name='VendorReceiveDiffBiService',
  full_name='vendor_bi.VendorReceiveDiffBiService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=2929,
  serialized_end=3283,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetReceiveDiffCollect',
    full_name='vendor_bi.VendorReceiveDiffBiService.GetReceiveDiffCollect',
    index=0,
    containing_service=None,
    input_type=_GETRECEIVEDIFFCOLLECTREQUEST,
    output_type=_GETRECEIVEDIFFCOLLECTRESPONSE,
    serialized_options=_b('\202\323\344\223\002/\022-/api/v2/supply/vendor_bi/receive_diff/collect'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReceiveDiffDetail',
    full_name='vendor_bi.VendorReceiveDiffBiService.GetReceiveDiffDetail',
    index=1,
    containing_service=None,
    input_type=_GETRECEIVEDIFFDETAILREQUEST,
    output_type=_GETRECEIVEDIFFDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\0020\022./api/v2/supply/vendor_bi/receive_diff/detailed'),
  ),
])
_sym_db.RegisterServiceDescriptor(_VENDORRECEIVEDIFFBISERVICE)

DESCRIPTOR.services_by_name['VendorReceiveDiffBiService'] = _VENDORRECEIVEDIFFBISERVICE

# @@protoc_insertion_point(module_scope)
