syntax = "proto3";

package manufactory;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

service ProcessingCost{
    // 创建加工费用单
    rpc CreateProcessingCost (CreateProcessingCostRequest) returns (CreateProcessingCostResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/processing/cost"
        body: "*"
        };
    }
    // 加工费用单列表查询
    rpc ListProcessingCost (ListProcessingCostRequest) returns (ListProcessingCostResponse){
        option (google.api.http) = {
        get: "/api/v2/supply/processing/cost/list"
        };
    }
    // 查询加工费用单详情
    rpc GetProcessingCostDetail (GetProcessingCostDetailRequest) returns (GetProcessingCostDetailResponse){
        option (google.api.http) = {
        get: "/api/v2/supply/processing/cost/{receipt_id}/detail"
        };
    }
    // 更新加工费用单
    rpc UpdateProcessingCost (UpdateProcessingCostRequest) returns (UpdateProcessingCostResponse){
        option (google.api.http) = {
        put: "/api/v2/supply/processing/cost/{receipt_id}"
        body: "*"
        };
    }
    // 加工费用单状态变更
    rpc ChangeProcessingCostStatus (ChangeProcessingCostStatusRequest) returns (ChangeProcessingCostStatusResponse){
        option (google.api.http) = {
        put: "/api/v2/supply/processing/cost/{receipt_id}/{status}"
        body: "*"
        };
    }
}

// 创建加工费用单请求参数
message CreateProcessingCostRequest{
    // 账期ID
    uint64 period_id = 1;
    // 订单状态:
    // INITED = 'INITED'           # 新建
    // SUBMITTED = 'SUBMITTED'     # 已提交
    // APPROVED = 'APPROVED'       # 已审核
    // REJECTED = 'REJECTED'       # 已驳回
    string status = 4;
    // 类型*预留*暂不用传
    string type = 5;
    // 月份yyyy-MM
    string month = 6;
    // 加工中心id
    uint64 machining_center_id = 7;
    // 加工中心code
    string machining_center_code = 8;
    // 加工中心名称
    string machining_center_name = 9;
    // 仓位id
    uint64 position_id = 10;
    // 仓位名称
    string position_name = 11;
    // 仓位code
    string position_code = 12;
    // 当月加工单价
    double unit_cost = 13;
    // 计价数量
    double quantity = 14;
    // 单位id
    uint64 unit_id = 15;
    // 单位名称
    string unit_name = 16;
    // 单位规格
    string unit_spec = 17;
    // 是否开启多仓位
    bool opened_position = 18;
    // 备注
    string remark = 20;
    // 幂等性校验请求id
    uint64 request_id = 25;
    uint64 cost_center_id = 26;
}

// 创建加工费用单返回
message CreateProcessingCostResponse{
    // 加工费用单id
    uint64 receipt_id = 1;
    string result = 2;
}

// 加工费用单列表查询请求参数
message ListProcessingCostRequest{
    // 月份
    string start_month = 1;
    string end_month = 2;
    // 加工中心ID列表
    repeated uint64 machining_centers = 3;
    // 账期ID列表
    repeated uint64 period_ids = 4;
    // 单号
    string code = 5;
    // 仓位ID列表
    repeated uint64 position_ids = 7;
    // 'INITED'新建 'SUBMITTED'已提交 'APPROVED'已审核 'REJECTED'已驳回
    repeated string status = 9;
    int64 limit = 10;
    uint64 offset = 11;
    bool include_total = 12;
    // 排序方式  asc or desc
    string order = 13;
    // 排序字段
    string sort = 14;
}

message CostDetail{
    uint64 id = 1;
    uint64 partner_id = 2;
    uint64 created_by = 3;
    string created_name = 4;
    uint64 updated_by = 5;
    string updated_name = 6;
    google.protobuf.Timestamp created_at = 7;
    google.protobuf.Timestamp updated_at = 8;
    string status = 9;
    string code = 11;
    // 月份
    string month = 12;
    // 类型*预留*
    string type = 13;
    // 加工中心id
    uint64 machining_center_id = 14;
    // 加工中心code
    string machining_center_code = 15;
    //加工中心名称
    string machining_center_name = 16;
    // 仓位id
    uint64 position_id = 17;
    // 仓位名称
    string position_name = 18;
    // 仓位code
    string position_code = 19;
    // 当月加工单价
    double unit_cost = 20;
    // 计价数量
    double quantity = 21;
    // 单位id
    uint64 unit_id = 22;
    // 单位名称
    string unit_name = 23;
    // 单位规格
    string unit_spec = 24;
    // 备注
    string remark = 25;
    // 是否开启多仓位
    bool opened_position = 26;
    // 幂等性校验请求id
    uint64 request_id = 30;
    // 处理状态用来标记是否进行分摊计算：INITED/PROCESSING/SUCCESS
    string process_status = 31;
    // 成本中心ID
    uint64 cost_center_id = 32;
    // 账期ID
    uint64 period_id = 33;
    // 账期名称
    string period_name = 34;
}

// 加工费用单列表查询返回参数
message ListProcessingCostResponse{
    repeated CostDetail rows = 1;
    uint64 total = 2;
}

// 查询加工费用单详情请求参数
message GetProcessingCostDetailRequest{
    uint64 receipt_id = 1;
}

message GetProcessingCostDetailResponse{
    CostDetail detail = 34;
}


// 更新加工费用单请求参数
message UpdateProcessingCostRequest{
    // 费用单id
    uint64 receipt_id = 1;
    // 'SUBMITTED'已提交 'APPROVED'已审核 'REJECTED'已驳回
    string status = 2;
    // 是否更新详情(保存操作时调用，不更新状态, 更新详情为true, 状态变更传false)
    bool update_detail = 3;
    // update_detail=true时才传以下参数
    CostDetail detail = 34;
}

message UpdateProcessingCostResponse{
    uint64 receipt_id = 1;
    string result = 2;
}

message ChangeProcessingCostStatusRequest {
    // 费用单id
    uint64 receipt_id = 1;
    // 'SUBMITTED'提交 'APPROVED'审核 'REJECTED'驳回
    string status = 2;
}

message ChangeProcessingCostStatusResponse{
    uint64 receipt_id = 1;
    string result = 2;
}