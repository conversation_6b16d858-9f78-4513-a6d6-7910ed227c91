{"swagger": "2.0", "info": {"title": "manufactory/adjust.proto", "version": "version not set"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/manufactory/adjust": {"get": {"summary": "GetAdjust查询每日损耗表9", "operationId": "GetAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/manufactoryGetAdjustResponse"}}}, "parameters": [{"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "reason_type", "in": "query", "required": false, "type": "string"}, {"name": "branches", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "product_ids", "description": "商品ids.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "status", "description": "每日损耗计划状态.\n\n - INITED: 新建\n - SUBMITTED: 提交\n - APPROVED: 审核\n - REJECTED: 驳回\n - CANCELLED: 作废", "in": "query", "required": false, "type": "array", "items": {"type": "string", "enum": ["NONE", "INITED", "SUBMITTED", "APPROVED", "REJECTED", "CANCELLED"]}}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序(默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "branch_type", "in": "query", "required": false, "type": "string"}, {"name": "sources", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}], "tags": ["ManufactoryAdjust"]}}, "/api/v2/supply/manufactory/adjust/auto_create/cancel": {"put": {"summary": "CancelAdjust 取消每日损耗表20", "operationId": "CancelAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/manufactoryCancelAdjustResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/manufactoryCancelAdjustRequest"}}], "tags": ["ManufactoryAdjust"]}}, "/api/v2/supply/manufactory/adjust/bi/collect": {"get": {"summary": "GetAdjustBiCollect每日损耗表汇总报表18", "operationId": "GetAdjustBiCollect", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/manufactoryGetAdjustBiCollectResponse"}}}, "parameters": [{"name": "category_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "product_name", "in": "query", "required": false, "type": "string"}, {"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "store_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "bom_product_id", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "period_symbol", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序(默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "reason_type", "description": "报废原因.", "in": "query", "required": false, "type": "string"}, {"name": "is_wms_store", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "branch_type", "description": "区分门店还是仓库.", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "hour_offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "position_ids", "description": "暂不使用.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}], "tags": ["ManufactoryAdjust"]}}, "/api/v2/supply/manufactory/adjust/bi/detailed": {"get": {"summary": "GetAdjustCollectDetailed损耗表明细汇总报表21", "operationId": "GetAdjustCollectDetailed", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/manufactoryGetAdjustCollectDetailedResponse"}}}, "parameters": [{"name": "category_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "product_name", "in": "query", "required": false, "type": "string"}, {"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "store_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "bom_product_id", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "order", "description": "排序(默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "is_wms_store", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "branch_type", "description": "区分门店还是仓库.", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "position_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "reason_type", "description": "报废原因.", "in": "query", "required": false, "type": "string"}], "tags": ["ManufactoryAdjust"]}}, "/api/v2/supply/manufactory/adjust/close/auto": {"get": {"summary": "AutoCloseCreatedAdjust自动关闭未确认的损耗表,不传参数默认前一天，传date操作date日期的单子22", "operationId": "AutoCloseCreatedAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/manufactoryAutoCloseCreatedAdjustResponse"}}}, "parameters": [{"name": "adjust_date", "in": "query", "required": false, "type": "string"}, {"name": "partner_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "user_id", "in": "query", "required": false, "type": "string", "format": "uint64"}], "tags": ["ManufactoryAdjust"]}}, "/api/v2/supply/manufactory/adjust/create": {"post": {"summary": "CreatedAdjust手动创建一个每日损耗表14", "operationId": "CreatedAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/manufactoryAdjust"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/manufactoryCreatedAdjustRequest"}}], "tags": ["ManufactoryAdjust"]}}, "/api/v2/supply/manufactory/adjust/material/cost": {"get": {"summary": "物料报废量成本报表", "operationId": "GetMaterialAdjustData", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/manufactoryGetMaterialAdjustDataResponse"}}}, "parameters": [{"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "store_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "material_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "limit", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "offset", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "is_wms_store", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}], "tags": ["ManufactoryAdjust"]}}, "/api/v2/supply/manufactory/adjust/pos": {"post": {"summary": "第三方根据code创建报废", "operationId": "CreatedAdjustByCode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/manufactoryAdjust"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/manufactoryCreatedAdjustByCodeRequest"}}], "tags": ["ManufactoryAdjust"]}}, "/api/v2/supply/manufactory/adjust/product/{adjust_id}/delete": {"put": {"summary": "DeleteAdjustProduct删除每日损耗表的商品17", "operationId": "DeleteAdjustProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/manufactoryDeleteAdjustProductResponse"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/manufactoryDeleteAdjustProductRequest"}}], "tags": ["ManufactoryAdjust"]}}, "/api/v2/supply/manufactory/adjust/store/products": {"get": {"summary": "GetAdjustProductByStoreID查询门店可损耗商品13", "operationId": "GetAdjustProductByStoreID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/manufactoryGetAdjustProductByStoreIDResponse"}}}, "parameters": [{"name": "store_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "adjust_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "search", "in": "query", "required": false, "type": "string"}, {"name": "search_fields", "in": "query", "required": false, "type": "string"}, {"name": "category_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["ManufactoryAdjust"]}}, "/api/v2/supply/manufactory/adjust/{adjust_id}": {"get": {"summary": "GetAdjustByID查询一个每日损耗表11", "operationId": "GetAdjustByID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/manufactoryAdjust"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "branch_type", "in": "query", "required": false, "type": "string"}], "tags": ["ManufactoryAdjust"]}}, "/api/v2/supply/manufactory/adjust/{adjust_id}/approve": {"put": {"summary": "审核一个报废单", "operationId": "ApproveAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/manufactoryApproveAdjustResponse"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/manufactoryApproveAdjustRequest"}}], "tags": ["ManufactoryAdjust"]}}, "/api/v2/supply/manufactory/adjust/{adjust_id}/confirm": {"put": {"summary": "ConfirmAdjust确认一个每日损耗表12", "operationId": "ConfirmAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/manufactoryConfirmAdjustResponse"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/manufactoryConfirmAdjustRequest"}}], "tags": ["ManufactoryAdjust"]}}, "/api/v2/supply/manufactory/adjust/{adjust_id}/delete": {"put": {"summary": "DeleteAdjust删除一个每日损耗表16", "operationId": "DeleteAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/manufactoryDeleteAdjustResponse"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/manufactoryDeleteAdjustRequest"}}], "tags": ["ManufactoryAdjust"]}}, "/api/v2/supply/manufactory/adjust/{adjust_id}/product": {"get": {"summary": "GetAdjustProduct每日损耗表商品查询10", "operationId": "GetAdjustProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/manufactoryGetAdjustProductResponse"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "order", "in": "query", "required": false, "type": "string"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["ManufactoryAdjust"]}}, "/api/v2/supply/manufactory/adjust/{adjust_id}/reject": {"put": {"summary": "驳回一个报废单", "operationId": "RejectAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/manufactoryRejectAdjustResponse"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/manufactoryRejectAdjustRequest"}}], "tags": ["ManufactoryAdjust"]}}, "/api/v2/supply/manufactory/adjust/{adjust_id}/submit": {"put": {"summary": "提交一个报废单", "operationId": "SubmitAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/manufactorySubmitAdjustResponse"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/manufactorySubmitAdjustRequest"}}], "tags": ["ManufactoryAdjust"]}}, "/api/v2/supply/manufactory/adjust/{adjust_id}/update": {"put": {"summary": "UpdateAdjust更新一个每日损耗表15", "operationId": "UpdateAdjust", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/manufactoryAdjust"}}}, "parameters": [{"name": "adjust_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/manufactoryUpdateAdjustRequest"}}], "tags": ["ManufactoryAdjust"]}}}, "definitions": {"GetAdjustRequestSTATUS": {"type": "string", "enum": ["NONE", "INITED", "SUBMITTED", "APPROVED", "REJECTED", "CANCELLED"], "default": "NONE", "title": "- INITED: 新建\n - SUBMITTED: 提交\n - APPROVED: 审核\n - REJECTED: 驳回\n - CANCELLED: 作废"}, "SkuRemarkTag": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "code": {"type": "string"}, "name": {"type": "string"}}}, "manufactoryAD_total": {"type": "object", "properties": {"count": {"type": "string", "format": "uint64"}, "sum_quantity": {"type": "number", "format": "double"}, "sum_accounting_quantity": {"type": "number", "format": "double"}, "sum_qty": {"type": "number", "format": "double"}, "sum_accounting_qty": {"type": "number", "format": "double"}}}, "manufactoryAdjust": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "adjust_order_number": {"type": "string", "format": "uint64"}, "adjust_store": {"type": "string", "format": "uint64"}, "adjust_store_secondary_id": {"type": "string"}, "code": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "process_status": {"type": "string"}, "reason_type": {"type": "string"}, "remark": {"type": "string"}, "status": {"type": "string"}, "adjust_date": {"type": "string", "format": "date-time"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_by": {"type": "string", "format": "uint64"}, "updated_by": {"type": "string", "format": "uint64"}, "user_id": {"type": "string", "format": "uint64"}, "branch_batch_id": {"type": "string", "format": "uint64"}, "schedule_code": {"type": "string"}, "schedule_id": {"type": "string", "format": "uint64"}, "request_id": {"type": "string", "format": "uint64"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "receive_id": {"type": "string", "format": "uint64"}, "receive_code": {"type": "string"}, "schedule_name": {"type": "string"}, "branch_type": {"type": "string"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/manufactoryAttachments"}, "title": "附件"}, "source": {"type": "string", "title": "\"POS_ADJUST\"      # POS端报废\n\"MANUAL_CREATED\"  # 手动新建\n\"PLAN_CREATED\"    # 报废计划创建"}, "reason_name": {"type": "string"}, "reject_reason": {"type": "string", "title": "驳回原因"}, "total_amount": {"type": "number", "format": "double"}, "total_sales_amount": {"type": "number", "format": "double"}}}, "manufactoryAdjustBiCollectResponse": {"type": "object", "properties": {"accounting_quantity": {"type": "number", "format": "double"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "category_code": {"type": "string"}, "category_id": {"type": "string", "format": "uint64"}, "category_name": {"type": "string"}, "product_code": {"type": "string", "title": "\"category_parent\""}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "store_code": {"type": "string"}, "store_id": {"type": "string", "format": "uint64"}, "store_name": {"type": "string"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "reason_type": {"type": "string"}, "reason_name": {"type": "string"}, "bom_product_id": {"type": "string", "format": "uint64", "title": "拆解原料"}, "bom_product_code": {"type": "string"}, "bom_product_name": {"type": "string"}, "bom_unit_id": {"type": "string", "format": "uint64", "title": "bom配方单位"}, "bom_unit_code": {"type": "string"}, "bom_unit_name": {"type": "string"}, "bom_accounting_unit_id": {"type": "string", "format": "uint64", "title": "bom核算单位"}, "bom_accounting_unit_code": {"type": "string"}, "bom_accounting_unit_name": {"type": "string"}, "qty": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double"}, "accounting_qty": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "period_symbol": {"type": "string"}, "position_id": {"type": "string", "format": "uint64"}, "position_code": {"type": "string"}, "position_name": {"type": "string"}, "adjust_date": {"type": "string", "format": "date-time"}}}, "manufactoryAdjustCollectDetailed": {"type": "object", "properties": {"accounting_quantity": {"type": "number", "format": "double"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "adjust_code": {"type": "string"}, "adjust_date": {"type": "string", "format": "date-time"}, "adjust_id": {"type": "string", "format": "uint64"}, "category_code": {"type": "string"}, "category_id": {"type": "string", "format": "uint64"}, "category_name": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "product_code": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "reason_type": {"type": "string"}, "reason_name": {"type": "string"}, "store_code": {"type": "string"}, "store_id": {"type": "string", "format": "uint64"}, "store_name": {"type": "string"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "bom_product_id": {"type": "string", "format": "uint64", "title": "拆解原料"}, "bom_product_code": {"type": "string"}, "bom_product_name": {"type": "string"}, "bom_unit_id": {"type": "string", "format": "uint64", "title": "bom配方单位"}, "bom_unit_code": {"type": "string"}, "bom_unit_name": {"type": "string"}, "bom_accounting_unit_id": {"type": "string", "format": "uint64", "title": "bom核算单位"}, "bom_accounting_unit_code": {"type": "string"}, "bom_accounting_unit_name": {"type": "string"}, "qty": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double"}, "accounting_qty": {"type": "number", "format": "double"}, "cost": {"type": "number", "format": "double"}, "branch_type": {"type": "string"}, "remark": {"type": "string"}, "position_id": {"type": "string", "format": "uint64"}, "position_code": {"type": "string"}, "position_name": {"type": "string"}}}, "manufactoryAdjustPositionProducts": {"type": "object", "properties": {"position_id": {"type": "string", "format": "uint64"}, "position_code": {"type": "string"}, "position_name": {"type": "string"}, "products": {"type": "array", "items": {"$ref": "#/definitions/manufactoryAdjustProduct"}}, "total": {"type": "string", "format": "uint64"}}}, "manufactoryAdjustProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "accounting_quantity": {"type": "number", "format": "double"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "accounting_unit_spec": {"type": "string"}, "adjust_id": {"type": "string", "format": "uint64"}, "adjust_store": {"type": "string", "format": "uint64"}, "confirmed_quantity": {"type": "number", "format": "double"}, "created_by": {"type": "string", "format": "uint64"}, "is_confirmed": {"type": "boolean", "format": "boolean"}, "item_number": {"type": "integer", "format": "int64"}, "material_number": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "product_code": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "spec": {"type": "string"}, "stocktake_quantity": {"type": "number", "format": "double"}, "stocktake_unit_id": {"type": "string", "format": "uint64"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "unit_spec": {"type": "string"}, "updated_by": {"type": "string", "format": "uint64"}, "adjust_date": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_at": {"type": "string", "format": "date-time"}, "user_id": {"type": "string", "format": "uint64"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "reason_type": {"type": "string"}, "convert_accounting_quantity": {"type": "number", "format": "double"}, "is_bom": {"type": "boolean", "format": "boolean"}, "position_id": {"type": "string", "format": "uint64"}, "sku_remark": {"type": "string"}, "units": {"type": "array", "items": {"$ref": "#/definitions/manufactoryCreateAdjustProductUint"}}, "model_name": {"type": "string"}, "tax_rate": {"type": "number", "format": "double"}, "tax_price": {"type": "number", "format": "double"}, "amount": {"type": "number", "format": "double"}, "tax_amount": {"type": "number", "format": "double"}, "cost_price": {"type": "number", "format": "double"}, "sales_amount": {"type": "number", "format": "double"}, "sales_price": {"type": "number", "format": "double"}}}, "manufactoryAdjustResponse": {"type": "object", "properties": {"accounting_quantity": {"type": "number", "format": "double", "title": "核算数量"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "category_code": {"type": "string"}, "category_id": {"type": "string", "format": "uint64"}, "category_name": {"type": "string"}, "product_code": {"type": "string", "title": "\"category_parent\""}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double", "title": "报废数量"}, "store_code": {"type": "string", "title": "门店编号，id,名称"}, "store_id": {"type": "string", "format": "uint64"}, "store_name": {"type": "string"}, "unit_id": {"type": "string", "format": "uint64", "title": "单位"}, "unit_name": {"type": "string"}, "reason_type": {"type": "string"}, "bom_product_id": {"type": "string", "format": "uint64", "title": "拆解原料"}, "bom_product_code": {"type": "string"}, "bom_product_name": {"type": "string"}, "qty": {"type": "number", "format": "double"}, "bom_unit_id": {"type": "string", "format": "uint64", "title": "bom配方单位"}, "bom_unit_code": {"type": "string"}, "bom_unit_name": {"type": "string"}, "bom_accounting_unit_id": {"type": "string", "format": "uint64", "title": "bom核算单位"}, "bom_accounting_unit_code": {"type": "string"}, "bom_accounting_unit_name": {"type": "string"}, "accounting_qty": {"type": "number", "format": "double"}, "price": {"type": "number", "format": "double", "title": "单位成本"}, "cost": {"type": "number", "format": "double", "title": "物料成本"}, "adjust_date": {"type": "string", "title": "报废日期"}}}, "manufactoryApproveAdjustRequest": {"type": "object", "properties": {"adjust_id": {"type": "string", "format": "uint64"}}}, "manufactoryApproveAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "manufactoryAttachments": {"type": "object", "properties": {"type": {"type": "string"}, "url": {"type": "string"}}}, "manufactoryAutoCloseCreatedAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "manufactoryCancelAdjustRequest": {"type": "object", "properties": {"adjust_id": {"type": "string", "format": "uint64"}}}, "manufactoryCancelAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "manufactoryConfirmAdjustRequest": {"type": "object", "properties": {"adjust_id": {"type": "string", "format": "uint64"}, "branch_type": {"type": "string"}}}, "manufactoryConfirmAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "manufactoryCreateAdjustProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64"}, "loss_report_order": {"type": "string"}, "product_code": {"type": "string"}, "product_name": {"type": "string"}, "model_name": {"type": "string"}, "storage_type": {"type": "string"}, "product_category_id": {"type": "string", "format": "uint64"}, "units": {"type": "array", "items": {"$ref": "#/definitions/manufactoryCreateAdjustProductUint"}}, "barcode": {"type": "array", "items": {"type": "string"}}}}, "manufactoryCreateAdjustProductUint": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "数据id"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "scope_id": {"type": "string", "format": "uint64", "title": "scope_id"}, "name": {"type": "string"}, "code": {"type": "string"}, "updated": {"type": "string"}, "rate": {"type": "number", "format": "double"}, "default": {"type": "boolean", "format": "boolean"}, "order": {"type": "boolean", "format": "boolean"}, "purchase": {"type": "boolean", "format": "boolean"}, "sales": {"type": "boolean", "format": "boolean"}, "stocktake": {"type": "boolean", "format": "boolean"}, "bom": {"type": "boolean", "format": "boolean"}, "default_stocktake": {"type": "boolean", "format": "boolean"}, "transfer": {"type": "boolean", "format": "boolean"}, "tax_rate": {"type": "number", "format": "double"}, "tax_price": {"type": "number", "format": "double"}, "cost_price": {"type": "number", "format": "double"}, "sales_price": {"type": "number", "format": "double"}}}, "manufactoryCreatedAdjustByCodeRequest": {"type": "object", "properties": {"adjust_store": {"type": "string"}, "products": {"type": "array", "items": {"$ref": "#/definitions/manufactoryCreatedAdjustProductByCode"}}, "reason_type": {"type": "string"}, "remark": {"type": "string"}, "request_id": {"type": "string"}, "adjust_date": {"type": "string"}, "lan": {"type": "string"}}}, "manufactoryCreatedAdjustProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "product_id": {"type": "string", "format": "uint64"}, "unit_id": {"type": "string", "format": "uint64"}, "quantity": {"type": "number", "format": "double"}, "reason_type": {"type": "string"}, "position_id": {"type": "string", "format": "uint64"}, "skuRemark": {"type": "array", "items": {"$ref": "#/definitions/manufactorySkuRemark"}}, "tax_rate": {"type": "number", "format": "double"}, "tax_price": {"type": "number", "format": "double"}, "amount": {"type": "number", "format": "double"}, "tax_amount": {"type": "number", "format": "double"}, "cost_price": {"type": "number", "format": "double"}, "sales_price": {"type": "number", "format": "double"}, "sales_amount": {"type": "number", "format": "double"}}}, "manufactoryCreatedAdjustProductByCode": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "product_code": {"type": "string"}, "unit_code": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "reason_type": {"type": "string"}, "skuRemark": {"type": "array", "items": {"$ref": "#/definitions/manufactorySkuRemark"}}}}, "manufactoryCreatedAdjustRequest": {"type": "object", "properties": {"adjust_store": {"type": "string", "format": "uint64"}, "products": {"type": "array", "items": {"$ref": "#/definitions/manufactoryCreatedAdjustProduct"}}, "reason_type": {"type": "string"}, "remark": {"type": "string"}, "request_id": {"type": "string", "format": "uint64"}, "adjust_date": {"type": "string", "format": "date-time"}, "branch_type": {"type": "string", "title": "区分门店还是仓库: STORE or WAREHOUSE"}, "position_id": {"type": "string", "format": "uint64"}, "source": {"type": "string", "title": "单据来源\n\"POS_ADJUST\"      # POS端报废\n\"MANUAL_CREATED\"  # 手动新建\n\"PLAN_CREATED\"    # 报废计划创建"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/manufactoryAttachments"}, "title": "附件"}}}, "manufactoryDeleteAdjustProductRequest": {"type": "object", "properties": {"adjust_id": {"type": "string", "format": "uint64"}, "ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "manufactoryDeleteAdjustProductResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "manufactoryDeleteAdjustRequest": {"type": "object", "properties": {"adjust_id": {"type": "string", "format": "uint64"}}}, "manufactoryDeleteAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "manufactoryGetAdjustBiCollectResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/manufactoryAdjustBiCollectResponse"}}, "total": {"$ref": "#/definitions/manufactoryAD_total"}}}, "manufactoryGetAdjustCollectDetailedResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/manufactoryAdjustCollectDetailed"}}, "total": {"$ref": "#/definitions/manufactoryAD_total"}}}, "manufactoryGetAdjustProductByStoreIDResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/manufactoryCreateAdjustProduct"}}, "total": {"type": "integer", "format": "int64"}}}, "manufactoryGetAdjustProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/manufactoryAdjustProduct"}}, "total": {"type": "integer", "format": "int64"}, "position_rows": {"type": "array", "items": {"$ref": "#/definitions/manufactoryAdjustPositionProducts"}}}}, "manufactoryGetAdjustResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/manufactoryAdjust"}}, "total": {"type": "integer", "format": "int64"}}}, "manufactoryGetMaterialAdjustDataResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/manufactoryAdjustResponse"}}, "total": {"$ref": "#/definitions/manufactoryAD_total"}}}, "manufactoryRejectAdjustRequest": {"type": "object", "properties": {"adjust_id": {"type": "string", "format": "uint64"}, "reject_reason": {"type": "string", "title": "驳回原因"}}}, "manufactoryRejectAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "manufactorySkuRemark": {"type": "object", "properties": {"name": {"$ref": "#/definitions/SkuRemarkTag"}, "values": {"$ref": "#/definitions/SkuRemarkTag"}}}, "manufactorySubmitAdjustRequest": {"type": "object", "properties": {"adjust_id": {"type": "string", "format": "uint64"}}}, "manufactorySubmitAdjustResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "manufactoryUpdateAdjustRequest": {"type": "object", "properties": {"adjust_id": {"type": "string", "format": "uint64"}, "products": {"type": "array", "items": {"$ref": "#/definitions/manufactoryCreatedAdjustProduct"}}, "remark": {"type": "string"}, "reason_type": {"type": "string"}, "adjust_date": {"type": "string", "format": "date-time"}, "branch_type": {"type": "string", "title": "区分门店还是仓库: STORE or WAREHOUSE"}, "attachments": {"type": "array", "items": {"$ref": "#/definitions/manufactoryAttachments"}, "title": "附件"}, "position_id": {"type": "string", "format": "uint64"}}}}}