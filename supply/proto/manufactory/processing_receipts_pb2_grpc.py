# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from manufactory import processing_receipts_pb2 as manufactory_dot_processing__receipts__pb2


class ProcessingReceiptsStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateProcessingReceipts = channel.unary_unary(
        '/manufactory.ProcessingReceipts/CreateProcessingReceipts',
        request_serializer=manufactory_dot_processing__receipts__pb2.CreateProcessingReceiptsRequest.SerializeToString,
        response_deserializer=manufactory_dot_processing__receipts__pb2.CreateProcessingReceiptsResponse.FromString,
        )
    self.ListProcessingReceipts = channel.unary_unary(
        '/manufactory.ProcessingReceipts/ListProcessingReceipts',
        request_serializer=manufactory_dot_processing__receipts__pb2.ListProcessingReceiptsRequest.SerializeToString,
        response_deserializer=manufactory_dot_processing__receipts__pb2.ListProcessingReceiptsResponse.FromString,
        )
    self.GetProcessingReceiptsDetail = channel.unary_unary(
        '/manufactory.ProcessingReceipts/GetProcessingReceiptsDetail',
        request_serializer=manufactory_dot_processing__receipts__pb2.GetProcessingReceiptsDetailRequest.SerializeToString,
        response_deserializer=manufactory_dot_processing__receipts__pb2.GetProcessingReceiptsDetailResponse.FromString,
        )
    self.UpdateProcessingReceipts = channel.unary_unary(
        '/manufactory.ProcessingReceipts/UpdateProcessingReceipts',
        request_serializer=manufactory_dot_processing__receipts__pb2.UpdateProcessingReceiptsRequest.SerializeToString,
        response_deserializer=manufactory_dot_processing__receipts__pb2.UpdateProcessingReceiptsResponse.FromString,
        )
    self.ChangeProcessingReceiptsStatus = channel.unary_unary(
        '/manufactory.ProcessingReceipts/ChangeProcessingReceiptsStatus',
        request_serializer=manufactory_dot_processing__receipts__pb2.ChangeProcessingReceiptsStatusRequest.SerializeToString,
        response_deserializer=manufactory_dot_processing__receipts__pb2.ChangeProcessingReceiptsStatusResponse.FromString,
        )


class ProcessingReceiptsServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def CreateProcessingReceipts(self, request, context):
    """创建加工单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListProcessingReceipts(self, request, context):
    """加工单列表查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProcessingReceiptsDetail(self, request, context):
    """查询加工单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateProcessingReceipts(self, request, context):
    """更新加工单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ChangeProcessingReceiptsStatus(self, request, context):
    """加工单状态变更
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_ProcessingReceiptsServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateProcessingReceipts': grpc.unary_unary_rpc_method_handler(
          servicer.CreateProcessingReceipts,
          request_deserializer=manufactory_dot_processing__receipts__pb2.CreateProcessingReceiptsRequest.FromString,
          response_serializer=manufactory_dot_processing__receipts__pb2.CreateProcessingReceiptsResponse.SerializeToString,
      ),
      'ListProcessingReceipts': grpc.unary_unary_rpc_method_handler(
          servicer.ListProcessingReceipts,
          request_deserializer=manufactory_dot_processing__receipts__pb2.ListProcessingReceiptsRequest.FromString,
          response_serializer=manufactory_dot_processing__receipts__pb2.ListProcessingReceiptsResponse.SerializeToString,
      ),
      'GetProcessingReceiptsDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetProcessingReceiptsDetail,
          request_deserializer=manufactory_dot_processing__receipts__pb2.GetProcessingReceiptsDetailRequest.FromString,
          response_serializer=manufactory_dot_processing__receipts__pb2.GetProcessingReceiptsDetailResponse.SerializeToString,
      ),
      'UpdateProcessingReceipts': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateProcessingReceipts,
          request_deserializer=manufactory_dot_processing__receipts__pb2.UpdateProcessingReceiptsRequest.FromString,
          response_serializer=manufactory_dot_processing__receipts__pb2.UpdateProcessingReceiptsResponse.SerializeToString,
      ),
      'ChangeProcessingReceiptsStatus': grpc.unary_unary_rpc_method_handler(
          servicer.ChangeProcessingReceiptsStatus,
          request_deserializer=manufactory_dot_processing__receipts__pb2.ChangeProcessingReceiptsStatusRequest.FromString,
          response_serializer=manufactory_dot_processing__receipts__pb2.ChangeProcessingReceiptsStatusResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'manufactory.ProcessingReceipts', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
