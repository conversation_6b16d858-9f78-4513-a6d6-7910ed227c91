{"swagger": "2.0", "info": {"title": "transfer.proto", "version": "version not set"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/inner/transfer": {"post": {"summary": "CreateTransfer 自动创建内部调拨单接口(暂时仅内部rpc调用)", "operationId": "CreateInnerTransfer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/transferCreateInnerTransferResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/transferCreateInnerTransferRequest"}}], "tags": ["transfer"]}}, "/api/v2/supply/transfer": {"get": {"summary": "GetTransfer 查询调拨单1", "operationId": "GetTransfer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/transferGetTransferResponse"}}}, "parameters": [{"name": "include_total", "description": "是否包含总数.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "status", "description": "状态.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "enum": ["NONE", "INITED", "SUBMITTED", "CONFIRMED", "CANCELLED"]}}, {"name": "shipping_stores", "description": "调出门店.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "receiving_stores", "description": "调入门店.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "offset", "description": "分页开始处.", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "description": "返回条数.", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "start_date", "description": "查询开始时间.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "查询结束时间.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "no_jde_code", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "auto_confirm", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "jde_code", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序(默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "branch_type", "description": "录入方式，区分门店/仓库/加工中心 STORE/WAREHOUSE/MACHINING_CENTER.", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "types", "description": "调拨类型(自动AUTO/手动MANUAL).", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "sub_type", "description": "区分内部`INTERNAL`/外部`EXTERNAL`调拨.", "in": "query", "required": false, "type": "string"}, {"name": "receiving_positions", "description": "接收仓位.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "shipping_positions", "description": "调出仓位.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}], "tags": ["transfer"]}}, "/api/v2/supply/transfer/bi/detailed": {"get": {"summary": "GetTransferCollectDetailed调拨单明细汇总报表15", "operationId": "GetTransferCollectDetailed", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/transferGetTransferCollectDetailedResponse"}}}, "parameters": [{"name": "st_ids", "description": "门店id列表.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "store_ids", "description": "掉入调出门店id，不用.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "category_ids", "description": "商品类别列表.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "product_name", "in": "query", "required": false, "type": "string"}, {"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "is_in", "description": "true时返回收货门店，false返回调出门店.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "order", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "jde_code", "in": "query", "required": false, "type": "string"}, {"name": "is_wms_store", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "branch_type", "description": "区分门店还是仓库.", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "type", "description": "调拨类型(自动AUTO/手动MANUAL).", "in": "query", "required": false, "type": "string"}, {"name": "sub_type", "description": "区分内部`INTERNAL`/外部`EXTERNAL`调拨(必传).", "in": "query", "required": false, "type": "string"}], "tags": ["transfer"]}}, "/api/v2/supply/transfer/collect/report": {"get": {"summary": "SubmitTransferReceiving 提交调拨单收货数量13\n   rpc SubmitTransferReceiving (SubmitTransferReceivingRequest) returns (Transfer) {\n       option (google.api.http) = { put: \"/api/v2/supply/transfer/main/{transfer_id}/submit/receiving\" body: \"*\"};\n   }\nFinalizedTransfer 核算完成调拨单13\n   rpc FinalizedTransfer (FinalizedTransferRequest) returns (FinalizedTransferResponse) {\n       option (google.api.http) = { put: \"/api/v2/supply/transfer/main/{transfer_id}/finalize\"};\n   }\nGetTransferCollect调拨单汇总报表14", "operationId": "GetTransferCollect", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/transferGetTransferCollectResponse"}}}, "parameters": [{"name": "st_ids", "description": "门店id列表.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "store_ids", "description": "掉入调出门店id，不用.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "category_ids", "description": "商品类别列表.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "product_name", "in": "query", "required": false, "type": "string"}, {"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "is_in", "description": "true时返回收货门店，false返回调出门店.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "order", "description": "排序(默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "jde_code", "in": "query", "required": false, "type": "string"}, {"name": "is_wms_store", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "branch_type", "description": "区分门店还是仓库.", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "type", "description": "调拨类型(自动AUTO/手动MANUAL).", "in": "query", "required": false, "type": "string"}, {"name": "sub_type", "description": "区分内部`INTERNAL`/外部`EXTERNAL`调拨(必传).", "in": "query", "required": false, "type": "string"}], "tags": ["transfer"]}}, "/api/v2/supply/transfer/main": {"post": {"summary": "CreateTransfer 创建调拨单6", "operationId": "CreateTransfer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/transferTransfer"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/transferCreateTransferRequest"}}], "tags": ["transfer"]}}, "/api/v2/supply/transfer/main/{transfer_id}/cancel": {"put": {"summary": "CancelTransfer 取消调拨单13", "operationId": "CancelTransfer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/transferTransfer"}}}, "parameters": [{"name": "transfer_id", "description": "调拨单ID", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/transferCancelTransferRequest"}}], "tags": ["transfer"]}}, "/api/v2/supply/transfer/main/{transfer_id}/confirm": {"put": {"summary": "ConfirmTransfer 确认调拨单8", "operationId": "ConfirmTransfer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/transferTransfer"}}}, "parameters": [{"name": "transfer_id", "description": "调拨单ID", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/transferConfirmTransferRequest"}}], "tags": ["transfer"]}}, "/api/v2/supply/transfer/main/{transfer_id}/delete": {"put": {"summary": "DeleteTransfer 删除调拨单11", "operationId": "DeleteTransfer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/transferDeleteTransferResponse"}}}, "parameters": [{"name": "transfer_id", "description": "调拨单ID", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/transferDeleteTransferRequest"}}], "tags": ["transfer"]}}, "/api/v2/supply/transfer/main/{transfer_id}/submit": {"put": {"summary": "SubmitTransfer 提交调拨单12", "operationId": "SubmitTransfer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/transferTransfer"}}}, "parameters": [{"name": "transfer_id", "description": "调拨单ID", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/transferSubmitTransferRequest"}}], "tags": ["transfer"]}}, "/api/v2/supply/transfer/main/{transfer_id}/update": {"put": {"summary": "UpdateTransfer 修改调拨单7", "operationId": "UpdateTransfer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/transferUpdateTransferResponse"}}}, "parameters": [{"name": "transfer_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/transferUpdateTransferRequest"}}], "tags": ["transfer"]}}, "/api/v2/supply/transfer/product/{transfer_product_id}/delete": {"put": {"summary": "DeleteTransferProduct 删除调拨单商品10", "operationId": "DeleteTransferProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/transferDeleteTransferProductResponse"}}}, "parameters": [{"name": "transfer_product_id", "description": "调拨商品条目ID", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/transferDeleteTransferProductRequest"}}], "tags": ["transfer"]}}, "/api/v2/supply/transfer/store/{store_id}/product": {"get": {"summary": "GetTransferProductByBranchID 取得门店可调拨商品3", "operationId": "GetTransferProductByBranchID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/transferGetTransferProductByBranchIDResponse"}}}, "parameters": [{"name": "store_id", "description": "门店id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "include_total", "description": "是否包含总数.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "order", "description": "排序方式.", "in": "query", "required": false, "type": "string"}, {"name": "offset", "description": "分页开始处.", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "description": "返回条数.", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "search_fields", "description": "模糊查询方式.", "in": "query", "required": false, "type": "string"}, {"name": "search", "description": "模糊查询条件.", "in": "query", "required": false, "type": "string"}, {"name": "category_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["transfer"]}}, "/api/v2/supply/transfer/store/{store_id}/region": {"get": {"summary": "GetTransferRegionByID 查询相同属性区域门店4", "operationId": "GetTransferRegionByBranchID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/transferGetTransferRegionByBranchIDResponse"}}}, "parameters": [{"name": "store_id", "description": "门店ID", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["transfer"]}}, "/api/v2/supply/transfer/{transfer_id}": {"get": {"summary": "GetTransferByID 查询一个调拨单2", "operationId": "GetTransferByID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/transferGetTransferByIDResponse"}}}, "parameters": [{"name": "transfer_id", "description": "调拨单id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["transfer"]}}, "/api/v2/supply/transfer/{transfer_id}/product": {"get": {"summary": "GetTransferProductByTransferID 获取一个调拨单商品5", "operationId": "GetTransferProductByTransferID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/transferGetTransferProductByTransferIDResponse"}}}, "parameters": [{"name": "transfer_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "include_total", "description": "是否包含总数.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "order", "description": "排序方式.", "in": "query", "required": false, "type": "string"}, {"name": "offset", "description": "分页开始处.", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "description": "返回条数.", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["transfer"]}}, "/ping": {"get": {"summary": "Ping 健康检查", "operationId": "<PERSON>", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/transferPong"}}}, "tags": ["transfer"]}}}, "definitions": {"protobufListValue": {"type": "object", "properties": {"values": {"type": "array", "items": {"$ref": "#/definitions/protobufValue"}, "description": "Repeated field of dynamically typed values."}}, "description": "`ListValue` is a wrapper around a repeated field of values.\n\nThe JSON representation for `ListValue` is JSON array."}, "protobufNullValue": {"type": "string", "enum": ["NULL_VALUE"], "default": "NULL_VALUE", "description": "`NullValue` is a singleton enumeration to represent the null value for the\n`Value` type union.\n\n The JSON representation for `NullValue` is JSON `null`.\n\n - NULL_VALUE: Null value."}, "protobufStruct": {"type": "object", "properties": {"fields": {"type": "object", "additionalProperties": {"$ref": "#/definitions/protobufValue"}, "description": "Unordered map of dynamically typed values."}}, "description": "`Struct` represents a structured data value, consisting of fields\nwhich map to dynamically typed values. In some languages, `Struct`\nmight be supported by a native representation. For example, in\nscripting languages like JS a struct is represented as an\nobject. The details of that representation are described together\nwith the proto support for the language.\n\nThe JSON representation for `Struct` is JSON object."}, "protobufValue": {"type": "object", "properties": {"null_value": {"$ref": "#/definitions/protobufNullValue", "description": "Represents a null value."}, "number_value": {"type": "number", "format": "double", "description": "Represents a double value."}, "string_value": {"type": "string", "description": "Represents a string value."}, "bool_value": {"type": "boolean", "format": "boolean", "description": "Represents a boolean value."}, "struct_value": {"$ref": "#/definitions/protobufStruct", "description": "Represents a structured value."}, "list_value": {"$ref": "#/definitions/protobufListValue", "description": "Represents a repeated `Value`."}}, "description": "`Value` represents a dynamically typed value which can be either\nnull, a number, a string, a boolean, a recursive struct value, or a\nlist of values. A producer of value is expected to set one of that\nvariants, absence of any variant indicates an error.\n\nThe JSON representation for `Value` is JSON value."}, "transferCancelTransferRequest": {"type": "object", "properties": {"transfer_id": {"type": "string", "format": "uint64", "title": "调拨单ID"}}}, "transferConfirmPostTransferProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64", "title": "商品ID"}, "unit_id": {"type": "string", "format": "uint64", "title": "调拨单位ID"}, "confirmed_received_quantity": {"type": "number", "format": "double", "title": "收货数量"}}}, "transferConfirmTransferRequest": {"type": "object", "properties": {"transfer_id": {"type": "string", "format": "uint64", "title": "调拨单ID"}, "receiver": {"type": "string", "format": "uint64"}, "receiving_store": {"type": "string", "format": "uint64"}, "products": {"type": "array", "items": {"$ref": "#/definitions/transferConfirmPostTransferProduct"}}, "branch_type": {"type": "string", "title": "录入方式，区分门店还是仓库STORE/WAREHOUSE"}}}, "transferCreateInnerTransferRequest": {"type": "object", "properties": {"request_id": {"type": "string", "format": "uint64", "title": "请求ID"}, "shipping_store": {"type": "string", "format": "uint64", "title": "调出门店（内部调拨只传调出门店）"}, "receiving_store": {"type": "string", "format": "uint64", "title": "接收门店"}, "products": {"type": "array", "items": {"$ref": "#/definitions/transferPostTransferProduct"}}, "remark": {"type": "string", "title": "备注"}, "shipping_date": {"type": "string", "format": "date-time", "title": "调出时间"}, "transfer_date": {"type": "string", "format": "date-time", "title": "调拨单日期"}, "branch_type": {"type": "string", "title": "录入方式，区分门店/仓库/加工中心STORE/WAREHOUSE/MACHINING_CENTER"}, "attachments": {"type": "string"}, "type": {"type": "string", "title": "调拨类型(自动AUTO/手动MANUAL)"}, "sub_type": {"type": "string", "title": "区分内部`INTERNAL`/外部`EXTERNAL`调拨"}, "receiving_position": {"type": "string", "format": "uint64", "title": "接收仓位id"}, "shipping_position": {"type": "string", "format": "uint64", "title": "调出仓位id"}, "receiving_position_name": {"type": "string", "title": "接收仓位名称"}, "shipping_position_name": {"type": "string", "title": "调出仓位名称"}, "sub_account_type": {"type": "string", "title": "子账户类型，如果开启了多仓位值为\"position\"没配置多仓位为空"}}}, "transferCreateInnerTransferResponse": {"type": "object", "properties": {"transfer_id": {"type": "string", "format": "uint64"}}}, "transferCreateTransferRequest": {"type": "object", "properties": {"request_id": {"type": "string", "format": "uint64", "title": "请求ID"}, "shipping_store": {"type": "string", "format": "uint64", "title": "调出门店"}, "receiving_store": {"type": "string", "format": "uint64", "title": "接收门店"}, "products": {"type": "array", "items": {"$ref": "#/definitions/transferPostTransferProduct"}}, "remark": {"type": "string", "title": "备注"}, "shipping_date": {"type": "string", "format": "date-time", "title": "调出时间"}, "transfer_date": {"type": "string", "format": "date-time", "title": "调拨单日期"}, "branch_type": {"type": "string", "title": "录入方式，区分门店/仓库/加工中心STORE/WAREHOUSE/MACHINING_CENTER"}, "attachments": {"type": "string"}, "type": {"type": "string", "title": "调拨类型(自动AUTO/手动MANUAL)"}, "sub_type": {"type": "string", "title": "区分内部`INTERNAL`/外部`EXTERNAL`调拨"}, "receiving_position": {"type": "string", "format": "uint64", "title": "接收仓位"}, "shipping_position": {"type": "string", "format": "uint64", "title": "调出仓位"}, "receiving_position_name": {"type": "string", "title": "接收仓位名称"}, "shipping_position_name": {"type": "string", "title": "调出仓位名称"}, "sub_account_type": {"type": "string", "title": "子账户类型，如果开启了多仓位值为\"position\"没配置多仓位为空"}}}, "transferDeleteTransferProductRequest": {"type": "object", "properties": {"transfer_product_id": {"type": "string", "format": "uint64", "title": "调拨商品条目ID"}}}, "transferDeleteTransferProductResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "transferDeleteTransferRequest": {"type": "object", "properties": {"transfer_id": {"type": "string", "format": "uint64", "title": "调拨单ID"}}}, "transferDeleteTransferResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "transferGetTransferByIDResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "调拨单ID"}, "master_id": {"type": "string", "format": "uint64"}, "partner_id": {"type": "string", "format": "uint64"}, "transfer_order_number": {"type": "string", "format": "uint64", "title": "调拨单号"}, "shipping_store": {"type": "string", "format": "uint64", "title": "调拨门店"}, "shipping_store_name": {"type": "string", "title": "调拨门店副ID"}, "receiving_store_name": {"type": "string", "title": "收货门店us_ID"}, "receiving_store": {"type": "string", "format": "uint64", "title": "收货门店"}, "code": {"type": "string", "title": "调拨单编码"}, "description": {"type": "string", "title": "描述"}, "reason_type": {"type": "string", "title": "调拨单"}, "receiver": {"type": "string", "format": "uint64", "title": "接收者"}, "remark": {"type": "string", "title": "备注"}, "shipper": {"type": "string", "format": "uint64", "title": "调拨人"}, "extends": {"type": "string"}, "type": {"type": "string"}, "status": {"$ref": "#/definitions/transferGetTransferByIDResponseSTATUS"}, "process_status": {"$ref": "#/definitions/transferP_STATUS", "title": "操作状态"}, "sub_type": {"type": "string"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人"}, "transfer_date": {"type": "string", "format": "date-time", "title": "调拨单日期"}, "receiving_date": {"type": "string", "format": "date-time", "title": "调拨单收货日"}, "shipping_date": {"type": "string", "format": "date-time", "title": "调拨时间"}, "created_at": {"type": "string", "format": "date-time", "title": "调拨单创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "user_id": {"type": "string", "format": "uint64"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "jde_order_id": {"type": "string"}, "auto_confirm": {"type": "boolean", "format": "boolean"}, "branch_type": {"type": "string"}, "attachments": {"type": "string"}, "receiving_position": {"type": "string", "format": "uint64", "title": "接收仓位id"}, "receiving_position_name": {"type": "string", "title": "接收仓位名称"}, "shipping_position": {"type": "string", "format": "uint64", "title": "调出仓位id"}, "shipping_position_name": {"type": "string", "title": "调出仓位名称"}, "sub_account_type": {"type": "string", "title": "子账户类型，如果开启了多仓位值为\"position\"没配置多仓位为空"}}}, "transferGetTransferByIDResponseSTATUS": {"type": "string", "enum": ["NONE", "INITED", "SUBMITTED", "CONFIRMED", "CANCELLED"], "default": "NONE", "title": "调拨单状态"}, "transferGetTransferCollectDetailedResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/transferTransferCollectDetailed"}}, "total": {"$ref": "#/definitions/transferTransferCollectTotal"}}}, "transferGetTransferCollectResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/transferTransferCollect"}}, "total": {"$ref": "#/definitions/transferTransferCollectTotal"}}}, "transferGetTransferProductByBranchIDResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/transferSelectTransferProduct"}}, "total": {"type": "integer", "format": "int64"}}}, "transferGetTransferProductByTransferIDResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/transferTransferProduct"}}, "total": {"type": "integer", "format": "int64"}}}, "transferGetTransferRegionByBranchIDResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/transferRegionBranch"}}}}, "transferGetTransferRequestSTATUS": {"type": "string", "enum": ["NONE", "INITED", "SUBMITTED", "CONFIRMED", "CANCELLED"], "default": "NONE"}, "transferGetTransferResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/transferTransfer"}}, "total": {"type": "integer", "format": "int64"}}}, "transferP_STATUS": {"type": "string", "enum": ["NONE", "INITED", "PROCESSING", "SUCCESSED", "FAILED"], "default": "NONE"}, "transferPong": {"type": "object", "properties": {"msg": {"type": "string"}}}, "transferPostTransferProduct": {"type": "object", "properties": {"product_id": {"type": "string", "format": "uint64", "title": "商品ID"}, "unit_id": {"type": "string", "format": "uint64", "title": "调拨单位ID"}, "quantity": {"type": "number", "format": "double", "title": "调拨数量"}}}, "transferRegionBranch": {"type": "object", "properties": {"address": {"type": "string", "title": "门店地址"}, "id": {"type": "string", "format": "uint64", "title": "门店ID"}, "name": {"type": "string", "title": "门店名字"}, "code": {"type": "string"}}}, "transferSelectTransferProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "商品ID"}, "product_code": {"type": "string", "title": "商品编码"}, "product_category_id": {"type": "string", "format": "uint64", "title": "单位分类"}, "product_name": {"type": "string", "title": "商品名字"}, "unit": {"type": "array", "items": {"$ref": "#/definitions/transferTransferProductUnit"}}, "barcode": {"type": "array", "items": {"type": "string"}}}}, "transferSubmitTransferRequest": {"type": "object", "properties": {"transfer_id": {"type": "string", "format": "uint64", "title": "调拨单ID"}, "receiver": {"type": "string", "format": "uint64"}, "receiving_store": {"type": "string", "format": "uint64"}, "products": {"type": "array", "items": {"$ref": "#/definitions/transferPostTransferProduct"}}, "branch_type": {"type": "string", "title": "录入方式，区分门店还是仓库STORE/WAREHOUSE"}}}, "transferTransfer": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "调拨单ID"}, "master_id": {"type": "string", "format": "uint64"}, "partner_id": {"type": "string", "format": "uint64"}, "transfer_order_number": {"type": "string", "format": "uint64", "title": "调拨单号"}, "shipping_store": {"type": "string", "format": "uint64", "title": "调拨门店ID"}, "shipping_store_name": {"type": "string", "title": "调拨门店名称"}, "receiving_store_name": {"type": "string", "title": "收货门店名称"}, "receiving_store": {"type": "string", "format": "uint64", "title": "收货门店ID"}, "code": {"type": "string", "title": "调拨单编码"}, "description": {"type": "string", "title": "描述"}, "reason_type": {"type": "string", "title": "调拨原因类型"}, "receiver": {"type": "string", "format": "uint64", "title": "接收人"}, "remark": {"type": "string", "title": "备注"}, "shipper": {"type": "string", "format": "uint64", "title": "调拨者"}, "extends": {"type": "string"}, "type": {"type": "string", "title": "调拨类型(自动AUTO/手动MANUAL)"}, "status": {"$ref": "#/definitions/transferTransferSTATUS"}, "process_status": {"$ref": "#/definitions/transferP_STATUS", "title": "操作状态"}, "sub_type": {"type": "string", "title": "区分内部`INTERNAL`/外部`EXTERNAL`调拨"}, "created_by": {"type": "string", "format": "uint64", "title": "创建者"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新者"}, "transfer_date": {"type": "string", "format": "date-time", "title": "调拨单日期"}, "receiving_date": {"type": "string", "format": "date-time", "title": "调拨单收货日"}, "shipping_date": {"type": "string", "format": "date-time", "title": "调拨时间"}, "created_at": {"type": "string", "format": "date-time", "title": "调拨单创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "request_id": {"type": "string", "format": "uint64"}, "user_id": {"type": "string", "format": "uint64"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "jde_order_id": {"type": "string"}, "auto_confirm": {"type": "boolean", "format": "boolean"}, "branch_type": {"type": "string", "title": "录入方式，区分门店还是仓库STORE/WAREHOUSE"}, "attachments": {"type": "string"}, "receiving_position": {"type": "string", "format": "uint64", "title": "接收仓位id"}, "receiving_position_name": {"type": "string", "title": "接收仓位名称"}, "shipping_position": {"type": "string", "format": "uint64", "title": "调出仓位id"}, "shipping_position_name": {"type": "string", "title": "调出仓位名称"}, "sub_account_type": {"type": "string", "title": "子账户类型，如果开启了多仓位值为\"position\"没配置多仓位为空"}}}, "transferTransferCollect": {"type": "object", "properties": {"accounting_quantity": {"type": "number", "format": "double"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "category_code": {"type": "string"}, "category_id": {"type": "string", "format": "uint64"}, "category_name": {"type": "string"}, "category_parent": {"$ref": "#/definitions/protobufStruct", "title": "商品标签层级"}, "product_code": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "product_price": {"type": "number", "format": "double"}, "receiving_store": {"type": "string", "format": "uint64"}, "receiving_store_code": {"type": "string"}, "receiving_store_us_id": {"type": "string"}, "receiving_store_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "shipping_store": {"type": "string", "format": "uint64"}, "shipping_store_code": {"type": "string"}, "shipping_store_name": {"type": "string"}, "shipping_store_us_id": {"type": "string"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "product_spec": {"type": "string"}, "price": {"type": "number", "format": "double"}, "receiving_position": {"type": "string", "format": "uint64"}, "receiving_position_code": {"type": "string"}, "receiving_position_name": {"type": "string"}, "shipping_position": {"type": "string", "format": "uint64"}, "shipping_position_code": {"type": "string"}, "shipping_position_name": {"type": "string"}}}, "transferTransferCollectDetailed": {"type": "object", "properties": {"accounting_quantity": {"type": "number", "format": "double"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "category_code": {"type": "string"}, "category_id": {"type": "string", "format": "uint64"}, "category_name": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "product_code": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "product_spec": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "receiving_store": {"type": "string", "format": "uint64"}, "receiving_store_code": {"type": "string"}, "receiving_store_name": {"type": "string"}, "shipping_store": {"type": "string", "format": "uint64"}, "shipping_store_code": {"type": "string"}, "shipping_store_name": {"type": "string"}, "status": {"type": "string"}, "transfer_code": {"type": "string"}, "transfer_date": {"type": "string", "format": "date-time"}, "transfer_id": {"type": "string", "format": "uint64"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "shipping_store_us_id": {"type": "string"}, "receiving_store_us_id": {"type": "string"}, "price": {"type": "number", "format": "double"}, "jde_code": {"type": "string"}, "code": {"type": "string"}, "receiving_position": {"type": "string", "format": "uint64"}, "receiving_position_code": {"type": "string"}, "receiving_position_name": {"type": "string"}, "shipping_position": {"type": "string", "format": "uint64"}, "shipping_position_code": {"type": "string"}, "shipping_position_name": {"type": "string"}}}, "transferTransferCollectTotal": {"type": "object", "properties": {"count": {"type": "string", "format": "uint64"}, "sum_accounting_quantity": {"type": "number", "format": "double"}, "sum_quantity": {"type": "number", "format": "double"}}}, "transferTransferProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "调拨商品条目ID"}, "product_id": {"type": "string", "format": "uint64", "title": "商品ID"}, "transfer_id": {"type": "string", "format": "uint64", "title": "调拨单ID"}, "unit_id": {"type": "string", "format": "uint64", "title": "调拨单位ID"}, "unit_name": {"type": "string", "title": "调拨单位名字"}, "unit_spec": {"type": "string", "title": "调拨单位分类"}, "accounting_quantity": {"type": "number", "format": "double", "title": "核算数量"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "核算单位ID"}, "accounting_unit_name": {"type": "string", "title": "核算单位名字"}, "accounting_unit_spec": {"type": "string", "title": "核算单位分类"}, "accounting_received_quantity": {"type": "number", "format": "double", "title": "确认数量"}, "description": {"type": "string", "title": "描述"}, "is_confirmed": {"type": "boolean", "format": "boolean", "title": "是否确认"}, "item_number": {"type": "integer", "format": "int64", "title": "条目数量"}, "material_number": {"type": "string", "title": "物料编码"}, "product_code": {"type": "string", "title": "商品编码"}, "product_name": {"type": "string", "title": "商品名字"}, "quantity": {"type": "number", "format": "double", "title": "数量"}, "receiving_store": {"type": "string", "format": "uint64", "title": "接收门店"}, "shipping_store": {"type": "string", "format": "uint64", "title": "调出门店"}, "created_by": {"type": "string", "format": "uint64", "title": "创建者"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新者"}, "transfer_date": {"type": "string", "format": "date-time", "title": "调拨单日期"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "extends": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "user_id": {"type": "string", "format": "uint64"}, "confirmed_received_quantity": {"type": "number", "format": "double", "title": "确认收货数量"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}}}, "transferTransferProductUnit": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "调拨单位ID"}, "quantity": {"type": "number", "format": "double", "title": "调拨数量"}, "name": {"type": "string"}}}, "transferTransferSTATUS": {"type": "string", "enum": ["NONE", "INITED", "SUBMITTED", "CONFIRMED", "CANCELLED"], "default": "NONE", "title": "调拨单状态"}, "transferUpdateTransferRequest": {"type": "object", "properties": {"transfer_id": {"type": "string", "format": "uint64"}, "products": {"type": "array", "items": {"$ref": "#/definitions/transferPostTransferProduct"}}, "remark": {"type": "string", "title": "备注"}, "transfer_date": {"type": "string", "format": "date-time", "title": "调拨单日期"}, "branch_type": {"type": "string", "title": "录入方式，区分门店还是仓库STORE/WAREHOUSE"}, "attachments": {"type": "string"}, "type": {"type": "string", "title": "调拨类型(自动AUTO/手动MANUAL)"}, "sub_type": {"type": "string", "title": "区分内部`INTERNAL`/外部`EXTERNAL`调拨"}}}, "transferUpdateTransferResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}}}