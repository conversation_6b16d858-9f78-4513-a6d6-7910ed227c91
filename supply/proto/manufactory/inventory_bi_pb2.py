# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: manufactory/inventory_bi.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='manufactory/inventory_bi.proto',
  package='manufactory',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1emanufactory/inventory_bi.proto\x12\x0bmanufactory\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x8a\x02\n\x18RealtimeInventoryRequest\x12\x12\n\nbranch_ids\x18\x01 \x03(\x04\x12\x13\n\x0bgeo_regions\x18\x02 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x03 \x03(\t\x12\x13\n\x0bproduct_ids\x18\x04 \x03(\x04\x12\r\n\x05limit\x18\x05 \x01(\x05\x12\x0e\n\x06offset\x18\x06 \x01(\x05\x12\r\n\x05order\x18\x07 \x01(\t\x12\x0c\n\x04sort\x18\x08 \x01(\t\x12\x0f\n\x07\x65xclude\x18\t \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\n \x01(\t\x12\x0b\n\x03lan\x18\x0b \x01(\t\x12\x14\n\x0cposition_ids\x18\x0c \x03(\x04\x12\x15\n\rreturn_fields\x18\r \x01(\t\"P\n\x19RealtimeInventoryResponse\x12$\n\x04rows\x18\x01 \x03(\x0b\x32\x16.manufactory.Inventory\x12\r\n\x05total\x18\x02 \x01(\x05\"\xf6\x02\n\x15\x44\x61ilyInventoryRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x02 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x03 \x03(\x04\x12.\n\nstart_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x06 \x01(\x05\x12\x0e\n\x06offset\x18\x07 \x01(\x05\x12\x0c\n\x04\x63ode\x18\t \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\n \x01(\t\x12\x0e\n\x06if_pre\x18\x0b \x01(\t\x12\x0e\n\x06if_end\x18\x0c \x01(\t\x12\x15\n\rexclude_empty\x18\r \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x0e \x01(\t\x12\x0b\n\x03lan\x18\x0f \x01(\t\x12\x14\n\x0cposition_ids\x18\x10 \x03(\x03\x12\x15\n\rreturn_fields\x18\x11 \x01(\t\"R\n\x16\x44\x61ilyInventoryResponse\x12)\n\x04rows\x18\x01 \x03(\x0b\x32\x1b.manufactory.DailyInventory\x12\r\n\x05total\x18\x02 \x01(\x05\"\xe1\x0b\n\x0e\x44\x61ilyInventory\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nproduct_id\x18\x05 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x08 \x01(\x04\x12\x15\n\rcategory_code\x18\t \x01(\t\x12\x15\n\rcategory_name\x18\n \x01(\t\x12\x0c\n\x04spec\x18\x0b \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x0c \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\r \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x0e \x01(\t\x12\x0f\n\x07pre_qty\x18\x0f \x01(\x01\x12\x0b\n\x03qty\x18\x10 \x01(\x01\x12\x15\n\rtrans_deposit\x18\x11 \x01(\x01\x12\x16\n\x0etrans_withdraw\x18\x12 \x01(\x01\x12\x14\n\x0cstoc_deposit\x18\x13 \x01(\x01\x12\x15\n\rstoc_withdraw\x18\x14 \x01(\x01\x12\x13\n\x0b\x61\x64j_deposit\x18\x15 \x01(\x01\x12\x14\n\x0c\x61\x64j_withdraw\x18\x16 \x01(\x01\x12\x13\n\x0brec_deposit\x18\x17 \x01(\x01\x12\x18\n\x10rec_diff_deposit\x18\x18 \x01(\x01\x12\x19\n\x11rec_diff_withdraw\x18\x19 \x01(\x01\x12\x14\n\x0cret_withdraw\x18\x1a \x01(\x01\x12\x12\n\nstart_time\x18\x1b \x01(\t\x12\x10\n\x08\x65nd_time\x18\x1c \x01(\t\x12\x15\n\rsales_deposit\x18\x1d \x01(\x01\x12\x16\n\x0esales_withdraw\x18\x1e \x01(\x01\x12\x14\n\x0cllyl_deposit\x18\x1f \x01(\x01\x12\x15\n\rllyl_withdraw\x18  \x01(\x01\x12\x15\n\rcpcrk_deposit\x18! \x01(\x01\x12\x16\n\x0e\x63pcrk_withdraw\x18\" \x01(\x01\x12\x14\n\x0crec_withdraw\x18# \x01(\x01\x12\x13\n\x0bret_deposit\x18$ \x01(\x01\x12\x12\n\nfreeze_qty\x18% \x01(\x01\x12\x18\n\x10purchase_deposit\x18& \x01(\x01\x12\x10\n\x08low_cost\x18\' \x01(\x01\x12\x18\n\x10inventory_adjust\x18( \x01(\x01\x12\x16\n\x0einventory_init\x18) \x01(\x01\x12\x13\n\x0bsub_account\x18* \x01(\t\x12-\n\x08\x63hildren\x18+ \x03(\x0b\x32\x1b.manufactory.DailyInventory\x12\x13\n\x0bposition_id\x18, \x01(\x04\x12\x15\n\rposition_name\x18- \x01(\t\x12\x15\n\rposition_code\x18. \x01(\t\x12\x15\n\rspick_deposit\x18/ \x01(\x01\x12\x1e\n\x16material_trans_deposit\x18\x30 \x01(\x01\x12\x1f\n\x17material_trans_withdraw\x18\x31 \x01(\x01\x12\x1a\n\x12processing_deposit\x18\x32 \x01(\x01\x12\x1b\n\x13processing_withdraw\x18\x33 \x01(\x01\x12\x17\n\x0fpacking_deposit\x18\x34 \x01(\x01\x12\x18\n\x10packing_withdraw\x18\x35 \x01(\x01\x12\x14\n\x0cret_transfer\x18\x36 \x01(\x01\x12\x16\n\x0etrans_transfer\x18\x37 \x01(\x01\x12\x16\n\x0etrans_delivery\x18\x38 \x01(\x01\x12\x16\n\x0etrans_purchase\x18\x39 \x01(\x01\x12\x1c\n\x14trans_return_release\x18: \x01(\x01\x12\x1e\n\x16trans_transfer_release\x18; \x01(\x01\x12\x1e\n\x16trans_delivery_release\x18< \x01(\x01\x12\x1e\n\x16trans_purchase_release\x18= \x01(\x01\x12\x13\n\x0btrans_begin\x18> \x01(\x01\x12\x11\n\ttrans_end\x18? \x01(\x01\"\xd7\x02\n\x18QueryInventoryLogRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x13\n\x0bproduct_ids\x18\x02 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x03 \x03(\x04\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x05 \x01(\t\x12\r\n\x05limit\x18\x06 \x01(\x03\x12\x0e\n\x06offset\x18\x07 \x01(\x03\x12.\n\nstart_date\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\norder_type\x18\n \x01(\t\x12\x14\n\x0c\x61\x63\x63ount_type\x18\x0b \x01(\t\x12\x0b\n\x03lan\x18\x0c \x01(\t\x12\x14\n\x0cposition_ids\x18\r \x03(\x04\x12\x15\n\rreturn_fields\x18\x0e \x01(\t\"g\n\x19QueryInventoryLogResponse\x12\'\n\x04rows\x18\x01 \x03(\x0b\x32\x19.manufactory.InventoryLog\x12\r\n\x05total\x18\x02 \x01(\x05\x12\x12\n\namount_sum\x18\x03 \x01(\x01\"\xd5\x05\n\x0cInventoryLog\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nproduct_id\x18\x05 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x0c\n\x04spec\x18\x08 \x01(\t\x12\x12\n\norder_code\x18\t \x01(\t\x12\x12\n\norder_type\x18\n \x01(\t\x12.\n\norder_time\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06\x61\x63tion\x18\x0c \x01(\t\x12\x0e\n\x06status\x18\r \x01(\t\x12\x0b\n\x03qty\x18\x0e \x01(\x01\x12\x10\n\x08stock_id\x18\x0f \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x10 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\x11 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x12 \x01(\t\x12\x16\n\x0e\x64\x65mand_unit_id\x18\x13 \x01(\x04\x12\x18\n\x10\x64\x65mand_unit_code\x18\x14 \x01(\t\x12\x18\n\x10\x64\x65mand_unit_name\x18\x15 \x01(\t\x12\x12\n\ndemand_qty\x18\x16 \x01(\x01\x12\x13\n\x0b\x63\x61tegory_id\x18\x17 \x01(\x04\x12\x15\n\rcategory_code\x18\x18 \x01(\t\x12\x15\n\rcategory_name\x18\x19 \x01(\t\x12\x14\n\x0cjde_order_id\x18\x1a \x01(\t\x12\x16\n\x0ejde_order_type\x18\x1b \x01(\t\x12\x0f\n\x07jde_mcu\x18\x1c \x01(\t\x12\x14\n\x0c\x61\x63\x63ount_type\x18\x1d \x01(\t\x12\x16\n\x0esub_account_id\x18\x1e \x01(\x04\x12\x18\n\x10sub_account_code\x18\x1f \x01(\t\x12\x18\n\x10sub_account_name\x18  \x01(\t\"\xd6\x06\n\tInventory\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nproduct_id\x18\x05 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x08 \x01(\x04\x12\x15\n\rcategory_code\x18\t \x01(\t\x12\x15\n\rcategory_name\x18\n \x01(\t\x12\x0c\n\x04spec\x18\x0b \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x0c \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\r \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x0e \x01(\t\x12\x0b\n\x03qty\x18\x0f \x01(\x01\x12\x16\n\x0e\x64\x65mand_unit_id\x18\x10 \x01(\x04\x12\x18\n\x10\x64\x65mand_unit_code\x18\x11 \x01(\t\x12\x18\n\x10\x64\x65mand_unit_name\x18\x12 \x01(\t\x12\x12\n\ndemand_qty\x18\x13 \x01(\x01\x12\x16\n\x0eproduct_status\x18\x14 \x01(\t\x12\x12\n\nfreeze_qty\x18\x15 \x01(\x01\x12\x12\n\nbroker_qty\x18\x16 \x01(\x01\x12.\n\x0c\x65xtra_detail\x18\x17 \x03(\x0b\x32\x18.manufactory.ExtraDetail\x12\x18\n\x10purchase_unit_id\x18\x18 \x01(\x04\x12\x1a\n\x12purchase_unit_code\x18\x19 \x01(\t\x12\x1a\n\x12purchase_unit_name\x18\x1a \x01(\t\x12\x14\n\x0cpurchase_qty\x18\x1b \x01(\x01\x12(\n\x08\x63hildren\x18\x1c \x03(\x0b\x32\x16.manufactory.Inventory\x12\x13\n\x0bposition_id\x18\x1d \x01(\x04\x12\x15\n\rposition_name\x18\x1e \x01(\t\x12\x15\n\rposition_code\x18\x1f \x01(\t\x12\x12\n\nprimary_id\x18\" \x01(\x04\x12\x11\n\ttax_price\x18# \x01(\x01\x12\x12\n\ncost_price\x18$ \x01(\x01\x12\x12\n\nsku_amount\x18% \x01(\x01\x12\x19\n\x11\x64\x65mand_broker_qty\x18& \x01(\x01\"\xd4\x01\n\x0b\x45xtraDetail\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x0b\n\x03qty\x18\x02 \x01(\x01\x12\x10\n\x08sku_type\x18\x03 \x01(\t\x12\x16\n\x0esub_account_id\x18\x04 \x01(\x04\x12\x12\n\nproduct_id\x18\x05 \x01(\t\x12\x16\n\x0equantity_avail\x18\x06 \x01(\x01\x12\x17\n\x0fquantity_freeze\x18\x07 \x01(\x01\x12\x17\n\x0fquantity_broker\x18\x08 \x01(\x01\x12\x0e\n\x06\x61mount\x18\t \x01(\x01\x12\x12\n\ndemand_qty\x18\n \x01(\x01\"K\n\x07\x41\x63\x63ount\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x19\n\x11\x64istribution_type\x18\x03 \x01(\t\"\x8f\x01\n\x08\x41\x63\x63ounts\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x0b\n\x03qty\x18\x03 \x01(\x01\x12\r\n\x05round\x18\x04 \x01(\x01\x12+\n\x0csub_accounts\x18\x05 \x03(\x0b\x32\x15.manufactory.Accounts\x12\x13\n\x0b\x62ranch_name\x18\x06 \x01(\t\"`\n\"RealtimeInventoryByAccountsRequest\x12&\n\x08\x61\x63\x63ounts\x18\x01 \x03(\x0b\x32\x14.manufactory.Account\x12\x12\n\ncheck_type\x18\x02 \x01(\t\"Y\n#RealtimeInventoryByAccountsResponse\x12\r\n\x05total\x18\x01 \x01(\x05\x12#\n\x04rows\x18\x02 \x03(\x0b\x32\x15.manufactory.Accounts\"\xe1\x02\n\x17SummaryInventoryRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x02 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x03 \x03(\x04\x12.\n\nstart_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x06 \x01(\x05\x12\x0e\n\x06offset\x18\x07 \x01(\x05\x12\x0c\n\x04\x63ode\x18\t \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\n \x01(\t\x12\x0e\n\x06if_pre\x18\x0b \x01(\t\x12\x0e\n\x06if_end\x18\x0c \x01(\t\x12\x15\n\rexclude_empty\x18\r \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x0e \x01(\t\x12\x0b\n\x03lan\x18\x0f \x01(\t\x12\x14\n\x0cposition_ids\x18\x10 \x03(\x03\"V\n\x18SummaryInventoryResponse\x12+\n\x04rows\x18\x01 \x03(\x0b\x32\x1d.manufactory.SummaryInventory\x12\r\n\x05total\x18\x02 \x01(\x05\"\xaf\r\n\x10SummaryInventory\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nproduct_id\x18\x05 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x16\n\x0eproduct_status\x18\x08 \x01(\t\x12\x0c\n\x04spec\x18\x0b \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x0c \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\r \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x0e \x01(\t\x12\x0f\n\x07pre_qty\x18\x0f \x01(\x01\x12\x0b\n\x03qty\x18\x10 \x01(\x01\x12\x15\n\rtrans_deposit\x18\x11 \x01(\x01\x12\x16\n\x0etrans_withdraw\x18\x12 \x01(\x01\x12\x14\n\x0cstoc_deposit\x18\x13 \x01(\x01\x12\x15\n\rstoc_withdraw\x18\x14 \x01(\x01\x12\x13\n\x0b\x61\x64j_deposit\x18\x15 \x01(\x01\x12\x14\n\x0c\x61\x64j_withdraw\x18\x16 \x01(\x01\x12\x13\n\x0brec_deposit\x18\x17 \x01(\x01\x12\x18\n\x10rec_diff_deposit\x18\x18 \x01(\x01\x12\x19\n\x11rec_diff_withdraw\x18\x19 \x01(\x01\x12\x14\n\x0cret_withdraw\x18\x1a \x01(\x01\x12.\n\nstart_time\x18\x1b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rsales_deposit\x18\x1d \x01(\x01\x12\x16\n\x0esales_withdraw\x18\x1e \x01(\x01\x12\x14\n\x0cllyl_deposit\x18\x1f \x01(\x01\x12\x15\n\rllyl_withdraw\x18  \x01(\x01\x12\x15\n\rcpcrk_deposit\x18! \x01(\x01\x12\x16\n\x0e\x63pcrk_withdraw\x18\" \x01(\x01\x12\x14\n\x0crec_withdraw\x18# \x01(\x01\x12\x13\n\x0bret_deposit\x18$ \x01(\x01\x12\x12\n\nfreeze_qty\x18% \x01(\x01\x12\x18\n\x10purchase_deposit\x18& \x01(\x01\x12\x10\n\x08low_cost\x18\' \x01(\x01\x12\x18\n\x10inventory_adjust\x18( \x01(\x01\x12\x16\n\x0einventory_init\x18) \x01(\x01\x12\x13\n\x0bsub_account\x18* \x01(\t\x12-\n\x08\x63hildren\x18+ \x03(\x0b\x32\x1b.manufactory.DailyInventory\x12\x13\n\x0bposition_id\x18, \x01(\x04\x12\x15\n\rposition_name\x18- \x01(\t\x12\x15\n\rposition_code\x18. \x01(\t\x12\x15\n\rspick_deposit\x18/ \x01(\x01\x12\x1e\n\x16material_trans_deposit\x18\x30 \x01(\x01\x12\x1f\n\x17material_trans_withdraw\x18\x31 \x01(\x01\x12\x1a\n\x12processing_deposit\x18\x32 \x01(\x01\x12\x1b\n\x13processing_withdraw\x18\x33 \x01(\x01\x12\x17\n\x0fpacking_deposit\x18\x34 \x01(\x01\x12\x18\n\x10packing_withdraw\x18\x35 \x01(\x01\x12\x14\n\x0cret_transfer\x18\x36 \x01(\x01\x12\x16\n\x0etrans_transfer\x18\x37 \x01(\x01\x12\x16\n\x0etrans_delivery\x18\x38 \x01(\x01\x12\x16\n\x0etrans_purchase\x18\x39 \x01(\x01\x12\x1c\n\x14trans_return_release\x18: \x01(\x01\x12\x1e\n\x16trans_transfer_release\x18; \x01(\x01\x12\x1e\n\x16trans_delivery_release\x18< \x01(\x01\x12\x1e\n\x16trans_purchase_release\x18= \x01(\x01\x12\x13\n\x0btrans_begin\x18> \x01(\x01\x12\x11\n\ttrans_end\x18? \x01(\x01\x12\x11\n\tcategory1\x18@ \x01(\t\x12\x11\n\tcategory2\x18\x41 \x01(\t\x12\x11\n\tcategory3\x18\x42 \x01(\t\x12\x11\n\tcategory4\x18\x43 \x01(\t\x12\x11\n\tcategory5\x18\x44 \x01(\t\x12\x11\n\tcategory6\x18\x45 \x01(\t\x12\x11\n\tcategory7\x18\x46 \x01(\t\x12\x11\n\tcategory8\x18G \x01(\t\x12\x11\n\tcategory9\x18H \x01(\t\x12\x12\n\ncategory10\x18I \x01(\t2\xc6\x06\n\x1dManufactoryInventoryBiService\x12\x99\x01\n\x11RealtimeInventory\x12%.manufactory.RealtimeInventoryRequest\x1a&.manufactory.RealtimeInventoryResponse\"5\x82\xd3\xe4\x93\x02/\x12-/api/v2/supply/manufactory/inventory/realtime\x12\x8d\x01\n\x0e\x44\x61ilyInventory\x12\".manufactory.DailyInventoryRequest\x1a#.manufactory.DailyInventoryResponse\"2\x82\xd3\xe4\x93\x02,\x12*/api/v2/supply/manufactory/inventory/daily\x12\x94\x01\n\x11QueryInventoryLog\x12%.manufactory.QueryInventoryLogRequest\x1a&.manufactory.QueryInventoryLogResponse\"0\x82\xd3\xe4\x93\x02*\x12(/api/v2/supply/manufactory/inventory/log\x12\xc6\x01\n\x1bRealtimeInventoryByAccounts\x12/.manufactory.RealtimeInventoryByAccountsRequest\x1a\x30.manufactory.RealtimeInventoryByAccountsResponse\"D\x82\xd3\xe4\x93\x02>\"9/api/v2/supply/manufactory/inventory/realtime/by/accounts:\x01*\x12\x98\x01\n\x10SummaryInventory\x12$.manufactory.SummaryInventoryRequest\x1a%.manufactory.SummaryInventoryResponse\"7\x82\xd3\xe4\x93\x02\x31\",/api/v2/supply/manufactory/inventory/summary:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_REALTIMEINVENTORYREQUEST = _descriptor.Descriptor(
  name='RealtimeInventoryRequest',
  full_name='manufactory.RealtimeInventoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='manufactory.RealtimeInventoryRequest.branch_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='geo_regions', full_name='manufactory.RealtimeInventoryRequest.geo_regions', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='manufactory.RealtimeInventoryRequest.category_ids', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='manufactory.RealtimeInventoryRequest.product_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.RealtimeInventoryRequest.limit', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.RealtimeInventoryRequest.offset', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='manufactory.RealtimeInventoryRequest.order', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='manufactory.RealtimeInventoryRequest.sort', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exclude', full_name='manufactory.RealtimeInventoryRequest.exclude', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.RealtimeInventoryRequest.branch_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.RealtimeInventoryRequest.lan', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='manufactory.RealtimeInventoryRequest.position_ids', index=11,
      number=12, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='manufactory.RealtimeInventoryRequest.return_fields', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=111,
  serialized_end=377,
)


_REALTIMEINVENTORYRESPONSE = _descriptor.Descriptor(
  name='RealtimeInventoryResponse',
  full_name='manufactory.RealtimeInventoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.RealtimeInventoryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.RealtimeInventoryResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=379,
  serialized_end=459,
)


_DAILYINVENTORYREQUEST = _descriptor.Descriptor(
  name='DailyInventoryRequest',
  full_name='manufactory.DailyInventoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='manufactory.DailyInventoryRequest.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='manufactory.DailyInventoryRequest.category_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='manufactory.DailyInventoryRequest.product_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='manufactory.DailyInventoryRequest.start_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='manufactory.DailyInventoryRequest.end_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.DailyInventoryRequest.limit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.DailyInventoryRequest.offset', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.DailyInventoryRequest.code', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='manufactory.DailyInventoryRequest.action', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='if_pre', full_name='manufactory.DailyInventoryRequest.if_pre', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='if_end', full_name='manufactory.DailyInventoryRequest.if_end', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exclude_empty', full_name='manufactory.DailyInventoryRequest.exclude_empty', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.DailyInventoryRequest.branch_type', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.DailyInventoryRequest.lan', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='manufactory.DailyInventoryRequest.position_ids', index=14,
      number=16, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='manufactory.DailyInventoryRequest.return_fields', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=462,
  serialized_end=836,
)


_DAILYINVENTORYRESPONSE = _descriptor.Descriptor(
  name='DailyInventoryResponse',
  full_name='manufactory.DailyInventoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.DailyInventoryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.DailyInventoryResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=838,
  serialized_end=920,
)


_DAILYINVENTORY = _descriptor.Descriptor(
  name='DailyInventory',
  full_name='manufactory.DailyInventory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.DailyInventory.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='manufactory.DailyInventory.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='manufactory.DailyInventory.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='manufactory.DailyInventory.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.DailyInventory.product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.DailyInventory.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.DailyInventory.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='manufactory.DailyInventory.category_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='manufactory.DailyInventory.category_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='manufactory.DailyInventory.category_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='manufactory.DailyInventory.spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='manufactory.DailyInventory.accounting_unit_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='manufactory.DailyInventory.accounting_unit_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.DailyInventory.accounting_unit_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_qty', full_name='manufactory.DailyInventory.pre_qty', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='manufactory.DailyInventory.qty', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_deposit', full_name='manufactory.DailyInventory.trans_deposit', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_withdraw', full_name='manufactory.DailyInventory.trans_withdraw', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stoc_deposit', full_name='manufactory.DailyInventory.stoc_deposit', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stoc_withdraw', full_name='manufactory.DailyInventory.stoc_withdraw', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adj_deposit', full_name='manufactory.DailyInventory.adj_deposit', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adj_withdraw', full_name='manufactory.DailyInventory.adj_withdraw', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_deposit', full_name='manufactory.DailyInventory.rec_deposit', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_diff_deposit', full_name='manufactory.DailyInventory.rec_diff_deposit', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_diff_withdraw', full_name='manufactory.DailyInventory.rec_diff_withdraw', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ret_withdraw', full_name='manufactory.DailyInventory.ret_withdraw', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_time', full_name='manufactory.DailyInventory.start_time', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='manufactory.DailyInventory.end_time', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_deposit', full_name='manufactory.DailyInventory.sales_deposit', index=28,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_withdraw', full_name='manufactory.DailyInventory.sales_withdraw', index=29,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='llyl_deposit', full_name='manufactory.DailyInventory.llyl_deposit', index=30,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='llyl_withdraw', full_name='manufactory.DailyInventory.llyl_withdraw', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cpcrk_deposit', full_name='manufactory.DailyInventory.cpcrk_deposit', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cpcrk_withdraw', full_name='manufactory.DailyInventory.cpcrk_withdraw', index=33,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_withdraw', full_name='manufactory.DailyInventory.rec_withdraw', index=34,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ret_deposit', full_name='manufactory.DailyInventory.ret_deposit', index=35,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='freeze_qty', full_name='manufactory.DailyInventory.freeze_qty', index=36,
      number=37, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_deposit', full_name='manufactory.DailyInventory.purchase_deposit', index=37,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='low_cost', full_name='manufactory.DailyInventory.low_cost', index=38,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_adjust', full_name='manufactory.DailyInventory.inventory_adjust', index=39,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_init', full_name='manufactory.DailyInventory.inventory_init', index=40,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account', full_name='manufactory.DailyInventory.sub_account', index=41,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='children', full_name='manufactory.DailyInventory.children', index=42,
      number=43, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.DailyInventory.position_id', index=43,
      number=44, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.DailyInventory.position_name', index=44,
      number=45, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.DailyInventory.position_code', index=45,
      number=46, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spick_deposit', full_name='manufactory.DailyInventory.spick_deposit', index=46,
      number=47, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_trans_deposit', full_name='manufactory.DailyInventory.material_trans_deposit', index=47,
      number=48, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_trans_withdraw', full_name='manufactory.DailyInventory.material_trans_withdraw', index=48,
      number=49, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_deposit', full_name='manufactory.DailyInventory.processing_deposit', index=49,
      number=50, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_withdraw', full_name='manufactory.DailyInventory.processing_withdraw', index=50,
      number=51, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_deposit', full_name='manufactory.DailyInventory.packing_deposit', index=51,
      number=52, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_withdraw', full_name='manufactory.DailyInventory.packing_withdraw', index=52,
      number=53, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ret_transfer', full_name='manufactory.DailyInventory.ret_transfer', index=53,
      number=54, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_transfer', full_name='manufactory.DailyInventory.trans_transfer', index=54,
      number=55, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_delivery', full_name='manufactory.DailyInventory.trans_delivery', index=55,
      number=56, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_purchase', full_name='manufactory.DailyInventory.trans_purchase', index=56,
      number=57, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_return_release', full_name='manufactory.DailyInventory.trans_return_release', index=57,
      number=58, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_transfer_release', full_name='manufactory.DailyInventory.trans_transfer_release', index=58,
      number=59, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_delivery_release', full_name='manufactory.DailyInventory.trans_delivery_release', index=59,
      number=60, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_purchase_release', full_name='manufactory.DailyInventory.trans_purchase_release', index=60,
      number=61, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_begin', full_name='manufactory.DailyInventory.trans_begin', index=61,
      number=62, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_end', full_name='manufactory.DailyInventory.trans_end', index=62,
      number=63, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=923,
  serialized_end=2428,
)


_QUERYINVENTORYLOGREQUEST = _descriptor.Descriptor(
  name='QueryInventoryLogRequest',
  full_name='manufactory.QueryInventoryLogRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='manufactory.QueryInventoryLogRequest.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='manufactory.QueryInventoryLogRequest.product_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='manufactory.QueryInventoryLogRequest.category_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.QueryInventoryLogRequest.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='manufactory.QueryInventoryLogRequest.action', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.QueryInventoryLogRequest.limit', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.QueryInventoryLogRequest.offset', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='manufactory.QueryInventoryLogRequest.start_date', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='manufactory.QueryInventoryLogRequest.end_date', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='manufactory.QueryInventoryLogRequest.order_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account_type', full_name='manufactory.QueryInventoryLogRequest.account_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.QueryInventoryLogRequest.lan', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='manufactory.QueryInventoryLogRequest.position_ids', index=12,
      number=13, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='manufactory.QueryInventoryLogRequest.return_fields', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2431,
  serialized_end=2774,
)


_QUERYINVENTORYLOGRESPONSE = _descriptor.Descriptor(
  name='QueryInventoryLogResponse',
  full_name='manufactory.QueryInventoryLogResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.QueryInventoryLogResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.QueryInventoryLogResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount_sum', full_name='manufactory.QueryInventoryLogResponse.amount_sum', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2776,
  serialized_end=2879,
)


_INVENTORYLOG = _descriptor.Descriptor(
  name='InventoryLog',
  full_name='manufactory.InventoryLog',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.InventoryLog.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='manufactory.InventoryLog.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='manufactory.InventoryLog.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='manufactory.InventoryLog.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.InventoryLog.product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.InventoryLog.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.InventoryLog.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='manufactory.InventoryLog.spec', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='manufactory.InventoryLog.order_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='manufactory.InventoryLog.order_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_time', full_name='manufactory.InventoryLog.order_time', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='manufactory.InventoryLog.action', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.InventoryLog.status', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='manufactory.InventoryLog.qty', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stock_id', full_name='manufactory.InventoryLog.stock_id', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='manufactory.InventoryLog.accounting_unit_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='manufactory.InventoryLog.accounting_unit_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.InventoryLog.accounting_unit_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_id', full_name='manufactory.InventoryLog.demand_unit_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_code', full_name='manufactory.InventoryLog.demand_unit_code', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_name', full_name='manufactory.InventoryLog.demand_unit_name', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_qty', full_name='manufactory.InventoryLog.demand_qty', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='manufactory.InventoryLog.category_id', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='manufactory.InventoryLog.category_code', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='manufactory.InventoryLog.category_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_order_id', full_name='manufactory.InventoryLog.jde_order_id', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_order_type', full_name='manufactory.InventoryLog.jde_order_type', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_mcu', full_name='manufactory.InventoryLog.jde_mcu', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account_type', full_name='manufactory.InventoryLog.account_type', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_id', full_name='manufactory.InventoryLog.sub_account_id', index=29,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_code', full_name='manufactory.InventoryLog.sub_account_code', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_name', full_name='manufactory.InventoryLog.sub_account_name', index=31,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2882,
  serialized_end=3607,
)


_INVENTORY = _descriptor.Descriptor(
  name='Inventory',
  full_name='manufactory.Inventory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.Inventory.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='manufactory.Inventory.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='manufactory.Inventory.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='manufactory.Inventory.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.Inventory.product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.Inventory.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.Inventory.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='manufactory.Inventory.category_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='manufactory.Inventory.category_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='manufactory.Inventory.category_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='manufactory.Inventory.spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='manufactory.Inventory.accounting_unit_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='manufactory.Inventory.accounting_unit_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.Inventory.accounting_unit_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='manufactory.Inventory.qty', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_id', full_name='manufactory.Inventory.demand_unit_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_code', full_name='manufactory.Inventory.demand_unit_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_name', full_name='manufactory.Inventory.demand_unit_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_qty', full_name='manufactory.Inventory.demand_qty', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_status', full_name='manufactory.Inventory.product_status', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='freeze_qty', full_name='manufactory.Inventory.freeze_qty', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='broker_qty', full_name='manufactory.Inventory.broker_qty', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extra_detail', full_name='manufactory.Inventory.extra_detail', index=22,
      number=23, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_unit_id', full_name='manufactory.Inventory.purchase_unit_id', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_unit_code', full_name='manufactory.Inventory.purchase_unit_code', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_unit_name', full_name='manufactory.Inventory.purchase_unit_name', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_qty', full_name='manufactory.Inventory.purchase_qty', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='children', full_name='manufactory.Inventory.children', index=27,
      number=28, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.Inventory.position_id', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.Inventory.position_name', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.Inventory.position_code', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='primary_id', full_name='manufactory.Inventory.primary_id', index=31,
      number=34, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='manufactory.Inventory.tax_price', index=32,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='manufactory.Inventory.cost_price', index=33,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sku_amount', full_name='manufactory.Inventory.sku_amount', index=34,
      number=37, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_broker_qty', full_name='manufactory.Inventory.demand_broker_qty', index=35,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3610,
  serialized_end=4464,
)


_EXTRADETAIL = _descriptor.Descriptor(
  name='ExtraDetail',
  full_name='manufactory.ExtraDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.ExtraDetail.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='manufactory.ExtraDetail.qty', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sku_type', full_name='manufactory.ExtraDetail.sku_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_id', full_name='manufactory.ExtraDetail.sub_account_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.ExtraDetail.product_id', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity_avail', full_name='manufactory.ExtraDetail.quantity_avail', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity_freeze', full_name='manufactory.ExtraDetail.quantity_freeze', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity_broker', full_name='manufactory.ExtraDetail.quantity_broker', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='manufactory.ExtraDetail.amount', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_qty', full_name='manufactory.ExtraDetail.demand_qty', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4467,
  serialized_end=4679,
)


_ACCOUNT = _descriptor.Descriptor(
  name='Account',
  full_name='manufactory.Account',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='manufactory.Account.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.Account.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='manufactory.Account.distribution_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4681,
  serialized_end=4756,
)


_ACCOUNTS = _descriptor.Descriptor(
  name='Accounts',
  full_name='manufactory.Accounts',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='manufactory.Accounts.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.Accounts.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='manufactory.Accounts.qty', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='round', full_name='manufactory.Accounts.round', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_accounts', full_name='manufactory.Accounts.sub_accounts', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='manufactory.Accounts.branch_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4759,
  serialized_end=4902,
)


_REALTIMEINVENTORYBYACCOUNTSREQUEST = _descriptor.Descriptor(
  name='RealtimeInventoryByAccountsRequest',
  full_name='manufactory.RealtimeInventoryByAccountsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounts', full_name='manufactory.RealtimeInventoryByAccountsRequest.accounts', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='check_type', full_name='manufactory.RealtimeInventoryByAccountsRequest.check_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4904,
  serialized_end=5000,
)


_REALTIMEINVENTORYBYACCOUNTSRESPONSE = _descriptor.Descriptor(
  name='RealtimeInventoryByAccountsResponse',
  full_name='manufactory.RealtimeInventoryByAccountsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.RealtimeInventoryByAccountsResponse.total', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.RealtimeInventoryByAccountsResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5002,
  serialized_end=5091,
)


_SUMMARYINVENTORYREQUEST = _descriptor.Descriptor(
  name='SummaryInventoryRequest',
  full_name='manufactory.SummaryInventoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='manufactory.SummaryInventoryRequest.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='manufactory.SummaryInventoryRequest.category_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='manufactory.SummaryInventoryRequest.product_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='manufactory.SummaryInventoryRequest.start_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='manufactory.SummaryInventoryRequest.end_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.SummaryInventoryRequest.limit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.SummaryInventoryRequest.offset', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.SummaryInventoryRequest.code', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='manufactory.SummaryInventoryRequest.action', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='if_pre', full_name='manufactory.SummaryInventoryRequest.if_pre', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='if_end', full_name='manufactory.SummaryInventoryRequest.if_end', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exclude_empty', full_name='manufactory.SummaryInventoryRequest.exclude_empty', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.SummaryInventoryRequest.branch_type', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.SummaryInventoryRequest.lan', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='manufactory.SummaryInventoryRequest.position_ids', index=14,
      number=16, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5094,
  serialized_end=5447,
)


_SUMMARYINVENTORYRESPONSE = _descriptor.Descriptor(
  name='SummaryInventoryResponse',
  full_name='manufactory.SummaryInventoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.SummaryInventoryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.SummaryInventoryResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5449,
  serialized_end=5535,
)


_SUMMARYINVENTORY = _descriptor.Descriptor(
  name='SummaryInventory',
  full_name='manufactory.SummaryInventory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.SummaryInventory.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='manufactory.SummaryInventory.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='manufactory.SummaryInventory.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='manufactory.SummaryInventory.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.SummaryInventory.product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.SummaryInventory.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.SummaryInventory.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_status', full_name='manufactory.SummaryInventory.product_status', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='manufactory.SummaryInventory.spec', index=8,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='manufactory.SummaryInventory.accounting_unit_id', index=9,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='manufactory.SummaryInventory.accounting_unit_code', index=10,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.SummaryInventory.accounting_unit_name', index=11,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_qty', full_name='manufactory.SummaryInventory.pre_qty', index=12,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='manufactory.SummaryInventory.qty', index=13,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_deposit', full_name='manufactory.SummaryInventory.trans_deposit', index=14,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_withdraw', full_name='manufactory.SummaryInventory.trans_withdraw', index=15,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stoc_deposit', full_name='manufactory.SummaryInventory.stoc_deposit', index=16,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stoc_withdraw', full_name='manufactory.SummaryInventory.stoc_withdraw', index=17,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adj_deposit', full_name='manufactory.SummaryInventory.adj_deposit', index=18,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adj_withdraw', full_name='manufactory.SummaryInventory.adj_withdraw', index=19,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_deposit', full_name='manufactory.SummaryInventory.rec_deposit', index=20,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_diff_deposit', full_name='manufactory.SummaryInventory.rec_diff_deposit', index=21,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_diff_withdraw', full_name='manufactory.SummaryInventory.rec_diff_withdraw', index=22,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ret_withdraw', full_name='manufactory.SummaryInventory.ret_withdraw', index=23,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_time', full_name='manufactory.SummaryInventory.start_time', index=24,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='manufactory.SummaryInventory.end_time', index=25,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_deposit', full_name='manufactory.SummaryInventory.sales_deposit', index=26,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_withdraw', full_name='manufactory.SummaryInventory.sales_withdraw', index=27,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='llyl_deposit', full_name='manufactory.SummaryInventory.llyl_deposit', index=28,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='llyl_withdraw', full_name='manufactory.SummaryInventory.llyl_withdraw', index=29,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cpcrk_deposit', full_name='manufactory.SummaryInventory.cpcrk_deposit', index=30,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cpcrk_withdraw', full_name='manufactory.SummaryInventory.cpcrk_withdraw', index=31,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_withdraw', full_name='manufactory.SummaryInventory.rec_withdraw', index=32,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ret_deposit', full_name='manufactory.SummaryInventory.ret_deposit', index=33,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='freeze_qty', full_name='manufactory.SummaryInventory.freeze_qty', index=34,
      number=37, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_deposit', full_name='manufactory.SummaryInventory.purchase_deposit', index=35,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='low_cost', full_name='manufactory.SummaryInventory.low_cost', index=36,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_adjust', full_name='manufactory.SummaryInventory.inventory_adjust', index=37,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_init', full_name='manufactory.SummaryInventory.inventory_init', index=38,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account', full_name='manufactory.SummaryInventory.sub_account', index=39,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='children', full_name='manufactory.SummaryInventory.children', index=40,
      number=43, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.SummaryInventory.position_id', index=41,
      number=44, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.SummaryInventory.position_name', index=42,
      number=45, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.SummaryInventory.position_code', index=43,
      number=46, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spick_deposit', full_name='manufactory.SummaryInventory.spick_deposit', index=44,
      number=47, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_trans_deposit', full_name='manufactory.SummaryInventory.material_trans_deposit', index=45,
      number=48, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_trans_withdraw', full_name='manufactory.SummaryInventory.material_trans_withdraw', index=46,
      number=49, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_deposit', full_name='manufactory.SummaryInventory.processing_deposit', index=47,
      number=50, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_withdraw', full_name='manufactory.SummaryInventory.processing_withdraw', index=48,
      number=51, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_deposit', full_name='manufactory.SummaryInventory.packing_deposit', index=49,
      number=52, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_withdraw', full_name='manufactory.SummaryInventory.packing_withdraw', index=50,
      number=53, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ret_transfer', full_name='manufactory.SummaryInventory.ret_transfer', index=51,
      number=54, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_transfer', full_name='manufactory.SummaryInventory.trans_transfer', index=52,
      number=55, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_delivery', full_name='manufactory.SummaryInventory.trans_delivery', index=53,
      number=56, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_purchase', full_name='manufactory.SummaryInventory.trans_purchase', index=54,
      number=57, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_return_release', full_name='manufactory.SummaryInventory.trans_return_release', index=55,
      number=58, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_transfer_release', full_name='manufactory.SummaryInventory.trans_transfer_release', index=56,
      number=59, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_delivery_release', full_name='manufactory.SummaryInventory.trans_delivery_release', index=57,
      number=60, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_purchase_release', full_name='manufactory.SummaryInventory.trans_purchase_release', index=58,
      number=61, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_begin', full_name='manufactory.SummaryInventory.trans_begin', index=59,
      number=62, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_end', full_name='manufactory.SummaryInventory.trans_end', index=60,
      number=63, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category1', full_name='manufactory.SummaryInventory.category1', index=61,
      number=64, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category2', full_name='manufactory.SummaryInventory.category2', index=62,
      number=65, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category3', full_name='manufactory.SummaryInventory.category3', index=63,
      number=66, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category4', full_name='manufactory.SummaryInventory.category4', index=64,
      number=67, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category5', full_name='manufactory.SummaryInventory.category5', index=65,
      number=68, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category6', full_name='manufactory.SummaryInventory.category6', index=66,
      number=69, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category7', full_name='manufactory.SummaryInventory.category7', index=67,
      number=70, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category8', full_name='manufactory.SummaryInventory.category8', index=68,
      number=71, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category9', full_name='manufactory.SummaryInventory.category9', index=69,
      number=72, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category10', full_name='manufactory.SummaryInventory.category10', index=70,
      number=73, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5538,
  serialized_end=7249,
)

_REALTIMEINVENTORYRESPONSE.fields_by_name['rows'].message_type = _INVENTORY
_DAILYINVENTORYREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DAILYINVENTORYREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DAILYINVENTORYRESPONSE.fields_by_name['rows'].message_type = _DAILYINVENTORY
_DAILYINVENTORY.fields_by_name['children'].message_type = _DAILYINVENTORY
_QUERYINVENTORYLOGREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYINVENTORYLOGREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYINVENTORYLOGRESPONSE.fields_by_name['rows'].message_type = _INVENTORYLOG
_INVENTORYLOG.fields_by_name['order_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_INVENTORY.fields_by_name['extra_detail'].message_type = _EXTRADETAIL
_INVENTORY.fields_by_name['children'].message_type = _INVENTORY
_ACCOUNTS.fields_by_name['sub_accounts'].message_type = _ACCOUNTS
_REALTIMEINVENTORYBYACCOUNTSREQUEST.fields_by_name['accounts'].message_type = _ACCOUNT
_REALTIMEINVENTORYBYACCOUNTSRESPONSE.fields_by_name['rows'].message_type = _ACCOUNTS
_SUMMARYINVENTORYREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SUMMARYINVENTORYREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SUMMARYINVENTORYRESPONSE.fields_by_name['rows'].message_type = _SUMMARYINVENTORY
_SUMMARYINVENTORY.fields_by_name['start_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SUMMARYINVENTORY.fields_by_name['end_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SUMMARYINVENTORY.fields_by_name['children'].message_type = _DAILYINVENTORY
DESCRIPTOR.message_types_by_name['RealtimeInventoryRequest'] = _REALTIMEINVENTORYREQUEST
DESCRIPTOR.message_types_by_name['RealtimeInventoryResponse'] = _REALTIMEINVENTORYRESPONSE
DESCRIPTOR.message_types_by_name['DailyInventoryRequest'] = _DAILYINVENTORYREQUEST
DESCRIPTOR.message_types_by_name['DailyInventoryResponse'] = _DAILYINVENTORYRESPONSE
DESCRIPTOR.message_types_by_name['DailyInventory'] = _DAILYINVENTORY
DESCRIPTOR.message_types_by_name['QueryInventoryLogRequest'] = _QUERYINVENTORYLOGREQUEST
DESCRIPTOR.message_types_by_name['QueryInventoryLogResponse'] = _QUERYINVENTORYLOGRESPONSE
DESCRIPTOR.message_types_by_name['InventoryLog'] = _INVENTORYLOG
DESCRIPTOR.message_types_by_name['Inventory'] = _INVENTORY
DESCRIPTOR.message_types_by_name['ExtraDetail'] = _EXTRADETAIL
DESCRIPTOR.message_types_by_name['Account'] = _ACCOUNT
DESCRIPTOR.message_types_by_name['Accounts'] = _ACCOUNTS
DESCRIPTOR.message_types_by_name['RealtimeInventoryByAccountsRequest'] = _REALTIMEINVENTORYBYACCOUNTSREQUEST
DESCRIPTOR.message_types_by_name['RealtimeInventoryByAccountsResponse'] = _REALTIMEINVENTORYBYACCOUNTSRESPONSE
DESCRIPTOR.message_types_by_name['SummaryInventoryRequest'] = _SUMMARYINVENTORYREQUEST
DESCRIPTOR.message_types_by_name['SummaryInventoryResponse'] = _SUMMARYINVENTORYRESPONSE
DESCRIPTOR.message_types_by_name['SummaryInventory'] = _SUMMARYINVENTORY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

RealtimeInventoryRequest = _reflection.GeneratedProtocolMessageType('RealtimeInventoryRequest', (_message.Message,), dict(
  DESCRIPTOR = _REALTIMEINVENTORYREQUEST,
  __module__ = 'manufactory.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.RealtimeInventoryRequest)
  ))
_sym_db.RegisterMessage(RealtimeInventoryRequest)

RealtimeInventoryResponse = _reflection.GeneratedProtocolMessageType('RealtimeInventoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _REALTIMEINVENTORYRESPONSE,
  __module__ = 'manufactory.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.RealtimeInventoryResponse)
  ))
_sym_db.RegisterMessage(RealtimeInventoryResponse)

DailyInventoryRequest = _reflection.GeneratedProtocolMessageType('DailyInventoryRequest', (_message.Message,), dict(
  DESCRIPTOR = _DAILYINVENTORYREQUEST,
  __module__ = 'manufactory.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.DailyInventoryRequest)
  ))
_sym_db.RegisterMessage(DailyInventoryRequest)

DailyInventoryResponse = _reflection.GeneratedProtocolMessageType('DailyInventoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _DAILYINVENTORYRESPONSE,
  __module__ = 'manufactory.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.DailyInventoryResponse)
  ))
_sym_db.RegisterMessage(DailyInventoryResponse)

DailyInventory = _reflection.GeneratedProtocolMessageType('DailyInventory', (_message.Message,), dict(
  DESCRIPTOR = _DAILYINVENTORY,
  __module__ = 'manufactory.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.DailyInventory)
  ))
_sym_db.RegisterMessage(DailyInventory)

QueryInventoryLogRequest = _reflection.GeneratedProtocolMessageType('QueryInventoryLogRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYINVENTORYLOGREQUEST,
  __module__ = 'manufactory.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.QueryInventoryLogRequest)
  ))
_sym_db.RegisterMessage(QueryInventoryLogRequest)

QueryInventoryLogResponse = _reflection.GeneratedProtocolMessageType('QueryInventoryLogResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYINVENTORYLOGRESPONSE,
  __module__ = 'manufactory.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.QueryInventoryLogResponse)
  ))
_sym_db.RegisterMessage(QueryInventoryLogResponse)

InventoryLog = _reflection.GeneratedProtocolMessageType('InventoryLog', (_message.Message,), dict(
  DESCRIPTOR = _INVENTORYLOG,
  __module__ = 'manufactory.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.InventoryLog)
  ))
_sym_db.RegisterMessage(InventoryLog)

Inventory = _reflection.GeneratedProtocolMessageType('Inventory', (_message.Message,), dict(
  DESCRIPTOR = _INVENTORY,
  __module__ = 'manufactory.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.Inventory)
  ))
_sym_db.RegisterMessage(Inventory)

ExtraDetail = _reflection.GeneratedProtocolMessageType('ExtraDetail', (_message.Message,), dict(
  DESCRIPTOR = _EXTRADETAIL,
  __module__ = 'manufactory.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ExtraDetail)
  ))
_sym_db.RegisterMessage(ExtraDetail)

Account = _reflection.GeneratedProtocolMessageType('Account', (_message.Message,), dict(
  DESCRIPTOR = _ACCOUNT,
  __module__ = 'manufactory.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.Account)
  ))
_sym_db.RegisterMessage(Account)

Accounts = _reflection.GeneratedProtocolMessageType('Accounts', (_message.Message,), dict(
  DESCRIPTOR = _ACCOUNTS,
  __module__ = 'manufactory.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.Accounts)
  ))
_sym_db.RegisterMessage(Accounts)

RealtimeInventoryByAccountsRequest = _reflection.GeneratedProtocolMessageType('RealtimeInventoryByAccountsRequest', (_message.Message,), dict(
  DESCRIPTOR = _REALTIMEINVENTORYBYACCOUNTSREQUEST,
  __module__ = 'manufactory.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.RealtimeInventoryByAccountsRequest)
  ))
_sym_db.RegisterMessage(RealtimeInventoryByAccountsRequest)

RealtimeInventoryByAccountsResponse = _reflection.GeneratedProtocolMessageType('RealtimeInventoryByAccountsResponse', (_message.Message,), dict(
  DESCRIPTOR = _REALTIMEINVENTORYBYACCOUNTSRESPONSE,
  __module__ = 'manufactory.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.RealtimeInventoryByAccountsResponse)
  ))
_sym_db.RegisterMessage(RealtimeInventoryByAccountsResponse)

SummaryInventoryRequest = _reflection.GeneratedProtocolMessageType('SummaryInventoryRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUMMARYINVENTORYREQUEST,
  __module__ = 'manufactory.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.SummaryInventoryRequest)
  ))
_sym_db.RegisterMessage(SummaryInventoryRequest)

SummaryInventoryResponse = _reflection.GeneratedProtocolMessageType('SummaryInventoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _SUMMARYINVENTORYRESPONSE,
  __module__ = 'manufactory.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.SummaryInventoryResponse)
  ))
_sym_db.RegisterMessage(SummaryInventoryResponse)

SummaryInventory = _reflection.GeneratedProtocolMessageType('SummaryInventory', (_message.Message,), dict(
  DESCRIPTOR = _SUMMARYINVENTORY,
  __module__ = 'manufactory.inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.SummaryInventory)
  ))
_sym_db.RegisterMessage(SummaryInventory)



_MANUFACTORYINVENTORYBISERVICE = _descriptor.ServiceDescriptor(
  name='ManufactoryInventoryBiService',
  full_name='manufactory.ManufactoryInventoryBiService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=7252,
  serialized_end=8090,
  methods=[
  _descriptor.MethodDescriptor(
    name='RealtimeInventory',
    full_name='manufactory.ManufactoryInventoryBiService.RealtimeInventory',
    index=0,
    containing_service=None,
    input_type=_REALTIMEINVENTORYREQUEST,
    output_type=_REALTIMEINVENTORYRESPONSE,
    serialized_options=_b('\202\323\344\223\002/\022-/api/v2/supply/manufactory/inventory/realtime'),
  ),
  _descriptor.MethodDescriptor(
    name='DailyInventory',
    full_name='manufactory.ManufactoryInventoryBiService.DailyInventory',
    index=1,
    containing_service=None,
    input_type=_DAILYINVENTORYREQUEST,
    output_type=_DAILYINVENTORYRESPONSE,
    serialized_options=_b('\202\323\344\223\002,\022*/api/v2/supply/manufactory/inventory/daily'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryInventoryLog',
    full_name='manufactory.ManufactoryInventoryBiService.QueryInventoryLog',
    index=2,
    containing_service=None,
    input_type=_QUERYINVENTORYLOGREQUEST,
    output_type=_QUERYINVENTORYLOGRESPONSE,
    serialized_options=_b('\202\323\344\223\002*\022(/api/v2/supply/manufactory/inventory/log'),
  ),
  _descriptor.MethodDescriptor(
    name='RealtimeInventoryByAccounts',
    full_name='manufactory.ManufactoryInventoryBiService.RealtimeInventoryByAccounts',
    index=3,
    containing_service=None,
    input_type=_REALTIMEINVENTORYBYACCOUNTSREQUEST,
    output_type=_REALTIMEINVENTORYBYACCOUNTSRESPONSE,
    serialized_options=_b('\202\323\344\223\002>\"9/api/v2/supply/manufactory/inventory/realtime/by/accounts:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='SummaryInventory',
    full_name='manufactory.ManufactoryInventoryBiService.SummaryInventory',
    index=4,
    containing_service=None,
    input_type=_SUMMARYINVENTORYREQUEST,
    output_type=_SUMMARYINVENTORYRESPONSE,
    serialized_options=_b('\202\323\344\223\0021\",/api/v2/supply/manufactory/inventory/summary:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_MANUFACTORYINVENTORYBISERVICE)

DESCRIPTOR.services_by_name['ManufactoryInventoryBiService'] = _MANUFACTORYINVENTORYBISERVICE

# @@protoc_insertion_point(module_scope)
