syntax = "proto3";

package manufactory;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

service PackingReceipts{
    // 创建包装单
    rpc CreatePackingReceipts (CreatePackingReceiptsRequest) returns (CreatePackingReceiptsResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/packing/receipts"
        body: "*"
        };
    }
    // 包装单列表查询
    rpc ListPackingReceipts (ListPackingReceiptsRequest) returns (ListPackingReceiptsResponse){
        option (google.api.http) = {
        get: "/api/v2/supply/packing/receipts/list"
        };
    }
    // 查询包装单详情
    rpc GetPackingReceiptsDetail (GetPackingReceiptsDetailRequest) returns (GetPackingReceiptsDetailResponse){
        option (google.api.http) = {
        get: "/api/v2/supply/packing/receipts/{receipt_id}/detail"
        };
    }
    // 更新包装单
    rpc UpdatePackingReceipts (UpdatePackingReceiptsRequest) returns (UpdatePackingReceiptsResponse){
        option (google.api.http) = {
        put: "/api/v2/supply/packing/receipts/{receipt_id}"
        body: "*"
        };
    }
    // 包装单状态变更
    rpc ChangePackingReceiptsStatus (ChangePackingStatusRequest) returns (ChangePackingStatusResponse){
        option (google.api.http) = {
        put: "/api/v2/supply/packing/receipts/{receipt_id}/{status}"
        body: "*"
        };
    }
}

// 创建包装单请求参数
message CreatePackingReceiptsRequest{
    // 订单状态:
    // INITED = 'INITED'           # 新建
    // SUBMITTED = 'SUBMITTED'     # 已提交
    // APPROVED = 'APPROVED'       # 已审核
    // REJECTED = 'REJECTED'       # 已驳回
    // DELETED = 'DELETED'         # 删除
    string status = 4;
    // 包装类型*预留*
    string type = 5;
    // 包装日期
    google.protobuf.Timestamp packing_date = 6;
    // 加工中心id
    uint64 machining_center_id = 7;
    // 加工中心code
    string machining_center_code = 8;
    //加工中心名称
    string machining_center_name = 9;
    // 仓位id
    uint64 position_id = 10;
    // 仓位名称
    string position_name = 11;
    // 仓位code
    string position_code = 12;
    // 包装规则ID
    uint64 packing_rule = 13;
    // 备注
    string remark = 14;
    // 目标物料ID
    uint64 target_material = 15;
    // 目标物料code
    string target_material_code = 16;
    // 目标物料name
    string target_material_name = 17;
    // 目标物料单位ID
    uint64 target_material_unit_id = 18;
    // 目标物料单位名称(拿不到可不传)
    string target_material_unit_name = 19;
    // 目标物料单位规格(拿不到可不传)
    string target_material_unit_spec = 20;
    // 包装数量
    double packed_quantity = 21;
    // 是否开启多仓位
    bool opened_position = 22;
    // 幂等性校验请求id
    uint64 request_id = 25;
    repeated PackingItems items = 26;
    // 成本中心ID
    uint64 cost_center_id = 30;
}

message PackingItems{
    uint64 id = 1;
    // 关联的包装单id
    uint64 main_id = 2;
    // 物料/商品id
    uint64 product_id = 3;
    // 商品code
    string product_code = 4;
    // 商品名称
    string product_name = 5;
    // 商品类型
    string product_type = 6;
    // 单位id
    uint64 unit_id = 7;
    // 单位名称
    string unit_name = 8;
    // 单位规格
    string unit_spec = 9;
    // 物料数量
    double quantity = 10;
    // 区分物料`material`/耗材`consumable`
    string type = 11;
    // 单位转换率
    double unit_rate = 12;
    uint64 partner_id = 16;
    uint64 created_by = 17;
    string created_name = 18;
    uint64 updated_by = 19;
    string updated_name = 20;
    google.protobuf.Timestamp created_at = 21;
    google.protobuf.Timestamp updated_at = 22;
}

// 创建包装单返回
message CreatePackingReceiptsResponse{
    // 包装单id
    uint64 receipt_id = 1;
    string result = 2;
}

// 包装单列表查询请求参数
message ListPackingReceiptsRequest{
    // 开始日期
    google.protobuf.Timestamp start_date = 1;
    // 结束日期
    google.protobuf.Timestamp end_date = 2;
    // 加工中心ID列表
    repeated uint64 machining_centers = 3;
    // 单号
    string code = 5;
    // 目标物料ID列表
    repeated uint64 target_materials = 6;
    // 仓位ID列表
    repeated uint64 position_ids = 7;
    // 订单状态 'INITED'新建 'SUBMITTED'已提交 'APPROVED'已审核 'REJECTED'已驳回， 多选传数组
    repeated string status = 9;
    int64 limit = 10;
    uint64 offset = 11;
    bool include_total = 12;
    // 排序方式  asc or desc
    string order = 13;
    // 排序字段
    string sort = 14;
}

message PackingRow{
    uint64 id = 1;
    uint64 partner_id = 2;
    uint64 created_by = 3;
    string created_name = 4;
    uint64 updated_by = 5;
    string updated_name = 6;
    google.protobuf.Timestamp created_at = 7;
    google.protobuf.Timestamp updated_at = 8;
    uint64 main_id = 9;
    string status = 10;
    string code = 11;
    // 包装日期
    google.protobuf.Timestamp packing_date = 12;
    // 包装类型*预留*
    string type = 13;
    // 加工中心id
    uint64 machining_center_id = 14;
    // 加工中心code
    string machining_center_code = 15;
    //加工中心名称
    string machining_center_name = 16;
    // 仓位id
    uint64 position_id = 17;
    // 仓位名称
    string position_name = 18;
    // 仓位code
    string position_code = 19;
    // 包装规则ID
    uint64 packing_rule = 20;
    // 目标物料ID
    uint64 target_material = 21;
    // 目标物料code
    string target_material_code = 22;
    // 目标物料name
    string target_material_name = 23;
    // 目标物料单位ID
    uint64 target_material_unit_id = 24;
    // 目标物料单位名称
    string target_material_unit_name = 25;
    // 目标物料单位规格
    string target_material_unit_spec = 26;
    // 目标物料单位转换率
    double target_material_unit_rate = 29;
    // 包装数量
    double packed_quantity = 27;
    // 是否开启多仓位
    bool opened_position = 28;
    // 备注
    string remark = 33;
    // 幂等性校验请求id
    uint64 request_id = 34;
    // 成本中心ID
    uint64 cost_center_id = 35;
}

// 包装单列表查询返回参数
message ListPackingReceiptsResponse{
    repeated PackingRow rows = 1;
    uint64 total = 2;
}

// 查询包装单详情请求参数
message GetPackingReceiptsDetailRequest{
    uint64 receipt_id = 1;
}

message GetPackingReceiptsDetailResponse{
    uint64 id = 1;
    uint64 partner_id = 2;
    uint64 created_by = 3;
    string created_name = 4;
    uint64 updated_by = 5;
    string updated_name = 6;
    google.protobuf.Timestamp created_at = 7;
    google.protobuf.Timestamp updated_at = 8;
    uint64 main_id = 9;
    string status = 10;
    string code = 11;
    // 包装日期
    google.protobuf.Timestamp packing_date = 12;
    // 包装类型*预留*
    string type = 13;
    // 加工中心id
    uint64 machining_center_id = 14;
    // 加工中心code
    string machining_center_code = 15;
    //加工中心名称
    string machining_center_name = 16;
    // 仓位id
    uint64 position_id = 17;
    // 仓位名称
    string position_name = 18;
    // 仓位code
    string position_code = 19;
    // 包装规则ID
    uint64 packing_rule = 20;
    // 目标物料ID
    uint64 target_material = 21;
    // 目标物料code
    string target_material_code = 22;
    // 目标物料name
    string target_material_name = 23;
    // 目标物料单位ID
    uint64 target_material_unit_id = 24;
    // 目标物料单位名称
    string target_material_unit_name = 25;
    // 目标物料单位规格
    string target_material_unit_spec = 26;
    // 包装数量
    double packed_quantity = 27;
    // 是否开启多仓位
    bool opened_position = 28;
    // 目标物料单位转换率
    double target_material_unit_rate = 29;
    // 幂等性校验请求id
    uint64 request_id = 33;
    // 备注
    string remark = 34;
    repeated PackingItems items = 35;
    // 成本中心ID
    uint64 cost_center_id = 36;
}


// 更新包装单请求参数
message UpdatePackingReceiptsRequest{
    // 包装id
    uint64 receipt_id = 1;
    // 包装日期
    google.protobuf.Timestamp packing_date = 6;
    // 仓位id
    uint64 position_id = 10;
    // 仓位名称
    string position_name = 11;
    // 仓位code
    string position_code = 12;
    // 包装规则ID
    uint64 packing_rule = 13;
    // 备注
    string remark = 14;
    // 加工中心id
    uint64 machining_center_id = 15;
    // 加工中心code
    string machining_center_code = 16;
    //加工中心名称
    string machining_center_name = 17;
    // 目标物料ID
    uint64 target_material = 18;
    // 目标物料code
    string target_material_code = 19;
    // 目标物料name
    string target_material_name = 20;
    // 目标物料单位ID
    uint64 target_material_unit_id = 21;
    // 目标物料单位名称(拿不到可不传)
    string target_material_unit_name = 22;
    // 目标物料单位规格(拿不到可不传)
    string target_material_unit_spec = 23;
    // 包装数量
    double packed_quantity = 24;
    // 是否开启多仓位
    bool opened_position = 25;
    // 幂等性校验请求id
    uint64 request_id = 30;
    repeated PackingItems items = 31;
}

message UpdatePackingReceiptsResponse{
    uint64 receipt_id = 1;
    string result = 2;
}

message ChangePackingStatusRequest {
    // 包装id
    uint64 receipt_id = 1;
    // 'SUBMITTED'提交 'APPROVED'审核 'REJECTED'驳回 'DELETED' 删除
    string status = 2;
}

message ChangePackingStatusResponse{
    uint64 receipt_id = 1;
    string result = 2;
}