# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from manufactory import packing_receipts_pb2 as manufactory_dot_packing__receipts__pb2


class PackingReceiptsStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreatePackingReceipts = channel.unary_unary(
        '/manufactory.PackingReceipts/CreatePackingReceipts',
        request_serializer=manufactory_dot_packing__receipts__pb2.CreatePackingReceiptsRequest.SerializeToString,
        response_deserializer=manufactory_dot_packing__receipts__pb2.CreatePackingReceiptsResponse.FromString,
        )
    self.ListPackingReceipts = channel.unary_unary(
        '/manufactory.PackingReceipts/ListPackingReceipts',
        request_serializer=manufactory_dot_packing__receipts__pb2.ListPackingReceiptsRequest.SerializeToString,
        response_deserializer=manufactory_dot_packing__receipts__pb2.ListPackingReceiptsResponse.FromString,
        )
    self.GetPackingReceiptsDetail = channel.unary_unary(
        '/manufactory.PackingReceipts/GetPackingReceiptsDetail',
        request_serializer=manufactory_dot_packing__receipts__pb2.GetPackingReceiptsDetailRequest.SerializeToString,
        response_deserializer=manufactory_dot_packing__receipts__pb2.GetPackingReceiptsDetailResponse.FromString,
        )
    self.UpdatePackingReceipts = channel.unary_unary(
        '/manufactory.PackingReceipts/UpdatePackingReceipts',
        request_serializer=manufactory_dot_packing__receipts__pb2.UpdatePackingReceiptsRequest.SerializeToString,
        response_deserializer=manufactory_dot_packing__receipts__pb2.UpdatePackingReceiptsResponse.FromString,
        )
    self.ChangePackingReceiptsStatus = channel.unary_unary(
        '/manufactory.PackingReceipts/ChangePackingReceiptsStatus',
        request_serializer=manufactory_dot_packing__receipts__pb2.ChangePackingStatusRequest.SerializeToString,
        response_deserializer=manufactory_dot_packing__receipts__pb2.ChangePackingStatusResponse.FromString,
        )


class PackingReceiptsServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def CreatePackingReceipts(self, request, context):
    """创建包装单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListPackingReceipts(self, request, context):
    """包装单列表查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPackingReceiptsDetail(self, request, context):
    """查询包装单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdatePackingReceipts(self, request, context):
    """更新包装单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ChangePackingReceiptsStatus(self, request, context):
    """包装单状态变更
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_PackingReceiptsServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreatePackingReceipts': grpc.unary_unary_rpc_method_handler(
          servicer.CreatePackingReceipts,
          request_deserializer=manufactory_dot_packing__receipts__pb2.CreatePackingReceiptsRequest.FromString,
          response_serializer=manufactory_dot_packing__receipts__pb2.CreatePackingReceiptsResponse.SerializeToString,
      ),
      'ListPackingReceipts': grpc.unary_unary_rpc_method_handler(
          servicer.ListPackingReceipts,
          request_deserializer=manufactory_dot_packing__receipts__pb2.ListPackingReceiptsRequest.FromString,
          response_serializer=manufactory_dot_packing__receipts__pb2.ListPackingReceiptsResponse.SerializeToString,
      ),
      'GetPackingReceiptsDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetPackingReceiptsDetail,
          request_deserializer=manufactory_dot_packing__receipts__pb2.GetPackingReceiptsDetailRequest.FromString,
          response_serializer=manufactory_dot_packing__receipts__pb2.GetPackingReceiptsDetailResponse.SerializeToString,
      ),
      'UpdatePackingReceipts': grpc.unary_unary_rpc_method_handler(
          servicer.UpdatePackingReceipts,
          request_deserializer=manufactory_dot_packing__receipts__pb2.UpdatePackingReceiptsRequest.FromString,
          response_serializer=manufactory_dot_packing__receipts__pb2.UpdatePackingReceiptsResponse.SerializeToString,
      ),
      'ChangePackingReceiptsStatus': grpc.unary_unary_rpc_method_handler(
          servicer.ChangePackingReceiptsStatus,
          request_deserializer=manufactory_dot_packing__receipts__pb2.ChangePackingStatusRequest.FromString,
          response_serializer=manufactory_dot_packing__receipts__pb2.ChangePackingStatusResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'manufactory.PackingReceipts', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
