# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: manufactory/packing_receipts.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='manufactory/packing_receipts.proto',
  package='manufactory',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\"manufactory/packing_receipts.proto\x12\x0bmanufactory\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xf6\x04\n\x1c\x43reatePackingReceiptsRequest\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x0c\n\x04type\x18\x05 \x01(\t\x12\x30\n\x0cpacking_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1b\n\x13machining_center_id\x18\x07 \x01(\x04\x12\x1d\n\x15machining_center_code\x18\x08 \x01(\t\x12\x1d\n\x15machining_center_name\x18\t \x01(\t\x12\x13\n\x0bposition_id\x18\n \x01(\x04\x12\x15\n\rposition_name\x18\x0b \x01(\t\x12\x15\n\rposition_code\x18\x0c \x01(\t\x12\x14\n\x0cpacking_rule\x18\r \x01(\x04\x12\x0e\n\x06remark\x18\x0e \x01(\t\x12\x17\n\x0ftarget_material\x18\x0f \x01(\x04\x12\x1c\n\x14target_material_code\x18\x10 \x01(\t\x12\x1c\n\x14target_material_name\x18\x11 \x01(\t\x12\x1f\n\x17target_material_unit_id\x18\x12 \x01(\x04\x12!\n\x19target_material_unit_name\x18\x13 \x01(\t\x12!\n\x19target_material_unit_spec\x18\x14 \x01(\t\x12\x17\n\x0fpacked_quantity\x18\x15 \x01(\x01\x12\x17\n\x0fopened_position\x18\x16 \x01(\x08\x12\x12\n\nrequest_id\x18\x19 \x01(\x04\x12(\n\x05items\x18\x1a \x03(\x0b\x32\x19.manufactory.PackingItems\x12\x16\n\x0e\x63ost_center_id\x18\x1e \x01(\x04\"\xb3\x03\n\x0cPackingItems\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0f\n\x07main_id\x18\x02 \x01(\x04\x12\x12\n\nproduct_id\x18\x03 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x04 \x01(\t\x12\x14\n\x0cproduct_name\x18\x05 \x01(\t\x12\x14\n\x0cproduct_type\x18\x06 \x01(\t\x12\x0f\n\x07unit_id\x18\x07 \x01(\x04\x12\x11\n\tunit_name\x18\x08 \x01(\t\x12\x11\n\tunit_spec\x18\t \x01(\t\x12\x10\n\x08quantity\x18\n \x01(\x01\x12\x0c\n\x04type\x18\x0b \x01(\t\x12\x11\n\tunit_rate\x18\x0c \x01(\x01\x12\x12\n\npartner_id\x18\x10 \x01(\x04\x12\x12\n\ncreated_by\x18\x11 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x12 \x01(\t\x12\x12\n\nupdated_by\x18\x13 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x14 \x01(\t\x12.\n\ncreated_at\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"C\n\x1d\x43reatePackingReceiptsResponse\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\"\xb6\x02\n\x1aListPackingReceiptsRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x19\n\x11machining_centers\x18\x03 \x03(\x04\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x18\n\x10target_materials\x18\x06 \x03(\x04\x12\x14\n\x0cposition_ids\x18\x07 \x03(\x04\x12\x0e\n\x06status\x18\t \x03(\t\x12\r\n\x05limit\x18\n \x01(\x03\x12\x0e\n\x06offset\x18\x0b \x01(\x04\x12\x15\n\rinclude_total\x18\x0c \x01(\x08\x12\r\n\x05order\x18\r \x01(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\"\xd0\x06\n\nPackingRow\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x12\n\ncreated_by\x18\x03 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x04 \x01(\t\x12\x12\n\nupdated_by\x18\x05 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x06 \x01(\t\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07main_id\x18\t \x01(\x04\x12\x0e\n\x06status\x18\n \x01(\t\x12\x0c\n\x04\x63ode\x18\x0b \x01(\t\x12\x30\n\x0cpacking_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04type\x18\r \x01(\t\x12\x1b\n\x13machining_center_id\x18\x0e \x01(\x04\x12\x1d\n\x15machining_center_code\x18\x0f \x01(\t\x12\x1d\n\x15machining_center_name\x18\x10 \x01(\t\x12\x13\n\x0bposition_id\x18\x11 \x01(\x04\x12\x15\n\rposition_name\x18\x12 \x01(\t\x12\x15\n\rposition_code\x18\x13 \x01(\t\x12\x14\n\x0cpacking_rule\x18\x14 \x01(\x04\x12\x17\n\x0ftarget_material\x18\x15 \x01(\x04\x12\x1c\n\x14target_material_code\x18\x16 \x01(\t\x12\x1c\n\x14target_material_name\x18\x17 \x01(\t\x12\x1f\n\x17target_material_unit_id\x18\x18 \x01(\x04\x12!\n\x19target_material_unit_name\x18\x19 \x01(\t\x12!\n\x19target_material_unit_spec\x18\x1a \x01(\t\x12!\n\x19target_material_unit_rate\x18\x1d \x01(\x01\x12\x17\n\x0fpacked_quantity\x18\x1b \x01(\x01\x12\x17\n\x0fopened_position\x18\x1c \x01(\x08\x12\x0e\n\x06remark\x18! \x01(\t\x12\x12\n\nrequest_id\x18\" \x01(\x04\x12\x16\n\x0e\x63ost_center_id\x18# \x01(\x04\"S\n\x1bListPackingReceiptsResponse\x12%\n\x04rows\x18\x01 \x03(\x0b\x32\x17.manufactory.PackingRow\x12\r\n\x05total\x18\x02 \x01(\x04\"5\n\x1fGetPackingReceiptsDetailRequest\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\"\x90\x07\n GetPackingReceiptsDetailResponse\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x12\n\ncreated_by\x18\x03 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x04 \x01(\t\x12\x12\n\nupdated_by\x18\x05 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x06 \x01(\t\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07main_id\x18\t \x01(\x04\x12\x0e\n\x06status\x18\n \x01(\t\x12\x0c\n\x04\x63ode\x18\x0b \x01(\t\x12\x30\n\x0cpacking_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04type\x18\r \x01(\t\x12\x1b\n\x13machining_center_id\x18\x0e \x01(\x04\x12\x1d\n\x15machining_center_code\x18\x0f \x01(\t\x12\x1d\n\x15machining_center_name\x18\x10 \x01(\t\x12\x13\n\x0bposition_id\x18\x11 \x01(\x04\x12\x15\n\rposition_name\x18\x12 \x01(\t\x12\x15\n\rposition_code\x18\x13 \x01(\t\x12\x14\n\x0cpacking_rule\x18\x14 \x01(\x04\x12\x17\n\x0ftarget_material\x18\x15 \x01(\x04\x12\x1c\n\x14target_material_code\x18\x16 \x01(\t\x12\x1c\n\x14target_material_name\x18\x17 \x01(\t\x12\x1f\n\x17target_material_unit_id\x18\x18 \x01(\x04\x12!\n\x19target_material_unit_name\x18\x19 \x01(\t\x12!\n\x19target_material_unit_spec\x18\x1a \x01(\t\x12\x17\n\x0fpacked_quantity\x18\x1b \x01(\x01\x12\x17\n\x0fopened_position\x18\x1c \x01(\x08\x12!\n\x19target_material_unit_rate\x18\x1d \x01(\x01\x12\x12\n\nrequest_id\x18! \x01(\x04\x12\x0e\n\x06remark\x18\" \x01(\t\x12(\n\x05items\x18# \x03(\x0b\x32\x19.manufactory.PackingItems\x12\x16\n\x0e\x63ost_center_id\x18$ \x01(\x04\"\xd4\x04\n\x1cUpdatePackingReceiptsRequest\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x30\n\x0cpacking_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0bposition_id\x18\n \x01(\x04\x12\x15\n\rposition_name\x18\x0b \x01(\t\x12\x15\n\rposition_code\x18\x0c \x01(\t\x12\x14\n\x0cpacking_rule\x18\r \x01(\x04\x12\x0e\n\x06remark\x18\x0e \x01(\t\x12\x1b\n\x13machining_center_id\x18\x0f \x01(\x04\x12\x1d\n\x15machining_center_code\x18\x10 \x01(\t\x12\x1d\n\x15machining_center_name\x18\x11 \x01(\t\x12\x17\n\x0ftarget_material\x18\x12 \x01(\x04\x12\x1c\n\x14target_material_code\x18\x13 \x01(\t\x12\x1c\n\x14target_material_name\x18\x14 \x01(\t\x12\x1f\n\x17target_material_unit_id\x18\x15 \x01(\x04\x12!\n\x19target_material_unit_name\x18\x16 \x01(\t\x12!\n\x19target_material_unit_spec\x18\x17 \x01(\t\x12\x17\n\x0fpacked_quantity\x18\x18 \x01(\x01\x12\x17\n\x0fopened_position\x18\x19 \x01(\x08\x12\x12\n\nrequest_id\x18\x1e \x01(\x04\x12(\n\x05items\x18\x1f \x03(\x0b\x32\x19.manufactory.PackingItems\"C\n\x1dUpdatePackingReceiptsResponse\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\"@\n\x1a\x43hangePackingStatusRequest\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\"A\n\x1b\x43hangePackingStatusResponse\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t2\xdd\x06\n\x0fPackingReceipts\x12\x9a\x01\n\x15\x43reatePackingReceipts\x12).manufactory.CreatePackingReceiptsRequest\x1a*.manufactory.CreatePackingReceiptsResponse\"*\x82\xd3\xe4\x93\x02$\"\x1f/api/v2/supply/packing/receipts:\x01*\x12\x96\x01\n\x13ListPackingReceipts\x12\'.manufactory.ListPackingReceiptsRequest\x1a(.manufactory.ListPackingReceiptsResponse\",\x82\xd3\xe4\x93\x02&\x12$/api/v2/supply/packing/receipts/list\x12\xb4\x01\n\x18GetPackingReceiptsDetail\x12,.manufactory.GetPackingReceiptsDetailRequest\x1a-.manufactory.GetPackingReceiptsDetailResponse\";\x82\xd3\xe4\x93\x02\x35\x12\x33/api/v2/supply/packing/receipts/{receipt_id}/detail\x12\xa7\x01\n\x15UpdatePackingReceipts\x12).manufactory.UpdatePackingReceiptsRequest\x1a*.manufactory.UpdatePackingReceiptsResponse\"7\x82\xd3\xe4\x93\x02\x31\x1a,/api/v2/supply/packing/receipts/{receipt_id}:\x01*\x12\xb2\x01\n\x1b\x43hangePackingReceiptsStatus\x12\'.manufactory.ChangePackingStatusRequest\x1a(.manufactory.ChangePackingStatusResponse\"@\x82\xd3\xe4\x93\x02:\x1a\x35/api/v2/supply/packing/receipts/{receipt_id}/{status}:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_CREATEPACKINGRECEIPTSREQUEST = _descriptor.Descriptor(
  name='CreatePackingReceiptsRequest',
  full_name='manufactory.CreatePackingReceiptsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.CreatePackingReceiptsRequest.status', index=0,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.CreatePackingReceiptsRequest.type', index=1,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_date', full_name='manufactory.CreatePackingReceiptsRequest.packing_date', index=2,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_id', full_name='manufactory.CreatePackingReceiptsRequest.machining_center_id', index=3,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_code', full_name='manufactory.CreatePackingReceiptsRequest.machining_center_code', index=4,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_name', full_name='manufactory.CreatePackingReceiptsRequest.machining_center_name', index=5,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.CreatePackingReceiptsRequest.position_id', index=6,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.CreatePackingReceiptsRequest.position_name', index=7,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.CreatePackingReceiptsRequest.position_code', index=8,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_rule', full_name='manufactory.CreatePackingReceiptsRequest.packing_rule', index=9,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.CreatePackingReceiptsRequest.remark', index=10,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material', full_name='manufactory.CreatePackingReceiptsRequest.target_material', index=11,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_code', full_name='manufactory.CreatePackingReceiptsRequest.target_material_code', index=12,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_name', full_name='manufactory.CreatePackingReceiptsRequest.target_material_name', index=13,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_id', full_name='manufactory.CreatePackingReceiptsRequest.target_material_unit_id', index=14,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_name', full_name='manufactory.CreatePackingReceiptsRequest.target_material_unit_name', index=15,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_spec', full_name='manufactory.CreatePackingReceiptsRequest.target_material_unit_spec', index=16,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packed_quantity', full_name='manufactory.CreatePackingReceiptsRequest.packed_quantity', index=17,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='opened_position', full_name='manufactory.CreatePackingReceiptsRequest.opened_position', index=18,
      number=22, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.CreatePackingReceiptsRequest.request_id', index=19,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='items', full_name='manufactory.CreatePackingReceiptsRequest.items', index=20,
      number=26, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='manufactory.CreatePackingReceiptsRequest.cost_center_id', index=21,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=115,
  serialized_end=745,
)


_PACKINGITEMS = _descriptor.Descriptor(
  name='PackingItems',
  full_name='manufactory.PackingItems',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.PackingItems.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_id', full_name='manufactory.PackingItems.main_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.PackingItems.product_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.PackingItems.product_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.PackingItems.product_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='manufactory.PackingItems.product_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.PackingItems.unit_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.PackingItems.unit_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='manufactory.PackingItems.unit_spec', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.PackingItems.quantity', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.PackingItems.type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='manufactory.PackingItems.unit_rate', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.PackingItems.partner_id', index=12,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.PackingItems.created_by', index=13,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.PackingItems.created_name', index=14,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.PackingItems.updated_by', index=15,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.PackingItems.updated_name', index=16,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.PackingItems.created_at', index=17,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.PackingItems.updated_at', index=18,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=748,
  serialized_end=1183,
)


_CREATEPACKINGRECEIPTSRESPONSE = _descriptor.Descriptor(
  name='CreatePackingReceiptsResponse',
  full_name='manufactory.CreatePackingReceiptsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='manufactory.CreatePackingReceiptsResponse.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.CreatePackingReceiptsResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1185,
  serialized_end=1252,
)


_LISTPACKINGRECEIPTSREQUEST = _descriptor.Descriptor(
  name='ListPackingReceiptsRequest',
  full_name='manufactory.ListPackingReceiptsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='manufactory.ListPackingReceiptsRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='manufactory.ListPackingReceiptsRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_centers', full_name='manufactory.ListPackingReceiptsRequest.machining_centers', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.ListPackingReceiptsRequest.code', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_materials', full_name='manufactory.ListPackingReceiptsRequest.target_materials', index=4,
      number=6, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='manufactory.ListPackingReceiptsRequest.position_ids', index=5,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.ListPackingReceiptsRequest.status', index=6,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.ListPackingReceiptsRequest.limit', index=7,
      number=10, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.ListPackingReceiptsRequest.offset', index=8,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='manufactory.ListPackingReceiptsRequest.include_total', index=9,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='manufactory.ListPackingReceiptsRequest.order', index=10,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='manufactory.ListPackingReceiptsRequest.sort', index=11,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1255,
  serialized_end=1565,
)


_PACKINGROW = _descriptor.Descriptor(
  name='PackingRow',
  full_name='manufactory.PackingRow',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.PackingRow.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.PackingRow.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.PackingRow.created_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.PackingRow.created_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.PackingRow.updated_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.PackingRow.updated_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.PackingRow.created_at', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.PackingRow.updated_at', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_id', full_name='manufactory.PackingRow.main_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.PackingRow.status', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.PackingRow.code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_date', full_name='manufactory.PackingRow.packing_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.PackingRow.type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_id', full_name='manufactory.PackingRow.machining_center_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_code', full_name='manufactory.PackingRow.machining_center_code', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_name', full_name='manufactory.PackingRow.machining_center_name', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.PackingRow.position_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.PackingRow.position_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.PackingRow.position_code', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_rule', full_name='manufactory.PackingRow.packing_rule', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material', full_name='manufactory.PackingRow.target_material', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_code', full_name='manufactory.PackingRow.target_material_code', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_name', full_name='manufactory.PackingRow.target_material_name', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_id', full_name='manufactory.PackingRow.target_material_unit_id', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_name', full_name='manufactory.PackingRow.target_material_unit_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_spec', full_name='manufactory.PackingRow.target_material_unit_spec', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_rate', full_name='manufactory.PackingRow.target_material_unit_rate', index=26,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packed_quantity', full_name='manufactory.PackingRow.packed_quantity', index=27,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='opened_position', full_name='manufactory.PackingRow.opened_position', index=28,
      number=28, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.PackingRow.remark', index=29,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.PackingRow.request_id', index=30,
      number=34, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='manufactory.PackingRow.cost_center_id', index=31,
      number=35, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1568,
  serialized_end=2416,
)


_LISTPACKINGRECEIPTSRESPONSE = _descriptor.Descriptor(
  name='ListPackingReceiptsResponse',
  full_name='manufactory.ListPackingReceiptsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.ListPackingReceiptsResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.ListPackingReceiptsResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2418,
  serialized_end=2501,
)


_GETPACKINGRECEIPTSDETAILREQUEST = _descriptor.Descriptor(
  name='GetPackingReceiptsDetailRequest',
  full_name='manufactory.GetPackingReceiptsDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='manufactory.GetPackingReceiptsDetailRequest.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2503,
  serialized_end=2556,
)


_GETPACKINGRECEIPTSDETAILRESPONSE = _descriptor.Descriptor(
  name='GetPackingReceiptsDetailResponse',
  full_name='manufactory.GetPackingReceiptsDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.GetPackingReceiptsDetailResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.GetPackingReceiptsDetailResponse.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.GetPackingReceiptsDetailResponse.created_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.GetPackingReceiptsDetailResponse.created_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.GetPackingReceiptsDetailResponse.updated_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.GetPackingReceiptsDetailResponse.updated_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.GetPackingReceiptsDetailResponse.created_at', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.GetPackingReceiptsDetailResponse.updated_at', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_id', full_name='manufactory.GetPackingReceiptsDetailResponse.main_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.GetPackingReceiptsDetailResponse.status', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.GetPackingReceiptsDetailResponse.code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_date', full_name='manufactory.GetPackingReceiptsDetailResponse.packing_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.GetPackingReceiptsDetailResponse.type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_id', full_name='manufactory.GetPackingReceiptsDetailResponse.machining_center_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_code', full_name='manufactory.GetPackingReceiptsDetailResponse.machining_center_code', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_name', full_name='manufactory.GetPackingReceiptsDetailResponse.machining_center_name', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.GetPackingReceiptsDetailResponse.position_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.GetPackingReceiptsDetailResponse.position_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.GetPackingReceiptsDetailResponse.position_code', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_rule', full_name='manufactory.GetPackingReceiptsDetailResponse.packing_rule', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material', full_name='manufactory.GetPackingReceiptsDetailResponse.target_material', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_code', full_name='manufactory.GetPackingReceiptsDetailResponse.target_material_code', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_name', full_name='manufactory.GetPackingReceiptsDetailResponse.target_material_name', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_id', full_name='manufactory.GetPackingReceiptsDetailResponse.target_material_unit_id', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_name', full_name='manufactory.GetPackingReceiptsDetailResponse.target_material_unit_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_spec', full_name='manufactory.GetPackingReceiptsDetailResponse.target_material_unit_spec', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packed_quantity', full_name='manufactory.GetPackingReceiptsDetailResponse.packed_quantity', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='opened_position', full_name='manufactory.GetPackingReceiptsDetailResponse.opened_position', index=27,
      number=28, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_rate', full_name='manufactory.GetPackingReceiptsDetailResponse.target_material_unit_rate', index=28,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.GetPackingReceiptsDetailResponse.request_id', index=29,
      number=33, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.GetPackingReceiptsDetailResponse.remark', index=30,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='items', full_name='manufactory.GetPackingReceiptsDetailResponse.items', index=31,
      number=35, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='manufactory.GetPackingReceiptsDetailResponse.cost_center_id', index=32,
      number=36, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2559,
  serialized_end=3471,
)


_UPDATEPACKINGRECEIPTSREQUEST = _descriptor.Descriptor(
  name='UpdatePackingReceiptsRequest',
  full_name='manufactory.UpdatePackingReceiptsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='manufactory.UpdatePackingReceiptsRequest.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_date', full_name='manufactory.UpdatePackingReceiptsRequest.packing_date', index=1,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.UpdatePackingReceiptsRequest.position_id', index=2,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.UpdatePackingReceiptsRequest.position_name', index=3,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.UpdatePackingReceiptsRequest.position_code', index=4,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_rule', full_name='manufactory.UpdatePackingReceiptsRequest.packing_rule', index=5,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.UpdatePackingReceiptsRequest.remark', index=6,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_id', full_name='manufactory.UpdatePackingReceiptsRequest.machining_center_id', index=7,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_code', full_name='manufactory.UpdatePackingReceiptsRequest.machining_center_code', index=8,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_name', full_name='manufactory.UpdatePackingReceiptsRequest.machining_center_name', index=9,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material', full_name='manufactory.UpdatePackingReceiptsRequest.target_material', index=10,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_code', full_name='manufactory.UpdatePackingReceiptsRequest.target_material_code', index=11,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_name', full_name='manufactory.UpdatePackingReceiptsRequest.target_material_name', index=12,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_id', full_name='manufactory.UpdatePackingReceiptsRequest.target_material_unit_id', index=13,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_name', full_name='manufactory.UpdatePackingReceiptsRequest.target_material_unit_name', index=14,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_spec', full_name='manufactory.UpdatePackingReceiptsRequest.target_material_unit_spec', index=15,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packed_quantity', full_name='manufactory.UpdatePackingReceiptsRequest.packed_quantity', index=16,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='opened_position', full_name='manufactory.UpdatePackingReceiptsRequest.opened_position', index=17,
      number=25, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.UpdatePackingReceiptsRequest.request_id', index=18,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='items', full_name='manufactory.UpdatePackingReceiptsRequest.items', index=19,
      number=31, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3474,
  serialized_end=4070,
)


_UPDATEPACKINGRECEIPTSRESPONSE = _descriptor.Descriptor(
  name='UpdatePackingReceiptsResponse',
  full_name='manufactory.UpdatePackingReceiptsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='manufactory.UpdatePackingReceiptsResponse.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.UpdatePackingReceiptsResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4072,
  serialized_end=4139,
)


_CHANGEPACKINGSTATUSREQUEST = _descriptor.Descriptor(
  name='ChangePackingStatusRequest',
  full_name='manufactory.ChangePackingStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='manufactory.ChangePackingStatusRequest.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.ChangePackingStatusRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4141,
  serialized_end=4205,
)


_CHANGEPACKINGSTATUSRESPONSE = _descriptor.Descriptor(
  name='ChangePackingStatusResponse',
  full_name='manufactory.ChangePackingStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='manufactory.ChangePackingStatusResponse.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.ChangePackingStatusResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4207,
  serialized_end=4272,
)

_CREATEPACKINGRECEIPTSREQUEST.fields_by_name['packing_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEPACKINGRECEIPTSREQUEST.fields_by_name['items'].message_type = _PACKINGITEMS
_PACKINGITEMS.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PACKINGITEMS.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPACKINGRECEIPTSREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPACKINGRECEIPTSREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PACKINGROW.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PACKINGROW.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PACKINGROW.fields_by_name['packing_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPACKINGRECEIPTSRESPONSE.fields_by_name['rows'].message_type = _PACKINGROW
_GETPACKINGRECEIPTSDETAILRESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPACKINGRECEIPTSDETAILRESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPACKINGRECEIPTSDETAILRESPONSE.fields_by_name['packing_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPACKINGRECEIPTSDETAILRESPONSE.fields_by_name['items'].message_type = _PACKINGITEMS
_UPDATEPACKINGRECEIPTSREQUEST.fields_by_name['packing_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATEPACKINGRECEIPTSREQUEST.fields_by_name['items'].message_type = _PACKINGITEMS
DESCRIPTOR.message_types_by_name['CreatePackingReceiptsRequest'] = _CREATEPACKINGRECEIPTSREQUEST
DESCRIPTOR.message_types_by_name['PackingItems'] = _PACKINGITEMS
DESCRIPTOR.message_types_by_name['CreatePackingReceiptsResponse'] = _CREATEPACKINGRECEIPTSRESPONSE
DESCRIPTOR.message_types_by_name['ListPackingReceiptsRequest'] = _LISTPACKINGRECEIPTSREQUEST
DESCRIPTOR.message_types_by_name['PackingRow'] = _PACKINGROW
DESCRIPTOR.message_types_by_name['ListPackingReceiptsResponse'] = _LISTPACKINGRECEIPTSRESPONSE
DESCRIPTOR.message_types_by_name['GetPackingReceiptsDetailRequest'] = _GETPACKINGRECEIPTSDETAILREQUEST
DESCRIPTOR.message_types_by_name['GetPackingReceiptsDetailResponse'] = _GETPACKINGRECEIPTSDETAILRESPONSE
DESCRIPTOR.message_types_by_name['UpdatePackingReceiptsRequest'] = _UPDATEPACKINGRECEIPTSREQUEST
DESCRIPTOR.message_types_by_name['UpdatePackingReceiptsResponse'] = _UPDATEPACKINGRECEIPTSRESPONSE
DESCRIPTOR.message_types_by_name['ChangePackingStatusRequest'] = _CHANGEPACKINGSTATUSREQUEST
DESCRIPTOR.message_types_by_name['ChangePackingStatusResponse'] = _CHANGEPACKINGSTATUSRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CreatePackingReceiptsRequest = _reflection.GeneratedProtocolMessageType('CreatePackingReceiptsRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPACKINGRECEIPTSREQUEST,
  __module__ = 'manufactory.packing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreatePackingReceiptsRequest)
  ))
_sym_db.RegisterMessage(CreatePackingReceiptsRequest)

PackingItems = _reflection.GeneratedProtocolMessageType('PackingItems', (_message.Message,), dict(
  DESCRIPTOR = _PACKINGITEMS,
  __module__ = 'manufactory.packing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.PackingItems)
  ))
_sym_db.RegisterMessage(PackingItems)

CreatePackingReceiptsResponse = _reflection.GeneratedProtocolMessageType('CreatePackingReceiptsResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPACKINGRECEIPTSRESPONSE,
  __module__ = 'manufactory.packing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreatePackingReceiptsResponse)
  ))
_sym_db.RegisterMessage(CreatePackingReceiptsResponse)

ListPackingReceiptsRequest = _reflection.GeneratedProtocolMessageType('ListPackingReceiptsRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTPACKINGRECEIPTSREQUEST,
  __module__ = 'manufactory.packing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ListPackingReceiptsRequest)
  ))
_sym_db.RegisterMessage(ListPackingReceiptsRequest)

PackingRow = _reflection.GeneratedProtocolMessageType('PackingRow', (_message.Message,), dict(
  DESCRIPTOR = _PACKINGROW,
  __module__ = 'manufactory.packing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.PackingRow)
  ))
_sym_db.RegisterMessage(PackingRow)

ListPackingReceiptsResponse = _reflection.GeneratedProtocolMessageType('ListPackingReceiptsResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTPACKINGRECEIPTSRESPONSE,
  __module__ = 'manufactory.packing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ListPackingReceiptsResponse)
  ))
_sym_db.RegisterMessage(ListPackingReceiptsResponse)

GetPackingReceiptsDetailRequest = _reflection.GeneratedProtocolMessageType('GetPackingReceiptsDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPACKINGRECEIPTSDETAILREQUEST,
  __module__ = 'manufactory.packing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetPackingReceiptsDetailRequest)
  ))
_sym_db.RegisterMessage(GetPackingReceiptsDetailRequest)

GetPackingReceiptsDetailResponse = _reflection.GeneratedProtocolMessageType('GetPackingReceiptsDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPACKINGRECEIPTSDETAILRESPONSE,
  __module__ = 'manufactory.packing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetPackingReceiptsDetailResponse)
  ))
_sym_db.RegisterMessage(GetPackingReceiptsDetailResponse)

UpdatePackingReceiptsRequest = _reflection.GeneratedProtocolMessageType('UpdatePackingReceiptsRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPACKINGRECEIPTSREQUEST,
  __module__ = 'manufactory.packing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.UpdatePackingReceiptsRequest)
  ))
_sym_db.RegisterMessage(UpdatePackingReceiptsRequest)

UpdatePackingReceiptsResponse = _reflection.GeneratedProtocolMessageType('UpdatePackingReceiptsResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPACKINGRECEIPTSRESPONSE,
  __module__ = 'manufactory.packing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.UpdatePackingReceiptsResponse)
  ))
_sym_db.RegisterMessage(UpdatePackingReceiptsResponse)

ChangePackingStatusRequest = _reflection.GeneratedProtocolMessageType('ChangePackingStatusRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHANGEPACKINGSTATUSREQUEST,
  __module__ = 'manufactory.packing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ChangePackingStatusRequest)
  ))
_sym_db.RegisterMessage(ChangePackingStatusRequest)

ChangePackingStatusResponse = _reflection.GeneratedProtocolMessageType('ChangePackingStatusResponse', (_message.Message,), dict(
  DESCRIPTOR = _CHANGEPACKINGSTATUSRESPONSE,
  __module__ = 'manufactory.packing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ChangePackingStatusResponse)
  ))
_sym_db.RegisterMessage(ChangePackingStatusResponse)



_PACKINGRECEIPTS = _descriptor.ServiceDescriptor(
  name='PackingReceipts',
  full_name='manufactory.PackingReceipts',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=4275,
  serialized_end=5136,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreatePackingReceipts',
    full_name='manufactory.PackingReceipts.CreatePackingReceipts',
    index=0,
    containing_service=None,
    input_type=_CREATEPACKINGRECEIPTSREQUEST,
    output_type=_CREATEPACKINGRECEIPTSRESPONSE,
    serialized_options=_b('\202\323\344\223\002$\"\037/api/v2/supply/packing/receipts:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListPackingReceipts',
    full_name='manufactory.PackingReceipts.ListPackingReceipts',
    index=1,
    containing_service=None,
    input_type=_LISTPACKINGRECEIPTSREQUEST,
    output_type=_LISTPACKINGRECEIPTSRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\022$/api/v2/supply/packing/receipts/list'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPackingReceiptsDetail',
    full_name='manufactory.PackingReceipts.GetPackingReceiptsDetail',
    index=2,
    containing_service=None,
    input_type=_GETPACKINGRECEIPTSDETAILREQUEST,
    output_type=_GETPACKINGRECEIPTSDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\0223/api/v2/supply/packing/receipts/{receipt_id}/detail'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdatePackingReceipts',
    full_name='manufactory.PackingReceipts.UpdatePackingReceipts',
    index=3,
    containing_service=None,
    input_type=_UPDATEPACKINGRECEIPTSREQUEST,
    output_type=_UPDATEPACKINGRECEIPTSRESPONSE,
    serialized_options=_b('\202\323\344\223\0021\032,/api/v2/supply/packing/receipts/{receipt_id}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ChangePackingReceiptsStatus',
    full_name='manufactory.PackingReceipts.ChangePackingReceiptsStatus',
    index=4,
    containing_service=None,
    input_type=_CHANGEPACKINGSTATUSREQUEST,
    output_type=_CHANGEPACKINGSTATUSRESPONSE,
    serialized_options=_b('\202\323\344\223\002:\0325/api/v2/supply/packing/receipts/{receipt_id}/{status}:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_PACKINGRECEIPTS)

DESCRIPTOR.services_by_name['PackingReceipts'] = _PACKINGRECEIPTS

# @@protoc_insertion_point(module_scope)
