# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: manufactory/processing_cost.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='manufactory/processing_cost.proto',
  package='manufactory',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n!manufactory/processing_cost.proto\x12\x0bmanufactory\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xac\x03\n\x1b\x43reateProcessingCostRequest\x12\x11\n\tperiod_id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x0c\n\x04type\x18\x05 \x01(\t\x12\r\n\x05month\x18\x06 \x01(\t\x12\x1b\n\x13machining_center_id\x18\x07 \x01(\x04\x12\x1d\n\x15machining_center_code\x18\x08 \x01(\t\x12\x1d\n\x15machining_center_name\x18\t \x01(\t\x12\x13\n\x0bposition_id\x18\n \x01(\x04\x12\x15\n\rposition_name\x18\x0b \x01(\t\x12\x15\n\rposition_code\x18\x0c \x01(\t\x12\x11\n\tunit_cost\x18\r \x01(\x01\x12\x10\n\x08quantity\x18\x0e \x01(\x01\x12\x0f\n\x07unit_id\x18\x0f \x01(\x04\x12\x11\n\tunit_name\x18\x10 \x01(\t\x12\x11\n\tunit_spec\x18\x11 \x01(\t\x12\x17\n\x0fopened_position\x18\x12 \x01(\x08\x12\x0e\n\x06remark\x18\x14 \x01(\t\x12\x12\n\nrequest_id\x18\x19 \x01(\x04\x12\x16\n\x0e\x63ost_center_id\x18\x1a \x01(\x04\"B\n\x1c\x43reateProcessingCostResponse\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\"\xf9\x01\n\x19ListProcessingCostRequest\x12\x13\n\x0bstart_month\x18\x01 \x01(\t\x12\x11\n\tend_month\x18\x02 \x01(\t\x12\x19\n\x11machining_centers\x18\x03 \x03(\x04\x12\x12\n\nperiod_ids\x18\x04 \x03(\x04\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x14\n\x0cposition_ids\x18\x07 \x03(\x04\x12\x0e\n\x06status\x18\t \x03(\t\x12\r\n\x05limit\x18\n \x01(\x03\x12\x0e\n\x06offset\x18\x0b \x01(\x04\x12\x15\n\rinclude_total\x18\x0c \x01(\x08\x12\r\n\x05order\x18\r \x01(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\"\xaa\x05\n\nCostDetail\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x12\n\ncreated_by\x18\x03 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x04 \x01(\t\x12\x12\n\nupdated_by\x18\x05 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x06 \x01(\t\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\t \x01(\t\x12\x0c\n\x04\x63ode\x18\x0b \x01(\t\x12\r\n\x05month\x18\x0c \x01(\t\x12\x0c\n\x04type\x18\r \x01(\t\x12\x1b\n\x13machining_center_id\x18\x0e \x01(\x04\x12\x1d\n\x15machining_center_code\x18\x0f \x01(\t\x12\x1d\n\x15machining_center_name\x18\x10 \x01(\t\x12\x13\n\x0bposition_id\x18\x11 \x01(\x04\x12\x15\n\rposition_name\x18\x12 \x01(\t\x12\x15\n\rposition_code\x18\x13 \x01(\t\x12\x11\n\tunit_cost\x18\x14 \x01(\x01\x12\x10\n\x08quantity\x18\x15 \x01(\x01\x12\x0f\n\x07unit_id\x18\x16 \x01(\x04\x12\x11\n\tunit_name\x18\x17 \x01(\t\x12\x11\n\tunit_spec\x18\x18 \x01(\t\x12\x0e\n\x06remark\x18\x19 \x01(\t\x12\x17\n\x0fopened_position\x18\x1a \x01(\x08\x12\x12\n\nrequest_id\x18\x1e \x01(\x04\x12\x16\n\x0eprocess_status\x18\x1f \x01(\t\x12\x16\n\x0e\x63ost_center_id\x18  \x01(\x04\x12\x11\n\tperiod_id\x18! \x01(\x04\x12\x13\n\x0bperiod_name\x18\" \x01(\t\"R\n\x1aListProcessingCostResponse\x12%\n\x04rows\x18\x01 \x03(\x0b\x32\x17.manufactory.CostDetail\x12\r\n\x05total\x18\x02 \x01(\x04\"4\n\x1eGetProcessingCostDetailRequest\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\"J\n\x1fGetProcessingCostDetailResponse\x12\'\n\x06\x64\x65tail\x18\" \x01(\x0b\x32\x17.manufactory.CostDetail\"\x81\x01\n\x1bUpdateProcessingCostRequest\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x15\n\rupdate_detail\x18\x03 \x01(\x08\x12\'\n\x06\x64\x65tail\x18\" \x01(\x0b\x32\x17.manufactory.CostDetail\"B\n\x1cUpdateProcessingCostResponse\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\"G\n!ChangeProcessingCostStatusRequest\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\"H\n\"ChangeProcessingCostStatusResponse\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t2\xd8\x06\n\x0eProcessingCost\x12\x96\x01\n\x14\x43reateProcessingCost\x12(.manufactory.CreateProcessingCostRequest\x1a).manufactory.CreateProcessingCostResponse\")\x82\xd3\xe4\x93\x02#\"\x1e/api/v2/supply/processing/cost:\x01*\x12\x92\x01\n\x12ListProcessingCost\x12&.manufactory.ListProcessingCostRequest\x1a\'.manufactory.ListProcessingCostResponse\"+\x82\xd3\xe4\x93\x02%\x12#/api/v2/supply/processing/cost/list\x12\xb0\x01\n\x17GetProcessingCostDetail\x12+.manufactory.GetProcessingCostDetailRequest\x1a,.manufactory.GetProcessingCostDetailResponse\":\x82\xd3\xe4\x93\x02\x34\x12\x32/api/v2/supply/processing/cost/{receipt_id}/detail\x12\xa3\x01\n\x14UpdateProcessingCost\x12(.manufactory.UpdateProcessingCostRequest\x1a).manufactory.UpdateProcessingCostResponse\"6\x82\xd3\xe4\x93\x02\x30\x1a+/api/v2/supply/processing/cost/{receipt_id}:\x01*\x12\xbe\x01\n\x1a\x43hangeProcessingCostStatus\x12..manufactory.ChangeProcessingCostStatusRequest\x1a/.manufactory.ChangeProcessingCostStatusResponse\"?\x82\xd3\xe4\x93\x02\x39\x1a\x34/api/v2/supply/processing/cost/{receipt_id}/{status}:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_CREATEPROCESSINGCOSTREQUEST = _descriptor.Descriptor(
  name='CreateProcessingCostRequest',
  full_name='manufactory.CreateProcessingCostRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='period_id', full_name='manufactory.CreateProcessingCostRequest.period_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.CreateProcessingCostRequest.status', index=1,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.CreateProcessingCostRequest.type', index=2,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='month', full_name='manufactory.CreateProcessingCostRequest.month', index=3,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_id', full_name='manufactory.CreateProcessingCostRequest.machining_center_id', index=4,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_code', full_name='manufactory.CreateProcessingCostRequest.machining_center_code', index=5,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_name', full_name='manufactory.CreateProcessingCostRequest.machining_center_name', index=6,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.CreateProcessingCostRequest.position_id', index=7,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.CreateProcessingCostRequest.position_name', index=8,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.CreateProcessingCostRequest.position_code', index=9,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_cost', full_name='manufactory.CreateProcessingCostRequest.unit_cost', index=10,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.CreateProcessingCostRequest.quantity', index=11,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.CreateProcessingCostRequest.unit_id', index=12,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.CreateProcessingCostRequest.unit_name', index=13,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='manufactory.CreateProcessingCostRequest.unit_spec', index=14,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='opened_position', full_name='manufactory.CreateProcessingCostRequest.opened_position', index=15,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.CreateProcessingCostRequest.remark', index=16,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.CreateProcessingCostRequest.request_id', index=17,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='manufactory.CreateProcessingCostRequest.cost_center_id', index=18,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=114,
  serialized_end=542,
)


_CREATEPROCESSINGCOSTRESPONSE = _descriptor.Descriptor(
  name='CreateProcessingCostResponse',
  full_name='manufactory.CreateProcessingCostResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='manufactory.CreateProcessingCostResponse.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.CreateProcessingCostResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=544,
  serialized_end=610,
)


_LISTPROCESSINGCOSTREQUEST = _descriptor.Descriptor(
  name='ListProcessingCostRequest',
  full_name='manufactory.ListProcessingCostRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_month', full_name='manufactory.ListProcessingCostRequest.start_month', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_month', full_name='manufactory.ListProcessingCostRequest.end_month', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_centers', full_name='manufactory.ListProcessingCostRequest.machining_centers', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_ids', full_name='manufactory.ListProcessingCostRequest.period_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.ListProcessingCostRequest.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='manufactory.ListProcessingCostRequest.position_ids', index=5,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.ListProcessingCostRequest.status', index=6,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.ListProcessingCostRequest.limit', index=7,
      number=10, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.ListProcessingCostRequest.offset', index=8,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='manufactory.ListProcessingCostRequest.include_total', index=9,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='manufactory.ListProcessingCostRequest.order', index=10,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='manufactory.ListProcessingCostRequest.sort', index=11,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=613,
  serialized_end=862,
)


_COSTDETAIL = _descriptor.Descriptor(
  name='CostDetail',
  full_name='manufactory.CostDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.CostDetail.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.CostDetail.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.CostDetail.created_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.CostDetail.created_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.CostDetail.updated_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.CostDetail.updated_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.CostDetail.created_at', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.CostDetail.updated_at', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.CostDetail.status', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.CostDetail.code', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='month', full_name='manufactory.CostDetail.month', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.CostDetail.type', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_id', full_name='manufactory.CostDetail.machining_center_id', index=12,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_code', full_name='manufactory.CostDetail.machining_center_code', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_name', full_name='manufactory.CostDetail.machining_center_name', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.CostDetail.position_id', index=15,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.CostDetail.position_name', index=16,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.CostDetail.position_code', index=17,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_cost', full_name='manufactory.CostDetail.unit_cost', index=18,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.CostDetail.quantity', index=19,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.CostDetail.unit_id', index=20,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.CostDetail.unit_name', index=21,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='manufactory.CostDetail.unit_spec', index=22,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.CostDetail.remark', index=23,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='opened_position', full_name='manufactory.CostDetail.opened_position', index=24,
      number=26, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.CostDetail.request_id', index=25,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='manufactory.CostDetail.process_status', index=26,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='manufactory.CostDetail.cost_center_id', index=27,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_id', full_name='manufactory.CostDetail.period_id', index=28,
      number=33, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_name', full_name='manufactory.CostDetail.period_name', index=29,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=865,
  serialized_end=1547,
)


_LISTPROCESSINGCOSTRESPONSE = _descriptor.Descriptor(
  name='ListProcessingCostResponse',
  full_name='manufactory.ListProcessingCostResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.ListProcessingCostResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.ListProcessingCostResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1549,
  serialized_end=1631,
)


_GETPROCESSINGCOSTDETAILREQUEST = _descriptor.Descriptor(
  name='GetProcessingCostDetailRequest',
  full_name='manufactory.GetProcessingCostDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='manufactory.GetProcessingCostDetailRequest.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1633,
  serialized_end=1685,
)


_GETPROCESSINGCOSTDETAILRESPONSE = _descriptor.Descriptor(
  name='GetProcessingCostDetailResponse',
  full_name='manufactory.GetProcessingCostDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='detail', full_name='manufactory.GetProcessingCostDetailResponse.detail', index=0,
      number=34, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1687,
  serialized_end=1761,
)


_UPDATEPROCESSINGCOSTREQUEST = _descriptor.Descriptor(
  name='UpdateProcessingCostRequest',
  full_name='manufactory.UpdateProcessingCostRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='manufactory.UpdateProcessingCostRequest.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.UpdateProcessingCostRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='update_detail', full_name='manufactory.UpdateProcessingCostRequest.update_detail', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='detail', full_name='manufactory.UpdateProcessingCostRequest.detail', index=3,
      number=34, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1764,
  serialized_end=1893,
)


_UPDATEPROCESSINGCOSTRESPONSE = _descriptor.Descriptor(
  name='UpdateProcessingCostResponse',
  full_name='manufactory.UpdateProcessingCostResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='manufactory.UpdateProcessingCostResponse.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.UpdateProcessingCostResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1895,
  serialized_end=1961,
)


_CHANGEPROCESSINGCOSTSTATUSREQUEST = _descriptor.Descriptor(
  name='ChangeProcessingCostStatusRequest',
  full_name='manufactory.ChangeProcessingCostStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='manufactory.ChangeProcessingCostStatusRequest.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.ChangeProcessingCostStatusRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1963,
  serialized_end=2034,
)


_CHANGEPROCESSINGCOSTSTATUSRESPONSE = _descriptor.Descriptor(
  name='ChangeProcessingCostStatusResponse',
  full_name='manufactory.ChangeProcessingCostStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='manufactory.ChangeProcessingCostStatusResponse.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.ChangeProcessingCostStatusResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2036,
  serialized_end=2108,
)

_COSTDETAIL.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_COSTDETAIL.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPROCESSINGCOSTRESPONSE.fields_by_name['rows'].message_type = _COSTDETAIL
_GETPROCESSINGCOSTDETAILRESPONSE.fields_by_name['detail'].message_type = _COSTDETAIL
_UPDATEPROCESSINGCOSTREQUEST.fields_by_name['detail'].message_type = _COSTDETAIL
DESCRIPTOR.message_types_by_name['CreateProcessingCostRequest'] = _CREATEPROCESSINGCOSTREQUEST
DESCRIPTOR.message_types_by_name['CreateProcessingCostResponse'] = _CREATEPROCESSINGCOSTRESPONSE
DESCRIPTOR.message_types_by_name['ListProcessingCostRequest'] = _LISTPROCESSINGCOSTREQUEST
DESCRIPTOR.message_types_by_name['CostDetail'] = _COSTDETAIL
DESCRIPTOR.message_types_by_name['ListProcessingCostResponse'] = _LISTPROCESSINGCOSTRESPONSE
DESCRIPTOR.message_types_by_name['GetProcessingCostDetailRequest'] = _GETPROCESSINGCOSTDETAILREQUEST
DESCRIPTOR.message_types_by_name['GetProcessingCostDetailResponse'] = _GETPROCESSINGCOSTDETAILRESPONSE
DESCRIPTOR.message_types_by_name['UpdateProcessingCostRequest'] = _UPDATEPROCESSINGCOSTREQUEST
DESCRIPTOR.message_types_by_name['UpdateProcessingCostResponse'] = _UPDATEPROCESSINGCOSTRESPONSE
DESCRIPTOR.message_types_by_name['ChangeProcessingCostStatusRequest'] = _CHANGEPROCESSINGCOSTSTATUSREQUEST
DESCRIPTOR.message_types_by_name['ChangeProcessingCostStatusResponse'] = _CHANGEPROCESSINGCOSTSTATUSRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CreateProcessingCostRequest = _reflection.GeneratedProtocolMessageType('CreateProcessingCostRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPROCESSINGCOSTREQUEST,
  __module__ = 'manufactory.processing_cost_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreateProcessingCostRequest)
  ))
_sym_db.RegisterMessage(CreateProcessingCostRequest)

CreateProcessingCostResponse = _reflection.GeneratedProtocolMessageType('CreateProcessingCostResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPROCESSINGCOSTRESPONSE,
  __module__ = 'manufactory.processing_cost_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreateProcessingCostResponse)
  ))
_sym_db.RegisterMessage(CreateProcessingCostResponse)

ListProcessingCostRequest = _reflection.GeneratedProtocolMessageType('ListProcessingCostRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTPROCESSINGCOSTREQUEST,
  __module__ = 'manufactory.processing_cost_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ListProcessingCostRequest)
  ))
_sym_db.RegisterMessage(ListProcessingCostRequest)

CostDetail = _reflection.GeneratedProtocolMessageType('CostDetail', (_message.Message,), dict(
  DESCRIPTOR = _COSTDETAIL,
  __module__ = 'manufactory.processing_cost_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CostDetail)
  ))
_sym_db.RegisterMessage(CostDetail)

ListProcessingCostResponse = _reflection.GeneratedProtocolMessageType('ListProcessingCostResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTPROCESSINGCOSTRESPONSE,
  __module__ = 'manufactory.processing_cost_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ListProcessingCostResponse)
  ))
_sym_db.RegisterMessage(ListProcessingCostResponse)

GetProcessingCostDetailRequest = _reflection.GeneratedProtocolMessageType('GetProcessingCostDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPROCESSINGCOSTDETAILREQUEST,
  __module__ = 'manufactory.processing_cost_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetProcessingCostDetailRequest)
  ))
_sym_db.RegisterMessage(GetProcessingCostDetailRequest)

GetProcessingCostDetailResponse = _reflection.GeneratedProtocolMessageType('GetProcessingCostDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPROCESSINGCOSTDETAILRESPONSE,
  __module__ = 'manufactory.processing_cost_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetProcessingCostDetailResponse)
  ))
_sym_db.RegisterMessage(GetProcessingCostDetailResponse)

UpdateProcessingCostRequest = _reflection.GeneratedProtocolMessageType('UpdateProcessingCostRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPROCESSINGCOSTREQUEST,
  __module__ = 'manufactory.processing_cost_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.UpdateProcessingCostRequest)
  ))
_sym_db.RegisterMessage(UpdateProcessingCostRequest)

UpdateProcessingCostResponse = _reflection.GeneratedProtocolMessageType('UpdateProcessingCostResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPROCESSINGCOSTRESPONSE,
  __module__ = 'manufactory.processing_cost_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.UpdateProcessingCostResponse)
  ))
_sym_db.RegisterMessage(UpdateProcessingCostResponse)

ChangeProcessingCostStatusRequest = _reflection.GeneratedProtocolMessageType('ChangeProcessingCostStatusRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHANGEPROCESSINGCOSTSTATUSREQUEST,
  __module__ = 'manufactory.processing_cost_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ChangeProcessingCostStatusRequest)
  ))
_sym_db.RegisterMessage(ChangeProcessingCostStatusRequest)

ChangeProcessingCostStatusResponse = _reflection.GeneratedProtocolMessageType('ChangeProcessingCostStatusResponse', (_message.Message,), dict(
  DESCRIPTOR = _CHANGEPROCESSINGCOSTSTATUSRESPONSE,
  __module__ = 'manufactory.processing_cost_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ChangeProcessingCostStatusResponse)
  ))
_sym_db.RegisterMessage(ChangeProcessingCostStatusResponse)



_PROCESSINGCOST = _descriptor.ServiceDescriptor(
  name='ProcessingCost',
  full_name='manufactory.ProcessingCost',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=2111,
  serialized_end=2967,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateProcessingCost',
    full_name='manufactory.ProcessingCost.CreateProcessingCost',
    index=0,
    containing_service=None,
    input_type=_CREATEPROCESSINGCOSTREQUEST,
    output_type=_CREATEPROCESSINGCOSTRESPONSE,
    serialized_options=_b('\202\323\344\223\002#\"\036/api/v2/supply/processing/cost:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListProcessingCost',
    full_name='manufactory.ProcessingCost.ListProcessingCost',
    index=1,
    containing_service=None,
    input_type=_LISTPROCESSINGCOSTREQUEST,
    output_type=_LISTPROCESSINGCOSTRESPONSE,
    serialized_options=_b('\202\323\344\223\002%\022#/api/v2/supply/processing/cost/list'),
  ),
  _descriptor.MethodDescriptor(
    name='GetProcessingCostDetail',
    full_name='manufactory.ProcessingCost.GetProcessingCostDetail',
    index=2,
    containing_service=None,
    input_type=_GETPROCESSINGCOSTDETAILREQUEST,
    output_type=_GETPROCESSINGCOSTDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\0024\0222/api/v2/supply/processing/cost/{receipt_id}/detail'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateProcessingCost',
    full_name='manufactory.ProcessingCost.UpdateProcessingCost',
    index=3,
    containing_service=None,
    input_type=_UPDATEPROCESSINGCOSTREQUEST,
    output_type=_UPDATEPROCESSINGCOSTRESPONSE,
    serialized_options=_b('\202\323\344\223\0020\032+/api/v2/supply/processing/cost/{receipt_id}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ChangeProcessingCostStatus',
    full_name='manufactory.ProcessingCost.ChangeProcessingCostStatus',
    index=4,
    containing_service=None,
    input_type=_CHANGEPROCESSINGCOSTSTATUSREQUEST,
    output_type=_CHANGEPROCESSINGCOSTSTATUSRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\0324/api/v2/supply/processing/cost/{receipt_id}/{status}:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_PROCESSINGCOST)

DESCRIPTOR.services_by_name['ProcessingCost'] = _PROCESSINGCOST

# @@protoc_insertion_point(module_scope)
