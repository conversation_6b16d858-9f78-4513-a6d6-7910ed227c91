syntax = "proto3";

package manufactory;
import "google/api/annotations.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

service ManufactoryPurchase {
    // 新建采购订单
    rpc CreatePurchaseOrder (CreatePurchaseOrderRequest) returns (CreatePurchaseOrderResponse) {
        option (google.api.http) = {
            post: "/api/v2/manufactory/create/purchase/order"
            body: "*"
        };
    }
    // 加工中心采购订单列表查询
    rpc ListPurchaseOrder (ListPurchaseOrderRequest) returns (ListPurchaseOrderResponse) {
        option (google.api.http) = {
            get: "/api/v2/manufactory/list/purchase/order"
        };
    }
    // 修改订单状态
    rpc ChangeOrderStatus (ChangeOrderStatusRequest) returns (ChangeOrderStatusResponse) {
        option (google.api.http) = {
            put: "/api/v2/manufactory/order/{status}/{order_id}"
            body: "*"
        };
    }
    // 更新商品信息：已驳回状态和新建状态下可以修改采购单信息
    rpc UpdatePurchaseOrder (UpdatePurchaseOrderRequest) returns (UpdatePurchaseOrderResponse) {
        option (google.api.http) = {
            post: "/api/v2/manufactory/update/purchase/order"
            body: "*"
        };
    }
    // 查询订单详情
    rpc GetOrderDetailById (GetOrderDetailByIdRequest) returns (GetOrderDetailByIdResponse) {
        option (google.api.http) = {
            get: "/api/v2/manufactory/order/detail/{order_id}"
        };
    }
    // 根据加工中心id拉商品列表
    rpc GetProductListByWHId (GetProductListByWHIdRequest) returns (GetProductListByWHIdResponse) {
        option (google.api.http) = {
            get:"/api/v2/manufactory/product/list/{id}"
        };
    }

    // 加工中心采购报表
    rpc GetPurchaseBi (GetPurchaseBiRequest) returns (GetPurchaseBiResponse) {
        option (google.api.http) = {
            get:"/api/v2/manufactory/purchase/bi/detailed"
        };
    }

    // 查询订单列表详情
    rpc GetOrdersDetailByIds (GetOrdersDetailByIdsRequest) returns (GetOrdersDetailByIdsResponse) {
        option (google.api.http) = {
            post: "/api/v2/manufactory/list/purchase/order/print"
            body: "*"
        };
    }
}

// 采购报表请求
message GetPurchaseBiRequest{
    // 供应商id
    repeated uint64 s_ids = 1;
    // 加工中心id
    repeated uint64 wh_ids = 2;
    repeated uint64 product_ids = 3;
    repeated uint64 category_ids = 4;
    // 订货日期
    google.protobuf.Timestamp start_date = 5;
    // 到货日期
    google.protobuf.Timestamp end_date = 6;
    int64 limit = 7;
    uint64 offset = 8;
    string branch_type = 9;
}

message GetPurchaseBiResponse{
    repeated PurchaseDetailed rows = 1;
    Total total = 2;
}

message Total{
    // 条数
    double count = 1;
    // 合计数量
    double sum_all_quantity = 2;
}

message PurchaseDetailed{
    // 核算单位id
    uint64 accounting_unit_id = 1;
    // 核算单位名称
    string accounting_unit_name = 2;
    // 商品类别id
    uint64 category_id = 3;
    // 商品类别编码
    string category_code = 4;
    // 商品类别名称
    string category_name = 5;
    // 商品类别父级
    string category_parent = 6;
    // 确认数量
    double confirmed_quantity = 7;
    // 商品id
    uint64 product_id = 8;
    // 商品编码
    string product_code = 9;
    // 商品名称
    string product_name = 10;
    // 商品规格
    string product_spec = 11;
    // 数量
    double purchase_quantity = 12;
    // 加工中心id
    uint64 wh_id = 16;
    // 加工中心编码
    string wh_code = 17;
    // 加工中心名称
    string wh_name = 18;
    // 单位id
    uint64 unit_id = 19;
    // 单位名称
    string unit_name = 20;
    // 配送日期
    string order_date = 21;
    // 订货日期
    string arrival_date = 22;
    // 订货单编码
    string order_code = 23;
    // 采购单id
    uint64 order_id = 25;
    // id
    uint64 id = 26;
    // 供应商id
    uint64 vendor_id = 27;
    // 供应商编码
    string vendor_code = 28;
    // 供应商名称
    string vendor_name = 29;
    // 采购含税价(单价)
    double price_tax = 30;
    // 采购商品成本(单价)
    double price = 31;
    // 税率
    double tax_rate = 32;
    // 税
    double tax = 33;
    // 商品sku状态
    string product_status = 34;
    // 采购单据状态
    string order_status = 35;
    // 备注
    string remark = 36;
}

// 创建采购单请求参数
message CreatePurchaseOrderRequest {
    // 订货日期
    google.protobuf.Timestamp order_date = 1;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 2;
    // 备注
    string remark = 3;
    // 收货加工中心的id
    uint64 received_by = 4;
    // 供应商id
    uint64 supplier_id = 5;
    // 供应商名称
    string supplier_name = 6;
    // 订单(单据)类型
    string order_type = 7;
    // 采购类型
    string purchase_type = 8;
    // 采购原因
    string purchase_reason = 9;
    // 加工中心订货单商品信息
    repeated product_item product_items = 10;
    // 区分加工中心/加工中心-WAREHOUSE/MACHINING_CENTER
    string branch_type = 11;
    // 唯一请求id，作幂等性校验
    uint64 request_id = 12;
}
message product_item {
    // 商品id
    uint64 product_id = 1;
    // 订货数量
    double quantity = 2;
    // 商品订货物流模式(NMD:配送, PUR:直送)
    string distribution_type = 3;
    // 采购含税价(单价)
    double price_tax = 4;
    // 采购单位id
    uint64 purchase_unit_id = 5;
    // 核算单位ID
    uint64 accounting_unit_id = 6;
    // 单位换算比例
    double unit_rate = 7;
    // 税率
    double tax_rate = 8;
    // 采购金额(含税合计)
    double purchase_price = 9;
    // 采购商品成本(单价)
    double price = 10;
}
// 创建采购单返回报文
message CreatePurchaseOrderResponse {
    // 对象id
    uint64 order_id = 1;
}


// 加工中心采购订单列表查询请求参数
message ListPurchaseOrderRequest {
    // 采购日期起
    google.protobuf.Timestamp start_date = 1;
    // 采购日期止
    google.protobuf.Timestamp end_date = 2;
    // 订单编号
    string order_code = 3;
    // 单据状态
    string order_status = 4;
    // 单据类型
    string order_type = 5;
    // 采购类型
    string purchase_type = 6;
    // 供应商id列表
    repeated uint64 supplier_ids = 7;
    // 收货加工中心id列表
    repeated uint64 received_ids = 8;
    // 采购原因
    string purchase_reason = 9;
    int64 limit = 10;
    uint64 offset = 11;
    bool include_total = 12;
    // 排序方式  asc or desc
    string sort_type = 13;
    // 排序字段
    string sort = 14;
    // 区分加工中心/加工中心-WAREHOUSE/MACHINING_CENTER
    string branch_type = 15;
    // 商品ids
    repeated uint64 product_ids = 16;
}
message PurchaseOrderRow {
    // 订单id
    uint64 order_id = 1;
    // 订单编号
    string order_code = 2;
    // 单据状态
    string order_status = 3;
    // 采购日期
    google.protobuf.Timestamp order_date = 4;
    // 收货加工中心id or code
    uint64 received_by = 5;
    // 收货加工中心名称
    string received_name = 6;
    // 单据类型
    string order_type = 7;
    // 供应商id or code
    uint64 supplier_id = 8;
    // 供应商名称
    string supplier_name = 9;
    // 合计含税金额
    double sum_price_tax = 10;
    // 合计税额
    double sum_tax = 11;
    // 合计金额(不含税)
    double sum_price = 12;
    // 合计关税
    double sum_tariff = 13;
    uint64 created_by = 14;
    string created_name = 15;
    google.protobuf.Timestamp created_at = 16;
    uint64 updated_by = 17;
    string updated_name = 18;
    google.protobuf.Timestamp updated_at = 19;
    // 区分加工中心/加工中心-WAREHOUSE/MACHINING_CENTER
    string branch_type = 20;
    // 采购原因
    string purchase_reason = 21;
    // 采购类型
    string purchase_type = 22;

}
// 加工中心采购订单列表查询结果
message ListPurchaseOrderResponse {
    repeated PurchaseOrderRow rows = 1;
    uint64 total = 2;
}


// 修改订单状态请求参数
message ChangeOrderStatusRequest {
    // 采购订单id
    uint64 order_id = 1;
    // 订单状态：INITED新建 SUBMITTED提交 REJECTED驳回 APPROVED审核 CANCELLED取消 SUCCESS已收货
    string status = 2;
}
// 修改订单状态返回报文
message ChangeOrderStatusResponse {
    uint64 order_id = 1;
    // 修改状态成功与否描述
    string description = 2;
}

// 更新采购单信息请求参数
message UpdatePurchaseOrderRequest {
    // 采购订单id
    uint64 order_id = 1;
    // 订货日期
    google.protobuf.Timestamp order_date = 2;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 3;
    // 备注
    string remark = 4;
    // 收货加工中心的id
    uint64 received_by = 5;
    // 供应商id
    uint64 supplier_id = 6;
    // 订单(单据)类型
    string order_type = 7;
    // 采购类型
    string purchase_type = 8;
    // 采购原因
    string purchase_reason = 9;
    // 更新订单明细是传入商品列表
    repeated product_item product_items = 10;
}
// 更新采购单信息返回报文
message UpdatePurchaseOrderResponse {
    uint64 order_id = 1;
    // 修改状态成功与否描述
    string description = 2;
}

// 查询采购订单详情请求参数
message GetOrderDetailByIdRequest {
    // 订单id
    uint64 order_id = 1;
}
// 订单商品
message OrderProductRow {
    uint64  id = 1;
    uint64 created_by = 2;
    string created_name = 3;
    uint64 updated_by = 4;
    string updated_name = 5;
    // 商品id
    uint64 product_id = 6;
    // 商品code
    string product_code = 7;
    // 商品名称
    string product_name = 8;
    // 商品类别id
    uint64 product_category_id = 9;
    // 商品类型
    string product_type = 10;
    // 采购单位id
    uint64 purchase_unit_id = 11;
    string purchase_unit_name = 12;
    string purchase_unit_spec = 13;
    // 销售类型
    string sale_type = 14;
    // 商品规格
    string spec = 15;
    // 核算单位ID
    uint64 accounting_unit_id = 16;
    string accounting_unit_name = 17;
    string accounting_unit_spec = 18;
    // 订货数量
    double quantity = 19;
    // 采购含税价(单价)
    double price_tax = 20;
    // 单位换算比例
    double unit_rate = 21;
    // 税率
    double tax_rate = 22;
    // 采购金额(含税合计)
    double purchase_price = 23;
    // 采购商品成本(单价)
    double price = 24;
}
// 查询采购订单详情返回报文
message GetOrderDetailByIdResponse {
    // 订单id
    uint64 order_id = 1;
    // 订单编号
    string order_code = 2;
    // 单据状态
    string order_status = 3;
    // 采购日期
    google.protobuf.Timestamp order_date = 4;
    // 收货加工中心id or code
    uint64 received_by = 5;
    // 收货加工中心名称
    string received_name = 6;
    // 单据类型
    string order_type = 7;
    // 供应商id or code
    uint64 supplier_id = 8;
    // 供应商名称
    string supplier_name = 9;
    // 创建人
    uint64 created_by = 10;
    // 创建人姓名
    string created_name = 11;
    // 更新人
    uint64 updated_by = 12;
    string updated_name = 13;
    // 采购类型
    string purchase_type = 14;
    // 采购原因
    string purchase_reason = 15;
    // 备注
    string remark = 16;
    // 订单商品列表
    repeated OrderProductRow products = 17;
    uint64 total_product = 18;
    google.protobuf.Timestamp created_at = 19;
    google.protobuf.Timestamp updated_at = 20;
    // 区分加工中心/加工中心-WAREHOUSE/MACHINING_CENTER
    string branch_type = 21;
    // 合计含税金额
    double sum_price_tax = 22;
    // 合计数量
    double total_quantity = 23;
}

// 根据加工中心id拉取商品请求参数
message GetProductListByWHIdRequest {
    // 加工中心id
    uint64 id = 1;
    // 商品分类id列表
    repeated uint64 product_category_ids = 2;
    // 是否包含停用商品
    bool return_all_products = 3;
    // 返回主档全部商品 不需加工中心下分类筛选 给前端用
    bool return_metadata_products = 4;
    // 要模糊查询的字符串
    string search = 5;
    // 要查询的字段, 多个逗号隔开;
    string search_fields = 6;
}
message MUnit{
    // 数据id
    string id = 1;
    // 商户id
    string partner_id = 2;
    // scope_id
    string scope_id = 3;
    string name = 4;
    string code = 5;
    string tp_code = 6;
    string updated = 7;
    double rate = 8;
    bool default = 9;
    bool order = 10;
    bool purchase = 11;
    bool sales = 12;
    bool stocktake = 13;
    bool bom = 14;
    bool transfer = 15;
    bool default_stocktake = 16;
}
message MProduct {
    // 商品id
    string id = 1;
    // 商户id
    string partner_id = 2;
    // scope_id
    string scope_id = 3;
    string name = 4;
    string code = 5;
    string second_code = 6;
    string sale_type = 7;
    string product_type = 8;
    string bom_type = 9;
    string storage_type = 10;
    string status = 11;
    string alias = 12;
    string category = 13;
    string updated = 14;
    google.protobuf.Struct extends = 15;
    google.protobuf.Struct extend_code = 16;
    repeated MUnit units = 17;
    string model_code = 18;
    string model_name = 19;
    int32 default_receiving_deviation_min = 20;
    int32 default_receiving_deviation_max = 21;
    int32 default_purchase_deviation_min = 22;
    int32 default_purchase_deviation_max = 23;
    string ledger_class = 24;
    string category_name = 25;
}
// 根据加工中心id拉取商品返回参数
message GetProductListByWHIdResponse {
    repeated MProduct rows = 1;
    int32 total = 2;
}

message GetOrdersDetailByIdsRequest {
    repeated uint64 order_ids = 1;
}

message GetOrdersDetailByIdsResponse {
    repeated GetOrderDetailByIdResponse rows = 1;
}