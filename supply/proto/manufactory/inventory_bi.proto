syntax = "proto3";
package manufactory;
import "google/api/annotations.proto";
// import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

// 库存报表相关服务
service ManufactoryInventoryBiService {

    rpc RealtimeInventory (RealtimeInventoryRequest) returns (RealtimeInventoryResponse) {
        option (google.api.http) = {
            get:"/api/v2/supply/manufactory/inventory/realtime"
        };
    }

    rpc DailyInventory (DailyInventoryRequest) returns (DailyInventoryResponse) {
        option (google.api.http) = {
            get:"/api/v2/supply/manufactory/inventory/daily"
        };
    }

    rpc QueryInventoryLog (QueryInventoryLogRequest) returns (QueryInventoryLogResponse) {
        option (google.api.http) = {
            get:"/api/v2/supply/manufactory/inventory/log"
        };
    }

    // 按账户(目前只兼容仓库/加工中心查询发货仓位库存)和指定商品查询实时库存，包装给前端用
    // 查询库存前校验当前账户是否有子账户，若有返回子账户的库存
    rpc RealtimeInventoryByAccounts (RealtimeInventoryByAccountsRequest) returns (RealtimeInventoryByAccountsResponse){
        option (google.api.http) = {
            post:"/api/v2/supply/manufactory/inventory/realtime/by/accounts"
            body:"*"
        };
    }

    rpc SummaryInventory (SummaryInventoryRequest) returns (SummaryInventoryResponse) {
        option (google.api.http) = {
            post: "/api/v2/supply/manufactory/inventory/summary"
            body: "*"
        };
    }

}





message RealtimeInventoryRequest {
    // 批次号
    repeated uint64 branch_ids = 1;
    // 地理区域
    repeated uint64 geo_regions = 2;
    // 商品类别
    repeated string category_ids = 3;
    // 商品id
    repeated uint64 product_ids = 4;
    // 分页大小
    int32 limit = 5;
    // 跳过行数
    int32 offset = 6;
    // 排序字段
    string order = 7;
    // 排序方式
    string sort = 8;
    // 是否排零
    string exclude = 9;
    // 区分门店和仓库 STORE/manufactory
    string branch_type = 10;
    string lan = 11;
    // 子账户(仓位)id列表
    repeated uint64 position_ids = 12;
    // 返回参数
    string return_fields = 13;

}

message RealtimeInventoryResponse {
    repeated Inventory rows = 1;
    int32 total = 2;
}

message DailyInventoryRequest {
    // 批次号
    uint64 branch_id = 1;
    // 商品类别
    repeated uint64 category_ids = 2;
    // 商品id
    repeated uint64 product_ids = 3;
    // 开始时间
    google.protobuf.Timestamp start_date = 4;
    // 结束时间
    google.protobuf.Timestamp end_date = 5;
    // 分页大小
    int32 limit = 6;
    // 跳过行数
    int32 offset = 7;
    string code = 9;
    string action = 10;
    string if_pre = 11;
    string if_end = 12;
    string exclude_empty = 13;
    // 用来区分门店和仓库 STORE/WAREHOUSE/MANUFACTORY
    string branch_type = 14;
    string lan = 15;
    // 子账户(仓位)id列表
    repeated int64 position_ids = 16;
    string return_fields = 17;
}

message DailyInventoryResponse {
    repeated DailyInventory rows = 1;
    int32 total = 2;
}

message DailyInventory {
    // 预留字段id
    uint64 id = 1;
    // 门店id
    uint64 store_id = 2;
    // 门店名称
    string store_name = 3;
    // 门店编码
    string store_code = 4;
    // 商品id
    uint64 product_id = 5;
    // 商品编码
    string product_code = 6;
    // 商品名称
    string product_name = 7;
    // 类别id
    uint64 category_id = 8;
    // 类别编码
    string category_code = 9;
    // 类别名称
    string category_name = 10;
    // 规格
    string spec = 11;
    // 核算单位id
    uint64 accounting_unit_id = 12;
    //核算单位编码
    string accounting_unit_code = 13;
    // 核算单位名称
    string accounting_unit_name = 14;
    // 期初库存
    double pre_qty = 15;
    // 期末库存
    double qty = 16;
    // 调拨入库
    double trans_deposit = 17;
    // 调拨出库
    double trans_withdraw = 18;
    // 盘点入库
    double stoc_deposit = 19;
    // 盘点出库
    double stoc_withdraw = 20;
    // 报废入库
    double adj_deposit = 21;
    // 报废出库
    double adj_withdraw = 22;
    // 收货入库数量
    double rec_deposit = 23;
    // 收货差异入库
    double rec_diff_deposit = 24;
    // 收货差异出库
    double rec_diff_withdraw = 25;
    // 退货出库
    double ret_withdraw = 26;
    // 上次切片节点
    string start_time = 27;
    // 切片节点结束时间
    string end_time = 28;
    // 销售入库
    double sales_deposit = 29;
    // 销售出库
    double sales_withdraw = 30;
    // 热卖领料入库
    double llyl_deposit = 31;
    // 热卖领料出库
    double llyl_withdraw = 32;
    // 热卖成品入库
    double cpcrk_deposit = 33;
    // 热卖成品出库
    double cpcrk_withdraw = 34;
    // 收货出库数量
    double rec_withdraw = 35;
    // 退货入库
    double ret_deposit = 36;
    // 冻结
    double freeze_qty = 37;
    // 采购收货入库
    double purchase_deposit = 38;
    // 低耗费用化
    double low_cost = 39;
    // 特殊库存调整
    double inventory_adjust = 40;
    // 库存初始化
    double inventory_init = 41;
    // 库存初始化
    string sub_account = 42;
    repeated DailyInventory children = 43;
    // 仓位id
    uint64 position_id = 44;
    // 仓位名称
    string position_name = 45;
    // 仓位编码
    string position_code = 46;
    // 自采入库
    double spick_deposit = 47;
    // 物料转换
    double material_trans_deposit = 48;
    double material_trans_withdraw = 49;
    // 加工入库
    double processing_deposit = 50;
    // 加工出库
    double processing_withdraw = 51;
    // 包装入库
    double packing_deposit = 52;
    // 包装出库
    double packing_withdraw = 53;
    // 退货在途
    double ret_transfer = 54;
    // 调拨在途
    double trans_transfer = 55;
    // 发货在途
    double trans_delivery = 56;
    // 采购在途
    double trans_purchase = 57;
    // 退货在途释放
    double trans_return_release = 58;
    // 调拨在途释放
    double trans_transfer_release = 59;
    // 发货在途释放
    double trans_delivery_release = 60;
    // 采购在途释放
    double trans_purchase_release = 61;
    // 在途期初库存
    double trans_begin = 62;
    // 在途期末库存
    double trans_end = 63;
}

message QueryInventoryLogRequest {
    //分支ID,比如一个门店、配送中心
    uint64 branch_id = 1;
    //产品ID，对应到具体的一个sku
    repeated uint64 product_ids = 2;
    // 商品类别
    repeated uint64 category_ids = 3;
    // 单据编号
    string code = 4;
    // 操作
    string action = 5;
    int64 limit = 6;
    int64 offset = 7;
    google.protobuf.Timestamp start_date = 8;
    google.protobuf.Timestamp end_date = 9;
    // 单据类型
    string order_type = 10;
    // 账户类型
    string account_type = 11;
    string lan = 12;
    // 子账户(仓位)id列表
    repeated uint64 position_ids = 13;
    // 返回参数
    string return_fields = 14;
}

message QueryInventoryLogResponse {
    repeated InventoryLog rows = 1;
    int32 total = 2;
    double amount_sum = 3;
}

message InventoryLog {
    // 预留字段id
    uint64 id = 1;
    // 门店id
    uint64 store_id = 2;
    // 门店名称
    string store_name = 3;
    // 门店编码
    string store_code = 4;
    // 商品id
    uint64 product_id = 5;
    // 商品编码
    string product_code = 6;
    // 商品名称
    string product_name = 7;
    // 规格
    string spec = 8;
    // 订单编码
    string order_code = 9;
    // 订单类型
    string order_type = 10;
    // 订单时间
    google.protobuf.Timestamp order_time = 11;
    // 操作
    string action = 12;
    // 状态
    string status = 13;
    // 流水数量
    double qty = 14;
    // 库存id
    string stock_id = 15;
    // 核算单位id
    uint64 accounting_unit_id = 16;
    // 核算单位编码
    string accounting_unit_code = 17;
    // 核算单位名称
    string accounting_unit_name = 18;
    // 订货单位id
    uint64 demand_unit_id = 19;
    // 订货单位编码
    string demand_unit_code = 20;
    // 订货单位名称
    string demand_unit_name = 21;
    // 订货流水数量
    double demand_qty = 22;
    // 类别id
    uint64 category_id = 23;
    // 类别编码
    string category_code = 24;
    // 类别名称
    string category_name = 25;
    // jde相关
    string jde_order_id = 26;
    string jde_order_type = 27;
    string jde_mcu = 28;
    // 账户类型
    string account_type = 29;
    // 子账户信息
    uint64 sub_account_id = 30;
    string sub_account_code = 31;
    string sub_account_name = 32;

}

message Inventory {
    // 预留字段id
    uint64 id = 1;
    // 门店id
    uint64 store_id = 2;
    // 门店名称
    string store_name = 3;
    // 门店编码
    string store_code = 4;
    // 商品id
    uint64 product_id = 5;
    // 商品编码
    string product_code = 6;
    // 商品名称
    string product_name = 7;
    // 类别id
    uint64 category_id = 8;
    // 类别编码
    string category_code = 9;
    // 类别名称
    string category_name = 10;
    // 规格
    string spec = 11;
    // 核算单位id
    uint64 accounting_unit_id = 12;
    //核算单位编码
    string accounting_unit_code = 13;
    // 核算单位名称
    string accounting_unit_name = 14;
    // 实时在店库存
    double qty = 15;
    // 订货单位id
    uint64 demand_unit_id = 16;
    // 订货单位编码
    string demand_unit_code = 17;
    // 订货单位名称
    string demand_unit_name = 18;
    // 订货流水数量
    double demand_qty = 19;
    // 商品状态
    string product_status = 20;
    // 冻结库存
    double freeze_qty = 21;
    // 在途库存
    double broker_qty = 22;
    // 库存详情
    repeated ExtraDetail extra_detail = 23;
    // 采购单位id
    uint64 purchase_unit_id = 24;
    // 采购单位编码
    string purchase_unit_code = 25;
    // 采购单位名称
    string purchase_unit_name = 26;
    // 采购流水数量
    double purchase_qty = 27;
    repeated Inventory children = 28;
    // 仓位id
    uint64 position_id = 29;
    // 仓位名称
    string position_name = 30;
    // 仓位编码
    string position_code = 31;
    uint64 primary_id = 34;
    // 含税单价
    double tax_price = 35;
    // 不含税单价
    double cost_price = 36;
    // 库存金额（含税）
    double sku_amount = 37;
    double demand_broker_qty = 38;

}

message ExtraDetail {
    // 业务类型
    string code = 1;
    // 数量
    double qty = 2;
    // 账户类型
    string sku_type = 3;
    // 
    uint64 sub_account_id = 4;
    // 
    string product_id = 5;
    // 
    double quantity_avail = 6;
    double quantity_freeze = 7;
    double quantity_broker = 8;
    double amount = 9;
    // 订货单位对应的在途
    double demand_qty = 10;
}

// 账户信息
message Account {
    uint64 branch_id = 1;           // 组织ID, 比如一个门店、仓库
    uint64 product_id = 2;          // 商品ID
    string distribution_type = 3;   // 总仓配送(NMD)、采购(PUR)、加工配送(PAD)
}

message Accounts{
    uint64 branch_id = 1;       // 组织ID, 比如一个门店、仓库
    uint64 product_id = 2;      // 商品ID
    double qty = 3;             // 实时库存数量
    double round = 4;           // 精度（预留）
    repeated Accounts sub_accounts = 5;  // 子账号明细（预留）
    string branch_name = 6;
}

// 批量查询单Branch、单Product库存
message RealtimeInventoryByAccountsRequest{
    repeated Account accounts = 1;
    // 拉取组织仓位配置校验的业务类型:例如"发货"deliverGoods、"采购收货"purchaseReceive
    string check_type = 2;
}
// 实时库存查询请求结果
message RealtimeInventoryByAccountsResponse {
    int32 total = 1;
    repeated Accounts rows = 2;
}

message SummaryInventoryRequest {
    // 仓库号
    uint64 branch_id = 1;
    // 商品类别
    repeated uint64 category_ids = 2;
    // 商品id
    repeated uint64 product_ids = 3;
    // 开始时间
    google.protobuf.Timestamp start_date = 4;
    // 结束时间
    google.protobuf.Timestamp end_date = 5;
    // 分页大小
    int32 limit = 6;
    // 跳过行数
    int32 offset = 7;
    string code = 9;
    string action = 10;
    string if_pre = 11;
    string if_end = 12;
    string exclude_empty = 13;
    // 用来区分门店和仓库 STORE/WAREHOUSE
    string branch_type = 14;
    string lan = 15;
    // 子账户(仓位)id列表
    repeated int64 position_ids = 16;
}

message SummaryInventoryResponse {
    repeated SummaryInventory rows = 1;
    int32 total = 2;
}

message SummaryInventory {
    // 预留字段id
    uint64 id = 1;
    // 门店id
    uint64 store_id = 2;
    // 门店名称
    string store_name = 3;
    // 门店编码
    string store_code = 4;
    // 商品id
    uint64 product_id = 5;
    // 商品编码
    string product_code = 6;
    // 商品名称
    string product_name = 7;
    // 商品状态
    string product_status = 8;
    // 规格
    string spec = 11;
    // 核算单位id
    uint64 accounting_unit_id = 12;
    //核算单位编码
    string accounting_unit_code = 13;
    // 核算单位名称
    string accounting_unit_name = 14;
    // 期初库存
    double pre_qty = 15;
    // 期末库存
    double qty = 16;
    // 调拨入库
    double trans_deposit = 17;
    // 调拨出库
    double trans_withdraw = 18;
    // 盘点入库
    double stoc_deposit = 19;
    // 盘点出库
    double stoc_withdraw = 20;
    // 报废入库
    double adj_deposit = 21;
    // 报废出库
    double adj_withdraw = 22;
    // 收货入库数量
    double rec_deposit = 23;
    // 收货差异入库
    double rec_diff_deposit = 24;
    // 收货差异出库
    double rec_diff_withdraw = 25;
    // 退货出库
    double ret_withdraw = 26;
    // 上次切片节点
    google.protobuf.Timestamp start_time = 27;
    // 切片节点结束时间
    google.protobuf.Timestamp end_time = 28;
    // 销售入库
    double sales_deposit = 29;
    // 销售出库
    double sales_withdraw = 30;
    // 热卖领料入库
    double llyl_deposit = 31;
    // 热卖领料出库
    double llyl_withdraw = 32;
    // 热卖成品入库
    double cpcrk_deposit = 33;
    // 热卖成品出库
    double cpcrk_withdraw = 34;
    // 收货出库数量
    double rec_withdraw = 35;
    // 退货入库
    double ret_deposit = 36;
    // 冻结
    double freeze_qty = 37;
    // 采购收货入库
    double purchase_deposit = 38;
    // 低耗费用化
    double low_cost = 39;
    // 特殊库存调整
    double inventory_adjust = 40;
    // 库存初始化
    double inventory_init = 41;
    // 库存初始化
    string sub_account = 42;
    repeated DailyInventory children = 43;
    // 仓位id
    uint64 position_id = 44;
    // 仓位名称
    string position_name = 45;
    // 仓位编码
    string position_code = 46;
    // 自采入库
    double spick_deposit = 47;
    // 物料转换
    double material_trans_deposit = 48;
    double material_trans_withdraw = 49;
    // 加工入库
    double processing_deposit = 50;
    // 加工出库
    double processing_withdraw = 51;
    // 包装入库
    double packing_deposit = 52;
    // 包装出库
    double packing_withdraw = 53;
    // 退货在途
    double ret_transfer = 54;
    // 调拨在途
    double trans_transfer = 55;
    // 发货在途
    double trans_delivery = 56;
    // 采购在途
    double trans_purchase = 57;
    // 退货在途释放
    double trans_return_release = 58;
    // 调拨在途释放
    double trans_transfer_release = 59;
    // 发货在途释放
    double trans_delivery_release = 60;
    // 采购在途释放
    double trans_purchase_release = 61;
    // 在途期初库存
    double trans_begin = 62;
    // 在途期末库存
    double trans_end = 63;
    // 一级商品类别
    string category1 = 64;
    // 二级商品类别
    string category2 = 65;
    // 三级商品类别
    string category3 = 66;
    // 四级商品类别
    string category4 = 67;
    // 五级商品类别
    string category5 = 68;
    // 六级商品类别
    string category6 = 69;
    // 七级商品类别
    string category7 = 70;
    // 八级商品类别
    string category8 = 71;
    // 九级商品类别
    string category9 = 72;
    // 十级商品类别
    string category10 = 73;
}
