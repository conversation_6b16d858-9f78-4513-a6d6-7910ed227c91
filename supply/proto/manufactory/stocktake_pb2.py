# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: manufactory/stocktake.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='manufactory/stocktake.proto',
  package='manufactory',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1bmanufactory/stocktake.proto\x12\x0bmanufactory\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"p\n GetProductByStocktakeTypeRequest\x12\x16\n\x0estocktake_type\x18\x01 \x01(\t\x12\x11\n\tbranch_id\x18\x02 \x01(\x04\x12\x14\n\x0cstorage_type\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\"b\n\x14StocktakeProductType\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\n\n\x02id\x18\x02 \x01(\x04\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x0c\n\x04spec\x18\x04 \x01(\t\x12\x14\n\x0cstorage_type\x18\x05 \x01(\t\"T\n!GetProductByStocktakeTypeResponse\x12/\n\x04rows\x18\x01 \x03(\x0b\x32!.manufactory.StocktakeProductType\"9\n\x1aGetStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\x8a\n\n\tStocktake\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x17\n\x0f\x62ranch_batch_id\x18\x03 \x01(\x04\x12\x11\n\tbranch_id\x18\x04 \x01(\x04\x12\x13\n\x0bschedule_id\x18\x05 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x06 \x01(\t\x12\x1b\n\x13\x63\x61lculate_inventory\x18\x07 \x01(\x08\x12\x0c\n\x04\x63ode\x18\x08 \x01(\t\x12\x13\n\x0b\x66orecasting\x18\t \x01(\x08\x12\x18\n\x10\x66orecasting_time\x18\n \x01(\t\x12\x0e\n\x06remark\x18\x0b \x01(\t\x12\x13\n\x0bresult_type\x18\x0c \x01(\t\x12\x15\n\rschedule_code\x18\r \x01(\t\x12\x14\n\x0cst_diff_flag\x18\x0e \x01(\x05\x12-\n\x06status\x18\x0f \x01(\x0e\x32\x1d.manufactory.Stocktake.STATUS\x12\x35\n\x0eprocess_status\x18\x10 \x01(\x0e\x32\x1d.manufactory.Stocktake.STATUS\x12\x1a\n\x12store_secondary_id\x18\x11 \x01(\x04\x12\x0c\n\x04type\x18\x12 \x01(\t\x12\x12\n\ncreated_by\x18\x13 \x01(\x04\x12\x12\n\nupdated_by\x18\x14 \x01(\x04\x12\x11\n\treview_by\x18\x15 \x01(\x04\x12/\n\x0btarget_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07user_id\x18\x19 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1a \x01(\t\x12\x14\n\x0cupdated_name\x18\x1b \x01(\t\x12\x15\n\rschedule_name\x18\x1c \x01(\t\x12\x18\n\x10\x64iff_err_message\x18\x1d \x01(\t\x12\x19\n\x11month_err_message\x18\x1f \x01(\t\x12\x15\n\roriginal_code\x18# \x01(\t\x12\x17\n\x0foriginal_doc_id\x18$ \x01(\x04\x12\x13\n\x0bis_recreate\x18% \x01(\x08\x12\x15\n\rrecreate_code\x18& \x01(\t\x12\x17\n\x0frecreate_doc_id\x18\' \x01(\x04\x12\x13\n\x0bsubmit_name\x18( \x01(\t\x12\x14\n\x0c\x61pprove_name\x18) \x01(\t\x12\x16\n\x0estocktake_type\x18* \x01(\t\x12\x12\n\nrequest_id\x18+ \x01(\x04\x12\x14\n\x0ctotal_amount\x18\x32 \x01(\x01\x12\x19\n\x11total_diff_amount\x18\x33 \x01(\x01\x12\x1a\n\x12total_sales_amount\x18\x34 \x01(\x01\x12\x1f\n\x17total_diff_sales_amount\x18\x35 \x01(\x01\x12\x32\n\x0b\x61ttachments\x18\x36 \x03(\x0b\x32\x1d.manufactory.AttachmentsStock\"\x87\x01\n\x06STATUS\x12\x0b\n\x07STARTED\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tCANCELLED\x10\x02\x12\r\n\tSUBMITTED\x10\x03\x12\x0c\n\x08REJECTED\x10\x04\x12\x0c\n\x08\x41PPROVED\x10\x05\x12\r\n\tCONFIRMED\x10\x06\x12\x0c\n\x08SUBMIT_0\x10\x07\x12\r\n\tAPPROVE_0\x10\x08\"-\n\x10\x41ttachmentsStock\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\"\xc8\x06\n\x13GetStocktakeRequest\x12\x15\n\rinclude_total\x18\x01 \x01(\x08\x12\x11\n\tstore_ids\x18\x02 \x03(\x04\x12\x12\n\nbranch_ids\x18\x03 \x03(\x04\x12?\n\x0cstore_status\x18\x04 \x03(\x0e\x32).manufactory.GetStocktakeRequest.S_STATUS\x12\x36\n\x05_type\x18\x05 \x03(\x0e\x32\'.manufactory.GetStocktakeRequest.S_TYPE\x12\x37\n\x06status\x18\x06 \x03(\x0e\x32\'.manufactory.GetStocktakeRequest.STATUS\x12\x15\n\rschedule_code\x18\x07 \x01(\t\x12\x13\n\x0bproduct_ids\x18\x08 \x03(\x04\x12\x0e\n\x06offset\x18\t \x01(\r\x12\r\n\x05limit\x18\n \x01(\r\x12.\n\nstart_date\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0btarget_date\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x63ode\x18\x0e \x01(\t\x12\x0b\n\x03ids\x18\x15 \x03(\x04\x12\x11\n\tis_create\x18\x0f \x01(\x08\x12\x16\n\x0estocktake_type\x18\x10 \x01(\t\x12\r\n\x05order\x18\x11 \x01(\t\x12\x0c\n\x04sort\x18\x12 \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x13 \x01(\t\x12\x0b\n\x03lan\x18\x14 \x01(\t\x12\x15\n\rschedule_name\x18\x16 \x01(\t\"\"\n\x08S_STATUS\x12\n\n\x06OPENED\x10\x00\x12\n\n\x06\x43LOSED\x10\x01\"\x1d\n\x06S_TYPE\x12\x05\n\x01W\x10\x00\x12\x05\n\x01\x44\x10\x01\x12\x05\n\x01M\x10\x02\"\x87\x01\n\x06STATUS\x12\x0b\n\x07STARTED\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tCANCELLED\x10\x02\x12\r\n\tSUBMITTED\x10\x03\x12\x0c\n\x08REJECTED\x10\x04\x12\x0c\n\x08\x41PPROVED\x10\x05\x12\r\n\tCONFIRMED\x10\x06\x12\x0c\n\x08SUBMIT_0\x10\x07\x12\r\n\tAPPROVE_0\x10\x08\"K\n\x14GetStocktakeResponse\x12$\n\x04rows\x18\x01 \x03(\x0b\x32\x16.manufactory.Stocktake\x12\r\n\x05total\x18\x02 \x01(\r\"\xdf\x01\n\x1aGetStocktakeProductRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x14\n\x0cinclude_unit\x18\x02 \x01(\x08\x12\r\n\x05limit\x18\x03 \x01(\r\x12\x0e\n\x06offset\x18\x04 \x01(\r\x12\x15\n\rinclude_total\x18\x05 \x01(\x08\x12\x13\n\x0b\x63\x61tegory_id\x18\x06 \x01(\x04\x12\x14\n\x0cstorage_type\x18\x07 \x01(\t\x12\x14\n\x0cproduct_name\x18\x08 \x01(\t\x12\x0b\n\x03lan\x18\t \x01(\t\x12\x17\n\x0finclude_barcode\x18\n \x01(\x08\"\xf7\x04\n\x17StocktakeProductTagName\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06\x64oc_id\x18\x02 \x01(\x04\x12\x0e\n\x06stp_id\x18\x03 \x01(\x04\x12\x12\n\nproduct_id\x18\x04 \x01(\x04\x12\x0e\n\x06tag_id\x18\x05 \x01(\x04\x12\x10\n\x08tag_name\x18\x06 \x01(\t\x12\x14\n\x0ctag_quantity\x18\x07 \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x08 \x01(\x01\x12\x0f\n\x07unit_id\x18\t \x01(\x04\x12\x11\n\tunit_spec\x18\n \x01(\t\x12\x11\n\tunit_name\x18\x0b \x01(\t\x12\x11\n\tunit_rate\x18\x0c \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\r \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x0e \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x0f \x01(\t\x12\x12\n\npartner_id\x18\x10 \x01(\x04\x12\x0f\n\x07user_id\x18\x11 \x01(\x04\x12\x12\n\ncreated_by\x18\x12 \x01(\x04\x12\x12\n\nupdated_by\x18\x13 \x01(\x04\x12.\n\ncreated_at\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\x16 \x01(\t\x12\x14\n\x0cupdated_name\x18\x17 \x01(\t\x12\x10\n\x08tax_rate\x18\x18 \x01(\x01\x12\x11\n\ttax_price\x18\x19 \x01(\x01\x12\x12\n\ncost_price\x18\x1a \x01(\x01\x12\x13\n\x0bsales_price\x18\x1b \x01(\x01\"\xd3\x01\n\x15StocktakeProductUnits\x12\x0f\n\x07unit_id\x18\x01 \x01(\x04\x12\x11\n\tunit_name\x18\x02 \x01(\t\x12\x11\n\tunit_spec\x18\x03 \x01(\t\x12\x11\n\tunit_rate\x18\x04 \x01(\x01\x12\x10\n\x08tax_rate\x18\x05 \x01(\x01\x12\x11\n\ttax_price\x18\x06 \x01(\x01\x12\x12\n\ncost_price\x18\x07 \x01(\x01\x12\x13\n\x0bsales_price\x18\x08 \x01(\x01\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\t \x01(\x08\x12\x11\n\tstocktake\x18\n \x01(\x08\"\x9e\x0b\n\x10StocktakeProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x0e\n\x06\x64oc_id\x18\x03 \x01(\x04\x12\x11\n\tbranch_id\x18\x04 \x01(\x04\x12\x12\n\nproduct_id\x18\x05 \x01(\x04\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x06 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x07 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x08 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\t \x01(\t\x12\x0c\n\x04\x63ode\x18\n \x01(\t\x12\x0f\n\x07\x64\x65leted\x18\x0b \x01(\x08\x12\x15\n\rdiff_quantity\x18\x0c \x01(\x01\x12\x15\n\rdisplay_order\x18\r \x01(\t\x12\x0f\n\x07\x65xtends\x18\x0e \x01(\t\x12\x0f\n\x07ignored\x18\x0f \x01(\x08\x12\x1a\n\x12inventory_quantity\x18\x10 \x01(\x01\x12\x11\n\tis_system\x18\x11 \x01(\x08\x12\x13\n\x0bitem_number\x18\x12 \x01(\x04\x12\x17\n\x0fmaterial_number\x18\x13 \x01(\t\x12\x14\n\x0cproduct_code\x18\x14 \x01(\t\x12\x14\n\x0cproduct_name\x18\x15 \x01(\t\x12\x10\n\x08quantity\x18\x16 \x01(\x01\x12\x0f\n\x07st_type\x18\x17 \x01(\t\x12\x14\n\x0cstorage_type\x18\x18 \x01(\t\x12\x1a\n\x12unit_diff_quantity\x18\x19 \x01(\x01\x12\x0f\n\x07unit_id\x18\x1a \x01(\x04\x12\x11\n\tunit_name\x18\x1b \x01(\t\x12\x11\n\tunit_rate\x18\x1c \x01(\x01\x12\x11\n\tunit_spec\x18\x1d \x01(\t\x12\x31\n\x05units\x18\x1e \x03(\x0b\x32\".manufactory.StocktakeProductUnits\x12\x12\n\ncreated_by\x18\x1f \x01(\x04\x12\x12\n\nupdated_by\x18  \x01(\x04\x12/\n\x0btarget_date\x18! \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\" \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18# \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07user_id\x18$ \x01(\x04\x12\x0e\n\x06status\x18% \x01(\t\x12\x0e\n\x06is_pda\x18& \x01(\x08\x12:\n\x0cproduct_tags\x18\' \x03(\x0b\x32$.manufactory.StocktakeProductTagName\x12\x10\n\x08is_empty\x18( \x01(\x08\x12\x14\n\x0c\x63reated_name\x18) \x01(\t\x12\x14\n\x0cupdated_name\x18* \x01(\t\x12\x0f\n\x07is_null\x18+ \x01(\x08\x12\x14\n\x0ctag_quantity\x18, \x01(\x01\x12#\n\x1b\x63onvert_accounting_quantity\x18- \x01(\x01\x12\x0e\n\x06is_bom\x18. \x01(\x08\x12\x1c\n\x14\x61llow_stocktake_edit\x18/ \x01(\x08\x12\x0c\n\x04spec\x18\x30 \x01(\t\x12\x12\n\ndiff_price\x18\x31 \x01(\x01\x12\x0f\n\x07\x62\x61rcode\x18\x32 \x03(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x36 \x01(\x04\x12\x13\n\x0bposition_id\x18\x37 \x01(\x04\x12\x15\n\rcategory_name\x18\x38 \x01(\t\x12\x10\n\x08tax_rate\x18\x39 \x01(\x01\x12\x11\n\ttax_price\x18: \x01(\x01\x12\x0e\n\x06\x61mount\x18; \x01(\x01\x12\x13\n\x0b\x64iff_amount\x18< \x01(\x01\x12\x12\n\ncost_price\x18= \x01(\x01\x12\x14\n\x0csales_amount\x18> \x01(\x01\x12\x13\n\x0bsales_price\x18? \x01(\x01\x12\x19\n\x11\x64iff_sales_amount\x18@ \x01(\x01\"\x98\x01\n\x1bGetStocktakeProductResponse\x12+\n\x04rows\x18\x01 \x03(\x0b\x32\x1d.manufactory.StocktakeProduct\x12\r\n\x05total\x18\x02 \x01(\x04\x12=\n\rposition_rows\x18\x03 \x03(\x0b\x32&.manufactory.StocktakePositionProducts\"\x90\x01\n\x1aUserCreateStocktakeRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x11\n\tbranch_id\x18\x02 \x01(\x04\x12\r\n\x05_type\x18\x03 \x01(\t\x12/\n\x0btarget_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0b\n\x03lan\x18\x05 \x01(\t\"\x9f\x06\n\x1bUserCreateStocktakeResponse\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x17\n\x0f\x62ranch_batch_id\x18\x03 \x01(\x04\x12\x11\n\tbranch_id\x18\x04 \x01(\x04\x12\x13\n\x0bschedule_id\x18\x05 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x06 \x01(\t\x12\x1b\n\x13\x63\x61lculate_inventory\x18\x07 \x01(\x08\x12\x0c\n\x04\x63ode\x18\x08 \x01(\t\x12\x13\n\x0b\x66orecasting\x18\t \x01(\x08\x12\x18\n\x10\x66orecasting_time\x18\n \x01(\t\x12\x0e\n\x06remark\x18\x0b \x01(\t\x12\x13\n\x0bresult_type\x18\x0c \x01(\t\x12\x15\n\rschedule_code\x18\r \x01(\t\x12\x14\n\x0cst_diff_flag\x18\x0e \x01(\x05\x12?\n\x06status\x18\x0f \x01(\x0e\x32/.manufactory.UserCreateStocktakeResponse.STATUS\x12G\n\x0eprocess_status\x18\x10 \x01(\x0e\x32/.manufactory.UserCreateStocktakeResponse.STATUS\x12\x1a\n\x12store_secondary_id\x18\x11 \x01(\x04\x12\x0c\n\x04type\x18\x12 \x01(\t\x12\x12\n\ncreated_by\x18\x13 \x01(\x04\x12\x12\n\nupdated_by\x18\x14 \x01(\x04\x12\x11\n\treview_by\x18\x15 \x01(\x04\x12/\n\x0btarget_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07user_id\x18\x19 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1a \x01(\t\x12\x14\n\x0cupdated_name\x18\x1b \x01(\t\"!\n\x06STATUS\x12\x0b\n\x07STARTED\x10\x00\x12\n\n\x06INITED\x10\x01\"b\n\x0bTagQuantity\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06tag_id\x18\x02 \x01(\x04\x12\x10\n\x08tag_name\x18\x03 \x01(\t\x12\x14\n\x0ctag_quantity\x18\x04 \x01(\x01\x12\x0f\n\x07unit_id\x18\x05 \x01(\x04\"\xbd\x01\n\x14PutStocktakeProducts\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08quantity\x18\x02 \x01(\x01\x12\x0f\n\x07unit_id\x18\x03 \x01(\x04\x12.\n\x0ctag_products\x18\x04 \x03(\x0b\x32\x18.manufactory.TagQuantity\x12\x0e\n\x06is_pda\x18\x05 \x01(\x08\x12\x10\n\x08is_empty\x18\x06 \x01(\x08\x12\x0f\n\x07is_null\x18\x07 \x01(\x08\x12\x13\n\x0b\x64\x65l_tag_ids\x18\x08 \x03(\x04\"\x80\x01\n\x1aPutStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x33\n\x08products\x18\x02 \x03(\x0b\x32!.manufactory.PutStocktakeProducts\x12\x0b\n\x03lan\x18\x03 \x01(\t\x12\x10\n\x08\x61ll_zero\x18\x04 \x01(\x08\"L\n\x1dRejectStocktakeProductRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x0e\n\x06reason\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\"0\n\x1eRejectStocktakeProductResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"-\n\x1bPutStocktakeByDocIDResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"_\n\x1c\x43heckStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\r\n\x05\x63heck\x18\x02 \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\"G\n\x1b\x43heckStocktakeByDocIDDetail\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\"\xe4\x05\n\x1d\x43heckStocktakeByDocIDResponse\x12\x0f\n\x07handler\x18\x01 \x01(\x08\x12\x38\n\x06\x61\x64just\x18\x02 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12@\n\x0ereceiving_diff\x18\x03 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12:\n\x08transfer\x18\x04 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12;\n\treceiving\x18\x05 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12\x38\n\x06return\x18\x06 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12\x42\n\x10\x64irect_receiving\x18\x07 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12G\n\x15\x64irect_receiving_diff\x18\x08 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12?\n\rdirect_return\x18\t \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12;\n\tstocktake\x18\n \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12\x38\n\x06\x64\x65mand\x18\x0b \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12>\n\x0cself_picking\x18\x0c \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\"L\n\x1e\x43onfirmStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\r\n\x05\x63heck\x18\x02 \x01(\x08\x12\x0b\n\x03lan\x18\x03 \x01(\t\"0\n\x1eSubmitStocktakeByDocIDResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"c\n\x1dSubmitStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x13\n\x0bsubmit_name\x18\x02 \x01(\t\x12\x0b\n\x03lan\x18\x03 \x01(\t\x12\x10\n\x08\x61ll_zero\x18\x04 \x01(\x08\"s\n\x1e\x41pproveStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x0c\n\x04type\x18\x02 \x01(\t\x12\x14\n\x0c\x61pprove_name\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\x12\x10\n\x08\x61ll_zero\x18\x05 \x01(\x08\"\xff\x04\n\x1f\x41pproveStocktakeByDocIDResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x0f\n\x07handler\x18\x02 \x01(\x08\x12\x38\n\x06\x61\x64just\x18\x03 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12@\n\x0ereceiving_diff\x18\x04 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12:\n\x08transfer\x18\x05 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12;\n\treceiving\x18\x06 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12\x38\n\x06return\x18\x07 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12\x42\n\x10\x64irect_receiving\x18\x08 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12G\n\x15\x64irect_receiving_diff\x18\t \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12?\n\rdirect_return\x18\n \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12>\n\x0cself_picking\x18\x0c \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\"1\n\x1f\x43onfirmStocktakeByDocIDResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"<\n\x1d\x43\x61ncelStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"0\n\x1e\x43\x61ncelStocktakeByDocIDResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"p\n\x1b\x43reateStocktakeBatchRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x30\n\x0crequest_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0b\n\x03lan\x18\x03 \x01(\t\"x\n\x1c\x43reateStocktakeBatchResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x16\n\x0eschedule_count\x18\x02 \x01(\r\x12\x30\n\x0crequest_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"k\n\x16\x43reateStocktakeRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x30\n\x0crequest_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0b\n\x03lan\x18\x03 \x01(\t\"[\n\x17\x43reateStocktakeResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x30\n\x0crequest_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"E\n\x1e\x43heckedStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x02 \x01(\t\"1\n\x1f\x43heckedStocktakeByDocIDResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"_\n\x17GetStocktakeTagsRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\x12\x10\n\x08tag_name\x18\x03 \x01(\t\x12\x12\n\nbranch_ids\x18\x04 \x03(\x04\"\xaa\x02\n\rStocktakeTags\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x12\n\npartner_id\x18\x03 \x01(\x04\x12\x0f\n\x07user_id\x18\x04 \x01(\x04\x12\x12\n\ncreated_by\x18\x05 \x01(\x04\x12\x12\n\nupdated_by\x18\x06 \x01(\x04\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\t \x01(\t\x12\x14\n\x0cupdated_name\x18\n \x01(\t\x12\x11\n\tbranch_id\x18\x0b \x01(\x04\x12\x13\n\x0b\x62ranch_name\x18\x0c \x01(\t\"D\n\x18GetStocktakeTagsResponse\x12(\n\x04rows\x18\x01 \x03(\x0b\x32\x1a.manufactory.StocktakeTags\"\xd5\x02\n\x1a\x41\x63tionStocktakeTagsRequest\x12\x0e\n\x06tag_id\x18\x01 \x01(\x04\x12>\n\x06\x61\x63tion\x18\x02 \x01(\x0e\x32..manufactory.ActionStocktakeTagsRequest.Action\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x11\n\tbranch_id\x18\x04 \x01(\x04\x12\x0b\n\x03lan\x18\x05 \x01(\t\x12\x12\n\nbranch_ids\x18\x06 \x03(\x04\x12\x12\n\nregion_ids\x18\x07 \x03(\x04\x12\x15\n\radd_dimension\x18\x08 \x01(\t\x12\x0f\n\x07tag_ids\x18\t \x03(\x04\x12\x13\n\x0borigin_name\x18\n \x01(\t\x12\x13\n\x0b\x63opy_branch\x18\x0b \x01(\x04\"?\n\x06\x41\x63tion\x12\x07\n\x03get\x10\x00\x12\n\n\x06\x63reate\x10\x01\x12\n\n\x06\x64\x65lete\x10\x02\x12\n\n\x06update\x10\x03\x12\x08\n\x04\x63opy\x10\x04\"\xb3\x02\n\x1b\x41\x63tionStocktakeTagsResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\n\n\x02id\x18\x02 \x01(\x04\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x12\n\npartner_id\x18\x04 \x01(\x04\x12\x0f\n\x07user_id\x18\x05 \x01(\x04\x12\x12\n\ncreated_by\x18\x06 \x01(\x04\x12\x12\n\nupdated_by\x18\x07 \x01(\x04\x12.\n\ncreated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\n \x01(\t\x12\x14\n\x0cupdated_name\x18\x0b \x01(\t\x12\x11\n\tbranch_id\x18\x0c \x01(\x04\"-\n\x1bGetStocktakeTagsByIdRequest\x12\x0e\n\x06tag_id\x18\x01 \x01(\x04\"/\n!DeleteStocktakeProductTagsRequest\x12\n\n\x02id\x18\x01 \x03(\x04\"4\n\"DeleteStocktakeProductTagsResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\xf9\x02\n\x1aGetStocktakeBalanceRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tstore_ids\x18\x03 \x03(\x04\x12\r\n\x05limit\x18\x04 \x01(\r\x12\x0e\n\x06offset\x18\x05 \x01(\r\x12/\n\x0btarget_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x07 \x03(\t\x12\x15\n\rinclude_total\x18\x08 \x01(\x08\x12\x15\n\rschedule_code\x18\t \x01(\t\x12\x16\n\x0estocktake_type\x18\n \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0b \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x0c \x01(\t\x12\x0b\n\x03lan\x18\r \x01(\t\x12\x0c\n\x04type\x18\x35 \x01(\t\"\x95\t\n\x10StocktakeBalance\x12\x17\n\x0f\x62ranch_batch_id\x18\x01 \x01(\x04\x12\x11\n\tbranch_id\x18\x02 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x03 \x01(\t\x12\x1b\n\x13\x63\x61lculate_inventory\x18\x04 \x01(\x08\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x13\n\x0b\x66orecasting\x18\x06 \x01(\x08\x12\x34\n\x10\x66orecasting_time\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\n\n\x02id\x18\x08 \x01(\x04\x12\x16\n\x0eprocess_status\x18\t \x01(\t\x12\x0e\n\x06remark\x18\n \x01(\t\x12\x13\n\x0bresult_type\x18\x0b \x01(\t\x12\x11\n\treview_by\x18\x0c \x01(\x04\x12\x15\n\rschedule_code\x18\r \x01(\t\x12\x13\n\x0bschedule_id\x18\x0e \x01(\x04\x12\x14\n\x0cst_diff_flag\x18\x0f \x01(\x05\x12\x0e\n\x06status\x18\x10 \x01(\t\x12\x1a\n\x12store_secondary_id\x18\x11 \x01(\x04\x12/\n\x0btarget_date\x18\x12 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04type\x18\x13 \x01(\t\x12\x12\n\npartner_id\x18\x14 \x01(\x04\x12\x0f\n\x07user_id\x18\x15 \x01(\x04\x12\x12\n\ncreated_by\x18\x16 \x01(\x04\x12\x12\n\nupdated_by\x18\x17 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x18 \x01(\t\x12\x14\n\x0cupdated_name\x18\x19 \x01(\t\x12.\n\ncreated_at\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rschedule_name\x18\x1c \x01(\t\x12\x18\n\x10\x64iff_err_message\x18\x1d \x01(\t\x12\x19\n\x11month_err_message\x18\x1f \x01(\t\x12\x13\n\x0b\x62ranch_name\x18# \x01(\t\x12\x13\n\x0b\x62ranch_code\x18$ \x01(\t\x12\x15\n\roriginal_code\x18% \x01(\t\x12\x17\n\x0foriginal_doc_id\x18& \x01(\x04\x12\x13\n\x0bis_recreate\x18\' \x01(\x08\x12\x15\n\rrecreate_code\x18( \x01(\t\x12\x17\n\x0frecreate_doc_id\x18) \x01(\x04\x12\x13\n\x0bsubmit_name\x18* \x01(\t\x12\x14\n\x0c\x61pprove_name\x18+ \x01(\t\x12\x16\n\x0estocktake_type\x18, \x01(\t\x12\x12\n\nrequest_id\x18- \x01(\x04\x12\x14\n\x0ctotal_amount\x18. \x01(\x01\x12\x19\n\x11total_diff_amount\x18/ \x01(\x01\x12\x1a\n\x12total_sales_amount\x18\x30 \x01(\x01\x12\x1f\n\x17total_diff_sales_amount\x18\x31 \x01(\x01\x12\x38\n\x0b\x61ttachments\x18\x32 \x03(\x0b\x32#.manufactory.AttachmentsManuBalance\"3\n\x16\x41ttachmentsManuBalance\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\"Y\n\x1bGetStocktakeBalanceResponse\x12+\n\x04rows\x18\x01 \x03(\x0b\x32\x1d.manufactory.StocktakeBalance\x12\r\n\x05total\x18\x02 \x01(\x04\"E\n&GetStocktakeBalanceProductGroupRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"d\n\x0cTagProductBi\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12\x14\n\x0ctag_quantity\x18\x02 \x01(\x01\x12\x15\n\rtag_unit_name\x18\x03 \x01(\t\x12\x15\n\rtag_uint_rate\x18\x04 \x01(\x01\"\xa0\x05\n\x1cStocktakeBalanceProductGroup\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x04 \x01(\t\x12\x15\n\rdiff_quantity\x18\x05 \x01(\x01\x12\x1a\n\x12inventory_quantity\x18\x06 \x01(\x01\x12\x11\n\tis_system\x18\x07 \x01(\x08\x12\x17\n\x0fmaterial_number\x18\x08 \x01(\t\x12\x14\n\x0cproduct_code\x18\t \x01(\t\x12\x12\n\nproduct_id\x18\n \x01(\x04\x12\x14\n\x0cproduct_name\x18\x0b \x01(\t\x12\x10\n\x08quantity\x18\x0c \x01(\x01\x12\x14\n\x0cstorage_type\x18\r \x01(\t\x12\x10\n\x08tag_code\x18\x0e \x01(\t\x12\x10\n\x08tag_name\x18\x0f \x01(\t\x12\x1a\n\x12unit_diff_quantity\x18\x10 \x01(\x01\x12\x0f\n\x07unit_id\x18\x11 \x01(\x04\x12\x11\n\tunit_name\x18\x12 \x01(\t\x12\x11\n\tunit_rate\x18\x13 \x01(\x01\x12\x11\n\tunit_spec\x18\x14 \x01(\t\x12.\n\x0btag_details\x18\x15 \x03(\x0b\x32\x19.manufactory.TagProductBi\x12%\n\x1d\x61\x63\x63ounting_inventory_quantity\x18\x16 \x01(\x01\x12 \n\x18\x61\x63\x63ounting_diff_quantity\x18\x17 \x01(\x01\x12\x13\n\x0bposition_id\x18\x19 \x01(\x04\x12\x15\n\rposition_code\x18\x1a \x01(\t\x12\x15\n\rposition_name\x18\x1b \x01(\t\"b\n\'GetStocktakeBalanceProductGroupResponse\x12\x37\n\x04rows\x18\x01 \x03(\x0b\x32).manufactory.StocktakeBalanceProductGroup\"\xe2\x02\n\x1aStocktakeBiDetailedRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63\x61tegory_ids\x18\x03 \x03(\x04\x12\x0c\n\x04type\x18\x04 \x01(\t\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12\r\n\x05limit\x18\x07 \x01(\r\x12\x15\n\rinclude_total\x18\x08 \x01(\x08\x12\x14\n\x0cproduct_name\x18\t \x01(\t\x12\x11\n\tstore_ids\x18\n \x03(\x04\x12\r\n\x05order\x18\x0b \x01(\t\x12\x0c\n\x04sort\x18\x0c \x01(\t\x12\x0c\n\x04\x63ode\x18\r \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x10 \x01(\t\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x16\n\x0estocktake_type\x18\x12 \x01(\t\"\x93\x0c\n\x13StocktakeBiDetailed\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x04 \x01(\t\x12\x15\n\rbranch_1_name\x18\x05 \x01(\t\x12\x15\n\rbranch_2_name\x18\x06 \x01(\t\x12\x11\n\tbranch_id\x18\x07 \x01(\x04\x12\x15\n\rcategory_code\x18\x08 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\t \x01(\x04\x12\x15\n\rcategory_name\x18\n \x01(\t\x12\x0c\n\x04\x63ode\x18\x0b \x01(\t\x12\x0f\n\x07\x64\x65leted\x18\x0c \x01(\x08\x12\x15\n\rdiff_quantity\x18\r \x01(\x01\x12\x15\n\rdisplay_order\x18\x0e \x01(\t\x12\x0e\n\x06\x64oc_id\x18\x0f \x01(\x04\x12\x0f\n\x07\x65xtends\x18\x10 \x01(\t\x12\n\n\x02id\x18\x11 \x01(\x04\x12\x0f\n\x07ignored\x18\x12 \x01(\x08\x12\x1a\n\x12inventory_quantity\x18\x13 \x01(\x01\x12\x11\n\tis_system\x18\x14 \x01(\x08\x12\x13\n\x0bitem_number\x18\x15 \x01(\x04\x12\x17\n\x0fmaterial_number\x18\x16 \x01(\t\x12\x14\n\x0cproduct_code\x18\x17 \x01(\t\x12\x12\n\nproduct_id\x18\x18 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x19 \x01(\t\x12\x10\n\x08quantity\x18\x1a \x01(\x01\x12\x0c\n\x04type\x18\x1b \x01(\t\x12\x14\n\x0cstorage_type\x18\x1c \x01(\t\x12\x12\n\nstore_code\x18\x1d \x01(\t\x12\x12\n\nstore_name\x18\x1e \x01(\t\x12/\n\x0btarget_date\x18\x1f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1a\n\x12unit_diff_quantity\x18  \x01(\x01\x12\x0f\n\x07unit_id\x18! \x01(\x04\x12\x11\n\tunit_name\x18\" \x01(\t\x12\x11\n\tunit_rate\x18# \x01(\x01\x12\x11\n\tunit_spec\x18$ \x01(\t\x12\x12\n\npartner_id\x18% \x01(\x04\x12\x12\n\ncreated_by\x18& \x01(\x04\x12\x12\n\nupdated_by\x18\' \x01(\x04\x12\x14\n\x0c\x63reated_name\x18( \x01(\t\x12\x14\n\x0cupdated_name\x18) \x01(\t\x12.\n\ncreated_at\x18* \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18+ \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x10\n\x08is_empty\x18, \x01(\x08\x12\x0e\n\x06is_pda\x18- \x01(\x08\x12\x0e\n\x06status\x18. \x01(\t\x12\r\n\x05units\x18/ \x01(\t\x12\x0f\n\x07user_id\x18\x30 \x01(\x04\x12\x0f\n\x07is_null\x18\x31 \x01(\x08\x12\x14\n\x0ctag_quantity\x18\x32 \x01(\x01\x12/\n\x0cproduct_tags\x18\x33 \x03(\x0b\x32\x19.manufactory.ProductTagBi\x12\x11\n\tis_enable\x18\x34 \x01(\x08\x12\x0c\n\x04spec\x18\x36 \x01(\t\x12\x17\n\x0f\x64iff_percentage\x18\x37 \x01(\x01\x12\x30\n\x0c\x63reated_time\x18\x38 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x19\n\x11\x63reated_user_name\x18\x39 \x01(\t\x12\x32\n\x0esubmitted_time\x18: \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1b\n\x13submitted_user_name\x18; \x01(\t\x12\x13\n\x0b\x62ranch_type\x18< \x01(\t\x12\x13\n\x0bposition_id\x18> \x01(\x04\x12\x15\n\rposition_code\x18? \x01(\t\x12\x15\n\rposition_name\x18@ \x01(\t\x12\x16\n\x0estocktake_type\x18\x35 \x01(\t\x12\x15\n\rschedule_code\x18\x41 \x01(\t\x12\x15\n\rschedule_name\x18\x42 \x01(\t\"\xc5\x01\n\x0cProductTagBi\x12\x13\n\x0btag_unit_id\x18\x01 \x01(\x04\x12\x15\n\rtag_unit_name\x18\x02 \x01(\t\x12\x14\n\x0ctag_quantity\x18\x03 \x01(\x01\x12\x10\n\x08tag_name\x18\x04 \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x05 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x06 \x01(\t\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x07 \x01(\x01\x12\n\n\x02id\x18\x08 \x01(\x04\"P\n\x08ST_total\x12\r\n\x05\x63ount\x18\x01 \x01(\x04\x12\x14\n\x0csum_quantity\x18\x02 \x01(\x01\x12\x1f\n\x17sum_accounting_quantity\x18\x03 \x01(\x01\"s\n\x1bStocktakeBiDetailedResponse\x12.\n\x04rows\x18\x01 \x03(\x0b\x32 .manufactory.StocktakeBiDetailed\x12$\n\x05total\x18\x02 \x01(\x0b\x32\x15.manufactory.ST_total\"\x99\x02\n\x1dStocktakeBalanceRegionRequest\x12\x15\n\rinclude_total\x18\x01 \x01(\x08\x12\r\n\x05limit\x18\x02 \x01(\r\x12\x0e\n\x06offset\x18\x03 \x01(\r\x12)\n\x05start\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tregion_id\x18\x06 \x01(\x04\x12\x13\n\x0bproduct_ids\x18\x07 \x03(\x04\x12\x0c\n\x04type\x18\x08 \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\t \x01(\t\x12\x0b\n\x03lan\x18\n \x01(\t\x12\x16\n\x0estocktake_type\x18\x0b \x01(\t\"\xc2\x03\n\x1dStocktakeBalanceRegionDetails\x12%\n\x1d\x61\x63\x63ounting_inventory_quantity\x18\x01 \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x02 \x01(\x01\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x13\n\x0b\x62ranch_name\x18\x04 \x01(\t\x12\x15\n\rdiff_quantity\x18\x05 \x01(\x01\x12\x10\n\x08\x64oc_type\x18\x06 \x01(\t\x12\x1a\n\x12inventory_quantity\x18\x07 \x01(\x01\x12\x17\n\x0fmaterial_number\x18\x08 \x01(\t\x12\x12\n\nproduct_id\x18\t \x01(\x04\x12\x14\n\x0cproduct_name\x18\n \x01(\t\x12\x10\n\x08quantity\x18\x0b \x01(\x01\x12\x10\n\x08store_id\x18\x0c \x01(\x04\x12\x12\n\nstore_name\x18\r \x01(\t\x12\r\n\x05tag_1\x18\x0e \x01(\x04\x12\x19\n\x11tag_1_name_pinyin\x18\x0f \x01(\t\x12\x1a\n\x12unit_diff_quantity\x18\x10 \x01(\x01\x12\x11\n\tunit_name\x18\x11 \x01(\t\x12\x11\n\tunit_rate\x18\x12 \x01(\x01\"\xa0\x04\n\x16StocktakeBalanceRegion\x12%\n\x1d\x61\x63\x63ounting_inventory_quantity\x18\x01 \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x02 \x01(\x01\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12;\n\x07\x64\x65tails\x18\x04 \x03(\x0b\x32*.manufactory.StocktakeBalanceRegionDetails\x12\x11\n\tunit_name\x18\x05 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x10\n\x08quantity\x18\x08 \x01(\x01\x12\x1a\n\x12inventory_quantity\x18\t \x01(\x01\x12\x1e\n\x16\x61\x63\x63oinventory_quantity\x18\n \x01(\x01\x12\x15\n\rdiff_quantity\x18\x0b \x01(\x01\x12\x1a\n\x12unit_diff_quantity\x18\x0c \x01(\x01\x12\x11\n\tunit_rate\x18\r \x01(\x01\x12\x0c\n\x04type\x18\x0e \x01(\t\x12\x12\n\nproduct_id\x18\x0f \x01(\x04\x12\x10\n\x08store_id\x18\x10 \x01(\x04\x12\x12\n\nstore_name\x18\x11 \x01(\t\x12\x12\n\nstore_code\x18\x12 \x01(\t\x12\x14\n\x0cproduct_code\x18\x13 \x01(\t\x12 \n\x18\x61\x63\x63ounting_diff_quantity\x18\x14 \x01(\x01\x12\x16\n\x0estocktake_type\x18\x15 \x01(\t\"b\n\x1eStocktakeBalanceRegionResponse\x12\x31\n\x04rows\x18\x01 \x03(\x0b\x32#.manufactory.StocktakeBalanceRegion\x12\r\n\x05total\x18\x02 \x01(\x04\"\xb9\x01\n\x15StoreDataScopeRequest\x12\x0e\n\x06search\x18\x01 \x01(\t\x12\x15\n\rsearch_fields\x18\x02 \x01(\t\x12\x0b\n\x03ids\x18\x03 \x03(\x04\x12\x15\n\rreturn_fields\x18\x04 \x01(\t\x12\x0f\n\x07\x66ilters\x18\x05 \x01(\t\x12\x18\n\x10relation_filters\x18\x06 \x01(\t\x12\r\n\x05limit\x18\x07 \x01(\x03\x12\x0e\n\x06offset\x18\x08 \x01(\x04\x12\x0b\n\x03lan\x18\t \x01(\t\"\x9f\x03\n\x0bScopeStores\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x13\n\x0bsecond_code\x18\x04 \x01(\t\x12\x0c\n\x04type\x18\x05 \x01(\t\x12\x0f\n\x07\x61\x64\x64ress\x18\x06 \x01(\t\x12\x0b\n\x03tel\x18\x07 \x01(\t\x12\x0f\n\x07\x63ontact\x18\x08 \x01(\t\x12\x0e\n\x06status\x18\t \x01(\t\x12\x0f\n\x07name_en\x18\n \x01(\t\x12\x11\n\topen_date\x18\x0b \x01(\t\x12\x12\n\nclose_date\x18\x0c \x01(\t\x12\r\n\x05\x65mail\x18\r \x01(\t\x12\x12\n\ngeo_region\x18\x0e \x03(\t\x12\x15\n\rbranch_region\x18\x0f \x03(\t\x12\x14\n\x0corder_region\x18\x10 \x03(\t\x12\x1b\n\x13\x64istribution_region\x18\x11 \x03(\t\x12\x17\n\x0fpurchase_region\x18\x12 \x03(\t\x12\x15\n\rmarket_region\x18\x13 \x03(\t\x12\x17\n\x0ftransfer_region\x18\x14 \x03(\t\x12\x18\n\x10\x61ttribute_region\x18\x15 \x03(\t\"G\n\x0eStoreDataScope\x12&\n\x04rows\x18\x01 \x03(\x0b\x32\x18.manufactory.ScopeStores\x12\r\n\x05total\x18\x02 \x01(\x04\":\n\x1b\x41\x64vanceStocktakeDiffRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\x99\x01\n\x1c\x41\x64vanceStocktakeDiffResponse\x12+\n\x04rows\x18\x01 \x03(\x0b\x32\x1d.manufactory.StocktakeProduct\x12\r\n\x05total\x18\x02 \x01(\x04\x12=\n\rposition_rows\x18\x03 \x03(\x0b\x32&.manufactory.StocktakePositionProducts\"\xa9\x03\n\x1aStocktakeDiffReportRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tstore_ids\x18\x03 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x04 \x03(\x04\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12/\n\x0btarget_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x63ode\x18\x08 \x01(\t\x12\x16\n\x0estocktake_type\x18\t \x01(\t\x12\x15\n\rschedule_code\x18\x0b \x01(\t\x12\x0e\n\x06status\x18\x0c \x01(\t\x12\r\n\x05order\x18\r \x01(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\x13\n\x0bupper_limit\x18\x0f \x01(\t\x12\x13\n\x0blower_limit\x18\x10 \x01(\t\x12\x14\n\x0cis_wms_store\x18\x11 \x01(\x08\x12\x0b\n\x03lan\x18\x12 \x01(\t\"\xb4\x05\n\x16StocktakeDiffReportRow\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_code\x18\x03 \x01(\t\x12\x12\n\nstore_name\x18\x04 \x01(\t\x12\x14\n\x0c\x63ompany_code\x18\x05 \x01(\t\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x12\n\nproduct_id\x18\x07 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x08 \x01(\t\x12\x0c\n\x04type\x18\t \x01(\t\x12\x15\n\rdiff_quantity\x18\n \x01(\x01\x12\x10\n\x08quantity\x18\x0b \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x0c \x01(\x01\x12\x1a\n\x12inventory_quantity\x18\r \x01(\x01\x12 \n\x18\x64iff_quantity_percentage\x18\x0e \x01(\x01\x12/\n\x0btarget_date\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07unit_id\x18\x10 \x01(\x04\x12\x11\n\tunit_name\x18\x11 \x01(\t\x12\x11\n\tunit_rate\x18\x12 \x01(\x01\x12\x11\n\tunit_code\x18\x13 \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x14 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x15 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\x16 \x01(\t\x12\x14\n\x0c\x63ompany_name\x18\x18 \x01(\t\x12%\n\x1d\x61\x63\x63ounting_inventory_quantity\x18\x19 \x01(\x01\x12 \n\x18\x61\x63\x63ounting_diff_quantity\x18\x1a \x01(\x01\x12\x11\n\tunit_spec\x18\x1b \x01(\t\x12\x0e\n\x06status\x18\x1c \x01(\t\x12\x15\n\rschedule_code\x18\x1d \x01(\t\"_\n\x1bStocktakeDiffReportResponse\x12\x31\n\x04rows\x18\x01 \x03(\x0b\x32#.manufactory.StocktakeDiffReportRow\x12\r\n\x05total\x18\x02 \x01(\x04\"\x96\x01\n\x17GetUncompleteDocRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x10\n\x08store_id\x18\x03 \x01(\x04\x12\x0b\n\x03lan\x18\x04 \x01(\t\"L\n\x11\x43heckDemandDetail\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x0f\n\x07is_plan\x18\x04 \x01(\x08\"\xcf\x05\n\x18GetUncompleteDocResponse\x12\x0f\n\x07handler\x18\x01 \x01(\x08\x12\x38\n\x06\x61\x64just\x18\x02 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12@\n\x0ereceiving_diff\x18\x03 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12:\n\x08transfer\x18\x04 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12;\n\treceiving\x18\x05 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12\x38\n\x06return\x18\x06 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12\x42\n\x10\x64irect_receiving\x18\x07 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12G\n\x15\x64irect_receiving_diff\x18\x08 \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12?\n\rdirect_return\x18\t \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12;\n\tstocktake\x18\n \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\x12.\n\x06\x64\x65mand\x18\x0b \x03(\x0b\x32\x1e.manufactory.CheckDemandDetail\x12\x38\n\x06\x61ssets\x18\x0c \x03(\x0b\x32(.manufactory.CheckStocktakeByDocIDDetail\"\xab\x01\n\x1bRecreateStocktakeDocRequest\x12\x0f\n\x07\x64oc_ids\x18\x01 \x03(\x04\x12\x1b\n\x13\x63\x61lculate_inventory\x18\x02 \x01(\x08\x12\x15\n\rschedule_name\x18\x03 \x01(\t\x12\x0e\n\x06remark\x18\x04 \x01(\t\x12\x15\n\rschedule_code\x18\x05 \x01(\t\x12\x13\n\x0bschedule_id\x18\x06 \x01(\x04\x12\x0b\n\x03lan\x18\x07 \x01(\t\"s\n\x1cRecreateStocktakeDocResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x1b\n\x13restocktake_doc_ids\x18\x02 \x03(\x04\x12&\n\x1ehas_recreate_doc_id_no_confirm\x18\x03 \x03(\x04\"\xd0\x02\n\x1dStocktakeDocStatisticsRequest\x12\x11\n\tstore_ids\x18\x01 \x03(\x04\x12.\n\nstart_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x37\n\x0fperiod_group_by\x18\x04 \x01(\x0e\x32\x1e.manufactory.PeriodGroupMethod\x12\x16\n\x0estocktake_type\x18\x05 \x03(\t\x12\x0e\n\x06status\x18\x06 \x03(\t\x12\r\n\x05limit\x18\x07 \x01(\x03\x12\x0e\n\x06offset\x18\x08 \x01(\x03\x12\r\n\x05order\x18\t \x01(\t\x12\x0c\n\x04sort\x18\n \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0b \x01(\x08\x12\x0b\n\x03lan\x18\x0c \x01(\t\"\x97\x01\n\x16StocktakeDocStatistics\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\t\x12\x12\n\nstore_code\x18\x02 \x01(\t\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x16\n\x0estocktake_type\x18\x04 \x01(\t\x12\x0e\n\x06status\x18\x05 \x01(\t\x12\r\n\x05\x63ount\x18\x06 \x01(\x01\x12\x10\n\x08store_id\x18\x07 \x01(\x03\"b\n\x1eStocktakeDocStatisticsResponse\x12\x31\n\x04rows\x18\x01 \x03(\x0b\x32#.manufactory.StocktakeDocStatistics\x12\r\n\x05total\x18\x02 \x01(\x03\"\xc7\x03\n!StocktakeDiffCollectReportRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tstore_ids\x18\x03 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x04 \x03(\x04\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12/\n\x0btarget_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x63ode\x18\x08 \x01(\t\x12\x16\n\x0estocktake_type\x18\t \x01(\t\x12\x15\n\rschedule_code\x18\x0b \x01(\t\x12\x0e\n\x06status\x18\x0c \x01(\t\x12\r\n\x05order\x18\r \x01(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\x15\n\rperiod_symbol\x18\x0f \x01(\t\x12\x13\n\x0bupper_limit\x18\x10 \x01(\t\x12\x13\n\x0blower_limit\x18\x11 \x01(\t\x12\x14\n\x0cis_wms_store\x18\x12 \x01(\x08\x12\x0b\n\x03lan\x18\x13 \x01(\t\"\xde\x04\n\x1dStocktakeDiffCollectReportRow\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x12\n\nstore_code\x18\x02 \x01(\t\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x14\n\x0c\x63ompany_code\x18\x04 \x01(\t\x12\x14\n\x0cproduct_code\x18\x05 \x01(\t\x12\x12\n\nproduct_id\x18\x06 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x15\n\rdiff_quantity\x18\x08 \x01(\x01\x12\x10\n\x08quantity\x18\t \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\n \x01(\x01\x12\x1a\n\x12inventory_quantity\x18\x0b \x01(\x01\x12 \n\x18\x64iff_quantity_percentage\x18\x0c \x01(\x01\x12\x0f\n\x07unit_id\x18\r \x01(\x04\x12\x11\n\tunit_name\x18\x0e \x01(\t\x12\x11\n\tunit_rate\x18\x0f \x01(\x01\x12\x11\n\tunit_code\x18\x10 \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x11 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x12 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\x13 \x01(\t\x12\x14\n\x0c\x63ompany_name\x18\x14 \x01(\t\x12%\n\x1d\x61\x63\x63ounting_inventory_quantity\x18\x15 \x01(\x01\x12 \n\x18\x61\x63\x63ounting_diff_quantity\x18\x16 \x01(\x01\x12\x11\n\tunit_spec\x18\x17 \x01(\t\x12\x15\n\rperiod_symbol\x18\x18 \x01(\t\"m\n\"StocktakeDiffCollectReportResponse\x12\x38\n\x04rows\x18\x01 \x03(\x0b\x32*.manufactory.StocktakeDiffCollectReportRow\x12\r\n\x05total\x18\x02 \x01(\x04\"\xbd\x01\n\x1aUncompleteDocReportRequest\x12\x10\n\x08\x62us_date\x18\x01 \x01(\t\x12\x11\n\tstore_ids\x18\x02 \x03(\x04\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\r\n\x05limit\x18\x05 \x01(\x04\x12\x0e\n\x06offset\x18\x06 \x01(\x04\x12\r\n\x05model\x18\x07 \x03(\t\x12\r\n\x05order\x18\r \x01(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0f \x01(\x08\x12\x0b\n\x03lan\x18\x10 \x01(\t\"\xcd\x02\n\x1bUncompleteDocReportResponse\x12J\n\x04rows\x18\x01 \x03(\x0b\x32<.manufactory.UncompleteDocReportResponse.UncompleteDocReport\x12\r\n\x05total\x18\x02 \x01(\x04\x1a\xd2\x01\n\x13UncompleteDocReport\x12\r\n\x05model\x18\x01 \x01(\t\x12\x12\n\nstore_code\x18\x02 \x01(\t\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\x17\n\x0f\x64oc_update_name\x18\x07 \x01(\t\x12\x17\n\x0f\x64oc_update_time\x18\x08 \x01(\t\x12\x10\n\x08\x64oc_date\x18\t \x01(\t\x12\x10\n\x08\x62us_date\x18\n \x01(\t\x12\x10\n\x08store_id\x18\x0b \x01(\x04\"U\n\x1dStocktakeProductImportRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x11\n\tfile_name\x18\x02 \x01(\t\x12\x11\n\tfile_data\x18\x03 \x01(\t\"\xfa\x02\n\x19ProductImportResponseRows\x12\x0f\n\x07row_num\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12\x10\n\x08quantity\x18\x04 \x01(\x01\x12\x14\n\x0cstorage_type\x18\x05 \x01(\t\x12\x0c\n\x04spec\x18\x06 \x01(\t\x12\x0c\n\x04unit\x18\x07 \x01(\t\x12\x11\n\terror_msg\x18\x08 \x01(\t\x12\x13\n\x0bposition_id\x18\t \x01(\t\x12\x15\n\rposition_code\x18\n \x01(\t\x12\x15\n\rposition_name\x18\x0b \x01(\t\x12\x11\n\tquantity1\x18\x0c \x01(\x01\x12\r\n\x05unit1\x18\r \x01(\t\x12\x11\n\tquantity2\x18\x0e \x01(\x01\x12\r\n\x05unit2\x18\x0f \x01(\t\x12\x11\n\tquantity3\x18\x10 \x01(\x01\x12\r\n\x05unit3\x18\x11 \x01(\t\x12\x11\n\tquantity4\x18\x12 \x01(\x01\x12\r\n\x05unit4\x18\x13 \x01(\t\"\x9d\x01\n\x1eStocktakeProductImportResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x34\n\x04rows\x18\x02 \x03(\x0b\x32&.manufactory.ProductImportResponseRows\x12\x11\n\tfile_name\x18\x03 \x01(\t\x12\x10\n\x08rows_num\x18\x04 \x01(\x04\x12\x10\n\x08\x62\x61tch_id\x18\x05 \x01(\x04\"E\n!UpdateStocktakeImportBatchRequest\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\"D\n\"UpdateStocktakeImportBatchResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x0e\n\x06\x64oc_id\x18\x02 \x01(\x04\"\x9e\x01\n\x19StocktakePositionProducts\x12\x13\n\x0bposition_id\x18\x01 \x01(\x04\x12\x15\n\rposition_code\x18\x02 \x01(\t\x12\x15\n\rposition_name\x18\x03 \x01(\t\x12/\n\x08products\x18\x04 \x03(\x0b\x32\x1d.manufactory.StocktakeProduct\x12\r\n\x05total\x18\x05 \x01(\x04*:\n\x11PeriodGroupMethod\x12\n\n\x06\x42Y_DAY\x10\x00\x12\x0c\n\x08\x42Y_MONTH\x10\x01\x12\x0b\n\x07\x42Y_YEAR\x10\x02\x32\xa2&\n\x14ManufactoryStockTake\x12\xb3\x01\n\x15\x43heckStocktakeByDocID\x12).manufactory.CheckStocktakeByDocIDRequest\x1a*.manufactory.CheckStocktakeByDocIDResponse\"C\x82\xd3\xe4\x93\x02=\x1a;/api/v2/supply/manufactory/stocktake/{doc_id}/confirm/check\x12\xb3\x01\n\x17\x43onfirmStocktakeByDocID\x12+.manufactory.ConfirmStocktakeByDocIDRequest\x1a,.manufactory.ConfirmStocktakeByDocIDResponse\"=\x82\xd3\xe4\x93\x02\x37\x1a\x35/api/v2/supply/manufactory/stocktake/{doc_id}/confirm\x12\xb6\x01\n\x17\x41pproveStocktakeByDocID\x12+.manufactory.ApproveStocktakeByDocIDRequest\x1a,.manufactory.ApproveStocktakeByDocIDResponse\"@\x82\xd3\xe4\x93\x02:\x1a\x35/api/v2/supply/manufactory/stocktake/{doc_id}/approve:\x01*\x12\xb2\x01\n\x16RejectStocktakeProduct\x12*.manufactory.RejectStocktakeProductRequest\x1a+.manufactory.RejectStocktakeProductResponse\"?\x82\xd3\xe4\x93\x02\x39\x1a\x34/api/v2/supply/manufactory/stocktake/{doc_id}/reject:\x01*\x12\xaf\x01\n\x16\x43\x61ncelStocktakeByDocID\x12*.manufactory.CancelStocktakeByDocIDRequest\x1a+.manufactory.CancelStocktakeByDocIDResponse\"<\x82\xd3\xe4\x93\x02\x36\x1a\x34/api/v2/supply/manufactory/stocktake/{doc_id}/cancel\x12\x8d\x01\n\x13GetStocktakeByDocID\x12\'.manufactory.GetStocktakeByDocIDRequest\x1a\x16.manufactory.Stocktake\"5\x82\xd3\xe4\x93\x02/\x12-/api/v2/supply/manufactory/stocktake/{doc_id}\x12\x81\x01\n\x0cGetStocktake\x12 .manufactory.GetStocktakeRequest\x1a!.manufactory.GetStocktakeResponse\",\x82\xd3\xe4\x93\x02&\x12$/api/v2/supply/manufactory/stocktake\x12\xa7\x01\n\x13GetStocktakeProduct\x12\'.manufactory.GetStocktakeProductRequest\x1a(.manufactory.GetStocktakeProductResponse\"=\x82\xd3\xe4\x93\x02\x37\x12\x35/api/v2/supply/manufactory/stocktake/{doc_id}/product\x12\xa2\x01\n\x13PutStocktakeByDocID\x12\'.manufactory.PutStocktakeByDocIDRequest\x1a(.manufactory.PutStocktakeByDocIDResponse\"8\x82\xd3\xe4\x93\x02\x32\x1a-/api/v2/supply/manufactory/stocktake/{doc_id}:\x01*\x12\xb9\x01\n\x17\x43heckedStocktakeByDocID\x12+.manufactory.CheckedStocktakeByDocIDRequest\x1a,.manufactory.CheckedStocktakeByDocIDResponse\"C\x82\xd3\xe4\x93\x02=\x1a\x38/api/v2/supply/manufactory/stocktake/{doc_id}/init/check:\x01*\x12\x9a\x01\n\x10GetStocktakeTags\x12$.manufactory.GetStocktakeTagsRequest\x1a%.manufactory.GetStocktakeTagsResponse\"9\x82\xd3\xe4\x93\x02\x33\x12\x31/api/v2/supply/manufactory/stocktake/product/tags\x12\x96\x01\n\x14GetStocktakeTagsById\x12(.manufactory.GetStocktakeTagsByIdRequest\x1a\x1a.manufactory.StocktakeTags\"8\x82\xd3\xe4\x93\x02\x32\x12-/api/v2/supply/mobile/stocktake/tags/{tag_id}:\x01*\x12\xad\x01\n\x13\x41\x63tionStocktakeTags\x12\'.manufactory.ActionStocktakeTagsRequest\x1a(.manufactory.ActionStocktakeTagsResponse\"C\x82\xd3\xe4\x93\x02=\x1a\x38/api/v2/supply/manufactory/stocktake/product/tags/action:\x01*\x12\xc1\x01\n\x1a\x44\x65leteStocktakeProductTags\x12..manufactory.DeleteStocktakeProductTagsRequest\x1a/.manufactory.DeleteStocktakeProductTagsResponse\"B\x82\xd3\xe4\x93\x02<\x1a\x37/api/v2/supply/manufactory/stocktake/product/tags/clean:\x01*\x12\xa1\x01\n\x13GetStocktakeBalance\x12\'.manufactory.GetStocktakeBalanceRequest\x1a(.manufactory.GetStocktakeBalanceResponse\"7\x82\xd3\xe4\x93\x02\x31\x12//api/v2/supply/manufactory/stocktake/bi/balance\x12\xb2\x01\n\x16SubmitStocktakeByDocID\x12*.manufactory.SubmitStocktakeByDocIDRequest\x1a+.manufactory.SubmitStocktakeByDocIDResponse\"?\x82\xd3\xe4\x93\x02\x39\x1a\x34/api/v2/supply/manufactory/stocktake/{doc_id}/submit:\x01*\x12\xd9\x01\n\x1fGetStocktakeBalanceProductGroup\x12\x33.manufactory.GetStocktakeBalanceProductGroupRequest\x1a\x34.manufactory.GetStocktakeBalanceProductGroupResponse\"K\x82\xd3\xe4\x93\x02\x45\x12\x43/api/v2/supply/manufactory/stocktake/balance/{doc_id}/product/group\x12\xa0\x01\n\x13StocktakeBiDetailed\x12\'.manufactory.StocktakeBiDetailedRequest\x1a(.manufactory.StocktakeBiDetailedResponse\"6\x82\xd3\xe4\x93\x02\x30\x12./api/v2/supply/manufactory/stocktake/bi/detail\x12\xb5\x01\n\x16StocktakeBalanceRegion\x12*.manufactory.StocktakeBalanceRegionRequest\x1a+.manufactory.StocktakeBalanceRegionResponse\"B\x82\xd3\xe4\x93\x02<\x12:/api/v2/supply/manufactory/stocktake/balance/product/group\x12\xaa\x01\n\x14\x41\x64vanceStocktakeDiff\x12(.manufactory.AdvanceStocktakeDiffRequest\x1a).manufactory.AdvanceStocktakeDiffResponse\"=\x82\xd3\xe4\x93\x02\x37\x12\x35/api/v2/supply/manufactory/stocktake/{doc_id}/advance\x12\xa2\x01\n\x13StocktakeDiffReport\x12\'.manufactory.StocktakeDiffReportRequest\x1a(.manufactory.StocktakeDiffReportResponse\"8\x82\xd3\xe4\x93\x02\x32\x12\x30/api/v2/supply/manufactory/stocktake/diff/report\x12\x92\x01\n\x10GetUncompleteDoc\x12$.manufactory.GetUncompleteDocRequest\x1a%.manufactory.GetUncompleteDocResponse\"1\x82\xd3\xe4\x93\x02+\x12)/api/v2/supply/manufactory/uncomplete_doc\x12\xa9\x01\n\x14RecreateStocktakeDoc\x12(.manufactory.RecreateStocktakeDocRequest\x1a).manufactory.RecreateStocktakeDocResponse\"<\x82\xd3\xe4\x93\x02\x36\"1/api/v2/supply/manufactory/recreate_stocktake_doc:\x01*\x12\xb1\x01\n\x16StocktakeDocStatistics\x12*.manufactory.StocktakeDocStatisticsRequest\x1a+.manufactory.StocktakeDocStatisticsResponse\">\x82\xd3\xe4\x93\x02\x38\"3/api/v2/supply/manufactory/stocktake_doc_statistics:\x01*\x12\xbf\x01\n\x1aStocktakeDiffCollectReport\x12..manufactory.StocktakeDiffCollectReportRequest\x1a/.manufactory.StocktakeDiffCollectReportResponse\"@\x82\xd3\xe4\x93\x02:\x12\x38/api/v2/supply/manufactory/stocktake/diff_collect/report\x12\xa2\x01\n\x13UncompleteDocReport\x12\'.manufactory.UncompleteDocReportRequest\x1a(.manufactory.UncompleteDocReportResponse\"8\x82\xd3\xe4\x93\x02\x32\x12\x30/api/v2/supply/manufactory/uncomplete_doc_report\x12\xb1\x01\n\x16StocktakeProductImport\x12*.manufactory.StocktakeProductImportRequest\x1a+.manufactory.StocktakeProductImportResponse\">\x82\xd3\xe4\x93\x02\x38\"3/api/v2/supply/manufactory/stocktake/product/import:\x01*\x12\xc2\x01\n\x1aUpdateStocktakeImportBatch\x12..manufactory.UpdateStocktakeImportBatchRequest\x1a/.manufactory.UpdateStocktakeImportBatchResponse\"C\x82\xd3\xe4\x93\x02=\x1a\x38/api/v2/supply/manufactory/update/stocktake/import/batch:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])

_PERIODGROUPMETHOD = _descriptor.EnumDescriptor(
  name='PeriodGroupMethod',
  full_name='manufactory.PeriodGroupMethod',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='BY_DAY', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BY_MONTH', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BY_YEAR', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=23773,
  serialized_end=23831,
)
_sym_db.RegisterEnumDescriptor(_PERIODGROUPMETHOD)

PeriodGroupMethod = enum_type_wrapper.EnumTypeWrapper(_PERIODGROUPMETHOD)
BY_DAY = 0
BY_MONTH = 1
BY_YEAR = 2


_STOCKTAKE_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='manufactory.Stocktake.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='STARTED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REJECTED', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVED', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMIT_0', index=7, number=7,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVE_0', index=8, number=8,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1622,
  serialized_end=1757,
)
_sym_db.RegisterEnumDescriptor(_STOCKTAKE_STATUS)

_GETSTOCKTAKEREQUEST_S_STATUS = _descriptor.EnumDescriptor(
  name='S_STATUS',
  full_name='manufactory.GetStocktakeRequest.S_STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='OPENED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CLOSED', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2444,
  serialized_end=2478,
)
_sym_db.RegisterEnumDescriptor(_GETSTOCKTAKEREQUEST_S_STATUS)

_GETSTOCKTAKEREQUEST_S_TYPE = _descriptor.EnumDescriptor(
  name='S_TYPE',
  full_name='manufactory.GetStocktakeRequest.S_TYPE',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='W', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='D', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='M', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2480,
  serialized_end=2509,
)
_sym_db.RegisterEnumDescriptor(_GETSTOCKTAKEREQUEST_S_TYPE)

_GETSTOCKTAKEREQUEST_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='manufactory.GetStocktakeRequest.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='STARTED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REJECTED', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVED', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMIT_0', index=7, number=7,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVE_0', index=8, number=8,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1622,
  serialized_end=1757,
)
_sym_db.RegisterEnumDescriptor(_GETSTOCKTAKEREQUEST_STATUS)

_USERCREATESTOCKTAKERESPONSE_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='manufactory.UserCreateStocktakeResponse.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='STARTED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1622,
  serialized_end=1655,
)
_sym_db.RegisterEnumDescriptor(_USERCREATESTOCKTAKERESPONSE_STATUS)

_ACTIONSTOCKTAKETAGSREQUEST_ACTION = _descriptor.EnumDescriptor(
  name='Action',
  full_name='manufactory.ActionStocktakeTagsRequest.Action',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='get', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='create', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='delete', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='update', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='copy', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=10314,
  serialized_end=10377,
)
_sym_db.RegisterEnumDescriptor(_ACTIONSTOCKTAKETAGSREQUEST_ACTION)


_GETPRODUCTBYSTOCKTAKETYPEREQUEST = _descriptor.Descriptor(
  name='GetProductByStocktakeTypeRequest',
  full_name='manufactory.GetProductByStocktakeTypeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='manufactory.GetProductByStocktakeTypeRequest.stocktake_type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='manufactory.GetProductByStocktakeTypeRequest.branch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='manufactory.GetProductByStocktakeTypeRequest.storage_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.GetProductByStocktakeTypeRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=107,
  serialized_end=219,
)


_STOCKTAKEPRODUCTTYPE = _descriptor.Descriptor(
  name='StocktakeProductType',
  full_name='manufactory.StocktakeProductType',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.StocktakeProductType.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.StocktakeProductType.id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='manufactory.StocktakeProductType.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='manufactory.StocktakeProductType.spec', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='manufactory.StocktakeProductType.storage_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=221,
  serialized_end=319,
)


_GETPRODUCTBYSTOCKTAKETYPERESPONSE = _descriptor.Descriptor(
  name='GetProductByStocktakeTypeResponse',
  full_name='manufactory.GetProductByStocktakeTypeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.GetProductByStocktakeTypeResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=321,
  serialized_end=405,
)


_GETSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='GetStocktakeByDocIDRequest',
  full_name='manufactory.GetStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='manufactory.GetStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.GetStocktakeByDocIDRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=407,
  serialized_end=464,
)


_STOCKTAKE = _descriptor.Descriptor(
  name='Stocktake',
  full_name='manufactory.Stocktake',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.Stocktake.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.Stocktake.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_batch_id', full_name='manufactory.Stocktake.branch_batch_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='manufactory.Stocktake.branch_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_id', full_name='manufactory.Stocktake.schedule_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.Stocktake.branch_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_inventory', full_name='manufactory.Stocktake.calculate_inventory', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.Stocktake.code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='forecasting', full_name='manufactory.Stocktake.forecasting', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='forecasting_time', full_name='manufactory.Stocktake.forecasting_time', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.Stocktake.remark', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result_type', full_name='manufactory.Stocktake.result_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='manufactory.Stocktake.schedule_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='st_diff_flag', full_name='manufactory.Stocktake.st_diff_flag', index=13,
      number=14, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.Stocktake.status', index=14,
      number=15, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='manufactory.Stocktake.process_status', index=15,
      number=16, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='manufactory.Stocktake.store_secondary_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.Stocktake.type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.Stocktake.created_by', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.Stocktake.updated_by', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='manufactory.Stocktake.review_by', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='manufactory.Stocktake.target_date', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.Stocktake.created_at', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.Stocktake.updated_at', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='manufactory.Stocktake.user_id', index=24,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.Stocktake.created_name', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.Stocktake.updated_name', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='manufactory.Stocktake.schedule_name', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_err_message', full_name='manufactory.Stocktake.diff_err_message', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='month_err_message', full_name='manufactory.Stocktake.month_err_message', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='original_code', full_name='manufactory.Stocktake.original_code', index=30,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='original_doc_id', full_name='manufactory.Stocktake.original_doc_id', index=31,
      number=36, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_recreate', full_name='manufactory.Stocktake.is_recreate', index=32,
      number=37, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='recreate_code', full_name='manufactory.Stocktake.recreate_code', index=33,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='recreate_doc_id', full_name='manufactory.Stocktake.recreate_doc_id', index=34,
      number=39, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='submit_name', full_name='manufactory.Stocktake.submit_name', index=35,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_name', full_name='manufactory.Stocktake.approve_name', index=36,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='manufactory.Stocktake.stocktake_type', index=37,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.Stocktake.request_id', index=38,
      number=43, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='manufactory.Stocktake.total_amount', index=39,
      number=50, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_diff_amount', full_name='manufactory.Stocktake.total_diff_amount', index=40,
      number=51, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_sales_amount', full_name='manufactory.Stocktake.total_sales_amount', index=41,
      number=52, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_diff_sales_amount', full_name='manufactory.Stocktake.total_diff_sales_amount', index=42,
      number=53, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='manufactory.Stocktake.attachments', index=43,
      number=54, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _STOCKTAKE_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=467,
  serialized_end=1757,
)


_ATTACHMENTSSTOCK = _descriptor.Descriptor(
  name='AttachmentsStock',
  full_name='manufactory.AttachmentsStock',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.AttachmentsStock.type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='manufactory.AttachmentsStock.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1759,
  serialized_end=1804,
)


_GETSTOCKTAKEREQUEST = _descriptor.Descriptor(
  name='GetStocktakeRequest',
  full_name='manufactory.GetStocktakeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='include_total', full_name='manufactory.GetStocktakeRequest.include_total', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='manufactory.GetStocktakeRequest.store_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='manufactory.GetStocktakeRequest.branch_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_status', full_name='manufactory.GetStocktakeRequest.store_status', index=3,
      number=4, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='_type', full_name='manufactory.GetStocktakeRequest._type', index=4,
      number=5, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.GetStocktakeRequest.status', index=5,
      number=6, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='manufactory.GetStocktakeRequest.schedule_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='manufactory.GetStocktakeRequest.product_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.GetStocktakeRequest.offset', index=8,
      number=9, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.GetStocktakeRequest.limit', index=9,
      number=10, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='manufactory.GetStocktakeRequest.start_date', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='manufactory.GetStocktakeRequest.end_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='manufactory.GetStocktakeRequest.target_date', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.GetStocktakeRequest.code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='manufactory.GetStocktakeRequest.ids', index=14,
      number=21, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_create', full_name='manufactory.GetStocktakeRequest.is_create', index=15,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='manufactory.GetStocktakeRequest.stocktake_type', index=16,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='manufactory.GetStocktakeRequest.order', index=17,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='manufactory.GetStocktakeRequest.sort', index=18,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.GetStocktakeRequest.branch_type', index=19,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.GetStocktakeRequest.lan', index=20,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='manufactory.GetStocktakeRequest.schedule_name', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _GETSTOCKTAKEREQUEST_S_STATUS,
    _GETSTOCKTAKEREQUEST_S_TYPE,
    _GETSTOCKTAKEREQUEST_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1807,
  serialized_end=2647,
)


_GETSTOCKTAKERESPONSE = _descriptor.Descriptor(
  name='GetStocktakeResponse',
  full_name='manufactory.GetStocktakeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.GetStocktakeResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.GetStocktakeResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2649,
  serialized_end=2724,
)


_GETSTOCKTAKEPRODUCTREQUEST = _descriptor.Descriptor(
  name='GetStocktakeProductRequest',
  full_name='manufactory.GetStocktakeProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='manufactory.GetStocktakeProductRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_unit', full_name='manufactory.GetStocktakeProductRequest.include_unit', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.GetStocktakeProductRequest.limit', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.GetStocktakeProductRequest.offset', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='manufactory.GetStocktakeProductRequest.include_total', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='manufactory.GetStocktakeProductRequest.category_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='manufactory.GetStocktakeProductRequest.storage_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.GetStocktakeProductRequest.product_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.GetStocktakeProductRequest.lan', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_barcode', full_name='manufactory.GetStocktakeProductRequest.include_barcode', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2727,
  serialized_end=2950,
)


_STOCKTAKEPRODUCTTAGNAME = _descriptor.Descriptor(
  name='StocktakeProductTagName',
  full_name='manufactory.StocktakeProductTagName',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.StocktakeProductTagName.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='manufactory.StocktakeProductTagName.doc_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stp_id', full_name='manufactory.StocktakeProductTagName.stp_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.StocktakeProductTagName.product_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_id', full_name='manufactory.StocktakeProductTagName.tag_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='manufactory.StocktakeProductTagName.tag_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_quantity', full_name='manufactory.StocktakeProductTagName.tag_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='manufactory.StocktakeProductTagName.accounting_quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.StocktakeProductTagName.unit_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='manufactory.StocktakeProductTagName.unit_spec', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.StocktakeProductTagName.unit_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='manufactory.StocktakeProductTagName.unit_rate', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='manufactory.StocktakeProductTagName.accounting_unit_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.StocktakeProductTagName.accounting_unit_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='manufactory.StocktakeProductTagName.accounting_unit_spec', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.StocktakeProductTagName.partner_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='manufactory.StocktakeProductTagName.user_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.StocktakeProductTagName.created_by', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.StocktakeProductTagName.updated_by', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.StocktakeProductTagName.created_at', index=19,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.StocktakeProductTagName.updated_at', index=20,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.StocktakeProductTagName.created_name', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.StocktakeProductTagName.updated_name', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='manufactory.StocktakeProductTagName.tax_rate', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='manufactory.StocktakeProductTagName.tax_price', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='manufactory.StocktakeProductTagName.cost_price', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='manufactory.StocktakeProductTagName.sales_price', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2953,
  serialized_end=3584,
)


_STOCKTAKEPRODUCTUNITS = _descriptor.Descriptor(
  name='StocktakeProductUnits',
  full_name='manufactory.StocktakeProductUnits',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.StocktakeProductUnits.unit_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.StocktakeProductUnits.unit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='manufactory.StocktakeProductUnits.unit_spec', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='manufactory.StocktakeProductUnits.unit_rate', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='manufactory.StocktakeProductUnits.tax_rate', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='manufactory.StocktakeProductUnits.tax_price', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='manufactory.StocktakeProductUnits.cost_price', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='manufactory.StocktakeProductUnits.sales_price', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default', full_name='manufactory.StocktakeProductUnits.default', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='manufactory.StocktakeProductUnits.stocktake', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3587,
  serialized_end=3798,
)


_STOCKTAKEPRODUCT = _descriptor.Descriptor(
  name='StocktakeProduct',
  full_name='manufactory.StocktakeProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.StocktakeProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.StocktakeProduct.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='manufactory.StocktakeProduct.doc_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='manufactory.StocktakeProduct.branch_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.StocktakeProduct.product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='manufactory.StocktakeProduct.accounting_quantity', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='manufactory.StocktakeProduct.accounting_unit_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.StocktakeProduct.accounting_unit_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='manufactory.StocktakeProduct.accounting_unit_spec', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.StocktakeProduct.code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deleted', full_name='manufactory.StocktakeProduct.deleted', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='manufactory.StocktakeProduct.diff_quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='display_order', full_name='manufactory.StocktakeProduct.display_order', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='manufactory.StocktakeProduct.extends', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ignored', full_name='manufactory.StocktakeProduct.ignored', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='manufactory.StocktakeProduct.inventory_quantity', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_system', full_name='manufactory.StocktakeProduct.is_system', index=16,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_number', full_name='manufactory.StocktakeProduct.item_number', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='manufactory.StocktakeProduct.material_number', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.StocktakeProduct.product_code', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.StocktakeProduct.product_name', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.StocktakeProduct.quantity', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='st_type', full_name='manufactory.StocktakeProduct.st_type', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='manufactory.StocktakeProduct.storage_type', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_diff_quantity', full_name='manufactory.StocktakeProduct.unit_diff_quantity', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.StocktakeProduct.unit_id', index=25,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.StocktakeProduct.unit_name', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='manufactory.StocktakeProduct.unit_rate', index=27,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='manufactory.StocktakeProduct.unit_spec', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='manufactory.StocktakeProduct.units', index=29,
      number=30, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.StocktakeProduct.created_by', index=30,
      number=31, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.StocktakeProduct.updated_by', index=31,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='manufactory.StocktakeProduct.target_date', index=32,
      number=33, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.StocktakeProduct.created_at', index=33,
      number=34, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.StocktakeProduct.updated_at', index=34,
      number=35, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='manufactory.StocktakeProduct.user_id', index=35,
      number=36, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.StocktakeProduct.status', index=36,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_pda', full_name='manufactory.StocktakeProduct.is_pda', index=37,
      number=38, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_tags', full_name='manufactory.StocktakeProduct.product_tags', index=38,
      number=39, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_empty', full_name='manufactory.StocktakeProduct.is_empty', index=39,
      number=40, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.StocktakeProduct.created_name', index=40,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.StocktakeProduct.updated_name', index=41,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_null', full_name='manufactory.StocktakeProduct.is_null', index=42,
      number=43, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_quantity', full_name='manufactory.StocktakeProduct.tag_quantity', index=43,
      number=44, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_accounting_quantity', full_name='manufactory.StocktakeProduct.convert_accounting_quantity', index=44,
      number=45, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_bom', full_name='manufactory.StocktakeProduct.is_bom', index=45,
      number=46, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_stocktake_edit', full_name='manufactory.StocktakeProduct.allow_stocktake_edit', index=46,
      number=47, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='manufactory.StocktakeProduct.spec', index=47,
      number=48, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_price', full_name='manufactory.StocktakeProduct.diff_price', index=48,
      number=49, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='barcode', full_name='manufactory.StocktakeProduct.barcode', index=49,
      number=50, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='manufactory.StocktakeProduct.category_id', index=50,
      number=54, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.StocktakeProduct.position_id', index=51,
      number=55, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='manufactory.StocktakeProduct.category_name', index=52,
      number=56, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='manufactory.StocktakeProduct.tax_rate', index=53,
      number=57, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='manufactory.StocktakeProduct.tax_price', index=54,
      number=58, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='manufactory.StocktakeProduct.amount', index=55,
      number=59, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_amount', full_name='manufactory.StocktakeProduct.diff_amount', index=56,
      number=60, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='manufactory.StocktakeProduct.cost_price', index=57,
      number=61, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='manufactory.StocktakeProduct.sales_amount', index=58,
      number=62, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='manufactory.StocktakeProduct.sales_price', index=59,
      number=63, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_sales_amount', full_name='manufactory.StocktakeProduct.diff_sales_amount', index=60,
      number=64, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3801,
  serialized_end=5239,
)


_GETSTOCKTAKEPRODUCTRESPONSE = _descriptor.Descriptor(
  name='GetStocktakeProductResponse',
  full_name='manufactory.GetStocktakeProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.GetStocktakeProductResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.GetStocktakeProductResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_rows', full_name='manufactory.GetStocktakeProductResponse.position_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5242,
  serialized_end=5394,
)


_USERCREATESTOCKTAKEREQUEST = _descriptor.Descriptor(
  name='UserCreateStocktakeRequest',
  full_name='manufactory.UserCreateStocktakeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.UserCreateStocktakeRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='manufactory.UserCreateStocktakeRequest.branch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='_type', full_name='manufactory.UserCreateStocktakeRequest._type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='manufactory.UserCreateStocktakeRequest.target_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.UserCreateStocktakeRequest.lan', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5397,
  serialized_end=5541,
)


_USERCREATESTOCKTAKERESPONSE = _descriptor.Descriptor(
  name='UserCreateStocktakeResponse',
  full_name='manufactory.UserCreateStocktakeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.UserCreateStocktakeResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.UserCreateStocktakeResponse.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_batch_id', full_name='manufactory.UserCreateStocktakeResponse.branch_batch_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='manufactory.UserCreateStocktakeResponse.branch_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_id', full_name='manufactory.UserCreateStocktakeResponse.schedule_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.UserCreateStocktakeResponse.branch_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_inventory', full_name='manufactory.UserCreateStocktakeResponse.calculate_inventory', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.UserCreateStocktakeResponse.code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='forecasting', full_name='manufactory.UserCreateStocktakeResponse.forecasting', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='forecasting_time', full_name='manufactory.UserCreateStocktakeResponse.forecasting_time', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.UserCreateStocktakeResponse.remark', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result_type', full_name='manufactory.UserCreateStocktakeResponse.result_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='manufactory.UserCreateStocktakeResponse.schedule_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='st_diff_flag', full_name='manufactory.UserCreateStocktakeResponse.st_diff_flag', index=13,
      number=14, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.UserCreateStocktakeResponse.status', index=14,
      number=15, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='manufactory.UserCreateStocktakeResponse.process_status', index=15,
      number=16, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='manufactory.UserCreateStocktakeResponse.store_secondary_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.UserCreateStocktakeResponse.type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.UserCreateStocktakeResponse.created_by', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.UserCreateStocktakeResponse.updated_by', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='manufactory.UserCreateStocktakeResponse.review_by', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='manufactory.UserCreateStocktakeResponse.target_date', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.UserCreateStocktakeResponse.created_at', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.UserCreateStocktakeResponse.updated_at', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='manufactory.UserCreateStocktakeResponse.user_id', index=24,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.UserCreateStocktakeResponse.created_name', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.UserCreateStocktakeResponse.updated_name', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _USERCREATESTOCKTAKERESPONSE_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5544,
  serialized_end=6343,
)


_TAGQUANTITY = _descriptor.Descriptor(
  name='TagQuantity',
  full_name='manufactory.TagQuantity',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.TagQuantity.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_id', full_name='manufactory.TagQuantity.tag_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='manufactory.TagQuantity.tag_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_quantity', full_name='manufactory.TagQuantity.tag_quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.TagQuantity.unit_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6345,
  serialized_end=6443,
)


_PUTSTOCKTAKEPRODUCTS = _descriptor.Descriptor(
  name='PutStocktakeProducts',
  full_name='manufactory.PutStocktakeProducts',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.PutStocktakeProducts.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.PutStocktakeProducts.quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.PutStocktakeProducts.unit_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_products', full_name='manufactory.PutStocktakeProducts.tag_products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_pda', full_name='manufactory.PutStocktakeProducts.is_pda', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_empty', full_name='manufactory.PutStocktakeProducts.is_empty', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_null', full_name='manufactory.PutStocktakeProducts.is_null', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='del_tag_ids', full_name='manufactory.PutStocktakeProducts.del_tag_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6446,
  serialized_end=6635,
)


_PUTSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='PutStocktakeByDocIDRequest',
  full_name='manufactory.PutStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='manufactory.PutStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='manufactory.PutStocktakeByDocIDRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.PutStocktakeByDocIDRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='all_zero', full_name='manufactory.PutStocktakeByDocIDRequest.all_zero', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6638,
  serialized_end=6766,
)


_REJECTSTOCKTAKEPRODUCTREQUEST = _descriptor.Descriptor(
  name='RejectStocktakeProductRequest',
  full_name='manufactory.RejectStocktakeProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='manufactory.RejectStocktakeProductRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='manufactory.RejectStocktakeProductRequest.reason', index=1,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.RejectStocktakeProductRequest.lan', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6768,
  serialized_end=6844,
)


_REJECTSTOCKTAKEPRODUCTRESPONSE = _descriptor.Descriptor(
  name='RejectStocktakeProductResponse',
  full_name='manufactory.RejectStocktakeProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.RejectStocktakeProductResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6846,
  serialized_end=6894,
)


_PUTSTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='PutStocktakeByDocIDResponse',
  full_name='manufactory.PutStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.PutStocktakeByDocIDResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6896,
  serialized_end=6941,
)


_CHECKSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='CheckStocktakeByDocIDRequest',
  full_name='manufactory.CheckStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='manufactory.CheckStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='check', full_name='manufactory.CheckStocktakeByDocIDRequest.check', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.CheckStocktakeByDocIDRequest.branch_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.CheckStocktakeByDocIDRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6943,
  serialized_end=7038,
)


_CHECKSTOCKTAKEBYDOCIDDETAIL = _descriptor.Descriptor(
  name='CheckStocktakeByDocIDDetail',
  full_name='manufactory.CheckStocktakeByDocIDDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.CheckStocktakeByDocIDDetail.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.CheckStocktakeByDocIDDetail.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.CheckStocktakeByDocIDDetail.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7040,
  serialized_end=7111,
)


_CHECKSTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='CheckStocktakeByDocIDResponse',
  full_name='manufactory.CheckStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='handler', full_name='manufactory.CheckStocktakeByDocIDResponse.handler', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust', full_name='manufactory.CheckStocktakeByDocIDResponse.adjust', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_diff', full_name='manufactory.CheckStocktakeByDocIDResponse.receiving_diff', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer', full_name='manufactory.CheckStocktakeByDocIDResponse.transfer', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving', full_name='manufactory.CheckStocktakeByDocIDResponse.receiving', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return', full_name='manufactory.CheckStocktakeByDocIDResponse.return', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_receiving', full_name='manufactory.CheckStocktakeByDocIDResponse.direct_receiving', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_receiving_diff', full_name='manufactory.CheckStocktakeByDocIDResponse.direct_receiving_diff', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_return', full_name='manufactory.CheckStocktakeByDocIDResponse.direct_return', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='manufactory.CheckStocktakeByDocIDResponse.stocktake', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand', full_name='manufactory.CheckStocktakeByDocIDResponse.demand', index=10,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='self_picking', full_name='manufactory.CheckStocktakeByDocIDResponse.self_picking', index=11,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7114,
  serialized_end=7854,
)


_CONFIRMSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='ConfirmStocktakeByDocIDRequest',
  full_name='manufactory.ConfirmStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='manufactory.ConfirmStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='check', full_name='manufactory.ConfirmStocktakeByDocIDRequest.check', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.ConfirmStocktakeByDocIDRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7856,
  serialized_end=7932,
)


_SUBMITSTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='SubmitStocktakeByDocIDResponse',
  full_name='manufactory.SubmitStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.SubmitStocktakeByDocIDResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7934,
  serialized_end=7982,
)


_SUBMITSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='SubmitStocktakeByDocIDRequest',
  full_name='manufactory.SubmitStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='manufactory.SubmitStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='submit_name', full_name='manufactory.SubmitStocktakeByDocIDRequest.submit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.SubmitStocktakeByDocIDRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='all_zero', full_name='manufactory.SubmitStocktakeByDocIDRequest.all_zero', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7984,
  serialized_end=8083,
)


_APPROVESTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='ApproveStocktakeByDocIDRequest',
  full_name='manufactory.ApproveStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='manufactory.ApproveStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.ApproveStocktakeByDocIDRequest.type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_name', full_name='manufactory.ApproveStocktakeByDocIDRequest.approve_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.ApproveStocktakeByDocIDRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='all_zero', full_name='manufactory.ApproveStocktakeByDocIDRequest.all_zero', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8085,
  serialized_end=8200,
)


_APPROVESTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='ApproveStocktakeByDocIDResponse',
  full_name='manufactory.ApproveStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.ApproveStocktakeByDocIDResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='handler', full_name='manufactory.ApproveStocktakeByDocIDResponse.handler', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust', full_name='manufactory.ApproveStocktakeByDocIDResponse.adjust', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_diff', full_name='manufactory.ApproveStocktakeByDocIDResponse.receiving_diff', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer', full_name='manufactory.ApproveStocktakeByDocIDResponse.transfer', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving', full_name='manufactory.ApproveStocktakeByDocIDResponse.receiving', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return', full_name='manufactory.ApproveStocktakeByDocIDResponse.return', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_receiving', full_name='manufactory.ApproveStocktakeByDocIDResponse.direct_receiving', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_receiving_diff', full_name='manufactory.ApproveStocktakeByDocIDResponse.direct_receiving_diff', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_return', full_name='manufactory.ApproveStocktakeByDocIDResponse.direct_return', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='self_picking', full_name='manufactory.ApproveStocktakeByDocIDResponse.self_picking', index=10,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8203,
  serialized_end=8842,
)


_CONFIRMSTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='ConfirmStocktakeByDocIDResponse',
  full_name='manufactory.ConfirmStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.ConfirmStocktakeByDocIDResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8844,
  serialized_end=8893,
)


_CANCELSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='CancelStocktakeByDocIDRequest',
  full_name='manufactory.CancelStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='manufactory.CancelStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.CancelStocktakeByDocIDRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8895,
  serialized_end=8955,
)


_CANCELSTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='CancelStocktakeByDocIDResponse',
  full_name='manufactory.CancelStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.CancelStocktakeByDocIDResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8957,
  serialized_end=9005,
)


_CREATESTOCKTAKEBATCHREQUEST = _descriptor.Descriptor(
  name='CreateStocktakeBatchRequest',
  full_name='manufactory.CreateStocktakeBatchRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.CreateStocktakeBatchRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='manufactory.CreateStocktakeBatchRequest.request_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.CreateStocktakeBatchRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9007,
  serialized_end=9119,
)


_CREATESTOCKTAKEBATCHRESPONSE = _descriptor.Descriptor(
  name='CreateStocktakeBatchResponse',
  full_name='manufactory.CreateStocktakeBatchResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.CreateStocktakeBatchResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_count', full_name='manufactory.CreateStocktakeBatchResponse.schedule_count', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='manufactory.CreateStocktakeBatchResponse.request_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9121,
  serialized_end=9241,
)


_CREATESTOCKTAKEREQUEST = _descriptor.Descriptor(
  name='CreateStocktakeRequest',
  full_name='manufactory.CreateStocktakeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.CreateStocktakeRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='manufactory.CreateStocktakeRequest.request_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.CreateStocktakeRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9243,
  serialized_end=9350,
)


_CREATESTOCKTAKERESPONSE = _descriptor.Descriptor(
  name='CreateStocktakeResponse',
  full_name='manufactory.CreateStocktakeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.CreateStocktakeResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='manufactory.CreateStocktakeResponse.request_date', index=1,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9352,
  serialized_end=9443,
)


_CHECKEDSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='CheckedStocktakeByDocIDRequest',
  full_name='manufactory.CheckedStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='manufactory.CheckedStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.CheckedStocktakeByDocIDRequest.branch_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9445,
  serialized_end=9514,
)


_CHECKEDSTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='CheckedStocktakeByDocIDResponse',
  full_name='manufactory.CheckedStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.CheckedStocktakeByDocIDResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9516,
  serialized_end=9565,
)


_GETSTOCKTAKETAGSREQUEST = _descriptor.Descriptor(
  name='GetStocktakeTagsRequest',
  full_name='manufactory.GetStocktakeTagsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='manufactory.GetStocktakeTagsRequest.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.GetStocktakeTagsRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='manufactory.GetStocktakeTagsRequest.tag_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='manufactory.GetStocktakeTagsRequest.branch_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9567,
  serialized_end=9662,
)


_STOCKTAKETAGS = _descriptor.Descriptor(
  name='StocktakeTags',
  full_name='manufactory.StocktakeTags',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.StocktakeTags.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='manufactory.StocktakeTags.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.StocktakeTags.partner_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='manufactory.StocktakeTags.user_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.StocktakeTags.created_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.StocktakeTags.updated_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.StocktakeTags.created_at', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.StocktakeTags.updated_at', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.StocktakeTags.created_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.StocktakeTags.updated_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='manufactory.StocktakeTags.branch_id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='manufactory.StocktakeTags.branch_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9665,
  serialized_end=9963,
)


_GETSTOCKTAKETAGSRESPONSE = _descriptor.Descriptor(
  name='GetStocktakeTagsResponse',
  full_name='manufactory.GetStocktakeTagsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.GetStocktakeTagsResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9965,
  serialized_end=10033,
)


_ACTIONSTOCKTAKETAGSREQUEST = _descriptor.Descriptor(
  name='ActionStocktakeTagsRequest',
  full_name='manufactory.ActionStocktakeTagsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_id', full_name='manufactory.ActionStocktakeTagsRequest.tag_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='manufactory.ActionStocktakeTagsRequest.action', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='manufactory.ActionStocktakeTagsRequest.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='manufactory.ActionStocktakeTagsRequest.branch_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.ActionStocktakeTagsRequest.lan', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='manufactory.ActionStocktakeTagsRequest.branch_ids', index=5,
      number=6, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_ids', full_name='manufactory.ActionStocktakeTagsRequest.region_ids', index=6,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='add_dimension', full_name='manufactory.ActionStocktakeTagsRequest.add_dimension', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_ids', full_name='manufactory.ActionStocktakeTagsRequest.tag_ids', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='origin_name', full_name='manufactory.ActionStocktakeTagsRequest.origin_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='copy_branch', full_name='manufactory.ActionStocktakeTagsRequest.copy_branch', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _ACTIONSTOCKTAKETAGSREQUEST_ACTION,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10036,
  serialized_end=10377,
)


_ACTIONSTOCKTAKETAGSRESPONSE = _descriptor.Descriptor(
  name='ActionStocktakeTagsResponse',
  full_name='manufactory.ActionStocktakeTagsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.ActionStocktakeTagsResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.ActionStocktakeTagsResponse.id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='manufactory.ActionStocktakeTagsResponse.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.ActionStocktakeTagsResponse.partner_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='manufactory.ActionStocktakeTagsResponse.user_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.ActionStocktakeTagsResponse.created_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.ActionStocktakeTagsResponse.updated_by', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.ActionStocktakeTagsResponse.created_at', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.ActionStocktakeTagsResponse.updated_at', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.ActionStocktakeTagsResponse.created_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.ActionStocktakeTagsResponse.updated_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='manufactory.ActionStocktakeTagsResponse.branch_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10380,
  serialized_end=10687,
)


_GETSTOCKTAKETAGSBYIDREQUEST = _descriptor.Descriptor(
  name='GetStocktakeTagsByIdRequest',
  full_name='manufactory.GetStocktakeTagsByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_id', full_name='manufactory.GetStocktakeTagsByIdRequest.tag_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10689,
  serialized_end=10734,
)


_DELETESTOCKTAKEPRODUCTTAGSREQUEST = _descriptor.Descriptor(
  name='DeleteStocktakeProductTagsRequest',
  full_name='manufactory.DeleteStocktakeProductTagsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.DeleteStocktakeProductTagsRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10736,
  serialized_end=10783,
)


_DELETESTOCKTAKEPRODUCTTAGSRESPONSE = _descriptor.Descriptor(
  name='DeleteStocktakeProductTagsResponse',
  full_name='manufactory.DeleteStocktakeProductTagsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.DeleteStocktakeProductTagsResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10785,
  serialized_end=10837,
)


_GETSTOCKTAKEBALANCEREQUEST = _descriptor.Descriptor(
  name='GetStocktakeBalanceRequest',
  full_name='manufactory.GetStocktakeBalanceRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='manufactory.GetStocktakeBalanceRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='manufactory.GetStocktakeBalanceRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='manufactory.GetStocktakeBalanceRequest.store_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.GetStocktakeBalanceRequest.limit', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.GetStocktakeBalanceRequest.offset', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='manufactory.GetStocktakeBalanceRequest.target_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.GetStocktakeBalanceRequest.status', index=6,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='manufactory.GetStocktakeBalanceRequest.include_total', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='manufactory.GetStocktakeBalanceRequest.schedule_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='manufactory.GetStocktakeBalanceRequest.stocktake_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='manufactory.GetStocktakeBalanceRequest.is_wms_store', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.GetStocktakeBalanceRequest.branch_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.GetStocktakeBalanceRequest.lan', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.GetStocktakeBalanceRequest.type', index=13,
      number=53, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10840,
  serialized_end=11217,
)


_STOCKTAKEBALANCE = _descriptor.Descriptor(
  name='StocktakeBalance',
  full_name='manufactory.StocktakeBalance',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_batch_id', full_name='manufactory.StocktakeBalance.branch_batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='manufactory.StocktakeBalance.branch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.StocktakeBalance.branch_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_inventory', full_name='manufactory.StocktakeBalance.calculate_inventory', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.StocktakeBalance.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='forecasting', full_name='manufactory.StocktakeBalance.forecasting', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='forecasting_time', full_name='manufactory.StocktakeBalance.forecasting_time', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.StocktakeBalance.id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='manufactory.StocktakeBalance.process_status', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.StocktakeBalance.remark', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result_type', full_name='manufactory.StocktakeBalance.result_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='manufactory.StocktakeBalance.review_by', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='manufactory.StocktakeBalance.schedule_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_id', full_name='manufactory.StocktakeBalance.schedule_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='st_diff_flag', full_name='manufactory.StocktakeBalance.st_diff_flag', index=14,
      number=15, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.StocktakeBalance.status', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='manufactory.StocktakeBalance.store_secondary_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='manufactory.StocktakeBalance.target_date', index=17,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.StocktakeBalance.type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.StocktakeBalance.partner_id', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='manufactory.StocktakeBalance.user_id', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.StocktakeBalance.created_by', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.StocktakeBalance.updated_by', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.StocktakeBalance.created_name', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.StocktakeBalance.updated_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.StocktakeBalance.created_at', index=25,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.StocktakeBalance.updated_at', index=26,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='manufactory.StocktakeBalance.schedule_name', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_err_message', full_name='manufactory.StocktakeBalance.diff_err_message', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='month_err_message', full_name='manufactory.StocktakeBalance.month_err_message', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='manufactory.StocktakeBalance.branch_name', index=30,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_code', full_name='manufactory.StocktakeBalance.branch_code', index=31,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='original_code', full_name='manufactory.StocktakeBalance.original_code', index=32,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='original_doc_id', full_name='manufactory.StocktakeBalance.original_doc_id', index=33,
      number=38, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_recreate', full_name='manufactory.StocktakeBalance.is_recreate', index=34,
      number=39, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='recreate_code', full_name='manufactory.StocktakeBalance.recreate_code', index=35,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='recreate_doc_id', full_name='manufactory.StocktakeBalance.recreate_doc_id', index=36,
      number=41, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='submit_name', full_name='manufactory.StocktakeBalance.submit_name', index=37,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_name', full_name='manufactory.StocktakeBalance.approve_name', index=38,
      number=43, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='manufactory.StocktakeBalance.stocktake_type', index=39,
      number=44, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.StocktakeBalance.request_id', index=40,
      number=45, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='manufactory.StocktakeBalance.total_amount', index=41,
      number=46, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_diff_amount', full_name='manufactory.StocktakeBalance.total_diff_amount', index=42,
      number=47, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_sales_amount', full_name='manufactory.StocktakeBalance.total_sales_amount', index=43,
      number=48, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_diff_sales_amount', full_name='manufactory.StocktakeBalance.total_diff_sales_amount', index=44,
      number=49, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='manufactory.StocktakeBalance.attachments', index=45,
      number=50, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11220,
  serialized_end=12393,
)


_ATTACHMENTSMANUBALANCE = _descriptor.Descriptor(
  name='AttachmentsManuBalance',
  full_name='manufactory.AttachmentsManuBalance',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.AttachmentsManuBalance.type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='manufactory.AttachmentsManuBalance.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12395,
  serialized_end=12446,
)


_GETSTOCKTAKEBALANCERESPONSE = _descriptor.Descriptor(
  name='GetStocktakeBalanceResponse',
  full_name='manufactory.GetStocktakeBalanceResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.GetStocktakeBalanceResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.GetStocktakeBalanceResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12448,
  serialized_end=12537,
)


_GETSTOCKTAKEBALANCEPRODUCTGROUPREQUEST = _descriptor.Descriptor(
  name='GetStocktakeBalanceProductGroupRequest',
  full_name='manufactory.GetStocktakeBalanceProductGroupRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='manufactory.GetStocktakeBalanceProductGroupRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.GetStocktakeBalanceProductGroupRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12539,
  serialized_end=12608,
)


_TAGPRODUCTBI = _descriptor.Descriptor(
  name='TagProductBi',
  full_name='manufactory.TagProductBi',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='manufactory.TagProductBi.tag_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_quantity', full_name='manufactory.TagProductBi.tag_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_unit_name', full_name='manufactory.TagProductBi.tag_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_uint_rate', full_name='manufactory.TagProductBi.tag_uint_rate', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12610,
  serialized_end=12710,
)


_STOCKTAKEBALANCEPRODUCTGROUP = _descriptor.Descriptor(
  name='StocktakeBalanceProductGroup',
  full_name='manufactory.StocktakeBalanceProductGroup',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='manufactory.StocktakeBalanceProductGroup.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='manufactory.StocktakeBalanceProductGroup.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.StocktakeBalanceProductGroup.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='manufactory.StocktakeBalanceProductGroup.accounting_unit_spec', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='manufactory.StocktakeBalanceProductGroup.diff_quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='manufactory.StocktakeBalanceProductGroup.inventory_quantity', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_system', full_name='manufactory.StocktakeBalanceProductGroup.is_system', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='manufactory.StocktakeBalanceProductGroup.material_number', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.StocktakeBalanceProductGroup.product_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.StocktakeBalanceProductGroup.product_id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.StocktakeBalanceProductGroup.product_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.StocktakeBalanceProductGroup.quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='manufactory.StocktakeBalanceProductGroup.storage_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_code', full_name='manufactory.StocktakeBalanceProductGroup.tag_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='manufactory.StocktakeBalanceProductGroup.tag_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_diff_quantity', full_name='manufactory.StocktakeBalanceProductGroup.unit_diff_quantity', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.StocktakeBalanceProductGroup.unit_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.StocktakeBalanceProductGroup.unit_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='manufactory.StocktakeBalanceProductGroup.unit_rate', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='manufactory.StocktakeBalanceProductGroup.unit_spec', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_details', full_name='manufactory.StocktakeBalanceProductGroup.tag_details', index=20,
      number=21, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_inventory_quantity', full_name='manufactory.StocktakeBalanceProductGroup.accounting_inventory_quantity', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_diff_quantity', full_name='manufactory.StocktakeBalanceProductGroup.accounting_diff_quantity', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.StocktakeBalanceProductGroup.position_id', index=23,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.StocktakeBalanceProductGroup.position_code', index=24,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.StocktakeBalanceProductGroup.position_name', index=25,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12713,
  serialized_end=13385,
)


_GETSTOCKTAKEBALANCEPRODUCTGROUPRESPONSE = _descriptor.Descriptor(
  name='GetStocktakeBalanceProductGroupResponse',
  full_name='manufactory.GetStocktakeBalanceProductGroupResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.GetStocktakeBalanceProductGroupResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13387,
  serialized_end=13485,
)


_STOCKTAKEBIDETAILEDREQUEST = _descriptor.Descriptor(
  name='StocktakeBiDetailedRequest',
  full_name='manufactory.StocktakeBiDetailedRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='manufactory.StocktakeBiDetailedRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='manufactory.StocktakeBiDetailedRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='manufactory.StocktakeBiDetailedRequest.category_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.StocktakeBiDetailedRequest.type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.StocktakeBiDetailedRequest.offset', index=4,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.StocktakeBiDetailedRequest.limit', index=5,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='manufactory.StocktakeBiDetailedRequest.include_total', index=6,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.StocktakeBiDetailedRequest.product_name', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='manufactory.StocktakeBiDetailedRequest.store_ids', index=8,
      number=10, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='manufactory.StocktakeBiDetailedRequest.order', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='manufactory.StocktakeBiDetailedRequest.sort', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.StocktakeBiDetailedRequest.code', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.StocktakeBiDetailedRequest.branch_type', index=12,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.StocktakeBiDetailedRequest.lan', index=13,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='manufactory.StocktakeBiDetailedRequest.stocktake_type', index=14,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13488,
  serialized_end=13842,
)


_STOCKTAKEBIDETAILED = _descriptor.Descriptor(
  name='StocktakeBiDetailed',
  full_name='manufactory.StocktakeBiDetailed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='manufactory.StocktakeBiDetailed.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='manufactory.StocktakeBiDetailed.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.StocktakeBiDetailed.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='manufactory.StocktakeBiDetailed.accounting_unit_spec', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_1_name', full_name='manufactory.StocktakeBiDetailed.branch_1_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_2_name', full_name='manufactory.StocktakeBiDetailed.branch_2_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='manufactory.StocktakeBiDetailed.branch_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='manufactory.StocktakeBiDetailed.category_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='manufactory.StocktakeBiDetailed.category_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='manufactory.StocktakeBiDetailed.category_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.StocktakeBiDetailed.code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deleted', full_name='manufactory.StocktakeBiDetailed.deleted', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='manufactory.StocktakeBiDetailed.diff_quantity', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='display_order', full_name='manufactory.StocktakeBiDetailed.display_order', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='manufactory.StocktakeBiDetailed.doc_id', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='manufactory.StocktakeBiDetailed.extends', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.StocktakeBiDetailed.id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ignored', full_name='manufactory.StocktakeBiDetailed.ignored', index=17,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='manufactory.StocktakeBiDetailed.inventory_quantity', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_system', full_name='manufactory.StocktakeBiDetailed.is_system', index=19,
      number=20, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_number', full_name='manufactory.StocktakeBiDetailed.item_number', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='manufactory.StocktakeBiDetailed.material_number', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.StocktakeBiDetailed.product_code', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.StocktakeBiDetailed.product_id', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.StocktakeBiDetailed.product_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.StocktakeBiDetailed.quantity', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.StocktakeBiDetailed.type', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='manufactory.StocktakeBiDetailed.storage_type', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='manufactory.StocktakeBiDetailed.store_code', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='manufactory.StocktakeBiDetailed.store_name', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='manufactory.StocktakeBiDetailed.target_date', index=30,
      number=31, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_diff_quantity', full_name='manufactory.StocktakeBiDetailed.unit_diff_quantity', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.StocktakeBiDetailed.unit_id', index=32,
      number=33, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.StocktakeBiDetailed.unit_name', index=33,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='manufactory.StocktakeBiDetailed.unit_rate', index=34,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='manufactory.StocktakeBiDetailed.unit_spec', index=35,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.StocktakeBiDetailed.partner_id', index=36,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.StocktakeBiDetailed.created_by', index=37,
      number=38, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.StocktakeBiDetailed.updated_by', index=38,
      number=39, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.StocktakeBiDetailed.created_name', index=39,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.StocktakeBiDetailed.updated_name', index=40,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.StocktakeBiDetailed.created_at', index=41,
      number=42, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.StocktakeBiDetailed.updated_at', index=42,
      number=43, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_empty', full_name='manufactory.StocktakeBiDetailed.is_empty', index=43,
      number=44, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_pda', full_name='manufactory.StocktakeBiDetailed.is_pda', index=44,
      number=45, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.StocktakeBiDetailed.status', index=45,
      number=46, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='manufactory.StocktakeBiDetailed.units', index=46,
      number=47, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='manufactory.StocktakeBiDetailed.user_id', index=47,
      number=48, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_null', full_name='manufactory.StocktakeBiDetailed.is_null', index=48,
      number=49, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_quantity', full_name='manufactory.StocktakeBiDetailed.tag_quantity', index=49,
      number=50, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_tags', full_name='manufactory.StocktakeBiDetailed.product_tags', index=50,
      number=51, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_enable', full_name='manufactory.StocktakeBiDetailed.is_enable', index=51,
      number=52, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='manufactory.StocktakeBiDetailed.spec', index=52,
      number=54, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_percentage', full_name='manufactory.StocktakeBiDetailed.diff_percentage', index=53,
      number=55, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_time', full_name='manufactory.StocktakeBiDetailed.created_time', index=54,
      number=56, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_user_name', full_name='manufactory.StocktakeBiDetailed.created_user_name', index=55,
      number=57, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='submitted_time', full_name='manufactory.StocktakeBiDetailed.submitted_time', index=56,
      number=58, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='submitted_user_name', full_name='manufactory.StocktakeBiDetailed.submitted_user_name', index=57,
      number=59, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.StocktakeBiDetailed.branch_type', index=58,
      number=60, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.StocktakeBiDetailed.position_id', index=59,
      number=62, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.StocktakeBiDetailed.position_code', index=60,
      number=63, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.StocktakeBiDetailed.position_name', index=61,
      number=64, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='manufactory.StocktakeBiDetailed.stocktake_type', index=62,
      number=53, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='manufactory.StocktakeBiDetailed.schedule_code', index=63,
      number=65, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='manufactory.StocktakeBiDetailed.schedule_name', index=64,
      number=66, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13845,
  serialized_end=15400,
)


_PRODUCTTAGBI = _descriptor.Descriptor(
  name='ProductTagBi',
  full_name='manufactory.ProductTagBi',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_unit_id', full_name='manufactory.ProductTagBi.tag_unit_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_unit_name', full_name='manufactory.ProductTagBi.tag_unit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_quantity', full_name='manufactory.ProductTagBi.tag_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='manufactory.ProductTagBi.tag_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='manufactory.ProductTagBi.accounting_unit_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.ProductTagBi.accounting_unit_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='manufactory.ProductTagBi.accounting_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.ProductTagBi.id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15403,
  serialized_end=15600,
)


_ST_TOTAL = _descriptor.Descriptor(
  name='ST_total',
  full_name='manufactory.ST_total',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='count', full_name='manufactory.ST_total.count', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_quantity', full_name='manufactory.ST_total.sum_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_accounting_quantity', full_name='manufactory.ST_total.sum_accounting_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15602,
  serialized_end=15682,
)


_STOCKTAKEBIDETAILEDRESPONSE = _descriptor.Descriptor(
  name='StocktakeBiDetailedResponse',
  full_name='manufactory.StocktakeBiDetailedResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.StocktakeBiDetailedResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.StocktakeBiDetailedResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15684,
  serialized_end=15799,
)


_STOCKTAKEBALANCEREGIONREQUEST = _descriptor.Descriptor(
  name='StocktakeBalanceRegionRequest',
  full_name='manufactory.StocktakeBalanceRegionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='include_total', full_name='manufactory.StocktakeBalanceRegionRequest.include_total', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.StocktakeBalanceRegionRequest.limit', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.StocktakeBalanceRegionRequest.offset', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='manufactory.StocktakeBalanceRegionRequest.start', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end', full_name='manufactory.StocktakeBalanceRegionRequest.end', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_id', full_name='manufactory.StocktakeBalanceRegionRequest.region_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='manufactory.StocktakeBalanceRegionRequest.product_ids', index=6,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.StocktakeBalanceRegionRequest.type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.StocktakeBalanceRegionRequest.branch_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.StocktakeBalanceRegionRequest.lan', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='manufactory.StocktakeBalanceRegionRequest.stocktake_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15802,
  serialized_end=16083,
)


_STOCKTAKEBALANCEREGIONDETAILS = _descriptor.Descriptor(
  name='StocktakeBalanceRegionDetails',
  full_name='manufactory.StocktakeBalanceRegionDetails',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_inventory_quantity', full_name='manufactory.StocktakeBalanceRegionDetails.accounting_inventory_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='manufactory.StocktakeBalanceRegionDetails.accounting_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.StocktakeBalanceRegionDetails.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='manufactory.StocktakeBalanceRegionDetails.branch_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='manufactory.StocktakeBalanceRegionDetails.diff_quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_type', full_name='manufactory.StocktakeBalanceRegionDetails.doc_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='manufactory.StocktakeBalanceRegionDetails.inventory_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='manufactory.StocktakeBalanceRegionDetails.material_number', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.StocktakeBalanceRegionDetails.product_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.StocktakeBalanceRegionDetails.product_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.StocktakeBalanceRegionDetails.quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='manufactory.StocktakeBalanceRegionDetails.store_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='manufactory.StocktakeBalanceRegionDetails.store_name', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_1', full_name='manufactory.StocktakeBalanceRegionDetails.tag_1', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_1_name_pinyin', full_name='manufactory.StocktakeBalanceRegionDetails.tag_1_name_pinyin', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_diff_quantity', full_name='manufactory.StocktakeBalanceRegionDetails.unit_diff_quantity', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.StocktakeBalanceRegionDetails.unit_name', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='manufactory.StocktakeBalanceRegionDetails.unit_rate', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=16086,
  serialized_end=16536,
)


_STOCKTAKEBALANCEREGION = _descriptor.Descriptor(
  name='StocktakeBalanceRegion',
  full_name='manufactory.StocktakeBalanceRegion',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_inventory_quantity', full_name='manufactory.StocktakeBalanceRegion.accounting_inventory_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='manufactory.StocktakeBalanceRegion.accounting_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.StocktakeBalanceRegion.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='details', full_name='manufactory.StocktakeBalanceRegion.details', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.StocktakeBalanceRegion.unit_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.StocktakeBalanceRegion.product_name', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.StocktakeBalanceRegion.quantity', index=6,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='manufactory.StocktakeBalanceRegion.inventory_quantity', index=7,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accoinventory_quantity', full_name='manufactory.StocktakeBalanceRegion.accoinventory_quantity', index=8,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='manufactory.StocktakeBalanceRegion.diff_quantity', index=9,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_diff_quantity', full_name='manufactory.StocktakeBalanceRegion.unit_diff_quantity', index=10,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='manufactory.StocktakeBalanceRegion.unit_rate', index=11,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.StocktakeBalanceRegion.type', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.StocktakeBalanceRegion.product_id', index=13,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='manufactory.StocktakeBalanceRegion.store_id', index=14,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='manufactory.StocktakeBalanceRegion.store_name', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='manufactory.StocktakeBalanceRegion.store_code', index=16,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.StocktakeBalanceRegion.product_code', index=17,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_diff_quantity', full_name='manufactory.StocktakeBalanceRegion.accounting_diff_quantity', index=18,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='manufactory.StocktakeBalanceRegion.stocktake_type', index=19,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=16539,
  serialized_end=17083,
)


_STOCKTAKEBALANCEREGIONRESPONSE = _descriptor.Descriptor(
  name='StocktakeBalanceRegionResponse',
  full_name='manufactory.StocktakeBalanceRegionResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.StocktakeBalanceRegionResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.StocktakeBalanceRegionResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17085,
  serialized_end=17183,
)


_STOREDATASCOPEREQUEST = _descriptor.Descriptor(
  name='StoreDataScopeRequest',
  full_name='manufactory.StoreDataScopeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='search', full_name='manufactory.StoreDataScopeRequest.search', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='manufactory.StoreDataScopeRequest.search_fields', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='manufactory.StoreDataScopeRequest.ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='manufactory.StoreDataScopeRequest.return_fields', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='manufactory.StoreDataScopeRequest.filters', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation_filters', full_name='manufactory.StoreDataScopeRequest.relation_filters', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.StoreDataScopeRequest.limit', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.StoreDataScopeRequest.offset', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.StoreDataScopeRequest.lan', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17186,
  serialized_end=17371,
)


_SCOPESTORES = _descriptor.Descriptor(
  name='ScopeStores',
  full_name='manufactory.ScopeStores',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.ScopeStores.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='manufactory.ScopeStores.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.ScopeStores.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='second_code', full_name='manufactory.ScopeStores.second_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.ScopeStores.type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='address', full_name='manufactory.ScopeStores.address', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tel', full_name='manufactory.ScopeStores.tel', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contact', full_name='manufactory.ScopeStores.contact', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.ScopeStores.status', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name_en', full_name='manufactory.ScopeStores.name_en', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='open_date', full_name='manufactory.ScopeStores.open_date', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='close_date', full_name='manufactory.ScopeStores.close_date', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='email', full_name='manufactory.ScopeStores.email', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='geo_region', full_name='manufactory.ScopeStores.geo_region', index=13,
      number=14, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_region', full_name='manufactory.ScopeStores.branch_region', index=14,
      number=15, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_region', full_name='manufactory.ScopeStores.order_region', index=15,
      number=16, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_region', full_name='manufactory.ScopeStores.distribution_region', index=16,
      number=17, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_region', full_name='manufactory.ScopeStores.purchase_region', index=17,
      number=18, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='market_region', full_name='manufactory.ScopeStores.market_region', index=18,
      number=19, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_region', full_name='manufactory.ScopeStores.transfer_region', index=19,
      number=20, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attribute_region', full_name='manufactory.ScopeStores.attribute_region', index=20,
      number=21, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17374,
  serialized_end=17789,
)


_STOREDATASCOPE = _descriptor.Descriptor(
  name='StoreDataScope',
  full_name='manufactory.StoreDataScope',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.StoreDataScope.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.StoreDataScope.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17791,
  serialized_end=17862,
)


_ADVANCESTOCKTAKEDIFFREQUEST = _descriptor.Descriptor(
  name='AdvanceStocktakeDiffRequest',
  full_name='manufactory.AdvanceStocktakeDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='manufactory.AdvanceStocktakeDiffRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.AdvanceStocktakeDiffRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17864,
  serialized_end=17922,
)


_ADVANCESTOCKTAKEDIFFRESPONSE = _descriptor.Descriptor(
  name='AdvanceStocktakeDiffResponse',
  full_name='manufactory.AdvanceStocktakeDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.AdvanceStocktakeDiffResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.AdvanceStocktakeDiffResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_rows', full_name='manufactory.AdvanceStocktakeDiffResponse.position_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17925,
  serialized_end=18078,
)


_STOCKTAKEDIFFREPORTREQUEST = _descriptor.Descriptor(
  name='StocktakeDiffReportRequest',
  full_name='manufactory.StocktakeDiffReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='manufactory.StocktakeDiffReportRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='manufactory.StocktakeDiffReportRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='manufactory.StocktakeDiffReportRequest.store_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='manufactory.StocktakeDiffReportRequest.product_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.StocktakeDiffReportRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.StocktakeDiffReportRequest.offset', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='manufactory.StocktakeDiffReportRequest.target_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.StocktakeDiffReportRequest.code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='manufactory.StocktakeDiffReportRequest.stocktake_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='manufactory.StocktakeDiffReportRequest.schedule_code', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.StocktakeDiffReportRequest.status', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='manufactory.StocktakeDiffReportRequest.order', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='manufactory.StocktakeDiffReportRequest.sort', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='upper_limit', full_name='manufactory.StocktakeDiffReportRequest.upper_limit', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lower_limit', full_name='manufactory.StocktakeDiffReportRequest.lower_limit', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='manufactory.StocktakeDiffReportRequest.is_wms_store', index=15,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.StocktakeDiffReportRequest.lan', index=16,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=18081,
  serialized_end=18506,
)


_STOCKTAKEDIFFREPORTROW = _descriptor.Descriptor(
  name='StocktakeDiffReportRow',
  full_name='manufactory.StocktakeDiffReportRow',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.StocktakeDiffReportRow.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='manufactory.StocktakeDiffReportRow.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='manufactory.StocktakeDiffReportRow.store_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='manufactory.StocktakeDiffReportRow.store_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_code', full_name='manufactory.StocktakeDiffReportRow.company_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.StocktakeDiffReportRow.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.StocktakeDiffReportRow.product_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.StocktakeDiffReportRow.product_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.StocktakeDiffReportRow.type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='manufactory.StocktakeDiffReportRow.diff_quantity', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.StocktakeDiffReportRow.quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='manufactory.StocktakeDiffReportRow.accounting_quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='manufactory.StocktakeDiffReportRow.inventory_quantity', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity_percentage', full_name='manufactory.StocktakeDiffReportRow.diff_quantity_percentage', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='manufactory.StocktakeDiffReportRow.target_date', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.StocktakeDiffReportRow.unit_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.StocktakeDiffReportRow.unit_name', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='manufactory.StocktakeDiffReportRow.unit_rate', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_code', full_name='manufactory.StocktakeDiffReportRow.unit_code', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='manufactory.StocktakeDiffReportRow.accounting_unit_id', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.StocktakeDiffReportRow.accounting_unit_name', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='manufactory.StocktakeDiffReportRow.accounting_unit_code', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_name', full_name='manufactory.StocktakeDiffReportRow.company_name', index=22,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_inventory_quantity', full_name='manufactory.StocktakeDiffReportRow.accounting_inventory_quantity', index=23,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_diff_quantity', full_name='manufactory.StocktakeDiffReportRow.accounting_diff_quantity', index=24,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='manufactory.StocktakeDiffReportRow.unit_spec', index=25,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.StocktakeDiffReportRow.status', index=26,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='manufactory.StocktakeDiffReportRow.schedule_code', index=27,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=18509,
  serialized_end=19201,
)


_STOCKTAKEDIFFREPORTRESPONSE = _descriptor.Descriptor(
  name='StocktakeDiffReportResponse',
  full_name='manufactory.StocktakeDiffReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.StocktakeDiffReportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.StocktakeDiffReportResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=19203,
  serialized_end=19298,
)


_GETUNCOMPLETEDOCREQUEST = _descriptor.Descriptor(
  name='GetUncompleteDocRequest',
  full_name='manufactory.GetUncompleteDocRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='manufactory.GetUncompleteDocRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='manufactory.GetUncompleteDocRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='manufactory.GetUncompleteDocRequest.store_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.GetUncompleteDocRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=19301,
  serialized_end=19451,
)


_CHECKDEMANDDETAIL = _descriptor.Descriptor(
  name='CheckDemandDetail',
  full_name='manufactory.CheckDemandDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.CheckDemandDetail.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.CheckDemandDetail.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.CheckDemandDetail.type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_plan', full_name='manufactory.CheckDemandDetail.is_plan', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=19453,
  serialized_end=19529,
)


_GETUNCOMPLETEDOCRESPONSE = _descriptor.Descriptor(
  name='GetUncompleteDocResponse',
  full_name='manufactory.GetUncompleteDocResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='handler', full_name='manufactory.GetUncompleteDocResponse.handler', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust', full_name='manufactory.GetUncompleteDocResponse.adjust', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_diff', full_name='manufactory.GetUncompleteDocResponse.receiving_diff', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer', full_name='manufactory.GetUncompleteDocResponse.transfer', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving', full_name='manufactory.GetUncompleteDocResponse.receiving', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return', full_name='manufactory.GetUncompleteDocResponse.return', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_receiving', full_name='manufactory.GetUncompleteDocResponse.direct_receiving', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_receiving_diff', full_name='manufactory.GetUncompleteDocResponse.direct_receiving_diff', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_return', full_name='manufactory.GetUncompleteDocResponse.direct_return', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='manufactory.GetUncompleteDocResponse.stocktake', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand', full_name='manufactory.GetUncompleteDocResponse.demand', index=10,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='assets', full_name='manufactory.GetUncompleteDocResponse.assets', index=11,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=19532,
  serialized_end=20251,
)


_RECREATESTOCKTAKEDOCREQUEST = _descriptor.Descriptor(
  name='RecreateStocktakeDocRequest',
  full_name='manufactory.RecreateStocktakeDocRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_ids', full_name='manufactory.RecreateStocktakeDocRequest.doc_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_inventory', full_name='manufactory.RecreateStocktakeDocRequest.calculate_inventory', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='manufactory.RecreateStocktakeDocRequest.schedule_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.RecreateStocktakeDocRequest.remark', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='manufactory.RecreateStocktakeDocRequest.schedule_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_id', full_name='manufactory.RecreateStocktakeDocRequest.schedule_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.RecreateStocktakeDocRequest.lan', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=20254,
  serialized_end=20425,
)


_RECREATESTOCKTAKEDOCRESPONSE = _descriptor.Descriptor(
  name='RecreateStocktakeDocResponse',
  full_name='manufactory.RecreateStocktakeDocResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.RecreateStocktakeDocResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='restocktake_doc_ids', full_name='manufactory.RecreateStocktakeDocResponse.restocktake_doc_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='has_recreate_doc_id_no_confirm', full_name='manufactory.RecreateStocktakeDocResponse.has_recreate_doc_id_no_confirm', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=20427,
  serialized_end=20542,
)


_STOCKTAKEDOCSTATISTICSREQUEST = _descriptor.Descriptor(
  name='StocktakeDocStatisticsRequest',
  full_name='manufactory.StocktakeDocStatisticsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='manufactory.StocktakeDocStatisticsRequest.store_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='manufactory.StocktakeDocStatisticsRequest.start_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='manufactory.StocktakeDocStatisticsRequest.end_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_group_by', full_name='manufactory.StocktakeDocStatisticsRequest.period_group_by', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='manufactory.StocktakeDocStatisticsRequest.stocktake_type', index=4,
      number=5, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.StocktakeDocStatisticsRequest.status', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.StocktakeDocStatisticsRequest.limit', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.StocktakeDocStatisticsRequest.offset', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='manufactory.StocktakeDocStatisticsRequest.order', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='manufactory.StocktakeDocStatisticsRequest.sort', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='manufactory.StocktakeDocStatisticsRequest.is_wms_store', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.StocktakeDocStatisticsRequest.lan', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=20545,
  serialized_end=20881,
)


_STOCKTAKEDOCSTATISTICS = _descriptor.Descriptor(
  name='StocktakeDocStatistics',
  full_name='manufactory.StocktakeDocStatistics',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='date', full_name='manufactory.StocktakeDocStatistics.date', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='manufactory.StocktakeDocStatistics.store_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='manufactory.StocktakeDocStatistics.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='manufactory.StocktakeDocStatistics.stocktake_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.StocktakeDocStatistics.status', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='count', full_name='manufactory.StocktakeDocStatistics.count', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='manufactory.StocktakeDocStatistics.store_id', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=20884,
  serialized_end=21035,
)


_STOCKTAKEDOCSTATISTICSRESPONSE = _descriptor.Descriptor(
  name='StocktakeDocStatisticsResponse',
  full_name='manufactory.StocktakeDocStatisticsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.StocktakeDocStatisticsResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.StocktakeDocStatisticsResponse.total', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=21037,
  serialized_end=21135,
)


_STOCKTAKEDIFFCOLLECTREPORTREQUEST = _descriptor.Descriptor(
  name='StocktakeDiffCollectReportRequest',
  full_name='manufactory.StocktakeDiffCollectReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='manufactory.StocktakeDiffCollectReportRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='manufactory.StocktakeDiffCollectReportRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='manufactory.StocktakeDiffCollectReportRequest.store_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='manufactory.StocktakeDiffCollectReportRequest.product_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.StocktakeDiffCollectReportRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.StocktakeDiffCollectReportRequest.offset', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='manufactory.StocktakeDiffCollectReportRequest.target_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.StocktakeDiffCollectReportRequest.code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='manufactory.StocktakeDiffCollectReportRequest.stocktake_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='manufactory.StocktakeDiffCollectReportRequest.schedule_code', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.StocktakeDiffCollectReportRequest.status', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='manufactory.StocktakeDiffCollectReportRequest.order', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='manufactory.StocktakeDiffCollectReportRequest.sort', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='manufactory.StocktakeDiffCollectReportRequest.period_symbol', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='upper_limit', full_name='manufactory.StocktakeDiffCollectReportRequest.upper_limit', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lower_limit', full_name='manufactory.StocktakeDiffCollectReportRequest.lower_limit', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='manufactory.StocktakeDiffCollectReportRequest.is_wms_store', index=16,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.StocktakeDiffCollectReportRequest.lan', index=17,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=21138,
  serialized_end=21593,
)


_STOCKTAKEDIFFCOLLECTREPORTROW = _descriptor.Descriptor(
  name='StocktakeDiffCollectReportRow',
  full_name='manufactory.StocktakeDiffCollectReportRow',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='manufactory.StocktakeDiffCollectReportRow.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='manufactory.StocktakeDiffCollectReportRow.store_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='manufactory.StocktakeDiffCollectReportRow.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_code', full_name='manufactory.StocktakeDiffCollectReportRow.company_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.StocktakeDiffCollectReportRow.product_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.StocktakeDiffCollectReportRow.product_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.StocktakeDiffCollectReportRow.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='manufactory.StocktakeDiffCollectReportRow.diff_quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.StocktakeDiffCollectReportRow.quantity', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='manufactory.StocktakeDiffCollectReportRow.accounting_quantity', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='manufactory.StocktakeDiffCollectReportRow.inventory_quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity_percentage', full_name='manufactory.StocktakeDiffCollectReportRow.diff_quantity_percentage', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.StocktakeDiffCollectReportRow.unit_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.StocktakeDiffCollectReportRow.unit_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='manufactory.StocktakeDiffCollectReportRow.unit_rate', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_code', full_name='manufactory.StocktakeDiffCollectReportRow.unit_code', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='manufactory.StocktakeDiffCollectReportRow.accounting_unit_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.StocktakeDiffCollectReportRow.accounting_unit_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='manufactory.StocktakeDiffCollectReportRow.accounting_unit_code', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_name', full_name='manufactory.StocktakeDiffCollectReportRow.company_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_inventory_quantity', full_name='manufactory.StocktakeDiffCollectReportRow.accounting_inventory_quantity', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_diff_quantity', full_name='manufactory.StocktakeDiffCollectReportRow.accounting_diff_quantity', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='manufactory.StocktakeDiffCollectReportRow.unit_spec', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='manufactory.StocktakeDiffCollectReportRow.period_symbol', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=21596,
  serialized_end=22202,
)


_STOCKTAKEDIFFCOLLECTREPORTRESPONSE = _descriptor.Descriptor(
  name='StocktakeDiffCollectReportResponse',
  full_name='manufactory.StocktakeDiffCollectReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.StocktakeDiffCollectReportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.StocktakeDiffCollectReportResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22204,
  serialized_end=22313,
)


_UNCOMPLETEDOCREPORTREQUEST = _descriptor.Descriptor(
  name='UncompleteDocReportRequest',
  full_name='manufactory.UncompleteDocReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bus_date', full_name='manufactory.UncompleteDocReportRequest.bus_date', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='manufactory.UncompleteDocReportRequest.store_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.UncompleteDocReportRequest.code', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.UncompleteDocReportRequest.limit', index=3,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.UncompleteDocReportRequest.offset', index=4,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model', full_name='manufactory.UncompleteDocReportRequest.model', index=5,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='manufactory.UncompleteDocReportRequest.order', index=6,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='manufactory.UncompleteDocReportRequest.sort', index=7,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='manufactory.UncompleteDocReportRequest.is_wms_store', index=8,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.UncompleteDocReportRequest.lan', index=9,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22316,
  serialized_end=22505,
)


_UNCOMPLETEDOCREPORTRESPONSE_UNCOMPLETEDOCREPORT = _descriptor.Descriptor(
  name='UncompleteDocReport',
  full_name='manufactory.UncompleteDocReportResponse.UncompleteDocReport',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='model', full_name='manufactory.UncompleteDocReportResponse.UncompleteDocReport.model', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='manufactory.UncompleteDocReportResponse.UncompleteDocReport.store_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='manufactory.UncompleteDocReportResponse.UncompleteDocReport.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.UncompleteDocReportResponse.UncompleteDocReport.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.UncompleteDocReportResponse.UncompleteDocReport.code', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_update_name', full_name='manufactory.UncompleteDocReportResponse.UncompleteDocReport.doc_update_name', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_update_time', full_name='manufactory.UncompleteDocReportResponse.UncompleteDocReport.doc_update_time', index=6,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_date', full_name='manufactory.UncompleteDocReportResponse.UncompleteDocReport.doc_date', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bus_date', full_name='manufactory.UncompleteDocReportResponse.UncompleteDocReport.bus_date', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='manufactory.UncompleteDocReportResponse.UncompleteDocReport.store_id', index=9,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22631,
  serialized_end=22841,
)

_UNCOMPLETEDOCREPORTRESPONSE = _descriptor.Descriptor(
  name='UncompleteDocReportResponse',
  full_name='manufactory.UncompleteDocReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.UncompleteDocReportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.UncompleteDocReportResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_UNCOMPLETEDOCREPORTRESPONSE_UNCOMPLETEDOCREPORT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22508,
  serialized_end=22841,
)


_STOCKTAKEPRODUCTIMPORTREQUEST = _descriptor.Descriptor(
  name='StocktakeProductImportRequest',
  full_name='manufactory.StocktakeProductImportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='manufactory.StocktakeProductImportRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_name', full_name='manufactory.StocktakeProductImportRequest.file_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_data', full_name='manufactory.StocktakeProductImportRequest.file_data', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22843,
  serialized_end=22928,
)


_PRODUCTIMPORTRESPONSEROWS = _descriptor.Descriptor(
  name='ProductImportResponseRows',
  full_name='manufactory.ProductImportResponseRows',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_num', full_name='manufactory.ProductImportResponseRows.row_num', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.ProductImportResponseRows.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.ProductImportResponseRows.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.ProductImportResponseRows.quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='manufactory.ProductImportResponseRows.storage_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='manufactory.ProductImportResponseRows.spec', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit', full_name='manufactory.ProductImportResponseRows.unit', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error_msg', full_name='manufactory.ProductImportResponseRows.error_msg', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.ProductImportResponseRows.position_id', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.ProductImportResponseRows.position_code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.ProductImportResponseRows.position_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity1', full_name='manufactory.ProductImportResponseRows.quantity1', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit1', full_name='manufactory.ProductImportResponseRows.unit1', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity2', full_name='manufactory.ProductImportResponseRows.quantity2', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit2', full_name='manufactory.ProductImportResponseRows.unit2', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity3', full_name='manufactory.ProductImportResponseRows.quantity3', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit3', full_name='manufactory.ProductImportResponseRows.unit3', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity4', full_name='manufactory.ProductImportResponseRows.quantity4', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit4', full_name='manufactory.ProductImportResponseRows.unit4', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22931,
  serialized_end=23309,
)


_STOCKTAKEPRODUCTIMPORTRESPONSE = _descriptor.Descriptor(
  name='StocktakeProductImportResponse',
  full_name='manufactory.StocktakeProductImportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.StocktakeProductImportResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.StocktakeProductImportResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_name', full_name='manufactory.StocktakeProductImportResponse.file_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows_num', full_name='manufactory.StocktakeProductImportResponse.rows_num', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='manufactory.StocktakeProductImportResponse.batch_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=23312,
  serialized_end=23469,
)


_UPDATESTOCKTAKEIMPORTBATCHREQUEST = _descriptor.Descriptor(
  name='UpdateStocktakeImportBatchRequest',
  full_name='manufactory.UpdateStocktakeImportBatchRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='manufactory.UpdateStocktakeImportBatchRequest.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.UpdateStocktakeImportBatchRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=23471,
  serialized_end=23540,
)


_UPDATESTOCKTAKEIMPORTBATCHRESPONSE = _descriptor.Descriptor(
  name='UpdateStocktakeImportBatchResponse',
  full_name='manufactory.UpdateStocktakeImportBatchResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.UpdateStocktakeImportBatchResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='manufactory.UpdateStocktakeImportBatchResponse.doc_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=23542,
  serialized_end=23610,
)


_STOCKTAKEPOSITIONPRODUCTS = _descriptor.Descriptor(
  name='StocktakePositionProducts',
  full_name='manufactory.StocktakePositionProducts',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.StocktakePositionProducts.position_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.StocktakePositionProducts.position_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.StocktakePositionProducts.position_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='manufactory.StocktakePositionProducts.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.StocktakePositionProducts.total', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=23613,
  serialized_end=23771,
)

_GETPRODUCTBYSTOCKTAKETYPERESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEPRODUCTTYPE
_STOCKTAKE.fields_by_name['status'].enum_type = _STOCKTAKE_STATUS
_STOCKTAKE.fields_by_name['process_status'].enum_type = _STOCKTAKE_STATUS
_STOCKTAKE.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKE.fields_by_name['attachments'].message_type = _ATTACHMENTSSTOCK
_STOCKTAKE_STATUS.containing_type = _STOCKTAKE
_GETSTOCKTAKEREQUEST.fields_by_name['store_status'].enum_type = _GETSTOCKTAKEREQUEST_S_STATUS
_GETSTOCKTAKEREQUEST.fields_by_name['_type'].enum_type = _GETSTOCKTAKEREQUEST_S_TYPE
_GETSTOCKTAKEREQUEST.fields_by_name['status'].enum_type = _GETSTOCKTAKEREQUEST_STATUS
_GETSTOCKTAKEREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEREQUEST.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEREQUEST_S_STATUS.containing_type = _GETSTOCKTAKEREQUEST
_GETSTOCKTAKEREQUEST_S_TYPE.containing_type = _GETSTOCKTAKEREQUEST
_GETSTOCKTAKEREQUEST_STATUS.containing_type = _GETSTOCKTAKEREQUEST
_GETSTOCKTAKERESPONSE.fields_by_name['rows'].message_type = _STOCKTAKE
_STOCKTAKEPRODUCTTAGNAME.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEPRODUCTTAGNAME.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEPRODUCT.fields_by_name['units'].message_type = _STOCKTAKEPRODUCTUNITS
_STOCKTAKEPRODUCT.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEPRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEPRODUCT.fields_by_name['product_tags'].message_type = _STOCKTAKEPRODUCTTAGNAME
_GETSTOCKTAKEPRODUCTRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEPRODUCT
_GETSTOCKTAKEPRODUCTRESPONSE.fields_by_name['position_rows'].message_type = _STOCKTAKEPOSITIONPRODUCTS
_USERCREATESTOCKTAKEREQUEST.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_USERCREATESTOCKTAKERESPONSE.fields_by_name['status'].enum_type = _USERCREATESTOCKTAKERESPONSE_STATUS
_USERCREATESTOCKTAKERESPONSE.fields_by_name['process_status'].enum_type = _USERCREATESTOCKTAKERESPONSE_STATUS
_USERCREATESTOCKTAKERESPONSE.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_USERCREATESTOCKTAKERESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_USERCREATESTOCKTAKERESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_USERCREATESTOCKTAKERESPONSE_STATUS.containing_type = _USERCREATESTOCKTAKERESPONSE
_PUTSTOCKTAKEPRODUCTS.fields_by_name['tag_products'].message_type = _TAGQUANTITY
_PUTSTOCKTAKEBYDOCIDREQUEST.fields_by_name['products'].message_type = _PUTSTOCKTAKEPRODUCTS
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['adjust'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['receiving_diff'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['transfer'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['receiving'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['return'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['direct_receiving'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['direct_receiving_diff'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['direct_return'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['stocktake'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['demand'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['self_picking'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['adjust'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['receiving_diff'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['transfer'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['receiving'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['return'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['direct_receiving'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['direct_receiving_diff'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['direct_return'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['self_picking'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CREATESTOCKTAKEBATCHREQUEST.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATESTOCKTAKEBATCHRESPONSE.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATESTOCKTAKEREQUEST.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATESTOCKTAKERESPONSE.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKETAGS.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKETAGS.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKETAGSRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKETAGS
_ACTIONSTOCKTAKETAGSREQUEST.fields_by_name['action'].enum_type = _ACTIONSTOCKTAKETAGSREQUEST_ACTION
_ACTIONSTOCKTAKETAGSREQUEST_ACTION.containing_type = _ACTIONSTOCKTAKETAGSREQUEST
_ACTIONSTOCKTAKETAGSRESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ACTIONSTOCKTAKETAGSRESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEBALANCEREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEBALANCEREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEBALANCEREQUEST.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCE.fields_by_name['forecasting_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCE.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCE.fields_by_name['attachments'].message_type = _ATTACHMENTSMANUBALANCE
_GETSTOCKTAKEBALANCERESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEBALANCE
_STOCKTAKEBALANCEPRODUCTGROUP.fields_by_name['tag_details'].message_type = _TAGPRODUCTBI
_GETSTOCKTAKEBALANCEPRODUCTGROUPRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEBALANCEPRODUCTGROUP
_STOCKTAKEBIDETAILEDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILEDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILED.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILED.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILED.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILED.fields_by_name['product_tags'].message_type = _PRODUCTTAGBI
_STOCKTAKEBIDETAILED.fields_by_name['created_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILED.fields_by_name['submitted_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILEDRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEBIDETAILED
_STOCKTAKEBIDETAILEDRESPONSE.fields_by_name['total'].message_type = _ST_TOTAL
_STOCKTAKEBALANCEREGIONREQUEST.fields_by_name['start'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCEREGIONREQUEST.fields_by_name['end'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCEREGION.fields_by_name['details'].message_type = _STOCKTAKEBALANCEREGIONDETAILS
_STOCKTAKEBALANCEREGIONRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEBALANCEREGION
_STOREDATASCOPE.fields_by_name['rows'].message_type = _SCOPESTORES
_ADVANCESTOCKTAKEDIFFRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEPRODUCT
_ADVANCESTOCKTAKEDIFFRESPONSE.fields_by_name['position_rows'].message_type = _STOCKTAKEPOSITIONPRODUCTS
_STOCKTAKEDIFFREPORTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFREPORTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFREPORTREQUEST.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFREPORTROW.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFREPORTRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEDIFFREPORTROW
_GETUNCOMPLETEDOCREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETUNCOMPLETEDOCREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['adjust'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['receiving_diff'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['transfer'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['receiving'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['return'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['direct_receiving'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['direct_receiving_diff'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['direct_return'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['stocktake'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['demand'].message_type = _CHECKDEMANDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['assets'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_STOCKTAKEDOCSTATISTICSREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDOCSTATISTICSREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDOCSTATISTICSREQUEST.fields_by_name['period_group_by'].enum_type = _PERIODGROUPMETHOD
_STOCKTAKEDOCSTATISTICSRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEDOCSTATISTICS
_STOCKTAKEDIFFCOLLECTREPORTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFCOLLECTREPORTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFCOLLECTREPORTREQUEST.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFCOLLECTREPORTRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEDIFFCOLLECTREPORTROW
_UNCOMPLETEDOCREPORTRESPONSE_UNCOMPLETEDOCREPORT.containing_type = _UNCOMPLETEDOCREPORTRESPONSE
_UNCOMPLETEDOCREPORTRESPONSE.fields_by_name['rows'].message_type = _UNCOMPLETEDOCREPORTRESPONSE_UNCOMPLETEDOCREPORT
_STOCKTAKEPRODUCTIMPORTRESPONSE.fields_by_name['rows'].message_type = _PRODUCTIMPORTRESPONSEROWS
_STOCKTAKEPOSITIONPRODUCTS.fields_by_name['products'].message_type = _STOCKTAKEPRODUCT
DESCRIPTOR.message_types_by_name['GetProductByStocktakeTypeRequest'] = _GETPRODUCTBYSTOCKTAKETYPEREQUEST
DESCRIPTOR.message_types_by_name['StocktakeProductType'] = _STOCKTAKEPRODUCTTYPE
DESCRIPTOR.message_types_by_name['GetProductByStocktakeTypeResponse'] = _GETPRODUCTBYSTOCKTAKETYPERESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeByDocIDRequest'] = _GETSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['Stocktake'] = _STOCKTAKE
DESCRIPTOR.message_types_by_name['AttachmentsStock'] = _ATTACHMENTSSTOCK
DESCRIPTOR.message_types_by_name['GetStocktakeRequest'] = _GETSTOCKTAKEREQUEST
DESCRIPTOR.message_types_by_name['GetStocktakeResponse'] = _GETSTOCKTAKERESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeProductRequest'] = _GETSTOCKTAKEPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['StocktakeProductTagName'] = _STOCKTAKEPRODUCTTAGNAME
DESCRIPTOR.message_types_by_name['StocktakeProductUnits'] = _STOCKTAKEPRODUCTUNITS
DESCRIPTOR.message_types_by_name['StocktakeProduct'] = _STOCKTAKEPRODUCT
DESCRIPTOR.message_types_by_name['GetStocktakeProductResponse'] = _GETSTOCKTAKEPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['UserCreateStocktakeRequest'] = _USERCREATESTOCKTAKEREQUEST
DESCRIPTOR.message_types_by_name['UserCreateStocktakeResponse'] = _USERCREATESTOCKTAKERESPONSE
DESCRIPTOR.message_types_by_name['TagQuantity'] = _TAGQUANTITY
DESCRIPTOR.message_types_by_name['PutStocktakeProducts'] = _PUTSTOCKTAKEPRODUCTS
DESCRIPTOR.message_types_by_name['PutStocktakeByDocIDRequest'] = _PUTSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['RejectStocktakeProductRequest'] = _REJECTSTOCKTAKEPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['RejectStocktakeProductResponse'] = _REJECTSTOCKTAKEPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['PutStocktakeByDocIDResponse'] = _PUTSTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['CheckStocktakeByDocIDRequest'] = _CHECKSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['CheckStocktakeByDocIDDetail'] = _CHECKSTOCKTAKEBYDOCIDDETAIL
DESCRIPTOR.message_types_by_name['CheckStocktakeByDocIDResponse'] = _CHECKSTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['ConfirmStocktakeByDocIDRequest'] = _CONFIRMSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['SubmitStocktakeByDocIDResponse'] = _SUBMITSTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['SubmitStocktakeByDocIDRequest'] = _SUBMITSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['ApproveStocktakeByDocIDRequest'] = _APPROVESTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['ApproveStocktakeByDocIDResponse'] = _APPROVESTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['ConfirmStocktakeByDocIDResponse'] = _CONFIRMSTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['CancelStocktakeByDocIDRequest'] = _CANCELSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['CancelStocktakeByDocIDResponse'] = _CANCELSTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['CreateStocktakeBatchRequest'] = _CREATESTOCKTAKEBATCHREQUEST
DESCRIPTOR.message_types_by_name['CreateStocktakeBatchResponse'] = _CREATESTOCKTAKEBATCHRESPONSE
DESCRIPTOR.message_types_by_name['CreateStocktakeRequest'] = _CREATESTOCKTAKEREQUEST
DESCRIPTOR.message_types_by_name['CreateStocktakeResponse'] = _CREATESTOCKTAKERESPONSE
DESCRIPTOR.message_types_by_name['CheckedStocktakeByDocIDRequest'] = _CHECKEDSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['CheckedStocktakeByDocIDResponse'] = _CHECKEDSTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeTagsRequest'] = _GETSTOCKTAKETAGSREQUEST
DESCRIPTOR.message_types_by_name['StocktakeTags'] = _STOCKTAKETAGS
DESCRIPTOR.message_types_by_name['GetStocktakeTagsResponse'] = _GETSTOCKTAKETAGSRESPONSE
DESCRIPTOR.message_types_by_name['ActionStocktakeTagsRequest'] = _ACTIONSTOCKTAKETAGSREQUEST
DESCRIPTOR.message_types_by_name['ActionStocktakeTagsResponse'] = _ACTIONSTOCKTAKETAGSRESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeTagsByIdRequest'] = _GETSTOCKTAKETAGSBYIDREQUEST
DESCRIPTOR.message_types_by_name['DeleteStocktakeProductTagsRequest'] = _DELETESTOCKTAKEPRODUCTTAGSREQUEST
DESCRIPTOR.message_types_by_name['DeleteStocktakeProductTagsResponse'] = _DELETESTOCKTAKEPRODUCTTAGSRESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeBalanceRequest'] = _GETSTOCKTAKEBALANCEREQUEST
DESCRIPTOR.message_types_by_name['StocktakeBalance'] = _STOCKTAKEBALANCE
DESCRIPTOR.message_types_by_name['AttachmentsManuBalance'] = _ATTACHMENTSMANUBALANCE
DESCRIPTOR.message_types_by_name['GetStocktakeBalanceResponse'] = _GETSTOCKTAKEBALANCERESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeBalanceProductGroupRequest'] = _GETSTOCKTAKEBALANCEPRODUCTGROUPREQUEST
DESCRIPTOR.message_types_by_name['TagProductBi'] = _TAGPRODUCTBI
DESCRIPTOR.message_types_by_name['StocktakeBalanceProductGroup'] = _STOCKTAKEBALANCEPRODUCTGROUP
DESCRIPTOR.message_types_by_name['GetStocktakeBalanceProductGroupResponse'] = _GETSTOCKTAKEBALANCEPRODUCTGROUPRESPONSE
DESCRIPTOR.message_types_by_name['StocktakeBiDetailedRequest'] = _STOCKTAKEBIDETAILEDREQUEST
DESCRIPTOR.message_types_by_name['StocktakeBiDetailed'] = _STOCKTAKEBIDETAILED
DESCRIPTOR.message_types_by_name['ProductTagBi'] = _PRODUCTTAGBI
DESCRIPTOR.message_types_by_name['ST_total'] = _ST_TOTAL
DESCRIPTOR.message_types_by_name['StocktakeBiDetailedResponse'] = _STOCKTAKEBIDETAILEDRESPONSE
DESCRIPTOR.message_types_by_name['StocktakeBalanceRegionRequest'] = _STOCKTAKEBALANCEREGIONREQUEST
DESCRIPTOR.message_types_by_name['StocktakeBalanceRegionDetails'] = _STOCKTAKEBALANCEREGIONDETAILS
DESCRIPTOR.message_types_by_name['StocktakeBalanceRegion'] = _STOCKTAKEBALANCEREGION
DESCRIPTOR.message_types_by_name['StocktakeBalanceRegionResponse'] = _STOCKTAKEBALANCEREGIONRESPONSE
DESCRIPTOR.message_types_by_name['StoreDataScopeRequest'] = _STOREDATASCOPEREQUEST
DESCRIPTOR.message_types_by_name['ScopeStores'] = _SCOPESTORES
DESCRIPTOR.message_types_by_name['StoreDataScope'] = _STOREDATASCOPE
DESCRIPTOR.message_types_by_name['AdvanceStocktakeDiffRequest'] = _ADVANCESTOCKTAKEDIFFREQUEST
DESCRIPTOR.message_types_by_name['AdvanceStocktakeDiffResponse'] = _ADVANCESTOCKTAKEDIFFRESPONSE
DESCRIPTOR.message_types_by_name['StocktakeDiffReportRequest'] = _STOCKTAKEDIFFREPORTREQUEST
DESCRIPTOR.message_types_by_name['StocktakeDiffReportRow'] = _STOCKTAKEDIFFREPORTROW
DESCRIPTOR.message_types_by_name['StocktakeDiffReportResponse'] = _STOCKTAKEDIFFREPORTRESPONSE
DESCRIPTOR.message_types_by_name['GetUncompleteDocRequest'] = _GETUNCOMPLETEDOCREQUEST
DESCRIPTOR.message_types_by_name['CheckDemandDetail'] = _CHECKDEMANDDETAIL
DESCRIPTOR.message_types_by_name['GetUncompleteDocResponse'] = _GETUNCOMPLETEDOCRESPONSE
DESCRIPTOR.message_types_by_name['RecreateStocktakeDocRequest'] = _RECREATESTOCKTAKEDOCREQUEST
DESCRIPTOR.message_types_by_name['RecreateStocktakeDocResponse'] = _RECREATESTOCKTAKEDOCRESPONSE
DESCRIPTOR.message_types_by_name['StocktakeDocStatisticsRequest'] = _STOCKTAKEDOCSTATISTICSREQUEST
DESCRIPTOR.message_types_by_name['StocktakeDocStatistics'] = _STOCKTAKEDOCSTATISTICS
DESCRIPTOR.message_types_by_name['StocktakeDocStatisticsResponse'] = _STOCKTAKEDOCSTATISTICSRESPONSE
DESCRIPTOR.message_types_by_name['StocktakeDiffCollectReportRequest'] = _STOCKTAKEDIFFCOLLECTREPORTREQUEST
DESCRIPTOR.message_types_by_name['StocktakeDiffCollectReportRow'] = _STOCKTAKEDIFFCOLLECTREPORTROW
DESCRIPTOR.message_types_by_name['StocktakeDiffCollectReportResponse'] = _STOCKTAKEDIFFCOLLECTREPORTRESPONSE
DESCRIPTOR.message_types_by_name['UncompleteDocReportRequest'] = _UNCOMPLETEDOCREPORTREQUEST
DESCRIPTOR.message_types_by_name['UncompleteDocReportResponse'] = _UNCOMPLETEDOCREPORTRESPONSE
DESCRIPTOR.message_types_by_name['StocktakeProductImportRequest'] = _STOCKTAKEPRODUCTIMPORTREQUEST
DESCRIPTOR.message_types_by_name['ProductImportResponseRows'] = _PRODUCTIMPORTRESPONSEROWS
DESCRIPTOR.message_types_by_name['StocktakeProductImportResponse'] = _STOCKTAKEPRODUCTIMPORTRESPONSE
DESCRIPTOR.message_types_by_name['UpdateStocktakeImportBatchRequest'] = _UPDATESTOCKTAKEIMPORTBATCHREQUEST
DESCRIPTOR.message_types_by_name['UpdateStocktakeImportBatchResponse'] = _UPDATESTOCKTAKEIMPORTBATCHRESPONSE
DESCRIPTOR.message_types_by_name['StocktakePositionProducts'] = _STOCKTAKEPOSITIONPRODUCTS
DESCRIPTOR.enum_types_by_name['PeriodGroupMethod'] = _PERIODGROUPMETHOD
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetProductByStocktakeTypeRequest = _reflection.GeneratedProtocolMessageType('GetProductByStocktakeTypeRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTBYSTOCKTAKETYPEREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetProductByStocktakeTypeRequest)
  ))
_sym_db.RegisterMessage(GetProductByStocktakeTypeRequest)

StocktakeProductType = _reflection.GeneratedProtocolMessageType('StocktakeProductType', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPRODUCTTYPE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeProductType)
  ))
_sym_db.RegisterMessage(StocktakeProductType)

GetProductByStocktakeTypeResponse = _reflection.GeneratedProtocolMessageType('GetProductByStocktakeTypeResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTBYSTOCKTAKETYPERESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetProductByStocktakeTypeResponse)
  ))
_sym_db.RegisterMessage(GetProductByStocktakeTypeResponse)

GetStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeByDocIDRequest)

Stocktake = _reflection.GeneratedProtocolMessageType('Stocktake', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.Stocktake)
  ))
_sym_db.RegisterMessage(Stocktake)

AttachmentsStock = _reflection.GeneratedProtocolMessageType('AttachmentsStock', (_message.Message,), dict(
  DESCRIPTOR = _ATTACHMENTSSTOCK,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.AttachmentsStock)
  ))
_sym_db.RegisterMessage(AttachmentsStock)

GetStocktakeRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetStocktakeRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeRequest)

GetStocktakeResponse = _reflection.GeneratedProtocolMessageType('GetStocktakeResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKERESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetStocktakeResponse)
  ))
_sym_db.RegisterMessage(GetStocktakeResponse)

GetStocktakeProductRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEPRODUCTREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetStocktakeProductRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeProductRequest)

StocktakeProductTagName = _reflection.GeneratedProtocolMessageType('StocktakeProductTagName', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPRODUCTTAGNAME,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeProductTagName)
  ))
_sym_db.RegisterMessage(StocktakeProductTagName)

StocktakeProductUnits = _reflection.GeneratedProtocolMessageType('StocktakeProductUnits', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPRODUCTUNITS,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeProductUnits)
  ))
_sym_db.RegisterMessage(StocktakeProductUnits)

StocktakeProduct = _reflection.GeneratedProtocolMessageType('StocktakeProduct', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPRODUCT,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeProduct)
  ))
_sym_db.RegisterMessage(StocktakeProduct)

GetStocktakeProductResponse = _reflection.GeneratedProtocolMessageType('GetStocktakeProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEPRODUCTRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetStocktakeProductResponse)
  ))
_sym_db.RegisterMessage(GetStocktakeProductResponse)

UserCreateStocktakeRequest = _reflection.GeneratedProtocolMessageType('UserCreateStocktakeRequest', (_message.Message,), dict(
  DESCRIPTOR = _USERCREATESTOCKTAKEREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.UserCreateStocktakeRequest)
  ))
_sym_db.RegisterMessage(UserCreateStocktakeRequest)

UserCreateStocktakeResponse = _reflection.GeneratedProtocolMessageType('UserCreateStocktakeResponse', (_message.Message,), dict(
  DESCRIPTOR = _USERCREATESTOCKTAKERESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.UserCreateStocktakeResponse)
  ))
_sym_db.RegisterMessage(UserCreateStocktakeResponse)

TagQuantity = _reflection.GeneratedProtocolMessageType('TagQuantity', (_message.Message,), dict(
  DESCRIPTOR = _TAGQUANTITY,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.TagQuantity)
  ))
_sym_db.RegisterMessage(TagQuantity)

PutStocktakeProducts = _reflection.GeneratedProtocolMessageType('PutStocktakeProducts', (_message.Message,), dict(
  DESCRIPTOR = _PUTSTOCKTAKEPRODUCTS,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.PutStocktakeProducts)
  ))
_sym_db.RegisterMessage(PutStocktakeProducts)

PutStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('PutStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _PUTSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.PutStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(PutStocktakeByDocIDRequest)

RejectStocktakeProductRequest = _reflection.GeneratedProtocolMessageType('RejectStocktakeProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _REJECTSTOCKTAKEPRODUCTREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.RejectStocktakeProductRequest)
  ))
_sym_db.RegisterMessage(RejectStocktakeProductRequest)

RejectStocktakeProductResponse = _reflection.GeneratedProtocolMessageType('RejectStocktakeProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _REJECTSTOCKTAKEPRODUCTRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.RejectStocktakeProductResponse)
  ))
_sym_db.RegisterMessage(RejectStocktakeProductResponse)

PutStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('PutStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _PUTSTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.PutStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(PutStocktakeByDocIDResponse)

CheckStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('CheckStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHECKSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CheckStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(CheckStocktakeByDocIDRequest)

CheckStocktakeByDocIDDetail = _reflection.GeneratedProtocolMessageType('CheckStocktakeByDocIDDetail', (_message.Message,), dict(
  DESCRIPTOR = _CHECKSTOCKTAKEBYDOCIDDETAIL,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CheckStocktakeByDocIDDetail)
  ))
_sym_db.RegisterMessage(CheckStocktakeByDocIDDetail)

CheckStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('CheckStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _CHECKSTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CheckStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(CheckStocktakeByDocIDResponse)

ConfirmStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('ConfirmStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ConfirmStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(ConfirmStocktakeByDocIDRequest)

SubmitStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('SubmitStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITSTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.SubmitStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(SubmitStocktakeByDocIDResponse)

SubmitStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('SubmitStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.SubmitStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(SubmitStocktakeByDocIDRequest)

ApproveStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('ApproveStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _APPROVESTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ApproveStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(ApproveStocktakeByDocIDRequest)

ApproveStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('ApproveStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _APPROVESTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ApproveStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(ApproveStocktakeByDocIDResponse)

ConfirmStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('ConfirmStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMSTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ConfirmStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(ConfirmStocktakeByDocIDResponse)

CancelStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('CancelStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _CANCELSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CancelStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(CancelStocktakeByDocIDRequest)

CancelStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('CancelStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _CANCELSTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CancelStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(CancelStocktakeByDocIDResponse)

CreateStocktakeBatchRequest = _reflection.GeneratedProtocolMessageType('CreateStocktakeBatchRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATESTOCKTAKEBATCHREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreateStocktakeBatchRequest)
  ))
_sym_db.RegisterMessage(CreateStocktakeBatchRequest)

CreateStocktakeBatchResponse = _reflection.GeneratedProtocolMessageType('CreateStocktakeBatchResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATESTOCKTAKEBATCHRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreateStocktakeBatchResponse)
  ))
_sym_db.RegisterMessage(CreateStocktakeBatchResponse)

CreateStocktakeRequest = _reflection.GeneratedProtocolMessageType('CreateStocktakeRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATESTOCKTAKEREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreateStocktakeRequest)
  ))
_sym_db.RegisterMessage(CreateStocktakeRequest)

CreateStocktakeResponse = _reflection.GeneratedProtocolMessageType('CreateStocktakeResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATESTOCKTAKERESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreateStocktakeResponse)
  ))
_sym_db.RegisterMessage(CreateStocktakeResponse)

CheckedStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('CheckedStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHECKEDSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CheckedStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(CheckedStocktakeByDocIDRequest)

CheckedStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('CheckedStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _CHECKEDSTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CheckedStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(CheckedStocktakeByDocIDResponse)

GetStocktakeTagsRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeTagsRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKETAGSREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetStocktakeTagsRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeTagsRequest)

StocktakeTags = _reflection.GeneratedProtocolMessageType('StocktakeTags', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKETAGS,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeTags)
  ))
_sym_db.RegisterMessage(StocktakeTags)

GetStocktakeTagsResponse = _reflection.GeneratedProtocolMessageType('GetStocktakeTagsResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKETAGSRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetStocktakeTagsResponse)
  ))
_sym_db.RegisterMessage(GetStocktakeTagsResponse)

ActionStocktakeTagsRequest = _reflection.GeneratedProtocolMessageType('ActionStocktakeTagsRequest', (_message.Message,), dict(
  DESCRIPTOR = _ACTIONSTOCKTAKETAGSREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ActionStocktakeTagsRequest)
  ))
_sym_db.RegisterMessage(ActionStocktakeTagsRequest)

ActionStocktakeTagsResponse = _reflection.GeneratedProtocolMessageType('ActionStocktakeTagsResponse', (_message.Message,), dict(
  DESCRIPTOR = _ACTIONSTOCKTAKETAGSRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ActionStocktakeTagsResponse)
  ))
_sym_db.RegisterMessage(ActionStocktakeTagsResponse)

GetStocktakeTagsByIdRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeTagsByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKETAGSBYIDREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetStocktakeTagsByIdRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeTagsByIdRequest)

DeleteStocktakeProductTagsRequest = _reflection.GeneratedProtocolMessageType('DeleteStocktakeProductTagsRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETESTOCKTAKEPRODUCTTAGSREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.DeleteStocktakeProductTagsRequest)
  ))
_sym_db.RegisterMessage(DeleteStocktakeProductTagsRequest)

DeleteStocktakeProductTagsResponse = _reflection.GeneratedProtocolMessageType('DeleteStocktakeProductTagsResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETESTOCKTAKEPRODUCTTAGSRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.DeleteStocktakeProductTagsResponse)
  ))
_sym_db.RegisterMessage(DeleteStocktakeProductTagsResponse)

GetStocktakeBalanceRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeBalanceRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEBALANCEREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetStocktakeBalanceRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeBalanceRequest)

StocktakeBalance = _reflection.GeneratedProtocolMessageType('StocktakeBalance', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBALANCE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeBalance)
  ))
_sym_db.RegisterMessage(StocktakeBalance)

AttachmentsManuBalance = _reflection.GeneratedProtocolMessageType('AttachmentsManuBalance', (_message.Message,), dict(
  DESCRIPTOR = _ATTACHMENTSMANUBALANCE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.AttachmentsManuBalance)
  ))
_sym_db.RegisterMessage(AttachmentsManuBalance)

GetStocktakeBalanceResponse = _reflection.GeneratedProtocolMessageType('GetStocktakeBalanceResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEBALANCERESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetStocktakeBalanceResponse)
  ))
_sym_db.RegisterMessage(GetStocktakeBalanceResponse)

GetStocktakeBalanceProductGroupRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeBalanceProductGroupRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEBALANCEPRODUCTGROUPREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetStocktakeBalanceProductGroupRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeBalanceProductGroupRequest)

TagProductBi = _reflection.GeneratedProtocolMessageType('TagProductBi', (_message.Message,), dict(
  DESCRIPTOR = _TAGPRODUCTBI,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.TagProductBi)
  ))
_sym_db.RegisterMessage(TagProductBi)

StocktakeBalanceProductGroup = _reflection.GeneratedProtocolMessageType('StocktakeBalanceProductGroup', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBALANCEPRODUCTGROUP,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeBalanceProductGroup)
  ))
_sym_db.RegisterMessage(StocktakeBalanceProductGroup)

GetStocktakeBalanceProductGroupResponse = _reflection.GeneratedProtocolMessageType('GetStocktakeBalanceProductGroupResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEBALANCEPRODUCTGROUPRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetStocktakeBalanceProductGroupResponse)
  ))
_sym_db.RegisterMessage(GetStocktakeBalanceProductGroupResponse)

StocktakeBiDetailedRequest = _reflection.GeneratedProtocolMessageType('StocktakeBiDetailedRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBIDETAILEDREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeBiDetailedRequest)
  ))
_sym_db.RegisterMessage(StocktakeBiDetailedRequest)

StocktakeBiDetailed = _reflection.GeneratedProtocolMessageType('StocktakeBiDetailed', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBIDETAILED,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeBiDetailed)
  ))
_sym_db.RegisterMessage(StocktakeBiDetailed)

ProductTagBi = _reflection.GeneratedProtocolMessageType('ProductTagBi', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTTAGBI,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ProductTagBi)
  ))
_sym_db.RegisterMessage(ProductTagBi)

ST_total = _reflection.GeneratedProtocolMessageType('ST_total', (_message.Message,), dict(
  DESCRIPTOR = _ST_TOTAL,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ST_total)
  ))
_sym_db.RegisterMessage(ST_total)

StocktakeBiDetailedResponse = _reflection.GeneratedProtocolMessageType('StocktakeBiDetailedResponse', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBIDETAILEDRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeBiDetailedResponse)
  ))
_sym_db.RegisterMessage(StocktakeBiDetailedResponse)

StocktakeBalanceRegionRequest = _reflection.GeneratedProtocolMessageType('StocktakeBalanceRegionRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBALANCEREGIONREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeBalanceRegionRequest)
  ))
_sym_db.RegisterMessage(StocktakeBalanceRegionRequest)

StocktakeBalanceRegionDetails = _reflection.GeneratedProtocolMessageType('StocktakeBalanceRegionDetails', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBALANCEREGIONDETAILS,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeBalanceRegionDetails)
  ))
_sym_db.RegisterMessage(StocktakeBalanceRegionDetails)

StocktakeBalanceRegion = _reflection.GeneratedProtocolMessageType('StocktakeBalanceRegion', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBALANCEREGION,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeBalanceRegion)
  ))
_sym_db.RegisterMessage(StocktakeBalanceRegion)

StocktakeBalanceRegionResponse = _reflection.GeneratedProtocolMessageType('StocktakeBalanceRegionResponse', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBALANCEREGIONRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeBalanceRegionResponse)
  ))
_sym_db.RegisterMessage(StocktakeBalanceRegionResponse)

StoreDataScopeRequest = _reflection.GeneratedProtocolMessageType('StoreDataScopeRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOREDATASCOPEREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StoreDataScopeRequest)
  ))
_sym_db.RegisterMessage(StoreDataScopeRequest)

ScopeStores = _reflection.GeneratedProtocolMessageType('ScopeStores', (_message.Message,), dict(
  DESCRIPTOR = _SCOPESTORES,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ScopeStores)
  ))
_sym_db.RegisterMessage(ScopeStores)

StoreDataScope = _reflection.GeneratedProtocolMessageType('StoreDataScope', (_message.Message,), dict(
  DESCRIPTOR = _STOREDATASCOPE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StoreDataScope)
  ))
_sym_db.RegisterMessage(StoreDataScope)

AdvanceStocktakeDiffRequest = _reflection.GeneratedProtocolMessageType('AdvanceStocktakeDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _ADVANCESTOCKTAKEDIFFREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.AdvanceStocktakeDiffRequest)
  ))
_sym_db.RegisterMessage(AdvanceStocktakeDiffRequest)

AdvanceStocktakeDiffResponse = _reflection.GeneratedProtocolMessageType('AdvanceStocktakeDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _ADVANCESTOCKTAKEDIFFRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.AdvanceStocktakeDiffResponse)
  ))
_sym_db.RegisterMessage(AdvanceStocktakeDiffResponse)

StocktakeDiffReportRequest = _reflection.GeneratedProtocolMessageType('StocktakeDiffReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDIFFREPORTREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeDiffReportRequest)
  ))
_sym_db.RegisterMessage(StocktakeDiffReportRequest)

StocktakeDiffReportRow = _reflection.GeneratedProtocolMessageType('StocktakeDiffReportRow', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDIFFREPORTROW,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeDiffReportRow)
  ))
_sym_db.RegisterMessage(StocktakeDiffReportRow)

StocktakeDiffReportResponse = _reflection.GeneratedProtocolMessageType('StocktakeDiffReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDIFFREPORTRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeDiffReportResponse)
  ))
_sym_db.RegisterMessage(StocktakeDiffReportResponse)

GetUncompleteDocRequest = _reflection.GeneratedProtocolMessageType('GetUncompleteDocRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETUNCOMPLETEDOCREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetUncompleteDocRequest)
  ))
_sym_db.RegisterMessage(GetUncompleteDocRequest)

CheckDemandDetail = _reflection.GeneratedProtocolMessageType('CheckDemandDetail', (_message.Message,), dict(
  DESCRIPTOR = _CHECKDEMANDDETAIL,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CheckDemandDetail)
  ))
_sym_db.RegisterMessage(CheckDemandDetail)

GetUncompleteDocResponse = _reflection.GeneratedProtocolMessageType('GetUncompleteDocResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETUNCOMPLETEDOCRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetUncompleteDocResponse)
  ))
_sym_db.RegisterMessage(GetUncompleteDocResponse)

RecreateStocktakeDocRequest = _reflection.GeneratedProtocolMessageType('RecreateStocktakeDocRequest', (_message.Message,), dict(
  DESCRIPTOR = _RECREATESTOCKTAKEDOCREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.RecreateStocktakeDocRequest)
  ))
_sym_db.RegisterMessage(RecreateStocktakeDocRequest)

RecreateStocktakeDocResponse = _reflection.GeneratedProtocolMessageType('RecreateStocktakeDocResponse', (_message.Message,), dict(
  DESCRIPTOR = _RECREATESTOCKTAKEDOCRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.RecreateStocktakeDocResponse)
  ))
_sym_db.RegisterMessage(RecreateStocktakeDocResponse)

StocktakeDocStatisticsRequest = _reflection.GeneratedProtocolMessageType('StocktakeDocStatisticsRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDOCSTATISTICSREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeDocStatisticsRequest)
  ))
_sym_db.RegisterMessage(StocktakeDocStatisticsRequest)

StocktakeDocStatistics = _reflection.GeneratedProtocolMessageType('StocktakeDocStatistics', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDOCSTATISTICS,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeDocStatistics)
  ))
_sym_db.RegisterMessage(StocktakeDocStatistics)

StocktakeDocStatisticsResponse = _reflection.GeneratedProtocolMessageType('StocktakeDocStatisticsResponse', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDOCSTATISTICSRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeDocStatisticsResponse)
  ))
_sym_db.RegisterMessage(StocktakeDocStatisticsResponse)

StocktakeDiffCollectReportRequest = _reflection.GeneratedProtocolMessageType('StocktakeDiffCollectReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDIFFCOLLECTREPORTREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeDiffCollectReportRequest)
  ))
_sym_db.RegisterMessage(StocktakeDiffCollectReportRequest)

StocktakeDiffCollectReportRow = _reflection.GeneratedProtocolMessageType('StocktakeDiffCollectReportRow', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDIFFCOLLECTREPORTROW,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeDiffCollectReportRow)
  ))
_sym_db.RegisterMessage(StocktakeDiffCollectReportRow)

StocktakeDiffCollectReportResponse = _reflection.GeneratedProtocolMessageType('StocktakeDiffCollectReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDIFFCOLLECTREPORTRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeDiffCollectReportResponse)
  ))
_sym_db.RegisterMessage(StocktakeDiffCollectReportResponse)

UncompleteDocReportRequest = _reflection.GeneratedProtocolMessageType('UncompleteDocReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _UNCOMPLETEDOCREPORTREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.UncompleteDocReportRequest)
  ))
_sym_db.RegisterMessage(UncompleteDocReportRequest)

UncompleteDocReportResponse = _reflection.GeneratedProtocolMessageType('UncompleteDocReportResponse', (_message.Message,), dict(

  UncompleteDocReport = _reflection.GeneratedProtocolMessageType('UncompleteDocReport', (_message.Message,), dict(
    DESCRIPTOR = _UNCOMPLETEDOCREPORTRESPONSE_UNCOMPLETEDOCREPORT,
    __module__ = 'manufactory.stocktake_pb2'
    # @@protoc_insertion_point(class_scope:manufactory.UncompleteDocReportResponse.UncompleteDocReport)
    ))
  ,
  DESCRIPTOR = _UNCOMPLETEDOCREPORTRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.UncompleteDocReportResponse)
  ))
_sym_db.RegisterMessage(UncompleteDocReportResponse)
_sym_db.RegisterMessage(UncompleteDocReportResponse.UncompleteDocReport)

StocktakeProductImportRequest = _reflection.GeneratedProtocolMessageType('StocktakeProductImportRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPRODUCTIMPORTREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeProductImportRequest)
  ))
_sym_db.RegisterMessage(StocktakeProductImportRequest)

ProductImportResponseRows = _reflection.GeneratedProtocolMessageType('ProductImportResponseRows', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTIMPORTRESPONSEROWS,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ProductImportResponseRows)
  ))
_sym_db.RegisterMessage(ProductImportResponseRows)

StocktakeProductImportResponse = _reflection.GeneratedProtocolMessageType('StocktakeProductImportResponse', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPRODUCTIMPORTRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakeProductImportResponse)
  ))
_sym_db.RegisterMessage(StocktakeProductImportResponse)

UpdateStocktakeImportBatchRequest = _reflection.GeneratedProtocolMessageType('UpdateStocktakeImportBatchRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATESTOCKTAKEIMPORTBATCHREQUEST,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.UpdateStocktakeImportBatchRequest)
  ))
_sym_db.RegisterMessage(UpdateStocktakeImportBatchRequest)

UpdateStocktakeImportBatchResponse = _reflection.GeneratedProtocolMessageType('UpdateStocktakeImportBatchResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATESTOCKTAKEIMPORTBATCHRESPONSE,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.UpdateStocktakeImportBatchResponse)
  ))
_sym_db.RegisterMessage(UpdateStocktakeImportBatchResponse)

StocktakePositionProducts = _reflection.GeneratedProtocolMessageType('StocktakePositionProducts', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPOSITIONPRODUCTS,
  __module__ = 'manufactory.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.StocktakePositionProducts)
  ))
_sym_db.RegisterMessage(StocktakePositionProducts)



_MANUFACTORYSTOCKTAKE = _descriptor.ServiceDescriptor(
  name='ManufactoryStockTake',
  full_name='manufactory.ManufactoryStockTake',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=23834,
  serialized_end=28732,
  methods=[
  _descriptor.MethodDescriptor(
    name='CheckStocktakeByDocID',
    full_name='manufactory.ManufactoryStockTake.CheckStocktakeByDocID',
    index=0,
    containing_service=None,
    input_type=_CHECKSTOCKTAKEBYDOCIDREQUEST,
    output_type=_CHECKSTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002=\032;/api/v2/supply/manufactory/stocktake/{doc_id}/confirm/check'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmStocktakeByDocID',
    full_name='manufactory.ManufactoryStockTake.ConfirmStocktakeByDocID',
    index=1,
    containing_service=None,
    input_type=_CONFIRMSTOCKTAKEBYDOCIDREQUEST,
    output_type=_CONFIRMSTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\0325/api/v2/supply/manufactory/stocktake/{doc_id}/confirm'),
  ),
  _descriptor.MethodDescriptor(
    name='ApproveStocktakeByDocID',
    full_name='manufactory.ManufactoryStockTake.ApproveStocktakeByDocID',
    index=2,
    containing_service=None,
    input_type=_APPROVESTOCKTAKEBYDOCIDREQUEST,
    output_type=_APPROVESTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002:\0325/api/v2/supply/manufactory/stocktake/{doc_id}/approve:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='RejectStocktakeProduct',
    full_name='manufactory.ManufactoryStockTake.RejectStocktakeProduct',
    index=3,
    containing_service=None,
    input_type=_REJECTSTOCKTAKEPRODUCTREQUEST,
    output_type=_REJECTSTOCKTAKEPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\0324/api/v2/supply/manufactory/stocktake/{doc_id}/reject:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CancelStocktakeByDocID',
    full_name='manufactory.ManufactoryStockTake.CancelStocktakeByDocID',
    index=4,
    containing_service=None,
    input_type=_CANCELSTOCKTAKEBYDOCIDREQUEST,
    output_type=_CANCELSTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0026\0324/api/v2/supply/manufactory/stocktake/{doc_id}/cancel'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktakeByDocID',
    full_name='manufactory.ManufactoryStockTake.GetStocktakeByDocID',
    index=5,
    containing_service=None,
    input_type=_GETSTOCKTAKEBYDOCIDREQUEST,
    output_type=_STOCKTAKE,
    serialized_options=_b('\202\323\344\223\002/\022-/api/v2/supply/manufactory/stocktake/{doc_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktake',
    full_name='manufactory.ManufactoryStockTake.GetStocktake',
    index=6,
    containing_service=None,
    input_type=_GETSTOCKTAKEREQUEST,
    output_type=_GETSTOCKTAKERESPONSE,
    serialized_options=_b('\202\323\344\223\002&\022$/api/v2/supply/manufactory/stocktake'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktakeProduct',
    full_name='manufactory.ManufactoryStockTake.GetStocktakeProduct',
    index=7,
    containing_service=None,
    input_type=_GETSTOCKTAKEPRODUCTREQUEST,
    output_type=_GETSTOCKTAKEPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\0225/api/v2/supply/manufactory/stocktake/{doc_id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='PutStocktakeByDocID',
    full_name='manufactory.ManufactoryStockTake.PutStocktakeByDocID',
    index=8,
    containing_service=None,
    input_type=_PUTSTOCKTAKEBYDOCIDREQUEST,
    output_type=_PUTSTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\032-/api/v2/supply/manufactory/stocktake/{doc_id}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CheckedStocktakeByDocID',
    full_name='manufactory.ManufactoryStockTake.CheckedStocktakeByDocID',
    index=9,
    containing_service=None,
    input_type=_CHECKEDSTOCKTAKEBYDOCIDREQUEST,
    output_type=_CHECKEDSTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002=\0328/api/v2/supply/manufactory/stocktake/{doc_id}/init/check:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktakeTags',
    full_name='manufactory.ManufactoryStockTake.GetStocktakeTags',
    index=10,
    containing_service=None,
    input_type=_GETSTOCKTAKETAGSREQUEST,
    output_type=_GETSTOCKTAKETAGSRESPONSE,
    serialized_options=_b('\202\323\344\223\0023\0221/api/v2/supply/manufactory/stocktake/product/tags'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktakeTagsById',
    full_name='manufactory.ManufactoryStockTake.GetStocktakeTagsById',
    index=11,
    containing_service=None,
    input_type=_GETSTOCKTAKETAGSBYIDREQUEST,
    output_type=_STOCKTAKETAGS,
    serialized_options=_b('\202\323\344\223\0022\022-/api/v2/supply/mobile/stocktake/tags/{tag_id}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ActionStocktakeTags',
    full_name='manufactory.ManufactoryStockTake.ActionStocktakeTags',
    index=12,
    containing_service=None,
    input_type=_ACTIONSTOCKTAKETAGSREQUEST,
    output_type=_ACTIONSTOCKTAKETAGSRESPONSE,
    serialized_options=_b('\202\323\344\223\002=\0328/api/v2/supply/manufactory/stocktake/product/tags/action:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteStocktakeProductTags',
    full_name='manufactory.ManufactoryStockTake.DeleteStocktakeProductTags',
    index=13,
    containing_service=None,
    input_type=_DELETESTOCKTAKEPRODUCTTAGSREQUEST,
    output_type=_DELETESTOCKTAKEPRODUCTTAGSRESPONSE,
    serialized_options=_b('\202\323\344\223\002<\0327/api/v2/supply/manufactory/stocktake/product/tags/clean:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktakeBalance',
    full_name='manufactory.ManufactoryStockTake.GetStocktakeBalance',
    index=14,
    containing_service=None,
    input_type=_GETSTOCKTAKEBALANCEREQUEST,
    output_type=_GETSTOCKTAKEBALANCERESPONSE,
    serialized_options=_b('\202\323\344\223\0021\022//api/v2/supply/manufactory/stocktake/bi/balance'),
  ),
  _descriptor.MethodDescriptor(
    name='SubmitStocktakeByDocID',
    full_name='manufactory.ManufactoryStockTake.SubmitStocktakeByDocID',
    index=15,
    containing_service=None,
    input_type=_SUBMITSTOCKTAKEBYDOCIDREQUEST,
    output_type=_SUBMITSTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\0324/api/v2/supply/manufactory/stocktake/{doc_id}/submit:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktakeBalanceProductGroup',
    full_name='manufactory.ManufactoryStockTake.GetStocktakeBalanceProductGroup',
    index=16,
    containing_service=None,
    input_type=_GETSTOCKTAKEBALANCEPRODUCTGROUPREQUEST,
    output_type=_GETSTOCKTAKEBALANCEPRODUCTGROUPRESPONSE,
    serialized_options=_b('\202\323\344\223\002E\022C/api/v2/supply/manufactory/stocktake/balance/{doc_id}/product/group'),
  ),
  _descriptor.MethodDescriptor(
    name='StocktakeBiDetailed',
    full_name='manufactory.ManufactoryStockTake.StocktakeBiDetailed',
    index=17,
    containing_service=None,
    input_type=_STOCKTAKEBIDETAILEDREQUEST,
    output_type=_STOCKTAKEBIDETAILEDRESPONSE,
    serialized_options=_b('\202\323\344\223\0020\022./api/v2/supply/manufactory/stocktake/bi/detail'),
  ),
  _descriptor.MethodDescriptor(
    name='StocktakeBalanceRegion',
    full_name='manufactory.ManufactoryStockTake.StocktakeBalanceRegion',
    index=18,
    containing_service=None,
    input_type=_STOCKTAKEBALANCEREGIONREQUEST,
    output_type=_STOCKTAKEBALANCEREGIONRESPONSE,
    serialized_options=_b('\202\323\344\223\002<\022:/api/v2/supply/manufactory/stocktake/balance/product/group'),
  ),
  _descriptor.MethodDescriptor(
    name='AdvanceStocktakeDiff',
    full_name='manufactory.ManufactoryStockTake.AdvanceStocktakeDiff',
    index=19,
    containing_service=None,
    input_type=_ADVANCESTOCKTAKEDIFFREQUEST,
    output_type=_ADVANCESTOCKTAKEDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\0225/api/v2/supply/manufactory/stocktake/{doc_id}/advance'),
  ),
  _descriptor.MethodDescriptor(
    name='StocktakeDiffReport',
    full_name='manufactory.ManufactoryStockTake.StocktakeDiffReport',
    index=20,
    containing_service=None,
    input_type=_STOCKTAKEDIFFREPORTREQUEST,
    output_type=_STOCKTAKEDIFFREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\0220/api/v2/supply/manufactory/stocktake/diff/report'),
  ),
  _descriptor.MethodDescriptor(
    name='GetUncompleteDoc',
    full_name='manufactory.ManufactoryStockTake.GetUncompleteDoc',
    index=21,
    containing_service=None,
    input_type=_GETUNCOMPLETEDOCREQUEST,
    output_type=_GETUNCOMPLETEDOCRESPONSE,
    serialized_options=_b('\202\323\344\223\002+\022)/api/v2/supply/manufactory/uncomplete_doc'),
  ),
  _descriptor.MethodDescriptor(
    name='RecreateStocktakeDoc',
    full_name='manufactory.ManufactoryStockTake.RecreateStocktakeDoc',
    index=22,
    containing_service=None,
    input_type=_RECREATESTOCKTAKEDOCREQUEST,
    output_type=_RECREATESTOCKTAKEDOCRESPONSE,
    serialized_options=_b('\202\323\344\223\0026\"1/api/v2/supply/manufactory/recreate_stocktake_doc:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='StocktakeDocStatistics',
    full_name='manufactory.ManufactoryStockTake.StocktakeDocStatistics',
    index=23,
    containing_service=None,
    input_type=_STOCKTAKEDOCSTATISTICSREQUEST,
    output_type=_STOCKTAKEDOCSTATISTICSRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\"3/api/v2/supply/manufactory/stocktake_doc_statistics:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='StocktakeDiffCollectReport',
    full_name='manufactory.ManufactoryStockTake.StocktakeDiffCollectReport',
    index=24,
    containing_service=None,
    input_type=_STOCKTAKEDIFFCOLLECTREPORTREQUEST,
    output_type=_STOCKTAKEDIFFCOLLECTREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002:\0228/api/v2/supply/manufactory/stocktake/diff_collect/report'),
  ),
  _descriptor.MethodDescriptor(
    name='UncompleteDocReport',
    full_name='manufactory.ManufactoryStockTake.UncompleteDocReport',
    index=25,
    containing_service=None,
    input_type=_UNCOMPLETEDOCREPORTREQUEST,
    output_type=_UNCOMPLETEDOCREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\0220/api/v2/supply/manufactory/uncomplete_doc_report'),
  ),
  _descriptor.MethodDescriptor(
    name='StocktakeProductImport',
    full_name='manufactory.ManufactoryStockTake.StocktakeProductImport',
    index=26,
    containing_service=None,
    input_type=_STOCKTAKEPRODUCTIMPORTREQUEST,
    output_type=_STOCKTAKEPRODUCTIMPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\"3/api/v2/supply/manufactory/stocktake/product/import:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateStocktakeImportBatch',
    full_name='manufactory.ManufactoryStockTake.UpdateStocktakeImportBatch',
    index=27,
    containing_service=None,
    input_type=_UPDATESTOCKTAKEIMPORTBATCHREQUEST,
    output_type=_UPDATESTOCKTAKEIMPORTBATCHRESPONSE,
    serialized_options=_b('\202\323\344\223\002=\0328/api/v2/supply/manufactory/update/stocktake/import/batch:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_MANUFACTORYSTOCKTAKE)

DESCRIPTOR.services_by_name['ManufactoryStockTake'] = _MANUFACTORYSTOCKTAKE

# @@protoc_insertion_point(module_scope)
