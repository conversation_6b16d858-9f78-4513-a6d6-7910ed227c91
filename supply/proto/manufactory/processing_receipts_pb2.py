# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: manufactory/processing_receipts.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='manufactory/processing_receipts.proto',
  package='manufactory',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n%manufactory/processing_receipts.proto\x12\x0bmanufactory\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xb7\x05\n\x1f\x43reateProcessingReceiptsRequest\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x0c\n\x04type\x18\x05 \x01(\t\x12\x33\n\x0fprocessing_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1b\n\x13machining_center_id\x18\x07 \x01(\x04\x12\x1d\n\x15machining_center_code\x18\x08 \x01(\t\x12\x1d\n\x15machining_center_name\x18\t \x01(\t\x12\x13\n\x0bposition_id\x18\n \x01(\x04\x12\x15\n\rposition_name\x18\x0b \x01(\t\x12\x15\n\rposition_code\x18\x0c \x01(\t\x12\x17\n\x0fprocessing_rule\x18\r \x01(\x04\x12\x0e\n\x06remark\x18\x0e \x01(\t\x12\x17\n\x0ftarget_material\x18\x0f \x01(\x04\x12\x1c\n\x14target_material_code\x18\x10 \x01(\t\x12\x1c\n\x14target_material_name\x18\x11 \x01(\t\x12\x1f\n\x17target_material_unit_id\x18\x12 \x01(\x04\x12!\n\x19target_material_unit_name\x18\x13 \x01(\t\x12!\n\x19target_material_unit_spec\x18\x14 \x01(\t\x12\x1a\n\x12theory_output_rate\x18\x15 \x01(\x01\x12\x1a\n\x12\x61\x63tual_output_rate\x18\x16 \x01(\x01\x12\x1e\n\x16\x61\x63tual_output_quantity\x18\x17 \x01(\x01\x12\x17\n\x0fopened_position\x18\x18 \x01(\x08\x12\x12\n\nrequest_id\x18\x19 \x01(\x04\x12!\n\x05items\x18\x1a \x03(\x0b\x32\x12.manufactory.Items\x12\x16\n\x0e\x63ost_center_id\x18\x1e \x01(\x04\"\xbc\x03\n\x05Items\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0f\n\x07main_id\x18\x02 \x01(\x04\x12\x12\n\nproduct_id\x18\x03 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x04 \x01(\t\x12\x14\n\x0cproduct_name\x18\x05 \x01(\t\x12\x14\n\x0cproduct_type\x18\x06 \x01(\t\x12\x0f\n\x07unit_id\x18\x07 \x01(\x04\x12\x11\n\tunit_name\x18\x08 \x01(\t\x12\x11\n\tunit_spec\x18\t \x01(\t\x12\x11\n\tunit_rate\x18\r \x01(\x01\x12\x15\n\rmaterial_rate\x18\x0b \x01(\x01\x12\x17\n\x0f\x61\x63tual_quantity\x18\x0c \x01(\x01\x12\x12\n\npartner_id\x18\x10 \x01(\x04\x12\x12\n\ncreated_by\x18\x11 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x12 \x01(\t\x12\x12\n\nupdated_by\x18\x13 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x14 \x01(\t\x12.\n\ncreated_at\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"F\n CreateProcessingReceiptsResponse\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\"\xb9\x02\n\x1dListProcessingReceiptsRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x19\n\x11machining_centers\x18\x03 \x03(\x04\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x18\n\x10target_materials\x18\x06 \x03(\x04\x12\x14\n\x0cposition_ids\x18\x07 \x03(\x04\x12\x0e\n\x06status\x18\t \x03(\t\x12\r\n\x05limit\x18\n \x01(\x03\x12\x0e\n\x06offset\x18\x0b \x01(\x04\x12\x15\n\rinclude_total\x18\x0c \x01(\x08\x12\r\n\x05order\x18\r \x01(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\"\x92\x07\n\x07ItemRow\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x12\n\ncreated_by\x18\x03 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x04 \x01(\t\x12\x12\n\nupdated_by\x18\x05 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x06 \x01(\t\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07main_id\x18\t \x01(\x04\x12\x0e\n\x06status\x18\n \x01(\t\x12\x0c\n\x04\x63ode\x18\x0b \x01(\t\x12\x33\n\x0fprocessing_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04type\x18\r \x01(\t\x12\x1b\n\x13machining_center_id\x18\x0e \x01(\x04\x12\x1d\n\x15machining_center_code\x18\x0f \x01(\t\x12\x1d\n\x15machining_center_name\x18\x10 \x01(\t\x12\x13\n\x0bposition_id\x18\x11 \x01(\x04\x12\x15\n\rposition_name\x18\x12 \x01(\t\x12\x15\n\rposition_code\x18\x13 \x01(\t\x12\x17\n\x0fprocessing_rule\x18\x14 \x01(\x04\x12\x17\n\x0ftarget_material\x18\x15 \x01(\x04\x12\x1c\n\x14target_material_code\x18\x16 \x01(\t\x12\x1c\n\x14target_material_name\x18\x17 \x01(\t\x12\x1f\n\x17target_material_unit_id\x18\x18 \x01(\x04\x12!\n\x19target_material_unit_name\x18\x19 \x01(\t\x12!\n\x19target_material_unit_spec\x18\x1a \x01(\t\x12!\n\x19target_material_unit_rate\x18  \x01(\x01\x12\x1a\n\x12theory_output_rate\x18\x1b \x01(\x01\x12\x1a\n\x12\x61\x63tual_output_rate\x18\x1c \x01(\x01\x12\x1e\n\x16\x61\x63tual_output_quantity\x18\x1d \x01(\x01\x12\x0e\n\x06remark\x18\x1e \x01(\t\x12\x17\n\x0fopened_position\x18\x1f \x01(\x08\x12\x12\n\nrequest_id\x18! \x01(\x04\x12\x16\n\x0e\x63ost_center_id\x18\" \x01(\x04\"S\n\x1eListProcessingReceiptsResponse\x12\"\n\x04rows\x18\x01 \x03(\x0b\x32\x14.manufactory.ItemRow\x12\r\n\x05total\x18\x02 \x01(\x04\"8\n\"GetProcessingReceiptsDetailRequest\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\"\xd1\x07\n#GetProcessingReceiptsDetailResponse\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x12\n\ncreated_by\x18\x03 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x04 \x01(\t\x12\x12\n\nupdated_by\x18\x05 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x06 \x01(\t\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07main_id\x18\t \x01(\x04\x12\x0e\n\x06status\x18\n \x01(\t\x12\x0c\n\x04\x63ode\x18\x0b \x01(\t\x12\x33\n\x0fprocessing_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04type\x18\r \x01(\t\x12\x1b\n\x13machining_center_id\x18\x0e \x01(\x04\x12\x1d\n\x15machining_center_code\x18\x0f \x01(\t\x12\x1d\n\x15machining_center_name\x18\x10 \x01(\t\x12\x13\n\x0bposition_id\x18\x11 \x01(\x04\x12\x15\n\rposition_name\x18\x12 \x01(\t\x12\x15\n\rposition_code\x18\x13 \x01(\t\x12\x17\n\x0fprocessing_rule\x18\x14 \x01(\x04\x12\x17\n\x0ftarget_material\x18\x15 \x01(\x04\x12\x1c\n\x14target_material_code\x18\x16 \x01(\t\x12\x1c\n\x14target_material_name\x18\x17 \x01(\t\x12\x1f\n\x17target_material_unit_id\x18\x18 \x01(\x04\x12!\n\x19target_material_unit_name\x18\x19 \x01(\t\x12!\n\x19target_material_unit_spec\x18\x1a \x01(\t\x12!\n\x19target_material_unit_rate\x18$ \x01(\x01\x12\x1a\n\x12theory_output_rate\x18\x1b \x01(\x01\x12\x1a\n\x12\x61\x63tual_output_rate\x18\x1c \x01(\x01\x12\x1e\n\x16\x61\x63tual_output_quantity\x18\x1d \x01(\x01\x12\x0e\n\x06remark\x18\x1e \x01(\t\x12\x17\n\x0fopened_position\x18\x1f \x01(\x08\x12\x12\n\nrequest_id\x18! \x01(\x04\x12!\n\x05items\x18\" \x03(\x0b\x32\x12.manufactory.Items\x12\x16\n\x0e\x63ost_center_id\x18# \x01(\x04\"\xad\x05\n\x1fUpdateProcessingReceiptsRequest\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x33\n\x0fprocessing_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0bposition_id\x18\n \x01(\x04\x12\x15\n\rposition_name\x18\x0b \x01(\t\x12\x15\n\rposition_code\x18\x0c \x01(\t\x12\x17\n\x0fprocessing_rule\x18\r \x01(\x04\x12\x0e\n\x06remark\x18\x0e \x01(\t\x12\x1b\n\x13machining_center_id\x18\x0f \x01(\x04\x12\x1d\n\x15machining_center_code\x18\x10 \x01(\t\x12\x1d\n\x15machining_center_name\x18\x11 \x01(\t\x12\x17\n\x0ftarget_material\x18\x12 \x01(\x04\x12\x1c\n\x14target_material_code\x18\x13 \x01(\t\x12\x1c\n\x14target_material_name\x18\x14 \x01(\t\x12\x1f\n\x17target_material_unit_id\x18\x15 \x01(\x04\x12!\n\x19target_material_unit_name\x18\x16 \x01(\t\x12!\n\x19target_material_unit_spec\x18\x17 \x01(\t\x12\x1a\n\x12theory_output_rate\x18\x1b \x01(\x01\x12\x1a\n\x12\x61\x63tual_output_rate\x18\x1c \x01(\x01\x12\x1e\n\x16\x61\x63tual_output_quantity\x18\x1d \x01(\x01\x12\x17\n\x0fopened_position\x18\x1e \x01(\x08\x12\x12\n\nrequest_id\x18! \x01(\x04\x12!\n\x05items\x18\" \x03(\x0b\x32\x12.manufactory.Items\x12\x16\n\x0e\x63ost_center_id\x18# \x01(\x04\"F\n UpdateProcessingReceiptsResponse\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\"K\n%ChangeProcessingReceiptsStatusRequest\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\"L\n&ChangeProcessingReceiptsStatusResponse\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t2\xac\x07\n\x12ProcessingReceipts\x12\xa6\x01\n\x18\x43reateProcessingReceipts\x12,.manufactory.CreateProcessingReceiptsRequest\x1a-.manufactory.CreateProcessingReceiptsResponse\"-\x82\xd3\xe4\x93\x02\'\"\"/api/v2/supply/processing/receipts:\x01*\x12\xa2\x01\n\x16ListProcessingReceipts\x12*.manufactory.ListProcessingReceiptsRequest\x1a+.manufactory.ListProcessingReceiptsResponse\"/\x82\xd3\xe4\x93\x02)\x12\'/api/v2/supply/processing/receipts/list\x12\xc0\x01\n\x1bGetProcessingReceiptsDetail\x12/.manufactory.GetProcessingReceiptsDetailRequest\x1a\x30.manufactory.GetProcessingReceiptsDetailResponse\">\x82\xd3\xe4\x93\x02\x38\x12\x36/api/v2/supply/processing/receipts/{receipt_id}/detail\x12\xb3\x01\n\x18UpdateProcessingReceipts\x12,.manufactory.UpdateProcessingReceiptsRequest\x1a-.manufactory.UpdateProcessingReceiptsResponse\":\x82\xd3\xe4\x93\x02\x34\x1a//api/v2/supply/processing/receipts/{receipt_id}:\x01*\x12\xce\x01\n\x1e\x43hangeProcessingReceiptsStatus\x12\x32.manufactory.ChangeProcessingReceiptsStatusRequest\x1a\x33.manufactory.ChangeProcessingReceiptsStatusResponse\"C\x82\xd3\xe4\x93\x02=\x1a\x38/api/v2/supply/processing/receipts/{receipt_id}/{status}:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_CREATEPROCESSINGRECEIPTSREQUEST = _descriptor.Descriptor(
  name='CreateProcessingReceiptsRequest',
  full_name='manufactory.CreateProcessingReceiptsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.CreateProcessingReceiptsRequest.status', index=0,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.CreateProcessingReceiptsRequest.type', index=1,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_date', full_name='manufactory.CreateProcessingReceiptsRequest.processing_date', index=2,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_id', full_name='manufactory.CreateProcessingReceiptsRequest.machining_center_id', index=3,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_code', full_name='manufactory.CreateProcessingReceiptsRequest.machining_center_code', index=4,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_name', full_name='manufactory.CreateProcessingReceiptsRequest.machining_center_name', index=5,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.CreateProcessingReceiptsRequest.position_id', index=6,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.CreateProcessingReceiptsRequest.position_name', index=7,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.CreateProcessingReceiptsRequest.position_code', index=8,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_rule', full_name='manufactory.CreateProcessingReceiptsRequest.processing_rule', index=9,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.CreateProcessingReceiptsRequest.remark', index=10,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material', full_name='manufactory.CreateProcessingReceiptsRequest.target_material', index=11,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_code', full_name='manufactory.CreateProcessingReceiptsRequest.target_material_code', index=12,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_name', full_name='manufactory.CreateProcessingReceiptsRequest.target_material_name', index=13,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_id', full_name='manufactory.CreateProcessingReceiptsRequest.target_material_unit_id', index=14,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_name', full_name='manufactory.CreateProcessingReceiptsRequest.target_material_unit_name', index=15,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_spec', full_name='manufactory.CreateProcessingReceiptsRequest.target_material_unit_spec', index=16,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theory_output_rate', full_name='manufactory.CreateProcessingReceiptsRequest.theory_output_rate', index=17,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actual_output_rate', full_name='manufactory.CreateProcessingReceiptsRequest.actual_output_rate', index=18,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actual_output_quantity', full_name='manufactory.CreateProcessingReceiptsRequest.actual_output_quantity', index=19,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='opened_position', full_name='manufactory.CreateProcessingReceiptsRequest.opened_position', index=20,
      number=24, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.CreateProcessingReceiptsRequest.request_id', index=21,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='items', full_name='manufactory.CreateProcessingReceiptsRequest.items', index=22,
      number=26, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='manufactory.CreateProcessingReceiptsRequest.cost_center_id', index=23,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=118,
  serialized_end=813,
)


_ITEMS = _descriptor.Descriptor(
  name='Items',
  full_name='manufactory.Items',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.Items.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_id', full_name='manufactory.Items.main_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.Items.product_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.Items.product_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.Items.product_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='manufactory.Items.product_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.Items.unit_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.Items.unit_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='manufactory.Items.unit_spec', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='manufactory.Items.unit_rate', index=9,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_rate', full_name='manufactory.Items.material_rate', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actual_quantity', full_name='manufactory.Items.actual_quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.Items.partner_id', index=12,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.Items.created_by', index=13,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.Items.created_name', index=14,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.Items.updated_by', index=15,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.Items.updated_name', index=16,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.Items.created_at', index=17,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.Items.updated_at', index=18,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=816,
  serialized_end=1260,
)


_CREATEPROCESSINGRECEIPTSRESPONSE = _descriptor.Descriptor(
  name='CreateProcessingReceiptsResponse',
  full_name='manufactory.CreateProcessingReceiptsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='manufactory.CreateProcessingReceiptsResponse.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.CreateProcessingReceiptsResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1262,
  serialized_end=1332,
)


_LISTPROCESSINGRECEIPTSREQUEST = _descriptor.Descriptor(
  name='ListProcessingReceiptsRequest',
  full_name='manufactory.ListProcessingReceiptsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='manufactory.ListProcessingReceiptsRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='manufactory.ListProcessingReceiptsRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_centers', full_name='manufactory.ListProcessingReceiptsRequest.machining_centers', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.ListProcessingReceiptsRequest.code', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_materials', full_name='manufactory.ListProcessingReceiptsRequest.target_materials', index=4,
      number=6, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='manufactory.ListProcessingReceiptsRequest.position_ids', index=5,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.ListProcessingReceiptsRequest.status', index=6,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.ListProcessingReceiptsRequest.limit', index=7,
      number=10, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.ListProcessingReceiptsRequest.offset', index=8,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='manufactory.ListProcessingReceiptsRequest.include_total', index=9,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='manufactory.ListProcessingReceiptsRequest.order', index=10,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='manufactory.ListProcessingReceiptsRequest.sort', index=11,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1335,
  serialized_end=1648,
)


_ITEMROW = _descriptor.Descriptor(
  name='ItemRow',
  full_name='manufactory.ItemRow',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.ItemRow.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.ItemRow.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.ItemRow.created_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.ItemRow.created_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.ItemRow.updated_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.ItemRow.updated_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.ItemRow.created_at', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.ItemRow.updated_at', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_id', full_name='manufactory.ItemRow.main_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.ItemRow.status', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.ItemRow.code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_date', full_name='manufactory.ItemRow.processing_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.ItemRow.type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_id', full_name='manufactory.ItemRow.machining_center_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_code', full_name='manufactory.ItemRow.machining_center_code', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_name', full_name='manufactory.ItemRow.machining_center_name', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.ItemRow.position_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.ItemRow.position_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.ItemRow.position_code', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_rule', full_name='manufactory.ItemRow.processing_rule', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material', full_name='manufactory.ItemRow.target_material', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_code', full_name='manufactory.ItemRow.target_material_code', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_name', full_name='manufactory.ItemRow.target_material_name', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_id', full_name='manufactory.ItemRow.target_material_unit_id', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_name', full_name='manufactory.ItemRow.target_material_unit_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_spec', full_name='manufactory.ItemRow.target_material_unit_spec', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_rate', full_name='manufactory.ItemRow.target_material_unit_rate', index=26,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theory_output_rate', full_name='manufactory.ItemRow.theory_output_rate', index=27,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actual_output_rate', full_name='manufactory.ItemRow.actual_output_rate', index=28,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actual_output_quantity', full_name='manufactory.ItemRow.actual_output_quantity', index=29,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.ItemRow.remark', index=30,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='opened_position', full_name='manufactory.ItemRow.opened_position', index=31,
      number=31, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.ItemRow.request_id', index=32,
      number=33, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='manufactory.ItemRow.cost_center_id', index=33,
      number=34, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1651,
  serialized_end=2565,
)


_LISTPROCESSINGRECEIPTSRESPONSE = _descriptor.Descriptor(
  name='ListProcessingReceiptsResponse',
  full_name='manufactory.ListProcessingReceiptsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.ListProcessingReceiptsResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.ListProcessingReceiptsResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2567,
  serialized_end=2650,
)


_GETPROCESSINGRECEIPTSDETAILREQUEST = _descriptor.Descriptor(
  name='GetProcessingReceiptsDetailRequest',
  full_name='manufactory.GetProcessingReceiptsDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='manufactory.GetProcessingReceiptsDetailRequest.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2652,
  serialized_end=2708,
)


_GETPROCESSINGRECEIPTSDETAILRESPONSE = _descriptor.Descriptor(
  name='GetProcessingReceiptsDetailResponse',
  full_name='manufactory.GetProcessingReceiptsDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.GetProcessingReceiptsDetailResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.GetProcessingReceiptsDetailResponse.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.GetProcessingReceiptsDetailResponse.created_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.GetProcessingReceiptsDetailResponse.created_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.GetProcessingReceiptsDetailResponse.updated_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.GetProcessingReceiptsDetailResponse.updated_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.GetProcessingReceiptsDetailResponse.created_at', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.GetProcessingReceiptsDetailResponse.updated_at', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_id', full_name='manufactory.GetProcessingReceiptsDetailResponse.main_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.GetProcessingReceiptsDetailResponse.status', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.GetProcessingReceiptsDetailResponse.code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_date', full_name='manufactory.GetProcessingReceiptsDetailResponse.processing_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.GetProcessingReceiptsDetailResponse.type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_id', full_name='manufactory.GetProcessingReceiptsDetailResponse.machining_center_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_code', full_name='manufactory.GetProcessingReceiptsDetailResponse.machining_center_code', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_name', full_name='manufactory.GetProcessingReceiptsDetailResponse.machining_center_name', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.GetProcessingReceiptsDetailResponse.position_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.GetProcessingReceiptsDetailResponse.position_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.GetProcessingReceiptsDetailResponse.position_code', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_rule', full_name='manufactory.GetProcessingReceiptsDetailResponse.processing_rule', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material', full_name='manufactory.GetProcessingReceiptsDetailResponse.target_material', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_code', full_name='manufactory.GetProcessingReceiptsDetailResponse.target_material_code', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_name', full_name='manufactory.GetProcessingReceiptsDetailResponse.target_material_name', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_id', full_name='manufactory.GetProcessingReceiptsDetailResponse.target_material_unit_id', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_name', full_name='manufactory.GetProcessingReceiptsDetailResponse.target_material_unit_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_spec', full_name='manufactory.GetProcessingReceiptsDetailResponse.target_material_unit_spec', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_rate', full_name='manufactory.GetProcessingReceiptsDetailResponse.target_material_unit_rate', index=26,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theory_output_rate', full_name='manufactory.GetProcessingReceiptsDetailResponse.theory_output_rate', index=27,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actual_output_rate', full_name='manufactory.GetProcessingReceiptsDetailResponse.actual_output_rate', index=28,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actual_output_quantity', full_name='manufactory.GetProcessingReceiptsDetailResponse.actual_output_quantity', index=29,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.GetProcessingReceiptsDetailResponse.remark', index=30,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='opened_position', full_name='manufactory.GetProcessingReceiptsDetailResponse.opened_position', index=31,
      number=31, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.GetProcessingReceiptsDetailResponse.request_id', index=32,
      number=33, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='items', full_name='manufactory.GetProcessingReceiptsDetailResponse.items', index=33,
      number=34, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='manufactory.GetProcessingReceiptsDetailResponse.cost_center_id', index=34,
      number=35, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2711,
  serialized_end=3688,
)


_UPDATEPROCESSINGRECEIPTSREQUEST = _descriptor.Descriptor(
  name='UpdateProcessingReceiptsRequest',
  full_name='manufactory.UpdateProcessingReceiptsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='manufactory.UpdateProcessingReceiptsRequest.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_date', full_name='manufactory.UpdateProcessingReceiptsRequest.processing_date', index=1,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.UpdateProcessingReceiptsRequest.position_id', index=2,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.UpdateProcessingReceiptsRequest.position_name', index=3,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.UpdateProcessingReceiptsRequest.position_code', index=4,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_rule', full_name='manufactory.UpdateProcessingReceiptsRequest.processing_rule', index=5,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.UpdateProcessingReceiptsRequest.remark', index=6,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_id', full_name='manufactory.UpdateProcessingReceiptsRequest.machining_center_id', index=7,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_code', full_name='manufactory.UpdateProcessingReceiptsRequest.machining_center_code', index=8,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_name', full_name='manufactory.UpdateProcessingReceiptsRequest.machining_center_name', index=9,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material', full_name='manufactory.UpdateProcessingReceiptsRequest.target_material', index=10,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_code', full_name='manufactory.UpdateProcessingReceiptsRequest.target_material_code', index=11,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_name', full_name='manufactory.UpdateProcessingReceiptsRequest.target_material_name', index=12,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_id', full_name='manufactory.UpdateProcessingReceiptsRequest.target_material_unit_id', index=13,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_name', full_name='manufactory.UpdateProcessingReceiptsRequest.target_material_unit_name', index=14,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_material_unit_spec', full_name='manufactory.UpdateProcessingReceiptsRequest.target_material_unit_spec', index=15,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theory_output_rate', full_name='manufactory.UpdateProcessingReceiptsRequest.theory_output_rate', index=16,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actual_output_rate', full_name='manufactory.UpdateProcessingReceiptsRequest.actual_output_rate', index=17,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actual_output_quantity', full_name='manufactory.UpdateProcessingReceiptsRequest.actual_output_quantity', index=18,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='opened_position', full_name='manufactory.UpdateProcessingReceiptsRequest.opened_position', index=19,
      number=30, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.UpdateProcessingReceiptsRequest.request_id', index=20,
      number=33, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='items', full_name='manufactory.UpdateProcessingReceiptsRequest.items', index=21,
      number=34, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='manufactory.UpdateProcessingReceiptsRequest.cost_center_id', index=22,
      number=35, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3691,
  serialized_end=4376,
)


_UPDATEPROCESSINGRECEIPTSRESPONSE = _descriptor.Descriptor(
  name='UpdateProcessingReceiptsResponse',
  full_name='manufactory.UpdateProcessingReceiptsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='manufactory.UpdateProcessingReceiptsResponse.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.UpdateProcessingReceiptsResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4378,
  serialized_end=4448,
)


_CHANGEPROCESSINGRECEIPTSSTATUSREQUEST = _descriptor.Descriptor(
  name='ChangeProcessingReceiptsStatusRequest',
  full_name='manufactory.ChangeProcessingReceiptsStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='manufactory.ChangeProcessingReceiptsStatusRequest.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.ChangeProcessingReceiptsStatusRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4450,
  serialized_end=4525,
)


_CHANGEPROCESSINGRECEIPTSSTATUSRESPONSE = _descriptor.Descriptor(
  name='ChangeProcessingReceiptsStatusResponse',
  full_name='manufactory.ChangeProcessingReceiptsStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='manufactory.ChangeProcessingReceiptsStatusResponse.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.ChangeProcessingReceiptsStatusResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4527,
  serialized_end=4603,
)

_CREATEPROCESSINGRECEIPTSREQUEST.fields_by_name['processing_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEPROCESSINGRECEIPTSREQUEST.fields_by_name['items'].message_type = _ITEMS
_ITEMS.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ITEMS.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPROCESSINGRECEIPTSREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPROCESSINGRECEIPTSREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ITEMROW.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ITEMROW.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ITEMROW.fields_by_name['processing_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPROCESSINGRECEIPTSRESPONSE.fields_by_name['rows'].message_type = _ITEMROW
_GETPROCESSINGRECEIPTSDETAILRESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPROCESSINGRECEIPTSDETAILRESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPROCESSINGRECEIPTSDETAILRESPONSE.fields_by_name['processing_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPROCESSINGRECEIPTSDETAILRESPONSE.fields_by_name['items'].message_type = _ITEMS
_UPDATEPROCESSINGRECEIPTSREQUEST.fields_by_name['processing_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATEPROCESSINGRECEIPTSREQUEST.fields_by_name['items'].message_type = _ITEMS
DESCRIPTOR.message_types_by_name['CreateProcessingReceiptsRequest'] = _CREATEPROCESSINGRECEIPTSREQUEST
DESCRIPTOR.message_types_by_name['Items'] = _ITEMS
DESCRIPTOR.message_types_by_name['CreateProcessingReceiptsResponse'] = _CREATEPROCESSINGRECEIPTSRESPONSE
DESCRIPTOR.message_types_by_name['ListProcessingReceiptsRequest'] = _LISTPROCESSINGRECEIPTSREQUEST
DESCRIPTOR.message_types_by_name['ItemRow'] = _ITEMROW
DESCRIPTOR.message_types_by_name['ListProcessingReceiptsResponse'] = _LISTPROCESSINGRECEIPTSRESPONSE
DESCRIPTOR.message_types_by_name['GetProcessingReceiptsDetailRequest'] = _GETPROCESSINGRECEIPTSDETAILREQUEST
DESCRIPTOR.message_types_by_name['GetProcessingReceiptsDetailResponse'] = _GETPROCESSINGRECEIPTSDETAILRESPONSE
DESCRIPTOR.message_types_by_name['UpdateProcessingReceiptsRequest'] = _UPDATEPROCESSINGRECEIPTSREQUEST
DESCRIPTOR.message_types_by_name['UpdateProcessingReceiptsResponse'] = _UPDATEPROCESSINGRECEIPTSRESPONSE
DESCRIPTOR.message_types_by_name['ChangeProcessingReceiptsStatusRequest'] = _CHANGEPROCESSINGRECEIPTSSTATUSREQUEST
DESCRIPTOR.message_types_by_name['ChangeProcessingReceiptsStatusResponse'] = _CHANGEPROCESSINGRECEIPTSSTATUSRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CreateProcessingReceiptsRequest = _reflection.GeneratedProtocolMessageType('CreateProcessingReceiptsRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPROCESSINGRECEIPTSREQUEST,
  __module__ = 'manufactory.processing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreateProcessingReceiptsRequest)
  ))
_sym_db.RegisterMessage(CreateProcessingReceiptsRequest)

Items = _reflection.GeneratedProtocolMessageType('Items', (_message.Message,), dict(
  DESCRIPTOR = _ITEMS,
  __module__ = 'manufactory.processing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.Items)
  ))
_sym_db.RegisterMessage(Items)

CreateProcessingReceiptsResponse = _reflection.GeneratedProtocolMessageType('CreateProcessingReceiptsResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPROCESSINGRECEIPTSRESPONSE,
  __module__ = 'manufactory.processing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreateProcessingReceiptsResponse)
  ))
_sym_db.RegisterMessage(CreateProcessingReceiptsResponse)

ListProcessingReceiptsRequest = _reflection.GeneratedProtocolMessageType('ListProcessingReceiptsRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTPROCESSINGRECEIPTSREQUEST,
  __module__ = 'manufactory.processing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ListProcessingReceiptsRequest)
  ))
_sym_db.RegisterMessage(ListProcessingReceiptsRequest)

ItemRow = _reflection.GeneratedProtocolMessageType('ItemRow', (_message.Message,), dict(
  DESCRIPTOR = _ITEMROW,
  __module__ = 'manufactory.processing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ItemRow)
  ))
_sym_db.RegisterMessage(ItemRow)

ListProcessingReceiptsResponse = _reflection.GeneratedProtocolMessageType('ListProcessingReceiptsResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTPROCESSINGRECEIPTSRESPONSE,
  __module__ = 'manufactory.processing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ListProcessingReceiptsResponse)
  ))
_sym_db.RegisterMessage(ListProcessingReceiptsResponse)

GetProcessingReceiptsDetailRequest = _reflection.GeneratedProtocolMessageType('GetProcessingReceiptsDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPROCESSINGRECEIPTSDETAILREQUEST,
  __module__ = 'manufactory.processing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetProcessingReceiptsDetailRequest)
  ))
_sym_db.RegisterMessage(GetProcessingReceiptsDetailRequest)

GetProcessingReceiptsDetailResponse = _reflection.GeneratedProtocolMessageType('GetProcessingReceiptsDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPROCESSINGRECEIPTSDETAILRESPONSE,
  __module__ = 'manufactory.processing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetProcessingReceiptsDetailResponse)
  ))
_sym_db.RegisterMessage(GetProcessingReceiptsDetailResponse)

UpdateProcessingReceiptsRequest = _reflection.GeneratedProtocolMessageType('UpdateProcessingReceiptsRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPROCESSINGRECEIPTSREQUEST,
  __module__ = 'manufactory.processing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.UpdateProcessingReceiptsRequest)
  ))
_sym_db.RegisterMessage(UpdateProcessingReceiptsRequest)

UpdateProcessingReceiptsResponse = _reflection.GeneratedProtocolMessageType('UpdateProcessingReceiptsResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPROCESSINGRECEIPTSRESPONSE,
  __module__ = 'manufactory.processing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.UpdateProcessingReceiptsResponse)
  ))
_sym_db.RegisterMessage(UpdateProcessingReceiptsResponse)

ChangeProcessingReceiptsStatusRequest = _reflection.GeneratedProtocolMessageType('ChangeProcessingReceiptsStatusRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHANGEPROCESSINGRECEIPTSSTATUSREQUEST,
  __module__ = 'manufactory.processing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ChangeProcessingReceiptsStatusRequest)
  ))
_sym_db.RegisterMessage(ChangeProcessingReceiptsStatusRequest)

ChangeProcessingReceiptsStatusResponse = _reflection.GeneratedProtocolMessageType('ChangeProcessingReceiptsStatusResponse', (_message.Message,), dict(
  DESCRIPTOR = _CHANGEPROCESSINGRECEIPTSSTATUSRESPONSE,
  __module__ = 'manufactory.processing_receipts_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ChangeProcessingReceiptsStatusResponse)
  ))
_sym_db.RegisterMessage(ChangeProcessingReceiptsStatusResponse)



_PROCESSINGRECEIPTS = _descriptor.ServiceDescriptor(
  name='ProcessingReceipts',
  full_name='manufactory.ProcessingReceipts',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=4606,
  serialized_end=5546,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateProcessingReceipts',
    full_name='manufactory.ProcessingReceipts.CreateProcessingReceipts',
    index=0,
    containing_service=None,
    input_type=_CREATEPROCESSINGRECEIPTSREQUEST,
    output_type=_CREATEPROCESSINGRECEIPTSRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\"\"/api/v2/supply/processing/receipts:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListProcessingReceipts',
    full_name='manufactory.ProcessingReceipts.ListProcessingReceipts',
    index=1,
    containing_service=None,
    input_type=_LISTPROCESSINGRECEIPTSREQUEST,
    output_type=_LISTPROCESSINGRECEIPTSRESPONSE,
    serialized_options=_b('\202\323\344\223\002)\022\'/api/v2/supply/processing/receipts/list'),
  ),
  _descriptor.MethodDescriptor(
    name='GetProcessingReceiptsDetail',
    full_name='manufactory.ProcessingReceipts.GetProcessingReceiptsDetail',
    index=2,
    containing_service=None,
    input_type=_GETPROCESSINGRECEIPTSDETAILREQUEST,
    output_type=_GETPROCESSINGRECEIPTSDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\0226/api/v2/supply/processing/receipts/{receipt_id}/detail'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateProcessingReceipts',
    full_name='manufactory.ProcessingReceipts.UpdateProcessingReceipts',
    index=3,
    containing_service=None,
    input_type=_UPDATEPROCESSINGRECEIPTSREQUEST,
    output_type=_UPDATEPROCESSINGRECEIPTSRESPONSE,
    serialized_options=_b('\202\323\344\223\0024\032//api/v2/supply/processing/receipts/{receipt_id}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ChangeProcessingReceiptsStatus',
    full_name='manufactory.ProcessingReceipts.ChangeProcessingReceiptsStatus',
    index=4,
    containing_service=None,
    input_type=_CHANGEPROCESSINGRECEIPTSSTATUSREQUEST,
    output_type=_CHANGEPROCESSINGRECEIPTSSTATUSRESPONSE,
    serialized_options=_b('\202\323\344\223\002=\0328/api/v2/supply/processing/receipts/{receipt_id}/{status}:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_PROCESSINGRECEIPTS)

DESCRIPTOR.services_by_name['ProcessingReceipts'] = _PROCESSINGRECEIPTS

# @@protoc_insertion_point(module_scope)
