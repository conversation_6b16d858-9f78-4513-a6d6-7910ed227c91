syntax = "proto3";

package manufactory;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

service ProcessingReceipts{
    // 创建加工单
    rpc CreateProcessingReceipts (CreateProcessingReceiptsRequest) returns (CreateProcessingReceiptsResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/processing/receipts"
        body: "*"
        };
    }
    // 加工单列表查询
    rpc ListProcessingReceipts (ListProcessingReceiptsRequest) returns (ListProcessingReceiptsResponse){
        option (google.api.http) = {
        get: "/api/v2/supply/processing/receipts/list"
        };
    }
    // 查询加工单详情
    rpc GetProcessingReceiptsDetail (GetProcessingReceiptsDetailRequest) returns (GetProcessingReceiptsDetailResponse){
        option (google.api.http) = {
        get: "/api/v2/supply/processing/receipts/{receipt_id}/detail"
        };
    }
    // 更新加工单
    rpc UpdateProcessingReceipts (UpdateProcessingReceiptsRequest) returns (UpdateProcessingReceiptsResponse){
        option (google.api.http) = {
        put: "/api/v2/supply/processing/receipts/{receipt_id}"
        body: "*"
        };
    }
    // 加工单状态变更
    rpc ChangeProcessingReceiptsStatus (ChangeProcessingReceiptsStatusRequest) returns (ChangeProcessingReceiptsStatusResponse){
        option (google.api.http) = {
        put: "/api/v2/supply/processing/receipts/{receipt_id}/{status}"
        body: "*"
        };
    }
}

// 创建加工单请求参数
message CreateProcessingReceiptsRequest{
    // 订单状态:
    // INITED = 'INITED'           # 新建
    // SUBMITTED = 'SUBMITTED'     # 已提交
    // APPROVED = 'APPROVED'       # 已审核
    // REJECTED = 'REJECTED'       # 已驳回
    string status = 4;
    // 加工类型*预留*
    string type = 5;
    // 加工日期
    google.protobuf.Timestamp processing_date = 6;
    // 加工中心id
    uint64 machining_center_id = 7;
    // 加工中心code
    string machining_center_code = 8;
    //加工中心名称
    string machining_center_name = 9;
    // 仓位id
    uint64 position_id = 10;
    // 仓位名称
    string position_name = 11;
    // 仓位code
    string position_code = 12;
    // 加工规则ID
    uint64 processing_rule = 13;
    // 备注
    string remark = 14;
    // 目标物料ID
    uint64 target_material = 15;
    // 目标物料code
    string target_material_code = 16;
    // 目标物料name
    string target_material_name = 17;
    // 目标物料单位ID
    uint64 target_material_unit_id = 18;
    // 目标物料单位名称(拿不到可不传)
    string target_material_unit_name = 19;
    // 目标物料单位规格(拿不到可不传)
    string target_material_unit_spec = 20;
    // 理论产出率
    double theory_output_rate = 21;
    // 实际产出率
    double actual_output_rate = 22;
    // 实际产出数量
    double actual_output_quantity = 23;
    // 是否开启多仓位
    bool opened_position = 24;
    // 幂等性校验请求id
    uint64 request_id = 25;
    // 商品(物料)条目
    repeated Items items = 26;
    // 成本中心ID
    uint64 cost_center_id = 30;
}

message Items{
    uint64 id = 1;
    // 关联的加工单id
    uint64 main_id = 2;
    // 物料/商品id
    uint64 product_id = 3;
    // 商品code
    string product_code = 4;
    // 商品名称
    string product_name = 5;
    // 商品类型
    string product_type = 6;
    // 单位id
    uint64 unit_id = 7;
    // 单位名称
    string unit_name = 8;
    // 单位规格
    string unit_spec = 9;
    // 单位转换率
    double unit_rate = 13;
    // 物料占比
    double material_rate = 11;
    // 实际加工物料数量
    double actual_quantity = 12;
    uint64 partner_id = 16;
    uint64 created_by = 17;
    string created_name = 18;
    uint64 updated_by = 19;
    string updated_name = 20;
    google.protobuf.Timestamp created_at = 21;
    google.protobuf.Timestamp updated_at = 22;
}

// 创建加工单返回
message CreateProcessingReceiptsResponse{
    // 加工单id
    uint64 receipt_id = 1;
    string result = 2;
}

// 加工单列表查询请求参数
message ListProcessingReceiptsRequest{
    // 开始日期
    google.protobuf.Timestamp start_date = 1;
    // 结束日期
    google.protobuf.Timestamp end_date = 2;
    // 加工中心ID列表
    repeated uint64 machining_centers = 3;
    // 单号
    string code = 5;
    // 目标物料ID列表
    repeated uint64 target_materials = 6;
    // 仓位ID列表
    repeated uint64 position_ids = 7;
    // 订单状态(多个传列表) 'INITED'新建 'SUBMITTED'已提交 'APPROVED'已审核 'REJECTED'已驳回
    repeated string status = 9;
    int64 limit = 10;
    uint64 offset = 11;
    bool include_total = 12;
    // 排序方式  asc or desc
    string order = 13;
    // 排序字段
    string sort = 14;
}

message ItemRow{
    uint64 id = 1;
    uint64 partner_id = 2;
    uint64 created_by = 3;
    string created_name = 4;
    uint64 updated_by = 5;
    string updated_name = 6;
    google.protobuf.Timestamp created_at = 7;
    google.protobuf.Timestamp updated_at = 8;
    uint64 main_id = 9;
    string status = 10;
    string code = 11;
    // 加工日期
    google.protobuf.Timestamp processing_date = 12;
    // 加工类型*预留*
    string type = 13;
    // 加工中心id
    uint64 machining_center_id = 14;
    // 加工中心code
    string machining_center_code = 15;
    //加工中心名称
    string machining_center_name = 16;
    // 仓位id
    uint64 position_id = 17;
    // 仓位名称
    string position_name = 18;
    // 仓位code
    string position_code = 19;
    // 加工规则ID
    uint64 processing_rule = 20;
    // 目标物料ID
    uint64 target_material = 21;
    // 目标物料code
    string target_material_code = 22;
    // 目标物料name
    string target_material_name = 23;
    // 目标物料单位ID
    uint64 target_material_unit_id = 24;
    // 目标物料单位名称
    string target_material_unit_name = 25;
    // 目标物料单位规格
    string target_material_unit_spec = 26;
    // 目标物料单位转换率
    double target_material_unit_rate = 32;
    // 理论产出率
    double theory_output_rate = 27;
    // 实际产出率
    double actual_output_rate = 28;
    // 实际产出数量
    double actual_output_quantity = 29;
    // 备注
    string remark = 30;
    // 是否开启多仓位
    bool opened_position = 31;
    // 幂等性校验请求id
    uint64 request_id = 33;
    // 成本中心ID
    uint64 cost_center_id = 34;
}

// 加工单列表查询返回参数
message ListProcessingReceiptsResponse{
    repeated ItemRow rows = 1;
    uint64 total = 2;
}

// 查询加工单详情请求参数
message GetProcessingReceiptsDetailRequest{
    uint64 receipt_id = 1;
}

message GetProcessingReceiptsDetailResponse{
    uint64 id = 1;
    uint64 partner_id = 2;
    uint64 created_by = 3;
    string created_name = 4;
    uint64 updated_by = 5;
    string updated_name = 6;
    google.protobuf.Timestamp created_at = 7;
    google.protobuf.Timestamp updated_at = 8;
    uint64 main_id = 9;
    string status = 10;
    string code = 11;
    // 加工日期
    google.protobuf.Timestamp processing_date = 12;
    // 加工类型*预留*
    string type = 13;
    // 加工中心id
    uint64 machining_center_id = 14;
    // 加工中心code
    string machining_center_code = 15;
    //加工中心名称
    string machining_center_name = 16;
    // 仓位id
    uint64 position_id = 17;
    // 仓位名称
    string position_name = 18;
    // 仓位code
    string position_code = 19;
    // 加工规则ID
    uint64 processing_rule = 20;
    // 目标物料ID
    uint64 target_material = 21;
    // 目标物料code
    string target_material_code = 22;
    // 目标物料name
    string target_material_name = 23;
    // 目标物料单位ID
    uint64 target_material_unit_id = 24;
    // 目标物料单位名称
    string target_material_unit_name = 25;
    // 目标物料单位规格
    string target_material_unit_spec = 26;
    // 目标物料单位转换率
    double target_material_unit_rate = 36;
    // 理论产出率
    double theory_output_rate = 27;
    // 实际产出率
    double actual_output_rate = 28;
    // 实际产出数量
    double actual_output_quantity = 29;
    // 备注
    string remark = 30;
    // 是否开启多仓位
    bool opened_position = 31;
    // 幂等性校验请求id
    uint64 request_id = 33;
    repeated Items items = 34;
    // 成本中心ID
    uint64 cost_center_id = 35;
}


// 更新加工单请求参数
message UpdateProcessingReceiptsRequest{
    // 加工id
    uint64 receipt_id = 1;
    // 加工日期
    google.protobuf.Timestamp processing_date = 6;
    // 仓位id
    uint64 position_id = 10;
    // 仓位名称
    string position_name = 11;
    // 仓位code
    string position_code = 12;
    // 加工规则ID
    uint64 processing_rule = 13;
    // 备注
    string remark = 14;
    // 加工中心id
    uint64 machining_center_id = 15;
    // 加工中心code
    string machining_center_code = 16;
    // 加工中心名称
    string machining_center_name = 17;
    // 目标物料ID
    uint64 target_material = 18;
    // 目标物料code
    string target_material_code = 19;
    // 目标物料name
    string target_material_name = 20;
    // 目标物料单位ID
    uint64 target_material_unit_id = 21;
    // 目标物料单位名称(拿不到可不传)
    string target_material_unit_name = 22;
    // 目标物料单位规格(拿不到可不传)
    string target_material_unit_spec = 23;
    // 理论产出率
    double theory_output_rate = 27;
    // 实际产出率
    double actual_output_rate = 28;
    // 实际产出数量
    double actual_output_quantity = 29;
    // 是否开启多仓位
    bool opened_position = 30;
    // 幂等性校验请求id
    uint64 request_id = 33;
    repeated Items items = 34;
    // 成本中心ID
    uint64 cost_center_id = 35;
}

message UpdateProcessingReceiptsResponse{
    uint64 receipt_id = 1;
    string result = 2;
}

// 加工单状态变更请求参数
message ChangeProcessingReceiptsStatusRequest {
    // 加工id
    uint64 receipt_id = 1;
    // 'SUBMITTED'已提交 'APPROVED'已审核 'REJECTED'已驳回
    string status = 2;
}

message ChangeProcessingReceiptsStatusResponse{
    uint64 receipt_id = 1;
    string result = 2;
}