# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: manufactory/adjust.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='manufactory/adjust.proto',
  package='manufactory',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x18manufactory/adjust.proto\x12\x0bmanufactory\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"[\n\x13\x43reateAdjustRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x30\n\x0crequest_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"X\n\x14\x43reateAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x30\n\x0crequest_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xc3\x03\n\x10GetAdjustRequest\x12\x15\n\rinclude_total\x18\x01 \x01(\x08\x12.\n\nstart_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0breason_type\x18\x04 \x01(\t\x12\x10\n\x08\x62ranches\x18\x05 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x06 \x03(\x04\x12\x0e\n\x06offset\x18\x07 \x01(\r\x12\r\n\x05limit\x18\x08 \x01(\r\x12\x34\n\x06status\x18\t \x03(\x0e\x32$.manufactory.GetAdjustRequest.STATUS\x12\x0c\n\x04\x63ode\x18\n \x01(\t\x12\r\n\x05order\x18\x0c \x01(\t\x12\x0c\n\x04sort\x18\r \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x1e \x01(\t\x12\x0f\n\x07sources\x18\x1f \x03(\t\"X\n\x06STATUS\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tSUBMITTED\x10\x02\x12\x0c\n\x08\x41PPROVED\x10\x03\x12\x0c\n\x08REJECTED\x10\x04\x12\r\n\tCANCELLED\x10\x05\"\x9b\x06\n\x06\x41\x64just\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1b\n\x13\x61\x64just_order_number\x18\x02 \x01(\x04\x12\x14\n\x0c\x61\x64just_store\x18\x03 \x01(\x04\x12!\n\x19\x61\x64just_store_secondary_id\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x12\n\npartner_id\x18\x06 \x01(\x04\x12\x16\n\x0eprocess_status\x18\x07 \x01(\t\x12\x13\n\x0breason_type\x18\x08 \x01(\t\x12\x0e\n\x06remark\x18\t \x01(\t\x12\x0e\n\x06status\x18\n \x01(\t\x12/\n\x0b\x61\x64just_date\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x0e \x01(\x04\x12\x12\n\nupdated_by\x18\x0f \x01(\x04\x12\x0f\n\x07user_id\x18\x10 \x01(\x04\x12\x17\n\x0f\x62ranch_batch_id\x18\x11 \x01(\x04\x12\x15\n\rschedule_code\x18\x12 \x01(\t\x12\x13\n\x0bschedule_id\x18\x13 \x01(\x04\x12\x12\n\nrequest_id\x18\x14 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x15 \x01(\t\x12\x14\n\x0cupdated_name\x18\x16 \x01(\t\x12\x12\n\nreceive_id\x18\x19 \x01(\x04\x12\x14\n\x0creceive_code\x18\x1a \x01(\t\x12\x15\n\rschedule_name\x18\x1c \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x1e \x01(\t\x12-\n\x0b\x61ttachments\x18\x1f \x03(\x0b\x32\x18.manufactory.Attachments\x12\x0e\n\x06source\x18  \x01(\t\x12\x13\n\x0breason_name\x18! \x01(\t\x12\x15\n\rreject_reason\x18$ \x01(\t\x12\x14\n\x0ctotal_amount\x18& \x01(\x01\x12\x1a\n\x12total_sales_amount\x18\' \x01(\x01\"E\n\x11GetAdjustResponse\x12!\n\x04rows\x18\x01 \x03(\x0b\x32\x13.manufactory.Adjust\x12\r\n\x05total\x18\x02 \x01(\r\"~\n\x17GetAdjustProductRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x15\n\rinclude_total\x18\x02 \x01(\x08\x12\r\n\x05order\x18\x03 \x01(\t\x12\x0e\n\x06offset\x18\x04 \x01(\r\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x0b\n\x03lan\x18\x06 \x01(\t\"\xb5\x08\n\rAdjustProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x02 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x03 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x04 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x05 \x01(\t\x12\x11\n\tadjust_id\x18\x06 \x01(\x04\x12\x14\n\x0c\x61\x64just_store\x18\x07 \x01(\x04\x12\x1a\n\x12\x63onfirmed_quantity\x18\x08 \x01(\x01\x12\x12\n\ncreated_by\x18\t \x01(\x04\x12\x14\n\x0cis_confirmed\x18\n \x01(\x08\x12\x13\n\x0bitem_number\x18\x0b \x01(\r\x12\x17\n\x0fmaterial_number\x18\x0c \x01(\t\x12\x12\n\npartner_id\x18\r \x01(\x04\x12\x14\n\x0cproduct_code\x18\x0e \x01(\t\x12\x12\n\nproduct_id\x18\x0f \x01(\x04\x12\x14\n\x0cproduct_name\x18\x10 \x01(\t\x12\x10\n\x08quantity\x18\x11 \x01(\x01\x12\x0c\n\x04spec\x18\x12 \x01(\t\x12\x1a\n\x12stocktake_quantity\x18\x13 \x01(\x01\x12\x19\n\x11stocktake_unit_id\x18\x14 \x01(\x04\x12\x0f\n\x07unit_id\x18\x15 \x01(\x04\x12\x11\n\tunit_name\x18\x16 \x01(\t\x12\x11\n\tunit_spec\x18\x17 \x01(\t\x12\x12\n\nupdated_by\x18\x18 \x01(\x04\x12/\n\x0b\x61\x64just_date\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x1b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07user_id\x18\x1c \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1d \x01(\t\x12\x14\n\x0cupdated_name\x18\x1e \x01(\t\x12\x13\n\x0breason_type\x18\x1f \x01(\t\x12#\n\x1b\x63onvert_accounting_quantity\x18  \x01(\x01\x12\x0e\n\x06is_bom\x18! \x01(\x08\x12\x13\n\x0bposition_id\x18\" \x01(\x04\x12\x12\n\nsku_remark\x18# \x01(\t\x12\x33\n\x05units\x18$ \x03(\x0b\x32$.manufactory.CreateAdjustProductUint\x12\x12\n\nmodel_name\x18% \x01(\t\x12\x10\n\x08tax_rate\x18& \x01(\x01\x12\x11\n\ttax_price\x18\' \x01(\x01\x12\x0e\n\x06\x61mount\x18( \x01(\x01\x12\x12\n\ntax_amount\x18) \x01(\x01\x12\x12\n\ncost_price\x18* \x01(\x01\x12\x14\n\x0csales_amount\x18+ \x01(\x01\x12\x13\n\x0bsales_price\x18, \x01(\x01\"\x8f\x01\n\x18GetAdjustProductResponse\x12(\n\x04rows\x18\x01 \x03(\x0b\x32\x1a.manufactory.AdjustProduct\x12\r\n\x05total\x18\x02 \x01(\r\x12:\n\rposition_rows\x18\x03 \x03(\x0b\x32#.manufactory.AdjustPositionProducts\">\n\x14GetAdjustByIDRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x02 \x01(\t\">\n\x14\x43onfirmAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x02 \x01(\t\"\'\n\x15\x43onfirmAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"(\n\x13SubmitAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\"&\n\x14SubmitAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\")\n\x14\x41pproveAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\"\'\n\x15\x41pproveAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"?\n\x13RejectAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x15\n\rreject_reason\x18\x02 \x01(\t\"&\n\x14RejectAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\x85\x02\n GetAdjustProductByStoreIDRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\r\x12\x0e\n\x06offset\x18\x03 \x01(\r\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12/\n\x0b\x61\x64just_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06search\x18\x06 \x01(\t\x12\x15\n\rsearch_fields\x18\x07 \x01(\t\x12\x14\n\x0c\x63\x61tegory_ids\x18\x08 \x03(\x04\x12\x0b\n\x03lan\x18\t \x01(\t\x12\x10\n\x08order_by\x18\n \x01(\t\x12\x0c\n\x04sort\x18\x0b \x01(\t\"\xe2\x02\n\x17\x43reateAdjustProductUint\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x0f\n\x07updated\x18\x07 \x01(\t\x12\x0c\n\x04rate\x18\x08 \x01(\x01\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\t \x01(\x08\x12\r\n\x05order\x18\n \x01(\x08\x12\x10\n\x08purchase\x18\x0b \x01(\x08\x12\r\n\x05sales\x18\x0c \x01(\x08\x12\x11\n\tstocktake\x18\r \x01(\x08\x12\x0b\n\x03\x62om\x18\x0e \x01(\x08\x12\x19\n\x11\x64\x65\x66\x61ult_stocktake\x18\x0f \x01(\x08\x12\x10\n\x08transfer\x18\x10 \x01(\x08\x12\x10\n\x08tax_rate\x18\x11 \x01(\x01\x12\x11\n\ttax_price\x18\x12 \x01(\x01\x12\x12\n\ncost_price\x18\x13 \x01(\x01\x12\x13\n\x0bsales_price\x18\x14 \x01(\x01\"\x99\x02\n\x13\x43reateAdjustProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x19\n\x11loss_report_order\x18\x02 \x01(\t\x12\x14\n\x0cproduct_code\x18\x03 \x01(\t\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12\x12\n\nmodel_name\x18\x05 \x01(\t\x12\x14\n\x0cstorage_type\x18\x06 \x01(\t\x12\x1b\n\x13product_category_id\x18\x07 \x01(\x04\x12\x33\n\x05units\x18\x08 \x03(\x0b\x32$.manufactory.CreateAdjustProductUint\x12\x0f\n\x07\x62\x61rcode\x18\t \x03(\t\x12\x1a\n\x12real_inventory_qty\x18\n \x01(\x01\"\xa6\x01\n!GetAdjustProductByStoreIDResponse\x12.\n\x04rows\x18\x01 \x03(\x0b\x32 .manufactory.CreateAdjustProduct\x12\r\n\x05total\x18\x02 \x01(\r\x12\x42\n\x18inventory_unchanged_rows\x18\x03 \x03(\x0b\x32 .manufactory.CreateAdjustProduct\"\xb6\x02\n\x14\x43reatedAdjustProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x0f\n\x07unit_id\x18\x03 \x01(\x04\x12\x10\n\x08quantity\x18\x04 \x01(\x01\x12\x13\n\x0breason_type\x18\x05 \x01(\t\x12\x13\n\x0bposition_id\x18\x06 \x01(\x04\x12)\n\tskuRemark\x18\x07 \x03(\x0b\x32\x16.manufactory.SkuRemark\x12\x10\n\x08tax_rate\x18\x08 \x01(\x01\x12\x11\n\ttax_price\x18\t \x01(\x01\x12\x0e\n\x06\x61mount\x18\n \x01(\x01\x12\x12\n\ntax_amount\x18\x0b \x01(\x01\x12\x12\n\ncost_price\x18\x0c \x01(\x01\x12\x13\n\x0bsales_price\x18\r \x01(\x01\x12\x14\n\x0csales_amount\x18\x0e \x01(\x01\"\xb4\x02\n\x14\x43reatedAdjustRequest\x12\x14\n\x0c\x61\x64just_store\x18\x01 \x01(\x04\x12\x33\n\x08products\x18\x02 \x03(\x0b\x32!.manufactory.CreatedAdjustProduct\x12\x13\n\x0breason_type\x18\x03 \x01(\t\x12\x0e\n\x06remark\x18\x04 \x01(\t\x12\x12\n\nrequest_id\x18\x05 \x01(\x04\x12/\n\x0b\x61\x64just_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\x07 \x01(\t\x12\x13\n\x0bposition_id\x18\x08 \x01(\x04\x12\x0e\n\x06source\x18\t \x01(\t\x12-\n\x0b\x61ttachments\x18\x0b \x03(\x0b\x32\x18.manufactory.Attachments\"(\n\x0b\x41ttachments\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\"\x8c\x02\n\x13UpdateAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x33\n\x08products\x18\x02 \x03(\x0b\x32!.manufactory.CreatedAdjustProduct\x12\x0e\n\x06remark\x18\x03 \x01(\t\x12\x13\n\x0breason_type\x18\x04 \x01(\t\x12/\n\x0b\x61\x64just_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\x06 \x01(\t\x12-\n\x0b\x61ttachments\x18\x07 \x03(\x0b\x32\x18.manufactory.Attachments\x12\x13\n\x0bposition_id\x18\x08 \x01(\x04\"(\n\x13\x44\x65leteAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\"&\n\x14\x44\x65leteAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"<\n\x1a\x44\x65leteAdjustProductRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x0b\n\x03ids\x18\x02 \x03(\x04\"-\n\x1b\x44\x65leteAdjustProductResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\xc0\x03\n\x19GetAdjustBiCollectRequest\x12\x14\n\x0c\x63\x61tegory_ids\x18\x01 \x03(\x04\x12\x14\n\x0cproduct_name\x18\x02 \x01(\t\x12.\n\nstart_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12\x11\n\tstore_ids\x18\x08 \x03(\x04\x12\x16\n\x0e\x62om_product_id\x18\t \x03(\x04\x12\x15\n\rperiod_symbol\x18\n \x01(\t\x12\r\n\x05order\x18\x0b \x01(\t\x12\x0c\n\x04sort\x18\x0c \x01(\t\x12\x0c\n\x04\x63ode\x18\r \x01(\t\x12\x13\n\x0breason_type\x18\x0e \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0f \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x10 \x01(\t\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x13\n\x0bhour_offset\x18\x12 \x01(\r\x12\x14\n\x0cposition_ids\x18\x13 \x03(\x04\"\xcd\x06\n\x17\x41\x64justBiCollectResponse\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x05 \x01(\x04\x12\x15\n\rcategory_name\x18\x06 \x01(\t\x12\x14\n\x0cproduct_code\x18\x07 \x01(\t\x12\x12\n\nproduct_id\x18\x08 \x01(\x04\x12\x14\n\x0cproduct_name\x18\t \x01(\t\x12\x10\n\x08quantity\x18\x0b \x01(\x01\x12\x12\n\nstore_code\x18\x0c \x01(\t\x12\x10\n\x08store_id\x18\r \x01(\x04\x12\x12\n\nstore_name\x18\x0e \x01(\t\x12\x0f\n\x07unit_id\x18\x0f \x01(\x04\x12\x11\n\tunit_name\x18\x10 \x01(\t\x12\x13\n\x0breason_type\x18\x11 \x01(\t\x12\x13\n\x0breason_name\x18\x15 \x01(\t\x12\x16\n\x0e\x62om_product_id\x18\x12 \x01(\x04\x12\x18\n\x10\x62om_product_code\x18\x13 \x01(\t\x12\x18\n\x10\x62om_product_name\x18\x14 \x01(\t\x12\x13\n\x0b\x62om_unit_id\x18\x16 \x01(\x04\x12\x15\n\rbom_unit_code\x18\x17 \x01(\t\x12\x15\n\rbom_unit_name\x18\x18 \x01(\t\x12\x1e\n\x16\x62om_accounting_unit_id\x18\x19 \x01(\x04\x12 \n\x18\x62om_accounting_unit_code\x18\x1a \x01(\t\x12 \n\x18\x62om_accounting_unit_name\x18\x1b \x01(\t\x12\x0b\n\x03qty\x18\x1c \x01(\x01\x12\r\n\x05price\x18\x1d \x01(\x01\x12\x16\n\x0e\x61\x63\x63ounting_qty\x18\x1e \x01(\x01\x12\x0c\n\x04\x63ost\x18\x1f \x01(\x01\x12\x15\n\rperiod_symbol\x18  \x01(\t\x12\x13\n\x0bposition_id\x18! \x01(\x04\x12\x15\n\rposition_code\x18\" \x01(\t\x12\x15\n\rposition_name\x18# \x01(\t\x12/\n\x0b\x61\x64just_date\x18$ \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"}\n\x08\x41\x44_total\x12\r\n\x05\x63ount\x18\x01 \x01(\x04\x12\x14\n\x0csum_quantity\x18\x02 \x01(\x01\x12\x1f\n\x17sum_accounting_quantity\x18\x03 \x01(\x01\x12\x0f\n\x07sum_qty\x18\x04 \x01(\x01\x12\x1a\n\x12sum_accounting_qty\x18\x05 \x01(\x01\"v\n\x1aGetAdjustBiCollectResponse\x12\x32\n\x04rows\x18\x01 \x03(\x0b\x32$.manufactory.AdjustBiCollectResponse\x12$\n\x05total\x18\x02 \x01(\x0b\x32\x15.manufactory.AD_total\"(\n\x13\x43\x61ncelAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\"&\n\x14\x43\x61ncelAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\x9a\x03\n\x1fGetAdjustCollectDetailedRequest\x12\x14\n\x0c\x63\x61tegory_ids\x18\x01 \x03(\x04\x12\x14\n\x0cproduct_name\x18\x02 \x01(\t\x12.\n\nstart_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12\x11\n\tstore_ids\x18\x08 \x03(\x04\x12\x16\n\x0e\x62om_product_id\x18\t \x03(\x04\x12\r\n\x05order\x18\n \x01(\t\x12\x0c\n\x04sort\x18\x0b \x01(\t\x12\x0c\n\x04\x63ode\x18\x0c \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0e \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x10 \x01(\t\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x14\n\x0cposition_ids\x18\x12 \x03(\x04\x12\x13\n\x0breason_type\x18\x13 \x01(\t\"\x8d\x07\n\x15\x41\x64justCollectDetailed\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x13\n\x0b\x61\x64just_code\x18\x04 \x01(\t\x12/\n\x0b\x61\x64just_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tadjust_id\x18\x06 \x01(\x04\x12\x15\n\rcategory_code\x18\x07 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x08 \x01(\x04\x12\x15\n\rcategory_name\x18\t \x01(\t\x12\n\n\x02id\x18\n \x01(\x04\x12\x14\n\x0cproduct_code\x18\x0b \x01(\t\x12\x12\n\nproduct_id\x18\x0c \x01(\x04\x12\x14\n\x0cproduct_name\x18\r \x01(\t\x12\x10\n\x08quantity\x18\x0e \x01(\x01\x12\x13\n\x0breason_type\x18\x0f \x01(\t\x12\x13\n\x0breason_name\x18! \x01(\t\x12\x12\n\nstore_code\x18\x10 \x01(\t\x12\x10\n\x08store_id\x18\x11 \x01(\x04\x12\x12\n\nstore_name\x18\x12 \x01(\t\x12\x0f\n\x07unit_id\x18\x13 \x01(\x04\x12\x11\n\tunit_name\x18\x14 \x01(\t\x12\x16\n\x0e\x62om_product_id\x18\x15 \x01(\x04\x12\x18\n\x10\x62om_product_code\x18\x16 \x01(\t\x12\x18\n\x10\x62om_product_name\x18\x17 \x01(\t\x12\x13\n\x0b\x62om_unit_id\x18\x18 \x01(\x04\x12\x15\n\rbom_unit_code\x18\x19 \x01(\t\x12\x15\n\rbom_unit_name\x18\x1a \x01(\t\x12\x1e\n\x16\x62om_accounting_unit_id\x18\x1b \x01(\x04\x12 \n\x18\x62om_accounting_unit_code\x18\x1c \x01(\t\x12 \n\x18\x62om_accounting_unit_name\x18\x1d \x01(\t\x12\x0b\n\x03qty\x18\x1e \x01(\x01\x12\r\n\x05price\x18\x1f \x01(\x01\x12\x16\n\x0e\x61\x63\x63ounting_qty\x18  \x01(\x01\x12\x0c\n\x04\x63ost\x18\" \x01(\x01\x12\x13\n\x0b\x62ranch_type\x18# \x01(\t\x12\x0e\n\x06remark\x18$ \x01(\t\x12\x13\n\x0bposition_id\x18% \x01(\x04\x12\x15\n\rposition_code\x18& \x01(\t\x12\x15\n\rposition_name\x18\' \x01(\t\"z\n GetAdjustCollectDetailedResponse\x12\x30\n\x04rows\x18\x01 \x03(\x0b\x32\".manufactory.AdjustCollectDetailed\x12$\n\x05total\x18\x02 \x01(\x0b\x32\x15.manufactory.AD_total\"0\n\x1e\x41utoCloseCreatedAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"Y\n\x1d\x41utoCloseCreatedAdjustRequest\x12\x13\n\x0b\x61\x64just_date\x18\x01 \x01(\t\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x0f\n\x07user_id\x18\x03 \x01(\x04\"\xfe\x01\n\x1cGetMaterialAdjustDataRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tstore_ids\x18\x03 \x03(\x04\x12\x14\n\x0cmaterial_ids\x18\x04 \x03(\x04\x12\r\n\x05limit\x18\x05 \x01(\x04\x12\x0e\n\x06offset\x18\x06 \x01(\x04\x12\x14\n\x0cis_wms_store\x18\x07 \x01(\x08\x12\x0b\n\x03lan\x18\n \x01(\t\x12\x15\n\rinclude_total\x18\x0b \x01(\x08\"p\n\x1dGetMaterialAdjustDataResponse\x12)\n\x04rows\x18\x01 \x03(\x0b\x32\x1b.manufactory.AdjustResponse\x12$\n\x05total\x18\x02 \x01(\x0b\x32\x15.manufactory.AD_total\"\xb9\x05\n\x0e\x41\x64justResponse\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x05 \x01(\x04\x12\x15\n\rcategory_name\x18\x06 \x01(\t\x12\x14\n\x0cproduct_code\x18\x07 \x01(\t\x12\x12\n\nproduct_id\x18\x08 \x01(\x04\x12\x14\n\x0cproduct_name\x18\t \x01(\t\x12\x10\n\x08quantity\x18\x0b \x01(\x01\x12\x12\n\nstore_code\x18\x0c \x01(\t\x12\x10\n\x08store_id\x18\r \x01(\x04\x12\x12\n\nstore_name\x18\x0e \x01(\t\x12\x0f\n\x07unit_id\x18\x0f \x01(\x04\x12\x11\n\tunit_name\x18\x10 \x01(\t\x12\x13\n\x0breason_type\x18\x11 \x01(\t\x12\x16\n\x0e\x62om_product_id\x18\x12 \x01(\x04\x12\x18\n\x10\x62om_product_code\x18\x13 \x01(\t\x12\x18\n\x10\x62om_product_name\x18\x14 \x01(\t\x12\x0b\n\x03qty\x18\x15 \x01(\x01\x12\x13\n\x0b\x62om_unit_id\x18\x16 \x01(\x04\x12\x15\n\rbom_unit_code\x18\x17 \x01(\t\x12\x15\n\rbom_unit_name\x18\x18 \x01(\t\x12\x1e\n\x16\x62om_accounting_unit_id\x18\x19 \x01(\x04\x12 \n\x18\x62om_accounting_unit_code\x18\x1a \x01(\t\x12 \n\x18\x62om_accounting_unit_name\x18\x1b \x01(\t\x12\x16\n\x0e\x61\x63\x63ounting_qty\x18\x1c \x01(\x01\x12\r\n\x05price\x18\x1d \x01(\x01\x12\x0c\n\x04\x63ost\x18\x1e \x01(\x01\x12\x13\n\x0b\x61\x64just_date\x18\x1f \x01(\t\"\xa3\x01\n\x1a\x43reatedAdjustProductByCode\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x11\n\tunit_code\x18\x03 \x01(\t\x12\x10\n\x08quantity\x18\x04 \x01(\x01\x12\x13\n\x0breason_type\x18\x05 \x01(\t\x12)\n\tskuRemark\x18\x06 \x03(\x0b\x32\x16.manufactory.SkuRemark\"\x90\x01\n\tSkuRemark\x12(\n\x04name\x18\x01 \x01(\x0b\x32\x1a.manufactory.SkuRemark.Tag\x12*\n\x06values\x18\x02 \x01(\x0b\x32\x1a.manufactory.SkuRemark.Tag\x1a-\n\x03Tag\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\"\xc8\x01\n\x1a\x43reatedAdjustByCodeRequest\x12\x14\n\x0c\x61\x64just_store\x18\x01 \x01(\t\x12\x39\n\x08products\x18\x02 \x03(\x0b\x32\'.manufactory.CreatedAdjustProductByCode\x12\x13\n\x0breason_type\x18\x03 \x01(\t\x12\x0e\n\x06remark\x18\x04 \x01(\t\x12\x12\n\nrequest_id\x18\x05 \x01(\t\x12\x13\n\x0b\x61\x64just_date\x18\x06 \x01(\t\x12\x0b\n\x03lan\x18\x07 \x01(\t\"\x98\x01\n\x16\x41\x64justPositionProducts\x12\x13\n\x0bposition_id\x18\x01 \x01(\x04\x12\x15\n\rposition_code\x18\x02 \x01(\t\x12\x15\n\rposition_name\x18\x03 \x01(\t\x12,\n\x08products\x18\x04 \x03(\x0b\x32\x1a.manufactory.AdjustProduct\x12\r\n\x05total\x18\x05 \x01(\x04\x32\xda\x15\n\x11ManufactoryAdjust\x12u\n\tGetAdjust\x12\x1d.manufactory.GetAdjustRequest\x1a\x1e.manufactory.GetAdjustResponse\")\x82\xd3\xe4\x93\x02#\x12!/api/v2/supply/manufactory/adjust\x12\x9e\x01\n\x10GetAdjustProduct\x12$.manufactory.GetAdjustProductRequest\x1a%.manufactory.GetAdjustProductResponse\"=\x82\xd3\xe4\x93\x02\x37\x12\x35/api/v2/supply/manufactory/adjust/{adjust_id}/product\x12~\n\rGetAdjustByID\x12!.manufactory.GetAdjustByIDRequest\x1a\x13.manufactory.Adjust\"5\x82\xd3\xe4\x93\x02/\x12-/api/v2/supply/manufactory/adjust/{adjust_id}\x12\x98\x01\n\rConfirmAdjust\x12!.manufactory.ConfirmAdjustRequest\x1a\".manufactory.ConfirmAdjustResponse\"@\x82\xd3\xe4\x93\x02:\x1a\x35/api/v2/supply/manufactory/adjust/{adjust_id}/confirm:\x01*\x12\x94\x01\n\x0cSubmitAdjust\x12 .manufactory.SubmitAdjustRequest\x1a!.manufactory.SubmitAdjustResponse\"?\x82\xd3\xe4\x93\x02\x39\x1a\x34/api/v2/supply/manufactory/adjust/{adjust_id}/submit:\x01*\x12\x98\x01\n\rApproveAdjust\x12!.manufactory.ApproveAdjustRequest\x1a\".manufactory.ApproveAdjustResponse\"@\x82\xd3\xe4\x93\x02:\x1a\x35/api/v2/supply/manufactory/adjust/{adjust_id}/approve:\x01*\x12\x94\x01\n\x0cRejectAdjust\x12 .manufactory.RejectAdjustRequest\x1a!.manufactory.RejectAdjustResponse\"?\x82\xd3\xe4\x93\x02\x39\x1a\x34/api/v2/supply/manufactory/adjust/{adjust_id}/reject:\x01*\x12\xb4\x01\n\x19GetAdjustProductByStoreID\x12-.manufactory.GetAdjustProductByStoreIDRequest\x1a..manufactory.GetAdjustProductByStoreIDResponse\"8\x82\xd3\xe4\x93\x02\x32\x12\x30/api/v2/supply/manufactory/adjust/store/products\x12|\n\rCreatedAdjust\x12!.manufactory.CreatedAdjustRequest\x1a\x13.manufactory.Adjust\"3\x82\xd3\xe4\x93\x02-\"(/api/v2/supply/manufactory/adjust/create:\x01*\x12\x86\x01\n\x0cUpdateAdjust\x12 .manufactory.UpdateAdjustRequest\x1a\x13.manufactory.Adjust\"?\x82\xd3\xe4\x93\x02\x39\x1a\x34/api/v2/supply/manufactory/adjust/{adjust_id}/update:\x01*\x12\x94\x01\n\x0c\x44\x65leteAdjust\x12 .manufactory.DeleteAdjustRequest\x1a!.manufactory.DeleteAdjustResponse\"?\x82\xd3\xe4\x93\x02\x39\x1a\x34/api/v2/supply/manufactory/adjust/{adjust_id}/delete:\x01*\x12\xb1\x01\n\x13\x44\x65leteAdjustProduct\x12\'.manufactory.DeleteAdjustProductRequest\x1a(.manufactory.DeleteAdjustProductResponse\"G\x82\xd3\xe4\x93\x02\x41\x1a</api/v2/supply/manufactory/adjust/product/{adjust_id}/delete:\x01*\x12\x9b\x01\n\x12GetAdjustBiCollect\x12&.manufactory.GetAdjustBiCollectRequest\x1a\'.manufactory.GetAdjustBiCollectResponse\"4\x82\xd3\xe4\x93\x02.\x12,/api/v2/supply/manufactory/adjust/bi/collect\x12\x94\x01\n\x0c\x43\x61ncelAdjust\x12 .manufactory.CancelAdjustRequest\x1a!.manufactory.CancelAdjustResponse\"?\x82\xd3\xe4\x93\x02\x39\x1a\x34/api/v2/supply/manufactory/adjust/auto_create/cancel:\x01*\x12\xae\x01\n\x18GetAdjustCollectDetailed\x12,.manufactory.GetAdjustCollectDetailedRequest\x1a-.manufactory.GetAdjustCollectDetailedResponse\"5\x82\xd3\xe4\x93\x02/\x12-/api/v2/supply/manufactory/adjust/bi/detailed\x12\xa7\x01\n\x16\x41utoCloseCreatedAdjust\x12*.manufactory.AutoCloseCreatedAdjustRequest\x1a+.manufactory.AutoCloseCreatedAdjustResponse\"4\x82\xd3\xe4\x93\x02.\x12,/api/v2/supply/manufactory/adjust/close/auto\x12\xa7\x01\n\x15GetMaterialAdjustData\x12).manufactory.GetMaterialAdjustDataRequest\x1a*.manufactory.GetMaterialAdjustDataResponse\"7\x82\xd3\xe4\x93\x02\x31\x12//api/v2/supply/manufactory/adjust/material/cost\x12\x85\x01\n\x13\x43reatedAdjustByCode\x12\'.manufactory.CreatedAdjustByCodeRequest\x1a\x13.manufactory.Adjust\"0\x82\xd3\xe4\x93\x02*\"%/api/v2/supply/manufactory/adjust/pos:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])



_GETADJUSTREQUEST_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='manufactory.GetAdjustRequest.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REJECTED', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=5, number=5,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=651,
  serialized_end=739,
)
_sym_db.RegisterEnumDescriptor(_GETADJUSTREQUEST_STATUS)


_CREATEADJUSTREQUEST = _descriptor.Descriptor(
  name='CreateAdjustRequest',
  full_name='manufactory.CreateAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.CreateAdjustRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='manufactory.CreateAdjustRequest.request_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=104,
  serialized_end=195,
)


_CREATEADJUSTRESPONSE = _descriptor.Descriptor(
  name='CreateAdjustResponse',
  full_name='manufactory.CreateAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.CreateAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='manufactory.CreateAdjustResponse.request_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=197,
  serialized_end=285,
)


_GETADJUSTREQUEST = _descriptor.Descriptor(
  name='GetAdjustRequest',
  full_name='manufactory.GetAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='include_total', full_name='manufactory.GetAdjustRequest.include_total', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='manufactory.GetAdjustRequest.start_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='manufactory.GetAdjustRequest.end_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='manufactory.GetAdjustRequest.reason_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branches', full_name='manufactory.GetAdjustRequest.branches', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='manufactory.GetAdjustRequest.product_ids', index=5,
      number=6, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.GetAdjustRequest.offset', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.GetAdjustRequest.limit', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.GetAdjustRequest.status', index=8,
      number=9, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.GetAdjustRequest.code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='manufactory.GetAdjustRequest.order', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='manufactory.GetAdjustRequest.sort', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.GetAdjustRequest.branch_type', index=12,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sources', full_name='manufactory.GetAdjustRequest.sources', index=13,
      number=31, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _GETADJUSTREQUEST_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=288,
  serialized_end=739,
)


_ADJUST = _descriptor.Descriptor(
  name='Adjust',
  full_name='manufactory.Adjust',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.Adjust.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_order_number', full_name='manufactory.Adjust.adjust_order_number', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_store', full_name='manufactory.Adjust.adjust_store', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_store_secondary_id', full_name='manufactory.Adjust.adjust_store_secondary_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.Adjust.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.Adjust.partner_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='manufactory.Adjust.process_status', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='manufactory.Adjust.reason_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.Adjust.remark', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.Adjust.status', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='manufactory.Adjust.adjust_date', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.Adjust.created_at', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.Adjust.updated_at', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.Adjust.created_by', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.Adjust.updated_by', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='manufactory.Adjust.user_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_batch_id', full_name='manufactory.Adjust.branch_batch_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='manufactory.Adjust.schedule_code', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_id', full_name='manufactory.Adjust.schedule_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.Adjust.request_id', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.Adjust.created_name', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.Adjust.updated_name', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_id', full_name='manufactory.Adjust.receive_id', index=22,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_code', full_name='manufactory.Adjust.receive_code', index=23,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='manufactory.Adjust.schedule_name', index=24,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.Adjust.branch_type', index=25,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='manufactory.Adjust.attachments', index=26,
      number=31, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source', full_name='manufactory.Adjust.source', index=27,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_name', full_name='manufactory.Adjust.reason_name', index=28,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='manufactory.Adjust.reject_reason', index=29,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='manufactory.Adjust.total_amount', index=30,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_sales_amount', full_name='manufactory.Adjust.total_sales_amount', index=31,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=742,
  serialized_end=1537,
)


_GETADJUSTRESPONSE = _descriptor.Descriptor(
  name='GetAdjustResponse',
  full_name='manufactory.GetAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.GetAdjustResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.GetAdjustResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1539,
  serialized_end=1608,
)


_GETADJUSTPRODUCTREQUEST = _descriptor.Descriptor(
  name='GetAdjustProductRequest',
  full_name='manufactory.GetAdjustProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='manufactory.GetAdjustProductRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='manufactory.GetAdjustProductRequest.include_total', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='manufactory.GetAdjustProductRequest.order', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.GetAdjustProductRequest.offset', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.GetAdjustProductRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.GetAdjustProductRequest.lan', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1610,
  serialized_end=1736,
)


_ADJUSTPRODUCT = _descriptor.Descriptor(
  name='AdjustProduct',
  full_name='manufactory.AdjustProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.AdjustProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='manufactory.AdjustProduct.accounting_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='manufactory.AdjustProduct.accounting_unit_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.AdjustProduct.accounting_unit_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='manufactory.AdjustProduct.accounting_unit_spec', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='manufactory.AdjustProduct.adjust_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_store', full_name='manufactory.AdjustProduct.adjust_store', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='manufactory.AdjustProduct.confirmed_quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.AdjustProduct.created_by', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_confirmed', full_name='manufactory.AdjustProduct.is_confirmed', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_number', full_name='manufactory.AdjustProduct.item_number', index=10,
      number=11, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='manufactory.AdjustProduct.material_number', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.AdjustProduct.partner_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.AdjustProduct.product_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.AdjustProduct.product_id', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.AdjustProduct.product_name', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.AdjustProduct.quantity', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='manufactory.AdjustProduct.spec', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_quantity', full_name='manufactory.AdjustProduct.stocktake_quantity', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_unit_id', full_name='manufactory.AdjustProduct.stocktake_unit_id', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.AdjustProduct.unit_id', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.AdjustProduct.unit_name', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='manufactory.AdjustProduct.unit_spec', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.AdjustProduct.updated_by', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='manufactory.AdjustProduct.adjust_date', index=24,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.AdjustProduct.updated_at', index=25,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.AdjustProduct.created_at', index=26,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='manufactory.AdjustProduct.user_id', index=27,
      number=28, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.AdjustProduct.created_name', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.AdjustProduct.updated_name', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='manufactory.AdjustProduct.reason_type', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_accounting_quantity', full_name='manufactory.AdjustProduct.convert_accounting_quantity', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_bom', full_name='manufactory.AdjustProduct.is_bom', index=32,
      number=33, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.AdjustProduct.position_id', index=33,
      number=34, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sku_remark', full_name='manufactory.AdjustProduct.sku_remark', index=34,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='manufactory.AdjustProduct.units', index=35,
      number=36, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_name', full_name='manufactory.AdjustProduct.model_name', index=36,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='manufactory.AdjustProduct.tax_rate', index=37,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='manufactory.AdjustProduct.tax_price', index=38,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='manufactory.AdjustProduct.amount', index=39,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_amount', full_name='manufactory.AdjustProduct.tax_amount', index=40,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='manufactory.AdjustProduct.cost_price', index=41,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='manufactory.AdjustProduct.sales_amount', index=42,
      number=43, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='manufactory.AdjustProduct.sales_price', index=43,
      number=44, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1739,
  serialized_end=2816,
)


_GETADJUSTPRODUCTRESPONSE = _descriptor.Descriptor(
  name='GetAdjustProductResponse',
  full_name='manufactory.GetAdjustProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.GetAdjustProductResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.GetAdjustProductResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_rows', full_name='manufactory.GetAdjustProductResponse.position_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2819,
  serialized_end=2962,
)


_GETADJUSTBYIDREQUEST = _descriptor.Descriptor(
  name='GetAdjustByIDRequest',
  full_name='manufactory.GetAdjustByIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='manufactory.GetAdjustByIDRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.GetAdjustByIDRequest.branch_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2964,
  serialized_end=3026,
)


_CONFIRMADJUSTREQUEST = _descriptor.Descriptor(
  name='ConfirmAdjustRequest',
  full_name='manufactory.ConfirmAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='manufactory.ConfirmAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.ConfirmAdjustRequest.branch_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3028,
  serialized_end=3090,
)


_CONFIRMADJUSTRESPONSE = _descriptor.Descriptor(
  name='ConfirmAdjustResponse',
  full_name='manufactory.ConfirmAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.ConfirmAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3092,
  serialized_end=3131,
)


_SUBMITADJUSTREQUEST = _descriptor.Descriptor(
  name='SubmitAdjustRequest',
  full_name='manufactory.SubmitAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='manufactory.SubmitAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3133,
  serialized_end=3173,
)


_SUBMITADJUSTRESPONSE = _descriptor.Descriptor(
  name='SubmitAdjustResponse',
  full_name='manufactory.SubmitAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.SubmitAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3175,
  serialized_end=3213,
)


_APPROVEADJUSTREQUEST = _descriptor.Descriptor(
  name='ApproveAdjustRequest',
  full_name='manufactory.ApproveAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='manufactory.ApproveAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3215,
  serialized_end=3256,
)


_APPROVEADJUSTRESPONSE = _descriptor.Descriptor(
  name='ApproveAdjustResponse',
  full_name='manufactory.ApproveAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.ApproveAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3258,
  serialized_end=3297,
)


_REJECTADJUSTREQUEST = _descriptor.Descriptor(
  name='RejectAdjustRequest',
  full_name='manufactory.RejectAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='manufactory.RejectAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='manufactory.RejectAdjustRequest.reject_reason', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3299,
  serialized_end=3362,
)


_REJECTADJUSTRESPONSE = _descriptor.Descriptor(
  name='RejectAdjustResponse',
  full_name='manufactory.RejectAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.RejectAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3364,
  serialized_end=3402,
)


_GETADJUSTPRODUCTBYSTOREIDREQUEST = _descriptor.Descriptor(
  name='GetAdjustProductByStoreIDRequest',
  full_name='manufactory.GetAdjustProductByStoreIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='manufactory.GetAdjustProductByStoreIDRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.GetAdjustProductByStoreIDRequest.limit', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.GetAdjustProductByStoreIDRequest.offset', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='manufactory.GetAdjustProductByStoreIDRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='manufactory.GetAdjustProductByStoreIDRequest.adjust_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='manufactory.GetAdjustProductByStoreIDRequest.search', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='manufactory.GetAdjustProductByStoreIDRequest.search_fields', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='manufactory.GetAdjustProductByStoreIDRequest.category_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.GetAdjustProductByStoreIDRequest.lan', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by', full_name='manufactory.GetAdjustProductByStoreIDRequest.order_by', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='manufactory.GetAdjustProductByStoreIDRequest.sort', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3405,
  serialized_end=3666,
)


_CREATEADJUSTPRODUCTUINT = _descriptor.Descriptor(
  name='CreateAdjustProductUint',
  full_name='manufactory.CreateAdjustProductUint',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.CreateAdjustProductUint.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.CreateAdjustProductUint.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='manufactory.CreateAdjustProductUint.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='manufactory.CreateAdjustProductUint.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.CreateAdjustProductUint.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='manufactory.CreateAdjustProductUint.updated', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='manufactory.CreateAdjustProductUint.rate', index=6,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default', full_name='manufactory.CreateAdjustProductUint.default', index=7,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='manufactory.CreateAdjustProductUint.order', index=8,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase', full_name='manufactory.CreateAdjustProductUint.purchase', index=9,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales', full_name='manufactory.CreateAdjustProductUint.sales', index=10,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='manufactory.CreateAdjustProductUint.stocktake', index=11,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom', full_name='manufactory.CreateAdjustProductUint.bom', index=12,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default_stocktake', full_name='manufactory.CreateAdjustProductUint.default_stocktake', index=13,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer', full_name='manufactory.CreateAdjustProductUint.transfer', index=14,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='manufactory.CreateAdjustProductUint.tax_rate', index=15,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='manufactory.CreateAdjustProductUint.tax_price', index=16,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='manufactory.CreateAdjustProductUint.cost_price', index=17,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='manufactory.CreateAdjustProductUint.sales_price', index=18,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3669,
  serialized_end=4023,
)


_CREATEADJUSTPRODUCT = _descriptor.Descriptor(
  name='CreateAdjustProduct',
  full_name='manufactory.CreateAdjustProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.CreateAdjustProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='loss_report_order', full_name='manufactory.CreateAdjustProduct.loss_report_order', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.CreateAdjustProduct.product_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.CreateAdjustProduct.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_name', full_name='manufactory.CreateAdjustProduct.model_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='manufactory.CreateAdjustProduct.storage_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_category_id', full_name='manufactory.CreateAdjustProduct.product_category_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='manufactory.CreateAdjustProduct.units', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='barcode', full_name='manufactory.CreateAdjustProduct.barcode', index=8,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='real_inventory_qty', full_name='manufactory.CreateAdjustProduct.real_inventory_qty', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4026,
  serialized_end=4307,
)


_GETADJUSTPRODUCTBYSTOREIDRESPONSE = _descriptor.Descriptor(
  name='GetAdjustProductByStoreIDResponse',
  full_name='manufactory.GetAdjustProductByStoreIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.GetAdjustProductByStoreIDResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.GetAdjustProductByStoreIDResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_unchanged_rows', full_name='manufactory.GetAdjustProductByStoreIDResponse.inventory_unchanged_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4310,
  serialized_end=4476,
)


_CREATEDADJUSTPRODUCT = _descriptor.Descriptor(
  name='CreatedAdjustProduct',
  full_name='manufactory.CreatedAdjustProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.CreatedAdjustProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.CreatedAdjustProduct.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.CreatedAdjustProduct.unit_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.CreatedAdjustProduct.quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='manufactory.CreatedAdjustProduct.reason_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.CreatedAdjustProduct.position_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='skuRemark', full_name='manufactory.CreatedAdjustProduct.skuRemark', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='manufactory.CreatedAdjustProduct.tax_rate', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='manufactory.CreatedAdjustProduct.tax_price', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='manufactory.CreatedAdjustProduct.amount', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_amount', full_name='manufactory.CreatedAdjustProduct.tax_amount', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='manufactory.CreatedAdjustProduct.cost_price', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='manufactory.CreatedAdjustProduct.sales_price', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='manufactory.CreatedAdjustProduct.sales_amount', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4479,
  serialized_end=4789,
)


_CREATEDADJUSTREQUEST = _descriptor.Descriptor(
  name='CreatedAdjustRequest',
  full_name='manufactory.CreatedAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_store', full_name='manufactory.CreatedAdjustRequest.adjust_store', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='manufactory.CreatedAdjustRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='manufactory.CreatedAdjustRequest.reason_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.CreatedAdjustRequest.remark', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.CreatedAdjustRequest.request_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='manufactory.CreatedAdjustRequest.adjust_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.CreatedAdjustRequest.branch_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.CreatedAdjustRequest.position_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source', full_name='manufactory.CreatedAdjustRequest.source', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='manufactory.CreatedAdjustRequest.attachments', index=9,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4792,
  serialized_end=5100,
)


_ATTACHMENTS = _descriptor.Descriptor(
  name='Attachments',
  full_name='manufactory.Attachments',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.Attachments.type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='manufactory.Attachments.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5102,
  serialized_end=5142,
)


_UPDATEADJUSTREQUEST = _descriptor.Descriptor(
  name='UpdateAdjustRequest',
  full_name='manufactory.UpdateAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='manufactory.UpdateAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='manufactory.UpdateAdjustRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.UpdateAdjustRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='manufactory.UpdateAdjustRequest.reason_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='manufactory.UpdateAdjustRequest.adjust_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.UpdateAdjustRequest.branch_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='manufactory.UpdateAdjustRequest.attachments', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.UpdateAdjustRequest.position_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5145,
  serialized_end=5413,
)


_DELETEADJUSTREQUEST = _descriptor.Descriptor(
  name='DeleteAdjustRequest',
  full_name='manufactory.DeleteAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='manufactory.DeleteAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5415,
  serialized_end=5455,
)


_DELETEADJUSTRESPONSE = _descriptor.Descriptor(
  name='DeleteAdjustResponse',
  full_name='manufactory.DeleteAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.DeleteAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5457,
  serialized_end=5495,
)


_DELETEADJUSTPRODUCTREQUEST = _descriptor.Descriptor(
  name='DeleteAdjustProductRequest',
  full_name='manufactory.DeleteAdjustProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='manufactory.DeleteAdjustProductRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='manufactory.DeleteAdjustProductRequest.ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5497,
  serialized_end=5557,
)


_DELETEADJUSTPRODUCTRESPONSE = _descriptor.Descriptor(
  name='DeleteAdjustProductResponse',
  full_name='manufactory.DeleteAdjustProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.DeleteAdjustProductResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5559,
  serialized_end=5604,
)


_GETADJUSTBICOLLECTREQUEST = _descriptor.Descriptor(
  name='GetAdjustBiCollectRequest',
  full_name='manufactory.GetAdjustBiCollectRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='manufactory.GetAdjustBiCollectRequest.category_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.GetAdjustBiCollectRequest.product_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='manufactory.GetAdjustBiCollectRequest.start_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='manufactory.GetAdjustBiCollectRequest.end_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.GetAdjustBiCollectRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.GetAdjustBiCollectRequest.offset', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='manufactory.GetAdjustBiCollectRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='manufactory.GetAdjustBiCollectRequest.store_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='manufactory.GetAdjustBiCollectRequest.bom_product_id', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='manufactory.GetAdjustBiCollectRequest.period_symbol', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='manufactory.GetAdjustBiCollectRequest.order', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='manufactory.GetAdjustBiCollectRequest.sort', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.GetAdjustBiCollectRequest.code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='manufactory.GetAdjustBiCollectRequest.reason_type', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='manufactory.GetAdjustBiCollectRequest.is_wms_store', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.GetAdjustBiCollectRequest.branch_type', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.GetAdjustBiCollectRequest.lan', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hour_offset', full_name='manufactory.GetAdjustBiCollectRequest.hour_offset', index=17,
      number=18, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='manufactory.GetAdjustBiCollectRequest.position_ids', index=18,
      number=19, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5607,
  serialized_end=6055,
)


_ADJUSTBICOLLECTRESPONSE = _descriptor.Descriptor(
  name='AdjustBiCollectResponse',
  full_name='manufactory.AdjustBiCollectResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='manufactory.AdjustBiCollectResponse.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='manufactory.AdjustBiCollectResponse.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.AdjustBiCollectResponse.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='manufactory.AdjustBiCollectResponse.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='manufactory.AdjustBiCollectResponse.category_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='manufactory.AdjustBiCollectResponse.category_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.AdjustBiCollectResponse.product_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.AdjustBiCollectResponse.product_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.AdjustBiCollectResponse.product_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.AdjustBiCollectResponse.quantity', index=9,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='manufactory.AdjustBiCollectResponse.store_code', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='manufactory.AdjustBiCollectResponse.store_id', index=11,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='manufactory.AdjustBiCollectResponse.store_name', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.AdjustBiCollectResponse.unit_id', index=13,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.AdjustBiCollectResponse.unit_name', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='manufactory.AdjustBiCollectResponse.reason_type', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_name', full_name='manufactory.AdjustBiCollectResponse.reason_name', index=16,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='manufactory.AdjustBiCollectResponse.bom_product_id', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_code', full_name='manufactory.AdjustBiCollectResponse.bom_product_code', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_name', full_name='manufactory.AdjustBiCollectResponse.bom_product_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_id', full_name='manufactory.AdjustBiCollectResponse.bom_unit_id', index=20,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_code', full_name='manufactory.AdjustBiCollectResponse.bom_unit_code', index=21,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_name', full_name='manufactory.AdjustBiCollectResponse.bom_unit_name', index=22,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_id', full_name='manufactory.AdjustBiCollectResponse.bom_accounting_unit_id', index=23,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_code', full_name='manufactory.AdjustBiCollectResponse.bom_accounting_unit_code', index=24,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_name', full_name='manufactory.AdjustBiCollectResponse.bom_accounting_unit_name', index=25,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='manufactory.AdjustBiCollectResponse.qty', index=26,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='manufactory.AdjustBiCollectResponse.price', index=27,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_qty', full_name='manufactory.AdjustBiCollectResponse.accounting_qty', index=28,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost', full_name='manufactory.AdjustBiCollectResponse.cost', index=29,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='manufactory.AdjustBiCollectResponse.period_symbol', index=30,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.AdjustBiCollectResponse.position_id', index=31,
      number=33, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.AdjustBiCollectResponse.position_code', index=32,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.AdjustBiCollectResponse.position_name', index=33,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='manufactory.AdjustBiCollectResponse.adjust_date', index=34,
      number=36, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6058,
  serialized_end=6903,
)


_AD_TOTAL = _descriptor.Descriptor(
  name='AD_total',
  full_name='manufactory.AD_total',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='count', full_name='manufactory.AD_total.count', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_quantity', full_name='manufactory.AD_total.sum_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_accounting_quantity', full_name='manufactory.AD_total.sum_accounting_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_qty', full_name='manufactory.AD_total.sum_qty', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_accounting_qty', full_name='manufactory.AD_total.sum_accounting_qty', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6905,
  serialized_end=7030,
)


_GETADJUSTBICOLLECTRESPONSE = _descriptor.Descriptor(
  name='GetAdjustBiCollectResponse',
  full_name='manufactory.GetAdjustBiCollectResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.GetAdjustBiCollectResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.GetAdjustBiCollectResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7032,
  serialized_end=7150,
)


_CANCELADJUSTREQUEST = _descriptor.Descriptor(
  name='CancelAdjustRequest',
  full_name='manufactory.CancelAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='manufactory.CancelAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7152,
  serialized_end=7192,
)


_CANCELADJUSTRESPONSE = _descriptor.Descriptor(
  name='CancelAdjustResponse',
  full_name='manufactory.CancelAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.CancelAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7194,
  serialized_end=7232,
)


_GETADJUSTCOLLECTDETAILEDREQUEST = _descriptor.Descriptor(
  name='GetAdjustCollectDetailedRequest',
  full_name='manufactory.GetAdjustCollectDetailedRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='manufactory.GetAdjustCollectDetailedRequest.category_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.GetAdjustCollectDetailedRequest.product_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='manufactory.GetAdjustCollectDetailedRequest.start_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='manufactory.GetAdjustCollectDetailedRequest.end_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.GetAdjustCollectDetailedRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.GetAdjustCollectDetailedRequest.offset', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='manufactory.GetAdjustCollectDetailedRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='manufactory.GetAdjustCollectDetailedRequest.store_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='manufactory.GetAdjustCollectDetailedRequest.bom_product_id', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='manufactory.GetAdjustCollectDetailedRequest.order', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='manufactory.GetAdjustCollectDetailedRequest.sort', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.GetAdjustCollectDetailedRequest.code', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='manufactory.GetAdjustCollectDetailedRequest.is_wms_store', index=12,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.GetAdjustCollectDetailedRequest.branch_type', index=13,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.GetAdjustCollectDetailedRequest.lan', index=14,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='manufactory.GetAdjustCollectDetailedRequest.position_ids', index=15,
      number=18, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='manufactory.GetAdjustCollectDetailedRequest.reason_type', index=16,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7235,
  serialized_end=7645,
)


_ADJUSTCOLLECTDETAILED = _descriptor.Descriptor(
  name='AdjustCollectDetailed',
  full_name='manufactory.AdjustCollectDetailed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='manufactory.AdjustCollectDetailed.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='manufactory.AdjustCollectDetailed.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.AdjustCollectDetailed.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_code', full_name='manufactory.AdjustCollectDetailed.adjust_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='manufactory.AdjustCollectDetailed.adjust_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='manufactory.AdjustCollectDetailed.adjust_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='manufactory.AdjustCollectDetailed.category_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='manufactory.AdjustCollectDetailed.category_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='manufactory.AdjustCollectDetailed.category_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.AdjustCollectDetailed.id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.AdjustCollectDetailed.product_code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.AdjustCollectDetailed.product_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.AdjustCollectDetailed.product_name', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.AdjustCollectDetailed.quantity', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='manufactory.AdjustCollectDetailed.reason_type', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_name', full_name='manufactory.AdjustCollectDetailed.reason_name', index=15,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='manufactory.AdjustCollectDetailed.store_code', index=16,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='manufactory.AdjustCollectDetailed.store_id', index=17,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='manufactory.AdjustCollectDetailed.store_name', index=18,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.AdjustCollectDetailed.unit_id', index=19,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.AdjustCollectDetailed.unit_name', index=20,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='manufactory.AdjustCollectDetailed.bom_product_id', index=21,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_code', full_name='manufactory.AdjustCollectDetailed.bom_product_code', index=22,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_name', full_name='manufactory.AdjustCollectDetailed.bom_product_name', index=23,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_id', full_name='manufactory.AdjustCollectDetailed.bom_unit_id', index=24,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_code', full_name='manufactory.AdjustCollectDetailed.bom_unit_code', index=25,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_name', full_name='manufactory.AdjustCollectDetailed.bom_unit_name', index=26,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_id', full_name='manufactory.AdjustCollectDetailed.bom_accounting_unit_id', index=27,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_code', full_name='manufactory.AdjustCollectDetailed.bom_accounting_unit_code', index=28,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_name', full_name='manufactory.AdjustCollectDetailed.bom_accounting_unit_name', index=29,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='manufactory.AdjustCollectDetailed.qty', index=30,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='manufactory.AdjustCollectDetailed.price', index=31,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_qty', full_name='manufactory.AdjustCollectDetailed.accounting_qty', index=32,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost', full_name='manufactory.AdjustCollectDetailed.cost', index=33,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.AdjustCollectDetailed.branch_type', index=34,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.AdjustCollectDetailed.remark', index=35,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.AdjustCollectDetailed.position_id', index=36,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.AdjustCollectDetailed.position_code', index=37,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.AdjustCollectDetailed.position_name', index=38,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7648,
  serialized_end=8557,
)


_GETADJUSTCOLLECTDETAILEDRESPONSE = _descriptor.Descriptor(
  name='GetAdjustCollectDetailedResponse',
  full_name='manufactory.GetAdjustCollectDetailedResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.GetAdjustCollectDetailedResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.GetAdjustCollectDetailedResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8559,
  serialized_end=8681,
)


_AUTOCLOSECREATEDADJUSTRESPONSE = _descriptor.Descriptor(
  name='AutoCloseCreatedAdjustResponse',
  full_name='manufactory.AutoCloseCreatedAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='manufactory.AutoCloseCreatedAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8683,
  serialized_end=8731,
)


_AUTOCLOSECREATEDADJUSTREQUEST = _descriptor.Descriptor(
  name='AutoCloseCreatedAdjustRequest',
  full_name='manufactory.AutoCloseCreatedAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='manufactory.AutoCloseCreatedAdjustRequest.adjust_date', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.AutoCloseCreatedAdjustRequest.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='manufactory.AutoCloseCreatedAdjustRequest.user_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8733,
  serialized_end=8822,
)


_GETMATERIALADJUSTDATAREQUEST = _descriptor.Descriptor(
  name='GetMaterialAdjustDataRequest',
  full_name='manufactory.GetMaterialAdjustDataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='manufactory.GetMaterialAdjustDataRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='manufactory.GetMaterialAdjustDataRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='manufactory.GetMaterialAdjustDataRequest.store_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_ids', full_name='manufactory.GetMaterialAdjustDataRequest.material_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.GetMaterialAdjustDataRequest.limit', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.GetMaterialAdjustDataRequest.offset', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='manufactory.GetMaterialAdjustDataRequest.is_wms_store', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.GetMaterialAdjustDataRequest.lan', index=7,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='manufactory.GetMaterialAdjustDataRequest.include_total', index=8,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8825,
  serialized_end=9079,
)


_GETMATERIALADJUSTDATARESPONSE = _descriptor.Descriptor(
  name='GetMaterialAdjustDataResponse',
  full_name='manufactory.GetMaterialAdjustDataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.GetMaterialAdjustDataResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.GetMaterialAdjustDataResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9081,
  serialized_end=9193,
)


_ADJUSTRESPONSE = _descriptor.Descriptor(
  name='AdjustResponse',
  full_name='manufactory.AdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='manufactory.AdjustResponse.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='manufactory.AdjustResponse.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.AdjustResponse.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='manufactory.AdjustResponse.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='manufactory.AdjustResponse.category_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='manufactory.AdjustResponse.category_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.AdjustResponse.product_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.AdjustResponse.product_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.AdjustResponse.product_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.AdjustResponse.quantity', index=9,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='manufactory.AdjustResponse.store_code', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='manufactory.AdjustResponse.store_id', index=11,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='manufactory.AdjustResponse.store_name', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.AdjustResponse.unit_id', index=13,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.AdjustResponse.unit_name', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='manufactory.AdjustResponse.reason_type', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='manufactory.AdjustResponse.bom_product_id', index=16,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_code', full_name='manufactory.AdjustResponse.bom_product_code', index=17,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_name', full_name='manufactory.AdjustResponse.bom_product_name', index=18,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='manufactory.AdjustResponse.qty', index=19,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_id', full_name='manufactory.AdjustResponse.bom_unit_id', index=20,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_code', full_name='manufactory.AdjustResponse.bom_unit_code', index=21,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_name', full_name='manufactory.AdjustResponse.bom_unit_name', index=22,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_id', full_name='manufactory.AdjustResponse.bom_accounting_unit_id', index=23,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_code', full_name='manufactory.AdjustResponse.bom_accounting_unit_code', index=24,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_name', full_name='manufactory.AdjustResponse.bom_accounting_unit_name', index=25,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_qty', full_name='manufactory.AdjustResponse.accounting_qty', index=26,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='manufactory.AdjustResponse.price', index=27,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost', full_name='manufactory.AdjustResponse.cost', index=28,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='manufactory.AdjustResponse.adjust_date', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9196,
  serialized_end=9893,
)


_CREATEDADJUSTPRODUCTBYCODE = _descriptor.Descriptor(
  name='CreatedAdjustProductByCode',
  full_name='manufactory.CreatedAdjustProductByCode',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.CreatedAdjustProductByCode.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.CreatedAdjustProductByCode.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_code', full_name='manufactory.CreatedAdjustProductByCode.unit_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.CreatedAdjustProductByCode.quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='manufactory.CreatedAdjustProductByCode.reason_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='skuRemark', full_name='manufactory.CreatedAdjustProductByCode.skuRemark', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9896,
  serialized_end=10059,
)


_SKUREMARK_TAG = _descriptor.Descriptor(
  name='Tag',
  full_name='manufactory.SkuRemark.Tag',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.SkuRemark.Tag.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.SkuRemark.Tag.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='manufactory.SkuRemark.Tag.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10161,
  serialized_end=10206,
)

_SKUREMARK = _descriptor.Descriptor(
  name='SkuRemark',
  full_name='manufactory.SkuRemark',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='manufactory.SkuRemark.name', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='values', full_name='manufactory.SkuRemark.values', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_SKUREMARK_TAG, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10062,
  serialized_end=10206,
)


_CREATEDADJUSTBYCODEREQUEST = _descriptor.Descriptor(
  name='CreatedAdjustByCodeRequest',
  full_name='manufactory.CreatedAdjustByCodeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_store', full_name='manufactory.CreatedAdjustByCodeRequest.adjust_store', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='manufactory.CreatedAdjustByCodeRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='manufactory.CreatedAdjustByCodeRequest.reason_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.CreatedAdjustByCodeRequest.remark', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.CreatedAdjustByCodeRequest.request_id', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='manufactory.CreatedAdjustByCodeRequest.adjust_date', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.CreatedAdjustByCodeRequest.lan', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10209,
  serialized_end=10409,
)


_ADJUSTPOSITIONPRODUCTS = _descriptor.Descriptor(
  name='AdjustPositionProducts',
  full_name='manufactory.AdjustPositionProducts',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='position_id', full_name='manufactory.AdjustPositionProducts.position_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='manufactory.AdjustPositionProducts.position_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='manufactory.AdjustPositionProducts.position_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='manufactory.AdjustPositionProducts.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.AdjustPositionProducts.total', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10412,
  serialized_end=10564,
)

_CREATEADJUSTREQUEST.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEADJUSTRESPONSE.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTREQUEST.fields_by_name['status'].enum_type = _GETADJUSTREQUEST_STATUS
_GETADJUSTREQUEST_STATUS.containing_type = _GETADJUSTREQUEST
_ADJUST.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUST.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUST.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_GETADJUSTRESPONSE.fields_by_name['rows'].message_type = _ADJUST
_ADJUSTPRODUCT.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUSTPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUSTPRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUSTPRODUCT.fields_by_name['units'].message_type = _CREATEADJUSTPRODUCTUINT
_GETADJUSTPRODUCTRESPONSE.fields_by_name['rows'].message_type = _ADJUSTPRODUCT
_GETADJUSTPRODUCTRESPONSE.fields_by_name['position_rows'].message_type = _ADJUSTPOSITIONPRODUCTS
_GETADJUSTPRODUCTBYSTOREIDREQUEST.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEADJUSTPRODUCT.fields_by_name['units'].message_type = _CREATEADJUSTPRODUCTUINT
_GETADJUSTPRODUCTBYSTOREIDRESPONSE.fields_by_name['rows'].message_type = _CREATEADJUSTPRODUCT
_GETADJUSTPRODUCTBYSTOREIDRESPONSE.fields_by_name['inventory_unchanged_rows'].message_type = _CREATEADJUSTPRODUCT
_CREATEDADJUSTPRODUCT.fields_by_name['skuRemark'].message_type = _SKUREMARK
_CREATEDADJUSTREQUEST.fields_by_name['products'].message_type = _CREATEDADJUSTPRODUCT
_CREATEDADJUSTREQUEST.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEDADJUSTREQUEST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_UPDATEADJUSTREQUEST.fields_by_name['products'].message_type = _CREATEDADJUSTPRODUCT
_UPDATEADJUSTREQUEST.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATEADJUSTREQUEST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_GETADJUSTBICOLLECTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTBICOLLECTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUSTBICOLLECTRESPONSE.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTBICOLLECTRESPONSE.fields_by_name['rows'].message_type = _ADJUSTBICOLLECTRESPONSE
_GETADJUSTBICOLLECTRESPONSE.fields_by_name['total'].message_type = _AD_TOTAL
_GETADJUSTCOLLECTDETAILEDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTCOLLECTDETAILEDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUSTCOLLECTDETAILED.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTCOLLECTDETAILEDRESPONSE.fields_by_name['rows'].message_type = _ADJUSTCOLLECTDETAILED
_GETADJUSTCOLLECTDETAILEDRESPONSE.fields_by_name['total'].message_type = _AD_TOTAL
_GETMATERIALADJUSTDATAREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETMATERIALADJUSTDATAREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETMATERIALADJUSTDATARESPONSE.fields_by_name['rows'].message_type = _ADJUSTRESPONSE
_GETMATERIALADJUSTDATARESPONSE.fields_by_name['total'].message_type = _AD_TOTAL
_CREATEDADJUSTPRODUCTBYCODE.fields_by_name['skuRemark'].message_type = _SKUREMARK
_SKUREMARK_TAG.containing_type = _SKUREMARK
_SKUREMARK.fields_by_name['name'].message_type = _SKUREMARK_TAG
_SKUREMARK.fields_by_name['values'].message_type = _SKUREMARK_TAG
_CREATEDADJUSTBYCODEREQUEST.fields_by_name['products'].message_type = _CREATEDADJUSTPRODUCTBYCODE
_ADJUSTPOSITIONPRODUCTS.fields_by_name['products'].message_type = _ADJUSTPRODUCT
DESCRIPTOR.message_types_by_name['CreateAdjustRequest'] = _CREATEADJUSTREQUEST
DESCRIPTOR.message_types_by_name['CreateAdjustResponse'] = _CREATEADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustRequest'] = _GETADJUSTREQUEST
DESCRIPTOR.message_types_by_name['Adjust'] = _ADJUST
DESCRIPTOR.message_types_by_name['GetAdjustResponse'] = _GETADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustProductRequest'] = _GETADJUSTPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['AdjustProduct'] = _ADJUSTPRODUCT
DESCRIPTOR.message_types_by_name['GetAdjustProductResponse'] = _GETADJUSTPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustByIDRequest'] = _GETADJUSTBYIDREQUEST
DESCRIPTOR.message_types_by_name['ConfirmAdjustRequest'] = _CONFIRMADJUSTREQUEST
DESCRIPTOR.message_types_by_name['ConfirmAdjustResponse'] = _CONFIRMADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['SubmitAdjustRequest'] = _SUBMITADJUSTREQUEST
DESCRIPTOR.message_types_by_name['SubmitAdjustResponse'] = _SUBMITADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['ApproveAdjustRequest'] = _APPROVEADJUSTREQUEST
DESCRIPTOR.message_types_by_name['ApproveAdjustResponse'] = _APPROVEADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['RejectAdjustRequest'] = _REJECTADJUSTREQUEST
DESCRIPTOR.message_types_by_name['RejectAdjustResponse'] = _REJECTADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustProductByStoreIDRequest'] = _GETADJUSTPRODUCTBYSTOREIDREQUEST
DESCRIPTOR.message_types_by_name['CreateAdjustProductUint'] = _CREATEADJUSTPRODUCTUINT
DESCRIPTOR.message_types_by_name['CreateAdjustProduct'] = _CREATEADJUSTPRODUCT
DESCRIPTOR.message_types_by_name['GetAdjustProductByStoreIDResponse'] = _GETADJUSTPRODUCTBYSTOREIDRESPONSE
DESCRIPTOR.message_types_by_name['CreatedAdjustProduct'] = _CREATEDADJUSTPRODUCT
DESCRIPTOR.message_types_by_name['CreatedAdjustRequest'] = _CREATEDADJUSTREQUEST
DESCRIPTOR.message_types_by_name['Attachments'] = _ATTACHMENTS
DESCRIPTOR.message_types_by_name['UpdateAdjustRequest'] = _UPDATEADJUSTREQUEST
DESCRIPTOR.message_types_by_name['DeleteAdjustRequest'] = _DELETEADJUSTREQUEST
DESCRIPTOR.message_types_by_name['DeleteAdjustResponse'] = _DELETEADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['DeleteAdjustProductRequest'] = _DELETEADJUSTPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['DeleteAdjustProductResponse'] = _DELETEADJUSTPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustBiCollectRequest'] = _GETADJUSTBICOLLECTREQUEST
DESCRIPTOR.message_types_by_name['AdjustBiCollectResponse'] = _ADJUSTBICOLLECTRESPONSE
DESCRIPTOR.message_types_by_name['AD_total'] = _AD_TOTAL
DESCRIPTOR.message_types_by_name['GetAdjustBiCollectResponse'] = _GETADJUSTBICOLLECTRESPONSE
DESCRIPTOR.message_types_by_name['CancelAdjustRequest'] = _CANCELADJUSTREQUEST
DESCRIPTOR.message_types_by_name['CancelAdjustResponse'] = _CANCELADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustCollectDetailedRequest'] = _GETADJUSTCOLLECTDETAILEDREQUEST
DESCRIPTOR.message_types_by_name['AdjustCollectDetailed'] = _ADJUSTCOLLECTDETAILED
DESCRIPTOR.message_types_by_name['GetAdjustCollectDetailedResponse'] = _GETADJUSTCOLLECTDETAILEDRESPONSE
DESCRIPTOR.message_types_by_name['AutoCloseCreatedAdjustResponse'] = _AUTOCLOSECREATEDADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['AutoCloseCreatedAdjustRequest'] = _AUTOCLOSECREATEDADJUSTREQUEST
DESCRIPTOR.message_types_by_name['GetMaterialAdjustDataRequest'] = _GETMATERIALADJUSTDATAREQUEST
DESCRIPTOR.message_types_by_name['GetMaterialAdjustDataResponse'] = _GETMATERIALADJUSTDATARESPONSE
DESCRIPTOR.message_types_by_name['AdjustResponse'] = _ADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['CreatedAdjustProductByCode'] = _CREATEDADJUSTPRODUCTBYCODE
DESCRIPTOR.message_types_by_name['SkuRemark'] = _SKUREMARK
DESCRIPTOR.message_types_by_name['CreatedAdjustByCodeRequest'] = _CREATEDADJUSTBYCODEREQUEST
DESCRIPTOR.message_types_by_name['AdjustPositionProducts'] = _ADJUSTPOSITIONPRODUCTS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CreateAdjustRequest = _reflection.GeneratedProtocolMessageType('CreateAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEADJUSTREQUEST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreateAdjustRequest)
  ))
_sym_db.RegisterMessage(CreateAdjustRequest)

CreateAdjustResponse = _reflection.GeneratedProtocolMessageType('CreateAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEADJUSTRESPONSE,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreateAdjustResponse)
  ))
_sym_db.RegisterMessage(CreateAdjustResponse)

GetAdjustRequest = _reflection.GeneratedProtocolMessageType('GetAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTREQUEST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetAdjustRequest)
  ))
_sym_db.RegisterMessage(GetAdjustRequest)

Adjust = _reflection.GeneratedProtocolMessageType('Adjust', (_message.Message,), dict(
  DESCRIPTOR = _ADJUST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.Adjust)
  ))
_sym_db.RegisterMessage(Adjust)

GetAdjustResponse = _reflection.GeneratedProtocolMessageType('GetAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTRESPONSE,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetAdjustResponse)
  ))
_sym_db.RegisterMessage(GetAdjustResponse)

GetAdjustProductRequest = _reflection.GeneratedProtocolMessageType('GetAdjustProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTPRODUCTREQUEST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetAdjustProductRequest)
  ))
_sym_db.RegisterMessage(GetAdjustProductRequest)

AdjustProduct = _reflection.GeneratedProtocolMessageType('AdjustProduct', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTPRODUCT,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.AdjustProduct)
  ))
_sym_db.RegisterMessage(AdjustProduct)

GetAdjustProductResponse = _reflection.GeneratedProtocolMessageType('GetAdjustProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTPRODUCTRESPONSE,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetAdjustProductResponse)
  ))
_sym_db.RegisterMessage(GetAdjustProductResponse)

GetAdjustByIDRequest = _reflection.GeneratedProtocolMessageType('GetAdjustByIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTBYIDREQUEST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetAdjustByIDRequest)
  ))
_sym_db.RegisterMessage(GetAdjustByIDRequest)

ConfirmAdjustRequest = _reflection.GeneratedProtocolMessageType('ConfirmAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMADJUSTREQUEST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ConfirmAdjustRequest)
  ))
_sym_db.RegisterMessage(ConfirmAdjustRequest)

ConfirmAdjustResponse = _reflection.GeneratedProtocolMessageType('ConfirmAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMADJUSTRESPONSE,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ConfirmAdjustResponse)
  ))
_sym_db.RegisterMessage(ConfirmAdjustResponse)

SubmitAdjustRequest = _reflection.GeneratedProtocolMessageType('SubmitAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITADJUSTREQUEST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.SubmitAdjustRequest)
  ))
_sym_db.RegisterMessage(SubmitAdjustRequest)

SubmitAdjustResponse = _reflection.GeneratedProtocolMessageType('SubmitAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITADJUSTRESPONSE,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.SubmitAdjustResponse)
  ))
_sym_db.RegisterMessage(SubmitAdjustResponse)

ApproveAdjustRequest = _reflection.GeneratedProtocolMessageType('ApproveAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _APPROVEADJUSTREQUEST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ApproveAdjustRequest)
  ))
_sym_db.RegisterMessage(ApproveAdjustRequest)

ApproveAdjustResponse = _reflection.GeneratedProtocolMessageType('ApproveAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _APPROVEADJUSTRESPONSE,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ApproveAdjustResponse)
  ))
_sym_db.RegisterMessage(ApproveAdjustResponse)

RejectAdjustRequest = _reflection.GeneratedProtocolMessageType('RejectAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _REJECTADJUSTREQUEST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.RejectAdjustRequest)
  ))
_sym_db.RegisterMessage(RejectAdjustRequest)

RejectAdjustResponse = _reflection.GeneratedProtocolMessageType('RejectAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _REJECTADJUSTRESPONSE,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.RejectAdjustResponse)
  ))
_sym_db.RegisterMessage(RejectAdjustResponse)

GetAdjustProductByStoreIDRequest = _reflection.GeneratedProtocolMessageType('GetAdjustProductByStoreIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTPRODUCTBYSTOREIDREQUEST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetAdjustProductByStoreIDRequest)
  ))
_sym_db.RegisterMessage(GetAdjustProductByStoreIDRequest)

CreateAdjustProductUint = _reflection.GeneratedProtocolMessageType('CreateAdjustProductUint', (_message.Message,), dict(
  DESCRIPTOR = _CREATEADJUSTPRODUCTUINT,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreateAdjustProductUint)
  ))
_sym_db.RegisterMessage(CreateAdjustProductUint)

CreateAdjustProduct = _reflection.GeneratedProtocolMessageType('CreateAdjustProduct', (_message.Message,), dict(
  DESCRIPTOR = _CREATEADJUSTPRODUCT,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreateAdjustProduct)
  ))
_sym_db.RegisterMessage(CreateAdjustProduct)

GetAdjustProductByStoreIDResponse = _reflection.GeneratedProtocolMessageType('GetAdjustProductByStoreIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTPRODUCTBYSTOREIDRESPONSE,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetAdjustProductByStoreIDResponse)
  ))
_sym_db.RegisterMessage(GetAdjustProductByStoreIDResponse)

CreatedAdjustProduct = _reflection.GeneratedProtocolMessageType('CreatedAdjustProduct', (_message.Message,), dict(
  DESCRIPTOR = _CREATEDADJUSTPRODUCT,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreatedAdjustProduct)
  ))
_sym_db.RegisterMessage(CreatedAdjustProduct)

CreatedAdjustRequest = _reflection.GeneratedProtocolMessageType('CreatedAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEDADJUSTREQUEST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreatedAdjustRequest)
  ))
_sym_db.RegisterMessage(CreatedAdjustRequest)

Attachments = _reflection.GeneratedProtocolMessageType('Attachments', (_message.Message,), dict(
  DESCRIPTOR = _ATTACHMENTS,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.Attachments)
  ))
_sym_db.RegisterMessage(Attachments)

UpdateAdjustRequest = _reflection.GeneratedProtocolMessageType('UpdateAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEADJUSTREQUEST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.UpdateAdjustRequest)
  ))
_sym_db.RegisterMessage(UpdateAdjustRequest)

DeleteAdjustRequest = _reflection.GeneratedProtocolMessageType('DeleteAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEADJUSTREQUEST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.DeleteAdjustRequest)
  ))
_sym_db.RegisterMessage(DeleteAdjustRequest)

DeleteAdjustResponse = _reflection.GeneratedProtocolMessageType('DeleteAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETEADJUSTRESPONSE,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.DeleteAdjustResponse)
  ))
_sym_db.RegisterMessage(DeleteAdjustResponse)

DeleteAdjustProductRequest = _reflection.GeneratedProtocolMessageType('DeleteAdjustProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEADJUSTPRODUCTREQUEST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.DeleteAdjustProductRequest)
  ))
_sym_db.RegisterMessage(DeleteAdjustProductRequest)

DeleteAdjustProductResponse = _reflection.GeneratedProtocolMessageType('DeleteAdjustProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETEADJUSTPRODUCTRESPONSE,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.DeleteAdjustProductResponse)
  ))
_sym_db.RegisterMessage(DeleteAdjustProductResponse)

GetAdjustBiCollectRequest = _reflection.GeneratedProtocolMessageType('GetAdjustBiCollectRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTBICOLLECTREQUEST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetAdjustBiCollectRequest)
  ))
_sym_db.RegisterMessage(GetAdjustBiCollectRequest)

AdjustBiCollectResponse = _reflection.GeneratedProtocolMessageType('AdjustBiCollectResponse', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTBICOLLECTRESPONSE,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.AdjustBiCollectResponse)
  ))
_sym_db.RegisterMessage(AdjustBiCollectResponse)

AD_total = _reflection.GeneratedProtocolMessageType('AD_total', (_message.Message,), dict(
  DESCRIPTOR = _AD_TOTAL,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.AD_total)
  ))
_sym_db.RegisterMessage(AD_total)

GetAdjustBiCollectResponse = _reflection.GeneratedProtocolMessageType('GetAdjustBiCollectResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTBICOLLECTRESPONSE,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetAdjustBiCollectResponse)
  ))
_sym_db.RegisterMessage(GetAdjustBiCollectResponse)

CancelAdjustRequest = _reflection.GeneratedProtocolMessageType('CancelAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _CANCELADJUSTREQUEST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CancelAdjustRequest)
  ))
_sym_db.RegisterMessage(CancelAdjustRequest)

CancelAdjustResponse = _reflection.GeneratedProtocolMessageType('CancelAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _CANCELADJUSTRESPONSE,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CancelAdjustResponse)
  ))
_sym_db.RegisterMessage(CancelAdjustResponse)

GetAdjustCollectDetailedRequest = _reflection.GeneratedProtocolMessageType('GetAdjustCollectDetailedRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTCOLLECTDETAILEDREQUEST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetAdjustCollectDetailedRequest)
  ))
_sym_db.RegisterMessage(GetAdjustCollectDetailedRequest)

AdjustCollectDetailed = _reflection.GeneratedProtocolMessageType('AdjustCollectDetailed', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTCOLLECTDETAILED,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.AdjustCollectDetailed)
  ))
_sym_db.RegisterMessage(AdjustCollectDetailed)

GetAdjustCollectDetailedResponse = _reflection.GeneratedProtocolMessageType('GetAdjustCollectDetailedResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTCOLLECTDETAILEDRESPONSE,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetAdjustCollectDetailedResponse)
  ))
_sym_db.RegisterMessage(GetAdjustCollectDetailedResponse)

AutoCloseCreatedAdjustResponse = _reflection.GeneratedProtocolMessageType('AutoCloseCreatedAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _AUTOCLOSECREATEDADJUSTRESPONSE,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.AutoCloseCreatedAdjustResponse)
  ))
_sym_db.RegisterMessage(AutoCloseCreatedAdjustResponse)

AutoCloseCreatedAdjustRequest = _reflection.GeneratedProtocolMessageType('AutoCloseCreatedAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _AUTOCLOSECREATEDADJUSTREQUEST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.AutoCloseCreatedAdjustRequest)
  ))
_sym_db.RegisterMessage(AutoCloseCreatedAdjustRequest)

GetMaterialAdjustDataRequest = _reflection.GeneratedProtocolMessageType('GetMaterialAdjustDataRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETMATERIALADJUSTDATAREQUEST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetMaterialAdjustDataRequest)
  ))
_sym_db.RegisterMessage(GetMaterialAdjustDataRequest)

GetMaterialAdjustDataResponse = _reflection.GeneratedProtocolMessageType('GetMaterialAdjustDataResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETMATERIALADJUSTDATARESPONSE,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetMaterialAdjustDataResponse)
  ))
_sym_db.RegisterMessage(GetMaterialAdjustDataResponse)

AdjustResponse = _reflection.GeneratedProtocolMessageType('AdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTRESPONSE,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.AdjustResponse)
  ))
_sym_db.RegisterMessage(AdjustResponse)

CreatedAdjustProductByCode = _reflection.GeneratedProtocolMessageType('CreatedAdjustProductByCode', (_message.Message,), dict(
  DESCRIPTOR = _CREATEDADJUSTPRODUCTBYCODE,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreatedAdjustProductByCode)
  ))
_sym_db.RegisterMessage(CreatedAdjustProductByCode)

SkuRemark = _reflection.GeneratedProtocolMessageType('SkuRemark', (_message.Message,), dict(

  Tag = _reflection.GeneratedProtocolMessageType('Tag', (_message.Message,), dict(
    DESCRIPTOR = _SKUREMARK_TAG,
    __module__ = 'manufactory.adjust_pb2'
    # @@protoc_insertion_point(class_scope:manufactory.SkuRemark.Tag)
    ))
  ,
  DESCRIPTOR = _SKUREMARK,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.SkuRemark)
  ))
_sym_db.RegisterMessage(SkuRemark)
_sym_db.RegisterMessage(SkuRemark.Tag)

CreatedAdjustByCodeRequest = _reflection.GeneratedProtocolMessageType('CreatedAdjustByCodeRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEDADJUSTBYCODEREQUEST,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreatedAdjustByCodeRequest)
  ))
_sym_db.RegisterMessage(CreatedAdjustByCodeRequest)

AdjustPositionProducts = _reflection.GeneratedProtocolMessageType('AdjustPositionProducts', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTPOSITIONPRODUCTS,
  __module__ = 'manufactory.adjust_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.AdjustPositionProducts)
  ))
_sym_db.RegisterMessage(AdjustPositionProducts)



_MANUFACTORYADJUST = _descriptor.ServiceDescriptor(
  name='ManufactoryAdjust',
  full_name='manufactory.ManufactoryAdjust',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=10567,
  serialized_end=13345,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetAdjust',
    full_name='manufactory.ManufactoryAdjust.GetAdjust',
    index=0,
    containing_service=None,
    input_type=_GETADJUSTREQUEST,
    output_type=_GETADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\002#\022!/api/v2/supply/manufactory/adjust'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustProduct',
    full_name='manufactory.ManufactoryAdjust.GetAdjustProduct',
    index=1,
    containing_service=None,
    input_type=_GETADJUSTPRODUCTREQUEST,
    output_type=_GETADJUSTPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\0225/api/v2/supply/manufactory/adjust/{adjust_id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustByID',
    full_name='manufactory.ManufactoryAdjust.GetAdjustByID',
    index=2,
    containing_service=None,
    input_type=_GETADJUSTBYIDREQUEST,
    output_type=_ADJUST,
    serialized_options=_b('\202\323\344\223\002/\022-/api/v2/supply/manufactory/adjust/{adjust_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmAdjust',
    full_name='manufactory.ManufactoryAdjust.ConfirmAdjust',
    index=3,
    containing_service=None,
    input_type=_CONFIRMADJUSTREQUEST,
    output_type=_CONFIRMADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\002:\0325/api/v2/supply/manufactory/adjust/{adjust_id}/confirm:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='SubmitAdjust',
    full_name='manufactory.ManufactoryAdjust.SubmitAdjust',
    index=4,
    containing_service=None,
    input_type=_SUBMITADJUSTREQUEST,
    output_type=_SUBMITADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\0324/api/v2/supply/manufactory/adjust/{adjust_id}/submit:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ApproveAdjust',
    full_name='manufactory.ManufactoryAdjust.ApproveAdjust',
    index=5,
    containing_service=None,
    input_type=_APPROVEADJUSTREQUEST,
    output_type=_APPROVEADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\002:\0325/api/v2/supply/manufactory/adjust/{adjust_id}/approve:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='RejectAdjust',
    full_name='manufactory.ManufactoryAdjust.RejectAdjust',
    index=6,
    containing_service=None,
    input_type=_REJECTADJUSTREQUEST,
    output_type=_REJECTADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\0324/api/v2/supply/manufactory/adjust/{adjust_id}/reject:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustProductByStoreID',
    full_name='manufactory.ManufactoryAdjust.GetAdjustProductByStoreID',
    index=7,
    containing_service=None,
    input_type=_GETADJUSTPRODUCTBYSTOREIDREQUEST,
    output_type=_GETADJUSTPRODUCTBYSTOREIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\0220/api/v2/supply/manufactory/adjust/store/products'),
  ),
  _descriptor.MethodDescriptor(
    name='CreatedAdjust',
    full_name='manufactory.ManufactoryAdjust.CreatedAdjust',
    index=8,
    containing_service=None,
    input_type=_CREATEDADJUSTREQUEST,
    output_type=_ADJUST,
    serialized_options=_b('\202\323\344\223\002-\"(/api/v2/supply/manufactory/adjust/create:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateAdjust',
    full_name='manufactory.ManufactoryAdjust.UpdateAdjust',
    index=9,
    containing_service=None,
    input_type=_UPDATEADJUSTREQUEST,
    output_type=_ADJUST,
    serialized_options=_b('\202\323\344\223\0029\0324/api/v2/supply/manufactory/adjust/{adjust_id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteAdjust',
    full_name='manufactory.ManufactoryAdjust.DeleteAdjust',
    index=10,
    containing_service=None,
    input_type=_DELETEADJUSTREQUEST,
    output_type=_DELETEADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\0324/api/v2/supply/manufactory/adjust/{adjust_id}/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteAdjustProduct',
    full_name='manufactory.ManufactoryAdjust.DeleteAdjustProduct',
    index=11,
    containing_service=None,
    input_type=_DELETEADJUSTPRODUCTREQUEST,
    output_type=_DELETEADJUSTPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\002A\032</api/v2/supply/manufactory/adjust/product/{adjust_id}/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustBiCollect',
    full_name='manufactory.ManufactoryAdjust.GetAdjustBiCollect',
    index=12,
    containing_service=None,
    input_type=_GETADJUSTBICOLLECTREQUEST,
    output_type=_GETADJUSTBICOLLECTRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\022,/api/v2/supply/manufactory/adjust/bi/collect'),
  ),
  _descriptor.MethodDescriptor(
    name='CancelAdjust',
    full_name='manufactory.ManufactoryAdjust.CancelAdjust',
    index=13,
    containing_service=None,
    input_type=_CANCELADJUSTREQUEST,
    output_type=_CANCELADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\0324/api/v2/supply/manufactory/adjust/auto_create/cancel:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustCollectDetailed',
    full_name='manufactory.ManufactoryAdjust.GetAdjustCollectDetailed',
    index=14,
    containing_service=None,
    input_type=_GETADJUSTCOLLECTDETAILEDREQUEST,
    output_type=_GETADJUSTCOLLECTDETAILEDRESPONSE,
    serialized_options=_b('\202\323\344\223\002/\022-/api/v2/supply/manufactory/adjust/bi/detailed'),
  ),
  _descriptor.MethodDescriptor(
    name='AutoCloseCreatedAdjust',
    full_name='manufactory.ManufactoryAdjust.AutoCloseCreatedAdjust',
    index=15,
    containing_service=None,
    input_type=_AUTOCLOSECREATEDADJUSTREQUEST,
    output_type=_AUTOCLOSECREATEDADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\022,/api/v2/supply/manufactory/adjust/close/auto'),
  ),
  _descriptor.MethodDescriptor(
    name='GetMaterialAdjustData',
    full_name='manufactory.ManufactoryAdjust.GetMaterialAdjustData',
    index=16,
    containing_service=None,
    input_type=_GETMATERIALADJUSTDATAREQUEST,
    output_type=_GETMATERIALADJUSTDATARESPONSE,
    serialized_options=_b('\202\323\344\223\0021\022//api/v2/supply/manufactory/adjust/material/cost'),
  ),
  _descriptor.MethodDescriptor(
    name='CreatedAdjustByCode',
    full_name='manufactory.ManufactoryAdjust.CreatedAdjustByCode',
    index=17,
    containing_service=None,
    input_type=_CREATEDADJUSTBYCODEREQUEST,
    output_type=_ADJUST,
    serialized_options=_b('\202\323\344\223\002*\"%/api/v2/supply/manufactory/adjust/pos:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_MANUFACTORYADJUST)

DESCRIPTOR.services_by_name['ManufactoryAdjust'] = _MANUFACTORYADJUST

# @@protoc_insertion_point(module_scope)
