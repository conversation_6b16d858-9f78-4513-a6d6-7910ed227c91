{"swagger": "2.0", "info": {"title": "stocktake.proto", "version": "version not set"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/store/data/scope": {"post": {"summary": "scope权限", "operationId": "GetStoreScope", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeStoreDataScope"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/stocktakeStoreDataScopeRequest"}}], "tags": ["stocktake"]}}, "/api/v2/supply/recreate_stocktake_doc": {"post": {"summary": "RecreateStocktakeDoc  重盘功能，重新生成盘点单35", "operationId": "RecreateStocktakeDoc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeRecreateStocktakeDocResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/stocktakeRecreateStocktakeDocRequest"}}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake": {"get": {"summary": "GetStocktake 查询盘点单20", "operationId": "GetStocktake", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeGetStocktakeResponse"}}}, "parameters": [{"name": "include_total", "description": "是否包含总数.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "store_ids", "description": "门店ID.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "branch_ids", "description": "营运区域id.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "store_status", "description": "门店状态.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "enum": ["OPENED", "CLOSED"]}}, {"name": "_type", "description": "盘点单类型.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "enum": ["W", "D", "M", "R"]}}, {"name": "status", "description": "盘点单状态.\n\n - INITED: 用户手动创建状态\n - CANCELLED: 用户取消状态\n - APPROVED: 财务核算检查后状态\n - CONFIRMED: 录入盘点单位确定状态\n - SUBMIT_0: 月盘全盘0提交", "in": "query", "required": false, "type": "array", "items": {"type": "string", "enum": ["INITED", "STARTED", "CANCELLED", "SUBMITTED", "REJECTED", "APPROVED", "CONFIRMED", "SUBMIT_0", "APPROVE_0"]}}, {"name": "schedule_code", "in": "query", "required": false, "type": "string"}, {"name": "offset", "description": "分页开始处.", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "description": "分页限制数.", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "start_date", "description": "查询开始时间.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "查询结束时间.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "target_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "is_create", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "jde_code", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序(默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "branch_type", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/balance/product/group": {"get": {"summary": "StocktakeBalanceRegion区域盘点31", "operationId": "StocktakeBalanceRegion", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeStocktakeBalanceRegionResponse"}}}, "parameters": [{"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "start", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "region_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "product_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "type", "in": "query", "required": false, "type": "string"}, {"name": "branch_type", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/balance/{doc_id}/product/group": {"get": {"summary": "GetStocktakeBalanceProductGroup门店盘点单损益汇总报表29", "operationId": "GetStocktakeBalanceProductGroup", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeGetStocktakeBalanceProductGroupResponse"}}}, "parameters": [{"name": "doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/bi/balance": {"get": {"summary": "GetStocktakeBalance盘点单损益报表27", "operationId": "GetStocktakeBalance", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeGetStocktakeBalanceResponse"}}}, "parameters": [{"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "store_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "target_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "status", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "schedule_code", "in": "query", "required": false, "type": "string"}, {"name": "stocktake_type", "in": "query", "required": false, "type": "string"}, {"name": "is_wms_store", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "branch_type", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/bi/detail": {"get": {"summary": "StocktakeBiDetailed盘点单报表30", "operationId": "StocktakeBiDetailed", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeStocktakeBiDetailedResponse"}}}, "parameters": [{"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "category_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "type", "in": "query", "required": false, "type": "string"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "product_name", "in": "query", "required": false, "type": "string"}, {"name": "store_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "order", "description": "排序(默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "jde_code", "in": "query", "required": false, "type": "string"}, {"name": "is_wms_store", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "branch_type", "description": "区分门店还是仓库/加工中心 STORE/WAREHOUSE/MACHINING_CENTER.", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/diff/report": {"get": {"summary": "StocktakeDiffReport  盘点差异表33", "operationId": "StocktakeDiffReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeStocktakeDiffReportResponse"}}}, "parameters": [{"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "store_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "product_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "target_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "stocktake_type", "in": "query", "required": false, "type": "string"}, {"name": "jde_diff_code", "in": "query", "required": false, "type": "string"}, {"name": "schedule_code", "in": "query", "required": false, "type": "string"}, {"name": "status", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序(默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "upper_limit", "description": "上限 区分0，传字符串.", "in": "query", "required": false, "type": "string"}, {"name": "lower_limit", "description": "下限 区分0，传字符串.", "in": "query", "required": false, "type": "string"}, {"name": "is_wms_store", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/diff_collect/report": {"get": {"summary": "StocktakeDiffCollectReport  盘点差异表汇总查询37", "operationId": "StocktakeDiffCollectReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeStocktakeDiffCollectReportResponse"}}}, "parameters": [{"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "store_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "product_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "target_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "stocktake_type", "in": "query", "required": false, "type": "string"}, {"name": "jde_diff_code", "in": "query", "required": false, "type": "string"}, {"name": "schedule_code", "in": "query", "required": false, "type": "string"}, {"name": "status", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序(默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "period_symbol", "in": "query", "required": false, "type": "string"}, {"name": "upper_limit", "description": "上限 区分0，传字符串.", "in": "query", "required": false, "type": "string"}, {"name": "lower_limit", "description": "下限 区分0，传字符串.", "in": "query", "required": false, "type": "string"}, {"name": "is_wms_store", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/product/import": {"post": {"summary": "仓库/门店盘点单商品明细导入", "operationId": "StocktakeProductImport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeStocktakeProductImportResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/stocktakeStocktakeProductImportRequest"}}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/product/tags": {"get": {"summary": "GetStocktakeTags获取盘点标签24", "operationId": "GetStocktakeTags", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeGetStocktakeTagsResponse"}}}, "parameters": [{"name": "branch_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "tag_name", "in": "query", "required": false, "type": "string"}, {"name": "branch_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/product/tags/action": {"put": {"summary": "ActionStocktakeTags增加，删除，更新,获取盘点标签25", "operationId": "ActionStocktakeTags", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeActionStocktakeTagsResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/stocktakeActionStocktakeTagsRequest"}}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/product/tags/clean": {"put": {"summary": "DeleteStocktakeProductTags删除盘点商品标签条目26", "operationId": "DeleteStocktakeProductTags", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeDeleteStocktakeProductTagsResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/stocktakeDeleteStocktakeProductTagsRequest"}}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/{doc_id}": {"get": {"summary": "GetStocktakeByDocID 获取一个盘点单19", "operationId": "GetStocktakeByDocID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeStocktake"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["stocktake"]}, "put": {"summary": "PutStocktakeByDocID 提交盘点单明细商品数量，更新盘点单, 22", "operationId": "PutStocktakeByDocID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakePutStocktakeByDocIDResponse"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/stocktakePutStocktakeByDocIDRequest"}}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/{doc_id}/advance": {"get": {"summary": "AdvanceStocktakeDiff  提前查看盘点损益32", "operationId": "AdvanceStocktakeDiff", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeAdvanceStocktakeDiffResponse"}}}, "parameters": [{"name": "doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/{doc_id}/approve": {"put": {"summary": "APPROVEDStocktakeByDocID  财务确认盘点单（status=APPROVED最终状态，）16", "operationId": "ApproveStocktakeByDocID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeApproveStocktakeByDocIDResponse"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/stocktakeApproveStocktakeByDocIDRequest"}}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/{doc_id}/cancel": {"put": {"summary": "CancelStocktakeByDocID  作废盘点单（status=CANCELED）18", "operationId": "CancelStocktakeByDocID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeCancelStocktakeByDocIDResponse"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/{doc_id}/confirm": {"put": {"summary": "ConfirmStocktakeByDocID 确认盘点单（status=CONFITRMED，核算完库存后'FINALIZED'）15", "operationId": "ConfirmStocktakeByDocID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeConfirmStocktakeByDocIDResponse"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/{doc_id}/confirm/check": {"put": {"summary": "备注:盘点商品在INITED和REJECTED状态可以填写数量，CONFITRMED，不能。\nCheckStocktakeByDocID 检查能否确认盘点单 14", "operationId": "CheckStocktakeByDocID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDResponse"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/{doc_id}/init/check": {"put": {"summary": "CheckedStocktakeByDocID 检查完成盘点单23", "operationId": "CheckedStocktakeByDocID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeCheckedStocktakeByDocIDResponse"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/stocktakeCheckedStocktakeByDocIDRequest"}}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/{doc_id}/product": {"get": {"summary": "GetStocktakeProduct 查询盘点单明细商品21", "operationId": "GetStocktakeProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeGetStocktakeProductResponse"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "include_unit", "description": "是否包含核算单位.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "limit", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "offset", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "include_total", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "category_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "storage_type", "in": "query", "required": false, "type": "string"}, {"name": "product_name", "in": "query", "required": false, "type": "string"}, {"name": "lan", "in": "query", "required": false, "type": "string"}, {"name": "include_barcode", "in": "query", "required": false, "type": "boolean", "format": "boolean"}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/{doc_id}/reject": {"put": {"summary": "RejectStocktakeProduct 财务驳回提交后的部分盘点单明细商品（status=REJECTED，部分商品status=REJECTED）17", "operationId": "RejectStocktakeProduct", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeRejectStocktakeProductResponse"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/stocktakeRejectStocktakeProductRequest"}}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake/{doc_id}/submit": {"put": {"summary": "SubmitStocktakeByDocID 提交盘点单28", "operationId": "SubmitStocktakeByDocID", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeSubmitStocktakeByDocIDResponse"}}}, "parameters": [{"name": "doc_id", "description": "盘点单doc_id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/stocktakeSubmitStocktakeByDocIDRequest"}}], "tags": ["stocktake"]}}, "/api/v2/supply/stocktake_doc_statistics": {"post": {"summary": "StocktakeDocStatistics  盘点单统计报表36", "operationId": "StocktakeDocStatistics", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeStocktakeDocStatisticsResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/stocktakeStocktakeDocStatisticsRequest"}}], "tags": ["stocktake"]}}, "/api/v2/supply/uncomplete_doc": {"get": {"summary": "GetUncompleteDoc  首页未完成单据34", "operationId": "GetUncompleteDoc", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeGetUncompleteDocResponse"}}}, "parameters": [{"name": "start_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "store_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["stocktake"]}}, "/api/v2/supply/uncomplete_doc_report": {"get": {"summary": "UncompleteDocReport  未完成单据报表38", "operationId": "UncompleteDocReport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeUncompleteDocReportResponse"}}}, "parameters": [{"name": "bus_date", "in": "query", "required": false, "type": "string"}, {"name": "store_ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "jde_code", "in": "query", "required": false, "type": "string"}, {"name": "code", "in": "query", "required": false, "type": "string"}, {"name": "limit", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "offset", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "model", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "order", "description": "排序(默认asc).", "in": "query", "required": false, "type": "string"}, {"name": "sort", "in": "query", "required": false, "type": "string"}, {"name": "is_wms_store", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "lan", "in": "query", "required": false, "type": "string"}], "tags": ["stocktake"]}}, "/api/v2/supply/update/stocktake/import/batch": {"put": {"summary": "仓库/门店盘点单商品导入文件状态修改", "operationId": "UpdateStocktakeImportBatch", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakeUpdateStocktakeImportBatchResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/stocktakeUpdateStocktakeImportBatchRequest"}}], "tags": ["stocktake"]}}, "/ping": {"get": {"summary": "Ping 健康检查", "operationId": "<PERSON>", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/stocktakePong"}}}, "tags": ["stocktake"]}}}, "definitions": {"GetStocktakeRequestS_STATUS": {"type": "string", "enum": ["OPENED", "CLOSED"], "default": "OPENED"}, "UncompleteDocReportResponseUncompleteDocReport": {"type": "object", "properties": {"model": {"type": "string"}, "store_code": {"type": "string"}, "store_name": {"type": "string"}, "status": {"type": "string"}, "jde_code": {"type": "string"}, "code": {"type": "string"}, "doc_update_name": {"type": "string"}, "doc_update_time": {"type": "string"}, "doc_date": {"type": "string"}, "bus_date": {"type": "string"}, "store_id": {"type": "string", "format": "uint64"}, "jde_type": {"type": "string"}}}, "stocktakeActionStocktakeTagsRequest": {"type": "object", "properties": {"tag_id": {"type": "string", "format": "uint64"}, "action": {"$ref": "#/definitions/stocktakeActionStocktakeTagsRequestAction"}, "name": {"type": "string"}, "branch_id": {"type": "string", "format": "uint64", "title": "复制新增操作对应原门店"}, "lan": {"type": "string"}, "branch_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "门店id列表"}, "region_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "区域id列表"}, "add_dimension": {"type": "string", "title": "添加维度：全市场/管理区域/门店->all/region/store"}, "tag_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "标签id-批量删除用"}, "origin_name": {"type": "string", "title": "批量修改中原标签名称"}, "copy_branch": {"type": "string", "format": "uint64", "title": "需要copy的门店"}}}, "stocktakeActionStocktakeTagsRequestAction": {"type": "string", "enum": ["get", "create", "delete", "update", "copy"], "default": "get"}, "stocktakeActionStocktakeTagsResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}, "id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "user_id": {"type": "string", "format": "uint64"}, "created_by": {"type": "string", "format": "uint64"}, "updated_by": {"type": "string", "format": "uint64"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "branch_id": {"type": "string", "format": "uint64"}}}, "stocktakeAdvanceStocktakeDiffResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/stocktakeStocktakeProduct"}}, "total": {"type": "string", "format": "uint64"}, "position_rows": {"type": "array", "items": {"$ref": "#/definitions/stocktakeStocktakePositionProducts"}}}}, "stocktakeApproveStocktakeByDocIDRequest": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "盘点单doc_id"}, "type": {"type": "string"}, "approve_name": {"type": "string"}, "lan": {"type": "string"}, "all_zero": {"type": "boolean", "format": "boolean", "title": "是否全盘零"}}}, "stocktakeApproveStocktakeByDocIDResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}, "handler": {"type": "boolean", "format": "boolean"}, "adjust": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "receiving_diff": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "transfer": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "receiving": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "return": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "direct_receiving": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "direct_receiving_diff": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "direct_return": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}}}, "stocktakeCancelStocktakeByDocIDResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "stocktakeCheckDemandDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "code": {"type": "string"}, "type": {"type": "string"}, "is_plan": {"type": "boolean", "format": "boolean"}}}, "stocktakeCheckStocktakeByDocIDDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "code": {"type": "string"}}}, "stocktakeCheckStocktakeByDocIDResponse": {"type": "object", "properties": {"handler": {"type": "boolean", "format": "boolean"}, "adjust": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "receiving_diff": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "transfer": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "receiving": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "return": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "direct_receiving": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "direct_receiving_diff": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "direct_return": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "stocktake": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "demand": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}}}, "stocktakeCheckedStocktakeByDocIDRequest": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "盘点单doc_id"}, "branch_type": {"type": "string", "title": "区分门店/仓库 WAREHOUSE/STORE"}}}, "stocktakeCheckedStocktakeByDocIDResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}, "title": "赶任务木想写注释了"}, "stocktakeConfirmStocktakeByDocIDResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "stocktakeDeleteStocktakeProductTagsRequest": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "stocktakeDeleteStocktakeProductTagsResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "stocktakeGetStocktakeBalanceProductGroupResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/stocktakeStocktakeBalanceProductGroup"}}}}, "stocktakeGetStocktakeBalanceResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/stocktakeStocktakeBalance"}}, "total": {"type": "string", "format": "uint64"}}}, "stocktakeGetStocktakeProductResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/stocktakeStocktakeProduct"}}, "total": {"type": "string", "format": "uint64"}, "position_rows": {"type": "array", "items": {"$ref": "#/definitions/stocktakeStocktakePositionProducts"}}}}, "stocktakeGetStocktakeRequestSTATUS": {"type": "string", "enum": ["INITED", "STARTED", "CANCELLED", "SUBMITTED", "REJECTED", "APPROVED", "CONFIRMED", "SUBMIT_0", "APPROVE_0"], "default": "INITED", "title": "- INITED: 用户手动创建状态\n - CANCELLED: 用户取消状态\n - APPROVED: 财务核算检查后状态\n - CONFIRMED: 录入盘点单位确定状态\n - SUBMIT_0: 月盘全盘0提交"}, "stocktakeGetStocktakeRequestS_TYPE": {"type": "string", "enum": ["W", "D", "M", "R"], "default": "W"}, "stocktakeGetStocktakeResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/stocktakeStocktake"}}, "total": {"type": "integer", "format": "int64"}}}, "stocktakeGetStocktakeTagsResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/stocktakeStocktakeTags"}}}}, "stocktakeGetUncompleteDocResponse": {"type": "object", "properties": {"handler": {"type": "boolean", "format": "boolean"}, "adjust": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "receiving_diff": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "transfer": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "receiving": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "return": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "direct_receiving": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "direct_receiving_diff": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "direct_return": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "stocktake": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}, "demand": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckDemandDetail"}}, "assets": {"type": "array", "items": {"$ref": "#/definitions/stocktakeCheckStocktakeByDocIDDetail"}}}}, "stocktakePeriodGroupMethod": {"type": "string", "enum": ["BY_DAY", "BY_MONTH", "BY_YEAR"], "default": "BY_DAY", "title": "- BY_DAY: 按天汇总\n - BY_MONTH: 按月汇总\n - BY_YEAR: 按年汇总"}, "stocktakePong": {"type": "object", "properties": {"msg": {"type": "string"}}}, "stocktakeProductImportResponseRows": {"type": "object", "properties": {"row_num": {"type": "string", "format": "uint64", "title": "行号"}, "product_code": {"type": "string", "title": "商品编号"}, "product_name": {"type": "string", "title": "商品名称"}, "quantity": {"type": "number", "format": "double", "title": "盘点数量"}, "storage_type": {"type": "string", "title": "储藏类型"}, "spec": {"type": "string", "title": "规格"}, "unit": {"type": "string", "title": "单位"}, "error_msg": {"type": "string", "title": "异常原因"}, "position_id": {"type": "string", "title": "仓位ID"}, "position_code": {"type": "string", "title": "仓位编号"}, "position_name": {"type": "string", "title": "仓位名称"}}}, "stocktakeProductTagBi": {"type": "object", "properties": {"tag_unit_id": {"type": "string", "format": "uint64"}, "tag_unit_name": {"type": "string"}, "tag_quantity": {"type": "number", "format": "double"}, "tag_name": {"type": "string"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "accounting_quantity": {"type": "number", "format": "double"}, "id": {"type": "string", "format": "uint64"}}}, "stocktakePutStocktakeByDocIDRequest": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "盘点单doc_id"}, "products": {"type": "array", "items": {"$ref": "#/definitions/stocktakePutStocktakeProducts"}}, "lan": {"type": "string"}, "all_zero": {"type": "boolean", "format": "boolean", "title": "月盘单全盘零"}}}, "stocktakePutStocktakeByDocIDResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "stocktakePutStocktakeProducts": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "quantity": {"type": "number", "format": "double", "title": "总数"}, "unit_id": {"type": "string", "format": "uint64"}, "tag_products": {"type": "array", "items": {"$ref": "#/definitions/stocktakeTagQuantity"}}, "is_pda": {"type": "boolean", "format": "boolean", "title": "是否pda盘点"}, "is_empty": {"type": "boolean", "format": "boolean", "title": "是否置0"}, "is_null": {"type": "boolean", "format": "boolean", "title": "PC盘点是否置空"}, "del_tag_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "删除的商品下标签id列表"}}}, "stocktakeRecreateStocktakeDocRequest": {"type": "object", "properties": {"doc_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "calculate_inventory": {"type": "boolean", "format": "boolean"}, "schedule_name": {"type": "string"}, "remark": {"type": "string"}, "schedule_code": {"type": "string"}, "schedule_id": {"type": "string", "format": "uint64"}, "lan": {"type": "string"}}}, "stocktakeRecreateStocktakeDocResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}, "restocktake_doc_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "has_recreate_doc_id_no_confirm": {"type": "array", "items": {"type": "string", "format": "uint64"}}}}, "stocktakeRejectStocktakeProductRequest": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "盘点单doc_id"}, "reason": {"type": "string"}, "lan": {"type": "string"}}}, "stocktakeRejectStocktakeProductResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "stocktakeST_total": {"type": "object", "properties": {"count": {"type": "string", "format": "uint64"}, "sum_quantity": {"type": "number", "format": "double"}, "sum_accounting_quantity": {"type": "number", "format": "double"}}}, "stocktakeScopeStores": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "code": {"type": "string"}, "second_code": {"type": "string"}, "type": {"type": "string"}, "address": {"type": "string"}, "tel": {"type": "string"}, "contact": {"type": "string"}, "status": {"type": "string"}, "name_en": {"type": "string"}, "open_date": {"type": "string"}, "close_date": {"type": "string"}, "email": {"type": "string"}, "geo_region": {"type": "array", "items": {"type": "string"}}, "branch_region": {"type": "array", "items": {"type": "string"}}, "order_region": {"type": "array", "items": {"type": "string"}}, "distribution_region": {"type": "array", "items": {"type": "string"}}, "purchase_region": {"type": "array", "items": {"type": "string"}}, "market_region": {"type": "array", "items": {"type": "string"}}, "transfer_region": {"type": "array", "items": {"type": "string"}}, "attribute_region": {"type": "array", "items": {"type": "string"}}}}, "stocktakeStocktake": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "盘点单id"}, "partner_id": {"type": "string", "format": "uint64"}, "branch_batch_id": {"type": "string", "format": "uint64", "title": "盘点批次id"}, "branch_id": {"type": "string", "format": "uint64", "title": "盘点单门店id"}, "schedule_id": {"type": "string", "format": "uint64", "title": "盘点计划id"}, "branch_type": {"type": "string", "title": "盘点单门店类型"}, "calculate_inventory": {"type": "boolean", "format": "boolean", "title": "是否计算库存"}, "code": {"type": "string", "title": "盘点单编码"}, "forecasting": {"type": "boolean", "format": "boolean", "title": "是否预计算"}, "forecasting_time": {"type": "string", "title": "预计算时间"}, "remark": {"type": "string", "title": "标识符"}, "result_type": {"type": "string", "title": "结果类型"}, "schedule_code": {"type": "string", "title": "盘点计划编码"}, "st_diff_flag": {"type": "integer", "format": "int32", "title": "盘点差异标识"}, "status": {"$ref": "#/definitions/stocktakeStocktakeSTATUS", "title": "盘点单状态"}, "process_status": {"$ref": "#/definitions/stocktakeStocktakeSTATUS", "title": "过程状态"}, "store_secondary_id": {"type": "string", "format": "uint64", "title": "门店us_id"}, "type": {"type": "string", "title": "盘点单类型"}, "created_by": {"type": "string", "format": "uint64", "title": "创建者"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新者"}, "review_by": {"type": "string", "format": "uint64", "title": "校验者"}, "target_date": {"type": "string", "format": "date-time", "title": "盘点日期"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "user_id": {"type": "string", "format": "uint64"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "schedule_name": {"type": "string", "title": "盘点计划名称"}, "diff_err_message": {"type": "string"}, "diff_jde_status": {"type": "string"}, "month_err_message": {"type": "string"}, "month_jde_status": {"type": "string"}, "jde_diff_code": {"type": "string"}, "jde_month_code": {"type": "string"}, "original_code": {"type": "string"}, "original_doc_id": {"type": "string", "format": "uint64"}, "is_recreate": {"type": "boolean", "format": "boolean"}, "recreate_code": {"type": "string"}, "recreate_doc_id": {"type": "string", "format": "uint64"}, "submit_name": {"type": "string"}, "approve_name": {"type": "string"}, "stocktake_type": {"type": "string"}, "request_id": {"type": "string", "format": "uint64"}}}, "stocktakeStocktakeBalance": {"type": "object", "properties": {"branch_batch_id": {"type": "string", "format": "uint64"}, "branch_id": {"type": "string", "format": "uint64"}, "branch_type": {"type": "string"}, "calculate_inventory": {"type": "boolean", "format": "boolean"}, "code": {"type": "string"}, "forecasting": {"type": "boolean", "format": "boolean"}, "forecasting_time": {"type": "string", "format": "date-time"}, "id": {"type": "string", "format": "uint64"}, "process_status": {"type": "string"}, "remark": {"type": "string"}, "result_type": {"type": "string"}, "review_by": {"type": "string", "format": "uint64"}, "schedule_code": {"type": "string"}, "schedule_id": {"type": "string", "format": "uint64"}, "st_diff_flag": {"type": "integer", "format": "int32"}, "status": {"type": "string"}, "store_secondary_id": {"type": "string", "format": "uint64"}, "target_date": {"type": "string", "format": "date-time"}, "type": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "user_id": {"type": "string", "format": "uint64"}, "created_by": {"type": "string", "format": "uint64"}, "updated_by": {"type": "string", "format": "uint64"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "schedule_name": {"type": "string"}, "diff_err_message": {"type": "string"}, "diff_jde_status": {"type": "string"}, "month_err_message": {"type": "string"}, "month_jde_status": {"type": "string"}, "jde_diff_code": {"type": "string"}, "jde_month_code": {"type": "string"}, "branch_name": {"type": "string"}, "branch_code": {"type": "string"}, "original_code": {"type": "string"}, "original_doc_id": {"type": "string", "format": "uint64"}, "is_recreate": {"type": "boolean", "format": "boolean"}, "recreate_code": {"type": "string"}, "recreate_doc_id": {"type": "string", "format": "uint64"}, "submit_name": {"type": "string"}, "approve_name": {"type": "string"}, "stocktake_type": {"type": "string"}, "request_id": {"type": "string", "format": "uint64"}}}, "stocktakeStocktakeBalanceProductGroup": {"type": "object", "properties": {"accounting_quantity": {"type": "number", "format": "double"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "accounting_unit_spec": {"type": "string"}, "diff_quantity": {"type": "number", "format": "double"}, "inventory_quantity": {"type": "number", "format": "double"}, "is_system": {"type": "boolean", "format": "boolean"}, "material_number": {"type": "string"}, "product_code": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "storage_type": {"type": "string"}, "tag_code": {"type": "string"}, "tag_name": {"type": "string"}, "unit_diff_quantity": {"type": "number", "format": "double"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "unit_rate": {"type": "number", "format": "double"}, "unit_spec": {"type": "string"}, "tag_details": {"type": "array", "items": {"$ref": "#/definitions/stocktakeTagProductBi"}}, "accounting_inventory_quantity": {"type": "number", "format": "double"}, "accounting_diff_quantity": {"type": "number", "format": "double"}, "position_id": {"type": "string", "format": "uint64"}, "position_code": {"type": "string"}, "position_name": {"type": "string"}}}, "stocktakeStocktakeBalanceRegion": {"type": "object", "properties": {"accounting_inventory_quantity": {"type": "number", "format": "double"}, "accounting_quantity": {"type": "number", "format": "double"}, "accounting_unit_name": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/stocktakeStocktakeBalanceRegionDetails"}}, "unit_name": {"type": "string"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "inventory_quantity": {"type": "number", "format": "double"}, "accoinventory_quantity": {"type": "number", "format": "double"}, "diff_quantity": {"type": "number", "format": "double"}, "unit_diff_quantity": {"type": "number", "format": "double"}, "unit_rate": {"type": "number", "format": "double"}, "doc_type": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "store_id": {"type": "string", "format": "uint64"}, "store_name": {"type": "string"}, "store_code": {"type": "string"}, "product_code": {"type": "string"}, "accounting_diff_quantity": {"type": "number", "format": "double"}}}, "stocktakeStocktakeBalanceRegionDetails": {"type": "object", "properties": {"accounting_inventory_quantity": {"type": "number", "format": "double"}, "accounting_quantity": {"type": "number", "format": "double"}, "accounting_unit_name": {"type": "string"}, "branch_name": {"type": "string"}, "diff_quantity": {"type": "number", "format": "double"}, "doc_type": {"type": "string"}, "inventory_quantity": {"type": "number", "format": "double"}, "material_number": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "store_id": {"type": "string", "format": "uint64"}, "store_name": {"type": "string"}, "tag_1": {"type": "string", "format": "uint64"}, "tag_1_name_pinyin": {"type": "string"}, "unit_diff_quantity": {"type": "number", "format": "double"}, "unit_name": {"type": "string"}, "unit_rate": {"type": "number", "format": "double"}}}, "stocktakeStocktakeBalanceRegionResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/stocktakeStocktakeBalanceRegion"}}, "total": {"type": "string", "format": "uint64"}}}, "stocktakeStocktakeBiDetailed": {"type": "object", "properties": {"accounting_quantity": {"type": "number", "format": "double"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "accounting_unit_spec": {"type": "string"}, "branch_1_name": {"type": "string"}, "branch_2_name": {"type": "string"}, "branch_id": {"type": "string", "format": "uint64"}, "category_code": {"type": "string"}, "category_id": {"type": "string", "format": "uint64"}, "category_name": {"type": "string"}, "code": {"type": "string", "title": "\"category_parent\""}, "deleted": {"type": "boolean", "format": "boolean"}, "diff_quantity": {"type": "number", "format": "double"}, "display_order": {"type": "string"}, "doc_id": {"type": "string", "format": "uint64"}, "extends": {"type": "string"}, "id": {"type": "string", "format": "uint64"}, "ignored": {"type": "boolean", "format": "boolean"}, "inventory_quantity": {"type": "number", "format": "double"}, "is_system": {"type": "boolean", "format": "boolean"}, "item_number": {"type": "string", "format": "uint64"}, "material_number": {"type": "string"}, "product_code": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "quantity": {"type": "number", "format": "double"}, "type": {"type": "string"}, "storage_type": {"type": "string"}, "store_code": {"type": "string"}, "store_name": {"type": "string"}, "target_date": {"type": "string", "format": "date-time"}, "unit_diff_quantity": {"type": "number", "format": "double"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "unit_rate": {"type": "number", "format": "double"}, "unit_spec": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "created_by": {"type": "string", "format": "uint64"}, "updated_by": {"type": "string", "format": "uint64"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "is_empty": {"type": "boolean", "format": "boolean"}, "is_pda": {"type": "boolean", "format": "boolean"}, "status": {"type": "string"}, "units": {"type": "string"}, "user_id": {"type": "string", "format": "uint64"}, "is_null": {"type": "boolean", "format": "boolean"}, "tag_quantity": {"type": "number", "format": "double"}, "product_tags": {"type": "array", "items": {"$ref": "#/definitions/stocktakeProductTagBi"}}, "is_enable": {"type": "boolean", "format": "boolean", "title": "商品状态、JDE盘点单号、规格、理论数量（加在盘点数量旁边）、盘点差异（百分比）、新建时间、操作人，提交时间、操作人。"}, "jde_code": {"type": "string"}, "spec": {"type": "string"}, "diff_percentage": {"type": "number", "format": "double"}, "created_time": {"type": "string", "format": "date-time"}, "created_user_name": {"type": "string"}, "submitted_time": {"type": "string", "format": "date-time"}, "submitted_user_name": {"type": "string"}, "branch_type": {"type": "string"}, "position_id": {"type": "string", "format": "uint64"}, "position_code": {"type": "string"}, "position_name": {"type": "string"}}}, "stocktakeStocktakeBiDetailedResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/stocktakeStocktakeBiDetailed"}}, "total": {"$ref": "#/definitions/stocktakeST_total"}}}, "stocktakeStocktakeDiffCollectReportResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/stocktakeStocktakeDiffCollectReportRow"}}, "total": {"type": "string", "format": "uint64"}}}, "stocktakeStocktakeDiffCollectReportRow": {"type": "object", "properties": {"store_id": {"type": "string", "format": "uint64"}, "store_code": {"type": "string"}, "store_name": {"type": "string"}, "company_code": {"type": "string"}, "product_code": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "diff_quantity": {"type": "number", "format": "double"}, "quantity": {"type": "number", "format": "double"}, "accounting_quantity": {"type": "number", "format": "double"}, "inventory_quantity": {"type": "number", "format": "double"}, "diff_quantity_percentage": {"type": "number", "format": "double"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "unit_rate": {"type": "number", "format": "double"}, "unit_code": {"type": "string"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "accounting_unit_code": {"type": "string"}, "company_name": {"type": "string"}, "accounting_inventory_quantity": {"type": "number", "format": "double"}, "accounting_diff_quantity": {"type": "number", "format": "double"}, "unit_spec": {"type": "string"}, "period_symbol": {"type": "string"}}}, "stocktakeStocktakeDiffReportResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/stocktakeStocktakeDiffReportRow"}}, "total": {"type": "string", "format": "uint64"}}}, "stocktakeStocktakeDiffReportRow": {"type": "object", "properties": {"code": {"type": "string"}, "store_id": {"type": "string", "format": "uint64"}, "store_code": {"type": "string"}, "store_name": {"type": "string"}, "company_code": {"type": "string"}, "product_code": {"type": "string"}, "product_id": {"type": "string", "format": "uint64"}, "product_name": {"type": "string"}, "type": {"type": "string"}, "diff_quantity": {"type": "number", "format": "double"}, "quantity": {"type": "number", "format": "double"}, "accounting_quantity": {"type": "number", "format": "double"}, "inventory_quantity": {"type": "number", "format": "double"}, "diff_quantity_percentage": {"type": "number", "format": "double"}, "target_date": {"type": "string", "format": "date-time"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "unit_rate": {"type": "number", "format": "double"}, "unit_code": {"type": "string"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "accounting_unit_code": {"type": "string"}, "jde_diff_code": {"type": "string"}, "company_name": {"type": "string"}, "accounting_inventory_quantity": {"type": "number", "format": "double"}, "accounting_diff_quantity": {"type": "number", "format": "double"}, "unit_spec": {"type": "string"}, "status": {"type": "string"}, "schedule_code": {"type": "string"}}}, "stocktakeStocktakeDocStatistics": {"type": "object", "properties": {"date": {"type": "string"}, "store_code": {"type": "string"}, "store_name": {"type": "string"}, "stocktake_type": {"type": "string"}, "status": {"type": "string"}, "count": {"type": "number", "format": "double"}, "store_id": {"type": "string", "format": "int64"}}, "title": "营业日|门店|盘点类型|异常类型|数量"}, "stocktakeStocktakeDocStatisticsRequest": {"type": "object", "properties": {"store_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "start_date": {"type": "string", "format": "date-time", "title": "开始时间"}, "end_date": {"type": "string", "format": "date-time", "title": "查询结束日期(此日期结果不包含)"}, "period_group_by": {"$ref": "#/definitions/stocktakePeriodGroupMethod", "title": "日期汇总方式"}, "stocktake_type": {"type": "array", "items": {"type": "string"}}, "status": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "string", "format": "int64"}, "offset": {"type": "string", "format": "int64"}, "order": {"type": "string"}, "sort": {"type": "string"}, "is_wms_store": {"type": "boolean", "format": "boolean"}, "lan": {"type": "string"}}, "title": "搜索条件：门店、营业日、盘点类型、异常类型\n统计类型：未生成未提交未审核其他"}, "stocktakeStocktakeDocStatisticsResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/stocktakeStocktakeDocStatistics"}}, "total": {"type": "string", "format": "int64"}}}, "stocktakeStocktakePositionProducts": {"type": "object", "properties": {"position_id": {"type": "string", "format": "uint64"}, "position_code": {"type": "string"}, "position_name": {"type": "string"}, "products": {"type": "array", "items": {"$ref": "#/definitions/stocktakeStocktakeProduct"}}, "total": {"type": "string", "format": "uint64"}}, "title": "position"}, "stocktakeStocktakeProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "盘点单详情id"}, "partner_id": {"type": "string", "format": "uint64"}, "doc_id": {"type": "string", "format": "uint64", "title": "盘点单id"}, "branch_id": {"type": "string", "format": "uint64", "title": "门店id"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "accounting_quantity": {"type": "number", "format": "double", "title": "核算数量"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "核算单位id"}, "accounting_unit_name": {"type": "string", "title": "核算单位名字"}, "accounting_unit_spec": {"type": "string", "title": "核算单位分类"}, "code": {"type": "string", "title": "盘点单编号"}, "deleted": {"type": "boolean", "format": "boolean"}, "diff_quantity": {"type": "number", "format": "double", "title": "盘点差异量"}, "display_order": {"type": "string", "title": "盘点排序码"}, "extends": {"type": "string"}, "ignored": {"type": "boolean", "format": "boolean"}, "inventory_quantity": {"type": "number", "format": "double", "title": "理论库存"}, "is_system": {"type": "boolean", "format": "boolean"}, "item_number": {"type": "string", "format": "uint64"}, "material_number": {"type": "string", "title": "物料编码"}, "product_code": {"type": "string", "title": "商品编码"}, "product_name": {"type": "string", "title": "商品名字"}, "quantity": {"type": "number", "format": "double", "title": "盘点数量"}, "st_type": {"type": "string", "title": "盘点单类型"}, "storage_type": {"type": "string", "title": "储藏类型"}, "unit_diff_quantity": {"type": "number", "format": "double", "title": "核算差异量"}, "unit_id": {"type": "string", "format": "uint64", "title": "盘点单位id"}, "unit_name": {"type": "string", "title": "盘点单位名字"}, "unit_rate": {"type": "number", "format": "double", "title": "盘点单位比率"}, "unit_spec": {"type": "string", "title": "盘点单位类型"}, "units": {"type": "array", "items": {"$ref": "#/definitions/stocktakeStocktakeProductUnits"}, "title": "盘点单位列表"}, "created_by": {"type": "string", "format": "uint64", "title": "创建者"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新者"}, "target_date": {"type": "string", "format": "date-time", "title": "盘点单日期"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "user_id": {"type": "string", "format": "uint64"}, "status": {"type": "string", "title": "盘点商品状态'INITED','REJECTED','CONFIRMED','FINALIZED','APPROVED'"}, "is_pda": {"type": "boolean", "format": "boolean"}, "product_tags": {"type": "array", "items": {"$ref": "#/definitions/stocktakeStocktakeProductTagName"}, "title": "商品标签明细"}, "is_empty": {"type": "boolean", "format": "boolean"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "is_null": {"type": "boolean", "format": "boolean"}, "tag_quantity": {"type": "number", "format": "double"}, "convert_accounting_quantity": {"type": "number", "format": "double"}, "is_bom": {"type": "boolean", "format": "boolean"}, "allow_stocktake_edit": {"type": "boolean", "format": "boolean"}, "spec": {"type": "string", "title": "商品规格"}, "diff_price": {"type": "number", "format": "double", "title": "损益金额"}, "barcode": {"type": "array", "items": {"type": "string"}}, "category_id": {"type": "string", "format": "uint64", "title": "商品类别id"}, "position_id": {"type": "string", "format": "uint64"}}}, "stocktakeStocktakeProductImportRequest": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "盘点单doc_id"}, "file_name": {"type": "string", "title": "文件名称"}, "file_data": {"type": "string", "title": "文件流"}}, "title": "仓库/门店盘点单商品明细导入请求参数"}, "stocktakeStocktakeProductImportResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean", "title": "导入失败为False,成功True"}, "rows": {"type": "array", "items": {"$ref": "#/definitions/stocktakeProductImportResponseRows"}}, "file_name": {"type": "string"}, "rows_num": {"type": "string", "format": "uint64", "title": "总行数"}, "batch_id": {"type": "string", "format": "uint64", "title": "导入记录id"}}, "title": "仓库/门店盘点单商品明细导入返回参数"}, "stocktakeStocktakeProductTagName": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "doc_id": {"type": "string", "format": "uint64"}, "stp_id": {"type": "string", "format": "uint64"}, "product_id": {"type": "string", "format": "uint64"}, "tag_id": {"type": "string", "format": "uint64"}, "tag_name": {"type": "string"}, "tag_quantity": {"type": "number", "format": "double"}, "accounting_quantity": {"type": "number", "format": "double"}, "unit_id": {"type": "string", "format": "uint64"}, "unit_spec": {"type": "string"}, "unit_name": {"type": "string"}, "unit_rate": {"type": "number", "format": "double"}, "accounting_unit_id": {"type": "string", "format": "uint64"}, "accounting_unit_name": {"type": "string"}, "accounting_unit_spec": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "user_id": {"type": "string", "format": "uint64"}, "created_by": {"type": "string", "format": "uint64"}, "updated_by": {"type": "string", "format": "uint64"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}}}, "stocktakeStocktakeProductUnits": {"type": "object", "properties": {"unit_id": {"type": "string", "format": "uint64"}, "unit_name": {"type": "string"}, "unit_spec": {"type": "string"}, "unit_rate": {"type": "number", "format": "double"}}}, "stocktakeStocktakeSTATUS": {"type": "string", "enum": ["INITED", "STARTED", "CANCELLED", "SUBMITTED", "REJECTED", "APPROVED", "CONFIRMED", "SUBMIT_0", "APPROVE_0"], "default": "INITED", "title": "- INITED: 用户手动创建状态\n - CANCELLED: 用户取消状态\n - APPROVED: 财务核算检查后状态\n - CONFIRMED: 录入盘点单位确定状态\n - SUBMIT_0: 月盘全盘0提交"}, "stocktakeStocktakeTags": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "partner_id": {"type": "string", "format": "uint64"}, "user_id": {"type": "string", "format": "uint64"}, "created_by": {"type": "string", "format": "uint64"}, "updated_by": {"type": "string", "format": "uint64"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "created_name": {"type": "string"}, "updated_name": {"type": "string"}, "branch_id": {"type": "string", "format": "uint64"}, "branch_name": {"type": "string"}}}, "stocktakeStoreDataScope": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/stocktakeScopeStores"}}, "total": {"type": "string", "format": "uint64"}}}, "stocktakeStoreDataScopeRequest": {"type": "object", "properties": {"search": {"type": "string"}, "search_fields": {"type": "string"}, "ids": {"type": "array", "items": {"type": "string", "format": "uint64"}}, "return_fields": {"type": "string"}, "filters": {"type": "string"}, "relation_filters": {"type": "string"}, "limit": {"type": "string", "format": "int64"}, "offset": {"type": "string", "format": "uint64"}, "lan": {"type": "string"}}}, "stocktakeSubmitStocktakeByDocIDRequest": {"type": "object", "properties": {"doc_id": {"type": "string", "format": "uint64", "title": "盘点单doc_id"}, "submit_name": {"type": "string"}, "lan": {"type": "string"}, "all_zero": {"type": "boolean", "format": "boolean", "title": "是否全盘零"}}}, "stocktakeSubmitStocktakeByDocIDResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "stocktakeTagProductBi": {"type": "object", "properties": {"tag_name": {"type": "string"}, "tag_quantity": {"type": "number", "format": "double"}, "tag_unit_name": {"type": "string"}, "tag_uint_rate": {"type": "number", "format": "double"}}}, "stocktakeTagQuantity": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "标签id"}, "tag_id": {"type": "string", "format": "uint64"}, "tag_name": {"type": "string", "title": "标签名"}, "tag_quantity": {"type": "number", "format": "double", "title": "标签名对应数量"}, "unit_id": {"type": "string", "format": "uint64", "title": "标签盘点单位"}}}, "stocktakeUncompleteDocReportResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/UncompleteDocReportResponseUncompleteDocReport"}}, "total": {"type": "string", "format": "uint64"}}}, "stocktakeUpdateStocktakeImportBatchRequest": {"type": "object", "properties": {"batch_id": {"type": "string", "format": "uint64", "title": "导入记录id"}, "status": {"type": "string", "title": "更新状态CANCEL/CONFIRM"}}, "title": "仓库/门店盘点单商品导入状态更新请求参数"}, "stocktakeUpdateStocktakeImportBatchResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean", "title": "更新失败为False,成功True"}, "doc_id": {"type": "string", "format": "uint64", "title": "盘点单号"}}, "title": "仓库/门店盘点单商品导入状态更新返回参数"}}}