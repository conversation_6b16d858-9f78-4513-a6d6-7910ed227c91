# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from manufactory import material_convert_pb2 as manufactory_dot_material__convert__pb2


class ManufactoryMaterialConvertStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateMaterialConvert = channel.unary_unary(
        '/manufactory.ManufactoryMaterialConvert/CreateMaterialConvert',
        request_serializer=manufactory_dot_material__convert__pb2.CreateMaterialConvertRequest.SerializeToString,
        response_deserializer=manufactory_dot_material__convert__pb2.CreateMaterialConvertResponse.FromString,
        )
    self.ListMaterialConvert = channel.unary_unary(
        '/manufactory.ManufactoryMaterialConvert/ListMaterialConvert',
        request_serializer=manufactory_dot_material__convert__pb2.ListMaterialConvertRequest.SerializeToString,
        response_deserializer=manufactory_dot_material__convert__pb2.ListMaterialConvertResponse.FromString,
        )
    self.GetMaterialConvertDetail = channel.unary_unary(
        '/manufactory.ManufactoryMaterialConvert/GetMaterialConvertDetail',
        request_serializer=manufactory_dot_material__convert__pb2.GetMaterialConvertDetailRequest.SerializeToString,
        response_deserializer=manufactory_dot_material__convert__pb2.GetMaterialConvertDetailResponse.FromString,
        )
    self.UpdateMaterialConvert = channel.unary_unary(
        '/manufactory.ManufactoryMaterialConvert/UpdateMaterialConvert',
        request_serializer=manufactory_dot_material__convert__pb2.UpdateMaterialConvertRequest.SerializeToString,
        response_deserializer=manufactory_dot_material__convert__pb2.UpdateMaterialConvertResponse.FromString,
        )


class ManufactoryMaterialConvertServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def CreateMaterialConvert(self, request, context):
    """创建物料转换单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListMaterialConvert(self, request, context):
    """物料转换单列表查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetMaterialConvertDetail(self, request, context):
    """查询物料转换单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateMaterialConvert(self, request, context):
    """更新物料转换单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_ManufactoryMaterialConvertServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateMaterialConvert': grpc.unary_unary_rpc_method_handler(
          servicer.CreateMaterialConvert,
          request_deserializer=manufactory_dot_material__convert__pb2.CreateMaterialConvertRequest.FromString,
          response_serializer=manufactory_dot_material__convert__pb2.CreateMaterialConvertResponse.SerializeToString,
      ),
      'ListMaterialConvert': grpc.unary_unary_rpc_method_handler(
          servicer.ListMaterialConvert,
          request_deserializer=manufactory_dot_material__convert__pb2.ListMaterialConvertRequest.FromString,
          response_serializer=manufactory_dot_material__convert__pb2.ListMaterialConvertResponse.SerializeToString,
      ),
      'GetMaterialConvertDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetMaterialConvertDetail,
          request_deserializer=manufactory_dot_material__convert__pb2.GetMaterialConvertDetailRequest.FromString,
          response_serializer=manufactory_dot_material__convert__pb2.GetMaterialConvertDetailResponse.SerializeToString,
      ),
      'UpdateMaterialConvert': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateMaterialConvert,
          request_deserializer=manufactory_dot_material__convert__pb2.UpdateMaterialConvertRequest.FromString,
          response_serializer=manufactory_dot_material__convert__pb2.UpdateMaterialConvertResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'manufactory.ManufactoryMaterialConvert', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
