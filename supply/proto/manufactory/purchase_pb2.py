# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: manufactory/purchase.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='manufactory/purchase.proto',
  package='manufactory',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1amanufactory/purchase.proto\x12\x0bmanufactory\x1a\x1cgoogle/api/annotations.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xf2\x01\n\x14GetPurchaseBiRequest\x12\r\n\x05s_ids\x18\x01 \x03(\x04\x12\x0e\n\x06wh_ids\x18\x02 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x03 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x04 \x03(\x04\x12.\n\nstart_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x07 \x01(\x03\x12\x0e\n\x06offset\x18\x08 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\t \x01(\t\"g\n\x15GetPurchaseBiResponse\x12+\n\x04rows\x18\x01 \x03(\x0b\x32\x1d.manufactory.PurchaseDetailed\x12!\n\x05total\x18\x02 \x01(\x0b\x32\x12.manufactory.Total\"0\n\x05Total\x12\r\n\x05\x63ount\x18\x01 \x01(\x01\x12\x18\n\x10sum_all_quantity\x18\x02 \x01(\x01\"\xa2\x05\n\x10PurchaseDetailed\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x01 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x02 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x03 \x01(\x04\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x15\n\rcategory_name\x18\x05 \x01(\t\x12\x17\n\x0f\x63\x61tegory_parent\x18\x06 \x01(\t\x12\x1a\n\x12\x63onfirmed_quantity\x18\x07 \x01(\x01\x12\x12\n\nproduct_id\x18\x08 \x01(\x04\x12\x14\n\x0cproduct_code\x18\t \x01(\t\x12\x14\n\x0cproduct_name\x18\n \x01(\t\x12\x14\n\x0cproduct_spec\x18\x0b \x01(\t\x12\x19\n\x11purchase_quantity\x18\x0c \x01(\x01\x12\r\n\x05wh_id\x18\x10 \x01(\x04\x12\x0f\n\x07wh_code\x18\x11 \x01(\t\x12\x0f\n\x07wh_name\x18\x12 \x01(\t\x12\x0f\n\x07unit_id\x18\x13 \x01(\x04\x12\x11\n\tunit_name\x18\x14 \x01(\t\x12\x12\n\norder_date\x18\x15 \x01(\t\x12\x14\n\x0c\x61rrival_date\x18\x16 \x01(\t\x12\x12\n\norder_code\x18\x17 \x01(\t\x12\x10\n\x08order_id\x18\x19 \x01(\x04\x12\n\n\x02id\x18\x1a \x01(\x04\x12\x11\n\tvendor_id\x18\x1b \x01(\x04\x12\x13\n\x0bvendor_code\x18\x1c \x01(\t\x12\x13\n\x0bvendor_name\x18\x1d \x01(\t\x12\x11\n\tprice_tax\x18\x1e \x01(\x01\x12\r\n\x05price\x18\x1f \x01(\x01\x12\x10\n\x08tax_rate\x18  \x01(\x01\x12\x0b\n\x03tax\x18! \x01(\x01\x12\x16\n\x0eproduct_status\x18\" \x01(\t\x12\x14\n\x0corder_status\x18# \x01(\t\x12\x0e\n\x06remark\x18$ \x01(\t\"\xee\x02\n\x1a\x43reatePurchaseOrderRequest\x12.\n\norder_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06remark\x18\x03 \x01(\t\x12\x13\n\x0breceived_by\x18\x04 \x01(\x04\x12\x13\n\x0bsupplier_id\x18\x05 \x01(\x04\x12\x15\n\rsupplier_name\x18\x06 \x01(\t\x12\x12\n\norder_type\x18\x07 \x01(\t\x12\x15\n\rpurchase_type\x18\x08 \x01(\t\x12\x17\n\x0fpurchase_reason\x18\t \x01(\t\x12\x30\n\rproduct_items\x18\n \x03(\x0b\x32\x19.manufactory.product_item\x12\x13\n\x0b\x62ranch_type\x18\x0b \x01(\t\x12\x12\n\nrequest_id\x18\x0c \x01(\x04\"\xe4\x01\n\x0cproduct_item\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x10\n\x08quantity\x18\x02 \x01(\x01\x12\x19\n\x11\x64istribution_type\x18\x03 \x01(\t\x12\x11\n\tprice_tax\x18\x04 \x01(\x01\x12\x18\n\x10purchase_unit_id\x18\x05 \x01(\x04\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x06 \x01(\x04\x12\x11\n\tunit_rate\x18\x07 \x01(\x01\x12\x10\n\x08tax_rate\x18\x08 \x01(\x01\x12\x16\n\x0epurchase_price\x18\t \x01(\x01\x12\r\n\x05price\x18\n \x01(\x01\"/\n\x1b\x43reatePurchaseOrderResponse\x12\x10\n\x08order_id\x18\x01 \x01(\x04\"\x93\x03\n\x18ListPurchaseOrderRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\norder_code\x18\x03 \x01(\t\x12\x14\n\x0corder_status\x18\x04 \x01(\t\x12\x12\n\norder_type\x18\x05 \x01(\t\x12\x15\n\rpurchase_type\x18\x06 \x01(\t\x12\x14\n\x0csupplier_ids\x18\x07 \x03(\x04\x12\x14\n\x0creceived_ids\x18\x08 \x03(\x04\x12\x17\n\x0fpurchase_reason\x18\t \x01(\t\x12\r\n\x05limit\x18\n \x01(\x03\x12\x0e\n\x06offset\x18\x0b \x01(\x04\x12\x15\n\rinclude_total\x18\x0c \x01(\x08\x12\x11\n\tsort_type\x18\r \x01(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x0f \x01(\t\x12\x13\n\x0bproduct_ids\x18\x10 \x03(\x04\"\xb2\x04\n\x10PurchaseOrderRow\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x12\n\norder_code\x18\x02 \x01(\t\x12\x14\n\x0corder_status\x18\x03 \x01(\t\x12.\n\norder_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0breceived_by\x18\x05 \x01(\x04\x12\x15\n\rreceived_name\x18\x06 \x01(\t\x12\x12\n\norder_type\x18\x07 \x01(\t\x12\x13\n\x0bsupplier_id\x18\x08 \x01(\x04\x12\x15\n\rsupplier_name\x18\t \x01(\t\x12\x15\n\rsum_price_tax\x18\n \x01(\x01\x12\x0f\n\x07sum_tax\x18\x0b \x01(\x01\x12\x11\n\tsum_price\x18\x0c \x01(\x01\x12\x12\n\nsum_tariff\x18\r \x01(\x01\x12\x12\n\ncreated_by\x18\x0e \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x0f \x01(\t\x12.\n\ncreated_at\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\x11 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x12 \x01(\t\x12.\n\nupdated_at\x18\x13 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\x14 \x01(\t\x12\x17\n\x0fpurchase_reason\x18\x15 \x01(\t\x12\x15\n\rpurchase_type\x18\x16 \x01(\t\"W\n\x19ListPurchaseOrderResponse\x12+\n\x04rows\x18\x01 \x03(\x0b\x32\x1d.manufactory.PurchaseOrderRow\x12\r\n\x05total\x18\x02 \x01(\x04\"<\n\x18\x43hangeOrderStatusRequest\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\"B\n\x19\x43hangeOrderStatusResponse\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\"\xc0\x02\n\x1aUpdatePurchaseOrderRequest\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12.\n\norder_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06remark\x18\x04 \x01(\t\x12\x13\n\x0breceived_by\x18\x05 \x01(\x04\x12\x13\n\x0bsupplier_id\x18\x06 \x01(\x04\x12\x12\n\norder_type\x18\x07 \x01(\t\x12\x15\n\rpurchase_type\x18\x08 \x01(\t\x12\x17\n\x0fpurchase_reason\x18\t \x01(\t\x12\x30\n\rproduct_items\x18\n \x03(\x0b\x32\x19.manufactory.product_item\"D\n\x1bUpdatePurchaseOrderResponse\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\"-\n\x19GetOrderDetailByIdRequest\x12\x10\n\x08order_id\x18\x01 \x01(\x04\"\xa0\x04\n\x0fOrderProductRow\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\ncreated_by\x18\x02 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x03 \x01(\t\x12\x12\n\nupdated_by\x18\x04 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x05 \x01(\t\x12\x12\n\nproduct_id\x18\x06 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x07 \x01(\t\x12\x14\n\x0cproduct_name\x18\x08 \x01(\t\x12\x1b\n\x13product_category_id\x18\t \x01(\x04\x12\x14\n\x0cproduct_type\x18\n \x01(\t\x12\x18\n\x10purchase_unit_id\x18\x0b \x01(\x04\x12\x1a\n\x12purchase_unit_name\x18\x0c \x01(\t\x12\x1a\n\x12purchase_unit_spec\x18\r \x01(\t\x12\x11\n\tsale_type\x18\x0e \x01(\t\x12\x0c\n\x04spec\x18\x0f \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x10 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x11 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x12 \x01(\t\x12\x10\n\x08quantity\x18\x13 \x01(\x01\x12\x11\n\tprice_tax\x18\x14 \x01(\x01\x12\x11\n\tunit_rate\x18\x15 \x01(\x01\x12\x10\n\x08tax_rate\x18\x16 \x01(\x01\x12\x16\n\x0epurchase_price\x18\x17 \x01(\x01\x12\r\n\x05price\x18\x18 \x01(\x01\"\xf3\x04\n\x1aGetOrderDetailByIdResponse\x12\x10\n\x08order_id\x18\x01 \x01(\x04\x12\x12\n\norder_code\x18\x02 \x01(\t\x12\x14\n\x0corder_status\x18\x03 \x01(\t\x12.\n\norder_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0breceived_by\x18\x05 \x01(\x04\x12\x15\n\rreceived_name\x18\x06 \x01(\t\x12\x12\n\norder_type\x18\x07 \x01(\t\x12\x13\n\x0bsupplier_id\x18\x08 \x01(\x04\x12\x15\n\rsupplier_name\x18\t \x01(\t\x12\x12\n\ncreated_by\x18\n \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x0b \x01(\t\x12\x12\n\nupdated_by\x18\x0c \x01(\x04\x12\x14\n\x0cupdated_name\x18\r \x01(\t\x12\x15\n\rpurchase_type\x18\x0e \x01(\t\x12\x17\n\x0fpurchase_reason\x18\x0f \x01(\t\x12\x0e\n\x06remark\x18\x10 \x01(\t\x12.\n\x08products\x18\x11 \x03(\x0b\x32\x1c.manufactory.OrderProductRow\x12\x15\n\rtotal_product\x18\x12 \x01(\x04\x12.\n\ncreated_at\x18\x13 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\x15 \x01(\t\x12\x15\n\rsum_price_tax\x18\x16 \x01(\x01\x12\x16\n\x0etotal_quantity\x18\x17 \x01(\x01\"\xad\x01\n\x1bGetProductListByWHIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1c\n\x14product_category_ids\x18\x02 \x03(\x04\x12\x1b\n\x13return_all_products\x18\x03 \x01(\x08\x12 \n\x18return_metadata_products\x18\x04 \x01(\x08\x12\x0e\n\x06search\x18\x05 \x01(\t\x12\x15\n\rsearch_fields\x18\x06 \x01(\t\"\x93\x02\n\x05MUnit\x12\n\n\x02id\x18\x01 \x01(\t\x12\x12\n\npartner_id\x18\x02 \x01(\t\x12\x10\n\x08scope_id\x18\x03 \x01(\t\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x0f\n\x07tp_code\x18\x06 \x01(\t\x12\x0f\n\x07updated\x18\x07 \x01(\t\x12\x0c\n\x04rate\x18\x08 \x01(\x01\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\t \x01(\x08\x12\r\n\x05order\x18\n \x01(\x08\x12\x10\n\x08purchase\x18\x0b \x01(\x08\x12\r\n\x05sales\x18\x0c \x01(\x08\x12\x11\n\tstocktake\x18\r \x01(\x08\x12\x0b\n\x03\x62om\x18\x0e \x01(\x08\x12\x10\n\x08transfer\x18\x0f \x01(\x08\x12\x19\n\x11\x64\x65\x66\x61ult_stocktake\x18\x10 \x01(\x08\"\xf2\x04\n\x08MProduct\x12\n\n\x02id\x18\x01 \x01(\t\x12\x12\n\npartner_id\x18\x02 \x01(\t\x12\x10\n\x08scope_id\x18\x03 \x01(\t\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x13\n\x0bsecond_code\x18\x06 \x01(\t\x12\x11\n\tsale_type\x18\x07 \x01(\t\x12\x14\n\x0cproduct_type\x18\x08 \x01(\t\x12\x10\n\x08\x62om_type\x18\t \x01(\t\x12\x14\n\x0cstorage_type\x18\n \x01(\t\x12\x0e\n\x06status\x18\x0b \x01(\t\x12\r\n\x05\x61lias\x18\x0c \x01(\t\x12\x10\n\x08\x63\x61tegory\x18\r \x01(\t\x12\x0f\n\x07updated\x18\x0e \x01(\t\x12(\n\x07\x65xtends\x18\x0f \x01(\x0b\x32\x17.google.protobuf.Struct\x12,\n\x0b\x65xtend_code\x18\x10 \x01(\x0b\x32\x17.google.protobuf.Struct\x12!\n\x05units\x18\x11 \x03(\x0b\x32\x12.manufactory.MUnit\x12\x12\n\nmodel_code\x18\x12 \x01(\t\x12\x12\n\nmodel_name\x18\x13 \x01(\t\x12\'\n\x1f\x64\x65\x66\x61ult_receiving_deviation_min\x18\x14 \x01(\x05\x12\'\n\x1f\x64\x65\x66\x61ult_receiving_deviation_max\x18\x15 \x01(\x05\x12&\n\x1e\x64\x65\x66\x61ult_purchase_deviation_min\x18\x16 \x01(\x05\x12&\n\x1e\x64\x65\x66\x61ult_purchase_deviation_max\x18\x17 \x01(\x05\x12\x14\n\x0cledger_class\x18\x18 \x01(\t\x12\x15\n\rcategory_name\x18\x19 \x01(\t\"R\n\x1cGetProductListByWHIdResponse\x12#\n\x04rows\x18\x01 \x03(\x0b\x32\x15.manufactory.MProduct\x12\r\n\x05total\x18\x02 \x01(\x05\"0\n\x1bGetOrdersDetailByIdsRequest\x12\x11\n\torder_ids\x18\x01 \x03(\x04\"U\n\x1cGetOrdersDetailByIdsResponse\x12\x35\n\x04rows\x18\x01 \x03(\x0b\x32\'.manufactory.GetOrderDetailByIdResponse2\xf9\t\n\x13ManufactoryPurchase\x12\x9e\x01\n\x13\x43reatePurchaseOrder\x12\'.manufactory.CreatePurchaseOrderRequest\x1a(.manufactory.CreatePurchaseOrderResponse\"4\x82\xd3\xe4\x93\x02.\")/api/v2/manufactory/create/purchase/order:\x01*\x12\x93\x01\n\x11ListPurchaseOrder\x12%.manufactory.ListPurchaseOrderRequest\x1a&.manufactory.ListPurchaseOrderResponse\"/\x82\xd3\xe4\x93\x02)\x12\'/api/v2/manufactory/list/purchase/order\x12\x9c\x01\n\x11\x43hangeOrderStatus\x12%.manufactory.ChangeOrderStatusRequest\x1a&.manufactory.ChangeOrderStatusResponse\"8\x82\xd3\xe4\x93\x02\x32\x1a-/api/v2/manufactory/order/{status}/{order_id}:\x01*\x12\x9e\x01\n\x13UpdatePurchaseOrder\x12\'.manufactory.UpdatePurchaseOrderRequest\x1a(.manufactory.UpdatePurchaseOrderResponse\"4\x82\xd3\xe4\x93\x02.\")/api/v2/manufactory/update/purchase/order:\x01*\x12\x9a\x01\n\x12GetOrderDetailById\x12&.manufactory.GetOrderDetailByIdRequest\x1a\'.manufactory.GetOrderDetailByIdResponse\"3\x82\xd3\xe4\x93\x02-\x12+/api/v2/manufactory/order/detail/{order_id}\x12\x9a\x01\n\x14GetProductListByWHId\x12(.manufactory.GetProductListByWHIdRequest\x1a).manufactory.GetProductListByWHIdResponse\"-\x82\xd3\xe4\x93\x02\'\x12%/api/v2/manufactory/product/list/{id}\x12\x88\x01\n\rGetPurchaseBi\x12!.manufactory.GetPurchaseBiRequest\x1a\".manufactory.GetPurchaseBiResponse\"0\x82\xd3\xe4\x93\x02*\x12(/api/v2/manufactory/purchase/bi/detailed\x12\xa5\x01\n\x14GetOrdersDetailByIds\x12(.manufactory.GetOrdersDetailByIdsRequest\x1a).manufactory.GetOrdersDetailByIdsResponse\"8\x82\xd3\xe4\x93\x02\x32\"-/api/v2/manufactory/list/purchase/order/print:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_struct__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_GETPURCHASEBIREQUEST = _descriptor.Descriptor(
  name='GetPurchaseBiRequest',
  full_name='manufactory.GetPurchaseBiRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='s_ids', full_name='manufactory.GetPurchaseBiRequest.s_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='wh_ids', full_name='manufactory.GetPurchaseBiRequest.wh_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='manufactory.GetPurchaseBiRequest.product_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='manufactory.GetPurchaseBiRequest.category_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='manufactory.GetPurchaseBiRequest.start_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='manufactory.GetPurchaseBiRequest.end_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.GetPurchaseBiRequest.limit', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.GetPurchaseBiRequest.offset', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.GetPurchaseBiRequest.branch_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=137,
  serialized_end=379,
)


_GETPURCHASEBIRESPONSE = _descriptor.Descriptor(
  name='GetPurchaseBiResponse',
  full_name='manufactory.GetPurchaseBiResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.GetPurchaseBiResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.GetPurchaseBiResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=381,
  serialized_end=484,
)


_TOTAL = _descriptor.Descriptor(
  name='Total',
  full_name='manufactory.Total',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='count', full_name='manufactory.Total.count', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_all_quantity', full_name='manufactory.Total.sum_all_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=486,
  serialized_end=534,
)


_PURCHASEDETAILED = _descriptor.Descriptor(
  name='PurchaseDetailed',
  full_name='manufactory.PurchaseDetailed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='manufactory.PurchaseDetailed.accounting_unit_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.PurchaseDetailed.accounting_unit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='manufactory.PurchaseDetailed.category_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='manufactory.PurchaseDetailed.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='manufactory.PurchaseDetailed.category_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_parent', full_name='manufactory.PurchaseDetailed.category_parent', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='manufactory.PurchaseDetailed.confirmed_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.PurchaseDetailed.product_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.PurchaseDetailed.product_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.PurchaseDetailed.product_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='manufactory.PurchaseDetailed.product_spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_quantity', full_name='manufactory.PurchaseDetailed.purchase_quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='wh_id', full_name='manufactory.PurchaseDetailed.wh_id', index=12,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='wh_code', full_name='manufactory.PurchaseDetailed.wh_code', index=13,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='wh_name', full_name='manufactory.PurchaseDetailed.wh_name', index=14,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.PurchaseDetailed.unit_id', index=15,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.PurchaseDetailed.unit_name', index=16,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date', full_name='manufactory.PurchaseDetailed.order_date', index=17,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='manufactory.PurchaseDetailed.arrival_date', index=18,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='manufactory.PurchaseDetailed.order_code', index=19,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_id', full_name='manufactory.PurchaseDetailed.order_id', index=20,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.PurchaseDetailed.id', index=21,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_id', full_name='manufactory.PurchaseDetailed.vendor_id', index=22,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_code', full_name='manufactory.PurchaseDetailed.vendor_code', index=23,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_name', full_name='manufactory.PurchaseDetailed.vendor_name', index=24,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_tax', full_name='manufactory.PurchaseDetailed.price_tax', index=25,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='manufactory.PurchaseDetailed.price', index=26,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='manufactory.PurchaseDetailed.tax_rate', index=27,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax', full_name='manufactory.PurchaseDetailed.tax', index=28,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_status', full_name='manufactory.PurchaseDetailed.product_status', index=29,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_status', full_name='manufactory.PurchaseDetailed.order_status', index=30,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.PurchaseDetailed.remark', index=31,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=537,
  serialized_end=1211,
)


_CREATEPURCHASEORDERREQUEST = _descriptor.Descriptor(
  name='CreatePurchaseOrderRequest',
  full_name='manufactory.CreatePurchaseOrderRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_date', full_name='manufactory.CreatePurchaseOrderRequest.order_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='manufactory.CreatePurchaseOrderRequest.arrival_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.CreatePurchaseOrderRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='manufactory.CreatePurchaseOrderRequest.received_by', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_id', full_name='manufactory.CreatePurchaseOrderRequest.supplier_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_name', full_name='manufactory.CreatePurchaseOrderRequest.supplier_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='manufactory.CreatePurchaseOrderRequest.order_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_type', full_name='manufactory.CreatePurchaseOrderRequest.purchase_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_reason', full_name='manufactory.CreatePurchaseOrderRequest.purchase_reason', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_items', full_name='manufactory.CreatePurchaseOrderRequest.product_items', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.CreatePurchaseOrderRequest.branch_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.CreatePurchaseOrderRequest.request_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1214,
  serialized_end=1580,
)


_PRODUCT_ITEM = _descriptor.Descriptor(
  name='product_item',
  full_name='manufactory.product_item',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.product_item.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.product_item.quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='manufactory.product_item.distribution_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_tax', full_name='manufactory.product_item.price_tax', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_unit_id', full_name='manufactory.product_item.purchase_unit_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='manufactory.product_item.accounting_unit_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='manufactory.product_item.unit_rate', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='manufactory.product_item.tax_rate', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_price', full_name='manufactory.product_item.purchase_price', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='manufactory.product_item.price', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1583,
  serialized_end=1811,
)


_CREATEPURCHASEORDERRESPONSE = _descriptor.Descriptor(
  name='CreatePurchaseOrderResponse',
  full_name='manufactory.CreatePurchaseOrderResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='manufactory.CreatePurchaseOrderResponse.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1813,
  serialized_end=1860,
)


_LISTPURCHASEORDERREQUEST = _descriptor.Descriptor(
  name='ListPurchaseOrderRequest',
  full_name='manufactory.ListPurchaseOrderRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='manufactory.ListPurchaseOrderRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='manufactory.ListPurchaseOrderRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='manufactory.ListPurchaseOrderRequest.order_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_status', full_name='manufactory.ListPurchaseOrderRequest.order_status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='manufactory.ListPurchaseOrderRequest.order_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_type', full_name='manufactory.ListPurchaseOrderRequest.purchase_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_ids', full_name='manufactory.ListPurchaseOrderRequest.supplier_ids', index=6,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_ids', full_name='manufactory.ListPurchaseOrderRequest.received_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_reason', full_name='manufactory.ListPurchaseOrderRequest.purchase_reason', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.ListPurchaseOrderRequest.limit', index=9,
      number=10, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.ListPurchaseOrderRequest.offset', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='manufactory.ListPurchaseOrderRequest.include_total', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort_type', full_name='manufactory.ListPurchaseOrderRequest.sort_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='manufactory.ListPurchaseOrderRequest.sort', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.ListPurchaseOrderRequest.branch_type', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='manufactory.ListPurchaseOrderRequest.product_ids', index=15,
      number=16, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1863,
  serialized_end=2266,
)


_PURCHASEORDERROW = _descriptor.Descriptor(
  name='PurchaseOrderRow',
  full_name='manufactory.PurchaseOrderRow',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='manufactory.PurchaseOrderRow.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='manufactory.PurchaseOrderRow.order_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_status', full_name='manufactory.PurchaseOrderRow.order_status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date', full_name='manufactory.PurchaseOrderRow.order_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='manufactory.PurchaseOrderRow.received_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_name', full_name='manufactory.PurchaseOrderRow.received_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='manufactory.PurchaseOrderRow.order_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_id', full_name='manufactory.PurchaseOrderRow.supplier_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_name', full_name='manufactory.PurchaseOrderRow.supplier_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price_tax', full_name='manufactory.PurchaseOrderRow.sum_price_tax', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_tax', full_name='manufactory.PurchaseOrderRow.sum_tax', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price', full_name='manufactory.PurchaseOrderRow.sum_price', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_tariff', full_name='manufactory.PurchaseOrderRow.sum_tariff', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.PurchaseOrderRow.created_by', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.PurchaseOrderRow.created_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.PurchaseOrderRow.created_at', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.PurchaseOrderRow.updated_by', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.PurchaseOrderRow.updated_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.PurchaseOrderRow.updated_at', index=18,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.PurchaseOrderRow.branch_type', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_reason', full_name='manufactory.PurchaseOrderRow.purchase_reason', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_type', full_name='manufactory.PurchaseOrderRow.purchase_type', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2269,
  serialized_end=2831,
)


_LISTPURCHASEORDERRESPONSE = _descriptor.Descriptor(
  name='ListPurchaseOrderResponse',
  full_name='manufactory.ListPurchaseOrderResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.ListPurchaseOrderResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.ListPurchaseOrderResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2833,
  serialized_end=2920,
)


_CHANGEORDERSTATUSREQUEST = _descriptor.Descriptor(
  name='ChangeOrderStatusRequest',
  full_name='manufactory.ChangeOrderStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='manufactory.ChangeOrderStatusRequest.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.ChangeOrderStatusRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2922,
  serialized_end=2982,
)


_CHANGEORDERSTATUSRESPONSE = _descriptor.Descriptor(
  name='ChangeOrderStatusResponse',
  full_name='manufactory.ChangeOrderStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='manufactory.ChangeOrderStatusResponse.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='manufactory.ChangeOrderStatusResponse.description', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2984,
  serialized_end=3050,
)


_UPDATEPURCHASEORDERREQUEST = _descriptor.Descriptor(
  name='UpdatePurchaseOrderRequest',
  full_name='manufactory.UpdatePurchaseOrderRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='manufactory.UpdatePurchaseOrderRequest.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date', full_name='manufactory.UpdatePurchaseOrderRequest.order_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='manufactory.UpdatePurchaseOrderRequest.arrival_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.UpdatePurchaseOrderRequest.remark', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='manufactory.UpdatePurchaseOrderRequest.received_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_id', full_name='manufactory.UpdatePurchaseOrderRequest.supplier_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='manufactory.UpdatePurchaseOrderRequest.order_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_type', full_name='manufactory.UpdatePurchaseOrderRequest.purchase_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_reason', full_name='manufactory.UpdatePurchaseOrderRequest.purchase_reason', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_items', full_name='manufactory.UpdatePurchaseOrderRequest.product_items', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3053,
  serialized_end=3373,
)


_UPDATEPURCHASEORDERRESPONSE = _descriptor.Descriptor(
  name='UpdatePurchaseOrderResponse',
  full_name='manufactory.UpdatePurchaseOrderResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='manufactory.UpdatePurchaseOrderResponse.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='manufactory.UpdatePurchaseOrderResponse.description', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3375,
  serialized_end=3443,
)


_GETORDERDETAILBYIDREQUEST = _descriptor.Descriptor(
  name='GetOrderDetailByIdRequest',
  full_name='manufactory.GetOrderDetailByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='manufactory.GetOrderDetailByIdRequest.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3445,
  serialized_end=3490,
)


_ORDERPRODUCTROW = _descriptor.Descriptor(
  name='OrderProductRow',
  full_name='manufactory.OrderProductRow',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.OrderProductRow.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.OrderProductRow.created_by', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.OrderProductRow.created_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.OrderProductRow.updated_by', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.OrderProductRow.updated_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.OrderProductRow.product_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.OrderProductRow.product_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.OrderProductRow.product_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_category_id', full_name='manufactory.OrderProductRow.product_category_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='manufactory.OrderProductRow.product_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_unit_id', full_name='manufactory.OrderProductRow.purchase_unit_id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_unit_name', full_name='manufactory.OrderProductRow.purchase_unit_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_unit_spec', full_name='manufactory.OrderProductRow.purchase_unit_spec', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sale_type', full_name='manufactory.OrderProductRow.sale_type', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='manufactory.OrderProductRow.spec', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='manufactory.OrderProductRow.accounting_unit_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.OrderProductRow.accounting_unit_name', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='manufactory.OrderProductRow.accounting_unit_spec', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.OrderProductRow.quantity', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_tax', full_name='manufactory.OrderProductRow.price_tax', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='manufactory.OrderProductRow.unit_rate', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='manufactory.OrderProductRow.tax_rate', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_price', full_name='manufactory.OrderProductRow.purchase_price', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='manufactory.OrderProductRow.price', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3493,
  serialized_end=4037,
)


_GETORDERDETAILBYIDRESPONSE = _descriptor.Descriptor(
  name='GetOrderDetailByIdResponse',
  full_name='manufactory.GetOrderDetailByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_id', full_name='manufactory.GetOrderDetailByIdResponse.order_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='manufactory.GetOrderDetailByIdResponse.order_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_status', full_name='manufactory.GetOrderDetailByIdResponse.order_status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date', full_name='manufactory.GetOrderDetailByIdResponse.order_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='manufactory.GetOrderDetailByIdResponse.received_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_name', full_name='manufactory.GetOrderDetailByIdResponse.received_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='manufactory.GetOrderDetailByIdResponse.order_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_id', full_name='manufactory.GetOrderDetailByIdResponse.supplier_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplier_name', full_name='manufactory.GetOrderDetailByIdResponse.supplier_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.GetOrderDetailByIdResponse.created_by', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.GetOrderDetailByIdResponse.created_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.GetOrderDetailByIdResponse.updated_by', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.GetOrderDetailByIdResponse.updated_name', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_type', full_name='manufactory.GetOrderDetailByIdResponse.purchase_type', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_reason', full_name='manufactory.GetOrderDetailByIdResponse.purchase_reason', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.GetOrderDetailByIdResponse.remark', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='manufactory.GetOrderDetailByIdResponse.products', index=16,
      number=17, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_product', full_name='manufactory.GetOrderDetailByIdResponse.total_product', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.GetOrderDetailByIdResponse.created_at', index=18,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.GetOrderDetailByIdResponse.updated_at', index=19,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='manufactory.GetOrderDetailByIdResponse.branch_type', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price_tax', full_name='manufactory.GetOrderDetailByIdResponse.sum_price_tax', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_quantity', full_name='manufactory.GetOrderDetailByIdResponse.total_quantity', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4040,
  serialized_end=4667,
)


_GETPRODUCTLISTBYWHIDREQUEST = _descriptor.Descriptor(
  name='GetProductListByWHIdRequest',
  full_name='manufactory.GetProductListByWHIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.GetProductListByWHIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_category_ids', full_name='manufactory.GetProductListByWHIdRequest.product_category_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_all_products', full_name='manufactory.GetProductListByWHIdRequest.return_all_products', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_metadata_products', full_name='manufactory.GetProductListByWHIdRequest.return_metadata_products', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='manufactory.GetProductListByWHIdRequest.search', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='manufactory.GetProductListByWHIdRequest.search_fields', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4670,
  serialized_end=4843,
)


_MUNIT = _descriptor.Descriptor(
  name='MUnit',
  full_name='manufactory.MUnit',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.MUnit.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.MUnit.partner_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='manufactory.MUnit.scope_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='manufactory.MUnit.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.MUnit.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tp_code', full_name='manufactory.MUnit.tp_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='manufactory.MUnit.updated', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='manufactory.MUnit.rate', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default', full_name='manufactory.MUnit.default', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='manufactory.MUnit.order', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase', full_name='manufactory.MUnit.purchase', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales', full_name='manufactory.MUnit.sales', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='manufactory.MUnit.stocktake', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom', full_name='manufactory.MUnit.bom', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer', full_name='manufactory.MUnit.transfer', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default_stocktake', full_name='manufactory.MUnit.default_stocktake', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4846,
  serialized_end=5121,
)


_MPRODUCT = _descriptor.Descriptor(
  name='MProduct',
  full_name='manufactory.MProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.MProduct.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.MProduct.partner_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='manufactory.MProduct.scope_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='manufactory.MProduct.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.MProduct.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='second_code', full_name='manufactory.MProduct.second_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sale_type', full_name='manufactory.MProduct.sale_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='manufactory.MProduct.product_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_type', full_name='manufactory.MProduct.bom_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='manufactory.MProduct.storage_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.MProduct.status', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='alias', full_name='manufactory.MProduct.alias', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category', full_name='manufactory.MProduct.category', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='manufactory.MProduct.updated', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='manufactory.MProduct.extends', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extend_code', full_name='manufactory.MProduct.extend_code', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='manufactory.MProduct.units', index=16,
      number=17, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_code', full_name='manufactory.MProduct.model_code', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_name', full_name='manufactory.MProduct.model_name', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default_receiving_deviation_min', full_name='manufactory.MProduct.default_receiving_deviation_min', index=19,
      number=20, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default_receiving_deviation_max', full_name='manufactory.MProduct.default_receiving_deviation_max', index=20,
      number=21, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default_purchase_deviation_min', full_name='manufactory.MProduct.default_purchase_deviation_min', index=21,
      number=22, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default_purchase_deviation_max', full_name='manufactory.MProduct.default_purchase_deviation_max', index=22,
      number=23, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ledger_class', full_name='manufactory.MProduct.ledger_class', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='manufactory.MProduct.category_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5124,
  serialized_end=5750,
)


_GETPRODUCTLISTBYWHIDRESPONSE = _descriptor.Descriptor(
  name='GetProductListByWHIdResponse',
  full_name='manufactory.GetProductListByWHIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.GetProductListByWHIdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.GetProductListByWHIdResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5752,
  serialized_end=5834,
)


_GETORDERSDETAILBYIDSREQUEST = _descriptor.Descriptor(
  name='GetOrdersDetailByIdsRequest',
  full_name='manufactory.GetOrdersDetailByIdsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_ids', full_name='manufactory.GetOrdersDetailByIdsRequest.order_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5836,
  serialized_end=5884,
)


_GETORDERSDETAILBYIDSRESPONSE = _descriptor.Descriptor(
  name='GetOrdersDetailByIdsResponse',
  full_name='manufactory.GetOrdersDetailByIdsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.GetOrdersDetailByIdsResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5886,
  serialized_end=5971,
)

_GETPURCHASEBIREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPURCHASEBIREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPURCHASEBIRESPONSE.fields_by_name['rows'].message_type = _PURCHASEDETAILED
_GETPURCHASEBIRESPONSE.fields_by_name['total'].message_type = _TOTAL
_CREATEPURCHASEORDERREQUEST.fields_by_name['order_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEPURCHASEORDERREQUEST.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEPURCHASEORDERREQUEST.fields_by_name['product_items'].message_type = _PRODUCT_ITEM
_LISTPURCHASEORDERREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPURCHASEORDERREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PURCHASEORDERROW.fields_by_name['order_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PURCHASEORDERROW.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PURCHASEORDERROW.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPURCHASEORDERRESPONSE.fields_by_name['rows'].message_type = _PURCHASEORDERROW
_UPDATEPURCHASEORDERREQUEST.fields_by_name['order_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATEPURCHASEORDERREQUEST.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATEPURCHASEORDERREQUEST.fields_by_name['product_items'].message_type = _PRODUCT_ITEM
_GETORDERDETAILBYIDRESPONSE.fields_by_name['order_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETORDERDETAILBYIDRESPONSE.fields_by_name['products'].message_type = _ORDERPRODUCTROW
_GETORDERDETAILBYIDRESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETORDERDETAILBYIDRESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_MPRODUCT.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_MPRODUCT.fields_by_name['extend_code'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_MPRODUCT.fields_by_name['units'].message_type = _MUNIT
_GETPRODUCTLISTBYWHIDRESPONSE.fields_by_name['rows'].message_type = _MPRODUCT
_GETORDERSDETAILBYIDSRESPONSE.fields_by_name['rows'].message_type = _GETORDERDETAILBYIDRESPONSE
DESCRIPTOR.message_types_by_name['GetPurchaseBiRequest'] = _GETPURCHASEBIREQUEST
DESCRIPTOR.message_types_by_name['GetPurchaseBiResponse'] = _GETPURCHASEBIRESPONSE
DESCRIPTOR.message_types_by_name['Total'] = _TOTAL
DESCRIPTOR.message_types_by_name['PurchaseDetailed'] = _PURCHASEDETAILED
DESCRIPTOR.message_types_by_name['CreatePurchaseOrderRequest'] = _CREATEPURCHASEORDERREQUEST
DESCRIPTOR.message_types_by_name['product_item'] = _PRODUCT_ITEM
DESCRIPTOR.message_types_by_name['CreatePurchaseOrderResponse'] = _CREATEPURCHASEORDERRESPONSE
DESCRIPTOR.message_types_by_name['ListPurchaseOrderRequest'] = _LISTPURCHASEORDERREQUEST
DESCRIPTOR.message_types_by_name['PurchaseOrderRow'] = _PURCHASEORDERROW
DESCRIPTOR.message_types_by_name['ListPurchaseOrderResponse'] = _LISTPURCHASEORDERRESPONSE
DESCRIPTOR.message_types_by_name['ChangeOrderStatusRequest'] = _CHANGEORDERSTATUSREQUEST
DESCRIPTOR.message_types_by_name['ChangeOrderStatusResponse'] = _CHANGEORDERSTATUSRESPONSE
DESCRIPTOR.message_types_by_name['UpdatePurchaseOrderRequest'] = _UPDATEPURCHASEORDERREQUEST
DESCRIPTOR.message_types_by_name['UpdatePurchaseOrderResponse'] = _UPDATEPURCHASEORDERRESPONSE
DESCRIPTOR.message_types_by_name['GetOrderDetailByIdRequest'] = _GETORDERDETAILBYIDREQUEST
DESCRIPTOR.message_types_by_name['OrderProductRow'] = _ORDERPRODUCTROW
DESCRIPTOR.message_types_by_name['GetOrderDetailByIdResponse'] = _GETORDERDETAILBYIDRESPONSE
DESCRIPTOR.message_types_by_name['GetProductListByWHIdRequest'] = _GETPRODUCTLISTBYWHIDREQUEST
DESCRIPTOR.message_types_by_name['MUnit'] = _MUNIT
DESCRIPTOR.message_types_by_name['MProduct'] = _MPRODUCT
DESCRIPTOR.message_types_by_name['GetProductListByWHIdResponse'] = _GETPRODUCTLISTBYWHIDRESPONSE
DESCRIPTOR.message_types_by_name['GetOrdersDetailByIdsRequest'] = _GETORDERSDETAILBYIDSREQUEST
DESCRIPTOR.message_types_by_name['GetOrdersDetailByIdsResponse'] = _GETORDERSDETAILBYIDSRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetPurchaseBiRequest = _reflection.GeneratedProtocolMessageType('GetPurchaseBiRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPURCHASEBIREQUEST,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetPurchaseBiRequest)
  ))
_sym_db.RegisterMessage(GetPurchaseBiRequest)

GetPurchaseBiResponse = _reflection.GeneratedProtocolMessageType('GetPurchaseBiResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPURCHASEBIRESPONSE,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetPurchaseBiResponse)
  ))
_sym_db.RegisterMessage(GetPurchaseBiResponse)

Total = _reflection.GeneratedProtocolMessageType('Total', (_message.Message,), dict(
  DESCRIPTOR = _TOTAL,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.Total)
  ))
_sym_db.RegisterMessage(Total)

PurchaseDetailed = _reflection.GeneratedProtocolMessageType('PurchaseDetailed', (_message.Message,), dict(
  DESCRIPTOR = _PURCHASEDETAILED,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.PurchaseDetailed)
  ))
_sym_db.RegisterMessage(PurchaseDetailed)

CreatePurchaseOrderRequest = _reflection.GeneratedProtocolMessageType('CreatePurchaseOrderRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPURCHASEORDERREQUEST,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreatePurchaseOrderRequest)
  ))
_sym_db.RegisterMessage(CreatePurchaseOrderRequest)

product_item = _reflection.GeneratedProtocolMessageType('product_item', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCT_ITEM,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.product_item)
  ))
_sym_db.RegisterMessage(product_item)

CreatePurchaseOrderResponse = _reflection.GeneratedProtocolMessageType('CreatePurchaseOrderResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPURCHASEORDERRESPONSE,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreatePurchaseOrderResponse)
  ))
_sym_db.RegisterMessage(CreatePurchaseOrderResponse)

ListPurchaseOrderRequest = _reflection.GeneratedProtocolMessageType('ListPurchaseOrderRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTPURCHASEORDERREQUEST,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ListPurchaseOrderRequest)
  ))
_sym_db.RegisterMessage(ListPurchaseOrderRequest)

PurchaseOrderRow = _reflection.GeneratedProtocolMessageType('PurchaseOrderRow', (_message.Message,), dict(
  DESCRIPTOR = _PURCHASEORDERROW,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.PurchaseOrderRow)
  ))
_sym_db.RegisterMessage(PurchaseOrderRow)

ListPurchaseOrderResponse = _reflection.GeneratedProtocolMessageType('ListPurchaseOrderResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTPURCHASEORDERRESPONSE,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ListPurchaseOrderResponse)
  ))
_sym_db.RegisterMessage(ListPurchaseOrderResponse)

ChangeOrderStatusRequest = _reflection.GeneratedProtocolMessageType('ChangeOrderStatusRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHANGEORDERSTATUSREQUEST,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ChangeOrderStatusRequest)
  ))
_sym_db.RegisterMessage(ChangeOrderStatusRequest)

ChangeOrderStatusResponse = _reflection.GeneratedProtocolMessageType('ChangeOrderStatusResponse', (_message.Message,), dict(
  DESCRIPTOR = _CHANGEORDERSTATUSRESPONSE,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ChangeOrderStatusResponse)
  ))
_sym_db.RegisterMessage(ChangeOrderStatusResponse)

UpdatePurchaseOrderRequest = _reflection.GeneratedProtocolMessageType('UpdatePurchaseOrderRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPURCHASEORDERREQUEST,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.UpdatePurchaseOrderRequest)
  ))
_sym_db.RegisterMessage(UpdatePurchaseOrderRequest)

UpdatePurchaseOrderResponse = _reflection.GeneratedProtocolMessageType('UpdatePurchaseOrderResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPURCHASEORDERRESPONSE,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.UpdatePurchaseOrderResponse)
  ))
_sym_db.RegisterMessage(UpdatePurchaseOrderResponse)

GetOrderDetailByIdRequest = _reflection.GeneratedProtocolMessageType('GetOrderDetailByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETORDERDETAILBYIDREQUEST,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetOrderDetailByIdRequest)
  ))
_sym_db.RegisterMessage(GetOrderDetailByIdRequest)

OrderProductRow = _reflection.GeneratedProtocolMessageType('OrderProductRow', (_message.Message,), dict(
  DESCRIPTOR = _ORDERPRODUCTROW,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.OrderProductRow)
  ))
_sym_db.RegisterMessage(OrderProductRow)

GetOrderDetailByIdResponse = _reflection.GeneratedProtocolMessageType('GetOrderDetailByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETORDERDETAILBYIDRESPONSE,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetOrderDetailByIdResponse)
  ))
_sym_db.RegisterMessage(GetOrderDetailByIdResponse)

GetProductListByWHIdRequest = _reflection.GeneratedProtocolMessageType('GetProductListByWHIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTLISTBYWHIDREQUEST,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetProductListByWHIdRequest)
  ))
_sym_db.RegisterMessage(GetProductListByWHIdRequest)

MUnit = _reflection.GeneratedProtocolMessageType('MUnit', (_message.Message,), dict(
  DESCRIPTOR = _MUNIT,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.MUnit)
  ))
_sym_db.RegisterMessage(MUnit)

MProduct = _reflection.GeneratedProtocolMessageType('MProduct', (_message.Message,), dict(
  DESCRIPTOR = _MPRODUCT,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.MProduct)
  ))
_sym_db.RegisterMessage(MProduct)

GetProductListByWHIdResponse = _reflection.GeneratedProtocolMessageType('GetProductListByWHIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTLISTBYWHIDRESPONSE,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetProductListByWHIdResponse)
  ))
_sym_db.RegisterMessage(GetProductListByWHIdResponse)

GetOrdersDetailByIdsRequest = _reflection.GeneratedProtocolMessageType('GetOrdersDetailByIdsRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETORDERSDETAILBYIDSREQUEST,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetOrdersDetailByIdsRequest)
  ))
_sym_db.RegisterMessage(GetOrdersDetailByIdsRequest)

GetOrdersDetailByIdsResponse = _reflection.GeneratedProtocolMessageType('GetOrdersDetailByIdsResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETORDERSDETAILBYIDSRESPONSE,
  __module__ = 'manufactory.purchase_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetOrdersDetailByIdsResponse)
  ))
_sym_db.RegisterMessage(GetOrdersDetailByIdsResponse)



_MANUFACTORYPURCHASE = _descriptor.ServiceDescriptor(
  name='ManufactoryPurchase',
  full_name='manufactory.ManufactoryPurchase',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=5974,
  serialized_end=7247,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreatePurchaseOrder',
    full_name='manufactory.ManufactoryPurchase.CreatePurchaseOrder',
    index=0,
    containing_service=None,
    input_type=_CREATEPURCHASEORDERREQUEST,
    output_type=_CREATEPURCHASEORDERRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\")/api/v2/manufactory/create/purchase/order:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListPurchaseOrder',
    full_name='manufactory.ManufactoryPurchase.ListPurchaseOrder',
    index=1,
    containing_service=None,
    input_type=_LISTPURCHASEORDERREQUEST,
    output_type=_LISTPURCHASEORDERRESPONSE,
    serialized_options=_b('\202\323\344\223\002)\022\'/api/v2/manufactory/list/purchase/order'),
  ),
  _descriptor.MethodDescriptor(
    name='ChangeOrderStatus',
    full_name='manufactory.ManufactoryPurchase.ChangeOrderStatus',
    index=2,
    containing_service=None,
    input_type=_CHANGEORDERSTATUSREQUEST,
    output_type=_CHANGEORDERSTATUSRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\032-/api/v2/manufactory/order/{status}/{order_id}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdatePurchaseOrder',
    full_name='manufactory.ManufactoryPurchase.UpdatePurchaseOrder',
    index=3,
    containing_service=None,
    input_type=_UPDATEPURCHASEORDERREQUEST,
    output_type=_UPDATEPURCHASEORDERRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\")/api/v2/manufactory/update/purchase/order:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetOrderDetailById',
    full_name='manufactory.ManufactoryPurchase.GetOrderDetailById',
    index=4,
    containing_service=None,
    input_type=_GETORDERDETAILBYIDREQUEST,
    output_type=_GETORDERDETAILBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002-\022+/api/v2/manufactory/order/detail/{order_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetProductListByWHId',
    full_name='manufactory.ManufactoryPurchase.GetProductListByWHId',
    index=5,
    containing_service=None,
    input_type=_GETPRODUCTLISTBYWHIDREQUEST,
    output_type=_GETPRODUCTLISTBYWHIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\022%/api/v2/manufactory/product/list/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetPurchaseBi',
    full_name='manufactory.ManufactoryPurchase.GetPurchaseBi',
    index=6,
    containing_service=None,
    input_type=_GETPURCHASEBIREQUEST,
    output_type=_GETPURCHASEBIRESPONSE,
    serialized_options=_b('\202\323\344\223\002*\022(/api/v2/manufactory/purchase/bi/detailed'),
  ),
  _descriptor.MethodDescriptor(
    name='GetOrdersDetailByIds',
    full_name='manufactory.ManufactoryPurchase.GetOrdersDetailByIds',
    index=7,
    containing_service=None,
    input_type=_GETORDERSDETAILBYIDSREQUEST,
    output_type=_GETORDERSDETAILBYIDSRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\"-/api/v2/manufactory/list/purchase/order/print:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_MANUFACTORYPURCHASE)

DESCRIPTOR.services_by_name['ManufactoryPurchase'] = _MANUFACTORYPURCHASE

# @@protoc_insertion_point(module_scope)
