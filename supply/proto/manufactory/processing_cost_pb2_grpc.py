# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from manufactory import processing_cost_pb2 as manufactory_dot_processing__cost__pb2


class ProcessingCostStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateProcessingCost = channel.unary_unary(
        '/manufactory.ProcessingCost/CreateProcessingCost',
        request_serializer=manufactory_dot_processing__cost__pb2.CreateProcessingCostRequest.SerializeToString,
        response_deserializer=manufactory_dot_processing__cost__pb2.CreateProcessingCostResponse.FromString,
        )
    self.ListProcessingCost = channel.unary_unary(
        '/manufactory.ProcessingCost/ListProcessingCost',
        request_serializer=manufactory_dot_processing__cost__pb2.ListProcessingCostRequest.SerializeToString,
        response_deserializer=manufactory_dot_processing__cost__pb2.ListProcessingCostResponse.FromString,
        )
    self.GetProcessingCostDetail = channel.unary_unary(
        '/manufactory.ProcessingCost/GetProcessingCostDetail',
        request_serializer=manufactory_dot_processing__cost__pb2.GetProcessingCostDetailRequest.SerializeToString,
        response_deserializer=manufactory_dot_processing__cost__pb2.GetProcessingCostDetailResponse.FromString,
        )
    self.UpdateProcessingCost = channel.unary_unary(
        '/manufactory.ProcessingCost/UpdateProcessingCost',
        request_serializer=manufactory_dot_processing__cost__pb2.UpdateProcessingCostRequest.SerializeToString,
        response_deserializer=manufactory_dot_processing__cost__pb2.UpdateProcessingCostResponse.FromString,
        )
    self.ChangeProcessingCostStatus = channel.unary_unary(
        '/manufactory.ProcessingCost/ChangeProcessingCostStatus',
        request_serializer=manufactory_dot_processing__cost__pb2.ChangeProcessingCostStatusRequest.SerializeToString,
        response_deserializer=manufactory_dot_processing__cost__pb2.ChangeProcessingCostStatusResponse.FromString,
        )


class ProcessingCostServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def CreateProcessingCost(self, request, context):
    """创建加工费用单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListProcessingCost(self, request, context):
    """加工费用单列表查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProcessingCostDetail(self, request, context):
    """查询加工费用单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateProcessingCost(self, request, context):
    """更新加工费用单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ChangeProcessingCostStatus(self, request, context):
    """加工费用单状态变更
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_ProcessingCostServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateProcessingCost': grpc.unary_unary_rpc_method_handler(
          servicer.CreateProcessingCost,
          request_deserializer=manufactory_dot_processing__cost__pb2.CreateProcessingCostRequest.FromString,
          response_serializer=manufactory_dot_processing__cost__pb2.CreateProcessingCostResponse.SerializeToString,
      ),
      'ListProcessingCost': grpc.unary_unary_rpc_method_handler(
          servicer.ListProcessingCost,
          request_deserializer=manufactory_dot_processing__cost__pb2.ListProcessingCostRequest.FromString,
          response_serializer=manufactory_dot_processing__cost__pb2.ListProcessingCostResponse.SerializeToString,
      ),
      'GetProcessingCostDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetProcessingCostDetail,
          request_deserializer=manufactory_dot_processing__cost__pb2.GetProcessingCostDetailRequest.FromString,
          response_serializer=manufactory_dot_processing__cost__pb2.GetProcessingCostDetailResponse.SerializeToString,
      ),
      'UpdateProcessingCost': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateProcessingCost,
          request_deserializer=manufactory_dot_processing__cost__pb2.UpdateProcessingCostRequest.FromString,
          response_serializer=manufactory_dot_processing__cost__pb2.UpdateProcessingCostResponse.SerializeToString,
      ),
      'ChangeProcessingCostStatus': grpc.unary_unary_rpc_method_handler(
          servicer.ChangeProcessingCostStatus,
          request_deserializer=manufactory_dot_processing__cost__pb2.ChangeProcessingCostStatusRequest.FromString,
          response_serializer=manufactory_dot_processing__cost__pb2.ChangeProcessingCostStatusResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'manufactory.ProcessingCost', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
