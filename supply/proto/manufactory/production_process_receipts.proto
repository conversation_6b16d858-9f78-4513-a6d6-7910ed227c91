syntax = "proto3";

package manufactory;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

service ManufactoryProductionProcessReceipts{
    // 创建生产单
    rpc CreateProductionProcessReceipts (CreateProductionProcessReceiptsRequest) returns (CreateProductionProcessReceiptsResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/manufactory/production_process/receipts"
        body: "*"
        };
    }
    // 生产单列表查询
    rpc ListProductionProcessReceipts (ListProductionProcessReceiptsRequest) returns (ListProductionProcessReceiptsResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/manufactory/production_process/list"
        };
    }
    // 生产单详情查询
    rpc GetProductionProcessReceiptDetail (GetProductionProcessReceiptDetailRequest) returns (GetProductionProcessReceiptDetailResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/manufactory/production_process/{receipt_id}/detail"
        };
    }
    // 更新生产单
    rpc UpdateProductionProcessReceipts (UpdateProductionProcessReceiptsRequest) returns (UpdateProductionProcessReceiptsResponse) {
        option (google.api.http) = {
        put: "/api/v2/supply/manufactory/production_process/{receipt_id}"
        body: "*"
        };
    }
    // 生产单状态变更
    rpc ChangeProductionProcessReceiptsStatus (ChangeProductionProcessReceiptsStatusRequest) returns (ChangeProductionProcessReceiptsStatusResponse) {
        option (google.api.http) = {
        put: "/api/v2/supply/manufactory/production_process/{receipt_id}/{status}"
        body: "*"
        };
    }
    // 生产转化率查询
    rpc GetMaterialToProductRate (GetMaterialToProductRateRequest) returns (GetMaterialToProductRateResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/manufactory/production_process/rate"
        };
    }
    // 根据选择的加工中心获取对应区域、门店下的规则ID
    rpc GetRuleByBranchId (GetRuleByBranchIdRequest) returns (GetRuleByBranchIdResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/manufactory/production_process/{branch_id}/rule/get"
        };
    }
}

// 创建生产单请求参数
message CreateProductionProcessReceiptsRequest{
    // 生产日期
    google.protobuf.Timestamp process_date=1;
    // 生产门店、仓库、加工中心ID
    uint64 process_store_id=2;
    // 生产门店、仓库、加工中心name
    string process_store_name=3;
    // 生产门店、仓库、加工中心code
    string process_store_code=4;
    // 备注
    string remark = 5;
    // 幂等性校验请求id
    uint64 request_id = 6;
    // 生产明细
//    repeated ProductionReceiptsRows rows = 7;
    // 生产明细
    repeated ProductionDetails details = 7;
    // 生产理论率和实际率
    repeated ProductionItems items = 8;
}

// 创建生产单返回
message CreateProductionProcessReceiptsResponse{
    // 包装单id
    uint64 receipt_id = 1;
    string result = 2;
}

message ProductionReceiptsRows {
    // 规则ID
    uint64 production_rule = 1;
    string production_rule_code = 2;
    string production_rule_name = 3;
    // 生产明细
    repeated ProductionDetails details = 4;
    // 生产理论率和实际率
    repeated ProductionItems items = 5;
}

message ProductionDetails{
    // 没有不传
    uint64 id = 1;
    // 物料/商品id
    uint64 product_id = 2;
    // 物料/商品code
    string product_code = 3;
    // 物料/商品名称
    string product_name = 4;
    // 单位id
    uint64 unit_id = 5;
    string unit_code = 6;
    string unit_name = 7;
    // 商品类型 material:原料；product：产出物
    string type = 8;
    // 物料数量
    double quantity = 9;
    // 规则ID
    uint64 production_rule = 10;
    string production_rule_code = 11;
    string production_rule_name = 12;
    // 规格
    uint64 specification_id = 13;
    string specification_code = 14;
    string specification_name = 15;
}

message ProductionItems{
    // 没有不传
    uint64 id = 1;
    // 原料ID
    uint64 from_material_id = 2;
    // 物料code
    string from_material_code= 3;
    // 物料名称
    string from_material_name = 4;
    // 产出物ID
    uint64 to_material_id = 5;
    // 产出物code
    string to_material_code= 6;
    // 产出物名称
    string to_material_name = 7;
    // 理论转换率
    double theoretical_rate = 8;
    // 实际转换率
    double actual_rate = 9;
    // 规则ID
    uint64 production_rule = 10;
    string production_rule_code = 11;
    string production_rule_name = 12;
}

message ListProductionProcessReceiptsRequest{
    // 开始日期
    google.protobuf.Timestamp start_date = 1;
    // 结束日期
    google.protobuf.Timestamp end_date = 2;
    // 门店、仓库、加工中心
    repeated uint64 process_store_ids = 3;
    // 单号
    string code = 4;
    // 订单状态 'INITED'新建 'SUBMITTED'已提交 'APPROVED'已审核 'REJECTED'已驳回， 多选传数组
    repeated string status = 5;
    int64 limit = 6;
    uint64 offset = 7;
    bool include_total = 8;
}

message ProductionProcessRow{
    uint64 id = 1;
    uint64 partner_id = 2;
    uint64 created_by = 3;
    string created_name = 4;
    uint64 updated_by = 5;
    string updated_name = 6;
    google.protobuf.Timestamp created_at = 7;
    google.protobuf.Timestamp updated_at = 8;
    string status = 9;
    string code = 10;
    // 生产单日期
    google.protobuf.Timestamp process_date = 11;
    // 生产门店、仓库、加工中心ID
    uint64 process_store_id=12;
    // 生产门店、仓库、加工中心name
    string process_store_name=13;
    // 生产门店、仓库、加工中心code
    string process_store_code=14;
    // 备注
    string remark = 15;
    // 幂等性校验请求id
    uint64 request_id = 16;
    // 类型
    string type = 17;
}

message ListProductionProcessReceiptsResponse{
    repeated ProductionProcessRow rows = 1;
    uint64 total = 2;
}

message GetProductionProcessReceiptDetailRequest{
    uint64 receipt_id = 1;
}

message GetProductionProcessReceiptDetailResponse{
    uint64 id = 1;
    uint64 partner_id = 2;
    uint64 created_by = 3;
    string created_name = 4;
    uint64 updated_by = 5;
    string updated_name = 6;
    google.protobuf.Timestamp created_at = 7;
    google.protobuf.Timestamp updated_at = 8;
    uint64 main_id = 9;
    string status = 10;
    string code = 11;
    // 生产单日期
    google.protobuf.Timestamp process_date = 12;
    // 生产门店、仓库、加工中心ID
    uint64 process_store_id=13;
    // 生产门店、仓库、加工中心name
    string process_store_name=14;
    // 生产门店、仓库、加工中心code
    string process_store_code=15;
    // 备注
    string remark = 16;
    // 幂等性校验请求id
    uint64 request_id = 17;
    // 规则以及下属商品比率信息
    repeated ProductionReceiptsRows rows = 18;
    // 生产明细
//    repeated ProductionDetails details = 18;
//    // 生产理论率和实际率
//    repeated ProductionItems items = 19;
    // 类型
    string type = 19;
}

message UpdateProductionProcessReceiptsRequest{
    uint64 receipt_id = 1;
    // 包装日期
    google.protobuf.Timestamp process_date = 2;
    // 生产门店、仓库、加工中心ID
    uint64 process_store_id=3;
    // 生产门店、仓库、加工中心name
    string process_store_name=4;
    // 生产门店、仓库、加工中心code
    string process_store_code=5;
    // 备注
    string remark = 6;
    // 生产明细
    repeated ProductionDetails details = 7;
    // 生产理论率和实际率
    repeated ProductionItems items = 8;
    // 删除的规则<预留字段>
    repeated uint64 receipt_rule_ids=9;
}

message UpdateProductionProcessReceiptsResponse{
    uint64 receipt_id = 1;
    string result = 2;
}

message ChangeProductionProcessReceiptsStatusRequest{
    uint64 receipt_id=1;
    string status=2;
}

message ChangeProductionProcessReceiptsStatusResponse{
    uint64 receipt_id = 1;
    string result = 2;
}

message GetMaterialToProductRateRequest{
    //是否包含总数
    bool include_total = 1;
    //门店ID
    repeated uint64 store_ids = 2;
    //查询开始时间
    google.protobuf.Timestamp start_date = 3;
    //查询结束时间
    google.protobuf.Timestamp end_date = 4;
    //分页开始处
    uint32 offset = 5;
    //分页限制数
    uint32 limit = 6;
    //生产原料
    repeated uint64 material_ids = 7;
    //产出物
    repeated uint64 product_ids = 8;
    //是否对比(no_comparison:不对比，higher_than:高于，lower_than:低于，equal_to:相等)
    string conversion_rate = 9;
}

message MaterialToProductRateRow{
    //生产原料
    uint64 material_id = 1;
    string material_code = 2;
    string material_name = 3;
    //产出物
    uint64 product_id = 4;
    string product_code = 5;
    string product_name = 6;
    //生产日期
    google.protobuf.Timestamp process_date = 7;
    //门店id
    uint64 store_id = 8;
    //门店编号
    string store_name = 9;
    string store_code = 10;
    //理论转换率
    double theoretical_rate = 11;
    //实际转换率
    double actual_rate = 12;
    //单号
    string code = 13;
    // 原料数量
    double material_quantity = 14;
    // 产出物数量
    double product_quantity = 15;
}

message GetMaterialToProductRateResponse{
    repeated MaterialToProductRateRow rows = 1;
    uint64 total = 2;
}

message GetRuleByBranchIdRequest{
    uint64 branch_id = 1;
}

message Rules{
    string id = 1;
    string code = 2;
    string name = 3;
}

message GetRuleByBranchIdResponse{
    repeated Rules rows= 1;
}
