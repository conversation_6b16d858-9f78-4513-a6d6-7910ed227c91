# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: manufactory/returns.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='manufactory/returns.proto',
  package='manufactory',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x19manufactory/returns.proto\x12\x0bmanufactory\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xfd\x08\n\x07Returns\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x11\n\treturn_by\x18\x03 \x01(\x04\x12\x15\n\rreturn_number\x18\x04 \x01(\t\x12\x0e\n\x06status\x18\x05 \x01(\t\x12\x11\n\treview_by\x18\x06 \x01(\x04\x12\x13\n\x0bis_returned\x18\x07 \x01(\x08\x12\x1e\n\x16return_delivery_number\x18\x08 \x01(\t\x12/\n\x0breturn_date\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x38\n\x14return_delivery_date\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rreturn_reason\x18\x0b \x01(\t\x12\x0c\n\x04type\x18\x0c \x01(\t\x12\x10\n\x08sub_type\x18\r \x01(\t\x12\x0e\n\x06remark\x18\x0e \x01(\t\x12\x1a\n\x12store_secondary_id\x18\x0f \x01(\t\x12.\n\ncreated_at\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x12 \x01(\x04\x12\x12\n\nupdated_by\x18\x13 \x01(\x04\x12\x11\n\treturn_to\x18\x14 \x01(\x04\x12\x18\n\x10inventory_status\x18\x15 \x01(\t\x12\x18\n\x10inventory_req_id\x18\x16 \x01(\x04\x12\x12\n\npartner_id\x18\x17 \x01(\x04\x12\x11\n\tis_direct\x18\x18 \x01(\x08\x12\x15\n\rreject_reason\x18\x19 \x01(\t\x12\x33\n\x0b\x61ttachments\x18\x1a \x03(\x0b\x32\x1e.manufactory.ReturnAttachments\x12\x14\n\x0c\x63reated_name\x18\x1b \x01(\t\x12\x14\n\x0cupdated_name\x18\x1c \x01(\t\x12\x14\n\x0cproduct_nums\x18\x1d \x01(\x04\x12\x16\n\x0elogistics_type\x18\x1f \x01(\t\x12\x12\n\ntrans_type\x18# \x01(\t\x12\x11\n\tsource_id\x18% \x01(\x04\x12\x13\n\x0bsource_code\x18& \x01(\t\x12\x31\n\rdelivery_date\x18\' \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nrequest_id\x18( \x01(\x04\x12\x16\n\x0ereturn_to_code\x18) \x01(\t\x12\x16\n\x0ereturn_to_name\x18* \x01(\t\x12\x16\n\x0ereturn_by_code\x18+ \x01(\t\x12\x16\n\x0ereturn_by_name\x18, \x01(\t\x12\x15\n\rfranchisee_id\x18\x37 \x01(\x04\x12\x17\n\x0f\x66ranchisee_code\x18\x38 \x01(\t\x12\x17\n\x0f\x66ranchisee_name\x18\x39 \x01(\t\x12\x13\n\x0bpayment_way\x18: \x01(\t\x12\x14\n\x0creceive_code\x18; \x01(\t\x12\x15\n\rdelivery_code\x18< \x01(\t\"u\n\x0cValidProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12\x0c\n\x04spec\x18\x04 \x01(\t\x12\x1f\n\x04unit\x18\x05 \x01(\x0b\x32\x11.manufactory.Unit\"2\n\x04Unit\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08quantity\x18\x02 \x01(\x05\x12\x0c\n\x04rate\x18\x03 \x01(\x01\"\xbe\x07\n\rProductDetail\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x11\n\treturn_id\x18\x02 \x01(\x04\x12\x11\n\treturn_by\x18\x03 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x05 \x01(\t\x12\x12\n\nproduct_id\x18\x06 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x10\n\x08quantity\x18\x08 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\t \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\n \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x0b \x01(\t\x12\x0f\n\x07unit_id\x18\x0c \x01(\x04\x12\x11\n\tunit_name\x18\r \x01(\t\x12\x11\n\tunit_rate\x18\x0e \x01(\x01\x12\x11\n\tunit_spec\x18\x0f \x01(\t\x12%\n\x1d\x61\x63\x63ounting_confirmed_quantity\x18\x10 \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x11 \x01(\x01\x12$\n\x1c\x61\x63\x63ounting_returned_quantity\x18\x12 \x01(\x01\x12\x1a\n\x12\x63onfirmed_quantity\x18\x13 \x01(\x01\x12\x19\n\x11returned_quantity\x18\x14 \x01(\x01\x12\x14\n\x0cis_confirmed\x18\x15 \x01(\x08\x12/\n\x0breturn_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\treturn_to\x18\x17 \x01(\x04\x12\x18\n\x10inventory_status\x18\x18 \x01(\t\x12\x18\n\x10inventory_req_id\x18\x19 \x01(\x04\x12.\n\ncreated_at\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x1b \x01(\x04\x12.\n\nupdated_at\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18\x1d \x01(\x04\x12\x12\n\npartner_id\x18\x1e \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1f \x01(\t\x12\x14\n\x0cupdated_name\x18  \x01(\t\x12\x14\n\x0cstorage_type\x18# \x01(\t\x12\r\n\x05price\x18$ \x01(\x01\x12\x10\n\x08tax_rate\x18% \x01(\x02\x12\x11\n\tprice_tax\x18& \x01(\x01\x12\x11\n\tsum_price\x18\' \x01(\x01\x12\x33\n\x0b\x61ttachments\x18- \x03(\x0b\x32\x1e.manufactory.ReturnAttachments\"\xe7\x02\n\rReturnProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x10\n\x08quantity\x18\x03 \x01(\x01\x12\x1a\n\x12\x63onfirmed_quantity\x18\x04 \x01(\x01\x12\x14\n\x0cproduct_code\x18\x05 \x01(\t\x12\x14\n\x0cproduct_name\x18\x06 \x01(\t\x12\x0f\n\x07unit_id\x18\x07 \x01(\x04\x12\x11\n\tunit_name\x18\x08 \x01(\t\x12\x11\n\tunit_spec\x18\t \x01(\t\x12\x11\n\tunit_rate\x18\n \x01(\x02\x12\x10\n\x08tax_rate\x18\x0b \x01(\x02\x12\r\n\x05price\x18\x0c \x01(\x02\x12\x11\n\tprice_tax\x18\r \x01(\x02\x12\x11\n\treturn_to\x18\x0e \x01(\x04\x12\x16\n\x0elogistics_type\x18\x0f \x01(\t\x12\x33\n\x0b\x61ttachments\x18\x10 \x03(\x0b\x32\x1e.manufactory.ReturnAttachments\"\xed\x02\n\x13\x43reateReturnRequest\x12\x11\n\treturn_by\x18\x01 \x01(\x04\x12\x16\n\x0elogistics_type\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x10\n\x08sub_type\x18\x04 \x01(\t\x12\x38\n\x14return_delivery_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tsource_id\x18\x06 \x01(\x04\x12\x13\n\x0bsource_code\x18\x07 \x01(\t\x12\x15\n\rreturn_reason\x18\x08 \x01(\t\x12\x0e\n\x06remark\x18\t \x01(\t\x12\x33\n\x0b\x61ttachments\x18\n \x03(\x0b\x32\x1e.manufactory.ReturnAttachments\x12\x12\n\nrequest_id\x18\x0b \x01(\x04\x12,\n\x08products\x18\x0c \x03(\x0b\x32\x1a.manufactory.ReturnProduct\x12\x0b\n\x03lan\x18\x14 \x01(\t\":\n\x14\x43reateReturnResponse\x12\x11\n\treturn_id\x18\x01 \x03(\x04\x12\x0f\n\x07payload\x18\x02 \x01(\x08\"\x87\x03\n\x1b\x43heckReturnAvailableRequest\x12\x11\n\treturn_by\x18\x01 \x01(\x04\x12\x38\n\x14return_delivery_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x10\n\x08sub_type\x18\x04 \x01(\t\x12\x15\n\rreturn_reason\x18\x05 \x01(\t\x12\x0e\n\x06remark\x18\x06 \x01(\t\x12,\n\x08products\x18\x07 \x03(\x0b\x32\x1a.manufactory.ReturnProduct\x12\x11\n\treturn_to\x18\t \x01(\x04\x12\x33\n\x0b\x61ttachments\x18\n \x03(\x0b\x32\x1e.manufactory.ReturnAttachments\x12\x16\n\x0elogistics_type\x18\x0b \x01(\t\x12\x11\n\tsource_id\x18\x0c \x01(\x04\x12\x13\n\x0bsource_code\x18\r \x01(\t\x12\x11\n\treturn_id\x18\x0e \x01(\x04\x12\x0b\n\x03lan\x18\x0f \x01(\t\"}\n\x16GetValidProductRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12\x14\n\x0cproduct_name\x18\x05 \x01(\t\x12\x0b\n\x03lan\x18\x06 \x01(\t\"*\n\x17GetValidProductResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"\xed\x03\n\x11ListReturnRequest\x12\x11\n\treturn_by\x18\x01 \x03(\x04\x12\x0e\n\x06status\x18\x02 \x03(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x11\n\tsource_id\x18\x04 \x01(\x04\x12\x13\n\x0bsource_code\x18\x05 \x01(\t\x12\x16\n\x0elogistics_type\x18\x06 \x01(\t\x12\x0c\n\x04type\x18\x07 \x01(\t\x12\x10\n\x08sub_type\x18\x08 \x01(\t\x12\x13\n\x0bproduct_ids\x18\t \x03(\x04\x12\x34\n\x10return_date_from\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0ereturn_date_to\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x36\n\x12\x64\x65livery_date_from\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x10\x64\x65livery_date_to\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x14 \x01(\x05\x12\x0e\n\x06offset\x18\x15 \x01(\x05\x12\r\n\x05order\x18\x16 \x01(\t\x12\x0c\n\x04sort\x18\x17 \x01(\t\x12\x0b\n\x03lan\x18\x18 \x01(\t\x12\x11\n\treturn_to\x18\x19 \x03(\x04\"G\n\x12ListReturnResponse\x12\"\n\x04rows\x18\x01 \x03(\x0b\x32\x14.manufactory.Returns\x12\r\n\x05total\x18\x02 \x01(\x04\"/\n\x14GetReturnByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"r\n\x1bGetReturnProductByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x0c\n\x04sort\x18\x04 \x01(\t\x12\r\n\x05order\x18\x05 \x01(\t\x12\x0b\n\x03lan\x18\x06 \x01(\t\"W\n\x1cGetReturnProductByIdResponse\x12(\n\x04rows\x18\x01 \x03(\x0b\x32\x1a.manufactory.ProductDetail\x12\r\n\x05total\x18\x02 \x01(\x04\".\n\x13SubmitReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"E\n\x13RejectReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x15\n\rreject_reason\x18\x02 \x01(\t\x12\x0b\n\x03lan\x18\x03 \x01(\t\"C\n\x14\x41pproveReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\ntrans_type\x18\x02 \x01(\t\x12\x0b\n\x03lan\x18\x03 \x01(\t\"/\n\x14\x43onfirmReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"0\n\x15\x44\x65liveryReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\x9d\x02\n\x13UpdateReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12,\n\x08products\x18\x02 \x03(\x0b\x32\x1a.manufactory.ReturnProduct\x12\x15\n\rreturn_reason\x18\x03 \x01(\t\x12\x0e\n\x06remark\x18\x04 \x01(\t\x12\x38\n\x14return_delivery_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\treturn_to\x18\x06 \x01(\x04\x12\x33\n\x0b\x61ttachments\x18\x07 \x03(\x0b\x32\x1e.manufactory.ReturnAttachments\x12\x0b\n\x03lan\x18\x08 \x01(\t\x12\x16\n\x0elogistics_type\x18\t \x01(\t\".\n\x13\x44\x65leteReturnRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\'\n\x14ReturnCommonResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\".\n\x11ReturnAttachments\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t2\xed\x0e\n\x18ManufactoryReturnService\x12\x82\x01\n\x0c\x43reateReturn\x12 .manufactory.CreateReturnRequest\x1a!.manufactory.CreateReturnResponse\"-\x82\xd3\xe4\x93\x02\'\"\"/api/v2/supply/manufactory/returns:\x01*\x12\x8b\x01\n\x0fGetValidProduct\x12#.manufactory.GetValidProductRequest\x1a\x19.manufactory.ValidProduct\"8\x82\xd3\xe4\x93\x02\x32\x12\x30/api/v2/supply/manufactory/returns/valid_product\x12y\n\nListReturn\x12\x1e.manufactory.ListReturnRequest\x1a\x1f.manufactory.ListReturnResponse\"*\x82\xd3\xe4\x93\x02$\x12\"/api/v2/supply/manufactory/returns\x12y\n\rGetReturnById\x12!.manufactory.GetReturnByIdRequest\x1a\x14.manufactory.Returns\"/\x82\xd3\xe4\x93\x02)\x12\'/api/v2/supply/manufactory/returns/{id}\x12\xa4\x01\n\x14GetReturnProductById\x12(.manufactory.GetReturnProductByIdRequest\x1a).manufactory.GetReturnProductByIdResponse\"7\x82\xd3\xe4\x93\x02\x31\x12//api/v2/supply/manufactory/returns/{id}/product\x12\x8e\x01\n\x0cSubmitReturn\x12 .manufactory.SubmitReturnRequest\x1a!.manufactory.ReturnCommonResponse\"9\x82\xd3\xe4\x93\x02\x33\x1a./api/v2/supply/manufactory/returns/{id}/submit:\x01*\x12\x8e\x01\n\x0cRejectReturn\x12 .manufactory.RejectReturnRequest\x1a!.manufactory.ReturnCommonResponse\"9\x82\xd3\xe4\x93\x02\x33\x1a./api/v2/supply/manufactory/returns/{id}/reject:\x01*\x12\x91\x01\n\rApproveReturn\x12!.manufactory.ApproveReturnRequest\x1a!.manufactory.ReturnCommonResponse\":\x82\xd3\xe4\x93\x02\x34\x1a//api/v2/supply/manufactory/returns/{id}/approve:\x01*\x12\x91\x01\n\rConfirmReturn\x12!.manufactory.ConfirmReturnRequest\x1a!.manufactory.ReturnCommonResponse\":\x82\xd3\xe4\x93\x02\x34\x1a//api/v2/supply/manufactory/returns/{id}/confirm:\x01*\x12\x94\x01\n\x0e\x44\x65liveryReturn\x12\".manufactory.DeliveryReturnRequest\x1a!.manufactory.ReturnCommonResponse\";\x82\xd3\xe4\x93\x02\x35\x1a\x30/api/v2/supply/manufactory/returns/{id}/delivery:\x01*\x12\x8e\x01\n\x0cUpdateReturn\x12 .manufactory.UpdateReturnRequest\x1a!.manufactory.ReturnCommonResponse\"9\x82\xd3\xe4\x93\x02\x33\x1a./api/v2/supply/manufactory/returns/{id}/update:\x01*\x12\x8e\x01\n\x0c\x44\x65leteReturn\x12 .manufactory.DeleteReturnRequest\x1a!.manufactory.ReturnCommonResponse\"9\x82\xd3\xe4\x93\x02\x33\x1a./api/v2/supply/manufactory/returns/{id}/delete:\x01*\x12\x9d\x01\n\x19\x43heckReturnAvailableByrec\x12(.manufactory.CheckReturnAvailableRequest\x1a!.manufactory.ReturnCommonResponse\"3\x82\xd3\xe4\x93\x02-\"(/api/v2/supply/manufactory/returns/check:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_RETURNS = _descriptor.Descriptor(
  name='Returns',
  full_name='manufactory.Returns',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.Returns.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.Returns.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_by', full_name='manufactory.Returns.return_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_number', full_name='manufactory.Returns.return_number', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.Returns.status', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='manufactory.Returns.review_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_returned', full_name='manufactory.Returns.is_returned', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_number', full_name='manufactory.Returns.return_delivery_number', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date', full_name='manufactory.Returns.return_date', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_date', full_name='manufactory.Returns.return_delivery_date', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_reason', full_name='manufactory.Returns.return_reason', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.Returns.type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='manufactory.Returns.sub_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.Returns.remark', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='manufactory.Returns.store_secondary_id', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.Returns.created_at', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.Returns.updated_at', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.Returns.created_by', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.Returns.updated_by', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='manufactory.Returns.return_to', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_status', full_name='manufactory.Returns.inventory_status', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_req_id', full_name='manufactory.Returns.inventory_req_id', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.Returns.partner_id', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='manufactory.Returns.is_direct', index=23,
      number=24, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='manufactory.Returns.reject_reason', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='manufactory.Returns.attachments', index=25,
      number=26, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.Returns.created_name', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.Returns.updated_name', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_nums', full_name='manufactory.Returns.product_nums', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='manufactory.Returns.logistics_type', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_type', full_name='manufactory.Returns.trans_type', index=30,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_id', full_name='manufactory.Returns.source_id', index=31,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_code', full_name='manufactory.Returns.source_code', index=32,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='manufactory.Returns.delivery_date', index=33,
      number=39, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.Returns.request_id', index=34,
      number=40, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to_code', full_name='manufactory.Returns.return_to_code', index=35,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to_name', full_name='manufactory.Returns.return_to_name', index=36,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_by_code', full_name='manufactory.Returns.return_by_code', index=37,
      number=43, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_by_name', full_name='manufactory.Returns.return_by_name', index=38,
      number=44, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='manufactory.Returns.franchisee_id', index=39,
      number=55, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_code', full_name='manufactory.Returns.franchisee_code', index=40,
      number=56, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_name', full_name='manufactory.Returns.franchisee_name', index=41,
      number=57, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payment_way', full_name='manufactory.Returns.payment_way', index=42,
      number=58, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_code', full_name='manufactory.Returns.receive_code', index=43,
      number=59, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_code', full_name='manufactory.Returns.delivery_code', index=44,
      number=60, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=106,
  serialized_end=1255,
)


_VALIDPRODUCT = _descriptor.Descriptor(
  name='ValidProduct',
  full_name='manufactory.ValidProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.ValidProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.ValidProduct.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.ValidProduct.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='manufactory.ValidProduct.spec', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit', full_name='manufactory.ValidProduct.unit', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1257,
  serialized_end=1374,
)


_UNIT = _descriptor.Descriptor(
  name='Unit',
  full_name='manufactory.Unit',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.Unit.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.Unit.quantity', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='manufactory.Unit.rate', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1376,
  serialized_end=1426,
)


_PRODUCTDETAIL = _descriptor.Descriptor(
  name='ProductDetail',
  full_name='manufactory.ProductDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.ProductDetail.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_id', full_name='manufactory.ProductDetail.return_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_by', full_name='manufactory.ProductDetail.return_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.ProductDetail.product_code', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.ProductDetail.product_id', index=4,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.ProductDetail.product_name', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.ProductDetail.quantity', index=6,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='manufactory.ProductDetail.accounting_unit_id', index=7,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='manufactory.ProductDetail.accounting_unit_name', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='manufactory.ProductDetail.accounting_unit_spec', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.ProductDetail.unit_id', index=10,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.ProductDetail.unit_name', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='manufactory.ProductDetail.unit_rate', index=12,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='manufactory.ProductDetail.unit_spec', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_confirmed_quantity', full_name='manufactory.ProductDetail.accounting_confirmed_quantity', index=14,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='manufactory.ProductDetail.accounting_quantity', index=15,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_returned_quantity', full_name='manufactory.ProductDetail.accounting_returned_quantity', index=16,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='manufactory.ProductDetail.confirmed_quantity', index=17,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='returned_quantity', full_name='manufactory.ProductDetail.returned_quantity', index=18,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_confirmed', full_name='manufactory.ProductDetail.is_confirmed', index=19,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date', full_name='manufactory.ProductDetail.return_date', index=20,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='manufactory.ProductDetail.return_to', index=21,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_status', full_name='manufactory.ProductDetail.inventory_status', index=22,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_req_id', full_name='manufactory.ProductDetail.inventory_req_id', index=23,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='manufactory.ProductDetail.created_at', index=24,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='manufactory.ProductDetail.created_by', index=25,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='manufactory.ProductDetail.updated_at', index=26,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='manufactory.ProductDetail.updated_by', index=27,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='manufactory.ProductDetail.partner_id', index=28,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='manufactory.ProductDetail.created_name', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='manufactory.ProductDetail.updated_name', index=30,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='manufactory.ProductDetail.storage_type', index=31,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='manufactory.ProductDetail.price', index=32,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='manufactory.ProductDetail.tax_rate', index=33,
      number=37, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_tax', full_name='manufactory.ProductDetail.price_tax', index=34,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_price', full_name='manufactory.ProductDetail.sum_price', index=35,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='manufactory.ProductDetail.attachments', index=36,
      number=45, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1429,
  serialized_end=2387,
)


_RETURNPRODUCT = _descriptor.Descriptor(
  name='ReturnProduct',
  full_name='manufactory.ReturnProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.ReturnProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='manufactory.ReturnProduct.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='manufactory.ReturnProduct.quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='manufactory.ReturnProduct.confirmed_quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='manufactory.ReturnProduct.product_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.ReturnProduct.product_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='manufactory.ReturnProduct.unit_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='manufactory.ReturnProduct.unit_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='manufactory.ReturnProduct.unit_spec', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='manufactory.ReturnProduct.unit_rate', index=9,
      number=10, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='manufactory.ReturnProduct.tax_rate', index=10,
      number=11, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='manufactory.ReturnProduct.price', index=11,
      number=12, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price_tax', full_name='manufactory.ReturnProduct.price_tax', index=12,
      number=13, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='manufactory.ReturnProduct.return_to', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='manufactory.ReturnProduct.logistics_type', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='manufactory.ReturnProduct.attachments', index=15,
      number=16, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2390,
  serialized_end=2749,
)


_CREATERETURNREQUEST = _descriptor.Descriptor(
  name='CreateReturnRequest',
  full_name='manufactory.CreateReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='return_by', full_name='manufactory.CreateReturnRequest.return_by', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='manufactory.CreateReturnRequest.logistics_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.CreateReturnRequest.type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='manufactory.CreateReturnRequest.sub_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_date', full_name='manufactory.CreateReturnRequest.return_delivery_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_id', full_name='manufactory.CreateReturnRequest.source_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_code', full_name='manufactory.CreateReturnRequest.source_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_reason', full_name='manufactory.CreateReturnRequest.return_reason', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.CreateReturnRequest.remark', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='manufactory.CreateReturnRequest.attachments', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='manufactory.CreateReturnRequest.request_id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='manufactory.CreateReturnRequest.products', index=11,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.CreateReturnRequest.lan', index=12,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2752,
  serialized_end=3117,
)


_CREATERETURNRESPONSE = _descriptor.Descriptor(
  name='CreateReturnResponse',
  full_name='manufactory.CreateReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='return_id', full_name='manufactory.CreateReturnResponse.return_id', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='payload', full_name='manufactory.CreateReturnResponse.payload', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3119,
  serialized_end=3177,
)


_CHECKRETURNAVAILABLEREQUEST = _descriptor.Descriptor(
  name='CheckReturnAvailableRequest',
  full_name='manufactory.CheckReturnAvailableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='return_by', full_name='manufactory.CheckReturnAvailableRequest.return_by', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_date', full_name='manufactory.CheckReturnAvailableRequest.return_delivery_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.CheckReturnAvailableRequest.type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='manufactory.CheckReturnAvailableRequest.sub_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_reason', full_name='manufactory.CheckReturnAvailableRequest.return_reason', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.CheckReturnAvailableRequest.remark', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='manufactory.CheckReturnAvailableRequest.products', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='manufactory.CheckReturnAvailableRequest.return_to', index=7,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='manufactory.CheckReturnAvailableRequest.attachments', index=8,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='manufactory.CheckReturnAvailableRequest.logistics_type', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_id', full_name='manufactory.CheckReturnAvailableRequest.source_id', index=10,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_code', full_name='manufactory.CheckReturnAvailableRequest.source_code', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_id', full_name='manufactory.CheckReturnAvailableRequest.return_id', index=12,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.CheckReturnAvailableRequest.lan', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3180,
  serialized_end=3571,
)


_GETVALIDPRODUCTREQUEST = _descriptor.Descriptor(
  name='GetValidProductRequest',
  full_name='manufactory.GetValidProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.GetValidProductRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.GetValidProductRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.GetValidProductRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='manufactory.GetValidProductRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='manufactory.GetValidProductRequest.product_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.GetValidProductRequest.lan', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3573,
  serialized_end=3698,
)


_GETVALIDPRODUCTRESPONSE = _descriptor.Descriptor(
  name='GetValidProductResponse',
  full_name='manufactory.GetValidProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='manufactory.GetValidProductResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3700,
  serialized_end=3742,
)


_LISTRETURNREQUEST = _descriptor.Descriptor(
  name='ListReturnRequest',
  full_name='manufactory.ListReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='return_by', full_name='manufactory.ListReturnRequest.return_by', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='manufactory.ListReturnRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='manufactory.ListReturnRequest.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_id', full_name='manufactory.ListReturnRequest.source_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_code', full_name='manufactory.ListReturnRequest.source_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='manufactory.ListReturnRequest.logistics_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.ListReturnRequest.type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='manufactory.ListReturnRequest.sub_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='manufactory.ListReturnRequest.product_ids', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_from', full_name='manufactory.ListReturnRequest.return_date_from', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_to', full_name='manufactory.ListReturnRequest.return_date_to', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_from', full_name='manufactory.ListReturnRequest.delivery_date_from', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_to', full_name='manufactory.ListReturnRequest.delivery_date_to', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.ListReturnRequest.limit', index=13,
      number=20, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.ListReturnRequest.offset', index=14,
      number=21, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='manufactory.ListReturnRequest.order', index=15,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='manufactory.ListReturnRequest.sort', index=16,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.ListReturnRequest.lan', index=17,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='manufactory.ListReturnRequest.return_to', index=18,
      number=25, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3745,
  serialized_end=4238,
)


_LISTRETURNRESPONSE = _descriptor.Descriptor(
  name='ListReturnResponse',
  full_name='manufactory.ListReturnResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.ListReturnResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.ListReturnResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4240,
  serialized_end=4311,
)


_GETRETURNBYIDREQUEST = _descriptor.Descriptor(
  name='GetReturnByIdRequest',
  full_name='manufactory.GetReturnByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.GetReturnByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.GetReturnByIdRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4313,
  serialized_end=4360,
)


_GETRETURNPRODUCTBYIDREQUEST = _descriptor.Descriptor(
  name='GetReturnProductByIdRequest',
  full_name='manufactory.GetReturnProductByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.GetReturnProductByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='manufactory.GetReturnProductByIdRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='manufactory.GetReturnProductByIdRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='manufactory.GetReturnProductByIdRequest.sort', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='manufactory.GetReturnProductByIdRequest.order', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.GetReturnProductByIdRequest.lan', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4362,
  serialized_end=4476,
)


_GETRETURNPRODUCTBYIDRESPONSE = _descriptor.Descriptor(
  name='GetReturnProductByIdResponse',
  full_name='manufactory.GetReturnProductByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='manufactory.GetReturnProductByIdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='manufactory.GetReturnProductByIdResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4478,
  serialized_end=4565,
)


_SUBMITRETURNREQUEST = _descriptor.Descriptor(
  name='SubmitReturnRequest',
  full_name='manufactory.SubmitReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.SubmitReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.SubmitReturnRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4567,
  serialized_end=4613,
)


_REJECTRETURNREQUEST = _descriptor.Descriptor(
  name='RejectReturnRequest',
  full_name='manufactory.RejectReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.RejectReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='manufactory.RejectReturnRequest.reject_reason', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.RejectReturnRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4615,
  serialized_end=4684,
)


_APPROVERETURNREQUEST = _descriptor.Descriptor(
  name='ApproveReturnRequest',
  full_name='manufactory.ApproveReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.ApproveReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_type', full_name='manufactory.ApproveReturnRequest.trans_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.ApproveReturnRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4686,
  serialized_end=4753,
)


_CONFIRMRETURNREQUEST = _descriptor.Descriptor(
  name='ConfirmReturnRequest',
  full_name='manufactory.ConfirmReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.ConfirmReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.ConfirmReturnRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4755,
  serialized_end=4802,
)


_DELIVERYRETURNREQUEST = _descriptor.Descriptor(
  name='DeliveryReturnRequest',
  full_name='manufactory.DeliveryReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.DeliveryReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.DeliveryReturnRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4804,
  serialized_end=4852,
)


_UPDATERETURNREQUEST = _descriptor.Descriptor(
  name='UpdateReturnRequest',
  full_name='manufactory.UpdateReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.UpdateReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='manufactory.UpdateReturnRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_reason', full_name='manufactory.UpdateReturnRequest.return_reason', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='manufactory.UpdateReturnRequest.remark', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_delivery_date', full_name='manufactory.UpdateReturnRequest.return_delivery_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_to', full_name='manufactory.UpdateReturnRequest.return_to', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='manufactory.UpdateReturnRequest.attachments', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.UpdateReturnRequest.lan', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='manufactory.UpdateReturnRequest.logistics_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4855,
  serialized_end=5140,
)


_DELETERETURNREQUEST = _descriptor.Descriptor(
  name='DeleteReturnRequest',
  full_name='manufactory.DeleteReturnRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='manufactory.DeleteReturnRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='manufactory.DeleteReturnRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5142,
  serialized_end=5188,
)


_RETURNCOMMONRESPONSE = _descriptor.Descriptor(
  name='ReturnCommonResponse',
  full_name='manufactory.ReturnCommonResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='manufactory.ReturnCommonResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5190,
  serialized_end=5229,
)


_RETURNATTACHMENTS = _descriptor.Descriptor(
  name='ReturnAttachments',
  full_name='manufactory.ReturnAttachments',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='manufactory.ReturnAttachments.type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='manufactory.ReturnAttachments.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5231,
  serialized_end=5277,
)

_RETURNS.fields_by_name['return_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RETURNS.fields_by_name['return_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RETURNS.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RETURNS.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RETURNS.fields_by_name['attachments'].message_type = _RETURNATTACHMENTS
_RETURNS.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_VALIDPRODUCT.fields_by_name['unit'].message_type = _UNIT
_PRODUCTDETAIL.fields_by_name['return_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCTDETAIL.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCTDETAIL.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCTDETAIL.fields_by_name['attachments'].message_type = _RETURNATTACHMENTS
_RETURNPRODUCT.fields_by_name['attachments'].message_type = _RETURNATTACHMENTS
_CREATERETURNREQUEST.fields_by_name['return_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATERETURNREQUEST.fields_by_name['attachments'].message_type = _RETURNATTACHMENTS
_CREATERETURNREQUEST.fields_by_name['products'].message_type = _RETURNPRODUCT
_CHECKRETURNAVAILABLEREQUEST.fields_by_name['return_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CHECKRETURNAVAILABLEREQUEST.fields_by_name['products'].message_type = _RETURNPRODUCT
_CHECKRETURNAVAILABLEREQUEST.fields_by_name['attachments'].message_type = _RETURNATTACHMENTS
_LISTRETURNREQUEST.fields_by_name['return_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNREQUEST.fields_by_name['return_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNREQUEST.fields_by_name['delivery_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNREQUEST.fields_by_name['delivery_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRETURNRESPONSE.fields_by_name['rows'].message_type = _RETURNS
_GETRETURNPRODUCTBYIDRESPONSE.fields_by_name['rows'].message_type = _PRODUCTDETAIL
_UPDATERETURNREQUEST.fields_by_name['products'].message_type = _RETURNPRODUCT
_UPDATERETURNREQUEST.fields_by_name['return_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATERETURNREQUEST.fields_by_name['attachments'].message_type = _RETURNATTACHMENTS
DESCRIPTOR.message_types_by_name['Returns'] = _RETURNS
DESCRIPTOR.message_types_by_name['ValidProduct'] = _VALIDPRODUCT
DESCRIPTOR.message_types_by_name['Unit'] = _UNIT
DESCRIPTOR.message_types_by_name['ProductDetail'] = _PRODUCTDETAIL
DESCRIPTOR.message_types_by_name['ReturnProduct'] = _RETURNPRODUCT
DESCRIPTOR.message_types_by_name['CreateReturnRequest'] = _CREATERETURNREQUEST
DESCRIPTOR.message_types_by_name['CreateReturnResponse'] = _CREATERETURNRESPONSE
DESCRIPTOR.message_types_by_name['CheckReturnAvailableRequest'] = _CHECKRETURNAVAILABLEREQUEST
DESCRIPTOR.message_types_by_name['GetValidProductRequest'] = _GETVALIDPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['GetValidProductResponse'] = _GETVALIDPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['ListReturnRequest'] = _LISTRETURNREQUEST
DESCRIPTOR.message_types_by_name['ListReturnResponse'] = _LISTRETURNRESPONSE
DESCRIPTOR.message_types_by_name['GetReturnByIdRequest'] = _GETRETURNBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetReturnProductByIdRequest'] = _GETRETURNPRODUCTBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetReturnProductByIdResponse'] = _GETRETURNPRODUCTBYIDRESPONSE
DESCRIPTOR.message_types_by_name['SubmitReturnRequest'] = _SUBMITRETURNREQUEST
DESCRIPTOR.message_types_by_name['RejectReturnRequest'] = _REJECTRETURNREQUEST
DESCRIPTOR.message_types_by_name['ApproveReturnRequest'] = _APPROVERETURNREQUEST
DESCRIPTOR.message_types_by_name['ConfirmReturnRequest'] = _CONFIRMRETURNREQUEST
DESCRIPTOR.message_types_by_name['DeliveryReturnRequest'] = _DELIVERYRETURNREQUEST
DESCRIPTOR.message_types_by_name['UpdateReturnRequest'] = _UPDATERETURNREQUEST
DESCRIPTOR.message_types_by_name['DeleteReturnRequest'] = _DELETERETURNREQUEST
DESCRIPTOR.message_types_by_name['ReturnCommonResponse'] = _RETURNCOMMONRESPONSE
DESCRIPTOR.message_types_by_name['ReturnAttachments'] = _RETURNATTACHMENTS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Returns = _reflection.GeneratedProtocolMessageType('Returns', (_message.Message,), dict(
  DESCRIPTOR = _RETURNS,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.Returns)
  ))
_sym_db.RegisterMessage(Returns)

ValidProduct = _reflection.GeneratedProtocolMessageType('ValidProduct', (_message.Message,), dict(
  DESCRIPTOR = _VALIDPRODUCT,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ValidProduct)
  ))
_sym_db.RegisterMessage(ValidProduct)

Unit = _reflection.GeneratedProtocolMessageType('Unit', (_message.Message,), dict(
  DESCRIPTOR = _UNIT,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.Unit)
  ))
_sym_db.RegisterMessage(Unit)

ProductDetail = _reflection.GeneratedProtocolMessageType('ProductDetail', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTDETAIL,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ProductDetail)
  ))
_sym_db.RegisterMessage(ProductDetail)

ReturnProduct = _reflection.GeneratedProtocolMessageType('ReturnProduct', (_message.Message,), dict(
  DESCRIPTOR = _RETURNPRODUCT,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ReturnProduct)
  ))
_sym_db.RegisterMessage(ReturnProduct)

CreateReturnRequest = _reflection.GeneratedProtocolMessageType('CreateReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATERETURNREQUEST,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreateReturnRequest)
  ))
_sym_db.RegisterMessage(CreateReturnRequest)

CreateReturnResponse = _reflection.GeneratedProtocolMessageType('CreateReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATERETURNRESPONSE,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CreateReturnResponse)
  ))
_sym_db.RegisterMessage(CreateReturnResponse)

CheckReturnAvailableRequest = _reflection.GeneratedProtocolMessageType('CheckReturnAvailableRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHECKRETURNAVAILABLEREQUEST,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.CheckReturnAvailableRequest)
  ))
_sym_db.RegisterMessage(CheckReturnAvailableRequest)

GetValidProductRequest = _reflection.GeneratedProtocolMessageType('GetValidProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETVALIDPRODUCTREQUEST,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetValidProductRequest)
  ))
_sym_db.RegisterMessage(GetValidProductRequest)

GetValidProductResponse = _reflection.GeneratedProtocolMessageType('GetValidProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETVALIDPRODUCTRESPONSE,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetValidProductResponse)
  ))
_sym_db.RegisterMessage(GetValidProductResponse)

ListReturnRequest = _reflection.GeneratedProtocolMessageType('ListReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTRETURNREQUEST,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ListReturnRequest)
  ))
_sym_db.RegisterMessage(ListReturnRequest)

ListReturnResponse = _reflection.GeneratedProtocolMessageType('ListReturnResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTRETURNRESPONSE,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ListReturnResponse)
  ))
_sym_db.RegisterMessage(ListReturnResponse)

GetReturnByIdRequest = _reflection.GeneratedProtocolMessageType('GetReturnByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRETURNBYIDREQUEST,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetReturnByIdRequest)
  ))
_sym_db.RegisterMessage(GetReturnByIdRequest)

GetReturnProductByIdRequest = _reflection.GeneratedProtocolMessageType('GetReturnProductByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRETURNPRODUCTBYIDREQUEST,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetReturnProductByIdRequest)
  ))
_sym_db.RegisterMessage(GetReturnProductByIdRequest)

GetReturnProductByIdResponse = _reflection.GeneratedProtocolMessageType('GetReturnProductByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRETURNPRODUCTBYIDRESPONSE,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.GetReturnProductByIdResponse)
  ))
_sym_db.RegisterMessage(GetReturnProductByIdResponse)

SubmitReturnRequest = _reflection.GeneratedProtocolMessageType('SubmitReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITRETURNREQUEST,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.SubmitReturnRequest)
  ))
_sym_db.RegisterMessage(SubmitReturnRequest)

RejectReturnRequest = _reflection.GeneratedProtocolMessageType('RejectReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _REJECTRETURNREQUEST,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.RejectReturnRequest)
  ))
_sym_db.RegisterMessage(RejectReturnRequest)

ApproveReturnRequest = _reflection.GeneratedProtocolMessageType('ApproveReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _APPROVERETURNREQUEST,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ApproveReturnRequest)
  ))
_sym_db.RegisterMessage(ApproveReturnRequest)

ConfirmReturnRequest = _reflection.GeneratedProtocolMessageType('ConfirmReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMRETURNREQUEST,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ConfirmReturnRequest)
  ))
_sym_db.RegisterMessage(ConfirmReturnRequest)

DeliveryReturnRequest = _reflection.GeneratedProtocolMessageType('DeliveryReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELIVERYRETURNREQUEST,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.DeliveryReturnRequest)
  ))
_sym_db.RegisterMessage(DeliveryReturnRequest)

UpdateReturnRequest = _reflection.GeneratedProtocolMessageType('UpdateReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERETURNREQUEST,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.UpdateReturnRequest)
  ))
_sym_db.RegisterMessage(UpdateReturnRequest)

DeleteReturnRequest = _reflection.GeneratedProtocolMessageType('DeleteReturnRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETERETURNREQUEST,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.DeleteReturnRequest)
  ))
_sym_db.RegisterMessage(DeleteReturnRequest)

ReturnCommonResponse = _reflection.GeneratedProtocolMessageType('ReturnCommonResponse', (_message.Message,), dict(
  DESCRIPTOR = _RETURNCOMMONRESPONSE,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ReturnCommonResponse)
  ))
_sym_db.RegisterMessage(ReturnCommonResponse)

ReturnAttachments = _reflection.GeneratedProtocolMessageType('ReturnAttachments', (_message.Message,), dict(
  DESCRIPTOR = _RETURNATTACHMENTS,
  __module__ = 'manufactory.returns_pb2'
  # @@protoc_insertion_point(class_scope:manufactory.ReturnAttachments)
  ))
_sym_db.RegisterMessage(ReturnAttachments)



_MANUFACTORYRETURNSERVICE = _descriptor.ServiceDescriptor(
  name='ManufactoryReturnService',
  full_name='manufactory.ManufactoryReturnService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=5280,
  serialized_end=7181,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateReturn',
    full_name='manufactory.ManufactoryReturnService.CreateReturn',
    index=0,
    containing_service=None,
    input_type=_CREATERETURNREQUEST,
    output_type=_CREATERETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\"\"/api/v2/supply/manufactory/returns:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetValidProduct',
    full_name='manufactory.ManufactoryReturnService.GetValidProduct',
    index=1,
    containing_service=None,
    input_type=_GETVALIDPRODUCTREQUEST,
    output_type=_VALIDPRODUCT,
    serialized_options=_b('\202\323\344\223\0022\0220/api/v2/supply/manufactory/returns/valid_product'),
  ),
  _descriptor.MethodDescriptor(
    name='ListReturn',
    full_name='manufactory.ManufactoryReturnService.ListReturn',
    index=2,
    containing_service=None,
    input_type=_LISTRETURNREQUEST,
    output_type=_LISTRETURNRESPONSE,
    serialized_options=_b('\202\323\344\223\002$\022\"/api/v2/supply/manufactory/returns'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReturnById',
    full_name='manufactory.ManufactoryReturnService.GetReturnById',
    index=3,
    containing_service=None,
    input_type=_GETRETURNBYIDREQUEST,
    output_type=_RETURNS,
    serialized_options=_b('\202\323\344\223\002)\022\'/api/v2/supply/manufactory/returns/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReturnProductById',
    full_name='manufactory.ManufactoryReturnService.GetReturnProductById',
    index=4,
    containing_service=None,
    input_type=_GETRETURNPRODUCTBYIDREQUEST,
    output_type=_GETRETURNPRODUCTBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0021\022//api/v2/supply/manufactory/returns/{id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='SubmitReturn',
    full_name='manufactory.ManufactoryReturnService.SubmitReturn',
    index=5,
    containing_service=None,
    input_type=_SUBMITRETURNREQUEST,
    output_type=_RETURNCOMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0023\032./api/v2/supply/manufactory/returns/{id}/submit:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='RejectReturn',
    full_name='manufactory.ManufactoryReturnService.RejectReturn',
    index=6,
    containing_service=None,
    input_type=_REJECTRETURNREQUEST,
    output_type=_RETURNCOMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0023\032./api/v2/supply/manufactory/returns/{id}/reject:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ApproveReturn',
    full_name='manufactory.ManufactoryReturnService.ApproveReturn',
    index=7,
    containing_service=None,
    input_type=_APPROVERETURNREQUEST,
    output_type=_RETURNCOMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0024\032//api/v2/supply/manufactory/returns/{id}/approve:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmReturn',
    full_name='manufactory.ManufactoryReturnService.ConfirmReturn',
    index=8,
    containing_service=None,
    input_type=_CONFIRMRETURNREQUEST,
    output_type=_RETURNCOMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0024\032//api/v2/supply/manufactory/returns/{id}/confirm:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeliveryReturn',
    full_name='manufactory.ManufactoryReturnService.DeliveryReturn',
    index=9,
    containing_service=None,
    input_type=_DELIVERYRETURNREQUEST,
    output_type=_RETURNCOMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\0320/api/v2/supply/manufactory/returns/{id}/delivery:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateReturn',
    full_name='manufactory.ManufactoryReturnService.UpdateReturn',
    index=10,
    containing_service=None,
    input_type=_UPDATERETURNREQUEST,
    output_type=_RETURNCOMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0023\032./api/v2/supply/manufactory/returns/{id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteReturn',
    full_name='manufactory.ManufactoryReturnService.DeleteReturn',
    index=11,
    containing_service=None,
    input_type=_DELETERETURNREQUEST,
    output_type=_RETURNCOMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0023\032./api/v2/supply/manufactory/returns/{id}/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CheckReturnAvailableByrec',
    full_name='manufactory.ManufactoryReturnService.CheckReturnAvailableByrec',
    index=12,
    containing_service=None,
    input_type=_CHECKRETURNAVAILABLEREQUEST,
    output_type=_RETURNCOMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\002-\"(/api/v2/supply/manufactory/returns/check:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_MANUFACTORYRETURNSERVICE)

DESCRIPTOR.services_by_name['ManufactoryReturnService'] = _MANUFACTORYRETURNSERVICE

# @@protoc_insertion_point(module_scope)
