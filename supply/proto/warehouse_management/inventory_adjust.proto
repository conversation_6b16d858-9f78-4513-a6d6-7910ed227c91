
syntax = "proto3";

package warehouse_management;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

service WarehouseInventoryAdjustService {

    // 根据仓库id查询可调整订货商品
    rpc GetDemandAdjustProductByBranchId (GetDemandAdjustProductByBranchIdRequest) returns (GetDemandAdjustProductByBranchIdResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/warehouse/inventory_adjust/valid_product/{store_id}"
        };
    }

    // 创建订货调整单
    rpc CreateDemandAdjust (CreateDemandAdjustRequest) returns (Response) {
        option (google.api.http) = {
        post: "/api/v2/supply/warehouse/inventory_adjust"
        body: "*"
        };
    }

    // 修改订单状态统一入口
    rpc DealDemandAdjustById (DealDemandAdjustByIdRequest) returns (Response) {
        option (google.api.http) = {
        put: "/api/v2/supply/warehouse/demand/{id}/{action}"
        body: "*"
        };
    };
}


// 查询可订货商品请求参数
message GetDemandAdjustProductByBranchIdRequest {
    // 仓库id
    uint64 store_id = 1;
    // 订货类型:门市订货(SD)、紧急订货(HD)、主配订货(MD)
    string type = 2;
    // 订货日期
    google.protobuf.Timestamp order_date = 3;
    // 物流模式: 配送-NMD, 直送-PUR
    string distribution_type = 4;
    // 配送中心
    uint64 vendor_id = 5;
    repeated uint64 category_ids = 6;
    string lan = 7;
}

message DemandAdjustProductUnit {
    // 订货单位id
    uint64 unit_id = 1;
    // 订货单位名称
    string unit_name = 2;
    // 订货code
    string unit_code = 3;
    // 比率
    double unit_rate = 4;
}

message DemandAdjustProduct {
    // 到货天数
    int32 arrival_days = 1;
    // 配送/供应商中心
    uint64 distribute_by = 2;
    // 配送(NMD)、采购(PUR)
    string distribution_type = 3;
    // 递增订量
    float increment_quantity = 4;
    // 最大订货量
    float max_quantity = 5;
    // 最小订货量
    float min_quantity = 6;
    // 商品类别id
    uint64 product_category_id = 7;
    // 商品编码
    string product_code = 8;
    // 商品id
    uint64 product_id = 9;
    // 商品名称
    string product_name = 10;
    // 订货数量
    double quantity = 11;
    // 规格
    string spec = 12;
    // 商品储藏类型编码
    string storage_type = 13;
    //多单位调整
    repeated DemandAdjustProductUnit units = 14;
    // 销售类型
    string sale_type = 17;
    // 商品属性
    string product_type = 18;
    // 建议订货量
    float suggest_quantity = 20;
    // 成品订货量
    float finished_quantity = 21;
    // 热麦订货量
    float bread_quantity = 22;
    // 茶饮订货量
    float tea_quantity = 23;
    // 原料订货量
    float raw_quantity = 24;
    // 直采价格
    float purchase_price = 25;
    // 直采税率
    float purchase_tax = 26;
    // 订货记录id(如果有此id则要传此id)
    uint64 id = 27;
    // 配送周期类型
    string distribution_circle = 28;
    // 配送中心
    uint64 distribution_center_id = 29;
    // 供应商
    uint64 vendor_id = 30;
    //
    string cycle_extends = 31;
}

message GetDemandAdjustProductByBranchIdResponse {
    repeated DemandAdjustProduct rows = 1;
    uint64 total = 2;
}

message CreateDemandAdjustRequest {
    // 订货日期
    google.protobuf.Timestamp demand_date = 1;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 2;
    // 备注
    string remark = 3;
    // 门市紧急订货单-HD，主配单-MD
    string type = 4;
    // 主配类型枚举类(仓库主配:STORE, 商品主配:PRODUCT)(主配单才有的字段)
    string sub_type = 5;
    // 仓库id
    uint64 store_id = 6;
    message Products {
        // 仓库id或者商品id
        uint64 product_id = 1;
        // 订货数量
        double quantity = 2;
        // 商品订货物流模式(NMD:配送, PUR:直送)
        string distribution_type = 3;
        uint64 centre_id = 4;
        uint64 unit_id = 5;
    }
    // 商品
    repeated Products products = 7;
}

// 处理单据
message DealDemandAdjustByIdRequest {
    uint64 id = 1;
    // 单据业务状态(INITED,F_COMMIT(成品订货提交),COMMIT(提交), CAL_DONE(原料计算完成, 这步不能通过前端修改), CAL_FAILED(原料计算失败，这步也不能通过前端修改),REJECTED(驳回订货单),APPROVED(审核订货单通过),FREEZE(冻结订货单),CANCELLED
    string action = 2;
    string description = 3;
    // 附件
    string attachments = 4;
}

// 统一返回对象
message Response {
    string description = 1;
}