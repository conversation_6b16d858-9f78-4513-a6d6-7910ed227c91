# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: warehouse_management/inventory_adjust.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='warehouse_management/inventory_adjust.proto',
  package='warehouse_management',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n+warehouse_management/inventory_adjust.proto\x12\x14warehouse_management\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xca\x01\n\'GetDemandAdjustProductByBranchIdRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x0c\n\x04type\x18\x02 \x01(\t\x12.\n\norder_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x19\n\x11\x64istribution_type\x18\x04 \x01(\t\x12\x11\n\tvendor_id\x18\x05 \x01(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x06 \x03(\x04\x12\x0b\n\x03lan\x18\x07 \x01(\t\"c\n\x17\x44\x65mandAdjustProductUnit\x12\x0f\n\x07unit_id\x18\x01 \x01(\x04\x12\x11\n\tunit_name\x18\x02 \x01(\t\x12\x11\n\tunit_code\x18\x03 \x01(\t\x12\x11\n\tunit_rate\x18\x04 \x01(\x01\"\xb9\x05\n\x13\x44\x65mandAdjustProduct\x12\x14\n\x0c\x61rrival_days\x18\x01 \x01(\x05\x12\x15\n\rdistribute_by\x18\x02 \x01(\x04\x12\x19\n\x11\x64istribution_type\x18\x03 \x01(\t\x12\x1a\n\x12increment_quantity\x18\x04 \x01(\x02\x12\x14\n\x0cmax_quantity\x18\x05 \x01(\x02\x12\x14\n\x0cmin_quantity\x18\x06 \x01(\x02\x12\x1b\n\x13product_category_id\x18\x07 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x08 \x01(\t\x12\x12\n\nproduct_id\x18\t \x01(\x04\x12\x14\n\x0cproduct_name\x18\n \x01(\t\x12\x10\n\x08quantity\x18\x0b \x01(\x01\x12\x0c\n\x04spec\x18\x0c \x01(\t\x12\x14\n\x0cstorage_type\x18\r \x01(\t\x12<\n\x05units\x18\x0e \x03(\x0b\x32-.warehouse_management.DemandAdjustProductUnit\x12\x11\n\tsale_type\x18\x11 \x01(\t\x12\x14\n\x0cproduct_type\x18\x12 \x01(\t\x12\x18\n\x10suggest_quantity\x18\x14 \x01(\x02\x12\x19\n\x11\x66inished_quantity\x18\x15 \x01(\x02\x12\x16\n\x0e\x62read_quantity\x18\x16 \x01(\x02\x12\x14\n\x0ctea_quantity\x18\x17 \x01(\x02\x12\x14\n\x0craw_quantity\x18\x18 \x01(\x02\x12\x16\n\x0epurchase_price\x18\x19 \x01(\x02\x12\x14\n\x0cpurchase_tax\x18\x1a \x01(\x02\x12\n\n\x02id\x18\x1b \x01(\x04\x12\x1b\n\x13\x64istribution_circle\x18\x1c \x01(\t\x12\x1e\n\x16\x64istribution_center_id\x18\x1d \x01(\x04\x12\x11\n\tvendor_id\x18\x1e \x01(\x04\x12\x15\n\rcycle_extends\x18\x1f \x01(\t\"r\n(GetDemandAdjustProductByBranchIdResponse\x12\x37\n\x04rows\x18\x01 \x03(\x0b\x32).warehouse_management.DemandAdjustProduct\x12\r\n\x05total\x18\x02 \x01(\x04\"\xfd\x02\n\x19\x43reateDemandAdjustRequest\x12/\n\x0b\x64\x65mand_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06remark\x18\x03 \x01(\t\x12\x0c\n\x04type\x18\x04 \x01(\t\x12\x10\n\x08sub_type\x18\x05 \x01(\t\x12\x10\n\x08store_id\x18\x06 \x01(\x04\x12J\n\x08products\x18\x07 \x03(\x0b\x32\x38.warehouse_management.CreateDemandAdjustRequest.Products\x1ao\n\x08Products\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x10\n\x08quantity\x18\x02 \x01(\x01\x12\x19\n\x11\x64istribution_type\x18\x03 \x01(\t\x12\x11\n\tcentre_id\x18\x04 \x01(\x04\x12\x0f\n\x07unit_id\x18\x05 \x01(\x04\"c\n\x1b\x44\x65\x61lDemandAdjustByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06\x61\x63tion\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x13\n\x0b\x61ttachments\x18\x04 \x01(\t\"\x1f\n\x08Response\x12\x13\n\x0b\x64\x65scription\x18\x01 \x01(\t2\xd5\x04\n\x1fWarehouseInventoryAdjustService\x12\xed\x01\n GetDemandAdjustProductByBranchId\x12=.warehouse_management.GetDemandAdjustProductByBranchIdRequest\x1a>.warehouse_management.GetDemandAdjustProductByBranchIdResponse\"J\x82\xd3\xe4\x93\x02\x44\x12\x42/api/v2/supply/warehouse/inventory_adjust/valid_product/{store_id}\x12\x9b\x01\n\x12\x43reateDemandAdjust\x12/.warehouse_management.CreateDemandAdjustRequest\x1a\x1e.warehouse_management.Response\"4\x82\xd3\xe4\x93\x02.\")/api/v2/supply/warehouse/inventory_adjust:\x01*\x12\xa3\x01\n\x14\x44\x65\x61lDemandAdjustById\x12\x31.warehouse_management.DealDemandAdjustByIdRequest\x1a\x1e.warehouse_management.Response\"8\x82\xd3\xe4\x93\x02\x32\x1a-/api/v2/supply/warehouse/demand/{id}/{action}:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_GETDEMANDADJUSTPRODUCTBYBRANCHIDREQUEST = _descriptor.Descriptor(
  name='GetDemandAdjustProductByBranchIdRequest',
  full_name='warehouse_management.GetDemandAdjustProductByBranchIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='warehouse_management.GetDemandAdjustProductByBranchIdRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse_management.GetDemandAdjustProductByBranchIdRequest.type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date', full_name='warehouse_management.GetDemandAdjustProductByBranchIdRequest.order_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='warehouse_management.GetDemandAdjustProductByBranchIdRequest.distribution_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_id', full_name='warehouse_management.GetDemandAdjustProductByBranchIdRequest.vendor_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='warehouse_management.GetDemandAdjustProductByBranchIdRequest.category_ids', index=5,
      number=6, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse_management.GetDemandAdjustProductByBranchIdRequest.lan', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=133,
  serialized_end=335,
)


_DEMANDADJUSTPRODUCTUNIT = _descriptor.Descriptor(
  name='DemandAdjustProductUnit',
  full_name='warehouse_management.DemandAdjustProductUnit',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='warehouse_management.DemandAdjustProductUnit.unit_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='warehouse_management.DemandAdjustProductUnit.unit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_code', full_name='warehouse_management.DemandAdjustProductUnit.unit_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='warehouse_management.DemandAdjustProductUnit.unit_rate', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=337,
  serialized_end=436,
)


_DEMANDADJUSTPRODUCT = _descriptor.Descriptor(
  name='DemandAdjustProduct',
  full_name='warehouse_management.DemandAdjustProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='arrival_days', full_name='warehouse_management.DemandAdjustProduct.arrival_days', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribute_by', full_name='warehouse_management.DemandAdjustProduct.distribute_by', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='warehouse_management.DemandAdjustProduct.distribution_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_quantity', full_name='warehouse_management.DemandAdjustProduct.increment_quantity', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_quantity', full_name='warehouse_management.DemandAdjustProduct.max_quantity', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_quantity', full_name='warehouse_management.DemandAdjustProduct.min_quantity', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_category_id', full_name='warehouse_management.DemandAdjustProduct.product_category_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='warehouse_management.DemandAdjustProduct.product_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='warehouse_management.DemandAdjustProduct.product_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse_management.DemandAdjustProduct.product_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='warehouse_management.DemandAdjustProduct.quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='warehouse_management.DemandAdjustProduct.spec', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='warehouse_management.DemandAdjustProduct.storage_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='warehouse_management.DemandAdjustProduct.units', index=13,
      number=14, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sale_type', full_name='warehouse_management.DemandAdjustProduct.sale_type', index=14,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='warehouse_management.DemandAdjustProduct.product_type', index=15,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='suggest_quantity', full_name='warehouse_management.DemandAdjustProduct.suggest_quantity', index=16,
      number=20, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='finished_quantity', full_name='warehouse_management.DemandAdjustProduct.finished_quantity', index=17,
      number=21, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bread_quantity', full_name='warehouse_management.DemandAdjustProduct.bread_quantity', index=18,
      number=22, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tea_quantity', full_name='warehouse_management.DemandAdjustProduct.tea_quantity', index=19,
      number=23, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='raw_quantity', full_name='warehouse_management.DemandAdjustProduct.raw_quantity', index=20,
      number=24, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_price', full_name='warehouse_management.DemandAdjustProduct.purchase_price', index=21,
      number=25, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_tax', full_name='warehouse_management.DemandAdjustProduct.purchase_tax', index=22,
      number=26, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse_management.DemandAdjustProduct.id', index=23,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_circle', full_name='warehouse_management.DemandAdjustProduct.distribution_circle', index=24,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_center_id', full_name='warehouse_management.DemandAdjustProduct.distribution_center_id', index=25,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_id', full_name='warehouse_management.DemandAdjustProduct.vendor_id', index=26,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cycle_extends', full_name='warehouse_management.DemandAdjustProduct.cycle_extends', index=27,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=439,
  serialized_end=1136,
)


_GETDEMANDADJUSTPRODUCTBYBRANCHIDRESPONSE = _descriptor.Descriptor(
  name='GetDemandAdjustProductByBranchIdResponse',
  full_name='warehouse_management.GetDemandAdjustProductByBranchIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse_management.GetDemandAdjustProductByBranchIdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse_management.GetDemandAdjustProductByBranchIdResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1138,
  serialized_end=1252,
)


_CREATEDEMANDADJUSTREQUEST_PRODUCTS = _descriptor.Descriptor(
  name='Products',
  full_name='warehouse_management.CreateDemandAdjustRequest.Products',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='warehouse_management.CreateDemandAdjustRequest.Products.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='warehouse_management.CreateDemandAdjustRequest.Products.quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='warehouse_management.CreateDemandAdjustRequest.Products.distribution_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='centre_id', full_name='warehouse_management.CreateDemandAdjustRequest.Products.centre_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='warehouse_management.CreateDemandAdjustRequest.Products.unit_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1525,
  serialized_end=1636,
)

_CREATEDEMANDADJUSTREQUEST = _descriptor.Descriptor(
  name='CreateDemandAdjustRequest',
  full_name='warehouse_management.CreateDemandAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='warehouse_management.CreateDemandAdjustRequest.demand_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='warehouse_management.CreateDemandAdjustRequest.arrival_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='warehouse_management.CreateDemandAdjustRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse_management.CreateDemandAdjustRequest.type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_type', full_name='warehouse_management.CreateDemandAdjustRequest.sub_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='warehouse_management.CreateDemandAdjustRequest.store_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='warehouse_management.CreateDemandAdjustRequest.products', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_CREATEDEMANDADJUSTREQUEST_PRODUCTS, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1255,
  serialized_end=1636,
)


_DEALDEMANDADJUSTBYIDREQUEST = _descriptor.Descriptor(
  name='DealDemandAdjustByIdRequest',
  full_name='warehouse_management.DealDemandAdjustByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse_management.DealDemandAdjustByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='warehouse_management.DealDemandAdjustByIdRequest.action', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='warehouse_management.DealDemandAdjustByIdRequest.description', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='warehouse_management.DealDemandAdjustByIdRequest.attachments', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1638,
  serialized_end=1737,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='warehouse_management.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='description', full_name='warehouse_management.Response.description', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1739,
  serialized_end=1770,
)

_GETDEMANDADJUSTPRODUCTBYBRANCHIDREQUEST.fields_by_name['order_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DEMANDADJUSTPRODUCT.fields_by_name['units'].message_type = _DEMANDADJUSTPRODUCTUNIT
_GETDEMANDADJUSTPRODUCTBYBRANCHIDRESPONSE.fields_by_name['rows'].message_type = _DEMANDADJUSTPRODUCT
_CREATEDEMANDADJUSTREQUEST_PRODUCTS.containing_type = _CREATEDEMANDADJUSTREQUEST
_CREATEDEMANDADJUSTREQUEST.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEDEMANDADJUSTREQUEST.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEDEMANDADJUSTREQUEST.fields_by_name['products'].message_type = _CREATEDEMANDADJUSTREQUEST_PRODUCTS
DESCRIPTOR.message_types_by_name['GetDemandAdjustProductByBranchIdRequest'] = _GETDEMANDADJUSTPRODUCTBYBRANCHIDREQUEST
DESCRIPTOR.message_types_by_name['DemandAdjustProductUnit'] = _DEMANDADJUSTPRODUCTUNIT
DESCRIPTOR.message_types_by_name['DemandAdjustProduct'] = _DEMANDADJUSTPRODUCT
DESCRIPTOR.message_types_by_name['GetDemandAdjustProductByBranchIdResponse'] = _GETDEMANDADJUSTPRODUCTBYBRANCHIDRESPONSE
DESCRIPTOR.message_types_by_name['CreateDemandAdjustRequest'] = _CREATEDEMANDADJUSTREQUEST
DESCRIPTOR.message_types_by_name['DealDemandAdjustByIdRequest'] = _DEALDEMANDADJUSTBYIDREQUEST
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetDemandAdjustProductByBranchIdRequest = _reflection.GeneratedProtocolMessageType('GetDemandAdjustProductByBranchIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDEMANDADJUSTPRODUCTBYBRANCHIDREQUEST,
  __module__ = 'warehouse_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse_management.GetDemandAdjustProductByBranchIdRequest)
  ))
_sym_db.RegisterMessage(GetDemandAdjustProductByBranchIdRequest)

DemandAdjustProductUnit = _reflection.GeneratedProtocolMessageType('DemandAdjustProductUnit', (_message.Message,), dict(
  DESCRIPTOR = _DEMANDADJUSTPRODUCTUNIT,
  __module__ = 'warehouse_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse_management.DemandAdjustProductUnit)
  ))
_sym_db.RegisterMessage(DemandAdjustProductUnit)

DemandAdjustProduct = _reflection.GeneratedProtocolMessageType('DemandAdjustProduct', (_message.Message,), dict(
  DESCRIPTOR = _DEMANDADJUSTPRODUCT,
  __module__ = 'warehouse_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse_management.DemandAdjustProduct)
  ))
_sym_db.RegisterMessage(DemandAdjustProduct)

GetDemandAdjustProductByBranchIdResponse = _reflection.GeneratedProtocolMessageType('GetDemandAdjustProductByBranchIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETDEMANDADJUSTPRODUCTBYBRANCHIDRESPONSE,
  __module__ = 'warehouse_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse_management.GetDemandAdjustProductByBranchIdResponse)
  ))
_sym_db.RegisterMessage(GetDemandAdjustProductByBranchIdResponse)

CreateDemandAdjustRequest = _reflection.GeneratedProtocolMessageType('CreateDemandAdjustRequest', (_message.Message,), dict(

  Products = _reflection.GeneratedProtocolMessageType('Products', (_message.Message,), dict(
    DESCRIPTOR = _CREATEDEMANDADJUSTREQUEST_PRODUCTS,
    __module__ = 'warehouse_management.inventory_adjust_pb2'
    # @@protoc_insertion_point(class_scope:warehouse_management.CreateDemandAdjustRequest.Products)
    ))
  ,
  DESCRIPTOR = _CREATEDEMANDADJUSTREQUEST,
  __module__ = 'warehouse_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse_management.CreateDemandAdjustRequest)
  ))
_sym_db.RegisterMessage(CreateDemandAdjustRequest)
_sym_db.RegisterMessage(CreateDemandAdjustRequest.Products)

DealDemandAdjustByIdRequest = _reflection.GeneratedProtocolMessageType('DealDemandAdjustByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _DEALDEMANDADJUSTBYIDREQUEST,
  __module__ = 'warehouse_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse_management.DealDemandAdjustByIdRequest)
  ))
_sym_db.RegisterMessage(DealDemandAdjustByIdRequest)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'warehouse_management.inventory_adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse_management.Response)
  ))
_sym_db.RegisterMessage(Response)



_WAREHOUSEINVENTORYADJUSTSERVICE = _descriptor.ServiceDescriptor(
  name='WarehouseInventoryAdjustService',
  full_name='warehouse_management.WarehouseInventoryAdjustService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=1773,
  serialized_end=2370,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetDemandAdjustProductByBranchId',
    full_name='warehouse_management.WarehouseInventoryAdjustService.GetDemandAdjustProductByBranchId',
    index=0,
    containing_service=None,
    input_type=_GETDEMANDADJUSTPRODUCTBYBRANCHIDREQUEST,
    output_type=_GETDEMANDADJUSTPRODUCTBYBRANCHIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002D\022B/api/v2/supply/warehouse/inventory_adjust/valid_product/{store_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='CreateDemandAdjust',
    full_name='warehouse_management.WarehouseInventoryAdjustService.CreateDemandAdjust',
    index=1,
    containing_service=None,
    input_type=_CREATEDEMANDADJUSTREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002.\")/api/v2/supply/warehouse/inventory_adjust:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DealDemandAdjustById',
    full_name='warehouse_management.WarehouseInventoryAdjustService.DealDemandAdjustById',
    index=2,
    containing_service=None,
    input_type=_DEALDEMANDADJUSTBYIDREQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\0022\032-/api/v2/supply/warehouse/demand/{id}/{action}:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_WAREHOUSEINVENTORYADJUSTSERVICE)

DESCRIPTOR.services_by_name['WarehouseInventoryAdjustService'] = _WAREHOUSEINVENTORYADJUSTSERVICE

# @@protoc_insertion_point(module_scope)
