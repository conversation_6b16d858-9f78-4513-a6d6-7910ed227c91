for file in `ls -  ls  */*.proto`; do
    echo $file
    echo  protoc --proto_path=. \
        --proto_path=/Users/<USER>/go/src/github.com/googleapis/ \
                --proto_path=/Users/<USER>/go/src/ \
        --openapiv2_out . \
        --openapiv2_opt Mexample.proto=. \
        --openapiv2_opt logtostderr=true \
        --openapiv2_opt json_names_for_fields=false \
                ./$file
    eval  protoc --proto_path=. \
        --proto_path=/Users/<USER>/go/src/github.com/googleapis/ \
                --proto_path=/Users/<USER>/go/src/ \
        --openapiv2_out . \
        --openapiv2_opt Mexample.proto=. \
        --openapiv2_opt logtostderr=true \
        --openapiv2_opt json_names_for_fields=false \
                ./$file

done