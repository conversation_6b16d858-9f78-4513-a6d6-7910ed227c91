# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: metadata/schema/schema.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='metadata/schema/schema.proto',
  package='schema',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1cmetadata/schema/schema.proto\x12\x06schema\x1a\x1cgoogle/api/annotations.proto\x1a\x1cgoogle/protobuf/struct.proto\"j\n\x17GetSchemaRefByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x19\n\x11include_attr_refs\x18\x02 \x01(\x08\x12(\n\x07\x65xtends\x18\x03 \x01(\x0b\x32\x17.google.protobuf.Struct\"p\n\x14GetSchemaByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x15\n\rinclude_attrs\x18\x02 \x01(\x08\x12(\n\x07\x65xtends\x18\x03 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x04 \x01(\t\"3\n\x18GetSchemaAttrByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"W\n\x12GetFormByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12(\n\x07\x65xtends\x18\x02 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x03 \x01(\t\"q\n\x13\x41\x64\x64SchemaRefRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05title\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12(\n\x07\x65xtends\x18\x04 \x01(\x0b\x32\x17.google.protobuf.Struct\"M\n\x16UpdateSchemaRefRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\'\n\x06\x66ields\x18\x02 \x01(\x0b\x32\x17.google.protobuf.Struct\"\xbd\x01\n\x14ListSchemaRefRequest\x12\r\n\x05limit\x18\x01 \x01(\x05\x12\x0e\n\x06offset\x18\x02 \x01(\x05\x12\x0c\n\x04sort\x18\x03 \x01(\t\x12\r\n\x05order\x18\x04 \x01(\t\x12\x15\n\rinclude_total\x18\x05 \x01(\x08\x12(\n\x07\x66ilters\x18\x06 \x01(\x0b\x32\x17.google.protobuf.Struct\x12(\n\x07\x65xtends\x18\x07 \x01(\x0b\x32\x17.google.protobuf.Struct\"\xf3\x01\n\x11ListSchemaRequest\x12\x0e\n\x06status\x18\x01 \x01(\t\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x0c\n\x04sort\x18\x04 \x01(\t\x12\r\n\x05order\x18\x05 \x01(\t\x12\x15\n\rinclude_total\x18\x06 \x01(\x08\x12(\n\x07\x66ilters\x18\x07 \x01(\x0b\x32\x17.google.protobuf.Struct\x12(\n\x07\x65xtends\x18\x08 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\t \x01(\t\x12\x0b\n\x03ids\x18\n \x03(\x04\x12\r\n\x05names\x18\x0b \x03(\t\"|\n$ListSchemaWithLocalizationMapRequest\x12\r\n\x05names\x18\x01 \x03(\t\x12\x0b\n\x03lan\x18\x02 \x01(\t\x12\x15\n\rinclude_attrs\x18\x03 \x01(\x08\x12!\n\x19include_all_localizations\x18\x04 \x01(\x08\"\xee\x01\n\x15ListSchemaAttrRequest\x12\x11\n\tschema_id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x0e\n\x06offset\x18\x04 \x01(\x05\x12\x0c\n\x04sort\x18\x05 \x01(\t\x12\r\n\x05order\x18\x06 \x01(\t\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12(\n\x07\x66ilters\x18\x08 \x01(\x0b\x32\x17.google.protobuf.Struct\x12(\n\x07\x65xtends\x18\t \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\n \x01(\t\"\xd5\x01\n\x0fListFormRequest\x12\x0e\n\x06status\x18\x01 \x01(\t\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x0c\n\x04sort\x18\x04 \x01(\t\x12\r\n\x05order\x18\x05 \x01(\t\x12\x15\n\rinclude_total\x18\x06 \x01(\x08\x12(\n\x07\x66ilters\x18\x07 \x01(\x0b\x32\x17.google.protobuf.Struct\x12(\n\x07\x65xtends\x18\x08 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\t \x01(\t\"\xb8\x01\n\x17\x41\x64\x64SchemaRefAttrRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05title\x18\x02 \x01(\t\x12\x15\n\rschema_ref_id\x18\x03 \x01(\x04\x12\x17\n\x0f\x61ttr_owner_type\x18\x04 \x01(\t\x12\x16\n\x0e\x61ttr_data_type\x18\x05 \x01(\t\x12(\n\x07\x65xtends\x18\x06 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0e\n\x06status\x18\x07 \x01(\t\"Q\n\x1aUpdateSchemaRefAttrRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\'\n\x06\x66ields\x18\x02 \x01(\x0b\x32\x17.google.protobuf.Struct\"\xdc\x01\n\x10\x41\x64\x64SchemaRequest\x12\x15\n\rschema_ref_id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\r\n\x05title\x18\x03 \x01(\t\x12\x14\n\x0cignore_state\x18\x04 \x01(\x08\x12\x14\n\x0c\x61llow_parent\x18\x05 \x01(\x08\x12\x0c\n\x04\x66orm\x18\x06 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x07 \x01(\t\x12(\n\x07\x65xtends\x18\x08 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0e\n\x06status\x18\t \x01(\t\x12\x0b\n\x03lan\x18\n \x01(\t\"W\n\x13UpdateSchemaRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\'\n\x06\x66ields\x18\x02 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x03 \x01(\t\"\xa4\x02\n\x14\x41\x64\x64SchemaAttrRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05title\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x11\n\tschema_id\x18\x04 \x01(\x04\x12\x13\n\x0bref_attr_id\x18\x05 \x01(\x04\x12\x17\n\x0f\x61ttr_owner_type\x18\x06 \x01(\t\x12\x16\n\x0e\x61ttr_data_type\x18\x07 \x01(\t\x12,\n\x0b\x63onstraints\x18\x08 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0c\n\x04\x66orm\x18\t \x01(\t\x12(\n\x07\x65xtends\x18\n \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0e\n\x06status\x18\x0b \x01(\t\x12\x0b\n\x03lan\x18\x0c \x01(\t\"[\n\x17UpdateSchemaAttrRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\'\n\x06\x66ields\x18\x02 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x03 \x01(\t\"h\n\x1eUpdateSchemaAttrByBatchRequest\x12\x11\n\tschema_id\x18\x01 \x01(\x04\x12&\n\x05\x61ttrs\x18\x02 \x03(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x03 \x01(\t\")\n\x1b\x44\x65leteSchemaAttrByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\"\xef\x01\n\x18\x41\x64\x64SchemaRelationRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05title\x18\x02 \x01(\t\x12\x18\n\x10source_schema_id\x18\x03 \x01(\x04\x12\x18\n\x10target_schema_id\x18\x04 \x01(\x04\x12\x14\n\x0cignore_state\x18\x05 \x01(\x08\x12\x0c\n\x04\x66orm\x18\x06 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x07 \x01(\t\x12,\n\x0b\x63onstraints\x18\x08 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0e\n\x06status\x18\t \x01(\t\x12\x0b\n\x03lan\x18\n \x01(\t\"x\n\x1cGetSchemaRelationByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x15\n\rinclude_attrs\x18\x02 \x01(\x08\x12(\n\x07\x65xtends\x18\x03 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x04 \x01(\t\"\xa4\x02\n\x19ListSchemaRelationRequest\x12\x18\n\x10source_schema_id\x18\x01 \x01(\x04\x12\x18\n\x10target_schema_id\x18\x02 \x01(\x04\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x0e\n\x06offset\x18\x04 \x01(\x05\x12\x0c\n\x04sort\x18\x05 \x01(\t\x12\x1e\n\x05order\x18\x06 \x01(\x0e\x32\x0f.schema.OrderBy\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12(\n\x07\x66ilters\x18\x08 \x01(\x0b\x32\x17.google.protobuf.Struct\x12(\n\x07\x65xtends\x18\t \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0e\n\x06status\x18\n \x01(\t\x12\x0b\n\x03lan\x18\x0b \x01(\t\"\xb0\x01\n,ListSchemaRelationWithLocalizationMapRequest\x12\x1a\n\x12source_schema_name\x18\x01 \x01(\t\x12\x1d\n\x15schema_relation_names\x18\x02 \x03(\t\x12\x0b\n\x03lan\x18\x03 \x01(\t\x12\x15\n\rinclude_attrs\x18\x04 \x01(\x08\x12!\n\x19include_all_localizations\x18\x05 \x01(\x08\"_\n\x1bUpdateSchemaRelationRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\'\n\x06\x66ields\x18\x02 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x03 \x01(\t\"\xf2\x01\n\x1c\x41\x64\x64SchemaRelationAttrRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05title\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x1a\n\x12schema_relation_id\x18\x04 \x01(\x04\x12\x13\n\x0bref_attr_id\x18\x05 \x01(\x04\x12\x16\n\x0e\x61ttr_data_type\x18\x06 \x01(\t\x12,\n\x0b\x63onstraints\x18\x07 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0c\n\x04\x66orm\x18\x08 \x01(\t\x12\x0e\n\x06status\x18\t \x01(\t\x12\x0b\n\x03lan\x18\n \x01(\t\"c\n\x1fUpdateSchemaRelationAttrRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\'\n\x06\x66ields\x18\x02 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x03 \x01(\t\"y\n&UpdateSchemaRelationAttrByBatchRequest\x12\x1a\n\x12schema_relation_id\x18\x01 \x01(\x04\x12&\n\x05\x61ttrs\x18\x02 \x03(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x03 \x01(\t\";\n GetSchemaRelationAttrByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\xff\x01\n\x1dListSchemaRelationAttrRequest\x12\x1a\n\x12schema_relation_id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x0e\n\x06offset\x18\x04 \x01(\x05\x12\x0c\n\x04sort\x18\x05 \x01(\t\x12\r\n\x05order\x18\x06 \x01(\t\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12(\n\x07\x66ilters\x18\x08 \x01(\x0b\x32\x17.google.protobuf.Struct\x12(\n\x07\x65xtends\x18\t \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\n \x01(\t\"1\n#DeleteSchemaRelationAttrByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\"m\n\x0e\x41\x64\x64\x46ormRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05title\x18\x02 \x01(\t\x12\x0c\n\x04\x66orm\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x0e\n\x06status\x18\x05 \x01(\t\x12\x0b\n\x03lan\x18\x06 \x01(\t\"U\n\x11UpdateFormRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\'\n\x06\x66ields\x18\x02 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x03 \x01(\t\"G\n\x15ListSchemaRefResponse\x12\x1f\n\x04rows\x18\x01 \x03(\x0b\x32\x11.schema.SchemaRef\x12\r\n\x05total\x18\x02 \x01(\x05\"A\n\x12ListSchemaResponse\x12\x1c\n\x04rows\x18\x01 \x03(\x0b\x32\x0e.schema.Schema\x12\r\n\x05total\x18\x02 \x01(\x05\"=\n\x10ListFormResponse\x12\x1a\n\x04rows\x18\x01 \x03(\x0b\x32\x0c.schema.Form\x12\r\n\x05total\x18\x02 \x01(\x05\"I\n\x16ListSchemaAttrResponse\x12 \n\x04rows\x18\x01 \x03(\x0b\x32\x12.schema.SchemaAttr\x12\r\n\x05total\x18\x02 \x01(\x05\"Q\n\x1aListSchemaRelationResponse\x12$\n\x04rows\x18\x01 \x03(\x0b\x32\x16.schema.SchemaRelation\x12\r\n\x05total\x18\x02 \x01(\x05\"Y\n\x1eListSchemaRelationAttrResponse\x12(\n\x04rows\x18\x01 \x03(\x0b\x32\x1a.schema.SchemaRelationAttr\x12\r\n\x05total\x18\x02 \x01(\x05\"[\n%ListSchemaWithLocalizationMapResponse\x12\x32\n\x07schemas\x18\x01 \x03(\x0b\x32!.schema.SchemaWithLocalizationMap\"k\n-ListSchemaRelationWithLocalizationMapResponse\x12:\n\x07schemas\x18\x01 \x03(\x0b\x32).schema.SchemaRelationWithLocalizationMap\"!\n\x0f\x44\x65\x66\x61ultResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\xbb\x01\n\tSchemaRef\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\r\n\x05title\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x0f\n\x07\x63reated\x18\x05 \x01(\t\x12\x0f\n\x07updated\x18\x06 \x01(\t\x12(\n\x07\x65xtends\x18\x07 \x01(\x0b\x32\x17.google.protobuf.Struct\x12$\n\x05\x61ttrs\x18\x08 \x03(\x0b\x32\x15.schema.SchemaRefAttr\"\xf4\x01\n\rSchemaRefAttr\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\r\n\x05title\x18\x03 \x01(\t\x12\x15\n\rschema_ref_id\x18\x04 \x01(\x04\x12/\n\x0f\x61ttr_owner_type\x18\x05 \x01(\x0e\x32\x16.schema.AttrSchemaType\x12\x16\n\x0e\x61ttr_data_type\x18\x06 \x01(\t\x12\x0f\n\x07\x63reated\x18\x07 \x01(\t\x12\x0f\n\x07updated\x18\x08 \x01(\t\x12(\n\x07\x65xtends\x18\t \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0e\n\x06status\x18\n \x01(\t\"\xaa\x02\n\x06Schema\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x15\n\rschema_ref_id\x18\x03 \x01(\x04\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\r\n\x05title\x18\x05 \x01(\t\x12\x14\n\x0cignore_state\x18\x06 \x01(\x08\x12\x14\n\x0c\x61llow_parent\x18\x07 \x01(\x08\x12\x0c\n\x04\x66orm\x18\x08 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\t \x01(\t\x12\x0f\n\x07\x63reated\x18\n \x01(\t\x12\x0f\n\x07updated\x18\x0b \x01(\t\x12(\n\x07\x65xtends\x18\x0c \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0e\n\x06status\x18\r \x01(\t\x12!\n\x05\x61ttrs\x18\x0e \x03(\x0b\x32\x12.schema.SchemaAttr\"\xb1\x01\n\x19SchemaWithLocalizationMap\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\r\n\x05title\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12.\n\rlocalizations\x18\x05 \x01(\x0b\x32\x17.google.protobuf.Struct\x12&\n\x05\x61ttrs\x18\x06 \x01(\x0b\x32\x17.google.protobuf.Struct\"\xb9\x01\n!SchemaRelationWithLocalizationMap\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\r\n\x05title\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12.\n\rlocalizations\x18\x05 \x01(\x0b\x32\x17.google.protobuf.Struct\x12&\n\x05\x61ttrs\x18\x06 \x01(\x0b\x32\x17.google.protobuf.Struct\"\xcf\x02\n\nSchemaAttr\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\r\n\x05title\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x12\n\npartner_id\x18\x05 \x01(\x04\x12\x11\n\tschema_id\x18\x06 \x01(\x04\x12\x13\n\x0bref_attr_id\x18\x07 \x01(\x04\x12\x17\n\x0f\x61ttr_owner_type\x18\x08 \x01(\t\x12\x16\n\x0e\x61ttr_data_type\x18\t \x01(\t\x12,\n\x0b\x63onstraints\x18\n \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0c\n\x04\x66orm\x18\x0b \x01(\t\x12\x0f\n\x07\x63reated\x18\x0c \x01(\t\x12\x0f\n\x07updated\x18\r \x01(\t\x12(\n\x07\x65xtends\x18\x0e \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0e\n\x06status\x18\x0f \x01(\t\"\xc5\x02\n\x0eSchemaRelation\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\r\n\x05title\x18\x04 \x01(\t\x12\x18\n\x10source_schema_id\x18\x05 \x01(\x04\x12\x18\n\x10target_schema_id\x18\x06 \x01(\x04\x12\x14\n\x0cignore_state\x18\x07 \x01(\x08\x12,\n\x0b\x63onstraints\x18\x08 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0c\n\x04\x66orm\x18\t \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\n \x01(\t\x12\x0f\n\x07\x63reated\x18\x0b \x01(\t\x12\x0f\n\x07updated\x18\x0c \x01(\t\x12\x0e\n\x06status\x18\r \x01(\t\x12)\n\x05\x61ttrs\x18\x0e \x03(\x0b\x32\x1a.schema.SchemaRelationAttr\"\xc7\x02\n\x12SchemaRelationAttr\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\r\n\x05title\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x12\n\npartner_id\x18\x05 \x01(\x04\x12\x1a\n\x12schema_relation_id\x18\x06 \x01(\x04\x12\x13\n\x0bref_attr_id\x18\x07 \x01(\x04\x12\x16\n\x0e\x61ttr_data_type\x18\x08 \x01(\t\x12,\n\x0b\x63onstraints\x18\t \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0c\n\x04\x66orm\x18\n \x01(\t\x12\x0f\n\x07\x63reated\x18\x0b \x01(\t\x12\x0f\n\x07updated\x18\x0c \x01(\t\x12(\n\x07\x65xtends\x18\r \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0e\n\x06status\x18\x0e \x01(\t\"\xe5\x01\n\x04\x46orm\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\r\n\x05title\x18\x04 \x01(\t\x12\x0c\n\x04\x66orm\x18\x05 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x06 \x01(\t\x12\x0f\n\x07\x63reated\x18\x07 \x01(\t\x12\x0f\n\x07updated\x18\x08 \x01(\t\x12(\n\x07\x65xtends\x18\t \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0e\n\x06status\x18\n \x01(\t\x12!\n\x05\x61ttrs\x18\x0b \x03(\x0b\x32\x12.schema.SchemaAttr*\x1c\n\x07OrderBy\x12\x07\n\x03\x41SC\x10\x00\x12\x08\n\x04\x44\x45SC\x10\x01*A\n\x0e\x41ttrSchemaType\x12\x10\n\x0c\x43\x41TALOG_ATTR\x10\x00\x12\x0f\n\x0b\x45NTITY_ATTR\x10\x01\x12\x0c\n\x08REL_ATTR\x10\x02*4\n\x0cSchemaStatus\x12\t\n\x05\x44RAFT\x10\x00\x12\x0b\n\x07\x45NABLED\x10\x01\x12\x0c\n\x08\x44ISABLED\x10\x02\x32\xe6 \n\rSchemaService\x12j\n\x0c\x41\x64\x64SchemaRef\x12\x1b.schema.AddSchemaRefRequest\x1a\x11.schema.SchemaRef\"*\x82\xd3\xe4\x93\x02$\"\x1f/api/v2/metadata/schema/ref/add:\x01*\x12s\n\x0fUpdateSchemaRef\x12\x1e.schema.UpdateSchemaRefRequest\x1a\x11.schema.SchemaRef\"-\x82\xd3\xe4\x93\x02\'\"\"/api/v2/metadata/schema/ref/update:\x01*\x12s\n\x10GetSchemaRefByID\x12\x1f.schema.GetSchemaRefByIdRequest\x1a\x11.schema.SchemaRef\"+\x82\xd3\xe4\x93\x02%\x12#/api/v2/metadata/schema/ref/by/{id}\x12w\n\rListSchemaRef\x12\x1c.schema.ListSchemaRefRequest\x1a\x1d.schema.ListSchemaRefResponse\")\x82\xd3\xe4\x93\x02#\x12!/api/v2/metadata/schema/ref/query\x12{\n\x10\x41\x64\x64SchemaRefAttr\x12\x1f.schema.AddSchemaRefAttrRequest\x1a\x15.schema.SchemaRefAttr\"/\x82\xd3\xe4\x93\x02)\"$/api/v2/metadata/schema/ref/attr/add:\x01*\x12\x84\x01\n\x13UpdateSchemaRefAttr\x12\".schema.UpdateSchemaRefAttrRequest\x1a\x15.schema.SchemaRefAttr\"2\x82\xd3\xe4\x93\x02,\"\'/api/v2/metadata/schema/ref/attr/update:\x01*\x12]\n\tAddSchema\x12\x18.schema.AddSchemaRequest\x1a\x0e.schema.Schema\"&\x82\xd3\xe4\x93\x02 \"\x1b/api/v2/metadata/schema/add:\x01*\x12\x66\n\x0cUpdateSchema\x12\x1b.schema.UpdateSchemaRequest\x1a\x0e.schema.Schema\")\x82\xd3\xe4\x93\x02#\"\x1e/api/v2/metadata/schema/update:\x01*\x12\x66\n\rGetSchemaByID\x12\x1c.schema.GetSchemaByIdRequest\x1a\x0e.schema.Schema\"\'\x82\xd3\xe4\x93\x02!\x12\x1f/api/v2/metadata/schema/by/{id}\x12m\n\nListSchema\x12\x19.schema.ListSchemaRequest\x1a\x1a.schema.ListSchemaResponse\"(\x82\xd3\xe4\x93\x02\"\"\x1d/api/v2/metadata/schema/query:\x01*\x12\xb3\x01\n\x1dListSchemaWithLocalizationMap\x12,.schema.ListSchemaWithLocalizationMapRequest\x1a-.schema.ListSchemaWithLocalizationMapResponse\"5\x82\xd3\xe4\x93\x02/\"*/api/v2/metadata/schema/localization/query:\x01*\x12n\n\rAddSchemaAttr\x12\x1c.schema.AddSchemaAttrRequest\x1a\x12.schema.SchemaAttr\"+\x82\xd3\xe4\x93\x02%\" /api/v2/metadata/schema/attr/add:\x01*\x12w\n\x10UpdateSchemaAttr\x12\x1f.schema.UpdateSchemaAttrRequest\x1a\x12.schema.SchemaAttr\".\x82\xd3\xe4\x93\x02(\"#/api/v2/metadata/schema/attr/update:\x01*\x12\x90\x01\n\x17UpdateSchemaAttrByBatch\x12&.schema.UpdateSchemaAttrByBatchRequest\x1a\x17.schema.DefaultResponse\"4\x82\xd3\xe4\x93\x02.\")/api/v2/metadata/schema/attr/update/batch:\x01*\x12w\n\x11GetSchemaAttrByID\x12 .schema.GetSchemaAttrByIdRequest\x1a\x12.schema.SchemaAttr\",\x82\xd3\xe4\x93\x02&\x12$/api/v2/metadata/schema/attr/by/{id}\x12~\n\x0eListSchemaAttr\x12\x1d.schema.ListSchemaAttrRequest\x1a\x1e.schema.ListSchemaAttrResponse\"-\x82\xd3\xe4\x93\x02\'\"\"/api/v2/metadata/schema/attr/query:\x01*\x12\x86\x01\n\x14\x44\x65leteSchemaAttrByID\x12#.schema.DeleteSchemaAttrByIdRequest\x1a\x17.schema.DefaultResponse\"0\x82\xd3\xe4\x93\x02**(/api/v2/metadata/schema/attr/delete/{id}\x12~\n\x11\x41\x64\x64SchemaRelation\x12 .schema.AddSchemaRelationRequest\x1a\x16.schema.SchemaRelation\"/\x82\xd3\xe4\x93\x02)\"$/api/v2/metadata/schema/relation/add:\x01*\x12\x87\x01\n\x14UpdateSchemaRelation\x12#.schema.UpdateSchemaRelationRequest\x1a\x16.schema.SchemaRelation\"2\x82\xd3\xe4\x93\x02,\"\'/api/v2/metadata/schema/relation/update:\x01*\x12\x87\x01\n\x15GetSchemaRelationByID\x12$.schema.GetSchemaRelationByIdRequest\x1a\x16.schema.SchemaRelation\"0\x82\xd3\xe4\x93\x02*\x12(/api/v2/metadata/schema/relation/by/{id}\x12\x8e\x01\n\x12ListSchemaRelation\x12!.schema.ListSchemaRelationRequest\x1a\".schema.ListSchemaRelationResponse\"1\x82\xd3\xe4\x93\x02+\"&/api/v2/metadata/schema/relation/query:\x01*\x12\xd4\x01\n%ListSchemaRelationWithLocalizationMap\x12\x34.schema.ListSchemaRelationWithLocalizationMapRequest\x1a\x35.schema.ListSchemaRelationWithLocalizationMapResponse\">\x82\xd3\xe4\x93\x02\x38\"3/api/v2/metadata/schema/relation/localization/query:\x01*\x12\x8f\x01\n\x15\x41\x64\x64SchemaRelationAttr\x12$.schema.AddSchemaRelationAttrRequest\x1a\x1a.schema.SchemaRelationAttr\"4\x82\xd3\xe4\x93\x02.\")/api/v2/metadata/schema/relation/attr/add:\x01*\x12\x98\x01\n\x18UpdateSchemaRelationAttr\x12\'.schema.UpdateSchemaRelationAttrRequest\x1a\x1a.schema.SchemaRelationAttr\"7\x82\xd3\xe4\x93\x02\x31\",/api/v2/metadata/schema/relation/attr/update:\x01*\x12\xa9\x01\n\x1fUpdateSchemaRelationAttrByBatch\x12..schema.UpdateSchemaRelationAttrByBatchRequest\x1a\x17.schema.DefaultResponse\"=\x82\xd3\xe4\x93\x02\x37\"2/api/v2/metadata/schema/relation/attr/update/batch:\x01*\x12\x98\x01\n\x19GetSchemaRelationAttrByID\x12(.schema.GetSchemaRelationAttrByIdRequest\x1a\x1a.schema.SchemaRelationAttr\"5\x82\xd3\xe4\x93\x02/\x12-/api/v2/metadata/schema/relation/attr/by/{id}\x12\x9f\x01\n\x16ListSchemaRelationAttr\x12%.schema.ListSchemaRelationAttrRequest\x1a&.schema.ListSchemaRelationAttrResponse\"6\x82\xd3\xe4\x93\x02\x30\"+/api/v2/metadata/schema/relation/attr/query:\x01*\x12\x9f\x01\n\x1c\x44\x65leteSchemaRelationAttrByID\x12+.schema.DeleteSchemaRelationAttrByIdRequest\x1a\x17.schema.DefaultResponse\"9\x82\xd3\xe4\x93\x02\x33*1/api/v2/metadata/schema/relation/attr/delete/{id}\x12U\n\x07\x41\x64\x64\x46orm\x12\x16.schema.AddFormRequest\x1a\x0c.schema.Form\"$\x82\xd3\xe4\x93\x02\x1e\"\x19/api/v2/metadata/form/add:\x01*\x12^\n\nUpdateForm\x12\x19.schema.UpdateFormRequest\x1a\x0c.schema.Form\"\'\x82\xd3\xe4\x93\x02!\"\x1c/api/v2/metadata/form/update:\x01*\x12^\n\x0bGetFormByID\x12\x1a.schema.GetFormByIdRequest\x1a\x0c.schema.Form\"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/api/v2/metadata/form/by/{id}\x12\x65\n\x08ListForm\x12\x17.schema.ListFormRequest\x1a\x18.schema.ListFormResponse\"&\x82\xd3\xe4\x93\x02 \"\x1b/api/v2/metadata/form/query:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_struct__pb2.DESCRIPTOR,])

_ORDERBY = _descriptor.EnumDescriptor(
  name='OrderBy',
  full_name='schema.OrderBy',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ASC', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DESC', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7793,
  serialized_end=7821,
)
_sym_db.RegisterEnumDescriptor(_ORDERBY)

OrderBy = enum_type_wrapper.EnumTypeWrapper(_ORDERBY)
_ATTRSCHEMATYPE = _descriptor.EnumDescriptor(
  name='AttrSchemaType',
  full_name='schema.AttrSchemaType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='CATALOG_ATTR', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ENTITY_ATTR', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REL_ATTR', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7823,
  serialized_end=7888,
)
_sym_db.RegisterEnumDescriptor(_ATTRSCHEMATYPE)

AttrSchemaType = enum_type_wrapper.EnumTypeWrapper(_ATTRSCHEMATYPE)
_SCHEMASTATUS = _descriptor.EnumDescriptor(
  name='SchemaStatus',
  full_name='schema.SchemaStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='DRAFT', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ENABLED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DISABLED', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=7890,
  serialized_end=7942,
)
_sym_db.RegisterEnumDescriptor(_SCHEMASTATUS)

SchemaStatus = enum_type_wrapper.EnumTypeWrapper(_SCHEMASTATUS)
ASC = 0
DESC = 1
CATALOG_ATTR = 0
ENTITY_ATTR = 1
REL_ATTR = 2
DRAFT = 0
ENABLED = 1
DISABLED = 2



_GETSCHEMAREFBYIDREQUEST = _descriptor.Descriptor(
  name='GetSchemaRefByIdRequest',
  full_name='schema.GetSchemaRefByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.GetSchemaRefByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_attr_refs', full_name='schema.GetSchemaRefByIdRequest.include_attr_refs', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.GetSchemaRefByIdRequest.extends', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=100,
  serialized_end=206,
)


_GETSCHEMABYIDREQUEST = _descriptor.Descriptor(
  name='GetSchemaByIdRequest',
  full_name='schema.GetSchemaByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.GetSchemaByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_attrs', full_name='schema.GetSchemaByIdRequest.include_attrs', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.GetSchemaByIdRequest.extends', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.GetSchemaByIdRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=208,
  serialized_end=320,
)


_GETSCHEMAATTRBYIDREQUEST = _descriptor.Descriptor(
  name='GetSchemaAttrByIdRequest',
  full_name='schema.GetSchemaAttrByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.GetSchemaAttrByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.GetSchemaAttrByIdRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=322,
  serialized_end=373,
)


_GETFORMBYIDREQUEST = _descriptor.Descriptor(
  name='GetFormByIdRequest',
  full_name='schema.GetFormByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.GetFormByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.GetFormByIdRequest.extends', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.GetFormByIdRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=375,
  serialized_end=462,
)


_ADDSCHEMAREFREQUEST = _descriptor.Descriptor(
  name='AddSchemaRefRequest',
  full_name='schema.AddSchemaRefRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='schema.AddSchemaRefRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='title', full_name='schema.AddSchemaRefRequest.title', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='schema.AddSchemaRefRequest.description', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.AddSchemaRefRequest.extends', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=464,
  serialized_end=577,
)


_UPDATESCHEMAREFREQUEST = _descriptor.Descriptor(
  name='UpdateSchemaRefRequest',
  full_name='schema.UpdateSchemaRefRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.UpdateSchemaRefRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='schema.UpdateSchemaRefRequest.fields', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=579,
  serialized_end=656,
)


_LISTSCHEMAREFREQUEST = _descriptor.Descriptor(
  name='ListSchemaRefRequest',
  full_name='schema.ListSchemaRefRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='limit', full_name='schema.ListSchemaRefRequest.limit', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='schema.ListSchemaRefRequest.offset', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='schema.ListSchemaRefRequest.sort', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='schema.ListSchemaRefRequest.order', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='schema.ListSchemaRefRequest.include_total', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='schema.ListSchemaRefRequest.filters', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.ListSchemaRefRequest.extends', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=659,
  serialized_end=848,
)


_LISTSCHEMAREQUEST = _descriptor.Descriptor(
  name='ListSchemaRequest',
  full_name='schema.ListSchemaRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='schema.ListSchemaRequest.status', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='schema.ListSchemaRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='schema.ListSchemaRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='schema.ListSchemaRequest.sort', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='schema.ListSchemaRequest.order', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='schema.ListSchemaRequest.include_total', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='schema.ListSchemaRequest.filters', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.ListSchemaRequest.extends', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.ListSchemaRequest.lan', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='schema.ListSchemaRequest.ids', index=9,
      number=10, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='names', full_name='schema.ListSchemaRequest.names', index=10,
      number=11, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=851,
  serialized_end=1094,
)


_LISTSCHEMAWITHLOCALIZATIONMAPREQUEST = _descriptor.Descriptor(
  name='ListSchemaWithLocalizationMapRequest',
  full_name='schema.ListSchemaWithLocalizationMapRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='names', full_name='schema.ListSchemaWithLocalizationMapRequest.names', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.ListSchemaWithLocalizationMapRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_attrs', full_name='schema.ListSchemaWithLocalizationMapRequest.include_attrs', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_all_localizations', full_name='schema.ListSchemaWithLocalizationMapRequest.include_all_localizations', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1096,
  serialized_end=1220,
)


_LISTSCHEMAATTRREQUEST = _descriptor.Descriptor(
  name='ListSchemaAttrRequest',
  full_name='schema.ListSchemaAttrRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='schema_id', full_name='schema.ListSchemaAttrRequest.schema_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='schema.ListSchemaAttrRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='schema.ListSchemaAttrRequest.limit', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='schema.ListSchemaAttrRequest.offset', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='schema.ListSchemaAttrRequest.sort', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='schema.ListSchemaAttrRequest.order', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='schema.ListSchemaAttrRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='schema.ListSchemaAttrRequest.filters', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.ListSchemaAttrRequest.extends', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.ListSchemaAttrRequest.lan', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1223,
  serialized_end=1461,
)


_LISTFORMREQUEST = _descriptor.Descriptor(
  name='ListFormRequest',
  full_name='schema.ListFormRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='schema.ListFormRequest.status', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='schema.ListFormRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='schema.ListFormRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='schema.ListFormRequest.sort', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='schema.ListFormRequest.order', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='schema.ListFormRequest.include_total', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='schema.ListFormRequest.filters', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.ListFormRequest.extends', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.ListFormRequest.lan', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1464,
  serialized_end=1677,
)


_ADDSCHEMAREFATTRREQUEST = _descriptor.Descriptor(
  name='AddSchemaRefAttrRequest',
  full_name='schema.AddSchemaRefAttrRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='schema.AddSchemaRefAttrRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='title', full_name='schema.AddSchemaRefAttrRequest.title', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_ref_id', full_name='schema.AddSchemaRefAttrRequest.schema_ref_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attr_owner_type', full_name='schema.AddSchemaRefAttrRequest.attr_owner_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attr_data_type', full_name='schema.AddSchemaRefAttrRequest.attr_data_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.AddSchemaRefAttrRequest.extends', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='schema.AddSchemaRefAttrRequest.status', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1680,
  serialized_end=1864,
)


_UPDATESCHEMAREFATTRREQUEST = _descriptor.Descriptor(
  name='UpdateSchemaRefAttrRequest',
  full_name='schema.UpdateSchemaRefAttrRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.UpdateSchemaRefAttrRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='schema.UpdateSchemaRefAttrRequest.fields', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1866,
  serialized_end=1947,
)


_ADDSCHEMAREQUEST = _descriptor.Descriptor(
  name='AddSchemaRequest',
  full_name='schema.AddSchemaRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='schema_ref_id', full_name='schema.AddSchemaRequest.schema_ref_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='schema.AddSchemaRequest.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='title', full_name='schema.AddSchemaRequest.title', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ignore_state', full_name='schema.AddSchemaRequest.ignore_state', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_parent', full_name='schema.AddSchemaRequest.allow_parent', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='form', full_name='schema.AddSchemaRequest.form', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='schema.AddSchemaRequest.description', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.AddSchemaRequest.extends', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='schema.AddSchemaRequest.status', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.AddSchemaRequest.lan', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1950,
  serialized_end=2170,
)


_UPDATESCHEMAREQUEST = _descriptor.Descriptor(
  name='UpdateSchemaRequest',
  full_name='schema.UpdateSchemaRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.UpdateSchemaRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='schema.UpdateSchemaRequest.fields', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.UpdateSchemaRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2172,
  serialized_end=2259,
)


_ADDSCHEMAATTRREQUEST = _descriptor.Descriptor(
  name='AddSchemaAttrRequest',
  full_name='schema.AddSchemaAttrRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='schema.AddSchemaAttrRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='title', full_name='schema.AddSchemaAttrRequest.title', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='schema.AddSchemaAttrRequest.description', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_id', full_name='schema.AddSchemaAttrRequest.schema_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ref_attr_id', full_name='schema.AddSchemaAttrRequest.ref_attr_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attr_owner_type', full_name='schema.AddSchemaAttrRequest.attr_owner_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attr_data_type', full_name='schema.AddSchemaAttrRequest.attr_data_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='constraints', full_name='schema.AddSchemaAttrRequest.constraints', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='form', full_name='schema.AddSchemaAttrRequest.form', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.AddSchemaAttrRequest.extends', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='schema.AddSchemaAttrRequest.status', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.AddSchemaAttrRequest.lan', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2262,
  serialized_end=2554,
)


_UPDATESCHEMAATTRREQUEST = _descriptor.Descriptor(
  name='UpdateSchemaAttrRequest',
  full_name='schema.UpdateSchemaAttrRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.UpdateSchemaAttrRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='schema.UpdateSchemaAttrRequest.fields', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.UpdateSchemaAttrRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2556,
  serialized_end=2647,
)


_UPDATESCHEMAATTRBYBATCHREQUEST = _descriptor.Descriptor(
  name='UpdateSchemaAttrByBatchRequest',
  full_name='schema.UpdateSchemaAttrByBatchRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='schema_id', full_name='schema.UpdateSchemaAttrByBatchRequest.schema_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attrs', full_name='schema.UpdateSchemaAttrByBatchRequest.attrs', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.UpdateSchemaAttrByBatchRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2649,
  serialized_end=2753,
)


_DELETESCHEMAATTRBYIDREQUEST = _descriptor.Descriptor(
  name='DeleteSchemaAttrByIdRequest',
  full_name='schema.DeleteSchemaAttrByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.DeleteSchemaAttrByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2755,
  serialized_end=2796,
)


_ADDSCHEMARELATIONREQUEST = _descriptor.Descriptor(
  name='AddSchemaRelationRequest',
  full_name='schema.AddSchemaRelationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='schema.AddSchemaRelationRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='title', full_name='schema.AddSchemaRelationRequest.title', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_id', full_name='schema.AddSchemaRelationRequest.source_schema_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_schema_id', full_name='schema.AddSchemaRelationRequest.target_schema_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ignore_state', full_name='schema.AddSchemaRelationRequest.ignore_state', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='form', full_name='schema.AddSchemaRelationRequest.form', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='schema.AddSchemaRelationRequest.description', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='constraints', full_name='schema.AddSchemaRelationRequest.constraints', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='schema.AddSchemaRelationRequest.status', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.AddSchemaRelationRequest.lan', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2799,
  serialized_end=3038,
)


_GETSCHEMARELATIONBYIDREQUEST = _descriptor.Descriptor(
  name='GetSchemaRelationByIdRequest',
  full_name='schema.GetSchemaRelationByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.GetSchemaRelationByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_attrs', full_name='schema.GetSchemaRelationByIdRequest.include_attrs', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.GetSchemaRelationByIdRequest.extends', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.GetSchemaRelationByIdRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3040,
  serialized_end=3160,
)


_LISTSCHEMARELATIONREQUEST = _descriptor.Descriptor(
  name='ListSchemaRelationRequest',
  full_name='schema.ListSchemaRelationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='source_schema_id', full_name='schema.ListSchemaRelationRequest.source_schema_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_schema_id', full_name='schema.ListSchemaRelationRequest.target_schema_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='schema.ListSchemaRelationRequest.limit', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='schema.ListSchemaRelationRequest.offset', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='schema.ListSchemaRelationRequest.sort', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='schema.ListSchemaRelationRequest.order', index=5,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='schema.ListSchemaRelationRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='schema.ListSchemaRelationRequest.filters', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.ListSchemaRelationRequest.extends', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='schema.ListSchemaRelationRequest.status', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.ListSchemaRelationRequest.lan', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3163,
  serialized_end=3455,
)


_LISTSCHEMARELATIONWITHLOCALIZATIONMAPREQUEST = _descriptor.Descriptor(
  name='ListSchemaRelationWithLocalizationMapRequest',
  full_name='schema.ListSchemaRelationWithLocalizationMapRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='schema.ListSchemaRelationWithLocalizationMapRequest.source_schema_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_names', full_name='schema.ListSchemaRelationWithLocalizationMapRequest.schema_relation_names', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.ListSchemaRelationWithLocalizationMapRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_attrs', full_name='schema.ListSchemaRelationWithLocalizationMapRequest.include_attrs', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_all_localizations', full_name='schema.ListSchemaRelationWithLocalizationMapRequest.include_all_localizations', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3458,
  serialized_end=3634,
)


_UPDATESCHEMARELATIONREQUEST = _descriptor.Descriptor(
  name='UpdateSchemaRelationRequest',
  full_name='schema.UpdateSchemaRelationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.UpdateSchemaRelationRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='schema.UpdateSchemaRelationRequest.fields', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.UpdateSchemaRelationRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3636,
  serialized_end=3731,
)


_ADDSCHEMARELATIONATTRREQUEST = _descriptor.Descriptor(
  name='AddSchemaRelationAttrRequest',
  full_name='schema.AddSchemaRelationAttrRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='schema.AddSchemaRelationAttrRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='title', full_name='schema.AddSchemaRelationAttrRequest.title', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='schema.AddSchemaRelationAttrRequest.description', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_id', full_name='schema.AddSchemaRelationAttrRequest.schema_relation_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ref_attr_id', full_name='schema.AddSchemaRelationAttrRequest.ref_attr_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attr_data_type', full_name='schema.AddSchemaRelationAttrRequest.attr_data_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='constraints', full_name='schema.AddSchemaRelationAttrRequest.constraints', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='form', full_name='schema.AddSchemaRelationAttrRequest.form', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='schema.AddSchemaRelationAttrRequest.status', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.AddSchemaRelationAttrRequest.lan', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3734,
  serialized_end=3976,
)


_UPDATESCHEMARELATIONATTRREQUEST = _descriptor.Descriptor(
  name='UpdateSchemaRelationAttrRequest',
  full_name='schema.UpdateSchemaRelationAttrRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.UpdateSchemaRelationAttrRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='schema.UpdateSchemaRelationAttrRequest.fields', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.UpdateSchemaRelationAttrRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3978,
  serialized_end=4077,
)


_UPDATESCHEMARELATIONATTRBYBATCHREQUEST = _descriptor.Descriptor(
  name='UpdateSchemaRelationAttrByBatchRequest',
  full_name='schema.UpdateSchemaRelationAttrByBatchRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='schema_relation_id', full_name='schema.UpdateSchemaRelationAttrByBatchRequest.schema_relation_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attrs', full_name='schema.UpdateSchemaRelationAttrByBatchRequest.attrs', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.UpdateSchemaRelationAttrByBatchRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4079,
  serialized_end=4200,
)


_GETSCHEMARELATIONATTRBYIDREQUEST = _descriptor.Descriptor(
  name='GetSchemaRelationAttrByIdRequest',
  full_name='schema.GetSchemaRelationAttrByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.GetSchemaRelationAttrByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.GetSchemaRelationAttrByIdRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4202,
  serialized_end=4261,
)


_LISTSCHEMARELATIONATTRREQUEST = _descriptor.Descriptor(
  name='ListSchemaRelationAttrRequest',
  full_name='schema.ListSchemaRelationAttrRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='schema_relation_id', full_name='schema.ListSchemaRelationAttrRequest.schema_relation_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='schema.ListSchemaRelationAttrRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='schema.ListSchemaRelationAttrRequest.limit', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='schema.ListSchemaRelationAttrRequest.offset', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='schema.ListSchemaRelationAttrRequest.sort', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='schema.ListSchemaRelationAttrRequest.order', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='schema.ListSchemaRelationAttrRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='schema.ListSchemaRelationAttrRequest.filters', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.ListSchemaRelationAttrRequest.extends', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.ListSchemaRelationAttrRequest.lan', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4264,
  serialized_end=4519,
)


_DELETESCHEMARELATIONATTRBYIDREQUEST = _descriptor.Descriptor(
  name='DeleteSchemaRelationAttrByIdRequest',
  full_name='schema.DeleteSchemaRelationAttrByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.DeleteSchemaRelationAttrByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4521,
  serialized_end=4570,
)


_ADDFORMREQUEST = _descriptor.Descriptor(
  name='AddFormRequest',
  full_name='schema.AddFormRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='schema.AddFormRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='title', full_name='schema.AddFormRequest.title', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='form', full_name='schema.AddFormRequest.form', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='schema.AddFormRequest.description', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='schema.AddFormRequest.status', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.AddFormRequest.lan', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4572,
  serialized_end=4681,
)


_UPDATEFORMREQUEST = _descriptor.Descriptor(
  name='UpdateFormRequest',
  full_name='schema.UpdateFormRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.UpdateFormRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='schema.UpdateFormRequest.fields', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='schema.UpdateFormRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4683,
  serialized_end=4768,
)


_LISTSCHEMAREFRESPONSE = _descriptor.Descriptor(
  name='ListSchemaRefResponse',
  full_name='schema.ListSchemaRefResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='schema.ListSchemaRefResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='schema.ListSchemaRefResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4770,
  serialized_end=4841,
)


_LISTSCHEMARESPONSE = _descriptor.Descriptor(
  name='ListSchemaResponse',
  full_name='schema.ListSchemaResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='schema.ListSchemaResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='schema.ListSchemaResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4843,
  serialized_end=4908,
)


_LISTFORMRESPONSE = _descriptor.Descriptor(
  name='ListFormResponse',
  full_name='schema.ListFormResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='schema.ListFormResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='schema.ListFormResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4910,
  serialized_end=4971,
)


_LISTSCHEMAATTRRESPONSE = _descriptor.Descriptor(
  name='ListSchemaAttrResponse',
  full_name='schema.ListSchemaAttrResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='schema.ListSchemaAttrResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='schema.ListSchemaAttrResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4973,
  serialized_end=5046,
)


_LISTSCHEMARELATIONRESPONSE = _descriptor.Descriptor(
  name='ListSchemaRelationResponse',
  full_name='schema.ListSchemaRelationResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='schema.ListSchemaRelationResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='schema.ListSchemaRelationResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5048,
  serialized_end=5129,
)


_LISTSCHEMARELATIONATTRRESPONSE = _descriptor.Descriptor(
  name='ListSchemaRelationAttrResponse',
  full_name='schema.ListSchemaRelationAttrResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='schema.ListSchemaRelationAttrResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='schema.ListSchemaRelationAttrResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5131,
  serialized_end=5220,
)


_LISTSCHEMAWITHLOCALIZATIONMAPRESPONSE = _descriptor.Descriptor(
  name='ListSchemaWithLocalizationMapResponse',
  full_name='schema.ListSchemaWithLocalizationMapResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='schemas', full_name='schema.ListSchemaWithLocalizationMapResponse.schemas', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5222,
  serialized_end=5313,
)


_LISTSCHEMARELATIONWITHLOCALIZATIONMAPRESPONSE = _descriptor.Descriptor(
  name='ListSchemaRelationWithLocalizationMapResponse',
  full_name='schema.ListSchemaRelationWithLocalizationMapResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='schemas', full_name='schema.ListSchemaRelationWithLocalizationMapResponse.schemas', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5315,
  serialized_end=5422,
)


_DEFAULTRESPONSE = _descriptor.Descriptor(
  name='DefaultResponse',
  full_name='schema.DefaultResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='schema.DefaultResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5424,
  serialized_end=5457,
)


_SCHEMAREF = _descriptor.Descriptor(
  name='SchemaRef',
  full_name='schema.SchemaRef',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.SchemaRef.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='schema.SchemaRef.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='title', full_name='schema.SchemaRef.title', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='schema.SchemaRef.description', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='schema.SchemaRef.created', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='schema.SchemaRef.updated', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.SchemaRef.extends', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attrs', full_name='schema.SchemaRef.attrs', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5460,
  serialized_end=5647,
)


_SCHEMAREFATTR = _descriptor.Descriptor(
  name='SchemaRefAttr',
  full_name='schema.SchemaRefAttr',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.SchemaRefAttr.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='schema.SchemaRefAttr.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='title', full_name='schema.SchemaRefAttr.title', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_ref_id', full_name='schema.SchemaRefAttr.schema_ref_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attr_owner_type', full_name='schema.SchemaRefAttr.attr_owner_type', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attr_data_type', full_name='schema.SchemaRefAttr.attr_data_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='schema.SchemaRefAttr.created', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='schema.SchemaRefAttr.updated', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.SchemaRefAttr.extends', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='schema.SchemaRefAttr.status', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5650,
  serialized_end=5894,
)


_SCHEMA = _descriptor.Descriptor(
  name='Schema',
  full_name='schema.Schema',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.Schema.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='schema.Schema.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_ref_id', full_name='schema.Schema.schema_ref_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='schema.Schema.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='title', full_name='schema.Schema.title', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ignore_state', full_name='schema.Schema.ignore_state', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_parent', full_name='schema.Schema.allow_parent', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='form', full_name='schema.Schema.form', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='schema.Schema.description', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='schema.Schema.created', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='schema.Schema.updated', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.Schema.extends', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='schema.Schema.status', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attrs', full_name='schema.Schema.attrs', index=13,
      number=14, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5897,
  serialized_end=6195,
)


_SCHEMAWITHLOCALIZATIONMAP = _descriptor.Descriptor(
  name='SchemaWithLocalizationMap',
  full_name='schema.SchemaWithLocalizationMap',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.SchemaWithLocalizationMap.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='schema.SchemaWithLocalizationMap.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='title', full_name='schema.SchemaWithLocalizationMap.title', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='schema.SchemaWithLocalizationMap.description', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='localizations', full_name='schema.SchemaWithLocalizationMap.localizations', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attrs', full_name='schema.SchemaWithLocalizationMap.attrs', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6198,
  serialized_end=6375,
)


_SCHEMARELATIONWITHLOCALIZATIONMAP = _descriptor.Descriptor(
  name='SchemaRelationWithLocalizationMap',
  full_name='schema.SchemaRelationWithLocalizationMap',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.SchemaRelationWithLocalizationMap.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='schema.SchemaRelationWithLocalizationMap.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='title', full_name='schema.SchemaRelationWithLocalizationMap.title', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='schema.SchemaRelationWithLocalizationMap.description', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='localizations', full_name='schema.SchemaRelationWithLocalizationMap.localizations', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attrs', full_name='schema.SchemaRelationWithLocalizationMap.attrs', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6378,
  serialized_end=6563,
)


_SCHEMAATTR = _descriptor.Descriptor(
  name='SchemaAttr',
  full_name='schema.SchemaAttr',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.SchemaAttr.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='schema.SchemaAttr.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='title', full_name='schema.SchemaAttr.title', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='schema.SchemaAttr.description', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='schema.SchemaAttr.partner_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_id', full_name='schema.SchemaAttr.schema_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ref_attr_id', full_name='schema.SchemaAttr.ref_attr_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attr_owner_type', full_name='schema.SchemaAttr.attr_owner_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attr_data_type', full_name='schema.SchemaAttr.attr_data_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='constraints', full_name='schema.SchemaAttr.constraints', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='form', full_name='schema.SchemaAttr.form', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='schema.SchemaAttr.created', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='schema.SchemaAttr.updated', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.SchemaAttr.extends', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='schema.SchemaAttr.status', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6566,
  serialized_end=6901,
)


_SCHEMARELATION = _descriptor.Descriptor(
  name='SchemaRelation',
  full_name='schema.SchemaRelation',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.SchemaRelation.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='schema.SchemaRelation.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='schema.SchemaRelation.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='title', full_name='schema.SchemaRelation.title', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_id', full_name='schema.SchemaRelation.source_schema_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_schema_id', full_name='schema.SchemaRelation.target_schema_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ignore_state', full_name='schema.SchemaRelation.ignore_state', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='constraints', full_name='schema.SchemaRelation.constraints', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='form', full_name='schema.SchemaRelation.form', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='schema.SchemaRelation.description', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='schema.SchemaRelation.created', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='schema.SchemaRelation.updated', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='schema.SchemaRelation.status', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attrs', full_name='schema.SchemaRelation.attrs', index=13,
      number=14, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6904,
  serialized_end=7229,
)


_SCHEMARELATIONATTR = _descriptor.Descriptor(
  name='SchemaRelationAttr',
  full_name='schema.SchemaRelationAttr',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.SchemaRelationAttr.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='schema.SchemaRelationAttr.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='title', full_name='schema.SchemaRelationAttr.title', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='schema.SchemaRelationAttr.description', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='schema.SchemaRelationAttr.partner_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_id', full_name='schema.SchemaRelationAttr.schema_relation_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ref_attr_id', full_name='schema.SchemaRelationAttr.ref_attr_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attr_data_type', full_name='schema.SchemaRelationAttr.attr_data_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='constraints', full_name='schema.SchemaRelationAttr.constraints', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='form', full_name='schema.SchemaRelationAttr.form', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='schema.SchemaRelationAttr.created', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='schema.SchemaRelationAttr.updated', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.SchemaRelationAttr.extends', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='schema.SchemaRelationAttr.status', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7232,
  serialized_end=7559,
)


_FORM = _descriptor.Descriptor(
  name='Form',
  full_name='schema.Form',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='schema.Form.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='schema.Form.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='schema.Form.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='title', full_name='schema.Form.title', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='form', full_name='schema.Form.form', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='schema.Form.description', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='schema.Form.created', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='schema.Form.updated', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='schema.Form.extends', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='schema.Form.status', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attrs', full_name='schema.Form.attrs', index=10,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7562,
  serialized_end=7791,
)

_GETSCHEMAREFBYIDREQUEST.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_GETSCHEMABYIDREQUEST.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_GETFORMBYIDREQUEST.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_ADDSCHEMAREFREQUEST.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_UPDATESCHEMAREFREQUEST.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTSCHEMAREFREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTSCHEMAREFREQUEST.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTSCHEMAREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTSCHEMAREQUEST.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTSCHEMAATTRREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTSCHEMAATTRREQUEST.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTFORMREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTFORMREQUEST.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_ADDSCHEMAREFATTRREQUEST.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_UPDATESCHEMAREFATTRREQUEST.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_ADDSCHEMAREQUEST.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_UPDATESCHEMAREQUEST.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_ADDSCHEMAATTRREQUEST.fields_by_name['constraints'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_ADDSCHEMAATTRREQUEST.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_UPDATESCHEMAATTRREQUEST.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_UPDATESCHEMAATTRBYBATCHREQUEST.fields_by_name['attrs'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_ADDSCHEMARELATIONREQUEST.fields_by_name['constraints'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_GETSCHEMARELATIONBYIDREQUEST.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTSCHEMARELATIONREQUEST.fields_by_name['order'].enum_type = _ORDERBY
_LISTSCHEMARELATIONREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTSCHEMARELATIONREQUEST.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_UPDATESCHEMARELATIONREQUEST.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_ADDSCHEMARELATIONATTRREQUEST.fields_by_name['constraints'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_UPDATESCHEMARELATIONATTRREQUEST.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_UPDATESCHEMARELATIONATTRBYBATCHREQUEST.fields_by_name['attrs'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTSCHEMARELATIONATTRREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTSCHEMARELATIONATTRREQUEST.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_UPDATEFORMREQUEST.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTSCHEMAREFRESPONSE.fields_by_name['rows'].message_type = _SCHEMAREF
_LISTSCHEMARESPONSE.fields_by_name['rows'].message_type = _SCHEMA
_LISTFORMRESPONSE.fields_by_name['rows'].message_type = _FORM
_LISTSCHEMAATTRRESPONSE.fields_by_name['rows'].message_type = _SCHEMAATTR
_LISTSCHEMARELATIONRESPONSE.fields_by_name['rows'].message_type = _SCHEMARELATION
_LISTSCHEMARELATIONATTRRESPONSE.fields_by_name['rows'].message_type = _SCHEMARELATIONATTR
_LISTSCHEMAWITHLOCALIZATIONMAPRESPONSE.fields_by_name['schemas'].message_type = _SCHEMAWITHLOCALIZATIONMAP
_LISTSCHEMARELATIONWITHLOCALIZATIONMAPRESPONSE.fields_by_name['schemas'].message_type = _SCHEMARELATIONWITHLOCALIZATIONMAP
_SCHEMAREF.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_SCHEMAREF.fields_by_name['attrs'].message_type = _SCHEMAREFATTR
_SCHEMAREFATTR.fields_by_name['attr_owner_type'].enum_type = _ATTRSCHEMATYPE
_SCHEMAREFATTR.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_SCHEMA.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_SCHEMA.fields_by_name['attrs'].message_type = _SCHEMAATTR
_SCHEMAWITHLOCALIZATIONMAP.fields_by_name['localizations'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_SCHEMAWITHLOCALIZATIONMAP.fields_by_name['attrs'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_SCHEMARELATIONWITHLOCALIZATIONMAP.fields_by_name['localizations'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_SCHEMARELATIONWITHLOCALIZATIONMAP.fields_by_name['attrs'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_SCHEMAATTR.fields_by_name['constraints'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_SCHEMAATTR.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_SCHEMARELATION.fields_by_name['constraints'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_SCHEMARELATION.fields_by_name['attrs'].message_type = _SCHEMARELATIONATTR
_SCHEMARELATIONATTR.fields_by_name['constraints'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_SCHEMARELATIONATTR.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_FORM.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_FORM.fields_by_name['attrs'].message_type = _SCHEMAATTR
DESCRIPTOR.message_types_by_name['GetSchemaRefByIdRequest'] = _GETSCHEMAREFBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetSchemaByIdRequest'] = _GETSCHEMABYIDREQUEST
DESCRIPTOR.message_types_by_name['GetSchemaAttrByIdRequest'] = _GETSCHEMAATTRBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetFormByIdRequest'] = _GETFORMBYIDREQUEST
DESCRIPTOR.message_types_by_name['AddSchemaRefRequest'] = _ADDSCHEMAREFREQUEST
DESCRIPTOR.message_types_by_name['UpdateSchemaRefRequest'] = _UPDATESCHEMAREFREQUEST
DESCRIPTOR.message_types_by_name['ListSchemaRefRequest'] = _LISTSCHEMAREFREQUEST
DESCRIPTOR.message_types_by_name['ListSchemaRequest'] = _LISTSCHEMAREQUEST
DESCRIPTOR.message_types_by_name['ListSchemaWithLocalizationMapRequest'] = _LISTSCHEMAWITHLOCALIZATIONMAPREQUEST
DESCRIPTOR.message_types_by_name['ListSchemaAttrRequest'] = _LISTSCHEMAATTRREQUEST
DESCRIPTOR.message_types_by_name['ListFormRequest'] = _LISTFORMREQUEST
DESCRIPTOR.message_types_by_name['AddSchemaRefAttrRequest'] = _ADDSCHEMAREFATTRREQUEST
DESCRIPTOR.message_types_by_name['UpdateSchemaRefAttrRequest'] = _UPDATESCHEMAREFATTRREQUEST
DESCRIPTOR.message_types_by_name['AddSchemaRequest'] = _ADDSCHEMAREQUEST
DESCRIPTOR.message_types_by_name['UpdateSchemaRequest'] = _UPDATESCHEMAREQUEST
DESCRIPTOR.message_types_by_name['AddSchemaAttrRequest'] = _ADDSCHEMAATTRREQUEST
DESCRIPTOR.message_types_by_name['UpdateSchemaAttrRequest'] = _UPDATESCHEMAATTRREQUEST
DESCRIPTOR.message_types_by_name['UpdateSchemaAttrByBatchRequest'] = _UPDATESCHEMAATTRBYBATCHREQUEST
DESCRIPTOR.message_types_by_name['DeleteSchemaAttrByIdRequest'] = _DELETESCHEMAATTRBYIDREQUEST
DESCRIPTOR.message_types_by_name['AddSchemaRelationRequest'] = _ADDSCHEMARELATIONREQUEST
DESCRIPTOR.message_types_by_name['GetSchemaRelationByIdRequest'] = _GETSCHEMARELATIONBYIDREQUEST
DESCRIPTOR.message_types_by_name['ListSchemaRelationRequest'] = _LISTSCHEMARELATIONREQUEST
DESCRIPTOR.message_types_by_name['ListSchemaRelationWithLocalizationMapRequest'] = _LISTSCHEMARELATIONWITHLOCALIZATIONMAPREQUEST
DESCRIPTOR.message_types_by_name['UpdateSchemaRelationRequest'] = _UPDATESCHEMARELATIONREQUEST
DESCRIPTOR.message_types_by_name['AddSchemaRelationAttrRequest'] = _ADDSCHEMARELATIONATTRREQUEST
DESCRIPTOR.message_types_by_name['UpdateSchemaRelationAttrRequest'] = _UPDATESCHEMARELATIONATTRREQUEST
DESCRIPTOR.message_types_by_name['UpdateSchemaRelationAttrByBatchRequest'] = _UPDATESCHEMARELATIONATTRBYBATCHREQUEST
DESCRIPTOR.message_types_by_name['GetSchemaRelationAttrByIdRequest'] = _GETSCHEMARELATIONATTRBYIDREQUEST
DESCRIPTOR.message_types_by_name['ListSchemaRelationAttrRequest'] = _LISTSCHEMARELATIONATTRREQUEST
DESCRIPTOR.message_types_by_name['DeleteSchemaRelationAttrByIdRequest'] = _DELETESCHEMARELATIONATTRBYIDREQUEST
DESCRIPTOR.message_types_by_name['AddFormRequest'] = _ADDFORMREQUEST
DESCRIPTOR.message_types_by_name['UpdateFormRequest'] = _UPDATEFORMREQUEST
DESCRIPTOR.message_types_by_name['ListSchemaRefResponse'] = _LISTSCHEMAREFRESPONSE
DESCRIPTOR.message_types_by_name['ListSchemaResponse'] = _LISTSCHEMARESPONSE
DESCRIPTOR.message_types_by_name['ListFormResponse'] = _LISTFORMRESPONSE
DESCRIPTOR.message_types_by_name['ListSchemaAttrResponse'] = _LISTSCHEMAATTRRESPONSE
DESCRIPTOR.message_types_by_name['ListSchemaRelationResponse'] = _LISTSCHEMARELATIONRESPONSE
DESCRIPTOR.message_types_by_name['ListSchemaRelationAttrResponse'] = _LISTSCHEMARELATIONATTRRESPONSE
DESCRIPTOR.message_types_by_name['ListSchemaWithLocalizationMapResponse'] = _LISTSCHEMAWITHLOCALIZATIONMAPRESPONSE
DESCRIPTOR.message_types_by_name['ListSchemaRelationWithLocalizationMapResponse'] = _LISTSCHEMARELATIONWITHLOCALIZATIONMAPRESPONSE
DESCRIPTOR.message_types_by_name['DefaultResponse'] = _DEFAULTRESPONSE
DESCRIPTOR.message_types_by_name['SchemaRef'] = _SCHEMAREF
DESCRIPTOR.message_types_by_name['SchemaRefAttr'] = _SCHEMAREFATTR
DESCRIPTOR.message_types_by_name['Schema'] = _SCHEMA
DESCRIPTOR.message_types_by_name['SchemaWithLocalizationMap'] = _SCHEMAWITHLOCALIZATIONMAP
DESCRIPTOR.message_types_by_name['SchemaRelationWithLocalizationMap'] = _SCHEMARELATIONWITHLOCALIZATIONMAP
DESCRIPTOR.message_types_by_name['SchemaAttr'] = _SCHEMAATTR
DESCRIPTOR.message_types_by_name['SchemaRelation'] = _SCHEMARELATION
DESCRIPTOR.message_types_by_name['SchemaRelationAttr'] = _SCHEMARELATIONATTR
DESCRIPTOR.message_types_by_name['Form'] = _FORM
DESCRIPTOR.enum_types_by_name['OrderBy'] = _ORDERBY
DESCRIPTOR.enum_types_by_name['AttrSchemaType'] = _ATTRSCHEMATYPE
DESCRIPTOR.enum_types_by_name['SchemaStatus'] = _SCHEMASTATUS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetSchemaRefByIdRequest = _reflection.GeneratedProtocolMessageType('GetSchemaRefByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSCHEMAREFBYIDREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.GetSchemaRefByIdRequest)
  ))
_sym_db.RegisterMessage(GetSchemaRefByIdRequest)

GetSchemaByIdRequest = _reflection.GeneratedProtocolMessageType('GetSchemaByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSCHEMABYIDREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.GetSchemaByIdRequest)
  ))
_sym_db.RegisterMessage(GetSchemaByIdRequest)

GetSchemaAttrByIdRequest = _reflection.GeneratedProtocolMessageType('GetSchemaAttrByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSCHEMAATTRBYIDREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.GetSchemaAttrByIdRequest)
  ))
_sym_db.RegisterMessage(GetSchemaAttrByIdRequest)

GetFormByIdRequest = _reflection.GeneratedProtocolMessageType('GetFormByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETFORMBYIDREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.GetFormByIdRequest)
  ))
_sym_db.RegisterMessage(GetFormByIdRequest)

AddSchemaRefRequest = _reflection.GeneratedProtocolMessageType('AddSchemaRefRequest', (_message.Message,), dict(
  DESCRIPTOR = _ADDSCHEMAREFREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.AddSchemaRefRequest)
  ))
_sym_db.RegisterMessage(AddSchemaRefRequest)

UpdateSchemaRefRequest = _reflection.GeneratedProtocolMessageType('UpdateSchemaRefRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATESCHEMAREFREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.UpdateSchemaRefRequest)
  ))
_sym_db.RegisterMessage(UpdateSchemaRefRequest)

ListSchemaRefRequest = _reflection.GeneratedProtocolMessageType('ListSchemaRefRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTSCHEMAREFREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.ListSchemaRefRequest)
  ))
_sym_db.RegisterMessage(ListSchemaRefRequest)

ListSchemaRequest = _reflection.GeneratedProtocolMessageType('ListSchemaRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTSCHEMAREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.ListSchemaRequest)
  ))
_sym_db.RegisterMessage(ListSchemaRequest)

ListSchemaWithLocalizationMapRequest = _reflection.GeneratedProtocolMessageType('ListSchemaWithLocalizationMapRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTSCHEMAWITHLOCALIZATIONMAPREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.ListSchemaWithLocalizationMapRequest)
  ))
_sym_db.RegisterMessage(ListSchemaWithLocalizationMapRequest)

ListSchemaAttrRequest = _reflection.GeneratedProtocolMessageType('ListSchemaAttrRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTSCHEMAATTRREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.ListSchemaAttrRequest)
  ))
_sym_db.RegisterMessage(ListSchemaAttrRequest)

ListFormRequest = _reflection.GeneratedProtocolMessageType('ListFormRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTFORMREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.ListFormRequest)
  ))
_sym_db.RegisterMessage(ListFormRequest)

AddSchemaRefAttrRequest = _reflection.GeneratedProtocolMessageType('AddSchemaRefAttrRequest', (_message.Message,), dict(
  DESCRIPTOR = _ADDSCHEMAREFATTRREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.AddSchemaRefAttrRequest)
  ))
_sym_db.RegisterMessage(AddSchemaRefAttrRequest)

UpdateSchemaRefAttrRequest = _reflection.GeneratedProtocolMessageType('UpdateSchemaRefAttrRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATESCHEMAREFATTRREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.UpdateSchemaRefAttrRequest)
  ))
_sym_db.RegisterMessage(UpdateSchemaRefAttrRequest)

AddSchemaRequest = _reflection.GeneratedProtocolMessageType('AddSchemaRequest', (_message.Message,), dict(
  DESCRIPTOR = _ADDSCHEMAREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.AddSchemaRequest)
  ))
_sym_db.RegisterMessage(AddSchemaRequest)

UpdateSchemaRequest = _reflection.GeneratedProtocolMessageType('UpdateSchemaRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATESCHEMAREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.UpdateSchemaRequest)
  ))
_sym_db.RegisterMessage(UpdateSchemaRequest)

AddSchemaAttrRequest = _reflection.GeneratedProtocolMessageType('AddSchemaAttrRequest', (_message.Message,), dict(
  DESCRIPTOR = _ADDSCHEMAATTRREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.AddSchemaAttrRequest)
  ))
_sym_db.RegisterMessage(AddSchemaAttrRequest)

UpdateSchemaAttrRequest = _reflection.GeneratedProtocolMessageType('UpdateSchemaAttrRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATESCHEMAATTRREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.UpdateSchemaAttrRequest)
  ))
_sym_db.RegisterMessage(UpdateSchemaAttrRequest)

UpdateSchemaAttrByBatchRequest = _reflection.GeneratedProtocolMessageType('UpdateSchemaAttrByBatchRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATESCHEMAATTRBYBATCHREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.UpdateSchemaAttrByBatchRequest)
  ))
_sym_db.RegisterMessage(UpdateSchemaAttrByBatchRequest)

DeleteSchemaAttrByIdRequest = _reflection.GeneratedProtocolMessageType('DeleteSchemaAttrByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETESCHEMAATTRBYIDREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.DeleteSchemaAttrByIdRequest)
  ))
_sym_db.RegisterMessage(DeleteSchemaAttrByIdRequest)

AddSchemaRelationRequest = _reflection.GeneratedProtocolMessageType('AddSchemaRelationRequest', (_message.Message,), dict(
  DESCRIPTOR = _ADDSCHEMARELATIONREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.AddSchemaRelationRequest)
  ))
_sym_db.RegisterMessage(AddSchemaRelationRequest)

GetSchemaRelationByIdRequest = _reflection.GeneratedProtocolMessageType('GetSchemaRelationByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSCHEMARELATIONBYIDREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.GetSchemaRelationByIdRequest)
  ))
_sym_db.RegisterMessage(GetSchemaRelationByIdRequest)

ListSchemaRelationRequest = _reflection.GeneratedProtocolMessageType('ListSchemaRelationRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTSCHEMARELATIONREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.ListSchemaRelationRequest)
  ))
_sym_db.RegisterMessage(ListSchemaRelationRequest)

ListSchemaRelationWithLocalizationMapRequest = _reflection.GeneratedProtocolMessageType('ListSchemaRelationWithLocalizationMapRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTSCHEMARELATIONWITHLOCALIZATIONMAPREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.ListSchemaRelationWithLocalizationMapRequest)
  ))
_sym_db.RegisterMessage(ListSchemaRelationWithLocalizationMapRequest)

UpdateSchemaRelationRequest = _reflection.GeneratedProtocolMessageType('UpdateSchemaRelationRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATESCHEMARELATIONREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.UpdateSchemaRelationRequest)
  ))
_sym_db.RegisterMessage(UpdateSchemaRelationRequest)

AddSchemaRelationAttrRequest = _reflection.GeneratedProtocolMessageType('AddSchemaRelationAttrRequest', (_message.Message,), dict(
  DESCRIPTOR = _ADDSCHEMARELATIONATTRREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.AddSchemaRelationAttrRequest)
  ))
_sym_db.RegisterMessage(AddSchemaRelationAttrRequest)

UpdateSchemaRelationAttrRequest = _reflection.GeneratedProtocolMessageType('UpdateSchemaRelationAttrRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATESCHEMARELATIONATTRREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.UpdateSchemaRelationAttrRequest)
  ))
_sym_db.RegisterMessage(UpdateSchemaRelationAttrRequest)

UpdateSchemaRelationAttrByBatchRequest = _reflection.GeneratedProtocolMessageType('UpdateSchemaRelationAttrByBatchRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATESCHEMARELATIONATTRBYBATCHREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.UpdateSchemaRelationAttrByBatchRequest)
  ))
_sym_db.RegisterMessage(UpdateSchemaRelationAttrByBatchRequest)

GetSchemaRelationAttrByIdRequest = _reflection.GeneratedProtocolMessageType('GetSchemaRelationAttrByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSCHEMARELATIONATTRBYIDREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.GetSchemaRelationAttrByIdRequest)
  ))
_sym_db.RegisterMessage(GetSchemaRelationAttrByIdRequest)

ListSchemaRelationAttrRequest = _reflection.GeneratedProtocolMessageType('ListSchemaRelationAttrRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTSCHEMARELATIONATTRREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.ListSchemaRelationAttrRequest)
  ))
_sym_db.RegisterMessage(ListSchemaRelationAttrRequest)

DeleteSchemaRelationAttrByIdRequest = _reflection.GeneratedProtocolMessageType('DeleteSchemaRelationAttrByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETESCHEMARELATIONATTRBYIDREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.DeleteSchemaRelationAttrByIdRequest)
  ))
_sym_db.RegisterMessage(DeleteSchemaRelationAttrByIdRequest)

AddFormRequest = _reflection.GeneratedProtocolMessageType('AddFormRequest', (_message.Message,), dict(
  DESCRIPTOR = _ADDFORMREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.AddFormRequest)
  ))
_sym_db.RegisterMessage(AddFormRequest)

UpdateFormRequest = _reflection.GeneratedProtocolMessageType('UpdateFormRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEFORMREQUEST,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.UpdateFormRequest)
  ))
_sym_db.RegisterMessage(UpdateFormRequest)

ListSchemaRefResponse = _reflection.GeneratedProtocolMessageType('ListSchemaRefResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTSCHEMAREFRESPONSE,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.ListSchemaRefResponse)
  ))
_sym_db.RegisterMessage(ListSchemaRefResponse)

ListSchemaResponse = _reflection.GeneratedProtocolMessageType('ListSchemaResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTSCHEMARESPONSE,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.ListSchemaResponse)
  ))
_sym_db.RegisterMessage(ListSchemaResponse)

ListFormResponse = _reflection.GeneratedProtocolMessageType('ListFormResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTFORMRESPONSE,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.ListFormResponse)
  ))
_sym_db.RegisterMessage(ListFormResponse)

ListSchemaAttrResponse = _reflection.GeneratedProtocolMessageType('ListSchemaAttrResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTSCHEMAATTRRESPONSE,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.ListSchemaAttrResponse)
  ))
_sym_db.RegisterMessage(ListSchemaAttrResponse)

ListSchemaRelationResponse = _reflection.GeneratedProtocolMessageType('ListSchemaRelationResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTSCHEMARELATIONRESPONSE,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.ListSchemaRelationResponse)
  ))
_sym_db.RegisterMessage(ListSchemaRelationResponse)

ListSchemaRelationAttrResponse = _reflection.GeneratedProtocolMessageType('ListSchemaRelationAttrResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTSCHEMARELATIONATTRRESPONSE,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.ListSchemaRelationAttrResponse)
  ))
_sym_db.RegisterMessage(ListSchemaRelationAttrResponse)

ListSchemaWithLocalizationMapResponse = _reflection.GeneratedProtocolMessageType('ListSchemaWithLocalizationMapResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTSCHEMAWITHLOCALIZATIONMAPRESPONSE,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.ListSchemaWithLocalizationMapResponse)
  ))
_sym_db.RegisterMessage(ListSchemaWithLocalizationMapResponse)

ListSchemaRelationWithLocalizationMapResponse = _reflection.GeneratedProtocolMessageType('ListSchemaRelationWithLocalizationMapResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTSCHEMARELATIONWITHLOCALIZATIONMAPRESPONSE,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.ListSchemaRelationWithLocalizationMapResponse)
  ))
_sym_db.RegisterMessage(ListSchemaRelationWithLocalizationMapResponse)

DefaultResponse = _reflection.GeneratedProtocolMessageType('DefaultResponse', (_message.Message,), dict(
  DESCRIPTOR = _DEFAULTRESPONSE,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.DefaultResponse)
  ))
_sym_db.RegisterMessage(DefaultResponse)

SchemaRef = _reflection.GeneratedProtocolMessageType('SchemaRef', (_message.Message,), dict(
  DESCRIPTOR = _SCHEMAREF,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.SchemaRef)
  ))
_sym_db.RegisterMessage(SchemaRef)

SchemaRefAttr = _reflection.GeneratedProtocolMessageType('SchemaRefAttr', (_message.Message,), dict(
  DESCRIPTOR = _SCHEMAREFATTR,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.SchemaRefAttr)
  ))
_sym_db.RegisterMessage(SchemaRefAttr)

Schema = _reflection.GeneratedProtocolMessageType('Schema', (_message.Message,), dict(
  DESCRIPTOR = _SCHEMA,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.Schema)
  ))
_sym_db.RegisterMessage(Schema)

SchemaWithLocalizationMap = _reflection.GeneratedProtocolMessageType('SchemaWithLocalizationMap', (_message.Message,), dict(
  DESCRIPTOR = _SCHEMAWITHLOCALIZATIONMAP,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.SchemaWithLocalizationMap)
  ))
_sym_db.RegisterMessage(SchemaWithLocalizationMap)

SchemaRelationWithLocalizationMap = _reflection.GeneratedProtocolMessageType('SchemaRelationWithLocalizationMap', (_message.Message,), dict(
  DESCRIPTOR = _SCHEMARELATIONWITHLOCALIZATIONMAP,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.SchemaRelationWithLocalizationMap)
  ))
_sym_db.RegisterMessage(SchemaRelationWithLocalizationMap)

SchemaAttr = _reflection.GeneratedProtocolMessageType('SchemaAttr', (_message.Message,), dict(
  DESCRIPTOR = _SCHEMAATTR,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.SchemaAttr)
  ))
_sym_db.RegisterMessage(SchemaAttr)

SchemaRelation = _reflection.GeneratedProtocolMessageType('SchemaRelation', (_message.Message,), dict(
  DESCRIPTOR = _SCHEMARELATION,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.SchemaRelation)
  ))
_sym_db.RegisterMessage(SchemaRelation)

SchemaRelationAttr = _reflection.GeneratedProtocolMessageType('SchemaRelationAttr', (_message.Message,), dict(
  DESCRIPTOR = _SCHEMARELATIONATTR,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.SchemaRelationAttr)
  ))
_sym_db.RegisterMessage(SchemaRelationAttr)

Form = _reflection.GeneratedProtocolMessageType('Form', (_message.Message,), dict(
  DESCRIPTOR = _FORM,
  __module__ = 'metadata.schema.schema_pb2'
  # @@protoc_insertion_point(class_scope:schema.Form)
  ))
_sym_db.RegisterMessage(Form)



_SCHEMASERVICE = _descriptor.ServiceDescriptor(
  name='SchemaService',
  full_name='schema.SchemaService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=7945,
  serialized_end=12143,
  methods=[
  _descriptor.MethodDescriptor(
    name='AddSchemaRef',
    full_name='schema.SchemaService.AddSchemaRef',
    index=0,
    containing_service=None,
    input_type=_ADDSCHEMAREFREQUEST,
    output_type=_SCHEMAREF,
    serialized_options=_b('\202\323\344\223\002$\"\037/api/v2/metadata/schema/ref/add:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateSchemaRef',
    full_name='schema.SchemaService.UpdateSchemaRef',
    index=1,
    containing_service=None,
    input_type=_UPDATESCHEMAREFREQUEST,
    output_type=_SCHEMAREF,
    serialized_options=_b('\202\323\344\223\002\'\"\"/api/v2/metadata/schema/ref/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetSchemaRefByID',
    full_name='schema.SchemaService.GetSchemaRefByID',
    index=2,
    containing_service=None,
    input_type=_GETSCHEMAREFBYIDREQUEST,
    output_type=_SCHEMAREF,
    serialized_options=_b('\202\323\344\223\002%\022#/api/v2/metadata/schema/ref/by/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListSchemaRef',
    full_name='schema.SchemaService.ListSchemaRef',
    index=3,
    containing_service=None,
    input_type=_LISTSCHEMAREFREQUEST,
    output_type=_LISTSCHEMAREFRESPONSE,
    serialized_options=_b('\202\323\344\223\002#\022!/api/v2/metadata/schema/ref/query'),
  ),
  _descriptor.MethodDescriptor(
    name='AddSchemaRefAttr',
    full_name='schema.SchemaService.AddSchemaRefAttr',
    index=4,
    containing_service=None,
    input_type=_ADDSCHEMAREFATTRREQUEST,
    output_type=_SCHEMAREFATTR,
    serialized_options=_b('\202\323\344\223\002)\"$/api/v2/metadata/schema/ref/attr/add:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateSchemaRefAttr',
    full_name='schema.SchemaService.UpdateSchemaRefAttr',
    index=5,
    containing_service=None,
    input_type=_UPDATESCHEMAREFATTRREQUEST,
    output_type=_SCHEMAREFATTR,
    serialized_options=_b('\202\323\344\223\002,\"\'/api/v2/metadata/schema/ref/attr/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='AddSchema',
    full_name='schema.SchemaService.AddSchema',
    index=6,
    containing_service=None,
    input_type=_ADDSCHEMAREQUEST,
    output_type=_SCHEMA,
    serialized_options=_b('\202\323\344\223\002 \"\033/api/v2/metadata/schema/add:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateSchema',
    full_name='schema.SchemaService.UpdateSchema',
    index=7,
    containing_service=None,
    input_type=_UPDATESCHEMAREQUEST,
    output_type=_SCHEMA,
    serialized_options=_b('\202\323\344\223\002#\"\036/api/v2/metadata/schema/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetSchemaByID',
    full_name='schema.SchemaService.GetSchemaByID',
    index=8,
    containing_service=None,
    input_type=_GETSCHEMABYIDREQUEST,
    output_type=_SCHEMA,
    serialized_options=_b('\202\323\344\223\002!\022\037/api/v2/metadata/schema/by/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListSchema',
    full_name='schema.SchemaService.ListSchema',
    index=9,
    containing_service=None,
    input_type=_LISTSCHEMAREQUEST,
    output_type=_LISTSCHEMARESPONSE,
    serialized_options=_b('\202\323\344\223\002\"\"\035/api/v2/metadata/schema/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListSchemaWithLocalizationMap',
    full_name='schema.SchemaService.ListSchemaWithLocalizationMap',
    index=10,
    containing_service=None,
    input_type=_LISTSCHEMAWITHLOCALIZATIONMAPREQUEST,
    output_type=_LISTSCHEMAWITHLOCALIZATIONMAPRESPONSE,
    serialized_options=_b('\202\323\344\223\002/\"*/api/v2/metadata/schema/localization/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='AddSchemaAttr',
    full_name='schema.SchemaService.AddSchemaAttr',
    index=11,
    containing_service=None,
    input_type=_ADDSCHEMAATTRREQUEST,
    output_type=_SCHEMAATTR,
    serialized_options=_b('\202\323\344\223\002%\" /api/v2/metadata/schema/attr/add:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateSchemaAttr',
    full_name='schema.SchemaService.UpdateSchemaAttr',
    index=12,
    containing_service=None,
    input_type=_UPDATESCHEMAATTRREQUEST,
    output_type=_SCHEMAATTR,
    serialized_options=_b('\202\323\344\223\002(\"#/api/v2/metadata/schema/attr/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateSchemaAttrByBatch',
    full_name='schema.SchemaService.UpdateSchemaAttrByBatch',
    index=13,
    containing_service=None,
    input_type=_UPDATESCHEMAATTRBYBATCHREQUEST,
    output_type=_DEFAULTRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\")/api/v2/metadata/schema/attr/update/batch:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetSchemaAttrByID',
    full_name='schema.SchemaService.GetSchemaAttrByID',
    index=14,
    containing_service=None,
    input_type=_GETSCHEMAATTRBYIDREQUEST,
    output_type=_SCHEMAATTR,
    serialized_options=_b('\202\323\344\223\002&\022$/api/v2/metadata/schema/attr/by/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListSchemaAttr',
    full_name='schema.SchemaService.ListSchemaAttr',
    index=15,
    containing_service=None,
    input_type=_LISTSCHEMAATTRREQUEST,
    output_type=_LISTSCHEMAATTRRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\"\"/api/v2/metadata/schema/attr/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteSchemaAttrByID',
    full_name='schema.SchemaService.DeleteSchemaAttrByID',
    index=16,
    containing_service=None,
    input_type=_DELETESCHEMAATTRBYIDREQUEST,
    output_type=_DEFAULTRESPONSE,
    serialized_options=_b('\202\323\344\223\002**(/api/v2/metadata/schema/attr/delete/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='AddSchemaRelation',
    full_name='schema.SchemaService.AddSchemaRelation',
    index=17,
    containing_service=None,
    input_type=_ADDSCHEMARELATIONREQUEST,
    output_type=_SCHEMARELATION,
    serialized_options=_b('\202\323\344\223\002)\"$/api/v2/metadata/schema/relation/add:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateSchemaRelation',
    full_name='schema.SchemaService.UpdateSchemaRelation',
    index=18,
    containing_service=None,
    input_type=_UPDATESCHEMARELATIONREQUEST,
    output_type=_SCHEMARELATION,
    serialized_options=_b('\202\323\344\223\002,\"\'/api/v2/metadata/schema/relation/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetSchemaRelationByID',
    full_name='schema.SchemaService.GetSchemaRelationByID',
    index=19,
    containing_service=None,
    input_type=_GETSCHEMARELATIONBYIDREQUEST,
    output_type=_SCHEMARELATION,
    serialized_options=_b('\202\323\344\223\002*\022(/api/v2/metadata/schema/relation/by/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListSchemaRelation',
    full_name='schema.SchemaService.ListSchemaRelation',
    index=20,
    containing_service=None,
    input_type=_LISTSCHEMARELATIONREQUEST,
    output_type=_LISTSCHEMARELATIONRESPONSE,
    serialized_options=_b('\202\323\344\223\002+\"&/api/v2/metadata/schema/relation/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListSchemaRelationWithLocalizationMap',
    full_name='schema.SchemaService.ListSchemaRelationWithLocalizationMap',
    index=21,
    containing_service=None,
    input_type=_LISTSCHEMARELATIONWITHLOCALIZATIONMAPREQUEST,
    output_type=_LISTSCHEMARELATIONWITHLOCALIZATIONMAPRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\"3/api/v2/metadata/schema/relation/localization/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='AddSchemaRelationAttr',
    full_name='schema.SchemaService.AddSchemaRelationAttr',
    index=22,
    containing_service=None,
    input_type=_ADDSCHEMARELATIONATTRREQUEST,
    output_type=_SCHEMARELATIONATTR,
    serialized_options=_b('\202\323\344\223\002.\")/api/v2/metadata/schema/relation/attr/add:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateSchemaRelationAttr',
    full_name='schema.SchemaService.UpdateSchemaRelationAttr',
    index=23,
    containing_service=None,
    input_type=_UPDATESCHEMARELATIONATTRREQUEST,
    output_type=_SCHEMARELATIONATTR,
    serialized_options=_b('\202\323\344\223\0021\",/api/v2/metadata/schema/relation/attr/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateSchemaRelationAttrByBatch',
    full_name='schema.SchemaService.UpdateSchemaRelationAttrByBatch',
    index=24,
    containing_service=None,
    input_type=_UPDATESCHEMARELATIONATTRBYBATCHREQUEST,
    output_type=_DEFAULTRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\"2/api/v2/metadata/schema/relation/attr/update/batch:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetSchemaRelationAttrByID',
    full_name='schema.SchemaService.GetSchemaRelationAttrByID',
    index=25,
    containing_service=None,
    input_type=_GETSCHEMARELATIONATTRBYIDREQUEST,
    output_type=_SCHEMARELATIONATTR,
    serialized_options=_b('\202\323\344\223\002/\022-/api/v2/metadata/schema/relation/attr/by/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListSchemaRelationAttr',
    full_name='schema.SchemaService.ListSchemaRelationAttr',
    index=26,
    containing_service=None,
    input_type=_LISTSCHEMARELATIONATTRREQUEST,
    output_type=_LISTSCHEMARELATIONATTRRESPONSE,
    serialized_options=_b('\202\323\344\223\0020\"+/api/v2/metadata/schema/relation/attr/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteSchemaRelationAttrByID',
    full_name='schema.SchemaService.DeleteSchemaRelationAttrByID',
    index=27,
    containing_service=None,
    input_type=_DELETESCHEMARELATIONATTRBYIDREQUEST,
    output_type=_DEFAULTRESPONSE,
    serialized_options=_b('\202\323\344\223\0023*1/api/v2/metadata/schema/relation/attr/delete/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='AddForm',
    full_name='schema.SchemaService.AddForm',
    index=28,
    containing_service=None,
    input_type=_ADDFORMREQUEST,
    output_type=_FORM,
    serialized_options=_b('\202\323\344\223\002\036\"\031/api/v2/metadata/form/add:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateForm',
    full_name='schema.SchemaService.UpdateForm',
    index=29,
    containing_service=None,
    input_type=_UPDATEFORMREQUEST,
    output_type=_FORM,
    serialized_options=_b('\202\323\344\223\002!\"\034/api/v2/metadata/form/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetFormByID',
    full_name='schema.SchemaService.GetFormByID',
    index=30,
    containing_service=None,
    input_type=_GETFORMBYIDREQUEST,
    output_type=_FORM,
    serialized_options=_b('\202\323\344\223\002\037\022\035/api/v2/metadata/form/by/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListForm',
    full_name='schema.SchemaService.ListForm',
    index=31,
    containing_service=None,
    input_type=_LISTFORMREQUEST,
    output_type=_LISTFORMRESPONSE,
    serialized_options=_b('\202\323\344\223\002 \"\033/api/v2/metadata/form/query:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_SCHEMASERVICE)

DESCRIPTOR.services_by_name['SchemaService'] = _SCHEMASERVICE

# @@protoc_insertion_point(module_scope)
