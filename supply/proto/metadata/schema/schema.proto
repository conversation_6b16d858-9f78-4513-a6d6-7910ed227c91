syntax = "proto3";
package schema;
import "google/api/annotations.proto";
import "google/protobuf/struct.proto";


// SchemaService 用于维护主档Schema
service SchemaService {
    
    // 添加Schema Ref
    rpc AddSchemaRef(AddSchemaRefRequest) returns (SchemaRef){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/ref/add"
        body: "*"
        };
    }
    // 修改Schema Ref
    rpc UpdateSchemaRef(UpdateSchemaRefRequest) returns (SchemaRef){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/ref/update"
        body: "*"
        };
    }
    // 根据id获取Schema Ref
    rpc GetSchemaRefByID(GetSchemaRefByIdRequest) returns (SchemaRef){
        option (google.api.http) = {
        get: "/api/v2/metadata/schema/ref/by/{id}"
        };
    }
    // 获Schema Ref列表
    rpc ListSchemaRef(ListSchemaRefRequest) returns (ListSchemaRefResponse){
        option (google.api.http) = {
        get: "/api/v2/metadata/schema/ref/query"
        };
    }
    // 添加Schema Ref Attr
    rpc AddSchemaRefAttr(AddSchemaRefAttrRequest) returns (SchemaRefAttr){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/ref/attr/add"
        body: "*"
        };
    }
    // 修改Schema Ref Attr
    rpc UpdateSchemaRefAttr(UpdateSchemaRefAttrRequest) returns (SchemaRefAttr){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/ref/attr/update"
        body: "*"
        };
    }
    // 添加Schema
    rpc AddSchema(AddSchemaRequest) returns (Schema){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/add"
        body: "*"
        };
    }
    // 修改Schema
    rpc UpdateSchema(UpdateSchemaRequest) returns (Schema){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/update"
        body: "*"
        };
    }
    // 根据id获取Schema
    rpc GetSchemaByID(GetSchemaByIdRequest) returns (Schema){
        option (google.api.http) = {
        get: "/api/v2/metadata/schema/by/{id}"
        };
    }
    // 获Schema列表
    rpc ListSchema(ListSchemaRequest) returns (ListSchemaResponse){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/query"
        body: "*"
        };
    }
    // 获Schema简化的Localizations键值信息
    rpc ListSchemaWithLocalizationMap(ListSchemaWithLocalizationMapRequest) returns (ListSchemaWithLocalizationMapResponse){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/localization/query"
        body: "*"
        };
    }
    // 添加Schema Attr
    rpc AddSchemaAttr(AddSchemaAttrRequest) returns (SchemaAttr){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/attr/add"
        body: "*"
        };
    }
    // 修改Schema Attr
    rpc UpdateSchemaAttr(UpdateSchemaAttrRequest) returns (SchemaAttr){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/attr/update"
        body: "*"
        };
    }
    // 批量修改和添加Schema Attr
    rpc UpdateSchemaAttrByBatch(UpdateSchemaAttrByBatchRequest) returns (DefaultResponse){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/attr/update/batch"
        body: "*"
        };
    }
    // 根据id获取Schema Attr
    rpc GetSchemaAttrByID(GetSchemaAttrByIdRequest) returns (SchemaAttr){
        option (google.api.http) = {
        get: "/api/v2/metadata/schema/attr/by/{id}"
        };
    }
    // 获Schema Attr列表
    rpc ListSchemaAttr(ListSchemaAttrRequest) returns (ListSchemaAttrResponse){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/attr/query"
        body: "*"
        };
    }
    // 根据id删除Schema Attr
    rpc DeleteSchemaAttrByID(DeleteSchemaAttrByIdRequest) returns (DefaultResponse){
        option (google.api.http) = {
        delete: "/api/v2/metadata/schema/attr/delete/{id}"
        };
    }
    // 添加Schema Relation
    rpc AddSchemaRelation(AddSchemaRelationRequest) returns (SchemaRelation){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/relation/add"
        body: "*"
        };
    }
    // 修改Schema Relation
    rpc UpdateSchemaRelation(UpdateSchemaRelationRequest) returns (SchemaRelation){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/relation/update"
        body: "*"
        };
    }
    // 根据id获取Schema Relation
    rpc GetSchemaRelationByID(GetSchemaRelationByIdRequest) returns (SchemaRelation){
        option (google.api.http) = {
        get: "/api/v2/metadata/schema/relation/by/{id}"
        };
    }
    // 获Schema Relation列表
    rpc ListSchemaRelation(ListSchemaRelationRequest) returns (ListSchemaRelationResponse){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/relation/query"
        body: "*"
        };
    }
     // 获Schema Relation简化的Localizations键值信息
    rpc ListSchemaRelationWithLocalizationMap(ListSchemaRelationWithLocalizationMapRequest) returns (ListSchemaRelationWithLocalizationMapResponse){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/relation/localization/query"
        body: "*"
        };
    }
    // 添加Schema Relation Attr
    rpc AddSchemaRelationAttr(AddSchemaRelationAttrRequest) returns (SchemaRelationAttr){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/relation/attr/add"
        body: "*"
        };
    }
    // 修改Schema Relation Attr
    rpc UpdateSchemaRelationAttr(UpdateSchemaRelationAttrRequest) returns (SchemaRelationAttr){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/relation/attr/update"
        body: "*"
        };
    }
    // 批量修改和添加Schema Relation Attr
    rpc UpdateSchemaRelationAttrByBatch(UpdateSchemaRelationAttrByBatchRequest) returns (DefaultResponse){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/relation/attr/update/batch"
        body: "*"
        };
    }
    // 根据id获取Schema Relation Attr
    rpc GetSchemaRelationAttrByID(GetSchemaRelationAttrByIdRequest) returns (SchemaRelationAttr){
        option (google.api.http) = {
        get: "/api/v2/metadata/schema/relation/attr/by/{id}"
        };
    }
    // 获Schema Relation Attr列表
    rpc ListSchemaRelationAttr(ListSchemaRelationAttrRequest) returns (ListSchemaRelationAttrResponse){
        option (google.api.http) = {
        post: "/api/v2/metadata/schema/relation/attr/query"
        body: "*"
        };
    }
    // 根据id删除Schema Relation Attr
    rpc DeleteSchemaRelationAttrByID(DeleteSchemaRelationAttrByIdRequest) returns (DefaultResponse){
        option (google.api.http) = {
        delete: "/api/v2/metadata/schema/relation/attr/delete/{id}"
        };
    }
    // 添加Form
    rpc AddForm(AddFormRequest) returns (Form){
        option (google.api.http) = {
        post: "/api/v2/metadata/form/add"
        body: "*"
        };
    }
    // 修改Form
    rpc UpdateForm(UpdateFormRequest) returns (Form){
        option (google.api.http) = {
        post: "/api/v2/metadata/form/update"
        body: "*"
        };
    }
    // 根据id获取Form
    rpc GetFormByID(GetFormByIdRequest) returns (Form){
        option (google.api.http) = {
        get: "/api/v2/metadata/form/by/{id}"
        };
    }
    // 获Form列表
    rpc ListForm(ListFormRequest) returns (ListFormResponse){
        option (google.api.http) = {
        post: "/api/v2/metadata/form/query"
        body: "*"
        };
    }
}


message GetSchemaRefByIdRequest{
    // schema 引用id
    uint64 id = 1;
    // 是否在结果集包含该schema相关引用属性
    bool include_attr_refs = 2;
    // 预留扩展查询字段json
    google.protobuf.Struct extends = 3;
}
message GetSchemaByIdRequest{
    // schema id
    uint64 id = 1;
    // 是否在结果集包含该schema相关属性
    bool include_attrs = 2;
    // 预留扩展查询字段json
    google.protobuf.Struct extends = 3;
    string lan = 4;
}
message GetSchemaAttrByIdRequest{
    // attr id
    uint64 id = 1;
    string lan = 2;
}
message GetFormByIdRequest{
    // form id
    uint64 id = 1;
    // 预留扩展查询字段json
    google.protobuf.Struct extends = 2;
    // 语言
    string lan = 3;
}

message AddSchemaRefRequest{
    string name = 1;
    // 显示名称
    string title = 2;
    // 描述
    string description = 3;
    // 扩展属性， json
    google.protobuf.Struct extends = 4;
}
message UpdateSchemaRefRequest{
    // schema ref id
    uint64 id = 1;
    // 更新字段json
    google.protobuf.Struct fields = 2;
}

message ListSchemaRefRequest{
    // 分页大小
    int32 limit = 1;
    // 跳过行数
    int32 offset = 2;
    // 排序字段
    string sort = 3;
    // 排序顺序
    string order = 4;
    // 返回总条数
    bool include_total = 5;
    // 附加过滤条件json
    google.protobuf.Struct filters = 6;
    // 扩展参数json
    google.protobuf.Struct extends = 7;
}
message ListSchemaRequest{
    // schema 状态
    string status = 1;
    // 分页大小
    int32 limit = 2;
    // 跳过行数
    int32 offset = 3;
    // 排序字段
    string sort = 4;
    // 排序顺序
    string order = 5;
    // 返回总条数
    bool include_total = 6;
    // 附加过滤条件
    google.protobuf.Struct filters = 7;
    // 扩展参数
    google.protobuf.Struct extends = 8;
    string lan = 9;
    repeated uint64 ids = 10;
    repeated string names = 11;
}
message ListSchemaWithLocalizationMapRequest{
    // schema name 列表
    repeated string names = 1;
    // 当前语言
    string lan = 2;
    // 是否包含属性
    bool include_attrs = 3;
    // 是否包含所有语言
    bool include_all_localizations = 4;
    
}
message ListSchemaAttrRequest{
    //schema id
    uint64 schema_id = 1;
    // 属性状态 
    string status = 2;
    // 分页大小
    int32 limit = 3;
    // 跳过行数
    int32 offset = 4;
    // 排序字段
    string sort = 5;
    // 排序顺序
    string order = 6;
    // 返回总条数
    bool include_total = 7;
    // 附加过滤条件json
    google.protobuf.Struct filters = 8;
    // 扩展参数json
    google.protobuf.Struct extends = 9;
    string lan = 10;
}
message ListFormRequest{
    // form 状态
    string status = 1;
    // 分页大小
    int32 limit = 2;
    // 跳过行数
    int32 offset = 3;
    // 排序字段
    string sort = 4;
    // 排序顺序
    string order = 5;
    // 返回总条数
    bool include_total = 6;
    // 附加过滤条件
    google.protobuf.Struct filters = 7;
    // 扩展参数
    google.protobuf.Struct extends = 8;
    // 语言
    string lan = 9;
}
message AddSchemaRefAttrRequest{
    // 名称(如: name, code, type)
    string name = 1;
    // 显示名称
    string title = 2;
    // 对应的SchemaRef的id
    uint64 schema_ref_id = 3;
    // 字段用途
    string attr_owner_type = 4;
    // 属性数据类型
    string attr_data_type = 5;
    // 扩展属性json
    google.protobuf.Struct extends = 6;
    // 属性状态 
    string status = 7;
}
message UpdateSchemaRefAttrRequest{
    // schema ref attr id
    uint64 id = 1;
    // 更新字段(json
    google.protobuf.Struct fields = 2;
}

message AddSchemaRequest{
    // schema ref id
    uint64 schema_ref_id = 1;
    // schema name
    string name = 2;
    // 显示名称
    string title = 3;
    // ignore state or not
    bool ignore_state=4;
    // allow parent(data tree) or not
    bool allow_parent = 5;
    //ui form data
	string form =6;
    // 描述
    string description = 7;
    // 扩展属性json
    google.protobuf.Struct extends = 8;
    // schema 状态
    string status = 9;
    string lan = 10;
    
}
message UpdateSchemaRequest{
    // schema id
    uint64 id = 1;
    // 更新字段json
    google.protobuf.Struct fields = 2;
    string lan = 3;
}
message AddSchemaAttrRequest{
    // 名称(如: name, code, type)
    string name = 1;
    // 显示名称
    string title = 2;
    // 描述
    string description = 3;
    // schema id
    uint64 schema_id = 4;
    // 对应的SchemaRefAttr的id
    uint64 ref_attr_id = 5;
    // 字段用途
    string attr_owner_type = 6;
    // 属性数据类型
    string attr_data_type = 7;
    //约束json
    google.protobuf.Struct constraints = 8;
    // Form UI
    string form = 9;
    // 扩展属性json
    google.protobuf.Struct extends = 10;
    // 属性状态 
    string status = 11;
    string lan = 12;
    
}
message UpdateSchemaAttrRequest{
    // attr id
    uint64 id = 1;
    // 更新字段json
    google.protobuf.Struct fields = 2;
    string lan = 3;
}
message UpdateSchemaAttrByBatchRequest{
    // schema id
    uint64 schema_id = 1;
    // list [{"id":"4160686397126082561","name":"status2", "attr_data_type":"string", "attr_owner_type":"entity_attr"}]
    repeated google.protobuf.Struct attrs = 2;
    string lan = 3;
}
message DeleteSchemaAttrByIdRequest{
    // attr id
    uint64 id = 1;
}
message AddSchemaRelationRequest{
    // schema name
    string name = 1;
    // 显示名称
    string title = 2;
    // 发起关联得schema id
    uint64 source_schema_id = 3;
    //被关联得schema id
    uint64 target_schema_id = 4;
    // ignore state or not
    bool ignore_state=5;
    //ui form data
	string form  =6;
    // 描述
    string description = 7;
    // 关系约束
    google.protobuf.Struct constraints = 8;
    // schema relation 状态
    string status = 9;
    string lan = 10;
    
}
message GetSchemaRelationByIdRequest{
    // schema relation id
    uint64 id = 1;
    // 是否在结果集包含该schema relation相关属性
    bool include_attrs = 2;
    // 预留扩展查询字段json
    google.protobuf.Struct extends = 3;
    string lan = 4;
}
message ListSchemaRelationRequest{
    // 发起关联的schema id
    uint64 source_schema_id = 1;
    // 被关联的schema id
    uint64 target_schema_id = 2;
    // 分页大小
    int32 limit = 3;
    // 跳过行数
    int32 offset = 4;
    // 排序字段
    string sort = 5;
    // 排序顺序
    OrderBy order = 6;
    // 返回总条数
    bool include_total = 7;
    // 附加过滤条件
    google.protobuf.Struct filters = 8;
    // 扩展参数
    google.protobuf.Struct extends = 9;
    // 状态
    string status = 10;
    string lan = 11;
}
message ListSchemaRelationWithLocalizationMapRequest{
    // 发起关联的schema name
    string source_schema_name = 1;
    // 关系名称列表
    repeated string schema_relation_names = 2;
    // 当前语言
    string lan = 3;
    //是否包含属性
    bool include_attrs = 4;
    // 是否包含所有语言
    bool include_all_localizations = 5;
}
message UpdateSchemaRelationRequest{
    // schema relation id
    uint64 id = 1;
    // 更新字段json
    google.protobuf.Struct fields = 2;
    string lan = 3;
}
message AddSchemaRelationAttrRequest{
    // 名称(如: name, code, type)
    string name = 1;
    // 显示名称
    string title = 2;
    // 描述
    string description = 3;
    // schema relation id
    uint64 schema_relation_id = 4;
    // 对应的SchemaRefAttr的id
    uint64 ref_attr_id = 5;
    // 属性数据类型
    string attr_data_type = 6;
    //约束json
    google.protobuf.Struct constraints = 7;
    // Form UI
    string form = 8;
    // 属性状态 
    string status = 9;
    string lan = 10;
    
}
message UpdateSchemaRelationAttrRequest{
    // attr id
    uint64 id = 1;
    // 更新字段json
    google.protobuf.Struct fields = 2;
    string lan =3;
}
message UpdateSchemaRelationAttrByBatchRequest{
    // schema relation id
    uint64 schema_relation_id = 1;
    // list [{"id":"4160686397126082561","name":"status2", "attr_data_type":"string"}]
    repeated google.protobuf.Struct attrs = 2;
    string lan =3;
}
message GetSchemaRelationAttrByIdRequest{
    // attr id
    uint64 id = 1;
    string lan =2;
}
message ListSchemaRelationAttrRequest{
    // schema relation id
    uint64 schema_relation_id = 1;
    // 属性状态 
    string status = 2;
    // 分页大小
    int32 limit = 3;
    // 跳过行数
    int32 offset = 4;
    // 排序字段
    string sort = 5;
    // 排序顺序
    string order = 6;
    // 返回总条数
    bool include_total = 7;
    // 附加过滤条件
    google.protobuf.Struct filters = 8;
    // 扩展参数
    google.protobuf.Struct extends = 9;
    string lan = 10;
}
message DeleteSchemaRelationAttrByIdRequest{
    // attr id
    uint64 id = 1;
}
message AddFormRequest{
    // form name
    string name = 1;
    // 显示名称
    string title = 2;
    //ui form data
	string form =3;
    // 描述
    string description = 4;
    // schema 状态
    string status = 5;
    // 多语言支持"localizations": { "en":{ "title": "Store Form"}, "zh_cn": {"title":"门店", "description":"维护门店信息"} }
    string lan = 6;
    
}
message UpdateFormRequest{
    // form id
    uint64 id = 1;
    // 更新字段json
    google.protobuf.Struct fields = 2;
    string lan =3;
}

message ListSchemaRefResponse{
    repeated SchemaRef rows = 1;
    int32 total = 2;
}
message ListSchemaResponse{
    repeated Schema rows = 1;
    int32 total = 2;
}

message ListFormResponse{
    repeated Form rows = 1;
    int32 total = 2;
}

message ListSchemaAttrResponse{
    repeated SchemaAttr rows = 1;
    int32 total = 2;
}

message ListSchemaRelationResponse{
    repeated SchemaRelation rows = 1;
    int32 total = 2;
}
message ListSchemaRelationAttrResponse{
    repeated SchemaRelationAttr rows = 1;
    int32 total = 2;
}

message ListSchemaWithLocalizationMapResponse{
    repeated SchemaWithLocalizationMap schemas =1;
}
message ListSchemaRelationWithLocalizationMapResponse{
    repeated SchemaRelationWithLocalizationMap schemas =1;
}
message DefaultResponse{
    bool result = 1;
}
// 排序枚举 
enum OrderBy {
    ASC = 0;
    DESC = 1;
  }


// schema type
enum AttrSchemaType{
    CATALOG_ATTR = 0;
    ENTITY_ATTR = 1;
    REL_ATTR = 2;
}

// Schema状态(草稿/启用/禁用)
enum SchemaStatus{
    DRAFT = 0;
    ENABLED = 1;
    DISABLED = 2;
}
message SchemaRef{
    // Schema ref id
    uint64 id = 1;
    // 名称(如: PRODUCT, STAFF)
    string name = 2;
    // 显示名称
    string title = 3;
    // 描述
    string description = 4;
    // 创建时间, string化的时间
    string created = 5;
    // 修改时间, string化的时间
    string updated = 6;
    // 扩展属性json
    google.protobuf.Struct extends = 7;
    // 关联的ref attr列表
    repeated SchemaRefAttr attrs = 8;
}
message SchemaRefAttr{
    // ref attr id
    uint64 id = 1;
    // 名称(如: name, code, type)
    string name = 2;
    // 显示名称
    string title = 3;
    // 对应的SchemaRef的id
    uint64 schema_ref_id = 4;
    // 字段用途
    AttrSchemaType attr_owner_type = 5;
    // 属性数据类型
    string attr_data_type = 6;
    // 创建时间, string化的时间
    string created = 7;
    // 修改时间, string化的时间
    string updated = 8;
    // 扩展属性json
    google.protobuf.Struct extends = 9;
    // 属性状态 
    string status = 10;
}
message Schema{
    // Schema id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // schema ref id
    uint64 schema_ref_id = 3;
    // schema name
    string name = 4;
    // 显示名称
    string title = 5;
    // ignore state or not
    bool ignore_state = 6;
    // allow parent(data tree) or not
    bool allow_parent = 7;
    //ui form data
	string form = 8;
    // 描述
    string description = 9;
    //创建日期
    string created = 10;
    //修改日期
    string updated = 11;
    // 扩展属性json
    google.protobuf.Struct extends = 12;
    // schema 状态
    string status = 13;
    // 关联的ref attr列表
    repeated SchemaAttr attrs = 14;
}
message SchemaWithLocalizationMap{
    // Schema id
    uint64 id = 1;
    // schema name
    string name = 2;
    // 显示名称
    string title = 3;
    // 描述
    string description = 4;
    google.protobuf.Struct localizations = 5;
    google.protobuf.Struct attrs = 6;
}
message SchemaRelationWithLocalizationMap{
    // Schema id
    uint64 id = 1;
    // schema name
    string name = 2;
    // 显示名称
    string title = 3;
    // 描述
    string description = 4;
    google.protobuf.Struct localizations = 5;
    google.protobuf.Struct attrs = 6;
}
message SchemaAttr{
    // attr id
    uint64 id = 1;
    // 名称(如: name, code, type)
    string name = 2;
    // 显示名称
    string title = 3;
    // 描述
    string description = 4;
    // 商户id
    uint64 partner_id = 5;
    // schema id
    uint64 schema_id = 6;
    // 对应的SchemaRefAttr的id
    uint64 ref_attr_id = 7;
    // 字段用途
    string attr_owner_type = 8;
    // 属性数据类型
    string attr_data_type = 9;
    //约束json
    google.protobuf.Struct constraints = 10;
    // Form UI
    string form = 11;
    // 创建时间, string化的时间
    string created = 12;
    // 修改时间, string化的时间
    string updated = 13;
    // 扩展属性json
    google.protobuf.Struct extends = 14;
    // 属性状态 
    string status = 15;
    
}
message SchemaRelation{
    // Schema relation id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // schema name
    string name = 3;
    // 显示名称
    string title = 4;
    // 发起关联得schema id
    uint64 source_schema_id = 5;
    //被关联得schema id
    uint64 target_schema_id = 6;
    // ignore state or not
    bool ignore_state = 7;
    //约束json
    google.protobuf.Struct constraints = 8;
    //ui form data
	string form = 9;
    // 描述
    string description = 10;
    //创建日期
    string created = 11;
    //修改日期
    string updated = 12;
    // schema 状态
    string status = 13;
    // 关联的attr列表
    repeated SchemaRelationAttr attrs = 14;
}
message SchemaRelationAttr{
    // attr id
    uint64 id = 1;
    // 名称(如: name, code, type)
    string name = 2;
    // 显示名称
    string title = 3;
    // 描述
    string description = 4;
    // 商户id
    uint64 partner_id = 5;
    // schema id
    uint64 schema_relation_id = 6;
    // 对应的SchemaRefAttr的id
    uint64 ref_attr_id = 7;
    // 属性数据类型
    string attr_data_type = 8;
    //约束json
    google.protobuf.Struct constraints = 9;
    // Form UI
    string form = 10;
    // 创建时间, string化的时间
    string created = 11;
    // 修改时间, string化的时间
    string updated = 12;
    // 扩展属性json
    google.protobuf.Struct extends = 13;
    // 属性状态 
    string status = 14;
    
}

message Form{
    // Form id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // schema name
    string name = 3;
    // 显示名称
    string title = 4;
    //ui form data
	string form = 5;
    // 描述
    string description = 6;
    //创建日期
    string created = 7;
    //修改日期
    string updated = 8;
    // 扩展属性json
    google.protobuf.Struct extends = 9;
    // schema 状态
    string status = 10;
    // 关联的ref attr列表
    repeated SchemaAttr attrs = 11;
}