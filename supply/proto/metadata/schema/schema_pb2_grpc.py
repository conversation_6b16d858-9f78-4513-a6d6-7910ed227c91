# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from metadata.schema import schema_pb2 as metadata_dot_schema_dot_schema__pb2


class SchemaServiceStub(object):
  """SchemaService 用于维护主档Schema
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.AddSchemaRef = channel.unary_unary(
        '/schema.SchemaService/AddSchemaRef',
        request_serializer=metadata_dot_schema_dot_schema__pb2.AddSchemaRefRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.SchemaRef.FromString,
        )
    self.UpdateSchemaRef = channel.unary_unary(
        '/schema.SchemaService/UpdateSchemaRef',
        request_serializer=metadata_dot_schema_dot_schema__pb2.UpdateSchemaRefRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.SchemaRef.FromString,
        )
    self.GetSchemaRefByID = channel.unary_unary(
        '/schema.SchemaService/GetSchemaRefByID',
        request_serializer=metadata_dot_schema_dot_schema__pb2.GetSchemaRefByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.SchemaRef.FromString,
        )
    self.ListSchemaRef = channel.unary_unary(
        '/schema.SchemaService/ListSchemaRef',
        request_serializer=metadata_dot_schema_dot_schema__pb2.ListSchemaRefRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.ListSchemaRefResponse.FromString,
        )
    self.AddSchemaRefAttr = channel.unary_unary(
        '/schema.SchemaService/AddSchemaRefAttr',
        request_serializer=metadata_dot_schema_dot_schema__pb2.AddSchemaRefAttrRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.SchemaRefAttr.FromString,
        )
    self.UpdateSchemaRefAttr = channel.unary_unary(
        '/schema.SchemaService/UpdateSchemaRefAttr',
        request_serializer=metadata_dot_schema_dot_schema__pb2.UpdateSchemaRefAttrRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.SchemaRefAttr.FromString,
        )
    self.AddSchema = channel.unary_unary(
        '/schema.SchemaService/AddSchema',
        request_serializer=metadata_dot_schema_dot_schema__pb2.AddSchemaRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.Schema.FromString,
        )
    self.UpdateSchema = channel.unary_unary(
        '/schema.SchemaService/UpdateSchema',
        request_serializer=metadata_dot_schema_dot_schema__pb2.UpdateSchemaRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.Schema.FromString,
        )
    self.GetSchemaByID = channel.unary_unary(
        '/schema.SchemaService/GetSchemaByID',
        request_serializer=metadata_dot_schema_dot_schema__pb2.GetSchemaByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.Schema.FromString,
        )
    self.ListSchema = channel.unary_unary(
        '/schema.SchemaService/ListSchema',
        request_serializer=metadata_dot_schema_dot_schema__pb2.ListSchemaRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.ListSchemaResponse.FromString,
        )
    self.ListSchemaWithLocalizationMap = channel.unary_unary(
        '/schema.SchemaService/ListSchemaWithLocalizationMap',
        request_serializer=metadata_dot_schema_dot_schema__pb2.ListSchemaWithLocalizationMapRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.ListSchemaWithLocalizationMapResponse.FromString,
        )
    self.AddSchemaAttr = channel.unary_unary(
        '/schema.SchemaService/AddSchemaAttr',
        request_serializer=metadata_dot_schema_dot_schema__pb2.AddSchemaAttrRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.SchemaAttr.FromString,
        )
    self.UpdateSchemaAttr = channel.unary_unary(
        '/schema.SchemaService/UpdateSchemaAttr',
        request_serializer=metadata_dot_schema_dot_schema__pb2.UpdateSchemaAttrRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.SchemaAttr.FromString,
        )
    self.UpdateSchemaAttrByBatch = channel.unary_unary(
        '/schema.SchemaService/UpdateSchemaAttrByBatch',
        request_serializer=metadata_dot_schema_dot_schema__pb2.UpdateSchemaAttrByBatchRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.DefaultResponse.FromString,
        )
    self.GetSchemaAttrByID = channel.unary_unary(
        '/schema.SchemaService/GetSchemaAttrByID',
        request_serializer=metadata_dot_schema_dot_schema__pb2.GetSchemaAttrByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.SchemaAttr.FromString,
        )
    self.ListSchemaAttr = channel.unary_unary(
        '/schema.SchemaService/ListSchemaAttr',
        request_serializer=metadata_dot_schema_dot_schema__pb2.ListSchemaAttrRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.ListSchemaAttrResponse.FromString,
        )
    self.DeleteSchemaAttrByID = channel.unary_unary(
        '/schema.SchemaService/DeleteSchemaAttrByID',
        request_serializer=metadata_dot_schema_dot_schema__pb2.DeleteSchemaAttrByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.DefaultResponse.FromString,
        )
    self.AddSchemaRelation = channel.unary_unary(
        '/schema.SchemaService/AddSchemaRelation',
        request_serializer=metadata_dot_schema_dot_schema__pb2.AddSchemaRelationRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.SchemaRelation.FromString,
        )
    self.UpdateSchemaRelation = channel.unary_unary(
        '/schema.SchemaService/UpdateSchemaRelation',
        request_serializer=metadata_dot_schema_dot_schema__pb2.UpdateSchemaRelationRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.SchemaRelation.FromString,
        )
    self.GetSchemaRelationByID = channel.unary_unary(
        '/schema.SchemaService/GetSchemaRelationByID',
        request_serializer=metadata_dot_schema_dot_schema__pb2.GetSchemaRelationByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.SchemaRelation.FromString,
        )
    self.ListSchemaRelation = channel.unary_unary(
        '/schema.SchemaService/ListSchemaRelation',
        request_serializer=metadata_dot_schema_dot_schema__pb2.ListSchemaRelationRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.ListSchemaRelationResponse.FromString,
        )
    self.ListSchemaRelationWithLocalizationMap = channel.unary_unary(
        '/schema.SchemaService/ListSchemaRelationWithLocalizationMap',
        request_serializer=metadata_dot_schema_dot_schema__pb2.ListSchemaRelationWithLocalizationMapRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.ListSchemaRelationWithLocalizationMapResponse.FromString,
        )
    self.AddSchemaRelationAttr = channel.unary_unary(
        '/schema.SchemaService/AddSchemaRelationAttr',
        request_serializer=metadata_dot_schema_dot_schema__pb2.AddSchemaRelationAttrRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.SchemaRelationAttr.FromString,
        )
    self.UpdateSchemaRelationAttr = channel.unary_unary(
        '/schema.SchemaService/UpdateSchemaRelationAttr',
        request_serializer=metadata_dot_schema_dot_schema__pb2.UpdateSchemaRelationAttrRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.SchemaRelationAttr.FromString,
        )
    self.UpdateSchemaRelationAttrByBatch = channel.unary_unary(
        '/schema.SchemaService/UpdateSchemaRelationAttrByBatch',
        request_serializer=metadata_dot_schema_dot_schema__pb2.UpdateSchemaRelationAttrByBatchRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.DefaultResponse.FromString,
        )
    self.GetSchemaRelationAttrByID = channel.unary_unary(
        '/schema.SchemaService/GetSchemaRelationAttrByID',
        request_serializer=metadata_dot_schema_dot_schema__pb2.GetSchemaRelationAttrByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.SchemaRelationAttr.FromString,
        )
    self.ListSchemaRelationAttr = channel.unary_unary(
        '/schema.SchemaService/ListSchemaRelationAttr',
        request_serializer=metadata_dot_schema_dot_schema__pb2.ListSchemaRelationAttrRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.ListSchemaRelationAttrResponse.FromString,
        )
    self.DeleteSchemaRelationAttrByID = channel.unary_unary(
        '/schema.SchemaService/DeleteSchemaRelationAttrByID',
        request_serializer=metadata_dot_schema_dot_schema__pb2.DeleteSchemaRelationAttrByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.DefaultResponse.FromString,
        )
    self.AddForm = channel.unary_unary(
        '/schema.SchemaService/AddForm',
        request_serializer=metadata_dot_schema_dot_schema__pb2.AddFormRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.Form.FromString,
        )
    self.UpdateForm = channel.unary_unary(
        '/schema.SchemaService/UpdateForm',
        request_serializer=metadata_dot_schema_dot_schema__pb2.UpdateFormRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.Form.FromString,
        )
    self.GetFormByID = channel.unary_unary(
        '/schema.SchemaService/GetFormByID',
        request_serializer=metadata_dot_schema_dot_schema__pb2.GetFormByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.Form.FromString,
        )
    self.ListForm = channel.unary_unary(
        '/schema.SchemaService/ListForm',
        request_serializer=metadata_dot_schema_dot_schema__pb2.ListFormRequest.SerializeToString,
        response_deserializer=metadata_dot_schema_dot_schema__pb2.ListFormResponse.FromString,
        )


class SchemaServiceServicer(object):
  """SchemaService 用于维护主档Schema
  """

  def AddSchemaRef(self, request, context):
    """添加Schema Ref
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateSchemaRef(self, request, context):
    """修改Schema Ref
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetSchemaRefByID(self, request, context):
    """根据id获取Schema Ref
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListSchemaRef(self, request, context):
    """获Schema Ref列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddSchemaRefAttr(self, request, context):
    """添加Schema Ref Attr
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateSchemaRefAttr(self, request, context):
    """修改Schema Ref Attr
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddSchema(self, request, context):
    """添加Schema
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateSchema(self, request, context):
    """修改Schema
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetSchemaByID(self, request, context):
    """根据id获取Schema
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListSchema(self, request, context):
    """获Schema列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListSchemaWithLocalizationMap(self, request, context):
    """获Schema简化的Localizations键值信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddSchemaAttr(self, request, context):
    """添加Schema Attr
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateSchemaAttr(self, request, context):
    """修改Schema Attr
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateSchemaAttrByBatch(self, request, context):
    """批量修改和添加Schema Attr
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetSchemaAttrByID(self, request, context):
    """根据id获取Schema Attr
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListSchemaAttr(self, request, context):
    """获Schema Attr列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteSchemaAttrByID(self, request, context):
    """根据id删除Schema Attr
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddSchemaRelation(self, request, context):
    """添加Schema Relation
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateSchemaRelation(self, request, context):
    """修改Schema Relation
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetSchemaRelationByID(self, request, context):
    """根据id获取Schema Relation
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListSchemaRelation(self, request, context):
    """获Schema Relation列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListSchemaRelationWithLocalizationMap(self, request, context):
    """获Schema Relation简化的Localizations键值信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddSchemaRelationAttr(self, request, context):
    """添加Schema Relation Attr
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateSchemaRelationAttr(self, request, context):
    """修改Schema Relation Attr
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateSchemaRelationAttrByBatch(self, request, context):
    """批量修改和添加Schema Relation Attr
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetSchemaRelationAttrByID(self, request, context):
    """根据id获取Schema Relation Attr
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListSchemaRelationAttr(self, request, context):
    """获Schema Relation Attr列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteSchemaRelationAttrByID(self, request, context):
    """根据id删除Schema Relation Attr
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddForm(self, request, context):
    """添加Form
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateForm(self, request, context):
    """修改Form
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetFormByID(self, request, context):
    """根据id获取Form
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListForm(self, request, context):
    """获Form列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_SchemaServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'AddSchemaRef': grpc.unary_unary_rpc_method_handler(
          servicer.AddSchemaRef,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.AddSchemaRefRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.SchemaRef.SerializeToString,
      ),
      'UpdateSchemaRef': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateSchemaRef,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.UpdateSchemaRefRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.SchemaRef.SerializeToString,
      ),
      'GetSchemaRefByID': grpc.unary_unary_rpc_method_handler(
          servicer.GetSchemaRefByID,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.GetSchemaRefByIdRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.SchemaRef.SerializeToString,
      ),
      'ListSchemaRef': grpc.unary_unary_rpc_method_handler(
          servicer.ListSchemaRef,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.ListSchemaRefRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.ListSchemaRefResponse.SerializeToString,
      ),
      'AddSchemaRefAttr': grpc.unary_unary_rpc_method_handler(
          servicer.AddSchemaRefAttr,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.AddSchemaRefAttrRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.SchemaRefAttr.SerializeToString,
      ),
      'UpdateSchemaRefAttr': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateSchemaRefAttr,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.UpdateSchemaRefAttrRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.SchemaRefAttr.SerializeToString,
      ),
      'AddSchema': grpc.unary_unary_rpc_method_handler(
          servicer.AddSchema,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.AddSchemaRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.Schema.SerializeToString,
      ),
      'UpdateSchema': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateSchema,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.UpdateSchemaRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.Schema.SerializeToString,
      ),
      'GetSchemaByID': grpc.unary_unary_rpc_method_handler(
          servicer.GetSchemaByID,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.GetSchemaByIdRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.Schema.SerializeToString,
      ),
      'ListSchema': grpc.unary_unary_rpc_method_handler(
          servicer.ListSchema,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.ListSchemaRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.ListSchemaResponse.SerializeToString,
      ),
      'ListSchemaWithLocalizationMap': grpc.unary_unary_rpc_method_handler(
          servicer.ListSchemaWithLocalizationMap,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.ListSchemaWithLocalizationMapRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.ListSchemaWithLocalizationMapResponse.SerializeToString,
      ),
      'AddSchemaAttr': grpc.unary_unary_rpc_method_handler(
          servicer.AddSchemaAttr,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.AddSchemaAttrRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.SchemaAttr.SerializeToString,
      ),
      'UpdateSchemaAttr': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateSchemaAttr,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.UpdateSchemaAttrRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.SchemaAttr.SerializeToString,
      ),
      'UpdateSchemaAttrByBatch': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateSchemaAttrByBatch,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.UpdateSchemaAttrByBatchRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.DefaultResponse.SerializeToString,
      ),
      'GetSchemaAttrByID': grpc.unary_unary_rpc_method_handler(
          servicer.GetSchemaAttrByID,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.GetSchemaAttrByIdRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.SchemaAttr.SerializeToString,
      ),
      'ListSchemaAttr': grpc.unary_unary_rpc_method_handler(
          servicer.ListSchemaAttr,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.ListSchemaAttrRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.ListSchemaAttrResponse.SerializeToString,
      ),
      'DeleteSchemaAttrByID': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteSchemaAttrByID,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.DeleteSchemaAttrByIdRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.DefaultResponse.SerializeToString,
      ),
      'AddSchemaRelation': grpc.unary_unary_rpc_method_handler(
          servicer.AddSchemaRelation,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.AddSchemaRelationRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.SchemaRelation.SerializeToString,
      ),
      'UpdateSchemaRelation': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateSchemaRelation,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.UpdateSchemaRelationRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.SchemaRelation.SerializeToString,
      ),
      'GetSchemaRelationByID': grpc.unary_unary_rpc_method_handler(
          servicer.GetSchemaRelationByID,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.GetSchemaRelationByIdRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.SchemaRelation.SerializeToString,
      ),
      'ListSchemaRelation': grpc.unary_unary_rpc_method_handler(
          servicer.ListSchemaRelation,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.ListSchemaRelationRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.ListSchemaRelationResponse.SerializeToString,
      ),
      'ListSchemaRelationWithLocalizationMap': grpc.unary_unary_rpc_method_handler(
          servicer.ListSchemaRelationWithLocalizationMap,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.ListSchemaRelationWithLocalizationMapRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.ListSchemaRelationWithLocalizationMapResponse.SerializeToString,
      ),
      'AddSchemaRelationAttr': grpc.unary_unary_rpc_method_handler(
          servicer.AddSchemaRelationAttr,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.AddSchemaRelationAttrRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.SchemaRelationAttr.SerializeToString,
      ),
      'UpdateSchemaRelationAttr': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateSchemaRelationAttr,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.UpdateSchemaRelationAttrRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.SchemaRelationAttr.SerializeToString,
      ),
      'UpdateSchemaRelationAttrByBatch': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateSchemaRelationAttrByBatch,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.UpdateSchemaRelationAttrByBatchRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.DefaultResponse.SerializeToString,
      ),
      'GetSchemaRelationAttrByID': grpc.unary_unary_rpc_method_handler(
          servicer.GetSchemaRelationAttrByID,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.GetSchemaRelationAttrByIdRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.SchemaRelationAttr.SerializeToString,
      ),
      'ListSchemaRelationAttr': grpc.unary_unary_rpc_method_handler(
          servicer.ListSchemaRelationAttr,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.ListSchemaRelationAttrRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.ListSchemaRelationAttrResponse.SerializeToString,
      ),
      'DeleteSchemaRelationAttrByID': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteSchemaRelationAttrByID,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.DeleteSchemaRelationAttrByIdRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.DefaultResponse.SerializeToString,
      ),
      'AddForm': grpc.unary_unary_rpc_method_handler(
          servicer.AddForm,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.AddFormRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.Form.SerializeToString,
      ),
      'UpdateForm': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateForm,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.UpdateFormRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.Form.SerializeToString,
      ),
      'GetFormByID': grpc.unary_unary_rpc_method_handler(
          servicer.GetFormByID,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.GetFormByIdRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.Form.SerializeToString,
      ),
      'ListForm': grpc.unary_unary_rpc_method_handler(
          servicer.ListForm,
          request_deserializer=metadata_dot_schema_dot_schema__pb2.ListFormRequest.FromString,
          response_serializer=metadata_dot_schema_dot_schema__pb2.ListFormResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'schema.SchemaService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
