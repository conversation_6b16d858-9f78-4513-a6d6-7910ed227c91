# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: metadata/region.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='metadata/region.proto',
  package='region',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x15metadata/region.proto\x12\x06region\x1a\x1cgoogle/api/annotations.proto\x1a\x1cgoogle/protobuf/struct.proto\"\\\n\x14GetRegionByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x15\n\rreturn_fields\x18\x03 \x01(\t\x12\x13\n\x0bregion_type\x18\x04 \x01(\t\"\xfe\x01\n\x11ListRegionRequest\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x15\n\rreturn_fields\x18\x02 \x01(\t\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x0e\n\x06offset\x18\x04 \x01(\x05\x12\x0c\n\x04sort\x18\x05 \x01(\t\x12\r\n\x05order\x18\x06 \x01(\t\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12\x0e\n\x06search\x18\x08 \x01(\t\x12\x15\n\rsearch_fields\x18\t \x01(\t\x12\x0b\n\x03ids\x18\n \x03(\x04\x12(\n\x07\x66ilters\x18\x0b \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x13\n\x0bregion_type\x18\x0c \x01(\t\"A\n\x12ListRegionResponse\x12\x1c\n\x04rows\x18\x01 \x03(\x0b\x32\x0e.region.Region\x12\r\n\x05total\x18\x02 \x01(\x05\"g\n\x06Region\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x0f\n\x07updated\x18\x06 \x01(\t\"!\n\x0f\x44\x65\x66\x61ultResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x32\xf3\x01\n\rRegionService\x12n\n\rGetRegionById\x12\x1c.region.GetRegionByIdRequest\x1a\x0e.region.Region\"/\x82\xd3\xe4\x93\x02)\x12\'/api/v2/region/{region_type}/by/id/{id}\x12r\n\nListRegion\x12\x19.region.ListRegionRequest\x1a\x1a.region.ListRegionResponse\"-\x82\xd3\xe4\x93\x02\'\"\"/api/v2/region/{region_type}/query:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_struct__pb2.DESCRIPTOR,])




_GETREGIONBYIDREQUEST = _descriptor.Descriptor(
  name='GetRegionByIdRequest',
  full_name='region.GetRegionByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='region.GetRegionByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='region.GetRegionByIdRequest.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='region.GetRegionByIdRequest.return_fields', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_type', full_name='region.GetRegionByIdRequest.region_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=93,
  serialized_end=185,
)


_LISTREGIONREQUEST = _descriptor.Descriptor(
  name='ListRegionRequest',
  full_name='region.ListRegionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='region.ListRegionRequest.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='region.ListRegionRequest.return_fields', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='region.ListRegionRequest.limit', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='region.ListRegionRequest.offset', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='region.ListRegionRequest.sort', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='region.ListRegionRequest.order', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='region.ListRegionRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='region.ListRegionRequest.search', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='region.ListRegionRequest.search_fields', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='region.ListRegionRequest.ids', index=9,
      number=10, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='region.ListRegionRequest.filters', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_type', full_name='region.ListRegionRequest.region_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=188,
  serialized_end=442,
)


_LISTREGIONRESPONSE = _descriptor.Descriptor(
  name='ListRegionResponse',
  full_name='region.ListRegionResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='region.ListRegionResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='region.ListRegionResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=444,
  serialized_end=509,
)


_REGION = _descriptor.Descriptor(
  name='Region',
  full_name='region.Region',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='region.Region.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='region.Region.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='region.Region.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='region.Region.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='region.Region.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='region.Region.updated', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=511,
  serialized_end=614,
)


_DEFAULTRESPONSE = _descriptor.Descriptor(
  name='DefaultResponse',
  full_name='region.DefaultResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='region.DefaultResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=616,
  serialized_end=649,
)

_LISTREGIONREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTREGIONRESPONSE.fields_by_name['rows'].message_type = _REGION
DESCRIPTOR.message_types_by_name['GetRegionByIdRequest'] = _GETREGIONBYIDREQUEST
DESCRIPTOR.message_types_by_name['ListRegionRequest'] = _LISTREGIONREQUEST
DESCRIPTOR.message_types_by_name['ListRegionResponse'] = _LISTREGIONRESPONSE
DESCRIPTOR.message_types_by_name['Region'] = _REGION
DESCRIPTOR.message_types_by_name['DefaultResponse'] = _DEFAULTRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetRegionByIdRequest = _reflection.GeneratedProtocolMessageType('GetRegionByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETREGIONBYIDREQUEST,
  __module__ = 'metadata.region_pb2'
  # @@protoc_insertion_point(class_scope:region.GetRegionByIdRequest)
  ))
_sym_db.RegisterMessage(GetRegionByIdRequest)

ListRegionRequest = _reflection.GeneratedProtocolMessageType('ListRegionRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTREGIONREQUEST,
  __module__ = 'metadata.region_pb2'
  # @@protoc_insertion_point(class_scope:region.ListRegionRequest)
  ))
_sym_db.RegisterMessage(ListRegionRequest)

ListRegionResponse = _reflection.GeneratedProtocolMessageType('ListRegionResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTREGIONRESPONSE,
  __module__ = 'metadata.region_pb2'
  # @@protoc_insertion_point(class_scope:region.ListRegionResponse)
  ))
_sym_db.RegisterMessage(ListRegionResponse)

Region = _reflection.GeneratedProtocolMessageType('Region', (_message.Message,), dict(
  DESCRIPTOR = _REGION,
  __module__ = 'metadata.region_pb2'
  # @@protoc_insertion_point(class_scope:region.Region)
  ))
_sym_db.RegisterMessage(Region)

DefaultResponse = _reflection.GeneratedProtocolMessageType('DefaultResponse', (_message.Message,), dict(
  DESCRIPTOR = _DEFAULTRESPONSE,
  __module__ = 'metadata.region_pb2'
  # @@protoc_insertion_point(class_scope:region.DefaultResponse)
  ))
_sym_db.RegisterMessage(DefaultResponse)



_REGIONSERVICE = _descriptor.ServiceDescriptor(
  name='RegionService',
  full_name='region.RegionService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=652,
  serialized_end=895,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetRegionById',
    full_name='region.RegionService.GetRegionById',
    index=0,
    containing_service=None,
    input_type=_GETREGIONBYIDREQUEST,
    output_type=_REGION,
    serialized_options=_b('\202\323\344\223\002)\022\'/api/v2/region/{region_type}/by/id/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListRegion',
    full_name='region.RegionService.ListRegion',
    index=1,
    containing_service=None,
    input_type=_LISTREGIONREQUEST,
    output_type=_LISTREGIONRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\"\"/api/v2/region/{region_type}/query:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_REGIONSERVICE)

DESCRIPTOR.services_by_name['RegionService'] = _REGIONSERVICE

# @@protoc_insertion_point(module_scope)
