syntax = "proto3";
package store;
import "google/api/annotations.proto";
import "google/protobuf/struct.proto";

// StoreService 用于从metadata获取门店信息数据
service StoreService {

    // 根据id获取门店
    rpc GetStoreById (GetStoreByIdRequest) returns (Store) {
        option (google.api.http) = {
        get: "/api/v2/store/by/id/{id}"
        };
    }
    // 查询 Store 列表
    rpc ListStore (ListStoreRequest) returns (ListStoreResponse) {
        option (google.api.http) = {
        post: "/api/v2/store/query"
        body: "*"
        };
    }

    // 获取门店数据权限
    // full_access=true 全部权限
    // full_access=false，则只有相关store ids的权限
    rpc GetStoreDataScope (GetStoreDataScopeRequest) returns (StoreDataScope) {
        option (google.api.http) = {
        get: "/api/v2/store/data/scope"
        };
    }
    rpc GetStoreDataScopeList (GetStoreDataScopeListReq) returns (GetStoreDataScopeListRes) {
        option (google.api.http) = {
        get: "/api/v2/store/list/scope"
        };
    }
    // 根据id获取DistributionCenter
    rpc GetDistributionCenterById (GetDistributionCenterByIdRequest) returns (DistributionCenter) {
        option (google.api.http) = {
        get: "/api/v2/distribution-center/by/id/{id}"
        };
    }
    // 查询 DistributionCenter 列表
    rpc ListDistributionCenter (ListDistributionCenterRequest) returns (ListDistributionCenterResponse) {
        option (google.api.http) = {
        post: "/api/v2/distribution-center/query"
        body: "*"
        };
    }
    // 根据id获取Company
    rpc GetCompanyById (GetCompanyByIdRequest) returns (Company) {
        option (google.api.http) = {
        get: "/api/v2/company/by/id/{id}"
        };
    }
    // 查询 Company 列表
    rpc ListCompany (ListCompanyRequest) returns (ListCompanyResponse) {
        option (google.api.http) = {
        post: "/api/v2/company/query"
        body: "*"
        };
    }
    // 根据id获取vendor
    rpc GetVendorById (GetVendorByIdRequest) returns (Vendor) {
        option (google.api.http) = {
        get: "/api/v2/vendor/by/id/{id}"
        };
    }
    // 查询 vendor 列表
    rpc ListVendor (ListVendorRequest) returns (ListVendorResponse) {
        option (google.api.http) = {
        post: "/api/v2/vendor/query"
        body: "*"
        };
    }
    // 获取仓库权限
    rpc GetWarehouseScope (GetWarehouseScopeRequest) returns (WarehouseScope) {
        option (google.api.http) = {
        get: "/api/v2/warehouse/data/scope"
        };
    }

    // 获取加工中心权限
    rpc GetMachiningCenterScopeDetail (GetMachiningCenterScopeRequest) returns (MachiningCenterDetailScope) {
        option (google.api.http) = {
        get: "/api/v2/machining/data/scope/detail"
        };
    }
}

message GetStoreByIdRequest {
    // 数据id
    uint64 id = 1;
    // 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
    string code = 2;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 3;
    string lan = 4;
}
message ListStoreRequest {
    // 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
    string code = 1;
    // 所需要返回的字段，包含relation里面的字段，如:return_fields="name,code,branch_region,geo_region"
    string return_fields = 2;
    // 分页大小
    int32 limit = 3;
    // 跳过行数
    int32 offset = 4;
    // 排序字段
    string sort = 5;
    // 排序顺序
    string order = 6;
    // 返回总条数
    bool include_total = 7;
    // 要模糊查询的字符串
    string search = 8;
    // 要查询的字段, 多个逗号隔开;
    // 如果传入relation.[relation name].[field], 则会顺带搜索关联的数据,
    // 例: 搜索store数据时, search_fields=relation.branch.name则会搜索管理区域名称,
    // 找到匹配的管理区域, 然后找到关联到这些管理区域以及所有下级区域的门店
    string search_fields = 9;
    // 按id列表查询
    repeated uint64 ids = 10;
    // 按字段过滤
    google.protobuf.Struct filters = 11;
    // 按关系深层次递归过滤(获取包含下级节点的数据)
    google.protobuf.Struct relation_filters = 12;
    string lan = 13;
}


message ListStoreResponse {
    repeated Store rows = 1;
    int32 total = 2;
}
message Store {
    // 数据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    string name = 4;
    string code = 5;
    string second_code = 6;
    string name_en = 7;
    string type = 8;
    string open_date = 9;
    string close_date = 10;
    string address = 11;
    string contact = 12;
    string tel = 13;
    string email = 14;
    string status = 15;
    repeated uint64 geo_region = 16;
    repeated uint64 branch_region = 17;
    repeated uint64 order_region = 18;
    repeated uint64 distribution_region = 19;
    repeated uint64 purchase_region = 20;
    repeated uint64 market_region = 21;
    repeated uint64 transfer_region = 22;
    repeated uint64 attribute_region = 23;
    repeated uint64 bom_region = 24;
    string updated = 25;
    google.protobuf.Struct extends = 26;
    google.protobuf.Struct extend_code = 27;
    repeated uint64 company_info = 28;
    string virtual_cold_warehose = 29;
    string belongs_to_warehose = 30;
    string open_status = 31;
    uint64 franchisee = 32;
    string chain_type = 33;
}

message DefaultResponse {
    bool result = 1;
}

message DistributionCenter {
    // 数据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    string name = 4;
    string code = 5;
    string updated = 6;
}

message GetDistributionCenterByIdRequest {
    // 数据id
    uint64 id = 1;
    // 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
    string code = 2;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 3;
    string lan = 4;
}
message ListDistributionCenterRequest {
    // 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
    string code = 1;
    // 所需要返回的字段，包含relation里面的字段，如:return_fields="name,code,branch_region,geo_region"
    string return_fields = 2;
    // 分页大小
    int32 limit = 3;
    // 跳过行数
    int32 offset = 4;
    // 排序字段
    string sort = 5;
    // 排序顺序
    string order = 6;
    // 返回总条数
    bool include_total = 7;
    // 要模糊查询的字符串
    string search = 8;
    // 要查询的字段, 多个逗号隔开;
    string search_fields = 9;
    // 按id列表查询
    repeated uint64 ids = 10;
    // 按字段过滤
    google.protobuf.Struct filters = 11;
    string lan = 12;
}


message ListDistributionCenterResponse {
    repeated DistributionCenter rows = 1;
    int32 total = 2;
}

message GetStoreDataScopeRequest {
    bool donot_transfer_branch_to_store = 1;
}

message StoreDataScope {
    // 是否有全部权限
    bool full_access = 1;
    // 如果full_access=false，则只有相关store ids的权限
    repeated uint64 scope_store_ids = 2;
    // 如果 GetStoreDataScopeRequest.donot_transfer_branch_to_store=true，则不会根据branch_ids去获取下面所有的门店
    // 但会同时返回 scope_branch_ids
    repeated uint64 scope_branch_ids = 3;
}
message GetStoreDataScopeListReq {
    string lan = 1;
}
message GetStoreDataScopeListRes {
    repeated StoreItem rows = 1;
}
message StoreItem {
    string id = 1;
    string code = 2;
    string name = 3;
}

message GetWarehouseScopeRequest {
}

message WarehouseScope {

    repeated uint64 warehouse_ids = 1;
}

message Company {
    // 数据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    string name = 4;
    string code = 5;
    string tax_no = 6;
    string tel = 7;
    string address = 8;
    string deposit_bank = 9;
    string bank_account = 10;
    string currency = 11;
    string business_licence_url = 12;
    string logo_url = 13;
    string updated = 14;
}

message GetCompanyByIdRequest {
    // 数据id
    uint64 id = 1;
    // 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
    string code = 2;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 3;
    string lan = 4;
}
message ListCompanyRequest {
    // 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
    string code = 1;
    string return_fields = 2;
    // 分页大小
    int32 limit = 3;
    // 跳过行数
    int32 offset = 4;
    // 排序字段
    string sort = 5;
    // 排序顺序
    string order = 6;
    // 返回总条数
    bool include_total = 7;
    // 要模糊查询的字符串
    string search = 8;
    // 要查询的字段, 多个逗号隔开;
    string search_fields = 9;
    // 按id列表查询
    repeated uint64 ids = 10;
    // 按字段过滤
    google.protobuf.Struct filters = 11;
    string lan = 12;
}


message ListCompanyResponse {
    repeated Company rows = 1;
    int32 total = 2;
}

message Vendor {
    // 数据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    string name = 4;
    string code = 5;
    string tax_no = 6;
    string tel = 7;
    string address = 8;
    string deposit_bank = 9;
    string bank_account = 10;
    string currency = 11;
    string contract_url = 12;
    string logo_url = 13;
    string updated = 14;
    string email = 15;
    string contact = 16;
    string abbreviation = 17;
    string settlement_method = 18;
    string contract_status = 19;
    string qualification_url = 20;
    string rank = 21;
}

message GetVendorByIdRequest {
    // 数据id
    uint64 id = 1;
    // 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
    string code = 2;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 3;
    string lan = 4;
}
message ListVendorRequest {
    // 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
    string code = 1;
    string return_fields = 2;
    // 分页大小
    int32 limit = 3;
    // 跳过行数
    int32 offset = 4;
    // 排序字段
    string sort = 5;
    // 排序顺序
    string order = 6;
    // 返回总条数
    bool include_total = 7;
    // 要模糊查询的字符串
    string search = 8;
    // 要查询的字段, 多个逗号隔开;
    string search_fields = 9;
    // 按id列表查询
    repeated uint64 ids = 10;
    // 按字段过滤
    google.protobuf.Struct filters = 11;
    string lan = 12;
}


message ListVendorResponse {
    repeated Vendor rows = 1;
    int32 total = 2;
}

message GetMachiningCenterScopeRequest{

}
message MachiningCenterDetailScope {
    message MachiningCenter {
        uint64 id = 1;
        string code = 2;
        string name = 3;
    }
    repeated MachiningCenter rows = 1;
}
