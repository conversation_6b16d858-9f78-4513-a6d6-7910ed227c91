# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: metadata/contract.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='metadata/contract.proto',
  package='contract',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x17metadata/contract.proto\x12\x08\x63ontract\x1a\x1cgoogle/api/annotations.proto\x1a\x1cgoogle/protobuf/struct.proto\"n\n\x0eListProductReq\x12\r\n\x05limit\x18\x01 \x01(\x05\x12\x0e\n\x06offset\x18\x02 \x01(\x05\x12\x11\n\tvendor_id\x18\x03 \x01(\t\x12\x16\n\x0e\x63\x65ntre_id_list\x18\x04 \x01(\t\x12\x12\n\nvalid_time\x18\x05 \x01(\t\"@\n\x0eListProductRes\x12\r\n\x05\x63ount\x18\x01 \x01(\x05\x12\x1f\n\x04list\x18\x02 \x03(\x0b\x32\x11.contract.Product\"F\n\x07Product\x12\x12\n\nproduct_id\x18\x01 \x01(\t\x12\'\n\x06\x66ields\x18\x02 \x01(\x0b\x32\x17.google.protobuf.Struct\"b\n\x11\x43reateContractReq\x12$\n\x08\x63ontract\x18\x01 \x01(\x0b\x32\x12.contract.Contract\x12\'\n\x08pro_list\x18\x02 \x03(\x0b\x32\x15.contract.ContractPro\"$\n\x11\x43reateContractRes\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\xba\x01\n\x08\x43ontract\x12\n\n\x02id\x18\x01 \x01(\t\x12\x13\n\x0b\x63ontract_no\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x11\n\tvendor_id\x18\x04 \x01(\t\x12\x12\n\nstart_time\x18\x05 \x01(\t\x12\x10\n\x08\x65nd_time\x18\x06 \x01(\t\x12\x0e\n\x06status\x18\x07 \x01(\t\x12\x0f\n\x07updated\x18\x08 \x01(\t\x12\x12\n\nupdated_by\x18\t \x01(\t\x12\x11\n\tpro_count\x18\n \x01(\x05\"\xcc\x01\n\x0b\x43ontractPro\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0e\n\x06pro_id\x18\x02 \x01(\t\x12\x10\n\x08pro_name\x18\x03 \x01(\t\x12\x10\n\x08pro_code\x18\x04 \x01(\t\x12\x0f\n\x07unit_id\x18\x05 \x01(\t\x12\x11\n\tunit_name\x18\x06 \x01(\t\x12\x11\n\tunit_code\x18\x07 \x01(\t\x12\x12\n\nmodel_name\x18\x08 \x01(\t\x12\x0c\n\x04rate\x18\t \x01(\x01\x12$\n\x07\x64\x65tails\x18\n \x03(\x0b\x32\x13.contract.ProDetail\"4\n\nDetailItem\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\"\xd8\x02\n\tProDetail\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0e\n\x06no_tax\x18\x06 \x01(\x01\x12\x0b\n\x03tax\x18\x07 \x01(\x01\x12\x16\n\x0eregion_id_list\x18\x08 \x03(\t\x12)\n\x0bregion_list\x18\x0b \x03(\x0b\x32\x14.contract.DetailItem\x12\x15\n\rstore_id_list\x18\t \x03(\t\x12(\n\nstore_list\x18\x0c \x03(\x0b\x32\x14.contract.DetailItem\x12\x19\n\x11warehouse_id_list\x18\n \x03(\t\x12,\n\x0ewarehouse_list\x18\r \x03(\x0b\x32\x14.contract.DetailItem\x12 \n\x18machining_center_id_list\x18\x0e \x03(\t\x12\x33\n\x15machining_center_list\x18\x0f \x03(\x0b\x32\x14.contract.DetailItem\"}\n\x12GetContractListReq\x12\r\n\x05limit\x18\x01 \x01(\x05\x12\x0e\n\x06offset\x18\x02 \x01(\x05\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x15\n\rsearch_fields\x18\x04 \x01(\t\x12\x0e\n\x06search\x18\x05 \x01(\t\x12\x11\n\tpro_names\x18\x06 \x03(\t\"E\n\x12GetContractListRes\x12 \n\x04list\x18\x01 \x03(\x0b\x32\x12.contract.Contract\x12\r\n\x05\x63ount\x18\x02 \x01(\x05\"5\n\x14GetContractDetailReq\x12\n\n\x02id\x18\x01 \x01(\t\x12\x11\n\tpro_names\x18\x02 \x03(\t\"e\n\x14GetContractDetailRes\x12$\n\x08\x63ontract\x18\x01 \x01(\x0b\x32\x12.contract.Contract\x12\'\n\x08pro_list\x18\x02 \x03(\x0b\x32\x15.contract.ContractPro\"x\n\x11UpdateContractReq\x12$\n\x08\x63ontract\x18\x02 \x01(\x0b\x32\x12.contract.Contract\x12\'\n\x08pro_list\x18\x03 \x03(\x0b\x32\x15.contract.ContractPro\x12\x14\n\x0c\x64\x65leted_list\x18\x04 \x03(\t\"$\n\x11UpdateContractRes\x12\x0f\n\x07success\x18\x01 \x01(\x08\"J\n\rGetTaxRateReq\x12\x13\n\x0bpro_id_list\x18\x01 \x03(\t\x12\x12\n\nstart_time\x18\x02 \x01(\t\x12\x10\n\x08\x65nd_time\x18\x03 \x01(\t\"6\n\rGetTaxRateRes\x12%\n\x04list\x18\x01 \x03(\x0b\x32\x17.google.protobuf.Struct\"\x90\x01\n\rGetTaxListReq\x12\x11\n\tvendor_id\x18\x01 \x01(\t\x12\x13\n\x0bpro_id_list\x18\x02 \x03(\t\x12\x14\n\x0cwarehouse_id\x18\x03 \x01(\t\x12\x12\n\nvalid_time\x18\x04 \x01(\t\x12\x10\n\x08store_id\x18\x05 \x01(\t\x12\x1b\n\x13machining_center_id\x18\x06 \x01(\t\"D\n\x03Tax\x12\x12\n\nproduct_id\x18\x01 \x01(\t\x12\x0c\n\x04rate\x18\x02 \x01(\x01\x12\x0b\n\x03tax\x18\x03 \x01(\x01\x12\x0e\n\x06no_tax\x18\x04 \x01(\x01\",\n\rGetTaxListRes\x12\x1b\n\x04list\x18\x01 \x03(\x0b\x32\r.contract.Tax\"{\n\rGetProListReq\x12\x11\n\tvendor_id\x18\x01 \x01(\t\x12\x14\n\x0cwarehouse_id\x18\x03 \x01(\t\x12\x12\n\nvalid_time\x18\x04 \x01(\t\x12\x10\n\x08store_id\x18\x05 \x01(\t\x12\x1b\n\x13machining_center_id\x18\x06 \x01(\t\"0\n\rGetProListRes\x12\x1f\n\x08pro_list\x18\x01 \x03(\x0b\x32\r.contract.Pro\"\xc2\x01\n\x03Pro\x12\x0e\n\x06pro_id\x18\x01 \x01(\t\x12\x10\n\x08pro_name\x18\x02 \x01(\t\x12\x10\n\x08pro_code\x18\x03 \x01(\t\x12\x0f\n\x07unit_id\x18\x04 \x01(\t\x12\x11\n\tunit_name\x18\x05 \x01(\t\x12\x11\n\tunit_code\x18\x06 \x01(\t\x12\x12\n\nmodel_name\x18\x07 \x01(\t\x12\x0c\n\x04rate\x18\x08 \x01(\x01\x12\x0b\n\x03tax\x18\t \x01(\x01\x12\x0e\n\x06no_tax\x18\n \x01(\x01\x12\x11\n\tunit_rate\x18\x0b \x01(\x01\"\x88\x01\n\x16GetContractMetaListReq\x12\x13\n\x0bpro_id_list\x18\x01 \x03(\t\x12\x16\n\x0evendor_id_list\x18\x02 \x03(\t\x12\x18\n\x10\x63\x61tegory_id_list\x18\x03 \x03(\t\x12\x14\n\x0cwarehouse_id\x18\x05 \x01(\t\x12\x11\n\tregion_id\x18\x06 \x01(\t\"&\n\x08MetaItem\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\"\xeb\x04\n\x04Item\x12\x10\n\x08pro_code\x18\x01 \x01(\t\x12\x10\n\x08pro_name\x18\x02 \x01(\t\x12\x15\n\rcategory_code\x18\x03 \x01(\t\x12\x15\n\rcategory_name\x18\x04 \x01(\t\x12\x13\n\x0bvendor_code\x18\x05 \x01(\t\x12\x13\n\x0bvendor_name\x18\x06 \x01(\t\x12\x19\n\x11\x64\x65\x66\x61ult_unit_code\x18\x07 \x01(\t\x12\x19\n\x11\x64\x65\x66\x61ult_unit_name\x18\x08 \x01(\t\x12\x0c\n\x04rate\x18\x1c \x01(\x01\x12\x12\n\nmodel_name\x18\t \x01(\t\x12*\n\x0ewarehouse_list\x18\n \x03(\x0b\x32\x12.contract.MetaItem\x12\'\n\x0bregion_list\x18\x0b \x03(\x0b\x32\x12.contract.MetaItem\x12\x15\n\rpurchase_code\x18\x0c \x01(\t\x12\x15\n\rpurchase_name\x18\r \x01(\t\x12\x15\n\rpurchase_rate\x18\x0e \x01(\x01\x12\x14\n\x0cpurchase_tax\x18\x0f \x01(\x01\x12\x12\n\norder_code\x18\x10 \x01(\t\x12\x12\n\norder_name\x18\x11 \x01(\t\x12\x12\n\norder_rate\x18\x19 \x01(\x01\x12\x11\n\torder_tax\x18\x1a \x01(\x01\x12\x10\n\x08\x62om_code\x18\x12 \x01(\t\x12\x10\n\x08\x62om_name\x18\x13 \x01(\t\x12\x0f\n\x07\x62om_tax\x18\x14 \x01(\x01\x12\x10\n\x08\x62om_rate\x18\x1b \x01(\x01\x12\x11\n\ttake_code\x18\x15 \x01(\t\x12\x11\n\ttake_name\x18\x16 \x01(\t\x12\x11\n\ttake_rate\x18\x17 \x01(\x01\x12\x10\n\x08take_tax\x18\x18 \x01(\x01\"6\n\x16GetContractMetaListRes\x12\x1c\n\x04list\x18\x01 \x03(\x0b\x32\x0e.contract.Item\"\x10\n\x0eGetSerialNoReq\"#\n\x0eGetSerialNoRes\x12\x11\n\tserial_no\x18\x01 \x01(\t2\xdf\x07\n\x0f\x43ontractService\x12n\n\x0e\x43reateContract\x12\x1b.contract.CreateContractReq\x1a\x1b.contract.CreateContractRes\"\"\x82\xd3\xe4\x93\x02\x1c\"\x17/api/v2/contract/create:\x01*\x12o\n\x0fGetContractList\x12\x1c.contract.GetContractListReq\x1a\x1c.contract.GetContractListRes\" \x82\xd3\xe4\x93\x02\x1a\"\x15/api/v2/contract/list:\x01*\x12w\n\x11GetContractDetail\x12\x1e.contract.GetContractDetailReq\x1a\x1e.contract.GetContractDetailRes\"\"\x82\xd3\xe4\x93\x02\x1c\"\x17/api/v2/contract/detail:\x01*\x12n\n\x0eUpdateContract\x12\x1b.contract.UpdateContractReq\x1a\x1b.contract.UpdateContractRes\"\"\x82\xd3\xe4\x93\x02\x1c\"\x17/api/v2/contract/update:\x01*\x12`\n\x0eGetTaxRateList\x12\x17.contract.GetTaxRateReq\x1a\x17.contract.GetTaxRateRes\"\x1c\x82\xd3\xe4\x93\x02\x16\"\x11/api/v2/rate/list:\x01*\x12[\n\nGetTaxList\x12\x17.contract.GetTaxListReq\x1a\x17.contract.GetTaxListRes\"\x1b\x82\xd3\xe4\x93\x02\x15\"\x10/api/v2/tax/list:\x01*\x12_\n\nGetProList\x12\x17.contract.GetProListReq\x1a\x17.contract.GetProListRes\"\x1f\x82\xd3\xe4\x93\x02\x19\"\x14/api/v2/product/list:\x01*\x12\x80\x01\n\x13GetContractMetaList\x12 .contract.GetContractMetaListReq\x1a .contract.GetContractMetaListRes\"%\x82\xd3\xe4\x93\x02\x1f\"\x1a/api/v2/contract/meta/list:\x01*\x12_\n\x0bGetSerialNo\x12\x18.contract.GetSerialNoReq\x1a\x18.contract.GetSerialNoRes\"\x1c\x82\xd3\xe4\x93\x02\x16\"\x11/api/v2/serial/no:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_struct__pb2.DESCRIPTOR,])




_LISTPRODUCTREQ = _descriptor.Descriptor(
  name='ListProductReq',
  full_name='contract.ListProductReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='limit', full_name='contract.ListProductReq.limit', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='contract.ListProductReq.offset', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_id', full_name='contract.ListProductReq.vendor_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='centre_id_list', full_name='contract.ListProductReq.centre_id_list', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='valid_time', full_name='contract.ListProductReq.valid_time', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=97,
  serialized_end=207,
)


_LISTPRODUCTRES = _descriptor.Descriptor(
  name='ListProductRes',
  full_name='contract.ListProductRes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='count', full_name='contract.ListProductRes.count', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='list', full_name='contract.ListProductRes.list', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=209,
  serialized_end=273,
)


_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='contract.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='contract.Product.product_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='contract.Product.fields', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=275,
  serialized_end=345,
)


_CREATECONTRACTREQ = _descriptor.Descriptor(
  name='CreateContractReq',
  full_name='contract.CreateContractReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='contract', full_name='contract.CreateContractReq.contract', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pro_list', full_name='contract.CreateContractReq.pro_list', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=347,
  serialized_end=445,
)


_CREATECONTRACTRES = _descriptor.Descriptor(
  name='CreateContractRes',
  full_name='contract.CreateContractRes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='contract.CreateContractRes.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=447,
  serialized_end=483,
)


_CONTRACT = _descriptor.Descriptor(
  name='Contract',
  full_name='contract.Contract',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='contract.Contract.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contract_no', full_name='contract.Contract.contract_no', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='contract.Contract.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_id', full_name='contract.Contract.vendor_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_time', full_name='contract.Contract.start_time', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='contract.Contract.end_time', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='contract.Contract.status', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='contract.Contract.updated', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='contract.Contract.updated_by', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pro_count', full_name='contract.Contract.pro_count', index=9,
      number=10, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=486,
  serialized_end=672,
)


_CONTRACTPRO = _descriptor.Descriptor(
  name='ContractPro',
  full_name='contract.ContractPro',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='contract.ContractPro.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pro_id', full_name='contract.ContractPro.pro_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pro_name', full_name='contract.ContractPro.pro_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pro_code', full_name='contract.ContractPro.pro_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='contract.ContractPro.unit_id', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='contract.ContractPro.unit_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_code', full_name='contract.ContractPro.unit_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_name', full_name='contract.ContractPro.model_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='contract.ContractPro.rate', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='details', full_name='contract.ContractPro.details', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=675,
  serialized_end=879,
)


_DETAILITEM = _descriptor.Descriptor(
  name='DetailItem',
  full_name='contract.DetailItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='contract.DetailItem.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='contract.DetailItem.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='contract.DetailItem.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=881,
  serialized_end=933,
)


_PRODETAIL = _descriptor.Descriptor(
  name='ProDetail',
  full_name='contract.ProDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='contract.ProDetail.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='no_tax', full_name='contract.ProDetail.no_tax', index=1,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax', full_name='contract.ProDetail.tax', index=2,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_id_list', full_name='contract.ProDetail.region_id_list', index=3,
      number=8, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_list', full_name='contract.ProDetail.region_list', index=4,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id_list', full_name='contract.ProDetail.store_id_list', index=5,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_list', full_name='contract.ProDetail.store_list', index=6,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='warehouse_id_list', full_name='contract.ProDetail.warehouse_id_list', index=7,
      number=10, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='warehouse_list', full_name='contract.ProDetail.warehouse_list', index=8,
      number=13, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_id_list', full_name='contract.ProDetail.machining_center_id_list', index=9,
      number=14, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_list', full_name='contract.ProDetail.machining_center_list', index=10,
      number=15, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=936,
  serialized_end=1280,
)


_GETCONTRACTLISTREQ = _descriptor.Descriptor(
  name='GetContractListReq',
  full_name='contract.GetContractListReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='limit', full_name='contract.GetContractListReq.limit', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='contract.GetContractListReq.offset', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='contract.GetContractListReq.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='contract.GetContractListReq.search_fields', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='contract.GetContractListReq.search', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pro_names', full_name='contract.GetContractListReq.pro_names', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1282,
  serialized_end=1407,
)


_GETCONTRACTLISTRES = _descriptor.Descriptor(
  name='GetContractListRes',
  full_name='contract.GetContractListRes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='list', full_name='contract.GetContractListRes.list', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='count', full_name='contract.GetContractListRes.count', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1409,
  serialized_end=1478,
)


_GETCONTRACTDETAILREQ = _descriptor.Descriptor(
  name='GetContractDetailReq',
  full_name='contract.GetContractDetailReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='contract.GetContractDetailReq.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pro_names', full_name='contract.GetContractDetailReq.pro_names', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1480,
  serialized_end=1533,
)


_GETCONTRACTDETAILRES = _descriptor.Descriptor(
  name='GetContractDetailRes',
  full_name='contract.GetContractDetailRes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='contract', full_name='contract.GetContractDetailRes.contract', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pro_list', full_name='contract.GetContractDetailRes.pro_list', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1535,
  serialized_end=1636,
)


_UPDATECONTRACTREQ = _descriptor.Descriptor(
  name='UpdateContractReq',
  full_name='contract.UpdateContractReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='contract', full_name='contract.UpdateContractReq.contract', index=0,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pro_list', full_name='contract.UpdateContractReq.pro_list', index=1,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deleted_list', full_name='contract.UpdateContractReq.deleted_list', index=2,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1638,
  serialized_end=1758,
)


_UPDATECONTRACTRES = _descriptor.Descriptor(
  name='UpdateContractRes',
  full_name='contract.UpdateContractRes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='contract.UpdateContractRes.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1760,
  serialized_end=1796,
)


_GETTAXRATEREQ = _descriptor.Descriptor(
  name='GetTaxRateReq',
  full_name='contract.GetTaxRateReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='pro_id_list', full_name='contract.GetTaxRateReq.pro_id_list', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_time', full_name='contract.GetTaxRateReq.start_time', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='contract.GetTaxRateReq.end_time', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1798,
  serialized_end=1872,
)


_GETTAXRATERES = _descriptor.Descriptor(
  name='GetTaxRateRes',
  full_name='contract.GetTaxRateRes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='list', full_name='contract.GetTaxRateRes.list', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1874,
  serialized_end=1928,
)


_GETTAXLISTREQ = _descriptor.Descriptor(
  name='GetTaxListReq',
  full_name='contract.GetTaxListReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='vendor_id', full_name='contract.GetTaxListReq.vendor_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pro_id_list', full_name='contract.GetTaxListReq.pro_id_list', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='warehouse_id', full_name='contract.GetTaxListReq.warehouse_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='valid_time', full_name='contract.GetTaxListReq.valid_time', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='contract.GetTaxListReq.store_id', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_id', full_name='contract.GetTaxListReq.machining_center_id', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1931,
  serialized_end=2075,
)


_TAX = _descriptor.Descriptor(
  name='Tax',
  full_name='contract.Tax',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='contract.Tax.product_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='contract.Tax.rate', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax', full_name='contract.Tax.tax', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='no_tax', full_name='contract.Tax.no_tax', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2077,
  serialized_end=2145,
)


_GETTAXLISTRES = _descriptor.Descriptor(
  name='GetTaxListRes',
  full_name='contract.GetTaxListRes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='list', full_name='contract.GetTaxListRes.list', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2147,
  serialized_end=2191,
)


_GETPROLISTREQ = _descriptor.Descriptor(
  name='GetProListReq',
  full_name='contract.GetProListReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='vendor_id', full_name='contract.GetProListReq.vendor_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='warehouse_id', full_name='contract.GetProListReq.warehouse_id', index=1,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='valid_time', full_name='contract.GetProListReq.valid_time', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='contract.GetProListReq.store_id', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='machining_center_id', full_name='contract.GetProListReq.machining_center_id', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2193,
  serialized_end=2316,
)


_GETPROLISTRES = _descriptor.Descriptor(
  name='GetProListRes',
  full_name='contract.GetProListRes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='pro_list', full_name='contract.GetProListRes.pro_list', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2318,
  serialized_end=2366,
)


_PRO = _descriptor.Descriptor(
  name='Pro',
  full_name='contract.Pro',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='pro_id', full_name='contract.Pro.pro_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pro_name', full_name='contract.Pro.pro_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pro_code', full_name='contract.Pro.pro_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='contract.Pro.unit_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='contract.Pro.unit_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_code', full_name='contract.Pro.unit_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_name', full_name='contract.Pro.model_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='contract.Pro.rate', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax', full_name='contract.Pro.tax', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='no_tax', full_name='contract.Pro.no_tax', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='contract.Pro.unit_rate', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2369,
  serialized_end=2563,
)


_GETCONTRACTMETALISTREQ = _descriptor.Descriptor(
  name='GetContractMetaListReq',
  full_name='contract.GetContractMetaListReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='pro_id_list', full_name='contract.GetContractMetaListReq.pro_id_list', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_id_list', full_name='contract.GetContractMetaListReq.vendor_id_list', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id_list', full_name='contract.GetContractMetaListReq.category_id_list', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='warehouse_id', full_name='contract.GetContractMetaListReq.warehouse_id', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_id', full_name='contract.GetContractMetaListReq.region_id', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2566,
  serialized_end=2702,
)


_METAITEM = _descriptor.Descriptor(
  name='MetaItem',
  full_name='contract.MetaItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='contract.MetaItem.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='contract.MetaItem.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2704,
  serialized_end=2742,
)


_ITEM = _descriptor.Descriptor(
  name='Item',
  full_name='contract.Item',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='pro_code', full_name='contract.Item.pro_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pro_name', full_name='contract.Item.pro_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='contract.Item.category_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='contract.Item.category_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_code', full_name='contract.Item.vendor_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_name', full_name='contract.Item.vendor_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default_unit_code', full_name='contract.Item.default_unit_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default_unit_name', full_name='contract.Item.default_unit_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='contract.Item.rate', index=8,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_name', full_name='contract.Item.model_name', index=9,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='warehouse_list', full_name='contract.Item.warehouse_list', index=10,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_list', full_name='contract.Item.region_list', index=11,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_code', full_name='contract.Item.purchase_code', index=12,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_name', full_name='contract.Item.purchase_name', index=13,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_rate', full_name='contract.Item.purchase_rate', index=14,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_tax', full_name='contract.Item.purchase_tax', index=15,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='contract.Item.order_code', index=16,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_name', full_name='contract.Item.order_name', index=17,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_rate', full_name='contract.Item.order_rate', index=18,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_tax', full_name='contract.Item.order_tax', index=19,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_code', full_name='contract.Item.bom_code', index=20,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_name', full_name='contract.Item.bom_name', index=21,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_tax', full_name='contract.Item.bom_tax', index=22,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_rate', full_name='contract.Item.bom_rate', index=23,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='take_code', full_name='contract.Item.take_code', index=24,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='take_name', full_name='contract.Item.take_name', index=25,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='take_rate', full_name='contract.Item.take_rate', index=26,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='take_tax', full_name='contract.Item.take_tax', index=27,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2745,
  serialized_end=3364,
)


_GETCONTRACTMETALISTRES = _descriptor.Descriptor(
  name='GetContractMetaListRes',
  full_name='contract.GetContractMetaListRes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='list', full_name='contract.GetContractMetaListRes.list', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3366,
  serialized_end=3420,
)


_GETSERIALNOREQ = _descriptor.Descriptor(
  name='GetSerialNoReq',
  full_name='contract.GetSerialNoReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3422,
  serialized_end=3438,
)


_GETSERIALNORES = _descriptor.Descriptor(
  name='GetSerialNoRes',
  full_name='contract.GetSerialNoRes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='serial_no', full_name='contract.GetSerialNoRes.serial_no', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3440,
  serialized_end=3475,
)

_LISTPRODUCTRES.fields_by_name['list'].message_type = _PRODUCT
_PRODUCT.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_CREATECONTRACTREQ.fields_by_name['contract'].message_type = _CONTRACT
_CREATECONTRACTREQ.fields_by_name['pro_list'].message_type = _CONTRACTPRO
_CONTRACTPRO.fields_by_name['details'].message_type = _PRODETAIL
_PRODETAIL.fields_by_name['region_list'].message_type = _DETAILITEM
_PRODETAIL.fields_by_name['store_list'].message_type = _DETAILITEM
_PRODETAIL.fields_by_name['warehouse_list'].message_type = _DETAILITEM
_PRODETAIL.fields_by_name['machining_center_list'].message_type = _DETAILITEM
_GETCONTRACTLISTRES.fields_by_name['list'].message_type = _CONTRACT
_GETCONTRACTDETAILRES.fields_by_name['contract'].message_type = _CONTRACT
_GETCONTRACTDETAILRES.fields_by_name['pro_list'].message_type = _CONTRACTPRO
_UPDATECONTRACTREQ.fields_by_name['contract'].message_type = _CONTRACT
_UPDATECONTRACTREQ.fields_by_name['pro_list'].message_type = _CONTRACTPRO
_GETTAXRATERES.fields_by_name['list'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_GETTAXLISTRES.fields_by_name['list'].message_type = _TAX
_GETPROLISTRES.fields_by_name['pro_list'].message_type = _PRO
_ITEM.fields_by_name['warehouse_list'].message_type = _METAITEM
_ITEM.fields_by_name['region_list'].message_type = _METAITEM
_GETCONTRACTMETALISTRES.fields_by_name['list'].message_type = _ITEM
DESCRIPTOR.message_types_by_name['ListProductReq'] = _LISTPRODUCTREQ
DESCRIPTOR.message_types_by_name['ListProductRes'] = _LISTPRODUCTRES
DESCRIPTOR.message_types_by_name['Product'] = _PRODUCT
DESCRIPTOR.message_types_by_name['CreateContractReq'] = _CREATECONTRACTREQ
DESCRIPTOR.message_types_by_name['CreateContractRes'] = _CREATECONTRACTRES
DESCRIPTOR.message_types_by_name['Contract'] = _CONTRACT
DESCRIPTOR.message_types_by_name['ContractPro'] = _CONTRACTPRO
DESCRIPTOR.message_types_by_name['DetailItem'] = _DETAILITEM
DESCRIPTOR.message_types_by_name['ProDetail'] = _PRODETAIL
DESCRIPTOR.message_types_by_name['GetContractListReq'] = _GETCONTRACTLISTREQ
DESCRIPTOR.message_types_by_name['GetContractListRes'] = _GETCONTRACTLISTRES
DESCRIPTOR.message_types_by_name['GetContractDetailReq'] = _GETCONTRACTDETAILREQ
DESCRIPTOR.message_types_by_name['GetContractDetailRes'] = _GETCONTRACTDETAILRES
DESCRIPTOR.message_types_by_name['UpdateContractReq'] = _UPDATECONTRACTREQ
DESCRIPTOR.message_types_by_name['UpdateContractRes'] = _UPDATECONTRACTRES
DESCRIPTOR.message_types_by_name['GetTaxRateReq'] = _GETTAXRATEREQ
DESCRIPTOR.message_types_by_name['GetTaxRateRes'] = _GETTAXRATERES
DESCRIPTOR.message_types_by_name['GetTaxListReq'] = _GETTAXLISTREQ
DESCRIPTOR.message_types_by_name['Tax'] = _TAX
DESCRIPTOR.message_types_by_name['GetTaxListRes'] = _GETTAXLISTRES
DESCRIPTOR.message_types_by_name['GetProListReq'] = _GETPROLISTREQ
DESCRIPTOR.message_types_by_name['GetProListRes'] = _GETPROLISTRES
DESCRIPTOR.message_types_by_name['Pro'] = _PRO
DESCRIPTOR.message_types_by_name['GetContractMetaListReq'] = _GETCONTRACTMETALISTREQ
DESCRIPTOR.message_types_by_name['MetaItem'] = _METAITEM
DESCRIPTOR.message_types_by_name['Item'] = _ITEM
DESCRIPTOR.message_types_by_name['GetContractMetaListRes'] = _GETCONTRACTMETALISTRES
DESCRIPTOR.message_types_by_name['GetSerialNoReq'] = _GETSERIALNOREQ
DESCRIPTOR.message_types_by_name['GetSerialNoRes'] = _GETSERIALNORES
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ListProductReq = _reflection.GeneratedProtocolMessageType('ListProductReq', (_message.Message,), dict(
  DESCRIPTOR = _LISTPRODUCTREQ,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.ListProductReq)
  ))
_sym_db.RegisterMessage(ListProductReq)

ListProductRes = _reflection.GeneratedProtocolMessageType('ListProductRes', (_message.Message,), dict(
  DESCRIPTOR = _LISTPRODUCTRES,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.ListProductRes)
  ))
_sym_db.RegisterMessage(ListProductRes)

Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCT,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.Product)
  ))
_sym_db.RegisterMessage(Product)

CreateContractReq = _reflection.GeneratedProtocolMessageType('CreateContractReq', (_message.Message,), dict(
  DESCRIPTOR = _CREATECONTRACTREQ,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.CreateContractReq)
  ))
_sym_db.RegisterMessage(CreateContractReq)

CreateContractRes = _reflection.GeneratedProtocolMessageType('CreateContractRes', (_message.Message,), dict(
  DESCRIPTOR = _CREATECONTRACTRES,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.CreateContractRes)
  ))
_sym_db.RegisterMessage(CreateContractRes)

Contract = _reflection.GeneratedProtocolMessageType('Contract', (_message.Message,), dict(
  DESCRIPTOR = _CONTRACT,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.Contract)
  ))
_sym_db.RegisterMessage(Contract)

ContractPro = _reflection.GeneratedProtocolMessageType('ContractPro', (_message.Message,), dict(
  DESCRIPTOR = _CONTRACTPRO,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.ContractPro)
  ))
_sym_db.RegisterMessage(ContractPro)

DetailItem = _reflection.GeneratedProtocolMessageType('DetailItem', (_message.Message,), dict(
  DESCRIPTOR = _DETAILITEM,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.DetailItem)
  ))
_sym_db.RegisterMessage(DetailItem)

ProDetail = _reflection.GeneratedProtocolMessageType('ProDetail', (_message.Message,), dict(
  DESCRIPTOR = _PRODETAIL,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.ProDetail)
  ))
_sym_db.RegisterMessage(ProDetail)

GetContractListReq = _reflection.GeneratedProtocolMessageType('GetContractListReq', (_message.Message,), dict(
  DESCRIPTOR = _GETCONTRACTLISTREQ,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.GetContractListReq)
  ))
_sym_db.RegisterMessage(GetContractListReq)

GetContractListRes = _reflection.GeneratedProtocolMessageType('GetContractListRes', (_message.Message,), dict(
  DESCRIPTOR = _GETCONTRACTLISTRES,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.GetContractListRes)
  ))
_sym_db.RegisterMessage(GetContractListRes)

GetContractDetailReq = _reflection.GeneratedProtocolMessageType('GetContractDetailReq', (_message.Message,), dict(
  DESCRIPTOR = _GETCONTRACTDETAILREQ,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.GetContractDetailReq)
  ))
_sym_db.RegisterMessage(GetContractDetailReq)

GetContractDetailRes = _reflection.GeneratedProtocolMessageType('GetContractDetailRes', (_message.Message,), dict(
  DESCRIPTOR = _GETCONTRACTDETAILRES,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.GetContractDetailRes)
  ))
_sym_db.RegisterMessage(GetContractDetailRes)

UpdateContractReq = _reflection.GeneratedProtocolMessageType('UpdateContractReq', (_message.Message,), dict(
  DESCRIPTOR = _UPDATECONTRACTREQ,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.UpdateContractReq)
  ))
_sym_db.RegisterMessage(UpdateContractReq)

UpdateContractRes = _reflection.GeneratedProtocolMessageType('UpdateContractRes', (_message.Message,), dict(
  DESCRIPTOR = _UPDATECONTRACTRES,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.UpdateContractRes)
  ))
_sym_db.RegisterMessage(UpdateContractRes)

GetTaxRateReq = _reflection.GeneratedProtocolMessageType('GetTaxRateReq', (_message.Message,), dict(
  DESCRIPTOR = _GETTAXRATEREQ,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.GetTaxRateReq)
  ))
_sym_db.RegisterMessage(GetTaxRateReq)

GetTaxRateRes = _reflection.GeneratedProtocolMessageType('GetTaxRateRes', (_message.Message,), dict(
  DESCRIPTOR = _GETTAXRATERES,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.GetTaxRateRes)
  ))
_sym_db.RegisterMessage(GetTaxRateRes)

GetTaxListReq = _reflection.GeneratedProtocolMessageType('GetTaxListReq', (_message.Message,), dict(
  DESCRIPTOR = _GETTAXLISTREQ,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.GetTaxListReq)
  ))
_sym_db.RegisterMessage(GetTaxListReq)

Tax = _reflection.GeneratedProtocolMessageType('Tax', (_message.Message,), dict(
  DESCRIPTOR = _TAX,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.Tax)
  ))
_sym_db.RegisterMessage(Tax)

GetTaxListRes = _reflection.GeneratedProtocolMessageType('GetTaxListRes', (_message.Message,), dict(
  DESCRIPTOR = _GETTAXLISTRES,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.GetTaxListRes)
  ))
_sym_db.RegisterMessage(GetTaxListRes)

GetProListReq = _reflection.GeneratedProtocolMessageType('GetProListReq', (_message.Message,), dict(
  DESCRIPTOR = _GETPROLISTREQ,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.GetProListReq)
  ))
_sym_db.RegisterMessage(GetProListReq)

GetProListRes = _reflection.GeneratedProtocolMessageType('GetProListRes', (_message.Message,), dict(
  DESCRIPTOR = _GETPROLISTRES,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.GetProListRes)
  ))
_sym_db.RegisterMessage(GetProListRes)

Pro = _reflection.GeneratedProtocolMessageType('Pro', (_message.Message,), dict(
  DESCRIPTOR = _PRO,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.Pro)
  ))
_sym_db.RegisterMessage(Pro)

GetContractMetaListReq = _reflection.GeneratedProtocolMessageType('GetContractMetaListReq', (_message.Message,), dict(
  DESCRIPTOR = _GETCONTRACTMETALISTREQ,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.GetContractMetaListReq)
  ))
_sym_db.RegisterMessage(GetContractMetaListReq)

MetaItem = _reflection.GeneratedProtocolMessageType('MetaItem', (_message.Message,), dict(
  DESCRIPTOR = _METAITEM,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.MetaItem)
  ))
_sym_db.RegisterMessage(MetaItem)

Item = _reflection.GeneratedProtocolMessageType('Item', (_message.Message,), dict(
  DESCRIPTOR = _ITEM,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.Item)
  ))
_sym_db.RegisterMessage(Item)

GetContractMetaListRes = _reflection.GeneratedProtocolMessageType('GetContractMetaListRes', (_message.Message,), dict(
  DESCRIPTOR = _GETCONTRACTMETALISTRES,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.GetContractMetaListRes)
  ))
_sym_db.RegisterMessage(GetContractMetaListRes)

GetSerialNoReq = _reflection.GeneratedProtocolMessageType('GetSerialNoReq', (_message.Message,), dict(
  DESCRIPTOR = _GETSERIALNOREQ,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.GetSerialNoReq)
  ))
_sym_db.RegisterMessage(GetSerialNoReq)

GetSerialNoRes = _reflection.GeneratedProtocolMessageType('GetSerialNoRes', (_message.Message,), dict(
  DESCRIPTOR = _GETSERIALNORES,
  __module__ = 'metadata.contract_pb2'
  # @@protoc_insertion_point(class_scope:contract.GetSerialNoRes)
  ))
_sym_db.RegisterMessage(GetSerialNoRes)



_CONTRACTSERVICE = _descriptor.ServiceDescriptor(
  name='ContractService',
  full_name='contract.ContractService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=3478,
  serialized_end=4469,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateContract',
    full_name='contract.ContractService.CreateContract',
    index=0,
    containing_service=None,
    input_type=_CREATECONTRACTREQ,
    output_type=_CREATECONTRACTRES,
    serialized_options=_b('\202\323\344\223\002\034\"\027/api/v2/contract/create:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetContractList',
    full_name='contract.ContractService.GetContractList',
    index=1,
    containing_service=None,
    input_type=_GETCONTRACTLISTREQ,
    output_type=_GETCONTRACTLISTRES,
    serialized_options=_b('\202\323\344\223\002\032\"\025/api/v2/contract/list:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetContractDetail',
    full_name='contract.ContractService.GetContractDetail',
    index=2,
    containing_service=None,
    input_type=_GETCONTRACTDETAILREQ,
    output_type=_GETCONTRACTDETAILRES,
    serialized_options=_b('\202\323\344\223\002\034\"\027/api/v2/contract/detail:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateContract',
    full_name='contract.ContractService.UpdateContract',
    index=3,
    containing_service=None,
    input_type=_UPDATECONTRACTREQ,
    output_type=_UPDATECONTRACTRES,
    serialized_options=_b('\202\323\344\223\002\034\"\027/api/v2/contract/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTaxRateList',
    full_name='contract.ContractService.GetTaxRateList',
    index=4,
    containing_service=None,
    input_type=_GETTAXRATEREQ,
    output_type=_GETTAXRATERES,
    serialized_options=_b('\202\323\344\223\002\026\"\021/api/v2/rate/list:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTaxList',
    full_name='contract.ContractService.GetTaxList',
    index=5,
    containing_service=None,
    input_type=_GETTAXLISTREQ,
    output_type=_GETTAXLISTRES,
    serialized_options=_b('\202\323\344\223\002\025\"\020/api/v2/tax/list:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetProList',
    full_name='contract.ContractService.GetProList',
    index=6,
    containing_service=None,
    input_type=_GETPROLISTREQ,
    output_type=_GETPROLISTRES,
    serialized_options=_b('\202\323\344\223\002\031\"\024/api/v2/product/list:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetContractMetaList',
    full_name='contract.ContractService.GetContractMetaList',
    index=7,
    containing_service=None,
    input_type=_GETCONTRACTMETALISTREQ,
    output_type=_GETCONTRACTMETALISTRES,
    serialized_options=_b('\202\323\344\223\002\037\"\032/api/v2/contract/meta/list:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetSerialNo',
    full_name='contract.ContractService.GetSerialNo',
    index=8,
    containing_service=None,
    input_type=_GETSERIALNOREQ,
    output_type=_GETSERIALNORES,
    serialized_options=_b('\202\323\344\223\002\026\"\021/api/v2/serial/no:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_CONTRACTSERVICE)

DESCRIPTOR.services_by_name['ContractService'] = _CONTRACTSERVICE

# @@protoc_insertion_point(module_scope)
