# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from metadata import product_pb2 as metadata_dot_product__pb2


class ProductServiceStub(object):
  """ProductService 用于从metadata获取Product数据
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetProductById = channel.unary_unary(
        '/product.ProductService/GetProductById',
        request_serializer=metadata_dot_product__pb2.GetProductByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_product__pb2.Product.FromString,
        )
    self.ListProduct = channel.unary_unary(
        '/product.ProductService/ListProduct',
        request_serializer=metadata_dot_product__pb2.ListProductRequest.SerializeToString,
        response_deserializer=metadata_dot_product__pb2.ListProductResponse.FromString,
        )
    self.ListAttributeRegionProductByStoreId = channel.unary_unary(
        '/product.ProductService/ListAttributeRegionProductByStoreId',
        request_serializer=metadata_dot_product__pb2.ListAttributeRegionProductByStoreRequest.SerializeToString,
        response_deserializer=metadata_dot_product__pb2.ListAttributeRegionProductByStoreResponse.FromString,
        )
    self.ListDistributionRegionProductByStoreId = channel.unary_unary(
        '/product.ProductService/ListDistributionRegionProductByStoreId',
        request_serializer=metadata_dot_product__pb2.ListDistributionRegionProductByStoreRequest.SerializeToString,
        response_deserializer=metadata_dot_product__pb2.ListDistributionRegionProductByStoreResponse.FromString,
        )
    self.ListPurchaseRegionProductByStoreId = channel.unary_unary(
        '/product.ProductService/ListPurchaseRegionProductByStoreId',
        request_serializer=metadata_dot_product__pb2.ListPurchaseRegionProductByStoreRequest.SerializeToString,
        response_deserializer=metadata_dot_product__pb2.ListPurchaseRegionProductByStoreResponse.FromString,
        )
    self.ListValidProductsForDistributionByStoreId = channel.unary_unary(
        '/product.ProductService/ListValidProductsForDistributionByStoreId',
        request_serializer=metadata_dot_product__pb2.ListValidProductsForDistributionByStoreRequest.SerializeToString,
        response_deserializer=metadata_dot_product__pb2.ListValidProductsForDistributionByStoreResponse.FromString,
        )
    self.ListValidStoresForDistributionByProduct = channel.unary_unary(
        '/product.ProductService/ListValidStoresForDistributionByProduct',
        request_serializer=metadata_dot_product__pb2.ListValidStoresForDistributionByProductRequest.SerializeToString,
        response_deserializer=metadata_dot_product__pb2.ListValidStoresForDistributionByProductResponse.FromString,
        )
    self.GetUnitById = channel.unary_unary(
        '/product.ProductService/GetUnitById',
        request_serializer=metadata_dot_product__pb2.GetUnitByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_product__pb2.Unit.FromString,
        )
    self.ListUnit = channel.unary_unary(
        '/product.ProductService/ListUnit',
        request_serializer=metadata_dot_product__pb2.ListUnitRequest.SerializeToString,
        response_deserializer=metadata_dot_product__pb2.ListUnitResponse.FromString,
        )
    self.GetProductCategoryById = channel.unary_unary(
        '/product.ProductService/GetProductCategoryById',
        request_serializer=metadata_dot_product__pb2.GetProductCategoryByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_product__pb2.Category.FromString,
        )
    self.ListProductCategory = channel.unary_unary(
        '/product.ProductService/ListProductCategory',
        request_serializer=metadata_dot_product__pb2.ListProductCategoryRequest.SerializeToString,
        response_deserializer=metadata_dot_product__pb2.ListProductCategoryResponse.FromString,
        )
    self.NewProductSku = channel.unary_unary(
        '/product.ProductService/NewProductSku',
        request_serializer=metadata_dot_product__pb2.NewProductSkuRequest.SerializeToString,
        response_deserializer=metadata_dot_product__pb2.NewProductSkuResponse.FromString,
        )
    self.UpdateProductSku = channel.unary_unary(
        '/product.ProductService/UpdateProductSku',
        request_serializer=metadata_dot_product__pb2.UpdateProductSkuRequest.SerializeToString,
        response_deserializer=metadata_dot_product__pb2.UpdateProductSkuResponse.FromString,
        )
    self.BatchUpdateProductMarketRegion = channel.unary_unary(
        '/product.ProductService/BatchUpdateProductMarketRegion',
        request_serializer=metadata_dot_product__pb2.BatchUpdateProductMarketRegionRequest.SerializeToString,
        response_deserializer=metadata_dot_product__pb2.BatchUpdateProductMarketRegionResponse.FromString,
        )
    self.QueryBatchTaskStatus = channel.unary_unary(
        '/product.ProductService/QueryBatchTaskStatus',
        request_serializer=metadata_dot_product__pb2.QueryBatchTaskStatusRequest.SerializeToString,
        response_deserializer=metadata_dot_product__pb2.QueryBatchTaskStatusResponse.FromString,
        )
    self.RedoBatchTaskStatus = channel.unary_unary(
        '/product.ProductService/RedoBatchTaskStatus',
        request_serializer=metadata_dot_product__pb2.RedoBatchTaskStatusRequest.SerializeToString,
        response_deserializer=metadata_dot_product__pb2.RedoBatchTaskStatusResponse.FromString,
        )
    self.BatchUpdateProductSpu = channel.unary_unary(
        '/product.ProductService/BatchUpdateProductSpu',
        request_serializer=metadata_dot_product__pb2.BatchUpdateProductSpuRequest.SerializeToString,
        response_deserializer=metadata_dot_product__pb2.BatchUpdateProductSpuResponse.FromString,
        )
    self.BatchUpdateProductTopping = channel.unary_unary(
        '/product.ProductService/BatchUpdateProductTopping',
        request_serializer=metadata_dot_product__pb2.BatchUpdateProductToppingRequest.SerializeToString,
        response_deserializer=metadata_dot_product__pb2.BatchUpdateProductToppingResponse.FromString,
        )


class ProductServiceServicer(object):
  """ProductService 用于从metadata获取Product数据
  """

  def GetProductById(self, request, context):
    """根据id获取商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListProduct(self, request, context):
    """查询 Product 列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListAttributeRegionProductByStoreId(self, request, context):
    """根据门店ID查询属性区域Product 列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListDistributionRegionProductByStoreId(self, request, context):
    """根据门店ID查询配送区域Product 列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListPurchaseRegionProductByStoreId(self, request, context):
    """根据门店ID查询采购区域Product 列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListValidProductsForDistributionByStoreId(self, request, context):
    """根据门店ID查询可以配送的Product列表(包含配送区域和采购区域)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListValidStoresForDistributionByProduct(self, request, context):
    """根据商品查询可以配送的Product列表对应的门店(包含配送区域和采购区域)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetUnitById(self, request, context):
    """根据id获取单位
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListUnit(self, request, context):
    """查询单位列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProductCategoryById(self, request, context):
    """根据id获取商品类别
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListProductCategory(self, request, context):
    """查询商品类别列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def NewProductSku(self, request, context):
    """新建product-sku
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateProductSku(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def BatchUpdateProductMarketRegion(self, request, context):
    """批量修改商品市场区域
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryBatchTaskStatus(self, request, context):
    """查询批量任务进度
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RedoBatchTaskStatus(self, request, context):
    """重试批量任务
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def BatchUpdateProductSpu(self, request, context):
    """批量修改商品附加属性(口味)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def BatchUpdateProductTopping(self, request, context):
    """批量修改商品加料
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_ProductServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetProductById': grpc.unary_unary_rpc_method_handler(
          servicer.GetProductById,
          request_deserializer=metadata_dot_product__pb2.GetProductByIdRequest.FromString,
          response_serializer=metadata_dot_product__pb2.Product.SerializeToString,
      ),
      'ListProduct': grpc.unary_unary_rpc_method_handler(
          servicer.ListProduct,
          request_deserializer=metadata_dot_product__pb2.ListProductRequest.FromString,
          response_serializer=metadata_dot_product__pb2.ListProductResponse.SerializeToString,
      ),
      'ListAttributeRegionProductByStoreId': grpc.unary_unary_rpc_method_handler(
          servicer.ListAttributeRegionProductByStoreId,
          request_deserializer=metadata_dot_product__pb2.ListAttributeRegionProductByStoreRequest.FromString,
          response_serializer=metadata_dot_product__pb2.ListAttributeRegionProductByStoreResponse.SerializeToString,
      ),
      'ListDistributionRegionProductByStoreId': grpc.unary_unary_rpc_method_handler(
          servicer.ListDistributionRegionProductByStoreId,
          request_deserializer=metadata_dot_product__pb2.ListDistributionRegionProductByStoreRequest.FromString,
          response_serializer=metadata_dot_product__pb2.ListDistributionRegionProductByStoreResponse.SerializeToString,
      ),
      'ListPurchaseRegionProductByStoreId': grpc.unary_unary_rpc_method_handler(
          servicer.ListPurchaseRegionProductByStoreId,
          request_deserializer=metadata_dot_product__pb2.ListPurchaseRegionProductByStoreRequest.FromString,
          response_serializer=metadata_dot_product__pb2.ListPurchaseRegionProductByStoreResponse.SerializeToString,
      ),
      'ListValidProductsForDistributionByStoreId': grpc.unary_unary_rpc_method_handler(
          servicer.ListValidProductsForDistributionByStoreId,
          request_deserializer=metadata_dot_product__pb2.ListValidProductsForDistributionByStoreRequest.FromString,
          response_serializer=metadata_dot_product__pb2.ListValidProductsForDistributionByStoreResponse.SerializeToString,
      ),
      'ListValidStoresForDistributionByProduct': grpc.unary_unary_rpc_method_handler(
          servicer.ListValidStoresForDistributionByProduct,
          request_deserializer=metadata_dot_product__pb2.ListValidStoresForDistributionByProductRequest.FromString,
          response_serializer=metadata_dot_product__pb2.ListValidStoresForDistributionByProductResponse.SerializeToString,
      ),
      'GetUnitById': grpc.unary_unary_rpc_method_handler(
          servicer.GetUnitById,
          request_deserializer=metadata_dot_product__pb2.GetUnitByIdRequest.FromString,
          response_serializer=metadata_dot_product__pb2.Unit.SerializeToString,
      ),
      'ListUnit': grpc.unary_unary_rpc_method_handler(
          servicer.ListUnit,
          request_deserializer=metadata_dot_product__pb2.ListUnitRequest.FromString,
          response_serializer=metadata_dot_product__pb2.ListUnitResponse.SerializeToString,
      ),
      'GetProductCategoryById': grpc.unary_unary_rpc_method_handler(
          servicer.GetProductCategoryById,
          request_deserializer=metadata_dot_product__pb2.GetProductCategoryByIdRequest.FromString,
          response_serializer=metadata_dot_product__pb2.Category.SerializeToString,
      ),
      'ListProductCategory': grpc.unary_unary_rpc_method_handler(
          servicer.ListProductCategory,
          request_deserializer=metadata_dot_product__pb2.ListProductCategoryRequest.FromString,
          response_serializer=metadata_dot_product__pb2.ListProductCategoryResponse.SerializeToString,
      ),
      'NewProductSku': grpc.unary_unary_rpc_method_handler(
          servicer.NewProductSku,
          request_deserializer=metadata_dot_product__pb2.NewProductSkuRequest.FromString,
          response_serializer=metadata_dot_product__pb2.NewProductSkuResponse.SerializeToString,
      ),
      'UpdateProductSku': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateProductSku,
          request_deserializer=metadata_dot_product__pb2.UpdateProductSkuRequest.FromString,
          response_serializer=metadata_dot_product__pb2.UpdateProductSkuResponse.SerializeToString,
      ),
      'BatchUpdateProductMarketRegion': grpc.unary_unary_rpc_method_handler(
          servicer.BatchUpdateProductMarketRegion,
          request_deserializer=metadata_dot_product__pb2.BatchUpdateProductMarketRegionRequest.FromString,
          response_serializer=metadata_dot_product__pb2.BatchUpdateProductMarketRegionResponse.SerializeToString,
      ),
      'QueryBatchTaskStatus': grpc.unary_unary_rpc_method_handler(
          servicer.QueryBatchTaskStatus,
          request_deserializer=metadata_dot_product__pb2.QueryBatchTaskStatusRequest.FromString,
          response_serializer=metadata_dot_product__pb2.QueryBatchTaskStatusResponse.SerializeToString,
      ),
      'RedoBatchTaskStatus': grpc.unary_unary_rpc_method_handler(
          servicer.RedoBatchTaskStatus,
          request_deserializer=metadata_dot_product__pb2.RedoBatchTaskStatusRequest.FromString,
          response_serializer=metadata_dot_product__pb2.RedoBatchTaskStatusResponse.SerializeToString,
      ),
      'BatchUpdateProductSpu': grpc.unary_unary_rpc_method_handler(
          servicer.BatchUpdateProductSpu,
          request_deserializer=metadata_dot_product__pb2.BatchUpdateProductSpuRequest.FromString,
          response_serializer=metadata_dot_product__pb2.BatchUpdateProductSpuResponse.SerializeToString,
      ),
      'BatchUpdateProductTopping': grpc.unary_unary_rpc_method_handler(
          servicer.BatchUpdateProductTopping,
          request_deserializer=metadata_dot_product__pb2.BatchUpdateProductToppingRequest.FromString,
          response_serializer=metadata_dot_product__pb2.BatchUpdateProductToppingResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'product.ProductService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
