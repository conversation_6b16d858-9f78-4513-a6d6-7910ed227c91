syntax = "proto3";
package product;
import "google/api/annotations.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";


// ProductService 用于从metadata获取Product数据
service ProductService {

    // 根据id获取商品
    rpc GetProductById(GetProductByIdRequest) returns (Product){
        option (google.api.http) = {
        get: "/api/v2/product/by/id/{id}"
        };
    }
    // 查询 Product 列表
    rpc ListProduct(ListProductRequest) returns (ListProductResponse){
        option (google.api.http) = {
        post: "/api/v2/product/query"
        body: "*"
        };
    }
    // 根据门店ID查询属性区域Product 列表
    rpc ListAttributeRegionProductByStoreId(ListAttributeRegionProductByStoreRequest) returns (ListAttributeRegionProductByStoreResponse){
        option (google.api.http) = {
        post: "/api/v2/product/in/attribute-region/by/store/{store_id}"
        body: "*"
        };
    }
    // 根据门店ID查询配送区域Product 列表
    rpc ListDistributionRegionProductByStoreId(ListDistributionRegionProductByStoreRequest) returns (ListDistributionRegionProductByStoreResponse){
        option (google.api.http) = {
        post: "/api/v2/product/in/distribution-region/by/store/{store_id}"
        body: "*"
        };
    }
    // 根据门店ID查询采购区域Product 列表
    rpc ListPurchaseRegionProductByStoreId(ListPurchaseRegionProductByStoreRequest) returns (ListPurchaseRegionProductByStoreResponse){
        option (google.api.http) = {
        post: "/api/v2/product/in/purchase-region/by/store/{store_id}"
        body: "*"
        };
    }
    // 根据门店ID查询可以配送的Product列表(包含配送区域和采购区域)
    rpc ListValidProductsForDistributionByStoreId(ListValidProductsForDistributionByStoreRequest) returns (ListValidProductsForDistributionByStoreResponse){
        option (google.api.http) = {
        post: "/api/v2/product/valid/for/distribution/by/store/{store_id}"
        body: "*"
        };
    }
    // 根据商品查询可以配送的Product列表对应的门店(包含配送区域和采购区域)
    rpc ListValidStoresForDistributionByProduct(ListValidStoresForDistributionByProductRequest) returns (ListValidStoresForDistributionByProductResponse){
        option (google.api.http) = {
        post: "/api/v2/product/valid/for/distribution/by/product"
        body: "*"
        };
    }
    // 根据id获取单位
    rpc GetUnitById(GetUnitByIdRequest) returns (Unit){
        option (google.api.http) = {
        get: "/api/v2/product/unit/by/id/{id}"
        };
    }
    // 查询单位列表
    rpc ListUnit(ListUnitRequest) returns (ListUnitResponse){
        option (google.api.http) = {
        post: "/api/v2/product/unit/query"
        body: "*"
        };
    }
    // 根据id获取商品类别
    rpc GetProductCategoryById(GetProductCategoryByIdRequest) returns (Category){
        option (google.api.http) = {
        get: "/api/v2/product/category/by/id/{id}"
        };
    }
    // 查询商品类别列表
    rpc ListProductCategory(ListProductCategoryRequest) returns (ListProductCategoryResponse){
        option (google.api.http) = {
        post: "/api/v2/product/category/query"
        body: "*"
        };
    }

    // 新建product-sku
    rpc NewProductSku(NewProductSkuRequest) returns (NewProductSkuResponse) {
        option (google.api.http) = {
        post: "/api/v2/product/sku/add"
        body: "*"
        };
    }
    rpc UpdateProductSku(UpdateProductSkuRequest) returns (UpdateProductSkuResponse) {
        option (google.api.http) = {
        post: "/api/v2/product/sku/update"
        body: "*"
        };
    }

    // 批量修改商品市场区域
    rpc BatchUpdateProductMarketRegion(BatchUpdateProductMarketRegionRequest) returns (BatchUpdateProductMarketRegionResponse) {
        option (google.api.http) = {
        post: "/api/v2/product/market_region/batch/update"
        body: "*"
        };
    }
    //查询批量任务进度
    rpc QueryBatchTaskStatus(QueryBatchTaskStatusRequest) returns (QueryBatchTaskStatusResponse) {
        option (google.api.http) = {
        get: "/api/v2/product/batch_task/by/id/{id}"
        };
    }
    //重试批量任务
    rpc RedoBatchTaskStatus(RedoBatchTaskStatusRequest) returns (RedoBatchTaskStatusResponse) {
        option (google.api.http) = {
        post: "/api/v2/product/batch_task/redo"
        body: "*"
        };
    }
    // 批量修改商品附加属性(口味)
    rpc BatchUpdateProductSpu(BatchUpdateProductSpuRequest) returns (BatchUpdateProductSpuResponse) {
        option (google.api.http) = {
        post: "/api/v2/product/product_spu/batch/update"
        body: "*"
        };
    }
    // 批量修改商品加料
    rpc BatchUpdateProductTopping(BatchUpdateProductToppingRequest) returns (BatchUpdateProductToppingResponse) {
        option (google.api.http) = {
            post: "/api/v2/product/topping/batch/update"
            body: "*"
        };
    }
}

message RedoBatchTaskStatusRequest {
    uint64 id = 1;
}

message RedoBatchTaskStatusResponse {
    bool success = 1;
}

message QueryBatchTaskStatusRequest {
    uint64 id = 1;
}

message QueryBatchTaskStatusResponse {
    TaskStatus master = 1;
    repeated TaskStatus items = 2;
}

message TaskStatus {
    uint64 id = 1;
    string status = 2;
}

message BatchUpdateProductMarketRegionRequest {
    repeated ProductPrice product_price = 1;
    repeated uint64 market_region = 2;
    bool overwrite = 3;
}

message ProductPrice {
    uint64 product_id = 1;
    google.protobuf.Struct fields = 2;
}

message BatchUpdateProductMarketRegionResponse {
    string request_id = 1;
}

message UpdateProductSkuRequest {
    google.protobuf.Struct fields = 1;
    uint64 id = 2;
}

message UpdateProductSkuResponse {
    uint64 id = 1;
}

message NewProductSkuRequest {
    google.protobuf.Struct fields = 1;
}

message NewProductSkuResponse {
    uint64 id = 1;
}

message GetProductByIdRequest{
    // 数据id
    uint64 id = 1;
    bool include_units =2;
    // 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
    string code = 3;
    // 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
    string relation = 4;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 5;
    string lan = 6;
}
message ListProductRequest{
    // 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
    string code = 1;
    // 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
    bool include_units =2;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 3;
    // 分页大小
    int32 limit = 4;
    // 跳过行数
    int32 offset = 5;
    // 排序字段
    string sort = 6;
    // 排序顺序
    string order = 7;
    // 返回总条数
    bool include_total = 8;
    // 要模糊查询的字符串
    string search = 9;
    // 要查询的字段, 多个逗号隔开;
    // 如果传入relation.[relation name].[field], 则会顺带搜索关联的数据,
    // 例: 搜索store数据时, search_fields=relation.branch.name则会搜索管理区域名称,
    // 找到匹配的管理区域, 然后找到关联到这些管理区域以及所有下级区域的门店
    string search_fields = 10;
    // 按id列表查询
    repeated uint64 ids = 11;
    // 按字段过滤
    google.protobuf.Struct filters = 12;
    // 按关系深层次递归过滤(获取包含下级节点的数据)
    google.protobuf.Struct relation_filters = 13;
    string lan = 14;
}


message ListProductResponse{
    repeated Product rows = 1;
    int32 total = 2;
}
message Product{
    // 数据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    string name = 4;
    string code = 5;
    string second_code = 6;
    string sale_type = 7;
    string product_type = 8;
    string bom_type = 9;
    string storage_type = 10;
    string status = 11;
    string alias = 12;
    uint64 category = 13;
    string updated = 14;
    google.protobuf.Struct extends = 15;
    google.protobuf.Struct extend_code = 16;
    repeated Unit units = 17;
    string model_code = 18;
    string model_name = 19;
    int32 default_receiving_deviation_min = 20;
    int32 default_receiving_deviation_max = 21;
    int32 default_purchase_deviation_min = 22;
    int32 default_purchase_deviation_max = 23;
    string ledger_class = 24;
    string category_name = 25;
}
message Unit{
    // 数据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    string name = 4;
    string code = 5;
    string tp_code = 6;
    string updated = 7;
    double rate = 8;
    bool default = 9;
    bool order = 10;
    bool purchase = 11;
    bool sales = 12;
    bool stocktake = 13;
    bool bom = 14;
    bool default_stocktake = 15;
    bool transfer = 16;
}

message DefaultResponse{
    bool result = 1;
}

message ListAttributeRegionProductByStoreRequest{
    uint64 store_id = 1;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 2;
    // 用于过滤的商品id
    repeated uint64 product_ids = 3;
    // 额外返回商品字段
    string include_product_fields = 4;
    // 是否包含商品单位
    bool include_product_units = 5;
    // 分页大小
    int32 limit = 6;
    // 跳过行数
    int32 offset = 7;
    // 排序字段
    string sort = 8;
    // 排序顺序
    string order = 9;
    // 返回总条数
    bool include_total = 10;
    // 按字段过滤
    google.protobuf.Struct filters = 11;
    // 按商品字段过滤
    google.protobuf.Struct product_filters = 12;
    string product_search = 13;
    string product_search_fields = 14;
    bool can_bom = 15;
    bool can_order = 16;
    bool can_purchase = 17;
    bool can_stocktake = 18;
    bool can_sales = 19;
    google.protobuf.Struct product_relation_filters = 20;
    bool check_division = 21;
    string lan = 22;
}
message ListDistributionRegionProductByStoreRequest{
    uint64 store_id = 1;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 2;
    // 用于过滤的商品id
    repeated uint64 product_ids = 3;
    // 额外返回商品字段
    string include_product_fields = 4;
    // 是否包含商品单位
    bool include_product_units = 5;
    // 分页大小
    int32 limit = 6;
    // 跳过行数
    int32 offset = 7;
    // 排序字段
    string sort = 8;
    // 排序顺序
    string order = 9;
    // 返回总条数
    bool include_total = 10;
    // 按字段过滤
    google.protobuf.Struct filters = 11;
    // 按商品字段过滤
    google.protobuf.Struct product_filters = 12;
    string product_search = 13;
    string product_search_fields = 14;
    bool can_bom = 15;
    bool can_order = 16;
    bool can_purchase = 17;
    bool can_stocktake = 18;
    bool can_sales = 19;
    google.protobuf.Struct product_relation_filters = 20;
    bool check_division = 21;
    string lan = 22;
}
message ListPurchaseRegionProductByStoreRequest{
    uint64 store_id = 1;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 2;
    // 用于过滤的商品id
    repeated uint64 product_ids = 3;
    // 额外返回商品字段
    string include_product_fields = 4;
    // 是否包含商品单位
    bool include_product_units = 5;
    // 分页大小
    int32 limit = 6;
    // 跳过行数
    int32 offset = 7;
    // 排序字段
    string sort = 8;
    // 排序顺序
    string order = 9;
    // 返回总条数
    bool include_total = 10;
    // 按字段过滤
    google.protobuf.Struct filters = 11;
    // 按商品字段过滤
    google.protobuf.Struct product_filters = 12;
    string product_search = 13;
    string product_search_fields = 14;
    bool can_bom = 15;
    bool can_order = 16;
    bool can_purchase = 17;
    bool can_stocktake = 18;
    bool can_sales = 19;
    google.protobuf.Struct product_relation_filters = 20;
    bool check_division = 21;
    string lan = 22;
}

message ListValidProductsForDistributionByStoreRequest{
    uint64 store_id = 1;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 2;
    // 用于过滤的商品id
    repeated uint64 product_ids = 3;
    // 额外返回商品字段
    string include_product_fields = 4;
    // 是否包含商品单位
    bool include_product_units = 5;
    // 分页大小
    int32 limit = 6;
    // 跳过行数
    int32 offset = 7;
    // 排序字段
    string sort = 8;
    // 排序顺序
    string order = 9;
    // 返回总条数
    bool include_total = 10;
    // 按字段过滤
    google.protobuf.Struct filters = 11;
    // 按商品字段过滤
    google.protobuf.Struct product_filters = 12;
    string product_search = 13;
    string product_search_fields = 14;
    string distr_type = 15;
    google.protobuf.Timestamp order_date = 16;
    google.protobuf.Struct product_relation_filters = 17;
    bool check_division = 18;
    bool is_demand = 19;
    string lan = 20;

}

message ListValidStoresForDistributionByProductRequest{
    // 用于过滤的商品id
    repeated uint64 product_ids = 1;
    string return_fields = 2;
    // 按字段过滤
    google.protobuf.Struct filters = 3;
    repeated uint64 store_ids = 4;
    google.protobuf.Struct store_filters = 5;
    google.protobuf.Struct store_relation_filters = 6;
    // 按商品字段过滤
    google.protobuf.Struct product_filters = 7;
    google.protobuf.Struct product_relation_filters = 8;
    string product_search = 9;
    string product_search_fields = 10;
    // 额外返回商品字段
    string include_product_fields = 11;
    // 是否包含商品单位
    bool include_product_units = 12;
    // 分页大小
    int32 limit = 13;
    // 跳过行数
    int32 offset = 14;
    // 排序字段
    string sort = 15;
    // 排序顺序
    string order = 16;
    // 返回总条数
    bool include_total = 17;
    string distr_type = 18;
    google.protobuf.Timestamp order_date = 19;
    bool check_division = 20;
    string lan = 21;

}
message ListAttributeRegionProductByStoreResponse{
    repeated AttributeRegionProduct rows = 1;
    int32 total = 2;
}
message ListDistributionRegionProductByStoreResponse{
    repeated DistributionRegionProduct rows = 1;
    int32 total = 2;
}
message ListPurchaseRegionProductByStoreResponse{
    repeated PurchaseRegionProduct rows = 1;
    int32 total = 2;
}
message ListValidProductsForDistributionByStoreResponse{
    repeated  ValidProductForDistribution rows = 1;
    int32 total = 2;
}
message ListValidStoresForDistributionByProductResponse{
    repeated  ValidStoreForDistribution rows = 1;
    int32 total = 2;
}

message AttributeRegionProduct{
    // 数据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    uint64 product_id = 4;
    Product product = 5;
    string inventory_type = 6;
    string per_thousand_period = 7;
    string replenish_method = 8;
    string safe_stock_method = 9;
    int32 unfreeze_day = 10;
    string cycle_type = 11;
    bool allow_adjust = 12;
    bool allow_stocktake = 13;
    bool allow_transfer = 14;
    google.protobuf.Struct extends = 15;
    bool allow_self_picking = 16;
}

message DistributionRegionProduct{
    // 数据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    uint64 product_id = 4;
    Product product = 5;
    string type = 6;
    uint64 distribution_center_id = 7;
    uint64 unit_id = 8;
    double min_number = 9;
    double max_number = 10;
    double increment_number = 11;
    int32 planned_arrival_days = 12;
    string circle_type = 13;
    string start_date = 14;
    int32 interval_days = 15;
    bool allow_main_order = 16;
    google.protobuf.Struct extends = 17;
    int32 receiving_deviation_min = 18;
    int32 receiving_deviation_max = 19;
    repeated Cycle cycles = 20;
    bool allow_deviation = 21;
    uint64 store_id = 22;
    string distr_type = 23;
    uint64 cycle_coef = 24;
    bool low_value_monthly = 25;
    string updated = 29;
}
message PurchaseRegionProduct{
    // 数据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    uint64 product_id = 4;
    Product product = 5;
    string type = 6;
    uint64 vendor_id = 7;
    uint64 unit_id = 8;
    double min_number = 9;
    double max_number = 10;
    double increment_number = 11;
    int32 planned_arrival_days = 12;
    string circle_type = 13;
    string start_date = 14;
    int32 interval_days = 15;
    double purchase_price = 16;
    double purchase_tax = 17;
    google.protobuf.Struct extends = 18;
    int32 purchase_deviation_min = 19;
    int32 purchase_deviation_max = 20;
    repeated Cycle cycles = 21;
    bool allow_main_order = 22;
    bool allow_deviation = 23;
    uint64 store_id = 24;
    uint64 cycle_coef = 25;
    double receive_deviation_min = 26;
    double receive_deviation_max = 27;
    string updated = 29;
}

message ValidProductForDistribution{
    // 数据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    uint64 product_id = 4;
    Product product = 5;
    string type = 6;
    uint64 distribution_center_id = 7;
    uint64 unit_id = 8;
    double min_number = 9;
    double max_number = 10;
    double increment_number = 11;
    int32 planned_arrival_days = 12;
    string circle_type = 13;
    string start_date = 14;
    int32 interval_days = 15;
    bool allow_main_order = 16;
    google.protobuf.Struct extends = 17;
    int32 receiving_deviation_min = 18;
    int32 receiving_deviation_max = 19;
    repeated Cycle cycles = 20;
    bool allow_deviation = 21;
    int32 purchase_deviation_min = 22;
    int32 purchase_deviation_max = 23;
    uint64 vendor_id = 24;
    string distr_type = 25;
    double purchase_price = 26;
    double purchase_tax = 27;
    uint64 cycle_coef = 28;
    string updated = 29;
    string created = 30;
}
message ValidStoreForDistribution{
    // 数据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    uint64 product_id = 4;
    Product product = 5;
    string type = 6;
    uint64 distribution_center_id = 7;
    uint64 unit_id = 8;
    double min_number = 9;
    double max_number = 10;
    double increment_number = 11;
    int32 planned_arrival_days = 12;
    string circle_type = 13;
    string start_date = 14;
    int32 interval_days = 15;
    bool allow_main_order = 16;
    google.protobuf.Struct extends = 17;
    int32 receiving_deviation_min = 18;
    int32 receiving_deviation_max = 19;
    repeated Cycle cycles = 20;
    bool allow_deviation = 21;
    int32 purchase_deviation_min = 22;
    int32 purchase_deviation_max = 23;
    uint64 vendor_id = 24;
    string distr_type = 25;
    uint64 store_id = 26;
    double purchase_price = 27;
    double purchase_tax = 28;
    uint64 cycle_coef = 29;
}
message GetUnitByIdRequest{
    // 数据id
    uint64 id = 1;
    // 需要返回的字段, 多个以逗号隔开
    string return_fields = 2;
}
message ListUnitRequest{
    // 需要返回的字段, 多个以逗号隔开
    string return_fields = 1;
    // 分页大小
    int32 limit = 2;
    // 跳过行数
    int32 offset = 3;
    // 排序字段
    string sort = 4;
    // 排序顺序
    string order = 5;
    // 返回总条数
    bool include_total = 6;
    // 要模糊查询的字符串
    string search = 7;
    // 要查询的字段, 多个逗号隔开;
    string search_fields = 8;
    // 按id列表查询
    repeated uint64 ids = 9;
    // 按字段过滤
    google.protobuf.Struct filters = 10;
    string lan = 11;
}

message ListUnitResponse{
    repeated Unit rows = 1;
    int32 total = 2;
}

message GetProductCategoryByIdRequest{
    // 数据id
    uint64 id = 1;
    // 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
    string code = 2;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 3;
    string lan = 4;
}
message ListProductCategoryRequest{
    // 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
    string code = 1;
    // 所需要返回的字段，包含relation里面的字段，如:return_fields="name,code,branch_region,geo_region"
    string return_fields = 2;
    // 分页大小
    int32 limit = 3;
    // 跳过行数
    int32 offset = 4;
    // 排序字段
    string sort = 5;
    // 排序顺序
    string order = 6;
    // 返回总条数
    bool include_total = 7;
    // 要模糊查询的字符串
    string search = 8;
    // 要查询的字段, 多个逗号隔开;
    string search_fields = 9;
    // 按id列表查询
    repeated uint64 ids = 10;
    // 按字段过滤
    google.protobuf.Struct filters = 11;
    string lan = 12;
}


message ListProductCategoryResponse{
    repeated Category rows = 1;
    int32 total = 2;
}
message Category{
    // 数据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    string name = 4;
    string code = 5;
    string updated = 6;
    uint64 parent_id = 7;

}

// 配送周期配置元素
message Cycle{
    // 计划到货天数
    int32 planned_arrival_days = 1;
    // 订货日
    string order_date = 2;

}

message BatchUpdateProductSpuRequest {
    // 新增的口味信息（包含商品id）
    repeated SpuFields spu_fields = 1;
    // 是否覆盖原来的记录(true会把原来的口味记录标记为disable 然后新加一条默认为true)
    bool overwrite = 2;
    string lan = 3;
}
message SpuFields{
    uint64 product_id = 1;
    google.protobuf.Struct fields = 2;
}
message BatchUpdateProductSpuResponse {
    // 任务ID
    string request_id = 1;
}

message BatchUpdateProductToppingRequest {
    // 新增的口味信息（包含商品id）
    repeated TopFields top_fields = 1;
    // 是否覆盖原来的记录(true会把原来的口味记录标记为disable 然后新加一条默认为true)
    bool overwrite = 2;
    string lan = 3;
}

message TopFields {
    uint64 product_id = 1;
    google.protobuf.Struct fields = 2;
}

message BatchUpdateProductToppingResponse {
    // 任务ID
    string request_id = 1;
}
