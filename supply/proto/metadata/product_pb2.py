# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: metadata/product.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='metadata/product.proto',
  package='product',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x16metadata/product.proto\x12\x07product\x1a\x1cgoogle/api/annotations.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"(\n\x1aRedoBatchTaskStatusRequest\x12\n\n\x02id\x18\x01 \x01(\x04\".\n\x1bRedoBatchTaskStatusResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\")\n\x1bQueryBatchTaskStatusRequest\x12\n\n\x02id\x18\x01 \x01(\x04\"g\n\x1cQueryBatchTaskStatusResponse\x12#\n\x06master\x18\x01 \x01(\x0b\x32\x13.product.TaskStatus\x12\"\n\x05items\x18\x02 \x03(\x0b\x32\x13.product.TaskStatus\"(\n\nTaskStatus\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\"\x7f\n%BatchUpdateProductMarketRegionRequest\x12,\n\rproduct_price\x18\x01 \x03(\x0b\x32\x15.product.ProductPrice\x12\x15\n\rmarket_region\x18\x02 \x03(\x04\x12\x11\n\toverwrite\x18\x03 \x01(\x08\"K\n\x0cProductPrice\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\'\n\x06\x66ields\x18\x02 \x01(\x0b\x32\x17.google.protobuf.Struct\"<\n&BatchUpdateProductMarketRegionResponse\x12\x12\n\nrequest_id\x18\x01 \x01(\t\"N\n\x17UpdateProductSkuRequest\x12\'\n\x06\x66ields\x18\x01 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\n\n\x02id\x18\x02 \x01(\x04\"&\n\x18UpdateProductSkuResponse\x12\n\n\x02id\x18\x01 \x01(\x04\"?\n\x14NewProductSkuRequest\x12\'\n\x06\x66ields\x18\x01 \x01(\x0b\x32\x17.google.protobuf.Struct\"#\n\x15NewProductSkuResponse\x12\n\n\x02id\x18\x01 \x01(\x04\"~\n\x15GetProductByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x15\n\rinclude_units\x18\x02 \x01(\x08\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x10\n\x08relation\x18\x04 \x01(\t\x12\x15\n\rreturn_fields\x18\x05 \x01(\t\x12\x0b\n\x03lan\x18\x06 \x01(\t\"\xc1\x02\n\x12ListProductRequest\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x15\n\rinclude_units\x18\x02 \x01(\x08\x12\x15\n\rreturn_fields\x18\x03 \x01(\t\x12\r\n\x05limit\x18\x04 \x01(\x05\x12\x0e\n\x06offset\x18\x05 \x01(\x05\x12\x0c\n\x04sort\x18\x06 \x01(\t\x12\r\n\x05order\x18\x07 \x01(\t\x12\x15\n\rinclude_total\x18\x08 \x01(\x08\x12\x0e\n\x06search\x18\t \x01(\t\x12\x15\n\rsearch_fields\x18\n \x01(\t\x12\x0b\n\x03ids\x18\x0b \x03(\x04\x12(\n\x07\x66ilters\x18\x0c \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x31\n\x10relation_filters\x18\r \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x0e \x01(\t\"D\n\x13ListProductResponse\x12\x1e\n\x04rows\x18\x01 \x03(\x0b\x32\x10.product.Product\x12\r\n\x05total\x18\x02 \x01(\x05\"\xec\x04\n\x07Product\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x13\n\x0bsecond_code\x18\x06 \x01(\t\x12\x11\n\tsale_type\x18\x07 \x01(\t\x12\x14\n\x0cproduct_type\x18\x08 \x01(\t\x12\x10\n\x08\x62om_type\x18\t \x01(\t\x12\x14\n\x0cstorage_type\x18\n \x01(\t\x12\x0e\n\x06status\x18\x0b \x01(\t\x12\r\n\x05\x61lias\x18\x0c \x01(\t\x12\x10\n\x08\x63\x61tegory\x18\r \x01(\x04\x12\x0f\n\x07updated\x18\x0e \x01(\t\x12(\n\x07\x65xtends\x18\x0f \x01(\x0b\x32\x17.google.protobuf.Struct\x12,\n\x0b\x65xtend_code\x18\x10 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x1c\n\x05units\x18\x11 \x03(\x0b\x32\r.product.Unit\x12\x12\n\nmodel_code\x18\x12 \x01(\t\x12\x12\n\nmodel_name\x18\x13 \x01(\t\x12\'\n\x1f\x64\x65\x66\x61ult_receiving_deviation_min\x18\x14 \x01(\x05\x12\'\n\x1f\x64\x65\x66\x61ult_receiving_deviation_max\x18\x15 \x01(\x05\x12&\n\x1e\x64\x65\x66\x61ult_purchase_deviation_min\x18\x16 \x01(\x05\x12&\n\x1e\x64\x65\x66\x61ult_purchase_deviation_max\x18\x17 \x01(\x05\x12\x14\n\x0cledger_class\x18\x18 \x01(\t\x12\x15\n\rcategory_name\x18\x19 \x01(\t\"\x92\x02\n\x04Unit\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x0f\n\x07tp_code\x18\x06 \x01(\t\x12\x0f\n\x07updated\x18\x07 \x01(\t\x12\x0c\n\x04rate\x18\x08 \x01(\x01\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\t \x01(\x08\x12\r\n\x05order\x18\n \x01(\x08\x12\x10\n\x08purchase\x18\x0b \x01(\x08\x12\r\n\x05sales\x18\x0c \x01(\x08\x12\x11\n\tstocktake\x18\r \x01(\x08\x12\x0b\n\x03\x62om\x18\x0e \x01(\x08\x12\x19\n\x11\x64\x65\x66\x61ult_stocktake\x18\x0f \x01(\x08\x12\x10\n\x08transfer\x18\x10 \x01(\x08\"!\n\x0f\x44\x65\x66\x61ultResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\xd1\x04\n(ListAttributeRegionProductByStoreRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x15\n\rreturn_fields\x18\x02 \x01(\t\x12\x13\n\x0bproduct_ids\x18\x03 \x03(\x04\x12\x1e\n\x16include_product_fields\x18\x04 \x01(\t\x12\x1d\n\x15include_product_units\x18\x05 \x01(\x08\x12\r\n\x05limit\x18\x06 \x01(\x05\x12\x0e\n\x06offset\x18\x07 \x01(\x05\x12\x0c\n\x04sort\x18\x08 \x01(\t\x12\r\n\x05order\x18\t \x01(\t\x12\x15\n\rinclude_total\x18\n \x01(\x08\x12(\n\x07\x66ilters\x18\x0b \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x30\n\x0fproduct_filters\x18\x0c \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x16\n\x0eproduct_search\x18\r \x01(\t\x12\x1d\n\x15product_search_fields\x18\x0e \x01(\t\x12\x0f\n\x07\x63\x61n_bom\x18\x0f \x01(\x08\x12\x11\n\tcan_order\x18\x10 \x01(\x08\x12\x14\n\x0c\x63\x61n_purchase\x18\x11 \x01(\x08\x12\x15\n\rcan_stocktake\x18\x12 \x01(\x08\x12\x11\n\tcan_sales\x18\x13 \x01(\x08\x12\x39\n\x18product_relation_filters\x18\x14 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x16\n\x0e\x63heck_division\x18\x15 \x01(\x08\x12\x0b\n\x03lan\x18\x16 \x01(\t\"\xd4\x04\n+ListDistributionRegionProductByStoreRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x15\n\rreturn_fields\x18\x02 \x01(\t\x12\x13\n\x0bproduct_ids\x18\x03 \x03(\x04\x12\x1e\n\x16include_product_fields\x18\x04 \x01(\t\x12\x1d\n\x15include_product_units\x18\x05 \x01(\x08\x12\r\n\x05limit\x18\x06 \x01(\x05\x12\x0e\n\x06offset\x18\x07 \x01(\x05\x12\x0c\n\x04sort\x18\x08 \x01(\t\x12\r\n\x05order\x18\t \x01(\t\x12\x15\n\rinclude_total\x18\n \x01(\x08\x12(\n\x07\x66ilters\x18\x0b \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x30\n\x0fproduct_filters\x18\x0c \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x16\n\x0eproduct_search\x18\r \x01(\t\x12\x1d\n\x15product_search_fields\x18\x0e \x01(\t\x12\x0f\n\x07\x63\x61n_bom\x18\x0f \x01(\x08\x12\x11\n\tcan_order\x18\x10 \x01(\x08\x12\x14\n\x0c\x63\x61n_purchase\x18\x11 \x01(\x08\x12\x15\n\rcan_stocktake\x18\x12 \x01(\x08\x12\x11\n\tcan_sales\x18\x13 \x01(\x08\x12\x39\n\x18product_relation_filters\x18\x14 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x16\n\x0e\x63heck_division\x18\x15 \x01(\x08\x12\x0b\n\x03lan\x18\x16 \x01(\t\"\xd0\x04\n\'ListPurchaseRegionProductByStoreRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x15\n\rreturn_fields\x18\x02 \x01(\t\x12\x13\n\x0bproduct_ids\x18\x03 \x03(\x04\x12\x1e\n\x16include_product_fields\x18\x04 \x01(\t\x12\x1d\n\x15include_product_units\x18\x05 \x01(\x08\x12\r\n\x05limit\x18\x06 \x01(\x05\x12\x0e\n\x06offset\x18\x07 \x01(\x05\x12\x0c\n\x04sort\x18\x08 \x01(\t\x12\r\n\x05order\x18\t \x01(\t\x12\x15\n\rinclude_total\x18\n \x01(\x08\x12(\n\x07\x66ilters\x18\x0b \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x30\n\x0fproduct_filters\x18\x0c \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x16\n\x0eproduct_search\x18\r \x01(\t\x12\x1d\n\x15product_search_fields\x18\x0e \x01(\t\x12\x0f\n\x07\x63\x61n_bom\x18\x0f \x01(\x08\x12\x11\n\tcan_order\x18\x10 \x01(\x08\x12\x14\n\x0c\x63\x61n_purchase\x18\x11 \x01(\x08\x12\x15\n\rcan_stocktake\x18\x12 \x01(\x08\x12\x11\n\tcan_sales\x18\x13 \x01(\x08\x12\x39\n\x18product_relation_filters\x18\x14 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x16\n\x0e\x63heck_division\x18\x15 \x01(\x08\x12\x0b\n\x03lan\x18\x16 \x01(\t\"\xca\x04\n.ListValidProductsForDistributionByStoreRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x15\n\rreturn_fields\x18\x02 \x01(\t\x12\x13\n\x0bproduct_ids\x18\x03 \x03(\x04\x12\x1e\n\x16include_product_fields\x18\x04 \x01(\t\x12\x1d\n\x15include_product_units\x18\x05 \x01(\x08\x12\r\n\x05limit\x18\x06 \x01(\x05\x12\x0e\n\x06offset\x18\x07 \x01(\x05\x12\x0c\n\x04sort\x18\x08 \x01(\t\x12\r\n\x05order\x18\t \x01(\t\x12\x15\n\rinclude_total\x18\n \x01(\x08\x12(\n\x07\x66ilters\x18\x0b \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x30\n\x0fproduct_filters\x18\x0c \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x16\n\x0eproduct_search\x18\r \x01(\t\x12\x1d\n\x15product_search_fields\x18\x0e \x01(\t\x12\x12\n\ndistr_type\x18\x0f \x01(\t\x12.\n\norder_date\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x39\n\x18product_relation_filters\x18\x11 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x16\n\x0e\x63heck_division\x18\x12 \x01(\x08\x12\x11\n\tis_demand\x18\x13 \x01(\x08\x12\x0b\n\x03lan\x18\x14 \x01(\t\"\xa1\x05\n.ListValidStoresForDistributionByProductRequest\x12\x13\n\x0bproduct_ids\x18\x01 \x03(\x04\x12\x15\n\rreturn_fields\x18\x02 \x01(\t\x12(\n\x07\x66ilters\x18\x03 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x11\n\tstore_ids\x18\x04 \x03(\x04\x12.\n\rstore_filters\x18\x05 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x37\n\x16store_relation_filters\x18\x06 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x30\n\x0fproduct_filters\x18\x07 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x39\n\x18product_relation_filters\x18\x08 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x16\n\x0eproduct_search\x18\t \x01(\t\x12\x1d\n\x15product_search_fields\x18\n \x01(\t\x12\x1e\n\x16include_product_fields\x18\x0b \x01(\t\x12\x1d\n\x15include_product_units\x18\x0c \x01(\x08\x12\r\n\x05limit\x18\r \x01(\x05\x12\x0e\n\x06offset\x18\x0e \x01(\x05\x12\x0c\n\x04sort\x18\x0f \x01(\t\x12\r\n\x05order\x18\x10 \x01(\t\x12\x15\n\rinclude_total\x18\x11 \x01(\x08\x12\x12\n\ndistr_type\x18\x12 \x01(\t\x12.\n\norder_date\x18\x13 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x16\n\x0e\x63heck_division\x18\x14 \x01(\x08\x12\x0b\n\x03lan\x18\x15 \x01(\t\"i\n)ListAttributeRegionProductByStoreResponse\x12-\n\x04rows\x18\x01 \x03(\x0b\x32\x1f.product.AttributeRegionProduct\x12\r\n\x05total\x18\x02 \x01(\x05\"o\n,ListDistributionRegionProductByStoreResponse\x12\x30\n\x04rows\x18\x01 \x03(\x0b\x32\".product.DistributionRegionProduct\x12\r\n\x05total\x18\x02 \x01(\x05\"g\n(ListPurchaseRegionProductByStoreResponse\x12,\n\x04rows\x18\x01 \x03(\x0b\x32\x1e.product.PurchaseRegionProduct\x12\r\n\x05total\x18\x02 \x01(\x05\"t\n/ListValidProductsForDistributionByStoreResponse\x12\x32\n\x04rows\x18\x01 \x03(\x0b\x32$.product.ValidProductForDistribution\x12\r\n\x05total\x18\x02 \x01(\x05\"r\n/ListValidStoresForDistributionByProductResponse\x12\x30\n\x04rows\x18\x01 \x03(\x0b\x32\".product.ValidStoreForDistribution\x12\r\n\x05total\x18\x02 \x01(\x05\"\xa2\x03\n\x16\x41ttributeRegionProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x12\n\nproduct_id\x18\x04 \x01(\x04\x12!\n\x07product\x18\x05 \x01(\x0b\x32\x10.product.Product\x12\x16\n\x0einventory_type\x18\x06 \x01(\t\x12\x1b\n\x13per_thousand_period\x18\x07 \x01(\t\x12\x18\n\x10replenish_method\x18\x08 \x01(\t\x12\x19\n\x11safe_stock_method\x18\t \x01(\t\x12\x14\n\x0cunfreeze_day\x18\n \x01(\x05\x12\x12\n\ncycle_type\x18\x0b \x01(\t\x12\x14\n\x0c\x61llow_adjust\x18\x0c \x01(\x08\x12\x17\n\x0f\x61llow_stocktake\x18\r \x01(\x08\x12\x16\n\x0e\x61llow_transfer\x18\x0e \x01(\x08\x12(\n\x07\x65xtends\x18\x0f \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x1a\n\x12\x61llow_self_picking\x18\x10 \x01(\x08\"\x88\x05\n\x19\x44istributionRegionProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x12\n\nproduct_id\x18\x04 \x01(\x04\x12!\n\x07product\x18\x05 \x01(\x0b\x32\x10.product.Product\x12\x0c\n\x04type\x18\x06 \x01(\t\x12\x1e\n\x16\x64istribution_center_id\x18\x07 \x01(\x04\x12\x0f\n\x07unit_id\x18\x08 \x01(\x04\x12\x12\n\nmin_number\x18\t \x01(\x01\x12\x12\n\nmax_number\x18\n \x01(\x01\x12\x18\n\x10increment_number\x18\x0b \x01(\x01\x12\x1c\n\x14planned_arrival_days\x18\x0c \x01(\x05\x12\x13\n\x0b\x63ircle_type\x18\r \x01(\t\x12\x12\n\nstart_date\x18\x0e \x01(\t\x12\x15\n\rinterval_days\x18\x0f \x01(\x05\x12\x18\n\x10\x61llow_main_order\x18\x10 \x01(\x08\x12(\n\x07\x65xtends\x18\x11 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x1f\n\x17receiving_deviation_min\x18\x12 \x01(\x05\x12\x1f\n\x17receiving_deviation_max\x18\x13 \x01(\x05\x12\x1e\n\x06\x63ycles\x18\x14 \x03(\x0b\x32\x0e.product.Cycle\x12\x17\n\x0f\x61llow_deviation\x18\x15 \x01(\x08\x12\x10\n\x08store_id\x18\x16 \x01(\x04\x12\x12\n\ndistr_type\x18\x17 \x01(\t\x12\x12\n\ncycle_coef\x18\x18 \x01(\x04\x12\x19\n\x11low_value_monthly\x18\x19 \x01(\x08\x12\x0f\n\x07updated\x18\x1d \x01(\t\"\xb2\x05\n\x15PurchaseRegionProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x12\n\nproduct_id\x18\x04 \x01(\x04\x12!\n\x07product\x18\x05 \x01(\x0b\x32\x10.product.Product\x12\x0c\n\x04type\x18\x06 \x01(\t\x12\x11\n\tvendor_id\x18\x07 \x01(\x04\x12\x0f\n\x07unit_id\x18\x08 \x01(\x04\x12\x12\n\nmin_number\x18\t \x01(\x01\x12\x12\n\nmax_number\x18\n \x01(\x01\x12\x18\n\x10increment_number\x18\x0b \x01(\x01\x12\x1c\n\x14planned_arrival_days\x18\x0c \x01(\x05\x12\x13\n\x0b\x63ircle_type\x18\r \x01(\t\x12\x12\n\nstart_date\x18\x0e \x01(\t\x12\x15\n\rinterval_days\x18\x0f \x01(\x05\x12\x16\n\x0epurchase_price\x18\x10 \x01(\x01\x12\x14\n\x0cpurchase_tax\x18\x11 \x01(\x01\x12(\n\x07\x65xtends\x18\x12 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x1e\n\x16purchase_deviation_min\x18\x13 \x01(\x05\x12\x1e\n\x16purchase_deviation_max\x18\x14 \x01(\x05\x12\x1e\n\x06\x63ycles\x18\x15 \x03(\x0b\x32\x0e.product.Cycle\x12\x18\n\x10\x61llow_main_order\x18\x16 \x01(\x08\x12\x17\n\x0f\x61llow_deviation\x18\x17 \x01(\x08\x12\x10\n\x08store_id\x18\x18 \x01(\x04\x12\x12\n\ncycle_coef\x18\x19 \x01(\x04\x12\x1d\n\x15receive_deviation_min\x18\x1a \x01(\x01\x12\x1d\n\x15receive_deviation_max\x18\x1b \x01(\x01\x12\x0f\n\x07updated\x18\x1d \x01(\t\"\xef\x05\n\x1bValidProductForDistribution\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x12\n\nproduct_id\x18\x04 \x01(\x04\x12!\n\x07product\x18\x05 \x01(\x0b\x32\x10.product.Product\x12\x0c\n\x04type\x18\x06 \x01(\t\x12\x1e\n\x16\x64istribution_center_id\x18\x07 \x01(\x04\x12\x0f\n\x07unit_id\x18\x08 \x01(\x04\x12\x12\n\nmin_number\x18\t \x01(\x01\x12\x12\n\nmax_number\x18\n \x01(\x01\x12\x18\n\x10increment_number\x18\x0b \x01(\x01\x12\x1c\n\x14planned_arrival_days\x18\x0c \x01(\x05\x12\x13\n\x0b\x63ircle_type\x18\r \x01(\t\x12\x12\n\nstart_date\x18\x0e \x01(\t\x12\x15\n\rinterval_days\x18\x0f \x01(\x05\x12\x18\n\x10\x61llow_main_order\x18\x10 \x01(\x08\x12(\n\x07\x65xtends\x18\x11 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x1f\n\x17receiving_deviation_min\x18\x12 \x01(\x05\x12\x1f\n\x17receiving_deviation_max\x18\x13 \x01(\x05\x12\x1e\n\x06\x63ycles\x18\x14 \x03(\x0b\x32\x0e.product.Cycle\x12\x17\n\x0f\x61llow_deviation\x18\x15 \x01(\x08\x12\x1e\n\x16purchase_deviation_min\x18\x16 \x01(\x05\x12\x1e\n\x16purchase_deviation_max\x18\x17 \x01(\x05\x12\x11\n\tvendor_id\x18\x18 \x01(\x04\x12\x12\n\ndistr_type\x18\x19 \x01(\t\x12\x16\n\x0epurchase_price\x18\x1a \x01(\x01\x12\x14\n\x0cpurchase_tax\x18\x1b \x01(\x01\x12\x12\n\ncycle_coef\x18\x1c \x01(\x04\x12\x0f\n\x07updated\x18\x1d \x01(\t\x12\x0f\n\x07\x63reated\x18\x1e \x01(\t\"\xdd\x05\n\x19ValidStoreForDistribution\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x12\n\nproduct_id\x18\x04 \x01(\x04\x12!\n\x07product\x18\x05 \x01(\x0b\x32\x10.product.Product\x12\x0c\n\x04type\x18\x06 \x01(\t\x12\x1e\n\x16\x64istribution_center_id\x18\x07 \x01(\x04\x12\x0f\n\x07unit_id\x18\x08 \x01(\x04\x12\x12\n\nmin_number\x18\t \x01(\x01\x12\x12\n\nmax_number\x18\n \x01(\x01\x12\x18\n\x10increment_number\x18\x0b \x01(\x01\x12\x1c\n\x14planned_arrival_days\x18\x0c \x01(\x05\x12\x13\n\x0b\x63ircle_type\x18\r \x01(\t\x12\x12\n\nstart_date\x18\x0e \x01(\t\x12\x15\n\rinterval_days\x18\x0f \x01(\x05\x12\x18\n\x10\x61llow_main_order\x18\x10 \x01(\x08\x12(\n\x07\x65xtends\x18\x11 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x1f\n\x17receiving_deviation_min\x18\x12 \x01(\x05\x12\x1f\n\x17receiving_deviation_max\x18\x13 \x01(\x05\x12\x1e\n\x06\x63ycles\x18\x14 \x03(\x0b\x32\x0e.product.Cycle\x12\x17\n\x0f\x61llow_deviation\x18\x15 \x01(\x08\x12\x1e\n\x16purchase_deviation_min\x18\x16 \x01(\x05\x12\x1e\n\x16purchase_deviation_max\x18\x17 \x01(\x05\x12\x11\n\tvendor_id\x18\x18 \x01(\x04\x12\x12\n\ndistr_type\x18\x19 \x01(\t\x12\x10\n\x08store_id\x18\x1a \x01(\x04\x12\x16\n\x0epurchase_price\x18\x1b \x01(\x01\x12\x14\n\x0cpurchase_tax\x18\x1c \x01(\x01\x12\x12\n\ncycle_coef\x18\x1d \x01(\x04\"7\n\x12GetUnitByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x15\n\rreturn_fields\x18\x02 \x01(\t\"\xe6\x01\n\x0fListUnitRequest\x12\x15\n\rreturn_fields\x18\x01 \x01(\t\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x0c\n\x04sort\x18\x04 \x01(\t\x12\r\n\x05order\x18\x05 \x01(\t\x12\x15\n\rinclude_total\x18\x06 \x01(\x08\x12\x0e\n\x06search\x18\x07 \x01(\t\x12\x15\n\rsearch_fields\x18\x08 \x01(\t\x12\x0b\n\x03ids\x18\t \x03(\x04\x12(\n\x07\x66ilters\x18\n \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x0b \x01(\t\">\n\x10ListUnitResponse\x12\x1b\n\x04rows\x18\x01 \x03(\x0b\x32\r.product.Unit\x12\r\n\x05total\x18\x02 \x01(\x05\"]\n\x1dGetProductCategoryByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x15\n\rreturn_fields\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\"\xff\x01\n\x1aListProductCategoryRequest\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x15\n\rreturn_fields\x18\x02 \x01(\t\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x0e\n\x06offset\x18\x04 \x01(\x05\x12\x0c\n\x04sort\x18\x05 \x01(\t\x12\r\n\x05order\x18\x06 \x01(\t\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12\x0e\n\x06search\x18\x08 \x01(\t\x12\x15\n\rsearch_fields\x18\t \x01(\t\x12\x0b\n\x03ids\x18\n \x03(\x04\x12(\n\x07\x66ilters\x18\x0b \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x0c \x01(\t\"M\n\x1bListProductCategoryResponse\x12\x1f\n\x04rows\x18\x01 \x03(\x0b\x32\x11.product.Category\x12\r\n\x05total\x18\x02 \x01(\x05\"|\n\x08\x43\x61tegory\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x0f\n\x07updated\x18\x06 \x01(\t\x12\x11\n\tparent_id\x18\x07 \x01(\x04\"9\n\x05\x43ycle\x12\x1c\n\x14planned_arrival_days\x18\x01 \x01(\x05\x12\x12\n\norder_date\x18\x02 \x01(\t\"f\n\x1c\x42\x61tchUpdateProductSpuRequest\x12&\n\nspu_fields\x18\x01 \x03(\x0b\x32\x12.product.SpuFields\x12\x11\n\toverwrite\x18\x02 \x01(\x08\x12\x0b\n\x03lan\x18\x03 \x01(\t\"H\n\tSpuFields\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\'\n\x06\x66ields\x18\x02 \x01(\x0b\x32\x17.google.protobuf.Struct\"3\n\x1d\x42\x61tchUpdateProductSpuResponse\x12\x12\n\nrequest_id\x18\x01 \x01(\t\"j\n BatchUpdateProductToppingRequest\x12&\n\ntop_fields\x18\x01 \x03(\x0b\x32\x12.product.TopFields\x12\x11\n\toverwrite\x18\x02 \x01(\x08\x12\x0b\n\x03lan\x18\x03 \x01(\t\"H\n\tTopFields\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\'\n\x06\x66ields\x18\x02 \x01(\x0b\x32\x17.google.protobuf.Struct\"7\n!BatchUpdateProductToppingResponse\x12\x12\n\nrequest_id\x18\x01 \x01(\t2\x9e\x16\n\x0eProductService\x12\x66\n\x0eGetProductById\x12\x1e.product.GetProductByIdRequest\x1a\x10.product.Product\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/api/v2/product/by/id/{id}\x12j\n\x0bListProduct\x12\x1b.product.ListProductRequest\x1a\x1c.product.ListProductResponse\" \x82\xd3\xe4\x93\x02\x1a\"\x15/api/v2/product/query:\x01*\x12\xd0\x01\n#ListAttributeRegionProductByStoreId\x12\x31.product.ListAttributeRegionProductByStoreRequest\x1a\x32.product.ListAttributeRegionProductByStoreResponse\"B\x82\xd3\xe4\x93\x02<\"7/api/v2/product/in/attribute-region/by/store/{store_id}:\x01*\x12\xdc\x01\n&ListDistributionRegionProductByStoreId\x12\x34.product.ListDistributionRegionProductByStoreRequest\x1a\x35.product.ListDistributionRegionProductByStoreResponse\"E\x82\xd3\xe4\x93\x02?\":/api/v2/product/in/distribution-region/by/store/{store_id}:\x01*\x12\xcc\x01\n\"ListPurchaseRegionProductByStoreId\x12\x30.product.ListPurchaseRegionProductByStoreRequest\x1a\x31.product.ListPurchaseRegionProductByStoreResponse\"A\x82\xd3\xe4\x93\x02;\"6/api/v2/product/in/purchase-region/by/store/{store_id}:\x01*\x12\xe5\x01\n)ListValidProductsForDistributionByStoreId\x12\x37.product.ListValidProductsForDistributionByStoreRequest\x1a\x38.product.ListValidProductsForDistributionByStoreResponse\"E\x82\xd3\xe4\x93\x02?\":/api/v2/product/valid/for/distribution/by/store/{store_id}:\x01*\x12\xda\x01\n\'ListValidStoresForDistributionByProduct\x12\x37.product.ListValidStoresForDistributionByProductRequest\x1a\x38.product.ListValidStoresForDistributionByProductResponse\"<\x82\xd3\xe4\x93\x02\x36\"1/api/v2/product/valid/for/distribution/by/product:\x01*\x12\x62\n\x0bGetUnitById\x12\x1b.product.GetUnitByIdRequest\x1a\r.product.Unit\"\'\x82\xd3\xe4\x93\x02!\x12\x1f/api/v2/product/unit/by/id/{id}\x12\x66\n\x08ListUnit\x12\x18.product.ListUnitRequest\x1a\x19.product.ListUnitResponse\"%\x82\xd3\xe4\x93\x02\x1f\"\x1a/api/v2/product/unit/query:\x01*\x12\x80\x01\n\x16GetProductCategoryById\x12&.product.GetProductCategoryByIdRequest\x1a\x11.product.Category\"+\x82\xd3\xe4\x93\x02%\x12#/api/v2/product/category/by/id/{id}\x12\x8b\x01\n\x13ListProductCategory\x12#.product.ListProductCategoryRequest\x1a$.product.ListProductCategoryResponse\")\x82\xd3\xe4\x93\x02#\"\x1e/api/v2/product/category/query:\x01*\x12r\n\rNewProductSku\x12\x1d.product.NewProductSkuRequest\x1a\x1e.product.NewProductSkuResponse\"\"\x82\xd3\xe4\x93\x02\x1c\"\x17/api/v2/product/sku/add:\x01*\x12~\n\x10UpdateProductSku\x12 .product.UpdateProductSkuRequest\x1a!.product.UpdateProductSkuResponse\"%\x82\xd3\xe4\x93\x02\x1f\"\x1a/api/v2/product/sku/update:\x01*\x12\xb8\x01\n\x1e\x42\x61tchUpdateProductMarketRegion\x12..product.BatchUpdateProductMarketRegionRequest\x1a/.product.BatchUpdateProductMarketRegionResponse\"5\x82\xd3\xe4\x93\x02/\"*/api/v2/product/market_region/batch/update:\x01*\x12\x92\x01\n\x14QueryBatchTaskStatus\x12$.product.QueryBatchTaskStatusRequest\x1a%.product.QueryBatchTaskStatusResponse\"-\x82\xd3\xe4\x93\x02\'\x12%/api/v2/product/batch_task/by/id/{id}\x12\x8c\x01\n\x13RedoBatchTaskStatus\x12#.product.RedoBatchTaskStatusRequest\x1a$.product.RedoBatchTaskStatusResponse\"*\x82\xd3\xe4\x93\x02$\"\x1f/api/v2/product/batch_task/redo:\x01*\x12\x9b\x01\n\x15\x42\x61tchUpdateProductSpu\x12%.product.BatchUpdateProductSpuRequest\x1a&.product.BatchUpdateProductSpuResponse\"3\x82\xd3\xe4\x93\x02-\"(/api/v2/product/product_spu/batch/update:\x01*\x12\xa3\x01\n\x19\x42\x61tchUpdateProductTopping\x12).product.BatchUpdateProductToppingRequest\x1a*.product.BatchUpdateProductToppingResponse\"/\x82\xd3\xe4\x93\x02)\"$/api/v2/product/topping/batch/update:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_struct__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_REDOBATCHTASKSTATUSREQUEST = _descriptor.Descriptor(
  name='RedoBatchTaskStatusRequest',
  full_name='product.RedoBatchTaskStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='product.RedoBatchTaskStatusRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=128,
  serialized_end=168,
)


_REDOBATCHTASKSTATUSRESPONSE = _descriptor.Descriptor(
  name='RedoBatchTaskStatusResponse',
  full_name='product.RedoBatchTaskStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='product.RedoBatchTaskStatusResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=170,
  serialized_end=216,
)


_QUERYBATCHTASKSTATUSREQUEST = _descriptor.Descriptor(
  name='QueryBatchTaskStatusRequest',
  full_name='product.QueryBatchTaskStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='product.QueryBatchTaskStatusRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=218,
  serialized_end=259,
)


_QUERYBATCHTASKSTATUSRESPONSE = _descriptor.Descriptor(
  name='QueryBatchTaskStatusResponse',
  full_name='product.QueryBatchTaskStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='master', full_name='product.QueryBatchTaskStatusResponse.master', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='items', full_name='product.QueryBatchTaskStatusResponse.items', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=261,
  serialized_end=364,
)


_TASKSTATUS = _descriptor.Descriptor(
  name='TaskStatus',
  full_name='product.TaskStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='product.TaskStatus.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='product.TaskStatus.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=366,
  serialized_end=406,
)


_BATCHUPDATEPRODUCTMARKETREGIONREQUEST = _descriptor.Descriptor(
  name='BatchUpdateProductMarketRegionRequest',
  full_name='product.BatchUpdateProductMarketRegionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_price', full_name='product.BatchUpdateProductMarketRegionRequest.product_price', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='market_region', full_name='product.BatchUpdateProductMarketRegionRequest.market_region', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='overwrite', full_name='product.BatchUpdateProductMarketRegionRequest.overwrite', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=408,
  serialized_end=535,
)


_PRODUCTPRICE = _descriptor.Descriptor(
  name='ProductPrice',
  full_name='product.ProductPrice',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='product.ProductPrice.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='product.ProductPrice.fields', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=537,
  serialized_end=612,
)


_BATCHUPDATEPRODUCTMARKETREGIONRESPONSE = _descriptor.Descriptor(
  name='BatchUpdateProductMarketRegionResponse',
  full_name='product.BatchUpdateProductMarketRegionResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='product.BatchUpdateProductMarketRegionResponse.request_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=614,
  serialized_end=674,
)


_UPDATEPRODUCTSKUREQUEST = _descriptor.Descriptor(
  name='UpdateProductSkuRequest',
  full_name='product.UpdateProductSkuRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fields', full_name='product.UpdateProductSkuRequest.fields', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='product.UpdateProductSkuRequest.id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=676,
  serialized_end=754,
)


_UPDATEPRODUCTSKURESPONSE = _descriptor.Descriptor(
  name='UpdateProductSkuResponse',
  full_name='product.UpdateProductSkuResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='product.UpdateProductSkuResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=756,
  serialized_end=794,
)


_NEWPRODUCTSKUREQUEST = _descriptor.Descriptor(
  name='NewProductSkuRequest',
  full_name='product.NewProductSkuRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fields', full_name='product.NewProductSkuRequest.fields', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=796,
  serialized_end=859,
)


_NEWPRODUCTSKURESPONSE = _descriptor.Descriptor(
  name='NewProductSkuResponse',
  full_name='product.NewProductSkuResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='product.NewProductSkuResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=861,
  serialized_end=896,
)


_GETPRODUCTBYIDREQUEST = _descriptor.Descriptor(
  name='GetProductByIdRequest',
  full_name='product.GetProductByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='product.GetProductByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_units', full_name='product.GetProductByIdRequest.include_units', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='product.GetProductByIdRequest.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation', full_name='product.GetProductByIdRequest.relation', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='product.GetProductByIdRequest.return_fields', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='product.GetProductByIdRequest.lan', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=898,
  serialized_end=1024,
)


_LISTPRODUCTREQUEST = _descriptor.Descriptor(
  name='ListProductRequest',
  full_name='product.ListProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='product.ListProductRequest.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_units', full_name='product.ListProductRequest.include_units', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='product.ListProductRequest.return_fields', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='product.ListProductRequest.limit', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='product.ListProductRequest.offset', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='product.ListProductRequest.sort', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='product.ListProductRequest.order', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='product.ListProductRequest.include_total', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='product.ListProductRequest.search', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='product.ListProductRequest.search_fields', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='product.ListProductRequest.ids', index=10,
      number=11, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='product.ListProductRequest.filters', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation_filters', full_name='product.ListProductRequest.relation_filters', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='product.ListProductRequest.lan', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1027,
  serialized_end=1348,
)


_LISTPRODUCTRESPONSE = _descriptor.Descriptor(
  name='ListProductResponse',
  full_name='product.ListProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='product.ListProductResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='product.ListProductResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1350,
  serialized_end=1418,
)


_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='product.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='product.Product.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='product.Product.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='product.Product.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='product.Product.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='product.Product.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='second_code', full_name='product.Product.second_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sale_type', full_name='product.Product.sale_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='product.Product.product_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_type', full_name='product.Product.bom_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='product.Product.storage_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='product.Product.status', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='alias', full_name='product.Product.alias', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category', full_name='product.Product.category', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='product.Product.updated', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='product.Product.extends', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extend_code', full_name='product.Product.extend_code', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='product.Product.units', index=16,
      number=17, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_code', full_name='product.Product.model_code', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_name', full_name='product.Product.model_name', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default_receiving_deviation_min', full_name='product.Product.default_receiving_deviation_min', index=19,
      number=20, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default_receiving_deviation_max', full_name='product.Product.default_receiving_deviation_max', index=20,
      number=21, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default_purchase_deviation_min', full_name='product.Product.default_purchase_deviation_min', index=21,
      number=22, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default_purchase_deviation_max', full_name='product.Product.default_purchase_deviation_max', index=22,
      number=23, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ledger_class', full_name='product.Product.ledger_class', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='product.Product.category_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1421,
  serialized_end=2041,
)


_UNIT = _descriptor.Descriptor(
  name='Unit',
  full_name='product.Unit',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='product.Unit.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='product.Unit.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='product.Unit.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='product.Unit.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='product.Unit.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tp_code', full_name='product.Unit.tp_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='product.Unit.updated', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='product.Unit.rate', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default', full_name='product.Unit.default', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='product.Unit.order', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase', full_name='product.Unit.purchase', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales', full_name='product.Unit.sales', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='product.Unit.stocktake', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom', full_name='product.Unit.bom', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default_stocktake', full_name='product.Unit.default_stocktake', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer', full_name='product.Unit.transfer', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2044,
  serialized_end=2318,
)


_DEFAULTRESPONSE = _descriptor.Descriptor(
  name='DefaultResponse',
  full_name='product.DefaultResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='product.DefaultResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2320,
  serialized_end=2353,
)


_LISTATTRIBUTEREGIONPRODUCTBYSTOREREQUEST = _descriptor.Descriptor(
  name='ListAttributeRegionProductByStoreRequest',
  full_name='product.ListAttributeRegionProductByStoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='product.ListAttributeRegionProductByStoreRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='product.ListAttributeRegionProductByStoreRequest.return_fields', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='product.ListAttributeRegionProductByStoreRequest.product_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_product_fields', full_name='product.ListAttributeRegionProductByStoreRequest.include_product_fields', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_product_units', full_name='product.ListAttributeRegionProductByStoreRequest.include_product_units', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='product.ListAttributeRegionProductByStoreRequest.limit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='product.ListAttributeRegionProductByStoreRequest.offset', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='product.ListAttributeRegionProductByStoreRequest.sort', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='product.ListAttributeRegionProductByStoreRequest.order', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='product.ListAttributeRegionProductByStoreRequest.include_total', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='product.ListAttributeRegionProductByStoreRequest.filters', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_filters', full_name='product.ListAttributeRegionProductByStoreRequest.product_filters', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_search', full_name='product.ListAttributeRegionProductByStoreRequest.product_search', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_search_fields', full_name='product.ListAttributeRegionProductByStoreRequest.product_search_fields', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_bom', full_name='product.ListAttributeRegionProductByStoreRequest.can_bom', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_order', full_name='product.ListAttributeRegionProductByStoreRequest.can_order', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_purchase', full_name='product.ListAttributeRegionProductByStoreRequest.can_purchase', index=16,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_stocktake', full_name='product.ListAttributeRegionProductByStoreRequest.can_stocktake', index=17,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_sales', full_name='product.ListAttributeRegionProductByStoreRequest.can_sales', index=18,
      number=19, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_relation_filters', full_name='product.ListAttributeRegionProductByStoreRequest.product_relation_filters', index=19,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='check_division', full_name='product.ListAttributeRegionProductByStoreRequest.check_division', index=20,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='product.ListAttributeRegionProductByStoreRequest.lan', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2356,
  serialized_end=2949,
)


_LISTDISTRIBUTIONREGIONPRODUCTBYSTOREREQUEST = _descriptor.Descriptor(
  name='ListDistributionRegionProductByStoreRequest',
  full_name='product.ListDistributionRegionProductByStoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='product.ListDistributionRegionProductByStoreRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='product.ListDistributionRegionProductByStoreRequest.return_fields', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='product.ListDistributionRegionProductByStoreRequest.product_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_product_fields', full_name='product.ListDistributionRegionProductByStoreRequest.include_product_fields', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_product_units', full_name='product.ListDistributionRegionProductByStoreRequest.include_product_units', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='product.ListDistributionRegionProductByStoreRequest.limit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='product.ListDistributionRegionProductByStoreRequest.offset', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='product.ListDistributionRegionProductByStoreRequest.sort', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='product.ListDistributionRegionProductByStoreRequest.order', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='product.ListDistributionRegionProductByStoreRequest.include_total', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='product.ListDistributionRegionProductByStoreRequest.filters', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_filters', full_name='product.ListDistributionRegionProductByStoreRequest.product_filters', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_search', full_name='product.ListDistributionRegionProductByStoreRequest.product_search', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_search_fields', full_name='product.ListDistributionRegionProductByStoreRequest.product_search_fields', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_bom', full_name='product.ListDistributionRegionProductByStoreRequest.can_bom', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_order', full_name='product.ListDistributionRegionProductByStoreRequest.can_order', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_purchase', full_name='product.ListDistributionRegionProductByStoreRequest.can_purchase', index=16,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_stocktake', full_name='product.ListDistributionRegionProductByStoreRequest.can_stocktake', index=17,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_sales', full_name='product.ListDistributionRegionProductByStoreRequest.can_sales', index=18,
      number=19, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_relation_filters', full_name='product.ListDistributionRegionProductByStoreRequest.product_relation_filters', index=19,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='check_division', full_name='product.ListDistributionRegionProductByStoreRequest.check_division', index=20,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='product.ListDistributionRegionProductByStoreRequest.lan', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2952,
  serialized_end=3548,
)


_LISTPURCHASEREGIONPRODUCTBYSTOREREQUEST = _descriptor.Descriptor(
  name='ListPurchaseRegionProductByStoreRequest',
  full_name='product.ListPurchaseRegionProductByStoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='product.ListPurchaseRegionProductByStoreRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='product.ListPurchaseRegionProductByStoreRequest.return_fields', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='product.ListPurchaseRegionProductByStoreRequest.product_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_product_fields', full_name='product.ListPurchaseRegionProductByStoreRequest.include_product_fields', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_product_units', full_name='product.ListPurchaseRegionProductByStoreRequest.include_product_units', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='product.ListPurchaseRegionProductByStoreRequest.limit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='product.ListPurchaseRegionProductByStoreRequest.offset', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='product.ListPurchaseRegionProductByStoreRequest.sort', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='product.ListPurchaseRegionProductByStoreRequest.order', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='product.ListPurchaseRegionProductByStoreRequest.include_total', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='product.ListPurchaseRegionProductByStoreRequest.filters', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_filters', full_name='product.ListPurchaseRegionProductByStoreRequest.product_filters', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_search', full_name='product.ListPurchaseRegionProductByStoreRequest.product_search', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_search_fields', full_name='product.ListPurchaseRegionProductByStoreRequest.product_search_fields', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_bom', full_name='product.ListPurchaseRegionProductByStoreRequest.can_bom', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_order', full_name='product.ListPurchaseRegionProductByStoreRequest.can_order', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_purchase', full_name='product.ListPurchaseRegionProductByStoreRequest.can_purchase', index=16,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_stocktake', full_name='product.ListPurchaseRegionProductByStoreRequest.can_stocktake', index=17,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_sales', full_name='product.ListPurchaseRegionProductByStoreRequest.can_sales', index=18,
      number=19, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_relation_filters', full_name='product.ListPurchaseRegionProductByStoreRequest.product_relation_filters', index=19,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='check_division', full_name='product.ListPurchaseRegionProductByStoreRequest.check_division', index=20,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='product.ListPurchaseRegionProductByStoreRequest.lan', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3551,
  serialized_end=4143,
)


_LISTVALIDPRODUCTSFORDISTRIBUTIONBYSTOREREQUEST = _descriptor.Descriptor(
  name='ListValidProductsForDistributionByStoreRequest',
  full_name='product.ListValidProductsForDistributionByStoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='product.ListValidProductsForDistributionByStoreRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='product.ListValidProductsForDistributionByStoreRequest.return_fields', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='product.ListValidProductsForDistributionByStoreRequest.product_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_product_fields', full_name='product.ListValidProductsForDistributionByStoreRequest.include_product_fields', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_product_units', full_name='product.ListValidProductsForDistributionByStoreRequest.include_product_units', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='product.ListValidProductsForDistributionByStoreRequest.limit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='product.ListValidProductsForDistributionByStoreRequest.offset', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='product.ListValidProductsForDistributionByStoreRequest.sort', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='product.ListValidProductsForDistributionByStoreRequest.order', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='product.ListValidProductsForDistributionByStoreRequest.include_total', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='product.ListValidProductsForDistributionByStoreRequest.filters', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_filters', full_name='product.ListValidProductsForDistributionByStoreRequest.product_filters', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_search', full_name='product.ListValidProductsForDistributionByStoreRequest.product_search', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_search_fields', full_name='product.ListValidProductsForDistributionByStoreRequest.product_search_fields', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='product.ListValidProductsForDistributionByStoreRequest.distr_type', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date', full_name='product.ListValidProductsForDistributionByStoreRequest.order_date', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_relation_filters', full_name='product.ListValidProductsForDistributionByStoreRequest.product_relation_filters', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='check_division', full_name='product.ListValidProductsForDistributionByStoreRequest.check_division', index=17,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_demand', full_name='product.ListValidProductsForDistributionByStoreRequest.is_demand', index=18,
      number=19, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='product.ListValidProductsForDistributionByStoreRequest.lan', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4146,
  serialized_end=4732,
)


_LISTVALIDSTORESFORDISTRIBUTIONBYPRODUCTREQUEST = _descriptor.Descriptor(
  name='ListValidStoresForDistributionByProductRequest',
  full_name='product.ListValidStoresForDistributionByProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='product.ListValidStoresForDistributionByProductRequest.product_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='product.ListValidStoresForDistributionByProductRequest.return_fields', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='product.ListValidStoresForDistributionByProductRequest.filters', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='product.ListValidStoresForDistributionByProductRequest.store_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_filters', full_name='product.ListValidStoresForDistributionByProductRequest.store_filters', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_relation_filters', full_name='product.ListValidStoresForDistributionByProductRequest.store_relation_filters', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_filters', full_name='product.ListValidStoresForDistributionByProductRequest.product_filters', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_relation_filters', full_name='product.ListValidStoresForDistributionByProductRequest.product_relation_filters', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_search', full_name='product.ListValidStoresForDistributionByProductRequest.product_search', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_search_fields', full_name='product.ListValidStoresForDistributionByProductRequest.product_search_fields', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_product_fields', full_name='product.ListValidStoresForDistributionByProductRequest.include_product_fields', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_product_units', full_name='product.ListValidStoresForDistributionByProductRequest.include_product_units', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='product.ListValidStoresForDistributionByProductRequest.limit', index=12,
      number=13, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='product.ListValidStoresForDistributionByProductRequest.offset', index=13,
      number=14, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='product.ListValidStoresForDistributionByProductRequest.sort', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='product.ListValidStoresForDistributionByProductRequest.order', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='product.ListValidStoresForDistributionByProductRequest.include_total', index=16,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='product.ListValidStoresForDistributionByProductRequest.distr_type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date', full_name='product.ListValidStoresForDistributionByProductRequest.order_date', index=18,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='check_division', full_name='product.ListValidStoresForDistributionByProductRequest.check_division', index=19,
      number=20, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='product.ListValidStoresForDistributionByProductRequest.lan', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4735,
  serialized_end=5408,
)


_LISTATTRIBUTEREGIONPRODUCTBYSTORERESPONSE = _descriptor.Descriptor(
  name='ListAttributeRegionProductByStoreResponse',
  full_name='product.ListAttributeRegionProductByStoreResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='product.ListAttributeRegionProductByStoreResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='product.ListAttributeRegionProductByStoreResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5410,
  serialized_end=5515,
)


_LISTDISTRIBUTIONREGIONPRODUCTBYSTORERESPONSE = _descriptor.Descriptor(
  name='ListDistributionRegionProductByStoreResponse',
  full_name='product.ListDistributionRegionProductByStoreResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='product.ListDistributionRegionProductByStoreResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='product.ListDistributionRegionProductByStoreResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5517,
  serialized_end=5628,
)


_LISTPURCHASEREGIONPRODUCTBYSTORERESPONSE = _descriptor.Descriptor(
  name='ListPurchaseRegionProductByStoreResponse',
  full_name='product.ListPurchaseRegionProductByStoreResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='product.ListPurchaseRegionProductByStoreResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='product.ListPurchaseRegionProductByStoreResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5630,
  serialized_end=5733,
)


_LISTVALIDPRODUCTSFORDISTRIBUTIONBYSTORERESPONSE = _descriptor.Descriptor(
  name='ListValidProductsForDistributionByStoreResponse',
  full_name='product.ListValidProductsForDistributionByStoreResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='product.ListValidProductsForDistributionByStoreResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='product.ListValidProductsForDistributionByStoreResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5735,
  serialized_end=5851,
)


_LISTVALIDSTORESFORDISTRIBUTIONBYPRODUCTRESPONSE = _descriptor.Descriptor(
  name='ListValidStoresForDistributionByProductResponse',
  full_name='product.ListValidStoresForDistributionByProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='product.ListValidStoresForDistributionByProductResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='product.ListValidStoresForDistributionByProductResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5853,
  serialized_end=5967,
)


_ATTRIBUTEREGIONPRODUCT = _descriptor.Descriptor(
  name='AttributeRegionProduct',
  full_name='product.AttributeRegionProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='product.AttributeRegionProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='product.AttributeRegionProduct.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='product.AttributeRegionProduct.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='product.AttributeRegionProduct.product_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product', full_name='product.AttributeRegionProduct.product', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_type', full_name='product.AttributeRegionProduct.inventory_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='per_thousand_period', full_name='product.AttributeRegionProduct.per_thousand_period', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='replenish_method', full_name='product.AttributeRegionProduct.replenish_method', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='safe_stock_method', full_name='product.AttributeRegionProduct.safe_stock_method', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unfreeze_day', full_name='product.AttributeRegionProduct.unfreeze_day', index=9,
      number=10, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cycle_type', full_name='product.AttributeRegionProduct.cycle_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_adjust', full_name='product.AttributeRegionProduct.allow_adjust', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_stocktake', full_name='product.AttributeRegionProduct.allow_stocktake', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_transfer', full_name='product.AttributeRegionProduct.allow_transfer', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='product.AttributeRegionProduct.extends', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_self_picking', full_name='product.AttributeRegionProduct.allow_self_picking', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5970,
  serialized_end=6388,
)


_DISTRIBUTIONREGIONPRODUCT = _descriptor.Descriptor(
  name='DistributionRegionProduct',
  full_name='product.DistributionRegionProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='product.DistributionRegionProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='product.DistributionRegionProduct.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='product.DistributionRegionProduct.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='product.DistributionRegionProduct.product_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product', full_name='product.DistributionRegionProduct.product', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='product.DistributionRegionProduct.type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_center_id', full_name='product.DistributionRegionProduct.distribution_center_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='product.DistributionRegionProduct.unit_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_number', full_name='product.DistributionRegionProduct.min_number', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_number', full_name='product.DistributionRegionProduct.max_number', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_number', full_name='product.DistributionRegionProduct.increment_number', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='planned_arrival_days', full_name='product.DistributionRegionProduct.planned_arrival_days', index=11,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='circle_type', full_name='product.DistributionRegionProduct.circle_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='product.DistributionRegionProduct.start_date', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='interval_days', full_name='product.DistributionRegionProduct.interval_days', index=14,
      number=15, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_main_order', full_name='product.DistributionRegionProduct.allow_main_order', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='product.DistributionRegionProduct.extends', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_deviation_min', full_name='product.DistributionRegionProduct.receiving_deviation_min', index=17,
      number=18, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_deviation_max', full_name='product.DistributionRegionProduct.receiving_deviation_max', index=18,
      number=19, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cycles', full_name='product.DistributionRegionProduct.cycles', index=19,
      number=20, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_deviation', full_name='product.DistributionRegionProduct.allow_deviation', index=20,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='product.DistributionRegionProduct.store_id', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='product.DistributionRegionProduct.distr_type', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cycle_coef', full_name='product.DistributionRegionProduct.cycle_coef', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='low_value_monthly', full_name='product.DistributionRegionProduct.low_value_monthly', index=24,
      number=25, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='product.DistributionRegionProduct.updated', index=25,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6391,
  serialized_end=7039,
)


_PURCHASEREGIONPRODUCT = _descriptor.Descriptor(
  name='PurchaseRegionProduct',
  full_name='product.PurchaseRegionProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='product.PurchaseRegionProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='product.PurchaseRegionProduct.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='product.PurchaseRegionProduct.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='product.PurchaseRegionProduct.product_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product', full_name='product.PurchaseRegionProduct.product', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='product.PurchaseRegionProduct.type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_id', full_name='product.PurchaseRegionProduct.vendor_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='product.PurchaseRegionProduct.unit_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_number', full_name='product.PurchaseRegionProduct.min_number', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_number', full_name='product.PurchaseRegionProduct.max_number', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_number', full_name='product.PurchaseRegionProduct.increment_number', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='planned_arrival_days', full_name='product.PurchaseRegionProduct.planned_arrival_days', index=11,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='circle_type', full_name='product.PurchaseRegionProduct.circle_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='product.PurchaseRegionProduct.start_date', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='interval_days', full_name='product.PurchaseRegionProduct.interval_days', index=14,
      number=15, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_price', full_name='product.PurchaseRegionProduct.purchase_price', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_tax', full_name='product.PurchaseRegionProduct.purchase_tax', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='product.PurchaseRegionProduct.extends', index=17,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_deviation_min', full_name='product.PurchaseRegionProduct.purchase_deviation_min', index=18,
      number=19, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_deviation_max', full_name='product.PurchaseRegionProduct.purchase_deviation_max', index=19,
      number=20, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cycles', full_name='product.PurchaseRegionProduct.cycles', index=20,
      number=21, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_main_order', full_name='product.PurchaseRegionProduct.allow_main_order', index=21,
      number=22, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_deviation', full_name='product.PurchaseRegionProduct.allow_deviation', index=22,
      number=23, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='product.PurchaseRegionProduct.store_id', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cycle_coef', full_name='product.PurchaseRegionProduct.cycle_coef', index=24,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_deviation_min', full_name='product.PurchaseRegionProduct.receive_deviation_min', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_deviation_max', full_name='product.PurchaseRegionProduct.receive_deviation_max', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='product.PurchaseRegionProduct.updated', index=27,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7042,
  serialized_end=7732,
)


_VALIDPRODUCTFORDISTRIBUTION = _descriptor.Descriptor(
  name='ValidProductForDistribution',
  full_name='product.ValidProductForDistribution',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='product.ValidProductForDistribution.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='product.ValidProductForDistribution.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='product.ValidProductForDistribution.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='product.ValidProductForDistribution.product_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product', full_name='product.ValidProductForDistribution.product', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='product.ValidProductForDistribution.type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_center_id', full_name='product.ValidProductForDistribution.distribution_center_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='product.ValidProductForDistribution.unit_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_number', full_name='product.ValidProductForDistribution.min_number', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_number', full_name='product.ValidProductForDistribution.max_number', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_number', full_name='product.ValidProductForDistribution.increment_number', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='planned_arrival_days', full_name='product.ValidProductForDistribution.planned_arrival_days', index=11,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='circle_type', full_name='product.ValidProductForDistribution.circle_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='product.ValidProductForDistribution.start_date', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='interval_days', full_name='product.ValidProductForDistribution.interval_days', index=14,
      number=15, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_main_order', full_name='product.ValidProductForDistribution.allow_main_order', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='product.ValidProductForDistribution.extends', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_deviation_min', full_name='product.ValidProductForDistribution.receiving_deviation_min', index=17,
      number=18, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_deviation_max', full_name='product.ValidProductForDistribution.receiving_deviation_max', index=18,
      number=19, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cycles', full_name='product.ValidProductForDistribution.cycles', index=19,
      number=20, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_deviation', full_name='product.ValidProductForDistribution.allow_deviation', index=20,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_deviation_min', full_name='product.ValidProductForDistribution.purchase_deviation_min', index=21,
      number=22, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_deviation_max', full_name='product.ValidProductForDistribution.purchase_deviation_max', index=22,
      number=23, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_id', full_name='product.ValidProductForDistribution.vendor_id', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='product.ValidProductForDistribution.distr_type', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_price', full_name='product.ValidProductForDistribution.purchase_price', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_tax', full_name='product.ValidProductForDistribution.purchase_tax', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cycle_coef', full_name='product.ValidProductForDistribution.cycle_coef', index=27,
      number=28, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='product.ValidProductForDistribution.updated', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='product.ValidProductForDistribution.created', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7735,
  serialized_end=8486,
)


_VALIDSTOREFORDISTRIBUTION = _descriptor.Descriptor(
  name='ValidStoreForDistribution',
  full_name='product.ValidStoreForDistribution',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='product.ValidStoreForDistribution.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='product.ValidStoreForDistribution.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='product.ValidStoreForDistribution.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='product.ValidStoreForDistribution.product_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product', full_name='product.ValidStoreForDistribution.product', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='product.ValidStoreForDistribution.type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_center_id', full_name='product.ValidStoreForDistribution.distribution_center_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='product.ValidStoreForDistribution.unit_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_number', full_name='product.ValidStoreForDistribution.min_number', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_number', full_name='product.ValidStoreForDistribution.max_number', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='increment_number', full_name='product.ValidStoreForDistribution.increment_number', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='planned_arrival_days', full_name='product.ValidStoreForDistribution.planned_arrival_days', index=11,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='circle_type', full_name='product.ValidStoreForDistribution.circle_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='product.ValidStoreForDistribution.start_date', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='interval_days', full_name='product.ValidStoreForDistribution.interval_days', index=14,
      number=15, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_main_order', full_name='product.ValidStoreForDistribution.allow_main_order', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='product.ValidStoreForDistribution.extends', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_deviation_min', full_name='product.ValidStoreForDistribution.receiving_deviation_min', index=17,
      number=18, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_deviation_max', full_name='product.ValidStoreForDistribution.receiving_deviation_max', index=18,
      number=19, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cycles', full_name='product.ValidStoreForDistribution.cycles', index=19,
      number=20, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_deviation', full_name='product.ValidStoreForDistribution.allow_deviation', index=20,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_deviation_min', full_name='product.ValidStoreForDistribution.purchase_deviation_min', index=21,
      number=22, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_deviation_max', full_name='product.ValidStoreForDistribution.purchase_deviation_max', index=22,
      number=23, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendor_id', full_name='product.ValidStoreForDistribution.vendor_id', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distr_type', full_name='product.ValidStoreForDistribution.distr_type', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='product.ValidStoreForDistribution.store_id', index=25,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_price', full_name='product.ValidStoreForDistribution.purchase_price', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_tax', full_name='product.ValidStoreForDistribution.purchase_tax', index=27,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cycle_coef', full_name='product.ValidStoreForDistribution.cycle_coef', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8489,
  serialized_end=9222,
)


_GETUNITBYIDREQUEST = _descriptor.Descriptor(
  name='GetUnitByIdRequest',
  full_name='product.GetUnitByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='product.GetUnitByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='product.GetUnitByIdRequest.return_fields', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9224,
  serialized_end=9279,
)


_LISTUNITREQUEST = _descriptor.Descriptor(
  name='ListUnitRequest',
  full_name='product.ListUnitRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='product.ListUnitRequest.return_fields', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='product.ListUnitRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='product.ListUnitRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='product.ListUnitRequest.sort', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='product.ListUnitRequest.order', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='product.ListUnitRequest.include_total', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='product.ListUnitRequest.search', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='product.ListUnitRequest.search_fields', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='product.ListUnitRequest.ids', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='product.ListUnitRequest.filters', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='product.ListUnitRequest.lan', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9282,
  serialized_end=9512,
)


_LISTUNITRESPONSE = _descriptor.Descriptor(
  name='ListUnitResponse',
  full_name='product.ListUnitResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='product.ListUnitResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='product.ListUnitResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9514,
  serialized_end=9576,
)


_GETPRODUCTCATEGORYBYIDREQUEST = _descriptor.Descriptor(
  name='GetProductCategoryByIdRequest',
  full_name='product.GetProductCategoryByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='product.GetProductCategoryByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='product.GetProductCategoryByIdRequest.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='product.GetProductCategoryByIdRequest.return_fields', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='product.GetProductCategoryByIdRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9578,
  serialized_end=9671,
)


_LISTPRODUCTCATEGORYREQUEST = _descriptor.Descriptor(
  name='ListProductCategoryRequest',
  full_name='product.ListProductCategoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='product.ListProductCategoryRequest.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='product.ListProductCategoryRequest.return_fields', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='product.ListProductCategoryRequest.limit', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='product.ListProductCategoryRequest.offset', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='product.ListProductCategoryRequest.sort', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='product.ListProductCategoryRequest.order', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='product.ListProductCategoryRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='product.ListProductCategoryRequest.search', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='product.ListProductCategoryRequest.search_fields', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='product.ListProductCategoryRequest.ids', index=9,
      number=10, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='product.ListProductCategoryRequest.filters', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='product.ListProductCategoryRequest.lan', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9674,
  serialized_end=9929,
)


_LISTPRODUCTCATEGORYRESPONSE = _descriptor.Descriptor(
  name='ListProductCategoryResponse',
  full_name='product.ListProductCategoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='product.ListProductCategoryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='product.ListProductCategoryResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9931,
  serialized_end=10008,
)


_CATEGORY = _descriptor.Descriptor(
  name='Category',
  full_name='product.Category',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='product.Category.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='product.Category.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='product.Category.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='product.Category.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='product.Category.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='product.Category.updated', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='parent_id', full_name='product.Category.parent_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10010,
  serialized_end=10134,
)


_CYCLE = _descriptor.Descriptor(
  name='Cycle',
  full_name='product.Cycle',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='planned_arrival_days', full_name='product.Cycle.planned_arrival_days', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date', full_name='product.Cycle.order_date', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10136,
  serialized_end=10193,
)


_BATCHUPDATEPRODUCTSPUREQUEST = _descriptor.Descriptor(
  name='BatchUpdateProductSpuRequest',
  full_name='product.BatchUpdateProductSpuRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='spu_fields', full_name='product.BatchUpdateProductSpuRequest.spu_fields', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='overwrite', full_name='product.BatchUpdateProductSpuRequest.overwrite', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='product.BatchUpdateProductSpuRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10195,
  serialized_end=10297,
)


_SPUFIELDS = _descriptor.Descriptor(
  name='SpuFields',
  full_name='product.SpuFields',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='product.SpuFields.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='product.SpuFields.fields', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10299,
  serialized_end=10371,
)


_BATCHUPDATEPRODUCTSPURESPONSE = _descriptor.Descriptor(
  name='BatchUpdateProductSpuResponse',
  full_name='product.BatchUpdateProductSpuResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='product.BatchUpdateProductSpuResponse.request_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10373,
  serialized_end=10424,
)


_BATCHUPDATEPRODUCTTOPPINGREQUEST = _descriptor.Descriptor(
  name='BatchUpdateProductToppingRequest',
  full_name='product.BatchUpdateProductToppingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='top_fields', full_name='product.BatchUpdateProductToppingRequest.top_fields', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='overwrite', full_name='product.BatchUpdateProductToppingRequest.overwrite', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='product.BatchUpdateProductToppingRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10426,
  serialized_end=10532,
)


_TOPFIELDS = _descriptor.Descriptor(
  name='TopFields',
  full_name='product.TopFields',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='product.TopFields.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='product.TopFields.fields', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10534,
  serialized_end=10606,
)


_BATCHUPDATEPRODUCTTOPPINGRESPONSE = _descriptor.Descriptor(
  name='BatchUpdateProductToppingResponse',
  full_name='product.BatchUpdateProductToppingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='product.BatchUpdateProductToppingResponse.request_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10608,
  serialized_end=10663,
)

_QUERYBATCHTASKSTATUSRESPONSE.fields_by_name['master'].message_type = _TASKSTATUS
_QUERYBATCHTASKSTATUSRESPONSE.fields_by_name['items'].message_type = _TASKSTATUS
_BATCHUPDATEPRODUCTMARKETREGIONREQUEST.fields_by_name['product_price'].message_type = _PRODUCTPRICE
_PRODUCTPRICE.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_UPDATEPRODUCTSKUREQUEST.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_NEWPRODUCTSKUREQUEST.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTPRODUCTREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTPRODUCTREQUEST.fields_by_name['relation_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTPRODUCTRESPONSE.fields_by_name['rows'].message_type = _PRODUCT
_PRODUCT.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_PRODUCT.fields_by_name['extend_code'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_PRODUCT.fields_by_name['units'].message_type = _UNIT
_LISTATTRIBUTEREGIONPRODUCTBYSTOREREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTATTRIBUTEREGIONPRODUCTBYSTOREREQUEST.fields_by_name['product_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTATTRIBUTEREGIONPRODUCTBYSTOREREQUEST.fields_by_name['product_relation_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTDISTRIBUTIONREGIONPRODUCTBYSTOREREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTDISTRIBUTIONREGIONPRODUCTBYSTOREREQUEST.fields_by_name['product_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTDISTRIBUTIONREGIONPRODUCTBYSTOREREQUEST.fields_by_name['product_relation_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTPURCHASEREGIONPRODUCTBYSTOREREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTPURCHASEREGIONPRODUCTBYSTOREREQUEST.fields_by_name['product_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTPURCHASEREGIONPRODUCTBYSTOREREQUEST.fields_by_name['product_relation_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTVALIDPRODUCTSFORDISTRIBUTIONBYSTOREREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTVALIDPRODUCTSFORDISTRIBUTIONBYSTOREREQUEST.fields_by_name['product_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTVALIDPRODUCTSFORDISTRIBUTIONBYSTOREREQUEST.fields_by_name['order_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTVALIDPRODUCTSFORDISTRIBUTIONBYSTOREREQUEST.fields_by_name['product_relation_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTVALIDSTORESFORDISTRIBUTIONBYPRODUCTREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTVALIDSTORESFORDISTRIBUTIONBYPRODUCTREQUEST.fields_by_name['store_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTVALIDSTORESFORDISTRIBUTIONBYPRODUCTREQUEST.fields_by_name['store_relation_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTVALIDSTORESFORDISTRIBUTIONBYPRODUCTREQUEST.fields_by_name['product_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTVALIDSTORESFORDISTRIBUTIONBYPRODUCTREQUEST.fields_by_name['product_relation_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTVALIDSTORESFORDISTRIBUTIONBYPRODUCTREQUEST.fields_by_name['order_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTATTRIBUTEREGIONPRODUCTBYSTORERESPONSE.fields_by_name['rows'].message_type = _ATTRIBUTEREGIONPRODUCT
_LISTDISTRIBUTIONREGIONPRODUCTBYSTORERESPONSE.fields_by_name['rows'].message_type = _DISTRIBUTIONREGIONPRODUCT
_LISTPURCHASEREGIONPRODUCTBYSTORERESPONSE.fields_by_name['rows'].message_type = _PURCHASEREGIONPRODUCT
_LISTVALIDPRODUCTSFORDISTRIBUTIONBYSTORERESPONSE.fields_by_name['rows'].message_type = _VALIDPRODUCTFORDISTRIBUTION
_LISTVALIDSTORESFORDISTRIBUTIONBYPRODUCTRESPONSE.fields_by_name['rows'].message_type = _VALIDSTOREFORDISTRIBUTION
_ATTRIBUTEREGIONPRODUCT.fields_by_name['product'].message_type = _PRODUCT
_ATTRIBUTEREGIONPRODUCT.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_DISTRIBUTIONREGIONPRODUCT.fields_by_name['product'].message_type = _PRODUCT
_DISTRIBUTIONREGIONPRODUCT.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_DISTRIBUTIONREGIONPRODUCT.fields_by_name['cycles'].message_type = _CYCLE
_PURCHASEREGIONPRODUCT.fields_by_name['product'].message_type = _PRODUCT
_PURCHASEREGIONPRODUCT.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_PURCHASEREGIONPRODUCT.fields_by_name['cycles'].message_type = _CYCLE
_VALIDPRODUCTFORDISTRIBUTION.fields_by_name['product'].message_type = _PRODUCT
_VALIDPRODUCTFORDISTRIBUTION.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_VALIDPRODUCTFORDISTRIBUTION.fields_by_name['cycles'].message_type = _CYCLE
_VALIDSTOREFORDISTRIBUTION.fields_by_name['product'].message_type = _PRODUCT
_VALIDSTOREFORDISTRIBUTION.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_VALIDSTOREFORDISTRIBUTION.fields_by_name['cycles'].message_type = _CYCLE
_LISTUNITREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTUNITRESPONSE.fields_by_name['rows'].message_type = _UNIT
_LISTPRODUCTCATEGORYREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTPRODUCTCATEGORYRESPONSE.fields_by_name['rows'].message_type = _CATEGORY
_BATCHUPDATEPRODUCTSPUREQUEST.fields_by_name['spu_fields'].message_type = _SPUFIELDS
_SPUFIELDS.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_BATCHUPDATEPRODUCTTOPPINGREQUEST.fields_by_name['top_fields'].message_type = _TOPFIELDS
_TOPFIELDS.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
DESCRIPTOR.message_types_by_name['RedoBatchTaskStatusRequest'] = _REDOBATCHTASKSTATUSREQUEST
DESCRIPTOR.message_types_by_name['RedoBatchTaskStatusResponse'] = _REDOBATCHTASKSTATUSRESPONSE
DESCRIPTOR.message_types_by_name['QueryBatchTaskStatusRequest'] = _QUERYBATCHTASKSTATUSREQUEST
DESCRIPTOR.message_types_by_name['QueryBatchTaskStatusResponse'] = _QUERYBATCHTASKSTATUSRESPONSE
DESCRIPTOR.message_types_by_name['TaskStatus'] = _TASKSTATUS
DESCRIPTOR.message_types_by_name['BatchUpdateProductMarketRegionRequest'] = _BATCHUPDATEPRODUCTMARKETREGIONREQUEST
DESCRIPTOR.message_types_by_name['ProductPrice'] = _PRODUCTPRICE
DESCRIPTOR.message_types_by_name['BatchUpdateProductMarketRegionResponse'] = _BATCHUPDATEPRODUCTMARKETREGIONRESPONSE
DESCRIPTOR.message_types_by_name['UpdateProductSkuRequest'] = _UPDATEPRODUCTSKUREQUEST
DESCRIPTOR.message_types_by_name['UpdateProductSkuResponse'] = _UPDATEPRODUCTSKURESPONSE
DESCRIPTOR.message_types_by_name['NewProductSkuRequest'] = _NEWPRODUCTSKUREQUEST
DESCRIPTOR.message_types_by_name['NewProductSkuResponse'] = _NEWPRODUCTSKURESPONSE
DESCRIPTOR.message_types_by_name['GetProductByIdRequest'] = _GETPRODUCTBYIDREQUEST
DESCRIPTOR.message_types_by_name['ListProductRequest'] = _LISTPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['ListProductResponse'] = _LISTPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['Product'] = _PRODUCT
DESCRIPTOR.message_types_by_name['Unit'] = _UNIT
DESCRIPTOR.message_types_by_name['DefaultResponse'] = _DEFAULTRESPONSE
DESCRIPTOR.message_types_by_name['ListAttributeRegionProductByStoreRequest'] = _LISTATTRIBUTEREGIONPRODUCTBYSTOREREQUEST
DESCRIPTOR.message_types_by_name['ListDistributionRegionProductByStoreRequest'] = _LISTDISTRIBUTIONREGIONPRODUCTBYSTOREREQUEST
DESCRIPTOR.message_types_by_name['ListPurchaseRegionProductByStoreRequest'] = _LISTPURCHASEREGIONPRODUCTBYSTOREREQUEST
DESCRIPTOR.message_types_by_name['ListValidProductsForDistributionByStoreRequest'] = _LISTVALIDPRODUCTSFORDISTRIBUTIONBYSTOREREQUEST
DESCRIPTOR.message_types_by_name['ListValidStoresForDistributionByProductRequest'] = _LISTVALIDSTORESFORDISTRIBUTIONBYPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['ListAttributeRegionProductByStoreResponse'] = _LISTATTRIBUTEREGIONPRODUCTBYSTORERESPONSE
DESCRIPTOR.message_types_by_name['ListDistributionRegionProductByStoreResponse'] = _LISTDISTRIBUTIONREGIONPRODUCTBYSTORERESPONSE
DESCRIPTOR.message_types_by_name['ListPurchaseRegionProductByStoreResponse'] = _LISTPURCHASEREGIONPRODUCTBYSTORERESPONSE
DESCRIPTOR.message_types_by_name['ListValidProductsForDistributionByStoreResponse'] = _LISTVALIDPRODUCTSFORDISTRIBUTIONBYSTORERESPONSE
DESCRIPTOR.message_types_by_name['ListValidStoresForDistributionByProductResponse'] = _LISTVALIDSTORESFORDISTRIBUTIONBYPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['AttributeRegionProduct'] = _ATTRIBUTEREGIONPRODUCT
DESCRIPTOR.message_types_by_name['DistributionRegionProduct'] = _DISTRIBUTIONREGIONPRODUCT
DESCRIPTOR.message_types_by_name['PurchaseRegionProduct'] = _PURCHASEREGIONPRODUCT
DESCRIPTOR.message_types_by_name['ValidProductForDistribution'] = _VALIDPRODUCTFORDISTRIBUTION
DESCRIPTOR.message_types_by_name['ValidStoreForDistribution'] = _VALIDSTOREFORDISTRIBUTION
DESCRIPTOR.message_types_by_name['GetUnitByIdRequest'] = _GETUNITBYIDREQUEST
DESCRIPTOR.message_types_by_name['ListUnitRequest'] = _LISTUNITREQUEST
DESCRIPTOR.message_types_by_name['ListUnitResponse'] = _LISTUNITRESPONSE
DESCRIPTOR.message_types_by_name['GetProductCategoryByIdRequest'] = _GETPRODUCTCATEGORYBYIDREQUEST
DESCRIPTOR.message_types_by_name['ListProductCategoryRequest'] = _LISTPRODUCTCATEGORYREQUEST
DESCRIPTOR.message_types_by_name['ListProductCategoryResponse'] = _LISTPRODUCTCATEGORYRESPONSE
DESCRIPTOR.message_types_by_name['Category'] = _CATEGORY
DESCRIPTOR.message_types_by_name['Cycle'] = _CYCLE
DESCRIPTOR.message_types_by_name['BatchUpdateProductSpuRequest'] = _BATCHUPDATEPRODUCTSPUREQUEST
DESCRIPTOR.message_types_by_name['SpuFields'] = _SPUFIELDS
DESCRIPTOR.message_types_by_name['BatchUpdateProductSpuResponse'] = _BATCHUPDATEPRODUCTSPURESPONSE
DESCRIPTOR.message_types_by_name['BatchUpdateProductToppingRequest'] = _BATCHUPDATEPRODUCTTOPPINGREQUEST
DESCRIPTOR.message_types_by_name['TopFields'] = _TOPFIELDS
DESCRIPTOR.message_types_by_name['BatchUpdateProductToppingResponse'] = _BATCHUPDATEPRODUCTTOPPINGRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

RedoBatchTaskStatusRequest = _reflection.GeneratedProtocolMessageType('RedoBatchTaskStatusRequest', (_message.Message,), dict(
  DESCRIPTOR = _REDOBATCHTASKSTATUSREQUEST,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.RedoBatchTaskStatusRequest)
  ))
_sym_db.RegisterMessage(RedoBatchTaskStatusRequest)

RedoBatchTaskStatusResponse = _reflection.GeneratedProtocolMessageType('RedoBatchTaskStatusResponse', (_message.Message,), dict(
  DESCRIPTOR = _REDOBATCHTASKSTATUSRESPONSE,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.RedoBatchTaskStatusResponse)
  ))
_sym_db.RegisterMessage(RedoBatchTaskStatusResponse)

QueryBatchTaskStatusRequest = _reflection.GeneratedProtocolMessageType('QueryBatchTaskStatusRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYBATCHTASKSTATUSREQUEST,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.QueryBatchTaskStatusRequest)
  ))
_sym_db.RegisterMessage(QueryBatchTaskStatusRequest)

QueryBatchTaskStatusResponse = _reflection.GeneratedProtocolMessageType('QueryBatchTaskStatusResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYBATCHTASKSTATUSRESPONSE,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.QueryBatchTaskStatusResponse)
  ))
_sym_db.RegisterMessage(QueryBatchTaskStatusResponse)

TaskStatus = _reflection.GeneratedProtocolMessageType('TaskStatus', (_message.Message,), dict(
  DESCRIPTOR = _TASKSTATUS,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.TaskStatus)
  ))
_sym_db.RegisterMessage(TaskStatus)

BatchUpdateProductMarketRegionRequest = _reflection.GeneratedProtocolMessageType('BatchUpdateProductMarketRegionRequest', (_message.Message,), dict(
  DESCRIPTOR = _BATCHUPDATEPRODUCTMARKETREGIONREQUEST,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.BatchUpdateProductMarketRegionRequest)
  ))
_sym_db.RegisterMessage(BatchUpdateProductMarketRegionRequest)

ProductPrice = _reflection.GeneratedProtocolMessageType('ProductPrice', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTPRICE,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.ProductPrice)
  ))
_sym_db.RegisterMessage(ProductPrice)

BatchUpdateProductMarketRegionResponse = _reflection.GeneratedProtocolMessageType('BatchUpdateProductMarketRegionResponse', (_message.Message,), dict(
  DESCRIPTOR = _BATCHUPDATEPRODUCTMARKETREGIONRESPONSE,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.BatchUpdateProductMarketRegionResponse)
  ))
_sym_db.RegisterMessage(BatchUpdateProductMarketRegionResponse)

UpdateProductSkuRequest = _reflection.GeneratedProtocolMessageType('UpdateProductSkuRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPRODUCTSKUREQUEST,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.UpdateProductSkuRequest)
  ))
_sym_db.RegisterMessage(UpdateProductSkuRequest)

UpdateProductSkuResponse = _reflection.GeneratedProtocolMessageType('UpdateProductSkuResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPRODUCTSKURESPONSE,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.UpdateProductSkuResponse)
  ))
_sym_db.RegisterMessage(UpdateProductSkuResponse)

NewProductSkuRequest = _reflection.GeneratedProtocolMessageType('NewProductSkuRequest', (_message.Message,), dict(
  DESCRIPTOR = _NEWPRODUCTSKUREQUEST,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.NewProductSkuRequest)
  ))
_sym_db.RegisterMessage(NewProductSkuRequest)

NewProductSkuResponse = _reflection.GeneratedProtocolMessageType('NewProductSkuResponse', (_message.Message,), dict(
  DESCRIPTOR = _NEWPRODUCTSKURESPONSE,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.NewProductSkuResponse)
  ))
_sym_db.RegisterMessage(NewProductSkuResponse)

GetProductByIdRequest = _reflection.GeneratedProtocolMessageType('GetProductByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTBYIDREQUEST,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.GetProductByIdRequest)
  ))
_sym_db.RegisterMessage(GetProductByIdRequest)

ListProductRequest = _reflection.GeneratedProtocolMessageType('ListProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTPRODUCTREQUEST,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.ListProductRequest)
  ))
_sym_db.RegisterMessage(ListProductRequest)

ListProductResponse = _reflection.GeneratedProtocolMessageType('ListProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTPRODUCTRESPONSE,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.ListProductResponse)
  ))
_sym_db.RegisterMessage(ListProductResponse)

Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCT,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.Product)
  ))
_sym_db.RegisterMessage(Product)

Unit = _reflection.GeneratedProtocolMessageType('Unit', (_message.Message,), dict(
  DESCRIPTOR = _UNIT,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.Unit)
  ))
_sym_db.RegisterMessage(Unit)

DefaultResponse = _reflection.GeneratedProtocolMessageType('DefaultResponse', (_message.Message,), dict(
  DESCRIPTOR = _DEFAULTRESPONSE,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.DefaultResponse)
  ))
_sym_db.RegisterMessage(DefaultResponse)

ListAttributeRegionProductByStoreRequest = _reflection.GeneratedProtocolMessageType('ListAttributeRegionProductByStoreRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTATTRIBUTEREGIONPRODUCTBYSTOREREQUEST,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.ListAttributeRegionProductByStoreRequest)
  ))
_sym_db.RegisterMessage(ListAttributeRegionProductByStoreRequest)

ListDistributionRegionProductByStoreRequest = _reflection.GeneratedProtocolMessageType('ListDistributionRegionProductByStoreRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTDISTRIBUTIONREGIONPRODUCTBYSTOREREQUEST,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.ListDistributionRegionProductByStoreRequest)
  ))
_sym_db.RegisterMessage(ListDistributionRegionProductByStoreRequest)

ListPurchaseRegionProductByStoreRequest = _reflection.GeneratedProtocolMessageType('ListPurchaseRegionProductByStoreRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTPURCHASEREGIONPRODUCTBYSTOREREQUEST,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.ListPurchaseRegionProductByStoreRequest)
  ))
_sym_db.RegisterMessage(ListPurchaseRegionProductByStoreRequest)

ListValidProductsForDistributionByStoreRequest = _reflection.GeneratedProtocolMessageType('ListValidProductsForDistributionByStoreRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTVALIDPRODUCTSFORDISTRIBUTIONBYSTOREREQUEST,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.ListValidProductsForDistributionByStoreRequest)
  ))
_sym_db.RegisterMessage(ListValidProductsForDistributionByStoreRequest)

ListValidStoresForDistributionByProductRequest = _reflection.GeneratedProtocolMessageType('ListValidStoresForDistributionByProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTVALIDSTORESFORDISTRIBUTIONBYPRODUCTREQUEST,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.ListValidStoresForDistributionByProductRequest)
  ))
_sym_db.RegisterMessage(ListValidStoresForDistributionByProductRequest)

ListAttributeRegionProductByStoreResponse = _reflection.GeneratedProtocolMessageType('ListAttributeRegionProductByStoreResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTATTRIBUTEREGIONPRODUCTBYSTORERESPONSE,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.ListAttributeRegionProductByStoreResponse)
  ))
_sym_db.RegisterMessage(ListAttributeRegionProductByStoreResponse)

ListDistributionRegionProductByStoreResponse = _reflection.GeneratedProtocolMessageType('ListDistributionRegionProductByStoreResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTDISTRIBUTIONREGIONPRODUCTBYSTORERESPONSE,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.ListDistributionRegionProductByStoreResponse)
  ))
_sym_db.RegisterMessage(ListDistributionRegionProductByStoreResponse)

ListPurchaseRegionProductByStoreResponse = _reflection.GeneratedProtocolMessageType('ListPurchaseRegionProductByStoreResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTPURCHASEREGIONPRODUCTBYSTORERESPONSE,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.ListPurchaseRegionProductByStoreResponse)
  ))
_sym_db.RegisterMessage(ListPurchaseRegionProductByStoreResponse)

ListValidProductsForDistributionByStoreResponse = _reflection.GeneratedProtocolMessageType('ListValidProductsForDistributionByStoreResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTVALIDPRODUCTSFORDISTRIBUTIONBYSTORERESPONSE,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.ListValidProductsForDistributionByStoreResponse)
  ))
_sym_db.RegisterMessage(ListValidProductsForDistributionByStoreResponse)

ListValidStoresForDistributionByProductResponse = _reflection.GeneratedProtocolMessageType('ListValidStoresForDistributionByProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTVALIDSTORESFORDISTRIBUTIONBYPRODUCTRESPONSE,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.ListValidStoresForDistributionByProductResponse)
  ))
_sym_db.RegisterMessage(ListValidStoresForDistributionByProductResponse)

AttributeRegionProduct = _reflection.GeneratedProtocolMessageType('AttributeRegionProduct', (_message.Message,), dict(
  DESCRIPTOR = _ATTRIBUTEREGIONPRODUCT,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.AttributeRegionProduct)
  ))
_sym_db.RegisterMessage(AttributeRegionProduct)

DistributionRegionProduct = _reflection.GeneratedProtocolMessageType('DistributionRegionProduct', (_message.Message,), dict(
  DESCRIPTOR = _DISTRIBUTIONREGIONPRODUCT,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.DistributionRegionProduct)
  ))
_sym_db.RegisterMessage(DistributionRegionProduct)

PurchaseRegionProduct = _reflection.GeneratedProtocolMessageType('PurchaseRegionProduct', (_message.Message,), dict(
  DESCRIPTOR = _PURCHASEREGIONPRODUCT,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.PurchaseRegionProduct)
  ))
_sym_db.RegisterMessage(PurchaseRegionProduct)

ValidProductForDistribution = _reflection.GeneratedProtocolMessageType('ValidProductForDistribution', (_message.Message,), dict(
  DESCRIPTOR = _VALIDPRODUCTFORDISTRIBUTION,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.ValidProductForDistribution)
  ))
_sym_db.RegisterMessage(ValidProductForDistribution)

ValidStoreForDistribution = _reflection.GeneratedProtocolMessageType('ValidStoreForDistribution', (_message.Message,), dict(
  DESCRIPTOR = _VALIDSTOREFORDISTRIBUTION,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.ValidStoreForDistribution)
  ))
_sym_db.RegisterMessage(ValidStoreForDistribution)

GetUnitByIdRequest = _reflection.GeneratedProtocolMessageType('GetUnitByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETUNITBYIDREQUEST,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.GetUnitByIdRequest)
  ))
_sym_db.RegisterMessage(GetUnitByIdRequest)

ListUnitRequest = _reflection.GeneratedProtocolMessageType('ListUnitRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTUNITREQUEST,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.ListUnitRequest)
  ))
_sym_db.RegisterMessage(ListUnitRequest)

ListUnitResponse = _reflection.GeneratedProtocolMessageType('ListUnitResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTUNITRESPONSE,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.ListUnitResponse)
  ))
_sym_db.RegisterMessage(ListUnitResponse)

GetProductCategoryByIdRequest = _reflection.GeneratedProtocolMessageType('GetProductCategoryByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTCATEGORYBYIDREQUEST,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.GetProductCategoryByIdRequest)
  ))
_sym_db.RegisterMessage(GetProductCategoryByIdRequest)

ListProductCategoryRequest = _reflection.GeneratedProtocolMessageType('ListProductCategoryRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTPRODUCTCATEGORYREQUEST,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.ListProductCategoryRequest)
  ))
_sym_db.RegisterMessage(ListProductCategoryRequest)

ListProductCategoryResponse = _reflection.GeneratedProtocolMessageType('ListProductCategoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTPRODUCTCATEGORYRESPONSE,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.ListProductCategoryResponse)
  ))
_sym_db.RegisterMessage(ListProductCategoryResponse)

Category = _reflection.GeneratedProtocolMessageType('Category', (_message.Message,), dict(
  DESCRIPTOR = _CATEGORY,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.Category)
  ))
_sym_db.RegisterMessage(Category)

Cycle = _reflection.GeneratedProtocolMessageType('Cycle', (_message.Message,), dict(
  DESCRIPTOR = _CYCLE,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.Cycle)
  ))
_sym_db.RegisterMessage(Cycle)

BatchUpdateProductSpuRequest = _reflection.GeneratedProtocolMessageType('BatchUpdateProductSpuRequest', (_message.Message,), dict(
  DESCRIPTOR = _BATCHUPDATEPRODUCTSPUREQUEST,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.BatchUpdateProductSpuRequest)
  ))
_sym_db.RegisterMessage(BatchUpdateProductSpuRequest)

SpuFields = _reflection.GeneratedProtocolMessageType('SpuFields', (_message.Message,), dict(
  DESCRIPTOR = _SPUFIELDS,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.SpuFields)
  ))
_sym_db.RegisterMessage(SpuFields)

BatchUpdateProductSpuResponse = _reflection.GeneratedProtocolMessageType('BatchUpdateProductSpuResponse', (_message.Message,), dict(
  DESCRIPTOR = _BATCHUPDATEPRODUCTSPURESPONSE,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.BatchUpdateProductSpuResponse)
  ))
_sym_db.RegisterMessage(BatchUpdateProductSpuResponse)

BatchUpdateProductToppingRequest = _reflection.GeneratedProtocolMessageType('BatchUpdateProductToppingRequest', (_message.Message,), dict(
  DESCRIPTOR = _BATCHUPDATEPRODUCTTOPPINGREQUEST,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.BatchUpdateProductToppingRequest)
  ))
_sym_db.RegisterMessage(BatchUpdateProductToppingRequest)

TopFields = _reflection.GeneratedProtocolMessageType('TopFields', (_message.Message,), dict(
  DESCRIPTOR = _TOPFIELDS,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.TopFields)
  ))
_sym_db.RegisterMessage(TopFields)

BatchUpdateProductToppingResponse = _reflection.GeneratedProtocolMessageType('BatchUpdateProductToppingResponse', (_message.Message,), dict(
  DESCRIPTOR = _BATCHUPDATEPRODUCTTOPPINGRESPONSE,
  __module__ = 'metadata.product_pb2'
  # @@protoc_insertion_point(class_scope:product.BatchUpdateProductToppingResponse)
  ))
_sym_db.RegisterMessage(BatchUpdateProductToppingResponse)



_PRODUCTSERVICE = _descriptor.ServiceDescriptor(
  name='ProductService',
  full_name='product.ProductService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=10666,
  serialized_end=13512,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetProductById',
    full_name='product.ProductService.GetProductById',
    index=0,
    containing_service=None,
    input_type=_GETPRODUCTBYIDREQUEST,
    output_type=_PRODUCT,
    serialized_options=_b('\202\323\344\223\002\034\022\032/api/v2/product/by/id/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListProduct',
    full_name='product.ProductService.ListProduct',
    index=1,
    containing_service=None,
    input_type=_LISTPRODUCTREQUEST,
    output_type=_LISTPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\002\032\"\025/api/v2/product/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListAttributeRegionProductByStoreId',
    full_name='product.ProductService.ListAttributeRegionProductByStoreId',
    index=2,
    containing_service=None,
    input_type=_LISTATTRIBUTEREGIONPRODUCTBYSTOREREQUEST,
    output_type=_LISTATTRIBUTEREGIONPRODUCTBYSTORERESPONSE,
    serialized_options=_b('\202\323\344\223\002<\"7/api/v2/product/in/attribute-region/by/store/{store_id}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListDistributionRegionProductByStoreId',
    full_name='product.ProductService.ListDistributionRegionProductByStoreId',
    index=3,
    containing_service=None,
    input_type=_LISTDISTRIBUTIONREGIONPRODUCTBYSTOREREQUEST,
    output_type=_LISTDISTRIBUTIONREGIONPRODUCTBYSTORERESPONSE,
    serialized_options=_b('\202\323\344\223\002?\":/api/v2/product/in/distribution-region/by/store/{store_id}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListPurchaseRegionProductByStoreId',
    full_name='product.ProductService.ListPurchaseRegionProductByStoreId',
    index=4,
    containing_service=None,
    input_type=_LISTPURCHASEREGIONPRODUCTBYSTOREREQUEST,
    output_type=_LISTPURCHASEREGIONPRODUCTBYSTORERESPONSE,
    serialized_options=_b('\202\323\344\223\002;\"6/api/v2/product/in/purchase-region/by/store/{store_id}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListValidProductsForDistributionByStoreId',
    full_name='product.ProductService.ListValidProductsForDistributionByStoreId',
    index=5,
    containing_service=None,
    input_type=_LISTVALIDPRODUCTSFORDISTRIBUTIONBYSTOREREQUEST,
    output_type=_LISTVALIDPRODUCTSFORDISTRIBUTIONBYSTORERESPONSE,
    serialized_options=_b('\202\323\344\223\002?\":/api/v2/product/valid/for/distribution/by/store/{store_id}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListValidStoresForDistributionByProduct',
    full_name='product.ProductService.ListValidStoresForDistributionByProduct',
    index=6,
    containing_service=None,
    input_type=_LISTVALIDSTORESFORDISTRIBUTIONBYPRODUCTREQUEST,
    output_type=_LISTVALIDSTORESFORDISTRIBUTIONBYPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\0026\"1/api/v2/product/valid/for/distribution/by/product:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetUnitById',
    full_name='product.ProductService.GetUnitById',
    index=7,
    containing_service=None,
    input_type=_GETUNITBYIDREQUEST,
    output_type=_UNIT,
    serialized_options=_b('\202\323\344\223\002!\022\037/api/v2/product/unit/by/id/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListUnit',
    full_name='product.ProductService.ListUnit',
    index=8,
    containing_service=None,
    input_type=_LISTUNITREQUEST,
    output_type=_LISTUNITRESPONSE,
    serialized_options=_b('\202\323\344\223\002\037\"\032/api/v2/product/unit/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetProductCategoryById',
    full_name='product.ProductService.GetProductCategoryById',
    index=9,
    containing_service=None,
    input_type=_GETPRODUCTCATEGORYBYIDREQUEST,
    output_type=_CATEGORY,
    serialized_options=_b('\202\323\344\223\002%\022#/api/v2/product/category/by/id/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListProductCategory',
    full_name='product.ProductService.ListProductCategory',
    index=10,
    containing_service=None,
    input_type=_LISTPRODUCTCATEGORYREQUEST,
    output_type=_LISTPRODUCTCATEGORYRESPONSE,
    serialized_options=_b('\202\323\344\223\002#\"\036/api/v2/product/category/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='NewProductSku',
    full_name='product.ProductService.NewProductSku',
    index=11,
    containing_service=None,
    input_type=_NEWPRODUCTSKUREQUEST,
    output_type=_NEWPRODUCTSKURESPONSE,
    serialized_options=_b('\202\323\344\223\002\034\"\027/api/v2/product/sku/add:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateProductSku',
    full_name='product.ProductService.UpdateProductSku',
    index=12,
    containing_service=None,
    input_type=_UPDATEPRODUCTSKUREQUEST,
    output_type=_UPDATEPRODUCTSKURESPONSE,
    serialized_options=_b('\202\323\344\223\002\037\"\032/api/v2/product/sku/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='BatchUpdateProductMarketRegion',
    full_name='product.ProductService.BatchUpdateProductMarketRegion',
    index=13,
    containing_service=None,
    input_type=_BATCHUPDATEPRODUCTMARKETREGIONREQUEST,
    output_type=_BATCHUPDATEPRODUCTMARKETREGIONRESPONSE,
    serialized_options=_b('\202\323\344\223\002/\"*/api/v2/product/market_region/batch/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryBatchTaskStatus',
    full_name='product.ProductService.QueryBatchTaskStatus',
    index=14,
    containing_service=None,
    input_type=_QUERYBATCHTASKSTATUSREQUEST,
    output_type=_QUERYBATCHTASKSTATUSRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\022%/api/v2/product/batch_task/by/id/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='RedoBatchTaskStatus',
    full_name='product.ProductService.RedoBatchTaskStatus',
    index=15,
    containing_service=None,
    input_type=_REDOBATCHTASKSTATUSREQUEST,
    output_type=_REDOBATCHTASKSTATUSRESPONSE,
    serialized_options=_b('\202\323\344\223\002$\"\037/api/v2/product/batch_task/redo:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='BatchUpdateProductSpu',
    full_name='product.ProductService.BatchUpdateProductSpu',
    index=16,
    containing_service=None,
    input_type=_BATCHUPDATEPRODUCTSPUREQUEST,
    output_type=_BATCHUPDATEPRODUCTSPURESPONSE,
    serialized_options=_b('\202\323\344\223\002-\"(/api/v2/product/product_spu/batch/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='BatchUpdateProductTopping',
    full_name='product.ProductService.BatchUpdateProductTopping',
    index=17,
    containing_service=None,
    input_type=_BATCHUPDATEPRODUCTTOPPINGREQUEST,
    output_type=_BATCHUPDATEPRODUCTTOPPINGRESPONSE,
    serialized_options=_b('\202\323\344\223\002)\"$/api/v2/product/topping/batch/update:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_PRODUCTSERVICE)

DESCRIPTOR.services_by_name['ProductService'] = _PRODUCTSERVICE

# @@protoc_insertion_point(module_scope)
