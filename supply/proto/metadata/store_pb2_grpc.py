# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from metadata import store_pb2 as metadata_dot_store__pb2


class StoreServiceStub(object):
  """StoreService 用于从metadata获取门店信息数据
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetStoreById = channel.unary_unary(
        '/store.StoreService/GetStoreById',
        request_serializer=metadata_dot_store__pb2.GetStoreByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_store__pb2.Store.FromString,
        )
    self.ListStore = channel.unary_unary(
        '/store.StoreService/ListStore',
        request_serializer=metadata_dot_store__pb2.ListStoreRequest.SerializeToString,
        response_deserializer=metadata_dot_store__pb2.ListStoreResponse.FromString,
        )
    self.GetStoreDataScope = channel.unary_unary(
        '/store.StoreService/GetStoreDataScope',
        request_serializer=metadata_dot_store__pb2.GetStoreDataScopeRequest.SerializeToString,
        response_deserializer=metadata_dot_store__pb2.StoreDataScope.FromString,
        )
    self.GetStoreDataScopeList = channel.unary_unary(
        '/store.StoreService/GetStoreDataScopeList',
        request_serializer=metadata_dot_store__pb2.GetStoreDataScopeListReq.SerializeToString,
        response_deserializer=metadata_dot_store__pb2.GetStoreDataScopeListRes.FromString,
        )
    self.GetDistributionCenterById = channel.unary_unary(
        '/store.StoreService/GetDistributionCenterById',
        request_serializer=metadata_dot_store__pb2.GetDistributionCenterByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_store__pb2.DistributionCenter.FromString,
        )
    self.ListDistributionCenter = channel.unary_unary(
        '/store.StoreService/ListDistributionCenter',
        request_serializer=metadata_dot_store__pb2.ListDistributionCenterRequest.SerializeToString,
        response_deserializer=metadata_dot_store__pb2.ListDistributionCenterResponse.FromString,
        )
    self.GetCompanyById = channel.unary_unary(
        '/store.StoreService/GetCompanyById',
        request_serializer=metadata_dot_store__pb2.GetCompanyByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_store__pb2.Company.FromString,
        )
    self.ListCompany = channel.unary_unary(
        '/store.StoreService/ListCompany',
        request_serializer=metadata_dot_store__pb2.ListCompanyRequest.SerializeToString,
        response_deserializer=metadata_dot_store__pb2.ListCompanyResponse.FromString,
        )
    self.GetVendorById = channel.unary_unary(
        '/store.StoreService/GetVendorById',
        request_serializer=metadata_dot_store__pb2.GetVendorByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_store__pb2.Vendor.FromString,
        )
    self.ListVendor = channel.unary_unary(
        '/store.StoreService/ListVendor',
        request_serializer=metadata_dot_store__pb2.ListVendorRequest.SerializeToString,
        response_deserializer=metadata_dot_store__pb2.ListVendorResponse.FromString,
        )
    self.GetWarehouseScope = channel.unary_unary(
        '/store.StoreService/GetWarehouseScope',
        request_serializer=metadata_dot_store__pb2.GetWarehouseScopeRequest.SerializeToString,
        response_deserializer=metadata_dot_store__pb2.WarehouseScope.FromString,
        )
    self.GetMachiningCenterScopeDetail = channel.unary_unary(
        '/store.StoreService/GetMachiningCenterScopeDetail',
        request_serializer=metadata_dot_store__pb2.GetMachiningCenterScopeRequest.SerializeToString,
        response_deserializer=metadata_dot_store__pb2.MachiningCenterDetailScope.FromString,
        )


class StoreServiceServicer(object):
  """StoreService 用于从metadata获取门店信息数据
  """

  def GetStoreById(self, request, context):
    """根据id获取门店
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListStore(self, request, context):
    """查询 Store 列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStoreDataScope(self, request, context):
    """获取门店数据权限
    full_access=true 全部权限
    full_access=false，则只有相关store ids的权限
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStoreDataScopeList(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDistributionCenterById(self, request, context):
    """根据id获取DistributionCenter
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListDistributionCenter(self, request, context):
    """查询 DistributionCenter 列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetCompanyById(self, request, context):
    """根据id获取Company
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListCompany(self, request, context):
    """查询 Company 列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetVendorById(self, request, context):
    """根据id获取vendor
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListVendor(self, request, context):
    """查询 vendor 列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetWarehouseScope(self, request, context):
    """获取仓库权限
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetMachiningCenterScopeDetail(self, request, context):
    """获取加工中心权限
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_StoreServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetStoreById': grpc.unary_unary_rpc_method_handler(
          servicer.GetStoreById,
          request_deserializer=metadata_dot_store__pb2.GetStoreByIdRequest.FromString,
          response_serializer=metadata_dot_store__pb2.Store.SerializeToString,
      ),
      'ListStore': grpc.unary_unary_rpc_method_handler(
          servicer.ListStore,
          request_deserializer=metadata_dot_store__pb2.ListStoreRequest.FromString,
          response_serializer=metadata_dot_store__pb2.ListStoreResponse.SerializeToString,
      ),
      'GetStoreDataScope': grpc.unary_unary_rpc_method_handler(
          servicer.GetStoreDataScope,
          request_deserializer=metadata_dot_store__pb2.GetStoreDataScopeRequest.FromString,
          response_serializer=metadata_dot_store__pb2.StoreDataScope.SerializeToString,
      ),
      'GetStoreDataScopeList': grpc.unary_unary_rpc_method_handler(
          servicer.GetStoreDataScopeList,
          request_deserializer=metadata_dot_store__pb2.GetStoreDataScopeListReq.FromString,
          response_serializer=metadata_dot_store__pb2.GetStoreDataScopeListRes.SerializeToString,
      ),
      'GetDistributionCenterById': grpc.unary_unary_rpc_method_handler(
          servicer.GetDistributionCenterById,
          request_deserializer=metadata_dot_store__pb2.GetDistributionCenterByIdRequest.FromString,
          response_serializer=metadata_dot_store__pb2.DistributionCenter.SerializeToString,
      ),
      'ListDistributionCenter': grpc.unary_unary_rpc_method_handler(
          servicer.ListDistributionCenter,
          request_deserializer=metadata_dot_store__pb2.ListDistributionCenterRequest.FromString,
          response_serializer=metadata_dot_store__pb2.ListDistributionCenterResponse.SerializeToString,
      ),
      'GetCompanyById': grpc.unary_unary_rpc_method_handler(
          servicer.GetCompanyById,
          request_deserializer=metadata_dot_store__pb2.GetCompanyByIdRequest.FromString,
          response_serializer=metadata_dot_store__pb2.Company.SerializeToString,
      ),
      'ListCompany': grpc.unary_unary_rpc_method_handler(
          servicer.ListCompany,
          request_deserializer=metadata_dot_store__pb2.ListCompanyRequest.FromString,
          response_serializer=metadata_dot_store__pb2.ListCompanyResponse.SerializeToString,
      ),
      'GetVendorById': grpc.unary_unary_rpc_method_handler(
          servicer.GetVendorById,
          request_deserializer=metadata_dot_store__pb2.GetVendorByIdRequest.FromString,
          response_serializer=metadata_dot_store__pb2.Vendor.SerializeToString,
      ),
      'ListVendor': grpc.unary_unary_rpc_method_handler(
          servicer.ListVendor,
          request_deserializer=metadata_dot_store__pb2.ListVendorRequest.FromString,
          response_serializer=metadata_dot_store__pb2.ListVendorResponse.SerializeToString,
      ),
      'GetWarehouseScope': grpc.unary_unary_rpc_method_handler(
          servicer.GetWarehouseScope,
          request_deserializer=metadata_dot_store__pb2.GetWarehouseScopeRequest.FromString,
          response_serializer=metadata_dot_store__pb2.WarehouseScope.SerializeToString,
      ),
      'GetMachiningCenterScopeDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetMachiningCenterScopeDetail,
          request_deserializer=metadata_dot_store__pb2.GetMachiningCenterScopeRequest.FromString,
          response_serializer=metadata_dot_store__pb2.MachiningCenterDetailScope.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'store.StoreService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
