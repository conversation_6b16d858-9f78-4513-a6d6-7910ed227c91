# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from metadata import region_pb2 as metadata_dot_region__pb2


class RegionServiceStub(object):
  """RegionService 用于从metadata获取门店信息数据
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetRegionById = channel.unary_unary(
        '/region.RegionService/GetRegionById',
        request_serializer=metadata_dot_region__pb2.GetRegionByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_region__pb2.Region.FromString,
        )
    self.ListRegion = channel.unary_unary(
        '/region.RegionService/ListRegion',
        request_serializer=metadata_dot_region__pb2.ListRegionRequest.SerializeToString,
        response_deserializer=metadata_dot_region__pb2.ListRegionResponse.FromString,
        )


class RegionServiceServicer(object):
  """RegionService 用于从metadata获取门店信息数据
  """

  def GetRegionById(self, request, context):
    """根据id获取区域
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListRegion(self, request, context):
    """查询 Product 列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_RegionServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetRegionById': grpc.unary_unary_rpc_method_handler(
          servicer.GetRegionById,
          request_deserializer=metadata_dot_region__pb2.GetRegionByIdRequest.FromString,
          response_serializer=metadata_dot_region__pb2.Region.SerializeToString,
      ),
      'ListRegion': grpc.unary_unary_rpc_method_handler(
          servicer.ListRegion,
          request_deserializer=metadata_dot_region__pb2.ListRegionRequest.FromString,
          response_serializer=metadata_dot_region__pb2.ListRegionResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'region.RegionService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
