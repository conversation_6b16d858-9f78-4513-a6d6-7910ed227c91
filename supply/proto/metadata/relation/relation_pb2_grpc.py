# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from metadata.relation import relation_pb2 as metadata_dot_relation_dot_relation__pb2


class RelationServiceStub(object):
  """RelationService 用于维护主档Entity Relation数据
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.AddEntityRelation = channel.unary_unary(
        '/relation.RelationService/AddEntityRelation',
        request_serializer=metadata_dot_relation_dot_relation__pb2.AddRelationRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.EntityRelation.FromString,
        )
    self.UpdateEntityRelation = channel.unary_unary(
        '/relation.RelationService/UpdateEntityRelation',
        request_serializer=metadata_dot_relation_dot_relation__pb2.UpdateRelaitonRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.EntityRelation.FromString,
        )
    self.SyncUpdateEntityRelation = channel.unary_unary(
        '/relation.RelationService/SyncUpdateEntityRelation',
        request_serializer=metadata_dot_relation_dot_relation__pb2.SyncRelationRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.EntityRelation.FromString,
        )
    self.GetEntityRelationById = channel.unary_unary(
        '/relation.RelationService/GetEntityRelationById',
        request_serializer=metadata_dot_relation_dot_relation__pb2.GetRelationByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.EntityRelation.FromString,
        )
    self.ListEntityRelation = channel.unary_unary(
        '/relation.RelationService/ListEntityRelation',
        request_serializer=metadata_dot_relation_dot_relation__pb2.ListRelationRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.ListEntityRelationResponse.FromString,
        )
    self.UpdateEntityRelationState = channel.unary_unary(
        '/relation.RelationService/UpdateEntityRelationState',
        request_serializer=metadata_dot_relation_dot_relation__pb2.UpdateEntityRelationStateRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.EntityRelation.FromString,
        )
    self.ProcessEntityRelationPendingChanges = channel.unary_unary(
        '/relation.RelationService/ProcessEntityRelationPendingChanges',
        request_serializer=metadata_dot_relation_dot_relation__pb2.ProcessEntityRelationPendingChangesRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.EntityRelation.FromString,
        )
    self.ListCrossedEntityRelation = channel.unary_unary(
        '/relation.RelationService/ListCrossedEntityRelation',
        request_serializer=metadata_dot_relation_dot_relation__pb2.ListCrossedRelationRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.ListEntityRelationResponse.FromString,
        )
    self.ListRuleRelation = channel.unary_unary(
        '/relation.RelationService/ListRuleRelation',
        request_serializer=metadata_dot_relation_dot_relation__pb2.ListRuleRelationReq.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.ListEntityRelationResponse.FromString,
        )
    self.CreateEntityRelationTaskFromPendingChanges = channel.unary_unary(
        '/relation.RelationService/CreateEntityRelationTaskFromPendingChanges',
        request_serializer=metadata_dot_relation_dot_relation__pb2.CreateEntityRelationTaskFromPendingChangesRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTask.FromString,
        )
    self.UpdateEntityRelationTask = channel.unary_unary(
        '/relation.RelationService/UpdateEntityRelationTask',
        request_serializer=metadata_dot_relation_dot_relation__pb2.UpdateEntityRelationTaskRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTask.FromString,
        )
    self.UpdateEntityRelationTaskStatus = channel.unary_unary(
        '/relation.RelationService/UpdateEntityRelationTaskStatus',
        request_serializer=metadata_dot_relation_dot_relation__pb2.UpdateEntityRelationTaskStatusRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTask.FromString,
        )
    self.DeleteEntityRelationTask = channel.unary_unary(
        '/relation.RelationService/DeleteEntityRelationTask',
        request_serializer=metadata_dot_relation_dot_relation__pb2.DeleteEntityRelationTaskRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTask.FromString,
        )
    self.GetEntityRelationTaskById = channel.unary_unary(
        '/relation.RelationService/GetEntityRelationTaskById',
        request_serializer=metadata_dot_relation_dot_relation__pb2.GetEntityRelationTaskByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTask.FromString,
        )
    self.ListEntityRelationTask = channel.unary_unary(
        '/relation.RelationService/ListEntityRelationTask',
        request_serializer=metadata_dot_relation_dot_relation__pb2.ListEntityRelationTaskRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.ListEntityRelationTaskResponse.FromString,
        )
    self.ListCrossedRelationBySource = channel.unary_unary(
        '/relation.RelationService/ListCrossedRelationBySource',
        request_serializer=metadata_dot_relation_dot_relation__pb2.ListCrossedRelationBySourceRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.ListEntityRelationResponse.FromString,
        )
    self.AddEntityRelationTemplate = channel.unary_unary(
        '/relation.RelationService/AddEntityRelationTemplate',
        request_serializer=metadata_dot_relation_dot_relation__pb2.AddRelationTemplateRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTemplate.FromString,
        )
    self.UpdateEntityRelationTemplate = channel.unary_unary(
        '/relation.RelationService/UpdateEntityRelationTemplate',
        request_serializer=metadata_dot_relation_dot_relation__pb2.UpdateRelaitonTemplateRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTemplate.FromString,
        )
    self.GetEntityRelationTemplateById = channel.unary_unary(
        '/relation.RelationService/GetEntityRelationTemplateById',
        request_serializer=metadata_dot_relation_dot_relation__pb2.GetRelationTemplateByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTemplate.FromString,
        )
    self.ListEntityRelationTemplate = channel.unary_unary(
        '/relation.RelationService/ListEntityRelationTemplate',
        request_serializer=metadata_dot_relation_dot_relation__pb2.ListRelationTemplateRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.ListEntityRelationTemplateResponse.FromString,
        )
    self.UpdateEntityRelationTemplateState = channel.unary_unary(
        '/relation.RelationService/UpdateEntityRelationTemplateState',
        request_serializer=metadata_dot_relation_dot_relation__pb2.UpdateEntityRelationTemplateStateRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTemplate.FromString,
        )
    self.ProcessEntityRelationTemplatePendingChanges = channel.unary_unary(
        '/relation.RelationService/ProcessEntityRelationTemplatePendingChanges',
        request_serializer=metadata_dot_relation_dot_relation__pb2.ProcessEntityRelationTemplatePendingChangesRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTemplate.FromString,
        )
    self.ListRegionProductByStore = channel.unary_unary(
        '/relation.RelationService/ListRegionProductByStore',
        request_serializer=metadata_dot_relation_dot_relation__pb2.ListRegionProductByStoreRequest.SerializeToString,
        response_deserializer=metadata_dot_relation_dot_relation__pb2.ListRegionProductByStoreResponse.FromString,
        )


class RelationServiceServicer(object):
  """RelationService 用于维护主档Entity Relation数据
  """

  def AddEntityRelation(self, request, context):
    """添加Entity Relation
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateEntityRelation(self, request, context):
    """修改Entity Relation
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SyncUpdateEntityRelation(self, request, context):
    """同步Entity Relation
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetEntityRelationById(self, request, context):
    """根据id获取Entity Relation
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListEntityRelation(self, request, context):
    """查询 Entity Relation 列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateEntityRelationState(self, request, context):
    """修改Entity Relation数据状态
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ProcessEntityRelationPendingChanges(self, request, context):
    """Apply或者Cancel pending changes
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListCrossedEntityRelation(self, request, context):
    """查询其它relation来查询Entity Relation列表，如通过门店id来查询它所关联的市场区域的商品价格
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListRuleRelation(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateEntityRelationTaskFromPendingChanges(self, request, context):
    """基于变更创建一个task
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateEntityRelationTask(self, request, context):
    """修改Entity Task
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateEntityRelationTaskStatus(self, request, context):
    """修改Entity Task 状态
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteEntityRelationTask(self, request, context):
    """删除Entity Task 状态
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetEntityRelationTaskById(self, request, context):
    """根据id获取Entity Task
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListEntityRelationTask(self, request, context):
    """查询 Entity Task 列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListCrossedRelationBySource(self, request, context):
    """通过起始关联关系查询其它交叉关联的Entity Relation列表，如通过商品id来查门店所关联的市场区域的商品价格
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddEntityRelationTemplate(self, request, context):
    """添加Entity Relation Template
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateEntityRelationTemplate(self, request, context):
    """修改Entity Relation Template
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetEntityRelationTemplateById(self, request, context):
    """根据id获取Entity Relation Template
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListEntityRelationTemplate(self, request, context):
    """查询 Entity Relation Template 列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateEntityRelationTemplateState(self, request, context):
    """修改Entity Relation Template数据状态
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ProcessEntityRelationTemplatePendingChanges(self, request, context):
    """Apply或者Cancel pending changes
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListRegionProductByStore(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_RelationServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'AddEntityRelation': grpc.unary_unary_rpc_method_handler(
          servicer.AddEntityRelation,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.AddRelationRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.EntityRelation.SerializeToString,
      ),
      'UpdateEntityRelation': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateEntityRelation,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.UpdateRelaitonRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.EntityRelation.SerializeToString,
      ),
      'SyncUpdateEntityRelation': grpc.unary_unary_rpc_method_handler(
          servicer.SyncUpdateEntityRelation,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.SyncRelationRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.EntityRelation.SerializeToString,
      ),
      'GetEntityRelationById': grpc.unary_unary_rpc_method_handler(
          servicer.GetEntityRelationById,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.GetRelationByIdRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.EntityRelation.SerializeToString,
      ),
      'ListEntityRelation': grpc.unary_unary_rpc_method_handler(
          servicer.ListEntityRelation,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.ListRelationRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.ListEntityRelationResponse.SerializeToString,
      ),
      'UpdateEntityRelationState': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateEntityRelationState,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.UpdateEntityRelationStateRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.EntityRelation.SerializeToString,
      ),
      'ProcessEntityRelationPendingChanges': grpc.unary_unary_rpc_method_handler(
          servicer.ProcessEntityRelationPendingChanges,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.ProcessEntityRelationPendingChangesRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.EntityRelation.SerializeToString,
      ),
      'ListCrossedEntityRelation': grpc.unary_unary_rpc_method_handler(
          servicer.ListCrossedEntityRelation,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.ListCrossedRelationRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.ListEntityRelationResponse.SerializeToString,
      ),
      'ListRuleRelation': grpc.unary_unary_rpc_method_handler(
          servicer.ListRuleRelation,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.ListRuleRelationReq.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.ListEntityRelationResponse.SerializeToString,
      ),
      'CreateEntityRelationTaskFromPendingChanges': grpc.unary_unary_rpc_method_handler(
          servicer.CreateEntityRelationTaskFromPendingChanges,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.CreateEntityRelationTaskFromPendingChangesRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTask.SerializeToString,
      ),
      'UpdateEntityRelationTask': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateEntityRelationTask,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.UpdateEntityRelationTaskRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTask.SerializeToString,
      ),
      'UpdateEntityRelationTaskStatus': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateEntityRelationTaskStatus,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.UpdateEntityRelationTaskStatusRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTask.SerializeToString,
      ),
      'DeleteEntityRelationTask': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteEntityRelationTask,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.DeleteEntityRelationTaskRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTask.SerializeToString,
      ),
      'GetEntityRelationTaskById': grpc.unary_unary_rpc_method_handler(
          servicer.GetEntityRelationTaskById,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.GetEntityRelationTaskByIdRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTask.SerializeToString,
      ),
      'ListEntityRelationTask': grpc.unary_unary_rpc_method_handler(
          servicer.ListEntityRelationTask,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.ListEntityRelationTaskRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.ListEntityRelationTaskResponse.SerializeToString,
      ),
      'ListCrossedRelationBySource': grpc.unary_unary_rpc_method_handler(
          servicer.ListCrossedRelationBySource,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.ListCrossedRelationBySourceRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.ListEntityRelationResponse.SerializeToString,
      ),
      'AddEntityRelationTemplate': grpc.unary_unary_rpc_method_handler(
          servicer.AddEntityRelationTemplate,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.AddRelationTemplateRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTemplate.SerializeToString,
      ),
      'UpdateEntityRelationTemplate': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateEntityRelationTemplate,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.UpdateRelaitonTemplateRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTemplate.SerializeToString,
      ),
      'GetEntityRelationTemplateById': grpc.unary_unary_rpc_method_handler(
          servicer.GetEntityRelationTemplateById,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.GetRelationTemplateByIdRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTemplate.SerializeToString,
      ),
      'ListEntityRelationTemplate': grpc.unary_unary_rpc_method_handler(
          servicer.ListEntityRelationTemplate,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.ListRelationTemplateRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.ListEntityRelationTemplateResponse.SerializeToString,
      ),
      'UpdateEntityRelationTemplateState': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateEntityRelationTemplateState,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.UpdateEntityRelationTemplateStateRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTemplate.SerializeToString,
      ),
      'ProcessEntityRelationTemplatePendingChanges': grpc.unary_unary_rpc_method_handler(
          servicer.ProcessEntityRelationTemplatePendingChanges,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.ProcessEntityRelationTemplatePendingChangesRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.EntityRelationTemplate.SerializeToString,
      ),
      'ListRegionProductByStore': grpc.unary_unary_rpc_method_handler(
          servicer.ListRegionProductByStore,
          request_deserializer=metadata_dot_relation_dot_relation__pb2.ListRegionProductByStoreRequest.FromString,
          response_serializer=metadata_dot_relation_dot_relation__pb2.ListRegionProductByStoreResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'relation.RelationService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
