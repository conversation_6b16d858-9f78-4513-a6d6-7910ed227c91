syntax = "proto3";
package relation;
import "google/api/annotations.proto";
import "google/protobuf/struct.proto";

// RelationService 用于维护主档Entity Relation数据
service RelationService {
    // 添加Entity Relation
    rpc AddEntityRelation (AddRelationRequest) returns (EntityRelation) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/add"
        body: "*"
        };
    }
    // 修改Entity Relation
    rpc UpdateEntityRelation (UpdateRelaitonRequest) returns (EntityRelation) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/update"
        body: "*"
        };
    }
    // 同步Entity Relation
    rpc SyncUpdateEntityRelation (SyncRelationRequest) returns (EntityRelation) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/sync/update"
        body: "*"
        };
    }

    // 根据id获取Entity Relation
    rpc GetEntityRelationById (GetRelationByIdRequest) returns (EntityRelation) {
        option (google.api.http) = {
        get: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/by/id/{id}"
        };
    }
    // 查询 Entity Relation 列表
    rpc ListEntityRelation (ListRelationRequest) returns (ListEntityRelationResponse) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/query"
        body: "*"
        };
    }
    // 修改Entity Relation数据状态
    rpc UpdateEntityRelationState (UpdateEntityRelationStateRequest) returns (EntityRelation) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/state/update"
        body: "*"
        };
    }
    // Apply或者Cancel pending changes
    rpc ProcessEntityRelationPendingChanges (ProcessEntityRelationPendingChangesRequest) returns (EntityRelation) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/changes/{action}"
        body: "*"
        };
    }
    // 查询其它relation来查询Entity Relation列表，如通过门店id来查询它所关联的市场区域的商品价格
    rpc ListCrossedEntityRelation (ListCrossedRelationRequest) returns (ListEntityRelationResponse) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/query/by/relation"
        body: "*"
        };
    }
    rpc ListRuleRelation (ListRuleRelationReq) returns (ListEntityRelationResponse) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{source_schema_name}/relation/rule/{schema_relation_name}/query"
        body: "*"
        };
    }

    // 基于变更创建一个task
    rpc CreateEntityRelationTaskFromPendingChanges (CreateEntityRelationTaskFromPendingChangesRequest) returns (EntityRelationTask) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/changes/to/task"
        body: "*"
        };
    }
    // 修改Entity Task
    rpc UpdateEntityRelationTask (UpdateEntityRelationTaskRequest) returns (EntityRelationTask) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/task/update"
        body: "*"
        };
    }
    // 修改Entity Task 状态
    rpc UpdateEntityRelationTaskStatus (UpdateEntityRelationTaskStatusRequest) returns (EntityRelationTask) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/task/update/status"
        body: "*"
        };
    }
    // 删除Entity Task 状态
    rpc DeleteEntityRelationTask (DeleteEntityRelationTaskRequest) returns (EntityRelationTask) {
        option (google.api.http) = {
        delete: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/task/by/id/{id}"
        };
    }
    // 根据id获取Entity Task
    rpc GetEntityRelationTaskById (GetEntityRelationTaskByIdRequest) returns (EntityRelationTask) {
        option (google.api.http) = {
        get: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/task/by/id/{id}"
        };
    }
    // 查询 Entity Task 列表
    rpc ListEntityRelationTask (ListEntityRelationTaskRequest) returns (ListEntityRelationTaskResponse) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/task/query"
        body: "*"
        };
    }
    // 通过起始关联关系查询其它交叉关联的Entity Relation列表，如通过商品id来查门店所关联的市场区域的商品价格
    rpc ListCrossedRelationBySource (ListCrossedRelationBySourceRequest) returns (ListEntityRelationResponse) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{source_schema_name}/relation/{source_schema_relation_name}/query/by/relation/reverse"
        body: "*"
        };
    }
    // 添加Entity Relation Template
    rpc AddEntityRelationTemplate (AddRelationTemplateRequest) returns (EntityRelationTemplate) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/template/add"
        body: "*"
        };
    }
    // 修改Entity Relation Template
    rpc UpdateEntityRelationTemplate (UpdateRelaitonTemplateRequest) returns (EntityRelationTemplate) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/template/update"
        body: "*"
        };
    }
    // 根据id获取Entity Relation Template
    rpc GetEntityRelationTemplateById (GetRelationTemplateByIdRequest) returns (EntityRelationTemplate) {
        option (google.api.http) = {
        get: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/template/by/id/{id}"
        };
    }
    // 查询 Entity Relation Template 列表
    rpc ListEntityRelationTemplate (ListRelationTemplateRequest) returns (ListEntityRelationTemplateResponse) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/template/query"
        body: "*"
        };
    }
    // 修改Entity Relation Template数据状态
    rpc UpdateEntityRelationTemplateState (UpdateEntityRelationTemplateStateRequest) returns (EntityRelationTemplate) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/template/state/update"
        body: "*"
        };
    }
    // Apply或者Cancel pending changes
    rpc ProcessEntityRelationTemplatePendingChanges (ProcessEntityRelationTemplatePendingChangesRequest) returns (EntityRelationTemplate) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/template/changes/{action}"
        body: "*"
        };
    }

    rpc ListRegionProductByStore (ListRegionProductByStoreRequest) returns (ListRegionProductByStoreResponse) {
        option (google.api.http) = {
        post: "/api/v2/product/in/{}/by/store/{store_id}"
        body: "*"
        };
    }
}
message AddRelationRequest {
    // 发起关联的schema 名称
    string source_schema_name = 1;
    // 发起关联的entity id
    uint64 source_entity_id = 2;
    // 被关联的entity id
    uint64 target_entity_id = 3;
    // 关系名称
    string schema_relation_name = 4;
    // 创建之后enable数据
    bool auto_enable = 5;
    // Entity字段json
    google.protobuf.Struct fields = 6;
    // 当前使用的语言
    string lan = 7;
    //已有id的relation
    string id = 8;
}

message SyncRelationRequest {
    // 数据id
    uint64 id = 1;
    string source_schema_name = 2;
    // 发起关联的entity id
    uint64 source_entity_id = 3;
    // 被关联的entity id
    uint64 target_entity_id = 4;
    // 关系名称
    string schema_relation_name = 5;
    // 创建之后enable数据
    bool auto_enable = 6;
    // Entity字段json
    google.protobuf.Struct fields = 7;
    // 当前使用的语言
    string lan = 8;
}

message UpdateRelaitonRequest {
    // 数据id
    uint64 id = 1;
    // 发起关联的schema 名称
    string source_schema_name = 2;
    // 关系名称
    string schema_relation_name = 3;
    // 对数据状态是ENABLED的数据自动apply change
    bool auto_apply = 4;
    // Entity字段json
    google.protobuf.Struct fields = 5;
    // 当前使用的语言
    string lan = 6;

}
message GetRelationByIdRequest {
    // 数据id
    uint64 id = 1;
    // 发起关联的schema 名称
    string source_schema_name = 2;
    // 关系名称
    string schema_relation_name = 3;
    // 是否包含数据状态
    bool include_state = 4;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 5;
    // include_pending_changes=true时, 如果相关记录包含pending的修改属性,
    // 则会获取修改属性attach到返回记录中pengding_changes字段
    bool include_pending_changes = 6;
    // include_pending_record=true时, 如果相关记录包含pending的修改属性,
    // 则会获取修改的属性合并到返回记录的一个副本, 从而将这个副本（最新数据）attach到返回记录的pending_record字段
    bool include_pending_record = 7;
    // 当前使用的语言
    string lan = 8;
    // 是否包含所有本地化信息
    bool include_all_localizations = 9;

}
message ListRelationRequest {
    // 指定要返回相关数据状态的记录, 默认只返回enabled, 多个状态用逗号隔开，
    // 如state=draft,disabled; state=all时返回所有数据状态的记录
    string state = 1;
    // 发起关联的schema 名称
    string source_schema_name = 2;
    // 关系名称
    string schema_relation_name = 3;
    // 是否包含数据状态
    bool include_state = 4;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 5;
    // include_pending_changes=true时, 如果相关记录包含pending的修改属性,
    // 则会获取修改属性attach到返回记录中pengding_changes字段
    bool include_pending_changes = 6;
    // include_pending_record=true时, 如果相关记录包含pending的修改属性,
    // 则会获取修改的属性合并到返回记录的一个副本, 从而将这个副本（最新数据）attach到返回记录的pending_record字段
    bool include_pending_record = 7;
    // 发起关联的Entity id list
    repeated uint64 source_entity_ids = 8;
    // 被关联的Entity id list
    repeated uint64 target_entity_ids = 9;
    // 额外返回source entity的字段
    repeated string include_source_entity_fields = 10;
    // 额外返回source entity 的 relation
    repeated string include_source_entity_relations = 11;
    // 分页大小
    int32 limit = 12;
    // 跳过行数
    int32 offset = 13;
    // 排序字段
    string sort = 14;
    // 排序顺序
    string order = 15;
    // 返回总条数
    bool include_total = 16;
    // 按id列表查询
    repeated uint64 ids = 17;
    // include_target_entity_parent_relations=true时, 同时返回target_entity_ids所有父级节点所关联的关系
    bool include_target_entity_parent_relations = 18;
    // override_target_entity_parent_relations=true时, 底层target_entity所关联的关系会覆盖其父级节点所关联的关系
    bool override_target_entity_parent_relations = 19;
    // 当前使用的语言
    string lan = 20;
    // 是否包含所有本地化信息
    bool include_all_localizations = 21;
    // 返回 state=DRAFT 或 state=ENABLED且有pending record的记录
    bool is_request = 22;

}

message ListCrossedRelationRequest {
    // 发起关联的schema 名称
    string source_schema_name = 1;
    // 关系名称
    string schema_relation_name = 2;
    // 用于查询的SchemaName
    string by_schema_name = 3;
    // 用于查询的SchemaRelationName
    string by_schema_relation_name = 4;
    // 用于查询的EntityId
    uint64 by_entity_id = 5;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 6;
    // 发起关联的Entity id list
    repeated uint64 source_entity_ids = 7;
    // 额外返回source entity的字段
    repeated string include_source_entity_fields = 8;
    // 额外返回source entity 的 relation
    repeated string include_source_entity_relations = 9;
    // 分页大小
    int32 limit = 10;
    // 跳过行数
    int32 offset = 11;
    // 排序字段
    string sort = 12;
    // 排序顺序
    string order = 13;
    // 返回总条数
    bool include_total = 14;
    // 按字段过滤
    google.protobuf.Struct filters = 15;
    // 按source entity字段过滤
    google.protobuf.Struct source_entity_filter = 16;
    // 当前使用的语言
    string lan = 17;
    string source_entity_search = 18;
    string source_entity_search_fields = 19;
    google.protobuf.Struct source_entity_relation_filter = 20;
}
message UpdateEntityRelationStateRequest {
    // 数据id
    uint64 id = 1;
    // source schema 名称
    string source_schema_name = 2;
    // 关系名称
    string schema_relation_name = 3;
    // state
    string state = 4;

}
message ProcessEntityRelationPendingChangesRequest {
    // 数据id
    uint64 id = 1;
    // source schema 名称
    string source_schema_name = 2;
    // 关系名称
    string schema_relation_name = 3;
    // action ("apply" or "cancel")
    string action = 4;

}
message ListCrossedRelationBySourceRequest {
    // 起始关联的schema name(e.g.: 商品)
    string source_schema_name = 1;
    // 起始关联的relation name(e.g.: 商品关联属性区域的Relation Name)
    string source_schema_relation_name = 2;
    // 交叉关联的schema name(e.g.: 门店)
    string crossed_schema_name = 3;
    // 交叉关联的relation name(e.g.: 门店关联属性区域的Relation Name)
    string crossed_schema_relation_name = 4;
    // 交叉关联的 entity ids(e.g.: 门店id)
    repeated uint64 crossed_entity_ids = 5;
    // 返回关系字段
    string return_fields = 6;
    // 起始关联的entity id(e.g.: 商品id)
    repeated uint64 source_entity_ids = 7;
    // 额外返回source entity的字段
    repeated string include_source_entity_fields = 8;
    // 额外返回source entity的relation
    repeated string include_source_entity_relations = 9;
    // 分页大小
    int32 limit = 10;
    // 跳过行数
    int32 offset = 11;
    // 排序字段
    string sort = 12;
    // 排序顺序
    string order = 13;
    // 返回总条数
    bool include_total = 14;
    // 按字段过滤
    google.protobuf.Struct filters = 15;
    // 按source entity字段过滤
    google.protobuf.Struct source_entity_filter = 16;
    // 当前使用的语言
    string lan = 17;
    string source_entity_search = 18;
    string source_entity_search_fields = 19;
    google.protobuf.Struct crossed_entity_filter = 20;
    string crossed_entity_search = 21;
    string crossed_entity_search_fields = 22;
    google.protobuf.Struct crossed_entity_relation_filter = 23;
    google.protobuf.Struct source_entity_relation_filter = 24;
}
message EntityRelation {
    // 数据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    // 发起关联的schema id
    uint64 source_schema_id = 4;
    // 发起关联的schema名称
    string source_schema_name = 5;
    // 发起关联的entity id
    uint64 source_entity_id = 6;
    // 被关联的schema id
    uint64 target_schema_id = 7;
    // 被关联的schema名称
    string target_schema_name = 8;
    // 被关联的entity id
    uint64 target_entity_id = 9;
    // 关系名称
    string schema_relation_name = 10;
    // 数据状态
    string state = 11;
    // 数据字段内容
    google.protobuf.Struct fields = 12;
    // 被更新的字段内容(未被接受的更新)
    google.protobuf.Struct fields_pending = 13;
    // fields合并了fields_pending
    google.protobuf.Struct record_pending = 14;
    // 是否有被更新字段
    bool pending = 15;
    // 创建时间
    string created = 16;
    // 最后一次修改时间
    string updated = 17;
    // 创建者
    string created_by = 18;
    // 最后一次修改者
    string updated_by = 19;
    // 数据被批处理状态
    string process_status = 20;
    // 交叉关联的entity id(e.g.: 门店id)
    uint64 crossed_entity_id = 21;
    // 交叉关联的schema name(e.g.: 门店)
    string crossed_schema_name = 22;

}
message ListEntityRelationResponse {
    repeated EntityRelation rows = 1;
    int32 total = 2;
}
message DefaultResponse {
    bool result = 1;
}

message CreateEntityRelationTaskFromPendingChangesRequest {
    // record_id
    uint64 record_id = 1;
    // 发起关联的schema 名称
    string source_schema_name = 2;
    // 关系名称
    string schema_relation_name = 3;
    // task 名称
    string name = 4;
    // 是否立即执行
    bool immediate = 5;
    // 开始执行时间
    string start = 6;
    // 自动审核
    bool auto_approve = 7;
}
message GetEntityRelationTaskByIdRequest {
    // 数据id
    uint64 id = 1;
    // 发起关联的schema 名称
    string source_schema_name = 2;
    // 关系名称
    string schema_relation_name = 3;
}
message ListEntityRelationTaskRequest {
    // 数据id
    repeated uint64 record_ids = 1;
    // schema 名称
    // 发起关联的schema 名称
    string source_schema_name = 2;
    // 关系名称
    string schema_relation_name = 3;
    // task 状态
    repeated string status = 4;
    // 批处理状态
    repeated string process_status = 5;
    // 分页大小
    int32 limit = 6;
    // 跳过行数
    int32 offset = 7;
    // 返回总条数
    bool include_total = 8;
    // 要模糊查询的字符串
    string search = 9;
    // 要查询的字段, 多个逗号隔开;
    string search_fields = 10;
    // 按id列表查询
    repeated uint64 ids = 11;
}
message UpdateEntityRelationTaskRequest {
    // 数据id
    uint64 id = 1;
    // 发起关联的schema 名称
    string source_schema_name = 2;
    // 关系名称
    string schema_relation_name = 3;
    // task 字段
    google.protobuf.Struct fields = 4;
}
message DeleteEntityRelationTaskRequest {
    // 数据id
    uint64 id = 1;
    // 发起关联的schema 名称
    string source_schema_name = 2;
    // 关系名称
    string schema_relation_name = 3;
}
message UpdateEntityRelationTaskStatusRequest {
    // 数据id
    uint64 id = 1;
    // 发起关联的schema 名称
    string source_schema_name = 2;
    // 关系名称
    string schema_relation_name = 3;
    // task 状态
    string status = 4;
}
message RunEntityRelationTaskRequest {
    // 数据id
    uint64 id = 1;
    // 发起关联的schema 名称
    string source_schema_name = 2;
    // 关系名称
    string schema_relation_name = 3;
}
message ListEntityRelationTaskResponse {
    repeated EntityRelationTask rows = 1;
    int32 total = 2;
}
message EntityRelationTask {
    // task id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    // job id
    uint64 job_id = 4;
    // schema type(entity, relation)
    string schema_type = 5;
    // task 名称
    string name = 6;
    // 数据id
    uint64 record_id = 7;
    // 数据字段内容
    google.protobuf.Struct content = 8;
    // task 状态
    string status = 9;
    // 数据被批处理状态
    string process_status = 10;
    // task动作
    string action = 11;
    // 是否立即执行
    bool immediate = 12;
    // 开始执行时间
    string start = 13;
    // 最后一次开始执行时间
    string last_start = 14;
    // 最后一次结束时间
    string last_end = 15;
    // 重试次数
    int32 retry = 16;
    // 创建时间
    string created = 17;
    // 最后一次修改时间
    string updated = 18;
    // 创建者
    uint64 created_by = 19;
    // 最后一次修改者
    uint64 updated_by = 20;

}

message AddRelationTemplateRequest {
    // 发起关联的schema 名称
    string source_schema_name = 1;
    // 被关联的entity id
    uint64 target_entity_id = 2;
    // 关系名称
    string schema_relation_name = 3;
    // 创建之后enable数据
    bool auto_enable = 4;
    // Entity字段json
    // 包含原Relation的所有字段以及"template_name:string"(无需额外配置RelationTemplate的schema)
    // fields里面还必须包含自己的relation字段，格式如下:
    // { "fields":{ "template_name":"xx模版", ..<其他原relation字段>..,
    //    "relation": { "<source_schema_name>(如product)":["123","234"],"<schema_relation_name>(如attribute_region, 注：是schema配置的relation name，不是entity name)":"567" }
    // }
    google.protobuf.Struct fields = 5;
    // 当前使用的语言
    string lan = 6;
}

message UpdateRelaitonTemplateRequest {
    // 数据id
    uint64 id = 1;
    // 发起关联的schema 名称
    string source_schema_name = 2;
    // 关系名称
    string schema_relation_name = 3;
    // 对数据状态是ENABLED的数据自动apply change
    bool auto_apply = 4;
    // Entity字段json
    google.protobuf.Struct fields = 5;
    // 当前使用的语言
    string lan = 6;

}
message GetRelationTemplateByIdRequest {
    // 数据id
    uint64 id = 1;
    // 发起关联的schema 名称
    string source_schema_name = 2;
    // 关系名称
    string schema_relation_name = 3;
    // 是否包含数据状态
    bool include_state = 4;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 5;
    // include_pending_changes=true时, 如果相关记录包含pending的修改属性,
    // 则会获取修改属性attach到返回记录中pengding_changes字段
    bool include_pending_changes = 6;
    // include_pending_record=true时, 如果相关记录包含pending的修改属性,
    // 则会获取修改的属性合并到返回记录的一个副本, 从而将这个副本（最新数据）attach到返回记录的pending_record字段
    bool include_pending_record = 7;
    // 当前使用的语言
    string lan = 8;
    // 是否包含所有本地化信息
    bool include_all_localizations = 9;
    // 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
    string relation = 10;

}

message ListRelationTemplateRequest {
    // 指定要返回相关数据状态的记录, 默认只返回enabled, 多个状态用逗号隔开，
    // 如state=draft,disabled; state=all时返回所有数据状态的记录
    string state = 1;
    // 发起关联的schema 名称
    string source_schema_name = 2;
    // 关系名称
    string schema_relation_name = 3;
    // 是否包含数据状态
    bool include_state = 4;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 5;
    // include_pending_changes=true时, 如果相关记录包含pending的修改属性,
    // 则会获取修改属性attach到返回记录中pengding_changes字段
    bool include_pending_changes = 6;
    // include_pending_record=true时, 如果相关记录包含pending的修改属性,
    // 则会获取修改的属性合并到返回记录的一个副本, 从而将这个副本（最新数据）attach到返回记录的pending_record字段
    bool include_pending_record = 7;
    // 被关联的Entity id list
    repeated uint64 target_entity_ids = 8;
    // 分页大小
    int32 limit = 9;
    // 跳过行数
    int32 offset = 10;
    // 排序字段
    string sort = 11;
    // 排序顺序
    string order = 12;
    // 返回总条数
    bool include_total = 13;
    // 按id列表查询
    repeated uint64 ids = 14;
    string lan = 15;
    // 是否包含所有本地化信息
    bool include_all_localizations = 16;
    // 返回 state=DRAFT 或 state=ENABLED且有pending record的记录
    bool is_request = 17;
    string search = 18;
    string search_fields = 19;
    google.protobuf.Struct filters = 20;
    google.protobuf.Struct relation_filters = 21;
    string relation = 22;
}

message EntityRelationTemplate {
    // 数据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    // 父级id
    uint64 parent_id = 4;
    // schema id
    uint64 schema_id = 5;
    // schema 名称
    string schema_name = 6;
    // 数据状态
    string state = 7;
    // 数据字段内容
    google.protobuf.Struct fields = 8;
    google.protobuf.Struct fields_pending = 9;
    google.protobuf.Struct record_pending = 10;
    // 是否有被更新字段
    bool pending = 11;
    // 创建时间
    string created = 12;
    // 最后一次修改时间
    string updated = 13;
    // 创建者
    string created_by = 14;
    // 最后一次修改者
    string updated_by = 15;
    // 数据被批处理状态
    string process_status = 16;
    string source_schema_name = 17;
    // 关系名称
    string schema_relation_name = 18;
    uint64 target_entity_id = 19;

}

message ListEntityRelationTemplateResponse {
    repeated EntityRelationTemplate rows = 1;
    int32 total = 2;
}

message UpdateEntityRelationTemplateStateRequest {
    // 数据id
    uint64 id = 1;
    // source schema 名称
    string source_schema_name = 2;
    // 关系名称
    string schema_relation_name = 3;
    // state
    string state = 4;

}

message ProcessEntityRelationTemplatePendingChangesRequest {
    // 数据id
    uint64 id = 1;
    // source schema 名称
    string source_schema_name = 2;
    // 关系名称
    string schema_relation_name = 3;
    // action ("apply" or "cancel")
    string action = 4;

}

message ListRuleRelationReq {
    // 发起关联的schema 名称
    string source_schema_name = 1;
    // 关系名称
    string schema_relation_name = 2;
    google.protobuf.Struct filters = 3;
    google.protobuf.Struct relation_filters = 4;
    string search = 5;
    string search_fields = 6;
    google.protobuf.Struct target_filters = 7;
    google.protobuf.Struct target_relation_filters = 8;
    string target_entity_search = 9;
    string target_entity_search_fields = 10;
    repeated uint64 target_id_list = 11;
    // 分页大小
    int32 limit = 12;
    // 跳过行数
    int32 offset = 13;
    bool include_total = 14;
    string lan = 15;
}

//统一的业务包装接口
message ListRegionProductByStoreRequest {
    uint64 store_id = 1;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 2;
    // 用于过滤的商品id
    repeated uint64 product_ids = 3;
    // 额外返回商品字段
    string include_product_fields = 4;
    // 是否包含商品单位
    bool include_product_units = 5;
    // 分页大小
    int32 limit = 6;
    // 跳过行数
    int32 offset = 7;
    // 排序字段
    string sort = 8;
    // 排序顺序
    string order = 9;
    // 返回总条数
    bool include_total = 10;
    // 按字段过滤
    google.protobuf.Struct filters = 11;
    // 按商品字段过滤
    google.protobuf.Struct product_filters = 12;
    string product_search = 13;
    string product_search_fields = 14;
    bool can_bom = 15;
    bool can_order = 16;
    bool can_purchase = 17;
    bool can_stocktake = 18;
    bool can_sales = 19;
    google.protobuf.Struct product_relation_filters = 20;
    bool check_division = 21;
    string lan = 22;

    string region = 23;
}
message ListRegionProductByStoreResponse {
    repeated google.protobuf.Struct rows = 1;
    int32 total = 2;
}
