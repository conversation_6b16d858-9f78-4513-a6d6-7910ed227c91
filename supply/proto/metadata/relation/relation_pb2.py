# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: metadata/relation/relation.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='metadata/relation/relation.proto',
  package='relation',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n metadata/relation/relation.proto\x12\x08relation\x1a\x1cgoogle/api/annotations.proto\x1a\x1cgoogle/protobuf/struct.proto\"\xd9\x01\n\x12\x41\x64\x64RelationRequest\x12\x1a\n\x12source_schema_name\x18\x01 \x01(\t\x12\x18\n\x10source_entity_id\x18\x02 \x01(\x04\x12\x18\n\x10target_entity_id\x18\x03 \x01(\x04\x12\x1c\n\x14schema_relation_name\x18\x04 \x01(\t\x12\x13\n\x0b\x61uto_enable\x18\x05 \x01(\x08\x12\'\n\x06\x66ields\x18\x06 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x07 \x01(\t\x12\n\n\x02id\x18\x08 \x01(\t\"\xda\x01\n\x13SyncRelationRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1a\n\x12source_schema_name\x18\x02 \x01(\t\x12\x18\n\x10source_entity_id\x18\x03 \x01(\x04\x12\x18\n\x10target_entity_id\x18\x04 \x01(\x04\x12\x1c\n\x14schema_relation_name\x18\x05 \x01(\t\x12\x13\n\x0b\x61uto_enable\x18\x06 \x01(\x08\x12\'\n\x06\x66ields\x18\x07 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x08 \x01(\t\"\xa7\x01\n\x15UpdateRelaitonRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1a\n\x12source_schema_name\x18\x02 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x03 \x01(\t\x12\x12\n\nauto_apply\x18\x04 \x01(\x08\x12\'\n\x06\x66ields\x18\x05 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x06 \x01(\t\"\xfd\x01\n\x16GetRelationByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1a\n\x12source_schema_name\x18\x02 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x03 \x01(\t\x12\x15\n\rinclude_state\x18\x04 \x01(\x08\x12\x15\n\rreturn_fields\x18\x05 \x01(\t\x12\x1f\n\x17include_pending_changes\x18\x06 \x01(\x08\x12\x1e\n\x16include_pending_record\x18\x07 \x01(\x08\x12\x0b\n\x03lan\x18\x08 \x01(\t\x12!\n\x19include_all_localizations\x18\t \x01(\x08\"\xd7\x04\n\x13ListRelationRequest\x12\r\n\x05state\x18\x01 \x01(\t\x12\x1a\n\x12source_schema_name\x18\x02 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x03 \x01(\t\x12\x15\n\rinclude_state\x18\x04 \x01(\x08\x12\x15\n\rreturn_fields\x18\x05 \x01(\t\x12\x1f\n\x17include_pending_changes\x18\x06 \x01(\x08\x12\x1e\n\x16include_pending_record\x18\x07 \x01(\x08\x12\x19\n\x11source_entity_ids\x18\x08 \x03(\x04\x12\x19\n\x11target_entity_ids\x18\t \x03(\x04\x12$\n\x1cinclude_source_entity_fields\x18\n \x03(\t\x12\'\n\x1finclude_source_entity_relations\x18\x0b \x03(\t\x12\r\n\x05limit\x18\x0c \x01(\x05\x12\x0e\n\x06offset\x18\r \x01(\x05\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\r\n\x05order\x18\x0f \x01(\t\x12\x15\n\rinclude_total\x18\x10 \x01(\x08\x12\x0b\n\x03ids\x18\x11 \x03(\x04\x12.\n&include_target_entity_parent_relations\x18\x12 \x01(\x08\x12/\n\'override_target_entity_parent_relations\x18\x13 \x01(\x08\x12\x0b\n\x03lan\x18\x14 \x01(\t\x12!\n\x19include_all_localizations\x18\x15 \x01(\x08\x12\x12\n\nis_request\x18\x16 \x01(\x08\"\xea\x04\n\x1aListCrossedRelationRequest\x12\x1a\n\x12source_schema_name\x18\x01 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x02 \x01(\t\x12\x16\n\x0e\x62y_schema_name\x18\x03 \x01(\t\x12\x1f\n\x17\x62y_schema_relation_name\x18\x04 \x01(\t\x12\x14\n\x0c\x62y_entity_id\x18\x05 \x01(\x04\x12\x15\n\rreturn_fields\x18\x06 \x01(\t\x12\x19\n\x11source_entity_ids\x18\x07 \x03(\x04\x12$\n\x1cinclude_source_entity_fields\x18\x08 \x03(\t\x12\'\n\x1finclude_source_entity_relations\x18\t \x03(\t\x12\r\n\x05limit\x18\n \x01(\x05\x12\x0e\n\x06offset\x18\x0b \x01(\x05\x12\x0c\n\x04sort\x18\x0c \x01(\t\x12\r\n\x05order\x18\r \x01(\t\x12\x15\n\rinclude_total\x18\x0e \x01(\x08\x12(\n\x07\x66ilters\x18\x0f \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x35\n\x14source_entity_filter\x18\x10 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x1c\n\x14source_entity_search\x18\x12 \x01(\t\x12#\n\x1bsource_entity_search_fields\x18\x13 \x01(\t\x12>\n\x1dsource_entity_relation_filter\x18\x14 \x01(\x0b\x32\x17.google.protobuf.Struct\"w\n UpdateEntityRelationStateRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1a\n\x12source_schema_name\x18\x02 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x03 \x01(\t\x12\r\n\x05state\x18\x04 \x01(\t\"\x82\x01\n*ProcessEntityRelationPendingChangesRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1a\n\x12source_schema_name\x18\x02 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x03 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x04 \x01(\t\"\xc7\x06\n\"ListCrossedRelationBySourceRequest\x12\x1a\n\x12source_schema_name\x18\x01 \x01(\t\x12#\n\x1bsource_schema_relation_name\x18\x02 \x01(\t\x12\x1b\n\x13\x63rossed_schema_name\x18\x03 \x01(\t\x12$\n\x1c\x63rossed_schema_relation_name\x18\x04 \x01(\t\x12\x1a\n\x12\x63rossed_entity_ids\x18\x05 \x03(\x04\x12\x15\n\rreturn_fields\x18\x06 \x01(\t\x12\x19\n\x11source_entity_ids\x18\x07 \x03(\x04\x12$\n\x1cinclude_source_entity_fields\x18\x08 \x03(\t\x12\'\n\x1finclude_source_entity_relations\x18\t \x03(\t\x12\r\n\x05limit\x18\n \x01(\x05\x12\x0e\n\x06offset\x18\x0b \x01(\x05\x12\x0c\n\x04sort\x18\x0c \x01(\t\x12\r\n\x05order\x18\r \x01(\t\x12\x15\n\rinclude_total\x18\x0e \x01(\x08\x12(\n\x07\x66ilters\x18\x0f \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x35\n\x14source_entity_filter\x18\x10 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x1c\n\x14source_entity_search\x18\x12 \x01(\t\x12#\n\x1bsource_entity_search_fields\x18\x13 \x01(\t\x12\x36\n\x15\x63rossed_entity_filter\x18\x14 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x1d\n\x15\x63rossed_entity_search\x18\x15 \x01(\t\x12$\n\x1c\x63rossed_entity_search_fields\x18\x16 \x01(\t\x12?\n\x1e\x63rossed_entity_relation_filter\x18\x17 \x01(\x0b\x32\x17.google.protobuf.Struct\x12>\n\x1dsource_entity_relation_filter\x18\x18 \x01(\x0b\x32\x17.google.protobuf.Struct\"\xc5\x04\n\x0e\x45ntityRelation\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x18\n\x10source_schema_id\x18\x04 \x01(\x04\x12\x1a\n\x12source_schema_name\x18\x05 \x01(\t\x12\x18\n\x10source_entity_id\x18\x06 \x01(\x04\x12\x18\n\x10target_schema_id\x18\x07 \x01(\x04\x12\x1a\n\x12target_schema_name\x18\x08 \x01(\t\x12\x18\n\x10target_entity_id\x18\t \x01(\x04\x12\x1c\n\x14schema_relation_name\x18\n \x01(\t\x12\r\n\x05state\x18\x0b \x01(\t\x12\'\n\x06\x66ields\x18\x0c \x01(\x0b\x32\x17.google.protobuf.Struct\x12/\n\x0e\x66ields_pending\x18\r \x01(\x0b\x32\x17.google.protobuf.Struct\x12/\n\x0erecord_pending\x18\x0e \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0f\n\x07pending\x18\x0f \x01(\x08\x12\x0f\n\x07\x63reated\x18\x10 \x01(\t\x12\x0f\n\x07updated\x18\x11 \x01(\t\x12\x12\n\ncreated_by\x18\x12 \x01(\t\x12\x12\n\nupdated_by\x18\x13 \x01(\t\x12\x16\n\x0eprocess_status\x18\x14 \x01(\t\x12\x19\n\x11\x63rossed_entity_id\x18\x15 \x01(\x04\x12\x1b\n\x13\x63rossed_schema_name\x18\x16 \x01(\t\"S\n\x1aListEntityRelationResponse\x12&\n\x04rows\x18\x01 \x03(\x0b\x32\x18.relation.EntityRelation\x12\r\n\x05total\x18\x02 \x01(\x05\"!\n\x0f\x44\x65\x66\x61ultResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\xc6\x01\n1CreateEntityRelationTaskFromPendingChangesRequest\x12\x11\n\trecord_id\x18\x01 \x01(\x04\x12\x1a\n\x12source_schema_name\x18\x02 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x03 \x01(\t\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x11\n\timmediate\x18\x05 \x01(\x08\x12\r\n\x05start\x18\x06 \x01(\t\x12\x14\n\x0c\x61uto_approve\x18\x07 \x01(\x08\"h\n GetEntityRelationTaskByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1a\n\x12source_schema_name\x18\x02 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x03 \x01(\t\"\xff\x01\n\x1dListEntityRelationTaskRequest\x12\x12\n\nrecord_ids\x18\x01 \x03(\x04\x12\x1a\n\x12source_schema_name\x18\x02 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x03(\t\x12\x16\n\x0eprocess_status\x18\x05 \x03(\t\x12\r\n\x05limit\x18\x06 \x01(\x05\x12\x0e\n\x06offset\x18\x07 \x01(\x05\x12\x15\n\rinclude_total\x18\x08 \x01(\x08\x12\x0e\n\x06search\x18\t \x01(\t\x12\x15\n\rsearch_fields\x18\n \x01(\t\x12\x0b\n\x03ids\x18\x0b \x03(\x04\"\x90\x01\n\x1fUpdateEntityRelationTaskRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1a\n\x12source_schema_name\x18\x02 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x03 \x01(\t\x12\'\n\x06\x66ields\x18\x04 \x01(\x0b\x32\x17.google.protobuf.Struct\"g\n\x1f\x44\x65leteEntityRelationTaskRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1a\n\x12source_schema_name\x18\x02 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x03 \x01(\t\"}\n%UpdateEntityRelationTaskStatusRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1a\n\x12source_schema_name\x18\x02 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\"d\n\x1cRunEntityRelationTaskRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1a\n\x12source_schema_name\x18\x02 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x03 \x01(\t\"[\n\x1eListEntityRelationTaskResponse\x12*\n\x04rows\x18\x01 \x03(\x0b\x32\x1c.relation.EntityRelationTask\x12\r\n\x05total\x18\x02 \x01(\x05\"\x8f\x03\n\x12\x45ntityRelationTask\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x0e\n\x06job_id\x18\x04 \x01(\x04\x12\x13\n\x0bschema_type\x18\x05 \x01(\t\x12\x0c\n\x04name\x18\x06 \x01(\t\x12\x11\n\trecord_id\x18\x07 \x01(\x04\x12(\n\x07\x63ontent\x18\x08 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0e\n\x06status\x18\t \x01(\t\x12\x16\n\x0eprocess_status\x18\n \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x0b \x01(\t\x12\x11\n\timmediate\x18\x0c \x01(\x08\x12\r\n\x05start\x18\r \x01(\t\x12\x12\n\nlast_start\x18\x0e \x01(\t\x12\x10\n\x08last_end\x18\x0f \x01(\t\x12\r\n\x05retry\x18\x10 \x01(\x05\x12\x0f\n\x07\x63reated\x18\x11 \x01(\t\x12\x0f\n\x07updated\x18\x12 \x01(\t\x12\x12\n\ncreated_by\x18\x13 \x01(\x04\x12\x12\n\nupdated_by\x18\x14 \x01(\x04\"\xbb\x01\n\x1a\x41\x64\x64RelationTemplateRequest\x12\x1a\n\x12source_schema_name\x18\x01 \x01(\t\x12\x18\n\x10target_entity_id\x18\x02 \x01(\x04\x12\x1c\n\x14schema_relation_name\x18\x03 \x01(\t\x12\x13\n\x0b\x61uto_enable\x18\x04 \x01(\x08\x12\'\n\x06\x66ields\x18\x05 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x06 \x01(\t\"\xaf\x01\n\x1dUpdateRelaitonTemplateRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1a\n\x12source_schema_name\x18\x02 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x03 \x01(\t\x12\x12\n\nauto_apply\x18\x04 \x01(\x08\x12\'\n\x06\x66ields\x18\x05 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x06 \x01(\t\"\x97\x02\n\x1eGetRelationTemplateByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1a\n\x12source_schema_name\x18\x02 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x03 \x01(\t\x12\x15\n\rinclude_state\x18\x04 \x01(\x08\x12\x15\n\rreturn_fields\x18\x05 \x01(\t\x12\x1f\n\x17include_pending_changes\x18\x06 \x01(\x08\x12\x1e\n\x16include_pending_record\x18\x07 \x01(\x08\x12\x0b\n\x03lan\x18\x08 \x01(\t\x12!\n\x19include_all_localizations\x18\t \x01(\x08\x12\x10\n\x08relation\x18\n \x01(\t\"\xaa\x04\n\x1bListRelationTemplateRequest\x12\r\n\x05state\x18\x01 \x01(\t\x12\x1a\n\x12source_schema_name\x18\x02 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x03 \x01(\t\x12\x15\n\rinclude_state\x18\x04 \x01(\x08\x12\x15\n\rreturn_fields\x18\x05 \x01(\t\x12\x1f\n\x17include_pending_changes\x18\x06 \x01(\x08\x12\x1e\n\x16include_pending_record\x18\x07 \x01(\x08\x12\x19\n\x11target_entity_ids\x18\x08 \x03(\x04\x12\r\n\x05limit\x18\t \x01(\x05\x12\x0e\n\x06offset\x18\n \x01(\x05\x12\x0c\n\x04sort\x18\x0b \x01(\t\x12\r\n\x05order\x18\x0c \x01(\t\x12\x15\n\rinclude_total\x18\r \x01(\x08\x12\x0b\n\x03ids\x18\x0e \x03(\x04\x12\x0b\n\x03lan\x18\x0f \x01(\t\x12!\n\x19include_all_localizations\x18\x10 \x01(\x08\x12\x12\n\nis_request\x18\x11 \x01(\x08\x12\x0e\n\x06search\x18\x12 \x01(\t\x12\x15\n\rsearch_fields\x18\x13 \x01(\t\x12(\n\x07\x66ilters\x18\x14 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x31\n\x10relation_filters\x18\x15 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x10\n\x08relation\x18\x16 \x01(\t\"\xe6\x03\n\x16\x45ntityRelationTemplate\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x11\n\tparent_id\x18\x04 \x01(\x04\x12\x11\n\tschema_id\x18\x05 \x01(\x04\x12\x13\n\x0bschema_name\x18\x06 \x01(\t\x12\r\n\x05state\x18\x07 \x01(\t\x12\'\n\x06\x66ields\x18\x08 \x01(\x0b\x32\x17.google.protobuf.Struct\x12/\n\x0e\x66ields_pending\x18\t \x01(\x0b\x32\x17.google.protobuf.Struct\x12/\n\x0erecord_pending\x18\n \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0f\n\x07pending\x18\x0b \x01(\x08\x12\x0f\n\x07\x63reated\x18\x0c \x01(\t\x12\x0f\n\x07updated\x18\r \x01(\t\x12\x12\n\ncreated_by\x18\x0e \x01(\t\x12\x12\n\nupdated_by\x18\x0f \x01(\t\x12\x16\n\x0eprocess_status\x18\x10 \x01(\t\x12\x1a\n\x12source_schema_name\x18\x11 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x12 \x01(\t\x12\x18\n\x10target_entity_id\x18\x13 \x01(\x04\"c\n\"ListEntityRelationTemplateResponse\x12.\n\x04rows\x18\x01 \x03(\x0b\x32 .relation.EntityRelationTemplate\x12\r\n\x05total\x18\x02 \x01(\x05\"\x7f\n(UpdateEntityRelationTemplateStateRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1a\n\x12source_schema_name\x18\x02 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x03 \x01(\t\x12\r\n\x05state\x18\x04 \x01(\t\"\x8a\x01\n2ProcessEntityRelationTemplatePendingChangesRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1a\n\x12source_schema_name\x18\x02 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x03 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x04 \x01(\t\"\xdc\x03\n\x13ListRuleRelationReq\x12\x1a\n\x12source_schema_name\x18\x01 \x01(\t\x12\x1c\n\x14schema_relation_name\x18\x02 \x01(\t\x12(\n\x07\x66ilters\x18\x03 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x31\n\x10relation_filters\x18\x04 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0e\n\x06search\x18\x05 \x01(\t\x12\x15\n\rsearch_fields\x18\x06 \x01(\t\x12/\n\x0etarget_filters\x18\x07 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x38\n\x17target_relation_filters\x18\x08 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x1c\n\x14target_entity_search\x18\t \x01(\t\x12#\n\x1btarget_entity_search_fields\x18\n \x01(\t\x12\x16\n\x0etarget_id_list\x18\x0b \x03(\x04\x12\r\n\x05limit\x18\x0c \x01(\x05\x12\x0e\n\x06offset\x18\r \x01(\x05\x12\x15\n\rinclude_total\x18\x0e \x01(\x08\x12\x0b\n\x03lan\x18\x0f \x01(\t\"\xd8\x04\n\x1fListRegionProductByStoreRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x15\n\rreturn_fields\x18\x02 \x01(\t\x12\x13\n\x0bproduct_ids\x18\x03 \x03(\x04\x12\x1e\n\x16include_product_fields\x18\x04 \x01(\t\x12\x1d\n\x15include_product_units\x18\x05 \x01(\x08\x12\r\n\x05limit\x18\x06 \x01(\x05\x12\x0e\n\x06offset\x18\x07 \x01(\x05\x12\x0c\n\x04sort\x18\x08 \x01(\t\x12\r\n\x05order\x18\t \x01(\t\x12\x15\n\rinclude_total\x18\n \x01(\x08\x12(\n\x07\x66ilters\x18\x0b \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x30\n\x0fproduct_filters\x18\x0c \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x16\n\x0eproduct_search\x18\r \x01(\t\x12\x1d\n\x15product_search_fields\x18\x0e \x01(\t\x12\x0f\n\x07\x63\x61n_bom\x18\x0f \x01(\x08\x12\x11\n\tcan_order\x18\x10 \x01(\x08\x12\x14\n\x0c\x63\x61n_purchase\x18\x11 \x01(\x08\x12\x15\n\rcan_stocktake\x18\x12 \x01(\x08\x12\x11\n\tcan_sales\x18\x13 \x01(\x08\x12\x39\n\x18product_relation_filters\x18\x14 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x16\n\x0e\x63heck_division\x18\x15 \x01(\x08\x12\x0b\n\x03lan\x18\x16 \x01(\t\x12\x0e\n\x06region\x18\x17 \x01(\t\"X\n ListRegionProductByStoreResponse\x12%\n\x04rows\x18\x01 \x03(\x0b\x32\x17.google.protobuf.Struct\x12\r\n\x05total\x18\x02 \x01(\x05\x32\xbf%\n\x0fRelationService\x12\xa8\x01\n\x11\x41\x64\x64\x45ntityRelation\x12\x1c.relation.AddRelationRequest\x1a\x18.relation.EntityRelation\"[\x82\xd3\xe4\x93\x02U\"P/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/add:\x01*\x12\xb1\x01\n\x14UpdateEntityRelation\x12\x1f.relation.UpdateRelaitonRequest\x1a\x18.relation.EntityRelation\"^\x82\xd3\xe4\x93\x02X\"S/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/update:\x01*\x12\xb8\x01\n\x18SyncUpdateEntityRelation\x12\x1d.relation.SyncRelationRequest\x1a\x18.relation.EntityRelation\"c\x82\xd3\xe4\x93\x02]\"X/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/sync/update:\x01*\x12\xb4\x01\n\x15GetEntityRelationById\x12 .relation.GetRelationByIdRequest\x1a\x18.relation.EntityRelation\"_\x82\xd3\xe4\x93\x02Y\x12W/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/by/id/{id}\x12\xb8\x01\n\x12ListEntityRelation\x12\x1d.relation.ListRelationRequest\x1a$.relation.ListEntityRelationResponse\"]\x82\xd3\xe4\x93\x02W\"R/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/query:\x01*\x12\xc7\x01\n\x19UpdateEntityRelationState\x12*.relation.UpdateEntityRelationStateRequest\x1a\x18.relation.EntityRelation\"d\x82\xd3\xe4\x93\x02^\"Y/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/state/update:\x01*\x12\xdf\x01\n#ProcessEntityRelationPendingChanges\x12\x34.relation.ProcessEntityRelationPendingChangesRequest\x1a\x18.relation.EntityRelation\"h\x82\xd3\xe4\x93\x02\x62\"]/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/changes/{action}:\x01*\x12\xd2\x01\n\x19ListCrossedEntityRelation\x12$.relation.ListCrossedRelationRequest\x1a$.relation.ListEntityRelationResponse\"i\x82\xd3\xe4\x93\x02\x63\"^/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/query/by/relation:\x01*\x12\xbb\x01\n\x10ListRuleRelation\x12\x1d.relation.ListRuleRelationReq\x1a$.relation.ListEntityRelationResponse\"b\x82\xd3\xe4\x93\x02\\\"W/api/v2/metadata/entity/{source_schema_name}/relation/rule/{schema_relation_name}/query:\x01*\x12\xf0\x01\n*CreateEntityRelationTaskFromPendingChanges\x12;.relation.CreateEntityRelationTaskFromPendingChangesRequest\x1a\x1c.relation.EntityRelationTask\"g\x82\xd3\xe4\x93\x02\x61\"\\/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/changes/to/task:\x01*\x12\xc8\x01\n\x18UpdateEntityRelationTask\x12).relation.UpdateEntityRelationTaskRequest\x1a\x1c.relation.EntityRelationTask\"c\x82\xd3\xe4\x93\x02]\"X/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/task/update:\x01*\x12\xdb\x01\n\x1eUpdateEntityRelationTaskStatus\x12/.relation.UpdateEntityRelationTaskStatusRequest\x1a\x1c.relation.EntityRelationTask\"j\x82\xd3\xe4\x93\x02\x64\"_/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/task/update/status:\x01*\x12\xc9\x01\n\x18\x44\x65leteEntityRelationTask\x12).relation.DeleteEntityRelationTaskRequest\x1a\x1c.relation.EntityRelationTask\"d\x82\xd3\xe4\x93\x02^*\\/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/task/by/id/{id}\x12\xcb\x01\n\x19GetEntityRelationTaskById\x12*.relation.GetEntityRelationTaskByIdRequest\x1a\x1c.relation.EntityRelationTask\"d\x82\xd3\xe4\x93\x02^\x12\\/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/task/by/id/{id}\x12\xcf\x01\n\x16ListEntityRelationTask\x12\'.relation.ListEntityRelationTaskRequest\x1a(.relation.ListEntityRelationTaskResponse\"b\x82\xd3\xe4\x93\x02\\\"W/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/task/query:\x01*\x12\xeb\x01\n\x1bListCrossedRelationBySource\x12,.relation.ListCrossedRelationBySourceRequest\x1a$.relation.ListEntityRelationResponse\"x\x82\xd3\xe4\x93\x02r\"m/api/v2/metadata/entity/{source_schema_name}/relation/{source_schema_relation_name}/query/by/relation/reverse:\x01*\x12\xc9\x01\n\x19\x41\x64\x64\x45ntityRelationTemplate\x12$.relation.AddRelationTemplateRequest\x1a .relation.EntityRelationTemplate\"d\x82\xd3\xe4\x93\x02^\"Y/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/template/add:\x01*\x12\xd2\x01\n\x1cUpdateEntityRelationTemplate\x12\'.relation.UpdateRelaitonTemplateRequest\x1a .relation.EntityRelationTemplate\"g\x82\xd3\xe4\x93\x02\x61\"\\/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/template/update:\x01*\x12\xd5\x01\n\x1dGetEntityRelationTemplateById\x12(.relation.GetRelationTemplateByIdRequest\x1a .relation.EntityRelationTemplate\"h\x82\xd3\xe4\x93\x02\x62\x12`/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/template/by/id/{id}\x12\xd9\x01\n\x1aListEntityRelationTemplate\x12%.relation.ListRelationTemplateRequest\x1a,.relation.ListEntityRelationTemplateResponse\"f\x82\xd3\xe4\x93\x02`\"[/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/template/query:\x01*\x12\xe8\x01\n!UpdateEntityRelationTemplateState\x12\x32.relation.UpdateEntityRelationTemplateStateRequest\x1a .relation.EntityRelationTemplate\"m\x82\xd3\xe4\x93\x02g\"b/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/template/state/update:\x01*\x12\x80\x02\n+ProcessEntityRelationTemplatePendingChanges\x12<.relation.ProcessEntityRelationTemplatePendingChangesRequest\x1a .relation.EntityRelationTemplate\"q\x82\xd3\xe4\x93\x02k\"f/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/template/changes/{action}:\x01*\x12\xa7\x01\n\x18ListRegionProductByStore\x12).relation.ListRegionProductByStoreRequest\x1a*.relation.ListRegionProductByStoreResponse\"4\x82\xd3\xe4\x93\x02.\")/api/v2/product/in/{}/by/store/{store_id}:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_struct__pb2.DESCRIPTOR,])




_ADDRELATIONREQUEST = _descriptor.Descriptor(
  name='AddRelationRequest',
  full_name='relation.AddRelationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.AddRelationRequest.source_schema_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_entity_id', full_name='relation.AddRelationRequest.source_entity_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_entity_id', full_name='relation.AddRelationRequest.target_entity_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.AddRelationRequest.schema_relation_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auto_enable', full_name='relation.AddRelationRequest.auto_enable', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='relation.AddRelationRequest.fields', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='relation.AddRelationRequest.lan', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='relation.AddRelationRequest.id', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=107,
  serialized_end=324,
)


_SYNCRELATIONREQUEST = _descriptor.Descriptor(
  name='SyncRelationRequest',
  full_name='relation.SyncRelationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='relation.SyncRelationRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.SyncRelationRequest.source_schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_entity_id', full_name='relation.SyncRelationRequest.source_entity_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_entity_id', full_name='relation.SyncRelationRequest.target_entity_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.SyncRelationRequest.schema_relation_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auto_enable', full_name='relation.SyncRelationRequest.auto_enable', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='relation.SyncRelationRequest.fields', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='relation.SyncRelationRequest.lan', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=327,
  serialized_end=545,
)


_UPDATERELAITONREQUEST = _descriptor.Descriptor(
  name='UpdateRelaitonRequest',
  full_name='relation.UpdateRelaitonRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='relation.UpdateRelaitonRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.UpdateRelaitonRequest.source_schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.UpdateRelaitonRequest.schema_relation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auto_apply', full_name='relation.UpdateRelaitonRequest.auto_apply', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='relation.UpdateRelaitonRequest.fields', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='relation.UpdateRelaitonRequest.lan', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=548,
  serialized_end=715,
)


_GETRELATIONBYIDREQUEST = _descriptor.Descriptor(
  name='GetRelationByIdRequest',
  full_name='relation.GetRelationByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='relation.GetRelationByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.GetRelationByIdRequest.source_schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.GetRelationByIdRequest.schema_relation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_state', full_name='relation.GetRelationByIdRequest.include_state', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='relation.GetRelationByIdRequest.return_fields', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_pending_changes', full_name='relation.GetRelationByIdRequest.include_pending_changes', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_pending_record', full_name='relation.GetRelationByIdRequest.include_pending_record', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='relation.GetRelationByIdRequest.lan', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_all_localizations', full_name='relation.GetRelationByIdRequest.include_all_localizations', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=718,
  serialized_end=971,
)


_LISTRELATIONREQUEST = _descriptor.Descriptor(
  name='ListRelationRequest',
  full_name='relation.ListRelationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='state', full_name='relation.ListRelationRequest.state', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.ListRelationRequest.source_schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.ListRelationRequest.schema_relation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_state', full_name='relation.ListRelationRequest.include_state', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='relation.ListRelationRequest.return_fields', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_pending_changes', full_name='relation.ListRelationRequest.include_pending_changes', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_pending_record', full_name='relation.ListRelationRequest.include_pending_record', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_entity_ids', full_name='relation.ListRelationRequest.source_entity_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_entity_ids', full_name='relation.ListRelationRequest.target_entity_ids', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_source_entity_fields', full_name='relation.ListRelationRequest.include_source_entity_fields', index=9,
      number=10, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_source_entity_relations', full_name='relation.ListRelationRequest.include_source_entity_relations', index=10,
      number=11, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='relation.ListRelationRequest.limit', index=11,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='relation.ListRelationRequest.offset', index=12,
      number=13, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='relation.ListRelationRequest.sort', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='relation.ListRelationRequest.order', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='relation.ListRelationRequest.include_total', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='relation.ListRelationRequest.ids', index=16,
      number=17, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_target_entity_parent_relations', full_name='relation.ListRelationRequest.include_target_entity_parent_relations', index=17,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='override_target_entity_parent_relations', full_name='relation.ListRelationRequest.override_target_entity_parent_relations', index=18,
      number=19, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='relation.ListRelationRequest.lan', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_all_localizations', full_name='relation.ListRelationRequest.include_all_localizations', index=20,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_request', full_name='relation.ListRelationRequest.is_request', index=21,
      number=22, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=974,
  serialized_end=1573,
)


_LISTCROSSEDRELATIONREQUEST = _descriptor.Descriptor(
  name='ListCrossedRelationRequest',
  full_name='relation.ListCrossedRelationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.ListCrossedRelationRequest.source_schema_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.ListCrossedRelationRequest.schema_relation_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='by_schema_name', full_name='relation.ListCrossedRelationRequest.by_schema_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='by_schema_relation_name', full_name='relation.ListCrossedRelationRequest.by_schema_relation_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='by_entity_id', full_name='relation.ListCrossedRelationRequest.by_entity_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='relation.ListCrossedRelationRequest.return_fields', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_entity_ids', full_name='relation.ListCrossedRelationRequest.source_entity_ids', index=6,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_source_entity_fields', full_name='relation.ListCrossedRelationRequest.include_source_entity_fields', index=7,
      number=8, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_source_entity_relations', full_name='relation.ListCrossedRelationRequest.include_source_entity_relations', index=8,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='relation.ListCrossedRelationRequest.limit', index=9,
      number=10, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='relation.ListCrossedRelationRequest.offset', index=10,
      number=11, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='relation.ListCrossedRelationRequest.sort', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='relation.ListCrossedRelationRequest.order', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='relation.ListCrossedRelationRequest.include_total', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='relation.ListCrossedRelationRequest.filters', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_entity_filter', full_name='relation.ListCrossedRelationRequest.source_entity_filter', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='relation.ListCrossedRelationRequest.lan', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_entity_search', full_name='relation.ListCrossedRelationRequest.source_entity_search', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_entity_search_fields', full_name='relation.ListCrossedRelationRequest.source_entity_search_fields', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_entity_relation_filter', full_name='relation.ListCrossedRelationRequest.source_entity_relation_filter', index=19,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1576,
  serialized_end=2194,
)


_UPDATEENTITYRELATIONSTATEREQUEST = _descriptor.Descriptor(
  name='UpdateEntityRelationStateRequest',
  full_name='relation.UpdateEntityRelationStateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='relation.UpdateEntityRelationStateRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.UpdateEntityRelationStateRequest.source_schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.UpdateEntityRelationStateRequest.schema_relation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='state', full_name='relation.UpdateEntityRelationStateRequest.state', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2196,
  serialized_end=2315,
)


_PROCESSENTITYRELATIONPENDINGCHANGESREQUEST = _descriptor.Descriptor(
  name='ProcessEntityRelationPendingChangesRequest',
  full_name='relation.ProcessEntityRelationPendingChangesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='relation.ProcessEntityRelationPendingChangesRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.ProcessEntityRelationPendingChangesRequest.source_schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.ProcessEntityRelationPendingChangesRequest.schema_relation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='relation.ProcessEntityRelationPendingChangesRequest.action', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2318,
  serialized_end=2448,
)


_LISTCROSSEDRELATIONBYSOURCEREQUEST = _descriptor.Descriptor(
  name='ListCrossedRelationBySourceRequest',
  full_name='relation.ListCrossedRelationBySourceRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.ListCrossedRelationBySourceRequest.source_schema_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_relation_name', full_name='relation.ListCrossedRelationBySourceRequest.source_schema_relation_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='crossed_schema_name', full_name='relation.ListCrossedRelationBySourceRequest.crossed_schema_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='crossed_schema_relation_name', full_name='relation.ListCrossedRelationBySourceRequest.crossed_schema_relation_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='crossed_entity_ids', full_name='relation.ListCrossedRelationBySourceRequest.crossed_entity_ids', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='relation.ListCrossedRelationBySourceRequest.return_fields', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_entity_ids', full_name='relation.ListCrossedRelationBySourceRequest.source_entity_ids', index=6,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_source_entity_fields', full_name='relation.ListCrossedRelationBySourceRequest.include_source_entity_fields', index=7,
      number=8, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_source_entity_relations', full_name='relation.ListCrossedRelationBySourceRequest.include_source_entity_relations', index=8,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='relation.ListCrossedRelationBySourceRequest.limit', index=9,
      number=10, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='relation.ListCrossedRelationBySourceRequest.offset', index=10,
      number=11, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='relation.ListCrossedRelationBySourceRequest.sort', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='relation.ListCrossedRelationBySourceRequest.order', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='relation.ListCrossedRelationBySourceRequest.include_total', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='relation.ListCrossedRelationBySourceRequest.filters', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_entity_filter', full_name='relation.ListCrossedRelationBySourceRequest.source_entity_filter', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='relation.ListCrossedRelationBySourceRequest.lan', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_entity_search', full_name='relation.ListCrossedRelationBySourceRequest.source_entity_search', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_entity_search_fields', full_name='relation.ListCrossedRelationBySourceRequest.source_entity_search_fields', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='crossed_entity_filter', full_name='relation.ListCrossedRelationBySourceRequest.crossed_entity_filter', index=19,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='crossed_entity_search', full_name='relation.ListCrossedRelationBySourceRequest.crossed_entity_search', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='crossed_entity_search_fields', full_name='relation.ListCrossedRelationBySourceRequest.crossed_entity_search_fields', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='crossed_entity_relation_filter', full_name='relation.ListCrossedRelationBySourceRequest.crossed_entity_relation_filter', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_entity_relation_filter', full_name='relation.ListCrossedRelationBySourceRequest.source_entity_relation_filter', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2451,
  serialized_end=3290,
)


_ENTITYRELATION = _descriptor.Descriptor(
  name='EntityRelation',
  full_name='relation.EntityRelation',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='relation.EntityRelation.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='relation.EntityRelation.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='relation.EntityRelation.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_id', full_name='relation.EntityRelation.source_schema_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.EntityRelation.source_schema_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_entity_id', full_name='relation.EntityRelation.source_entity_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_schema_id', full_name='relation.EntityRelation.target_schema_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_schema_name', full_name='relation.EntityRelation.target_schema_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_entity_id', full_name='relation.EntityRelation.target_entity_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.EntityRelation.schema_relation_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='state', full_name='relation.EntityRelation.state', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='relation.EntityRelation.fields', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields_pending', full_name='relation.EntityRelation.fields_pending', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='record_pending', full_name='relation.EntityRelation.record_pending', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pending', full_name='relation.EntityRelation.pending', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='relation.EntityRelation.created', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='relation.EntityRelation.updated', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='relation.EntityRelation.created_by', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='relation.EntityRelation.updated_by', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='relation.EntityRelation.process_status', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='crossed_entity_id', full_name='relation.EntityRelation.crossed_entity_id', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='crossed_schema_name', full_name='relation.EntityRelation.crossed_schema_name', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3293,
  serialized_end=3874,
)


_LISTENTITYRELATIONRESPONSE = _descriptor.Descriptor(
  name='ListEntityRelationResponse',
  full_name='relation.ListEntityRelationResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='relation.ListEntityRelationResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='relation.ListEntityRelationResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3876,
  serialized_end=3959,
)


_DEFAULTRESPONSE = _descriptor.Descriptor(
  name='DefaultResponse',
  full_name='relation.DefaultResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='relation.DefaultResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3961,
  serialized_end=3994,
)


_CREATEENTITYRELATIONTASKFROMPENDINGCHANGESREQUEST = _descriptor.Descriptor(
  name='CreateEntityRelationTaskFromPendingChangesRequest',
  full_name='relation.CreateEntityRelationTaskFromPendingChangesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='record_id', full_name='relation.CreateEntityRelationTaskFromPendingChangesRequest.record_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.CreateEntityRelationTaskFromPendingChangesRequest.source_schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.CreateEntityRelationTaskFromPendingChangesRequest.schema_relation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='relation.CreateEntityRelationTaskFromPendingChangesRequest.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='immediate', full_name='relation.CreateEntityRelationTaskFromPendingChangesRequest.immediate', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='relation.CreateEntityRelationTaskFromPendingChangesRequest.start', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auto_approve', full_name='relation.CreateEntityRelationTaskFromPendingChangesRequest.auto_approve', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3997,
  serialized_end=4195,
)


_GETENTITYRELATIONTASKBYIDREQUEST = _descriptor.Descriptor(
  name='GetEntityRelationTaskByIdRequest',
  full_name='relation.GetEntityRelationTaskByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='relation.GetEntityRelationTaskByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.GetEntityRelationTaskByIdRequest.source_schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.GetEntityRelationTaskByIdRequest.schema_relation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4197,
  serialized_end=4301,
)


_LISTENTITYRELATIONTASKREQUEST = _descriptor.Descriptor(
  name='ListEntityRelationTaskRequest',
  full_name='relation.ListEntityRelationTaskRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='record_ids', full_name='relation.ListEntityRelationTaskRequest.record_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.ListEntityRelationTaskRequest.source_schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.ListEntityRelationTaskRequest.schema_relation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='relation.ListEntityRelationTaskRequest.status', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='relation.ListEntityRelationTaskRequest.process_status', index=4,
      number=5, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='relation.ListEntityRelationTaskRequest.limit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='relation.ListEntityRelationTaskRequest.offset', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='relation.ListEntityRelationTaskRequest.include_total', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='relation.ListEntityRelationTaskRequest.search', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='relation.ListEntityRelationTaskRequest.search_fields', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='relation.ListEntityRelationTaskRequest.ids', index=10,
      number=11, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4304,
  serialized_end=4559,
)


_UPDATEENTITYRELATIONTASKREQUEST = _descriptor.Descriptor(
  name='UpdateEntityRelationTaskRequest',
  full_name='relation.UpdateEntityRelationTaskRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='relation.UpdateEntityRelationTaskRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.UpdateEntityRelationTaskRequest.source_schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.UpdateEntityRelationTaskRequest.schema_relation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='relation.UpdateEntityRelationTaskRequest.fields', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4562,
  serialized_end=4706,
)


_DELETEENTITYRELATIONTASKREQUEST = _descriptor.Descriptor(
  name='DeleteEntityRelationTaskRequest',
  full_name='relation.DeleteEntityRelationTaskRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='relation.DeleteEntityRelationTaskRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.DeleteEntityRelationTaskRequest.source_schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.DeleteEntityRelationTaskRequest.schema_relation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4708,
  serialized_end=4811,
)


_UPDATEENTITYRELATIONTASKSTATUSREQUEST = _descriptor.Descriptor(
  name='UpdateEntityRelationTaskStatusRequest',
  full_name='relation.UpdateEntityRelationTaskStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='relation.UpdateEntityRelationTaskStatusRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.UpdateEntityRelationTaskStatusRequest.source_schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.UpdateEntityRelationTaskStatusRequest.schema_relation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='relation.UpdateEntityRelationTaskStatusRequest.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4813,
  serialized_end=4938,
)


_RUNENTITYRELATIONTASKREQUEST = _descriptor.Descriptor(
  name='RunEntityRelationTaskRequest',
  full_name='relation.RunEntityRelationTaskRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='relation.RunEntityRelationTaskRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.RunEntityRelationTaskRequest.source_schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.RunEntityRelationTaskRequest.schema_relation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4940,
  serialized_end=5040,
)


_LISTENTITYRELATIONTASKRESPONSE = _descriptor.Descriptor(
  name='ListEntityRelationTaskResponse',
  full_name='relation.ListEntityRelationTaskResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='relation.ListEntityRelationTaskResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='relation.ListEntityRelationTaskResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5042,
  serialized_end=5133,
)


_ENTITYRELATIONTASK = _descriptor.Descriptor(
  name='EntityRelationTask',
  full_name='relation.EntityRelationTask',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='relation.EntityRelationTask.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='relation.EntityRelationTask.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='relation.EntityRelationTask.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='job_id', full_name='relation.EntityRelationTask.job_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_type', full_name='relation.EntityRelationTask.schema_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='relation.EntityRelationTask.name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='record_id', full_name='relation.EntityRelationTask.record_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='content', full_name='relation.EntityRelationTask.content', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='relation.EntityRelationTask.status', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='relation.EntityRelationTask.process_status', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='relation.EntityRelationTask.action', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='immediate', full_name='relation.EntityRelationTask.immediate', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='relation.EntityRelationTask.start', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='last_start', full_name='relation.EntityRelationTask.last_start', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='last_end', full_name='relation.EntityRelationTask.last_end', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='retry', full_name='relation.EntityRelationTask.retry', index=15,
      number=16, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='relation.EntityRelationTask.created', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='relation.EntityRelationTask.updated', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='relation.EntityRelationTask.created_by', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='relation.EntityRelationTask.updated_by', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5136,
  serialized_end=5535,
)


_ADDRELATIONTEMPLATEREQUEST = _descriptor.Descriptor(
  name='AddRelationTemplateRequest',
  full_name='relation.AddRelationTemplateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.AddRelationTemplateRequest.source_schema_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_entity_id', full_name='relation.AddRelationTemplateRequest.target_entity_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.AddRelationTemplateRequest.schema_relation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auto_enable', full_name='relation.AddRelationTemplateRequest.auto_enable', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='relation.AddRelationTemplateRequest.fields', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='relation.AddRelationTemplateRequest.lan', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5538,
  serialized_end=5725,
)


_UPDATERELAITONTEMPLATEREQUEST = _descriptor.Descriptor(
  name='UpdateRelaitonTemplateRequest',
  full_name='relation.UpdateRelaitonTemplateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='relation.UpdateRelaitonTemplateRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.UpdateRelaitonTemplateRequest.source_schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.UpdateRelaitonTemplateRequest.schema_relation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auto_apply', full_name='relation.UpdateRelaitonTemplateRequest.auto_apply', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='relation.UpdateRelaitonTemplateRequest.fields', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='relation.UpdateRelaitonTemplateRequest.lan', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5728,
  serialized_end=5903,
)


_GETRELATIONTEMPLATEBYIDREQUEST = _descriptor.Descriptor(
  name='GetRelationTemplateByIdRequest',
  full_name='relation.GetRelationTemplateByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='relation.GetRelationTemplateByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.GetRelationTemplateByIdRequest.source_schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.GetRelationTemplateByIdRequest.schema_relation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_state', full_name='relation.GetRelationTemplateByIdRequest.include_state', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='relation.GetRelationTemplateByIdRequest.return_fields', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_pending_changes', full_name='relation.GetRelationTemplateByIdRequest.include_pending_changes', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_pending_record', full_name='relation.GetRelationTemplateByIdRequest.include_pending_record', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='relation.GetRelationTemplateByIdRequest.lan', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_all_localizations', full_name='relation.GetRelationTemplateByIdRequest.include_all_localizations', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation', full_name='relation.GetRelationTemplateByIdRequest.relation', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5906,
  serialized_end=6185,
)


_LISTRELATIONTEMPLATEREQUEST = _descriptor.Descriptor(
  name='ListRelationTemplateRequest',
  full_name='relation.ListRelationTemplateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='state', full_name='relation.ListRelationTemplateRequest.state', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.ListRelationTemplateRequest.source_schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.ListRelationTemplateRequest.schema_relation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_state', full_name='relation.ListRelationTemplateRequest.include_state', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='relation.ListRelationTemplateRequest.return_fields', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_pending_changes', full_name='relation.ListRelationTemplateRequest.include_pending_changes', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_pending_record', full_name='relation.ListRelationTemplateRequest.include_pending_record', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_entity_ids', full_name='relation.ListRelationTemplateRequest.target_entity_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='relation.ListRelationTemplateRequest.limit', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='relation.ListRelationTemplateRequest.offset', index=9,
      number=10, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='relation.ListRelationTemplateRequest.sort', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='relation.ListRelationTemplateRequest.order', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='relation.ListRelationTemplateRequest.include_total', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='relation.ListRelationTemplateRequest.ids', index=13,
      number=14, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='relation.ListRelationTemplateRequest.lan', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_all_localizations', full_name='relation.ListRelationTemplateRequest.include_all_localizations', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_request', full_name='relation.ListRelationTemplateRequest.is_request', index=16,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='relation.ListRelationTemplateRequest.search', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='relation.ListRelationTemplateRequest.search_fields', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='relation.ListRelationTemplateRequest.filters', index=19,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation_filters', full_name='relation.ListRelationTemplateRequest.relation_filters', index=20,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation', full_name='relation.ListRelationTemplateRequest.relation', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6188,
  serialized_end=6742,
)


_ENTITYRELATIONTEMPLATE = _descriptor.Descriptor(
  name='EntityRelationTemplate',
  full_name='relation.EntityRelationTemplate',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='relation.EntityRelationTemplate.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='relation.EntityRelationTemplate.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='relation.EntityRelationTemplate.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='parent_id', full_name='relation.EntityRelationTemplate.parent_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_id', full_name='relation.EntityRelationTemplate.schema_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='relation.EntityRelationTemplate.schema_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='state', full_name='relation.EntityRelationTemplate.state', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='relation.EntityRelationTemplate.fields', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields_pending', full_name='relation.EntityRelationTemplate.fields_pending', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='record_pending', full_name='relation.EntityRelationTemplate.record_pending', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pending', full_name='relation.EntityRelationTemplate.pending', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='relation.EntityRelationTemplate.created', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='relation.EntityRelationTemplate.updated', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='relation.EntityRelationTemplate.created_by', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='relation.EntityRelationTemplate.updated_by', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='relation.EntityRelationTemplate.process_status', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.EntityRelationTemplate.source_schema_name', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.EntityRelationTemplate.schema_relation_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_entity_id', full_name='relation.EntityRelationTemplate.target_entity_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6745,
  serialized_end=7231,
)


_LISTENTITYRELATIONTEMPLATERESPONSE = _descriptor.Descriptor(
  name='ListEntityRelationTemplateResponse',
  full_name='relation.ListEntityRelationTemplateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='relation.ListEntityRelationTemplateResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='relation.ListEntityRelationTemplateResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7233,
  serialized_end=7332,
)


_UPDATEENTITYRELATIONTEMPLATESTATEREQUEST = _descriptor.Descriptor(
  name='UpdateEntityRelationTemplateStateRequest',
  full_name='relation.UpdateEntityRelationTemplateStateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='relation.UpdateEntityRelationTemplateStateRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.UpdateEntityRelationTemplateStateRequest.source_schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.UpdateEntityRelationTemplateStateRequest.schema_relation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='state', full_name='relation.UpdateEntityRelationTemplateStateRequest.state', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7334,
  serialized_end=7461,
)


_PROCESSENTITYRELATIONTEMPLATEPENDINGCHANGESREQUEST = _descriptor.Descriptor(
  name='ProcessEntityRelationTemplatePendingChangesRequest',
  full_name='relation.ProcessEntityRelationTemplatePendingChangesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='relation.ProcessEntityRelationTemplatePendingChangesRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.ProcessEntityRelationTemplatePendingChangesRequest.source_schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.ProcessEntityRelationTemplatePendingChangesRequest.schema_relation_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='relation.ProcessEntityRelationTemplatePendingChangesRequest.action', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7464,
  serialized_end=7602,
)


_LISTRULERELATIONREQ = _descriptor.Descriptor(
  name='ListRuleRelationReq',
  full_name='relation.ListRuleRelationReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='source_schema_name', full_name='relation.ListRuleRelationReq.source_schema_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_relation_name', full_name='relation.ListRuleRelationReq.schema_relation_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='relation.ListRuleRelationReq.filters', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation_filters', full_name='relation.ListRuleRelationReq.relation_filters', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='relation.ListRuleRelationReq.search', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='relation.ListRuleRelationReq.search_fields', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_filters', full_name='relation.ListRuleRelationReq.target_filters', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_relation_filters', full_name='relation.ListRuleRelationReq.target_relation_filters', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_entity_search', full_name='relation.ListRuleRelationReq.target_entity_search', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_entity_search_fields', full_name='relation.ListRuleRelationReq.target_entity_search_fields', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_id_list', full_name='relation.ListRuleRelationReq.target_id_list', index=10,
      number=11, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='relation.ListRuleRelationReq.limit', index=11,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='relation.ListRuleRelationReq.offset', index=12,
      number=13, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='relation.ListRuleRelationReq.include_total', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='relation.ListRuleRelationReq.lan', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7605,
  serialized_end=8081,
)


_LISTREGIONPRODUCTBYSTOREREQUEST = _descriptor.Descriptor(
  name='ListRegionProductByStoreRequest',
  full_name='relation.ListRegionProductByStoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='relation.ListRegionProductByStoreRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='relation.ListRegionProductByStoreRequest.return_fields', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='relation.ListRegionProductByStoreRequest.product_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_product_fields', full_name='relation.ListRegionProductByStoreRequest.include_product_fields', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_product_units', full_name='relation.ListRegionProductByStoreRequest.include_product_units', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='relation.ListRegionProductByStoreRequest.limit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='relation.ListRegionProductByStoreRequest.offset', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='relation.ListRegionProductByStoreRequest.sort', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='relation.ListRegionProductByStoreRequest.order', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='relation.ListRegionProductByStoreRequest.include_total', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='relation.ListRegionProductByStoreRequest.filters', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_filters', full_name='relation.ListRegionProductByStoreRequest.product_filters', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_search', full_name='relation.ListRegionProductByStoreRequest.product_search', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_search_fields', full_name='relation.ListRegionProductByStoreRequest.product_search_fields', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_bom', full_name='relation.ListRegionProductByStoreRequest.can_bom', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_order', full_name='relation.ListRegionProductByStoreRequest.can_order', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_purchase', full_name='relation.ListRegionProductByStoreRequest.can_purchase', index=16,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_stocktake', full_name='relation.ListRegionProductByStoreRequest.can_stocktake', index=17,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='can_sales', full_name='relation.ListRegionProductByStoreRequest.can_sales', index=18,
      number=19, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_relation_filters', full_name='relation.ListRegionProductByStoreRequest.product_relation_filters', index=19,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='check_division', full_name='relation.ListRegionProductByStoreRequest.check_division', index=20,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='relation.ListRegionProductByStoreRequest.lan', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region', full_name='relation.ListRegionProductByStoreRequest.region', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8084,
  serialized_end=8684,
)


_LISTREGIONPRODUCTBYSTORERESPONSE = _descriptor.Descriptor(
  name='ListRegionProductByStoreResponse',
  full_name='relation.ListRegionProductByStoreResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='relation.ListRegionProductByStoreResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='relation.ListRegionProductByStoreResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8686,
  serialized_end=8774,
)

_ADDRELATIONREQUEST.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_SYNCRELATIONREQUEST.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_UPDATERELAITONREQUEST.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTCROSSEDRELATIONREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTCROSSEDRELATIONREQUEST.fields_by_name['source_entity_filter'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTCROSSEDRELATIONREQUEST.fields_by_name['source_entity_relation_filter'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTCROSSEDRELATIONBYSOURCEREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTCROSSEDRELATIONBYSOURCEREQUEST.fields_by_name['source_entity_filter'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTCROSSEDRELATIONBYSOURCEREQUEST.fields_by_name['crossed_entity_filter'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTCROSSEDRELATIONBYSOURCEREQUEST.fields_by_name['crossed_entity_relation_filter'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTCROSSEDRELATIONBYSOURCEREQUEST.fields_by_name['source_entity_relation_filter'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_ENTITYRELATION.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_ENTITYRELATION.fields_by_name['fields_pending'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_ENTITYRELATION.fields_by_name['record_pending'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTENTITYRELATIONRESPONSE.fields_by_name['rows'].message_type = _ENTITYRELATION
_UPDATEENTITYRELATIONTASKREQUEST.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTENTITYRELATIONTASKRESPONSE.fields_by_name['rows'].message_type = _ENTITYRELATIONTASK
_ENTITYRELATIONTASK.fields_by_name['content'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_ADDRELATIONTEMPLATEREQUEST.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_UPDATERELAITONTEMPLATEREQUEST.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTRELATIONTEMPLATEREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTRELATIONTEMPLATEREQUEST.fields_by_name['relation_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_ENTITYRELATIONTEMPLATE.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_ENTITYRELATIONTEMPLATE.fields_by_name['fields_pending'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_ENTITYRELATIONTEMPLATE.fields_by_name['record_pending'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTENTITYRELATIONTEMPLATERESPONSE.fields_by_name['rows'].message_type = _ENTITYRELATIONTEMPLATE
_LISTRULERELATIONREQ.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTRULERELATIONREQ.fields_by_name['relation_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTRULERELATIONREQ.fields_by_name['target_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTRULERELATIONREQ.fields_by_name['target_relation_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTREGIONPRODUCTBYSTOREREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTREGIONPRODUCTBYSTOREREQUEST.fields_by_name['product_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTREGIONPRODUCTBYSTOREREQUEST.fields_by_name['product_relation_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTREGIONPRODUCTBYSTORERESPONSE.fields_by_name['rows'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
DESCRIPTOR.message_types_by_name['AddRelationRequest'] = _ADDRELATIONREQUEST
DESCRIPTOR.message_types_by_name['SyncRelationRequest'] = _SYNCRELATIONREQUEST
DESCRIPTOR.message_types_by_name['UpdateRelaitonRequest'] = _UPDATERELAITONREQUEST
DESCRIPTOR.message_types_by_name['GetRelationByIdRequest'] = _GETRELATIONBYIDREQUEST
DESCRIPTOR.message_types_by_name['ListRelationRequest'] = _LISTRELATIONREQUEST
DESCRIPTOR.message_types_by_name['ListCrossedRelationRequest'] = _LISTCROSSEDRELATIONREQUEST
DESCRIPTOR.message_types_by_name['UpdateEntityRelationStateRequest'] = _UPDATEENTITYRELATIONSTATEREQUEST
DESCRIPTOR.message_types_by_name['ProcessEntityRelationPendingChangesRequest'] = _PROCESSENTITYRELATIONPENDINGCHANGESREQUEST
DESCRIPTOR.message_types_by_name['ListCrossedRelationBySourceRequest'] = _LISTCROSSEDRELATIONBYSOURCEREQUEST
DESCRIPTOR.message_types_by_name['EntityRelation'] = _ENTITYRELATION
DESCRIPTOR.message_types_by_name['ListEntityRelationResponse'] = _LISTENTITYRELATIONRESPONSE
DESCRIPTOR.message_types_by_name['DefaultResponse'] = _DEFAULTRESPONSE
DESCRIPTOR.message_types_by_name['CreateEntityRelationTaskFromPendingChangesRequest'] = _CREATEENTITYRELATIONTASKFROMPENDINGCHANGESREQUEST
DESCRIPTOR.message_types_by_name['GetEntityRelationTaskByIdRequest'] = _GETENTITYRELATIONTASKBYIDREQUEST
DESCRIPTOR.message_types_by_name['ListEntityRelationTaskRequest'] = _LISTENTITYRELATIONTASKREQUEST
DESCRIPTOR.message_types_by_name['UpdateEntityRelationTaskRequest'] = _UPDATEENTITYRELATIONTASKREQUEST
DESCRIPTOR.message_types_by_name['DeleteEntityRelationTaskRequest'] = _DELETEENTITYRELATIONTASKREQUEST
DESCRIPTOR.message_types_by_name['UpdateEntityRelationTaskStatusRequest'] = _UPDATEENTITYRELATIONTASKSTATUSREQUEST
DESCRIPTOR.message_types_by_name['RunEntityRelationTaskRequest'] = _RUNENTITYRELATIONTASKREQUEST
DESCRIPTOR.message_types_by_name['ListEntityRelationTaskResponse'] = _LISTENTITYRELATIONTASKRESPONSE
DESCRIPTOR.message_types_by_name['EntityRelationTask'] = _ENTITYRELATIONTASK
DESCRIPTOR.message_types_by_name['AddRelationTemplateRequest'] = _ADDRELATIONTEMPLATEREQUEST
DESCRIPTOR.message_types_by_name['UpdateRelaitonTemplateRequest'] = _UPDATERELAITONTEMPLATEREQUEST
DESCRIPTOR.message_types_by_name['GetRelationTemplateByIdRequest'] = _GETRELATIONTEMPLATEBYIDREQUEST
DESCRIPTOR.message_types_by_name['ListRelationTemplateRequest'] = _LISTRELATIONTEMPLATEREQUEST
DESCRIPTOR.message_types_by_name['EntityRelationTemplate'] = _ENTITYRELATIONTEMPLATE
DESCRIPTOR.message_types_by_name['ListEntityRelationTemplateResponse'] = _LISTENTITYRELATIONTEMPLATERESPONSE
DESCRIPTOR.message_types_by_name['UpdateEntityRelationTemplateStateRequest'] = _UPDATEENTITYRELATIONTEMPLATESTATEREQUEST
DESCRIPTOR.message_types_by_name['ProcessEntityRelationTemplatePendingChangesRequest'] = _PROCESSENTITYRELATIONTEMPLATEPENDINGCHANGESREQUEST
DESCRIPTOR.message_types_by_name['ListRuleRelationReq'] = _LISTRULERELATIONREQ
DESCRIPTOR.message_types_by_name['ListRegionProductByStoreRequest'] = _LISTREGIONPRODUCTBYSTOREREQUEST
DESCRIPTOR.message_types_by_name['ListRegionProductByStoreResponse'] = _LISTREGIONPRODUCTBYSTORERESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

AddRelationRequest = _reflection.GeneratedProtocolMessageType('AddRelationRequest', (_message.Message,), dict(
  DESCRIPTOR = _ADDRELATIONREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.AddRelationRequest)
  ))
_sym_db.RegisterMessage(AddRelationRequest)

SyncRelationRequest = _reflection.GeneratedProtocolMessageType('SyncRelationRequest', (_message.Message,), dict(
  DESCRIPTOR = _SYNCRELATIONREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.SyncRelationRequest)
  ))
_sym_db.RegisterMessage(SyncRelationRequest)

UpdateRelaitonRequest = _reflection.GeneratedProtocolMessageType('UpdateRelaitonRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERELAITONREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.UpdateRelaitonRequest)
  ))
_sym_db.RegisterMessage(UpdateRelaitonRequest)

GetRelationByIdRequest = _reflection.GeneratedProtocolMessageType('GetRelationByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRELATIONBYIDREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.GetRelationByIdRequest)
  ))
_sym_db.RegisterMessage(GetRelationByIdRequest)

ListRelationRequest = _reflection.GeneratedProtocolMessageType('ListRelationRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTRELATIONREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.ListRelationRequest)
  ))
_sym_db.RegisterMessage(ListRelationRequest)

ListCrossedRelationRequest = _reflection.GeneratedProtocolMessageType('ListCrossedRelationRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTCROSSEDRELATIONREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.ListCrossedRelationRequest)
  ))
_sym_db.RegisterMessage(ListCrossedRelationRequest)

UpdateEntityRelationStateRequest = _reflection.GeneratedProtocolMessageType('UpdateEntityRelationStateRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEENTITYRELATIONSTATEREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.UpdateEntityRelationStateRequest)
  ))
_sym_db.RegisterMessage(UpdateEntityRelationStateRequest)

ProcessEntityRelationPendingChangesRequest = _reflection.GeneratedProtocolMessageType('ProcessEntityRelationPendingChangesRequest', (_message.Message,), dict(
  DESCRIPTOR = _PROCESSENTITYRELATIONPENDINGCHANGESREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.ProcessEntityRelationPendingChangesRequest)
  ))
_sym_db.RegisterMessage(ProcessEntityRelationPendingChangesRequest)

ListCrossedRelationBySourceRequest = _reflection.GeneratedProtocolMessageType('ListCrossedRelationBySourceRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTCROSSEDRELATIONBYSOURCEREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.ListCrossedRelationBySourceRequest)
  ))
_sym_db.RegisterMessage(ListCrossedRelationBySourceRequest)

EntityRelation = _reflection.GeneratedProtocolMessageType('EntityRelation', (_message.Message,), dict(
  DESCRIPTOR = _ENTITYRELATION,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.EntityRelation)
  ))
_sym_db.RegisterMessage(EntityRelation)

ListEntityRelationResponse = _reflection.GeneratedProtocolMessageType('ListEntityRelationResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTENTITYRELATIONRESPONSE,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.ListEntityRelationResponse)
  ))
_sym_db.RegisterMessage(ListEntityRelationResponse)

DefaultResponse = _reflection.GeneratedProtocolMessageType('DefaultResponse', (_message.Message,), dict(
  DESCRIPTOR = _DEFAULTRESPONSE,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.DefaultResponse)
  ))
_sym_db.RegisterMessage(DefaultResponse)

CreateEntityRelationTaskFromPendingChangesRequest = _reflection.GeneratedProtocolMessageType('CreateEntityRelationTaskFromPendingChangesRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEENTITYRELATIONTASKFROMPENDINGCHANGESREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.CreateEntityRelationTaskFromPendingChangesRequest)
  ))
_sym_db.RegisterMessage(CreateEntityRelationTaskFromPendingChangesRequest)

GetEntityRelationTaskByIdRequest = _reflection.GeneratedProtocolMessageType('GetEntityRelationTaskByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETENTITYRELATIONTASKBYIDREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.GetEntityRelationTaskByIdRequest)
  ))
_sym_db.RegisterMessage(GetEntityRelationTaskByIdRequest)

ListEntityRelationTaskRequest = _reflection.GeneratedProtocolMessageType('ListEntityRelationTaskRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTENTITYRELATIONTASKREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.ListEntityRelationTaskRequest)
  ))
_sym_db.RegisterMessage(ListEntityRelationTaskRequest)

UpdateEntityRelationTaskRequest = _reflection.GeneratedProtocolMessageType('UpdateEntityRelationTaskRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEENTITYRELATIONTASKREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.UpdateEntityRelationTaskRequest)
  ))
_sym_db.RegisterMessage(UpdateEntityRelationTaskRequest)

DeleteEntityRelationTaskRequest = _reflection.GeneratedProtocolMessageType('DeleteEntityRelationTaskRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEENTITYRELATIONTASKREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.DeleteEntityRelationTaskRequest)
  ))
_sym_db.RegisterMessage(DeleteEntityRelationTaskRequest)

UpdateEntityRelationTaskStatusRequest = _reflection.GeneratedProtocolMessageType('UpdateEntityRelationTaskStatusRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEENTITYRELATIONTASKSTATUSREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.UpdateEntityRelationTaskStatusRequest)
  ))
_sym_db.RegisterMessage(UpdateEntityRelationTaskStatusRequest)

RunEntityRelationTaskRequest = _reflection.GeneratedProtocolMessageType('RunEntityRelationTaskRequest', (_message.Message,), dict(
  DESCRIPTOR = _RUNENTITYRELATIONTASKREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.RunEntityRelationTaskRequest)
  ))
_sym_db.RegisterMessage(RunEntityRelationTaskRequest)

ListEntityRelationTaskResponse = _reflection.GeneratedProtocolMessageType('ListEntityRelationTaskResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTENTITYRELATIONTASKRESPONSE,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.ListEntityRelationTaskResponse)
  ))
_sym_db.RegisterMessage(ListEntityRelationTaskResponse)

EntityRelationTask = _reflection.GeneratedProtocolMessageType('EntityRelationTask', (_message.Message,), dict(
  DESCRIPTOR = _ENTITYRELATIONTASK,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.EntityRelationTask)
  ))
_sym_db.RegisterMessage(EntityRelationTask)

AddRelationTemplateRequest = _reflection.GeneratedProtocolMessageType('AddRelationTemplateRequest', (_message.Message,), dict(
  DESCRIPTOR = _ADDRELATIONTEMPLATEREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.AddRelationTemplateRequest)
  ))
_sym_db.RegisterMessage(AddRelationTemplateRequest)

UpdateRelaitonTemplateRequest = _reflection.GeneratedProtocolMessageType('UpdateRelaitonTemplateRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERELAITONTEMPLATEREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.UpdateRelaitonTemplateRequest)
  ))
_sym_db.RegisterMessage(UpdateRelaitonTemplateRequest)

GetRelationTemplateByIdRequest = _reflection.GeneratedProtocolMessageType('GetRelationTemplateByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRELATIONTEMPLATEBYIDREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.GetRelationTemplateByIdRequest)
  ))
_sym_db.RegisterMessage(GetRelationTemplateByIdRequest)

ListRelationTemplateRequest = _reflection.GeneratedProtocolMessageType('ListRelationTemplateRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTRELATIONTEMPLATEREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.ListRelationTemplateRequest)
  ))
_sym_db.RegisterMessage(ListRelationTemplateRequest)

EntityRelationTemplate = _reflection.GeneratedProtocolMessageType('EntityRelationTemplate', (_message.Message,), dict(
  DESCRIPTOR = _ENTITYRELATIONTEMPLATE,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.EntityRelationTemplate)
  ))
_sym_db.RegisterMessage(EntityRelationTemplate)

ListEntityRelationTemplateResponse = _reflection.GeneratedProtocolMessageType('ListEntityRelationTemplateResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTENTITYRELATIONTEMPLATERESPONSE,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.ListEntityRelationTemplateResponse)
  ))
_sym_db.RegisterMessage(ListEntityRelationTemplateResponse)

UpdateEntityRelationTemplateStateRequest = _reflection.GeneratedProtocolMessageType('UpdateEntityRelationTemplateStateRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEENTITYRELATIONTEMPLATESTATEREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.UpdateEntityRelationTemplateStateRequest)
  ))
_sym_db.RegisterMessage(UpdateEntityRelationTemplateStateRequest)

ProcessEntityRelationTemplatePendingChangesRequest = _reflection.GeneratedProtocolMessageType('ProcessEntityRelationTemplatePendingChangesRequest', (_message.Message,), dict(
  DESCRIPTOR = _PROCESSENTITYRELATIONTEMPLATEPENDINGCHANGESREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.ProcessEntityRelationTemplatePendingChangesRequest)
  ))
_sym_db.RegisterMessage(ProcessEntityRelationTemplatePendingChangesRequest)

ListRuleRelationReq = _reflection.GeneratedProtocolMessageType('ListRuleRelationReq', (_message.Message,), dict(
  DESCRIPTOR = _LISTRULERELATIONREQ,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.ListRuleRelationReq)
  ))
_sym_db.RegisterMessage(ListRuleRelationReq)

ListRegionProductByStoreRequest = _reflection.GeneratedProtocolMessageType('ListRegionProductByStoreRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTREGIONPRODUCTBYSTOREREQUEST,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.ListRegionProductByStoreRequest)
  ))
_sym_db.RegisterMessage(ListRegionProductByStoreRequest)

ListRegionProductByStoreResponse = _reflection.GeneratedProtocolMessageType('ListRegionProductByStoreResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTREGIONPRODUCTBYSTORERESPONSE,
  __module__ = 'metadata.relation.relation_pb2'
  # @@protoc_insertion_point(class_scope:relation.ListRegionProductByStoreResponse)
  ))
_sym_db.RegisterMessage(ListRegionProductByStoreResponse)



_RELATIONSERVICE = _descriptor.ServiceDescriptor(
  name='RelationService',
  full_name='relation.RelationService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=8777,
  serialized_end=13576,
  methods=[
  _descriptor.MethodDescriptor(
    name='AddEntityRelation',
    full_name='relation.RelationService.AddEntityRelation',
    index=0,
    containing_service=None,
    input_type=_ADDRELATIONREQUEST,
    output_type=_ENTITYRELATION,
    serialized_options=_b('\202\323\344\223\002U\"P/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/add:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateEntityRelation',
    full_name='relation.RelationService.UpdateEntityRelation',
    index=1,
    containing_service=None,
    input_type=_UPDATERELAITONREQUEST,
    output_type=_ENTITYRELATION,
    serialized_options=_b('\202\323\344\223\002X\"S/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='SyncUpdateEntityRelation',
    full_name='relation.RelationService.SyncUpdateEntityRelation',
    index=2,
    containing_service=None,
    input_type=_SYNCRELATIONREQUEST,
    output_type=_ENTITYRELATION,
    serialized_options=_b('\202\323\344\223\002]\"X/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/sync/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetEntityRelationById',
    full_name='relation.RelationService.GetEntityRelationById',
    index=3,
    containing_service=None,
    input_type=_GETRELATIONBYIDREQUEST,
    output_type=_ENTITYRELATION,
    serialized_options=_b('\202\323\344\223\002Y\022W/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/by/id/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListEntityRelation',
    full_name='relation.RelationService.ListEntityRelation',
    index=4,
    containing_service=None,
    input_type=_LISTRELATIONREQUEST,
    output_type=_LISTENTITYRELATIONRESPONSE,
    serialized_options=_b('\202\323\344\223\002W\"R/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateEntityRelationState',
    full_name='relation.RelationService.UpdateEntityRelationState',
    index=5,
    containing_service=None,
    input_type=_UPDATEENTITYRELATIONSTATEREQUEST,
    output_type=_ENTITYRELATION,
    serialized_options=_b('\202\323\344\223\002^\"Y/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/state/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ProcessEntityRelationPendingChanges',
    full_name='relation.RelationService.ProcessEntityRelationPendingChanges',
    index=6,
    containing_service=None,
    input_type=_PROCESSENTITYRELATIONPENDINGCHANGESREQUEST,
    output_type=_ENTITYRELATION,
    serialized_options=_b('\202\323\344\223\002b\"]/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/changes/{action}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListCrossedEntityRelation',
    full_name='relation.RelationService.ListCrossedEntityRelation',
    index=7,
    containing_service=None,
    input_type=_LISTCROSSEDRELATIONREQUEST,
    output_type=_LISTENTITYRELATIONRESPONSE,
    serialized_options=_b('\202\323\344\223\002c\"^/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/query/by/relation:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListRuleRelation',
    full_name='relation.RelationService.ListRuleRelation',
    index=8,
    containing_service=None,
    input_type=_LISTRULERELATIONREQ,
    output_type=_LISTENTITYRELATIONRESPONSE,
    serialized_options=_b('\202\323\344\223\002\\\"W/api/v2/metadata/entity/{source_schema_name}/relation/rule/{schema_relation_name}/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CreateEntityRelationTaskFromPendingChanges',
    full_name='relation.RelationService.CreateEntityRelationTaskFromPendingChanges',
    index=9,
    containing_service=None,
    input_type=_CREATEENTITYRELATIONTASKFROMPENDINGCHANGESREQUEST,
    output_type=_ENTITYRELATIONTASK,
    serialized_options=_b('\202\323\344\223\002a\"\\/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/changes/to/task:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateEntityRelationTask',
    full_name='relation.RelationService.UpdateEntityRelationTask',
    index=10,
    containing_service=None,
    input_type=_UPDATEENTITYRELATIONTASKREQUEST,
    output_type=_ENTITYRELATIONTASK,
    serialized_options=_b('\202\323\344\223\002]\"X/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/task/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateEntityRelationTaskStatus',
    full_name='relation.RelationService.UpdateEntityRelationTaskStatus',
    index=11,
    containing_service=None,
    input_type=_UPDATEENTITYRELATIONTASKSTATUSREQUEST,
    output_type=_ENTITYRELATIONTASK,
    serialized_options=_b('\202\323\344\223\002d\"_/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/task/update/status:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteEntityRelationTask',
    full_name='relation.RelationService.DeleteEntityRelationTask',
    index=12,
    containing_service=None,
    input_type=_DELETEENTITYRELATIONTASKREQUEST,
    output_type=_ENTITYRELATIONTASK,
    serialized_options=_b('\202\323\344\223\002^*\\/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/task/by/id/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetEntityRelationTaskById',
    full_name='relation.RelationService.GetEntityRelationTaskById',
    index=13,
    containing_service=None,
    input_type=_GETENTITYRELATIONTASKBYIDREQUEST,
    output_type=_ENTITYRELATIONTASK,
    serialized_options=_b('\202\323\344\223\002^\022\\/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/task/by/id/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListEntityRelationTask',
    full_name='relation.RelationService.ListEntityRelationTask',
    index=14,
    containing_service=None,
    input_type=_LISTENTITYRELATIONTASKREQUEST,
    output_type=_LISTENTITYRELATIONTASKRESPONSE,
    serialized_options=_b('\202\323\344\223\002\\\"W/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/task/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListCrossedRelationBySource',
    full_name='relation.RelationService.ListCrossedRelationBySource',
    index=15,
    containing_service=None,
    input_type=_LISTCROSSEDRELATIONBYSOURCEREQUEST,
    output_type=_LISTENTITYRELATIONRESPONSE,
    serialized_options=_b('\202\323\344\223\002r\"m/api/v2/metadata/entity/{source_schema_name}/relation/{source_schema_relation_name}/query/by/relation/reverse:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='AddEntityRelationTemplate',
    full_name='relation.RelationService.AddEntityRelationTemplate',
    index=16,
    containing_service=None,
    input_type=_ADDRELATIONTEMPLATEREQUEST,
    output_type=_ENTITYRELATIONTEMPLATE,
    serialized_options=_b('\202\323\344\223\002^\"Y/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/template/add:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateEntityRelationTemplate',
    full_name='relation.RelationService.UpdateEntityRelationTemplate',
    index=17,
    containing_service=None,
    input_type=_UPDATERELAITONTEMPLATEREQUEST,
    output_type=_ENTITYRELATIONTEMPLATE,
    serialized_options=_b('\202\323\344\223\002a\"\\/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/template/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetEntityRelationTemplateById',
    full_name='relation.RelationService.GetEntityRelationTemplateById',
    index=18,
    containing_service=None,
    input_type=_GETRELATIONTEMPLATEBYIDREQUEST,
    output_type=_ENTITYRELATIONTEMPLATE,
    serialized_options=_b('\202\323\344\223\002b\022`/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/template/by/id/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListEntityRelationTemplate',
    full_name='relation.RelationService.ListEntityRelationTemplate',
    index=19,
    containing_service=None,
    input_type=_LISTRELATIONTEMPLATEREQUEST,
    output_type=_LISTENTITYRELATIONTEMPLATERESPONSE,
    serialized_options=_b('\202\323\344\223\002`\"[/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/template/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateEntityRelationTemplateState',
    full_name='relation.RelationService.UpdateEntityRelationTemplateState',
    index=20,
    containing_service=None,
    input_type=_UPDATEENTITYRELATIONTEMPLATESTATEREQUEST,
    output_type=_ENTITYRELATIONTEMPLATE,
    serialized_options=_b('\202\323\344\223\002g\"b/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/template/state/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ProcessEntityRelationTemplatePendingChanges',
    full_name='relation.RelationService.ProcessEntityRelationTemplatePendingChanges',
    index=21,
    containing_service=None,
    input_type=_PROCESSENTITYRELATIONTEMPLATEPENDINGCHANGESREQUEST,
    output_type=_ENTITYRELATIONTEMPLATE,
    serialized_options=_b('\202\323\344\223\002k\"f/api/v2/metadata/entity/{source_schema_name}/relation/{schema_relation_name}/template/changes/{action}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListRegionProductByStore',
    full_name='relation.RelationService.ListRegionProductByStore',
    index=22,
    containing_service=None,
    input_type=_LISTREGIONPRODUCTBYSTOREREQUEST,
    output_type=_LISTREGIONPRODUCTBYSTORERESPONSE,
    serialized_options=_b('\202\323\344\223\002.\")/api/v2/product/in/{}/by/store/{store_id}:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_RELATIONSERVICE)

DESCRIPTOR.services_by_name['RelationService'] = _RELATIONSERVICE

# @@protoc_insertion_point(module_scope)
