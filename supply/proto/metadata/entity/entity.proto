syntax = "proto3";
package entity;
import "google/api/annotations.proto";
import "google/protobuf/struct.proto";

// EntityService 用于维护主档Entity数据
service EntityService {
    // 添加Entity
    rpc AddEntity (AddEntityRequest) returns (Entity) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{schema_name}/add"
        body: "*"
        };
    }
    // 修改Entity
    rpc UpdateEntity (UpdateEntityRequest) returns (Entity) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{schema_name}/update"
        body: "*"
        };
    }
    // 同步Entity
    rpc SyncUpdateEntity (SyncEntityRequest) returns (Entity) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{schema_name}/sync/update"
        body: "*"
        };
    }
    // 根据id获取Entity
    rpc GetEntityById (GetEntityByIdRequest) returns (Entity) {
        option (google.api.http) = {
        get: "/api/v2/metadata/entity/{schema_name}/by/id/{id}"
        };
    }
    // 查询 Entity 列表
    rpc ListEntity (ListEntityRequest) returns (ListEntityResponse) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{schema_name}/query"
        body: "*"
        };
    }
    // 修改Entity数据状态
    rpc UpdateEntityState (UpdateEntityStateRequest) returns (Entity) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{schema_name}/state/update"
        body: "*"
        };
    }
    // Apply或者Cancel pending changes
    rpc ProcessEntityPendingChanges (ProcessEntityPendingChangesRequest) returns (Entity) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{schema_name}/changes/{action}"
        body: "*"
        };
    }
    // 基于变更创建一个task
    rpc CreateEntityTaskFromPendingChanges (CreateEntityTaskFromPendingChangesRequest) returns (EntityTask) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{schema_name}/changes/to/task"
        body: "*"
        };
    }
    // 修改Entity Task
    rpc UpdateEntityTask (UpdateEntityTaskRequest) returns (EntityTask) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{schema_name}/task/update"
        body: "*"
        };
    }
    // 修改Entity Task 状态
    rpc UpdateEntityTaskStatus (UpdateEntityTaskStatusRequest) returns (EntityTask) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{schema_name}/task/update/status"
        body: "*"
        };
    }
    // 删除Entity Task 状态
    rpc DeleteEntityTask (DeleteEntityTaskRequest) returns (EntityTask) {
        option (google.api.http) = {
        delete: "/api/v2/metadata/entity/{schema_name}/task/by/id/{id}"
        };
    }
    // 根据id获取Entity Task
    rpc GetEntityTaskById (GetEntityTaskByIdRequest) returns (EntityTask) {
        option (google.api.http) = {
        get: "/api/v2/metadata/entity/{schema_name}/task/by/id/{id}"
        };
    }
    // 查询 Entity Task 列表
    rpc ListEntityTask (ListEntityTaskRequest) returns (ListEntityTaskResponse) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{schema_name}/task/query"
        body: "*"
        };
    }
    // 根据id列表获取所有id的子节点以及子孙节点
    rpc GetChildrenEntityIds (GetChildrenEntityIdsRequest) returns (GetChildrenEntityIdsResponse) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{schema_name}/childrenids"
        body: "*"
        };
    }
    // 根据id列表获取所有id的上级节点
    rpc GetParentEntityIds (GetParentEntityIdsRequest) returns (GetParentEntityIdsResponse) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/{schema_name}/parentids"
        body: "*"
        };
    }

    // 刷新物化视图
    rpc RefreshEntityMV (RefreshEntityMVRequest) returns (RefreshEntityMVResponse) {
        option (google.api.http) = {
        post: "/api/v2/metadata/refresh"
        };
    }

    // 修改EntityBatch
    rpc UpdateEntityBatch (UpdateEntityBatchRequest) returns (UpdateEntityBatchResponse) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/update/batch"
        body: "*"
        };
    }

    // 添加EntityBatch
    rpc AddEntityBatch (AddEntityBatchRequest) returns (AddEntityBatchResponse) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/add/batch"
        body: "*"
        };
    }

    // 查询 Entity 列表
    rpc SqlQuery (SqlQueryRequest) returns (ListEntityResponse) {
        option (google.api.http) = {
        post: "/api/v2/metadata/entity/query/sql"
        body: "*"
        };
    }
}


message AddEntityRequest {
    // schema 名称
    string schema_name = 1;
    // 创建之后enable数据
    bool auto_enable = 2;
    // Entity字段json
    google.protobuf.Struct fields = 3;
    // 当前使用的语言
    string lan = 4;
    //为了导入数据的参数
    string id = 5;
}
message SyncEntityRequest {
    // 数据id
    uint64 id = 1;
    // schema 名称
    string schema_name = 2;
    // 对数据状态是ENABLED的数据自动apply change
    bool auto_apply = 3;
    // 创建之后enable数据
    bool auto_enable = 4;
    // Entity字段json
    google.protobuf.Struct fields = 5;
    // 当前使用的语言
    string lan = 6;
}
message UpdateEntityRequest {
    // 数据id
    uint64 id = 1;
    // schema 名称
    string schema_name = 2;
    // 对数据状态是ENABLED的数据自动apply change
    bool auto_apply = 3;
    // Entity字段json
    google.protobuf.Struct fields = 4;
    // 当前使用的语言
    string lan = 5;
    // merged
    bool is_merged = 6;
    bool apply_current = 7;
}
message GetEntityByIdRequest {
    // 数据id
    uint64 id = 1;
    // schema 名称
    string schema_name = 2;
    // 是否包含数据状态
    bool include_state = 3;
    // 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
    string code = 4;
    // 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
    string relation = 5;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 6;
    // include_pending_changes=true时, 如果相关记录包含pending的修改属性,
    // 则会获取修改属性attach到返回记录中pengding_changes字段
    bool include_pending_changes = 7;
    // include_pending_record=true时, 如果相关记录包含pending的修改属性,
    // 则会获取修改的属性合并到返回记录的一个副本, 从而将这个副本（最新数据）attach到返回记录的pending_record字段
    bool include_pending_record = 8;
    // include_parents=true返回所有父级节点
    bool include_parents = 9;
    // 当前使用的语言
    string lan = 10;
    // 是否包含所有本地化信息
    bool include_all_localizations = 11;

}
message ListEntityRequest {
    // schema 名称
    string schema_name = 1;
    // 指定要返回相关数据状态的记录, 默认只返回enabled, 多个状态用逗号隔开，
    // 如state=draft,disabled; state=all时返回所有数据状态的记录
    string state = 2;
    // 是否包含数据状态
    bool include_state = 3;
    // 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
    string code = 4;
    // 需要返回的relation, 多个用逗号隔开, "all"返回所有, 默认不返回
    string relation = 5;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 6;
    // include_pending_changes=true时, 如果相关记录包含pending的修改属性,
    // 则会获取修改属性attach到返回记录中fields_pending字段
    bool include_pending_changes = 7;
    // include_pending_record=true时, 如果相关记录包含pending的修改属性,
    // 则会获取修改的属性合并到返回记录的一个副本, 从而将这个副本（最新数据）attach到返回记录的record_pending字段
    bool include_pending_record = 8;
    // include_parents=true返回所有父级节点
    bool include_parents = 9;
    // 分页大小
    int32 limit = 10;
    // 跳过行数
    int32 offset = 11;
    // 排序字段
    string sort = 12;
    // 排序顺序
    string order = 13;
    // 返回总条数
    bool include_total = 14;
    // 要模糊查询的字符串
    string search = 15;
    // 要查询的字段, 多个逗号隔开;
    // 如果传入relation.[relation name].[field], 则会顺带搜索关联的数据,
    // 例: 搜索store数据时, search_fields=relation.branch.name则会搜索管理区域名称,
    // 找到匹配的管理区域, 然后找到关联到这些管理区域以及所有下级区域的门店
    string search_fields = 16;
    // 按id列表查询
    repeated uint64 ids = 17;
    // 按字段过滤
    google.protobuf.Struct filters = 18;
    // 按关系深层次递归过滤(获取包含下级节点的数据)
    google.protobuf.Struct relation_filters = 19;
    // 当前使用的语言
    string lan = 20;
    // 是否包含所有本地化信息
    bool include_all_localizations = 21;
    // 返回 state=DRAFT 或 state=ENABLED且有pending record的记录
    bool is_request = 22;
    // 多租户下公共数据
    bool get_common = 23;
}
message UpdateEntityStateRequest {
    // 数据id
    uint64 id = 1;
    // schema 名称
    string schema_name = 2;
    // state
    string state = 3;
    // 当前使用的语言
    string lan = 4;

}
message ProcessEntityPendingChangesRequest {
    // 数据id
    uint64 id = 1;
    // schema 名称
    string schema_name = 2;
    // action ("apply" or "cancel")
    string action = 3;

}

message ListEntityResponse {
    repeated Entity rows = 1;
    int32 total = 2;
}
message Entity {
    // 数据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    // 父级id
    uint64 parent_id = 4;
    // schema id
    uint64 schema_id = 5;
    // schema 名称
    string schema_name = 6;
    // 数据状态
    string state = 7;
    // 数据字段内容
    google.protobuf.Struct fields = 8;
    //string fields = 8;
    // 被更新的字段内容(未被接受的更新)
    // string fields_pending = 9;
    google.protobuf.Struct fields_pending = 9;
    // fields合并了fields_pending
    // string record_pending = 10;
    google.protobuf.Struct record_pending = 10;
    // 是否有被更新字段
    bool pending = 11;
    // 创建时间
    string created = 12;
    // 最后一次修改时间
    string updated = 13;
    // 创建者
    string created_by = 14;
    // 最后一次修改者
    string updated_by = 15;
    // 数据被批处理状态
    string process_status = 16;
    // 父级节点
    Entity parent = 17;

}
message DefaultResponse {
    bool result = 1;
}
message CreateEntityTaskFromPendingChangesRequest {
    // record_id
    uint64 record_id = 1;
    string schema_name = 2;
    // task 名称
    string name = 3;
    // 是否立即执行
    bool immediate = 4;
    // 开始执行时间
    string start = 5;
    // 自动审核
    bool auto_approve = 6;
}
message GetEntityTaskByIdRequest {
    // 数据id
    uint64 id = 1;
    string schema_name = 2;
}
message ListEntityTaskRequest {
    // 数据id
    repeated uint64 record_ids = 1;
    // schema 名称
    string schema_name = 2;
    // task 状态
    repeated string status = 3;
    // 批处理状态
    repeated string process_status = 4;
    // 分页大小
    int32 limit = 5;
    // 跳过行数
    int32 offset = 6;
    // 返回总条数
    bool include_total = 7;
    // 要模糊查询的字符串
    string search = 8;
    // 要查询的字段, 多个逗号隔开;
    string search_fields = 9;
    // 按id列表查询
    repeated uint64 ids = 10;
}
message UpdateEntityTaskRequest {
    // 数据id
    uint64 id = 1;
    string schema_name = 2;
    // task 字段
    google.protobuf.Struct fields = 3;
}
message DeleteEntityTaskRequest {
    // 数据id
    uint64 id = 1;
    string schema_name = 2;
}
message UpdateEntityTaskStatusRequest {
    // 数据id
    uint64 id = 1;
    string schema_name = 2;
    // task 状态
    string status = 3;
}
message RunEntityTaskRequest {
    // 数据id
    uint64 id = 1;
    string schema_name = 2;
}
message ListEntityTaskResponse {
    repeated EntityTask rows = 1;
    int32 total = 2;
}
message EntityTask {
    // task id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    // job id
    uint64 job_id = 4;
    // schema type(entity, relation)
    string schema_type = 5;
    // task 名称
    string name = 6;
    // 数据id
    uint64 record_id = 7;
    // 数据字段内容
    google.protobuf.Struct content = 8;
    //数据原先的字段内容
    google.protobuf.Struct content_from = 21;
    // task 状态
    string status = 9;
    // 数据被批处理状态
    string process_status = 10;
    // task动作
    string action = 11;
    // 是否立即执行
    bool immediate = 12;
    // 开始执行时间
    string start = 13;
    // 最后一次开始执行时间
    string last_start = 14;
    // 最后一次结束时间
    string last_end = 15;
    // 重试次数
    int32 retry = 16;
    // 创建时间
    string created = 17;
    // 最后一次修改时间
    string updated = 18;
    // 创建者
    uint64 created_by = 19;
    // 最后一次修改者
    uint64 updated_by = 20;

}

message GetChildrenEntityIdsRequest {
    // schema 名称
    string schema_name = 1;
    // 按id列表查询
    repeated uint64 ids = 2;
}
message GetChildrenEntityIdsResponse {
    repeated uint64 children_ids = 1;
}
message GetParentEntityIdsRequest {
    // schema 名称
    string schema_name = 1;
    // 按id列表查询
    repeated uint64 ids = 2;
}
message GetParentEntityIdsResponse {
    repeated uint64 parent_ids = 1;
}

message RefreshEntityMVRequest {
    string schema_name = 1;
}
message RefreshEntityMVResponse {
    string result = 1;
}

//
message UpdateEntityBatchRequest {
    repeated UpdateEntityRequest entitys = 1;
}
message UpdateEntityBatchResponse {
    string message = 1;
    string code = 2;
    repeated uint64 error_ids = 3;
}

message AddEntityBatchRequest {
    repeated AddEntityRequest entitys = 1;
}
message AddEntityBatchResponse {
    string message = 1;
    string code = 2;
    repeated uint64 error_ids = 3;
}
message SqlQueryRequest {
    string sql = 1;
}
