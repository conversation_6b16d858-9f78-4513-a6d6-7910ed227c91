# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from metadata.entity import entity_pb2 as metadata_dot_entity_dot_entity__pb2


class EntityServiceStub(object):
  """EntityService 用于维护主档Entity数据
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.AddEntity = channel.unary_unary(
        '/entity.EntityService/AddEntity',
        request_serializer=metadata_dot_entity_dot_entity__pb2.AddEntityRequest.SerializeToString,
        response_deserializer=metadata_dot_entity_dot_entity__pb2.Entity.FromString,
        )
    self.UpdateEntity = channel.unary_unary(
        '/entity.EntityService/UpdateEntity',
        request_serializer=metadata_dot_entity_dot_entity__pb2.UpdateEntityRequest.SerializeToString,
        response_deserializer=metadata_dot_entity_dot_entity__pb2.Entity.FromString,
        )
    self.SyncUpdateEntity = channel.unary_unary(
        '/entity.EntityService/SyncUpdateEntity',
        request_serializer=metadata_dot_entity_dot_entity__pb2.SyncEntityRequest.SerializeToString,
        response_deserializer=metadata_dot_entity_dot_entity__pb2.Entity.FromString,
        )
    self.GetEntityById = channel.unary_unary(
        '/entity.EntityService/GetEntityById',
        request_serializer=metadata_dot_entity_dot_entity__pb2.GetEntityByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_entity_dot_entity__pb2.Entity.FromString,
        )
    self.ListEntity = channel.unary_unary(
        '/entity.EntityService/ListEntity',
        request_serializer=metadata_dot_entity_dot_entity__pb2.ListEntityRequest.SerializeToString,
        response_deserializer=metadata_dot_entity_dot_entity__pb2.ListEntityResponse.FromString,
        )
    self.UpdateEntityState = channel.unary_unary(
        '/entity.EntityService/UpdateEntityState',
        request_serializer=metadata_dot_entity_dot_entity__pb2.UpdateEntityStateRequest.SerializeToString,
        response_deserializer=metadata_dot_entity_dot_entity__pb2.Entity.FromString,
        )
    self.ProcessEntityPendingChanges = channel.unary_unary(
        '/entity.EntityService/ProcessEntityPendingChanges',
        request_serializer=metadata_dot_entity_dot_entity__pb2.ProcessEntityPendingChangesRequest.SerializeToString,
        response_deserializer=metadata_dot_entity_dot_entity__pb2.Entity.FromString,
        )
    self.CreateEntityTaskFromPendingChanges = channel.unary_unary(
        '/entity.EntityService/CreateEntityTaskFromPendingChanges',
        request_serializer=metadata_dot_entity_dot_entity__pb2.CreateEntityTaskFromPendingChangesRequest.SerializeToString,
        response_deserializer=metadata_dot_entity_dot_entity__pb2.EntityTask.FromString,
        )
    self.UpdateEntityTask = channel.unary_unary(
        '/entity.EntityService/UpdateEntityTask',
        request_serializer=metadata_dot_entity_dot_entity__pb2.UpdateEntityTaskRequest.SerializeToString,
        response_deserializer=metadata_dot_entity_dot_entity__pb2.EntityTask.FromString,
        )
    self.UpdateEntityTaskStatus = channel.unary_unary(
        '/entity.EntityService/UpdateEntityTaskStatus',
        request_serializer=metadata_dot_entity_dot_entity__pb2.UpdateEntityTaskStatusRequest.SerializeToString,
        response_deserializer=metadata_dot_entity_dot_entity__pb2.EntityTask.FromString,
        )
    self.DeleteEntityTask = channel.unary_unary(
        '/entity.EntityService/DeleteEntityTask',
        request_serializer=metadata_dot_entity_dot_entity__pb2.DeleteEntityTaskRequest.SerializeToString,
        response_deserializer=metadata_dot_entity_dot_entity__pb2.EntityTask.FromString,
        )
    self.GetEntityTaskById = channel.unary_unary(
        '/entity.EntityService/GetEntityTaskById',
        request_serializer=metadata_dot_entity_dot_entity__pb2.GetEntityTaskByIdRequest.SerializeToString,
        response_deserializer=metadata_dot_entity_dot_entity__pb2.EntityTask.FromString,
        )
    self.ListEntityTask = channel.unary_unary(
        '/entity.EntityService/ListEntityTask',
        request_serializer=metadata_dot_entity_dot_entity__pb2.ListEntityTaskRequest.SerializeToString,
        response_deserializer=metadata_dot_entity_dot_entity__pb2.ListEntityTaskResponse.FromString,
        )
    self.GetChildrenEntityIds = channel.unary_unary(
        '/entity.EntityService/GetChildrenEntityIds',
        request_serializer=metadata_dot_entity_dot_entity__pb2.GetChildrenEntityIdsRequest.SerializeToString,
        response_deserializer=metadata_dot_entity_dot_entity__pb2.GetChildrenEntityIdsResponse.FromString,
        )
    self.GetParentEntityIds = channel.unary_unary(
        '/entity.EntityService/GetParentEntityIds',
        request_serializer=metadata_dot_entity_dot_entity__pb2.GetParentEntityIdsRequest.SerializeToString,
        response_deserializer=metadata_dot_entity_dot_entity__pb2.GetParentEntityIdsResponse.FromString,
        )
    self.RefreshEntityMV = channel.unary_unary(
        '/entity.EntityService/RefreshEntityMV',
        request_serializer=metadata_dot_entity_dot_entity__pb2.RefreshEntityMVRequest.SerializeToString,
        response_deserializer=metadata_dot_entity_dot_entity__pb2.RefreshEntityMVResponse.FromString,
        )
    self.UpdateEntityBatch = channel.unary_unary(
        '/entity.EntityService/UpdateEntityBatch',
        request_serializer=metadata_dot_entity_dot_entity__pb2.UpdateEntityBatchRequest.SerializeToString,
        response_deserializer=metadata_dot_entity_dot_entity__pb2.UpdateEntityBatchResponse.FromString,
        )
    self.AddEntityBatch = channel.unary_unary(
        '/entity.EntityService/AddEntityBatch',
        request_serializer=metadata_dot_entity_dot_entity__pb2.AddEntityBatchRequest.SerializeToString,
        response_deserializer=metadata_dot_entity_dot_entity__pb2.AddEntityBatchResponse.FromString,
        )
    self.SqlQuery = channel.unary_unary(
        '/entity.EntityService/SqlQuery',
        request_serializer=metadata_dot_entity_dot_entity__pb2.SqlQueryRequest.SerializeToString,
        response_deserializer=metadata_dot_entity_dot_entity__pb2.ListEntityResponse.FromString,
        )


class EntityServiceServicer(object):
  """EntityService 用于维护主档Entity数据
  """

  def AddEntity(self, request, context):
    """添加Entity
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateEntity(self, request, context):
    """修改Entity
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SyncUpdateEntity(self, request, context):
    """同步Entity
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetEntityById(self, request, context):
    """根据id获取Entity
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListEntity(self, request, context):
    """查询 Entity 列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateEntityState(self, request, context):
    """修改Entity数据状态
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ProcessEntityPendingChanges(self, request, context):
    """Apply或者Cancel pending changes
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateEntityTaskFromPendingChanges(self, request, context):
    """基于变更创建一个task
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateEntityTask(self, request, context):
    """修改Entity Task
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateEntityTaskStatus(self, request, context):
    """修改Entity Task 状态
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteEntityTask(self, request, context):
    """删除Entity Task 状态
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetEntityTaskById(self, request, context):
    """根据id获取Entity Task
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListEntityTask(self, request, context):
    """查询 Entity Task 列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetChildrenEntityIds(self, request, context):
    """根据id列表获取所有id的子节点以及子孙节点
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetParentEntityIds(self, request, context):
    """根据id列表获取所有id的上级节点
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RefreshEntityMV(self, request, context):
    """刷新物化视图
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateEntityBatch(self, request, context):
    """修改EntityBatch
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AddEntityBatch(self, request, context):
    """添加EntityBatch
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SqlQuery(self, request, context):
    """查询 Entity 列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_EntityServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'AddEntity': grpc.unary_unary_rpc_method_handler(
          servicer.AddEntity,
          request_deserializer=metadata_dot_entity_dot_entity__pb2.AddEntityRequest.FromString,
          response_serializer=metadata_dot_entity_dot_entity__pb2.Entity.SerializeToString,
      ),
      'UpdateEntity': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateEntity,
          request_deserializer=metadata_dot_entity_dot_entity__pb2.UpdateEntityRequest.FromString,
          response_serializer=metadata_dot_entity_dot_entity__pb2.Entity.SerializeToString,
      ),
      'SyncUpdateEntity': grpc.unary_unary_rpc_method_handler(
          servicer.SyncUpdateEntity,
          request_deserializer=metadata_dot_entity_dot_entity__pb2.SyncEntityRequest.FromString,
          response_serializer=metadata_dot_entity_dot_entity__pb2.Entity.SerializeToString,
      ),
      'GetEntityById': grpc.unary_unary_rpc_method_handler(
          servicer.GetEntityById,
          request_deserializer=metadata_dot_entity_dot_entity__pb2.GetEntityByIdRequest.FromString,
          response_serializer=metadata_dot_entity_dot_entity__pb2.Entity.SerializeToString,
      ),
      'ListEntity': grpc.unary_unary_rpc_method_handler(
          servicer.ListEntity,
          request_deserializer=metadata_dot_entity_dot_entity__pb2.ListEntityRequest.FromString,
          response_serializer=metadata_dot_entity_dot_entity__pb2.ListEntityResponse.SerializeToString,
      ),
      'UpdateEntityState': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateEntityState,
          request_deserializer=metadata_dot_entity_dot_entity__pb2.UpdateEntityStateRequest.FromString,
          response_serializer=metadata_dot_entity_dot_entity__pb2.Entity.SerializeToString,
      ),
      'ProcessEntityPendingChanges': grpc.unary_unary_rpc_method_handler(
          servicer.ProcessEntityPendingChanges,
          request_deserializer=metadata_dot_entity_dot_entity__pb2.ProcessEntityPendingChangesRequest.FromString,
          response_serializer=metadata_dot_entity_dot_entity__pb2.Entity.SerializeToString,
      ),
      'CreateEntityTaskFromPendingChanges': grpc.unary_unary_rpc_method_handler(
          servicer.CreateEntityTaskFromPendingChanges,
          request_deserializer=metadata_dot_entity_dot_entity__pb2.CreateEntityTaskFromPendingChangesRequest.FromString,
          response_serializer=metadata_dot_entity_dot_entity__pb2.EntityTask.SerializeToString,
      ),
      'UpdateEntityTask': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateEntityTask,
          request_deserializer=metadata_dot_entity_dot_entity__pb2.UpdateEntityTaskRequest.FromString,
          response_serializer=metadata_dot_entity_dot_entity__pb2.EntityTask.SerializeToString,
      ),
      'UpdateEntityTaskStatus': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateEntityTaskStatus,
          request_deserializer=metadata_dot_entity_dot_entity__pb2.UpdateEntityTaskStatusRequest.FromString,
          response_serializer=metadata_dot_entity_dot_entity__pb2.EntityTask.SerializeToString,
      ),
      'DeleteEntityTask': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteEntityTask,
          request_deserializer=metadata_dot_entity_dot_entity__pb2.DeleteEntityTaskRequest.FromString,
          response_serializer=metadata_dot_entity_dot_entity__pb2.EntityTask.SerializeToString,
      ),
      'GetEntityTaskById': grpc.unary_unary_rpc_method_handler(
          servicer.GetEntityTaskById,
          request_deserializer=metadata_dot_entity_dot_entity__pb2.GetEntityTaskByIdRequest.FromString,
          response_serializer=metadata_dot_entity_dot_entity__pb2.EntityTask.SerializeToString,
      ),
      'ListEntityTask': grpc.unary_unary_rpc_method_handler(
          servicer.ListEntityTask,
          request_deserializer=metadata_dot_entity_dot_entity__pb2.ListEntityTaskRequest.FromString,
          response_serializer=metadata_dot_entity_dot_entity__pb2.ListEntityTaskResponse.SerializeToString,
      ),
      'GetChildrenEntityIds': grpc.unary_unary_rpc_method_handler(
          servicer.GetChildrenEntityIds,
          request_deserializer=metadata_dot_entity_dot_entity__pb2.GetChildrenEntityIdsRequest.FromString,
          response_serializer=metadata_dot_entity_dot_entity__pb2.GetChildrenEntityIdsResponse.SerializeToString,
      ),
      'GetParentEntityIds': grpc.unary_unary_rpc_method_handler(
          servicer.GetParentEntityIds,
          request_deserializer=metadata_dot_entity_dot_entity__pb2.GetParentEntityIdsRequest.FromString,
          response_serializer=metadata_dot_entity_dot_entity__pb2.GetParentEntityIdsResponse.SerializeToString,
      ),
      'RefreshEntityMV': grpc.unary_unary_rpc_method_handler(
          servicer.RefreshEntityMV,
          request_deserializer=metadata_dot_entity_dot_entity__pb2.RefreshEntityMVRequest.FromString,
          response_serializer=metadata_dot_entity_dot_entity__pb2.RefreshEntityMVResponse.SerializeToString,
      ),
      'UpdateEntityBatch': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateEntityBatch,
          request_deserializer=metadata_dot_entity_dot_entity__pb2.UpdateEntityBatchRequest.FromString,
          response_serializer=metadata_dot_entity_dot_entity__pb2.UpdateEntityBatchResponse.SerializeToString,
      ),
      'AddEntityBatch': grpc.unary_unary_rpc_method_handler(
          servicer.AddEntityBatch,
          request_deserializer=metadata_dot_entity_dot_entity__pb2.AddEntityBatchRequest.FromString,
          response_serializer=metadata_dot_entity_dot_entity__pb2.AddEntityBatchResponse.SerializeToString,
      ),
      'SqlQuery': grpc.unary_unary_rpc_method_handler(
          servicer.SqlQuery,
          request_deserializer=metadata_dot_entity_dot_entity__pb2.SqlQueryRequest.FromString,
          response_serializer=metadata_dot_entity_dot_entity__pb2.ListEntityResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'entity.EntityService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
