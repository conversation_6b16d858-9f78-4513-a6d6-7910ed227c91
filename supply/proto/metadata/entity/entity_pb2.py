# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: metadata/entity/entity.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='metadata/entity/entity.proto',
  package='entity',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1cmetadata/entity/entity.proto\x12\x06\x65ntity\x1a\x1cgoogle/api/annotations.proto\x1a\x1cgoogle/protobuf/struct.proto\"~\n\x10\x41\x64\x64\x45ntityRequest\x12\x13\n\x0bschema_name\x18\x01 \x01(\t\x12\x13\n\x0b\x61uto_enable\x18\x02 \x01(\x08\x12\'\n\x06\x66ields\x18\x03 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x04 \x01(\t\x12\n\n\x02id\x18\x05 \x01(\t\"\x93\x01\n\x11SyncEntityRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x13\n\x0bschema_name\x18\x02 \x01(\t\x12\x12\n\nauto_apply\x18\x03 \x01(\x08\x12\x13\n\x0b\x61uto_enable\x18\x04 \x01(\x08\x12\'\n\x06\x66ields\x18\x05 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x06 \x01(\t\"\xaa\x01\n\x13UpdateEntityRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x13\n\x0bschema_name\x18\x02 \x01(\t\x12\x12\n\nauto_apply\x18\x03 \x01(\x08\x12\'\n\x06\x66ields\x18\x04 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x05 \x01(\t\x12\x11\n\tis_merged\x18\x06 \x01(\x08\x12\x15\n\rapply_current\x18\x07 \x01(\x08\"\x8f\x02\n\x14GetEntityByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x13\n\x0bschema_name\x18\x02 \x01(\t\x12\x15\n\rinclude_state\x18\x03 \x01(\x08\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\x10\n\x08relation\x18\x05 \x01(\t\x12\x15\n\rreturn_fields\x18\x06 \x01(\t\x12\x1f\n\x17include_pending_changes\x18\x07 \x01(\x08\x12\x1e\n\x16include_pending_record\x18\x08 \x01(\x08\x12\x17\n\x0finclude_parents\x18\t \x01(\x08\x12\x0b\n\x03lan\x18\n \x01(\t\x12!\n\x19include_all_localizations\x18\x0b \x01(\x08\"\x9b\x04\n\x11ListEntityRequest\x12\x13\n\x0bschema_name\x18\x01 \x01(\t\x12\r\n\x05state\x18\x02 \x01(\t\x12\x15\n\rinclude_state\x18\x03 \x01(\x08\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\x10\n\x08relation\x18\x05 \x01(\t\x12\x15\n\rreturn_fields\x18\x06 \x01(\t\x12\x1f\n\x17include_pending_changes\x18\x07 \x01(\x08\x12\x1e\n\x16include_pending_record\x18\x08 \x01(\x08\x12\x17\n\x0finclude_parents\x18\t \x01(\x08\x12\r\n\x05limit\x18\n \x01(\x05\x12\x0e\n\x06offset\x18\x0b \x01(\x05\x12\x0c\n\x04sort\x18\x0c \x01(\t\x12\r\n\x05order\x18\r \x01(\t\x12\x15\n\rinclude_total\x18\x0e \x01(\x08\x12\x0e\n\x06search\x18\x0f \x01(\t\x12\x15\n\rsearch_fields\x18\x10 \x01(\t\x12\x0b\n\x03ids\x18\x11 \x03(\x04\x12(\n\x07\x66ilters\x18\x12 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x31\n\x10relation_filters\x18\x13 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x14 \x01(\t\x12!\n\x19include_all_localizations\x18\x15 \x01(\x08\x12\x12\n\nis_request\x18\x16 \x01(\x08\x12\x12\n\nget_common\x18\x17 \x01(\x08\"W\n\x18UpdateEntityStateRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x13\n\x0bschema_name\x18\x02 \x01(\t\x12\r\n\x05state\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\"U\n\"ProcessEntityPendingChangesRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x13\n\x0bschema_name\x18\x02 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x03 \x01(\t\"A\n\x12ListEntityResponse\x12\x1c\n\x04rows\x18\x01 \x03(\x0b\x32\x0e.entity.Entity\x12\r\n\x05total\x18\x02 \x01(\x05\"\xa2\x03\n\x06\x45ntity\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x11\n\tparent_id\x18\x04 \x01(\x04\x12\x11\n\tschema_id\x18\x05 \x01(\x04\x12\x13\n\x0bschema_name\x18\x06 \x01(\t\x12\r\n\x05state\x18\x07 \x01(\t\x12\'\n\x06\x66ields\x18\x08 \x01(\x0b\x32\x17.google.protobuf.Struct\x12/\n\x0e\x66ields_pending\x18\t \x01(\x0b\x32\x17.google.protobuf.Struct\x12/\n\x0erecord_pending\x18\n \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0f\n\x07pending\x18\x0b \x01(\x08\x12\x0f\n\x07\x63reated\x18\x0c \x01(\t\x12\x0f\n\x07updated\x18\r \x01(\t\x12\x12\n\ncreated_by\x18\x0e \x01(\t\x12\x12\n\nupdated_by\x18\x0f \x01(\t\x12\x16\n\x0eprocess_status\x18\x10 \x01(\t\x12\x1e\n\x06parent\x18\x11 \x01(\x0b\x32\x0e.entity.Entity\"!\n\x0f\x44\x65\x66\x61ultResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\x99\x01\n)CreateEntityTaskFromPendingChangesRequest\x12\x11\n\trecord_id\x18\x01 \x01(\x04\x12\x13\n\x0bschema_name\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x11\n\timmediate\x18\x04 \x01(\x08\x12\r\n\x05start\x18\x05 \x01(\t\x12\x14\n\x0c\x61uto_approve\x18\x06 \x01(\x08\";\n\x18GetEntityTaskByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x13\n\x0bschema_name\x18\x02 \x01(\t\"\xd2\x01\n\x15ListEntityTaskRequest\x12\x12\n\nrecord_ids\x18\x01 \x03(\x04\x12\x13\n\x0bschema_name\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x03(\t\x12\x16\n\x0eprocess_status\x18\x04 \x03(\t\x12\r\n\x05limit\x18\x05 \x01(\x05\x12\x0e\n\x06offset\x18\x06 \x01(\x05\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12\x0e\n\x06search\x18\x08 \x01(\t\x12\x15\n\rsearch_fields\x18\t \x01(\t\x12\x0b\n\x03ids\x18\n \x03(\x04\"c\n\x17UpdateEntityTaskRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x13\n\x0bschema_name\x18\x02 \x01(\t\x12\'\n\x06\x66ields\x18\x03 \x01(\x0b\x32\x17.google.protobuf.Struct\":\n\x17\x44\x65leteEntityTaskRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x13\n\x0bschema_name\x18\x02 \x01(\t\"P\n\x1dUpdateEntityTaskStatusRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x13\n\x0bschema_name\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\"7\n\x14RunEntityTaskRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x13\n\x0bschema_name\x18\x02 \x01(\t\"I\n\x16ListEntityTaskResponse\x12 \n\x04rows\x18\x01 \x03(\x0b\x32\x12.entity.EntityTask\x12\r\n\x05total\x18\x02 \x01(\x05\"\xb6\x03\n\nEntityTask\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x0e\n\x06job_id\x18\x04 \x01(\x04\x12\x13\n\x0bschema_type\x18\x05 \x01(\t\x12\x0c\n\x04name\x18\x06 \x01(\t\x12\x11\n\trecord_id\x18\x07 \x01(\x04\x12(\n\x07\x63ontent\x18\x08 \x01(\x0b\x32\x17.google.protobuf.Struct\x12-\n\x0c\x63ontent_from\x18\x15 \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0e\n\x06status\x18\t \x01(\t\x12\x16\n\x0eprocess_status\x18\n \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x0b \x01(\t\x12\x11\n\timmediate\x18\x0c \x01(\x08\x12\r\n\x05start\x18\r \x01(\t\x12\x12\n\nlast_start\x18\x0e \x01(\t\x12\x10\n\x08last_end\x18\x0f \x01(\t\x12\r\n\x05retry\x18\x10 \x01(\x05\x12\x0f\n\x07\x63reated\x18\x11 \x01(\t\x12\x0f\n\x07updated\x18\x12 \x01(\t\x12\x12\n\ncreated_by\x18\x13 \x01(\x04\x12\x12\n\nupdated_by\x18\x14 \x01(\x04\"?\n\x1bGetChildrenEntityIdsRequest\x12\x13\n\x0bschema_name\x18\x01 \x01(\t\x12\x0b\n\x03ids\x18\x02 \x03(\x04\"4\n\x1cGetChildrenEntityIdsResponse\x12\x14\n\x0c\x63hildren_ids\x18\x01 \x03(\x04\"=\n\x19GetParentEntityIdsRequest\x12\x13\n\x0bschema_name\x18\x01 \x01(\t\x12\x0b\n\x03ids\x18\x02 \x03(\x04\"0\n\x1aGetParentEntityIdsResponse\x12\x12\n\nparent_ids\x18\x01 \x03(\x04\"-\n\x16RefreshEntityMVRequest\x12\x13\n\x0bschema_name\x18\x01 \x01(\t\")\n\x17RefreshEntityMVResponse\x12\x0e\n\x06result\x18\x01 \x01(\t\"H\n\x18UpdateEntityBatchRequest\x12,\n\x07\x65ntitys\x18\x01 \x03(\x0b\x32\x1b.entity.UpdateEntityRequest\"M\n\x19UpdateEntityBatchResponse\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x11\n\terror_ids\x18\x03 \x03(\x04\"B\n\x15\x41\x64\x64\x45ntityBatchRequest\x12)\n\x07\x65ntitys\x18\x01 \x03(\x0b\x32\x18.entity.AddEntityRequest\"J\n\x16\x41\x64\x64\x45ntityBatchResponse\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x11\n\terror_ids\x18\x03 \x03(\x04\"\x1e\n\x0fSqlQueryRequest\x12\x0b\n\x03sql\x18\x01 \x01(\t2\xad\x14\n\rEntityService\x12k\n\tAddEntity\x12\x18.entity.AddEntityRequest\x1a\x0e.entity.Entity\"4\x82\xd3\xe4\x93\x02.\")/api/v2/metadata/entity/{schema_name}/add:\x01*\x12t\n\x0cUpdateEntity\x12\x1b.entity.UpdateEntityRequest\x1a\x0e.entity.Entity\"7\x82\xd3\xe4\x93\x02\x31\",/api/v2/metadata/entity/{schema_name}/update:\x01*\x12{\n\x10SyncUpdateEntity\x12\x19.entity.SyncEntityRequest\x1a\x0e.entity.Entity\"<\x82\xd3\xe4\x93\x02\x36\"1/api/v2/metadata/entity/{schema_name}/sync/update:\x01*\x12w\n\rGetEntityById\x12\x1c.entity.GetEntityByIdRequest\x1a\x0e.entity.Entity\"8\x82\xd3\xe4\x93\x02\x32\x12\x30/api/v2/metadata/entity/{schema_name}/by/id/{id}\x12{\n\nListEntity\x12\x19.entity.ListEntityRequest\x1a\x1a.entity.ListEntityResponse\"6\x82\xd3\xe4\x93\x02\x30\"+/api/v2/metadata/entity/{schema_name}/query:\x01*\x12\x84\x01\n\x11UpdateEntityState\x12 .entity.UpdateEntityStateRequest\x1a\x0e.entity.Entity\"=\x82\xd3\xe4\x93\x02\x37\"2/api/v2/metadata/entity/{schema_name}/state/update:\x01*\x12\x9c\x01\n\x1bProcessEntityPendingChanges\x12*.entity.ProcessEntityPendingChangesRequest\x1a\x0e.entity.Entity\"A\x82\xd3\xe4\x93\x02;\"6/api/v2/metadata/entity/{schema_name}/changes/{action}:\x01*\x12\xad\x01\n\"CreateEntityTaskFromPendingChanges\x12\x31.entity.CreateEntityTaskFromPendingChangesRequest\x1a\x12.entity.EntityTask\"@\x82\xd3\xe4\x93\x02:\"5/api/v2/metadata/entity/{schema_name}/changes/to/task:\x01*\x12\x85\x01\n\x10UpdateEntityTask\x12\x1f.entity.UpdateEntityTaskRequest\x1a\x12.entity.EntityTask\"<\x82\xd3\xe4\x93\x02\x36\"1/api/v2/metadata/entity/{schema_name}/task/update:\x01*\x12\x98\x01\n\x16UpdateEntityTaskStatus\x12%.entity.UpdateEntityTaskStatusRequest\x1a\x12.entity.EntityTask\"C\x82\xd3\xe4\x93\x02=\"8/api/v2/metadata/entity/{schema_name}/task/update/status:\x01*\x12\x86\x01\n\x10\x44\x65leteEntityTask\x12\x1f.entity.DeleteEntityTaskRequest\x1a\x12.entity.EntityTask\"=\x82\xd3\xe4\x93\x02\x37*5/api/v2/metadata/entity/{schema_name}/task/by/id/{id}\x12\x88\x01\n\x11GetEntityTaskById\x12 .entity.GetEntityTaskByIdRequest\x1a\x12.entity.EntityTask\"=\x82\xd3\xe4\x93\x02\x37\x12\x35/api/v2/metadata/entity/{schema_name}/task/by/id/{id}\x12\x8c\x01\n\x0eListEntityTask\x12\x1d.entity.ListEntityTaskRequest\x1a\x1e.entity.ListEntityTaskResponse\";\x82\xd3\xe4\x93\x02\x35\"0/api/v2/metadata/entity/{schema_name}/task/query:\x01*\x12\x9f\x01\n\x14GetChildrenEntityIds\x12#.entity.GetChildrenEntityIdsRequest\x1a$.entity.GetChildrenEntityIdsResponse\"<\x82\xd3\xe4\x93\x02\x36\"1/api/v2/metadata/entity/{schema_name}/childrenids:\x01*\x12\x97\x01\n\x12GetParentEntityIds\x12!.entity.GetParentEntityIdsRequest\x1a\".entity.GetParentEntityIdsResponse\":\x82\xd3\xe4\x93\x02\x34\"//api/v2/metadata/entity/{schema_name}/parentids:\x01*\x12t\n\x0fRefreshEntityMV\x12\x1e.entity.RefreshEntityMVRequest\x1a\x1f.entity.RefreshEntityMVResponse\" \x82\xd3\xe4\x93\x02\x1a\"\x18/api/v2/metadata/refresh\x12\x89\x01\n\x11UpdateEntityBatch\x12 .entity.UpdateEntityBatchRequest\x1a!.entity.UpdateEntityBatchResponse\"/\x82\xd3\xe4\x93\x02)\"$/api/v2/metadata/entity/update/batch:\x01*\x12}\n\x0e\x41\x64\x64\x45ntityBatch\x12\x1d.entity.AddEntityBatchRequest\x1a\x1e.entity.AddEntityBatchResponse\",\x82\xd3\xe4\x93\x02&\"!/api/v2/metadata/entity/add/batch:\x01*\x12m\n\x08SqlQuery\x12\x17.entity.SqlQueryRequest\x1a\x1a.entity.ListEntityResponse\",\x82\xd3\xe4\x93\x02&\"!/api/v2/metadata/entity/query/sql:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_struct__pb2.DESCRIPTOR,])




_ADDENTITYREQUEST = _descriptor.Descriptor(
  name='AddEntityRequest',
  full_name='entity.AddEntityRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='entity.AddEntityRequest.schema_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auto_enable', full_name='entity.AddEntityRequest.auto_enable', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='entity.AddEntityRequest.fields', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='entity.AddEntityRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='entity.AddEntityRequest.id', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=100,
  serialized_end=226,
)


_SYNCENTITYREQUEST = _descriptor.Descriptor(
  name='SyncEntityRequest',
  full_name='entity.SyncEntityRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='entity.SyncEntityRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='entity.SyncEntityRequest.schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auto_apply', full_name='entity.SyncEntityRequest.auto_apply', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auto_enable', full_name='entity.SyncEntityRequest.auto_enable', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='entity.SyncEntityRequest.fields', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='entity.SyncEntityRequest.lan', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=229,
  serialized_end=376,
)


_UPDATEENTITYREQUEST = _descriptor.Descriptor(
  name='UpdateEntityRequest',
  full_name='entity.UpdateEntityRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='entity.UpdateEntityRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='entity.UpdateEntityRequest.schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auto_apply', full_name='entity.UpdateEntityRequest.auto_apply', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='entity.UpdateEntityRequest.fields', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='entity.UpdateEntityRequest.lan', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_merged', full_name='entity.UpdateEntityRequest.is_merged', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='apply_current', full_name='entity.UpdateEntityRequest.apply_current', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=379,
  serialized_end=549,
)


_GETENTITYBYIDREQUEST = _descriptor.Descriptor(
  name='GetEntityByIdRequest',
  full_name='entity.GetEntityByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='entity.GetEntityByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='entity.GetEntityByIdRequest.schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_state', full_name='entity.GetEntityByIdRequest.include_state', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='entity.GetEntityByIdRequest.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation', full_name='entity.GetEntityByIdRequest.relation', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='entity.GetEntityByIdRequest.return_fields', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_pending_changes', full_name='entity.GetEntityByIdRequest.include_pending_changes', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_pending_record', full_name='entity.GetEntityByIdRequest.include_pending_record', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_parents', full_name='entity.GetEntityByIdRequest.include_parents', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='entity.GetEntityByIdRequest.lan', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_all_localizations', full_name='entity.GetEntityByIdRequest.include_all_localizations', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=552,
  serialized_end=823,
)


_LISTENTITYREQUEST = _descriptor.Descriptor(
  name='ListEntityRequest',
  full_name='entity.ListEntityRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='entity.ListEntityRequest.schema_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='state', full_name='entity.ListEntityRequest.state', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_state', full_name='entity.ListEntityRequest.include_state', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='entity.ListEntityRequest.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation', full_name='entity.ListEntityRequest.relation', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='entity.ListEntityRequest.return_fields', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_pending_changes', full_name='entity.ListEntityRequest.include_pending_changes', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_pending_record', full_name='entity.ListEntityRequest.include_pending_record', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_parents', full_name='entity.ListEntityRequest.include_parents', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='entity.ListEntityRequest.limit', index=9,
      number=10, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='entity.ListEntityRequest.offset', index=10,
      number=11, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='entity.ListEntityRequest.sort', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='entity.ListEntityRequest.order', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='entity.ListEntityRequest.include_total', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='entity.ListEntityRequest.search', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='entity.ListEntityRequest.search_fields', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='entity.ListEntityRequest.ids', index=16,
      number=17, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='entity.ListEntityRequest.filters', index=17,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation_filters', full_name='entity.ListEntityRequest.relation_filters', index=18,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='entity.ListEntityRequest.lan', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_all_localizations', full_name='entity.ListEntityRequest.include_all_localizations', index=20,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_request', full_name='entity.ListEntityRequest.is_request', index=21,
      number=22, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='get_common', full_name='entity.ListEntityRequest.get_common', index=22,
      number=23, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=826,
  serialized_end=1365,
)


_UPDATEENTITYSTATEREQUEST = _descriptor.Descriptor(
  name='UpdateEntityStateRequest',
  full_name='entity.UpdateEntityStateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='entity.UpdateEntityStateRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='entity.UpdateEntityStateRequest.schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='state', full_name='entity.UpdateEntityStateRequest.state', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='entity.UpdateEntityStateRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1367,
  serialized_end=1454,
)


_PROCESSENTITYPENDINGCHANGESREQUEST = _descriptor.Descriptor(
  name='ProcessEntityPendingChangesRequest',
  full_name='entity.ProcessEntityPendingChangesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='entity.ProcessEntityPendingChangesRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='entity.ProcessEntityPendingChangesRequest.schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='entity.ProcessEntityPendingChangesRequest.action', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1456,
  serialized_end=1541,
)


_LISTENTITYRESPONSE = _descriptor.Descriptor(
  name='ListEntityResponse',
  full_name='entity.ListEntityResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='entity.ListEntityResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='entity.ListEntityResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1543,
  serialized_end=1608,
)


_ENTITY = _descriptor.Descriptor(
  name='Entity',
  full_name='entity.Entity',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='entity.Entity.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='entity.Entity.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='entity.Entity.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='parent_id', full_name='entity.Entity.parent_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_id', full_name='entity.Entity.schema_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='entity.Entity.schema_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='state', full_name='entity.Entity.state', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='entity.Entity.fields', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields_pending', full_name='entity.Entity.fields_pending', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='record_pending', full_name='entity.Entity.record_pending', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pending', full_name='entity.Entity.pending', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='entity.Entity.created', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='entity.Entity.updated', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='entity.Entity.created_by', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='entity.Entity.updated_by', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='entity.Entity.process_status', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='parent', full_name='entity.Entity.parent', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1611,
  serialized_end=2029,
)


_DEFAULTRESPONSE = _descriptor.Descriptor(
  name='DefaultResponse',
  full_name='entity.DefaultResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='entity.DefaultResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2031,
  serialized_end=2064,
)


_CREATEENTITYTASKFROMPENDINGCHANGESREQUEST = _descriptor.Descriptor(
  name='CreateEntityTaskFromPendingChangesRequest',
  full_name='entity.CreateEntityTaskFromPendingChangesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='record_id', full_name='entity.CreateEntityTaskFromPendingChangesRequest.record_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='entity.CreateEntityTaskFromPendingChangesRequest.schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='entity.CreateEntityTaskFromPendingChangesRequest.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='immediate', full_name='entity.CreateEntityTaskFromPendingChangesRequest.immediate', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='entity.CreateEntityTaskFromPendingChangesRequest.start', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auto_approve', full_name='entity.CreateEntityTaskFromPendingChangesRequest.auto_approve', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2067,
  serialized_end=2220,
)


_GETENTITYTASKBYIDREQUEST = _descriptor.Descriptor(
  name='GetEntityTaskByIdRequest',
  full_name='entity.GetEntityTaskByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='entity.GetEntityTaskByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='entity.GetEntityTaskByIdRequest.schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2222,
  serialized_end=2281,
)


_LISTENTITYTASKREQUEST = _descriptor.Descriptor(
  name='ListEntityTaskRequest',
  full_name='entity.ListEntityTaskRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='record_ids', full_name='entity.ListEntityTaskRequest.record_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='entity.ListEntityTaskRequest.schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='entity.ListEntityTaskRequest.status', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='entity.ListEntityTaskRequest.process_status', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='entity.ListEntityTaskRequest.limit', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='entity.ListEntityTaskRequest.offset', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='entity.ListEntityTaskRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='entity.ListEntityTaskRequest.search', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='entity.ListEntityTaskRequest.search_fields', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='entity.ListEntityTaskRequest.ids', index=9,
      number=10, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2284,
  serialized_end=2494,
)


_UPDATEENTITYTASKREQUEST = _descriptor.Descriptor(
  name='UpdateEntityTaskRequest',
  full_name='entity.UpdateEntityTaskRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='entity.UpdateEntityTaskRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='entity.UpdateEntityTaskRequest.schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fields', full_name='entity.UpdateEntityTaskRequest.fields', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2496,
  serialized_end=2595,
)


_DELETEENTITYTASKREQUEST = _descriptor.Descriptor(
  name='DeleteEntityTaskRequest',
  full_name='entity.DeleteEntityTaskRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='entity.DeleteEntityTaskRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='entity.DeleteEntityTaskRequest.schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2597,
  serialized_end=2655,
)


_UPDATEENTITYTASKSTATUSREQUEST = _descriptor.Descriptor(
  name='UpdateEntityTaskStatusRequest',
  full_name='entity.UpdateEntityTaskStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='entity.UpdateEntityTaskStatusRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='entity.UpdateEntityTaskStatusRequest.schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='entity.UpdateEntityTaskStatusRequest.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2657,
  serialized_end=2737,
)


_RUNENTITYTASKREQUEST = _descriptor.Descriptor(
  name='RunEntityTaskRequest',
  full_name='entity.RunEntityTaskRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='entity.RunEntityTaskRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='entity.RunEntityTaskRequest.schema_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2739,
  serialized_end=2794,
)


_LISTENTITYTASKRESPONSE = _descriptor.Descriptor(
  name='ListEntityTaskResponse',
  full_name='entity.ListEntityTaskResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='entity.ListEntityTaskResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='entity.ListEntityTaskResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2796,
  serialized_end=2869,
)


_ENTITYTASK = _descriptor.Descriptor(
  name='EntityTask',
  full_name='entity.EntityTask',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='entity.EntityTask.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='entity.EntityTask.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='entity.EntityTask.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='job_id', full_name='entity.EntityTask.job_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schema_type', full_name='entity.EntityTask.schema_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='entity.EntityTask.name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='record_id', full_name='entity.EntityTask.record_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='content', full_name='entity.EntityTask.content', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='content_from', full_name='entity.EntityTask.content_from', index=8,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='entity.EntityTask.status', index=9,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='entity.EntityTask.process_status', index=10,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='entity.EntityTask.action', index=11,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='immediate', full_name='entity.EntityTask.immediate', index=12,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='entity.EntityTask.start', index=13,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='last_start', full_name='entity.EntityTask.last_start', index=14,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='last_end', full_name='entity.EntityTask.last_end', index=15,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='retry', full_name='entity.EntityTask.retry', index=16,
      number=16, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='entity.EntityTask.created', index=17,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='entity.EntityTask.updated', index=18,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='entity.EntityTask.created_by', index=19,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='entity.EntityTask.updated_by', index=20,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2872,
  serialized_end=3310,
)


_GETCHILDRENENTITYIDSREQUEST = _descriptor.Descriptor(
  name='GetChildrenEntityIdsRequest',
  full_name='entity.GetChildrenEntityIdsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='entity.GetChildrenEntityIdsRequest.schema_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='entity.GetChildrenEntityIdsRequest.ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3312,
  serialized_end=3375,
)


_GETCHILDRENENTITYIDSRESPONSE = _descriptor.Descriptor(
  name='GetChildrenEntityIdsResponse',
  full_name='entity.GetChildrenEntityIdsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='children_ids', full_name='entity.GetChildrenEntityIdsResponse.children_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3377,
  serialized_end=3429,
)


_GETPARENTENTITYIDSREQUEST = _descriptor.Descriptor(
  name='GetParentEntityIdsRequest',
  full_name='entity.GetParentEntityIdsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='entity.GetParentEntityIdsRequest.schema_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='entity.GetParentEntityIdsRequest.ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3431,
  serialized_end=3492,
)


_GETPARENTENTITYIDSRESPONSE = _descriptor.Descriptor(
  name='GetParentEntityIdsResponse',
  full_name='entity.GetParentEntityIdsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='parent_ids', full_name='entity.GetParentEntityIdsResponse.parent_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3494,
  serialized_end=3542,
)


_REFRESHENTITYMVREQUEST = _descriptor.Descriptor(
  name='RefreshEntityMVRequest',
  full_name='entity.RefreshEntityMVRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='schema_name', full_name='entity.RefreshEntityMVRequest.schema_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3544,
  serialized_end=3589,
)


_REFRESHENTITYMVRESPONSE = _descriptor.Descriptor(
  name='RefreshEntityMVResponse',
  full_name='entity.RefreshEntityMVResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='entity.RefreshEntityMVResponse.result', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3591,
  serialized_end=3632,
)


_UPDATEENTITYBATCHREQUEST = _descriptor.Descriptor(
  name='UpdateEntityBatchRequest',
  full_name='entity.UpdateEntityBatchRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='entitys', full_name='entity.UpdateEntityBatchRequest.entitys', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3634,
  serialized_end=3706,
)


_UPDATEENTITYBATCHRESPONSE = _descriptor.Descriptor(
  name='UpdateEntityBatchResponse',
  full_name='entity.UpdateEntityBatchResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='message', full_name='entity.UpdateEntityBatchResponse.message', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='entity.UpdateEntityBatchResponse.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error_ids', full_name='entity.UpdateEntityBatchResponse.error_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3708,
  serialized_end=3785,
)


_ADDENTITYBATCHREQUEST = _descriptor.Descriptor(
  name='AddEntityBatchRequest',
  full_name='entity.AddEntityBatchRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='entitys', full_name='entity.AddEntityBatchRequest.entitys', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3787,
  serialized_end=3853,
)


_ADDENTITYBATCHRESPONSE = _descriptor.Descriptor(
  name='AddEntityBatchResponse',
  full_name='entity.AddEntityBatchResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='message', full_name='entity.AddEntityBatchResponse.message', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='entity.AddEntityBatchResponse.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error_ids', full_name='entity.AddEntityBatchResponse.error_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3855,
  serialized_end=3929,
)


_SQLQUERYREQUEST = _descriptor.Descriptor(
  name='SqlQueryRequest',
  full_name='entity.SqlQueryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sql', full_name='entity.SqlQueryRequest.sql', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3931,
  serialized_end=3961,
)

_ADDENTITYREQUEST.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_SYNCENTITYREQUEST.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_UPDATEENTITYREQUEST.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTENTITYREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTENTITYREQUEST.fields_by_name['relation_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTENTITYRESPONSE.fields_by_name['rows'].message_type = _ENTITY
_ENTITY.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_ENTITY.fields_by_name['fields_pending'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_ENTITY.fields_by_name['record_pending'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_ENTITY.fields_by_name['parent'].message_type = _ENTITY
_UPDATEENTITYTASKREQUEST.fields_by_name['fields'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTENTITYTASKRESPONSE.fields_by_name['rows'].message_type = _ENTITYTASK
_ENTITYTASK.fields_by_name['content'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_ENTITYTASK.fields_by_name['content_from'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_UPDATEENTITYBATCHREQUEST.fields_by_name['entitys'].message_type = _UPDATEENTITYREQUEST
_ADDENTITYBATCHREQUEST.fields_by_name['entitys'].message_type = _ADDENTITYREQUEST
DESCRIPTOR.message_types_by_name['AddEntityRequest'] = _ADDENTITYREQUEST
DESCRIPTOR.message_types_by_name['SyncEntityRequest'] = _SYNCENTITYREQUEST
DESCRIPTOR.message_types_by_name['UpdateEntityRequest'] = _UPDATEENTITYREQUEST
DESCRIPTOR.message_types_by_name['GetEntityByIdRequest'] = _GETENTITYBYIDREQUEST
DESCRIPTOR.message_types_by_name['ListEntityRequest'] = _LISTENTITYREQUEST
DESCRIPTOR.message_types_by_name['UpdateEntityStateRequest'] = _UPDATEENTITYSTATEREQUEST
DESCRIPTOR.message_types_by_name['ProcessEntityPendingChangesRequest'] = _PROCESSENTITYPENDINGCHANGESREQUEST
DESCRIPTOR.message_types_by_name['ListEntityResponse'] = _LISTENTITYRESPONSE
DESCRIPTOR.message_types_by_name['Entity'] = _ENTITY
DESCRIPTOR.message_types_by_name['DefaultResponse'] = _DEFAULTRESPONSE
DESCRIPTOR.message_types_by_name['CreateEntityTaskFromPendingChangesRequest'] = _CREATEENTITYTASKFROMPENDINGCHANGESREQUEST
DESCRIPTOR.message_types_by_name['GetEntityTaskByIdRequest'] = _GETENTITYTASKBYIDREQUEST
DESCRIPTOR.message_types_by_name['ListEntityTaskRequest'] = _LISTENTITYTASKREQUEST
DESCRIPTOR.message_types_by_name['UpdateEntityTaskRequest'] = _UPDATEENTITYTASKREQUEST
DESCRIPTOR.message_types_by_name['DeleteEntityTaskRequest'] = _DELETEENTITYTASKREQUEST
DESCRIPTOR.message_types_by_name['UpdateEntityTaskStatusRequest'] = _UPDATEENTITYTASKSTATUSREQUEST
DESCRIPTOR.message_types_by_name['RunEntityTaskRequest'] = _RUNENTITYTASKREQUEST
DESCRIPTOR.message_types_by_name['ListEntityTaskResponse'] = _LISTENTITYTASKRESPONSE
DESCRIPTOR.message_types_by_name['EntityTask'] = _ENTITYTASK
DESCRIPTOR.message_types_by_name['GetChildrenEntityIdsRequest'] = _GETCHILDRENENTITYIDSREQUEST
DESCRIPTOR.message_types_by_name['GetChildrenEntityIdsResponse'] = _GETCHILDRENENTITYIDSRESPONSE
DESCRIPTOR.message_types_by_name['GetParentEntityIdsRequest'] = _GETPARENTENTITYIDSREQUEST
DESCRIPTOR.message_types_by_name['GetParentEntityIdsResponse'] = _GETPARENTENTITYIDSRESPONSE
DESCRIPTOR.message_types_by_name['RefreshEntityMVRequest'] = _REFRESHENTITYMVREQUEST
DESCRIPTOR.message_types_by_name['RefreshEntityMVResponse'] = _REFRESHENTITYMVRESPONSE
DESCRIPTOR.message_types_by_name['UpdateEntityBatchRequest'] = _UPDATEENTITYBATCHREQUEST
DESCRIPTOR.message_types_by_name['UpdateEntityBatchResponse'] = _UPDATEENTITYBATCHRESPONSE
DESCRIPTOR.message_types_by_name['AddEntityBatchRequest'] = _ADDENTITYBATCHREQUEST
DESCRIPTOR.message_types_by_name['AddEntityBatchResponse'] = _ADDENTITYBATCHRESPONSE
DESCRIPTOR.message_types_by_name['SqlQueryRequest'] = _SQLQUERYREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

AddEntityRequest = _reflection.GeneratedProtocolMessageType('AddEntityRequest', (_message.Message,), dict(
  DESCRIPTOR = _ADDENTITYREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.AddEntityRequest)
  ))
_sym_db.RegisterMessage(AddEntityRequest)

SyncEntityRequest = _reflection.GeneratedProtocolMessageType('SyncEntityRequest', (_message.Message,), dict(
  DESCRIPTOR = _SYNCENTITYREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.SyncEntityRequest)
  ))
_sym_db.RegisterMessage(SyncEntityRequest)

UpdateEntityRequest = _reflection.GeneratedProtocolMessageType('UpdateEntityRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEENTITYREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.UpdateEntityRequest)
  ))
_sym_db.RegisterMessage(UpdateEntityRequest)

GetEntityByIdRequest = _reflection.GeneratedProtocolMessageType('GetEntityByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETENTITYBYIDREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.GetEntityByIdRequest)
  ))
_sym_db.RegisterMessage(GetEntityByIdRequest)

ListEntityRequest = _reflection.GeneratedProtocolMessageType('ListEntityRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTENTITYREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.ListEntityRequest)
  ))
_sym_db.RegisterMessage(ListEntityRequest)

UpdateEntityStateRequest = _reflection.GeneratedProtocolMessageType('UpdateEntityStateRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEENTITYSTATEREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.UpdateEntityStateRequest)
  ))
_sym_db.RegisterMessage(UpdateEntityStateRequest)

ProcessEntityPendingChangesRequest = _reflection.GeneratedProtocolMessageType('ProcessEntityPendingChangesRequest', (_message.Message,), dict(
  DESCRIPTOR = _PROCESSENTITYPENDINGCHANGESREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.ProcessEntityPendingChangesRequest)
  ))
_sym_db.RegisterMessage(ProcessEntityPendingChangesRequest)

ListEntityResponse = _reflection.GeneratedProtocolMessageType('ListEntityResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTENTITYRESPONSE,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.ListEntityResponse)
  ))
_sym_db.RegisterMessage(ListEntityResponse)

Entity = _reflection.GeneratedProtocolMessageType('Entity', (_message.Message,), dict(
  DESCRIPTOR = _ENTITY,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.Entity)
  ))
_sym_db.RegisterMessage(Entity)

DefaultResponse = _reflection.GeneratedProtocolMessageType('DefaultResponse', (_message.Message,), dict(
  DESCRIPTOR = _DEFAULTRESPONSE,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.DefaultResponse)
  ))
_sym_db.RegisterMessage(DefaultResponse)

CreateEntityTaskFromPendingChangesRequest = _reflection.GeneratedProtocolMessageType('CreateEntityTaskFromPendingChangesRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEENTITYTASKFROMPENDINGCHANGESREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.CreateEntityTaskFromPendingChangesRequest)
  ))
_sym_db.RegisterMessage(CreateEntityTaskFromPendingChangesRequest)

GetEntityTaskByIdRequest = _reflection.GeneratedProtocolMessageType('GetEntityTaskByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETENTITYTASKBYIDREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.GetEntityTaskByIdRequest)
  ))
_sym_db.RegisterMessage(GetEntityTaskByIdRequest)

ListEntityTaskRequest = _reflection.GeneratedProtocolMessageType('ListEntityTaskRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTENTITYTASKREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.ListEntityTaskRequest)
  ))
_sym_db.RegisterMessage(ListEntityTaskRequest)

UpdateEntityTaskRequest = _reflection.GeneratedProtocolMessageType('UpdateEntityTaskRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEENTITYTASKREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.UpdateEntityTaskRequest)
  ))
_sym_db.RegisterMessage(UpdateEntityTaskRequest)

DeleteEntityTaskRequest = _reflection.GeneratedProtocolMessageType('DeleteEntityTaskRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEENTITYTASKREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.DeleteEntityTaskRequest)
  ))
_sym_db.RegisterMessage(DeleteEntityTaskRequest)

UpdateEntityTaskStatusRequest = _reflection.GeneratedProtocolMessageType('UpdateEntityTaskStatusRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEENTITYTASKSTATUSREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.UpdateEntityTaskStatusRequest)
  ))
_sym_db.RegisterMessage(UpdateEntityTaskStatusRequest)

RunEntityTaskRequest = _reflection.GeneratedProtocolMessageType('RunEntityTaskRequest', (_message.Message,), dict(
  DESCRIPTOR = _RUNENTITYTASKREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.RunEntityTaskRequest)
  ))
_sym_db.RegisterMessage(RunEntityTaskRequest)

ListEntityTaskResponse = _reflection.GeneratedProtocolMessageType('ListEntityTaskResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTENTITYTASKRESPONSE,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.ListEntityTaskResponse)
  ))
_sym_db.RegisterMessage(ListEntityTaskResponse)

EntityTask = _reflection.GeneratedProtocolMessageType('EntityTask', (_message.Message,), dict(
  DESCRIPTOR = _ENTITYTASK,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.EntityTask)
  ))
_sym_db.RegisterMessage(EntityTask)

GetChildrenEntityIdsRequest = _reflection.GeneratedProtocolMessageType('GetChildrenEntityIdsRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETCHILDRENENTITYIDSREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.GetChildrenEntityIdsRequest)
  ))
_sym_db.RegisterMessage(GetChildrenEntityIdsRequest)

GetChildrenEntityIdsResponse = _reflection.GeneratedProtocolMessageType('GetChildrenEntityIdsResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETCHILDRENENTITYIDSRESPONSE,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.GetChildrenEntityIdsResponse)
  ))
_sym_db.RegisterMessage(GetChildrenEntityIdsResponse)

GetParentEntityIdsRequest = _reflection.GeneratedProtocolMessageType('GetParentEntityIdsRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPARENTENTITYIDSREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.GetParentEntityIdsRequest)
  ))
_sym_db.RegisterMessage(GetParentEntityIdsRequest)

GetParentEntityIdsResponse = _reflection.GeneratedProtocolMessageType('GetParentEntityIdsResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPARENTENTITYIDSRESPONSE,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.GetParentEntityIdsResponse)
  ))
_sym_db.RegisterMessage(GetParentEntityIdsResponse)

RefreshEntityMVRequest = _reflection.GeneratedProtocolMessageType('RefreshEntityMVRequest', (_message.Message,), dict(
  DESCRIPTOR = _REFRESHENTITYMVREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.RefreshEntityMVRequest)
  ))
_sym_db.RegisterMessage(RefreshEntityMVRequest)

RefreshEntityMVResponse = _reflection.GeneratedProtocolMessageType('RefreshEntityMVResponse', (_message.Message,), dict(
  DESCRIPTOR = _REFRESHENTITYMVRESPONSE,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.RefreshEntityMVResponse)
  ))
_sym_db.RegisterMessage(RefreshEntityMVResponse)

UpdateEntityBatchRequest = _reflection.GeneratedProtocolMessageType('UpdateEntityBatchRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEENTITYBATCHREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.UpdateEntityBatchRequest)
  ))
_sym_db.RegisterMessage(UpdateEntityBatchRequest)

UpdateEntityBatchResponse = _reflection.GeneratedProtocolMessageType('UpdateEntityBatchResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEENTITYBATCHRESPONSE,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.UpdateEntityBatchResponse)
  ))
_sym_db.RegisterMessage(UpdateEntityBatchResponse)

AddEntityBatchRequest = _reflection.GeneratedProtocolMessageType('AddEntityBatchRequest', (_message.Message,), dict(
  DESCRIPTOR = _ADDENTITYBATCHREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.AddEntityBatchRequest)
  ))
_sym_db.RegisterMessage(AddEntityBatchRequest)

AddEntityBatchResponse = _reflection.GeneratedProtocolMessageType('AddEntityBatchResponse', (_message.Message,), dict(
  DESCRIPTOR = _ADDENTITYBATCHRESPONSE,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.AddEntityBatchResponse)
  ))
_sym_db.RegisterMessage(AddEntityBatchResponse)

SqlQueryRequest = _reflection.GeneratedProtocolMessageType('SqlQueryRequest', (_message.Message,), dict(
  DESCRIPTOR = _SQLQUERYREQUEST,
  __module__ = 'metadata.entity.entity_pb2'
  # @@protoc_insertion_point(class_scope:entity.SqlQueryRequest)
  ))
_sym_db.RegisterMessage(SqlQueryRequest)



_ENTITYSERVICE = _descriptor.ServiceDescriptor(
  name='EntityService',
  full_name='entity.EntityService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=3964,
  serialized_end=6569,
  methods=[
  _descriptor.MethodDescriptor(
    name='AddEntity',
    full_name='entity.EntityService.AddEntity',
    index=0,
    containing_service=None,
    input_type=_ADDENTITYREQUEST,
    output_type=_ENTITY,
    serialized_options=_b('\202\323\344\223\002.\")/api/v2/metadata/entity/{schema_name}/add:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateEntity',
    full_name='entity.EntityService.UpdateEntity',
    index=1,
    containing_service=None,
    input_type=_UPDATEENTITYREQUEST,
    output_type=_ENTITY,
    serialized_options=_b('\202\323\344\223\0021\",/api/v2/metadata/entity/{schema_name}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='SyncUpdateEntity',
    full_name='entity.EntityService.SyncUpdateEntity',
    index=2,
    containing_service=None,
    input_type=_SYNCENTITYREQUEST,
    output_type=_ENTITY,
    serialized_options=_b('\202\323\344\223\0026\"1/api/v2/metadata/entity/{schema_name}/sync/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetEntityById',
    full_name='entity.EntityService.GetEntityById',
    index=3,
    containing_service=None,
    input_type=_GETENTITYBYIDREQUEST,
    output_type=_ENTITY,
    serialized_options=_b('\202\323\344\223\0022\0220/api/v2/metadata/entity/{schema_name}/by/id/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListEntity',
    full_name='entity.EntityService.ListEntity',
    index=4,
    containing_service=None,
    input_type=_LISTENTITYREQUEST,
    output_type=_LISTENTITYRESPONSE,
    serialized_options=_b('\202\323\344\223\0020\"+/api/v2/metadata/entity/{schema_name}/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateEntityState',
    full_name='entity.EntityService.UpdateEntityState',
    index=5,
    containing_service=None,
    input_type=_UPDATEENTITYSTATEREQUEST,
    output_type=_ENTITY,
    serialized_options=_b('\202\323\344\223\0027\"2/api/v2/metadata/entity/{schema_name}/state/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ProcessEntityPendingChanges',
    full_name='entity.EntityService.ProcessEntityPendingChanges',
    index=6,
    containing_service=None,
    input_type=_PROCESSENTITYPENDINGCHANGESREQUEST,
    output_type=_ENTITY,
    serialized_options=_b('\202\323\344\223\002;\"6/api/v2/metadata/entity/{schema_name}/changes/{action}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CreateEntityTaskFromPendingChanges',
    full_name='entity.EntityService.CreateEntityTaskFromPendingChanges',
    index=7,
    containing_service=None,
    input_type=_CREATEENTITYTASKFROMPENDINGCHANGESREQUEST,
    output_type=_ENTITYTASK,
    serialized_options=_b('\202\323\344\223\002:\"5/api/v2/metadata/entity/{schema_name}/changes/to/task:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateEntityTask',
    full_name='entity.EntityService.UpdateEntityTask',
    index=8,
    containing_service=None,
    input_type=_UPDATEENTITYTASKREQUEST,
    output_type=_ENTITYTASK,
    serialized_options=_b('\202\323\344\223\0026\"1/api/v2/metadata/entity/{schema_name}/task/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateEntityTaskStatus',
    full_name='entity.EntityService.UpdateEntityTaskStatus',
    index=9,
    containing_service=None,
    input_type=_UPDATEENTITYTASKSTATUSREQUEST,
    output_type=_ENTITYTASK,
    serialized_options=_b('\202\323\344\223\002=\"8/api/v2/metadata/entity/{schema_name}/task/update/status:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteEntityTask',
    full_name='entity.EntityService.DeleteEntityTask',
    index=10,
    containing_service=None,
    input_type=_DELETEENTITYTASKREQUEST,
    output_type=_ENTITYTASK,
    serialized_options=_b('\202\323\344\223\0027*5/api/v2/metadata/entity/{schema_name}/task/by/id/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetEntityTaskById',
    full_name='entity.EntityService.GetEntityTaskById',
    index=11,
    containing_service=None,
    input_type=_GETENTITYTASKBYIDREQUEST,
    output_type=_ENTITYTASK,
    serialized_options=_b('\202\323\344\223\0027\0225/api/v2/metadata/entity/{schema_name}/task/by/id/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListEntityTask',
    full_name='entity.EntityService.ListEntityTask',
    index=12,
    containing_service=None,
    input_type=_LISTENTITYTASKREQUEST,
    output_type=_LISTENTITYTASKRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\"0/api/v2/metadata/entity/{schema_name}/task/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetChildrenEntityIds',
    full_name='entity.EntityService.GetChildrenEntityIds',
    index=13,
    containing_service=None,
    input_type=_GETCHILDRENENTITYIDSREQUEST,
    output_type=_GETCHILDRENENTITYIDSRESPONSE,
    serialized_options=_b('\202\323\344\223\0026\"1/api/v2/metadata/entity/{schema_name}/childrenids:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetParentEntityIds',
    full_name='entity.EntityService.GetParentEntityIds',
    index=14,
    containing_service=None,
    input_type=_GETPARENTENTITYIDSREQUEST,
    output_type=_GETPARENTENTITYIDSRESPONSE,
    serialized_options=_b('\202\323\344\223\0024\"//api/v2/metadata/entity/{schema_name}/parentids:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='RefreshEntityMV',
    full_name='entity.EntityService.RefreshEntityMV',
    index=15,
    containing_service=None,
    input_type=_REFRESHENTITYMVREQUEST,
    output_type=_REFRESHENTITYMVRESPONSE,
    serialized_options=_b('\202\323\344\223\002\032\"\030/api/v2/metadata/refresh'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateEntityBatch',
    full_name='entity.EntityService.UpdateEntityBatch',
    index=16,
    containing_service=None,
    input_type=_UPDATEENTITYBATCHREQUEST,
    output_type=_UPDATEENTITYBATCHRESPONSE,
    serialized_options=_b('\202\323\344\223\002)\"$/api/v2/metadata/entity/update/batch:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='AddEntityBatch',
    full_name='entity.EntityService.AddEntityBatch',
    index=17,
    containing_service=None,
    input_type=_ADDENTITYBATCHREQUEST,
    output_type=_ADDENTITYBATCHRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\"!/api/v2/metadata/entity/add/batch:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='SqlQuery',
    full_name='entity.EntityService.SqlQuery',
    index=18,
    containing_service=None,
    input_type=_SQLQUERYREQUEST,
    output_type=_LISTENTITYRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\"!/api/v2/metadata/entity/query/sql:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_ENTITYSERVICE)

DESCRIPTOR.services_by_name['EntityService'] = _ENTITYSERVICE

# @@protoc_insertion_point(module_scope)
