syntax = "proto3";
package region;
import "google/api/annotations.proto";
import "google/protobuf/struct.proto";


// RegionService 用于从metadata获取门店信息数据
service RegionService {

    // 根据id获取区域
    rpc GetRegionById(GetRegionByIdRequest) returns (Region){
        option (google.api.http) = {
        get: "/api/v2/region/{region_type}/by/id/{id}"
        };
    }
    // 查询 Product 列表
    rpc ListRegion(ListRegionRequest) returns (ListRegionResponse){
        option (google.api.http) = {
        post: "/api/v2/region/{region_type}/query"
        body: "*"
        };
    }
}

message GetRegionByIdRequest{
    // 数据id
    uint64 id = 1;
    // 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
    string code = 2;
    // 除code和relation之外需要返回的字段, 多个以逗号隔开
    string return_fields = 3;
    // 指定要查询返回的region类型，如：order/distribution/purchase/geo/branch
    string region_type = 4;
}
message ListRegionRequest{
    // 需要返回的extend_code内容, 多个用逗号隔开, "all"返回所有, 默认不返回
    string code = 1;
    // 所需要返回的字段，包含relation里面的字段，如:return_fields="name,code,branch_region,geo_region"
    string return_fields = 2;
    // 分页大小
    int32 limit = 3;
    // 跳过行数
    int32 offset = 4;
    // 排序字段
    string sort = 5;
    // 排序顺序
    string order = 6;
    // 返回总条数
    bool include_total = 7;
    // 要模糊查询的字符串
    string search = 8;
    // 要查询的字段, 多个逗号隔开;
    string search_fields = 9;
    // 按id列表查询
    repeated uint64 ids = 10;
    // 按字段过滤
    google.protobuf.Struct filters = 11;
    // 指定要查询返回的region类型，如：order/distribution/purchase/geo/branch
    string region_type = 12;
}


message ListRegionResponse{
    repeated Region rows = 1;
    int32 total = 2;
}
message Region{
    // 数据id
    uint64 id = 1;
    // 商户id
    uint64 partner_id = 2;
    // scope_id
    uint64 scope_id = 3;
    string name = 4;
    string code = 5;
	string updated = 6;

}

message DefaultResponse{
    bool result = 1;
}