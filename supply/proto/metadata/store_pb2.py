# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: metadata/store.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import struct_pb2 as google_dot_protobuf_dot_struct__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='metadata/store.proto',
  package='store',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x14metadata/store.proto\x12\x05store\x1a\x1cgoogle/api/annotations.proto\x1a\x1cgoogle/protobuf/struct.proto\"S\n\x13GetStoreByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x15\n\rreturn_fields\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\"\xa8\x02\n\x10ListStoreRequest\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x15\n\rreturn_fields\x18\x02 \x01(\t\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x0e\n\x06offset\x18\x04 \x01(\x05\x12\x0c\n\x04sort\x18\x05 \x01(\t\x12\r\n\x05order\x18\x06 \x01(\t\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12\x0e\n\x06search\x18\x08 \x01(\t\x12\x15\n\rsearch_fields\x18\t \x01(\t\x12\x0b\n\x03ids\x18\n \x03(\x04\x12(\n\x07\x66ilters\x18\x0b \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x31\n\x10relation_filters\x18\x0c \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\r \x01(\t\">\n\x11ListStoreResponse\x12\x1a\n\x04rows\x18\x01 \x03(\x0b\x32\x0c.store.Store\x12\r\n\x05total\x18\x02 \x01(\x05\"\xcb\x05\n\x05Store\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x13\n\x0bsecond_code\x18\x06 \x01(\t\x12\x0f\n\x07name_en\x18\x07 \x01(\t\x12\x0c\n\x04type\x18\x08 \x01(\t\x12\x11\n\topen_date\x18\t \x01(\t\x12\x12\n\nclose_date\x18\n \x01(\t\x12\x0f\n\x07\x61\x64\x64ress\x18\x0b \x01(\t\x12\x0f\n\x07\x63ontact\x18\x0c \x01(\t\x12\x0b\n\x03tel\x18\r \x01(\t\x12\r\n\x05\x65mail\x18\x0e \x01(\t\x12\x0e\n\x06status\x18\x0f \x01(\t\x12\x12\n\ngeo_region\x18\x10 \x03(\x04\x12\x15\n\rbranch_region\x18\x11 \x03(\x04\x12\x14\n\x0corder_region\x18\x12 \x03(\x04\x12\x1b\n\x13\x64istribution_region\x18\x13 \x03(\x04\x12\x17\n\x0fpurchase_region\x18\x14 \x03(\x04\x12\x15\n\rmarket_region\x18\x15 \x03(\x04\x12\x17\n\x0ftransfer_region\x18\x16 \x03(\x04\x12\x18\n\x10\x61ttribute_region\x18\x17 \x03(\x04\x12\x12\n\nbom_region\x18\x18 \x03(\x04\x12\x0f\n\x07updated\x18\x19 \x01(\t\x12(\n\x07\x65xtends\x18\x1a \x01(\x0b\x32\x17.google.protobuf.Struct\x12,\n\x0b\x65xtend_code\x18\x1b \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x14\n\x0c\x63ompany_info\x18\x1c \x03(\x04\x12\x1d\n\x15virtual_cold_warehose\x18\x1d \x01(\t\x12\x1b\n\x13\x62\x65longs_to_warehose\x18\x1e \x01(\t\x12\x13\n\x0bopen_status\x18\x1f \x01(\t\x12\x12\n\nfranchisee\x18  \x01(\x04\x12\x12\n\nchain_type\x18! \x01(\t\"!\n\x0f\x44\x65\x66\x61ultResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"s\n\x12\x44istributionCenter\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x0f\n\x07updated\x18\x06 \x01(\t\"`\n GetDistributionCenterByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x15\n\rreturn_fields\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\"\x82\x02\n\x1dListDistributionCenterRequest\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x15\n\rreturn_fields\x18\x02 \x01(\t\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x0e\n\x06offset\x18\x04 \x01(\x05\x12\x0c\n\x04sort\x18\x05 \x01(\t\x12\r\n\x05order\x18\x06 \x01(\t\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12\x0e\n\x06search\x18\x08 \x01(\t\x12\x15\n\rsearch_fields\x18\t \x01(\t\x12\x0b\n\x03ids\x18\n \x03(\x04\x12(\n\x07\x66ilters\x18\x0b \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x0c \x01(\t\"X\n\x1eListDistributionCenterResponse\x12\'\n\x04rows\x18\x01 \x03(\x0b\x32\x19.store.DistributionCenter\x12\r\n\x05total\x18\x02 \x01(\x05\"B\n\x18GetStoreDataScopeRequest\x12&\n\x1e\x64onot_transfer_branch_to_store\x18\x01 \x01(\x08\"X\n\x0eStoreDataScope\x12\x13\n\x0b\x66ull_access\x18\x01 \x01(\x08\x12\x17\n\x0fscope_store_ids\x18\x02 \x03(\x04\x12\x18\n\x10scope_branch_ids\x18\x03 \x03(\x04\"\'\n\x18GetStoreDataScopeListReq\x12\x0b\n\x03lan\x18\x01 \x01(\t\":\n\x18GetStoreDataScopeListRes\x12\x1e\n\x04rows\x18\x01 \x03(\x0b\x32\x10.store.StoreItem\"3\n\tStoreItem\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\"\x1a\n\x18GetWarehouseScopeRequest\"\'\n\x0eWarehouseScope\x12\x15\n\rwarehouse_ids\x18\x01 \x03(\x04\"\x84\x02\n\x07\x43ompany\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x0e\n\x06tax_no\x18\x06 \x01(\t\x12\x0b\n\x03tel\x18\x07 \x01(\t\x12\x0f\n\x07\x61\x64\x64ress\x18\x08 \x01(\t\x12\x14\n\x0c\x64\x65posit_bank\x18\t \x01(\t\x12\x14\n\x0c\x62\x61nk_account\x18\n \x01(\t\x12\x10\n\x08\x63urrency\x18\x0b \x01(\t\x12\x1c\n\x14\x62usiness_licence_url\x18\x0c \x01(\t\x12\x10\n\x08logo_url\x18\r \x01(\t\x12\x0f\n\x07updated\x18\x0e \x01(\t\"U\n\x15GetCompanyByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x15\n\rreturn_fields\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\"\xf7\x01\n\x12ListCompanyRequest\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x15\n\rreturn_fields\x18\x02 \x01(\t\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x0e\n\x06offset\x18\x04 \x01(\x05\x12\x0c\n\x04sort\x18\x05 \x01(\t\x12\r\n\x05order\x18\x06 \x01(\t\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12\x0e\n\x06search\x18\x08 \x01(\t\x12\x15\n\rsearch_fields\x18\t \x01(\t\x12\x0b\n\x03ids\x18\n \x03(\x04\x12(\n\x07\x66ilters\x18\x0b \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x0c \x01(\t\"B\n\x13ListCompanyResponse\x12\x1c\n\x04rows\x18\x01 \x03(\x0b\x32\x0e.store.Company\x12\r\n\x05total\x18\x02 \x01(\x05\"\x8e\x03\n\x06Vendor\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x0e\n\x06tax_no\x18\x06 \x01(\t\x12\x0b\n\x03tel\x18\x07 \x01(\t\x12\x0f\n\x07\x61\x64\x64ress\x18\x08 \x01(\t\x12\x14\n\x0c\x64\x65posit_bank\x18\t \x01(\t\x12\x14\n\x0c\x62\x61nk_account\x18\n \x01(\t\x12\x10\n\x08\x63urrency\x18\x0b \x01(\t\x12\x14\n\x0c\x63ontract_url\x18\x0c \x01(\t\x12\x10\n\x08logo_url\x18\r \x01(\t\x12\x0f\n\x07updated\x18\x0e \x01(\t\x12\r\n\x05\x65mail\x18\x0f \x01(\t\x12\x0f\n\x07\x63ontact\x18\x10 \x01(\t\x12\x14\n\x0c\x61\x62\x62reviation\x18\x11 \x01(\t\x12\x19\n\x11settlement_method\x18\x12 \x01(\t\x12\x17\n\x0f\x63ontract_status\x18\x13 \x01(\t\x12\x19\n\x11qualification_url\x18\x14 \x01(\t\x12\x0c\n\x04rank\x18\x15 \x01(\t\"T\n\x14GetVendorByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x15\n\rreturn_fields\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\"\xf6\x01\n\x11ListVendorRequest\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x15\n\rreturn_fields\x18\x02 \x01(\t\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x0e\n\x06offset\x18\x04 \x01(\x05\x12\x0c\n\x04sort\x18\x05 \x01(\t\x12\r\n\x05order\x18\x06 \x01(\t\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12\x0e\n\x06search\x18\x08 \x01(\t\x12\x15\n\rsearch_fields\x18\t \x01(\t\x12\x0b\n\x03ids\x18\n \x03(\x04\x12(\n\x07\x66ilters\x18\x0b \x01(\x0b\x32\x17.google.protobuf.Struct\x12\x0b\n\x03lan\x18\x0c \x01(\t\"@\n\x12ListVendorResponse\x12\x1b\n\x04rows\x18\x01 \x03(\x0b\x32\r.store.Vendor\x12\r\n\x05total\x18\x02 \x01(\x05\" \n\x1eGetMachiningCenterScopeRequest\"\x98\x01\n\x1aMachiningCenterDetailScope\x12?\n\x04rows\x18\x01 \x03(\x0b\x32\x31.store.MachiningCenterDetailScope.MachiningCenter\x1a\x39\n\x0fMachiningCenter\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t2\xfa\n\n\x0cStoreService\x12Z\n\x0cGetStoreById\x12\x1a.store.GetStoreByIdRequest\x1a\x0c.store.Store\" \x82\xd3\xe4\x93\x02\x1a\x12\x18/api/v2/store/by/id/{id}\x12^\n\tListStore\x12\x17.store.ListStoreRequest\x1a\x18.store.ListStoreResponse\"\x1e\x82\xd3\xe4\x93\x02\x18\"\x13/api/v2/store/query:\x01*\x12m\n\x11GetStoreDataScope\x12\x1f.store.GetStoreDataScopeRequest\x1a\x15.store.StoreDataScope\" \x82\xd3\xe4\x93\x02\x1a\x12\x18/api/v2/store/data/scope\x12{\n\x15GetStoreDataScopeList\x12\x1f.store.GetStoreDataScopeListReq\x1a\x1f.store.GetStoreDataScopeListRes\" \x82\xd3\xe4\x93\x02\x1a\x12\x18/api/v2/store/list/scope\x12\x8f\x01\n\x19GetDistributionCenterById\x12\'.store.GetDistributionCenterByIdRequest\x1a\x19.store.DistributionCenter\".\x82\xd3\xe4\x93\x02(\x12&/api/v2/distribution-center/by/id/{id}\x12\x93\x01\n\x16ListDistributionCenter\x12$.store.ListDistributionCenterRequest\x1a%.store.ListDistributionCenterResponse\",\x82\xd3\xe4\x93\x02&\"!/api/v2/distribution-center/query:\x01*\x12\x62\n\x0eGetCompanyById\x12\x1c.store.GetCompanyByIdRequest\x1a\x0e.store.Company\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/api/v2/company/by/id/{id}\x12\x66\n\x0bListCompany\x12\x19.store.ListCompanyRequest\x1a\x1a.store.ListCompanyResponse\" \x82\xd3\xe4\x93\x02\x1a\"\x15/api/v2/company/query:\x01*\x12^\n\rGetVendorById\x12\x1b.store.GetVendorByIdRequest\x1a\r.store.Vendor\"!\x82\xd3\xe4\x93\x02\x1b\x12\x19/api/v2/vendor/by/id/{id}\x12\x62\n\nListVendor\x12\x18.store.ListVendorRequest\x1a\x19.store.ListVendorResponse\"\x1f\x82\xd3\xe4\x93\x02\x19\"\x14/api/v2/vendor/query:\x01*\x12q\n\x11GetWarehouseScope\x12\x1f.store.GetWarehouseScopeRequest\x1a\x15.store.WarehouseScope\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/api/v2/warehouse/data/scope\x12\x96\x01\n\x1dGetMachiningCenterScopeDetail\x12%.store.GetMachiningCenterScopeRequest\x1a!.store.MachiningCenterDetailScope\"+\x82\xd3\xe4\x93\x02%\x12#/api/v2/machining/data/scope/detailb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_struct__pb2.DESCRIPTOR,])




_GETSTOREBYIDREQUEST = _descriptor.Descriptor(
  name='GetStoreByIdRequest',
  full_name='store.GetStoreByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store.GetStoreByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store.GetStoreByIdRequest.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='store.GetStoreByIdRequest.return_fields', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store.GetStoreByIdRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=91,
  serialized_end=174,
)


_LISTSTOREREQUEST = _descriptor.Descriptor(
  name='ListStoreRequest',
  full_name='store.ListStoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='store.ListStoreRequest.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='store.ListStoreRequest.return_fields', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store.ListStoreRequest.limit', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store.ListStoreRequest.offset', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='store.ListStoreRequest.sort', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='store.ListStoreRequest.order', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='store.ListStoreRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='store.ListStoreRequest.search', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='store.ListStoreRequest.search_fields', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='store.ListStoreRequest.ids', index=9,
      number=10, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='store.ListStoreRequest.filters', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation_filters', full_name='store.ListStoreRequest.relation_filters', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store.ListStoreRequest.lan', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=177,
  serialized_end=473,
)


_LISTSTORERESPONSE = _descriptor.Descriptor(
  name='ListStoreResponse',
  full_name='store.ListStoreResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store.ListStoreResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store.ListStoreResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=475,
  serialized_end=537,
)


_STORE = _descriptor.Descriptor(
  name='Store',
  full_name='store.Store',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store.Store.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='store.Store.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='store.Store.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='store.Store.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store.Store.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='second_code', full_name='store.Store.second_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name_en', full_name='store.Store.name_en', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='store.Store.type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='open_date', full_name='store.Store.open_date', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='close_date', full_name='store.Store.close_date', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='address', full_name='store.Store.address', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contact', full_name='store.Store.contact', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tel', full_name='store.Store.tel', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='email', full_name='store.Store.email', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store.Store.status', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='geo_region', full_name='store.Store.geo_region', index=15,
      number=16, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_region', full_name='store.Store.branch_region', index=16,
      number=17, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_region', full_name='store.Store.order_region', index=17,
      number=18, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_region', full_name='store.Store.distribution_region', index=18,
      number=19, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_region', full_name='store.Store.purchase_region', index=19,
      number=20, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='market_region', full_name='store.Store.market_region', index=20,
      number=21, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_region', full_name='store.Store.transfer_region', index=21,
      number=22, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attribute_region', full_name='store.Store.attribute_region', index=22,
      number=23, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_region', full_name='store.Store.bom_region', index=23,
      number=24, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='store.Store.updated', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='store.Store.extends', index=25,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extend_code', full_name='store.Store.extend_code', index=26,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_info', full_name='store.Store.company_info', index=27,
      number=28, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='virtual_cold_warehose', full_name='store.Store.virtual_cold_warehose', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='belongs_to_warehose', full_name='store.Store.belongs_to_warehose', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='open_status', full_name='store.Store.open_status', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee', full_name='store.Store.franchisee', index=31,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='chain_type', full_name='store.Store.chain_type', index=32,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=540,
  serialized_end=1255,
)


_DEFAULTRESPONSE = _descriptor.Descriptor(
  name='DefaultResponse',
  full_name='store.DefaultResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='store.DefaultResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1257,
  serialized_end=1290,
)


_DISTRIBUTIONCENTER = _descriptor.Descriptor(
  name='DistributionCenter',
  full_name='store.DistributionCenter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store.DistributionCenter.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='store.DistributionCenter.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='store.DistributionCenter.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='store.DistributionCenter.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store.DistributionCenter.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='store.DistributionCenter.updated', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1292,
  serialized_end=1407,
)


_GETDISTRIBUTIONCENTERBYIDREQUEST = _descriptor.Descriptor(
  name='GetDistributionCenterByIdRequest',
  full_name='store.GetDistributionCenterByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store.GetDistributionCenterByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store.GetDistributionCenterByIdRequest.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='store.GetDistributionCenterByIdRequest.return_fields', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store.GetDistributionCenterByIdRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1409,
  serialized_end=1505,
)


_LISTDISTRIBUTIONCENTERREQUEST = _descriptor.Descriptor(
  name='ListDistributionCenterRequest',
  full_name='store.ListDistributionCenterRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='store.ListDistributionCenterRequest.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='store.ListDistributionCenterRequest.return_fields', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store.ListDistributionCenterRequest.limit', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store.ListDistributionCenterRequest.offset', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='store.ListDistributionCenterRequest.sort', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='store.ListDistributionCenterRequest.order', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='store.ListDistributionCenterRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='store.ListDistributionCenterRequest.search', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='store.ListDistributionCenterRequest.search_fields', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='store.ListDistributionCenterRequest.ids', index=9,
      number=10, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='store.ListDistributionCenterRequest.filters', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store.ListDistributionCenterRequest.lan', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1508,
  serialized_end=1766,
)


_LISTDISTRIBUTIONCENTERRESPONSE = _descriptor.Descriptor(
  name='ListDistributionCenterResponse',
  full_name='store.ListDistributionCenterResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store.ListDistributionCenterResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store.ListDistributionCenterResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1768,
  serialized_end=1856,
)


_GETSTOREDATASCOPEREQUEST = _descriptor.Descriptor(
  name='GetStoreDataScopeRequest',
  full_name='store.GetStoreDataScopeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='donot_transfer_branch_to_store', full_name='store.GetStoreDataScopeRequest.donot_transfer_branch_to_store', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1858,
  serialized_end=1924,
)


_STOREDATASCOPE = _descriptor.Descriptor(
  name='StoreDataScope',
  full_name='store.StoreDataScope',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='full_access', full_name='store.StoreDataScope.full_access', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_store_ids', full_name='store.StoreDataScope.scope_store_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_branch_ids', full_name='store.StoreDataScope.scope_branch_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1926,
  serialized_end=2014,
)


_GETSTOREDATASCOPELISTREQ = _descriptor.Descriptor(
  name='GetStoreDataScopeListReq',
  full_name='store.GetStoreDataScopeListReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='lan', full_name='store.GetStoreDataScopeListReq.lan', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2016,
  serialized_end=2055,
)


_GETSTOREDATASCOPELISTRES = _descriptor.Descriptor(
  name='GetStoreDataScopeListRes',
  full_name='store.GetStoreDataScopeListRes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store.GetStoreDataScopeListRes.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2057,
  serialized_end=2115,
)


_STOREITEM = _descriptor.Descriptor(
  name='StoreItem',
  full_name='store.StoreItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store.StoreItem.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store.StoreItem.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='store.StoreItem.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2117,
  serialized_end=2168,
)


_GETWAREHOUSESCOPEREQUEST = _descriptor.Descriptor(
  name='GetWarehouseScopeRequest',
  full_name='store.GetWarehouseScopeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2170,
  serialized_end=2196,
)


_WAREHOUSESCOPE = _descriptor.Descriptor(
  name='WarehouseScope',
  full_name='store.WarehouseScope',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='warehouse_ids', full_name='store.WarehouseScope.warehouse_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2198,
  serialized_end=2237,
)


_COMPANY = _descriptor.Descriptor(
  name='Company',
  full_name='store.Company',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store.Company.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='store.Company.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='store.Company.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='store.Company.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store.Company.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_no', full_name='store.Company.tax_no', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tel', full_name='store.Company.tel', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='address', full_name='store.Company.address', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deposit_bank', full_name='store.Company.deposit_bank', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bank_account', full_name='store.Company.bank_account', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='store.Company.currency', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='business_licence_url', full_name='store.Company.business_licence_url', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logo_url', full_name='store.Company.logo_url', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='store.Company.updated', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2240,
  serialized_end=2500,
)


_GETCOMPANYBYIDREQUEST = _descriptor.Descriptor(
  name='GetCompanyByIdRequest',
  full_name='store.GetCompanyByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store.GetCompanyByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store.GetCompanyByIdRequest.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='store.GetCompanyByIdRequest.return_fields', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store.GetCompanyByIdRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2502,
  serialized_end=2587,
)


_LISTCOMPANYREQUEST = _descriptor.Descriptor(
  name='ListCompanyRequest',
  full_name='store.ListCompanyRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='store.ListCompanyRequest.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='store.ListCompanyRequest.return_fields', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store.ListCompanyRequest.limit', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store.ListCompanyRequest.offset', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='store.ListCompanyRequest.sort', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='store.ListCompanyRequest.order', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='store.ListCompanyRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='store.ListCompanyRequest.search', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='store.ListCompanyRequest.search_fields', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='store.ListCompanyRequest.ids', index=9,
      number=10, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='store.ListCompanyRequest.filters', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store.ListCompanyRequest.lan', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2590,
  serialized_end=2837,
)


_LISTCOMPANYRESPONSE = _descriptor.Descriptor(
  name='ListCompanyResponse',
  full_name='store.ListCompanyResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store.ListCompanyResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store.ListCompanyResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2839,
  serialized_end=2905,
)


_VENDOR = _descriptor.Descriptor(
  name='Vendor',
  full_name='store.Vendor',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store.Vendor.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='store.Vendor.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='store.Vendor.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='store.Vendor.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store.Vendor.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_no', full_name='store.Vendor.tax_no', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tel', full_name='store.Vendor.tel', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='address', full_name='store.Vendor.address', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deposit_bank', full_name='store.Vendor.deposit_bank', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bank_account', full_name='store.Vendor.bank_account', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='store.Vendor.currency', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contract_url', full_name='store.Vendor.contract_url', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logo_url', full_name='store.Vendor.logo_url', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='store.Vendor.updated', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='email', full_name='store.Vendor.email', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contact', full_name='store.Vendor.contact', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='abbreviation', full_name='store.Vendor.abbreviation', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='settlement_method', full_name='store.Vendor.settlement_method', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contract_status', full_name='store.Vendor.contract_status', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qualification_url', full_name='store.Vendor.qualification_url', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rank', full_name='store.Vendor.rank', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2908,
  serialized_end=3306,
)


_GETVENDORBYIDREQUEST = _descriptor.Descriptor(
  name='GetVendorByIdRequest',
  full_name='store.GetVendorByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store.GetVendorByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store.GetVendorByIdRequest.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='store.GetVendorByIdRequest.return_fields', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store.GetVendorByIdRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3308,
  serialized_end=3392,
)


_LISTVENDORREQUEST = _descriptor.Descriptor(
  name='ListVendorRequest',
  full_name='store.ListVendorRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='store.ListVendorRequest.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='store.ListVendorRequest.return_fields', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store.ListVendorRequest.limit', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store.ListVendorRequest.offset', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='store.ListVendorRequest.sort', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='store.ListVendorRequest.order', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='store.ListVendorRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='store.ListVendorRequest.search', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='store.ListVendorRequest.search_fields', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='store.ListVendorRequest.ids', index=9,
      number=10, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='store.ListVendorRequest.filters', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store.ListVendorRequest.lan', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3395,
  serialized_end=3641,
)


_LISTVENDORRESPONSE = _descriptor.Descriptor(
  name='ListVendorResponse',
  full_name='store.ListVendorResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store.ListVendorResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store.ListVendorResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3643,
  serialized_end=3707,
)


_GETMACHININGCENTERSCOPEREQUEST = _descriptor.Descriptor(
  name='GetMachiningCenterScopeRequest',
  full_name='store.GetMachiningCenterScopeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3709,
  serialized_end=3741,
)


_MACHININGCENTERDETAILSCOPE_MACHININGCENTER = _descriptor.Descriptor(
  name='MachiningCenter',
  full_name='store.MachiningCenterDetailScope.MachiningCenter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store.MachiningCenterDetailScope.MachiningCenter.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store.MachiningCenterDetailScope.MachiningCenter.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='store.MachiningCenterDetailScope.MachiningCenter.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3839,
  serialized_end=3896,
)

_MACHININGCENTERDETAILSCOPE = _descriptor.Descriptor(
  name='MachiningCenterDetailScope',
  full_name='store.MachiningCenterDetailScope',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store.MachiningCenterDetailScope.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_MACHININGCENTERDETAILSCOPE_MACHININGCENTER, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3744,
  serialized_end=3896,
)

_LISTSTOREREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTSTOREREQUEST.fields_by_name['relation_filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTSTORERESPONSE.fields_by_name['rows'].message_type = _STORE
_STORE.fields_by_name['extends'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_STORE.fields_by_name['extend_code'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTDISTRIBUTIONCENTERREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTDISTRIBUTIONCENTERRESPONSE.fields_by_name['rows'].message_type = _DISTRIBUTIONCENTER
_GETSTOREDATASCOPELISTRES.fields_by_name['rows'].message_type = _STOREITEM
_LISTCOMPANYREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTCOMPANYRESPONSE.fields_by_name['rows'].message_type = _COMPANY
_LISTVENDORREQUEST.fields_by_name['filters'].message_type = google_dot_protobuf_dot_struct__pb2._STRUCT
_LISTVENDORRESPONSE.fields_by_name['rows'].message_type = _VENDOR
_MACHININGCENTERDETAILSCOPE_MACHININGCENTER.containing_type = _MACHININGCENTERDETAILSCOPE
_MACHININGCENTERDETAILSCOPE.fields_by_name['rows'].message_type = _MACHININGCENTERDETAILSCOPE_MACHININGCENTER
DESCRIPTOR.message_types_by_name['GetStoreByIdRequest'] = _GETSTOREBYIDREQUEST
DESCRIPTOR.message_types_by_name['ListStoreRequest'] = _LISTSTOREREQUEST
DESCRIPTOR.message_types_by_name['ListStoreResponse'] = _LISTSTORERESPONSE
DESCRIPTOR.message_types_by_name['Store'] = _STORE
DESCRIPTOR.message_types_by_name['DefaultResponse'] = _DEFAULTRESPONSE
DESCRIPTOR.message_types_by_name['DistributionCenter'] = _DISTRIBUTIONCENTER
DESCRIPTOR.message_types_by_name['GetDistributionCenterByIdRequest'] = _GETDISTRIBUTIONCENTERBYIDREQUEST
DESCRIPTOR.message_types_by_name['ListDistributionCenterRequest'] = _LISTDISTRIBUTIONCENTERREQUEST
DESCRIPTOR.message_types_by_name['ListDistributionCenterResponse'] = _LISTDISTRIBUTIONCENTERRESPONSE
DESCRIPTOR.message_types_by_name['GetStoreDataScopeRequest'] = _GETSTOREDATASCOPEREQUEST
DESCRIPTOR.message_types_by_name['StoreDataScope'] = _STOREDATASCOPE
DESCRIPTOR.message_types_by_name['GetStoreDataScopeListReq'] = _GETSTOREDATASCOPELISTREQ
DESCRIPTOR.message_types_by_name['GetStoreDataScopeListRes'] = _GETSTOREDATASCOPELISTRES
DESCRIPTOR.message_types_by_name['StoreItem'] = _STOREITEM
DESCRIPTOR.message_types_by_name['GetWarehouseScopeRequest'] = _GETWAREHOUSESCOPEREQUEST
DESCRIPTOR.message_types_by_name['WarehouseScope'] = _WAREHOUSESCOPE
DESCRIPTOR.message_types_by_name['Company'] = _COMPANY
DESCRIPTOR.message_types_by_name['GetCompanyByIdRequest'] = _GETCOMPANYBYIDREQUEST
DESCRIPTOR.message_types_by_name['ListCompanyRequest'] = _LISTCOMPANYREQUEST
DESCRIPTOR.message_types_by_name['ListCompanyResponse'] = _LISTCOMPANYRESPONSE
DESCRIPTOR.message_types_by_name['Vendor'] = _VENDOR
DESCRIPTOR.message_types_by_name['GetVendorByIdRequest'] = _GETVENDORBYIDREQUEST
DESCRIPTOR.message_types_by_name['ListVendorRequest'] = _LISTVENDORREQUEST
DESCRIPTOR.message_types_by_name['ListVendorResponse'] = _LISTVENDORRESPONSE
DESCRIPTOR.message_types_by_name['GetMachiningCenterScopeRequest'] = _GETMACHININGCENTERSCOPEREQUEST
DESCRIPTOR.message_types_by_name['MachiningCenterDetailScope'] = _MACHININGCENTERDETAILSCOPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetStoreByIdRequest = _reflection.GeneratedProtocolMessageType('GetStoreByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOREBYIDREQUEST,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.GetStoreByIdRequest)
  ))
_sym_db.RegisterMessage(GetStoreByIdRequest)

ListStoreRequest = _reflection.GeneratedProtocolMessageType('ListStoreRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTSTOREREQUEST,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.ListStoreRequest)
  ))
_sym_db.RegisterMessage(ListStoreRequest)

ListStoreResponse = _reflection.GeneratedProtocolMessageType('ListStoreResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTSTORERESPONSE,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.ListStoreResponse)
  ))
_sym_db.RegisterMessage(ListStoreResponse)

Store = _reflection.GeneratedProtocolMessageType('Store', (_message.Message,), dict(
  DESCRIPTOR = _STORE,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.Store)
  ))
_sym_db.RegisterMessage(Store)

DefaultResponse = _reflection.GeneratedProtocolMessageType('DefaultResponse', (_message.Message,), dict(
  DESCRIPTOR = _DEFAULTRESPONSE,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.DefaultResponse)
  ))
_sym_db.RegisterMessage(DefaultResponse)

DistributionCenter = _reflection.GeneratedProtocolMessageType('DistributionCenter', (_message.Message,), dict(
  DESCRIPTOR = _DISTRIBUTIONCENTER,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.DistributionCenter)
  ))
_sym_db.RegisterMessage(DistributionCenter)

GetDistributionCenterByIdRequest = _reflection.GeneratedProtocolMessageType('GetDistributionCenterByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDISTRIBUTIONCENTERBYIDREQUEST,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.GetDistributionCenterByIdRequest)
  ))
_sym_db.RegisterMessage(GetDistributionCenterByIdRequest)

ListDistributionCenterRequest = _reflection.GeneratedProtocolMessageType('ListDistributionCenterRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTDISTRIBUTIONCENTERREQUEST,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.ListDistributionCenterRequest)
  ))
_sym_db.RegisterMessage(ListDistributionCenterRequest)

ListDistributionCenterResponse = _reflection.GeneratedProtocolMessageType('ListDistributionCenterResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTDISTRIBUTIONCENTERRESPONSE,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.ListDistributionCenterResponse)
  ))
_sym_db.RegisterMessage(ListDistributionCenterResponse)

GetStoreDataScopeRequest = _reflection.GeneratedProtocolMessageType('GetStoreDataScopeRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOREDATASCOPEREQUEST,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.GetStoreDataScopeRequest)
  ))
_sym_db.RegisterMessage(GetStoreDataScopeRequest)

StoreDataScope = _reflection.GeneratedProtocolMessageType('StoreDataScope', (_message.Message,), dict(
  DESCRIPTOR = _STOREDATASCOPE,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.StoreDataScope)
  ))
_sym_db.RegisterMessage(StoreDataScope)

GetStoreDataScopeListReq = _reflection.GeneratedProtocolMessageType('GetStoreDataScopeListReq', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOREDATASCOPELISTREQ,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.GetStoreDataScopeListReq)
  ))
_sym_db.RegisterMessage(GetStoreDataScopeListReq)

GetStoreDataScopeListRes = _reflection.GeneratedProtocolMessageType('GetStoreDataScopeListRes', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOREDATASCOPELISTRES,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.GetStoreDataScopeListRes)
  ))
_sym_db.RegisterMessage(GetStoreDataScopeListRes)

StoreItem = _reflection.GeneratedProtocolMessageType('StoreItem', (_message.Message,), dict(
  DESCRIPTOR = _STOREITEM,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.StoreItem)
  ))
_sym_db.RegisterMessage(StoreItem)

GetWarehouseScopeRequest = _reflection.GeneratedProtocolMessageType('GetWarehouseScopeRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETWAREHOUSESCOPEREQUEST,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.GetWarehouseScopeRequest)
  ))
_sym_db.RegisterMessage(GetWarehouseScopeRequest)

WarehouseScope = _reflection.GeneratedProtocolMessageType('WarehouseScope', (_message.Message,), dict(
  DESCRIPTOR = _WAREHOUSESCOPE,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.WarehouseScope)
  ))
_sym_db.RegisterMessage(WarehouseScope)

Company = _reflection.GeneratedProtocolMessageType('Company', (_message.Message,), dict(
  DESCRIPTOR = _COMPANY,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.Company)
  ))
_sym_db.RegisterMessage(Company)

GetCompanyByIdRequest = _reflection.GeneratedProtocolMessageType('GetCompanyByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETCOMPANYBYIDREQUEST,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.GetCompanyByIdRequest)
  ))
_sym_db.RegisterMessage(GetCompanyByIdRequest)

ListCompanyRequest = _reflection.GeneratedProtocolMessageType('ListCompanyRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTCOMPANYREQUEST,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.ListCompanyRequest)
  ))
_sym_db.RegisterMessage(ListCompanyRequest)

ListCompanyResponse = _reflection.GeneratedProtocolMessageType('ListCompanyResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTCOMPANYRESPONSE,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.ListCompanyResponse)
  ))
_sym_db.RegisterMessage(ListCompanyResponse)

Vendor = _reflection.GeneratedProtocolMessageType('Vendor', (_message.Message,), dict(
  DESCRIPTOR = _VENDOR,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.Vendor)
  ))
_sym_db.RegisterMessage(Vendor)

GetVendorByIdRequest = _reflection.GeneratedProtocolMessageType('GetVendorByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETVENDORBYIDREQUEST,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.GetVendorByIdRequest)
  ))
_sym_db.RegisterMessage(GetVendorByIdRequest)

ListVendorRequest = _reflection.GeneratedProtocolMessageType('ListVendorRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTVENDORREQUEST,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.ListVendorRequest)
  ))
_sym_db.RegisterMessage(ListVendorRequest)

ListVendorResponse = _reflection.GeneratedProtocolMessageType('ListVendorResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTVENDORRESPONSE,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.ListVendorResponse)
  ))
_sym_db.RegisterMessage(ListVendorResponse)

GetMachiningCenterScopeRequest = _reflection.GeneratedProtocolMessageType('GetMachiningCenterScopeRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETMACHININGCENTERSCOPEREQUEST,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.GetMachiningCenterScopeRequest)
  ))
_sym_db.RegisterMessage(GetMachiningCenterScopeRequest)

MachiningCenterDetailScope = _reflection.GeneratedProtocolMessageType('MachiningCenterDetailScope', (_message.Message,), dict(

  MachiningCenter = _reflection.GeneratedProtocolMessageType('MachiningCenter', (_message.Message,), dict(
    DESCRIPTOR = _MACHININGCENTERDETAILSCOPE_MACHININGCENTER,
    __module__ = 'metadata.store_pb2'
    # @@protoc_insertion_point(class_scope:store.MachiningCenterDetailScope.MachiningCenter)
    ))
  ,
  DESCRIPTOR = _MACHININGCENTERDETAILSCOPE,
  __module__ = 'metadata.store_pb2'
  # @@protoc_insertion_point(class_scope:store.MachiningCenterDetailScope)
  ))
_sym_db.RegisterMessage(MachiningCenterDetailScope)
_sym_db.RegisterMessage(MachiningCenterDetailScope.MachiningCenter)



_STORESERVICE = _descriptor.ServiceDescriptor(
  name='StoreService',
  full_name='store.StoreService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=3899,
  serialized_end=5301,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetStoreById',
    full_name='store.StoreService.GetStoreById',
    index=0,
    containing_service=None,
    input_type=_GETSTOREBYIDREQUEST,
    output_type=_STORE,
    serialized_options=_b('\202\323\344\223\002\032\022\030/api/v2/store/by/id/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListStore',
    full_name='store.StoreService.ListStore',
    index=1,
    containing_service=None,
    input_type=_LISTSTOREREQUEST,
    output_type=_LISTSTORERESPONSE,
    serialized_options=_b('\202\323\344\223\002\030\"\023/api/v2/store/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStoreDataScope',
    full_name='store.StoreService.GetStoreDataScope',
    index=2,
    containing_service=None,
    input_type=_GETSTOREDATASCOPEREQUEST,
    output_type=_STOREDATASCOPE,
    serialized_options=_b('\202\323\344\223\002\032\022\030/api/v2/store/data/scope'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStoreDataScopeList',
    full_name='store.StoreService.GetStoreDataScopeList',
    index=3,
    containing_service=None,
    input_type=_GETSTOREDATASCOPELISTREQ,
    output_type=_GETSTOREDATASCOPELISTRES,
    serialized_options=_b('\202\323\344\223\002\032\022\030/api/v2/store/list/scope'),
  ),
  _descriptor.MethodDescriptor(
    name='GetDistributionCenterById',
    full_name='store.StoreService.GetDistributionCenterById',
    index=4,
    containing_service=None,
    input_type=_GETDISTRIBUTIONCENTERBYIDREQUEST,
    output_type=_DISTRIBUTIONCENTER,
    serialized_options=_b('\202\323\344\223\002(\022&/api/v2/distribution-center/by/id/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListDistributionCenter',
    full_name='store.StoreService.ListDistributionCenter',
    index=5,
    containing_service=None,
    input_type=_LISTDISTRIBUTIONCENTERREQUEST,
    output_type=_LISTDISTRIBUTIONCENTERRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\"!/api/v2/distribution-center/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetCompanyById',
    full_name='store.StoreService.GetCompanyById',
    index=6,
    containing_service=None,
    input_type=_GETCOMPANYBYIDREQUEST,
    output_type=_COMPANY,
    serialized_options=_b('\202\323\344\223\002\034\022\032/api/v2/company/by/id/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListCompany',
    full_name='store.StoreService.ListCompany',
    index=7,
    containing_service=None,
    input_type=_LISTCOMPANYREQUEST,
    output_type=_LISTCOMPANYRESPONSE,
    serialized_options=_b('\202\323\344\223\002\032\"\025/api/v2/company/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetVendorById',
    full_name='store.StoreService.GetVendorById',
    index=8,
    containing_service=None,
    input_type=_GETVENDORBYIDREQUEST,
    output_type=_VENDOR,
    serialized_options=_b('\202\323\344\223\002\033\022\031/api/v2/vendor/by/id/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ListVendor',
    full_name='store.StoreService.ListVendor',
    index=9,
    containing_service=None,
    input_type=_LISTVENDORREQUEST,
    output_type=_LISTVENDORRESPONSE,
    serialized_options=_b('\202\323\344\223\002\031\"\024/api/v2/vendor/query:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetWarehouseScope',
    full_name='store.StoreService.GetWarehouseScope',
    index=10,
    containing_service=None,
    input_type=_GETWAREHOUSESCOPEREQUEST,
    output_type=_WAREHOUSESCOPE,
    serialized_options=_b('\202\323\344\223\002\036\022\034/api/v2/warehouse/data/scope'),
  ),
  _descriptor.MethodDescriptor(
    name='GetMachiningCenterScopeDetail',
    full_name='store.StoreService.GetMachiningCenterScopeDetail',
    index=11,
    containing_service=None,
    input_type=_GETMACHININGCENTERSCOPEREQUEST,
    output_type=_MACHININGCENTERDETAILSCOPE,
    serialized_options=_b('\202\323\344\223\002%\022#/api/v2/machining/data/scope/detail'),
  ),
])
_sym_db.RegisterServiceDescriptor(_STORESERVICE)

DESCRIPTOR.services_by_name['StoreService'] = _STORESERVICE

# @@protoc_insertion_point(module_scope)
