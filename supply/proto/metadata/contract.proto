syntax = "proto3";
package contract;
import "google/api/annotations.proto";
import "google/protobuf/struct.proto";


service ContractService{
    rpc CreateContract(CreateContractReq) returns (CreateContractRes){
        option (google.api.http) = {
        post: "/api/v2/contract/create"
        body: "*"
        };
    }
    rpc GetContractList(GetContractListReq) returns (GetContractListRes){
        option (google.api.http) = {
        post: "/api/v2/contract/list"
        body: "*"
        };
    }
    rpc GetContractDetail(GetContractDetailReq) returns (GetContractDetailRes){
        option (google.api.http) = {
        post: "/api/v2/contract/detail"
        body: "*"
        };
    }
    rpc UpdateContract(UpdateContractReq) returns (UpdateContractRes){
        option (google.api.http) = {
        post: "/api/v2/contract/update"
        body: "*"
        };
    }
    rpc GetTaxRateList(GetTaxRateReq) returns (GetTaxRateRes){
        option (google.api.http) = {
        post: "/api/v2/rate/list"
        body: "*"
        };
    }
    rpc GetTaxList(GetTaxListReq) returns (GetTaxListRes){
        option (google.api.http) = {
        post: "/api/v2/tax/list"
        body: "*"
        };
    }
    rpc GetProList(GetProListReq) returns (GetProListRes){
        option (google.api.http) = {
        post: "/api/v2/product/list"
        body: "*"
        };
    }
    rpc GetContractMetaList(GetContractMetaListReq) returns (GetContractMetaListRes){
        option (google.api.http) = {
        post: "/api/v2/contract/meta/list"
        body: "*"
        };
    }
    rpc GetSerialNo(GetSerialNoReq) returns (GetSerialNoRes){
        option (google.api.http) = {
        post: "/api/v2/serial/no"
        body: "*"
        };
    }
}


message ListProductReq{
    //分页大小
    int32 limit = 1;
    //跳过行数
    int32 offset = 2;
    //供应商id
    string vendor_id = 3;
    //仓库id
    string centre_id_list = 4;
    //采购时间
    string valid_time = 5;

}

message ListProductRes{
    int32 count = 1;
    repeated Product list = 2;
}

message Product {
    string product_id = 1;
    google.protobuf.Struct  fields = 2;
}

message CreateContractReq {
    Contract contract = 1;
    repeated ContractPro pro_list = 2;
}

message CreateContractRes {
    bool success = 1;
}

message Contract{
    string id = 1;
    string contract_no = 2;
    string name = 3;
    string vendor_id = 4;
    string start_time = 5;
    string end_time = 6;
    string status = 7;
    string updated = 8;
    string updated_by = 9;
    int32  pro_count = 10;
}

message ContractPro{
    string id = 1;
    string pro_id = 2;
    string pro_name = 3;
    string pro_code = 4;
    string unit_id = 5;
    string unit_name = 6;
    string unit_code = 7;
    string model_name = 8;
    double rate = 9;
    repeated ProDetail details = 10;
}

message DetailItem {
    string id = 1;
    string name = 2;
    string code = 3;
}
 message ProDetail{
    string id = 1;
    double no_tax = 6;
    double tax = 7;
    repeated string region_id_list = 8;
    repeated DetailItem region_list = 11;
    repeated string store_id_list = 9;
    repeated DetailItem store_list = 12;
    repeated string warehouse_id_list = 10;
    repeated DetailItem warehouse_list = 13;
    // 加工中心
    repeated string machining_center_id_list = 14;
    repeated DetailItem machining_center_list = 15;
}

message GetContractListReq{
    //分页大小
    int32 limit = 1;
    //跳过行数
    int32 offset = 2;
//    //合同名称
//    string name = 3;
//    //合同编号
//    string contract_no = 4;
    //状态
    string status = 3;
    //
    string  search_fields = 4;
    string search = 5;
    // 合同中的商品ID
    repeated string pro_names = 6;
}

message GetContractListRes{
    repeated Contract list = 1;
    int32 count = 2;
}

message GetContractDetailReq{
    string id = 1;
    repeated string pro_names = 2;
}

message GetContractDetailRes{
    Contract contract = 1;
    repeated ContractPro pro_list = 2;
}


message UpdateContractReq{
    Contract contract = 2;
    repeated ContractPro pro_list = 3;
    repeated string deleted_list = 4;

}

message UpdateContractRes{
    bool success = 1;
}

message GetTaxRateReq{
    repeated string pro_id_list = 1;
    string start_time = 2;
    string end_time = 3;
}

message GetTaxRateRes{
    repeated google.protobuf.Struct list = 1;
}

message GetTaxListReq{
    string vendor_id = 1;
    repeated string pro_id_list = 2;
    string warehouse_id = 3;
    string valid_time = 4;
    string store_id = 5;
    string machining_center_id = 6;
}

message Tax{
    string product_id = 1;
    double rate = 2;
    double tax = 3;
    double no_tax = 4;
}

message GetTaxListRes{
    repeated Tax list = 1;
}

message GetProListReq{
    string vendor_id = 1;
    string warehouse_id = 3;
    string valid_time = 4;
    string store_id = 5;
    string machining_center_id = 6;
}

message GetProListRes{
    repeated Pro pro_list = 1;
}

message Pro {
    string pro_id = 1;
    string pro_name = 2;
    string pro_code = 3;
    string unit_id = 4;
    string unit_name = 5;
    string unit_code = 6;
    string model_name = 7;
    double rate = 8;
    double tax = 9;
    double no_tax = 10;
    double unit_rate = 11;
}

message GetContractMetaListReq{
   repeated string pro_id_list = 1;
   repeated string vendor_id_list = 2;
   repeated string category_id_list = 3;
   string warehouse_id = 5;
   string region_id = 6;
}

message MetaItem{
    string code = 1;
    string name = 2;
}

message Item{
    string      pro_code = 1;
    string      pro_name = 2;
    string      category_code = 3;
    string      category_name = 4;
    string      vendor_code = 5;
    string      vendor_name = 6;
    string      default_unit_code = 7;
    string      default_unit_name = 8;
    double      rate = 28;
    string      model_name = 9;
    repeated    MetaItem warehouse_list = 10;
    repeated    MetaItem region_list = 11;
    string      purchase_code = 12;
    string      purchase_name = 13;
    double      purchase_rate = 14;
    double      purchase_tax = 15;
    string      order_code = 16;
    string      order_name = 17;
    double      order_rate = 25;
    double      order_tax = 26;
    string      bom_code = 18;
    string      bom_name = 19;
    double      bom_tax = 20;
    double      bom_rate = 27;
    string      take_code = 21;
    string      take_name = 22;
    double      take_rate = 23;
    double      take_tax = 24;
}

message GetContractMetaListRes{
   repeated Item list = 1;
}

message GetSerialNoReq{

}

message GetSerialNoRes{
    string serial_no = 1;
}
