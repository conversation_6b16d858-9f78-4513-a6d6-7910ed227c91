# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from metadata import contract_pb2 as metadata_dot_contract__pb2


class ContractServiceStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateContract = channel.unary_unary(
        '/contract.ContractService/CreateContract',
        request_serializer=metadata_dot_contract__pb2.CreateContractReq.SerializeToString,
        response_deserializer=metadata_dot_contract__pb2.CreateContractRes.FromString,
        )
    self.GetContractList = channel.unary_unary(
        '/contract.ContractService/GetContractList',
        request_serializer=metadata_dot_contract__pb2.GetContractListReq.SerializeToString,
        response_deserializer=metadata_dot_contract__pb2.GetContractListRes.FromString,
        )
    self.GetContractDetail = channel.unary_unary(
        '/contract.ContractService/GetContractDetail',
        request_serializer=metadata_dot_contract__pb2.GetContractDetailReq.SerializeToString,
        response_deserializer=metadata_dot_contract__pb2.GetContractDetailRes.FromString,
        )
    self.UpdateContract = channel.unary_unary(
        '/contract.ContractService/UpdateContract',
        request_serializer=metadata_dot_contract__pb2.UpdateContractReq.SerializeToString,
        response_deserializer=metadata_dot_contract__pb2.UpdateContractRes.FromString,
        )
    self.GetTaxRateList = channel.unary_unary(
        '/contract.ContractService/GetTaxRateList',
        request_serializer=metadata_dot_contract__pb2.GetTaxRateReq.SerializeToString,
        response_deserializer=metadata_dot_contract__pb2.GetTaxRateRes.FromString,
        )
    self.GetTaxList = channel.unary_unary(
        '/contract.ContractService/GetTaxList',
        request_serializer=metadata_dot_contract__pb2.GetTaxListReq.SerializeToString,
        response_deserializer=metadata_dot_contract__pb2.GetTaxListRes.FromString,
        )
    self.GetProList = channel.unary_unary(
        '/contract.ContractService/GetProList',
        request_serializer=metadata_dot_contract__pb2.GetProListReq.SerializeToString,
        response_deserializer=metadata_dot_contract__pb2.GetProListRes.FromString,
        )
    self.GetContractMetaList = channel.unary_unary(
        '/contract.ContractService/GetContractMetaList',
        request_serializer=metadata_dot_contract__pb2.GetContractMetaListReq.SerializeToString,
        response_deserializer=metadata_dot_contract__pb2.GetContractMetaListRes.FromString,
        )
    self.GetSerialNo = channel.unary_unary(
        '/contract.ContractService/GetSerialNo',
        request_serializer=metadata_dot_contract__pb2.GetSerialNoReq.SerializeToString,
        response_deserializer=metadata_dot_contract__pb2.GetSerialNoRes.FromString,
        )


class ContractServiceServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def CreateContract(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetContractList(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetContractDetail(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateContract(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTaxRateList(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTaxList(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProList(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetContractMetaList(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetSerialNo(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_ContractServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateContract': grpc.unary_unary_rpc_method_handler(
          servicer.CreateContract,
          request_deserializer=metadata_dot_contract__pb2.CreateContractReq.FromString,
          response_serializer=metadata_dot_contract__pb2.CreateContractRes.SerializeToString,
      ),
      'GetContractList': grpc.unary_unary_rpc_method_handler(
          servicer.GetContractList,
          request_deserializer=metadata_dot_contract__pb2.GetContractListReq.FromString,
          response_serializer=metadata_dot_contract__pb2.GetContractListRes.SerializeToString,
      ),
      'GetContractDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetContractDetail,
          request_deserializer=metadata_dot_contract__pb2.GetContractDetailReq.FromString,
          response_serializer=metadata_dot_contract__pb2.GetContractDetailRes.SerializeToString,
      ),
      'UpdateContract': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateContract,
          request_deserializer=metadata_dot_contract__pb2.UpdateContractReq.FromString,
          response_serializer=metadata_dot_contract__pb2.UpdateContractRes.SerializeToString,
      ),
      'GetTaxRateList': grpc.unary_unary_rpc_method_handler(
          servicer.GetTaxRateList,
          request_deserializer=metadata_dot_contract__pb2.GetTaxRateReq.FromString,
          response_serializer=metadata_dot_contract__pb2.GetTaxRateRes.SerializeToString,
      ),
      'GetTaxList': grpc.unary_unary_rpc_method_handler(
          servicer.GetTaxList,
          request_deserializer=metadata_dot_contract__pb2.GetTaxListReq.FromString,
          response_serializer=metadata_dot_contract__pb2.GetTaxListRes.SerializeToString,
      ),
      'GetProList': grpc.unary_unary_rpc_method_handler(
          servicer.GetProList,
          request_deserializer=metadata_dot_contract__pb2.GetProListReq.FromString,
          response_serializer=metadata_dot_contract__pb2.GetProListRes.SerializeToString,
      ),
      'GetContractMetaList': grpc.unary_unary_rpc_method_handler(
          servicer.GetContractMetaList,
          request_deserializer=metadata_dot_contract__pb2.GetContractMetaListReq.FromString,
          response_serializer=metadata_dot_contract__pb2.GetContractMetaListRes.SerializeToString,
      ),
      'GetSerialNo': grpc.unary_unary_rpc_method_handler(
          servicer.GetSerialNo,
          request_deserializer=metadata_dot_contract__pb2.GetSerialNoReq.FromString,
          response_serializer=metadata_dot_contract__pb2.GetSerialNoRes.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'contract.ContractService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
