syntax = "proto3";
import "google/api/annotations.proto";
//supply查询订货价
service ThirdPartyDemandService {
    rpc GetDemandByCode(GetDemandByCodeRequest) returns (GetOrderByCodeResponse) {
        option (google.api.http) = {
            post: "/api/v2/supply/demand/thirdparty"
            body: "*"
        };
    }
}

message GetDemandByCodeRequest {
   //码值
   string code = 1;
   string partner_id = 2;
}
message GetOrderByCodeResponse {
    //code
    string code = 1;
    //批次id
    int64 id = 2;
    //订单类型
    string type = 3;
    //期望日期
    string expect_date = 4;
    //remark
    string remark = 5;
}
