syntax = "proto3";
package third_party;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// 第三方操作收货差异相关服务
service TpReceiveDiffService{

    // 更新并审核收货差异单
    rpc FinishReceiveDiff (FinishReceiveDiffRequest) returns (CommonResponse){
        option (google.api.http) = {
            put:"/api/v2/supply/tp/receive-diff/{id}/submit"
            body:"*"
        };
    }

}

message FinishReceiveDiffRequest{
    // 收货单单号
    string receive_code = 1;
    // 收货差异单商品详情
    repeated ReceiveDiffProduct products = 2;
    // 备注
    string remark = 3;
}


message ReceiveDiffProduct{
    string product_code = 1;
    // 门店承担数量
    double s_diff_quantity = 2;
    // 配送费承担数量
    double d_diff_quantity = 3;
    // 差异原因
    string reason_type = 4;
    // 备注
    string remark = 5;
}

message CommonResponse{
    string code = 1;
    string status = 2;
    string msg = 3;
    string failed_code = 4;
}