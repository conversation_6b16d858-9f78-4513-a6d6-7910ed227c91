# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: third_party/demand.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='third_party/demand.proto',
  package='demand',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x18third_party/demand.proto\x12\x06\x64\x65mand\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xea\x02\n\x17\x43reateMainDemandRequest\x12/\n\x0b\x64\x65mand_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x61rrival_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06remark\x18\x03 \x01(\t\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12=\n\x08products\x18\x05 \x03(\x0b\x32+.demand.CreateMainDemandRequest.ProductItem\x12\x10\n\x08\x62\x61tch_id\x18\x07 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x08 \x01(\t\x1ai\n\x0bProductItem\x12\x14\n\x0cproduct_code\x18\x01 \x01(\t\x12\x10\n\x08quantity\x18\x02 \x01(\x02\x12\x19\n\x11\x64istribution_type\x18\x03 \x01(\t\x12\x17\n\x0f\x64istribution_by\x18\x04 \x01(\x04\"l\n\x18\x43reateMainDemandResponse\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x0b\n\x03msg\x18\x03 \x01(\t\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\x13\n\x0b\x66\x61iled_code\x18\x05 \x01(\t2\x9a\x01\n\x08TpDemand\x12\x8d\x01\n\x10\x43reateMainDemand\x12\x1f.demand.CreateMainDemandRequest\x1a .demand.CreateMainDemandResponse\"6\x82\xd3\xe4\x93\x02\x30\"+/api/v2/supply/tp/hd_assignment/main_demand:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_CREATEMAINDEMANDREQUEST_PRODUCTITEM = _descriptor.Descriptor(
  name='ProductItem',
  full_name='demand.CreateMainDemandRequest.ProductItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_code', full_name='demand.CreateMainDemandRequest.ProductItem.product_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='demand.CreateMainDemandRequest.ProductItem.quantity', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='demand.CreateMainDemandRequest.ProductItem.distribution_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_by', full_name='demand.CreateMainDemandRequest.ProductItem.distribution_by', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=357,
  serialized_end=462,
)

_CREATEMAINDEMANDREQUEST = _descriptor.Descriptor(
  name='CreateMainDemandRequest',
  full_name='demand.CreateMainDemandRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='demand.CreateMainDemandRequest.demand_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_date', full_name='demand.CreateMainDemandRequest.arrival_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='demand.CreateMainDemandRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='demand.CreateMainDemandRequest.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='demand.CreateMainDemandRequest.products', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='demand.CreateMainDemandRequest.batch_id', index=5,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='demand.CreateMainDemandRequest.code', index=6,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_CREATEMAINDEMANDREQUEST_PRODUCTITEM, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=100,
  serialized_end=462,
)


_CREATEMAINDEMANDRESPONSE = _descriptor.Descriptor(
  name='CreateMainDemandResponse',
  full_name='demand.CreateMainDemandResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='demand.CreateMainDemandResponse.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='demand.CreateMainDemandResponse.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msg', full_name='demand.CreateMainDemandResponse.msg', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='demand.CreateMainDemandResponse.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='failed_code', full_name='demand.CreateMainDemandResponse.failed_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=464,
  serialized_end=572,
)

_CREATEMAINDEMANDREQUEST_PRODUCTITEM.containing_type = _CREATEMAINDEMANDREQUEST
_CREATEMAINDEMANDREQUEST.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEMAINDEMANDREQUEST.fields_by_name['arrival_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEMAINDEMANDREQUEST.fields_by_name['products'].message_type = _CREATEMAINDEMANDREQUEST_PRODUCTITEM
DESCRIPTOR.message_types_by_name['CreateMainDemandRequest'] = _CREATEMAINDEMANDREQUEST
DESCRIPTOR.message_types_by_name['CreateMainDemandResponse'] = _CREATEMAINDEMANDRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CreateMainDemandRequest = _reflection.GeneratedProtocolMessageType('CreateMainDemandRequest', (_message.Message,), dict(

  ProductItem = _reflection.GeneratedProtocolMessageType('ProductItem', (_message.Message,), dict(
    DESCRIPTOR = _CREATEMAINDEMANDREQUEST_PRODUCTITEM,
    __module__ = 'third_party.demand_pb2'
    # @@protoc_insertion_point(class_scope:demand.CreateMainDemandRequest.ProductItem)
    ))
  ,
  DESCRIPTOR = _CREATEMAINDEMANDREQUEST,
  __module__ = 'third_party.demand_pb2'
  # @@protoc_insertion_point(class_scope:demand.CreateMainDemandRequest)
  ))
_sym_db.RegisterMessage(CreateMainDemandRequest)
_sym_db.RegisterMessage(CreateMainDemandRequest.ProductItem)

CreateMainDemandResponse = _reflection.GeneratedProtocolMessageType('CreateMainDemandResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEMAINDEMANDRESPONSE,
  __module__ = 'third_party.demand_pb2'
  # @@protoc_insertion_point(class_scope:demand.CreateMainDemandResponse)
  ))
_sym_db.RegisterMessage(CreateMainDemandResponse)



_TPDEMAND = _descriptor.ServiceDescriptor(
  name='TpDemand',
  full_name='demand.TpDemand',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=575,
  serialized_end=729,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateMainDemand',
    full_name='demand.TpDemand.CreateMainDemand',
    index=0,
    containing_service=None,
    input_type=_CREATEMAINDEMANDREQUEST,
    output_type=_CREATEMAINDEMANDRESPONSE,
    serialized_options=_b('\202\323\344\223\0020\"+/api/v2/supply/tp/hd_assignment/main_demand:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_TPDEMAND)

DESCRIPTOR.services_by_name['TpDemand'] = _TPDEMAND

# @@protoc_insertion_point(module_scope)
