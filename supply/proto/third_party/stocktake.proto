syntax = "proto3";

package stocktake;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

//Stocktake 盘点服务——供三方使用
service TpStocktake {

    //GetStocktake 查询盘点单
    rpc GetStocktakeBrief (GetStocktakeBriefRequest) returns (GetStocktakeBriefResponse) {
        option (google.api.http) = {get: "/api/v2/supply/third_party/stocktake" };
    }
}

message GetStocktakeBriefRequest {
    // 门店编号
    repeated string store_code = 1;
    //查询开始时间
    google.protobuf.Timestamp start_date = 2;
    //查询结束时间
    google.protobuf.Timestamp end_date = 3;
}
message GetStocktakeBriefResponse {
    repeated StocktakeBrief rows = 1;
    uint32 total = 2;
}

message StocktakeBrief {
    // 门店编号
    string store_code = 1;
    // 盘点单号
    string code = 2;
    // 盘点单类型
    string type = 3;
    // 盘点单状态
    string status = 4;
    // 盘点日期
    google.protobuf.Timestamp stocktake_date = 5;
}