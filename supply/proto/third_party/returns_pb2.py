# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: third_party/returns.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='third_party/returns.proto',
  package='third_party',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x19third_party/returns.proto\x12\x0bthird_party\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"+\n\x1b\x44\x65liveryReturnByCodeRequest\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\"p\n\x1c\x44\x65liveryReturnByCodeResponse\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x0b\n\x03msg\x18\x03 \x01(\t\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\x13\n\x0b\x66\x61iled_code\x18\x05 \x01(\t2\xae\x01\n\x0fTpReturnService\x12\x9a\x01\n\x14\x44\x65liveryReturnByCode\x12(.third_party.DeliveryReturnByCodeRequest\x1a).third_party.DeliveryReturnByCodeResponse\"-\x82\xd3\xe4\x93\x02\'\x1a\"/api/v2/supply/tp/returns/delivery:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_DELIVERYRETURNBYCODEREQUEST = _descriptor.Descriptor(
  name='DeliveryReturnByCodeRequest',
  full_name='third_party.DeliveryReturnByCodeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='third_party.DeliveryReturnByCodeRequest.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=105,
  serialized_end=148,
)


_DELIVERYRETURNBYCODERESPONSE = _descriptor.Descriptor(
  name='DeliveryReturnByCodeResponse',
  full_name='third_party.DeliveryReturnByCodeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='third_party.DeliveryReturnByCodeResponse.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='third_party.DeliveryReturnByCodeResponse.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msg', full_name='third_party.DeliveryReturnByCodeResponse.msg', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='third_party.DeliveryReturnByCodeResponse.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='failed_code', full_name='third_party.DeliveryReturnByCodeResponse.failed_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=150,
  serialized_end=262,
)

DESCRIPTOR.message_types_by_name['DeliveryReturnByCodeRequest'] = _DELIVERYRETURNBYCODEREQUEST
DESCRIPTOR.message_types_by_name['DeliveryReturnByCodeResponse'] = _DELIVERYRETURNBYCODERESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

DeliveryReturnByCodeRequest = _reflection.GeneratedProtocolMessageType('DeliveryReturnByCodeRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELIVERYRETURNBYCODEREQUEST,
  __module__ = 'third_party.returns_pb2'
  # @@protoc_insertion_point(class_scope:third_party.DeliveryReturnByCodeRequest)
  ))
_sym_db.RegisterMessage(DeliveryReturnByCodeRequest)

DeliveryReturnByCodeResponse = _reflection.GeneratedProtocolMessageType('DeliveryReturnByCodeResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELIVERYRETURNBYCODERESPONSE,
  __module__ = 'third_party.returns_pb2'
  # @@protoc_insertion_point(class_scope:third_party.DeliveryReturnByCodeResponse)
  ))
_sym_db.RegisterMessage(DeliveryReturnByCodeResponse)



_TPRETURNSERVICE = _descriptor.ServiceDescriptor(
  name='TpReturnService',
  full_name='third_party.TpReturnService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=265,
  serialized_end=439,
  methods=[
  _descriptor.MethodDescriptor(
    name='DeliveryReturnByCode',
    full_name='third_party.TpReturnService.DeliveryReturnByCode',
    index=0,
    containing_service=None,
    input_type=_DELIVERYRETURNBYCODEREQUEST,
    output_type=_DELIVERYRETURNBYCODERESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\032\"/api/v2/supply/tp/returns/delivery:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_TPRETURNSERVICE)

DESCRIPTOR.services_by_name['TpReturnService'] = _TPRETURNSERVICE

# @@protoc_insertion_point(module_scope)
