# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from third_party import returns_pb2 as third__party_dot_returns__pb2


class TpReturnServiceStub(object):
  """三方退货相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.DeliveryReturnByCode = channel.unary_unary(
        '/third_party.TpReturnService/DeliveryReturnByCode',
        request_serializer=third__party_dot_returns__pb2.DeliveryReturnByCodeRequest.SerializeToString,
        response_deserializer=third__party_dot_returns__pb2.DeliveryReturnByCodeResponse.FromString,
        )


class TpReturnServiceServicer(object):
  """三方退货相关服务
  """

  def DeliveryReturnByCode(self, request, context):
    """三方确认提货
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_TpReturnServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'DeliveryReturnByCode': grpc.unary_unary_rpc_method_handler(
          servicer.DeliveryReturnByCode,
          request_deserializer=third__party_dot_returns__pb2.DeliveryReturnByCodeRequest.FromString,
          response_serializer=third__party_dot_returns__pb2.DeliveryReturnByCodeResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'third_party.TpReturnService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
