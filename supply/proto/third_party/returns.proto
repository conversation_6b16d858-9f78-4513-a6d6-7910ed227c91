syntax = "proto3";
package third_party;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// 三方退货相关服务
service TpReturnService {

    // 三方确认提货
    rpc DeliveryReturnByCode (DeliveryReturnByCodeRequest) returns (DeliveryReturnByCodeResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/tp/returns/delivery"
            body:"*"
        };
    }
}



message DeliveryReturnByCodeRequest {
    string code = 1;
}

message DeliveryReturnByCodeResponse {
    uint64 batch_id = 1;
    string status = 2;
    string msg = 3;
    string code = 4;
    string failed_code = 5;

}