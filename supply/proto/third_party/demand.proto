syntax = "proto3";

package demand;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

service TpDemand {

    // 三方创建主配单
    rpc CreateMainDemand (CreateMainDemandRequest) returns (CreateMainDemandResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/tp/hd_assignment/main_demand"
        body: "*"
        };
    }

}

// 创建商品主配单请求参数
message CreateMainDemandRequest {
    // 订货日期
    google.protobuf.Timestamp demand_date = 1;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 2;
    // 备注
    string remark = 3;
    // 门店编码
    string store_code = 4;
    // 商品主配单的参数
    repeated ProductItem products = 5;

    message ProductItem {
        // 商品编码
        string product_code = 1;
        // 订货数量
        float quantity = 2;
        // 商品订货物流模式(NMD:配送, PUR:直送, PAD:加工中心配送)
        string distribution_type = 3;
        // 配送中心(不修改则可以不传)
        uint64 distribution_by = 4;
    }
    // 唯一请求id
    uint64 batch_id = 7;
    // 单号 —— htj用这个做code
    string code = 8;

}

message CreateMainDemandResponse {
    uint64 batch_id = 1;
    string status = 2;
    string msg = 3;
    string code = 4;
    string failed_code = 5;

}