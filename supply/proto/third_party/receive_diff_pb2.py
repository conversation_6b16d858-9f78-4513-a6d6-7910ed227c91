# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: third_party/receive_diff.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='third_party/receive_diff.proto',
  package='third_party',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1ethird_party/receive_diff.proto\x12\x0bthird_party\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"s\n\x18\x46inishReceiveDiffRequest\x12\x14\n\x0creceive_code\x18\x01 \x01(\t\x12\x31\n\x08products\x18\x02 \x03(\x0b\x32\x1f.third_party.ReceiveDiffProduct\x12\x0e\n\x06remark\x18\x03 \x01(\t\"\x81\x01\n\x12ReceiveDiffProduct\x12\x14\n\x0cproduct_code\x18\x01 \x01(\t\x12\x17\n\x0fs_diff_quantity\x18\x02 \x01(\x01\x12\x17\n\x0f\x64_diff_quantity\x18\x03 \x01(\x01\x12\x13\n\x0breason_type\x18\x04 \x01(\t\x12\x0e\n\x06remark\x18\x05 \x01(\t\"P\n\x0e\x43ommonResponse\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x0b\n\x03msg\x18\x03 \x01(\t\x12\x13\n\x0b\x66\x61iled_code\x18\x04 \x01(\t2\xa7\x01\n\x14TpReceiveDiffService\x12\x8e\x01\n\x11\x46inishReceiveDiff\x12%.third_party.FinishReceiveDiffRequest\x1a\x1b.third_party.CommonResponse\"5\x82\xd3\xe4\x93\x02/\x1a*/api/v2/supply/tp/receive-diff/{id}/submit:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_FINISHRECEIVEDIFFREQUEST = _descriptor.Descriptor(
  name='FinishReceiveDiffRequest',
  full_name='third_party.FinishReceiveDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receive_code', full_name='third_party.FinishReceiveDiffRequest.receive_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='third_party.FinishReceiveDiffRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='third_party.FinishReceiveDiffRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=110,
  serialized_end=225,
)


_RECEIVEDIFFPRODUCT = _descriptor.Descriptor(
  name='ReceiveDiffProduct',
  full_name='third_party.ReceiveDiffProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_code', full_name='third_party.ReceiveDiffProduct.product_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_quantity', full_name='third_party.ReceiveDiffProduct.s_diff_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_quantity', full_name='third_party.ReceiveDiffProduct.d_diff_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='third_party.ReceiveDiffProduct.reason_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='third_party.ReceiveDiffProduct.remark', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=228,
  serialized_end=357,
)


_COMMONRESPONSE = _descriptor.Descriptor(
  name='CommonResponse',
  full_name='third_party.CommonResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='third_party.CommonResponse.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='third_party.CommonResponse.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msg', full_name='third_party.CommonResponse.msg', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='failed_code', full_name='third_party.CommonResponse.failed_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=359,
  serialized_end=439,
)

_FINISHRECEIVEDIFFREQUEST.fields_by_name['products'].message_type = _RECEIVEDIFFPRODUCT
DESCRIPTOR.message_types_by_name['FinishReceiveDiffRequest'] = _FINISHRECEIVEDIFFREQUEST
DESCRIPTOR.message_types_by_name['ReceiveDiffProduct'] = _RECEIVEDIFFPRODUCT
DESCRIPTOR.message_types_by_name['CommonResponse'] = _COMMONRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

FinishReceiveDiffRequest = _reflection.GeneratedProtocolMessageType('FinishReceiveDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _FINISHRECEIVEDIFFREQUEST,
  __module__ = 'third_party.receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:third_party.FinishReceiveDiffRequest)
  ))
_sym_db.RegisterMessage(FinishReceiveDiffRequest)

ReceiveDiffProduct = _reflection.GeneratedProtocolMessageType('ReceiveDiffProduct', (_message.Message,), dict(
  DESCRIPTOR = _RECEIVEDIFFPRODUCT,
  __module__ = 'third_party.receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:third_party.ReceiveDiffProduct)
  ))
_sym_db.RegisterMessage(ReceiveDiffProduct)

CommonResponse = _reflection.GeneratedProtocolMessageType('CommonResponse', (_message.Message,), dict(
  DESCRIPTOR = _COMMONRESPONSE,
  __module__ = 'third_party.receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:third_party.CommonResponse)
  ))
_sym_db.RegisterMessage(CommonResponse)



_TPRECEIVEDIFFSERVICE = _descriptor.ServiceDescriptor(
  name='TpReceiveDiffService',
  full_name='third_party.TpReceiveDiffService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=442,
  serialized_end=609,
  methods=[
  _descriptor.MethodDescriptor(
    name='FinishReceiveDiff',
    full_name='third_party.TpReceiveDiffService.FinishReceiveDiff',
    index=0,
    containing_service=None,
    input_type=_FINISHRECEIVEDIFFREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\002/\032*/api/v2/supply/tp/receive-diff/{id}/submit:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_TPRECEIVEDIFFSERVICE)

DESCRIPTOR.services_by_name['TpReceiveDiffService'] = _TPRECEIVEDIFFSERVICE

# @@protoc_insertion_point(module_scope)
