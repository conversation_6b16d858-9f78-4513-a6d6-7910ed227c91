# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from third_party import demands_pb2 as third__party_dot_demands__pb2


class ThirdPartyDemandServiceStub(object):
  """supply查询订货价
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetDemandByCode = channel.unary_unary(
        '/ThirdPartyDemandService/GetDemandByCode',
        request_serializer=third__party_dot_demands__pb2.GetDemandByCodeRequest.SerializeToString,
        response_deserializer=third__party_dot_demands__pb2.GetOrderByCodeResponse.FromString,
        )


class ThirdPartyDemandServiceServicer(object):
  """supply查询订货价
  """

  def GetDemandByCode(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_ThirdPartyDemandServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetDemandByCode': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandByCode,
          request_deserializer=third__party_dot_demands__pb2.GetDemandByCodeRequest.FromString,
          response_serializer=third__party_dot_demands__pb2.GetOrderByCodeResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'ThirdPartyDemandService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
