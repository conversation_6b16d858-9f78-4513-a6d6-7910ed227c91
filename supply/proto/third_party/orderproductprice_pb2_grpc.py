# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from third_party import orderproductprice_pb2 as third__party_dot_orderproductprice__pb2


class QuerySupplyOrderProductPriceStub(object):
  """supply查询订货价
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetOrderProductPrice = channel.unary_unary(
        '/QuerySupplyOrderProductPrice/GetOrderProductPrice',
        request_serializer=third__party_dot_orderproductprice__pb2.GetOrderProductPriceRequest.SerializeToString,
        response_deserializer=third__party_dot_orderproductprice__pb2.GetOrderProductPriceResponse.FromString,
        )


class QuerySupplyOrderProductPriceServicer(object):
  """supply查询订货价
  """

  def GetOrderProductPrice(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_QuerySupplyOrderProductPriceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetOrderProductPrice': grpc.unary_unary_rpc_method_handler(
          servicer.GetOrderProductPrice,
          request_deserializer=third__party_dot_orderproductprice__pb2.GetOrderProductPriceRequest.FromString,
          response_serializer=third__party_dot_orderproductprice__pb2.GetOrderProductPriceResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'QuerySupplyOrderProductPrice', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
