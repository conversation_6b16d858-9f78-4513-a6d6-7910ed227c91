# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from third_party import receive_diff_pb2 as third__party_dot_receive__diff__pb2


class TpReceiveDiffServiceStub(object):
  """第三方操作收货差异相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.FinishReceiveDiff = channel.unary_unary(
        '/third_party.TpReceiveDiffService/FinishReceiveDiff',
        request_serializer=third__party_dot_receive__diff__pb2.FinishReceiveDiffRequest.SerializeToString,
        response_deserializer=third__party_dot_receive__diff__pb2.CommonResponse.FromString,
        )


class TpReceiveDiffServiceServicer(object):
  """第三方操作收货差异相关服务
  """

  def FinishReceiveDiff(self, request, context):
    """更新并审核收货差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_TpReceiveDiffServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'FinishReceiveDiff': grpc.unary_unary_rpc_method_handler(
          servicer.FinishReceiveDiff,
          request_deserializer=third__party_dot_receive__diff__pb2.FinishReceiveDiffRequest.FromString,
          response_serializer=third__party_dot_receive__diff__pb2.CommonResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'third_party.TpReceiveDiffService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
