# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from third_party import demand_pb2 as third__party_dot_demand__pb2


class TpDemandStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateMainDemand = channel.unary_unary(
        '/demand.TpDemand/CreateMainDemand',
        request_serializer=third__party_dot_demand__pb2.CreateMainDemandRequest.SerializeToString,
        response_deserializer=third__party_dot_demand__pb2.CreateMainDemandResponse.FromString,
        )


class TpDemandServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def CreateMainDemand(self, request, context):
    """三方创建主配单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_TpDemandServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateMainDemand': grpc.unary_unary_rpc_method_handler(
          servicer.CreateMainDemand,
          request_deserializer=third__party_dot_demand__pb2.CreateMainDemandRequest.FromString,
          response_serializer=third__party_dot_demand__pb2.CreateMainDemandResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'demand.TpDemand', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
