syntax = "proto3";

import "google/api/annotations.proto";
option java_package = "com.hex.framework.grpc";

//supply查询订货价
service QuerySupplyOrderProductPrice {
    rpc GetOrderProductPrice (GetOrderProductPriceRequest) returns (GetOrderProductPriceResponse) {
        option (google.api.http) = {
                    post: "/api/v2/integration/supply/getOrderProductPrice"
                    body: "*"
        };
    }
}

message GetOrderProductPriceRequest {
    //门店编号
    uint64 storeId = 1;
    //商品编号
    repeated uint64 productId= 2;
    repeated string productCode= 3;
    //分页参数
    string pageNum = 4;
    //分页条数
    string pageSize = 5;
    //是否返回全部
    bool isAll = 6;
}
message GetOrderProductPriceResponse {
    repeated SupplyOrderProductPrice rows = 1;
    uint64 total = 2;
}

message SupplyOrderProductPrice {
    // 门店code
    string storeCode = 1;
    // 门店编码
    uint64 storeId = 2;
    // 商品code
    string productCode = 3;
    // 商品编码
    uint64 productId = 4;
     // 单位code
    string unitCode = 5;
    // 单位编码
    uint64 unitId = 6;
    // 订货价
    double price = 7;
}