# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: third_party/demands.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='third_party/demands.proto',
  package='',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x19third_party/demands.proto\x1a\x1cgoogle/api/annotations.proto\":\n\x16GetDemandByCodeRequest\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x12\n\npartner_id\x18\x02 \x01(\t\"e\n\x16GetOrderByCodeResponse\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\n\n\x02id\x18\x02 \x01(\x03\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x13\n\x0b\x65xpect_date\x18\x04 \x01(\t\x12\x0e\n\x06remark\x18\x05 \x01(\t2\x8b\x01\n\x17ThirdPartyDemandService\x12p\n\x0fGetDemandByCode\x12\x17.GetDemandByCodeRequest\x1a\x17.GetOrderByCodeResponse\"+\x82\xd3\xe4\x93\x02%\" /api/v2/supply/demand/thirdparty:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,])




_GETDEMANDBYCODEREQUEST = _descriptor.Descriptor(
  name='GetDemandByCodeRequest',
  full_name='GetDemandByCodeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='GetDemandByCodeRequest.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='GetDemandByCodeRequest.partner_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=59,
  serialized_end=117,
)


_GETORDERBYCODERESPONSE = _descriptor.Descriptor(
  name='GetOrderByCodeResponse',
  full_name='GetOrderByCodeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='GetOrderByCodeResponse.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='GetOrderByCodeResponse.id', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='GetOrderByCodeResponse.type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='expect_date', full_name='GetOrderByCodeResponse.expect_date', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='GetOrderByCodeResponse.remark', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=119,
  serialized_end=220,
)

DESCRIPTOR.message_types_by_name['GetDemandByCodeRequest'] = _GETDEMANDBYCODEREQUEST
DESCRIPTOR.message_types_by_name['GetOrderByCodeResponse'] = _GETORDERBYCODERESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetDemandByCodeRequest = _reflection.GeneratedProtocolMessageType('GetDemandByCodeRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDEMANDBYCODEREQUEST,
  __module__ = 'third_party.demands_pb2'
  # @@protoc_insertion_point(class_scope:GetDemandByCodeRequest)
  ))
_sym_db.RegisterMessage(GetDemandByCodeRequest)

GetOrderByCodeResponse = _reflection.GeneratedProtocolMessageType('GetOrderByCodeResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETORDERBYCODERESPONSE,
  __module__ = 'third_party.demands_pb2'
  # @@protoc_insertion_point(class_scope:GetOrderByCodeResponse)
  ))
_sym_db.RegisterMessage(GetOrderByCodeResponse)



_THIRDPARTYDEMANDSERVICE = _descriptor.ServiceDescriptor(
  name='ThirdPartyDemandService',
  full_name='ThirdPartyDemandService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=223,
  serialized_end=362,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetDemandByCode',
    full_name='ThirdPartyDemandService.GetDemandByCode',
    index=0,
    containing_service=None,
    input_type=_GETDEMANDBYCODEREQUEST,
    output_type=_GETORDERBYCODERESPONSE,
    serialized_options=_b('\202\323\344\223\002%\" /api/v2/supply/demand/thirdparty:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_THIRDPARTYDEMANDSERVICE)

DESCRIPTOR.services_by_name['ThirdPartyDemandService'] = _THIRDPARTYDEMANDSERVICE

# @@protoc_insertion_point(module_scope)
