# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: third_party/finance_reconciliation.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='third_party/finance_reconciliation.proto',
  package='finance_reconciliation',
  syntax='proto3',
  serialized_options=_b('\n\026com.hex.framework.grpc'),
  serialized_pb=_b('\n(third_party/finance_reconciliation.proto\x12\x16\x66inance_reconciliation\x1a\x1cgoogle/api/annotations.proto\"\xae\x01\n\x18\x46inanceReconciliationReq\x12\x0e\n\x06iGjahr\x18\x01 \x01(\x05\x12\x0e\n\x06iMonat\x18\x02 \x01(\x05\x12\x0e\n\x06iKunnr\x18\x03 \x01(\t\x12\x0e\n\x06iBukrs\x18\x04 \x01(\t\x12\r\n\x05limit\x18\x05 \x01(\x05\x12\x0e\n\x06offset\x18\x06 \x01(\x05\x12\x11\n\tlikeBezei\x18\x07 \x01(\t\x12\r\n\x05order\x18\x08 \x01(\x05\x12\x11\n\tlikeBelnr\x18\t \x01(\t\"\x8c\x01\n\x1d\x46inanceReconciliationResponse\x12?\n\x04rows\x18\x01 \x03(\x0b\x32\x31.finance_reconciliation.FinanceReconciliationItem\x12\r\n\x05total\x18\x02 \x01(\x05\x12\x0b\n\x03msg\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\x05\"\xc3\x01\n\x19\x46inanceReconciliationItem\x12\r\n\x05kunnr\x18\x01 \x01(\t\x12\r\n\x05\x62udat\x18\x02 \x01(\t\x12\r\n\x05\x62\x65lnr\x18\x03 \x01(\t\x12\r\n\x05\x62lart\x18\x04 \x01(\t\x12\x0e\n\x06\x64mbtrY\x18\x05 \x01(\x01\x12\x0e\n\x06\x64mbtrH\x18\x06 \x01(\x01\x12\x0e\n\x06\x64mbtrE\x18\x07 \x01(\x01\x12\r\n\x05klimk\x18\x08 \x01(\x01\x12\r\n\x05\x62\x65zei\x18\t \x01(\t\x12\r\n\x05name1\x18\n \x01(\t\x12\r\n\x05\x63gdes\x18\x0b \x01(\t2\xca\x01\n\x1c\x46inanceReconciliationService\x12\xa9\x01\n\x18getFinanceReconciliation\x12\x30.finance_reconciliation.FinanceReconciliationReq\x1a\x35.finance_reconciliation.FinanceReconciliationResponse\"$\x82\xd3\xe4\x93\x02\x1e\"\x19/api/bidata/v1/fin/select:\x01*B\x18\n\x16\x63om.hex.framework.grpcb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,])




_FINANCERECONCILIATIONREQ = _descriptor.Descriptor(
  name='FinanceReconciliationReq',
  full_name='finance_reconciliation.FinanceReconciliationReq',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='iGjahr', full_name='finance_reconciliation.FinanceReconciliationReq.iGjahr', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='iMonat', full_name='finance_reconciliation.FinanceReconciliationReq.iMonat', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='iKunnr', full_name='finance_reconciliation.FinanceReconciliationReq.iKunnr', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='iBukrs', full_name='finance_reconciliation.FinanceReconciliationReq.iBukrs', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='finance_reconciliation.FinanceReconciliationReq.limit', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='finance_reconciliation.FinanceReconciliationReq.offset', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='likeBezei', full_name='finance_reconciliation.FinanceReconciliationReq.likeBezei', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='finance_reconciliation.FinanceReconciliationReq.order', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='likeBelnr', full_name='finance_reconciliation.FinanceReconciliationReq.likeBelnr', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=99,
  serialized_end=273,
)


_FINANCERECONCILIATIONRESPONSE = _descriptor.Descriptor(
  name='FinanceReconciliationResponse',
  full_name='finance_reconciliation.FinanceReconciliationResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='finance_reconciliation.FinanceReconciliationResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='finance_reconciliation.FinanceReconciliationResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msg', full_name='finance_reconciliation.FinanceReconciliationResponse.msg', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='finance_reconciliation.FinanceReconciliationResponse.status', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=276,
  serialized_end=416,
)


_FINANCERECONCILIATIONITEM = _descriptor.Descriptor(
  name='FinanceReconciliationItem',
  full_name='finance_reconciliation.FinanceReconciliationItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='kunnr', full_name='finance_reconciliation.FinanceReconciliationItem.kunnr', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='budat', full_name='finance_reconciliation.FinanceReconciliationItem.budat', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='belnr', full_name='finance_reconciliation.FinanceReconciliationItem.belnr', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='blart', full_name='finance_reconciliation.FinanceReconciliationItem.blart', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dmbtrY', full_name='finance_reconciliation.FinanceReconciliationItem.dmbtrY', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dmbtrH', full_name='finance_reconciliation.FinanceReconciliationItem.dmbtrH', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dmbtrE', full_name='finance_reconciliation.FinanceReconciliationItem.dmbtrE', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='klimk', full_name='finance_reconciliation.FinanceReconciliationItem.klimk', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bezei', full_name='finance_reconciliation.FinanceReconciliationItem.bezei', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name1', full_name='finance_reconciliation.FinanceReconciliationItem.name1', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cgdes', full_name='finance_reconciliation.FinanceReconciliationItem.cgdes', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=419,
  serialized_end=614,
)

_FINANCERECONCILIATIONRESPONSE.fields_by_name['rows'].message_type = _FINANCERECONCILIATIONITEM
DESCRIPTOR.message_types_by_name['FinanceReconciliationReq'] = _FINANCERECONCILIATIONREQ
DESCRIPTOR.message_types_by_name['FinanceReconciliationResponse'] = _FINANCERECONCILIATIONRESPONSE
DESCRIPTOR.message_types_by_name['FinanceReconciliationItem'] = _FINANCERECONCILIATIONITEM
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

FinanceReconciliationReq = _reflection.GeneratedProtocolMessageType('FinanceReconciliationReq', (_message.Message,), dict(
  DESCRIPTOR = _FINANCERECONCILIATIONREQ,
  __module__ = 'third_party.finance_reconciliation_pb2'
  # @@protoc_insertion_point(class_scope:finance_reconciliation.FinanceReconciliationReq)
  ))
_sym_db.RegisterMessage(FinanceReconciliationReq)

FinanceReconciliationResponse = _reflection.GeneratedProtocolMessageType('FinanceReconciliationResponse', (_message.Message,), dict(
  DESCRIPTOR = _FINANCERECONCILIATIONRESPONSE,
  __module__ = 'third_party.finance_reconciliation_pb2'
  # @@protoc_insertion_point(class_scope:finance_reconciliation.FinanceReconciliationResponse)
  ))
_sym_db.RegisterMessage(FinanceReconciliationResponse)

FinanceReconciliationItem = _reflection.GeneratedProtocolMessageType('FinanceReconciliationItem', (_message.Message,), dict(
  DESCRIPTOR = _FINANCERECONCILIATIONITEM,
  __module__ = 'third_party.finance_reconciliation_pb2'
  # @@protoc_insertion_point(class_scope:finance_reconciliation.FinanceReconciliationItem)
  ))
_sym_db.RegisterMessage(FinanceReconciliationItem)


DESCRIPTOR._options = None

_FINANCERECONCILIATIONSERVICE = _descriptor.ServiceDescriptor(
  name='FinanceReconciliationService',
  full_name='finance_reconciliation.FinanceReconciliationService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=617,
  serialized_end=819,
  methods=[
  _descriptor.MethodDescriptor(
    name='getFinanceReconciliation',
    full_name='finance_reconciliation.FinanceReconciliationService.getFinanceReconciliation',
    index=0,
    containing_service=None,
    input_type=_FINANCERECONCILIATIONREQ,
    output_type=_FINANCERECONCILIATIONRESPONSE,
    serialized_options=_b('\202\323\344\223\002\036\"\031/api/bidata/v1/fin/select:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_FINANCERECONCILIATIONSERVICE)

DESCRIPTOR.services_by_name['FinanceReconciliationService'] = _FINANCERECONCILIATIONSERVICE

# @@protoc_insertion_point(module_scope)
