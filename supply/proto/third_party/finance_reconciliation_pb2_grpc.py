# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from third_party import finance_reconciliation_pb2 as third__party_dot_finance__reconciliation__pb2


class FinanceReconciliationServiceStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.getFinanceReconciliation = channel.unary_unary(
        '/finance_reconciliation.FinanceReconciliationService/getFinanceReconciliation',
        request_serializer=third__party_dot_finance__reconciliation__pb2.FinanceReconciliationReq.SerializeToString,
        response_deserializer=third__party_dot_finance__reconciliation__pb2.FinanceReconciliationResponse.FromString,
        )


class FinanceReconciliationServiceServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def getFinanceReconciliation(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_FinanceReconciliationServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'getFinanceReconciliation': grpc.unary_unary_rpc_method_handler(
          servicer.getFinanceReconciliation,
          request_deserializer=third__party_dot_finance__reconciliation__pb2.FinanceReconciliationReq.FromString,
          response_serializer=third__party_dot_finance__reconciliation__pb2.FinanceReconciliationResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'finance_reconciliation.FinanceReconciliationService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
