# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: third_party/stocktake.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='third_party/stocktake.proto',
  package='stocktake',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1bthird_party/stocktake.proto\x12\tstocktake\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x8c\x01\n\x18GetStocktakeBriefRequest\x12\x12\n\nstore_code\x18\x01 \x03(\t\x12.\n\nstart_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"S\n\x19GetStocktakeBriefResponse\x12\'\n\x04rows\x18\x01 \x03(\x0b\x32\x19.stocktake.StocktakeBrief\x12\r\n\x05total\x18\x02 \x01(\r\"\x84\x01\n\x0eStocktakeBrief\x12\x12\n\nstore_code\x18\x01 \x01(\t\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x32\n\x0estocktake_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp2\x9c\x01\n\x0bTpStocktake\x12\x8c\x01\n\x11GetStocktakeBrief\x12#.stocktake.GetStocktakeBriefRequest\x1a$.stocktake.GetStocktakeBriefResponse\",\x82\xd3\xe4\x93\x02&\x12$/api/v2/supply/third_party/stocktakeb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_GETSTOCKTAKEBRIEFREQUEST = _descriptor.Descriptor(
  name='GetStocktakeBriefRequest',
  full_name='stocktake.GetStocktakeBriefRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_code', full_name='stocktake.GetStocktakeBriefRequest.store_code', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='stocktake.GetStocktakeBriefRequest.start_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='stocktake.GetStocktakeBriefRequest.end_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=106,
  serialized_end=246,
)


_GETSTOCKTAKEBRIEFRESPONSE = _descriptor.Descriptor(
  name='GetStocktakeBriefResponse',
  full_name='stocktake.GetStocktakeBriefResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='stocktake.GetStocktakeBriefResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='stocktake.GetStocktakeBriefResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=248,
  serialized_end=331,
)


_STOCKTAKEBRIEF = _descriptor.Descriptor(
  name='StocktakeBrief',
  full_name='stocktake.StocktakeBrief',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_code', full_name='stocktake.StocktakeBrief.store_code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='stocktake.StocktakeBrief.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='stocktake.StocktakeBrief.type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='stocktake.StocktakeBrief.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_date', full_name='stocktake.StocktakeBrief.stocktake_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=334,
  serialized_end=466,
)

_GETSTOCKTAKEBRIEFREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEBRIEFREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEBRIEFRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEBRIEF
_STOCKTAKEBRIEF.fields_by_name['stocktake_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['GetStocktakeBriefRequest'] = _GETSTOCKTAKEBRIEFREQUEST
DESCRIPTOR.message_types_by_name['GetStocktakeBriefResponse'] = _GETSTOCKTAKEBRIEFRESPONSE
DESCRIPTOR.message_types_by_name['StocktakeBrief'] = _STOCKTAKEBRIEF
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetStocktakeBriefRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeBriefRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEBRIEFREQUEST,
  __module__ = 'third_party.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.GetStocktakeBriefRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeBriefRequest)

GetStocktakeBriefResponse = _reflection.GeneratedProtocolMessageType('GetStocktakeBriefResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEBRIEFRESPONSE,
  __module__ = 'third_party.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.GetStocktakeBriefResponse)
  ))
_sym_db.RegisterMessage(GetStocktakeBriefResponse)

StocktakeBrief = _reflection.GeneratedProtocolMessageType('StocktakeBrief', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBRIEF,
  __module__ = 'third_party.stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeBrief)
  ))
_sym_db.RegisterMessage(StocktakeBrief)



_TPSTOCKTAKE = _descriptor.ServiceDescriptor(
  name='TpStocktake',
  full_name='stocktake.TpStocktake',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=469,
  serialized_end=625,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetStocktakeBrief',
    full_name='stocktake.TpStocktake.GetStocktakeBrief',
    index=0,
    containing_service=None,
    input_type=_GETSTOCKTAKEBRIEFREQUEST,
    output_type=_GETSTOCKTAKEBRIEFRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\022$/api/v2/supply/third_party/stocktake'),
  ),
])
_sym_db.RegisterServiceDescriptor(_TPSTOCKTAKE)

DESCRIPTOR.services_by_name['TpStocktake'] = _TPSTOCKTAKE

# @@protoc_insertion_point(module_scope)
