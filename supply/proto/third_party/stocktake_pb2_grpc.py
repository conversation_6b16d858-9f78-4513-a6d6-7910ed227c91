# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from third_party import stocktake_pb2 as third__party_dot_stocktake__pb2


class TpStocktakeStub(object):
  """Stocktake 盘点服务——供三方使用
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetStocktakeBrief = channel.unary_unary(
        '/stocktake.TpStocktake/GetStocktakeBrief',
        request_serializer=third__party_dot_stocktake__pb2.GetStocktakeBriefRequest.SerializeToString,
        response_deserializer=third__party_dot_stocktake__pb2.GetStocktakeBriefResponse.FromString,
        )


class TpStocktakeServicer(object):
  """Stocktake 盘点服务——供三方使用
  """

  def GetStocktakeBrief(self, request, context):
    """GetStocktake 查询盘点单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_TpStocktakeServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetStocktakeBrief': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktakeBrief,
          request_deserializer=third__party_dot_stocktake__pb2.GetStocktakeBriefRequest.FromString,
          response_serializer=third__party_dot_stocktake__pb2.GetStocktakeBriefResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'stocktake.TpStocktake', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
