# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: third_party/orderproductprice.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='third_party/orderproductprice.proto',
  package='',
  syntax='proto3',
  serialized_options=_b('\n\026com.hex.framework.grpc'),
  serialized_pb=_b('\n#third_party/orderproductprice.proto\x1a\x1cgoogle/api/annotations.proto\"\x88\x01\n\x1bGetOrderProductPriceRequest\x12\x0f\n\x07storeId\x18\x01 \x01(\x04\x12\x11\n\tproductId\x18\x02 \x03(\x04\x12\x13\n\x0bproductCode\x18\x03 \x03(\t\x12\x0f\n\x07pageNum\x18\x04 \x01(\t\x12\x10\n\x08pageSize\x18\x05 \x01(\t\x12\r\n\x05isAll\x18\x06 \x01(\x08\"U\n\x1cGetOrderProductPriceResponse\x12&\n\x04rows\x18\x01 \x03(\x0b\x32\x18.SupplyOrderProductPrice\x12\r\n\x05total\x18\x02 \x01(\x04\"\x96\x01\n\x17SupplyOrderProductPrice\x12\x11\n\tstoreCode\x18\x01 \x01(\t\x12\x0f\n\x07storeId\x18\x02 \x01(\x04\x12\x13\n\x0bproductCode\x18\x03 \x01(\t\x12\x11\n\tproductId\x18\x04 \x01(\x04\x12\x10\n\x08unitCode\x18\x05 \x01(\t\x12\x0e\n\x06unitId\x18\x06 \x01(\x04\x12\r\n\x05price\x18\x07 \x01(\x01\x32\xb0\x01\n\x1cQuerySupplyOrderProductPrice\x12\x8f\x01\n\x14GetOrderProductPrice\x12\x1c.GetOrderProductPriceRequest\x1a\x1d.GetOrderProductPriceResponse\":\x82\xd3\xe4\x93\x02\x34\"//api/v2/integration/supply/getOrderProductPrice:\x01*B\x18\n\x16\x63om.hex.framework.grpcb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,])




_GETORDERPRODUCTPRICEREQUEST = _descriptor.Descriptor(
  name='GetOrderProductPriceRequest',
  full_name='GetOrderProductPriceRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='storeId', full_name='GetOrderProductPriceRequest.storeId', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='productId', full_name='GetOrderProductPriceRequest.productId', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='productCode', full_name='GetOrderProductPriceRequest.productCode', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pageNum', full_name='GetOrderProductPriceRequest.pageNum', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pageSize', full_name='GetOrderProductPriceRequest.pageSize', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isAll', full_name='GetOrderProductPriceRequest.isAll', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=70,
  serialized_end=206,
)


_GETORDERPRODUCTPRICERESPONSE = _descriptor.Descriptor(
  name='GetOrderProductPriceResponse',
  full_name='GetOrderProductPriceResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='GetOrderProductPriceResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='GetOrderProductPriceResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=208,
  serialized_end=293,
)


_SUPPLYORDERPRODUCTPRICE = _descriptor.Descriptor(
  name='SupplyOrderProductPrice',
  full_name='SupplyOrderProductPrice',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='storeCode', full_name='SupplyOrderProductPrice.storeCode', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storeId', full_name='SupplyOrderProductPrice.storeId', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='productCode', full_name='SupplyOrderProductPrice.productCode', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='productId', full_name='SupplyOrderProductPrice.productId', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unitCode', full_name='SupplyOrderProductPrice.unitCode', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unitId', full_name='SupplyOrderProductPrice.unitId', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='SupplyOrderProductPrice.price', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=296,
  serialized_end=446,
)

_GETORDERPRODUCTPRICERESPONSE.fields_by_name['rows'].message_type = _SUPPLYORDERPRODUCTPRICE
DESCRIPTOR.message_types_by_name['GetOrderProductPriceRequest'] = _GETORDERPRODUCTPRICEREQUEST
DESCRIPTOR.message_types_by_name['GetOrderProductPriceResponse'] = _GETORDERPRODUCTPRICERESPONSE
DESCRIPTOR.message_types_by_name['SupplyOrderProductPrice'] = _SUPPLYORDERPRODUCTPRICE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetOrderProductPriceRequest = _reflection.GeneratedProtocolMessageType('GetOrderProductPriceRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETORDERPRODUCTPRICEREQUEST,
  __module__ = 'third_party.orderproductprice_pb2'
  # @@protoc_insertion_point(class_scope:GetOrderProductPriceRequest)
  ))
_sym_db.RegisterMessage(GetOrderProductPriceRequest)

GetOrderProductPriceResponse = _reflection.GeneratedProtocolMessageType('GetOrderProductPriceResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETORDERPRODUCTPRICERESPONSE,
  __module__ = 'third_party.orderproductprice_pb2'
  # @@protoc_insertion_point(class_scope:GetOrderProductPriceResponse)
  ))
_sym_db.RegisterMessage(GetOrderProductPriceResponse)

SupplyOrderProductPrice = _reflection.GeneratedProtocolMessageType('SupplyOrderProductPrice', (_message.Message,), dict(
  DESCRIPTOR = _SUPPLYORDERPRODUCTPRICE,
  __module__ = 'third_party.orderproductprice_pb2'
  # @@protoc_insertion_point(class_scope:SupplyOrderProductPrice)
  ))
_sym_db.RegisterMessage(SupplyOrderProductPrice)


DESCRIPTOR._options = None

_QUERYSUPPLYORDERPRODUCTPRICE = _descriptor.ServiceDescriptor(
  name='QuerySupplyOrderProductPrice',
  full_name='QuerySupplyOrderProductPrice',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=449,
  serialized_end=625,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetOrderProductPrice',
    full_name='QuerySupplyOrderProductPrice.GetOrderProductPrice',
    index=0,
    containing_service=None,
    input_type=_GETORDERPRODUCTPRICEREQUEST,
    output_type=_GETORDERPRODUCTPRICERESPONSE,
    serialized_options=_b('\202\323\344\223\0024\"//api/v2/integration/supply/getOrderProductPrice:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_QUERYSUPPLYORDERPRODUCTPRICE)

DESCRIPTOR.services_by_name['QuerySupplyOrderProductPrice'] = _QUERYSUPPLYORDERPRODUCTPRICE

# @@protoc_insertion_point(module_scope)
