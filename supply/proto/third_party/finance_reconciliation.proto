syntax = "proto3";

import "google/api/annotations.proto";
option java_package = "com.hex.framework.grpc";

package finance_reconciliation;


service FinanceReconciliationService{

  rpc getFinanceReconciliation(FinanceReconciliationReq) returns (FinanceReconciliationResponse){
    option (google.api.http)={
      post:"/api/bidata/v1/fin/select"
      body:"*"
    };
  }
}

// 留夫鸭 sap接口入参
message FinanceReconciliationReq{
  // 会计年度
  int32 iGjahr = 1;
  // 会计期间
  int32 iMonat = 2;
  // 客户编码 门店
  string iKunnr = 3;
  // 公司代码 公司
  string iBukrs = 4;
  // 每页条数
  int32 limit = 5;
  // 页数
  int32 offset = 6;
  // 摘要模糊查询字段
  string likeBezei = 7;
  // 排序 正序 0  倒叙 1
  int32 order = 8;
  // 凭证号模糊查询
  string likeBelnr = 9;
}

message FinanceReconciliationResponse{
  // 数据组
  repeated FinanceReconciliationItem rows = 1;
  // 数据条数
  int32 total = 2;
  // 信息
  string msg = 3;
  // 状态码 0：正常 1：异常
  int32 status = 4;
}

message FinanceReconciliationItem{
  // 客户编号
  string kunnr = 1;
  // 凭证中的过账日期
  string budat = 2;
  // 凭证编号
  string belnr = 3;
  // 凭证类型
  string blart = 4;
  // 本期应收
  double dmbtrY = 5;
  // 本期回款
  double dmbtrH = 6;
  // 余额
  double dmbtrE = 7;
  // 额度
  double klimk = 8;
  // 摘要
  string bezei = 9;
  // 名称
  string name1 = 10;
  // 利润中心组
  string cgdes = 11;
}
