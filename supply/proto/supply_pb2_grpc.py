# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import supply_pb2 as supply__pb2


class supplyStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.Ping = channel.unary_unary(
        '/supply.supply/Ping',
        request_serializer=supply__pb2.Null.SerializeToString,
        response_deserializer=supply__pb2.Pong.FromString,
        )
    self.Error = channel.unary_unary(
        '/supply.supply/Error',
        request_serializer=supply__pb2.Null.SerializeToString,
        response_deserializer=supply__pb2.Null.FromString,
        )
    self.TestMethod = channel.unary_unary(
        '/supply.supply/TestMethod',
        request_serializer=supply__pb2.Point.SerializeToString,
        response_deserializer=supply__pb2.Note.FromString,
        )
    self.ActDemandBatchStore = channel.unary_unary(
        '/supply.supply/ActDemandBatchStore',
        request_serializer=supply__pb2.ActDemandBatchRequest.SerializeToString,
        response_deserializer=supply__pb2.DemandBatch.FromString,
        )
    self.CheckAndHandleDemand = channel.unary_unary(
        '/supply.supply/CheckAndHandleDemand',
        request_serializer=supply__pb2.Bizdt.SerializeToString,
        response_deserializer=supply__pb2.Response.FromString,
        )
    self.ListDemand = channel.unary_unary(
        '/supply.supply/ListDemand',
        request_serializer=supply__pb2.QueryDemandRequest.SerializeToString,
        response_deserializer=supply__pb2.QueryDemandResponse.FromString,
        )
    self.GetDemandDetail = channel.unary_unary(
        '/supply.supply/GetDemandDetail',
        request_serializer=supply__pb2.IdRequest.SerializeToString,
        response_deserializer=supply__pb2.DemandEntity.FromString,
        )
    self.GetDemandProductDetail = channel.unary_unary(
        '/supply.supply/GetDemandProductDetail',
        request_serializer=supply__pb2.IdRequest.SerializeToString,
        response_deserializer=supply__pb2.QueryDemandProductResponse.FromString,
        )
    self.UpdateDemandProduct = channel.unary_unary(
        '/supply.supply/UpdateDemandProduct',
        request_serializer=supply__pb2.UpdateDemandProductRequest.SerializeToString,
        response_deserializer=supply__pb2.UpdateDemandProductRequest.FromString,
        )
    self.ChangeDemandStatus = channel.unary_unary(
        '/supply.supply/ChangeDemandStatus',
        request_serializer=supply__pb2.ChangeDemandStatusRequest.SerializeToString,
        response_deserializer=supply__pb2.Response.FromString,
        )
    self.CreateDemandMain = channel.unary_unary(
        '/supply.supply/CreateDemandMain',
        request_serializer=supply__pb2.DemandMainRequest.SerializeToString,
        response_deserializer=supply__pb2.IdRequest.FromString,
        )
    self.CreateProductMain = channel.unary_unary(
        '/supply.supply/CreateProductMain',
        request_serializer=supply__pb2.ProductMainRequest.SerializeToString,
        response_deserializer=supply__pb2.IdRequest.FromString,
        )
    self.ExportDemandMain = channel.unary_unary(
        '/supply.supply/ExportDemandMain',
        request_serializer=supply__pb2.ExportDemandMainRequest.SerializeToString,
        response_deserializer=supply__pb2.ExportDemandMainResponse.FromString,
        )
    self.ResetDemandMain = channel.unary_unary(
        '/supply.supply/ResetDemandMain',
        request_serializer=supply__pb2.IdRequest.SerializeToString,
        response_deserializer=supply__pb2.Response.FromString,
        )
    self.DeleteDemandMain = channel.unary_unary(
        '/supply.supply/DeleteDemandMain',
        request_serializer=supply__pb2.IdRequest.SerializeToString,
        response_deserializer=supply__pb2.Response.FromString,
        )
    self.ListDemandOrder = channel.unary_unary(
        '/supply.supply/ListDemandOrder',
        request_serializer=supply__pb2.ListDemandOrderRequest.SerializeToString,
        response_deserializer=supply__pb2.ListDemandOrderResponse.FromString,
        )
    self.GetDemandOrderDetail = channel.unary_unary(
        '/supply.supply/GetDemandOrderDetail',
        request_serializer=supply__pb2.IdRequest.SerializeToString,
        response_deserializer=supply__pb2.GetDemandOrderDetailResponse.FromString,
        )
    self.GetDemandOrderProductDetail = channel.unary_unary(
        '/supply.supply/GetDemandOrderProductDetail',
        request_serializer=supply__pb2.IdRequest.SerializeToString,
        response_deserializer=supply__pb2.GetDemandOrderProductDetailResponse.FromString,
        )
    self.GetValidProductByStoreId = channel.unary_unary(
        '/supply.supply/GetValidProductByStoreId',
        request_serializer=supply__pb2.GetValidProductByStoreIdRequest.SerializeToString,
        response_deserializer=supply__pb2.QueryDemandProductResponse.FromString,
        )
    self.GetValidStoreByProductId = channel.unary_unary(
        '/supply.supply/GetValidStoreByProductId',
        request_serializer=supply__pb2.GetValidStoreByProductIdRequest.SerializeToString,
        response_deserializer=supply__pb2.GetValidStoreByProductIdResponse.FromString,
        )
    self.UpdateDemandInfo = channel.unary_unary(
        '/supply.supply/UpdateDemandInfo',
        request_serializer=supply__pb2.UpdateDemandInfoRequest.SerializeToString,
        response_deserializer=supply__pb2.Response.FromString,
        )
    self.DeleteDemandProduct = channel.unary_unary(
        '/supply.supply/DeleteDemandProduct',
        request_serializer=supply__pb2.DeleteDemandProductRequest.SerializeToString,
        response_deserializer=supply__pb2.Response.FromString,
        )
    self.CreateVacancyOrder = channel.unary_unary(
        '/supply.supply/CreateVacancyOrder',
        request_serializer=supply__pb2.CreateVacancyOrderRequest.SerializeToString,
        response_deserializer=supply__pb2.IdRequest.FromString,
        )
    self.ListVacancy = channel.unary_unary(
        '/supply.supply/ListVacancy',
        request_serializer=supply__pb2.ListVacancyRequest.SerializeToString,
        response_deserializer=supply__pb2.ListVacancyResponse.FromString,
        )
    self.GetVacancyDetail = channel.unary_unary(
        '/supply.supply/GetVacancyDetail',
        request_serializer=supply__pb2.IdRequest.SerializeToString,
        response_deserializer=supply__pb2.Vacancy.FromString,
        )
    self.GetVacancyProduct = channel.unary_unary(
        '/supply.supply/GetVacancyProduct',
        request_serializer=supply__pb2.IdRequest.SerializeToString,
        response_deserializer=supply__pb2.GetVacancyProductResponse.FromString,
        )
    self.GetVacancyOrderProduct = channel.unary_unary(
        '/supply.supply/GetVacancyOrderProduct',
        request_serializer=supply__pb2.IdRequest.SerializeToString,
        response_deserializer=supply__pb2.GetVacancyOrderProductResponse.FromString,
        )
    self.UpdateVacancyQuantity = channel.unary_unary(
        '/supply.supply/UpdateVacancyQuantity',
        request_serializer=supply__pb2.UpdateVacancyQuantityRequest.SerializeToString,
        response_deserializer=supply__pb2.Response.FromString,
        )
    self.ReVacancyQuantity = channel.unary_unary(
        '/supply.supply/ReVacancyQuantity',
        request_serializer=supply__pb2.IdRequest.SerializeToString,
        response_deserializer=supply__pb2.Response.FromString,
        )
    self.ExecuteVacancy = channel.unary_unary(
        '/supply.supply/ExecuteVacancy',
        request_serializer=supply__pb2.IdRequest.SerializeToString,
        response_deserializer=supply__pb2.Response.FromString,
        )
    self.UpdateVacancyRemark = channel.unary_unary(
        '/supply.supply/UpdateVacancyRemark',
        request_serializer=supply__pb2.UpdateVacancyRemarkRequest.SerializeToString,
        response_deserializer=supply__pb2.Response.FromString,
        )
    self.ExportVacancyProduct = channel.unary_unary(
        '/supply.supply/ExportVacancyProduct',
        request_serializer=supply__pb2.IdRequest.SerializeToString,
        response_deserializer=supply__pb2.Response.FromString,
        )
    self.ListFirstDelivery = channel.unary_unary(
        '/supply.supply/ListFirstDelivery',
        request_serializer=supply__pb2.ListFirstDeliveryRequest.SerializeToString,
        response_deserializer=supply__pb2.ListFirstDeliveryResponse.FromString,
        )
    self.ExportFirstDelivery = channel.unary_unary(
        '/supply.supply/ExportFirstDelivery',
        request_serializer=supply__pb2.IdRequest.SerializeToString,
        response_deserializer=supply__pb2.Response.FromString,
        )
    self.QueryBiOrderReport = channel.unary_unary(
        '/supply.supply/QueryBiOrderReport',
        request_serializer=supply__pb2.QueryOrderBiReportRequest.SerializeToString,
        response_deserializer=supply__pb2.QueryOrderBiReportResponse.FromString,
        )
    self.CancelDemandOrder = channel.unary_unary(
        '/supply.supply/CancelDemandOrder',
        request_serializer=supply__pb2.CancelDemandOrderRequest.SerializeToString,
        response_deserializer=supply__pb2.CancelDemandOrderResponse.FromString,
        )
    self.UploadDemandMaster = channel.unary_unary(
        '/supply.supply/UploadDemandMaster',
        request_serializer=supply__pb2.UploadDemandMasterRequest.SerializeToString,
        response_deserializer=supply__pb2.UploadDemandMasterResponse.FromString,
        )
    self.GetDemandMasterUploadByBatchId = channel.unary_unary(
        '/supply.supply/GetDemandMasterUploadByBatchId',
        request_serializer=supply__pb2.GetDemandMasterUploadByBatchIdRequest.SerializeToString,
        response_deserializer=supply__pb2.GetDemandMasterUploadByBatchIdResponse.FromString,
        )
    self.GetDemandMasterUpload = channel.unary_unary(
        '/supply.supply/GetDemandMasterUpload',
        request_serializer=supply__pb2.GetDemandMasterUploadRequest.SerializeToString,
        response_deserializer=supply__pb2.GetDemandMasterUploadResponse.FromString,
        )
    self.ApproveDemandMasterUpload = channel.unary_unary(
        '/supply.supply/ApproveDemandMasterUpload',
        request_serializer=supply__pb2.ApproveDemandMasterUploadRequest.SerializeToString,
        response_deserializer=supply__pb2.ApproveDemandMasterUploadResponse.FromString,
        )
    self.CancelDemandMasterUpload = channel.unary_unary(
        '/supply.supply/CancelDemandMasterUpload',
        request_serializer=supply__pb2.CancelDemandMasterUploadRequest.SerializeToString,
        response_deserializer=supply__pb2.CancelDemandMasterUploadResponse.FromString,
        )
    self.ListForecastStore = channel.unary_unary(
        '/supply.supply/ListForecastStore',
        request_serializer=supply__pb2.ListForecastStoreRequest.SerializeToString,
        response_deserializer=supply__pb2.ListForecastStoreResponse.FromString,
        )
    self.ListProductSaleForecast = channel.unary_unary(
        '/supply.supply/ListProductSaleForecast',
        request_serializer=supply__pb2.ListProductSaleForecastRequest.SerializeToString,
        response_deserializer=supply__pb2.ListProductSaleForecastResponse.FromString,
        )
    self.ConfirmProductSaleForecast = channel.unary_unary(
        '/supply.supply/ConfirmProductSaleForecast',
        request_serializer=supply__pb2.ConfirmProductSaleForecastRequest.SerializeToString,
        response_deserializer=supply__pb2.ConfirmProductSaleForecastResponse.FromString,
        )
    self.GetProductSaleForecast = channel.unary_unary(
        '/supply.supply/GetProductSaleForecast',
        request_serializer=supply__pb2.GetProductSaleForecastRequest.SerializeToString,
        response_deserializer=supply__pb2.GetProductSaleForecastResponse.FromString,
        )
    self.GetDemandAdjustProductByStoreId = channel.unary_unary(
        '/supply.supply/GetDemandAdjustProductByStoreId',
        request_serializer=supply__pb2.GetDemandAdjustProductByStoreIdRequest.SerializeToString,
        response_deserializer=supply__pb2.GetDemandAdjustProductByStoreIdResponse.FromString,
        )
    self.CreateDemandAdjust = channel.unary_unary(
        '/supply.supply/CreateDemandAdjust',
        request_serializer=supply__pb2.CreateDemandAdjustRequest.SerializeToString,
        response_deserializer=supply__pb2.CreateDemandAdjustResponse.FromString,
        )
    self.GetDistributionByDemand = channel.unary_unary(
        '/supply.supply/GetDistributionByDemand',
        request_serializer=supply__pb2.IdRequest.SerializeToString,
        response_deserializer=supply__pb2.GetDistributionByDemandResponse.FromString,
        )
    self.UploadOrderCollection = channel.unary_unary(
        '/supply.supply/UploadOrderCollection',
        request_serializer=supply__pb2.UploadOrderCollectionRequest.SerializeToString,
        response_deserializer=supply__pb2.UploadOrderCollectionResponse.FromString,
        )
    self.GetOrderCollectionUploadByBatchId = channel.unary_unary(
        '/supply.supply/GetOrderCollectionUploadByBatchId',
        request_serializer=supply__pb2.GetOrderCollectionUploadByBatchIdRequest.SerializeToString,
        response_deserializer=supply__pb2.GetOrderCollectionUploadByBatchIdResponse.FromString,
        )
    self.GetOrderCollectionUpload = channel.unary_unary(
        '/supply.supply/GetOrderCollectionUpload',
        request_serializer=supply__pb2.GetOrderCollectionUploadRequest.SerializeToString,
        response_deserializer=supply__pb2.GetOrderCollectionUploadResponse.FromString,
        )
    self.ApproveOrderCollectionUpload = channel.unary_unary(
        '/supply.supply/ApproveOrderCollectionUpload',
        request_serializer=supply__pb2.ApproveOrderCollectionUploadRequest.SerializeToString,
        response_deserializer=supply__pb2.ApproveOrderCollectionUploadResponse.FromString,
        )
    self.CancelOrderCollectionUpload = channel.unary_unary(
        '/supply.supply/CancelOrderCollectionUpload',
        request_serializer=supply__pb2.CancelOrderCollectionUploadRequest.SerializeToString,
        response_deserializer=supply__pb2.CancelOrderCollectionUploadResponse.FromString,
        )
    self.WmsBohDocSummaryReport = channel.unary_unary(
        '/supply.supply/WmsBohDocSummaryReport',
        request_serializer=supply__pb2.WmsBohDocSummaryReportRequest.SerializeToString,
        response_deserializer=supply__pb2.WmsBohDocSummaryReportResponse.FromString,
        )
    self.WmsBohDocDetailReport = channel.unary_unary(
        '/supply.supply/WmsBohDocDetailReport',
        request_serializer=supply__pb2.WmsBohDocDetailReportRequest.SerializeToString,
        response_deserializer=supply__pb2.WmsBohDocDetailReportResponse.FromString,
        )
    self.CreateSmartDemandRule = channel.unary_unary(
        '/supply.supply/CreateSmartDemandRule',
        request_serializer=supply__pb2.CreateSmartDemandRuleRequest.SerializeToString,
        response_deserializer=supply__pb2.CreateSmartDemandRuleResponse.FromString,
        )
    self.PutSmartDemandRule = channel.unary_unary(
        '/supply.supply/PutSmartDemandRule',
        request_serializer=supply__pb2.PutSmartDemandRuleRequest.SerializeToString,
        response_deserializer=supply__pb2.PutSmartDemandRuleResponse.FromString,
        )
    self.ActionSmartDemandRule = channel.unary_unary(
        '/supply.supply/ActionSmartDemandRule',
        request_serializer=supply__pb2.ActionSmartDemandRuleRequest.SerializeToString,
        response_deserializer=supply__pb2.ActionSmartDemandRuleResponse.FromString,
        )
    self.GetSmartDemandRule = channel.unary_unary(
        '/supply.supply/GetSmartDemandRule',
        request_serializer=supply__pb2.GetSmartDemandRuleRequest.SerializeToString,
        response_deserializer=supply__pb2.GetSmartDemandRuleResponse.FromString,
        )
    self.QueryReturnOrders = channel.unary_unary(
        '/supply.supply/QueryReturnOrders',
        request_serializer=supply__pb2.QueryReturnOrdersRequest.SerializeToString,
        response_deserializer=supply__pb2.QueryReturnOrdersResponse.FromString,
        )
    self.DemoGetStoreProduct = channel.unary_unary(
        '/supply.supply/DemoGetStoreProduct',
        request_serializer=supply__pb2.DemoGetStoreProductRequest.SerializeToString,
        response_deserializer=supply__pb2.DemoGetStoreProductResponse.FromString,
        )
    self.DemoSplitStoreProduct = channel.unary_unary(
        '/supply.supply/DemoSplitStoreProduct',
        request_serializer=supply__pb2.DemoSplitStoreProductRequest.SerializeToString,
        response_deserializer=supply__pb2.DemoSplitStoreProductResponse.FromString,
        )
    self.DemoSaveStoreProduct = channel.unary_unary(
        '/supply.supply/DemoSaveStoreProduct',
        request_serializer=supply__pb2.DemoSaveStoreProductRequest.SerializeToString,
        response_deserializer=supply__pb2.DemoSaveStoreProductResponse.FromString,
        )
    self.CreateDemandByPreOrder = channel.unary_unary(
        '/supply.supply/CreateDemandByPreOrder',
        request_serializer=supply__pb2.CreateDemandByPreOrderRequest.SerializeToString,
        response_deserializer=supply__pb2.IdRequest.FromString,
        )


class supplyServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def Ping(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def Error(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def TestMethod(self, request, context):
    """DEMOMETHOD
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ActDemandBatchStore(self, request, context):
    """夜间生成门市订货单批次
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CheckAndHandleDemand(self, request, context):
    """夜间检查未发送订货单，生成门市要货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListDemand(self, request, context):
    """查询门店订货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDemandDetail(self, request, context):
    """获取门市订货单详细
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDemandProductDetail(self, request, context):
    """获取订货商品信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateDemandProduct(self, request, context):
    """门店订货
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ChangeDemandStatus(self, request, context):
    """修改订单状态统一入口
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateDemandMain(self, request, context):
    """创建门店紧急订货订单和门店主配单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateProductMain(self, request, context):
    """创建商品主配单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ExportDemandMain(self, request, context):
    """导出订单商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ResetDemandMain(self, request, context):
    """重置门店订单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteDemandMain(self, request, context):
    """删除订货单(门市订货单不能删除)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListDemandOrder(self, request, context):
    """查询要货订单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDemandOrderDetail(self, request, context):
    """获取要货单详细
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDemandOrderProductDetail(self, request, context):
    """获取要货单商品详细
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetValidProductByStoreId(self, request, context):
    """根据门店id查询可订货商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetValidStoreByProductId(self, request, context):
    """根据商品id获取可订货门店(此接口会很慢)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateDemandInfo(self, request, context):
    """修改订货单信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteDemandProduct(self, request, context):
    """删除紧急订货单或者主配单订货商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateVacancyOrder(self, request, context):
    """创建缺货检核单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListVacancy(self, request, context):
    """查询缺货检核单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetVacancyDetail(self, request, context):
    """查看缺货检核单详细信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetVacancyProduct(self, request, context):
    """查看缺货检核单缺货商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetVacancyOrderProduct(self, request, context):
    """查看要货单缺货商品记录
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateVacancyQuantity(self, request, context):
    """保存缺货检核配送数量
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ReVacancyQuantity(self, request, context):
    """重新检核
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ExecuteVacancy(self, request, context):
    """执行发送缺货检核成功的单子
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateVacancyRemark(self, request, context):
    """修改缺货检核单备注
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ExportVacancyProduct(self, request, context):
    """导出缺货信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListFirstDelivery(self, request, context):
    """查询首配信息
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ExportFirstDelivery(self, request, context):
    """导出首配报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryBiOrderReport(self, request, context):
    """要货单报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CancelDemandOrder(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UploadDemandMaster(self, request, context):
    """导入主配单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDemandMasterUploadByBatchId(self, request, context):
    """查询导入明细
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDemandMasterUpload(self, request, context):
    """查询导入记录
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ApproveDemandMasterUpload(self, request, context):
    """审核主配导入记录开始干活
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CancelDemandMasterUpload(self, request, context):
    """取消主配导入
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListForecastStore(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListProductSaleForecast(self, request, context):
    """查询面包和茶饮的一周内的预测销售量
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ConfirmProductSaleForecast(self, request, context):
    """确认面包和茶饮的一周内的预测销售量
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProductSaleForecast(self, request, context):
    """建议订货量查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDemandAdjustProductByStoreId(self, request, context):
    """根据门店id查询可调整订货商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateDemandAdjust(self, request, context):
    """创建订货调整单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetDistributionByDemand(self, request, context):
    """根据订单id获取订货单商品对应的仓库
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UploadOrderCollection(self, request, context):
    """调整单导入
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetOrderCollectionUploadByBatchId(self, request, context):
    """查询调整单导入明细
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetOrderCollectionUpload(self, request, context):
    """查询调整单导入记录
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ApproveOrderCollectionUpload(self, request, context):
    """审核调整单导入记录开始干活
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CancelOrderCollectionUpload(self, request, context):
    """取消调整单导入导入
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def WmsBohDocSummaryReport(self, request, context):
    """Wms-boh单据核对表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def WmsBohDocDetailReport(self, request, context):
    """Wms-boh单据核对表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateSmartDemandRule(self, request, context):
    """智能订货配置 smart-demand
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def PutSmartDemandRule(self, request, context):
    """更新智能订货配置 smart-demand
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ActionSmartDemandRule(self, request, context):
    """状态智能订货配置 smart-demand
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetSmartDemandRule(self, request, context):
    """获取智能订货配置列表加明细
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryReturnOrders(self, request, context):
    """退货单报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DemoGetStoreProduct(self, request, context):
    """熊猫不走demo环境演示api
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DemoSplitStoreProduct(self, request, context):
    """熊猫不走demo环境提交订货成品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DemoSaveStoreProduct(self, request, context):
    """熊猫不走demo环境保存订货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateDemandByPreOrder(self, request, context):
    """创建预订单转订货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_supplyServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'Ping': grpc.unary_unary_rpc_method_handler(
          servicer.Ping,
          request_deserializer=supply__pb2.Null.FromString,
          response_serializer=supply__pb2.Pong.SerializeToString,
      ),
      'Error': grpc.unary_unary_rpc_method_handler(
          servicer.Error,
          request_deserializer=supply__pb2.Null.FromString,
          response_serializer=supply__pb2.Null.SerializeToString,
      ),
      'TestMethod': grpc.unary_unary_rpc_method_handler(
          servicer.TestMethod,
          request_deserializer=supply__pb2.Point.FromString,
          response_serializer=supply__pb2.Note.SerializeToString,
      ),
      'ActDemandBatchStore': grpc.unary_unary_rpc_method_handler(
          servicer.ActDemandBatchStore,
          request_deserializer=supply__pb2.ActDemandBatchRequest.FromString,
          response_serializer=supply__pb2.DemandBatch.SerializeToString,
      ),
      'CheckAndHandleDemand': grpc.unary_unary_rpc_method_handler(
          servicer.CheckAndHandleDemand,
          request_deserializer=supply__pb2.Bizdt.FromString,
          response_serializer=supply__pb2.Response.SerializeToString,
      ),
      'ListDemand': grpc.unary_unary_rpc_method_handler(
          servicer.ListDemand,
          request_deserializer=supply__pb2.QueryDemandRequest.FromString,
          response_serializer=supply__pb2.QueryDemandResponse.SerializeToString,
      ),
      'GetDemandDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandDetail,
          request_deserializer=supply__pb2.IdRequest.FromString,
          response_serializer=supply__pb2.DemandEntity.SerializeToString,
      ),
      'GetDemandProductDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandProductDetail,
          request_deserializer=supply__pb2.IdRequest.FromString,
          response_serializer=supply__pb2.QueryDemandProductResponse.SerializeToString,
      ),
      'UpdateDemandProduct': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateDemandProduct,
          request_deserializer=supply__pb2.UpdateDemandProductRequest.FromString,
          response_serializer=supply__pb2.UpdateDemandProductRequest.SerializeToString,
      ),
      'ChangeDemandStatus': grpc.unary_unary_rpc_method_handler(
          servicer.ChangeDemandStatus,
          request_deserializer=supply__pb2.ChangeDemandStatusRequest.FromString,
          response_serializer=supply__pb2.Response.SerializeToString,
      ),
      'CreateDemandMain': grpc.unary_unary_rpc_method_handler(
          servicer.CreateDemandMain,
          request_deserializer=supply__pb2.DemandMainRequest.FromString,
          response_serializer=supply__pb2.IdRequest.SerializeToString,
      ),
      'CreateProductMain': grpc.unary_unary_rpc_method_handler(
          servicer.CreateProductMain,
          request_deserializer=supply__pb2.ProductMainRequest.FromString,
          response_serializer=supply__pb2.IdRequest.SerializeToString,
      ),
      'ExportDemandMain': grpc.unary_unary_rpc_method_handler(
          servicer.ExportDemandMain,
          request_deserializer=supply__pb2.ExportDemandMainRequest.FromString,
          response_serializer=supply__pb2.ExportDemandMainResponse.SerializeToString,
      ),
      'ResetDemandMain': grpc.unary_unary_rpc_method_handler(
          servicer.ResetDemandMain,
          request_deserializer=supply__pb2.IdRequest.FromString,
          response_serializer=supply__pb2.Response.SerializeToString,
      ),
      'DeleteDemandMain': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteDemandMain,
          request_deserializer=supply__pb2.IdRequest.FromString,
          response_serializer=supply__pb2.Response.SerializeToString,
      ),
      'ListDemandOrder': grpc.unary_unary_rpc_method_handler(
          servicer.ListDemandOrder,
          request_deserializer=supply__pb2.ListDemandOrderRequest.FromString,
          response_serializer=supply__pb2.ListDemandOrderResponse.SerializeToString,
      ),
      'GetDemandOrderDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandOrderDetail,
          request_deserializer=supply__pb2.IdRequest.FromString,
          response_serializer=supply__pb2.GetDemandOrderDetailResponse.SerializeToString,
      ),
      'GetDemandOrderProductDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandOrderProductDetail,
          request_deserializer=supply__pb2.IdRequest.FromString,
          response_serializer=supply__pb2.GetDemandOrderProductDetailResponse.SerializeToString,
      ),
      'GetValidProductByStoreId': grpc.unary_unary_rpc_method_handler(
          servicer.GetValidProductByStoreId,
          request_deserializer=supply__pb2.GetValidProductByStoreIdRequest.FromString,
          response_serializer=supply__pb2.QueryDemandProductResponse.SerializeToString,
      ),
      'GetValidStoreByProductId': grpc.unary_unary_rpc_method_handler(
          servicer.GetValidStoreByProductId,
          request_deserializer=supply__pb2.GetValidStoreByProductIdRequest.FromString,
          response_serializer=supply__pb2.GetValidStoreByProductIdResponse.SerializeToString,
      ),
      'UpdateDemandInfo': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateDemandInfo,
          request_deserializer=supply__pb2.UpdateDemandInfoRequest.FromString,
          response_serializer=supply__pb2.Response.SerializeToString,
      ),
      'DeleteDemandProduct': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteDemandProduct,
          request_deserializer=supply__pb2.DeleteDemandProductRequest.FromString,
          response_serializer=supply__pb2.Response.SerializeToString,
      ),
      'CreateVacancyOrder': grpc.unary_unary_rpc_method_handler(
          servicer.CreateVacancyOrder,
          request_deserializer=supply__pb2.CreateVacancyOrderRequest.FromString,
          response_serializer=supply__pb2.IdRequest.SerializeToString,
      ),
      'ListVacancy': grpc.unary_unary_rpc_method_handler(
          servicer.ListVacancy,
          request_deserializer=supply__pb2.ListVacancyRequest.FromString,
          response_serializer=supply__pb2.ListVacancyResponse.SerializeToString,
      ),
      'GetVacancyDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetVacancyDetail,
          request_deserializer=supply__pb2.IdRequest.FromString,
          response_serializer=supply__pb2.Vacancy.SerializeToString,
      ),
      'GetVacancyProduct': grpc.unary_unary_rpc_method_handler(
          servicer.GetVacancyProduct,
          request_deserializer=supply__pb2.IdRequest.FromString,
          response_serializer=supply__pb2.GetVacancyProductResponse.SerializeToString,
      ),
      'GetVacancyOrderProduct': grpc.unary_unary_rpc_method_handler(
          servicer.GetVacancyOrderProduct,
          request_deserializer=supply__pb2.IdRequest.FromString,
          response_serializer=supply__pb2.GetVacancyOrderProductResponse.SerializeToString,
      ),
      'UpdateVacancyQuantity': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateVacancyQuantity,
          request_deserializer=supply__pb2.UpdateVacancyQuantityRequest.FromString,
          response_serializer=supply__pb2.Response.SerializeToString,
      ),
      'ReVacancyQuantity': grpc.unary_unary_rpc_method_handler(
          servicer.ReVacancyQuantity,
          request_deserializer=supply__pb2.IdRequest.FromString,
          response_serializer=supply__pb2.Response.SerializeToString,
      ),
      'ExecuteVacancy': grpc.unary_unary_rpc_method_handler(
          servicer.ExecuteVacancy,
          request_deserializer=supply__pb2.IdRequest.FromString,
          response_serializer=supply__pb2.Response.SerializeToString,
      ),
      'UpdateVacancyRemark': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateVacancyRemark,
          request_deserializer=supply__pb2.UpdateVacancyRemarkRequest.FromString,
          response_serializer=supply__pb2.Response.SerializeToString,
      ),
      'ExportVacancyProduct': grpc.unary_unary_rpc_method_handler(
          servicer.ExportVacancyProduct,
          request_deserializer=supply__pb2.IdRequest.FromString,
          response_serializer=supply__pb2.Response.SerializeToString,
      ),
      'ListFirstDelivery': grpc.unary_unary_rpc_method_handler(
          servicer.ListFirstDelivery,
          request_deserializer=supply__pb2.ListFirstDeliveryRequest.FromString,
          response_serializer=supply__pb2.ListFirstDeliveryResponse.SerializeToString,
      ),
      'ExportFirstDelivery': grpc.unary_unary_rpc_method_handler(
          servicer.ExportFirstDelivery,
          request_deserializer=supply__pb2.IdRequest.FromString,
          response_serializer=supply__pb2.Response.SerializeToString,
      ),
      'QueryBiOrderReport': grpc.unary_unary_rpc_method_handler(
          servicer.QueryBiOrderReport,
          request_deserializer=supply__pb2.QueryOrderBiReportRequest.FromString,
          response_serializer=supply__pb2.QueryOrderBiReportResponse.SerializeToString,
      ),
      'CancelDemandOrder': grpc.unary_unary_rpc_method_handler(
          servicer.CancelDemandOrder,
          request_deserializer=supply__pb2.CancelDemandOrderRequest.FromString,
          response_serializer=supply__pb2.CancelDemandOrderResponse.SerializeToString,
      ),
      'UploadDemandMaster': grpc.unary_unary_rpc_method_handler(
          servicer.UploadDemandMaster,
          request_deserializer=supply__pb2.UploadDemandMasterRequest.FromString,
          response_serializer=supply__pb2.UploadDemandMasterResponse.SerializeToString,
      ),
      'GetDemandMasterUploadByBatchId': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandMasterUploadByBatchId,
          request_deserializer=supply__pb2.GetDemandMasterUploadByBatchIdRequest.FromString,
          response_serializer=supply__pb2.GetDemandMasterUploadByBatchIdResponse.SerializeToString,
      ),
      'GetDemandMasterUpload': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandMasterUpload,
          request_deserializer=supply__pb2.GetDemandMasterUploadRequest.FromString,
          response_serializer=supply__pb2.GetDemandMasterUploadResponse.SerializeToString,
      ),
      'ApproveDemandMasterUpload': grpc.unary_unary_rpc_method_handler(
          servicer.ApproveDemandMasterUpload,
          request_deserializer=supply__pb2.ApproveDemandMasterUploadRequest.FromString,
          response_serializer=supply__pb2.ApproveDemandMasterUploadResponse.SerializeToString,
      ),
      'CancelDemandMasterUpload': grpc.unary_unary_rpc_method_handler(
          servicer.CancelDemandMasterUpload,
          request_deserializer=supply__pb2.CancelDemandMasterUploadRequest.FromString,
          response_serializer=supply__pb2.CancelDemandMasterUploadResponse.SerializeToString,
      ),
      'ListForecastStore': grpc.unary_unary_rpc_method_handler(
          servicer.ListForecastStore,
          request_deserializer=supply__pb2.ListForecastStoreRequest.FromString,
          response_serializer=supply__pb2.ListForecastStoreResponse.SerializeToString,
      ),
      'ListProductSaleForecast': grpc.unary_unary_rpc_method_handler(
          servicer.ListProductSaleForecast,
          request_deserializer=supply__pb2.ListProductSaleForecastRequest.FromString,
          response_serializer=supply__pb2.ListProductSaleForecastResponse.SerializeToString,
      ),
      'ConfirmProductSaleForecast': grpc.unary_unary_rpc_method_handler(
          servicer.ConfirmProductSaleForecast,
          request_deserializer=supply__pb2.ConfirmProductSaleForecastRequest.FromString,
          response_serializer=supply__pb2.ConfirmProductSaleForecastResponse.SerializeToString,
      ),
      'GetProductSaleForecast': grpc.unary_unary_rpc_method_handler(
          servicer.GetProductSaleForecast,
          request_deserializer=supply__pb2.GetProductSaleForecastRequest.FromString,
          response_serializer=supply__pb2.GetProductSaleForecastResponse.SerializeToString,
      ),
      'GetDemandAdjustProductByStoreId': grpc.unary_unary_rpc_method_handler(
          servicer.GetDemandAdjustProductByStoreId,
          request_deserializer=supply__pb2.GetDemandAdjustProductByStoreIdRequest.FromString,
          response_serializer=supply__pb2.GetDemandAdjustProductByStoreIdResponse.SerializeToString,
      ),
      'CreateDemandAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.CreateDemandAdjust,
          request_deserializer=supply__pb2.CreateDemandAdjustRequest.FromString,
          response_serializer=supply__pb2.CreateDemandAdjustResponse.SerializeToString,
      ),
      'GetDistributionByDemand': grpc.unary_unary_rpc_method_handler(
          servicer.GetDistributionByDemand,
          request_deserializer=supply__pb2.IdRequest.FromString,
          response_serializer=supply__pb2.GetDistributionByDemandResponse.SerializeToString,
      ),
      'UploadOrderCollection': grpc.unary_unary_rpc_method_handler(
          servicer.UploadOrderCollection,
          request_deserializer=supply__pb2.UploadOrderCollectionRequest.FromString,
          response_serializer=supply__pb2.UploadOrderCollectionResponse.SerializeToString,
      ),
      'GetOrderCollectionUploadByBatchId': grpc.unary_unary_rpc_method_handler(
          servicer.GetOrderCollectionUploadByBatchId,
          request_deserializer=supply__pb2.GetOrderCollectionUploadByBatchIdRequest.FromString,
          response_serializer=supply__pb2.GetOrderCollectionUploadByBatchIdResponse.SerializeToString,
      ),
      'GetOrderCollectionUpload': grpc.unary_unary_rpc_method_handler(
          servicer.GetOrderCollectionUpload,
          request_deserializer=supply__pb2.GetOrderCollectionUploadRequest.FromString,
          response_serializer=supply__pb2.GetOrderCollectionUploadResponse.SerializeToString,
      ),
      'ApproveOrderCollectionUpload': grpc.unary_unary_rpc_method_handler(
          servicer.ApproveOrderCollectionUpload,
          request_deserializer=supply__pb2.ApproveOrderCollectionUploadRequest.FromString,
          response_serializer=supply__pb2.ApproveOrderCollectionUploadResponse.SerializeToString,
      ),
      'CancelOrderCollectionUpload': grpc.unary_unary_rpc_method_handler(
          servicer.CancelOrderCollectionUpload,
          request_deserializer=supply__pb2.CancelOrderCollectionUploadRequest.FromString,
          response_serializer=supply__pb2.CancelOrderCollectionUploadResponse.SerializeToString,
      ),
      'WmsBohDocSummaryReport': grpc.unary_unary_rpc_method_handler(
          servicer.WmsBohDocSummaryReport,
          request_deserializer=supply__pb2.WmsBohDocSummaryReportRequest.FromString,
          response_serializer=supply__pb2.WmsBohDocSummaryReportResponse.SerializeToString,
      ),
      'WmsBohDocDetailReport': grpc.unary_unary_rpc_method_handler(
          servicer.WmsBohDocDetailReport,
          request_deserializer=supply__pb2.WmsBohDocDetailReportRequest.FromString,
          response_serializer=supply__pb2.WmsBohDocDetailReportResponse.SerializeToString,
      ),
      'CreateSmartDemandRule': grpc.unary_unary_rpc_method_handler(
          servicer.CreateSmartDemandRule,
          request_deserializer=supply__pb2.CreateSmartDemandRuleRequest.FromString,
          response_serializer=supply__pb2.CreateSmartDemandRuleResponse.SerializeToString,
      ),
      'PutSmartDemandRule': grpc.unary_unary_rpc_method_handler(
          servicer.PutSmartDemandRule,
          request_deserializer=supply__pb2.PutSmartDemandRuleRequest.FromString,
          response_serializer=supply__pb2.PutSmartDemandRuleResponse.SerializeToString,
      ),
      'ActionSmartDemandRule': grpc.unary_unary_rpc_method_handler(
          servicer.ActionSmartDemandRule,
          request_deserializer=supply__pb2.ActionSmartDemandRuleRequest.FromString,
          response_serializer=supply__pb2.ActionSmartDemandRuleResponse.SerializeToString,
      ),
      'GetSmartDemandRule': grpc.unary_unary_rpc_method_handler(
          servicer.GetSmartDemandRule,
          request_deserializer=supply__pb2.GetSmartDemandRuleRequest.FromString,
          response_serializer=supply__pb2.GetSmartDemandRuleResponse.SerializeToString,
      ),
      'QueryReturnOrders': grpc.unary_unary_rpc_method_handler(
          servicer.QueryReturnOrders,
          request_deserializer=supply__pb2.QueryReturnOrdersRequest.FromString,
          response_serializer=supply__pb2.QueryReturnOrdersResponse.SerializeToString,
      ),
      'DemoGetStoreProduct': grpc.unary_unary_rpc_method_handler(
          servicer.DemoGetStoreProduct,
          request_deserializer=supply__pb2.DemoGetStoreProductRequest.FromString,
          response_serializer=supply__pb2.DemoGetStoreProductResponse.SerializeToString,
      ),
      'DemoSplitStoreProduct': grpc.unary_unary_rpc_method_handler(
          servicer.DemoSplitStoreProduct,
          request_deserializer=supply__pb2.DemoSplitStoreProductRequest.FromString,
          response_serializer=supply__pb2.DemoSplitStoreProductResponse.SerializeToString,
      ),
      'DemoSaveStoreProduct': grpc.unary_unary_rpc_method_handler(
          servicer.DemoSaveStoreProduct,
          request_deserializer=supply__pb2.DemoSaveStoreProductRequest.FromString,
          response_serializer=supply__pb2.DemoSaveStoreProductResponse.SerializeToString,
      ),
      'CreateDemandByPreOrder': grpc.unary_unary_rpc_method_handler(
          servicer.CreateDemandByPreOrder,
          request_deserializer=supply__pb2.CreateDemandByPreOrderRequest.FromString,
          response_serializer=supply__pb2.IdRequest.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'supply.supply', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
