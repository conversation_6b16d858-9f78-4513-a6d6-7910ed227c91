{"swagger": "2.0", "info": {"title": "irons/irons.proto", "version": "version not set"}, "tags": [{"name": "UploadService"}, {"name": "DownloadService"}, {"name": "Common"}, {"name": "SqlQuery"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/common/download": {"post": {"summary": "提交下载任务(前端)", "operationId": "DownloadService_Download", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ironsDownloadTask"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ironsDownloadRequest"}}], "tags": ["DownloadService"]}}, "/api/v1/common/download/{task_id}": {"get": {"summary": "获取文件下载地址", "operationId": "Common_GetDownloadURL", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ironsDownloadURL"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "task_id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["Common"]}}, "/api/v1/common/gethelpers/{parse_method}": {"get": {"summary": "由参数获取解析函数及其描述(参数为)", "operationId": "Common_GetAllParseHelper", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ironsAllParseHelper"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "parse_method", "description": "上传？or下载？", "in": "path", "required": true, "type": "string"}], "tags": ["Common"]}}, "/api/v1/common/status/{task_id}": {"get": {"summary": "前端轮询任务状态", "operationId": "Common_GetTaskStatus", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ironsRespTaskStatus"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "task_id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["Common"]}}, "/api/v1/common/upload": {"post": {"summary": "Upload 上传任务", "operationId": "UploadService_Upload", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ironsUploadResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "description": " (streaming inputs)", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ironsUploadRequest"}}], "tags": ["UploadService"]}}, "/api/v1/common/upload/commit/{task_id}": {"get": {"summary": "确认提交", "operationId": "Common_ConfirmCommit", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ironsCommitStatus"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "task_id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["Common"]}}, "/api/v1/common/upload/gettask": {"post": {"summary": "GetCurrPageTask 获取当前页所有对应任务", "operationId": "Common_GetCurrPageTask", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ironsTaskIntros"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ironsGetInfo"}}], "tags": ["Common"]}}, "/api/v1/common/upload/gettask/temp": {"post": {"summary": "TODO GetCurrPageTask 获取当前页所有对应任务(临时方案)", "operationId": "Common_GetCurrPageTaskTemp", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ironsTaskListTemp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ironsGetInfo"}}], "tags": ["Common"]}}, "/api/v1/common/verify/{task_id}": {"get": {"summary": "TODO 此接口考虑存在必要", "operationId": "Common_GetVerifyData", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ironsVerifyData"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "task_id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["Common"]}}, "/api/v1/common/{task_id}": {"get": {"summary": "获取Task详情", "operationId": "Common_GetTaskInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ironsTaskInfo"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "task_id", "in": "path", "required": true, "type": "string", "format": "int64"}], "tags": ["Common"]}}, "/api/v1/query/querysql": {"post": {"operationId": "SqlQuery_SqlExport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/ironsSqlDownloadTask"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/ironsSqlPlan"}}], "tags": ["SqlQuery"]}}}, "definitions": {"ironsAllParseHelper": {"type": "object", "properties": {"total": {"type": "string", "format": "int64"}, "helper": {"type": "array", "items": {"$ref": "#/definitions/ironsParseHelperStruct"}}}}, "ironsCommitStatus": {"type": "object", "properties": {"status": {"type": "string", "format": "int64"}, "description": {"type": "string"}}}, "ironsDownloadRequest": {"type": "object", "properties": {"scope": {"$ref": "#/definitions/ironsScope"}, "template": {"type": "string"}, "name": {"type": "string", "title": "导出的文件名"}, "downloadhelper": {"type": "string", "title": "上传文件的解析处理函数"}, "query": {"type": "string", "title": "导出字段及其限制"}}}, "ironsDownloadTask": {"type": "object", "properties": {"task_id": {"type": "string", "format": "int64"}, "url": {"type": "string"}, "completed_at": {"type": "string", "format": "date-time"}}}, "ironsDownloadURL": {"type": "object", "properties": {"url": {"type": "string"}}}, "ironsError": {"type": "object", "properties": {"err": {"type": "string"}}}, "ironsGetInfo": {"type": "object", "properties": {"page": {"type": "string", "format": "int64", "title": "page 从1开始,两个都为0时获取所有数据"}, "size": {"type": "string", "format": "int64"}, "method": {"type": "string", "title": "上传or下载(为空全搜索)"}}}, "ironsParseHelperStruct": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}}}, "ironsRespTaskStatus": {"type": "object", "properties": {"status": {"$ref": "#/definitions/ironsUiTaskStatus"}}}, "ironsScope": {"type": "object", "properties": {"partner_id": {"type": "string", "format": "int64", "title": "上传者pid"}, "domain": {"type": "string", "title": "域"}, "service_topic": {"type": "string", "title": "上传处理的目标服务"}, "service_channel": {"type": "string"}}}, "ironsSqlDownloadTask": {"type": "object", "properties": {"task_id": {"type": "string", "format": "int64"}, "completed_at": {"type": "string", "format": "date-time"}}}, "ironsSqlPlan": {"type": "object", "properties": {"query_sql": {"type": "string"}, "template_url": {"type": "string"}, "export_file_name": {"type": "string"}}}, "ironsTaskInfo": {"type": "object", "properties": {"name": {"type": "string"}, "upload_by": {"type": "string"}, "file_size": {"type": "string", "title": "文件大小"}, "create_at": {"type": "string", "format": "date-time"}, "complate_at": {"type": "string", "format": "date-time"}, "status_sign": {"type": "string", "title": "文件状态"}, "query": {"type": "string", "title": "文件导出时的过滤规则"}, "url": {"type": "string", "title": "文件到处时下载的url"}}}, "ironsTaskIntro": {"type": "object", "properties": {"status": {"$ref": "#/definitions/ironsUiTaskStatus", "title": "简介状态"}, "task_name": {"type": "string"}, "task_id": {"type": "string", "format": "int64", "title": "和后端交互用"}, "upload_by": {"type": "string"}, "create_at": {"type": "string", "format": "date-time"}, "complate_at": {"type": "string", "format": "date-time"}}}, "ironsTaskIntros": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/ironsTaskIntro"}}}}, "ironsTaskItemCell": {"type": "object", "properties": {"value": {"type": "string", "title": "列中元素"}, "verify": {"$ref": "#/definitions/ironsVerifyMsg"}}, "title": "TaskItemCell 上传的数据单元项"}, "ironsTaskItemRow": {"type": "object", "properties": {"row": {"type": "string", "format": "int64", "title": "该行的所在的行序 -》第几行"}, "items": {"type": "array", "items": {"$ref": "#/definitions/ironsTaskItemCell"}, "title": "改行所有列数据"}}}, "ironsTaskItemsTemp": {"type": "object", "properties": {"status": {"$ref": "#/definitions/ironsUiTaskStatus", "title": "简介状态"}, "task_com_url": {"type": "string"}, "task_name": {"type": "string"}, "task_id": {"type": "string", "format": "int64", "title": "和后端交互用"}, "upload_by": {"type": "string"}, "create_at": {"type": "string", "format": "date-time"}, "complate_at": {"type": "string", "format": "date-time"}}}, "ironsTaskListTemp": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/ironsTaskItemsTemp"}}}}, "ironsUiTaskStatus": {"type": "string", "enum": ["Succ", "Fail", "Processing", "Pending"], "default": "Succ", "title": "- Succ: 成功\n - Fail: 失败\n - Processing: 处理中\n - Pending: 待提交"}, "ironsUploadRequest": {"type": "object", "properties": {"scope": {"$ref": "#/definitions/ironsScope", "title": "关于上传"}, "type": {"$ref": "#/definitions/ironsUploadType", "title": "上传种类"}, "name": {"type": "string", "title": "导出文件名同请求名"}, "uploadhelper": {"type": "string", "title": "上传文件的解析处理函数"}, "data": {"type": "string", "format": "byte", "title": "流式传输"}}, "title": "UploadRequest 上传数据请求"}, "ironsUploadResponse": {"type": "object", "properties": {"task_id": {"type": "string", "format": "int64"}, "created": {"type": "string", "format": "date-time"}}}, "ironsUploadTaskItemM": {"type": "object", "properties": {"total_row": {"type": "string", "format": "int64"}, "rows": {"type": "array", "items": {"$ref": "#/definitions/ironsTaskItemRow"}}, "isContinue": {"type": "boolean"}}}, "ironsUploadType": {"type": "string", "enum": ["XLSX", "CSV", "JSON"], "default": "XLSX"}, "ironsVerifyData": {"type": "object", "properties": {"status": {"type": "string", "format": "int64", "title": "0 初始化  1"}}}, "ironsVerifyMsg": {"type": "object", "properties": {"index": {"type": "string", "format": "int64", "title": "第几列"}, "status": {"type": "boolean", "title": "验证后正确状态"}, "verify_msg": {"type": "string", "title": "验证信息"}}}, "ironsVerifyRows": {"type": "object", "properties": {"row": {"type": "string", "format": "int64", "title": "哪一行"}, "info": {"type": "array", "items": {"$ref": "#/definitions/ironsVerifyMsg"}, "title": "这一整行的信息"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}}}