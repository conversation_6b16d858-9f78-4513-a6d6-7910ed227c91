# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: irons/irons.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='irons/irons.proto',
  package='irons',
  syntax='proto3',
  serialized_options=_b('Z\tapi2/api2'),
  serialized_pb=_b('\n\x11irons/irons.proto\x12\x05irons\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x7f\n\rUploadRequest\x12\x1b\n\x05scope\x18\x01 \x01(\x0b\x32\x0c.irons.Scope\x12\x1f\n\x04type\x18\x02 \x01(\x0e\x32\x11.irons.UploadType\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x14\n\x0cuploadhelper\x18\x04 \x01(\t\x12\x0c\n\x04\x64\x61ta\x18\x05 \x01(\x0c\"[\n\x05Scope\x12\x12\n\npartner_id\x18\x01 \x01(\x03\x12\x0e\n\x06\x64omain\x18\x02 \x01(\t\x12\x15\n\rservice_topic\x18\x03 \x01(\t\x12\x17\n\x0fservice_channel\x18\x04 \x01(\t\"\xc7\x01\n\tTaskIntro\x12#\n\x06status\x18\x01 \x01(\x0e\x32\x13.irons.UiTaskStatus\x12\x11\n\ttask_name\x18\x02 \x01(\t\x12\x0f\n\x07task_id\x18\x03 \x01(\x03\x12\x11\n\tupload_by\x18\x04 \x01(\t\x12-\n\tcreate_at\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0b\x63omplate_at\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"Y\n\x11UploadTaskItemAck\x12\x0f\n\x07task_id\x18\x01 \x01(\x03\x12\x1f\n\x04rows\x18\x02 \x03(\x0b\x32\x11.irons.VerifyRows\x12\x12\n\nisContinue\x18\x03 \x01(\x08\"9\n\nVerifyRows\x12\x0b\n\x03row\x18\x01 \x01(\x03\x12\x1e\n\x04info\x18\x02 \x03(\x0b\x32\x10.irons.VerifyMsg\"?\n\x0cTaskItemCell\x12\r\n\x05value\x18\x01 \x01(\t\x12 \n\x06verify\x18\x02 \x01(\x0b\x32\x10.irons.VerifyMsg\">\n\tVerifyMsg\x12\r\n\x05index\x18\x01 \x01(\x03\x12\x0e\n\x06status\x18\x02 \x01(\x08\x12\x12\n\nverify_msg\x18\x03 \x01(\t\"Z\n\x0fUploadTaskItemM\x12\x11\n\ttotal_row\x18\x01 \x01(\x03\x12 \n\x04rows\x18\x02 \x03(\x0b\x32\x12.irons.TaskItemRow\x12\x12\n\nisContinue\x18\x03 \x01(\x08\">\n\x0bTaskItemRow\x12\x0b\n\x03row\x18\x01 \x01(\x03\x12\"\n\x05items\x18\x02 \x03(\x0b\x32\x13.irons.TaskItemCell\"N\n\x0eUploadResponse\x12\x0f\n\x07task_id\x18\x01 \x01(\x03\x12+\n\x07\x63reated\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"u\n\x0f\x44ownloadRequest\x12\x1b\n\x05scope\x18\x02 \x01(\x0b\x32\x0c.irons.Scope\x12\x10\n\x08template\x18\x03 \x01(\t\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x16\n\x0e\x64ownloadhelper\x18\x05 \x01(\t\x12\r\n\x05query\x18\x06 \x01(\t\"^\n\x0c\x44ownloadTask\x12\x0f\n\x07task_id\x18\x01 \x01(\x03\x12\x0b\n\x03url\x18\x02 \x01(\t\x12\x30\n\x0c\x63ompleted_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"J\n\x15\x44ownloadQueryResponse\x12\r\n\x05total\x18\x01 \x01(\x03\x12\"\n\x05tasks\x18\x02 \x03(\x0b\x32\x13.irons.DownloadTask\"R\n\x13\x44ownloadTaskRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\x03\x12\r\n\x05title\x18\x02 \x01(\x0c\x12\x0c\n\x04\x64\x61ta\x18\x03 \x01(\x0c\x12\r\n\x05\x65rror\x18\x04 \x01(\t\"l\n\x15\x44ownloadCustomRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\x03\x12\x11\n\tshee_name\x18\x02 \x01(\t\x12\x0c\n\x04\x64\x61ta\x18\x03 \x01(\x0c\x12\x12\n\nneed_merge\x18\x04 \x01(\x08\x12\r\n\x05\x65rror\x18\x05 \x01(\t\"\x14\n\x05\x45rror\x12\x0b\n\x03\x65rr\x18\x01 \x01(\t\"5\n\x0eRespTaskStatus\x12#\n\x06status\x18\x01 \x01(\x0e\x32\x13.irons.UiTaskStatus\"\x1c\n\nVerifyData\x12\x0e\n\x06status\x18\x01 \x01(\x03\"3\n\x0c\x43ommitStatus\x12\x0e\n\x06status\x18\x01 \x01(\x03\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\"#\n\x0bParseHelper\x12\x14\n\x0cparse_method\x18\x01 \x01(\t\"I\n\x0e\x41llParseHelper\x12\r\n\x05total\x18\x01 \x01(\x03\x12(\n\x06helper\x18\x02 \x03(\x0b\x32\x18.irons.ParseHelperStruct\"6\n\x11ParseHelperStruct\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\"\x1a\n\x0b\x44ownloadURL\x12\x0b\n\x03url\x18\x01 \x01(\t\"\x07\n\x05\x65mpty\"\x1f\n\x0cGetTaskInfor\x12\x0f\n\x07task_id\x18\x01 \x01(\x03\"\xcf\x01\n\x08TaskInfo\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\tupload_by\x18\x02 \x01(\t\x12\x11\n\tfile_size\x18\x03 \x01(\t\x12-\n\tcreate_at\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0b\x63omplate_at\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0bstatus_sign\x18\x06 \x01(\t\x12\r\n\x05query\x18\x07 \x01(\t\x12\x0b\n\x03url\x18\x08 \x01(\t\"5\n\x07GetInfo\x12\x0c\n\x04page\x18\x01 \x01(\x03\x12\x0c\n\x04size\x18\x02 \x01(\x03\x12\x0e\n\x06method\x18\x03 \x01(\t\"-\n\nTaskIntros\x12\x1f\n\x05items\x18\x01 \x03(\x0b\x32\x10.irons.TaskIntro\"3\n\x0cTaskListTemp\x12#\n\x05items\x18\x01 \x03(\x0b\x32\x14.irons.TaskItemsTemp\"\xe1\x01\n\rTaskItemsTemp\x12#\n\x06status\x18\x01 \x01(\x0e\x32\x13.irons.UiTaskStatus\x12\x14\n\x0ctask_com_url\x18\x02 \x01(\t\x12\x11\n\ttask_name\x18\x03 \x01(\t\x12\x0f\n\x07task_id\x18\x04 \x01(\x03\x12\x11\n\tupload_by\x18\x05 \x01(\t\x12-\n\tcreate_at\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0b\x63omplate_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"L\n\x07SqlPlan\x12\x11\n\tquery_sql\x18\x01 \x01(\t\x12\x14\n\x0ctemplate_url\x18\x02 \x01(\t\x12\x18\n\x10\x65xport_file_name\x18\x03 \x01(\t\"T\n\x0fSqlDownloadTask\x12\x0f\n\x07task_id\x18\x01 \x01(\x03\x12\x30\n\x0c\x63ompleted_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp*)\n\nUploadType\x12\x08\n\x04XLSX\x10\x00\x12\x07\n\x03\x43SV\x10\x01\x12\x08\n\x04JSON\x10\x02*?\n\x0cUiTaskStatus\x12\x08\n\x04Succ\x10\x00\x12\x08\n\x04\x46\x61il\x10\x01\x12\x0e\n\nProcessing\x10\x02\x12\x0b\n\x07Pending\x10\x03\x32\x8f\x02\n\rUploadService\x12Y\n\x06Upload\x12\x14.irons.UploadRequest\x1a\x15.irons.UploadResponse\" \x82\xd3\xe4\x93\x02\x1a\"\x15/api/v1/common/upload:\x01*(\x01\x12V\n\x1cProcessTaskValidatironstream\x12\x18.irons.UploadTaskItemAck\x1a\x16.irons.UploadTaskItemM\"\x00(\x01\x30\x01\x12K\n\x11ProcessTaskStream\x12\x18.irons.UploadTaskItemAck\x1a\x16.irons.UploadTaskItemM\"\x00(\x01\x30\x01\x32\xf1\x01\n\x0f\x44ownloadService\x12[\n\x08\x44ownload\x12\x16.irons.DownloadRequest\x1a\x13.irons.DownloadTask\"\"\x82\xd3\xe4\x93\x02\x1c\"\x17/api/v1/common/download:\x01*\x12?\n\x0fProcessDownload\x12\x1a.irons.DownloadTaskRequest\x1a\x0c.irons.Error\"\x00(\x01\x12@\n\x0e\x43ustomDownload\x12\x1c.irons.DownloadCustomRequest\x1a\x0c.irons.Error\"\x00(\x01\x32\xb5\x06\n\x06\x43ommon\x12\x64\n\x0eGetDownloadURL\x12\x13.irons.GetTaskInfor\x1a\x12.irons.DownloadURL\")\x82\xd3\xe4\x93\x02#\x12!/api/v1/common/download/{task_id}\x12U\n\x0bGetTaskInfo\x12\x13.irons.GetTaskInfor\x1a\x0f.irons.TaskInfo\" \x82\xd3\xe4\x93\x02\x1a\x12\x18/api/v1/common/{task_id}\x12^\n\x0fGetCurrPageTask\x12\x0e.irons.GetInfo\x1a\x11.irons.TaskIntros\"(\x82\xd3\xe4\x93\x02\"\"\x1d/api/v1/common/upload/gettask:\x01*\x12i\n\x13GetCurrPageTaskTemp\x12\x0e.irons.GetInfo\x1a\x13.irons.TaskListTemp\"-\x82\xd3\xe4\x93\x02\'\"\"/api/v1/common/upload/gettask/temp:\x01*\x12p\n\x11GetAllParseHelper\x12\x12.irons.ParseHelper\x1a\x15.irons.AllParseHelper\"0\x82\xd3\xe4\x93\x02*\x12(/api/v1/common/gethelpers/{parse_method}\x12i\n\rConfirmCommit\x12\x13.irons.GetTaskInfor\x1a\x13.irons.CommitStatus\".\x82\xd3\xe4\x93\x02(\x12&/api/v1/common/upload/commit/{task_id}\x12`\n\rGetVerifyData\x12\x13.irons.GetTaskInfor\x1a\x11.irons.VerifyData\"\'\x82\xd3\xe4\x93\x02!\x12\x1f/api/v1/common/verify/{task_id}\x12\x64\n\rGetTaskStatus\x12\x13.irons.GetTaskInfor\x1a\x15.irons.RespTaskStatus\"\'\x82\xd3\xe4\x93\x02!\x12\x1f/api/v1/common/status/{task_id}2b\n\x08SqlQuery\x12V\n\tSqlExport\x12\x0e.irons.SqlPlan\x1a\x16.irons.SqlDownloadTask\"!\x82\xd3\xe4\x93\x02\x1b\"\x16/api/v1/query/querysql:\x01*B\x0bZ\tapi2/api2b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])

_UPLOADTYPE = _descriptor.EnumDescriptor(
  name='UploadType',
  full_name='irons.UploadType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='XLSX', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CSV', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='JSON', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2670,
  serialized_end=2711,
)
_sym_db.RegisterEnumDescriptor(_UPLOADTYPE)

UploadType = enum_type_wrapper.EnumTypeWrapper(_UPLOADTYPE)
_UITASKSTATUS = _descriptor.EnumDescriptor(
  name='UiTaskStatus',
  full_name='irons.UiTaskStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='Succ', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Fail', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Processing', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Pending', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2713,
  serialized_end=2776,
)
_sym_db.RegisterEnumDescriptor(_UITASKSTATUS)

UiTaskStatus = enum_type_wrapper.EnumTypeWrapper(_UITASKSTATUS)
XLSX = 0
CSV = 1
JSON = 2
Succ = 0
Fail = 1
Processing = 2
Pending = 3



_UPLOADREQUEST = _descriptor.Descriptor(
  name='UploadRequest',
  full_name='irons.UploadRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='scope', full_name='irons.UploadRequest.scope', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='irons.UploadRequest.type', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='irons.UploadRequest.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='uploadhelper', full_name='irons.UploadRequest.uploadhelper', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data', full_name='irons.UploadRequest.data', index=4,
      number=5, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=91,
  serialized_end=218,
)


_SCOPE = _descriptor.Descriptor(
  name='Scope',
  full_name='irons.Scope',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='irons.Scope.partner_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='domain', full_name='irons.Scope.domain', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='service_topic', full_name='irons.Scope.service_topic', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='service_channel', full_name='irons.Scope.service_channel', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=220,
  serialized_end=311,
)


_TASKINTRO = _descriptor.Descriptor(
  name='TaskIntro',
  full_name='irons.TaskIntro',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='irons.TaskIntro.status', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='task_name', full_name='irons.TaskIntro.task_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='task_id', full_name='irons.TaskIntro.task_id', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='upload_by', full_name='irons.TaskIntro.upload_by', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='create_at', full_name='irons.TaskIntro.create_at', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='complate_at', full_name='irons.TaskIntro.complate_at', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=314,
  serialized_end=513,
)


_UPLOADTASKITEMACK = _descriptor.Descriptor(
  name='UploadTaskItemAck',
  full_name='irons.UploadTaskItemAck',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='task_id', full_name='irons.UploadTaskItemAck.task_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='irons.UploadTaskItemAck.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isContinue', full_name='irons.UploadTaskItemAck.isContinue', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=515,
  serialized_end=604,
)


_VERIFYROWS = _descriptor.Descriptor(
  name='VerifyRows',
  full_name='irons.VerifyRows',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='row', full_name='irons.VerifyRows.row', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='info', full_name='irons.VerifyRows.info', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=606,
  serialized_end=663,
)


_TASKITEMCELL = _descriptor.Descriptor(
  name='TaskItemCell',
  full_name='irons.TaskItemCell',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='irons.TaskItemCell.value', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='verify', full_name='irons.TaskItemCell.verify', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=665,
  serialized_end=728,
)


_VERIFYMSG = _descriptor.Descriptor(
  name='VerifyMsg',
  full_name='irons.VerifyMsg',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='index', full_name='irons.VerifyMsg.index', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='irons.VerifyMsg.status', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='verify_msg', full_name='irons.VerifyMsg.verify_msg', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=730,
  serialized_end=792,
)


_UPLOADTASKITEMM = _descriptor.Descriptor(
  name='UploadTaskItemM',
  full_name='irons.UploadTaskItemM',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total_row', full_name='irons.UploadTaskItemM.total_row', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='irons.UploadTaskItemM.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isContinue', full_name='irons.UploadTaskItemM.isContinue', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=794,
  serialized_end=884,
)


_TASKITEMROW = _descriptor.Descriptor(
  name='TaskItemRow',
  full_name='irons.TaskItemRow',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='row', full_name='irons.TaskItemRow.row', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='items', full_name='irons.TaskItemRow.items', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=886,
  serialized_end=948,
)


_UPLOADRESPONSE = _descriptor.Descriptor(
  name='UploadResponse',
  full_name='irons.UploadResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='task_id', full_name='irons.UploadResponse.task_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created', full_name='irons.UploadResponse.created', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=950,
  serialized_end=1028,
)


_DOWNLOADREQUEST = _descriptor.Descriptor(
  name='DownloadRequest',
  full_name='irons.DownloadRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='scope', full_name='irons.DownloadRequest.scope', index=0,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='template', full_name='irons.DownloadRequest.template', index=1,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='irons.DownloadRequest.name', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='downloadhelper', full_name='irons.DownloadRequest.downloadhelper', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='query', full_name='irons.DownloadRequest.query', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1030,
  serialized_end=1147,
)


_DOWNLOADTASK = _descriptor.Descriptor(
  name='DownloadTask',
  full_name='irons.DownloadTask',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='task_id', full_name='irons.DownloadTask.task_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='irons.DownloadTask.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='completed_at', full_name='irons.DownloadTask.completed_at', index=2,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1149,
  serialized_end=1243,
)


_DOWNLOADQUERYRESPONSE = _descriptor.Descriptor(
  name='DownloadQueryResponse',
  full_name='irons.DownloadQueryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='irons.DownloadQueryResponse.total', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tasks', full_name='irons.DownloadQueryResponse.tasks', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1245,
  serialized_end=1319,
)


_DOWNLOADTASKREQUEST = _descriptor.Descriptor(
  name='DownloadTaskRequest',
  full_name='irons.DownloadTaskRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='task_id', full_name='irons.DownloadTaskRequest.task_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='title', full_name='irons.DownloadTaskRequest.title', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data', full_name='irons.DownloadTaskRequest.data', index=2,
      number=3, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error', full_name='irons.DownloadTaskRequest.error', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1321,
  serialized_end=1403,
)


_DOWNLOADCUSTOMREQUEST = _descriptor.Descriptor(
  name='DownloadCustomRequest',
  full_name='irons.DownloadCustomRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='task_id', full_name='irons.DownloadCustomRequest.task_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='shee_name', full_name='irons.DownloadCustomRequest.shee_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data', full_name='irons.DownloadCustomRequest.data', index=2,
      number=3, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='need_merge', full_name='irons.DownloadCustomRequest.need_merge', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error', full_name='irons.DownloadCustomRequest.error', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1405,
  serialized_end=1513,
)


_ERROR = _descriptor.Descriptor(
  name='Error',
  full_name='irons.Error',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='err', full_name='irons.Error.err', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1515,
  serialized_end=1535,
)


_RESPTASKSTATUS = _descriptor.Descriptor(
  name='RespTaskStatus',
  full_name='irons.RespTaskStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='irons.RespTaskStatus.status', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1537,
  serialized_end=1590,
)


_VERIFYDATA = _descriptor.Descriptor(
  name='VerifyData',
  full_name='irons.VerifyData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='irons.VerifyData.status', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1592,
  serialized_end=1620,
)


_COMMITSTATUS = _descriptor.Descriptor(
  name='CommitStatus',
  full_name='irons.CommitStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='irons.CommitStatus.status', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='irons.CommitStatus.description', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1622,
  serialized_end=1673,
)


_PARSEHELPER = _descriptor.Descriptor(
  name='ParseHelper',
  full_name='irons.ParseHelper',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='parse_method', full_name='irons.ParseHelper.parse_method', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1675,
  serialized_end=1710,
)


_ALLPARSEHELPER = _descriptor.Descriptor(
  name='AllParseHelper',
  full_name='irons.AllParseHelper',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='irons.AllParseHelper.total', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='helper', full_name='irons.AllParseHelper.helper', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1712,
  serialized_end=1785,
)


_PARSEHELPERSTRUCT = _descriptor.Descriptor(
  name='ParseHelperStruct',
  full_name='irons.ParseHelperStruct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='irons.ParseHelperStruct.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='irons.ParseHelperStruct.description', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1787,
  serialized_end=1841,
)


_DOWNLOADURL = _descriptor.Descriptor(
  name='DownloadURL',
  full_name='irons.DownloadURL',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='url', full_name='irons.DownloadURL.url', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1843,
  serialized_end=1869,
)


_EMPTY = _descriptor.Descriptor(
  name='empty',
  full_name='irons.empty',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1871,
  serialized_end=1878,
)


_GETTASKINFOR = _descriptor.Descriptor(
  name='GetTaskInfor',
  full_name='irons.GetTaskInfor',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='task_id', full_name='irons.GetTaskInfor.task_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1880,
  serialized_end=1911,
)


_TASKINFO = _descriptor.Descriptor(
  name='TaskInfo',
  full_name='irons.TaskInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='irons.TaskInfo.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='upload_by', full_name='irons.TaskInfo.upload_by', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_size', full_name='irons.TaskInfo.file_size', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='create_at', full_name='irons.TaskInfo.create_at', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='complate_at', full_name='irons.TaskInfo.complate_at', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status_sign', full_name='irons.TaskInfo.status_sign', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='query', full_name='irons.TaskInfo.query', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='irons.TaskInfo.url', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1914,
  serialized_end=2121,
)


_GETINFO = _descriptor.Descriptor(
  name='GetInfo',
  full_name='irons.GetInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='page', full_name='irons.GetInfo.page', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='size', full_name='irons.GetInfo.size', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='method', full_name='irons.GetInfo.method', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2123,
  serialized_end=2176,
)


_TASKINTROS = _descriptor.Descriptor(
  name='TaskIntros',
  full_name='irons.TaskIntros',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='items', full_name='irons.TaskIntros.items', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2178,
  serialized_end=2223,
)


_TASKLISTTEMP = _descriptor.Descriptor(
  name='TaskListTemp',
  full_name='irons.TaskListTemp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='items', full_name='irons.TaskListTemp.items', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2225,
  serialized_end=2276,
)


_TASKITEMSTEMP = _descriptor.Descriptor(
  name='TaskItemsTemp',
  full_name='irons.TaskItemsTemp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='irons.TaskItemsTemp.status', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='task_com_url', full_name='irons.TaskItemsTemp.task_com_url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='task_name', full_name='irons.TaskItemsTemp.task_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='task_id', full_name='irons.TaskItemsTemp.task_id', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='upload_by', full_name='irons.TaskItemsTemp.upload_by', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='create_at', full_name='irons.TaskItemsTemp.create_at', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='complate_at', full_name='irons.TaskItemsTemp.complate_at', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2279,
  serialized_end=2504,
)


_SQLPLAN = _descriptor.Descriptor(
  name='SqlPlan',
  full_name='irons.SqlPlan',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='query_sql', full_name='irons.SqlPlan.query_sql', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='template_url', full_name='irons.SqlPlan.template_url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='export_file_name', full_name='irons.SqlPlan.export_file_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2506,
  serialized_end=2582,
)


_SQLDOWNLOADTASK = _descriptor.Descriptor(
  name='SqlDownloadTask',
  full_name='irons.SqlDownloadTask',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='task_id', full_name='irons.SqlDownloadTask.task_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='completed_at', full_name='irons.SqlDownloadTask.completed_at', index=1,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2584,
  serialized_end=2668,
)

_UPLOADREQUEST.fields_by_name['scope'].message_type = _SCOPE
_UPLOADREQUEST.fields_by_name['type'].enum_type = _UPLOADTYPE
_TASKINTRO.fields_by_name['status'].enum_type = _UITASKSTATUS
_TASKINTRO.fields_by_name['create_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TASKINTRO.fields_by_name['complate_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPLOADTASKITEMACK.fields_by_name['rows'].message_type = _VERIFYROWS
_VERIFYROWS.fields_by_name['info'].message_type = _VERIFYMSG
_TASKITEMCELL.fields_by_name['verify'].message_type = _VERIFYMSG
_UPLOADTASKITEMM.fields_by_name['rows'].message_type = _TASKITEMROW
_TASKITEMROW.fields_by_name['items'].message_type = _TASKITEMCELL
_UPLOADRESPONSE.fields_by_name['created'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DOWNLOADREQUEST.fields_by_name['scope'].message_type = _SCOPE
_DOWNLOADTASK.fields_by_name['completed_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DOWNLOADQUERYRESPONSE.fields_by_name['tasks'].message_type = _DOWNLOADTASK
_RESPTASKSTATUS.fields_by_name['status'].enum_type = _UITASKSTATUS
_ALLPARSEHELPER.fields_by_name['helper'].message_type = _PARSEHELPERSTRUCT
_TASKINFO.fields_by_name['create_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TASKINFO.fields_by_name['complate_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TASKINTROS.fields_by_name['items'].message_type = _TASKINTRO
_TASKLISTTEMP.fields_by_name['items'].message_type = _TASKITEMSTEMP
_TASKITEMSTEMP.fields_by_name['status'].enum_type = _UITASKSTATUS
_TASKITEMSTEMP.fields_by_name['create_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TASKITEMSTEMP.fields_by_name['complate_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_SQLDOWNLOADTASK.fields_by_name['completed_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['UploadRequest'] = _UPLOADREQUEST
DESCRIPTOR.message_types_by_name['Scope'] = _SCOPE
DESCRIPTOR.message_types_by_name['TaskIntro'] = _TASKINTRO
DESCRIPTOR.message_types_by_name['UploadTaskItemAck'] = _UPLOADTASKITEMACK
DESCRIPTOR.message_types_by_name['VerifyRows'] = _VERIFYROWS
DESCRIPTOR.message_types_by_name['TaskItemCell'] = _TASKITEMCELL
DESCRIPTOR.message_types_by_name['VerifyMsg'] = _VERIFYMSG
DESCRIPTOR.message_types_by_name['UploadTaskItemM'] = _UPLOADTASKITEMM
DESCRIPTOR.message_types_by_name['TaskItemRow'] = _TASKITEMROW
DESCRIPTOR.message_types_by_name['UploadResponse'] = _UPLOADRESPONSE
DESCRIPTOR.message_types_by_name['DownloadRequest'] = _DOWNLOADREQUEST
DESCRIPTOR.message_types_by_name['DownloadTask'] = _DOWNLOADTASK
DESCRIPTOR.message_types_by_name['DownloadQueryResponse'] = _DOWNLOADQUERYRESPONSE
DESCRIPTOR.message_types_by_name['DownloadTaskRequest'] = _DOWNLOADTASKREQUEST
DESCRIPTOR.message_types_by_name['DownloadCustomRequest'] = _DOWNLOADCUSTOMREQUEST
DESCRIPTOR.message_types_by_name['Error'] = _ERROR
DESCRIPTOR.message_types_by_name['RespTaskStatus'] = _RESPTASKSTATUS
DESCRIPTOR.message_types_by_name['VerifyData'] = _VERIFYDATA
DESCRIPTOR.message_types_by_name['CommitStatus'] = _COMMITSTATUS
DESCRIPTOR.message_types_by_name['ParseHelper'] = _PARSEHELPER
DESCRIPTOR.message_types_by_name['AllParseHelper'] = _ALLPARSEHELPER
DESCRIPTOR.message_types_by_name['ParseHelperStruct'] = _PARSEHELPERSTRUCT
DESCRIPTOR.message_types_by_name['DownloadURL'] = _DOWNLOADURL
DESCRIPTOR.message_types_by_name['empty'] = _EMPTY
DESCRIPTOR.message_types_by_name['GetTaskInfor'] = _GETTASKINFOR
DESCRIPTOR.message_types_by_name['TaskInfo'] = _TASKINFO
DESCRIPTOR.message_types_by_name['GetInfo'] = _GETINFO
DESCRIPTOR.message_types_by_name['TaskIntros'] = _TASKINTROS
DESCRIPTOR.message_types_by_name['TaskListTemp'] = _TASKLISTTEMP
DESCRIPTOR.message_types_by_name['TaskItemsTemp'] = _TASKITEMSTEMP
DESCRIPTOR.message_types_by_name['SqlPlan'] = _SQLPLAN
DESCRIPTOR.message_types_by_name['SqlDownloadTask'] = _SQLDOWNLOADTASK
DESCRIPTOR.enum_types_by_name['UploadType'] = _UPLOADTYPE
DESCRIPTOR.enum_types_by_name['UiTaskStatus'] = _UITASKSTATUS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

UploadRequest = _reflection.GeneratedProtocolMessageType('UploadRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPLOADREQUEST,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.UploadRequest)
  ))
_sym_db.RegisterMessage(UploadRequest)

Scope = _reflection.GeneratedProtocolMessageType('Scope', (_message.Message,), dict(
  DESCRIPTOR = _SCOPE,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.Scope)
  ))
_sym_db.RegisterMessage(Scope)

TaskIntro = _reflection.GeneratedProtocolMessageType('TaskIntro', (_message.Message,), dict(
  DESCRIPTOR = _TASKINTRO,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.TaskIntro)
  ))
_sym_db.RegisterMessage(TaskIntro)

UploadTaskItemAck = _reflection.GeneratedProtocolMessageType('UploadTaskItemAck', (_message.Message,), dict(
  DESCRIPTOR = _UPLOADTASKITEMACK,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.UploadTaskItemAck)
  ))
_sym_db.RegisterMessage(UploadTaskItemAck)

VerifyRows = _reflection.GeneratedProtocolMessageType('VerifyRows', (_message.Message,), dict(
  DESCRIPTOR = _VERIFYROWS,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.VerifyRows)
  ))
_sym_db.RegisterMessage(VerifyRows)

TaskItemCell = _reflection.GeneratedProtocolMessageType('TaskItemCell', (_message.Message,), dict(
  DESCRIPTOR = _TASKITEMCELL,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.TaskItemCell)
  ))
_sym_db.RegisterMessage(TaskItemCell)

VerifyMsg = _reflection.GeneratedProtocolMessageType('VerifyMsg', (_message.Message,), dict(
  DESCRIPTOR = _VERIFYMSG,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.VerifyMsg)
  ))
_sym_db.RegisterMessage(VerifyMsg)

UploadTaskItemM = _reflection.GeneratedProtocolMessageType('UploadTaskItemM', (_message.Message,), dict(
  DESCRIPTOR = _UPLOADTASKITEMM,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.UploadTaskItemM)
  ))
_sym_db.RegisterMessage(UploadTaskItemM)

TaskItemRow = _reflection.GeneratedProtocolMessageType('TaskItemRow', (_message.Message,), dict(
  DESCRIPTOR = _TASKITEMROW,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.TaskItemRow)
  ))
_sym_db.RegisterMessage(TaskItemRow)

UploadResponse = _reflection.GeneratedProtocolMessageType('UploadResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPLOADRESPONSE,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.UploadResponse)
  ))
_sym_db.RegisterMessage(UploadResponse)

DownloadRequest = _reflection.GeneratedProtocolMessageType('DownloadRequest', (_message.Message,), dict(
  DESCRIPTOR = _DOWNLOADREQUEST,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.DownloadRequest)
  ))
_sym_db.RegisterMessage(DownloadRequest)

DownloadTask = _reflection.GeneratedProtocolMessageType('DownloadTask', (_message.Message,), dict(
  DESCRIPTOR = _DOWNLOADTASK,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.DownloadTask)
  ))
_sym_db.RegisterMessage(DownloadTask)

DownloadQueryResponse = _reflection.GeneratedProtocolMessageType('DownloadQueryResponse', (_message.Message,), dict(
  DESCRIPTOR = _DOWNLOADQUERYRESPONSE,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.DownloadQueryResponse)
  ))
_sym_db.RegisterMessage(DownloadQueryResponse)

DownloadTaskRequest = _reflection.GeneratedProtocolMessageType('DownloadTaskRequest', (_message.Message,), dict(
  DESCRIPTOR = _DOWNLOADTASKREQUEST,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.DownloadTaskRequest)
  ))
_sym_db.RegisterMessage(DownloadTaskRequest)

DownloadCustomRequest = _reflection.GeneratedProtocolMessageType('DownloadCustomRequest', (_message.Message,), dict(
  DESCRIPTOR = _DOWNLOADCUSTOMREQUEST,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.DownloadCustomRequest)
  ))
_sym_db.RegisterMessage(DownloadCustomRequest)

Error = _reflection.GeneratedProtocolMessageType('Error', (_message.Message,), dict(
  DESCRIPTOR = _ERROR,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.Error)
  ))
_sym_db.RegisterMessage(Error)

RespTaskStatus = _reflection.GeneratedProtocolMessageType('RespTaskStatus', (_message.Message,), dict(
  DESCRIPTOR = _RESPTASKSTATUS,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.RespTaskStatus)
  ))
_sym_db.RegisterMessage(RespTaskStatus)

VerifyData = _reflection.GeneratedProtocolMessageType('VerifyData', (_message.Message,), dict(
  DESCRIPTOR = _VERIFYDATA,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.VerifyData)
  ))
_sym_db.RegisterMessage(VerifyData)

CommitStatus = _reflection.GeneratedProtocolMessageType('CommitStatus', (_message.Message,), dict(
  DESCRIPTOR = _COMMITSTATUS,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.CommitStatus)
  ))
_sym_db.RegisterMessage(CommitStatus)

ParseHelper = _reflection.GeneratedProtocolMessageType('ParseHelper', (_message.Message,), dict(
  DESCRIPTOR = _PARSEHELPER,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.ParseHelper)
  ))
_sym_db.RegisterMessage(ParseHelper)

AllParseHelper = _reflection.GeneratedProtocolMessageType('AllParseHelper', (_message.Message,), dict(
  DESCRIPTOR = _ALLPARSEHELPER,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.AllParseHelper)
  ))
_sym_db.RegisterMessage(AllParseHelper)

ParseHelperStruct = _reflection.GeneratedProtocolMessageType('ParseHelperStruct', (_message.Message,), dict(
  DESCRIPTOR = _PARSEHELPERSTRUCT,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.ParseHelperStruct)
  ))
_sym_db.RegisterMessage(ParseHelperStruct)

DownloadURL = _reflection.GeneratedProtocolMessageType('DownloadURL', (_message.Message,), dict(
  DESCRIPTOR = _DOWNLOADURL,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.DownloadURL)
  ))
_sym_db.RegisterMessage(DownloadURL)

empty = _reflection.GeneratedProtocolMessageType('empty', (_message.Message,), dict(
  DESCRIPTOR = _EMPTY,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.empty)
  ))
_sym_db.RegisterMessage(empty)

GetTaskInfor = _reflection.GeneratedProtocolMessageType('GetTaskInfor', (_message.Message,), dict(
  DESCRIPTOR = _GETTASKINFOR,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.GetTaskInfor)
  ))
_sym_db.RegisterMessage(GetTaskInfor)

TaskInfo = _reflection.GeneratedProtocolMessageType('TaskInfo', (_message.Message,), dict(
  DESCRIPTOR = _TASKINFO,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.TaskInfo)
  ))
_sym_db.RegisterMessage(TaskInfo)

GetInfo = _reflection.GeneratedProtocolMessageType('GetInfo', (_message.Message,), dict(
  DESCRIPTOR = _GETINFO,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.GetInfo)
  ))
_sym_db.RegisterMessage(GetInfo)

TaskIntros = _reflection.GeneratedProtocolMessageType('TaskIntros', (_message.Message,), dict(
  DESCRIPTOR = _TASKINTROS,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.TaskIntros)
  ))
_sym_db.RegisterMessage(TaskIntros)

TaskListTemp = _reflection.GeneratedProtocolMessageType('TaskListTemp', (_message.Message,), dict(
  DESCRIPTOR = _TASKLISTTEMP,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.TaskListTemp)
  ))
_sym_db.RegisterMessage(TaskListTemp)

TaskItemsTemp = _reflection.GeneratedProtocolMessageType('TaskItemsTemp', (_message.Message,), dict(
  DESCRIPTOR = _TASKITEMSTEMP,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.TaskItemsTemp)
  ))
_sym_db.RegisterMessage(TaskItemsTemp)

SqlPlan = _reflection.GeneratedProtocolMessageType('SqlPlan', (_message.Message,), dict(
  DESCRIPTOR = _SQLPLAN,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.SqlPlan)
  ))
_sym_db.RegisterMessage(SqlPlan)

SqlDownloadTask = _reflection.GeneratedProtocolMessageType('SqlDownloadTask', (_message.Message,), dict(
  DESCRIPTOR = _SQLDOWNLOADTASK,
  __module__ = 'irons.irons_pb2'
  # @@protoc_insertion_point(class_scope:irons.SqlDownloadTask)
  ))
_sym_db.RegisterMessage(SqlDownloadTask)


DESCRIPTOR._options = None

_UPLOADSERVICE = _descriptor.ServiceDescriptor(
  name='UploadService',
  full_name='irons.UploadService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=2779,
  serialized_end=3050,
  methods=[
  _descriptor.MethodDescriptor(
    name='Upload',
    full_name='irons.UploadService.Upload',
    index=0,
    containing_service=None,
    input_type=_UPLOADREQUEST,
    output_type=_UPLOADRESPONSE,
    serialized_options=_b('\202\323\344\223\002\032\"\025/api/v1/common/upload:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ProcessTaskValidatironstream',
    full_name='irons.UploadService.ProcessTaskValidatironstream',
    index=1,
    containing_service=None,
    input_type=_UPLOADTASKITEMACK,
    output_type=_UPLOADTASKITEMM,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='ProcessTaskStream',
    full_name='irons.UploadService.ProcessTaskStream',
    index=2,
    containing_service=None,
    input_type=_UPLOADTASKITEMACK,
    output_type=_UPLOADTASKITEMM,
    serialized_options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_UPLOADSERVICE)

DESCRIPTOR.services_by_name['UploadService'] = _UPLOADSERVICE


_DOWNLOADSERVICE = _descriptor.ServiceDescriptor(
  name='DownloadService',
  full_name='irons.DownloadService',
  file=DESCRIPTOR,
  index=1,
  serialized_options=None,
  serialized_start=3053,
  serialized_end=3294,
  methods=[
  _descriptor.MethodDescriptor(
    name='Download',
    full_name='irons.DownloadService.Download',
    index=0,
    containing_service=None,
    input_type=_DOWNLOADREQUEST,
    output_type=_DOWNLOADTASK,
    serialized_options=_b('\202\323\344\223\002\034\"\027/api/v1/common/download:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ProcessDownload',
    full_name='irons.DownloadService.ProcessDownload',
    index=1,
    containing_service=None,
    input_type=_DOWNLOADTASKREQUEST,
    output_type=_ERROR,
    serialized_options=None,
  ),
  _descriptor.MethodDescriptor(
    name='CustomDownload',
    full_name='irons.DownloadService.CustomDownload',
    index=2,
    containing_service=None,
    input_type=_DOWNLOADCUSTOMREQUEST,
    output_type=_ERROR,
    serialized_options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_DOWNLOADSERVICE)

DESCRIPTOR.services_by_name['DownloadService'] = _DOWNLOADSERVICE


_COMMON = _descriptor.ServiceDescriptor(
  name='Common',
  full_name='irons.Common',
  file=DESCRIPTOR,
  index=2,
  serialized_options=None,
  serialized_start=3297,
  serialized_end=4118,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetDownloadURL',
    full_name='irons.Common.GetDownloadURL',
    index=0,
    containing_service=None,
    input_type=_GETTASKINFOR,
    output_type=_DOWNLOADURL,
    serialized_options=_b('\202\323\344\223\002#\022!/api/v1/common/download/{task_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTaskInfo',
    full_name='irons.Common.GetTaskInfo',
    index=1,
    containing_service=None,
    input_type=_GETTASKINFOR,
    output_type=_TASKINFO,
    serialized_options=_b('\202\323\344\223\002\032\022\030/api/v1/common/{task_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetCurrPageTask',
    full_name='irons.Common.GetCurrPageTask',
    index=2,
    containing_service=None,
    input_type=_GETINFO,
    output_type=_TASKINTROS,
    serialized_options=_b('\202\323\344\223\002\"\"\035/api/v1/common/upload/gettask:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetCurrPageTaskTemp',
    full_name='irons.Common.GetCurrPageTaskTemp',
    index=3,
    containing_service=None,
    input_type=_GETINFO,
    output_type=_TASKLISTTEMP,
    serialized_options=_b('\202\323\344\223\002\'\"\"/api/v1/common/upload/gettask/temp:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAllParseHelper',
    full_name='irons.Common.GetAllParseHelper',
    index=4,
    containing_service=None,
    input_type=_PARSEHELPER,
    output_type=_ALLPARSEHELPER,
    serialized_options=_b('\202\323\344\223\002*\022(/api/v1/common/gethelpers/{parse_method}'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmCommit',
    full_name='irons.Common.ConfirmCommit',
    index=5,
    containing_service=None,
    input_type=_GETTASKINFOR,
    output_type=_COMMITSTATUS,
    serialized_options=_b('\202\323\344\223\002(\022&/api/v1/common/upload/commit/{task_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetVerifyData',
    full_name='irons.Common.GetVerifyData',
    index=6,
    containing_service=None,
    input_type=_GETTASKINFOR,
    output_type=_VERIFYDATA,
    serialized_options=_b('\202\323\344\223\002!\022\037/api/v1/common/verify/{task_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetTaskStatus',
    full_name='irons.Common.GetTaskStatus',
    index=7,
    containing_service=None,
    input_type=_GETTASKINFOR,
    output_type=_RESPTASKSTATUS,
    serialized_options=_b('\202\323\344\223\002!\022\037/api/v1/common/status/{task_id}'),
  ),
])
_sym_db.RegisterServiceDescriptor(_COMMON)

DESCRIPTOR.services_by_name['Common'] = _COMMON


_SQLQUERY = _descriptor.ServiceDescriptor(
  name='SqlQuery',
  full_name='irons.SqlQuery',
  file=DESCRIPTOR,
  index=3,
  serialized_options=None,
  serialized_start=4120,
  serialized_end=4218,
  methods=[
  _descriptor.MethodDescriptor(
    name='SqlExport',
    full_name='irons.SqlQuery.SqlExport',
    index=0,
    containing_service=None,
    input_type=_SQLPLAN,
    output_type=_SQLDOWNLOADTASK,
    serialized_options=_b('\202\323\344\223\002\033\"\026/api/v1/query/querysql:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_SQLQUERY)

DESCRIPTOR.services_by_name['SqlQuery'] = _SQLQUERY

# @@protoc_insertion_point(module_scope)
