# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from irons import irons_pb2 as irons_dot_irons__pb2


class UploadServiceStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.Upload = channel.stream_unary(
        '/irons.UploadService/Upload',
        request_serializer=irons_dot_irons__pb2.UploadRequest.SerializeToString,
        response_deserializer=irons_dot_irons__pb2.UploadResponse.FromString,
        )
    self.ProcessTaskValidatironstream = channel.stream_stream(
        '/irons.UploadService/ProcessTaskValidatironstream',
        request_serializer=irons_dot_irons__pb2.UploadTaskItemAck.SerializeToString,
        response_deserializer=irons_dot_irons__pb2.UploadTaskItemM.FromString,
        )
    self.ProcessTaskStream = channel.stream_stream(
        '/irons.UploadService/ProcessTaskStream',
        request_serializer=irons_dot_irons__pb2.UploadTaskItemAck.SerializeToString,
        response_deserializer=irons_dot_irons__pb2.UploadTaskItemM.FromString,
        )


class UploadServiceServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def Upload(self, request_iterator, context):
    """Upload 上传任务
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ProcessTaskValidatironstream(self, request_iterator, context):
    """验证业务结果
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ProcessTaskStream(self, request_iterator, context):
    """处理业务结果
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_UploadServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'Upload': grpc.stream_unary_rpc_method_handler(
          servicer.Upload,
          request_deserializer=irons_dot_irons__pb2.UploadRequest.FromString,
          response_serializer=irons_dot_irons__pb2.UploadResponse.SerializeToString,
      ),
      'ProcessTaskValidatironstream': grpc.stream_stream_rpc_method_handler(
          servicer.ProcessTaskValidatironstream,
          request_deserializer=irons_dot_irons__pb2.UploadTaskItemAck.FromString,
          response_serializer=irons_dot_irons__pb2.UploadTaskItemM.SerializeToString,
      ),
      'ProcessTaskStream': grpc.stream_stream_rpc_method_handler(
          servicer.ProcessTaskStream,
          request_deserializer=irons_dot_irons__pb2.UploadTaskItemAck.FromString,
          response_serializer=irons_dot_irons__pb2.UploadTaskItemM.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'irons.UploadService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))


class DownloadServiceStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.Download = channel.unary_unary(
        '/irons.DownloadService/Download',
        request_serializer=irons_dot_irons__pb2.DownloadRequest.SerializeToString,
        response_deserializer=irons_dot_irons__pb2.DownloadTask.FromString,
        )
    self.ProcessDownload = channel.stream_unary(
        '/irons.DownloadService/ProcessDownload',
        request_serializer=irons_dot_irons__pb2.DownloadTaskRequest.SerializeToString,
        response_deserializer=irons_dot_irons__pb2.Error.FromString,
        )
    self.CustomDownload = channel.stream_unary(
        '/irons.DownloadService/CustomDownload',
        request_serializer=irons_dot_irons__pb2.DownloadCustomRequest.SerializeToString,
        response_deserializer=irons_dot_irons__pb2.Error.FromString,
        )


class DownloadServiceServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def Download(self, request, context):
    """提交下载任务(前端)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ProcessDownload(self, request_iterator, context):
    """开始下载文件生成
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CustomDownload(self, request_iterator, context):
    """业务报表(与自定义报表对接)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_DownloadServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'Download': grpc.unary_unary_rpc_method_handler(
          servicer.Download,
          request_deserializer=irons_dot_irons__pb2.DownloadRequest.FromString,
          response_serializer=irons_dot_irons__pb2.DownloadTask.SerializeToString,
      ),
      'ProcessDownload': grpc.stream_unary_rpc_method_handler(
          servicer.ProcessDownload,
          request_deserializer=irons_dot_irons__pb2.DownloadTaskRequest.FromString,
          response_serializer=irons_dot_irons__pb2.Error.SerializeToString,
      ),
      'CustomDownload': grpc.stream_unary_rpc_method_handler(
          servicer.CustomDownload,
          request_deserializer=irons_dot_irons__pb2.DownloadCustomRequest.FromString,
          response_serializer=irons_dot_irons__pb2.Error.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'irons.DownloadService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))


class CommonStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetDownloadURL = channel.unary_unary(
        '/irons.Common/GetDownloadURL',
        request_serializer=irons_dot_irons__pb2.GetTaskInfor.SerializeToString,
        response_deserializer=irons_dot_irons__pb2.DownloadURL.FromString,
        )
    self.GetTaskInfo = channel.unary_unary(
        '/irons.Common/GetTaskInfo',
        request_serializer=irons_dot_irons__pb2.GetTaskInfor.SerializeToString,
        response_deserializer=irons_dot_irons__pb2.TaskInfo.FromString,
        )
    self.GetCurrPageTask = channel.unary_unary(
        '/irons.Common/GetCurrPageTask',
        request_serializer=irons_dot_irons__pb2.GetInfo.SerializeToString,
        response_deserializer=irons_dot_irons__pb2.TaskIntros.FromString,
        )
    self.GetCurrPageTaskTemp = channel.unary_unary(
        '/irons.Common/GetCurrPageTaskTemp',
        request_serializer=irons_dot_irons__pb2.GetInfo.SerializeToString,
        response_deserializer=irons_dot_irons__pb2.TaskListTemp.FromString,
        )
    self.GetAllParseHelper = channel.unary_unary(
        '/irons.Common/GetAllParseHelper',
        request_serializer=irons_dot_irons__pb2.ParseHelper.SerializeToString,
        response_deserializer=irons_dot_irons__pb2.AllParseHelper.FromString,
        )
    self.ConfirmCommit = channel.unary_unary(
        '/irons.Common/ConfirmCommit',
        request_serializer=irons_dot_irons__pb2.GetTaskInfor.SerializeToString,
        response_deserializer=irons_dot_irons__pb2.CommitStatus.FromString,
        )
    self.GetVerifyData = channel.unary_unary(
        '/irons.Common/GetVerifyData',
        request_serializer=irons_dot_irons__pb2.GetTaskInfor.SerializeToString,
        response_deserializer=irons_dot_irons__pb2.VerifyData.FromString,
        )
    self.GetTaskStatus = channel.unary_unary(
        '/irons.Common/GetTaskStatus',
        request_serializer=irons_dot_irons__pb2.GetTaskInfor.SerializeToString,
        response_deserializer=irons_dot_irons__pb2.RespTaskStatus.FromString,
        )


class CommonServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def GetDownloadURL(self, request, context):
    """获取文件下载地址
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTaskInfo(self, request, context):
    """获取Task详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetCurrPageTask(self, request, context):
    """GetCurrPageTask 获取当前页所有对应任务
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetCurrPageTaskTemp(self, request, context):
    """TODO GetCurrPageTask 获取当前页所有对应任务(临时方案)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetAllParseHelper(self, request, context):
    """由参数获取解析函数及其描述(参数为)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ConfirmCommit(self, request, context):
    """确认提交
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetVerifyData(self, request, context):
    """TODO 此接口考虑存在必要
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTaskStatus(self, request, context):
    """前端轮询任务状态
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_CommonServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetDownloadURL': grpc.unary_unary_rpc_method_handler(
          servicer.GetDownloadURL,
          request_deserializer=irons_dot_irons__pb2.GetTaskInfor.FromString,
          response_serializer=irons_dot_irons__pb2.DownloadURL.SerializeToString,
      ),
      'GetTaskInfo': grpc.unary_unary_rpc_method_handler(
          servicer.GetTaskInfo,
          request_deserializer=irons_dot_irons__pb2.GetTaskInfor.FromString,
          response_serializer=irons_dot_irons__pb2.TaskInfo.SerializeToString,
      ),
      'GetCurrPageTask': grpc.unary_unary_rpc_method_handler(
          servicer.GetCurrPageTask,
          request_deserializer=irons_dot_irons__pb2.GetInfo.FromString,
          response_serializer=irons_dot_irons__pb2.TaskIntros.SerializeToString,
      ),
      'GetCurrPageTaskTemp': grpc.unary_unary_rpc_method_handler(
          servicer.GetCurrPageTaskTemp,
          request_deserializer=irons_dot_irons__pb2.GetInfo.FromString,
          response_serializer=irons_dot_irons__pb2.TaskListTemp.SerializeToString,
      ),
      'GetAllParseHelper': grpc.unary_unary_rpc_method_handler(
          servicer.GetAllParseHelper,
          request_deserializer=irons_dot_irons__pb2.ParseHelper.FromString,
          response_serializer=irons_dot_irons__pb2.AllParseHelper.SerializeToString,
      ),
      'ConfirmCommit': grpc.unary_unary_rpc_method_handler(
          servicer.ConfirmCommit,
          request_deserializer=irons_dot_irons__pb2.GetTaskInfor.FromString,
          response_serializer=irons_dot_irons__pb2.CommitStatus.SerializeToString,
      ),
      'GetVerifyData': grpc.unary_unary_rpc_method_handler(
          servicer.GetVerifyData,
          request_deserializer=irons_dot_irons__pb2.GetTaskInfor.FromString,
          response_serializer=irons_dot_irons__pb2.VerifyData.SerializeToString,
      ),
      'GetTaskStatus': grpc.unary_unary_rpc_method_handler(
          servicer.GetTaskStatus,
          request_deserializer=irons_dot_irons__pb2.GetTaskInfor.FromString,
          response_serializer=irons_dot_irons__pb2.RespTaskStatus.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'irons.Common', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))


class SqlQueryStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.SqlExport = channel.unary_unary(
        '/irons.SqlQuery/SqlExport',
        request_serializer=irons_dot_irons__pb2.SqlPlan.SerializeToString,
        response_deserializer=irons_dot_irons__pb2.SqlDownloadTask.FromString,
        )


class SqlQueryServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def SqlExport(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_SqlQueryServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'SqlExport': grpc.unary_unary_rpc_method_handler(
          servicer.SqlExport,
          request_deserializer=irons_dot_irons__pb2.SqlPlan.FromString,
          response_serializer=irons_dot_irons__pb2.SqlDownloadTask.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'irons.SqlQuery', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
