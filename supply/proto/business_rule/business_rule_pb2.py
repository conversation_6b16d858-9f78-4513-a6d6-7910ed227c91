# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: business_rule/business_rule.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='business_rule/business_rule.proto',
  package='business_rule',
  syntax='proto3',
  serialized_options=_b('Z\017./business_rule'),
  serialized_pb=_b('\n!business_rule/business_rule.proto\x12\rbusiness_rule\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x17\n\x07Request\x12\x0c\n\x04ping\x18\x01 \x01(\t\")\n\x08Response\x12\x0c\n\x04\x63ode\x18\x01 \x01(\r\x12\x0f\n\x07message\x18\x02 \x01(\t\"B\n\x04Page\x12\r\n\x05order\x18\x01 \x01(\t\x12\x0c\n\x04sort\x18\x02 \x01(\t\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x0e\n\x06offset\x18\x04 \x01(\x05\"\xea\x01\n\x0c\x42usinessRule\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x11\n\trule_type\x18\x04 \x01(\t\x12\x15\n\rlogistic_mode\x18\x05 \x01(\t\x12\x1e\n\x16\x64istribution_center_id\x18\x06 \x01(\x04\x12\x0e\n\x06status\x18\x08 \x01(\t\x12\x14\n\x0cupdated_name\x18\x0b \x01(\t\x12+\n\x07updated\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rproduct_count\x18\r \x01(\x03\"\xb6\x03\n\x10OrderRuleProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12\x14\n\x0cproduct_code\x18\x04 \x01(\t\x12\x15\n\rorder_unit_id\x18\x05 \x01(\x04\x12\x17\n\x0forder_unit_name\x18\x06 \x01(\t\x12\x0f\n\x07min_qty\x18\x07 \x01(\t\x12\x0f\n\x07max_qty\x18\x08 \x01(\t\x12\x10\n\x08incr_qty\x18\t \x01(\t\x12\x12\n\ncycle_type\x18\n \x01(\t\x12\x12\n\nstart_date\x18\x0b \x01(\t\x12\x14\n\x0cinterval_day\x18\x0c \x01(\t\x12\x14\n\x0c\x61rrival_days\x18\r \x03(\t\x12\x12\n\norder_days\x18\x0e \x03(\t\x12\x18\n\x10\x61llow_main_order\x18\x0f \x01(\x08\x12\x0e\n\x06status\x18\x10 \x01(\t\x12\x1f\n\x17receiving_deviation_min\x18\x11 \x01(\t\x12\x1f\n\x17receiving_deviation_max\x18\x12 \x01(\t\x12\x1e\n\x16main_order_arrival_day\x18\x13 \x01(\x03\"\x8a\x01\n\x16\x43reateOrderRuleRequest\x12\x32\n\rbusiness_rule\x18\x01 \x01(\x0b\x32\x1b.business_rule.BusinessRule\x12<\n\x13order_rule_products\x18\x02 \x03(\x0b\x32\x1f.business_rule.OrderRuleProduct\"\xa0\x01\n\x16UpdateOrderRuleRequest\x12\x32\n\rbusiness_rule\x18\x01 \x01(\x0b\x32\x1b.business_rule.BusinessRule\x12<\n\x13order_rule_products\x18\x02 \x03(\x0b\x32\x1f.business_rule.OrderRuleProduct\x12\x14\n\x0c\x64\x65l_item_ids\x18\x04 \x03(\x04\"\xc5\x02\n\x1bQueryOrderRuleDetailRequest\x12\x16\n\x0eorder_rule_ids\x18\x01 \x03(\x04\x12\x0b\n\x03ids\x18\x02 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x03 \x03(\x04\x12\x16\n\x0eproduct_status\x18\x04 \x01(\t\x12\x15\n\rlogistic_mode\x18\x05 \x01(\t\x12\x11\n\tstore_ids\x18\x06 \x03(\x04\x12\x14\n\x0cstore_status\x18\x07 \x01(\t\x12\x13\n\x0brule_status\x18\x08 \x01(\t\x12!\n\x04page\x18\t \x01(\x0b\x32\x13.business_rule.Page\x12/\n\tview_type\x18\n \x01(\x0e\x32\x1c.business_rule.QueryViewType\x12\x16\n\x0eproduct_search\x18\x0b \x01(\t\x12\x13\n\x0brule_search\x18\x0c \x01(\t\"d\n\x1cQueryOrderRuleDetailResponse\x12\x35\n\x04rows\x18\x02 \x03(\x0b\x32\'.business_rule.OrderRuleProductFullInfo\x12\r\n\x05total\x18\x03 \x01(\x03\"\xdd\x06\n\x18OrderRuleProductFullInfo\x12\x1d\n\x15order_rule_product_id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12\x14\n\x0cproduct_code\x18\x04 \x01(\t\x12\x15\n\rorder_unit_id\x18\x05 \x01(\x04\x12\x17\n\x0forder_unit_name\x18\x06 \x01(\t\x12\x0f\n\x07min_qty\x18\x07 \x01(\t\x12\x0f\n\x07max_qty\x18\x08 \x01(\t\x12\x10\n\x08incr_qty\x18\t \x01(\t\x12\x12\n\ncycle_type\x18\n \x01(\t\x12\x12\n\nstart_date\x18\x0b \x01(\t\x12\x14\n\x0c\x61rrival_days\x18\x0c \x03(\t\x12\x12\n\norder_days\x18\r \x03(\t\x12\x15\n\rorder_rule_id\x18\x0e \x01(\x04\x12\x17\n\x0forder_rule_name\x18\x0f \x01(\t\x12\x17\n\x0forder_rule_code\x18\x10 \x01(\t\x12\x15\n\rlogistic_mode\x18\x11 \x01(\t\x12\x1e\n\x16\x64istribution_center_id\x18\x12 \x01(\x04\x12 \n\x18\x64istribution_center_name\x18\x13 \x01(\t\x12 \n\x18\x64istribution_center_code\x18\x14 \x01(\t\x12\x0e\n\x06status\x18\x15 \x01(\t\x12\x10\n\x08store_id\x18\x16 \x01(\x04\x12\x12\n\nstore_name\x18\x17 \x01(\t\x12\x12\n\nstore_code\x18\x18 \x01(\t\x12\x14\n\x0cstore_status\x18\x19 \x01(\t\x12\x1b\n\x13order_rule_store_id\x18\x1a \x01(\x04\x12\x14\n\x0cinterval_day\x18\x1b \x01(\t\x12\x14\n\x0cupdated_name\x18\x1c \x01(\t\x12+\n\x07updated\x18\x1d \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x18\n\x10\x61llow_main_order\x18\x1e \x01(\x08\x12\x16\n\x0eproduct_status\x18\x1f \x01(\t\x12\x1f\n\x17receiving_deviation_min\x18  \x01(\t\x12\x1f\n\x17receiving_deviation_max\x18! \x01(\t\x12\x1e\n\x16main_order_arrival_day\x18\" \x01(\x03\"\\\n\x14\x42usinessRuleResponse\x12$\n\x04\x63ode\x18\x01 \x01(\x0e\x32\x16.business_rule.ErrCode\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\r\n\x05total\x18\x04 \x01(\x03\"\'\n\x19\x44\x65leteBusinessRuleRequest\x12\n\n\x02id\x18\x01 \x01(\x04\".\n\x0eOrderRuleStore\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\"o\n\x1b\x43reateOrderRuleStoreRequest\x12\x16\n\x0eorder_rule_ids\x18\x01 \x03(\x04\x12\x38\n\x11order_rule_stores\x18\x02 \x03(\x0b\x32\x1d.business_rule.OrderRuleStore\"*\n\x1b\x44\x65leteOrderRuleStoreRequest\x12\x0b\n\x03ids\x18\x01 \x03(\x04\"\x81\x02\n\x1a\x43heckOrderRuleExistRequest\x12\x44\n\x15update_order_rule_req\x18\x01 \x01(\x0b\x32%.business_rule.UpdateOrderRuleRequest\x12O\n\x1b\x63reate_order_rule_store_req\x18\x02 \x01(\x0b\x32*.business_rule.CreateOrderRuleStoreRequest\x12)\n\x06\x61\x63tion\x18\x03 \x01(\x0e\x32\x19.business_rule.ActionCode\x12!\n\x04page\x18\x04 \x01(\x0b\x32\x13.business_rule.Page\"c\n\x1b\x43heckOrderRuleExistResponse\x12\x35\n\x04rows\x18\x01 \x03(\x0b\x32\'.business_rule.OrderRuleProductFullInfo\x12\r\n\x05total\x18\x02 \x01(\x03\"\xee\x01\n\"QueryOrderRuleDetailByStoreRequest\x12\x16\n\x0eorder_rule_ids\x18\x01 \x03(\x04\x12\x0b\n\x03ids\x18\x02 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x03 \x03(\x04\x12\x16\n\x0eproduct_status\x18\x04 \x01(\t\x12\x15\n\rlogistic_mode\x18\x05 \x01(\t\x12\x11\n\tstore_ids\x18\x06 \x03(\x04\x12\x14\n\x0cstore_status\x18\x07 \x01(\t\x12\x13\n\x0brule_status\x18\x08 \x01(\t\x12!\n\x04page\x18\t \x01(\x0b\x32\x13.business_rule.Page\"\xcb\x01\n\x1fQueryValidProductByStoreRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x13\n\x0bproduct_ids\x18\x02 \x03(\x04\x12.\n\norder_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\x0b\x66ilter_type\x18\x04 \x01(\x0e\x32\x19.business_rule.FilterType\x12!\n\x04page\x18\x05 \x01(\x0b\x32\x13.business_rule.Page\"i\n QueryValidProductByStoreResponse\x12\x36\n\x04rows\x18\x01 \x03(\x0b\x32(.business_rule.OrderRuleProductBasicInfo\x12\r\n\x05total\x18\x02 \x01(\x03\"\xf0\x04\n\x19OrderRuleProductBasicInfo\x12\x1d\n\x15order_rule_product_id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x1b\n\x13product_category_id\x18\x03 \x01(\x04\x12\x0c\n\x04spec\x18\x04 \x01(\t\x12\x15\n\rorder_unit_id\x18\x05 \x01(\x04\x12\x17\n\x0forder_unit_name\x18\x06 \x01(\t\x12\x17\n\x0forder_unit_code\x18\x07 \x01(\t\x12\x17\n\x0forder_unit_rate\x18\x08 \x01(\t\x12\x14\n\x0cstorage_type\x18\t \x01(\t\x12\x11\n\tsale_type\x18\n \x01(\t\x12\x14\n\x0cproduct_type\x18\x0b \x01(\t\x12\x0f\n\x07min_qty\x18\x0c \x01(\t\x12\x0f\n\x07max_qty\x18\r \x01(\t\x12\x10\n\x08incr_qty\x18\x0e \x01(\t\x12\x13\n\x0b\x61rrival_day\x18\x0f \x01(\t\x12\x15\n\rorder_rule_id\x18\x10 \x01(\x04\x12\x15\n\rlogistic_mode\x18\x11 \x01(\t\x12\x1e\n\x16\x64istribution_center_id\x18\x12 \x01(\x04\x12\x10\n\x08store_id\x18\x13 \x01(\x04\x12\x1b\n\x13order_rule_store_id\x18\x14 \x01(\x04\x12\x12\n\ncycle_type\x18\x15 \x01(\t\x12\x18\n\x10\x61llow_main_order\x18\x16 \x01(\x08\x12\x1f\n\x17receiving_deviation_min\x18\x17 \x01(\t\x12\x1f\n\x17receiving_deviation_max\x18\x18 \x01(\t\x12\x1e\n\x16main_order_arrival_day\x18\x19 \x01(\x03\"\xdc\x01\n/QueryValidStoresForDistributionByProductRequest\x12\x11\n\tstore_ids\x18\x01 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x02 \x03(\x04\x12.\n\norder_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\x0b\x66ilter_type\x18\x04 \x01(\x0e\x32\x19.business_rule.FilterType\x12!\n\x04page\x18\x05 \x01(\x0b\x32\x13.business_rule.Page*\x83\x01\n\x07\x45rrCode\x12\x0b\n\x07Success\x10\x00\x12\x11\n\rDuplicateCode\x10\x01\x12\x11\n\rDuplicateName\x10\x02\x12\x14\n\x10\x44uplicateProduct\x10\x03\x12\x18\n\x14\x44uplicateCodeAndName\x10\x04\x12\x15\n\x11\x44uplicateRelation\x10\x05*N\n\nActionCode\x12\x11\n\rUnknownAction\x10\x00\x12\x13\n\x0fUpdateOrderRule\x10\x01\x12\x18\n\x14\x43reateOrderRuleStore\x10\x02*m\n\rQueryViewType\x12\x0f\n\x0bUnknownView\x10\x00\x12\x18\n\x14OrderRuleProductView\x10\x01\x12\x16\n\x12OrderRuleStoreView\x10\x02\x12\x19\n\x15OrderRuleFullInfoView\x10\x03*D\n\nFilterType\x12\x13\n\x0fOrderRuleFilter\x10\x00\x12\x13\n\x0fMainOrderFilter\x10\x01\x12\x0c\n\x08NoFilter\x10\x02\x32\xff\x0c\n\x11OrderRuleServices\x12^\n\x04Ping\x12\x16.business_rule.Request\x1a\x17.business_rule.Response\"%\x82\xd3\xe4\x93\x02\x1f\"\x1a/api/v1/business-rule/ping:\x01*\x12\x91\x01\n\x0f\x43reateOrderRule\x12%.business_rule.CreateOrderRuleRequest\x1a#.business_rule.BusinessRuleResponse\"2\x82\xd3\xe4\x93\x02,\"\'/api/v1/business-rule/order-rule/create:\x01*\x12\x91\x01\n\x0fUpdateOrderRule\x12%.business_rule.UpdateOrderRuleRequest\x1a#.business_rule.BusinessRuleResponse\"2\x82\xd3\xe4\x93\x02,\"\'/api/v1/business-rule/order-rule/update:\x01*\x12\xaa\x01\n\x14QueryOrderRuleDetail\x12*.business_rule.QueryOrderRuleDetailRequest\x1a+.business_rule.QueryOrderRuleDetailResponse\"9\x82\xd3\xe4\x93\x02\x33\"./api/v1/business-rule/order-rule/query-by-rule:\x01*\x12\x94\x01\n\x0f\x44\x65leteOrderRule\x12(.business_rule.DeleteBusinessRuleRequest\x1a#.business_rule.BusinessRuleResponse\"2\x82\xd3\xe4\x93\x02,\"\'/api/v1/business-rule/order-rule/delete:\x01*\x12\xa1\x01\n\x14\x43reateOrderRuleStore\x12*.business_rule.CreateOrderRuleStoreRequest\x1a#.business_rule.BusinessRuleResponse\"8\x82\xd3\xe4\x93\x02\x32\"-/api/v1/business-rule/order-rule/store/create:\x01*\x12\xa1\x01\n\x14\x44\x65leteOrderRuleStore\x12*.business_rule.DeleteOrderRuleStoreRequest\x1a#.business_rule.BusinessRuleResponse\"8\x82\xd3\xe4\x93\x02\x32\"-/api/v1/business-rule/order-rule/store/delete:\x01*\x12\xa5\x01\n\x13\x43heckOrderRuleExist\x12).business_rule.CheckOrderRuleExistRequest\x1a*.business_rule.CheckOrderRuleExistResponse\"7\x82\xd3\xe4\x93\x02\x31\",/api/v1/business-rule/order-rule/check-exist:\x01*\x12\xc5\x01\n\x18QueryValidProductByStore\x12..business_rule.QueryValidProductByStoreRequest\x1a/.business_rule.QueryValidProductByStoreResponse\"H\x82\xd3\xe4\x93\x02\x42\"=/api/v1/business-rule/order-rule/query-valid-product-by-store:\x01*\x12\xe5\x01\n(QueryValidStoresForDistributionByProduct\x12>.business_rule.QueryValidStoresForDistributionByProductRequest\x1a/.business_rule.QueryValidProductByStoreResponse\"H\x82\xd3\xe4\x93\x02\x42\"=/api/v1/business-rule/order-rule/query-valid-store-by-product:\x01*B\x11Z\x0f./business_ruleb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])

_ERRCODE = _descriptor.EnumDescriptor(
  name='ErrCode',
  full_name='business_rule.ErrCode',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='Success', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DuplicateCode', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DuplicateName', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DuplicateProduct', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DuplicateCodeAndName', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DuplicateRelation', index=5, number=5,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4633,
  serialized_end=4764,
)
_sym_db.RegisterEnumDescriptor(_ERRCODE)

ErrCode = enum_type_wrapper.EnumTypeWrapper(_ERRCODE)
_ACTIONCODE = _descriptor.EnumDescriptor(
  name='ActionCode',
  full_name='business_rule.ActionCode',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UnknownAction', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='UpdateOrderRule', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CreateOrderRuleStore', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4766,
  serialized_end=4844,
)
_sym_db.RegisterEnumDescriptor(_ACTIONCODE)

ActionCode = enum_type_wrapper.EnumTypeWrapper(_ACTIONCODE)
_QUERYVIEWTYPE = _descriptor.EnumDescriptor(
  name='QueryViewType',
  full_name='business_rule.QueryViewType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UnknownView', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderRuleProductView', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderRuleStoreView', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='OrderRuleFullInfoView', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4846,
  serialized_end=4955,
)
_sym_db.RegisterEnumDescriptor(_QUERYVIEWTYPE)

QueryViewType = enum_type_wrapper.EnumTypeWrapper(_QUERYVIEWTYPE)
_FILTERTYPE = _descriptor.EnumDescriptor(
  name='FilterType',
  full_name='business_rule.FilterType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='OrderRuleFilter', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MainOrderFilter', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='NoFilter', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4957,
  serialized_end=5025,
)
_sym_db.RegisterEnumDescriptor(_FILTERTYPE)

FilterType = enum_type_wrapper.EnumTypeWrapper(_FILTERTYPE)
Success = 0
DuplicateCode = 1
DuplicateName = 2
DuplicateProduct = 3
DuplicateCodeAndName = 4
DuplicateRelation = 5
UnknownAction = 0
UpdateOrderRule = 1
CreateOrderRuleStore = 2
UnknownView = 0
OrderRuleProductView = 1
OrderRuleStoreView = 2
OrderRuleFullInfoView = 3
OrderRuleFilter = 0
MainOrderFilter = 1
NoFilter = 2



_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='business_rule.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ping', full_name='business_rule.Request.ping', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=115,
  serialized_end=138,
)


_RESPONSE = _descriptor.Descriptor(
  name='Response',
  full_name='business_rule.Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='business_rule.Response.code', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='message', full_name='business_rule.Response.message', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=140,
  serialized_end=181,
)


_PAGE = _descriptor.Descriptor(
  name='Page',
  full_name='business_rule.Page',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order', full_name='business_rule.Page.order', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='business_rule.Page.sort', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='business_rule.Page.limit', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='business_rule.Page.offset', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=183,
  serialized_end=249,
)


_BUSINESSRULE = _descriptor.Descriptor(
  name='BusinessRule',
  full_name='business_rule.BusinessRule',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='business_rule.BusinessRule.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='business_rule.BusinessRule.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='business_rule.BusinessRule.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rule_type', full_name='business_rule.BusinessRule.rule_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistic_mode', full_name='business_rule.BusinessRule.logistic_mode', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_center_id', full_name='business_rule.BusinessRule.distribution_center_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='business_rule.BusinessRule.status', index=6,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='business_rule.BusinessRule.updated_name', index=7,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='business_rule.BusinessRule.updated', index=8,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_count', full_name='business_rule.BusinessRule.product_count', index=9,
      number=13, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=252,
  serialized_end=486,
)


_ORDERRULEPRODUCT = _descriptor.Descriptor(
  name='OrderRuleProduct',
  full_name='business_rule.OrderRuleProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='business_rule.OrderRuleProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='business_rule.OrderRuleProduct.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='business_rule.OrderRuleProduct.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='business_rule.OrderRuleProduct.product_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_unit_id', full_name='business_rule.OrderRuleProduct.order_unit_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_unit_name', full_name='business_rule.OrderRuleProduct.order_unit_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_qty', full_name='business_rule.OrderRuleProduct.min_qty', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_qty', full_name='business_rule.OrderRuleProduct.max_qty', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='incr_qty', full_name='business_rule.OrderRuleProduct.incr_qty', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cycle_type', full_name='business_rule.OrderRuleProduct.cycle_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='business_rule.OrderRuleProduct.start_date', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='interval_day', full_name='business_rule.OrderRuleProduct.interval_day', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_days', full_name='business_rule.OrderRuleProduct.arrival_days', index=12,
      number=13, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_days', full_name='business_rule.OrderRuleProduct.order_days', index=13,
      number=14, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_main_order', full_name='business_rule.OrderRuleProduct.allow_main_order', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='business_rule.OrderRuleProduct.status', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_deviation_min', full_name='business_rule.OrderRuleProduct.receiving_deviation_min', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_deviation_max', full_name='business_rule.OrderRuleProduct.receiving_deviation_max', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_order_arrival_day', full_name='business_rule.OrderRuleProduct.main_order_arrival_day', index=18,
      number=19, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=489,
  serialized_end=927,
)


_CREATEORDERRULEREQUEST = _descriptor.Descriptor(
  name='CreateOrderRuleRequest',
  full_name='business_rule.CreateOrderRuleRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='business_rule', full_name='business_rule.CreateOrderRuleRequest.business_rule', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_rule_products', full_name='business_rule.CreateOrderRuleRequest.order_rule_products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=930,
  serialized_end=1068,
)


_UPDATEORDERRULEREQUEST = _descriptor.Descriptor(
  name='UpdateOrderRuleRequest',
  full_name='business_rule.UpdateOrderRuleRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='business_rule', full_name='business_rule.UpdateOrderRuleRequest.business_rule', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_rule_products', full_name='business_rule.UpdateOrderRuleRequest.order_rule_products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='del_item_ids', full_name='business_rule.UpdateOrderRuleRequest.del_item_ids', index=2,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1071,
  serialized_end=1231,
)


_QUERYORDERRULEDETAILREQUEST = _descriptor.Descriptor(
  name='QueryOrderRuleDetailRequest',
  full_name='business_rule.QueryOrderRuleDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_rule_ids', full_name='business_rule.QueryOrderRuleDetailRequest.order_rule_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='business_rule.QueryOrderRuleDetailRequest.ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='business_rule.QueryOrderRuleDetailRequest.product_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_status', full_name='business_rule.QueryOrderRuleDetailRequest.product_status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistic_mode', full_name='business_rule.QueryOrderRuleDetailRequest.logistic_mode', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='business_rule.QueryOrderRuleDetailRequest.store_ids', index=5,
      number=6, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_status', full_name='business_rule.QueryOrderRuleDetailRequest.store_status', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rule_status', full_name='business_rule.QueryOrderRuleDetailRequest.rule_status', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='business_rule.QueryOrderRuleDetailRequest.page', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='view_type', full_name='business_rule.QueryOrderRuleDetailRequest.view_type', index=9,
      number=10, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_search', full_name='business_rule.QueryOrderRuleDetailRequest.product_search', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rule_search', full_name='business_rule.QueryOrderRuleDetailRequest.rule_search', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1234,
  serialized_end=1559,
)


_QUERYORDERRULEDETAILRESPONSE = _descriptor.Descriptor(
  name='QueryOrderRuleDetailResponse',
  full_name='business_rule.QueryOrderRuleDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='business_rule.QueryOrderRuleDetailResponse.rows', index=0,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='business_rule.QueryOrderRuleDetailResponse.total', index=1,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1561,
  serialized_end=1661,
)


_ORDERRULEPRODUCTFULLINFO = _descriptor.Descriptor(
  name='OrderRuleProductFullInfo',
  full_name='business_rule.OrderRuleProductFullInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_rule_product_id', full_name='business_rule.OrderRuleProductFullInfo.order_rule_product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='business_rule.OrderRuleProductFullInfo.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='business_rule.OrderRuleProductFullInfo.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='business_rule.OrderRuleProductFullInfo.product_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_unit_id', full_name='business_rule.OrderRuleProductFullInfo.order_unit_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_unit_name', full_name='business_rule.OrderRuleProductFullInfo.order_unit_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_qty', full_name='business_rule.OrderRuleProductFullInfo.min_qty', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_qty', full_name='business_rule.OrderRuleProductFullInfo.max_qty', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='incr_qty', full_name='business_rule.OrderRuleProductFullInfo.incr_qty', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cycle_type', full_name='business_rule.OrderRuleProductFullInfo.cycle_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='business_rule.OrderRuleProductFullInfo.start_date', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_days', full_name='business_rule.OrderRuleProductFullInfo.arrival_days', index=11,
      number=12, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_days', full_name='business_rule.OrderRuleProductFullInfo.order_days', index=12,
      number=13, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_rule_id', full_name='business_rule.OrderRuleProductFullInfo.order_rule_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_rule_name', full_name='business_rule.OrderRuleProductFullInfo.order_rule_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_rule_code', full_name='business_rule.OrderRuleProductFullInfo.order_rule_code', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistic_mode', full_name='business_rule.OrderRuleProductFullInfo.logistic_mode', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_center_id', full_name='business_rule.OrderRuleProductFullInfo.distribution_center_id', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_center_name', full_name='business_rule.OrderRuleProductFullInfo.distribution_center_name', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_center_code', full_name='business_rule.OrderRuleProductFullInfo.distribution_center_code', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='business_rule.OrderRuleProductFullInfo.status', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='business_rule.OrderRuleProductFullInfo.store_id', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='business_rule.OrderRuleProductFullInfo.store_name', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='business_rule.OrderRuleProductFullInfo.store_code', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_status', full_name='business_rule.OrderRuleProductFullInfo.store_status', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_rule_store_id', full_name='business_rule.OrderRuleProductFullInfo.order_rule_store_id', index=25,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='interval_day', full_name='business_rule.OrderRuleProductFullInfo.interval_day', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='business_rule.OrderRuleProductFullInfo.updated_name', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='business_rule.OrderRuleProductFullInfo.updated', index=28,
      number=29, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_main_order', full_name='business_rule.OrderRuleProductFullInfo.allow_main_order', index=29,
      number=30, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_status', full_name='business_rule.OrderRuleProductFullInfo.product_status', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_deviation_min', full_name='business_rule.OrderRuleProductFullInfo.receiving_deviation_min', index=31,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_deviation_max', full_name='business_rule.OrderRuleProductFullInfo.receiving_deviation_max', index=32,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_order_arrival_day', full_name='business_rule.OrderRuleProductFullInfo.main_order_arrival_day', index=33,
      number=34, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1664,
  serialized_end=2525,
)


_BUSINESSRULERESPONSE = _descriptor.Descriptor(
  name='BusinessRuleResponse',
  full_name='business_rule.BusinessRuleResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='business_rule.BusinessRuleResponse.code', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='message', full_name='business_rule.BusinessRuleResponse.message', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='business_rule.BusinessRuleResponse.total', index=2,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2527,
  serialized_end=2619,
)


_DELETEBUSINESSRULEREQUEST = _descriptor.Descriptor(
  name='DeleteBusinessRuleRequest',
  full_name='business_rule.DeleteBusinessRuleRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='business_rule.DeleteBusinessRuleRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2621,
  serialized_end=2660,
)


_ORDERRULESTORE = _descriptor.Descriptor(
  name='OrderRuleStore',
  full_name='business_rule.OrderRuleStore',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='business_rule.OrderRuleStore.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='business_rule.OrderRuleStore.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2662,
  serialized_end=2708,
)


_CREATEORDERRULESTOREREQUEST = _descriptor.Descriptor(
  name='CreateOrderRuleStoreRequest',
  full_name='business_rule.CreateOrderRuleStoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_rule_ids', full_name='business_rule.CreateOrderRuleStoreRequest.order_rule_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_rule_stores', full_name='business_rule.CreateOrderRuleStoreRequest.order_rule_stores', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2710,
  serialized_end=2821,
)


_DELETEORDERRULESTOREREQUEST = _descriptor.Descriptor(
  name='DeleteOrderRuleStoreRequest',
  full_name='business_rule.DeleteOrderRuleStoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='ids', full_name='business_rule.DeleteOrderRuleStoreRequest.ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2823,
  serialized_end=2865,
)


_CHECKORDERRULEEXISTREQUEST = _descriptor.Descriptor(
  name='CheckOrderRuleExistRequest',
  full_name='business_rule.CheckOrderRuleExistRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='update_order_rule_req', full_name='business_rule.CheckOrderRuleExistRequest.update_order_rule_req', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='create_order_rule_store_req', full_name='business_rule.CheckOrderRuleExistRequest.create_order_rule_store_req', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='business_rule.CheckOrderRuleExistRequest.action', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='business_rule.CheckOrderRuleExistRequest.page', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2868,
  serialized_end=3125,
)


_CHECKORDERRULEEXISTRESPONSE = _descriptor.Descriptor(
  name='CheckOrderRuleExistResponse',
  full_name='business_rule.CheckOrderRuleExistResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='business_rule.CheckOrderRuleExistResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='business_rule.CheckOrderRuleExistResponse.total', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3127,
  serialized_end=3226,
)


_QUERYORDERRULEDETAILBYSTOREREQUEST = _descriptor.Descriptor(
  name='QueryOrderRuleDetailByStoreRequest',
  full_name='business_rule.QueryOrderRuleDetailByStoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_rule_ids', full_name='business_rule.QueryOrderRuleDetailByStoreRequest.order_rule_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='business_rule.QueryOrderRuleDetailByStoreRequest.ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='business_rule.QueryOrderRuleDetailByStoreRequest.product_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_status', full_name='business_rule.QueryOrderRuleDetailByStoreRequest.product_status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistic_mode', full_name='business_rule.QueryOrderRuleDetailByStoreRequest.logistic_mode', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='business_rule.QueryOrderRuleDetailByStoreRequest.store_ids', index=5,
      number=6, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_status', full_name='business_rule.QueryOrderRuleDetailByStoreRequest.store_status', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rule_status', full_name='business_rule.QueryOrderRuleDetailByStoreRequest.rule_status', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='business_rule.QueryOrderRuleDetailByStoreRequest.page', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3229,
  serialized_end=3467,
)


_QUERYVALIDPRODUCTBYSTOREREQUEST = _descriptor.Descriptor(
  name='QueryValidProductByStoreRequest',
  full_name='business_rule.QueryValidProductByStoreRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='business_rule.QueryValidProductByStoreRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='business_rule.QueryValidProductByStoreRequest.product_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date', full_name='business_rule.QueryValidProductByStoreRequest.order_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filter_type', full_name='business_rule.QueryValidProductByStoreRequest.filter_type', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='business_rule.QueryValidProductByStoreRequest.page', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3470,
  serialized_end=3673,
)


_QUERYVALIDPRODUCTBYSTORERESPONSE = _descriptor.Descriptor(
  name='QueryValidProductByStoreResponse',
  full_name='business_rule.QueryValidProductByStoreResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='business_rule.QueryValidProductByStoreResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='business_rule.QueryValidProductByStoreResponse.total', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3675,
  serialized_end=3780,
)


_ORDERRULEPRODUCTBASICINFO = _descriptor.Descriptor(
  name='OrderRuleProductBasicInfo',
  full_name='business_rule.OrderRuleProductBasicInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='order_rule_product_id', full_name='business_rule.OrderRuleProductBasicInfo.order_rule_product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='business_rule.OrderRuleProductBasicInfo.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_category_id', full_name='business_rule.OrderRuleProductBasicInfo.product_category_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='business_rule.OrderRuleProductBasicInfo.spec', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_unit_id', full_name='business_rule.OrderRuleProductBasicInfo.order_unit_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_unit_name', full_name='business_rule.OrderRuleProductBasicInfo.order_unit_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_unit_code', full_name='business_rule.OrderRuleProductBasicInfo.order_unit_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_unit_rate', full_name='business_rule.OrderRuleProductBasicInfo.order_unit_rate', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='business_rule.OrderRuleProductBasicInfo.storage_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sale_type', full_name='business_rule.OrderRuleProductBasicInfo.sale_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='business_rule.OrderRuleProductBasicInfo.product_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='min_qty', full_name='business_rule.OrderRuleProductBasicInfo.min_qty', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='max_qty', full_name='business_rule.OrderRuleProductBasicInfo.max_qty', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='incr_qty', full_name='business_rule.OrderRuleProductBasicInfo.incr_qty', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='arrival_day', full_name='business_rule.OrderRuleProductBasicInfo.arrival_day', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_rule_id', full_name='business_rule.OrderRuleProductBasicInfo.order_rule_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistic_mode', full_name='business_rule.OrderRuleProductBasicInfo.logistic_mode', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_center_id', full_name='business_rule.OrderRuleProductBasicInfo.distribution_center_id', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='business_rule.OrderRuleProductBasicInfo.store_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_rule_store_id', full_name='business_rule.OrderRuleProductBasicInfo.order_rule_store_id', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cycle_type', full_name='business_rule.OrderRuleProductBasicInfo.cycle_type', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_main_order', full_name='business_rule.OrderRuleProductBasicInfo.allow_main_order', index=21,
      number=22, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_deviation_min', full_name='business_rule.OrderRuleProductBasicInfo.receiving_deviation_min', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_deviation_max', full_name='business_rule.OrderRuleProductBasicInfo.receiving_deviation_max', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_order_arrival_day', full_name='business_rule.OrderRuleProductBasicInfo.main_order_arrival_day', index=24,
      number=25, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3783,
  serialized_end=4407,
)


_QUERYVALIDSTORESFORDISTRIBUTIONBYPRODUCTREQUEST = _descriptor.Descriptor(
  name='QueryValidStoresForDistributionByProductRequest',
  full_name='business_rule.QueryValidStoresForDistributionByProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='business_rule.QueryValidStoresForDistributionByProductRequest.store_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='business_rule.QueryValidStoresForDistributionByProductRequest.product_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_date', full_name='business_rule.QueryValidStoresForDistributionByProductRequest.order_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filter_type', full_name='business_rule.QueryValidStoresForDistributionByProductRequest.filter_type', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='business_rule.QueryValidStoresForDistributionByProductRequest.page', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4410,
  serialized_end=4630,
)

_BUSINESSRULE.fields_by_name['updated'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEORDERRULEREQUEST.fields_by_name['business_rule'].message_type = _BUSINESSRULE
_CREATEORDERRULEREQUEST.fields_by_name['order_rule_products'].message_type = _ORDERRULEPRODUCT
_UPDATEORDERRULEREQUEST.fields_by_name['business_rule'].message_type = _BUSINESSRULE
_UPDATEORDERRULEREQUEST.fields_by_name['order_rule_products'].message_type = _ORDERRULEPRODUCT
_QUERYORDERRULEDETAILREQUEST.fields_by_name['page'].message_type = _PAGE
_QUERYORDERRULEDETAILREQUEST.fields_by_name['view_type'].enum_type = _QUERYVIEWTYPE
_QUERYORDERRULEDETAILRESPONSE.fields_by_name['rows'].message_type = _ORDERRULEPRODUCTFULLINFO
_ORDERRULEPRODUCTFULLINFO.fields_by_name['updated'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_BUSINESSRULERESPONSE.fields_by_name['code'].enum_type = _ERRCODE
_CREATEORDERRULESTOREREQUEST.fields_by_name['order_rule_stores'].message_type = _ORDERRULESTORE
_CHECKORDERRULEEXISTREQUEST.fields_by_name['update_order_rule_req'].message_type = _UPDATEORDERRULEREQUEST
_CHECKORDERRULEEXISTREQUEST.fields_by_name['create_order_rule_store_req'].message_type = _CREATEORDERRULESTOREREQUEST
_CHECKORDERRULEEXISTREQUEST.fields_by_name['action'].enum_type = _ACTIONCODE
_CHECKORDERRULEEXISTREQUEST.fields_by_name['page'].message_type = _PAGE
_CHECKORDERRULEEXISTRESPONSE.fields_by_name['rows'].message_type = _ORDERRULEPRODUCTFULLINFO
_QUERYORDERRULEDETAILBYSTOREREQUEST.fields_by_name['page'].message_type = _PAGE
_QUERYVALIDPRODUCTBYSTOREREQUEST.fields_by_name['order_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYVALIDPRODUCTBYSTOREREQUEST.fields_by_name['filter_type'].enum_type = _FILTERTYPE
_QUERYVALIDPRODUCTBYSTOREREQUEST.fields_by_name['page'].message_type = _PAGE
_QUERYVALIDPRODUCTBYSTORERESPONSE.fields_by_name['rows'].message_type = _ORDERRULEPRODUCTBASICINFO
_QUERYVALIDSTORESFORDISTRIBUTIONBYPRODUCTREQUEST.fields_by_name['order_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYVALIDSTORESFORDISTRIBUTIONBYPRODUCTREQUEST.fields_by_name['filter_type'].enum_type = _FILTERTYPE
_QUERYVALIDSTORESFORDISTRIBUTIONBYPRODUCTREQUEST.fields_by_name['page'].message_type = _PAGE
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Response'] = _RESPONSE
DESCRIPTOR.message_types_by_name['Page'] = _PAGE
DESCRIPTOR.message_types_by_name['BusinessRule'] = _BUSINESSRULE
DESCRIPTOR.message_types_by_name['OrderRuleProduct'] = _ORDERRULEPRODUCT
DESCRIPTOR.message_types_by_name['CreateOrderRuleRequest'] = _CREATEORDERRULEREQUEST
DESCRIPTOR.message_types_by_name['UpdateOrderRuleRequest'] = _UPDATEORDERRULEREQUEST
DESCRIPTOR.message_types_by_name['QueryOrderRuleDetailRequest'] = _QUERYORDERRULEDETAILREQUEST
DESCRIPTOR.message_types_by_name['QueryOrderRuleDetailResponse'] = _QUERYORDERRULEDETAILRESPONSE
DESCRIPTOR.message_types_by_name['OrderRuleProductFullInfo'] = _ORDERRULEPRODUCTFULLINFO
DESCRIPTOR.message_types_by_name['BusinessRuleResponse'] = _BUSINESSRULERESPONSE
DESCRIPTOR.message_types_by_name['DeleteBusinessRuleRequest'] = _DELETEBUSINESSRULEREQUEST
DESCRIPTOR.message_types_by_name['OrderRuleStore'] = _ORDERRULESTORE
DESCRIPTOR.message_types_by_name['CreateOrderRuleStoreRequest'] = _CREATEORDERRULESTOREREQUEST
DESCRIPTOR.message_types_by_name['DeleteOrderRuleStoreRequest'] = _DELETEORDERRULESTOREREQUEST
DESCRIPTOR.message_types_by_name['CheckOrderRuleExistRequest'] = _CHECKORDERRULEEXISTREQUEST
DESCRIPTOR.message_types_by_name['CheckOrderRuleExistResponse'] = _CHECKORDERRULEEXISTRESPONSE
DESCRIPTOR.message_types_by_name['QueryOrderRuleDetailByStoreRequest'] = _QUERYORDERRULEDETAILBYSTOREREQUEST
DESCRIPTOR.message_types_by_name['QueryValidProductByStoreRequest'] = _QUERYVALIDPRODUCTBYSTOREREQUEST
DESCRIPTOR.message_types_by_name['QueryValidProductByStoreResponse'] = _QUERYVALIDPRODUCTBYSTORERESPONSE
DESCRIPTOR.message_types_by_name['OrderRuleProductBasicInfo'] = _ORDERRULEPRODUCTBASICINFO
DESCRIPTOR.message_types_by_name['QueryValidStoresForDistributionByProductRequest'] = _QUERYVALIDSTORESFORDISTRIBUTIONBYPRODUCTREQUEST
DESCRIPTOR.enum_types_by_name['ErrCode'] = _ERRCODE
DESCRIPTOR.enum_types_by_name['ActionCode'] = _ACTIONCODE
DESCRIPTOR.enum_types_by_name['QueryViewType'] = _QUERYVIEWTYPE
DESCRIPTOR.enum_types_by_name['FilterType'] = _FILTERTYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), dict(
  DESCRIPTOR = _REQUEST,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.Request)
  ))
_sym_db.RegisterMessage(Request)

Response = _reflection.GeneratedProtocolMessageType('Response', (_message.Message,), dict(
  DESCRIPTOR = _RESPONSE,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.Response)
  ))
_sym_db.RegisterMessage(Response)

Page = _reflection.GeneratedProtocolMessageType('Page', (_message.Message,), dict(
  DESCRIPTOR = _PAGE,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.Page)
  ))
_sym_db.RegisterMessage(Page)

BusinessRule = _reflection.GeneratedProtocolMessageType('BusinessRule', (_message.Message,), dict(
  DESCRIPTOR = _BUSINESSRULE,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.BusinessRule)
  ))
_sym_db.RegisterMessage(BusinessRule)

OrderRuleProduct = _reflection.GeneratedProtocolMessageType('OrderRuleProduct', (_message.Message,), dict(
  DESCRIPTOR = _ORDERRULEPRODUCT,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.OrderRuleProduct)
  ))
_sym_db.RegisterMessage(OrderRuleProduct)

CreateOrderRuleRequest = _reflection.GeneratedProtocolMessageType('CreateOrderRuleRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEORDERRULEREQUEST,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.CreateOrderRuleRequest)
  ))
_sym_db.RegisterMessage(CreateOrderRuleRequest)

UpdateOrderRuleRequest = _reflection.GeneratedProtocolMessageType('UpdateOrderRuleRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEORDERRULEREQUEST,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.UpdateOrderRuleRequest)
  ))
_sym_db.RegisterMessage(UpdateOrderRuleRequest)

QueryOrderRuleDetailRequest = _reflection.GeneratedProtocolMessageType('QueryOrderRuleDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYORDERRULEDETAILREQUEST,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.QueryOrderRuleDetailRequest)
  ))
_sym_db.RegisterMessage(QueryOrderRuleDetailRequest)

QueryOrderRuleDetailResponse = _reflection.GeneratedProtocolMessageType('QueryOrderRuleDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYORDERRULEDETAILRESPONSE,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.QueryOrderRuleDetailResponse)
  ))
_sym_db.RegisterMessage(QueryOrderRuleDetailResponse)

OrderRuleProductFullInfo = _reflection.GeneratedProtocolMessageType('OrderRuleProductFullInfo', (_message.Message,), dict(
  DESCRIPTOR = _ORDERRULEPRODUCTFULLINFO,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.OrderRuleProductFullInfo)
  ))
_sym_db.RegisterMessage(OrderRuleProductFullInfo)

BusinessRuleResponse = _reflection.GeneratedProtocolMessageType('BusinessRuleResponse', (_message.Message,), dict(
  DESCRIPTOR = _BUSINESSRULERESPONSE,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.BusinessRuleResponse)
  ))
_sym_db.RegisterMessage(BusinessRuleResponse)

DeleteBusinessRuleRequest = _reflection.GeneratedProtocolMessageType('DeleteBusinessRuleRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEBUSINESSRULEREQUEST,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.DeleteBusinessRuleRequest)
  ))
_sym_db.RegisterMessage(DeleteBusinessRuleRequest)

OrderRuleStore = _reflection.GeneratedProtocolMessageType('OrderRuleStore', (_message.Message,), dict(
  DESCRIPTOR = _ORDERRULESTORE,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.OrderRuleStore)
  ))
_sym_db.RegisterMessage(OrderRuleStore)

CreateOrderRuleStoreRequest = _reflection.GeneratedProtocolMessageType('CreateOrderRuleStoreRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEORDERRULESTOREREQUEST,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.CreateOrderRuleStoreRequest)
  ))
_sym_db.RegisterMessage(CreateOrderRuleStoreRequest)

DeleteOrderRuleStoreRequest = _reflection.GeneratedProtocolMessageType('DeleteOrderRuleStoreRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEORDERRULESTOREREQUEST,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.DeleteOrderRuleStoreRequest)
  ))
_sym_db.RegisterMessage(DeleteOrderRuleStoreRequest)

CheckOrderRuleExistRequest = _reflection.GeneratedProtocolMessageType('CheckOrderRuleExistRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHECKORDERRULEEXISTREQUEST,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.CheckOrderRuleExistRequest)
  ))
_sym_db.RegisterMessage(CheckOrderRuleExistRequest)

CheckOrderRuleExistResponse = _reflection.GeneratedProtocolMessageType('CheckOrderRuleExistResponse', (_message.Message,), dict(
  DESCRIPTOR = _CHECKORDERRULEEXISTRESPONSE,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.CheckOrderRuleExistResponse)
  ))
_sym_db.RegisterMessage(CheckOrderRuleExistResponse)

QueryOrderRuleDetailByStoreRequest = _reflection.GeneratedProtocolMessageType('QueryOrderRuleDetailByStoreRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYORDERRULEDETAILBYSTOREREQUEST,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.QueryOrderRuleDetailByStoreRequest)
  ))
_sym_db.RegisterMessage(QueryOrderRuleDetailByStoreRequest)

QueryValidProductByStoreRequest = _reflection.GeneratedProtocolMessageType('QueryValidProductByStoreRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYVALIDPRODUCTBYSTOREREQUEST,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.QueryValidProductByStoreRequest)
  ))
_sym_db.RegisterMessage(QueryValidProductByStoreRequest)

QueryValidProductByStoreResponse = _reflection.GeneratedProtocolMessageType('QueryValidProductByStoreResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYVALIDPRODUCTBYSTORERESPONSE,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.QueryValidProductByStoreResponse)
  ))
_sym_db.RegisterMessage(QueryValidProductByStoreResponse)

OrderRuleProductBasicInfo = _reflection.GeneratedProtocolMessageType('OrderRuleProductBasicInfo', (_message.Message,), dict(
  DESCRIPTOR = _ORDERRULEPRODUCTBASICINFO,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.OrderRuleProductBasicInfo)
  ))
_sym_db.RegisterMessage(OrderRuleProductBasicInfo)

QueryValidStoresForDistributionByProductRequest = _reflection.GeneratedProtocolMessageType('QueryValidStoresForDistributionByProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYVALIDSTORESFORDISTRIBUTIONBYPRODUCTREQUEST,
  __module__ = 'business_rule.business_rule_pb2'
  # @@protoc_insertion_point(class_scope:business_rule.QueryValidStoresForDistributionByProductRequest)
  ))
_sym_db.RegisterMessage(QueryValidStoresForDistributionByProductRequest)


DESCRIPTOR._options = None

_ORDERRULESERVICES = _descriptor.ServiceDescriptor(
  name='OrderRuleServices',
  full_name='business_rule.OrderRuleServices',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=5028,
  serialized_end=6691,
  methods=[
  _descriptor.MethodDescriptor(
    name='Ping',
    full_name='business_rule.OrderRuleServices.Ping',
    index=0,
    containing_service=None,
    input_type=_REQUEST,
    output_type=_RESPONSE,
    serialized_options=_b('\202\323\344\223\002\037\"\032/api/v1/business-rule/ping:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CreateOrderRule',
    full_name='business_rule.OrderRuleServices.CreateOrderRule',
    index=1,
    containing_service=None,
    input_type=_CREATEORDERRULEREQUEST,
    output_type=_BUSINESSRULERESPONSE,
    serialized_options=_b('\202\323\344\223\002,\"\'/api/v1/business-rule/order-rule/create:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateOrderRule',
    full_name='business_rule.OrderRuleServices.UpdateOrderRule',
    index=2,
    containing_service=None,
    input_type=_UPDATEORDERRULEREQUEST,
    output_type=_BUSINESSRULERESPONSE,
    serialized_options=_b('\202\323\344\223\002,\"\'/api/v1/business-rule/order-rule/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryOrderRuleDetail',
    full_name='business_rule.OrderRuleServices.QueryOrderRuleDetail',
    index=3,
    containing_service=None,
    input_type=_QUERYORDERRULEDETAILREQUEST,
    output_type=_QUERYORDERRULEDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\0023\"./api/v1/business-rule/order-rule/query-by-rule:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteOrderRule',
    full_name='business_rule.OrderRuleServices.DeleteOrderRule',
    index=4,
    containing_service=None,
    input_type=_DELETEBUSINESSRULEREQUEST,
    output_type=_BUSINESSRULERESPONSE,
    serialized_options=_b('\202\323\344\223\002,\"\'/api/v1/business-rule/order-rule/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CreateOrderRuleStore',
    full_name='business_rule.OrderRuleServices.CreateOrderRuleStore',
    index=5,
    containing_service=None,
    input_type=_CREATEORDERRULESTOREREQUEST,
    output_type=_BUSINESSRULERESPONSE,
    serialized_options=_b('\202\323\344\223\0022\"-/api/v1/business-rule/order-rule/store/create:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteOrderRuleStore',
    full_name='business_rule.OrderRuleServices.DeleteOrderRuleStore',
    index=6,
    containing_service=None,
    input_type=_DELETEORDERRULESTOREREQUEST,
    output_type=_BUSINESSRULERESPONSE,
    serialized_options=_b('\202\323\344\223\0022\"-/api/v1/business-rule/order-rule/store/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CheckOrderRuleExist',
    full_name='business_rule.OrderRuleServices.CheckOrderRuleExist',
    index=7,
    containing_service=None,
    input_type=_CHECKORDERRULEEXISTREQUEST,
    output_type=_CHECKORDERRULEEXISTRESPONSE,
    serialized_options=_b('\202\323\344\223\0021\",/api/v1/business-rule/order-rule/check-exist:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryValidProductByStore',
    full_name='business_rule.OrderRuleServices.QueryValidProductByStore',
    index=8,
    containing_service=None,
    input_type=_QUERYVALIDPRODUCTBYSTOREREQUEST,
    output_type=_QUERYVALIDPRODUCTBYSTORERESPONSE,
    serialized_options=_b('\202\323\344\223\002B\"=/api/v1/business-rule/order-rule/query-valid-product-by-store:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryValidStoresForDistributionByProduct',
    full_name='business_rule.OrderRuleServices.QueryValidStoresForDistributionByProduct',
    index=9,
    containing_service=None,
    input_type=_QUERYVALIDSTORESFORDISTRIBUTIONBYPRODUCTREQUEST,
    output_type=_QUERYVALIDPRODUCTBYSTORERESPONSE,
    serialized_options=_b('\202\323\344\223\002B\"=/api/v1/business-rule/order-rule/query-valid-store-by-product:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_ORDERRULESERVICES)

DESCRIPTOR.services_by_name['OrderRuleServices'] = _ORDERRULESERVICES

# @@protoc_insertion_point(module_scope)
