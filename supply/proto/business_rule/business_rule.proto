syntax = "proto3";
package business_rule;
option go_package = "./business_rule";
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";


service OrderRuleServices {
    rpc Ping (Request) returns (Response) {
        option (google.api.http) = {
      post:"/api/v1/business-rule/ping"
      body:"*"
    };
    };
    // 创建订货规则
    rpc CreateOrderRule (CreateOrderRuleRequest) returns (BusinessRuleResponse) {
        option (google.api.http) = {
      post:"/api/v1/business-rule/order-rule/create"
      body:"*"
    };
    };

    // 更新订货规则
    rpc UpdateOrderRule (UpdateOrderRuleRequest) returns (BusinessRuleResponse) {
        option (google.api.http) = {
      post:"/api/v1/business-rule/order-rule/update"
      body:"*"
    };
    };

    // 查询订货规则详情规则(多规则视角，web查询使用)
    rpc QueryOrderRuleDetail (QueryOrderRuleDetailRequest) returns (QueryOrderRuleDetailResponse) {
        option (google.api.http) = {
      post:"/api/v1/business-rule/order-rule/query-by-rule"
      body:"*"
    };
    };

    //删除订货规则
    rpc DeleteOrderRule (DeleteBusinessRuleRequest) returns (BusinessRuleResponse) {
        option (google.api.http) = {
      post:"/api/v1/business-rule/order-rule/delete"
      body:"*"
    };
    };

    // 创建关联关系(规则和门店关联)
    rpc CreateOrderRuleStore (CreateOrderRuleStoreRequest) returns (BusinessRuleResponse) {
        option (google.api.http) = {
      post:"/api/v1/business-rule/order-rule/store/create"
      body:"*"
    };
    };
    // 移除关联关系(规则和门店关联)
    rpc DeleteOrderRuleStore (DeleteOrderRuleStoreRequest) returns (BusinessRuleResponse) {
        option (google.api.http) = {
      post:"/api/v1/business-rule/order-rule/store/delete"
      body:"*"
    };
    };

    // 订货规则数据(门店/商品)是否已经存在
    rpc CheckOrderRuleExist (CheckOrderRuleExistRequest) returns (CheckOrderRuleExistResponse) {
        option (google.api.http) = {
      post:"/api/v1/business-rule/order-rule/check-exist"
      body:"*"
    };
    };

    // 查询门店可订货商品（其他业务调用）
    rpc QueryValidProductByStore (QueryValidProductByStoreRequest) returns (QueryValidProductByStoreResponse) {
        option (google.api.http) = {
      post:"/api/v1/business-rule/order-rule/query-valid-product-by-store"
      body:"*"
    };
    };

    rpc QueryValidStoresForDistributionByProduct (QueryValidStoresForDistributionByProductRequest) returns (QueryValidProductByStoreResponse) {
        option (google.api.http) = {
      post: "/api/v1/business-rule/order-rule/query-valid-store-by-product"
      body: "*"
    };
    };
}

message Request {
    string ping = 1;
}
message Response {
    uint32 code = 1;
    string message = 2;
}
message Page {
    string order = 1;
    string sort = 2;
    int32 limit = 3;
    int32 offset = 4;
}

enum ErrCode {
    Success = 0;
    DuplicateCode = 1;
    DuplicateName = 2;
    DuplicateProduct = 3;
    DuplicateCodeAndName = 4;
    DuplicateRelation = 5;
}
message BusinessRule {
    // 规则ID
    uint64 id = 1;
    // 规则名称
    // @gotags: validate:"required"
    string name = 2;
    // 规则编码
    // @gotags: validate:"required"
    string code = 3;
    // 规则类型
    // @gotags: validate:"required"
    string rule_type = 4;
    // 物流模式
    // @gotags: validate:"required"
    string logistic_mode = 5;
    // 仓库/供应商ID
    // @gotags: validate:"required"
    uint64 distribution_center_id = 6;
    // 状态
    string status = 8;
    // 更新人名称
    string updated_name = 11;
    // 更新时间
    google.protobuf.Timestamp updated = 12;
    // 商品数量
    int64 product_count = 13;
}

message OrderRuleProduct {
    uint64 id = 1;
    // 产品ID
    // @gotags: validate:"required"
    uint64 product_id = 2;
    // 产品名称
    string product_name = 3;
    // 产品编码
    string product_code = 4;
    // 订货单位ID
    // @gotags: validate:"required"
    uint64 order_unit_id = 5;
    // 订货单位名称
    string order_unit_name = 6;
    // 最小订量
    // @gotags: validate:"required"
    string min_qty = 7;
    // 最大订量
    // @gotags: validate:"required"
    string max_qty = 8;
    // 递增定量
    // @gotags: validate:"required"
    string incr_qty = 9;
    // 循环类型(日:d 周:w 月:m)
    // @gotags: validate:"required,oneof=d w m"
    string cycle_type = 10;
    // 按天循环开始日期(循环类型为天时必填)
    // @gotags: validate:"required_if=CycleType d"
    string start_date = 11;
    // 间隔天数
    // @gotags: validate:"required_if=CycleType d"
    string interval_day = 12;
    // 预计到货天数
    // @gotags: validate:"required,dive,number"
    repeated string arrival_days = 13;
    // 循环订货日期
    // @gotags: validate:"unique,required_unless=CycleType d,dive,number"
    repeated string order_days = 14;
    // 是否允许主配
    bool allow_main_order = 15;
    string status = 16;
    // 收货偏差最小值
    string receiving_deviation_min = 17;
    // 收货偏差最大值
    string receiving_deviation_max = 18;
    // 主配到货天数
    // @gotags: validate:"required"
    int64 main_order_arrival_day = 19;
}

message CreateOrderRuleRequest {
    // 订货规则主表
    // @gotags: validate:"required"
    BusinessRule business_rule = 1;
    // 订货规则商品
    // @gotags: validate:"required,unique=ProductId,dive"
    repeated OrderRuleProduct order_rule_products = 2;
}

message UpdateOrderRuleRequest {
    // 订货规则主表
    // @gotags: validate:"required"
    BusinessRule business_rule = 1;
    // 订货规则商品
    // @gotags: validate:"unique=ProductId,dive,required"
    repeated OrderRuleProduct order_rule_products = 2;
    // 需要删除记录id
    repeated uint64 del_item_ids = 4;
}

message QueryOrderRuleDetailRequest {
    // 订货规则ID
    repeated uint64 order_rule_ids = 1;
    // 数据id
    repeated uint64 ids = 2;
    // 商品ID
    repeated uint64 product_ids = 3;
    // 商品状态
    string product_status = 4;
    // 物流模式
    string logistic_mode = 5;
    // 门店ID
    repeated uint64 store_ids = 6;
    //门店状态
    string store_status = 7;
    // 订货规则状态：ENABLED/DISABLED
    string rule_status = 8;
    // @gotags: validate:"required"
    Page page = 9;
    // 查询视角
    // @gotags: validate:"required"
    QueryViewType view_type = 10;
    // 按商品模糊查询
    string product_search = 11;
    // 按规则名称模糊查询
    string rule_search = 12;
}

message QueryOrderRuleDetailResponse {

    repeated OrderRuleProductFullInfo rows = 2;
    // 总数
    int64 total = 3;

}


message OrderRuleProductFullInfo {
    // 订货规则商品数据ID
    uint64 order_rule_product_id = 1;
    // 产品ID
    // @gotags: validate:"required"
    uint64 product_id = 2;
    // 产品名称
    string product_name = 3;
    // 产品编码
    string product_code = 4;
    // 订货单位ID
    // @gotags: validate:"required"
    uint64 order_unit_id = 5;
    // 订货单位名称
    string order_unit_name = 6;
    // 最小订量
    // @gotags: validate:"required"
    string min_qty = 7;
    // 最大订量
    // @gotags: validate:"required"
    string max_qty = 8;
    // 递增定量
    // @gotags: validate:"required"
    string incr_qty = 9;
    // 循环类型(日:d 周:w 月:m)
    // @gotags: validate:"required"
    string cycle_type = 10;
    // 按天循环开始日期(循环类型为天时必填)
    // @gotags: validate:"required_if=CycleType d"
    string start_date = 11;
    // 预计到货天数
    // @gotags: validate:"required,number,dive"
    repeated string arrival_days = 12;
    // 循环订货日期
    // @gotags: validate:"required,number,dive,unique"
    repeated string order_days = 13;
    // 订货规则ID
    uint64 order_rule_id = 14;
    // 订货规则名称
    string order_rule_name = 15;
    // 订货规则编码
    string order_rule_code = 16;
    // 物流模式
    string logistic_mode = 17;
    // 仓库/供应商ID
    // @gotags: validate:"required"
    uint64 distribution_center_id = 18;
    // 仓库/供应商名称
    string distribution_center_name = 19;
    // 仓库/供应商编码
    string distribution_center_code = 20;
    // 规则状态
    string status = 21;
    // 门店id
    uint64 store_id = 22;
    // 门店名称
    string store_name = 23;
    // 门店编码
    string store_code = 24;
    // 门店状态
    string store_status = 25;
    //订货规则门店数据ID
    uint64 order_rule_store_id = 26;
    // 间隔天数
    string interval_day = 27;
    // 更新人名称
    string updated_name = 28;
    // 更新时间
    google.protobuf.Timestamp updated = 29;
    //  是否允许主配
    bool allow_main_order = 30;
    // 商品状态
    string product_status = 31;
    // 收货偏差最小值
    string receiving_deviation_min = 32;
    // 收货偏差最大值
    string receiving_deviation_max = 33;
    // 主配到货天数
    int64 main_order_arrival_day = 34;
}


message BusinessRuleResponse {
    // 异常code
    ErrCode code = 1;
    // 异常原因
    string message = 2;
    int64 total = 4;
}
message DeleteBusinessRuleRequest {

    // @gotags: validate:"required"
    uint64 id = 1;
}

message OrderRuleStore {
    // id
    uint64 id = 1;
    // 门店ID
    // @gotags: validate:"required"
    uint64 store_id = 2;
}


message CreateOrderRuleStoreRequest {
    // 订货规则ID
    // @gotags: validate:"required,unique"
    repeated uint64 order_rule_ids = 1;
    // 门店ID
    // @gotags: validate:"required,unique=StoreId,dive"
    repeated OrderRuleStore order_rule_stores = 2;
}

message DeleteOrderRuleStoreRequest {
    // 关系ID (web 查询时返回的order_rule_store_id)
    // @gotags: validate:"required"
    repeated uint64 ids = 1;
}


message CheckOrderRuleExistRequest {
    // 更新订货规则请求
    UpdateOrderRuleRequest update_order_rule_req = 1;
    // 增加订货规则关联门店请求
    // @gotags: validate:"required_without=UpdateOrderRuleReq"

    CreateOrderRuleStoreRequest create_order_rule_store_req = 2;
    //  操作类型
    // @gotags: validate:"required"
    ActionCode action = 3;
    // 分页信息
    // @gotags: validate:"required"
    Page page = 4;
}
enum ActionCode {
    UnknownAction = 0;
    UpdateOrderRule = 1;
    CreateOrderRuleStore = 2;
}

enum QueryViewType {
    UnknownView = 0;
    // 订货规则商品明细
    OrderRuleProductView = 1;
    // 订货规则门店明细
    OrderRuleStoreView = 2;
    // 可订货商品
    OrderRuleFullInfoView = 3;
}

message CheckOrderRuleExistResponse {
    repeated OrderRuleProductFullInfo rows = 1;
    // 总数
    int64 total = 2;
}

message QueryOrderRuleDetailByStoreRequest {
    // 订货规则ID
    repeated uint64 order_rule_ids = 1;
    // 数据id
    repeated uint64 ids = 2;
    // 商品ID
    repeated uint64 product_ids = 3;
    // 商品状态
    string product_status = 4;
    // 物流模式
    string logistic_mode = 5;
    // 门店ID
    repeated uint64 store_ids = 6;
    //门店状态
    string store_status = 7;
    // 订货规则状态：ENABLED/DISABLED
    string rule_status = 8;

    // @gotags: validate:"required"
    Page page = 9;
}


message QueryValidProductByStoreRequest {
    // 门店ID
    // @gotags: validate:"required"
    uint64 store_id = 1;
    // 商品ID
    repeated uint64 product_ids = 2;
    // 订货日期（不传入取当前时间）
    google.protobuf.Timestamp order_date = 3;
    // 过滤类型
    FilterType filter_type = 4;
    // 分页
    // @gotags: validate:"required"
    Page page = 5;
}

message QueryValidProductByStoreResponse {
    repeated OrderRuleProductBasicInfo rows = 1;
    // 总数
    int64 total = 2;
}

message OrderRuleProductBasicInfo {
    // 订货规则商品数据ID
    uint64 order_rule_product_id = 1;
    uint64 product_id = 2;
    // 商品类别ID
    uint64 product_category_id = 3;
    //  规格
    string spec = 4;
    //  订货单位ID
    uint64 order_unit_id = 5;
    // 订货单位名称
    string order_unit_name = 6;
    // 订货单位编码
    string order_unit_code = 7;
    // 订货单位比例
    string order_unit_rate = 8;
    // 商品存贮类型
    string storage_type = 9;
    // 商品销售类型
    string sale_type = 10;
    // 商品类型
    string product_type = 11;
    // 最小订量
    string min_qty = 12;
    // 最大订量
    string max_qty = 13;
    // 递增定量
    string incr_qty = 14;
    // 预计到货天数
    string arrival_day = 15;
    // 订货规则ID
    uint64 order_rule_id = 16;
    // 物流模式
    string logistic_mode = 17;
    // 仓库/供应商ID
    uint64 distribution_center_id = 18;
    // 门店id
    uint64 store_id = 19;
    //订货规则门店数据ID
    uint64 order_rule_store_id = 20;
    // 循环类型(日:d 周:w 月:m)
    string cycle_type = 21;
    // 是否允许主配
    bool allow_main_order = 22;
    // 收货偏差最小值
    string receiving_deviation_min = 23;
    // 收货偏差最大值
    string receiving_deviation_max = 24;
    // 主配到货天数
    int64 main_order_arrival_day = 25;
    // 预计到货日期文本描述
    string plan_arrive_date_text = 26;
    // 下次预计到货时间文本描述
    string next_plan_arrive_date_text = 27;
    // 安全库存
    string safety_qty = 28;
}

message QueryValidStoresForDistributionByProductRequest {
    // 门店ID
    repeated uint64 store_ids = 1;
    // 商品ID
    // @gotags: validate:"required"
    repeated uint64 product_ids = 2;
    // 订货日期（不传入取当前时间）
    google.protobuf.Timestamp order_date = 3;
    // 过滤类型
    FilterType filter_type = 4;
    // 分页
    // @gotags: validate:"required"
    Page page = 5;
}

enum FilterType {
    // 按订货规则过滤
    OrderRuleFilter = 0;
    // 按是否可主配过滤
    MainOrderFilter = 1;
    //不过滤只要有规则即可
    NoFilter = 2;
}


