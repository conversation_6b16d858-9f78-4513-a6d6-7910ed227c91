# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from business_rule import business_rule_pb2 as business__rule_dot_business__rule__pb2


class OrderRuleServicesStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.Ping = channel.unary_unary(
        '/business_rule.OrderRuleServices/Ping',
        request_serializer=business__rule_dot_business__rule__pb2.Request.SerializeToString,
        response_deserializer=business__rule_dot_business__rule__pb2.Response.FromString,
        )
    self.CreateOrderRule = channel.unary_unary(
        '/business_rule.OrderRuleServices/CreateOrderRule',
        request_serializer=business__rule_dot_business__rule__pb2.CreateOrderRuleRequest.SerializeToString,
        response_deserializer=business__rule_dot_business__rule__pb2.BusinessRuleResponse.FromString,
        )
    self.UpdateOrderRule = channel.unary_unary(
        '/business_rule.OrderRuleServices/UpdateOrderRule',
        request_serializer=business__rule_dot_business__rule__pb2.UpdateOrderRuleRequest.SerializeToString,
        response_deserializer=business__rule_dot_business__rule__pb2.BusinessRuleResponse.FromString,
        )
    self.QueryOrderRuleDetail = channel.unary_unary(
        '/business_rule.OrderRuleServices/QueryOrderRuleDetail',
        request_serializer=business__rule_dot_business__rule__pb2.QueryOrderRuleDetailRequest.SerializeToString,
        response_deserializer=business__rule_dot_business__rule__pb2.QueryOrderRuleDetailResponse.FromString,
        )
    self.DeleteOrderRule = channel.unary_unary(
        '/business_rule.OrderRuleServices/DeleteOrderRule',
        request_serializer=business__rule_dot_business__rule__pb2.DeleteBusinessRuleRequest.SerializeToString,
        response_deserializer=business__rule_dot_business__rule__pb2.BusinessRuleResponse.FromString,
        )
    self.CreateOrderRuleStore = channel.unary_unary(
        '/business_rule.OrderRuleServices/CreateOrderRuleStore',
        request_serializer=business__rule_dot_business__rule__pb2.CreateOrderRuleStoreRequest.SerializeToString,
        response_deserializer=business__rule_dot_business__rule__pb2.BusinessRuleResponse.FromString,
        )
    self.DeleteOrderRuleStore = channel.unary_unary(
        '/business_rule.OrderRuleServices/DeleteOrderRuleStore',
        request_serializer=business__rule_dot_business__rule__pb2.DeleteOrderRuleStoreRequest.SerializeToString,
        response_deserializer=business__rule_dot_business__rule__pb2.BusinessRuleResponse.FromString,
        )
    self.CheckOrderRuleExist = channel.unary_unary(
        '/business_rule.OrderRuleServices/CheckOrderRuleExist',
        request_serializer=business__rule_dot_business__rule__pb2.CheckOrderRuleExistRequest.SerializeToString,
        response_deserializer=business__rule_dot_business__rule__pb2.CheckOrderRuleExistResponse.FromString,
        )
    self.QueryValidProductByStore = channel.unary_unary(
        '/business_rule.OrderRuleServices/QueryValidProductByStore',
        request_serializer=business__rule_dot_business__rule__pb2.QueryValidProductByStoreRequest.SerializeToString,
        response_deserializer=business__rule_dot_business__rule__pb2.QueryValidProductByStoreResponse.FromString,
        )
    self.QueryValidStoresForDistributionByProduct = channel.unary_unary(
        '/business_rule.OrderRuleServices/QueryValidStoresForDistributionByProduct',
        request_serializer=business__rule_dot_business__rule__pb2.QueryValidStoresForDistributionByProductRequest.SerializeToString,
        response_deserializer=business__rule_dot_business__rule__pb2.QueryValidProductByStoreResponse.FromString,
        )


class OrderRuleServicesServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def Ping(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateOrderRule(self, request, context):
    """创建订货规则
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateOrderRule(self, request, context):
    """更新订货规则
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryOrderRuleDetail(self, request, context):
    """查询订货规则详情规则(多规则视角，web查询使用)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteOrderRule(self, request, context):
    """删除订货规则
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreateOrderRuleStore(self, request, context):
    """创建关联关系(规则和门店关联)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteOrderRuleStore(self, request, context):
    """移除关联关系(规则和门店关联)
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CheckOrderRuleExist(self, request, context):
    """订货规则数据(门店/商品)是否已经存在
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryValidProductByStore(self, request, context):
    """查询门店可订货商品（其他业务调用）
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryValidStoresForDistributionByProduct(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_OrderRuleServicesServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'Ping': grpc.unary_unary_rpc_method_handler(
          servicer.Ping,
          request_deserializer=business__rule_dot_business__rule__pb2.Request.FromString,
          response_serializer=business__rule_dot_business__rule__pb2.Response.SerializeToString,
      ),
      'CreateOrderRule': grpc.unary_unary_rpc_method_handler(
          servicer.CreateOrderRule,
          request_deserializer=business__rule_dot_business__rule__pb2.CreateOrderRuleRequest.FromString,
          response_serializer=business__rule_dot_business__rule__pb2.BusinessRuleResponse.SerializeToString,
      ),
      'UpdateOrderRule': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateOrderRule,
          request_deserializer=business__rule_dot_business__rule__pb2.UpdateOrderRuleRequest.FromString,
          response_serializer=business__rule_dot_business__rule__pb2.BusinessRuleResponse.SerializeToString,
      ),
      'QueryOrderRuleDetail': grpc.unary_unary_rpc_method_handler(
          servicer.QueryOrderRuleDetail,
          request_deserializer=business__rule_dot_business__rule__pb2.QueryOrderRuleDetailRequest.FromString,
          response_serializer=business__rule_dot_business__rule__pb2.QueryOrderRuleDetailResponse.SerializeToString,
      ),
      'DeleteOrderRule': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteOrderRule,
          request_deserializer=business__rule_dot_business__rule__pb2.DeleteBusinessRuleRequest.FromString,
          response_serializer=business__rule_dot_business__rule__pb2.BusinessRuleResponse.SerializeToString,
      ),
      'CreateOrderRuleStore': grpc.unary_unary_rpc_method_handler(
          servicer.CreateOrderRuleStore,
          request_deserializer=business__rule_dot_business__rule__pb2.CreateOrderRuleStoreRequest.FromString,
          response_serializer=business__rule_dot_business__rule__pb2.BusinessRuleResponse.SerializeToString,
      ),
      'DeleteOrderRuleStore': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteOrderRuleStore,
          request_deserializer=business__rule_dot_business__rule__pb2.DeleteOrderRuleStoreRequest.FromString,
          response_serializer=business__rule_dot_business__rule__pb2.BusinessRuleResponse.SerializeToString,
      ),
      'CheckOrderRuleExist': grpc.unary_unary_rpc_method_handler(
          servicer.CheckOrderRuleExist,
          request_deserializer=business__rule_dot_business__rule__pb2.CheckOrderRuleExistRequest.FromString,
          response_serializer=business__rule_dot_business__rule__pb2.CheckOrderRuleExistResponse.SerializeToString,
      ),
      'QueryValidProductByStore': grpc.unary_unary_rpc_method_handler(
          servicer.QueryValidProductByStore,
          request_deserializer=business__rule_dot_business__rule__pb2.QueryValidProductByStoreRequest.FromString,
          response_serializer=business__rule_dot_business__rule__pb2.QueryValidProductByStoreResponse.SerializeToString,
      ),
      'QueryValidStoresForDistributionByProduct': grpc.unary_unary_rpc_method_handler(
          servicer.QueryValidStoresForDistributionByProduct,
          request_deserializer=business__rule_dot_business__rule__pb2.QueryValidStoresForDistributionByProductRequest.FromString,
          response_serializer=business__rule_dot_business__rule__pb2.QueryValidProductByStoreResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'business_rule.OrderRuleServices', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
