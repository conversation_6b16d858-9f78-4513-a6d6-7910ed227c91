# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: store_extra/receive_diff.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='store_extra/receive_diff.proto',
  package='store_extra',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x1estore_extra/receive_diff.proto\x12\x0bstore_extra\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x8c\x05\n\x16ListReceiveDiffRequest\x12\x11\n\tstore_ids\x18\x01 \x03(\x04\x12\x0e\n\x06status\x18\x02 \x03(\t\x12.\n\nstart_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x05 \x01(\x05\x12\x0e\n\x06offset\x18\x06 \x01(\x05\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12\x11\n\tis_direct\x18\x08 \x01(\x08\x12\x15\n\rreceiving_ids\x18\t \x01(\x04\x12\x16\n\x0ereceiving_code\x18\n \x01(\t\x12\x13\n\x0bproduct_ids\x18\x0b \x03(\x04\x12\x0c\n\x04\x63ode\x18\x0c \x01(\t\x12\x16\n\x0elogistics_type\x18\r \x03(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\r\n\x05order\x18\x0f \x01(\t\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x34\n\x10return_date_from\x18\x12 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0ereturn_date_to\x18\x13 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x36\n\x12\x64\x65livery_date_from\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x10\x64\x65livery_date_to\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x64\x65livery_bys\x18\x16 \x03(\x04\x12\x11\n\tdiff_type\x18\x17 \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x1c \x01(\t\"P\n\x17ListReceiveDiffResponse\x12&\n\x04rows\x18\x01 \x03(\x0b\x32\x18.store_extra.ReceiveDiff\x12\r\n\x05total\x18\x02 \x01(\x04\"j\n\x19GetReceiveDiffByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12\x0b\n\x03lan\x18\x05 \x01(\t\"\x8e\x01\n GetReceiveDiffProductByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12\x0c\n\x04sort\x18\x05 \x01(\t\x12\r\n\x05order\x18\x06 \x01(\t\x12\x0b\n\x03lan\x18\x07 \x01(\t\"a\n!GetReceiveDiffProductByIdResponse\x12-\n\x04rows\x18\x01 \x03(\x0b\x32\x1f.store_extra.ReceiveDiffProduct\x12\r\n\x05total\x18\x02 \x01(\x04\"3\n\x18SubmitReceiveDiffRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"3\n\x18\x43onfirmReceiveDiffRequet\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"y\n\x18RejectReceiveDiffRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x15\n\rreject_reason\x18\x02 \x01(\t\x12-\n\x0b\x61ttachments\x18\x03 \x03(\x0b\x32\x18.store_extra.Attachments\x12\x0b\n\x03lan\x18\x04 \x01(\t\"3\n\x18\x44\x65leteReceiveDiffRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\x9a\x01\n\x18UpdateReceiveDiffRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12&\n\x08products\x18\x02 \x03(\x0b\x32\x14.store_extra.Product\x12-\n\x0b\x61ttachments\x18\x03 \x03(\x0b\x32\x18.store_extra.Attachments\x12\x0b\n\x03lan\x18\x04 \x01(\t\x12\x0e\n\x06remark\x18\x05 \x01(\t\"\x80\n\n\x0bReceiveDiff\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x13\n\x0breceived_by\x18\x03 \x01(\x04\x12\x14\n\x0creceiving_id\x18\x04 \x01(\x04\x12\x16\n\x0ereceiving_code\x18\x05 \x01(\t\x12\x11\n\tmaster_id\x18\x06 \x01(\x04\x12\x13\n\x0bmaster_code\x18\x07 \x01(\t\x12\x0e\n\x06status\x18\x08 \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1c\n\x14\x64\x65livery_note_number\x18\n \x01(\t\x12\x13\n\x0b\x64\x65livery_by\x18\x0b \x01(\x04\x12\x31\n\rdelivery_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0breason_type\x18\r \x01(\t\x12\x11\n\treview_by\x18\x0e \x01(\x04\x12\x0e\n\x06remark\x18\x0f \x01(\t\x12\x1a\n\x12store_secondary_id\x18\x10 \x01(\t\x12\x18\n\x10inventory_status\x18\x11 \x01(\t\x12\x18\n\x10inventory_req_id\x18\x12 \x01(\x04\x12.\n\ncreated_at\x18\x13 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x15 \x01(\x04\x12\x12\n\nupdated_by\x18\x16 \x01(\x04\x12\x12\n\npartner_id\x18\x17 \x01(\x04\x12\x11\n\tis_direct\x18\x18 \x01(\x08\x12\x14\n\x0c\x63reated_name\x18\x19 \x01(\t\x12\x14\n\x0cupdated_name\x18\x1a \x01(\t\x12\x15\n\rreject_reason\x18\x1b \x01(\t\x12-\n\x0b\x61ttachments\x18\x1c \x03(\x0b\x32\x18.store_extra.Attachments\x12\x14\n\x0cproduct_nums\x18\x1d \x01(\x04\x12\x15\n\rreceived_type\x18\x1f \x01(\t\x12\x12\n\nrequest_id\x18  \x01(\x04\x12\x16\n\x0elogistics_type\x18! \x01(\t\x12\x11\n\tsend_type\x18\" \x01(\t\x12\x35\n\x11pre_delivery_date\x18$ \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\treturn_id\x18% \x01(\x04\x12-\n\tsend_date\x18& \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\' \x01(\t\x12\x16\n\x0esub_receive_by\x18( \x01(\x04\x12\x18\n\x10\x64\x65livery_by_name\x18) \x01(\t\x12\x30\n\x0creceive_date\x18* \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x11\x61uto_confirm_date\x18+ \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1b\n\x13product_names_brief\x18, \x03(\t\x12\x18\n\x10\x64\x65livery_by_code\x18- \x01(\t\x12\x11\n\tdiff_type\x18. \x01(\t\x12\x15\n\rfranchisee_id\x18/ \x01(\x04\x12\x11\n\trefund_id\x18\x32 \x01(\x04\x12\x13\n\x0brefund_code\x18\x33 \x01(\t\"\xd5\x08\n\x12ReceiveDiffProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x14\n\x0creceiving_id\x18\x02 \x01(\x04\x12\x13\n\x0breceived_by\x18\x03 \x01(\x04\x12\x12\n\nproduct_id\x18\x04 \x01(\x04\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x05 \x01(\x04\x12\x0f\n\x07unit_id\x18\x06 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x07 \x01(\t\x12\x14\n\x0cproduct_name\x18\x08 \x01(\t\x12\x17\n\x0fmaterial_number\x18\t \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\n \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x0b \x01(\t\x12\x11\n\tunit_name\x18\x0c \x01(\t\x12\x11\n\tunit_spec\x18\r \x01(\t\x12\x11\n\tunit_rate\x18\x0e \x01(\x01\x12\x10\n\x08quantity\x18\x11 \x01(\x01\x12$\n\x1creceived_accounting_quantity\x18\x12 \x01(\x01\x12\x19\n\x11received_quantity\x18\x13 \x01(\x01\x12%\n\x1d\x63onfirmed_accounting_quantity\x18\x14 \x01(\x01\x12\x1a\n\x12\x63onfirmed_quantity\x18\x15 \x01(\x01\x12 \n\x18\x64iff_accounting_quantity\x18\x16 \x01(\x01\x12\x15\n\rdiff_quantity\x18\x17 \x01(\x01\x12\"\n\x1as_diff_accounting_quantity\x18\x18 \x01(\x01\x12\x17\n\x0fs_diff_quantity\x18\x19 \x01(\x01\x12\"\n\x1a\x64_diff_accounting_quantity\x18\x1a \x01(\x01\x12\x17\n\x0f\x64_diff_quantity\x18\x1b \x01(\x01\x12/\n\x0b\x64\x65mand_date\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x1d \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x1e \x01(\x04\x12.\n\nupdated_at\x18\x1f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18  \x01(\x04\x12\x12\n\npartner_id\x18! \x01(\x04\x12\x0f\n\x07\x64iff_id\x18\" \x01(\x04\x12\x13\n\x0breason_type\x18# \x01(\t\x12\x15\n\rreject_reason\x18$ \x01(\t\x12-\n\x0b\x61ttachments\x18% \x03(\x0b\x32\x18.store_extra.Attachments\x12\x14\n\x0c\x63reated_name\x18& \x01(\t\x12\x14\n\x0cupdated_name\x18\' \x01(\t\x12\x0e\n\x06remark\x18( \x01(\t\x12\x12\n\ncost_price\x18) \x01(\x01\x12\x11\n\ttax_price\x18* \x01(\x01\x12\x10\n\x08tax_rate\x18+ \x01(\x01\x12\x16\n\x0esub_receive_by\x18, \x01(\x04\"l\n\x07Product\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x17\n\x0fs_diff_quantity\x18\x02 \x01(\x01\x12\x17\n\x0f\x64_diff_quantity\x18\x03 \x01(\x01\x12\x13\n\x0breason_type\x18\x04 \x01(\t\x12\x0e\n\x06remark\x18\x05 \x01(\t\"R\n\x0b\x44iffProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06remark\x18\x02 \x01(\t\x12\x13\n\x0breason_type\x18\x03 \x01(\t\x12\x12\n\ncreated_by\x18\x04 \x01(\x04\"!\n\x0e\x43ommonResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"(\n\x0b\x41ttachments\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t2\xdf\x08\n\x1cStoreExtraReceiveDiffService\x12\x8d\x01\n\x0fListReceiveDiff\x12#.store_extra.ListReceiveDiffRequest\x1a$.store_extra.ListReceiveDiffResponse\"/\x82\xd3\xe4\x93\x02)\x12\'/api/v2/supply/store_extra/receive-diff\x12\x8c\x01\n\x12GetReceiveDiffById\x12&.store_extra.GetReceiveDiffByIdRequest\x1a\x18.store_extra.ReceiveDiff\"4\x82\xd3\xe4\x93\x02.\x12,/api/v2/supply/store_extra/receive-diff/{id}\x12\xb8\x01\n\x19GetReceiveDiffProductById\x12-.store_extra.GetReceiveDiffProductByIdRequest\x1a..store_extra.GetReceiveDiffProductByIdResponse\"<\x82\xd3\xe4\x93\x02\x36\x12\x34/api/v2/supply/store_extra/receive-diff/{id}/product\x12\x99\x01\n\x12\x43onfirmReceiveDiff\x12%.store_extra.ConfirmReceiveDiffRequet\x1a\x1b.store_extra.CommonResponse\"?\x82\xd3\xe4\x93\x02\x39\x1a\x34/api/v2/supply/store_extra/receive-diff/{id}/confirm:\x01*\x12\x97\x01\n\x11RejectReceiveDiff\x12%.store_extra.RejectReceiveDiffRequest\x1a\x1b.store_extra.CommonResponse\">\x82\xd3\xe4\x93\x02\x38\x1a\x33/api/v2/supply/store_extra/receive-diff/{id}/reject:\x01*\x12\x97\x01\n\x11UpdateReceiveDiff\x12%.store_extra.UpdateReceiveDiffRequest\x1a\x1b.store_extra.CommonResponse\">\x82\xd3\xe4\x93\x02\x38\x1a\x33/api/v2/supply/store_extra/receive-diff/{id}/update:\x01*\x12\x94\x01\n\x11\x44\x65leteReceiveDiff\x12%.store_extra.DeleteReceiveDiffRequest\x1a\x1b.store_extra.CommonResponse\";\x82\xd3\xe4\x93\x02\x35\x1a\x33/api/v2/supply/store_extra/receive-diff/{id}/deleteb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_LISTRECEIVEDIFFREQUEST = _descriptor.Descriptor(
  name='ListReceiveDiffRequest',
  full_name='store_extra.ListReceiveDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='store_extra.ListReceiveDiffRequest.store_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store_extra.ListReceiveDiffRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='store_extra.ListReceiveDiffRequest.start_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='store_extra.ListReceiveDiffRequest.end_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store_extra.ListReceiveDiffRequest.limit', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store_extra.ListReceiveDiffRequest.offset', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='store_extra.ListReceiveDiffRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='store_extra.ListReceiveDiffRequest.is_direct', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_ids', full_name='store_extra.ListReceiveDiffRequest.receiving_ids', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_code', full_name='store_extra.ListReceiveDiffRequest.receiving_code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='store_extra.ListReceiveDiffRequest.product_ids', index=10,
      number=11, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store_extra.ListReceiveDiffRequest.code', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='store_extra.ListReceiveDiffRequest.logistics_type', index=12,
      number=13, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='store_extra.ListReceiveDiffRequest.sort', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='store_extra.ListReceiveDiffRequest.order', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_extra.ListReceiveDiffRequest.lan', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_from', full_name='store_extra.ListReceiveDiffRequest.return_date_from', index=16,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_to', full_name='store_extra.ListReceiveDiffRequest.return_date_to', index=17,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_from', full_name='store_extra.ListReceiveDiffRequest.delivery_date_from', index=18,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_to', full_name='store_extra.ListReceiveDiffRequest.delivery_date_to', index=19,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_bys', full_name='store_extra.ListReceiveDiffRequest.delivery_bys', index=20,
      number=22, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_type', full_name='store_extra.ListReceiveDiffRequest.diff_type', index=21,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='store_extra.ListReceiveDiffRequest.branch_type', index=22,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=111,
  serialized_end=763,
)


_LISTRECEIVEDIFFRESPONSE = _descriptor.Descriptor(
  name='ListReceiveDiffResponse',
  full_name='store_extra.ListReceiveDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_extra.ListReceiveDiffResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store_extra.ListReceiveDiffResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=765,
  serialized_end=845,
)


_GETRECEIVEDIFFBYIDREQUEST = _descriptor.Descriptor(
  name='GetReceiveDiffByIdRequest',
  full_name='store_extra.GetReceiveDiffByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_extra.GetReceiveDiffByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store_extra.GetReceiveDiffByIdRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store_extra.GetReceiveDiffByIdRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='store_extra.GetReceiveDiffByIdRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_extra.GetReceiveDiffByIdRequest.lan', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=847,
  serialized_end=953,
)


_GETRECEIVEDIFFPRODUCTBYIDREQUEST = _descriptor.Descriptor(
  name='GetReceiveDiffProductByIdRequest',
  full_name='store_extra.GetReceiveDiffProductByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_extra.GetReceiveDiffProductByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='store_extra.GetReceiveDiffProductByIdRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='store_extra.GetReceiveDiffProductByIdRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='store_extra.GetReceiveDiffProductByIdRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='store_extra.GetReceiveDiffProductByIdRequest.sort', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='store_extra.GetReceiveDiffProductByIdRequest.order', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_extra.GetReceiveDiffProductByIdRequest.lan', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=956,
  serialized_end=1098,
)


_GETRECEIVEDIFFPRODUCTBYIDRESPONSE = _descriptor.Descriptor(
  name='GetReceiveDiffProductByIdResponse',
  full_name='store_extra.GetReceiveDiffProductByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='store_extra.GetReceiveDiffProductByIdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='store_extra.GetReceiveDiffProductByIdResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1100,
  serialized_end=1197,
)


_SUBMITRECEIVEDIFFREQUEST = _descriptor.Descriptor(
  name='SubmitReceiveDiffRequest',
  full_name='store_extra.SubmitReceiveDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_extra.SubmitReceiveDiffRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_extra.SubmitReceiveDiffRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1199,
  serialized_end=1250,
)


_CONFIRMRECEIVEDIFFREQUET = _descriptor.Descriptor(
  name='ConfirmReceiveDiffRequet',
  full_name='store_extra.ConfirmReceiveDiffRequet',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_extra.ConfirmReceiveDiffRequet.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_extra.ConfirmReceiveDiffRequet.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1252,
  serialized_end=1303,
)


_REJECTRECEIVEDIFFREQUEST = _descriptor.Descriptor(
  name='RejectReceiveDiffRequest',
  full_name='store_extra.RejectReceiveDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_extra.RejectReceiveDiffRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='store_extra.RejectReceiveDiffRequest.reject_reason', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_extra.RejectReceiveDiffRequest.attachments', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_extra.RejectReceiveDiffRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1305,
  serialized_end=1426,
)


_DELETERECEIVEDIFFREQUEST = _descriptor.Descriptor(
  name='DeleteReceiveDiffRequest',
  full_name='store_extra.DeleteReceiveDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_extra.DeleteReceiveDiffRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_extra.DeleteReceiveDiffRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1428,
  serialized_end=1479,
)


_UPDATERECEIVEDIFFREQUEST = _descriptor.Descriptor(
  name='UpdateReceiveDiffRequest',
  full_name='store_extra.UpdateReceiveDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_extra.UpdateReceiveDiffRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='store_extra.UpdateReceiveDiffRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_extra.UpdateReceiveDiffRequest.attachments', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='store_extra.UpdateReceiveDiffRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_extra.UpdateReceiveDiffRequest.remark', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1482,
  serialized_end=1636,
)


_RECEIVEDIFF = _descriptor.Descriptor(
  name='ReceiveDiff',
  full_name='store_extra.ReceiveDiff',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_extra.ReceiveDiff.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='store_extra.ReceiveDiff.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='store_extra.ReceiveDiff.received_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_id', full_name='store_extra.ReceiveDiff.receiving_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_code', full_name='store_extra.ReceiveDiff.receiving_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='master_id', full_name='store_extra.ReceiveDiff.master_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='master_code', full_name='store_extra.ReceiveDiff.master_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='store_extra.ReceiveDiff.status', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='store_extra.ReceiveDiff.demand_date', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_note_number', full_name='store_extra.ReceiveDiff.delivery_note_number', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='store_extra.ReceiveDiff.delivery_by', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='store_extra.ReceiveDiff.delivery_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='store_extra.ReceiveDiff.reason_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='store_extra.ReceiveDiff.review_by', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_extra.ReceiveDiff.remark', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='store_extra.ReceiveDiff.store_secondary_id', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_status', full_name='store_extra.ReceiveDiff.inventory_status', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_req_id', full_name='store_extra.ReceiveDiff.inventory_req_id', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='store_extra.ReceiveDiff.created_at', index=18,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='store_extra.ReceiveDiff.updated_at', index=19,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='store_extra.ReceiveDiff.created_by', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='store_extra.ReceiveDiff.updated_by', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='store_extra.ReceiveDiff.partner_id', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='store_extra.ReceiveDiff.is_direct', index=23,
      number=24, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='store_extra.ReceiveDiff.created_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='store_extra.ReceiveDiff.updated_name', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='store_extra.ReceiveDiff.reject_reason', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_extra.ReceiveDiff.attachments', index=27,
      number=28, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_nums', full_name='store_extra.ReceiveDiff.product_nums', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_type', full_name='store_extra.ReceiveDiff.received_type', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='store_extra.ReceiveDiff.request_id', index=30,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='store_extra.ReceiveDiff.logistics_type', index=31,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='send_type', full_name='store_extra.ReceiveDiff.send_type', index=32,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_delivery_date', full_name='store_extra.ReceiveDiff.pre_delivery_date', index=33,
      number=36, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_id', full_name='store_extra.ReceiveDiff.return_id', index=34,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='send_date', full_name='store_extra.ReceiveDiff.send_date', index=35,
      number=38, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='store_extra.ReceiveDiff.branch_type', index=36,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='store_extra.ReceiveDiff.sub_receive_by', index=37,
      number=40, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_name', full_name='store_extra.ReceiveDiff.delivery_by_name', index=38,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_date', full_name='store_extra.ReceiveDiff.receive_date', index=39,
      number=42, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auto_confirm_date', full_name='store_extra.ReceiveDiff.auto_confirm_date', index=40,
      number=43, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_names_brief', full_name='store_extra.ReceiveDiff.product_names_brief', index=41,
      number=44, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_code', full_name='store_extra.ReceiveDiff.delivery_by_code', index=42,
      number=45, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_type', full_name='store_extra.ReceiveDiff.diff_type', index=43,
      number=46, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='store_extra.ReceiveDiff.franchisee_id', index=44,
      number=47, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_id', full_name='store_extra.ReceiveDiff.refund_id', index=45,
      number=50, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_code', full_name='store_extra.ReceiveDiff.refund_code', index=46,
      number=51, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1639,
  serialized_end=2919,
)


_RECEIVEDIFFPRODUCT = _descriptor.Descriptor(
  name='ReceiveDiffProduct',
  full_name='store_extra.ReceiveDiffProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_extra.ReceiveDiffProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_id', full_name='store_extra.ReceiveDiffProduct.receiving_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='store_extra.ReceiveDiffProduct.received_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='store_extra.ReceiveDiffProduct.product_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='store_extra.ReceiveDiffProduct.accounting_unit_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='store_extra.ReceiveDiffProduct.unit_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='store_extra.ReceiveDiffProduct.product_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='store_extra.ReceiveDiffProduct.product_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='store_extra.ReceiveDiffProduct.material_number', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='store_extra.ReceiveDiffProduct.accounting_unit_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='store_extra.ReceiveDiffProduct.accounting_unit_spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='store_extra.ReceiveDiffProduct.unit_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='store_extra.ReceiveDiffProduct.unit_spec', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='store_extra.ReceiveDiffProduct.unit_rate', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='store_extra.ReceiveDiffProduct.quantity', index=14,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_accounting_quantity', full_name='store_extra.ReceiveDiffProduct.received_accounting_quantity', index=15,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_quantity', full_name='store_extra.ReceiveDiffProduct.received_quantity', index=16,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_accounting_quantity', full_name='store_extra.ReceiveDiffProduct.confirmed_accounting_quantity', index=17,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='store_extra.ReceiveDiffProduct.confirmed_quantity', index=18,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_accounting_quantity', full_name='store_extra.ReceiveDiffProduct.diff_accounting_quantity', index=19,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='store_extra.ReceiveDiffProduct.diff_quantity', index=20,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_accounting_quantity', full_name='store_extra.ReceiveDiffProduct.s_diff_accounting_quantity', index=21,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_quantity', full_name='store_extra.ReceiveDiffProduct.s_diff_quantity', index=22,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_accounting_quantity', full_name='store_extra.ReceiveDiffProduct.d_diff_accounting_quantity', index=23,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_quantity', full_name='store_extra.ReceiveDiffProduct.d_diff_quantity', index=24,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='store_extra.ReceiveDiffProduct.demand_date', index=25,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='store_extra.ReceiveDiffProduct.created_at', index=26,
      number=29, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='store_extra.ReceiveDiffProduct.created_by', index=27,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='store_extra.ReceiveDiffProduct.updated_at', index=28,
      number=31, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='store_extra.ReceiveDiffProduct.updated_by', index=29,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='store_extra.ReceiveDiffProduct.partner_id', index=30,
      number=33, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_id', full_name='store_extra.ReceiveDiffProduct.diff_id', index=31,
      number=34, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='store_extra.ReceiveDiffProduct.reason_type', index=32,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='store_extra.ReceiveDiffProduct.reject_reason', index=33,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='store_extra.ReceiveDiffProduct.attachments', index=34,
      number=37, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='store_extra.ReceiveDiffProduct.created_name', index=35,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='store_extra.ReceiveDiffProduct.updated_name', index=36,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_extra.ReceiveDiffProduct.remark', index=37,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='store_extra.ReceiveDiffProduct.cost_price', index=38,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='store_extra.ReceiveDiffProduct.tax_price', index=39,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='store_extra.ReceiveDiffProduct.tax_rate', index=40,
      number=43, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='store_extra.ReceiveDiffProduct.sub_receive_by', index=41,
      number=44, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2922,
  serialized_end=4031,
)


_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='store_extra.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_extra.Product.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_quantity', full_name='store_extra.Product.s_diff_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_quantity', full_name='store_extra.Product.d_diff_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='store_extra.Product.reason_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_extra.Product.remark', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4033,
  serialized_end=4141,
)


_DIFFPRODUCT = _descriptor.Descriptor(
  name='DiffProduct',
  full_name='store_extra.DiffProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='store_extra.DiffProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='store_extra.DiffProduct.remark', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='store_extra.DiffProduct.reason_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='store_extra.DiffProduct.created_by', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4143,
  serialized_end=4225,
)


_COMMONRESPONSE = _descriptor.Descriptor(
  name='CommonResponse',
  full_name='store_extra.CommonResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='store_extra.CommonResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4227,
  serialized_end=4260,
)


_ATTACHMENTS = _descriptor.Descriptor(
  name='Attachments',
  full_name='store_extra.Attachments',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='store_extra.Attachments.type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='store_extra.Attachments.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4262,
  serialized_end=4302,
)

_LISTRECEIVEDIFFREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEDIFFREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEDIFFREQUEST.fields_by_name['return_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEDIFFREQUEST.fields_by_name['return_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEDIFFREQUEST.fields_by_name['delivery_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEDIFFREQUEST.fields_by_name['delivery_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEDIFFRESPONSE.fields_by_name['rows'].message_type = _RECEIVEDIFF
_GETRECEIVEDIFFPRODUCTBYIDRESPONSE.fields_by_name['rows'].message_type = _RECEIVEDIFFPRODUCT
_REJECTRECEIVEDIFFREQUEST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_UPDATERECEIVEDIFFREQUEST.fields_by_name['products'].message_type = _PRODUCT
_UPDATERECEIVEDIFFREQUEST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_RECEIVEDIFF.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFF.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFF.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFF.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFF.fields_by_name['attachments'].message_type = _ATTACHMENTS
_RECEIVEDIFF.fields_by_name['pre_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFF.fields_by_name['send_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFF.fields_by_name['receive_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFF.fields_by_name['auto_confirm_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFFPRODUCT.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFFPRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFFPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFFPRODUCT.fields_by_name['attachments'].message_type = _ATTACHMENTS
DESCRIPTOR.message_types_by_name['ListReceiveDiffRequest'] = _LISTRECEIVEDIFFREQUEST
DESCRIPTOR.message_types_by_name['ListReceiveDiffResponse'] = _LISTRECEIVEDIFFRESPONSE
DESCRIPTOR.message_types_by_name['GetReceiveDiffByIdRequest'] = _GETRECEIVEDIFFBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetReceiveDiffProductByIdRequest'] = _GETRECEIVEDIFFPRODUCTBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetReceiveDiffProductByIdResponse'] = _GETRECEIVEDIFFPRODUCTBYIDRESPONSE
DESCRIPTOR.message_types_by_name['SubmitReceiveDiffRequest'] = _SUBMITRECEIVEDIFFREQUEST
DESCRIPTOR.message_types_by_name['ConfirmReceiveDiffRequet'] = _CONFIRMRECEIVEDIFFREQUET
DESCRIPTOR.message_types_by_name['RejectReceiveDiffRequest'] = _REJECTRECEIVEDIFFREQUEST
DESCRIPTOR.message_types_by_name['DeleteReceiveDiffRequest'] = _DELETERECEIVEDIFFREQUEST
DESCRIPTOR.message_types_by_name['UpdateReceiveDiffRequest'] = _UPDATERECEIVEDIFFREQUEST
DESCRIPTOR.message_types_by_name['ReceiveDiff'] = _RECEIVEDIFF
DESCRIPTOR.message_types_by_name['ReceiveDiffProduct'] = _RECEIVEDIFFPRODUCT
DESCRIPTOR.message_types_by_name['Product'] = _PRODUCT
DESCRIPTOR.message_types_by_name['DiffProduct'] = _DIFFPRODUCT
DESCRIPTOR.message_types_by_name['CommonResponse'] = _COMMONRESPONSE
DESCRIPTOR.message_types_by_name['Attachments'] = _ATTACHMENTS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ListReceiveDiffRequest = _reflection.GeneratedProtocolMessageType('ListReceiveDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTRECEIVEDIFFREQUEST,
  __module__ = 'store_extra.receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:store_extra.ListReceiveDiffRequest)
  ))
_sym_db.RegisterMessage(ListReceiveDiffRequest)

ListReceiveDiffResponse = _reflection.GeneratedProtocolMessageType('ListReceiveDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTRECEIVEDIFFRESPONSE,
  __module__ = 'store_extra.receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:store_extra.ListReceiveDiffResponse)
  ))
_sym_db.RegisterMessage(ListReceiveDiffResponse)

GetReceiveDiffByIdRequest = _reflection.GeneratedProtocolMessageType('GetReceiveDiffByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVEDIFFBYIDREQUEST,
  __module__ = 'store_extra.receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:store_extra.GetReceiveDiffByIdRequest)
  ))
_sym_db.RegisterMessage(GetReceiveDiffByIdRequest)

GetReceiveDiffProductByIdRequest = _reflection.GeneratedProtocolMessageType('GetReceiveDiffProductByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVEDIFFPRODUCTBYIDREQUEST,
  __module__ = 'store_extra.receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:store_extra.GetReceiveDiffProductByIdRequest)
  ))
_sym_db.RegisterMessage(GetReceiveDiffProductByIdRequest)

GetReceiveDiffProductByIdResponse = _reflection.GeneratedProtocolMessageType('GetReceiveDiffProductByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVEDIFFPRODUCTBYIDRESPONSE,
  __module__ = 'store_extra.receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:store_extra.GetReceiveDiffProductByIdResponse)
  ))
_sym_db.RegisterMessage(GetReceiveDiffProductByIdResponse)

SubmitReceiveDiffRequest = _reflection.GeneratedProtocolMessageType('SubmitReceiveDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITRECEIVEDIFFREQUEST,
  __module__ = 'store_extra.receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:store_extra.SubmitReceiveDiffRequest)
  ))
_sym_db.RegisterMessage(SubmitReceiveDiffRequest)

ConfirmReceiveDiffRequet = _reflection.GeneratedProtocolMessageType('ConfirmReceiveDiffRequet', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMRECEIVEDIFFREQUET,
  __module__ = 'store_extra.receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:store_extra.ConfirmReceiveDiffRequet)
  ))
_sym_db.RegisterMessage(ConfirmReceiveDiffRequet)

RejectReceiveDiffRequest = _reflection.GeneratedProtocolMessageType('RejectReceiveDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _REJECTRECEIVEDIFFREQUEST,
  __module__ = 'store_extra.receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:store_extra.RejectReceiveDiffRequest)
  ))
_sym_db.RegisterMessage(RejectReceiveDiffRequest)

DeleteReceiveDiffRequest = _reflection.GeneratedProtocolMessageType('DeleteReceiveDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETERECEIVEDIFFREQUEST,
  __module__ = 'store_extra.receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:store_extra.DeleteReceiveDiffRequest)
  ))
_sym_db.RegisterMessage(DeleteReceiveDiffRequest)

UpdateReceiveDiffRequest = _reflection.GeneratedProtocolMessageType('UpdateReceiveDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERECEIVEDIFFREQUEST,
  __module__ = 'store_extra.receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:store_extra.UpdateReceiveDiffRequest)
  ))
_sym_db.RegisterMessage(UpdateReceiveDiffRequest)

ReceiveDiff = _reflection.GeneratedProtocolMessageType('ReceiveDiff', (_message.Message,), dict(
  DESCRIPTOR = _RECEIVEDIFF,
  __module__ = 'store_extra.receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:store_extra.ReceiveDiff)
  ))
_sym_db.RegisterMessage(ReceiveDiff)

ReceiveDiffProduct = _reflection.GeneratedProtocolMessageType('ReceiveDiffProduct', (_message.Message,), dict(
  DESCRIPTOR = _RECEIVEDIFFPRODUCT,
  __module__ = 'store_extra.receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:store_extra.ReceiveDiffProduct)
  ))
_sym_db.RegisterMessage(ReceiveDiffProduct)

Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCT,
  __module__ = 'store_extra.receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:store_extra.Product)
  ))
_sym_db.RegisterMessage(Product)

DiffProduct = _reflection.GeneratedProtocolMessageType('DiffProduct', (_message.Message,), dict(
  DESCRIPTOR = _DIFFPRODUCT,
  __module__ = 'store_extra.receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:store_extra.DiffProduct)
  ))
_sym_db.RegisterMessage(DiffProduct)

CommonResponse = _reflection.GeneratedProtocolMessageType('CommonResponse', (_message.Message,), dict(
  DESCRIPTOR = _COMMONRESPONSE,
  __module__ = 'store_extra.receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:store_extra.CommonResponse)
  ))
_sym_db.RegisterMessage(CommonResponse)

Attachments = _reflection.GeneratedProtocolMessageType('Attachments', (_message.Message,), dict(
  DESCRIPTOR = _ATTACHMENTS,
  __module__ = 'store_extra.receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:store_extra.Attachments)
  ))
_sym_db.RegisterMessage(Attachments)



_STOREEXTRARECEIVEDIFFSERVICE = _descriptor.ServiceDescriptor(
  name='StoreExtraReceiveDiffService',
  full_name='store_extra.StoreExtraReceiveDiffService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=4305,
  serialized_end=5424,
  methods=[
  _descriptor.MethodDescriptor(
    name='ListReceiveDiff',
    full_name='store_extra.StoreExtraReceiveDiffService.ListReceiveDiff',
    index=0,
    containing_service=None,
    input_type=_LISTRECEIVEDIFFREQUEST,
    output_type=_LISTRECEIVEDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\002)\022\'/api/v2/supply/store_extra/receive-diff'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReceiveDiffById',
    full_name='store_extra.StoreExtraReceiveDiffService.GetReceiveDiffById',
    index=1,
    containing_service=None,
    input_type=_GETRECEIVEDIFFBYIDREQUEST,
    output_type=_RECEIVEDIFF,
    serialized_options=_b('\202\323\344\223\002.\022,/api/v2/supply/store_extra/receive-diff/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReceiveDiffProductById',
    full_name='store_extra.StoreExtraReceiveDiffService.GetReceiveDiffProductById',
    index=2,
    containing_service=None,
    input_type=_GETRECEIVEDIFFPRODUCTBYIDREQUEST,
    output_type=_GETRECEIVEDIFFPRODUCTBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0026\0224/api/v2/supply/store_extra/receive-diff/{id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmReceiveDiff',
    full_name='store_extra.StoreExtraReceiveDiffService.ConfirmReceiveDiff',
    index=3,
    containing_service=None,
    input_type=_CONFIRMRECEIVEDIFFREQUET,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\0324/api/v2/supply/store_extra/receive-diff/{id}/confirm:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='RejectReceiveDiff',
    full_name='store_extra.StoreExtraReceiveDiffService.RejectReceiveDiff',
    index=4,
    containing_service=None,
    input_type=_REJECTRECEIVEDIFFREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\0323/api/v2/supply/store_extra/receive-diff/{id}/reject:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateReceiveDiff',
    full_name='store_extra.StoreExtraReceiveDiffService.UpdateReceiveDiff',
    index=5,
    containing_service=None,
    input_type=_UPDATERECEIVEDIFFREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\0323/api/v2/supply/store_extra/receive-diff/{id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteReceiveDiff',
    full_name='store_extra.StoreExtraReceiveDiffService.DeleteReceiveDiff',
    index=6,
    containing_service=None,
    input_type=_DELETERECEIVEDIFFREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\0323/api/v2/supply/store_extra/receive-diff/{id}/delete'),
  ),
])
_sym_db.RegisterServiceDescriptor(_STOREEXTRARECEIVEDIFFSERVICE)

DESCRIPTOR.services_by_name['StoreExtraReceiveDiffService'] = _STOREEXTRARECEIVEDIFFSERVICE

# @@protoc_insertion_point(module_scope)
