# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from store_extra import returns_pb2 as store__extra_dot_returns__pb2


class StoreExtraReturnServiceStub(object):
  """门店单据审核-退货相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetValidProduct = channel.unary_unary(
        '/store_extra.StoreExtraReturnService/GetValidProduct',
        request_serializer=store__extra_dot_returns__pb2.GetValidProductRequest.SerializeToString,
        response_deserializer=store__extra_dot_returns__pb2.ValidProduct.FromString,
        )
    self.ListReturn = channel.unary_unary(
        '/store_extra.StoreExtraReturnService/ListReturn',
        request_serializer=store__extra_dot_returns__pb2.ListReturnRequest.SerializeToString,
        response_deserializer=store__extra_dot_returns__pb2.ListReturnResponse.FromString,
        )
    self.GetReturnById = channel.unary_unary(
        '/store_extra.StoreExtraReturnService/GetReturnById',
        request_serializer=store__extra_dot_returns__pb2.GetReturnByIdRequest.SerializeToString,
        response_deserializer=store__extra_dot_returns__pb2.Returns.FromString,
        )
    self.GetReturnProductById = channel.unary_unary(
        '/store_extra.StoreExtraReturnService/GetReturnProductById',
        request_serializer=store__extra_dot_returns__pb2.GetReturnProductByIdRequest.SerializeToString,
        response_deserializer=store__extra_dot_returns__pb2.GetReturnProductByIdResponse.FromString,
        )
    self.RejectReturn = channel.unary_unary(
        '/store_extra.StoreExtraReturnService/RejectReturn',
        request_serializer=store__extra_dot_returns__pb2.RejectReturnRequest.SerializeToString,
        response_deserializer=store__extra_dot_returns__pb2.ReturnCommonResponse.FromString,
        )
    self.ApproveReturn = channel.unary_unary(
        '/store_extra.StoreExtraReturnService/ApproveReturn',
        request_serializer=store__extra_dot_returns__pb2.ApproveReturnRequest.SerializeToString,
        response_deserializer=store__extra_dot_returns__pb2.ReturnCommonResponse.FromString,
        )
    self.ConfirmReturn = channel.unary_unary(
        '/store_extra.StoreExtraReturnService/ConfirmReturn',
        request_serializer=store__extra_dot_returns__pb2.ConfirmReturnRequest.SerializeToString,
        response_deserializer=store__extra_dot_returns__pb2.ReturnCommonResponse.FromString,
        )
    self.DeliveryReturn = channel.unary_unary(
        '/store_extra.StoreExtraReturnService/DeliveryReturn',
        request_serializer=store__extra_dot_returns__pb2.DeliveryReturnRequest.SerializeToString,
        response_deserializer=store__extra_dot_returns__pb2.ReturnCommonResponse.FromString,
        )
    self.UpdateReturn = channel.unary_unary(
        '/store_extra.StoreExtraReturnService/UpdateReturn',
        request_serializer=store__extra_dot_returns__pb2.UpdateReturnRequest.SerializeToString,
        response_deserializer=store__extra_dot_returns__pb2.ReturnCommonResponse.FromString,
        )
    self.DeleteReturn = channel.unary_unary(
        '/store_extra.StoreExtraReturnService/DeleteReturn',
        request_serializer=store__extra_dot_returns__pb2.DeleteReturnRequest.SerializeToString,
        response_deserializer=store__extra_dot_returns__pb2.ReturnCommonResponse.FromString,
        )
    self.CheckReturnAvailableByrec = channel.unary_unary(
        '/store_extra.StoreExtraReturnService/CheckReturnAvailableByrec',
        request_serializer=store__extra_dot_returns__pb2.CheckReturnAvailableRequest.SerializeToString,
        response_deserializer=store__extra_dot_returns__pb2.ReturnCommonResponse.FromString,
        )


class StoreExtraReturnServiceServicer(object):
  """门店单据审核-退货相关服务
  """

  def GetValidProduct(self, request, context):
    """查询可退货商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListReturn(self, request, context):
    """查询退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReturnById(self, request, context):
    """根据id查询退货单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReturnProductById(self, request, context):
    """根据id查询退货单商品详情
    查询退货单详情时需调用
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RejectReturn(self, request, context):
    """驳回退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ApproveReturn(self, request, context):
    """审核退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ConfirmReturn(self, request, context):
    """确认退货
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeliveryReturn(self, request, context):
    """确认提货
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateReturn(self, request, context):
    """更新退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteReturn(self, request, context):
    """删除新建的退货单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CheckReturnAvailableByrec(self, request, context):
    """校验按收货原单退货是否超退
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_StoreExtraReturnServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetValidProduct': grpc.unary_unary_rpc_method_handler(
          servicer.GetValidProduct,
          request_deserializer=store__extra_dot_returns__pb2.GetValidProductRequest.FromString,
          response_serializer=store__extra_dot_returns__pb2.ValidProduct.SerializeToString,
      ),
      'ListReturn': grpc.unary_unary_rpc_method_handler(
          servicer.ListReturn,
          request_deserializer=store__extra_dot_returns__pb2.ListReturnRequest.FromString,
          response_serializer=store__extra_dot_returns__pb2.ListReturnResponse.SerializeToString,
      ),
      'GetReturnById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReturnById,
          request_deserializer=store__extra_dot_returns__pb2.GetReturnByIdRequest.FromString,
          response_serializer=store__extra_dot_returns__pb2.Returns.SerializeToString,
      ),
      'GetReturnProductById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReturnProductById,
          request_deserializer=store__extra_dot_returns__pb2.GetReturnProductByIdRequest.FromString,
          response_serializer=store__extra_dot_returns__pb2.GetReturnProductByIdResponse.SerializeToString,
      ),
      'RejectReturn': grpc.unary_unary_rpc_method_handler(
          servicer.RejectReturn,
          request_deserializer=store__extra_dot_returns__pb2.RejectReturnRequest.FromString,
          response_serializer=store__extra_dot_returns__pb2.ReturnCommonResponse.SerializeToString,
      ),
      'ApproveReturn': grpc.unary_unary_rpc_method_handler(
          servicer.ApproveReturn,
          request_deserializer=store__extra_dot_returns__pb2.ApproveReturnRequest.FromString,
          response_serializer=store__extra_dot_returns__pb2.ReturnCommonResponse.SerializeToString,
      ),
      'ConfirmReturn': grpc.unary_unary_rpc_method_handler(
          servicer.ConfirmReturn,
          request_deserializer=store__extra_dot_returns__pb2.ConfirmReturnRequest.FromString,
          response_serializer=store__extra_dot_returns__pb2.ReturnCommonResponse.SerializeToString,
      ),
      'DeliveryReturn': grpc.unary_unary_rpc_method_handler(
          servicer.DeliveryReturn,
          request_deserializer=store__extra_dot_returns__pb2.DeliveryReturnRequest.FromString,
          response_serializer=store__extra_dot_returns__pb2.ReturnCommonResponse.SerializeToString,
      ),
      'UpdateReturn': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateReturn,
          request_deserializer=store__extra_dot_returns__pb2.UpdateReturnRequest.FromString,
          response_serializer=store__extra_dot_returns__pb2.ReturnCommonResponse.SerializeToString,
      ),
      'DeleteReturn': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteReturn,
          request_deserializer=store__extra_dot_returns__pb2.DeleteReturnRequest.FromString,
          response_serializer=store__extra_dot_returns__pb2.ReturnCommonResponse.SerializeToString,
      ),
      'CheckReturnAvailableByrec': grpc.unary_unary_rpc_method_handler(
          servicer.CheckReturnAvailableByrec,
          request_deserializer=store__extra_dot_returns__pb2.CheckReturnAvailableRequest.FromString,
          response_serializer=store__extra_dot_returns__pb2.ReturnCommonResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'store_extra.StoreExtraReturnService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
