# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from store_extra import receive_diff_pb2 as store__extra_dot_receive__diff__pb2


class StoreExtraReceiveDiffServiceStub(object):
  """门店单据审核-收货差异单相关服务（查看、审核）
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.ListReceiveDiff = channel.unary_unary(
        '/store_extra.StoreExtraReceiveDiffService/ListReceiveDiff',
        request_serializer=store__extra_dot_receive__diff__pb2.ListReceiveDiffRequest.SerializeToString,
        response_deserializer=store__extra_dot_receive__diff__pb2.ListReceiveDiffResponse.FromString,
        )
    self.GetReceiveDiffById = channel.unary_unary(
        '/store_extra.StoreExtraReceiveDiffService/GetReceiveDiffById',
        request_serializer=store__extra_dot_receive__diff__pb2.GetReceiveDiffByIdRequest.SerializeToString,
        response_deserializer=store__extra_dot_receive__diff__pb2.ReceiveDiff.FromString,
        )
    self.GetReceiveDiffProductById = channel.unary_unary(
        '/store_extra.StoreExtraReceiveDiffService/GetReceiveDiffProductById',
        request_serializer=store__extra_dot_receive__diff__pb2.GetReceiveDiffProductByIdRequest.SerializeToString,
        response_deserializer=store__extra_dot_receive__diff__pb2.GetReceiveDiffProductByIdResponse.FromString,
        )
    self.ConfirmReceiveDiff = channel.unary_unary(
        '/store_extra.StoreExtraReceiveDiffService/ConfirmReceiveDiff',
        request_serializer=store__extra_dot_receive__diff__pb2.ConfirmReceiveDiffRequet.SerializeToString,
        response_deserializer=store__extra_dot_receive__diff__pb2.CommonResponse.FromString,
        )
    self.RejectReceiveDiff = channel.unary_unary(
        '/store_extra.StoreExtraReceiveDiffService/RejectReceiveDiff',
        request_serializer=store__extra_dot_receive__diff__pb2.RejectReceiveDiffRequest.SerializeToString,
        response_deserializer=store__extra_dot_receive__diff__pb2.CommonResponse.FromString,
        )
    self.UpdateReceiveDiff = channel.unary_unary(
        '/store_extra.StoreExtraReceiveDiffService/UpdateReceiveDiff',
        request_serializer=store__extra_dot_receive__diff__pb2.UpdateReceiveDiffRequest.SerializeToString,
        response_deserializer=store__extra_dot_receive__diff__pb2.CommonResponse.FromString,
        )
    self.DeleteReceiveDiff = channel.unary_unary(
        '/store_extra.StoreExtraReceiveDiffService/DeleteReceiveDiff',
        request_serializer=store__extra_dot_receive__diff__pb2.DeleteReceiveDiffRequest.SerializeToString,
        response_deserializer=store__extra_dot_receive__diff__pb2.CommonResponse.FromString,
        )


class StoreExtraReceiveDiffServiceServicer(object):
  """门店单据审核-收货差异单相关服务（查看、审核）
  """

  def ListReceiveDiff(self, request, context):
    """查询收货差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceiveDiffById(self, request, context):
    """根据id查询收货差异单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceiveDiffProductById(self, request, context):
    """根据id查商品详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ConfirmReceiveDiff(self, request, context):
    """确认收货差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RejectReceiveDiff(self, request, context):
    """驳回收货差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateReceiveDiff(self, request, context):
    """更新收货单差异单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteReceiveDiff(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_StoreExtraReceiveDiffServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'ListReceiveDiff': grpc.unary_unary_rpc_method_handler(
          servicer.ListReceiveDiff,
          request_deserializer=store__extra_dot_receive__diff__pb2.ListReceiveDiffRequest.FromString,
          response_serializer=store__extra_dot_receive__diff__pb2.ListReceiveDiffResponse.SerializeToString,
      ),
      'GetReceiveDiffById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceiveDiffById,
          request_deserializer=store__extra_dot_receive__diff__pb2.GetReceiveDiffByIdRequest.FromString,
          response_serializer=store__extra_dot_receive__diff__pb2.ReceiveDiff.SerializeToString,
      ),
      'GetReceiveDiffProductById': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceiveDiffProductById,
          request_deserializer=store__extra_dot_receive__diff__pb2.GetReceiveDiffProductByIdRequest.FromString,
          response_serializer=store__extra_dot_receive__diff__pb2.GetReceiveDiffProductByIdResponse.SerializeToString,
      ),
      'ConfirmReceiveDiff': grpc.unary_unary_rpc_method_handler(
          servicer.ConfirmReceiveDiff,
          request_deserializer=store__extra_dot_receive__diff__pb2.ConfirmReceiveDiffRequet.FromString,
          response_serializer=store__extra_dot_receive__diff__pb2.CommonResponse.SerializeToString,
      ),
      'RejectReceiveDiff': grpc.unary_unary_rpc_method_handler(
          servicer.RejectReceiveDiff,
          request_deserializer=store__extra_dot_receive__diff__pb2.RejectReceiveDiffRequest.FromString,
          response_serializer=store__extra_dot_receive__diff__pb2.CommonResponse.SerializeToString,
      ),
      'UpdateReceiveDiff': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateReceiveDiff,
          request_deserializer=store__extra_dot_receive__diff__pb2.UpdateReceiveDiffRequest.FromString,
          response_serializer=store__extra_dot_receive__diff__pb2.CommonResponse.SerializeToString,
      ),
      'DeleteReceiveDiff': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteReceiveDiff,
          request_deserializer=store__extra_dot_receive__diff__pb2.DeleteReceiveDiffRequest.FromString,
          response_serializer=store__extra_dot_receive__diff__pb2.CommonResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'store_extra.StoreExtraReceiveDiffService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
