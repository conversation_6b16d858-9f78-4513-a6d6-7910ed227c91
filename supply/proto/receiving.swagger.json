{"swagger": "2.0", "info": {"title": "receiving.proto", "version": "version not set"}, "schemes": ["http", "https"], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v2/supply/receiving": {"get": {"summary": "查询收货单", "operationId": "ListReceiving", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/receivingListReceivingResponse"}}}, "parameters": [{"name": "order_id", "description": "订货单号.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "batch_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "delivery_id", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "batch_code", "description": "订货单号.", "in": "query", "required": false, "type": "string"}, {"name": "delivery_code", "description": "发货单号.", "in": "query", "required": false, "type": "string"}, {"name": "code", "description": "收货单号.", "in": "query", "required": false, "type": "string"}, {"name": "order_code", "description": "要货单号.", "in": "query", "required": false, "type": "string"}, {"name": "receive_bys", "description": "接受方id.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "delivery_bys", "description": "发送方.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "status", "description": "订单状态.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "process_status", "description": "订单状态.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "start_date", "description": "订货开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "订货结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "distr_type", "description": "物流类型.", "in": "query", "required": false, "type": "string"}, {"name": "batch_type", "description": "业务类型.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "limit", "description": "分页大小.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "跳过行数.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "sort", "description": "排序字段.", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序顺序.", "in": "query", "required": false, "type": "string"}, {"name": "main_branch_type", "description": "单据main_branch类型：S-门店，W-仓库，V-供应商\n收货单receive_by是main_branch.", "in": "query", "required": false, "type": "string"}, {"name": "delivery_start_date", "description": "配送开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "delivery_end_date", "description": "配送结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "cost_trans_status", "description": "传输成本引擎 0:未传输，1:已传输.", "in": "query", "required": false, "type": "string"}, {"name": "received_start_date", "description": "收货开始时间.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "received_end_date", "description": "收货结束时间.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "sub_account_type", "description": "子账户类型.", "in": "query", "required": false, "type": "string"}, {"name": "sub_receive_bys", "description": "子账户接收方.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "sub_delivery_bys", "description": "子账户发送方.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "geo_regions", "description": "收货方地理区域 —— 目前只支持门店.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "demand_type", "description": "订单类型.", "in": "query", "required": false, "type": "string"}, {"name": "product_ids", "description": "商品ids.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "include_products", "description": "是否包含商品.", "in": "query", "required": false, "type": "string"}, {"name": "ids", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "expect_start_date", "description": "预计到货日期开始时间.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "expect_end_date", "description": "预计到货日期结束时间.", "in": "query", "required": false, "type": "string", "format": "date-time"}], "tags": ["ReceivingService"]}, "post": {"summary": "创建收货单\nresponse?", "operationId": "CreateReceiving", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/receivingCreateReceivingResponse"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/receivingCreateReceivingRequest"}}], "tags": ["ReceivingService"]}}, "/api/v2/supply/receiving/cold": {"get": {"summary": "查询收货单--冷链", "operationId": "ListReceivingCold", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/receivingListReceivingColdResponse"}}}, "parameters": [{"name": "order_id", "description": "订货单号.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "store_ids", "description": "门店id.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "status", "description": "订单状态\nenum Status{\n    INITED = 0;\n    CONFIRMED = 1;\n}.", "in": "query", "required": false, "type": "array", "items": {"type": "string"}}, {"name": "has_diffs", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "start_date", "description": "配送开始日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "end_date", "description": "配送结束日期.", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "limit", "description": "要货订单号/ 订货单号\nuint64 order_id = 9;\n分页大小.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "跳过行数.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "include_total", "description": "返回总条数.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "is_direct", "description": "是否直送.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "jde_order_id", "description": "jde单号.", "in": "query", "required": false, "type": "string", "format": "uint64"}, {"name": "code", "description": "收货单号.", "in": "query", "required": false, "type": "string"}, {"name": "geo_regions", "description": "地理区域.", "in": "query", "required": false, "type": "array", "items": {"type": "string", "format": "uint64"}}, {"name": "logistics_type", "description": "单据物流来源类型:BREAD 面包工厂.", "in": "query", "required": false, "type": "string"}, {"name": "is_adjust", "description": "调整单标志.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "sort", "description": "排序字段.", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序顺序.", "in": "query", "required": false, "type": "string"}], "tags": ["ReceivingService"]}}, "/api/v2/supply/receiving/{id}": {"get": {"summary": "根据id查询收货单详情", "operationId": "GetReceivingById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/receivingReceiving"}}}, "parameters": [{"name": "id", "description": "收货单id(receiving_id)", "in": "path", "required": true, "type": "string", "format": "uint64"}], "tags": ["ReceivingService"]}}, "/api/v2/supply/receiving/{id}/confirm": {"put": {"summary": "确认收货", "operationId": "ConfirmReceiving", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/receivingConfirmReceivingResponse"}}}, "parameters": [{"name": "id", "description": "收货单id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/receivingConfirmReceivingRequest"}}], "tags": ["ReceivingService"]}}, "/api/v2/supply/receiving/{id}/product": {"get": {"summary": "根据receiving_id查商品详情", "operationId": "GetReceivingProductById", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/receivingGetReceivingProductByIdResponse"}}}, "parameters": [{"name": "id", "description": "id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "limit", "description": "分页大小.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "offset", "description": "跳过行数.", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "include_total", "description": "返回总条数.", "in": "query", "required": false, "type": "boolean", "format": "boolean"}, {"name": "sort", "description": "排序字段.", "in": "query", "required": false, "type": "string"}, {"name": "order", "description": "排序顺序.", "in": "query", "required": false, "type": "string"}], "tags": ["ReceivingService"]}}, "/api/v2/supply/receiving/{id}/suspend": {"put": {"summary": "货品未到店，收货单挂起", "operationId": "SuspendReceiving", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/receivingSuspendReceivingResponse"}}}, "parameters": [{"name": "id", "description": "收货单id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/receivingSuspendReceivingRequest"}}], "tags": ["ReceivingService"]}}, "/api/v2/supply/receiving/{id}/update": {"put": {"summary": "更新收货单\ndelete", "operationId": "UpdateReceiving", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/receivingUpdateReceivingResponse"}}}, "parameters": [{"name": "id", "description": "收货单id", "in": "path", "required": true, "type": "string", "format": "uint64"}, {"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/receivingUpdateReceivingRequest"}}], "tags": ["ReceivingService"]}}}, "definitions": {"receivingConfirmReceivingRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "收货单id"}, "confirmed_products": {"type": "array", "items": {"$ref": "#/definitions/receivingProduct"}, "title": "确认数量"}, "has_checked": {"type": "boolean", "format": "boolean", "title": "是否盲点？"}, "remark": {"type": "string", "title": "备注"}, "delay_reason": {"type": "string", "title": "延迟原因"}, "attachments": {"type": "string", "title": "附件"}, "nosign_reason": {"type": "string", "title": "不签名的原因"}, "signature": {"type": "string", "title": "签名"}}}, "receivingConfirmReceivingResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "receivingCreateReceivingRequest": {"type": "object", "properties": {"request_id": {"type": "string", "format": "uint64", "title": "request_id 校验请求是否唯一\nMia：是否可以用要货单id"}, "code": {"type": "string", "title": "收货单单号"}, "master_id": {"type": "string", "format": "uint64", "title": "订货单id\n原master_id"}, "master_code": {"type": "string", "title": "订货单单号"}, "demand_order_id": {"type": "string", "format": "uint64", "title": "要货单id"}, "demand_order_code": {"type": "string", "title": "要货单单号\n原order_id"}, "jde_order_id": {"type": "string", "format": "uint64", "title": "JDE订单号\n原sap_order_id, 类型需确认"}, "storage_type": {"type": "string", "title": "储藏方式"}, "received_by": {"type": "string", "format": "uint64", "title": "接收门店-门店id"}, "store_secondary_id": {"type": "string", "title": "jde公司编号-kcoo"}, "delivery_note_number": {"type": "string", "title": "配送单号 \nMia：？"}, "delivery_by": {"type": "string", "title": "配送方"}, "delivery_date": {"type": "string", "format": "date-time", "title": "配送时间"}, "demand_date": {"type": "string", "format": "date-time", "title": "订货日期"}, "is_direct": {"type": "boolean", "format": "boolean", "title": "配送方式，True：直送，False：配送"}, "logistics_type": {"type": "string", "title": "单据物流来源类型: BREAD 面包工厂"}, "products": {"type": "array", "items": {"$ref": "#/definitions/receivingProduct"}}, "master_type": {"type": "string", "title": "收货单生成来源：TRANSFER-调拨单；None-要货单"}, "expected_arrival_date": {"type": "string", "format": "date-time"}, "actual_arrival_date": {"type": "string", "format": "date-time"}, "is_adjust": {"type": "boolean", "format": "boolean", "title": "调整单标志"}}}, "receivingCreateReceivingResponse": {"type": "object", "properties": {"receiving_id": {"type": "string", "format": "uint64"}}, "title": "Mia：是否可以直接返回操作结果，而不附加数据"}, "receivingGetReceivingProductByIdResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/receivingProduct"}, "title": "wrapper.Status status = 1;"}, "total": {"type": "string", "format": "uint64"}}}, "receivingListReceivingColdResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/receivingReceiving"}}, "total": {"type": "string", "format": "uint64"}}}, "receivingListReceivingResponse": {"type": "object", "properties": {"rows": {"type": "array", "items": {"$ref": "#/definitions/receivingReceiving"}}, "total": {"type": "string", "format": "uint64"}}}, "receivingProduct": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "receiving_id": {"type": "string", "format": "uint64", "title": "收货单id"}, "received_by": {"type": "string", "format": "uint64", "title": "收货门店"}, "product_id": {"type": "string", "format": "uint64", "title": "商品id"}, "accounting_unit_id": {"type": "string", "format": "uint64", "title": "核算单位id"}, "unit_id": {"type": "string", "format": "uint64", "title": "单位id"}, "product_code": {"type": "string", "title": "商品编码"}, "product_name": {"type": "string", "title": "商品名称"}, "material_number": {"type": "string", "title": "物料编码"}, "accounting_unit_name": {"type": "string", "title": "单位名称"}, "accounting_unit_spec": {"type": "string", "title": "单位规格"}, "unit_name": {"type": "string", "title": "核算单位名称"}, "unit_spec": {"type": "string", "title": "核算单位规格"}, "unit_rate": {"type": "number", "format": "double", "title": "转换比率"}, "storage_type": {"type": "string", "title": "存储方式\nenum STORAGE {\n    \"干货\" = 1;\n    不干货 = 2;\n}"}, "required_accounting_quantity": {"type": "number", "format": "double", "title": "订货数量"}, "required_quantity": {"type": "number", "format": "double", "title": "核算订货数量"}, "received_accounting_quantity": {"type": "number", "format": "double", "title": "收货数量"}, "received_quantity": {"type": "number", "format": "double", "title": "核算收货数量"}, "confirmed_accounting_quantity": {"type": "number", "format": "double", "title": "确认收货数量"}, "confirmed_quantity": {"type": "number", "format": "double", "title": "核算确认收货数量"}, "is_confirmed": {"type": "boolean", "format": "boolean", "title": "收货状态"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "created_by": {"type": "string", "format": "uint64", "title": "创建人id"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新日期"}, "updated_by": {"type": "string", "format": "uint64", "title": "更新人id"}, "jde_product_id": {"type": "string", "format": "uint64", "title": "jde预留商品id"}, "demand_date": {"type": "string", "format": "date-time", "title": "订货日期"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "created_name": {"type": "string", "title": "创建人"}, "updated_name": {"type": "string", "title": "更新人"}, "jde_line": {"type": "number", "format": "double"}, "hws_line": {"type": "integer", "format": "int64"}, "is_first_delivery": {"type": "string", "title": "首配标志"}, "price": {"type": "number", "format": "double", "title": "成本价"}, "tax_price": {"type": "number", "format": "double", "title": "含税价"}, "tax_rate": {"type": "number", "format": "double", "title": "税率"}, "sale_type": {"type": "string", "title": "销售类型"}}}, "receivingReceiving": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "收货单id"}, "code": {"type": "string", "title": "收货单单号"}, "master_id": {"type": "string", "format": "uint64", "title": "订货单id\n原master_id"}, "master_code": {"type": "string", "title": "订货单单号"}, "demand_order_id": {"type": "string", "format": "uint64", "title": "要货单id"}, "demand_order_code": {"type": "string", "title": "要货单单号\n原order_id"}, "receiving_diff_id": {"type": "string", "format": "uint64", "title": "收货差异单id\n为空，则无收货差异，增减库存；不为空，在确认收货差异单后再增减库存"}, "jde_order_id": {"type": "string", "format": "uint64", "title": "JDE订单号"}, "received_by": {"type": "string", "format": "uint64", "title": "接收门店-门店id"}, "store_secondary_id": {"type": "string", "title": "jde公司编号-kcoo"}, "status": {"type": "string", "title": "收货单状态\nenum Status {\n    // 新建/未收货\n    INITED = 0;\n    // 已确认\n    CONFIRMED = 1;\n}"}, "delivery_note_number": {"type": "string", "title": "配送单号 \nMia：？"}, "delivery_by": {"type": "string", "title": "配送方"}, "delivery_date": {"type": "string", "format": "date-time", "title": "配送时间"}, "is_direct": {"type": "boolean", "format": "boolean", "title": "是否直送\nMia：冷链/ 大货？"}, "calculate_inventory": {"type": "boolean", "format": "boolean", "title": "是否需要增减库存"}, "storage_type": {"type": "string", "title": "存储类型 - 干货/？？"}, "remark": {"type": "string", "title": "备注"}, "inventory_status": {"type": "string", "title": "库存引擎调用\nenum inventoryStatus {\n    SENT = 1; // 已发送\n    SENT_SUC = 2; //调用成功\n    SENT_FAIL = 3; //调用失败\n}"}, "inventory_req_id": {"type": "string", "format": "uint64", "title": "库存id预留字段"}, "process_status": {"type": "string", "title": "差异单生成状态\nenum DiffProcessStatus {\n    INIT = 0; // \n    CREATED = 1; // 收货差异单生成\n}"}, "created_at": {"type": "string", "format": "date-time", "title": "创建时间"}, "updated_at": {"type": "string", "format": "date-time", "title": "更新时间"}, "created_by": {"type": "string", "format": "int64", "title": "创建人id"}, "updated_by": {"type": "string", "format": "int64", "title": "更新人id"}, "demand_date": {"type": "string", "format": "date-time", "title": "订货时间"}, "received_type": {"type": "string", "title": "收货类型"}, "has_checked": {"type": "boolean", "format": "boolean", "title": "是否已验证"}, "partner_id": {"type": "string", "format": "uint64", "title": "商户id"}, "delay_reason": {"type": "string", "title": "延迟原因"}, "attachments": {"type": "string", "title": "附件"}, "product_nums": {"type": "string", "format": "uint64", "title": "商品数量"}, "created_name": {"type": "string", "title": "创建人"}, "updated_name": {"type": "string", "title": "更新人"}, "expected_arrival_date": {"type": "string", "format": "date-time", "title": "预计到货日期"}, "actual_arrival_date": {"type": "string", "format": "date-time", "title": "实际到货日期-JDE"}, "nosign_reason": {"type": "string", "title": "不签名的原因"}, "signature": {"type": "string", "title": "签名"}, "logistics_type": {"type": "string", "title": "单据物流来源类型:BREAD 面包工厂"}, "is_adjust": {"type": "boolean", "format": "boolean", "title": "调整单标志"}, "send_type": {"type": "string", "title": "对接三方的渠道"}}}, "receivingSuspendReceivingRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "收货单id"}, "remark": {"type": "string", "title": "备注"}, "delay_reason": {"type": "string", "title": "延迟原因"}, "attachments": {"type": "string", "title": "附件"}, "nosign_reason": {"type": "string", "title": "不签名的原因"}, "signature": {"type": "string", "title": "签名"}}}, "receivingSuspendReceivingResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}}}, "receivingUpdateReceivingRequest": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "收货单id"}, "product": {"type": "array", "items": {"$ref": "#/definitions/receivingProduct"}, "title": "product"}}}, "receivingUpdateReceivingResponse": {"type": "object", "properties": {"result": {"type": "boolean", "format": "boolean"}, "payload": {"$ref": "#/definitions/receivingReceiving"}}}}}