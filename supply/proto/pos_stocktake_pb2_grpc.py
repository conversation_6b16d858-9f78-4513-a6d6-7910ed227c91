# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import pos_stocktake_pb2 as pos__stocktake__pb2


class PosStocktakeStub(object):
  """固定资产收货相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.UploadPosStocktake = channel.unary_unary(
        '/pos_stocktake.PosStocktake/UploadPosStocktake',
        request_serializer=pos__stocktake__pb2.UploadPosStocktakeRequest.SerializeToString,
        response_deserializer=pos__stocktake__pb2.UploadPosStocktakeResponse.FromString,
        )
    self.GetStocktakeQuantity = channel.unary_unary(
        '/pos_stocktake.PosStocktake/GetStocktakeQuantity',
        request_serializer=pos__stocktake__pb2.GetStocktakeQuantityRequest.SerializeToString,
        response_deserializer=pos__stocktake__pb2.GetStocktakeQuantityReponse.FromString,
        )


class PosStocktakeServicer(object):
  """固定资产收货相关服务
  """

  def UploadPosStocktake(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetStocktakeQuantity(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_PosStocktakeServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'UploadPosStocktake': grpc.unary_unary_rpc_method_handler(
          servicer.UploadPosStocktake,
          request_deserializer=pos__stocktake__pb2.UploadPosStocktakeRequest.FromString,
          response_serializer=pos__stocktake__pb2.UploadPosStocktakeResponse.SerializeToString,
      ),
      'GetStocktakeQuantity': grpc.unary_unary_rpc_method_handler(
          servicer.GetStocktakeQuantity,
          request_deserializer=pos__stocktake__pb2.GetStocktakeQuantityRequest.FromString,
          response_serializer=pos__stocktake__pb2.GetStocktakeQuantityReponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'pos_stocktake.PosStocktake', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
