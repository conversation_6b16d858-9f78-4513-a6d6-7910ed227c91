syntax = "proto3";
package low_cost;
import "google/api/annotations.proto";
// import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

// 库存报表相关服务
service LowCostService{
    rpc ListLowCost(ListLowCostRequest) returns (ListLowCostResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/low_cost"
        };
    }
}


message ListLowCostRequest{
    repeated uint64 store_ids = 1;
    repeated uint64 product_ids = 2;
    google.protobuf.Timestamp end_date = 3;
    google.protobuf.Timestamp start_date = 4;
    string glpt = 5;
    uint32 limit = 6;
    uint32 offset = 7; 
    
}

message ListLowCostResponse{
    repeated LowCost rows = 1;
    uint32 total = 2;

}

message LowCost{
    // 预留字段id
    uint64 id = 1;
    // 门店id
    uint64 store_id = 2;
    // 门店名称
    string store_name = 3;
    // 门店编码
    string store_code = 4;
    // 商品id
    uint64 product_id = 5;
    // 商品编码
    string product_code = 6;
    // 商品名称
    string product_name = 7;
    // 类别id
    uint64 category_id = 8;
    // 类别编码
    string category_code = 9;
    // 类别名称
    string category_name = 10;
    // 规格
    string spec = 11;
    // 订单编码
    string order_code = 12;
    // 订单类型
    string order_type = 13;
    // jde相关
    string jde_order_id = 14;
    string jde_order_type = 15;
    string jde_mcu = 16;
    // 订单时间
    google.protobuf.Timestamp order_time = 17;
    // 操作
    string action = 18;
    // 状态
    string status = 19;
    // 流水数量
    double qty = 20;
    // 库存id
    string stock_id = 21;
    // 核算单位id
    uint64 accounting_unit_id = 22;
    // 核算单位编码
    string accounting_unit_code = 23;
    // 核算单位名称
    string accounting_unit_name = 24;
    // 总账级分类
    string glpt = 25;
    // 
   uint64  partner_id = 26;
   google.protobuf.Timestamp created_at = 27;
   google.protobuf.Timestamp updated_at = 28;
   string origin_order_code = 29;
   string inventory_id = 30;
   string product_status = 31;
   // 订单时间
   google.protobuf.Timestamp operation_time = 32;
}