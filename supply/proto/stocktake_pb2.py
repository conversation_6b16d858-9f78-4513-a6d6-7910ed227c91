# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: stocktake.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='stocktake.proto',
  package='stocktake',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x0fstocktake.proto\x12\tstocktake\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x13\n\x04Pong\x12\x0b\n\x03msg\x18\x01 \x01(\t\"p\n GetProductByStocktakeTypeRequest\x12\x16\n\x0estocktake_type\x18\x01 \x01(\t\x12\x11\n\tbranch_id\x18\x02 \x01(\x04\x12\x14\n\x0cstorage_type\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\"b\n\x14StocktakeProductType\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\n\n\x02id\x18\x02 \x01(\x04\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x0c\n\x04spec\x18\x04 \x01(\t\x12\x14\n\x0cstorage_type\x18\x05 \x01(\t\"R\n!GetProductByStocktakeTypeResponse\x12-\n\x04rows\x18\x01 \x03(\x0b\x32\x1f.stocktake.StocktakeProductType\"9\n\x1aGetStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\xff\t\n\tStocktake\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x17\n\x0f\x62ranch_batch_id\x18\x03 \x01(\x04\x12\x11\n\tbranch_id\x18\x04 \x01(\x04\x12\x13\n\x0bschedule_id\x18\x05 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x06 \x01(\t\x12\x1b\n\x13\x63\x61lculate_inventory\x18\x07 \x01(\x08\x12\x0c\n\x04\x63ode\x18\x08 \x01(\t\x12\x13\n\x0b\x66orecasting\x18\t \x01(\x08\x12\x18\n\x10\x66orecasting_time\x18\n \x01(\t\x12\x0e\n\x06remark\x18\x0b \x01(\t\x12\x13\n\x0bresult_type\x18\x0c \x01(\t\x12\x15\n\rschedule_code\x18\r \x01(\t\x12\x14\n\x0cst_diff_flag\x18\x0e \x01(\x05\x12+\n\x06status\x18\x0f \x01(\x0e\x32\x1b.stocktake.Stocktake.STATUS\x12\x33\n\x0eprocess_status\x18\x10 \x01(\x0e\x32\x1b.stocktake.Stocktake.STATUS\x12\x1a\n\x12store_secondary_id\x18\x11 \x01(\x04\x12\x0c\n\x04type\x18\x12 \x01(\t\x12\x12\n\ncreated_by\x18\x13 \x01(\x04\x12\x12\n\nupdated_by\x18\x14 \x01(\x04\x12\x11\n\treview_by\x18\x15 \x01(\x04\x12/\n\x0btarget_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07user_id\x18\x19 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1a \x01(\t\x12\x14\n\x0cupdated_name\x18\x1b \x01(\t\x12\x15\n\rschedule_name\x18\x1c \x01(\t\x12\x18\n\x10\x64iff_err_message\x18\x1d \x01(\t\x12\x19\n\x11month_err_message\x18\x1f \x01(\t\x12\x15\n\roriginal_code\x18# \x01(\t\x12\x17\n\x0foriginal_doc_id\x18$ \x01(\x04\x12\x13\n\x0bis_recreate\x18% \x01(\x08\x12\x15\n\rrecreate_code\x18& \x01(\t\x12\x17\n\x0frecreate_doc_id\x18\' \x01(\x04\x12\x13\n\x0bsubmit_name\x18( \x01(\t\x12\x14\n\x0c\x61pprove_name\x18) \x01(\t\x12\x16\n\x0estocktake_type\x18* \x01(\t\x12\x12\n\nrequest_id\x18+ \x01(\x04\x12\x14\n\x0ctotal_amount\x18\x32 \x01(\x01\x12\x19\n\x11total_diff_amount\x18\x33 \x01(\x01\x12\x1a\n\x12total_sales_amount\x18\x34 \x01(\x01\x12\x1f\n\x17total_diff_sales_amount\x18\x35 \x01(\x01\x12+\n\x0b\x61ttachments\x18\x36 \x03(\x0b\x32\x16.stocktake.Attachments\"\x87\x01\n\x06STATUS\x12\x0b\n\x07STARTED\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tCANCELLED\x10\x02\x12\r\n\tSUBMITTED\x10\x03\x12\x0c\n\x08REJECTED\x10\x04\x12\x0c\n\x08\x41PPROVED\x10\x05\x12\r\n\tCONFIRMED\x10\x06\x12\x0c\n\x08SUBMIT_0\x10\x07\x12\r\n\tAPPROVE_0\x10\x08\"\xc2\x06\n\x13GetStocktakeRequest\x12\x15\n\rinclude_total\x18\x01 \x01(\x08\x12\x11\n\tstore_ids\x18\x02 \x03(\x04\x12\x12\n\nbranch_ids\x18\x03 \x03(\x04\x12=\n\x0cstore_status\x18\x04 \x03(\x0e\x32\'.stocktake.GetStocktakeRequest.S_STATUS\x12\x34\n\x05_type\x18\x05 \x03(\x0e\x32%.stocktake.GetStocktakeRequest.S_TYPE\x12\x35\n\x06status\x18\x06 \x03(\x0e\x32%.stocktake.GetStocktakeRequest.STATUS\x12\x15\n\rschedule_code\x18\x07 \x01(\t\x12\x13\n\x0bproduct_ids\x18\x08 \x03(\x04\x12\x0e\n\x06offset\x18\t \x01(\r\x12\r\n\x05limit\x18\n \x01(\r\x12.\n\nstart_date\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0btarget_date\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x63ode\x18\x0e \x01(\t\x12\x0b\n\x03ids\x18\x15 \x03(\x04\x12\x11\n\tis_create\x18\x0f \x01(\x08\x12\x16\n\x0estocktake_type\x18\x10 \x01(\t\x12\r\n\x05order\x18\x11 \x01(\t\x12\x0c\n\x04sort\x18\x12 \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x13 \x01(\t\x12\x0b\n\x03lan\x18\x14 \x01(\t\x12\x15\n\rschedule_name\x18\x16 \x01(\t\"\"\n\x08S_STATUS\x12\n\n\x06OPENED\x10\x00\x12\n\n\x06\x43LOSED\x10\x01\"\x1d\n\x06S_TYPE\x12\x05\n\x01W\x10\x00\x12\x05\n\x01\x44\x10\x01\x12\x05\n\x01M\x10\x02\"\x87\x01\n\x06STATUS\x12\x0b\n\x07STARTED\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tCANCELLED\x10\x02\x12\r\n\tSUBMITTED\x10\x03\x12\x0c\n\x08REJECTED\x10\x04\x12\x0c\n\x08\x41PPROVED\x10\x05\x12\r\n\tCONFIRMED\x10\x06\x12\x0c\n\x08SUBMIT_0\x10\x07\x12\r\n\tAPPROVE_0\x10\x08\"I\n\x14GetStocktakeResponse\x12\"\n\x04rows\x18\x01 \x03(\x0b\x32\x14.stocktake.Stocktake\x12\r\n\x05total\x18\x02 \x01(\r\"\xdf\x01\n\x1aGetStocktakeProductRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x14\n\x0cinclude_unit\x18\x02 \x01(\x08\x12\r\n\x05limit\x18\x03 \x01(\r\x12\x0e\n\x06offset\x18\x04 \x01(\r\x12\x15\n\rinclude_total\x18\x05 \x01(\x08\x12\x13\n\x0b\x63\x61tegory_id\x18\x06 \x01(\x04\x12\x14\n\x0cstorage_type\x18\x07 \x01(\t\x12\x14\n\x0cproduct_name\x18\x08 \x01(\t\x12\x0b\n\x03lan\x18\t \x01(\t\x12\x17\n\x0finclude_barcode\x18\n \x01(\x08\"\xf7\x04\n\x17StocktakeProductTagName\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06\x64oc_id\x18\x02 \x01(\x04\x12\x0e\n\x06stp_id\x18\x03 \x01(\x04\x12\x12\n\nproduct_id\x18\x04 \x01(\x04\x12\x0e\n\x06tag_id\x18\x05 \x01(\x04\x12\x10\n\x08tag_name\x18\x06 \x01(\t\x12\x14\n\x0ctag_quantity\x18\x07 \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x08 \x01(\x01\x12\x0f\n\x07unit_id\x18\t \x01(\x04\x12\x11\n\tunit_spec\x18\n \x01(\t\x12\x11\n\tunit_name\x18\x0b \x01(\t\x12\x11\n\tunit_rate\x18\x0c \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\r \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x0e \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x0f \x01(\t\x12\x12\n\npartner_id\x18\x10 \x01(\x04\x12\x0f\n\x07user_id\x18\x11 \x01(\x04\x12\x12\n\ncreated_by\x18\x12 \x01(\x04\x12\x12\n\nupdated_by\x18\x13 \x01(\x04\x12.\n\ncreated_at\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\x16 \x01(\t\x12\x14\n\x0cupdated_name\x18\x17 \x01(\t\x12\x10\n\x08tax_rate\x18\x18 \x01(\x01\x12\x11\n\ttax_price\x18\x19 \x01(\x01\x12\x12\n\ncost_price\x18\x1a \x01(\x01\x12\x13\n\x0bsales_price\x18\x1b \x01(\x01\"\xd3\x01\n\x15StocktakeProductUnits\x12\x0f\n\x07unit_id\x18\x01 \x01(\x04\x12\x11\n\tunit_name\x18\x02 \x01(\t\x12\x11\n\tunit_spec\x18\x03 \x01(\t\x12\x11\n\tunit_rate\x18\x04 \x01(\x01\x12\x10\n\x08tax_rate\x18\x05 \x01(\x01\x12\x11\n\ttax_price\x18\x06 \x01(\x01\x12\x12\n\ncost_price\x18\x07 \x01(\x01\x12\x13\n\x0bsales_price\x18\x08 \x01(\x01\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\t \x01(\x08\x12\x11\n\tstocktake\x18\n \x01(\x08\"\x9a\x0b\n\x10StocktakeProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x0e\n\x06\x64oc_id\x18\x03 \x01(\x04\x12\x11\n\tbranch_id\x18\x04 \x01(\x04\x12\x12\n\nproduct_id\x18\x05 \x01(\x04\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x06 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x07 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x08 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\t \x01(\t\x12\x0c\n\x04\x63ode\x18\n \x01(\t\x12\x0f\n\x07\x64\x65leted\x18\x0b \x01(\x08\x12\x15\n\rdiff_quantity\x18\x0c \x01(\x01\x12\x15\n\rdisplay_order\x18\r \x01(\t\x12\x0f\n\x07\x65xtends\x18\x0e \x01(\t\x12\x0f\n\x07ignored\x18\x0f \x01(\x08\x12\x1a\n\x12inventory_quantity\x18\x10 \x01(\x01\x12\x11\n\tis_system\x18\x11 \x01(\x08\x12\x13\n\x0bitem_number\x18\x12 \x01(\x04\x12\x17\n\x0fmaterial_number\x18\x13 \x01(\t\x12\x14\n\x0cproduct_code\x18\x14 \x01(\t\x12\x14\n\x0cproduct_name\x18\x15 \x01(\t\x12\x10\n\x08quantity\x18\x16 \x01(\x01\x12\x0f\n\x07st_type\x18\x17 \x01(\t\x12\x14\n\x0cstorage_type\x18\x18 \x01(\t\x12\x1a\n\x12unit_diff_quantity\x18\x19 \x01(\x01\x12\x0f\n\x07unit_id\x18\x1a \x01(\x04\x12\x11\n\tunit_name\x18\x1b \x01(\t\x12\x11\n\tunit_rate\x18\x1c \x01(\x01\x12\x11\n\tunit_spec\x18\x1d \x01(\t\x12/\n\x05units\x18\x1e \x03(\x0b\x32 .stocktake.StocktakeProductUnits\x12\x12\n\ncreated_by\x18\x1f \x01(\x04\x12\x12\n\nupdated_by\x18  \x01(\x04\x12/\n\x0btarget_date\x18! \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\" \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18# \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07user_id\x18$ \x01(\x04\x12\x0e\n\x06status\x18% \x01(\t\x12\x0e\n\x06is_pda\x18& \x01(\x08\x12\x38\n\x0cproduct_tags\x18\' \x03(\x0b\x32\".stocktake.StocktakeProductTagName\x12\x10\n\x08is_empty\x18( \x01(\x08\x12\x14\n\x0c\x63reated_name\x18) \x01(\t\x12\x14\n\x0cupdated_name\x18* \x01(\t\x12\x0f\n\x07is_null\x18+ \x01(\x08\x12\x14\n\x0ctag_quantity\x18, \x01(\x01\x12#\n\x1b\x63onvert_accounting_quantity\x18- \x01(\x01\x12\x0e\n\x06is_bom\x18. \x01(\x08\x12\x1c\n\x14\x61llow_stocktake_edit\x18/ \x01(\x08\x12\x0c\n\x04spec\x18\x30 \x01(\t\x12\x12\n\ndiff_price\x18\x31 \x01(\x01\x12\x0f\n\x07\x62\x61rcode\x18\x32 \x03(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x36 \x01(\x04\x12\x13\n\x0bposition_id\x18\x37 \x01(\x04\x12\x15\n\rcategory_name\x18\x38 \x01(\t\x12\x10\n\x08tax_rate\x18\x39 \x01(\x01\x12\x11\n\ttax_price\x18: \x01(\x01\x12\x0e\n\x06\x61mount\x18; \x01(\x01\x12\x13\n\x0b\x64iff_amount\x18< \x01(\x01\x12\x12\n\ncost_price\x18= \x01(\x01\x12\x14\n\x0csales_amount\x18> \x01(\x01\x12\x13\n\x0bsales_price\x18? \x01(\x01\x12\x19\n\x11\x64iff_sales_amount\x18@ \x01(\x01\"\x94\x01\n\x1bGetStocktakeProductResponse\x12)\n\x04rows\x18\x01 \x03(\x0b\x32\x1b.stocktake.StocktakeProduct\x12\r\n\x05total\x18\x02 \x01(\x04\x12;\n\rposition_rows\x18\x03 \x03(\x0b\x32$.stocktake.StocktakePositionProducts\"\x90\x01\n\x1aUserCreateStocktakeRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x11\n\tbranch_id\x18\x02 \x01(\x04\x12\r\n\x05_type\x18\x03 \x01(\t\x12/\n\x0btarget_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0b\n\x03lan\x18\x05 \x01(\t\"\x9b\x06\n\x1bUserCreateStocktakeResponse\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x17\n\x0f\x62ranch_batch_id\x18\x03 \x01(\x04\x12\x11\n\tbranch_id\x18\x04 \x01(\x04\x12\x13\n\x0bschedule_id\x18\x05 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x06 \x01(\t\x12\x1b\n\x13\x63\x61lculate_inventory\x18\x07 \x01(\x08\x12\x0c\n\x04\x63ode\x18\x08 \x01(\t\x12\x13\n\x0b\x66orecasting\x18\t \x01(\x08\x12\x18\n\x10\x66orecasting_time\x18\n \x01(\t\x12\x0e\n\x06remark\x18\x0b \x01(\t\x12\x13\n\x0bresult_type\x18\x0c \x01(\t\x12\x15\n\rschedule_code\x18\r \x01(\t\x12\x14\n\x0cst_diff_flag\x18\x0e \x01(\x05\x12=\n\x06status\x18\x0f \x01(\x0e\x32-.stocktake.UserCreateStocktakeResponse.STATUS\x12\x45\n\x0eprocess_status\x18\x10 \x01(\x0e\x32-.stocktake.UserCreateStocktakeResponse.STATUS\x12\x1a\n\x12store_secondary_id\x18\x11 \x01(\x04\x12\x0c\n\x04type\x18\x12 \x01(\t\x12\x12\n\ncreated_by\x18\x13 \x01(\x04\x12\x12\n\nupdated_by\x18\x14 \x01(\x04\x12\x11\n\treview_by\x18\x15 \x01(\x04\x12/\n\x0btarget_date\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07user_id\x18\x19 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1a \x01(\t\x12\x14\n\x0cupdated_name\x18\x1b \x01(\t\"!\n\x06STATUS\x12\x0b\n\x07STARTED\x10\x00\x12\n\n\x06INITED\x10\x01\"b\n\x0bTagQuantity\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06tag_id\x18\x02 \x01(\x04\x12\x10\n\x08tag_name\x18\x03 \x01(\t\x12\x14\n\x0ctag_quantity\x18\x04 \x01(\x01\x12\x0f\n\x07unit_id\x18\x05 \x01(\x04\"\xbb\x01\n\x14PutStocktakeProducts\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08quantity\x18\x02 \x01(\x01\x12\x0f\n\x07unit_id\x18\x03 \x01(\x04\x12,\n\x0ctag_products\x18\x04 \x03(\x0b\x32\x16.stocktake.TagQuantity\x12\x0e\n\x06is_pda\x18\x05 \x01(\x08\x12\x10\n\x08is_empty\x18\x06 \x01(\x08\x12\x0f\n\x07is_null\x18\x07 \x01(\x08\x12\x13\n\x0b\x64\x65l_tag_ids\x18\x08 \x03(\x04\"\xab\x01\n\x1aPutStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x31\n\x08products\x18\x02 \x03(\x0b\x32\x1f.stocktake.PutStocktakeProducts\x12\x0b\n\x03lan\x18\x03 \x01(\t\x12\x10\n\x08\x61ll_zero\x18\x04 \x01(\x08\x12+\n\x0b\x61ttachments\x18\x05 \x03(\x0b\x32\x16.stocktake.Attachments\"(\n\x0b\x41ttachments\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\"L\n\x1dRejectStocktakeProductRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x0e\n\x06reason\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\"0\n\x1eRejectStocktakeProductResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"-\n\x1bPutStocktakeByDocIDResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"_\n\x1c\x43heckStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\r\n\x05\x63heck\x18\x02 \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\"G\n\x1b\x43heckStocktakeByDocIDDetail\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\"\xce\x05\n\x1d\x43heckStocktakeByDocIDResponse\x12\x0f\n\x07handler\x18\x01 \x01(\x08\x12\x36\n\x06\x61\x64just\x18\x02 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12>\n\x0ereceiving_diff\x18\x03 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12\x38\n\x08transfer\x18\x04 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12\x39\n\treceiving\x18\x05 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12\x36\n\x06return\x18\x06 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12@\n\x10\x64irect_receiving\x18\x07 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12\x45\n\x15\x64irect_receiving_diff\x18\x08 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12=\n\rdirect_return\x18\t \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12\x39\n\tstocktake\x18\n \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12\x36\n\x06\x64\x65mand\x18\x0b \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12<\n\x0cself_picking\x18\x0c \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\"L\n\x1e\x43onfirmStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\r\n\x05\x63heck\x18\x02 \x01(\x08\x12\x0b\n\x03lan\x18\x03 \x01(\t\"0\n\x1eSubmitStocktakeByDocIDResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"s\n\x1dSubmitStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x13\n\x0bsubmit_name\x18\x02 \x01(\t\x12\x0b\n\x03lan\x18\x03 \x01(\t\x12\x10\n\x08\x61ll_zero\x18\x04 \x01(\x08\x12\x0e\n\x06source\x18\x05 \x01(\t\"\x83\x01\n\x1e\x41pproveStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x0c\n\x04type\x18\x02 \x01(\t\x12\x14\n\x0c\x61pprove_name\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\x12\x10\n\x08\x61ll_zero\x18\x05 \x01(\x08\x12\x0e\n\x06source\x18\x06 \x01(\t\"\xed\x04\n\x1f\x41pproveStocktakeByDocIDResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x0f\n\x07handler\x18\x02 \x01(\x08\x12\x36\n\x06\x61\x64just\x18\x03 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12>\n\x0ereceiving_diff\x18\x04 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12\x38\n\x08transfer\x18\x05 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12\x39\n\treceiving\x18\x06 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12\x36\n\x06return\x18\x07 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12@\n\x10\x64irect_receiving\x18\x08 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12\x45\n\x15\x64irect_receiving_diff\x18\t \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12=\n\rdirect_return\x18\n \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12<\n\x0cself_picking\x18\x0c \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\"1\n\x1f\x43onfirmStocktakeByDocIDResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"<\n\x1d\x43\x61ncelStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"0\n\x1e\x43\x61ncelStocktakeByDocIDResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"p\n\x1b\x43reateStocktakeBatchRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x30\n\x0crequest_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0b\n\x03lan\x18\x03 \x01(\t\"x\n\x1c\x43reateStocktakeBatchResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x16\n\x0eschedule_count\x18\x02 \x01(\r\x12\x30\n\x0crequest_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"k\n\x16\x43reateStocktakeRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x30\n\x0crequest_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0b\n\x03lan\x18\x03 \x01(\t\"[\n\x17\x43reateStocktakeResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x30\n\x0crequest_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"E\n\x1e\x43heckedStocktakeByDocIDRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x02 \x01(\t\"1\n\x1f\x43heckedStocktakeByDocIDResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"_\n\x17GetStocktakeTagsRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\x12\x10\n\x08tag_name\x18\x03 \x01(\t\x12\x12\n\nbranch_ids\x18\x04 \x03(\x04\"\xaa\x02\n\rStocktakeTags\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x12\n\npartner_id\x18\x03 \x01(\x04\x12\x0f\n\x07user_id\x18\x04 \x01(\x04\x12\x12\n\ncreated_by\x18\x05 \x01(\x04\x12\x12\n\nupdated_by\x18\x06 \x01(\x04\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\t \x01(\t\x12\x14\n\x0cupdated_name\x18\n \x01(\t\x12\x11\n\tbranch_id\x18\x0b \x01(\x04\x12\x13\n\x0b\x62ranch_name\x18\x0c \x01(\t\"B\n\x18GetStocktakeTagsResponse\x12&\n\x04rows\x18\x01 \x03(\x0b\x32\x18.stocktake.StocktakeTags\"\xd3\x02\n\x1a\x41\x63tionStocktakeTagsRequest\x12\x0e\n\x06tag_id\x18\x01 \x01(\x04\x12<\n\x06\x61\x63tion\x18\x02 \x01(\x0e\x32,.stocktake.ActionStocktakeTagsRequest.Action\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x11\n\tbranch_id\x18\x04 \x01(\x04\x12\x0b\n\x03lan\x18\x05 \x01(\t\x12\x12\n\nbranch_ids\x18\x06 \x03(\x04\x12\x12\n\nregion_ids\x18\x07 \x03(\x04\x12\x15\n\radd_dimension\x18\x08 \x01(\t\x12\x0f\n\x07tag_ids\x18\t \x03(\x04\x12\x13\n\x0borigin_name\x18\n \x01(\t\x12\x13\n\x0b\x63opy_branch\x18\x0b \x01(\x04\"?\n\x06\x41\x63tion\x12\x07\n\x03get\x10\x00\x12\n\n\x06\x63reate\x10\x01\x12\n\n\x06\x64\x65lete\x10\x02\x12\n\n\x06update\x10\x03\x12\x08\n\x04\x63opy\x10\x04\"\xb3\x02\n\x1b\x41\x63tionStocktakeTagsResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\n\n\x02id\x18\x02 \x01(\x04\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x12\n\npartner_id\x18\x04 \x01(\x04\x12\x0f\n\x07user_id\x18\x05 \x01(\x04\x12\x12\n\ncreated_by\x18\x06 \x01(\x04\x12\x12\n\nupdated_by\x18\x07 \x01(\x04\x12.\n\ncreated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63reated_name\x18\n \x01(\t\x12\x14\n\x0cupdated_name\x18\x0b \x01(\t\x12\x11\n\tbranch_id\x18\x0c \x01(\x04\"-\n\x1bGetStocktakeTagsByIdRequest\x12\x0e\n\x06tag_id\x18\x01 \x01(\x04\"/\n!DeleteStocktakeProductTagsRequest\x12\n\n\x02id\x18\x01 \x03(\x04\"4\n\"DeleteStocktakeProductTagsResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\xf9\x02\n\x1aGetStocktakeBalanceRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tstore_ids\x18\x03 \x03(\x04\x12\r\n\x05limit\x18\x04 \x01(\r\x12\x0e\n\x06offset\x18\x05 \x01(\r\x12/\n\x0btarget_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\x07 \x03(\t\x12\x15\n\rinclude_total\x18\x08 \x01(\x08\x12\x15\n\rschedule_code\x18\t \x01(\t\x12\x16\n\x0estocktake_type\x18\n \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0b \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x0c \x01(\t\x12\x0b\n\x03lan\x18\r \x01(\t\x12\x0c\n\x04type\x18\x35 \x01(\t\"\xdb\x08\n\x10StocktakeBalance\x12\x17\n\x0f\x62ranch_batch_id\x18\x01 \x01(\x04\x12\x11\n\tbranch_id\x18\x02 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x03 \x01(\t\x12\x1b\n\x13\x63\x61lculate_inventory\x18\x04 \x01(\x08\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x13\n\x0b\x66orecasting\x18\x06 \x01(\x08\x12\x34\n\x10\x66orecasting_time\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\n\n\x02id\x18\x08 \x01(\x04\x12\x16\n\x0eprocess_status\x18\t \x01(\t\x12\x0e\n\x06remark\x18\n \x01(\t\x12\x13\n\x0bresult_type\x18\x0b \x01(\t\x12\x11\n\treview_by\x18\x0c \x01(\x04\x12\x15\n\rschedule_code\x18\r \x01(\t\x12\x13\n\x0bschedule_id\x18\x0e \x01(\x04\x12\x14\n\x0cst_diff_flag\x18\x0f \x01(\x05\x12\x0e\n\x06status\x18\x10 \x01(\t\x12\x1a\n\x12store_secondary_id\x18\x11 \x01(\x04\x12/\n\x0btarget_date\x18\x12 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04type\x18\x13 \x01(\t\x12\x12\n\npartner_id\x18\x14 \x01(\x04\x12\x0f\n\x07user_id\x18\x15 \x01(\x04\x12\x12\n\ncreated_by\x18\x16 \x01(\x04\x12\x12\n\nupdated_by\x18\x17 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x18 \x01(\t\x12\x14\n\x0cupdated_name\x18\x19 \x01(\t\x12.\n\ncreated_at\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rschedule_name\x18\x1c \x01(\t\x12\x18\n\x10\x64iff_err_message\x18\x1d \x01(\t\x12\x19\n\x11month_err_message\x18\x1f \x01(\t\x12\x13\n\x0b\x62ranch_name\x18# \x01(\t\x12\x13\n\x0b\x62ranch_code\x18$ \x01(\t\x12\x15\n\roriginal_code\x18% \x01(\t\x12\x17\n\x0foriginal_doc_id\x18& \x01(\x04\x12\x13\n\x0bis_recreate\x18\' \x01(\x08\x12\x15\n\rrecreate_code\x18( \x01(\t\x12\x17\n\x0frecreate_doc_id\x18) \x01(\x04\x12\x13\n\x0bsubmit_name\x18* \x01(\t\x12\x14\n\x0c\x61pprove_name\x18+ \x01(\t\x12\x16\n\x0estocktake_type\x18, \x01(\t\x12\x12\n\nrequest_id\x18- \x01(\x04\x12\x14\n\x0ctotal_amount\x18. \x01(\x01\x12\x19\n\x11total_diff_amount\x18/ \x01(\x01\x12\x1a\n\x12total_sales_amount\x18\x30 \x01(\x01\x12\x1f\n\x17total_diff_sales_amount\x18\x31 \x01(\x01\"W\n\x1bGetStocktakeBalanceResponse\x12)\n\x04rows\x18\x01 \x03(\x0b\x32\x1b.stocktake.StocktakeBalance\x12\r\n\x05total\x18\x02 \x01(\x04\"E\n&GetStocktakeBalanceProductGroupRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"d\n\x0cTagProductBi\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12\x14\n\x0ctag_quantity\x18\x02 \x01(\x01\x12\x15\n\rtag_unit_name\x18\x03 \x01(\t\x12\x15\n\rtag_uint_rate\x18\x04 \x01(\x01\"\x9e\x05\n\x1cStocktakeBalanceProductGroup\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x04 \x01(\t\x12\x15\n\rdiff_quantity\x18\x05 \x01(\x01\x12\x1a\n\x12inventory_quantity\x18\x06 \x01(\x01\x12\x11\n\tis_system\x18\x07 \x01(\x08\x12\x17\n\x0fmaterial_number\x18\x08 \x01(\t\x12\x14\n\x0cproduct_code\x18\t \x01(\t\x12\x12\n\nproduct_id\x18\n \x01(\x04\x12\x14\n\x0cproduct_name\x18\x0b \x01(\t\x12\x10\n\x08quantity\x18\x0c \x01(\x01\x12\x14\n\x0cstorage_type\x18\r \x01(\t\x12\x10\n\x08tag_code\x18\x0e \x01(\t\x12\x10\n\x08tag_name\x18\x0f \x01(\t\x12\x1a\n\x12unit_diff_quantity\x18\x10 \x01(\x01\x12\x0f\n\x07unit_id\x18\x11 \x01(\x04\x12\x11\n\tunit_name\x18\x12 \x01(\t\x12\x11\n\tunit_rate\x18\x13 \x01(\x01\x12\x11\n\tunit_spec\x18\x14 \x01(\t\x12,\n\x0btag_details\x18\x15 \x03(\x0b\x32\x17.stocktake.TagProductBi\x12%\n\x1d\x61\x63\x63ounting_inventory_quantity\x18\x16 \x01(\x01\x12 \n\x18\x61\x63\x63ounting_diff_quantity\x18\x17 \x01(\x01\x12\x13\n\x0bposition_id\x18\x19 \x01(\x04\x12\x15\n\rposition_code\x18\x1a \x01(\t\x12\x15\n\rposition_name\x18\x1b \x01(\t\"`\n\'GetStocktakeBalanceProductGroupResponse\x12\x35\n\x04rows\x18\x01 \x03(\x0b\x32\'.stocktake.StocktakeBalanceProductGroup\"\xf8\x02\n\x1aStocktakeBiDetailedRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0c\x63\x61tegory_ids\x18\x03 \x03(\x04\x12\x0c\n\x04type\x18\x04 \x01(\t\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12\r\n\x05limit\x18\x07 \x01(\r\x12\x15\n\rinclude_total\x18\x08 \x01(\x08\x12\x14\n\x0cproduct_name\x18\t \x01(\t\x12\x11\n\tstore_ids\x18\n \x03(\x04\x12\r\n\x05order\x18\x0b \x01(\t\x12\x0c\n\x04sort\x18\x0c \x01(\t\x12\x0c\n\x04\x63ode\x18\r \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0f \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x10 \x01(\t\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x16\n\x0estocktake_type\x18\x12 \x01(\t\"\x91\x0c\n\x13StocktakeBiDetailed\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x04 \x01(\t\x12\x15\n\rbranch_1_name\x18\x05 \x01(\t\x12\x15\n\rbranch_2_name\x18\x06 \x01(\t\x12\x11\n\tbranch_id\x18\x07 \x01(\x04\x12\x15\n\rcategory_code\x18\x08 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\t \x01(\x04\x12\x15\n\rcategory_name\x18\n \x01(\t\x12\x0c\n\x04\x63ode\x18\x0b \x01(\t\x12\x0f\n\x07\x64\x65leted\x18\x0c \x01(\x08\x12\x15\n\rdiff_quantity\x18\r \x01(\x01\x12\x15\n\rdisplay_order\x18\x0e \x01(\t\x12\x0e\n\x06\x64oc_id\x18\x0f \x01(\x04\x12\x0f\n\x07\x65xtends\x18\x10 \x01(\t\x12\n\n\x02id\x18\x11 \x01(\x04\x12\x0f\n\x07ignored\x18\x12 \x01(\x08\x12\x1a\n\x12inventory_quantity\x18\x13 \x01(\x01\x12\x11\n\tis_system\x18\x14 \x01(\x08\x12\x13\n\x0bitem_number\x18\x15 \x01(\x04\x12\x17\n\x0fmaterial_number\x18\x16 \x01(\t\x12\x14\n\x0cproduct_code\x18\x17 \x01(\t\x12\x12\n\nproduct_id\x18\x18 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x19 \x01(\t\x12\x10\n\x08quantity\x18\x1a \x01(\x01\x12\x0c\n\x04type\x18\x1b \x01(\t\x12\x14\n\x0cstorage_type\x18\x1c \x01(\t\x12\x12\n\nstore_code\x18\x1d \x01(\t\x12\x12\n\nstore_name\x18\x1e \x01(\t\x12/\n\x0btarget_date\x18\x1f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1a\n\x12unit_diff_quantity\x18  \x01(\x01\x12\x0f\n\x07unit_id\x18! \x01(\x04\x12\x11\n\tunit_name\x18\" \x01(\t\x12\x11\n\tunit_rate\x18# \x01(\x01\x12\x11\n\tunit_spec\x18$ \x01(\t\x12\x12\n\npartner_id\x18% \x01(\x04\x12\x12\n\ncreated_by\x18& \x01(\x04\x12\x12\n\nupdated_by\x18\' \x01(\x04\x12\x14\n\x0c\x63reated_name\x18( \x01(\t\x12\x14\n\x0cupdated_name\x18) \x01(\t\x12.\n\ncreated_at\x18* \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18+ \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x10\n\x08is_empty\x18, \x01(\x08\x12\x0e\n\x06is_pda\x18- \x01(\x08\x12\x0e\n\x06status\x18. \x01(\t\x12\r\n\x05units\x18/ \x01(\t\x12\x0f\n\x07user_id\x18\x30 \x01(\x04\x12\x0f\n\x07is_null\x18\x31 \x01(\x08\x12\x14\n\x0ctag_quantity\x18\x32 \x01(\x01\x12-\n\x0cproduct_tags\x18\x33 \x03(\x0b\x32\x17.stocktake.ProductTagBi\x12\x11\n\tis_enable\x18\x34 \x01(\x08\x12\x0c\n\x04spec\x18\x36 \x01(\t\x12\x17\n\x0f\x64iff_percentage\x18\x37 \x01(\x01\x12\x30\n\x0c\x63reated_time\x18\x38 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x19\n\x11\x63reated_user_name\x18\x39 \x01(\t\x12\x32\n\x0esubmitted_time\x18: \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1b\n\x13submitted_user_name\x18; \x01(\t\x12\x13\n\x0b\x62ranch_type\x18< \x01(\t\x12\x13\n\x0bposition_id\x18> \x01(\x04\x12\x15\n\rposition_code\x18? \x01(\t\x12\x15\n\rposition_name\x18@ \x01(\t\x12\x16\n\x0estocktake_type\x18\x35 \x01(\t\x12\x15\n\rschedule_code\x18\x41 \x01(\t\x12\x15\n\rschedule_name\x18\x42 \x01(\t\"\xc5\x01\n\x0cProductTagBi\x12\x13\n\x0btag_unit_id\x18\x01 \x01(\x04\x12\x15\n\rtag_unit_name\x18\x02 \x01(\t\x12\x14\n\x0ctag_quantity\x18\x03 \x01(\x01\x12\x10\n\x08tag_name\x18\x04 \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x05 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x06 \x01(\t\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x07 \x01(\x01\x12\n\n\x02id\x18\x08 \x01(\x04\"P\n\x08ST_total\x12\r\n\x05\x63ount\x18\x01 \x01(\x04\x12\x14\n\x0csum_quantity\x18\x02 \x01(\x01\x12\x1f\n\x17sum_accounting_quantity\x18\x03 \x01(\x01\"o\n\x1bStocktakeBiDetailedResponse\x12,\n\x04rows\x18\x01 \x03(\x0b\x32\x1e.stocktake.StocktakeBiDetailed\x12\"\n\x05total\x18\x02 \x01(\x0b\x32\x13.stocktake.ST_total\"\x99\x02\n\x1dStocktakeBalanceRegionRequest\x12\x15\n\rinclude_total\x18\x01 \x01(\x08\x12\r\n\x05limit\x18\x02 \x01(\r\x12\x0e\n\x06offset\x18\x03 \x01(\r\x12)\n\x05start\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\'\n\x03\x65nd\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tregion_id\x18\x06 \x01(\x04\x12\x13\n\x0bproduct_ids\x18\x07 \x03(\x04\x12\x0c\n\x04type\x18\x08 \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\t \x01(\t\x12\x0b\n\x03lan\x18\n \x01(\t\x12\x16\n\x0estocktake_type\x18\x0b \x01(\t\"\xc2\x03\n\x1dStocktakeBalanceRegionDetails\x12%\n\x1d\x61\x63\x63ounting_inventory_quantity\x18\x01 \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x02 \x01(\x01\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x13\n\x0b\x62ranch_name\x18\x04 \x01(\t\x12\x15\n\rdiff_quantity\x18\x05 \x01(\x01\x12\x10\n\x08\x64oc_type\x18\x06 \x01(\t\x12\x1a\n\x12inventory_quantity\x18\x07 \x01(\x01\x12\x17\n\x0fmaterial_number\x18\x08 \x01(\t\x12\x12\n\nproduct_id\x18\t \x01(\x04\x12\x14\n\x0cproduct_name\x18\n \x01(\t\x12\x10\n\x08quantity\x18\x0b \x01(\x01\x12\x10\n\x08store_id\x18\x0c \x01(\x04\x12\x12\n\nstore_name\x18\r \x01(\t\x12\r\n\x05tag_1\x18\x0e \x01(\x04\x12\x19\n\x11tag_1_name_pinyin\x18\x0f \x01(\t\x12\x1a\n\x12unit_diff_quantity\x18\x10 \x01(\x01\x12\x11\n\tunit_name\x18\x11 \x01(\t\x12\x11\n\tunit_rate\x18\x12 \x01(\x01\"\x9e\x04\n\x16StocktakeBalanceRegion\x12%\n\x1d\x61\x63\x63ounting_inventory_quantity\x18\x01 \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x02 \x01(\x01\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x39\n\x07\x64\x65tails\x18\x04 \x03(\x0b\x32(.stocktake.StocktakeBalanceRegionDetails\x12\x11\n\tunit_name\x18\x05 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x10\n\x08quantity\x18\x08 \x01(\x01\x12\x1a\n\x12inventory_quantity\x18\t \x01(\x01\x12\x1e\n\x16\x61\x63\x63oinventory_quantity\x18\n \x01(\x01\x12\x15\n\rdiff_quantity\x18\x0b \x01(\x01\x12\x1a\n\x12unit_diff_quantity\x18\x0c \x01(\x01\x12\x11\n\tunit_rate\x18\r \x01(\x01\x12\x0c\n\x04type\x18\x0e \x01(\t\x12\x12\n\nproduct_id\x18\x0f \x01(\x04\x12\x10\n\x08store_id\x18\x10 \x01(\x04\x12\x12\n\nstore_name\x18\x11 \x01(\t\x12\x12\n\nstore_code\x18\x12 \x01(\t\x12\x14\n\x0cproduct_code\x18\x13 \x01(\t\x12 \n\x18\x61\x63\x63ounting_diff_quantity\x18\x14 \x01(\x01\x12\x16\n\x0estocktake_type\x18\x15 \x01(\t\"`\n\x1eStocktakeBalanceRegionResponse\x12/\n\x04rows\x18\x01 \x03(\x0b\x32!.stocktake.StocktakeBalanceRegion\x12\r\n\x05total\x18\x02 \x01(\x04\"\xb9\x01\n\x15StoreDataScopeRequest\x12\x0e\n\x06search\x18\x01 \x01(\t\x12\x15\n\rsearch_fields\x18\x02 \x01(\t\x12\x0b\n\x03ids\x18\x03 \x03(\x04\x12\x15\n\rreturn_fields\x18\x04 \x01(\t\x12\x0f\n\x07\x66ilters\x18\x05 \x01(\t\x12\x18\n\x10relation_filters\x18\x06 \x01(\t\x12\r\n\x05limit\x18\x07 \x01(\x03\x12\x0e\n\x06offset\x18\x08 \x01(\x04\x12\x0b\n\x03lan\x18\t \x01(\t\"\x9f\x03\n\x0bScopeStores\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x13\n\x0bsecond_code\x18\x04 \x01(\t\x12\x0c\n\x04type\x18\x05 \x01(\t\x12\x0f\n\x07\x61\x64\x64ress\x18\x06 \x01(\t\x12\x0b\n\x03tel\x18\x07 \x01(\t\x12\x0f\n\x07\x63ontact\x18\x08 \x01(\t\x12\x0e\n\x06status\x18\t \x01(\t\x12\x0f\n\x07name_en\x18\n \x01(\t\x12\x11\n\topen_date\x18\x0b \x01(\t\x12\x12\n\nclose_date\x18\x0c \x01(\t\x12\r\n\x05\x65mail\x18\r \x01(\t\x12\x12\n\ngeo_region\x18\x0e \x03(\t\x12\x15\n\rbranch_region\x18\x0f \x03(\t\x12\x14\n\x0corder_region\x18\x10 \x03(\t\x12\x1b\n\x13\x64istribution_region\x18\x11 \x03(\t\x12\x17\n\x0fpurchase_region\x18\x12 \x03(\t\x12\x15\n\rmarket_region\x18\x13 \x03(\t\x12\x17\n\x0ftransfer_region\x18\x14 \x03(\t\x12\x18\n\x10\x61ttribute_region\x18\x15 \x03(\t\"E\n\x0eStoreDataScope\x12$\n\x04rows\x18\x01 \x03(\x0b\x32\x16.stocktake.ScopeStores\x12\r\n\x05total\x18\x02 \x01(\x04\":\n\x1b\x41\x64vanceStocktakeDiffRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"\x95\x01\n\x1c\x41\x64vanceStocktakeDiffResponse\x12)\n\x04rows\x18\x01 \x03(\x0b\x32\x1b.stocktake.StocktakeProduct\x12\r\n\x05total\x18\x02 \x01(\x04\x12;\n\rposition_rows\x18\x03 \x03(\x0b\x32$.stocktake.StocktakePositionProducts\"\xa9\x03\n\x1aStocktakeDiffReportRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tstore_ids\x18\x03 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x04 \x03(\x04\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12/\n\x0btarget_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x63ode\x18\x08 \x01(\t\x12\x16\n\x0estocktake_type\x18\t \x01(\t\x12\x15\n\rschedule_code\x18\x0b \x01(\t\x12\x0e\n\x06status\x18\x0c \x01(\t\x12\r\n\x05order\x18\r \x01(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\x13\n\x0bupper_limit\x18\x0f \x01(\t\x12\x13\n\x0blower_limit\x18\x10 \x01(\t\x12\x14\n\x0cis_wms_store\x18\x11 \x01(\x08\x12\x0b\n\x03lan\x18\x12 \x01(\t\"\xb4\x05\n\x16StocktakeDiffReportRow\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_code\x18\x03 \x01(\t\x12\x12\n\nstore_name\x18\x04 \x01(\t\x12\x14\n\x0c\x63ompany_code\x18\x05 \x01(\t\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x12\n\nproduct_id\x18\x07 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x08 \x01(\t\x12\x0c\n\x04type\x18\t \x01(\t\x12\x15\n\rdiff_quantity\x18\n \x01(\x01\x12\x10\n\x08quantity\x18\x0b \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x0c \x01(\x01\x12\x1a\n\x12inventory_quantity\x18\r \x01(\x01\x12 \n\x18\x64iff_quantity_percentage\x18\x0e \x01(\x01\x12/\n\x0btarget_date\x18\x0f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07unit_id\x18\x10 \x01(\x04\x12\x11\n\tunit_name\x18\x11 \x01(\t\x12\x11\n\tunit_rate\x18\x12 \x01(\x01\x12\x11\n\tunit_code\x18\x13 \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x14 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x15 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\x16 \x01(\t\x12\x14\n\x0c\x63ompany_name\x18\x18 \x01(\t\x12%\n\x1d\x61\x63\x63ounting_inventory_quantity\x18\x19 \x01(\x01\x12 \n\x18\x61\x63\x63ounting_diff_quantity\x18\x1a \x01(\x01\x12\x11\n\tunit_spec\x18\x1b \x01(\t\x12\x0e\n\x06status\x18\x1c \x01(\t\x12\x15\n\rschedule_code\x18\x1d \x01(\t\"]\n\x1bStocktakeDiffReportResponse\x12/\n\x04rows\x18\x01 \x03(\x0b\x32!.stocktake.StocktakeDiffReportRow\x12\r\n\x05total\x18\x02 \x01(\x04\"\x96\x01\n\x17GetUncompleteDocRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x10\n\x08store_id\x18\x03 \x01(\x04\x12\x0b\n\x03lan\x18\x04 \x01(\t\"L\n\x11\x43heckDemandDetail\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\t\x12\x0f\n\x07is_plan\x18\x04 \x01(\x08\"\xb9\x05\n\x18GetUncompleteDocResponse\x12\x0f\n\x07handler\x18\x01 \x01(\x08\x12\x36\n\x06\x61\x64just\x18\x02 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12>\n\x0ereceiving_diff\x18\x03 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12\x38\n\x08transfer\x18\x04 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12\x39\n\treceiving\x18\x05 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12\x36\n\x06return\x18\x06 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12@\n\x10\x64irect_receiving\x18\x07 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12\x45\n\x15\x64irect_receiving_diff\x18\x08 \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12=\n\rdirect_return\x18\t \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12\x39\n\tstocktake\x18\n \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\x12,\n\x06\x64\x65mand\x18\x0b \x03(\x0b\x32\x1c.stocktake.CheckDemandDetail\x12\x36\n\x06\x61ssets\x18\x0c \x03(\x0b\x32&.stocktake.CheckStocktakeByDocIDDetail\"\xab\x01\n\x1bRecreateStocktakeDocRequest\x12\x0f\n\x07\x64oc_ids\x18\x01 \x03(\x04\x12\x1b\n\x13\x63\x61lculate_inventory\x18\x02 \x01(\x08\x12\x15\n\rschedule_name\x18\x03 \x01(\t\x12\x0e\n\x06remark\x18\x04 \x01(\t\x12\x15\n\rschedule_code\x18\x05 \x01(\t\x12\x13\n\x0bschedule_id\x18\x06 \x01(\x04\x12\x0b\n\x03lan\x18\x07 \x01(\t\"s\n\x1cRecreateStocktakeDocResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x1b\n\x13restocktake_doc_ids\x18\x02 \x03(\x04\x12&\n\x1ehas_recreate_doc_id_no_confirm\x18\x03 \x03(\x04\"\xce\x02\n\x1dStocktakeDocStatisticsRequest\x12\x11\n\tstore_ids\x18\x01 \x03(\x04\x12.\n\nstart_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x0fperiod_group_by\x18\x04 \x01(\x0e\x32\x1c.stocktake.PeriodGroupMethod\x12\x16\n\x0estocktake_type\x18\x05 \x03(\t\x12\x0e\n\x06status\x18\x06 \x03(\t\x12\r\n\x05limit\x18\x07 \x01(\x03\x12\x0e\n\x06offset\x18\x08 \x01(\x03\x12\r\n\x05order\x18\t \x01(\t\x12\x0c\n\x04sort\x18\n \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0b \x01(\x08\x12\x0b\n\x03lan\x18\x0c \x01(\t\"\x97\x01\n\x16StocktakeDocStatistics\x12\x0c\n\x04\x64\x61te\x18\x01 \x01(\t\x12\x12\n\nstore_code\x18\x02 \x01(\t\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x16\n\x0estocktake_type\x18\x04 \x01(\t\x12\x0e\n\x06status\x18\x05 \x01(\t\x12\r\n\x05\x63ount\x18\x06 \x01(\x01\x12\x10\n\x08store_id\x18\x07 \x01(\x03\"`\n\x1eStocktakeDocStatisticsResponse\x12/\n\x04rows\x18\x01 \x03(\x0b\x32!.stocktake.StocktakeDocStatistics\x12\r\n\x05total\x18\x02 \x01(\x03\"\xc7\x03\n!StocktakeDiffCollectReportRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tstore_ids\x18\x03 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x04 \x03(\x04\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12/\n\x0btarget_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04\x63ode\x18\x08 \x01(\t\x12\x16\n\x0estocktake_type\x18\t \x01(\t\x12\x15\n\rschedule_code\x18\x0b \x01(\t\x12\x0e\n\x06status\x18\x0c \x01(\t\x12\r\n\x05order\x18\r \x01(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\x15\n\rperiod_symbol\x18\x0f \x01(\t\x12\x13\n\x0bupper_limit\x18\x10 \x01(\t\x12\x13\n\x0blower_limit\x18\x11 \x01(\t\x12\x14\n\x0cis_wms_store\x18\x12 \x01(\x08\x12\x0b\n\x03lan\x18\x13 \x01(\t\"\xde\x04\n\x1dStocktakeDiffCollectReportRow\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\x12\n\nstore_code\x18\x02 \x01(\t\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x14\n\x0c\x63ompany_code\x18\x04 \x01(\t\x12\x14\n\x0cproduct_code\x18\x05 \x01(\t\x12\x12\n\nproduct_id\x18\x06 \x01(\x04\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x15\n\rdiff_quantity\x18\x08 \x01(\x01\x12\x10\n\x08quantity\x18\t \x01(\x01\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\n \x01(\x01\x12\x1a\n\x12inventory_quantity\x18\x0b \x01(\x01\x12 \n\x18\x64iff_quantity_percentage\x18\x0c \x01(\x01\x12\x0f\n\x07unit_id\x18\r \x01(\x04\x12\x11\n\tunit_name\x18\x0e \x01(\t\x12\x11\n\tunit_rate\x18\x0f \x01(\x01\x12\x11\n\tunit_code\x18\x10 \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x11 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x12 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\x13 \x01(\t\x12\x14\n\x0c\x63ompany_name\x18\x14 \x01(\t\x12%\n\x1d\x61\x63\x63ounting_inventory_quantity\x18\x15 \x01(\x01\x12 \n\x18\x61\x63\x63ounting_diff_quantity\x18\x16 \x01(\x01\x12\x11\n\tunit_spec\x18\x17 \x01(\t\x12\x15\n\rperiod_symbol\x18\x18 \x01(\t\"k\n\"StocktakeDiffCollectReportResponse\x12\x36\n\x04rows\x18\x01 \x03(\x0b\x32(.stocktake.StocktakeDiffCollectReportRow\x12\r\n\x05total\x18\x02 \x01(\x04\"\xbd\x01\n\x1aUncompleteDocReportRequest\x12\x10\n\x08\x62us_date\x18\x01 \x01(\t\x12\x11\n\tstore_ids\x18\x02 \x03(\x04\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\r\n\x05limit\x18\x05 \x01(\x04\x12\x0e\n\x06offset\x18\x06 \x01(\x04\x12\r\n\x05model\x18\x07 \x03(\t\x12\r\n\x05order\x18\r \x01(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0f \x01(\x08\x12\x0b\n\x03lan\x18\x10 \x01(\t\"\xcb\x02\n\x1bUncompleteDocReportResponse\x12H\n\x04rows\x18\x01 \x03(\x0b\x32:.stocktake.UncompleteDocReportResponse.UncompleteDocReport\x12\r\n\x05total\x18\x02 \x01(\x04\x1a\xd2\x01\n\x13UncompleteDocReport\x12\r\n\x05model\x18\x01 \x01(\t\x12\x12\n\nstore_code\x18\x02 \x01(\t\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x06 \x01(\t\x12\x17\n\x0f\x64oc_update_name\x18\x07 \x01(\t\x12\x17\n\x0f\x64oc_update_time\x18\x08 \x01(\t\x12\x10\n\x08\x64oc_date\x18\t \x01(\t\x12\x10\n\x08\x62us_date\x18\n \x01(\t\x12\x10\n\x08store_id\x18\x0b \x01(\x04\"U\n\x1dStocktakeProductImportRequest\x12\x0e\n\x06\x64oc_id\x18\x01 \x01(\x04\x12\x11\n\tfile_name\x18\x02 \x01(\t\x12\x11\n\tfile_data\x18\x03 \x01(\t\"\xf2\x01\n\x19ProductImportResponseRows\x12\x0f\n\x07row_num\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x14\n\x0cproduct_name\x18\x03 \x01(\t\x12\x10\n\x08quantity\x18\x04 \x01(\x01\x12\x14\n\x0cstorage_type\x18\x05 \x01(\t\x12\x0c\n\x04spec\x18\x06 \x01(\t\x12\x0c\n\x04unit\x18\x07 \x01(\t\x12\x11\n\terror_msg\x18\x08 \x01(\t\x12\x13\n\x0bposition_id\x18\t \x01(\t\x12\x15\n\rposition_code\x18\n \x01(\t\x12\x15\n\rposition_name\x18\x0b \x01(\t\"\x9b\x01\n\x1eStocktakeProductImportResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x32\n\x04rows\x18\x02 \x03(\x0b\x32$.stocktake.ProductImportResponseRows\x12\x11\n\tfile_name\x18\x03 \x01(\t\x12\x10\n\x08rows_num\x18\x04 \x01(\x04\x12\x10\n\x08\x62\x61tch_id\x18\x05 \x01(\x04\"E\n!UpdateStocktakeImportBatchRequest\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\"D\n\"UpdateStocktakeImportBatchResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x0e\n\x06\x64oc_id\x18\x02 \x01(\x04\"\x9c\x01\n\x19StocktakePositionProducts\x12\x13\n\x0bposition_id\x18\x01 \x01(\x04\x12\x15\n\rposition_code\x18\x02 \x01(\t\x12\x15\n\rposition_name\x18\x03 \x01(\t\x12-\n\x08products\x18\x04 \x03(\x0b\x32\x1b.stocktake.StocktakeProduct\x12\r\n\x05total\x18\x05 \x01(\x04*:\n\x11PeriodGroupMethod\x12\n\n\x06\x42Y_DAY\x10\x00\x12\x0c\n\x08\x42Y_MONTH\x10\x01\x12\x0b\n\x07\x42Y_YEAR\x10\x02\x32\xff\"\n\tstocktake\x12q\n\rGetStoreScope\x12 .stocktake.StoreDataScopeRequest\x1a\x19.stocktake.StoreDataScope\"#\x82\xd3\xe4\x93\x02\x1d\"\x18/api/v2/store/data/scope:\x01*\x12>\n\x04Ping\x12\x16.google.protobuf.Empty\x1a\x0f.stocktake.Pong\"\r\x82\xd3\xe4\x93\x02\x07\x12\x05/ping\x12\xa3\x01\n\x15\x43heckStocktakeByDocID\x12\'.stocktake.CheckStocktakeByDocIDRequest\x1a(.stocktake.CheckStocktakeByDocIDResponse\"7\x82\xd3\xe4\x93\x02\x31\x1a//api/v2/supply/stocktake/{doc_id}/confirm/check\x12\xa3\x01\n\x17\x43onfirmStocktakeByDocID\x12).stocktake.ConfirmStocktakeByDocIDRequest\x1a*.stocktake.ConfirmStocktakeByDocIDResponse\"1\x82\xd3\xe4\x93\x02+\x1a)/api/v2/supply/stocktake/{doc_id}/confirm\x12\xa6\x01\n\x17\x41pproveStocktakeByDocID\x12).stocktake.ApproveStocktakeByDocIDRequest\x1a*.stocktake.ApproveStocktakeByDocIDResponse\"4\x82\xd3\xe4\x93\x02.\x1a)/api/v2/supply/stocktake/{doc_id}/approve:\x01*\x12\xa2\x01\n\x16RejectStocktakeProduct\x12(.stocktake.RejectStocktakeProductRequest\x1a).stocktake.RejectStocktakeProductResponse\"3\x82\xd3\xe4\x93\x02-\x1a(/api/v2/supply/stocktake/{doc_id}/reject:\x01*\x12\x9f\x01\n\x16\x43\x61ncelStocktakeByDocID\x12(.stocktake.CancelStocktakeByDocIDRequest\x1a).stocktake.CancelStocktakeByDocIDResponse\"0\x82\xd3\xe4\x93\x02*\x1a(/api/v2/supply/stocktake/{doc_id}/cancel\x12}\n\x13GetStocktakeByDocID\x12%.stocktake.GetStocktakeByDocIDRequest\x1a\x14.stocktake.Stocktake\")\x82\xd3\xe4\x93\x02#\x12!/api/v2/supply/stocktake/{doc_id}\x12q\n\x0cGetStocktake\x12\x1e.stocktake.GetStocktakeRequest\x1a\x1f.stocktake.GetStocktakeResponse\" \x82\xd3\xe4\x93\x02\x1a\x12\x18/api/v2/supply/stocktake\x12\x97\x01\n\x13GetStocktakeProduct\x12%.stocktake.GetStocktakeProductRequest\x1a&.stocktake.GetStocktakeProductResponse\"1\x82\xd3\xe4\x93\x02+\x12)/api/v2/supply/stocktake/{doc_id}/product\x12\x92\x01\n\x13PutStocktakeByDocID\x12%.stocktake.PutStocktakeByDocIDRequest\x1a&.stocktake.PutStocktakeByDocIDResponse\",\x82\xd3\xe4\x93\x02&\x1a!/api/v2/supply/stocktake/{doc_id}:\x01*\x12\xa9\x01\n\x17\x43heckedStocktakeByDocID\x12).stocktake.CheckedStocktakeByDocIDRequest\x1a*.stocktake.CheckedStocktakeByDocIDResponse\"7\x82\xd3\xe4\x93\x02\x31\x1a,/api/v2/supply/stocktake/{doc_id}/init/check:\x01*\x12\x8a\x01\n\x10GetStocktakeTags\x12\".stocktake.GetStocktakeTagsRequest\x1a#.stocktake.GetStocktakeTagsResponse\"-\x82\xd3\xe4\x93\x02\'\x12%/api/v2/supply/stocktake/product/tags\x12\x9d\x01\n\x13\x41\x63tionStocktakeTags\x12%.stocktake.ActionStocktakeTagsRequest\x1a&.stocktake.ActionStocktakeTagsResponse\"7\x82\xd3\xe4\x93\x02\x31\x1a,/api/v2/supply/stocktake/product/tags/action:\x01*\x12\xb1\x01\n\x1a\x44\x65leteStocktakeProductTags\x12,.stocktake.DeleteStocktakeProductTagsRequest\x1a-.stocktake.DeleteStocktakeProductTagsResponse\"6\x82\xd3\xe4\x93\x02\x30\x1a+/api/v2/supply/stocktake/product/tags/clean:\x01*\x12\x91\x01\n\x13GetStocktakeBalance\x12%.stocktake.GetStocktakeBalanceRequest\x1a&.stocktake.GetStocktakeBalanceResponse\"+\x82\xd3\xe4\x93\x02%\x12#/api/v2/supply/stocktake/bi/balance\x12\xa2\x01\n\x16SubmitStocktakeByDocID\x12(.stocktake.SubmitStocktakeByDocIDRequest\x1a).stocktake.SubmitStocktakeByDocIDResponse\"3\x82\xd3\xe4\x93\x02-\x1a(/api/v2/supply/stocktake/{doc_id}/submit:\x01*\x12\xc9\x01\n\x1fGetStocktakeBalanceProductGroup\x12\x31.stocktake.GetStocktakeBalanceProductGroupRequest\x1a\x32.stocktake.GetStocktakeBalanceProductGroupResponse\"?\x82\xd3\xe4\x93\x02\x39\x12\x37/api/v2/supply/stocktake/balance/{doc_id}/product/group\x12\x90\x01\n\x13StocktakeBiDetailed\x12%.stocktake.StocktakeBiDetailedRequest\x1a&.stocktake.StocktakeBiDetailedResponse\"*\x82\xd3\xe4\x93\x02$\x12\"/api/v2/supply/stocktake/bi/detail\x12\xa5\x01\n\x16StocktakeBalanceRegion\x12(.stocktake.StocktakeBalanceRegionRequest\x1a).stocktake.StocktakeBalanceRegionResponse\"6\x82\xd3\xe4\x93\x02\x30\x12./api/v2/supply/stocktake/balance/product/group\x12\x9a\x01\n\x14\x41\x64vanceStocktakeDiff\x12&.stocktake.AdvanceStocktakeDiffRequest\x1a\'.stocktake.AdvanceStocktakeDiffResponse\"1\x82\xd3\xe4\x93\x02+\x12)/api/v2/supply/stocktake/{doc_id}/advance\x12\x92\x01\n\x13StocktakeDiffReport\x12%.stocktake.StocktakeDiffReportRequest\x1a&.stocktake.StocktakeDiffReportResponse\",\x82\xd3\xe4\x93\x02&\x12$/api/v2/supply/stocktake/diff/report\x12\x82\x01\n\x10GetUncompleteDoc\x12\".stocktake.GetUncompleteDocRequest\x1a#.stocktake.GetUncompleteDocResponse\"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/api/v2/supply/uncomplete_doc\x12\x99\x01\n\x14RecreateStocktakeDoc\x12&.stocktake.RecreateStocktakeDocRequest\x1a\'.stocktake.RecreateStocktakeDocResponse\"0\x82\xd3\xe4\x93\x02*\"%/api/v2/supply/recreate_stocktake_doc:\x01*\x12\xa1\x01\n\x16StocktakeDocStatistics\x12(.stocktake.StocktakeDocStatisticsRequest\x1a).stocktake.StocktakeDocStatisticsResponse\"2\x82\xd3\xe4\x93\x02,\"\'/api/v2/supply/stocktake_doc_statistics:\x01*\x12\xaf\x01\n\x1aStocktakeDiffCollectReport\x12,.stocktake.StocktakeDiffCollectReportRequest\x1a-.stocktake.StocktakeDiffCollectReportResponse\"4\x82\xd3\xe4\x93\x02.\x12,/api/v2/supply/stocktake/diff_collect/report\x12\x92\x01\n\x13UncompleteDocReport\x12%.stocktake.UncompleteDocReportRequest\x1a&.stocktake.UncompleteDocReportResponse\",\x82\xd3\xe4\x93\x02&\x12$/api/v2/supply/uncomplete_doc_report\x12\xa1\x01\n\x16StocktakeProductImport\x12(.stocktake.StocktakeProductImportRequest\x1a).stocktake.StocktakeProductImportResponse\"2\x82\xd3\xe4\x93\x02,\"\'/api/v2/supply/stocktake/product/import:\x01*\x12\xb2\x01\n\x1aUpdateStocktakeImportBatch\x12,.stocktake.UpdateStocktakeImportBatchRequest\x1a-.stocktake.UpdateStocktakeImportBatchResponse\"7\x82\xd3\xe4\x93\x02\x31\x1a,/api/v2/supply/update/stocktake/import/batch:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_empty__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])

_PERIODGROUPMETHOD = _descriptor.EnumDescriptor(
  name='PeriodGroupMethod',
  full_name='stocktake.PeriodGroupMethod',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='BY_DAY', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BY_MONTH', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='BY_YEAR', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=23518,
  serialized_end=23576,
)
_sym_db.RegisterEnumDescriptor(_PERIODGROUPMETHOD)

PeriodGroupMethod = enum_type_wrapper.EnumTypeWrapper(_PERIODGROUPMETHOD)
BY_DAY = 0
BY_MONTH = 1
BY_YEAR = 2


_STOCKTAKE_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='stocktake.Stocktake.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='STARTED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REJECTED', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVED', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMIT_0', index=7, number=7,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVE_0', index=8, number=8,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1645,
  serialized_end=1780,
)
_sym_db.RegisterEnumDescriptor(_STOCKTAKE_STATUS)

_GETSTOCKTAKEREQUEST_S_STATUS = _descriptor.EnumDescriptor(
  name='S_STATUS',
  full_name='stocktake.GetStocktakeRequest.S_STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='OPENED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CLOSED', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2414,
  serialized_end=2448,
)
_sym_db.RegisterEnumDescriptor(_GETSTOCKTAKEREQUEST_S_STATUS)

_GETSTOCKTAKEREQUEST_S_TYPE = _descriptor.EnumDescriptor(
  name='S_TYPE',
  full_name='stocktake.GetStocktakeRequest.S_TYPE',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='W', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='D', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='M', index=2, number=2,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2450,
  serialized_end=2479,
)
_sym_db.RegisterEnumDescriptor(_GETSTOCKTAKEREQUEST_S_TYPE)

_GETSTOCKTAKEREQUEST_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='stocktake.GetStocktakeRequest.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='STARTED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REJECTED', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVED', index=5, number=5,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=6, number=6,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMIT_0', index=7, number=7,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVE_0', index=8, number=8,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1645,
  serialized_end=1780,
)
_sym_db.RegisterEnumDescriptor(_GETSTOCKTAKEREQUEST_STATUS)

_USERCREATESTOCKTAKERESPONSE_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='stocktake.UserCreateStocktakeResponse.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='STARTED', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1645,
  serialized_end=1678,
)
_sym_db.RegisterEnumDescriptor(_USERCREATESTOCKTAKERESPONSE_STATUS)

_ACTIONSTOCKTAKETAGSREQUEST_ACTION = _descriptor.EnumDescriptor(
  name='Action',
  full_name='stocktake.ActionStocktakeTagsRequest.Action',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='get', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='create', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='delete', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='update', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='copy', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=10342,
  serialized_end=10405,
)
_sym_db.RegisterEnumDescriptor(_ACTIONSTOCKTAKETAGSREQUEST_ACTION)


_PONG = _descriptor.Descriptor(
  name='Pong',
  full_name='stocktake.Pong',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='msg', full_name='stocktake.Pong.msg', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=122,
  serialized_end=141,
)


_GETPRODUCTBYSTOCKTAKETYPEREQUEST = _descriptor.Descriptor(
  name='GetProductByStocktakeTypeRequest',
  full_name='stocktake.GetProductByStocktakeTypeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='stocktake.GetProductByStocktakeTypeRequest.stocktake_type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='stocktake.GetProductByStocktakeTypeRequest.branch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='stocktake.GetProductByStocktakeTypeRequest.storage_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.GetProductByStocktakeTypeRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=143,
  serialized_end=255,
)


_STOCKTAKEPRODUCTTYPE = _descriptor.Descriptor(
  name='StocktakeProductType',
  full_name='stocktake.StocktakeProductType',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='stocktake.StocktakeProductType.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='stocktake.StocktakeProductType.id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='stocktake.StocktakeProductType.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='stocktake.StocktakeProductType.spec', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='stocktake.StocktakeProductType.storage_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=257,
  serialized_end=355,
)


_GETPRODUCTBYSTOCKTAKETYPERESPONSE = _descriptor.Descriptor(
  name='GetProductByStocktakeTypeResponse',
  full_name='stocktake.GetProductByStocktakeTypeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='stocktake.GetProductByStocktakeTypeResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=357,
  serialized_end=439,
)


_GETSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='GetStocktakeByDocIDRequest',
  full_name='stocktake.GetStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='stocktake.GetStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.GetStocktakeByDocIDRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=441,
  serialized_end=498,
)


_STOCKTAKE = _descriptor.Descriptor(
  name='Stocktake',
  full_name='stocktake.Stocktake',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='stocktake.Stocktake.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='stocktake.Stocktake.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_batch_id', full_name='stocktake.Stocktake.branch_batch_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='stocktake.Stocktake.branch_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_id', full_name='stocktake.Stocktake.schedule_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='stocktake.Stocktake.branch_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_inventory', full_name='stocktake.Stocktake.calculate_inventory', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='stocktake.Stocktake.code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='forecasting', full_name='stocktake.Stocktake.forecasting', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='forecasting_time', full_name='stocktake.Stocktake.forecasting_time', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='stocktake.Stocktake.remark', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result_type', full_name='stocktake.Stocktake.result_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='stocktake.Stocktake.schedule_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='st_diff_flag', full_name='stocktake.Stocktake.st_diff_flag', index=13,
      number=14, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='stocktake.Stocktake.status', index=14,
      number=15, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='stocktake.Stocktake.process_status', index=15,
      number=16, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='stocktake.Stocktake.store_secondary_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='stocktake.Stocktake.type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='stocktake.Stocktake.created_by', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='stocktake.Stocktake.updated_by', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='stocktake.Stocktake.review_by', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='stocktake.Stocktake.target_date', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='stocktake.Stocktake.created_at', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='stocktake.Stocktake.updated_at', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='stocktake.Stocktake.user_id', index=24,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='stocktake.Stocktake.created_name', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='stocktake.Stocktake.updated_name', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='stocktake.Stocktake.schedule_name', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_err_message', full_name='stocktake.Stocktake.diff_err_message', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='month_err_message', full_name='stocktake.Stocktake.month_err_message', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='original_code', full_name='stocktake.Stocktake.original_code', index=30,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='original_doc_id', full_name='stocktake.Stocktake.original_doc_id', index=31,
      number=36, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_recreate', full_name='stocktake.Stocktake.is_recreate', index=32,
      number=37, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='recreate_code', full_name='stocktake.Stocktake.recreate_code', index=33,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='recreate_doc_id', full_name='stocktake.Stocktake.recreate_doc_id', index=34,
      number=39, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='submit_name', full_name='stocktake.Stocktake.submit_name', index=35,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_name', full_name='stocktake.Stocktake.approve_name', index=36,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='stocktake.Stocktake.stocktake_type', index=37,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='stocktake.Stocktake.request_id', index=38,
      number=43, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='stocktake.Stocktake.total_amount', index=39,
      number=50, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_diff_amount', full_name='stocktake.Stocktake.total_diff_amount', index=40,
      number=51, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_sales_amount', full_name='stocktake.Stocktake.total_sales_amount', index=41,
      number=52, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_diff_sales_amount', full_name='stocktake.Stocktake.total_diff_sales_amount', index=42,
      number=53, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='stocktake.Stocktake.attachments', index=43,
      number=54, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _STOCKTAKE_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=501,
  serialized_end=1780,
)


_GETSTOCKTAKEREQUEST = _descriptor.Descriptor(
  name='GetStocktakeRequest',
  full_name='stocktake.GetStocktakeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='include_total', full_name='stocktake.GetStocktakeRequest.include_total', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='stocktake.GetStocktakeRequest.store_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='stocktake.GetStocktakeRequest.branch_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_status', full_name='stocktake.GetStocktakeRequest.store_status', index=3,
      number=4, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='_type', full_name='stocktake.GetStocktakeRequest._type', index=4,
      number=5, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='stocktake.GetStocktakeRequest.status', index=5,
      number=6, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='stocktake.GetStocktakeRequest.schedule_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='stocktake.GetStocktakeRequest.product_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='stocktake.GetStocktakeRequest.offset', index=8,
      number=9, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='stocktake.GetStocktakeRequest.limit', index=9,
      number=10, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='stocktake.GetStocktakeRequest.start_date', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='stocktake.GetStocktakeRequest.end_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='stocktake.GetStocktakeRequest.target_date', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='stocktake.GetStocktakeRequest.code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='stocktake.GetStocktakeRequest.ids', index=14,
      number=21, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_create', full_name='stocktake.GetStocktakeRequest.is_create', index=15,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='stocktake.GetStocktakeRequest.stocktake_type', index=16,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='stocktake.GetStocktakeRequest.order', index=17,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='stocktake.GetStocktakeRequest.sort', index=18,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='stocktake.GetStocktakeRequest.branch_type', index=19,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.GetStocktakeRequest.lan', index=20,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='stocktake.GetStocktakeRequest.schedule_name', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _GETSTOCKTAKEREQUEST_S_STATUS,
    _GETSTOCKTAKEREQUEST_S_TYPE,
    _GETSTOCKTAKEREQUEST_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1783,
  serialized_end=2617,
)


_GETSTOCKTAKERESPONSE = _descriptor.Descriptor(
  name='GetStocktakeResponse',
  full_name='stocktake.GetStocktakeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='stocktake.GetStocktakeResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='stocktake.GetStocktakeResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2619,
  serialized_end=2692,
)


_GETSTOCKTAKEPRODUCTREQUEST = _descriptor.Descriptor(
  name='GetStocktakeProductRequest',
  full_name='stocktake.GetStocktakeProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='stocktake.GetStocktakeProductRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_unit', full_name='stocktake.GetStocktakeProductRequest.include_unit', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='stocktake.GetStocktakeProductRequest.limit', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='stocktake.GetStocktakeProductRequest.offset', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='stocktake.GetStocktakeProductRequest.include_total', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='stocktake.GetStocktakeProductRequest.category_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='stocktake.GetStocktakeProductRequest.storage_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='stocktake.GetStocktakeProductRequest.product_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.GetStocktakeProductRequest.lan', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_barcode', full_name='stocktake.GetStocktakeProductRequest.include_barcode', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2695,
  serialized_end=2918,
)


_STOCKTAKEPRODUCTTAGNAME = _descriptor.Descriptor(
  name='StocktakeProductTagName',
  full_name='stocktake.StocktakeProductTagName',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='stocktake.StocktakeProductTagName.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='stocktake.StocktakeProductTagName.doc_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stp_id', full_name='stocktake.StocktakeProductTagName.stp_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='stocktake.StocktakeProductTagName.product_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_id', full_name='stocktake.StocktakeProductTagName.tag_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='stocktake.StocktakeProductTagName.tag_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_quantity', full_name='stocktake.StocktakeProductTagName.tag_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='stocktake.StocktakeProductTagName.accounting_quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='stocktake.StocktakeProductTagName.unit_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='stocktake.StocktakeProductTagName.unit_spec', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='stocktake.StocktakeProductTagName.unit_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='stocktake.StocktakeProductTagName.unit_rate', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='stocktake.StocktakeProductTagName.accounting_unit_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='stocktake.StocktakeProductTagName.accounting_unit_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='stocktake.StocktakeProductTagName.accounting_unit_spec', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='stocktake.StocktakeProductTagName.partner_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='stocktake.StocktakeProductTagName.user_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='stocktake.StocktakeProductTagName.created_by', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='stocktake.StocktakeProductTagName.updated_by', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='stocktake.StocktakeProductTagName.created_at', index=19,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='stocktake.StocktakeProductTagName.updated_at', index=20,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='stocktake.StocktakeProductTagName.created_name', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='stocktake.StocktakeProductTagName.updated_name', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='stocktake.StocktakeProductTagName.tax_rate', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='stocktake.StocktakeProductTagName.tax_price', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='stocktake.StocktakeProductTagName.cost_price', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='stocktake.StocktakeProductTagName.sales_price', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2921,
  serialized_end=3552,
)


_STOCKTAKEPRODUCTUNITS = _descriptor.Descriptor(
  name='StocktakeProductUnits',
  full_name='stocktake.StocktakeProductUnits',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='stocktake.StocktakeProductUnits.unit_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='stocktake.StocktakeProductUnits.unit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='stocktake.StocktakeProductUnits.unit_spec', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='stocktake.StocktakeProductUnits.unit_rate', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='stocktake.StocktakeProductUnits.tax_rate', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='stocktake.StocktakeProductUnits.tax_price', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='stocktake.StocktakeProductUnits.cost_price', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='stocktake.StocktakeProductUnits.sales_price', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default', full_name='stocktake.StocktakeProductUnits.default', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='stocktake.StocktakeProductUnits.stocktake', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3555,
  serialized_end=3766,
)


_STOCKTAKEPRODUCT = _descriptor.Descriptor(
  name='StocktakeProduct',
  full_name='stocktake.StocktakeProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='stocktake.StocktakeProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='stocktake.StocktakeProduct.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='stocktake.StocktakeProduct.doc_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='stocktake.StocktakeProduct.branch_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='stocktake.StocktakeProduct.product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='stocktake.StocktakeProduct.accounting_quantity', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='stocktake.StocktakeProduct.accounting_unit_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='stocktake.StocktakeProduct.accounting_unit_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='stocktake.StocktakeProduct.accounting_unit_spec', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='stocktake.StocktakeProduct.code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deleted', full_name='stocktake.StocktakeProduct.deleted', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='stocktake.StocktakeProduct.diff_quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='display_order', full_name='stocktake.StocktakeProduct.display_order', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='stocktake.StocktakeProduct.extends', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ignored', full_name='stocktake.StocktakeProduct.ignored', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='stocktake.StocktakeProduct.inventory_quantity', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_system', full_name='stocktake.StocktakeProduct.is_system', index=16,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_number', full_name='stocktake.StocktakeProduct.item_number', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='stocktake.StocktakeProduct.material_number', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='stocktake.StocktakeProduct.product_code', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='stocktake.StocktakeProduct.product_name', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='stocktake.StocktakeProduct.quantity', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='st_type', full_name='stocktake.StocktakeProduct.st_type', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='stocktake.StocktakeProduct.storage_type', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_diff_quantity', full_name='stocktake.StocktakeProduct.unit_diff_quantity', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='stocktake.StocktakeProduct.unit_id', index=25,
      number=26, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='stocktake.StocktakeProduct.unit_name', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='stocktake.StocktakeProduct.unit_rate', index=27,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='stocktake.StocktakeProduct.unit_spec', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='stocktake.StocktakeProduct.units', index=29,
      number=30, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='stocktake.StocktakeProduct.created_by', index=30,
      number=31, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='stocktake.StocktakeProduct.updated_by', index=31,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='stocktake.StocktakeProduct.target_date', index=32,
      number=33, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='stocktake.StocktakeProduct.created_at', index=33,
      number=34, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='stocktake.StocktakeProduct.updated_at', index=34,
      number=35, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='stocktake.StocktakeProduct.user_id', index=35,
      number=36, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='stocktake.StocktakeProduct.status', index=36,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_pda', full_name='stocktake.StocktakeProduct.is_pda', index=37,
      number=38, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_tags', full_name='stocktake.StocktakeProduct.product_tags', index=38,
      number=39, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_empty', full_name='stocktake.StocktakeProduct.is_empty', index=39,
      number=40, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='stocktake.StocktakeProduct.created_name', index=40,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='stocktake.StocktakeProduct.updated_name', index=41,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_null', full_name='stocktake.StocktakeProduct.is_null', index=42,
      number=43, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_quantity', full_name='stocktake.StocktakeProduct.tag_quantity', index=43,
      number=44, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_accounting_quantity', full_name='stocktake.StocktakeProduct.convert_accounting_quantity', index=44,
      number=45, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_bom', full_name='stocktake.StocktakeProduct.is_bom', index=45,
      number=46, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allow_stocktake_edit', full_name='stocktake.StocktakeProduct.allow_stocktake_edit', index=46,
      number=47, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='stocktake.StocktakeProduct.spec', index=47,
      number=48, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_price', full_name='stocktake.StocktakeProduct.diff_price', index=48,
      number=49, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='barcode', full_name='stocktake.StocktakeProduct.barcode', index=49,
      number=50, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='stocktake.StocktakeProduct.category_id', index=50,
      number=54, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='stocktake.StocktakeProduct.position_id', index=51,
      number=55, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='stocktake.StocktakeProduct.category_name', index=52,
      number=56, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='stocktake.StocktakeProduct.tax_rate', index=53,
      number=57, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='stocktake.StocktakeProduct.tax_price', index=54,
      number=58, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='stocktake.StocktakeProduct.amount', index=55,
      number=59, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_amount', full_name='stocktake.StocktakeProduct.diff_amount', index=56,
      number=60, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='stocktake.StocktakeProduct.cost_price', index=57,
      number=61, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='stocktake.StocktakeProduct.sales_amount', index=58,
      number=62, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='stocktake.StocktakeProduct.sales_price', index=59,
      number=63, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_sales_amount', full_name='stocktake.StocktakeProduct.diff_sales_amount', index=60,
      number=64, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3769,
  serialized_end=5203,
)


_GETSTOCKTAKEPRODUCTRESPONSE = _descriptor.Descriptor(
  name='GetStocktakeProductResponse',
  full_name='stocktake.GetStocktakeProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='stocktake.GetStocktakeProductResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='stocktake.GetStocktakeProductResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_rows', full_name='stocktake.GetStocktakeProductResponse.position_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5206,
  serialized_end=5354,
)


_USERCREATESTOCKTAKEREQUEST = _descriptor.Descriptor(
  name='UserCreateStocktakeRequest',
  full_name='stocktake.UserCreateStocktakeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='stocktake.UserCreateStocktakeRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='stocktake.UserCreateStocktakeRequest.branch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='_type', full_name='stocktake.UserCreateStocktakeRequest._type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='stocktake.UserCreateStocktakeRequest.target_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.UserCreateStocktakeRequest.lan', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5357,
  serialized_end=5501,
)


_USERCREATESTOCKTAKERESPONSE = _descriptor.Descriptor(
  name='UserCreateStocktakeResponse',
  full_name='stocktake.UserCreateStocktakeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='stocktake.UserCreateStocktakeResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='stocktake.UserCreateStocktakeResponse.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_batch_id', full_name='stocktake.UserCreateStocktakeResponse.branch_batch_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='stocktake.UserCreateStocktakeResponse.branch_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_id', full_name='stocktake.UserCreateStocktakeResponse.schedule_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='stocktake.UserCreateStocktakeResponse.branch_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_inventory', full_name='stocktake.UserCreateStocktakeResponse.calculate_inventory', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='stocktake.UserCreateStocktakeResponse.code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='forecasting', full_name='stocktake.UserCreateStocktakeResponse.forecasting', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='forecasting_time', full_name='stocktake.UserCreateStocktakeResponse.forecasting_time', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='stocktake.UserCreateStocktakeResponse.remark', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result_type', full_name='stocktake.UserCreateStocktakeResponse.result_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='stocktake.UserCreateStocktakeResponse.schedule_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='st_diff_flag', full_name='stocktake.UserCreateStocktakeResponse.st_diff_flag', index=13,
      number=14, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='stocktake.UserCreateStocktakeResponse.status', index=14,
      number=15, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='stocktake.UserCreateStocktakeResponse.process_status', index=15,
      number=16, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='stocktake.UserCreateStocktakeResponse.store_secondary_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='stocktake.UserCreateStocktakeResponse.type', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='stocktake.UserCreateStocktakeResponse.created_by', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='stocktake.UserCreateStocktakeResponse.updated_by', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='stocktake.UserCreateStocktakeResponse.review_by', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='stocktake.UserCreateStocktakeResponse.target_date', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='stocktake.UserCreateStocktakeResponse.created_at', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='stocktake.UserCreateStocktakeResponse.updated_at', index=23,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='stocktake.UserCreateStocktakeResponse.user_id', index=24,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='stocktake.UserCreateStocktakeResponse.created_name', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='stocktake.UserCreateStocktakeResponse.updated_name', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _USERCREATESTOCKTAKERESPONSE_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5504,
  serialized_end=6299,
)


_TAGQUANTITY = _descriptor.Descriptor(
  name='TagQuantity',
  full_name='stocktake.TagQuantity',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='stocktake.TagQuantity.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_id', full_name='stocktake.TagQuantity.tag_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='stocktake.TagQuantity.tag_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_quantity', full_name='stocktake.TagQuantity.tag_quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='stocktake.TagQuantity.unit_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6301,
  serialized_end=6399,
)


_PUTSTOCKTAKEPRODUCTS = _descriptor.Descriptor(
  name='PutStocktakeProducts',
  full_name='stocktake.PutStocktakeProducts',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='stocktake.PutStocktakeProducts.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='stocktake.PutStocktakeProducts.quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='stocktake.PutStocktakeProducts.unit_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_products', full_name='stocktake.PutStocktakeProducts.tag_products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_pda', full_name='stocktake.PutStocktakeProducts.is_pda', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_empty', full_name='stocktake.PutStocktakeProducts.is_empty', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_null', full_name='stocktake.PutStocktakeProducts.is_null', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='del_tag_ids', full_name='stocktake.PutStocktakeProducts.del_tag_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6402,
  serialized_end=6589,
)


_PUTSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='PutStocktakeByDocIDRequest',
  full_name='stocktake.PutStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='stocktake.PutStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='stocktake.PutStocktakeByDocIDRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.PutStocktakeByDocIDRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='all_zero', full_name='stocktake.PutStocktakeByDocIDRequest.all_zero', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='stocktake.PutStocktakeByDocIDRequest.attachments', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6592,
  serialized_end=6763,
)


_ATTACHMENTS = _descriptor.Descriptor(
  name='Attachments',
  full_name='stocktake.Attachments',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='stocktake.Attachments.type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='stocktake.Attachments.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6765,
  serialized_end=6805,
)


_REJECTSTOCKTAKEPRODUCTREQUEST = _descriptor.Descriptor(
  name='RejectStocktakeProductRequest',
  full_name='stocktake.RejectStocktakeProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='stocktake.RejectStocktakeProductRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='stocktake.RejectStocktakeProductRequest.reason', index=1,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.RejectStocktakeProductRequest.lan', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6807,
  serialized_end=6883,
)


_REJECTSTOCKTAKEPRODUCTRESPONSE = _descriptor.Descriptor(
  name='RejectStocktakeProductResponse',
  full_name='stocktake.RejectStocktakeProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='stocktake.RejectStocktakeProductResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6885,
  serialized_end=6933,
)


_PUTSTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='PutStocktakeByDocIDResponse',
  full_name='stocktake.PutStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='stocktake.PutStocktakeByDocIDResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6935,
  serialized_end=6980,
)


_CHECKSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='CheckStocktakeByDocIDRequest',
  full_name='stocktake.CheckStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='stocktake.CheckStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='check', full_name='stocktake.CheckStocktakeByDocIDRequest.check', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='stocktake.CheckStocktakeByDocIDRequest.branch_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.CheckStocktakeByDocIDRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6982,
  serialized_end=7077,
)


_CHECKSTOCKTAKEBYDOCIDDETAIL = _descriptor.Descriptor(
  name='CheckStocktakeByDocIDDetail',
  full_name='stocktake.CheckStocktakeByDocIDDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='stocktake.CheckStocktakeByDocIDDetail.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='stocktake.CheckStocktakeByDocIDDetail.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='stocktake.CheckStocktakeByDocIDDetail.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7079,
  serialized_end=7150,
)


_CHECKSTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='CheckStocktakeByDocIDResponse',
  full_name='stocktake.CheckStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='handler', full_name='stocktake.CheckStocktakeByDocIDResponse.handler', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust', full_name='stocktake.CheckStocktakeByDocIDResponse.adjust', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_diff', full_name='stocktake.CheckStocktakeByDocIDResponse.receiving_diff', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer', full_name='stocktake.CheckStocktakeByDocIDResponse.transfer', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving', full_name='stocktake.CheckStocktakeByDocIDResponse.receiving', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return', full_name='stocktake.CheckStocktakeByDocIDResponse.return', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_receiving', full_name='stocktake.CheckStocktakeByDocIDResponse.direct_receiving', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_receiving_diff', full_name='stocktake.CheckStocktakeByDocIDResponse.direct_receiving_diff', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_return', full_name='stocktake.CheckStocktakeByDocIDResponse.direct_return', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='stocktake.CheckStocktakeByDocIDResponse.stocktake', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand', full_name='stocktake.CheckStocktakeByDocIDResponse.demand', index=10,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='self_picking', full_name='stocktake.CheckStocktakeByDocIDResponse.self_picking', index=11,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7153,
  serialized_end=7871,
)


_CONFIRMSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='ConfirmStocktakeByDocIDRequest',
  full_name='stocktake.ConfirmStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='stocktake.ConfirmStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='check', full_name='stocktake.ConfirmStocktakeByDocIDRequest.check', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.ConfirmStocktakeByDocIDRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7873,
  serialized_end=7949,
)


_SUBMITSTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='SubmitStocktakeByDocIDResponse',
  full_name='stocktake.SubmitStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='stocktake.SubmitStocktakeByDocIDResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7951,
  serialized_end=7999,
)


_SUBMITSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='SubmitStocktakeByDocIDRequest',
  full_name='stocktake.SubmitStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='stocktake.SubmitStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='submit_name', full_name='stocktake.SubmitStocktakeByDocIDRequest.submit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.SubmitStocktakeByDocIDRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='all_zero', full_name='stocktake.SubmitStocktakeByDocIDRequest.all_zero', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source', full_name='stocktake.SubmitStocktakeByDocIDRequest.source', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8001,
  serialized_end=8116,
)


_APPROVESTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='ApproveStocktakeByDocIDRequest',
  full_name='stocktake.ApproveStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='stocktake.ApproveStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='stocktake.ApproveStocktakeByDocIDRequest.type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_name', full_name='stocktake.ApproveStocktakeByDocIDRequest.approve_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.ApproveStocktakeByDocIDRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='all_zero', full_name='stocktake.ApproveStocktakeByDocIDRequest.all_zero', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source', full_name='stocktake.ApproveStocktakeByDocIDRequest.source', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8119,
  serialized_end=8250,
)


_APPROVESTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='ApproveStocktakeByDocIDResponse',
  full_name='stocktake.ApproveStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='stocktake.ApproveStocktakeByDocIDResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='handler', full_name='stocktake.ApproveStocktakeByDocIDResponse.handler', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust', full_name='stocktake.ApproveStocktakeByDocIDResponse.adjust', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_diff', full_name='stocktake.ApproveStocktakeByDocIDResponse.receiving_diff', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer', full_name='stocktake.ApproveStocktakeByDocIDResponse.transfer', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving', full_name='stocktake.ApproveStocktakeByDocIDResponse.receiving', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return', full_name='stocktake.ApproveStocktakeByDocIDResponse.return', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_receiving', full_name='stocktake.ApproveStocktakeByDocIDResponse.direct_receiving', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_receiving_diff', full_name='stocktake.ApproveStocktakeByDocIDResponse.direct_receiving_diff', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_return', full_name='stocktake.ApproveStocktakeByDocIDResponse.direct_return', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='self_picking', full_name='stocktake.ApproveStocktakeByDocIDResponse.self_picking', index=10,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8253,
  serialized_end=8874,
)


_CONFIRMSTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='ConfirmStocktakeByDocIDResponse',
  full_name='stocktake.ConfirmStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='stocktake.ConfirmStocktakeByDocIDResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8876,
  serialized_end=8925,
)


_CANCELSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='CancelStocktakeByDocIDRequest',
  full_name='stocktake.CancelStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='stocktake.CancelStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.CancelStocktakeByDocIDRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8927,
  serialized_end=8987,
)


_CANCELSTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='CancelStocktakeByDocIDResponse',
  full_name='stocktake.CancelStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='stocktake.CancelStocktakeByDocIDResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8989,
  serialized_end=9037,
)


_CREATESTOCKTAKEBATCHREQUEST = _descriptor.Descriptor(
  name='CreateStocktakeBatchRequest',
  full_name='stocktake.CreateStocktakeBatchRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='stocktake.CreateStocktakeBatchRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='stocktake.CreateStocktakeBatchRequest.request_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.CreateStocktakeBatchRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9039,
  serialized_end=9151,
)


_CREATESTOCKTAKEBATCHRESPONSE = _descriptor.Descriptor(
  name='CreateStocktakeBatchResponse',
  full_name='stocktake.CreateStocktakeBatchResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='stocktake.CreateStocktakeBatchResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_count', full_name='stocktake.CreateStocktakeBatchResponse.schedule_count', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='stocktake.CreateStocktakeBatchResponse.request_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9153,
  serialized_end=9273,
)


_CREATESTOCKTAKEREQUEST = _descriptor.Descriptor(
  name='CreateStocktakeRequest',
  full_name='stocktake.CreateStocktakeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='stocktake.CreateStocktakeRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='stocktake.CreateStocktakeRequest.request_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.CreateStocktakeRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9275,
  serialized_end=9382,
)


_CREATESTOCKTAKERESPONSE = _descriptor.Descriptor(
  name='CreateStocktakeResponse',
  full_name='stocktake.CreateStocktakeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='stocktake.CreateStocktakeResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='stocktake.CreateStocktakeResponse.request_date', index=1,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9384,
  serialized_end=9475,
)


_CHECKEDSTOCKTAKEBYDOCIDREQUEST = _descriptor.Descriptor(
  name='CheckedStocktakeByDocIDRequest',
  full_name='stocktake.CheckedStocktakeByDocIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='stocktake.CheckedStocktakeByDocIDRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='stocktake.CheckedStocktakeByDocIDRequest.branch_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9477,
  serialized_end=9546,
)


_CHECKEDSTOCKTAKEBYDOCIDRESPONSE = _descriptor.Descriptor(
  name='CheckedStocktakeByDocIDResponse',
  full_name='stocktake.CheckedStocktakeByDocIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='stocktake.CheckedStocktakeByDocIDResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9548,
  serialized_end=9597,
)


_GETSTOCKTAKETAGSREQUEST = _descriptor.Descriptor(
  name='GetStocktakeTagsRequest',
  full_name='stocktake.GetStocktakeTagsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='stocktake.GetStocktakeTagsRequest.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.GetStocktakeTagsRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='stocktake.GetStocktakeTagsRequest.tag_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='stocktake.GetStocktakeTagsRequest.branch_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9599,
  serialized_end=9694,
)


_STOCKTAKETAGS = _descriptor.Descriptor(
  name='StocktakeTags',
  full_name='stocktake.StocktakeTags',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='stocktake.StocktakeTags.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='stocktake.StocktakeTags.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='stocktake.StocktakeTags.partner_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='stocktake.StocktakeTags.user_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='stocktake.StocktakeTags.created_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='stocktake.StocktakeTags.updated_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='stocktake.StocktakeTags.created_at', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='stocktake.StocktakeTags.updated_at', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='stocktake.StocktakeTags.created_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='stocktake.StocktakeTags.updated_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='stocktake.StocktakeTags.branch_id', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='stocktake.StocktakeTags.branch_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9697,
  serialized_end=9995,
)


_GETSTOCKTAKETAGSRESPONSE = _descriptor.Descriptor(
  name='GetStocktakeTagsResponse',
  full_name='stocktake.GetStocktakeTagsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='stocktake.GetStocktakeTagsResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9997,
  serialized_end=10063,
)


_ACTIONSTOCKTAKETAGSREQUEST = _descriptor.Descriptor(
  name='ActionStocktakeTagsRequest',
  full_name='stocktake.ActionStocktakeTagsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_id', full_name='stocktake.ActionStocktakeTagsRequest.tag_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='stocktake.ActionStocktakeTagsRequest.action', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='stocktake.ActionStocktakeTagsRequest.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='stocktake.ActionStocktakeTagsRequest.branch_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.ActionStocktakeTagsRequest.lan', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='stocktake.ActionStocktakeTagsRequest.branch_ids', index=5,
      number=6, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_ids', full_name='stocktake.ActionStocktakeTagsRequest.region_ids', index=6,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='add_dimension', full_name='stocktake.ActionStocktakeTagsRequest.add_dimension', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_ids', full_name='stocktake.ActionStocktakeTagsRequest.tag_ids', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='origin_name', full_name='stocktake.ActionStocktakeTagsRequest.origin_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='copy_branch', full_name='stocktake.ActionStocktakeTagsRequest.copy_branch', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _ACTIONSTOCKTAKETAGSREQUEST_ACTION,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10066,
  serialized_end=10405,
)


_ACTIONSTOCKTAKETAGSRESPONSE = _descriptor.Descriptor(
  name='ActionStocktakeTagsResponse',
  full_name='stocktake.ActionStocktakeTagsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='stocktake.ActionStocktakeTagsResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='stocktake.ActionStocktakeTagsResponse.id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='stocktake.ActionStocktakeTagsResponse.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='stocktake.ActionStocktakeTagsResponse.partner_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='stocktake.ActionStocktakeTagsResponse.user_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='stocktake.ActionStocktakeTagsResponse.created_by', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='stocktake.ActionStocktakeTagsResponse.updated_by', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='stocktake.ActionStocktakeTagsResponse.created_at', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='stocktake.ActionStocktakeTagsResponse.updated_at', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='stocktake.ActionStocktakeTagsResponse.created_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='stocktake.ActionStocktakeTagsResponse.updated_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='stocktake.ActionStocktakeTagsResponse.branch_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10408,
  serialized_end=10715,
)


_GETSTOCKTAKETAGSBYIDREQUEST = _descriptor.Descriptor(
  name='GetStocktakeTagsByIdRequest',
  full_name='stocktake.GetStocktakeTagsByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_id', full_name='stocktake.GetStocktakeTagsByIdRequest.tag_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10717,
  serialized_end=10762,
)


_DELETESTOCKTAKEPRODUCTTAGSREQUEST = _descriptor.Descriptor(
  name='DeleteStocktakeProductTagsRequest',
  full_name='stocktake.DeleteStocktakeProductTagsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='stocktake.DeleteStocktakeProductTagsRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10764,
  serialized_end=10811,
)


_DELETESTOCKTAKEPRODUCTTAGSRESPONSE = _descriptor.Descriptor(
  name='DeleteStocktakeProductTagsResponse',
  full_name='stocktake.DeleteStocktakeProductTagsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='stocktake.DeleteStocktakeProductTagsResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10813,
  serialized_end=10865,
)


_GETSTOCKTAKEBALANCEREQUEST = _descriptor.Descriptor(
  name='GetStocktakeBalanceRequest',
  full_name='stocktake.GetStocktakeBalanceRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='stocktake.GetStocktakeBalanceRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='stocktake.GetStocktakeBalanceRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='stocktake.GetStocktakeBalanceRequest.store_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='stocktake.GetStocktakeBalanceRequest.limit', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='stocktake.GetStocktakeBalanceRequest.offset', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='stocktake.GetStocktakeBalanceRequest.target_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='stocktake.GetStocktakeBalanceRequest.status', index=6,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='stocktake.GetStocktakeBalanceRequest.include_total', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='stocktake.GetStocktakeBalanceRequest.schedule_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='stocktake.GetStocktakeBalanceRequest.stocktake_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='stocktake.GetStocktakeBalanceRequest.is_wms_store', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='stocktake.GetStocktakeBalanceRequest.branch_type', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.GetStocktakeBalanceRequest.lan', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='stocktake.GetStocktakeBalanceRequest.type', index=13,
      number=53, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10868,
  serialized_end=11245,
)


_STOCKTAKEBALANCE = _descriptor.Descriptor(
  name='StocktakeBalance',
  full_name='stocktake.StocktakeBalance',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_batch_id', full_name='stocktake.StocktakeBalance.branch_batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='stocktake.StocktakeBalance.branch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='stocktake.StocktakeBalance.branch_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_inventory', full_name='stocktake.StocktakeBalance.calculate_inventory', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='stocktake.StocktakeBalance.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='forecasting', full_name='stocktake.StocktakeBalance.forecasting', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='forecasting_time', full_name='stocktake.StocktakeBalance.forecasting_time', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='stocktake.StocktakeBalance.id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='stocktake.StocktakeBalance.process_status', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='stocktake.StocktakeBalance.remark', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result_type', full_name='stocktake.StocktakeBalance.result_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='stocktake.StocktakeBalance.review_by', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='stocktake.StocktakeBalance.schedule_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_id', full_name='stocktake.StocktakeBalance.schedule_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='st_diff_flag', full_name='stocktake.StocktakeBalance.st_diff_flag', index=14,
      number=15, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='stocktake.StocktakeBalance.status', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='stocktake.StocktakeBalance.store_secondary_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='stocktake.StocktakeBalance.target_date', index=17,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='stocktake.StocktakeBalance.type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='stocktake.StocktakeBalance.partner_id', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='stocktake.StocktakeBalance.user_id', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='stocktake.StocktakeBalance.created_by', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='stocktake.StocktakeBalance.updated_by', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='stocktake.StocktakeBalance.created_name', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='stocktake.StocktakeBalance.updated_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='stocktake.StocktakeBalance.created_at', index=25,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='stocktake.StocktakeBalance.updated_at', index=26,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='stocktake.StocktakeBalance.schedule_name', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_err_message', full_name='stocktake.StocktakeBalance.diff_err_message', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='month_err_message', full_name='stocktake.StocktakeBalance.month_err_message', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='stocktake.StocktakeBalance.branch_name', index=30,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_code', full_name='stocktake.StocktakeBalance.branch_code', index=31,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='original_code', full_name='stocktake.StocktakeBalance.original_code', index=32,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='original_doc_id', full_name='stocktake.StocktakeBalance.original_doc_id', index=33,
      number=38, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_recreate', full_name='stocktake.StocktakeBalance.is_recreate', index=34,
      number=39, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='recreate_code', full_name='stocktake.StocktakeBalance.recreate_code', index=35,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='recreate_doc_id', full_name='stocktake.StocktakeBalance.recreate_doc_id', index=36,
      number=41, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='submit_name', full_name='stocktake.StocktakeBalance.submit_name', index=37,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approve_name', full_name='stocktake.StocktakeBalance.approve_name', index=38,
      number=43, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='stocktake.StocktakeBalance.stocktake_type', index=39,
      number=44, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='stocktake.StocktakeBalance.request_id', index=40,
      number=45, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='stocktake.StocktakeBalance.total_amount', index=41,
      number=46, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_diff_amount', full_name='stocktake.StocktakeBalance.total_diff_amount', index=42,
      number=47, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_sales_amount', full_name='stocktake.StocktakeBalance.total_sales_amount', index=43,
      number=48, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_diff_sales_amount', full_name='stocktake.StocktakeBalance.total_diff_sales_amount', index=44,
      number=49, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=11248,
  serialized_end=12363,
)


_GETSTOCKTAKEBALANCERESPONSE = _descriptor.Descriptor(
  name='GetStocktakeBalanceResponse',
  full_name='stocktake.GetStocktakeBalanceResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='stocktake.GetStocktakeBalanceResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='stocktake.GetStocktakeBalanceResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12365,
  serialized_end=12452,
)


_GETSTOCKTAKEBALANCEPRODUCTGROUPREQUEST = _descriptor.Descriptor(
  name='GetStocktakeBalanceProductGroupRequest',
  full_name='stocktake.GetStocktakeBalanceProductGroupRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='stocktake.GetStocktakeBalanceProductGroupRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.GetStocktakeBalanceProductGroupRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12454,
  serialized_end=12523,
)


_TAGPRODUCTBI = _descriptor.Descriptor(
  name='TagProductBi',
  full_name='stocktake.TagProductBi',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='stocktake.TagProductBi.tag_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_quantity', full_name='stocktake.TagProductBi.tag_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_unit_name', full_name='stocktake.TagProductBi.tag_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_uint_rate', full_name='stocktake.TagProductBi.tag_uint_rate', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12525,
  serialized_end=12625,
)


_STOCKTAKEBALANCEPRODUCTGROUP = _descriptor.Descriptor(
  name='StocktakeBalanceProductGroup',
  full_name='stocktake.StocktakeBalanceProductGroup',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='stocktake.StocktakeBalanceProductGroup.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='stocktake.StocktakeBalanceProductGroup.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='stocktake.StocktakeBalanceProductGroup.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='stocktake.StocktakeBalanceProductGroup.accounting_unit_spec', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='stocktake.StocktakeBalanceProductGroup.diff_quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='stocktake.StocktakeBalanceProductGroup.inventory_quantity', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_system', full_name='stocktake.StocktakeBalanceProductGroup.is_system', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='stocktake.StocktakeBalanceProductGroup.material_number', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='stocktake.StocktakeBalanceProductGroup.product_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='stocktake.StocktakeBalanceProductGroup.product_id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='stocktake.StocktakeBalanceProductGroup.product_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='stocktake.StocktakeBalanceProductGroup.quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='stocktake.StocktakeBalanceProductGroup.storage_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_code', full_name='stocktake.StocktakeBalanceProductGroup.tag_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='stocktake.StocktakeBalanceProductGroup.tag_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_diff_quantity', full_name='stocktake.StocktakeBalanceProductGroup.unit_diff_quantity', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='stocktake.StocktakeBalanceProductGroup.unit_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='stocktake.StocktakeBalanceProductGroup.unit_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='stocktake.StocktakeBalanceProductGroup.unit_rate', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='stocktake.StocktakeBalanceProductGroup.unit_spec', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_details', full_name='stocktake.StocktakeBalanceProductGroup.tag_details', index=20,
      number=21, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_inventory_quantity', full_name='stocktake.StocktakeBalanceProductGroup.accounting_inventory_quantity', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_diff_quantity', full_name='stocktake.StocktakeBalanceProductGroup.accounting_diff_quantity', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='stocktake.StocktakeBalanceProductGroup.position_id', index=23,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='stocktake.StocktakeBalanceProductGroup.position_code', index=24,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='stocktake.StocktakeBalanceProductGroup.position_name', index=25,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=12628,
  serialized_end=13298,
)


_GETSTOCKTAKEBALANCEPRODUCTGROUPRESPONSE = _descriptor.Descriptor(
  name='GetStocktakeBalanceProductGroupResponse',
  full_name='stocktake.GetStocktakeBalanceProductGroupResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='stocktake.GetStocktakeBalanceProductGroupResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13300,
  serialized_end=13396,
)


_STOCKTAKEBIDETAILEDREQUEST = _descriptor.Descriptor(
  name='StocktakeBiDetailedRequest',
  full_name='stocktake.StocktakeBiDetailedRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='stocktake.StocktakeBiDetailedRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='stocktake.StocktakeBiDetailedRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='stocktake.StocktakeBiDetailedRequest.category_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='stocktake.StocktakeBiDetailedRequest.type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='stocktake.StocktakeBiDetailedRequest.offset', index=4,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='stocktake.StocktakeBiDetailedRequest.limit', index=5,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='stocktake.StocktakeBiDetailedRequest.include_total', index=6,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='stocktake.StocktakeBiDetailedRequest.product_name', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='stocktake.StocktakeBiDetailedRequest.store_ids', index=8,
      number=10, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='stocktake.StocktakeBiDetailedRequest.order', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='stocktake.StocktakeBiDetailedRequest.sort', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='stocktake.StocktakeBiDetailedRequest.code', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='stocktake.StocktakeBiDetailedRequest.is_wms_store', index=12,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='stocktake.StocktakeBiDetailedRequest.branch_type', index=13,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.StocktakeBiDetailedRequest.lan', index=14,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='stocktake.StocktakeBiDetailedRequest.stocktake_type', index=15,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13399,
  serialized_end=13775,
)


_STOCKTAKEBIDETAILED = _descriptor.Descriptor(
  name='StocktakeBiDetailed',
  full_name='stocktake.StocktakeBiDetailed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='stocktake.StocktakeBiDetailed.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='stocktake.StocktakeBiDetailed.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='stocktake.StocktakeBiDetailed.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='stocktake.StocktakeBiDetailed.accounting_unit_spec', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_1_name', full_name='stocktake.StocktakeBiDetailed.branch_1_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_2_name', full_name='stocktake.StocktakeBiDetailed.branch_2_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='stocktake.StocktakeBiDetailed.branch_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='stocktake.StocktakeBiDetailed.category_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='stocktake.StocktakeBiDetailed.category_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='stocktake.StocktakeBiDetailed.category_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='stocktake.StocktakeBiDetailed.code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deleted', full_name='stocktake.StocktakeBiDetailed.deleted', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='stocktake.StocktakeBiDetailed.diff_quantity', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='display_order', full_name='stocktake.StocktakeBiDetailed.display_order', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='stocktake.StocktakeBiDetailed.doc_id', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='stocktake.StocktakeBiDetailed.extends', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='stocktake.StocktakeBiDetailed.id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ignored', full_name='stocktake.StocktakeBiDetailed.ignored', index=17,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='stocktake.StocktakeBiDetailed.inventory_quantity', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_system', full_name='stocktake.StocktakeBiDetailed.is_system', index=19,
      number=20, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_number', full_name='stocktake.StocktakeBiDetailed.item_number', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='stocktake.StocktakeBiDetailed.material_number', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='stocktake.StocktakeBiDetailed.product_code', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='stocktake.StocktakeBiDetailed.product_id', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='stocktake.StocktakeBiDetailed.product_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='stocktake.StocktakeBiDetailed.quantity', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='stocktake.StocktakeBiDetailed.type', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='stocktake.StocktakeBiDetailed.storage_type', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='stocktake.StocktakeBiDetailed.store_code', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='stocktake.StocktakeBiDetailed.store_name', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='stocktake.StocktakeBiDetailed.target_date', index=30,
      number=31, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_diff_quantity', full_name='stocktake.StocktakeBiDetailed.unit_diff_quantity', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='stocktake.StocktakeBiDetailed.unit_id', index=32,
      number=33, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='stocktake.StocktakeBiDetailed.unit_name', index=33,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='stocktake.StocktakeBiDetailed.unit_rate', index=34,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='stocktake.StocktakeBiDetailed.unit_spec', index=35,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='stocktake.StocktakeBiDetailed.partner_id', index=36,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='stocktake.StocktakeBiDetailed.created_by', index=37,
      number=38, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='stocktake.StocktakeBiDetailed.updated_by', index=38,
      number=39, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='stocktake.StocktakeBiDetailed.created_name', index=39,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='stocktake.StocktakeBiDetailed.updated_name', index=40,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='stocktake.StocktakeBiDetailed.created_at', index=41,
      number=42, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='stocktake.StocktakeBiDetailed.updated_at', index=42,
      number=43, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_empty', full_name='stocktake.StocktakeBiDetailed.is_empty', index=43,
      number=44, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_pda', full_name='stocktake.StocktakeBiDetailed.is_pda', index=44,
      number=45, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='stocktake.StocktakeBiDetailed.status', index=45,
      number=46, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='stocktake.StocktakeBiDetailed.units', index=46,
      number=47, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='stocktake.StocktakeBiDetailed.user_id', index=47,
      number=48, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_null', full_name='stocktake.StocktakeBiDetailed.is_null', index=48,
      number=49, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_quantity', full_name='stocktake.StocktakeBiDetailed.tag_quantity', index=49,
      number=50, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_tags', full_name='stocktake.StocktakeBiDetailed.product_tags', index=50,
      number=51, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_enable', full_name='stocktake.StocktakeBiDetailed.is_enable', index=51,
      number=52, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='stocktake.StocktakeBiDetailed.spec', index=52,
      number=54, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_percentage', full_name='stocktake.StocktakeBiDetailed.diff_percentage', index=53,
      number=55, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_time', full_name='stocktake.StocktakeBiDetailed.created_time', index=54,
      number=56, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_user_name', full_name='stocktake.StocktakeBiDetailed.created_user_name', index=55,
      number=57, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='submitted_time', full_name='stocktake.StocktakeBiDetailed.submitted_time', index=56,
      number=58, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='submitted_user_name', full_name='stocktake.StocktakeBiDetailed.submitted_user_name', index=57,
      number=59, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='stocktake.StocktakeBiDetailed.branch_type', index=58,
      number=60, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='stocktake.StocktakeBiDetailed.position_id', index=59,
      number=62, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='stocktake.StocktakeBiDetailed.position_code', index=60,
      number=63, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='stocktake.StocktakeBiDetailed.position_name', index=61,
      number=64, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='stocktake.StocktakeBiDetailed.stocktake_type', index=62,
      number=53, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='stocktake.StocktakeBiDetailed.schedule_code', index=63,
      number=65, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='stocktake.StocktakeBiDetailed.schedule_name', index=64,
      number=66, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=13778,
  serialized_end=15331,
)


_PRODUCTTAGBI = _descriptor.Descriptor(
  name='ProductTagBi',
  full_name='stocktake.ProductTagBi',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag_unit_id', full_name='stocktake.ProductTagBi.tag_unit_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_unit_name', full_name='stocktake.ProductTagBi.tag_unit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_quantity', full_name='stocktake.ProductTagBi.tag_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_name', full_name='stocktake.ProductTagBi.tag_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='stocktake.ProductTagBi.accounting_unit_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='stocktake.ProductTagBi.accounting_unit_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='stocktake.ProductTagBi.accounting_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='stocktake.ProductTagBi.id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15334,
  serialized_end=15531,
)


_ST_TOTAL = _descriptor.Descriptor(
  name='ST_total',
  full_name='stocktake.ST_total',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='count', full_name='stocktake.ST_total.count', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_quantity', full_name='stocktake.ST_total.sum_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_accounting_quantity', full_name='stocktake.ST_total.sum_accounting_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15533,
  serialized_end=15613,
)


_STOCKTAKEBIDETAILEDRESPONSE = _descriptor.Descriptor(
  name='StocktakeBiDetailedResponse',
  full_name='stocktake.StocktakeBiDetailedResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='stocktake.StocktakeBiDetailedResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='stocktake.StocktakeBiDetailedResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15615,
  serialized_end=15726,
)


_STOCKTAKEBALANCEREGIONREQUEST = _descriptor.Descriptor(
  name='StocktakeBalanceRegionRequest',
  full_name='stocktake.StocktakeBalanceRegionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='include_total', full_name='stocktake.StocktakeBalanceRegionRequest.include_total', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='stocktake.StocktakeBalanceRegionRequest.limit', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='stocktake.StocktakeBalanceRegionRequest.offset', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start', full_name='stocktake.StocktakeBalanceRegionRequest.start', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end', full_name='stocktake.StocktakeBalanceRegionRequest.end', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='region_id', full_name='stocktake.StocktakeBalanceRegionRequest.region_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='stocktake.StocktakeBalanceRegionRequest.product_ids', index=6,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='stocktake.StocktakeBalanceRegionRequest.type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='stocktake.StocktakeBalanceRegionRequest.branch_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.StocktakeBalanceRegionRequest.lan', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='stocktake.StocktakeBalanceRegionRequest.stocktake_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=15729,
  serialized_end=16010,
)


_STOCKTAKEBALANCEREGIONDETAILS = _descriptor.Descriptor(
  name='StocktakeBalanceRegionDetails',
  full_name='stocktake.StocktakeBalanceRegionDetails',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_inventory_quantity', full_name='stocktake.StocktakeBalanceRegionDetails.accounting_inventory_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='stocktake.StocktakeBalanceRegionDetails.accounting_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='stocktake.StocktakeBalanceRegionDetails.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='stocktake.StocktakeBalanceRegionDetails.branch_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='stocktake.StocktakeBalanceRegionDetails.diff_quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_type', full_name='stocktake.StocktakeBalanceRegionDetails.doc_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='stocktake.StocktakeBalanceRegionDetails.inventory_quantity', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='stocktake.StocktakeBalanceRegionDetails.material_number', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='stocktake.StocktakeBalanceRegionDetails.product_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='stocktake.StocktakeBalanceRegionDetails.product_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='stocktake.StocktakeBalanceRegionDetails.quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='stocktake.StocktakeBalanceRegionDetails.store_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='stocktake.StocktakeBalanceRegionDetails.store_name', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_1', full_name='stocktake.StocktakeBalanceRegionDetails.tag_1', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tag_1_name_pinyin', full_name='stocktake.StocktakeBalanceRegionDetails.tag_1_name_pinyin', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_diff_quantity', full_name='stocktake.StocktakeBalanceRegionDetails.unit_diff_quantity', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='stocktake.StocktakeBalanceRegionDetails.unit_name', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='stocktake.StocktakeBalanceRegionDetails.unit_rate', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=16013,
  serialized_end=16463,
)


_STOCKTAKEBALANCEREGION = _descriptor.Descriptor(
  name='StocktakeBalanceRegion',
  full_name='stocktake.StocktakeBalanceRegion',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_inventory_quantity', full_name='stocktake.StocktakeBalanceRegion.accounting_inventory_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='stocktake.StocktakeBalanceRegion.accounting_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='stocktake.StocktakeBalanceRegion.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='details', full_name='stocktake.StocktakeBalanceRegion.details', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='stocktake.StocktakeBalanceRegion.unit_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='stocktake.StocktakeBalanceRegion.product_name', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='stocktake.StocktakeBalanceRegion.quantity', index=6,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='stocktake.StocktakeBalanceRegion.inventory_quantity', index=7,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accoinventory_quantity', full_name='stocktake.StocktakeBalanceRegion.accoinventory_quantity', index=8,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='stocktake.StocktakeBalanceRegion.diff_quantity', index=9,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_diff_quantity', full_name='stocktake.StocktakeBalanceRegion.unit_diff_quantity', index=10,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='stocktake.StocktakeBalanceRegion.unit_rate', index=11,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='stocktake.StocktakeBalanceRegion.type', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='stocktake.StocktakeBalanceRegion.product_id', index=13,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='stocktake.StocktakeBalanceRegion.store_id', index=14,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='stocktake.StocktakeBalanceRegion.store_name', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='stocktake.StocktakeBalanceRegion.store_code', index=16,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='stocktake.StocktakeBalanceRegion.product_code', index=17,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_diff_quantity', full_name='stocktake.StocktakeBalanceRegion.accounting_diff_quantity', index=18,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='stocktake.StocktakeBalanceRegion.stocktake_type', index=19,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=16466,
  serialized_end=17008,
)


_STOCKTAKEBALANCEREGIONRESPONSE = _descriptor.Descriptor(
  name='StocktakeBalanceRegionResponse',
  full_name='stocktake.StocktakeBalanceRegionResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='stocktake.StocktakeBalanceRegionResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='stocktake.StocktakeBalanceRegionResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17010,
  serialized_end=17106,
)


_STOREDATASCOPEREQUEST = _descriptor.Descriptor(
  name='StoreDataScopeRequest',
  full_name='stocktake.StoreDataScopeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='search', full_name='stocktake.StoreDataScopeRequest.search', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='stocktake.StoreDataScopeRequest.search_fields', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='stocktake.StoreDataScopeRequest.ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='stocktake.StoreDataScopeRequest.return_fields', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='stocktake.StoreDataScopeRequest.filters', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='relation_filters', full_name='stocktake.StoreDataScopeRequest.relation_filters', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='stocktake.StoreDataScopeRequest.limit', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='stocktake.StoreDataScopeRequest.offset', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.StoreDataScopeRequest.lan', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17109,
  serialized_end=17294,
)


_SCOPESTORES = _descriptor.Descriptor(
  name='ScopeStores',
  full_name='stocktake.ScopeStores',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='stocktake.ScopeStores.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='stocktake.ScopeStores.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='stocktake.ScopeStores.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='second_code', full_name='stocktake.ScopeStores.second_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='stocktake.ScopeStores.type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='address', full_name='stocktake.ScopeStores.address', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tel', full_name='stocktake.ScopeStores.tel', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contact', full_name='stocktake.ScopeStores.contact', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='stocktake.ScopeStores.status', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name_en', full_name='stocktake.ScopeStores.name_en', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='open_date', full_name='stocktake.ScopeStores.open_date', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='close_date', full_name='stocktake.ScopeStores.close_date', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='email', full_name='stocktake.ScopeStores.email', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='geo_region', full_name='stocktake.ScopeStores.geo_region', index=13,
      number=14, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_region', full_name='stocktake.ScopeStores.branch_region', index=14,
      number=15, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_region', full_name='stocktake.ScopeStores.order_region', index=15,
      number=16, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_region', full_name='stocktake.ScopeStores.distribution_region', index=16,
      number=17, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_region', full_name='stocktake.ScopeStores.purchase_region', index=17,
      number=18, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='market_region', full_name='stocktake.ScopeStores.market_region', index=18,
      number=19, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer_region', full_name='stocktake.ScopeStores.transfer_region', index=19,
      number=20, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attribute_region', full_name='stocktake.ScopeStores.attribute_region', index=20,
      number=21, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17297,
  serialized_end=17712,
)


_STOREDATASCOPE = _descriptor.Descriptor(
  name='StoreDataScope',
  full_name='stocktake.StoreDataScope',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='stocktake.StoreDataScope.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='stocktake.StoreDataScope.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17714,
  serialized_end=17783,
)


_ADVANCESTOCKTAKEDIFFREQUEST = _descriptor.Descriptor(
  name='AdvanceStocktakeDiffRequest',
  full_name='stocktake.AdvanceStocktakeDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='stocktake.AdvanceStocktakeDiffRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.AdvanceStocktakeDiffRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17785,
  serialized_end=17843,
)


_ADVANCESTOCKTAKEDIFFRESPONSE = _descriptor.Descriptor(
  name='AdvanceStocktakeDiffResponse',
  full_name='stocktake.AdvanceStocktakeDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='stocktake.AdvanceStocktakeDiffResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='stocktake.AdvanceStocktakeDiffResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_rows', full_name='stocktake.AdvanceStocktakeDiffResponse.position_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17846,
  serialized_end=17995,
)


_STOCKTAKEDIFFREPORTREQUEST = _descriptor.Descriptor(
  name='StocktakeDiffReportRequest',
  full_name='stocktake.StocktakeDiffReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='stocktake.StocktakeDiffReportRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='stocktake.StocktakeDiffReportRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='stocktake.StocktakeDiffReportRequest.store_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='stocktake.StocktakeDiffReportRequest.product_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='stocktake.StocktakeDiffReportRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='stocktake.StocktakeDiffReportRequest.offset', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='stocktake.StocktakeDiffReportRequest.target_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='stocktake.StocktakeDiffReportRequest.code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='stocktake.StocktakeDiffReportRequest.stocktake_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='stocktake.StocktakeDiffReportRequest.schedule_code', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='stocktake.StocktakeDiffReportRequest.status', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='stocktake.StocktakeDiffReportRequest.order', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='stocktake.StocktakeDiffReportRequest.sort', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='upper_limit', full_name='stocktake.StocktakeDiffReportRequest.upper_limit', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lower_limit', full_name='stocktake.StocktakeDiffReportRequest.lower_limit', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='stocktake.StocktakeDiffReportRequest.is_wms_store', index=15,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.StocktakeDiffReportRequest.lan', index=16,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=17998,
  serialized_end=18423,
)


_STOCKTAKEDIFFREPORTROW = _descriptor.Descriptor(
  name='StocktakeDiffReportRow',
  full_name='stocktake.StocktakeDiffReportRow',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='stocktake.StocktakeDiffReportRow.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='stocktake.StocktakeDiffReportRow.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='stocktake.StocktakeDiffReportRow.store_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='stocktake.StocktakeDiffReportRow.store_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_code', full_name='stocktake.StocktakeDiffReportRow.company_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='stocktake.StocktakeDiffReportRow.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='stocktake.StocktakeDiffReportRow.product_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='stocktake.StocktakeDiffReportRow.product_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='stocktake.StocktakeDiffReportRow.type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='stocktake.StocktakeDiffReportRow.diff_quantity', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='stocktake.StocktakeDiffReportRow.quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='stocktake.StocktakeDiffReportRow.accounting_quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='stocktake.StocktakeDiffReportRow.inventory_quantity', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity_percentage', full_name='stocktake.StocktakeDiffReportRow.diff_quantity_percentage', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='stocktake.StocktakeDiffReportRow.target_date', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='stocktake.StocktakeDiffReportRow.unit_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='stocktake.StocktakeDiffReportRow.unit_name', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='stocktake.StocktakeDiffReportRow.unit_rate', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_code', full_name='stocktake.StocktakeDiffReportRow.unit_code', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='stocktake.StocktakeDiffReportRow.accounting_unit_id', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='stocktake.StocktakeDiffReportRow.accounting_unit_name', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='stocktake.StocktakeDiffReportRow.accounting_unit_code', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_name', full_name='stocktake.StocktakeDiffReportRow.company_name', index=22,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_inventory_quantity', full_name='stocktake.StocktakeDiffReportRow.accounting_inventory_quantity', index=23,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_diff_quantity', full_name='stocktake.StocktakeDiffReportRow.accounting_diff_quantity', index=24,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='stocktake.StocktakeDiffReportRow.unit_spec', index=25,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='stocktake.StocktakeDiffReportRow.status', index=26,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='stocktake.StocktakeDiffReportRow.schedule_code', index=27,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=18426,
  serialized_end=19118,
)


_STOCKTAKEDIFFREPORTRESPONSE = _descriptor.Descriptor(
  name='StocktakeDiffReportResponse',
  full_name='stocktake.StocktakeDiffReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='stocktake.StocktakeDiffReportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='stocktake.StocktakeDiffReportResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=19120,
  serialized_end=19213,
)


_GETUNCOMPLETEDOCREQUEST = _descriptor.Descriptor(
  name='GetUncompleteDocRequest',
  full_name='stocktake.GetUncompleteDocRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='stocktake.GetUncompleteDocRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='stocktake.GetUncompleteDocRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='stocktake.GetUncompleteDocRequest.store_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.GetUncompleteDocRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=19216,
  serialized_end=19366,
)


_CHECKDEMANDDETAIL = _descriptor.Descriptor(
  name='CheckDemandDetail',
  full_name='stocktake.CheckDemandDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='stocktake.CheckDemandDetail.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='stocktake.CheckDemandDetail.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='stocktake.CheckDemandDetail.type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_plan', full_name='stocktake.CheckDemandDetail.is_plan', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=19368,
  serialized_end=19444,
)


_GETUNCOMPLETEDOCRESPONSE = _descriptor.Descriptor(
  name='GetUncompleteDocResponse',
  full_name='stocktake.GetUncompleteDocResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='handler', full_name='stocktake.GetUncompleteDocResponse.handler', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust', full_name='stocktake.GetUncompleteDocResponse.adjust', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_diff', full_name='stocktake.GetUncompleteDocResponse.receiving_diff', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer', full_name='stocktake.GetUncompleteDocResponse.transfer', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving', full_name='stocktake.GetUncompleteDocResponse.receiving', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return', full_name='stocktake.GetUncompleteDocResponse.return', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_receiving', full_name='stocktake.GetUncompleteDocResponse.direct_receiving', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_receiving_diff', full_name='stocktake.GetUncompleteDocResponse.direct_receiving_diff', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='direct_return', full_name='stocktake.GetUncompleteDocResponse.direct_return', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='stocktake.GetUncompleteDocResponse.stocktake', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand', full_name='stocktake.GetUncompleteDocResponse.demand', index=10,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='assets', full_name='stocktake.GetUncompleteDocResponse.assets', index=11,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=19447,
  serialized_end=20144,
)


_RECREATESTOCKTAKEDOCREQUEST = _descriptor.Descriptor(
  name='RecreateStocktakeDocRequest',
  full_name='stocktake.RecreateStocktakeDocRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_ids', full_name='stocktake.RecreateStocktakeDocRequest.doc_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='calculate_inventory', full_name='stocktake.RecreateStocktakeDocRequest.calculate_inventory', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='stocktake.RecreateStocktakeDocRequest.schedule_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='stocktake.RecreateStocktakeDocRequest.remark', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='stocktake.RecreateStocktakeDocRequest.schedule_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_id', full_name='stocktake.RecreateStocktakeDocRequest.schedule_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.RecreateStocktakeDocRequest.lan', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=20147,
  serialized_end=20318,
)


_RECREATESTOCKTAKEDOCRESPONSE = _descriptor.Descriptor(
  name='RecreateStocktakeDocResponse',
  full_name='stocktake.RecreateStocktakeDocResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='stocktake.RecreateStocktakeDocResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='restocktake_doc_ids', full_name='stocktake.RecreateStocktakeDocResponse.restocktake_doc_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='has_recreate_doc_id_no_confirm', full_name='stocktake.RecreateStocktakeDocResponse.has_recreate_doc_id_no_confirm', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=20320,
  serialized_end=20435,
)


_STOCKTAKEDOCSTATISTICSREQUEST = _descriptor.Descriptor(
  name='StocktakeDocStatisticsRequest',
  full_name='stocktake.StocktakeDocStatisticsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='stocktake.StocktakeDocStatisticsRequest.store_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='stocktake.StocktakeDocStatisticsRequest.start_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='stocktake.StocktakeDocStatisticsRequest.end_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_group_by', full_name='stocktake.StocktakeDocStatisticsRequest.period_group_by', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='stocktake.StocktakeDocStatisticsRequest.stocktake_type', index=4,
      number=5, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='stocktake.StocktakeDocStatisticsRequest.status', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='stocktake.StocktakeDocStatisticsRequest.limit', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='stocktake.StocktakeDocStatisticsRequest.offset', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='stocktake.StocktakeDocStatisticsRequest.order', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='stocktake.StocktakeDocStatisticsRequest.sort', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='stocktake.StocktakeDocStatisticsRequest.is_wms_store', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.StocktakeDocStatisticsRequest.lan', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=20438,
  serialized_end=20772,
)


_STOCKTAKEDOCSTATISTICS = _descriptor.Descriptor(
  name='StocktakeDocStatistics',
  full_name='stocktake.StocktakeDocStatistics',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='date', full_name='stocktake.StocktakeDocStatistics.date', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='stocktake.StocktakeDocStatistics.store_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='stocktake.StocktakeDocStatistics.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='stocktake.StocktakeDocStatistics.stocktake_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='stocktake.StocktakeDocStatistics.status', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='count', full_name='stocktake.StocktakeDocStatistics.count', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='stocktake.StocktakeDocStatistics.store_id', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=20775,
  serialized_end=20926,
)


_STOCKTAKEDOCSTATISTICSRESPONSE = _descriptor.Descriptor(
  name='StocktakeDocStatisticsResponse',
  full_name='stocktake.StocktakeDocStatisticsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='stocktake.StocktakeDocStatisticsResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='stocktake.StocktakeDocStatisticsResponse.total', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=20928,
  serialized_end=21024,
)


_STOCKTAKEDIFFCOLLECTREPORTREQUEST = _descriptor.Descriptor(
  name='StocktakeDiffCollectReportRequest',
  full_name='stocktake.StocktakeDiffCollectReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='stocktake.StocktakeDiffCollectReportRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='stocktake.StocktakeDiffCollectReportRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='stocktake.StocktakeDiffCollectReportRequest.store_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='stocktake.StocktakeDiffCollectReportRequest.product_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='stocktake.StocktakeDiffCollectReportRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='stocktake.StocktakeDiffCollectReportRequest.offset', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='target_date', full_name='stocktake.StocktakeDiffCollectReportRequest.target_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='stocktake.StocktakeDiffCollectReportRequest.code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_type', full_name='stocktake.StocktakeDiffCollectReportRequest.stocktake_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='stocktake.StocktakeDiffCollectReportRequest.schedule_code', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='stocktake.StocktakeDiffCollectReportRequest.status', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='stocktake.StocktakeDiffCollectReportRequest.order', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='stocktake.StocktakeDiffCollectReportRequest.sort', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='stocktake.StocktakeDiffCollectReportRequest.period_symbol', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='upper_limit', full_name='stocktake.StocktakeDiffCollectReportRequest.upper_limit', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lower_limit', full_name='stocktake.StocktakeDiffCollectReportRequest.lower_limit', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='stocktake.StocktakeDiffCollectReportRequest.is_wms_store', index=16,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.StocktakeDiffCollectReportRequest.lan', index=17,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=21027,
  serialized_end=21482,
)


_STOCKTAKEDIFFCOLLECTREPORTROW = _descriptor.Descriptor(
  name='StocktakeDiffCollectReportRow',
  full_name='stocktake.StocktakeDiffCollectReportRow',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='stocktake.StocktakeDiffCollectReportRow.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='stocktake.StocktakeDiffCollectReportRow.store_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='stocktake.StocktakeDiffCollectReportRow.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_code', full_name='stocktake.StocktakeDiffCollectReportRow.company_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='stocktake.StocktakeDiffCollectReportRow.product_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='stocktake.StocktakeDiffCollectReportRow.product_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='stocktake.StocktakeDiffCollectReportRow.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='stocktake.StocktakeDiffCollectReportRow.diff_quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='stocktake.StocktakeDiffCollectReportRow.quantity', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='stocktake.StocktakeDiffCollectReportRow.accounting_quantity', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_quantity', full_name='stocktake.StocktakeDiffCollectReportRow.inventory_quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity_percentage', full_name='stocktake.StocktakeDiffCollectReportRow.diff_quantity_percentage', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='stocktake.StocktakeDiffCollectReportRow.unit_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='stocktake.StocktakeDiffCollectReportRow.unit_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='stocktake.StocktakeDiffCollectReportRow.unit_rate', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_code', full_name='stocktake.StocktakeDiffCollectReportRow.unit_code', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='stocktake.StocktakeDiffCollectReportRow.accounting_unit_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='stocktake.StocktakeDiffCollectReportRow.accounting_unit_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='stocktake.StocktakeDiffCollectReportRow.accounting_unit_code', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='company_name', full_name='stocktake.StocktakeDiffCollectReportRow.company_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_inventory_quantity', full_name='stocktake.StocktakeDiffCollectReportRow.accounting_inventory_quantity', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_diff_quantity', full_name='stocktake.StocktakeDiffCollectReportRow.accounting_diff_quantity', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='stocktake.StocktakeDiffCollectReportRow.unit_spec', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='stocktake.StocktakeDiffCollectReportRow.period_symbol', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=21485,
  serialized_end=22091,
)


_STOCKTAKEDIFFCOLLECTREPORTRESPONSE = _descriptor.Descriptor(
  name='StocktakeDiffCollectReportResponse',
  full_name='stocktake.StocktakeDiffCollectReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='stocktake.StocktakeDiffCollectReportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='stocktake.StocktakeDiffCollectReportResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22093,
  serialized_end=22200,
)


_UNCOMPLETEDOCREPORTREQUEST = _descriptor.Descriptor(
  name='UncompleteDocReportRequest',
  full_name='stocktake.UncompleteDocReportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bus_date', full_name='stocktake.UncompleteDocReportRequest.bus_date', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='stocktake.UncompleteDocReportRequest.store_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='stocktake.UncompleteDocReportRequest.code', index=2,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='stocktake.UncompleteDocReportRequest.limit', index=3,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='stocktake.UncompleteDocReportRequest.offset', index=4,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model', full_name='stocktake.UncompleteDocReportRequest.model', index=5,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='stocktake.UncompleteDocReportRequest.order', index=6,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='stocktake.UncompleteDocReportRequest.sort', index=7,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='stocktake.UncompleteDocReportRequest.is_wms_store', index=8,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='stocktake.UncompleteDocReportRequest.lan', index=9,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22203,
  serialized_end=22392,
)


_UNCOMPLETEDOCREPORTRESPONSE_UNCOMPLETEDOCREPORT = _descriptor.Descriptor(
  name='UncompleteDocReport',
  full_name='stocktake.UncompleteDocReportResponse.UncompleteDocReport',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='model', full_name='stocktake.UncompleteDocReportResponse.UncompleteDocReport.model', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='stocktake.UncompleteDocReportResponse.UncompleteDocReport.store_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='stocktake.UncompleteDocReportResponse.UncompleteDocReport.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='stocktake.UncompleteDocReportResponse.UncompleteDocReport.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='stocktake.UncompleteDocReportResponse.UncompleteDocReport.code', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_update_name', full_name='stocktake.UncompleteDocReportResponse.UncompleteDocReport.doc_update_name', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_update_time', full_name='stocktake.UncompleteDocReportResponse.UncompleteDocReport.doc_update_time', index=6,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_date', full_name='stocktake.UncompleteDocReportResponse.UncompleteDocReport.doc_date', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bus_date', full_name='stocktake.UncompleteDocReportResponse.UncompleteDocReport.bus_date', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='stocktake.UncompleteDocReportResponse.UncompleteDocReport.store_id', index=9,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22516,
  serialized_end=22726,
)

_UNCOMPLETEDOCREPORTRESPONSE = _descriptor.Descriptor(
  name='UncompleteDocReportResponse',
  full_name='stocktake.UncompleteDocReportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='stocktake.UncompleteDocReportResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='stocktake.UncompleteDocReportResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_UNCOMPLETEDOCREPORTRESPONSE_UNCOMPLETEDOCREPORT, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22395,
  serialized_end=22726,
)


_STOCKTAKEPRODUCTIMPORTREQUEST = _descriptor.Descriptor(
  name='StocktakeProductImportRequest',
  full_name='stocktake.StocktakeProductImportRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='stocktake.StocktakeProductImportRequest.doc_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_name', full_name='stocktake.StocktakeProductImportRequest.file_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_data', full_name='stocktake.StocktakeProductImportRequest.file_data', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22728,
  serialized_end=22813,
)


_PRODUCTIMPORTRESPONSEROWS = _descriptor.Descriptor(
  name='ProductImportResponseRows',
  full_name='stocktake.ProductImportResponseRows',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_num', full_name='stocktake.ProductImportResponseRows.row_num', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='stocktake.ProductImportResponseRows.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='stocktake.ProductImportResponseRows.product_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='stocktake.ProductImportResponseRows.quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='stocktake.ProductImportResponseRows.storage_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='stocktake.ProductImportResponseRows.spec', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit', full_name='stocktake.ProductImportResponseRows.unit', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error_msg', full_name='stocktake.ProductImportResponseRows.error_msg', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='stocktake.ProductImportResponseRows.position_id', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='stocktake.ProductImportResponseRows.position_code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='stocktake.ProductImportResponseRows.position_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=22816,
  serialized_end=23058,
)


_STOCKTAKEPRODUCTIMPORTRESPONSE = _descriptor.Descriptor(
  name='StocktakeProductImportResponse',
  full_name='stocktake.StocktakeProductImportResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='stocktake.StocktakeProductImportResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='stocktake.StocktakeProductImportResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='file_name', full_name='stocktake.StocktakeProductImportResponse.file_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows_num', full_name='stocktake.StocktakeProductImportResponse.rows_num', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='stocktake.StocktakeProductImportResponse.batch_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=23061,
  serialized_end=23216,
)


_UPDATESTOCKTAKEIMPORTBATCHREQUEST = _descriptor.Descriptor(
  name='UpdateStocktakeImportBatchRequest',
  full_name='stocktake.UpdateStocktakeImportBatchRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='stocktake.UpdateStocktakeImportBatchRequest.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='stocktake.UpdateStocktakeImportBatchRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=23218,
  serialized_end=23287,
)


_UPDATESTOCKTAKEIMPORTBATCHRESPONSE = _descriptor.Descriptor(
  name='UpdateStocktakeImportBatchResponse',
  full_name='stocktake.UpdateStocktakeImportBatchResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='stocktake.UpdateStocktakeImportBatchResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_id', full_name='stocktake.UpdateStocktakeImportBatchResponse.doc_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=23289,
  serialized_end=23357,
)


_STOCKTAKEPOSITIONPRODUCTS = _descriptor.Descriptor(
  name='StocktakePositionProducts',
  full_name='stocktake.StocktakePositionProducts',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='position_id', full_name='stocktake.StocktakePositionProducts.position_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='stocktake.StocktakePositionProducts.position_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='stocktake.StocktakePositionProducts.position_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='stocktake.StocktakePositionProducts.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='stocktake.StocktakePositionProducts.total', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=23360,
  serialized_end=23516,
)

_GETPRODUCTBYSTOCKTAKETYPERESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEPRODUCTTYPE
_STOCKTAKE.fields_by_name['status'].enum_type = _STOCKTAKE_STATUS
_STOCKTAKE.fields_by_name['process_status'].enum_type = _STOCKTAKE_STATUS
_STOCKTAKE.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKE.fields_by_name['attachments'].message_type = _ATTACHMENTS
_STOCKTAKE_STATUS.containing_type = _STOCKTAKE
_GETSTOCKTAKEREQUEST.fields_by_name['store_status'].enum_type = _GETSTOCKTAKEREQUEST_S_STATUS
_GETSTOCKTAKEREQUEST.fields_by_name['_type'].enum_type = _GETSTOCKTAKEREQUEST_S_TYPE
_GETSTOCKTAKEREQUEST.fields_by_name['status'].enum_type = _GETSTOCKTAKEREQUEST_STATUS
_GETSTOCKTAKEREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEREQUEST.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEREQUEST_S_STATUS.containing_type = _GETSTOCKTAKEREQUEST
_GETSTOCKTAKEREQUEST_S_TYPE.containing_type = _GETSTOCKTAKEREQUEST
_GETSTOCKTAKEREQUEST_STATUS.containing_type = _GETSTOCKTAKEREQUEST
_GETSTOCKTAKERESPONSE.fields_by_name['rows'].message_type = _STOCKTAKE
_STOCKTAKEPRODUCTTAGNAME.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEPRODUCTTAGNAME.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEPRODUCT.fields_by_name['units'].message_type = _STOCKTAKEPRODUCTUNITS
_STOCKTAKEPRODUCT.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEPRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEPRODUCT.fields_by_name['product_tags'].message_type = _STOCKTAKEPRODUCTTAGNAME
_GETSTOCKTAKEPRODUCTRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEPRODUCT
_GETSTOCKTAKEPRODUCTRESPONSE.fields_by_name['position_rows'].message_type = _STOCKTAKEPOSITIONPRODUCTS
_USERCREATESTOCKTAKEREQUEST.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_USERCREATESTOCKTAKERESPONSE.fields_by_name['status'].enum_type = _USERCREATESTOCKTAKERESPONSE_STATUS
_USERCREATESTOCKTAKERESPONSE.fields_by_name['process_status'].enum_type = _USERCREATESTOCKTAKERESPONSE_STATUS
_USERCREATESTOCKTAKERESPONSE.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_USERCREATESTOCKTAKERESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_USERCREATESTOCKTAKERESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_USERCREATESTOCKTAKERESPONSE_STATUS.containing_type = _USERCREATESTOCKTAKERESPONSE
_PUTSTOCKTAKEPRODUCTS.fields_by_name['tag_products'].message_type = _TAGQUANTITY
_PUTSTOCKTAKEBYDOCIDREQUEST.fields_by_name['products'].message_type = _PUTSTOCKTAKEPRODUCTS
_PUTSTOCKTAKEBYDOCIDREQUEST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['adjust'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['receiving_diff'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['transfer'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['receiving'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['return'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['direct_receiving'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['direct_receiving_diff'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['direct_return'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['stocktake'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['demand'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CHECKSTOCKTAKEBYDOCIDRESPONSE.fields_by_name['self_picking'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['adjust'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['receiving_diff'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['transfer'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['receiving'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['return'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['direct_receiving'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['direct_receiving_diff'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['direct_return'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_APPROVESTOCKTAKEBYDOCIDRESPONSE.fields_by_name['self_picking'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_CREATESTOCKTAKEBATCHREQUEST.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATESTOCKTAKEBATCHRESPONSE.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATESTOCKTAKEREQUEST.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATESTOCKTAKERESPONSE.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKETAGS.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKETAGS.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKETAGSRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKETAGS
_ACTIONSTOCKTAKETAGSREQUEST.fields_by_name['action'].enum_type = _ACTIONSTOCKTAKETAGSREQUEST_ACTION
_ACTIONSTOCKTAKETAGSREQUEST_ACTION.containing_type = _ACTIONSTOCKTAKETAGSREQUEST
_ACTIONSTOCKTAKETAGSRESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ACTIONSTOCKTAKETAGSRESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEBALANCEREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEBALANCEREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEBALANCEREQUEST.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCE.fields_by_name['forecasting_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCE.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETSTOCKTAKEBALANCERESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEBALANCE
_STOCKTAKEBALANCEPRODUCTGROUP.fields_by_name['tag_details'].message_type = _TAGPRODUCTBI
_GETSTOCKTAKEBALANCEPRODUCTGROUPRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEBALANCEPRODUCTGROUP
_STOCKTAKEBIDETAILEDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILEDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILED.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILED.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILED.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILED.fields_by_name['product_tags'].message_type = _PRODUCTTAGBI
_STOCKTAKEBIDETAILED.fields_by_name['created_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILED.fields_by_name['submitted_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBIDETAILEDRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEBIDETAILED
_STOCKTAKEBIDETAILEDRESPONSE.fields_by_name['total'].message_type = _ST_TOTAL
_STOCKTAKEBALANCEREGIONREQUEST.fields_by_name['start'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCEREGIONREQUEST.fields_by_name['end'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEBALANCEREGION.fields_by_name['details'].message_type = _STOCKTAKEBALANCEREGIONDETAILS
_STOCKTAKEBALANCEREGIONRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEBALANCEREGION
_STOREDATASCOPE.fields_by_name['rows'].message_type = _SCOPESTORES
_ADVANCESTOCKTAKEDIFFRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEPRODUCT
_ADVANCESTOCKTAKEDIFFRESPONSE.fields_by_name['position_rows'].message_type = _STOCKTAKEPOSITIONPRODUCTS
_STOCKTAKEDIFFREPORTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFREPORTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFREPORTREQUEST.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFREPORTROW.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFREPORTRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEDIFFREPORTROW
_GETUNCOMPLETEDOCREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETUNCOMPLETEDOCREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['adjust'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['receiving_diff'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['transfer'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['receiving'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['return'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['direct_receiving'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['direct_receiving_diff'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['direct_return'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['stocktake'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['demand'].message_type = _CHECKDEMANDDETAIL
_GETUNCOMPLETEDOCRESPONSE.fields_by_name['assets'].message_type = _CHECKSTOCKTAKEBYDOCIDDETAIL
_STOCKTAKEDOCSTATISTICSREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDOCSTATISTICSREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDOCSTATISTICSREQUEST.fields_by_name['period_group_by'].enum_type = _PERIODGROUPMETHOD
_STOCKTAKEDOCSTATISTICSRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEDOCSTATISTICS
_STOCKTAKEDIFFCOLLECTREPORTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFCOLLECTREPORTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFCOLLECTREPORTREQUEST.fields_by_name['target_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_STOCKTAKEDIFFCOLLECTREPORTRESPONSE.fields_by_name['rows'].message_type = _STOCKTAKEDIFFCOLLECTREPORTROW
_UNCOMPLETEDOCREPORTRESPONSE_UNCOMPLETEDOCREPORT.containing_type = _UNCOMPLETEDOCREPORTRESPONSE
_UNCOMPLETEDOCREPORTRESPONSE.fields_by_name['rows'].message_type = _UNCOMPLETEDOCREPORTRESPONSE_UNCOMPLETEDOCREPORT
_STOCKTAKEPRODUCTIMPORTRESPONSE.fields_by_name['rows'].message_type = _PRODUCTIMPORTRESPONSEROWS
_STOCKTAKEPOSITIONPRODUCTS.fields_by_name['products'].message_type = _STOCKTAKEPRODUCT
DESCRIPTOR.message_types_by_name['Pong'] = _PONG
DESCRIPTOR.message_types_by_name['GetProductByStocktakeTypeRequest'] = _GETPRODUCTBYSTOCKTAKETYPEREQUEST
DESCRIPTOR.message_types_by_name['StocktakeProductType'] = _STOCKTAKEPRODUCTTYPE
DESCRIPTOR.message_types_by_name['GetProductByStocktakeTypeResponse'] = _GETPRODUCTBYSTOCKTAKETYPERESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeByDocIDRequest'] = _GETSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['Stocktake'] = _STOCKTAKE
DESCRIPTOR.message_types_by_name['GetStocktakeRequest'] = _GETSTOCKTAKEREQUEST
DESCRIPTOR.message_types_by_name['GetStocktakeResponse'] = _GETSTOCKTAKERESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeProductRequest'] = _GETSTOCKTAKEPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['StocktakeProductTagName'] = _STOCKTAKEPRODUCTTAGNAME
DESCRIPTOR.message_types_by_name['StocktakeProductUnits'] = _STOCKTAKEPRODUCTUNITS
DESCRIPTOR.message_types_by_name['StocktakeProduct'] = _STOCKTAKEPRODUCT
DESCRIPTOR.message_types_by_name['GetStocktakeProductResponse'] = _GETSTOCKTAKEPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['UserCreateStocktakeRequest'] = _USERCREATESTOCKTAKEREQUEST
DESCRIPTOR.message_types_by_name['UserCreateStocktakeResponse'] = _USERCREATESTOCKTAKERESPONSE
DESCRIPTOR.message_types_by_name['TagQuantity'] = _TAGQUANTITY
DESCRIPTOR.message_types_by_name['PutStocktakeProducts'] = _PUTSTOCKTAKEPRODUCTS
DESCRIPTOR.message_types_by_name['PutStocktakeByDocIDRequest'] = _PUTSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['Attachments'] = _ATTACHMENTS
DESCRIPTOR.message_types_by_name['RejectStocktakeProductRequest'] = _REJECTSTOCKTAKEPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['RejectStocktakeProductResponse'] = _REJECTSTOCKTAKEPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['PutStocktakeByDocIDResponse'] = _PUTSTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['CheckStocktakeByDocIDRequest'] = _CHECKSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['CheckStocktakeByDocIDDetail'] = _CHECKSTOCKTAKEBYDOCIDDETAIL
DESCRIPTOR.message_types_by_name['CheckStocktakeByDocIDResponse'] = _CHECKSTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['ConfirmStocktakeByDocIDRequest'] = _CONFIRMSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['SubmitStocktakeByDocIDResponse'] = _SUBMITSTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['SubmitStocktakeByDocIDRequest'] = _SUBMITSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['ApproveStocktakeByDocIDRequest'] = _APPROVESTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['ApproveStocktakeByDocIDResponse'] = _APPROVESTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['ConfirmStocktakeByDocIDResponse'] = _CONFIRMSTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['CancelStocktakeByDocIDRequest'] = _CANCELSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['CancelStocktakeByDocIDResponse'] = _CANCELSTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['CreateStocktakeBatchRequest'] = _CREATESTOCKTAKEBATCHREQUEST
DESCRIPTOR.message_types_by_name['CreateStocktakeBatchResponse'] = _CREATESTOCKTAKEBATCHRESPONSE
DESCRIPTOR.message_types_by_name['CreateStocktakeRequest'] = _CREATESTOCKTAKEREQUEST
DESCRIPTOR.message_types_by_name['CreateStocktakeResponse'] = _CREATESTOCKTAKERESPONSE
DESCRIPTOR.message_types_by_name['CheckedStocktakeByDocIDRequest'] = _CHECKEDSTOCKTAKEBYDOCIDREQUEST
DESCRIPTOR.message_types_by_name['CheckedStocktakeByDocIDResponse'] = _CHECKEDSTOCKTAKEBYDOCIDRESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeTagsRequest'] = _GETSTOCKTAKETAGSREQUEST
DESCRIPTOR.message_types_by_name['StocktakeTags'] = _STOCKTAKETAGS
DESCRIPTOR.message_types_by_name['GetStocktakeTagsResponse'] = _GETSTOCKTAKETAGSRESPONSE
DESCRIPTOR.message_types_by_name['ActionStocktakeTagsRequest'] = _ACTIONSTOCKTAKETAGSREQUEST
DESCRIPTOR.message_types_by_name['ActionStocktakeTagsResponse'] = _ACTIONSTOCKTAKETAGSRESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeTagsByIdRequest'] = _GETSTOCKTAKETAGSBYIDREQUEST
DESCRIPTOR.message_types_by_name['DeleteStocktakeProductTagsRequest'] = _DELETESTOCKTAKEPRODUCTTAGSREQUEST
DESCRIPTOR.message_types_by_name['DeleteStocktakeProductTagsResponse'] = _DELETESTOCKTAKEPRODUCTTAGSRESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeBalanceRequest'] = _GETSTOCKTAKEBALANCEREQUEST
DESCRIPTOR.message_types_by_name['StocktakeBalance'] = _STOCKTAKEBALANCE
DESCRIPTOR.message_types_by_name['GetStocktakeBalanceResponse'] = _GETSTOCKTAKEBALANCERESPONSE
DESCRIPTOR.message_types_by_name['GetStocktakeBalanceProductGroupRequest'] = _GETSTOCKTAKEBALANCEPRODUCTGROUPREQUEST
DESCRIPTOR.message_types_by_name['TagProductBi'] = _TAGPRODUCTBI
DESCRIPTOR.message_types_by_name['StocktakeBalanceProductGroup'] = _STOCKTAKEBALANCEPRODUCTGROUP
DESCRIPTOR.message_types_by_name['GetStocktakeBalanceProductGroupResponse'] = _GETSTOCKTAKEBALANCEPRODUCTGROUPRESPONSE
DESCRIPTOR.message_types_by_name['StocktakeBiDetailedRequest'] = _STOCKTAKEBIDETAILEDREQUEST
DESCRIPTOR.message_types_by_name['StocktakeBiDetailed'] = _STOCKTAKEBIDETAILED
DESCRIPTOR.message_types_by_name['ProductTagBi'] = _PRODUCTTAGBI
DESCRIPTOR.message_types_by_name['ST_total'] = _ST_TOTAL
DESCRIPTOR.message_types_by_name['StocktakeBiDetailedResponse'] = _STOCKTAKEBIDETAILEDRESPONSE
DESCRIPTOR.message_types_by_name['StocktakeBalanceRegionRequest'] = _STOCKTAKEBALANCEREGIONREQUEST
DESCRIPTOR.message_types_by_name['StocktakeBalanceRegionDetails'] = _STOCKTAKEBALANCEREGIONDETAILS
DESCRIPTOR.message_types_by_name['StocktakeBalanceRegion'] = _STOCKTAKEBALANCEREGION
DESCRIPTOR.message_types_by_name['StocktakeBalanceRegionResponse'] = _STOCKTAKEBALANCEREGIONRESPONSE
DESCRIPTOR.message_types_by_name['StoreDataScopeRequest'] = _STOREDATASCOPEREQUEST
DESCRIPTOR.message_types_by_name['ScopeStores'] = _SCOPESTORES
DESCRIPTOR.message_types_by_name['StoreDataScope'] = _STOREDATASCOPE
DESCRIPTOR.message_types_by_name['AdvanceStocktakeDiffRequest'] = _ADVANCESTOCKTAKEDIFFREQUEST
DESCRIPTOR.message_types_by_name['AdvanceStocktakeDiffResponse'] = _ADVANCESTOCKTAKEDIFFRESPONSE
DESCRIPTOR.message_types_by_name['StocktakeDiffReportRequest'] = _STOCKTAKEDIFFREPORTREQUEST
DESCRIPTOR.message_types_by_name['StocktakeDiffReportRow'] = _STOCKTAKEDIFFREPORTROW
DESCRIPTOR.message_types_by_name['StocktakeDiffReportResponse'] = _STOCKTAKEDIFFREPORTRESPONSE
DESCRIPTOR.message_types_by_name['GetUncompleteDocRequest'] = _GETUNCOMPLETEDOCREQUEST
DESCRIPTOR.message_types_by_name['CheckDemandDetail'] = _CHECKDEMANDDETAIL
DESCRIPTOR.message_types_by_name['GetUncompleteDocResponse'] = _GETUNCOMPLETEDOCRESPONSE
DESCRIPTOR.message_types_by_name['RecreateStocktakeDocRequest'] = _RECREATESTOCKTAKEDOCREQUEST
DESCRIPTOR.message_types_by_name['RecreateStocktakeDocResponse'] = _RECREATESTOCKTAKEDOCRESPONSE
DESCRIPTOR.message_types_by_name['StocktakeDocStatisticsRequest'] = _STOCKTAKEDOCSTATISTICSREQUEST
DESCRIPTOR.message_types_by_name['StocktakeDocStatistics'] = _STOCKTAKEDOCSTATISTICS
DESCRIPTOR.message_types_by_name['StocktakeDocStatisticsResponse'] = _STOCKTAKEDOCSTATISTICSRESPONSE
DESCRIPTOR.message_types_by_name['StocktakeDiffCollectReportRequest'] = _STOCKTAKEDIFFCOLLECTREPORTREQUEST
DESCRIPTOR.message_types_by_name['StocktakeDiffCollectReportRow'] = _STOCKTAKEDIFFCOLLECTREPORTROW
DESCRIPTOR.message_types_by_name['StocktakeDiffCollectReportResponse'] = _STOCKTAKEDIFFCOLLECTREPORTRESPONSE
DESCRIPTOR.message_types_by_name['UncompleteDocReportRequest'] = _UNCOMPLETEDOCREPORTREQUEST
DESCRIPTOR.message_types_by_name['UncompleteDocReportResponse'] = _UNCOMPLETEDOCREPORTRESPONSE
DESCRIPTOR.message_types_by_name['StocktakeProductImportRequest'] = _STOCKTAKEPRODUCTIMPORTREQUEST
DESCRIPTOR.message_types_by_name['ProductImportResponseRows'] = _PRODUCTIMPORTRESPONSEROWS
DESCRIPTOR.message_types_by_name['StocktakeProductImportResponse'] = _STOCKTAKEPRODUCTIMPORTRESPONSE
DESCRIPTOR.message_types_by_name['UpdateStocktakeImportBatchRequest'] = _UPDATESTOCKTAKEIMPORTBATCHREQUEST
DESCRIPTOR.message_types_by_name['UpdateStocktakeImportBatchResponse'] = _UPDATESTOCKTAKEIMPORTBATCHRESPONSE
DESCRIPTOR.message_types_by_name['StocktakePositionProducts'] = _STOCKTAKEPOSITIONPRODUCTS
DESCRIPTOR.enum_types_by_name['PeriodGroupMethod'] = _PERIODGROUPMETHOD
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Pong = _reflection.GeneratedProtocolMessageType('Pong', (_message.Message,), dict(
  DESCRIPTOR = _PONG,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.Pong)
  ))
_sym_db.RegisterMessage(Pong)

GetProductByStocktakeTypeRequest = _reflection.GeneratedProtocolMessageType('GetProductByStocktakeTypeRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTBYSTOCKTAKETYPEREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.GetProductByStocktakeTypeRequest)
  ))
_sym_db.RegisterMessage(GetProductByStocktakeTypeRequest)

StocktakeProductType = _reflection.GeneratedProtocolMessageType('StocktakeProductType', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPRODUCTTYPE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeProductType)
  ))
_sym_db.RegisterMessage(StocktakeProductType)

GetProductByStocktakeTypeResponse = _reflection.GeneratedProtocolMessageType('GetProductByStocktakeTypeResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTBYSTOCKTAKETYPERESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.GetProductByStocktakeTypeResponse)
  ))
_sym_db.RegisterMessage(GetProductByStocktakeTypeResponse)

GetStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.GetStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeByDocIDRequest)

Stocktake = _reflection.GeneratedProtocolMessageType('Stocktake', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.Stocktake)
  ))
_sym_db.RegisterMessage(Stocktake)

GetStocktakeRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.GetStocktakeRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeRequest)

GetStocktakeResponse = _reflection.GeneratedProtocolMessageType('GetStocktakeResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKERESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.GetStocktakeResponse)
  ))
_sym_db.RegisterMessage(GetStocktakeResponse)

GetStocktakeProductRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEPRODUCTREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.GetStocktakeProductRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeProductRequest)

StocktakeProductTagName = _reflection.GeneratedProtocolMessageType('StocktakeProductTagName', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPRODUCTTAGNAME,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeProductTagName)
  ))
_sym_db.RegisterMessage(StocktakeProductTagName)

StocktakeProductUnits = _reflection.GeneratedProtocolMessageType('StocktakeProductUnits', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPRODUCTUNITS,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeProductUnits)
  ))
_sym_db.RegisterMessage(StocktakeProductUnits)

StocktakeProduct = _reflection.GeneratedProtocolMessageType('StocktakeProduct', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPRODUCT,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeProduct)
  ))
_sym_db.RegisterMessage(StocktakeProduct)

GetStocktakeProductResponse = _reflection.GeneratedProtocolMessageType('GetStocktakeProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEPRODUCTRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.GetStocktakeProductResponse)
  ))
_sym_db.RegisterMessage(GetStocktakeProductResponse)

UserCreateStocktakeRequest = _reflection.GeneratedProtocolMessageType('UserCreateStocktakeRequest', (_message.Message,), dict(
  DESCRIPTOR = _USERCREATESTOCKTAKEREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.UserCreateStocktakeRequest)
  ))
_sym_db.RegisterMessage(UserCreateStocktakeRequest)

UserCreateStocktakeResponse = _reflection.GeneratedProtocolMessageType('UserCreateStocktakeResponse', (_message.Message,), dict(
  DESCRIPTOR = _USERCREATESTOCKTAKERESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.UserCreateStocktakeResponse)
  ))
_sym_db.RegisterMessage(UserCreateStocktakeResponse)

TagQuantity = _reflection.GeneratedProtocolMessageType('TagQuantity', (_message.Message,), dict(
  DESCRIPTOR = _TAGQUANTITY,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.TagQuantity)
  ))
_sym_db.RegisterMessage(TagQuantity)

PutStocktakeProducts = _reflection.GeneratedProtocolMessageType('PutStocktakeProducts', (_message.Message,), dict(
  DESCRIPTOR = _PUTSTOCKTAKEPRODUCTS,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.PutStocktakeProducts)
  ))
_sym_db.RegisterMessage(PutStocktakeProducts)

PutStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('PutStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _PUTSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.PutStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(PutStocktakeByDocIDRequest)

Attachments = _reflection.GeneratedProtocolMessageType('Attachments', (_message.Message,), dict(
  DESCRIPTOR = _ATTACHMENTS,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.Attachments)
  ))
_sym_db.RegisterMessage(Attachments)

RejectStocktakeProductRequest = _reflection.GeneratedProtocolMessageType('RejectStocktakeProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _REJECTSTOCKTAKEPRODUCTREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.RejectStocktakeProductRequest)
  ))
_sym_db.RegisterMessage(RejectStocktakeProductRequest)

RejectStocktakeProductResponse = _reflection.GeneratedProtocolMessageType('RejectStocktakeProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _REJECTSTOCKTAKEPRODUCTRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.RejectStocktakeProductResponse)
  ))
_sym_db.RegisterMessage(RejectStocktakeProductResponse)

PutStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('PutStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _PUTSTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.PutStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(PutStocktakeByDocIDResponse)

CheckStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('CheckStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHECKSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.CheckStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(CheckStocktakeByDocIDRequest)

CheckStocktakeByDocIDDetail = _reflection.GeneratedProtocolMessageType('CheckStocktakeByDocIDDetail', (_message.Message,), dict(
  DESCRIPTOR = _CHECKSTOCKTAKEBYDOCIDDETAIL,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.CheckStocktakeByDocIDDetail)
  ))
_sym_db.RegisterMessage(CheckStocktakeByDocIDDetail)

CheckStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('CheckStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _CHECKSTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.CheckStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(CheckStocktakeByDocIDResponse)

ConfirmStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('ConfirmStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.ConfirmStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(ConfirmStocktakeByDocIDRequest)

SubmitStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('SubmitStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITSTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.SubmitStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(SubmitStocktakeByDocIDResponse)

SubmitStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('SubmitStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.SubmitStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(SubmitStocktakeByDocIDRequest)

ApproveStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('ApproveStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _APPROVESTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.ApproveStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(ApproveStocktakeByDocIDRequest)

ApproveStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('ApproveStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _APPROVESTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.ApproveStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(ApproveStocktakeByDocIDResponse)

ConfirmStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('ConfirmStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMSTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.ConfirmStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(ConfirmStocktakeByDocIDResponse)

CancelStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('CancelStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _CANCELSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.CancelStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(CancelStocktakeByDocIDRequest)

CancelStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('CancelStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _CANCELSTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.CancelStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(CancelStocktakeByDocIDResponse)

CreateStocktakeBatchRequest = _reflection.GeneratedProtocolMessageType('CreateStocktakeBatchRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATESTOCKTAKEBATCHREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.CreateStocktakeBatchRequest)
  ))
_sym_db.RegisterMessage(CreateStocktakeBatchRequest)

CreateStocktakeBatchResponse = _reflection.GeneratedProtocolMessageType('CreateStocktakeBatchResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATESTOCKTAKEBATCHRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.CreateStocktakeBatchResponse)
  ))
_sym_db.RegisterMessage(CreateStocktakeBatchResponse)

CreateStocktakeRequest = _reflection.GeneratedProtocolMessageType('CreateStocktakeRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATESTOCKTAKEREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.CreateStocktakeRequest)
  ))
_sym_db.RegisterMessage(CreateStocktakeRequest)

CreateStocktakeResponse = _reflection.GeneratedProtocolMessageType('CreateStocktakeResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATESTOCKTAKERESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.CreateStocktakeResponse)
  ))
_sym_db.RegisterMessage(CreateStocktakeResponse)

CheckedStocktakeByDocIDRequest = _reflection.GeneratedProtocolMessageType('CheckedStocktakeByDocIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHECKEDSTOCKTAKEBYDOCIDREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.CheckedStocktakeByDocIDRequest)
  ))
_sym_db.RegisterMessage(CheckedStocktakeByDocIDRequest)

CheckedStocktakeByDocIDResponse = _reflection.GeneratedProtocolMessageType('CheckedStocktakeByDocIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _CHECKEDSTOCKTAKEBYDOCIDRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.CheckedStocktakeByDocIDResponse)
  ))
_sym_db.RegisterMessage(CheckedStocktakeByDocIDResponse)

GetStocktakeTagsRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeTagsRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKETAGSREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.GetStocktakeTagsRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeTagsRequest)

StocktakeTags = _reflection.GeneratedProtocolMessageType('StocktakeTags', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKETAGS,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeTags)
  ))
_sym_db.RegisterMessage(StocktakeTags)

GetStocktakeTagsResponse = _reflection.GeneratedProtocolMessageType('GetStocktakeTagsResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKETAGSRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.GetStocktakeTagsResponse)
  ))
_sym_db.RegisterMessage(GetStocktakeTagsResponse)

ActionStocktakeTagsRequest = _reflection.GeneratedProtocolMessageType('ActionStocktakeTagsRequest', (_message.Message,), dict(
  DESCRIPTOR = _ACTIONSTOCKTAKETAGSREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.ActionStocktakeTagsRequest)
  ))
_sym_db.RegisterMessage(ActionStocktakeTagsRequest)

ActionStocktakeTagsResponse = _reflection.GeneratedProtocolMessageType('ActionStocktakeTagsResponse', (_message.Message,), dict(
  DESCRIPTOR = _ACTIONSTOCKTAKETAGSRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.ActionStocktakeTagsResponse)
  ))
_sym_db.RegisterMessage(ActionStocktakeTagsResponse)

GetStocktakeTagsByIdRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeTagsByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKETAGSBYIDREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.GetStocktakeTagsByIdRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeTagsByIdRequest)

DeleteStocktakeProductTagsRequest = _reflection.GeneratedProtocolMessageType('DeleteStocktakeProductTagsRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETESTOCKTAKEPRODUCTTAGSREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.DeleteStocktakeProductTagsRequest)
  ))
_sym_db.RegisterMessage(DeleteStocktakeProductTagsRequest)

DeleteStocktakeProductTagsResponse = _reflection.GeneratedProtocolMessageType('DeleteStocktakeProductTagsResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETESTOCKTAKEPRODUCTTAGSRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.DeleteStocktakeProductTagsResponse)
  ))
_sym_db.RegisterMessage(DeleteStocktakeProductTagsResponse)

GetStocktakeBalanceRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeBalanceRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEBALANCEREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.GetStocktakeBalanceRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeBalanceRequest)

StocktakeBalance = _reflection.GeneratedProtocolMessageType('StocktakeBalance', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBALANCE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeBalance)
  ))
_sym_db.RegisterMessage(StocktakeBalance)

GetStocktakeBalanceResponse = _reflection.GeneratedProtocolMessageType('GetStocktakeBalanceResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEBALANCERESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.GetStocktakeBalanceResponse)
  ))
_sym_db.RegisterMessage(GetStocktakeBalanceResponse)

GetStocktakeBalanceProductGroupRequest = _reflection.GeneratedProtocolMessageType('GetStocktakeBalanceProductGroupRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEBALANCEPRODUCTGROUPREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.GetStocktakeBalanceProductGroupRequest)
  ))
_sym_db.RegisterMessage(GetStocktakeBalanceProductGroupRequest)

TagProductBi = _reflection.GeneratedProtocolMessageType('TagProductBi', (_message.Message,), dict(
  DESCRIPTOR = _TAGPRODUCTBI,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.TagProductBi)
  ))
_sym_db.RegisterMessage(TagProductBi)

StocktakeBalanceProductGroup = _reflection.GeneratedProtocolMessageType('StocktakeBalanceProductGroup', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBALANCEPRODUCTGROUP,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeBalanceProductGroup)
  ))
_sym_db.RegisterMessage(StocktakeBalanceProductGroup)

GetStocktakeBalanceProductGroupResponse = _reflection.GeneratedProtocolMessageType('GetStocktakeBalanceProductGroupResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSTOCKTAKEBALANCEPRODUCTGROUPRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.GetStocktakeBalanceProductGroupResponse)
  ))
_sym_db.RegisterMessage(GetStocktakeBalanceProductGroupResponse)

StocktakeBiDetailedRequest = _reflection.GeneratedProtocolMessageType('StocktakeBiDetailedRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBIDETAILEDREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeBiDetailedRequest)
  ))
_sym_db.RegisterMessage(StocktakeBiDetailedRequest)

StocktakeBiDetailed = _reflection.GeneratedProtocolMessageType('StocktakeBiDetailed', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBIDETAILED,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeBiDetailed)
  ))
_sym_db.RegisterMessage(StocktakeBiDetailed)

ProductTagBi = _reflection.GeneratedProtocolMessageType('ProductTagBi', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTTAGBI,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.ProductTagBi)
  ))
_sym_db.RegisterMessage(ProductTagBi)

ST_total = _reflection.GeneratedProtocolMessageType('ST_total', (_message.Message,), dict(
  DESCRIPTOR = _ST_TOTAL,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.ST_total)
  ))
_sym_db.RegisterMessage(ST_total)

StocktakeBiDetailedResponse = _reflection.GeneratedProtocolMessageType('StocktakeBiDetailedResponse', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBIDETAILEDRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeBiDetailedResponse)
  ))
_sym_db.RegisterMessage(StocktakeBiDetailedResponse)

StocktakeBalanceRegionRequest = _reflection.GeneratedProtocolMessageType('StocktakeBalanceRegionRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBALANCEREGIONREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeBalanceRegionRequest)
  ))
_sym_db.RegisterMessage(StocktakeBalanceRegionRequest)

StocktakeBalanceRegionDetails = _reflection.GeneratedProtocolMessageType('StocktakeBalanceRegionDetails', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBALANCEREGIONDETAILS,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeBalanceRegionDetails)
  ))
_sym_db.RegisterMessage(StocktakeBalanceRegionDetails)

StocktakeBalanceRegion = _reflection.GeneratedProtocolMessageType('StocktakeBalanceRegion', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBALANCEREGION,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeBalanceRegion)
  ))
_sym_db.RegisterMessage(StocktakeBalanceRegion)

StocktakeBalanceRegionResponse = _reflection.GeneratedProtocolMessageType('StocktakeBalanceRegionResponse', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEBALANCEREGIONRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeBalanceRegionResponse)
  ))
_sym_db.RegisterMessage(StocktakeBalanceRegionResponse)

StoreDataScopeRequest = _reflection.GeneratedProtocolMessageType('StoreDataScopeRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOREDATASCOPEREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StoreDataScopeRequest)
  ))
_sym_db.RegisterMessage(StoreDataScopeRequest)

ScopeStores = _reflection.GeneratedProtocolMessageType('ScopeStores', (_message.Message,), dict(
  DESCRIPTOR = _SCOPESTORES,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.ScopeStores)
  ))
_sym_db.RegisterMessage(ScopeStores)

StoreDataScope = _reflection.GeneratedProtocolMessageType('StoreDataScope', (_message.Message,), dict(
  DESCRIPTOR = _STOREDATASCOPE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StoreDataScope)
  ))
_sym_db.RegisterMessage(StoreDataScope)

AdvanceStocktakeDiffRequest = _reflection.GeneratedProtocolMessageType('AdvanceStocktakeDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _ADVANCESTOCKTAKEDIFFREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.AdvanceStocktakeDiffRequest)
  ))
_sym_db.RegisterMessage(AdvanceStocktakeDiffRequest)

AdvanceStocktakeDiffResponse = _reflection.GeneratedProtocolMessageType('AdvanceStocktakeDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _ADVANCESTOCKTAKEDIFFRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.AdvanceStocktakeDiffResponse)
  ))
_sym_db.RegisterMessage(AdvanceStocktakeDiffResponse)

StocktakeDiffReportRequest = _reflection.GeneratedProtocolMessageType('StocktakeDiffReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDIFFREPORTREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeDiffReportRequest)
  ))
_sym_db.RegisterMessage(StocktakeDiffReportRequest)

StocktakeDiffReportRow = _reflection.GeneratedProtocolMessageType('StocktakeDiffReportRow', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDIFFREPORTROW,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeDiffReportRow)
  ))
_sym_db.RegisterMessage(StocktakeDiffReportRow)

StocktakeDiffReportResponse = _reflection.GeneratedProtocolMessageType('StocktakeDiffReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDIFFREPORTRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeDiffReportResponse)
  ))
_sym_db.RegisterMessage(StocktakeDiffReportResponse)

GetUncompleteDocRequest = _reflection.GeneratedProtocolMessageType('GetUncompleteDocRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETUNCOMPLETEDOCREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.GetUncompleteDocRequest)
  ))
_sym_db.RegisterMessage(GetUncompleteDocRequest)

CheckDemandDetail = _reflection.GeneratedProtocolMessageType('CheckDemandDetail', (_message.Message,), dict(
  DESCRIPTOR = _CHECKDEMANDDETAIL,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.CheckDemandDetail)
  ))
_sym_db.RegisterMessage(CheckDemandDetail)

GetUncompleteDocResponse = _reflection.GeneratedProtocolMessageType('GetUncompleteDocResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETUNCOMPLETEDOCRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.GetUncompleteDocResponse)
  ))
_sym_db.RegisterMessage(GetUncompleteDocResponse)

RecreateStocktakeDocRequest = _reflection.GeneratedProtocolMessageType('RecreateStocktakeDocRequest', (_message.Message,), dict(
  DESCRIPTOR = _RECREATESTOCKTAKEDOCREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.RecreateStocktakeDocRequest)
  ))
_sym_db.RegisterMessage(RecreateStocktakeDocRequest)

RecreateStocktakeDocResponse = _reflection.GeneratedProtocolMessageType('RecreateStocktakeDocResponse', (_message.Message,), dict(
  DESCRIPTOR = _RECREATESTOCKTAKEDOCRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.RecreateStocktakeDocResponse)
  ))
_sym_db.RegisterMessage(RecreateStocktakeDocResponse)

StocktakeDocStatisticsRequest = _reflection.GeneratedProtocolMessageType('StocktakeDocStatisticsRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDOCSTATISTICSREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeDocStatisticsRequest)
  ))
_sym_db.RegisterMessage(StocktakeDocStatisticsRequest)

StocktakeDocStatistics = _reflection.GeneratedProtocolMessageType('StocktakeDocStatistics', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDOCSTATISTICS,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeDocStatistics)
  ))
_sym_db.RegisterMessage(StocktakeDocStatistics)

StocktakeDocStatisticsResponse = _reflection.GeneratedProtocolMessageType('StocktakeDocStatisticsResponse', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDOCSTATISTICSRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeDocStatisticsResponse)
  ))
_sym_db.RegisterMessage(StocktakeDocStatisticsResponse)

StocktakeDiffCollectReportRequest = _reflection.GeneratedProtocolMessageType('StocktakeDiffCollectReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDIFFCOLLECTREPORTREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeDiffCollectReportRequest)
  ))
_sym_db.RegisterMessage(StocktakeDiffCollectReportRequest)

StocktakeDiffCollectReportRow = _reflection.GeneratedProtocolMessageType('StocktakeDiffCollectReportRow', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDIFFCOLLECTREPORTROW,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeDiffCollectReportRow)
  ))
_sym_db.RegisterMessage(StocktakeDiffCollectReportRow)

StocktakeDiffCollectReportResponse = _reflection.GeneratedProtocolMessageType('StocktakeDiffCollectReportResponse', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEDIFFCOLLECTREPORTRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeDiffCollectReportResponse)
  ))
_sym_db.RegisterMessage(StocktakeDiffCollectReportResponse)

UncompleteDocReportRequest = _reflection.GeneratedProtocolMessageType('UncompleteDocReportRequest', (_message.Message,), dict(
  DESCRIPTOR = _UNCOMPLETEDOCREPORTREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.UncompleteDocReportRequest)
  ))
_sym_db.RegisterMessage(UncompleteDocReportRequest)

UncompleteDocReportResponse = _reflection.GeneratedProtocolMessageType('UncompleteDocReportResponse', (_message.Message,), dict(

  UncompleteDocReport = _reflection.GeneratedProtocolMessageType('UncompleteDocReport', (_message.Message,), dict(
    DESCRIPTOR = _UNCOMPLETEDOCREPORTRESPONSE_UNCOMPLETEDOCREPORT,
    __module__ = 'stocktake_pb2'
    # @@protoc_insertion_point(class_scope:stocktake.UncompleteDocReportResponse.UncompleteDocReport)
    ))
  ,
  DESCRIPTOR = _UNCOMPLETEDOCREPORTRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.UncompleteDocReportResponse)
  ))
_sym_db.RegisterMessage(UncompleteDocReportResponse)
_sym_db.RegisterMessage(UncompleteDocReportResponse.UncompleteDocReport)

StocktakeProductImportRequest = _reflection.GeneratedProtocolMessageType('StocktakeProductImportRequest', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPRODUCTIMPORTREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeProductImportRequest)
  ))
_sym_db.RegisterMessage(StocktakeProductImportRequest)

ProductImportResponseRows = _reflection.GeneratedProtocolMessageType('ProductImportResponseRows', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTIMPORTRESPONSEROWS,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.ProductImportResponseRows)
  ))
_sym_db.RegisterMessage(ProductImportResponseRows)

StocktakeProductImportResponse = _reflection.GeneratedProtocolMessageType('StocktakeProductImportResponse', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPRODUCTIMPORTRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakeProductImportResponse)
  ))
_sym_db.RegisterMessage(StocktakeProductImportResponse)

UpdateStocktakeImportBatchRequest = _reflection.GeneratedProtocolMessageType('UpdateStocktakeImportBatchRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATESTOCKTAKEIMPORTBATCHREQUEST,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.UpdateStocktakeImportBatchRequest)
  ))
_sym_db.RegisterMessage(UpdateStocktakeImportBatchRequest)

UpdateStocktakeImportBatchResponse = _reflection.GeneratedProtocolMessageType('UpdateStocktakeImportBatchResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATESTOCKTAKEIMPORTBATCHRESPONSE,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.UpdateStocktakeImportBatchResponse)
  ))
_sym_db.RegisterMessage(UpdateStocktakeImportBatchResponse)

StocktakePositionProducts = _reflection.GeneratedProtocolMessageType('StocktakePositionProducts', (_message.Message,), dict(
  DESCRIPTOR = _STOCKTAKEPOSITIONPRODUCTS,
  __module__ = 'stocktake_pb2'
  # @@protoc_insertion_point(class_scope:stocktake.StocktakePositionProducts)
  ))
_sym_db.RegisterMessage(StocktakePositionProducts)



_STOCKTAKE = _descriptor.ServiceDescriptor(
  name='stocktake',
  full_name='stocktake.stocktake',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=23579,
  serialized_end=28058,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetStoreScope',
    full_name='stocktake.stocktake.GetStoreScope',
    index=0,
    containing_service=None,
    input_type=_STOREDATASCOPEREQUEST,
    output_type=_STOREDATASCOPE,
    serialized_options=_b('\202\323\344\223\002\035\"\030/api/v2/store/data/scope:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='Ping',
    full_name='stocktake.stocktake.Ping',
    index=1,
    containing_service=None,
    input_type=google_dot_protobuf_dot_empty__pb2._EMPTY,
    output_type=_PONG,
    serialized_options=_b('\202\323\344\223\002\007\022\005/ping'),
  ),
  _descriptor.MethodDescriptor(
    name='CheckStocktakeByDocID',
    full_name='stocktake.stocktake.CheckStocktakeByDocID',
    index=2,
    containing_service=None,
    input_type=_CHECKSTOCKTAKEBYDOCIDREQUEST,
    output_type=_CHECKSTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0021\032//api/v2/supply/stocktake/{doc_id}/confirm/check'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmStocktakeByDocID',
    full_name='stocktake.stocktake.ConfirmStocktakeByDocID',
    index=3,
    containing_service=None,
    input_type=_CONFIRMSTOCKTAKEBYDOCIDREQUEST,
    output_type=_CONFIRMSTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002+\032)/api/v2/supply/stocktake/{doc_id}/confirm'),
  ),
  _descriptor.MethodDescriptor(
    name='ApproveStocktakeByDocID',
    full_name='stocktake.stocktake.ApproveStocktakeByDocID',
    index=4,
    containing_service=None,
    input_type=_APPROVESTOCKTAKEBYDOCIDREQUEST,
    output_type=_APPROVESTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\032)/api/v2/supply/stocktake/{doc_id}/approve:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='RejectStocktakeProduct',
    full_name='stocktake.stocktake.RejectStocktakeProduct',
    index=5,
    containing_service=None,
    input_type=_REJECTSTOCKTAKEPRODUCTREQUEST,
    output_type=_REJECTSTOCKTAKEPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\002-\032(/api/v2/supply/stocktake/{doc_id}/reject:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CancelStocktakeByDocID',
    full_name='stocktake.stocktake.CancelStocktakeByDocID',
    index=6,
    containing_service=None,
    input_type=_CANCELSTOCKTAKEBYDOCIDREQUEST,
    output_type=_CANCELSTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002*\032(/api/v2/supply/stocktake/{doc_id}/cancel'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktakeByDocID',
    full_name='stocktake.stocktake.GetStocktakeByDocID',
    index=7,
    containing_service=None,
    input_type=_GETSTOCKTAKEBYDOCIDREQUEST,
    output_type=_STOCKTAKE,
    serialized_options=_b('\202\323\344\223\002#\022!/api/v2/supply/stocktake/{doc_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktake',
    full_name='stocktake.stocktake.GetStocktake',
    index=8,
    containing_service=None,
    input_type=_GETSTOCKTAKEREQUEST,
    output_type=_GETSTOCKTAKERESPONSE,
    serialized_options=_b('\202\323\344\223\002\032\022\030/api/v2/supply/stocktake'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktakeProduct',
    full_name='stocktake.stocktake.GetStocktakeProduct',
    index=9,
    containing_service=None,
    input_type=_GETSTOCKTAKEPRODUCTREQUEST,
    output_type=_GETSTOCKTAKEPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\002+\022)/api/v2/supply/stocktake/{doc_id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='PutStocktakeByDocID',
    full_name='stocktake.stocktake.PutStocktakeByDocID',
    index=10,
    containing_service=None,
    input_type=_PUTSTOCKTAKEBYDOCIDREQUEST,
    output_type=_PUTSTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\032!/api/v2/supply/stocktake/{doc_id}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CheckedStocktakeByDocID',
    full_name='stocktake.stocktake.CheckedStocktakeByDocID',
    index=11,
    containing_service=None,
    input_type=_CHECKEDSTOCKTAKEBYDOCIDREQUEST,
    output_type=_CHECKEDSTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0021\032,/api/v2/supply/stocktake/{doc_id}/init/check:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktakeTags',
    full_name='stocktake.stocktake.GetStocktakeTags',
    index=12,
    containing_service=None,
    input_type=_GETSTOCKTAKETAGSREQUEST,
    output_type=_GETSTOCKTAKETAGSRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\022%/api/v2/supply/stocktake/product/tags'),
  ),
  _descriptor.MethodDescriptor(
    name='ActionStocktakeTags',
    full_name='stocktake.stocktake.ActionStocktakeTags',
    index=13,
    containing_service=None,
    input_type=_ACTIONSTOCKTAKETAGSREQUEST,
    output_type=_ACTIONSTOCKTAKETAGSRESPONSE,
    serialized_options=_b('\202\323\344\223\0021\032,/api/v2/supply/stocktake/product/tags/action:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteStocktakeProductTags',
    full_name='stocktake.stocktake.DeleteStocktakeProductTags',
    index=14,
    containing_service=None,
    input_type=_DELETESTOCKTAKEPRODUCTTAGSREQUEST,
    output_type=_DELETESTOCKTAKEPRODUCTTAGSRESPONSE,
    serialized_options=_b('\202\323\344\223\0020\032+/api/v2/supply/stocktake/product/tags/clean:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktakeBalance',
    full_name='stocktake.stocktake.GetStocktakeBalance',
    index=15,
    containing_service=None,
    input_type=_GETSTOCKTAKEBALANCEREQUEST,
    output_type=_GETSTOCKTAKEBALANCERESPONSE,
    serialized_options=_b('\202\323\344\223\002%\022#/api/v2/supply/stocktake/bi/balance'),
  ),
  _descriptor.MethodDescriptor(
    name='SubmitStocktakeByDocID',
    full_name='stocktake.stocktake.SubmitStocktakeByDocID',
    index=16,
    containing_service=None,
    input_type=_SUBMITSTOCKTAKEBYDOCIDREQUEST,
    output_type=_SUBMITSTOCKTAKEBYDOCIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002-\032(/api/v2/supply/stocktake/{doc_id}/submit:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetStocktakeBalanceProductGroup',
    full_name='stocktake.stocktake.GetStocktakeBalanceProductGroup',
    index=17,
    containing_service=None,
    input_type=_GETSTOCKTAKEBALANCEPRODUCTGROUPREQUEST,
    output_type=_GETSTOCKTAKEBALANCEPRODUCTGROUPRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\0227/api/v2/supply/stocktake/balance/{doc_id}/product/group'),
  ),
  _descriptor.MethodDescriptor(
    name='StocktakeBiDetailed',
    full_name='stocktake.stocktake.StocktakeBiDetailed',
    index=18,
    containing_service=None,
    input_type=_STOCKTAKEBIDETAILEDREQUEST,
    output_type=_STOCKTAKEBIDETAILEDRESPONSE,
    serialized_options=_b('\202\323\344\223\002$\022\"/api/v2/supply/stocktake/bi/detail'),
  ),
  _descriptor.MethodDescriptor(
    name='StocktakeBalanceRegion',
    full_name='stocktake.stocktake.StocktakeBalanceRegion',
    index=19,
    containing_service=None,
    input_type=_STOCKTAKEBALANCEREGIONREQUEST,
    output_type=_STOCKTAKEBALANCEREGIONRESPONSE,
    serialized_options=_b('\202\323\344\223\0020\022./api/v2/supply/stocktake/balance/product/group'),
  ),
  _descriptor.MethodDescriptor(
    name='AdvanceStocktakeDiff',
    full_name='stocktake.stocktake.AdvanceStocktakeDiff',
    index=20,
    containing_service=None,
    input_type=_ADVANCESTOCKTAKEDIFFREQUEST,
    output_type=_ADVANCESTOCKTAKEDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\002+\022)/api/v2/supply/stocktake/{doc_id}/advance'),
  ),
  _descriptor.MethodDescriptor(
    name='StocktakeDiffReport',
    full_name='stocktake.stocktake.StocktakeDiffReport',
    index=21,
    containing_service=None,
    input_type=_STOCKTAKEDIFFREPORTREQUEST,
    output_type=_STOCKTAKEDIFFREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\022$/api/v2/supply/stocktake/diff/report'),
  ),
  _descriptor.MethodDescriptor(
    name='GetUncompleteDoc',
    full_name='stocktake.stocktake.GetUncompleteDoc',
    index=22,
    containing_service=None,
    input_type=_GETUNCOMPLETEDOCREQUEST,
    output_type=_GETUNCOMPLETEDOCRESPONSE,
    serialized_options=_b('\202\323\344\223\002\037\022\035/api/v2/supply/uncomplete_doc'),
  ),
  _descriptor.MethodDescriptor(
    name='RecreateStocktakeDoc',
    full_name='stocktake.stocktake.RecreateStocktakeDoc',
    index=23,
    containing_service=None,
    input_type=_RECREATESTOCKTAKEDOCREQUEST,
    output_type=_RECREATESTOCKTAKEDOCRESPONSE,
    serialized_options=_b('\202\323\344\223\002*\"%/api/v2/supply/recreate_stocktake_doc:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='StocktakeDocStatistics',
    full_name='stocktake.stocktake.StocktakeDocStatistics',
    index=24,
    containing_service=None,
    input_type=_STOCKTAKEDOCSTATISTICSREQUEST,
    output_type=_STOCKTAKEDOCSTATISTICSRESPONSE,
    serialized_options=_b('\202\323\344\223\002,\"\'/api/v2/supply/stocktake_doc_statistics:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='StocktakeDiffCollectReport',
    full_name='stocktake.stocktake.StocktakeDiffCollectReport',
    index=25,
    containing_service=None,
    input_type=_STOCKTAKEDIFFCOLLECTREPORTREQUEST,
    output_type=_STOCKTAKEDIFFCOLLECTREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\022,/api/v2/supply/stocktake/diff_collect/report'),
  ),
  _descriptor.MethodDescriptor(
    name='UncompleteDocReport',
    full_name='stocktake.stocktake.UncompleteDocReport',
    index=26,
    containing_service=None,
    input_type=_UNCOMPLETEDOCREPORTREQUEST,
    output_type=_UNCOMPLETEDOCREPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\022$/api/v2/supply/uncomplete_doc_report'),
  ),
  _descriptor.MethodDescriptor(
    name='StocktakeProductImport',
    full_name='stocktake.stocktake.StocktakeProductImport',
    index=27,
    containing_service=None,
    input_type=_STOCKTAKEPRODUCTIMPORTREQUEST,
    output_type=_STOCKTAKEPRODUCTIMPORTRESPONSE,
    serialized_options=_b('\202\323\344\223\002,\"\'/api/v2/supply/stocktake/product/import:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateStocktakeImportBatch',
    full_name='stocktake.stocktake.UpdateStocktakeImportBatch',
    index=28,
    containing_service=None,
    input_type=_UPDATESTOCKTAKEIMPORTBATCHREQUEST,
    output_type=_UPDATESTOCKTAKEIMPORTBATCHRESPONSE,
    serialized_options=_b('\202\323\344\223\0021\032,/api/v2/supply/update/stocktake/import/batch:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_STOCKTAKE)

DESCRIPTOR.services_by_name['stocktake'] = _STOCKTAKE

# @@protoc_insertion_point(module_scope)
