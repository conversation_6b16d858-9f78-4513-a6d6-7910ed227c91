# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import receiving_bi_pb2 as receiving__bi__pb2


class ReceivingBiServiceStub(object):
  """收货报表相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetReceivingCollect = channel.unary_unary(
        '/receiving.ReceivingBiService/GetReceivingCollect',
        request_serializer=receiving__bi__pb2.GetReceivingCollectRequest.SerializeToString,
        response_deserializer=receiving__bi__pb2.GetReceivingCollectResponse.FromString,
        )
    self.GetReceivingDetail = channel.unary_unary(
        '/receiving.ReceivingBiService/GetReceivingDetail',
        request_serializer=receiving__bi__pb2.GetReceivingDetailRequest.SerializeToString,
        response_deserializer=receiving__bi__pb2.GetReceivingDetailResponse.FromString,
        )


class ReceivingBiServiceServicer(object):
  """收货报表相关服务
  """

  def GetReceivingCollect(self, request, context):
    """查询收货单汇总报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceivingDetail(self, request, context):
    """查询收货单详情报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_ReceivingBiServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetReceivingCollect': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceivingCollect,
          request_deserializer=receiving__bi__pb2.GetReceivingCollectRequest.FromString,
          response_serializer=receiving__bi__pb2.GetReceivingCollectResponse.SerializeToString,
      ),
      'GetReceivingDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceivingDetail,
          request_deserializer=receiving__bi__pb2.GetReceivingDetailRequest.FromString,
          response_serializer=receiving__bi__pb2.GetReceivingDetailResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'receiving.ReceivingBiService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
