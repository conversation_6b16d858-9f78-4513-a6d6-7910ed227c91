syntax = "proto3";

package cost;


import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// 现在进销存报表也有按照成本中心维度, 对比产品设计的进销存报表 & 成本报表, 其实就是依赖成本计算然后分单据类型进行统计, 两个报表并没有太大差异,
// 所以计算时不区分报表类型, 统一按照需求计算出各维度(成本中心/门店/仓库/加工中心)所有统计项, 前端展示不同报表按需过滤字段即可.
//
// 计算任务逻辑为:
//   1. 拉取账期内各种单据, 汇总需要的字段(数量、金额、门店等), 做成一张大的单据流水表(即countType & reportType全部为0), 方便后续的计算
//   2. 按照成本中心为维度计算报表字段, 主要是计算商品成本, 是后续其他维度(门店/仓库/加工中心)的前置依赖. 此处需要注意, 由于有些字段需要计算出前置
//      的某些原料才能算出成本, 此处将其按照产成顺序以及产成相关单据区分(reportType), 一步一步计算出各类商品的成本. 体现到计算任务上, 就是每个
//      计算维度(countType)又分为几个reportType(原料/加工/包装/物料转换/成品)
//   3. 成本中心维度的计算完成后, 就有了其他维度需要的成本单价, 其他维度按照同样的逻辑循环计算即可

// 定义 reports 服务
service Reports {
  // 插入计算任务
  rpc CreateTask (CreateTaskRequest) returns (CommonResponse) {
    option (google.api.http) = {
      post:"/api/v1/cost-engine/tasks"
    };
  }

  // 查询计算运行结果请求
  rpc QueryTask (QueryTaskRequest) returns (QueryTaskResponse) {
    option (google.api.http) = {
      get:"/api/v1/cost-engine/tasks/{id}"
    };
  }

  // 查询计算运行结果请求
  rpc HoloSendMysqlSuccess (HoloSendMysqlSuccessRequest) returns (HoloSendMysqlSuccessResponse) {
    option (google.api.http) = {
      post:"/api/v1/cost-engine/holo-send-mysql/success"
    };
  }

  // 查询报表
  rpc QueryCostReports (QueryReportsRequest) returns (QueryReportsResponse) {
    option (google.api.http) = {
      post:"/api/v1/cost-engine/reports"
    };
  }
}

// 创建任务request
// 计算任务需要分成 CountType * ReportType个, 每个任务之间需要间隔15分钟左右等待同步到阿里云数据湖
// 计算任务创建需要有严格的顺序, 必须先计算单据流水(即countType = 0), 然后成本中心(即branchType = 0)维度, 再按照ReportType枚举的顺序一个一个来
//  一个租户每个账期第一个计算任务即countType从0开始, 然后按照reportType的枚举顺序每个都是一个单独的task, 即完整计算一个租户的成本需要的task总数为costCenterId * countType * reportType
// e.g.
// {
//   "taskId": 1111111,
//   "preTaskId": 1111110,
//   "periodId": 1,
//   "partnerId": 100,
//   "costCenterId": 2,
//   "operator": 3,
//   "countType": 0,
//   "reportType": 1,
//   "taskType": 1,
//   "startTime": {
//     "seconds": 20,
//     "nanos": 10
//   },
//   "endTime": {
//     "seconds": 20,
//     "nanos": 10
//   }
// }
message CreateTaskRequest {
  uint64 taskId = 1; // 任务id, 需要全局唯一
  uint64 prePeriodId = 2; // 上个账期的id
  uint64 periodId = 3; // 账期id
  uint64 partnerId = 4; // 商户id
  uint64 costCenterId = 5; // 成本中心id

  uint64 operator = 6; // 操作人id

  CountType  countType = 8; // 计算维度类型: 0: 计算单据流水数据 1: 成本中心 2: 门店/仓库/加工中心等
  ReportType reportType = 9; // 计算单据类型 0: 全部, 只适用于branchType = 0计算流水时可用 1: 原料 2: 加工 3: 包装 4: 物料转换 5: 成品
  TaskType   taskType = 10; // 任务种类：1:账期任务(一段时间)

  google.protobuf.Timestamp startTime = 11; //期初时间
  google.protobuf.Timestamp endTime = 12; //期末时间

  repeated CreateTasks CreateTasks = 13;
}

message    CreateTasks {
  uint64 taskId = 1; // 任务id, 需要全局唯一
  uint64 prePeriodId = 2; // 上个账期的id
  uint64 periodId = 3; // 账期id
  uint64 partnerId = 4; // 商户id
  uint64 costCenterId = 5; // 成本中心id

  uint64 operator = 6; // 操作人id

  CountType  countType = 8; // 计算维度类型: 0: 计算单据流水数据 1: 成本中心 2: 门店/仓库/加工中心等
  ReportType reportType = 9; // 计算单据类型 0: 全部, 只适用于branchType = 0计算流水时可用 1: 原料 2: 加工 3: 包装 4: 物料转换 5: 成品
  TaskType   taskType = 10; // 任务种类：1:账期任务(一段时间)

  google.protobuf.Timestamp startTime = 11; //期初时间
  google.protobuf.Timestamp endTime = 12; //期末时间
}


//holo同步mysql成功
message HoloSendMysqlSuccessRequest {
  google.protobuf.Timestamp busDate = 1; //营业时间
}


message    HoloSendMysqlSuccessResponse{
  CommonStatus status = 1;  // 响应状态
}

//查询计算运行结果请求
message QueryTaskRequest {
  uint64 taskId = 1; // 需要检查的计算任务id, 可选
  uint64 periodId = 2; // 账期id
  uint64 partnerId = 3; // 商户id
  uint64 costCenterId = 4; // 成本中心id

  google.protobuf.Timestamp startTime = 5; //期初时间
  google.protobuf.Timestamp endTime = 6; //期末时间
}

// 任务信息查询结果
message QueryTaskResponse {
  CommonStatus status = 1; // 响应状态

  repeated TaskInfo list = 2; // 任务列表
}

// 成本报表查询
message QueryReportsRequest {
  uint64 taskId = 1; // 任务id - 和periodId只能二选一
  uint64 periodId = 2; // 账期id - 和taskId只能二选一
  uint64 partnerId = 3; // 租户id
  uint64 costCenterId = 4; // 成本中心id

  repeated uint64 branchIds = 5; // branchType类别对应id 门店id或成本中心id
  repeated uint64 prdIds = 6; // 需要统计的商品id

  CountType  countType = 7; // 计算维度类型: 0: 计算单据流水数据 1: 成本中心 2: 门店/仓库/加工中心等
  BranchType branchType = 8; // 报表维度类型: 0 成本中心 1 门店 2 仓库 3 加工中心 4 门店汇总

  // 过滤条件
  // map['data_not_eq'] = 'zero' -> 查询不为0的数据
  // map['err_code_eq'] = '1'    -> key = err_code_eq 查询异常数据; value = 1 没有找到单据,且没期初数据
  map<string, string> filters = 9;

  google.protobuf.Timestamp startTime = 10; //期初时间
  google.protobuf.Timestamp endTime = 11; //期末时间

  Pagination page = 12; // 分页
}

//  GetBalanceReportsReply响应结构
message QueryReportsResponse {
  repeated ReportData data = 1; // 报表数据

  Pagination page = 2; // 分页
}

//成本数据
message ReportData {
  uint64 id = 1; // id
  uint64 countTaskId = 2; // 任务id
  uint64 partnerId = 3; // 租户id
  uint64 periodId = 4; // 账期id
  uint64 costCenterId = 5; // 成本中心id
  uint64 branchId = 6; // 维度id, 成本中心id/门店id/仓库id/加工中心id等
  uint64 productId = 7; // 商品id

  double startQty = 8; // 期初数量
  double startCost = 9; // 期初成本单价
  double startAmount = 10; // 期初成本金额
  double selfIn = 11; // 自采入库数量
  double selfAmount = 12; // 自采入库金额
  double qtyIn = 13; // 采购/直送入库数量
  double amountIn = 14; // 采购/直送入库金额
  double qtyOut = 15; // 采购/直送退货数量
  double amountOut = 16; // 采购/直送退货金额
  double amountBalance = 17; // 调整金额
  double qtyBalance = 18; // 调整数量
  double qtyBalanceIn = 19; // 调整收货数量
  double qtyBalanceOut = 20; // 调整退货数量
  double deliveryOut = 21; // 配送出库数量
  double deliveryOutCost = 22; // 配送出库成本
  double deliveryIn = 23; // 配送入库数量
  double deliveryInCost = 24; // 配送入库成本
  double allotOut = 25; // 调拨出数量
  double allotOutCost = 26; // 调拨出成本
  double allotIn = 27; // 调拨入数量
  double allotInCost = 28; // 调拨入成本
  double scrapQty = 29; // 报废数量
  double scrapCost = 30; // 报废成本
  double saleQty = 31; // 销售数量
  double saleCost = 32; // 销售成本
  double scrapRatio = 33; // 废销比
  double inventoryDiff = 34; // 盘点损益
  double inventoryCost = 35; // 盘点损益成本
  double endQty = 36; // 期末数量
  double endCost = 37; // 当期成本单价
  double endAmount = 38; // 期末成本
  double brokerQty = 39; // 在途数量
  double brokerCost = 40; // 在途商品成本
  double workQty = 41; // 本期产成数量
  double makePrice = 42; // 消耗的原料总价格
  double workPrice = 43; // 加工费总额

  google.protobuf.Timestamp startTime = 44; // 统计的开始时间
  google.protobuf.Timestamp endTime = 45; // 统计的结束时间

  uint32 countType = 46; // 计算维度类型: 1 成本中心 2 门店/仓库/加工中心..., 暂时不做区分
  uint32 branchType = 47; // 报表维度类型: 0 成本中心 1 门店 2 仓库 3 加工中心
  uint32 taskType = 48; // 1:账期任务(一段时间)
  uint32 errorCode = 49; // 1：没有找到单据,且没期初数据

  google.protobuf.Timestamp createdTime = 50; // 创建时间
  google.protobuf.Timestamp updatedTime = 51; // 更新时间
}

message TaskInfo {
  uint64 id = 1;  // 计算任务id
  uint64 taskId = 2;  // 任务id
  uint64 partnerId = 3;  // 租户id
  uint64 periodId = 4;  // 账期id
  uint64 costCenterId = 5;  // 成本中心id

  google.protobuf.Timestamp startTime = 6; // 账期开始时间
  google.protobuf.Timestamp endTime = 7; // 账期结束时间

  TaskStatus taskStatus = 8;  // 任务状态
  string     reason = 9;  // 任务失败时的原因
  TaskType   taskType = 10; // 任务类型
  CountType  countType = 11; // 计算类型
  ReportType reportType = 12; // 报表类型
  uint32     retry = 13; // 重试次数

  google.protobuf.Timestamp lastRunTime = 14; // 最后计算时间
}

// 公共空响应结构
message CommonResponse {
  CommonStatus status = 1;  // 响应状态
}

//分页
message Pagination {
  int32  offset = 1;
  int32  limit = 2;
  string order = 3;
  string sort = 4;
  int32  total = 5; // 总条数, 请求时不需要
}

// 公共状态
message CommonStatus {
  int32  code = 1; // 状态码
  string message = 2; // msg
  string detail = 3; // 状态详情
}

// 任务状态
enum TaskStatus {
  TaskInit = 0; // 初始值, 无意义
  TaskWait = 1; // 待执行
  TaskProcess = 2; // 计算中
  TaskSuccess = 3; // 计算成功
  TaskFail = 4; // 计算失败
}

// 任务计算类型
enum TaskType {
  TaskTypeInit = 0; // 初始值, 无意义
  TaskTypePeriod = 1; // 账期, 时间区间
}

// 任务计算单据类型
enum ReportType {
  ReportTypeInit = 0; // 全部, 只在countType = 1时有意义
  Material = 1; // 原料
  Process = 2; // 加工
  Package = 3; // 包装
  Convert = 4; // 物料转换
  Product = 5; // 成品
}

// 计算维度类型
enum CountType {
  BranchTypeInit = 0; // 计算流水
  CostCenter = 1; // 成本中心
  Branch = 2; // 门店/仓库/加工中心..., 暂时不做区分
  Collect = 3; // 金额汇总
}

// 报表维度类型
enum BranchType {
  COST_CENTER = 0; // 成本中心
  STORE = 1; // 门店
  WAREHOUSE = 2; // 仓库
  MANUFACTORY = 3; // 加工中心
}