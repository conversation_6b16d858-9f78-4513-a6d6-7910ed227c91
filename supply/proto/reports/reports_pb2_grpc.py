# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from reports import reports_pb2 as reports_dot_reports__pb2


class ReportsStub(object):
  """现在进销存报表也有按照成本中心维度, 对比产品设计的进销存报表 & 成本报表, 其实就是依赖成本计算然后分单据类型进行统计, 两个报表并没有太大差异,
  所以计算时不区分报表类型, 统一按照需求计算出各维度(成本中心/门店/仓库/加工中心)所有统计项, 前端展示不同报表按需过滤字段即可.

  计算任务逻辑为:
  1. 拉取账期内各种单据, 汇总需要的字段(数量、金额、门店等), 做成一张大的单据流水表(即countType & reportType全部为0), 方便后续的计算
  2. 按照成本中心为维度计算报表字段, 主要是计算商品成本, 是后续其他维度(门店/仓库/加工中心)的前置依赖. 此处需要注意, 由于有些字段需要计算出前置
  的某些原料才能算出成本, 此处将其按照产成顺序以及产成相关单据区分(reportType), 一步一步计算出各类商品的成本. 体现到计算任务上, 就是每个
  计算维度(countType)又分为几个reportType(原料/加工/包装/物料转换/成品)
  3. 成本中心维度的计算完成后, 就有了其他维度需要的成本单价, 其他维度按照同样的逻辑循环计算即可

  定义 reports 服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateTask = channel.unary_unary(
        '/cost.Reports/CreateTask',
        request_serializer=reports_dot_reports__pb2.CreateTaskRequest.SerializeToString,
        response_deserializer=reports_dot_reports__pb2.CommonResponse.FromString,
        )
    self.QueryTask = channel.unary_unary(
        '/cost.Reports/QueryTask',
        request_serializer=reports_dot_reports__pb2.QueryTaskRequest.SerializeToString,
        response_deserializer=reports_dot_reports__pb2.QueryTaskResponse.FromString,
        )
    self.HoloSendMysqlSuccess = channel.unary_unary(
        '/cost.Reports/HoloSendMysqlSuccess',
        request_serializer=reports_dot_reports__pb2.HoloSendMysqlSuccessRequest.SerializeToString,
        response_deserializer=reports_dot_reports__pb2.HoloSendMysqlSuccessResponse.FromString,
        )
    self.QueryCostReports = channel.unary_unary(
        '/cost.Reports/QueryCostReports',
        request_serializer=reports_dot_reports__pb2.QueryReportsRequest.SerializeToString,
        response_deserializer=reports_dot_reports__pb2.QueryReportsResponse.FromString,
        )


class ReportsServicer(object):
  """现在进销存报表也有按照成本中心维度, 对比产品设计的进销存报表 & 成本报表, 其实就是依赖成本计算然后分单据类型进行统计, 两个报表并没有太大差异,
  所以计算时不区分报表类型, 统一按照需求计算出各维度(成本中心/门店/仓库/加工中心)所有统计项, 前端展示不同报表按需过滤字段即可.

  计算任务逻辑为:
  1. 拉取账期内各种单据, 汇总需要的字段(数量、金额、门店等), 做成一张大的单据流水表(即countType & reportType全部为0), 方便后续的计算
  2. 按照成本中心为维度计算报表字段, 主要是计算商品成本, 是后续其他维度(门店/仓库/加工中心)的前置依赖. 此处需要注意, 由于有些字段需要计算出前置
  的某些原料才能算出成本, 此处将其按照产成顺序以及产成相关单据区分(reportType), 一步一步计算出各类商品的成本. 体现到计算任务上, 就是每个
  计算维度(countType)又分为几个reportType(原料/加工/包装/物料转换/成品)
  3. 成本中心维度的计算完成后, 就有了其他维度需要的成本单价, 其他维度按照同样的逻辑循环计算即可

  定义 reports 服务
  """

  def CreateTask(self, request, context):
    """插入计算任务
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryTask(self, request, context):
    """查询计算运行结果请求
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def HoloSendMysqlSuccess(self, request, context):
    """查询计算运行结果请求
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryCostReports(self, request, context):
    """查询报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_ReportsServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateTask': grpc.unary_unary_rpc_method_handler(
          servicer.CreateTask,
          request_deserializer=reports_dot_reports__pb2.CreateTaskRequest.FromString,
          response_serializer=reports_dot_reports__pb2.CommonResponse.SerializeToString,
      ),
      'QueryTask': grpc.unary_unary_rpc_method_handler(
          servicer.QueryTask,
          request_deserializer=reports_dot_reports__pb2.QueryTaskRequest.FromString,
          response_serializer=reports_dot_reports__pb2.QueryTaskResponse.SerializeToString,
      ),
      'HoloSendMysqlSuccess': grpc.unary_unary_rpc_method_handler(
          servicer.HoloSendMysqlSuccess,
          request_deserializer=reports_dot_reports__pb2.HoloSendMysqlSuccessRequest.FromString,
          response_serializer=reports_dot_reports__pb2.HoloSendMysqlSuccessResponse.SerializeToString,
      ),
      'QueryCostReports': grpc.unary_unary_rpc_method_handler(
          servicer.QueryCostReports,
          request_deserializer=reports_dot_reports__pb2.QueryReportsRequest.FromString,
          response_serializer=reports_dot_reports__pb2.QueryReportsResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'cost.Reports', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
