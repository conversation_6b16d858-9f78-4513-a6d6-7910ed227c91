# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: reports/reports.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='reports/reports.proto',
  package='cost',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x15reports/reports.proto\x12\x04\x63ost\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xf5\x02\n\x11\x43reateTaskRequest\x12\x0e\n\x06taskId\x18\x01 \x01(\x04\x12\x13\n\x0bprePeriodId\x18\x02 \x01(\x04\x12\x10\n\x08periodId\x18\x03 \x01(\x04\x12\x11\n\tpartnerId\x18\x04 \x01(\x04\x12\x14\n\x0c\x63ostCenterId\x18\x05 \x01(\x04\x12\x10\n\x08operator\x18\x06 \x01(\x04\x12\"\n\tcountType\x18\x08 \x01(\x0e\x32\x0f.cost.CountType\x12$\n\nreportType\x18\t \x01(\x0e\x32\x10.cost.ReportType\x12 \n\x08taskType\x18\n \x01(\x0e\x32\x0e.cost.TaskType\x12-\n\tstartTime\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12+\n\x07\x65ndTime\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12&\n\x0b\x43reateTasks\x18\r \x03(\x0b\x32\x11.cost.CreateTasks\"\xc7\x02\n\x0b\x43reateTasks\x12\x0e\n\x06taskId\x18\x01 \x01(\x04\x12\x13\n\x0bprePeriodId\x18\x02 \x01(\x04\x12\x10\n\x08periodId\x18\x03 \x01(\x04\x12\x11\n\tpartnerId\x18\x04 \x01(\x04\x12\x14\n\x0c\x63ostCenterId\x18\x05 \x01(\x04\x12\x10\n\x08operator\x18\x06 \x01(\x04\x12\"\n\tcountType\x18\x08 \x01(\x0e\x32\x0f.cost.CountType\x12$\n\nreportType\x18\t \x01(\x0e\x32\x10.cost.ReportType\x12 \n\x08taskType\x18\n \x01(\x0e\x32\x0e.cost.TaskType\x12-\n\tstartTime\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12+\n\x07\x65ndTime\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"J\n\x1bHoloSendMysqlSuccessRequest\x12+\n\x07\x62usDate\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"B\n\x1cHoloSendMysqlSuccessResponse\x12\"\n\x06status\x18\x01 \x01(\x0b\x32\x12.cost.CommonStatus\"\xb9\x01\n\x10QueryTaskRequest\x12\x0e\n\x06taskId\x18\x01 \x01(\x04\x12\x10\n\x08periodId\x18\x02 \x01(\x04\x12\x11\n\tpartnerId\x18\x03 \x01(\x04\x12\x14\n\x0c\x63ostCenterId\x18\x04 \x01(\x04\x12-\n\tstartTime\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12+\n\x07\x65ndTime\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"U\n\x11QueryTaskResponse\x12\"\n\x06status\x18\x01 \x01(\x0b\x32\x12.cost.CommonStatus\x12\x1c\n\x04list\x18\x02 \x03(\x0b\x32\x0e.cost.TaskInfo\"\xb2\x03\n\x13QueryReportsRequest\x12\x0e\n\x06taskId\x18\x01 \x01(\x04\x12\x10\n\x08periodId\x18\x02 \x01(\x04\x12\x11\n\tpartnerId\x18\x03 \x01(\x04\x12\x14\n\x0c\x63ostCenterId\x18\x04 \x01(\x04\x12\x11\n\tbranchIds\x18\x05 \x03(\x04\x12\x0e\n\x06prdIds\x18\x06 \x03(\x04\x12\"\n\tcountType\x18\x07 \x01(\x0e\x32\x0f.cost.CountType\x12$\n\nbranchType\x18\x08 \x01(\x0e\x32\x10.cost.BranchType\x12\x37\n\x07\x66ilters\x18\t \x03(\x0b\x32&.cost.QueryReportsRequest.FiltersEntry\x12-\n\tstartTime\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12+\n\x07\x65ndTime\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1e\n\x04page\x18\x0c \x01(\x0b\x32\x10.cost.Pagination\x1a.\n\x0c\x46iltersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"V\n\x14QueryReportsResponse\x12\x1e\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x10.cost.ReportData\x12\x1e\n\x04page\x18\x02 \x01(\x0b\x32\x10.cost.Pagination\"\xd5\x08\n\nReportData\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x13\n\x0b\x63ountTaskId\x18\x02 \x01(\x04\x12\x11\n\tpartnerId\x18\x03 \x01(\x04\x12\x10\n\x08periodId\x18\x04 \x01(\x04\x12\x14\n\x0c\x63ostCenterId\x18\x05 \x01(\x04\x12\x10\n\x08\x62ranchId\x18\x06 \x01(\x04\x12\x11\n\tproductId\x18\x07 \x01(\x04\x12\x10\n\x08startQty\x18\x08 \x01(\x01\x12\x11\n\tstartCost\x18\t \x01(\x01\x12\x13\n\x0bstartAmount\x18\n \x01(\x01\x12\x0e\n\x06selfIn\x18\x0b \x01(\x01\x12\x12\n\nselfAmount\x18\x0c \x01(\x01\x12\r\n\x05qtyIn\x18\r \x01(\x01\x12\x10\n\x08\x61mountIn\x18\x0e \x01(\x01\x12\x0e\n\x06qtyOut\x18\x0f \x01(\x01\x12\x11\n\tamountOut\x18\x10 \x01(\x01\x12\x15\n\ramountBalance\x18\x11 \x01(\x01\x12\x12\n\nqtyBalance\x18\x12 \x01(\x01\x12\x14\n\x0cqtyBalanceIn\x18\x13 \x01(\x01\x12\x15\n\rqtyBalanceOut\x18\x14 \x01(\x01\x12\x13\n\x0b\x64\x65liveryOut\x18\x15 \x01(\x01\x12\x17\n\x0f\x64\x65liveryOutCost\x18\x16 \x01(\x01\x12\x12\n\ndeliveryIn\x18\x17 \x01(\x01\x12\x16\n\x0e\x64\x65liveryInCost\x18\x18 \x01(\x01\x12\x10\n\x08\x61llotOut\x18\x19 \x01(\x01\x12\x14\n\x0c\x61llotOutCost\x18\x1a \x01(\x01\x12\x0f\n\x07\x61llotIn\x18\x1b \x01(\x01\x12\x13\n\x0b\x61llotInCost\x18\x1c \x01(\x01\x12\x10\n\x08scrapQty\x18\x1d \x01(\x01\x12\x11\n\tscrapCost\x18\x1e \x01(\x01\x12\x0f\n\x07saleQty\x18\x1f \x01(\x01\x12\x10\n\x08saleCost\x18  \x01(\x01\x12\x12\n\nscrapRatio\x18! \x01(\x01\x12\x15\n\rinventoryDiff\x18\" \x01(\x01\x12\x15\n\rinventoryCost\x18# \x01(\x01\x12\x0e\n\x06\x65ndQty\x18$ \x01(\x01\x12\x0f\n\x07\x65ndCost\x18% \x01(\x01\x12\x11\n\tendAmount\x18& \x01(\x01\x12\x11\n\tbrokerQty\x18\' \x01(\x01\x12\x12\n\nbrokerCost\x18( \x01(\x01\x12\x0f\n\x07workQty\x18) \x01(\x01\x12\x11\n\tmakePrice\x18* \x01(\x01\x12\x11\n\tworkPrice\x18+ \x01(\x01\x12-\n\tstartTime\x18, \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12+\n\x07\x65ndTime\x18- \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tcountType\x18. \x01(\r\x12\x12\n\nbranchType\x18/ \x01(\r\x12\x10\n\x08taskType\x18\x30 \x01(\r\x12\x11\n\terrorCode\x18\x31 \x01(\r\x12/\n\x0b\x63reatedTime\x18\x32 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12/\n\x0bupdatedTime\x18\x33 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\x9f\x03\n\x08TaskInfo\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06taskId\x18\x02 \x01(\x04\x12\x11\n\tpartnerId\x18\x03 \x01(\x04\x12\x10\n\x08periodId\x18\x04 \x01(\x04\x12\x14\n\x0c\x63ostCenterId\x18\x05 \x01(\x04\x12-\n\tstartTime\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12+\n\x07\x65ndTime\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12$\n\ntaskStatus\x18\x08 \x01(\x0e\x32\x10.cost.TaskStatus\x12\x0e\n\x06reason\x18\t \x01(\t\x12 \n\x08taskType\x18\n \x01(\x0e\x32\x0e.cost.TaskType\x12\"\n\tcountType\x18\x0b \x01(\x0e\x32\x0f.cost.CountType\x12$\n\nreportType\x18\x0c \x01(\x0e\x32\x10.cost.ReportType\x12\r\n\x05retry\x18\r \x01(\r\x12/\n\x0blastRunTime\x18\x0e \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"4\n\x0e\x43ommonResponse\x12\"\n\x06status\x18\x01 \x01(\x0b\x32\x12.cost.CommonStatus\"W\n\nPagination\x12\x0e\n\x06offset\x18\x01 \x01(\x05\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\r\n\x05order\x18\x03 \x01(\t\x12\x0c\n\x04sort\x18\x04 \x01(\t\x12\r\n\x05total\x18\x05 \x01(\x05\"=\n\x0c\x43ommonStatus\x12\x0c\n\x04\x63ode\x18\x01 \x01(\x05\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0e\n\x06\x64\x65tail\x18\x03 \x01(\t*X\n\nTaskStatus\x12\x0c\n\x08TaskInit\x10\x00\x12\x0c\n\x08TaskWait\x10\x01\x12\x0f\n\x0bTaskProcess\x10\x02\x12\x0f\n\x0bTaskSuccess\x10\x03\x12\x0c\n\x08TaskFail\x10\x04*0\n\x08TaskType\x12\x10\n\x0cTaskTypeInit\x10\x00\x12\x12\n\x0eTaskTypePeriod\x10\x01*b\n\nReportType\x12\x12\n\x0eReportTypeInit\x10\x00\x12\x0c\n\x08Material\x10\x01\x12\x0b\n\x07Process\x10\x02\x12\x0b\n\x07Package\x10\x03\x12\x0b\n\x07\x43onvert\x10\x04\x12\x0b\n\x07Product\x10\x05*H\n\tCountType\x12\x12\n\x0e\x42ranchTypeInit\x10\x00\x12\x0e\n\nCostCenter\x10\x01\x12\n\n\x06\x42ranch\x10\x02\x12\x0b\n\x07\x43ollect\x10\x03*H\n\nBranchType\x12\x0f\n\x0b\x43OST_CENTER\x10\x00\x12\t\n\x05STORE\x10\x01\x12\r\n\tWAREHOUSE\x10\x02\x12\x0f\n\x0bMANUFACTORY\x10\x03\x32\xd4\x03\n\x07Reports\x12^\n\nCreateTask\x12\x17.cost.CreateTaskRequest\x1a\x14.cost.CommonResponse\"!\x82\xd3\xe4\x93\x02\x1b\"\x19/api/v1/cost-engine/tasks\x12\x64\n\tQueryTask\x12\x16.cost.QueryTaskRequest\x1a\x17.cost.QueryTaskResponse\"&\x82\xd3\xe4\x93\x02 \x12\x1e/api/v1/cost-engine/tasks/{id}\x12\x92\x01\n\x14HoloSendMysqlSuccess\x12!.cost.HoloSendMysqlSuccessRequest\x1a\".cost.HoloSendMysqlSuccessResponse\"3\x82\xd3\xe4\x93\x02-\"+/api/v1/cost-engine/holo-send-mysql/success\x12n\n\x10QueryCostReports\x12\x19.cost.QueryReportsRequest\x1a\x1a.cost.QueryReportsResponse\"#\x82\xd3\xe4\x93\x02\x1d\"\x1b/api/v1/cost-engine/reportsb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])

_TASKSTATUS = _descriptor.EnumDescriptor(
  name='TaskStatus',
  full_name='cost.TaskStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TaskInit', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TaskWait', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TaskProcess', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TaskSuccess', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TaskFail', index=4, number=4,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3480,
  serialized_end=3568,
)
_sym_db.RegisterEnumDescriptor(_TASKSTATUS)

TaskStatus = enum_type_wrapper.EnumTypeWrapper(_TASKSTATUS)
_TASKTYPE = _descriptor.EnumDescriptor(
  name='TaskType',
  full_name='cost.TaskType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TaskTypeInit', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TaskTypePeriod', index=1, number=1,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3570,
  serialized_end=3618,
)
_sym_db.RegisterEnumDescriptor(_TASKTYPE)

TaskType = enum_type_wrapper.EnumTypeWrapper(_TASKTYPE)
_REPORTTYPE = _descriptor.EnumDescriptor(
  name='ReportType',
  full_name='cost.ReportType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ReportTypeInit', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Material', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Process', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Package', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Convert', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Product', index=5, number=5,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3620,
  serialized_end=3718,
)
_sym_db.RegisterEnumDescriptor(_REPORTTYPE)

ReportType = enum_type_wrapper.EnumTypeWrapper(_REPORTTYPE)
_COUNTTYPE = _descriptor.EnumDescriptor(
  name='CountType',
  full_name='cost.CountType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='BranchTypeInit', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CostCenter', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Branch', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='Collect', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3720,
  serialized_end=3792,
)
_sym_db.RegisterEnumDescriptor(_COUNTTYPE)

CountType = enum_type_wrapper.EnumTypeWrapper(_COUNTTYPE)
_BRANCHTYPE = _descriptor.EnumDescriptor(
  name='BranchType',
  full_name='cost.BranchType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='COST_CENTER', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='STORE', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='WAREHOUSE', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MANUFACTORY', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3794,
  serialized_end=3866,
)
_sym_db.RegisterEnumDescriptor(_BRANCHTYPE)

BranchType = enum_type_wrapper.EnumTypeWrapper(_BRANCHTYPE)
TaskInit = 0
TaskWait = 1
TaskProcess = 2
TaskSuccess = 3
TaskFail = 4
TaskTypeInit = 0
TaskTypePeriod = 1
ReportTypeInit = 0
Material = 1
Process = 2
Package = 3
Convert = 4
Product = 5
BranchTypeInit = 0
CostCenter = 1
Branch = 2
Collect = 3
COST_CENTER = 0
STORE = 1
WAREHOUSE = 2
MANUFACTORY = 3



_CREATETASKREQUEST = _descriptor.Descriptor(
  name='CreateTaskRequest',
  full_name='cost.CreateTaskRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='taskId', full_name='cost.CreateTaskRequest.taskId', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='prePeriodId', full_name='cost.CreateTaskRequest.prePeriodId', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='periodId', full_name='cost.CreateTaskRequest.periodId', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partnerId', full_name='cost.CreateTaskRequest.partnerId', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='costCenterId', full_name='cost.CreateTaskRequest.costCenterId', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='operator', full_name='cost.CreateTaskRequest.operator', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='countType', full_name='cost.CreateTaskRequest.countType', index=6,
      number=8, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reportType', full_name='cost.CreateTaskRequest.reportType', index=7,
      number=9, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='taskType', full_name='cost.CreateTaskRequest.taskType', index=8,
      number=10, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='startTime', full_name='cost.CreateTaskRequest.startTime', index=9,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='endTime', full_name='cost.CreateTaskRequest.endTime', index=10,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='CreateTasks', full_name='cost.CreateTaskRequest.CreateTasks', index=11,
      number=13, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=95,
  serialized_end=468,
)


_CREATETASKS = _descriptor.Descriptor(
  name='CreateTasks',
  full_name='cost.CreateTasks',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='taskId', full_name='cost.CreateTasks.taskId', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='prePeriodId', full_name='cost.CreateTasks.prePeriodId', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='periodId', full_name='cost.CreateTasks.periodId', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partnerId', full_name='cost.CreateTasks.partnerId', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='costCenterId', full_name='cost.CreateTasks.costCenterId', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='operator', full_name='cost.CreateTasks.operator', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='countType', full_name='cost.CreateTasks.countType', index=6,
      number=8, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reportType', full_name='cost.CreateTasks.reportType', index=7,
      number=9, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='taskType', full_name='cost.CreateTasks.taskType', index=8,
      number=10, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='startTime', full_name='cost.CreateTasks.startTime', index=9,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='endTime', full_name='cost.CreateTasks.endTime', index=10,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=471,
  serialized_end=798,
)


_HOLOSENDMYSQLSUCCESSREQUEST = _descriptor.Descriptor(
  name='HoloSendMysqlSuccessRequest',
  full_name='cost.HoloSendMysqlSuccessRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='busDate', full_name='cost.HoloSendMysqlSuccessRequest.busDate', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=800,
  serialized_end=874,
)


_HOLOSENDMYSQLSUCCESSRESPONSE = _descriptor.Descriptor(
  name='HoloSendMysqlSuccessResponse',
  full_name='cost.HoloSendMysqlSuccessResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='cost.HoloSendMysqlSuccessResponse.status', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=876,
  serialized_end=942,
)


_QUERYTASKREQUEST = _descriptor.Descriptor(
  name='QueryTaskRequest',
  full_name='cost.QueryTaskRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='taskId', full_name='cost.QueryTaskRequest.taskId', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='periodId', full_name='cost.QueryTaskRequest.periodId', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partnerId', full_name='cost.QueryTaskRequest.partnerId', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='costCenterId', full_name='cost.QueryTaskRequest.costCenterId', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='startTime', full_name='cost.QueryTaskRequest.startTime', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='endTime', full_name='cost.QueryTaskRequest.endTime', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=945,
  serialized_end=1130,
)


_QUERYTASKRESPONSE = _descriptor.Descriptor(
  name='QueryTaskResponse',
  full_name='cost.QueryTaskResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='cost.QueryTaskResponse.status', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='list', full_name='cost.QueryTaskResponse.list', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1132,
  serialized_end=1217,
)


_QUERYREPORTSREQUEST_FILTERSENTRY = _descriptor.Descriptor(
  name='FiltersEntry',
  full_name='cost.QueryReportsRequest.FiltersEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='cost.QueryReportsRequest.FiltersEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='cost.QueryReportsRequest.FiltersEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=_b('8\001'),
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1608,
  serialized_end=1654,
)

_QUERYREPORTSREQUEST = _descriptor.Descriptor(
  name='QueryReportsRequest',
  full_name='cost.QueryReportsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='taskId', full_name='cost.QueryReportsRequest.taskId', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='periodId', full_name='cost.QueryReportsRequest.periodId', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partnerId', full_name='cost.QueryReportsRequest.partnerId', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='costCenterId', full_name='cost.QueryReportsRequest.costCenterId', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branchIds', full_name='cost.QueryReportsRequest.branchIds', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='prdIds', full_name='cost.QueryReportsRequest.prdIds', index=5,
      number=6, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='countType', full_name='cost.QueryReportsRequest.countType', index=6,
      number=7, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branchType', full_name='cost.QueryReportsRequest.branchType', index=7,
      number=8, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='cost.QueryReportsRequest.filters', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='startTime', full_name='cost.QueryReportsRequest.startTime', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='endTime', full_name='cost.QueryReportsRequest.endTime', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='cost.QueryReportsRequest.page', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_QUERYREPORTSREQUEST_FILTERSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1220,
  serialized_end=1654,
)


_QUERYREPORTSRESPONSE = _descriptor.Descriptor(
  name='QueryReportsResponse',
  full_name='cost.QueryReportsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='data', full_name='cost.QueryReportsResponse.data', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='page', full_name='cost.QueryReportsResponse.page', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1656,
  serialized_end=1742,
)


_REPORTDATA = _descriptor.Descriptor(
  name='ReportData',
  full_name='cost.ReportData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='cost.ReportData.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='countTaskId', full_name='cost.ReportData.countTaskId', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partnerId', full_name='cost.ReportData.partnerId', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='periodId', full_name='cost.ReportData.periodId', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='costCenterId', full_name='cost.ReportData.costCenterId', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branchId', full_name='cost.ReportData.branchId', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='productId', full_name='cost.ReportData.productId', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='startQty', full_name='cost.ReportData.startQty', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='startCost', full_name='cost.ReportData.startCost', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='startAmount', full_name='cost.ReportData.startAmount', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='selfIn', full_name='cost.ReportData.selfIn', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='selfAmount', full_name='cost.ReportData.selfAmount', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qtyIn', full_name='cost.ReportData.qtyIn', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amountIn', full_name='cost.ReportData.amountIn', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qtyOut', full_name='cost.ReportData.qtyOut', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amountOut', full_name='cost.ReportData.amountOut', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amountBalance', full_name='cost.ReportData.amountBalance', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qtyBalance', full_name='cost.ReportData.qtyBalance', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qtyBalanceIn', full_name='cost.ReportData.qtyBalanceIn', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qtyBalanceOut', full_name='cost.ReportData.qtyBalanceOut', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deliveryOut', full_name='cost.ReportData.deliveryOut', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deliveryOutCost', full_name='cost.ReportData.deliveryOutCost', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deliveryIn', full_name='cost.ReportData.deliveryIn', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='deliveryInCost', full_name='cost.ReportData.deliveryInCost', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allotOut', full_name='cost.ReportData.allotOut', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allotOutCost', full_name='cost.ReportData.allotOutCost', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allotIn', full_name='cost.ReportData.allotIn', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='allotInCost', full_name='cost.ReportData.allotInCost', index=27,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scrapQty', full_name='cost.ReportData.scrapQty', index=28,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scrapCost', full_name='cost.ReportData.scrapCost', index=29,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='saleQty', full_name='cost.ReportData.saleQty', index=30,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='saleCost', full_name='cost.ReportData.saleCost', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scrapRatio', full_name='cost.ReportData.scrapRatio', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventoryDiff', full_name='cost.ReportData.inventoryDiff', index=33,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventoryCost', full_name='cost.ReportData.inventoryCost', index=34,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='endQty', full_name='cost.ReportData.endQty', index=35,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='endCost', full_name='cost.ReportData.endCost', index=36,
      number=37, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='endAmount', full_name='cost.ReportData.endAmount', index=37,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='brokerQty', full_name='cost.ReportData.brokerQty', index=38,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='brokerCost', full_name='cost.ReportData.brokerCost', index=39,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='workQty', full_name='cost.ReportData.workQty', index=40,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='makePrice', full_name='cost.ReportData.makePrice', index=41,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='workPrice', full_name='cost.ReportData.workPrice', index=42,
      number=43, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='startTime', full_name='cost.ReportData.startTime', index=43,
      number=44, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='endTime', full_name='cost.ReportData.endTime', index=44,
      number=45, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='countType', full_name='cost.ReportData.countType', index=45,
      number=46, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branchType', full_name='cost.ReportData.branchType', index=46,
      number=47, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='taskType', full_name='cost.ReportData.taskType', index=47,
      number=48, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errorCode', full_name='cost.ReportData.errorCode', index=48,
      number=49, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='createdTime', full_name='cost.ReportData.createdTime', index=49,
      number=50, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updatedTime', full_name='cost.ReportData.updatedTime', index=50,
      number=51, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1745,
  serialized_end=2854,
)


_TASKINFO = _descriptor.Descriptor(
  name='TaskInfo',
  full_name='cost.TaskInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='cost.TaskInfo.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='taskId', full_name='cost.TaskInfo.taskId', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partnerId', full_name='cost.TaskInfo.partnerId', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='periodId', full_name='cost.TaskInfo.periodId', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='costCenterId', full_name='cost.TaskInfo.costCenterId', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='startTime', full_name='cost.TaskInfo.startTime', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='endTime', full_name='cost.TaskInfo.endTime', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='taskStatus', full_name='cost.TaskInfo.taskStatus', index=7,
      number=8, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='cost.TaskInfo.reason', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='taskType', full_name='cost.TaskInfo.taskType', index=9,
      number=10, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='countType', full_name='cost.TaskInfo.countType', index=10,
      number=11, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reportType', full_name='cost.TaskInfo.reportType', index=11,
      number=12, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='retry', full_name='cost.TaskInfo.retry', index=12,
      number=13, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lastRunTime', full_name='cost.TaskInfo.lastRunTime', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2857,
  serialized_end=3272,
)


_COMMONRESPONSE = _descriptor.Descriptor(
  name='CommonResponse',
  full_name='cost.CommonResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='cost.CommonResponse.status', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3274,
  serialized_end=3326,
)


_PAGINATION = _descriptor.Descriptor(
  name='Pagination',
  full_name='cost.Pagination',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='offset', full_name='cost.Pagination.offset', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='cost.Pagination.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='cost.Pagination.order', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='cost.Pagination.sort', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='cost.Pagination.total', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3328,
  serialized_end=3415,
)


_COMMONSTATUS = _descriptor.Descriptor(
  name='CommonStatus',
  full_name='cost.CommonStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='cost.CommonStatus.code', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='message', full_name='cost.CommonStatus.message', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='detail', full_name='cost.CommonStatus.detail', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3417,
  serialized_end=3478,
)

_CREATETASKREQUEST.fields_by_name['countType'].enum_type = _COUNTTYPE
_CREATETASKREQUEST.fields_by_name['reportType'].enum_type = _REPORTTYPE
_CREATETASKREQUEST.fields_by_name['taskType'].enum_type = _TASKTYPE
_CREATETASKREQUEST.fields_by_name['startTime'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATETASKREQUEST.fields_by_name['endTime'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATETASKREQUEST.fields_by_name['CreateTasks'].message_type = _CREATETASKS
_CREATETASKS.fields_by_name['countType'].enum_type = _COUNTTYPE
_CREATETASKS.fields_by_name['reportType'].enum_type = _REPORTTYPE
_CREATETASKS.fields_by_name['taskType'].enum_type = _TASKTYPE
_CREATETASKS.fields_by_name['startTime'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATETASKS.fields_by_name['endTime'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_HOLOSENDMYSQLSUCCESSREQUEST.fields_by_name['busDate'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_HOLOSENDMYSQLSUCCESSRESPONSE.fields_by_name['status'].message_type = _COMMONSTATUS
_QUERYTASKREQUEST.fields_by_name['startTime'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYTASKREQUEST.fields_by_name['endTime'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYTASKRESPONSE.fields_by_name['status'].message_type = _COMMONSTATUS
_QUERYTASKRESPONSE.fields_by_name['list'].message_type = _TASKINFO
_QUERYREPORTSREQUEST_FILTERSENTRY.containing_type = _QUERYREPORTSREQUEST
_QUERYREPORTSREQUEST.fields_by_name['countType'].enum_type = _COUNTTYPE
_QUERYREPORTSREQUEST.fields_by_name['branchType'].enum_type = _BRANCHTYPE
_QUERYREPORTSREQUEST.fields_by_name['filters'].message_type = _QUERYREPORTSREQUEST_FILTERSENTRY
_QUERYREPORTSREQUEST.fields_by_name['startTime'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYREPORTSREQUEST.fields_by_name['endTime'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYREPORTSREQUEST.fields_by_name['page'].message_type = _PAGINATION
_QUERYREPORTSRESPONSE.fields_by_name['data'].message_type = _REPORTDATA
_QUERYREPORTSRESPONSE.fields_by_name['page'].message_type = _PAGINATION
_REPORTDATA.fields_by_name['startTime'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_REPORTDATA.fields_by_name['endTime'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_REPORTDATA.fields_by_name['createdTime'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_REPORTDATA.fields_by_name['updatedTime'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TASKINFO.fields_by_name['startTime'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TASKINFO.fields_by_name['endTime'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TASKINFO.fields_by_name['taskStatus'].enum_type = _TASKSTATUS
_TASKINFO.fields_by_name['taskType'].enum_type = _TASKTYPE
_TASKINFO.fields_by_name['countType'].enum_type = _COUNTTYPE
_TASKINFO.fields_by_name['reportType'].enum_type = _REPORTTYPE
_TASKINFO.fields_by_name['lastRunTime'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_COMMONRESPONSE.fields_by_name['status'].message_type = _COMMONSTATUS
DESCRIPTOR.message_types_by_name['CreateTaskRequest'] = _CREATETASKREQUEST
DESCRIPTOR.message_types_by_name['CreateTasks'] = _CREATETASKS
DESCRIPTOR.message_types_by_name['HoloSendMysqlSuccessRequest'] = _HOLOSENDMYSQLSUCCESSREQUEST
DESCRIPTOR.message_types_by_name['HoloSendMysqlSuccessResponse'] = _HOLOSENDMYSQLSUCCESSRESPONSE
DESCRIPTOR.message_types_by_name['QueryTaskRequest'] = _QUERYTASKREQUEST
DESCRIPTOR.message_types_by_name['QueryTaskResponse'] = _QUERYTASKRESPONSE
DESCRIPTOR.message_types_by_name['QueryReportsRequest'] = _QUERYREPORTSREQUEST
DESCRIPTOR.message_types_by_name['QueryReportsResponse'] = _QUERYREPORTSRESPONSE
DESCRIPTOR.message_types_by_name['ReportData'] = _REPORTDATA
DESCRIPTOR.message_types_by_name['TaskInfo'] = _TASKINFO
DESCRIPTOR.message_types_by_name['CommonResponse'] = _COMMONRESPONSE
DESCRIPTOR.message_types_by_name['Pagination'] = _PAGINATION
DESCRIPTOR.message_types_by_name['CommonStatus'] = _COMMONSTATUS
DESCRIPTOR.enum_types_by_name['TaskStatus'] = _TASKSTATUS
DESCRIPTOR.enum_types_by_name['TaskType'] = _TASKTYPE
DESCRIPTOR.enum_types_by_name['ReportType'] = _REPORTTYPE
DESCRIPTOR.enum_types_by_name['CountType'] = _COUNTTYPE
DESCRIPTOR.enum_types_by_name['BranchType'] = _BRANCHTYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CreateTaskRequest = _reflection.GeneratedProtocolMessageType('CreateTaskRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATETASKREQUEST,
  __module__ = 'reports.reports_pb2'
  # @@protoc_insertion_point(class_scope:cost.CreateTaskRequest)
  ))
_sym_db.RegisterMessage(CreateTaskRequest)

CreateTasks = _reflection.GeneratedProtocolMessageType('CreateTasks', (_message.Message,), dict(
  DESCRIPTOR = _CREATETASKS,
  __module__ = 'reports.reports_pb2'
  # @@protoc_insertion_point(class_scope:cost.CreateTasks)
  ))
_sym_db.RegisterMessage(CreateTasks)

HoloSendMysqlSuccessRequest = _reflection.GeneratedProtocolMessageType('HoloSendMysqlSuccessRequest', (_message.Message,), dict(
  DESCRIPTOR = _HOLOSENDMYSQLSUCCESSREQUEST,
  __module__ = 'reports.reports_pb2'
  # @@protoc_insertion_point(class_scope:cost.HoloSendMysqlSuccessRequest)
  ))
_sym_db.RegisterMessage(HoloSendMysqlSuccessRequest)

HoloSendMysqlSuccessResponse = _reflection.GeneratedProtocolMessageType('HoloSendMysqlSuccessResponse', (_message.Message,), dict(
  DESCRIPTOR = _HOLOSENDMYSQLSUCCESSRESPONSE,
  __module__ = 'reports.reports_pb2'
  # @@protoc_insertion_point(class_scope:cost.HoloSendMysqlSuccessResponse)
  ))
_sym_db.RegisterMessage(HoloSendMysqlSuccessResponse)

QueryTaskRequest = _reflection.GeneratedProtocolMessageType('QueryTaskRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYTASKREQUEST,
  __module__ = 'reports.reports_pb2'
  # @@protoc_insertion_point(class_scope:cost.QueryTaskRequest)
  ))
_sym_db.RegisterMessage(QueryTaskRequest)

QueryTaskResponse = _reflection.GeneratedProtocolMessageType('QueryTaskResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYTASKRESPONSE,
  __module__ = 'reports.reports_pb2'
  # @@protoc_insertion_point(class_scope:cost.QueryTaskResponse)
  ))
_sym_db.RegisterMessage(QueryTaskResponse)

QueryReportsRequest = _reflection.GeneratedProtocolMessageType('QueryReportsRequest', (_message.Message,), dict(

  FiltersEntry = _reflection.GeneratedProtocolMessageType('FiltersEntry', (_message.Message,), dict(
    DESCRIPTOR = _QUERYREPORTSREQUEST_FILTERSENTRY,
    __module__ = 'reports.reports_pb2'
    # @@protoc_insertion_point(class_scope:cost.QueryReportsRequest.FiltersEntry)
    ))
  ,
  DESCRIPTOR = _QUERYREPORTSREQUEST,
  __module__ = 'reports.reports_pb2'
  # @@protoc_insertion_point(class_scope:cost.QueryReportsRequest)
  ))
_sym_db.RegisterMessage(QueryReportsRequest)
_sym_db.RegisterMessage(QueryReportsRequest.FiltersEntry)

QueryReportsResponse = _reflection.GeneratedProtocolMessageType('QueryReportsResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYREPORTSRESPONSE,
  __module__ = 'reports.reports_pb2'
  # @@protoc_insertion_point(class_scope:cost.QueryReportsResponse)
  ))
_sym_db.RegisterMessage(QueryReportsResponse)

ReportData = _reflection.GeneratedProtocolMessageType('ReportData', (_message.Message,), dict(
  DESCRIPTOR = _REPORTDATA,
  __module__ = 'reports.reports_pb2'
  # @@protoc_insertion_point(class_scope:cost.ReportData)
  ))
_sym_db.RegisterMessage(ReportData)

TaskInfo = _reflection.GeneratedProtocolMessageType('TaskInfo', (_message.Message,), dict(
  DESCRIPTOR = _TASKINFO,
  __module__ = 'reports.reports_pb2'
  # @@protoc_insertion_point(class_scope:cost.TaskInfo)
  ))
_sym_db.RegisterMessage(TaskInfo)

CommonResponse = _reflection.GeneratedProtocolMessageType('CommonResponse', (_message.Message,), dict(
  DESCRIPTOR = _COMMONRESPONSE,
  __module__ = 'reports.reports_pb2'
  # @@protoc_insertion_point(class_scope:cost.CommonResponse)
  ))
_sym_db.RegisterMessage(CommonResponse)

Pagination = _reflection.GeneratedProtocolMessageType('Pagination', (_message.Message,), dict(
  DESCRIPTOR = _PAGINATION,
  __module__ = 'reports.reports_pb2'
  # @@protoc_insertion_point(class_scope:cost.Pagination)
  ))
_sym_db.RegisterMessage(Pagination)

CommonStatus = _reflection.GeneratedProtocolMessageType('CommonStatus', (_message.Message,), dict(
  DESCRIPTOR = _COMMONSTATUS,
  __module__ = 'reports.reports_pb2'
  # @@protoc_insertion_point(class_scope:cost.CommonStatus)
  ))
_sym_db.RegisterMessage(CommonStatus)


_QUERYREPORTSREQUEST_FILTERSENTRY._options = None

_REPORTS = _descriptor.ServiceDescriptor(
  name='Reports',
  full_name='cost.Reports',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=3869,
  serialized_end=4337,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateTask',
    full_name='cost.Reports.CreateTask',
    index=0,
    containing_service=None,
    input_type=_CREATETASKREQUEST,
    output_type=_COMMONRESPONSE,
    serialized_options=_b('\202\323\344\223\002\033\"\031/api/v1/cost-engine/tasks'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryTask',
    full_name='cost.Reports.QueryTask',
    index=1,
    containing_service=None,
    input_type=_QUERYTASKREQUEST,
    output_type=_QUERYTASKRESPONSE,
    serialized_options=_b('\202\323\344\223\002 \022\036/api/v1/cost-engine/tasks/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='HoloSendMysqlSuccess',
    full_name='cost.Reports.HoloSendMysqlSuccess',
    index=2,
    containing_service=None,
    input_type=_HOLOSENDMYSQLSUCCESSREQUEST,
    output_type=_HOLOSENDMYSQLSUCCESSRESPONSE,
    serialized_options=_b('\202\323\344\223\002-\"+/api/v1/cost-engine/holo-send-mysql/success'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryCostReports',
    full_name='cost.Reports.QueryCostReports',
    index=3,
    containing_service=None,
    input_type=_QUERYREPORTSREQUEST,
    output_type=_QUERYREPORTSRESPONSE,
    serialized_options=_b('\202\323\344\223\002\035\"\033/api/v1/cost-engine/reports'),
  ),
])
_sym_db.RegisterServiceDescriptor(_REPORTS)

DESCRIPTOR.services_by_name['Reports'] = _REPORTS

# @@protoc_insertion_point(module_scope)
