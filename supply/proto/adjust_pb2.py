# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: adjust.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='adjust.proto',
  package='adjust',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x0c\x61\x64just.proto\x12\x06\x61\x64just\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x13\n\x04Pong\x12\x0b\n\x03msg\x18\x01 \x01(\t\"[\n\x13\x43reateAdjustRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x30\n\x0crequest_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"X\n\x14\x43reateAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x30\n\x0crequest_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xa2\x03\n\x10GetAdjustRequest\x12\x15\n\rinclude_total\x18\x01 \x01(\x08\x12.\n\nstart_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0breason_type\x18\x04 \x01(\t\x12\x10\n\x08\x62ranches\x18\x05 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x06 \x03(\x04\x12\x0e\n\x06offset\x18\x07 \x01(\r\x12\r\n\x05limit\x18\x08 \x01(\r\x12/\n\x06status\x18\t \x03(\x0e\x32\x1f.adjust.GetAdjustRequest.STATUS\x12\x0c\n\x04\x63ode\x18\n \x01(\t\x12\r\n\x05order\x18\x0c \x01(\t\x12\x0c\n\x04sort\x18\r \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x1e \x01(\t\x12\x0f\n\x07sources\x18\x1f \x01(\t\"<\n\x06STATUS\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tCONFIRMED\x10\x02\x12\r\n\tCANCELLED\x10\x03\"\x96\x06\n\x06\x41\x64just\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1b\n\x13\x61\x64just_order_number\x18\x02 \x01(\x04\x12\x14\n\x0c\x61\x64just_store\x18\x03 \x01(\x04\x12!\n\x19\x61\x64just_store_secondary_id\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x12\n\npartner_id\x18\x06 \x01(\x04\x12\x16\n\x0eprocess_status\x18\x07 \x01(\t\x12\x13\n\x0breason_type\x18\x08 \x01(\t\x12\x0e\n\x06remark\x18\t \x01(\t\x12\x0e\n\x06status\x18\n \x01(\t\x12/\n\x0b\x61\x64just_date\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x0e \x01(\x04\x12\x12\n\nupdated_by\x18\x0f \x01(\x04\x12\x0f\n\x07user_id\x18\x10 \x01(\x04\x12\x17\n\x0f\x62ranch_batch_id\x18\x11 \x01(\x04\x12\x15\n\rschedule_code\x18\x12 \x01(\t\x12\x13\n\x0bschedule_id\x18\x13 \x01(\x04\x12\x12\n\nrequest_id\x18\x14 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x15 \x01(\t\x12\x14\n\x0cupdated_name\x18\x16 \x01(\t\x12\x12\n\nreceive_id\x18\x19 \x01(\x04\x12\x14\n\x0creceive_code\x18\x1a \x01(\t\x12\x15\n\rschedule_name\x18\x1c \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x1e \x01(\t\x12(\n\x0b\x61ttachments\x18\x1f \x03(\x0b\x32\x13.adjust.Attachments\x12\x0e\n\x06source\x18  \x01(\t\x12\x13\n\x0breason_name\x18! \x01(\t\x12\x15\n\rreject_reason\x18$ \x01(\t\x12\x14\n\x0ctotal_amount\x18& \x01(\x01\x12\x1a\n\x12total_sales_amount\x18\' \x01(\x01\"@\n\x11GetAdjustResponse\x12\x1c\n\x04rows\x18\x01 \x03(\x0b\x32\x0e.adjust.Adjust\x12\r\n\x05total\x18\x02 \x01(\r\"~\n\x17GetAdjustProductRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x15\n\rinclude_total\x18\x02 \x01(\x08\x12\r\n\x05order\x18\x03 \x01(\t\x12\x0e\n\x06offset\x18\x04 \x01(\r\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x0b\n\x03lan\x18\x06 \x01(\t\"\xec\x07\n\rAdjustProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x02 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x03 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x04 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x05 \x01(\t\x12\x11\n\tadjust_id\x18\x06 \x01(\x04\x12\x14\n\x0c\x61\x64just_store\x18\x07 \x01(\x04\x12\x1a\n\x12\x63onfirmed_quantity\x18\x08 \x01(\x01\x12\x12\n\ncreated_by\x18\t \x01(\x04\x12\x14\n\x0cis_confirmed\x18\n \x01(\x08\x12\x13\n\x0bitem_number\x18\x0b \x01(\r\x12\x17\n\x0fmaterial_number\x18\x0c \x01(\t\x12\x12\n\npartner_id\x18\r \x01(\x04\x12\x14\n\x0cproduct_code\x18\x0e \x01(\t\x12\x12\n\nproduct_id\x18\x0f \x01(\x04\x12\x14\n\x0cproduct_name\x18\x10 \x01(\t\x12\x10\n\x08quantity\x18\x11 \x01(\x01\x12\x0c\n\x04spec\x18\x12 \x01(\t\x12\x1a\n\x12stocktake_quantity\x18\x13 \x01(\x01\x12\x19\n\x11stocktake_unit_id\x18\x14 \x01(\x04\x12\x0f\n\x07unit_id\x18\x15 \x01(\x04\x12\x11\n\tunit_name\x18\x16 \x01(\t\x12\x11\n\tunit_spec\x18\x17 \x01(\t\x12\x12\n\nupdated_by\x18\x18 \x01(\x04\x12/\n\x0b\x61\x64just_date\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x1b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07user_id\x18\x1c \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1d \x01(\t\x12\x14\n\x0cupdated_name\x18\x1e \x01(\t\x12\x13\n\x0breason_type\x18\x1f \x01(\t\x12#\n\x1b\x63onvert_accounting_quantity\x18  \x01(\x01\x12\x0e\n\x06is_bom\x18! \x01(\x08\x12\x13\n\x0bposition_id\x18\" \x01(\x04\x12\x12\n\nsku_remark\x18# \x01(\t\x12\x10\n\x08tax_rate\x18& \x01(\x01\x12\x11\n\ttax_price\x18\' \x01(\x01\x12\x0e\n\x06\x61mount\x18( \x01(\x01\x12\x12\n\ntax_amount\x18) \x01(\x01\x12\x12\n\ncost_price\x18* \x01(\x01\x12\x14\n\x0csales_amount\x18+ \x01(\x01\x12\x13\n\x0bsales_price\x18, \x01(\x01\"\x85\x01\n\x18GetAdjustProductResponse\x12#\n\x04rows\x18\x01 \x03(\x0b\x32\x15.adjust.AdjustProduct\x12\r\n\x05total\x18\x02 \x01(\r\x12\x35\n\rposition_rows\x18\x03 \x03(\x0b\x32\x1e.adjust.AdjustPositionProducts\">\n\x14GetAdjustByIDRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x02 \x01(\t\">\n\x14\x43onfirmAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x02 \x01(\t\"\'\n\x15\x43onfirmAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"(\n\x13SubmitAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\"&\n\x14SubmitAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\")\n\x14\x41pproveAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\"\'\n\x15\x41pproveAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"?\n\x13RejectAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x15\n\rreject_reason\x18\x02 \x01(\t\"&\n\x14RejectAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\x85\x02\n GetAdjustProductByStoreIDRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\r\x12\x0e\n\x06offset\x18\x03 \x01(\r\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12/\n\x0b\x61\x64just_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06search\x18\x06 \x01(\t\x12\x15\n\rsearch_fields\x18\x07 \x01(\t\x12\x14\n\x0c\x63\x61tegory_ids\x18\x08 \x03(\x04\x12\x0b\n\x03lan\x18\t \x01(\t\x12\x10\n\x08order_by\x18\n \x01(\t\x12\x0c\n\x04sort\x18\x0b \x01(\t\"\xf3\x02\n\x17\x43reateAdjustProductUint\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x0f\n\x07updated\x18\x07 \x01(\t\x12\x0c\n\x04rate\x18\x08 \x01(\x01\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\t \x01(\x08\x12\r\n\x05order\x18\n \x01(\x08\x12\x10\n\x08purchase\x18\x0b \x01(\x08\x12\r\n\x05sales\x18\x0c \x01(\x08\x12\x11\n\tstocktake\x18\r \x01(\x08\x12\x0b\n\x03\x62om\x18\x0e \x01(\x08\x12\x19\n\x11\x64\x65\x66\x61ult_stocktake\x18\x0f \x01(\x08\x12\x10\n\x08transfer\x18\x10 \x01(\x08\x12\x10\n\x08tax_rate\x18\x11 \x01(\x01\x12\x11\n\ttax_price\x18\x12 \x01(\x01\x12\x12\n\ncost_price\x18\x13 \x01(\x01\x12\x13\n\x0bsales_price\x18\x14 \x01(\x01\x12\x0f\n\x07receive\x18\x15 \x01(\x08\"\x8e\x02\n\x13\x43reateAdjustProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x19\n\x11loss_report_order\x18\x02 \x01(\t\x12\x14\n\x0cproduct_code\x18\x03 \x01(\t\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12\x0c\n\x04spec\x18\x05 \x01(\t\x12\x14\n\x0cstorage_type\x18\x06 \x01(\t\x12\x1b\n\x13product_category_id\x18\x07 \x01(\x04\x12.\n\x05units\x18\x08 \x03(\x0b\x32\x1f.adjust.CreateAdjustProductUint\x12\x0f\n\x07\x62\x61rcode\x18\t \x03(\t\x12\x1a\n\x12real_inventory_qty\x18\n \x01(\x01\"\x9c\x01\n!GetAdjustProductByStoreIDResponse\x12)\n\x04rows\x18\x01 \x03(\x0b\x32\x1b.adjust.CreateAdjustProduct\x12\r\n\x05total\x18\x02 \x01(\r\x12=\n\x18inventory_unchanged_rows\x18\x03 \x03(\x0b\x32\x1b.adjust.CreateAdjustProduct\"\xb1\x02\n\x14\x43reatedAdjustProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x0f\n\x07unit_id\x18\x03 \x01(\x04\x12\x10\n\x08quantity\x18\x04 \x01(\x01\x12\x13\n\x0breason_type\x18\x05 \x01(\t\x12\x13\n\x0bposition_id\x18\x06 \x01(\x04\x12$\n\tskuRemark\x18\x07 \x03(\x0b\x32\x11.adjust.SkuRemark\x12\x10\n\x08tax_rate\x18\x08 \x01(\x01\x12\x11\n\ttax_price\x18\t \x01(\x01\x12\x0e\n\x06\x61mount\x18\n \x01(\x01\x12\x12\n\ntax_amount\x18\x0b \x01(\x01\x12\x12\n\ncost_price\x18\x0c \x01(\x01\x12\x13\n\x0bsales_price\x18\r \x01(\x01\x12\x14\n\x0csales_amount\x18\x0e \x01(\x01\"\x80\x02\n\x14\x43reatedAdjustRequest\x12\x14\n\x0c\x61\x64just_store\x18\x01 \x01(\x04\x12.\n\x08products\x18\x02 \x03(\x0b\x32\x1c.adjust.CreatedAdjustProduct\x12\x13\n\x0breason_type\x18\x03 \x01(\t\x12\x0e\n\x06remark\x18\x04 \x01(\t\x12\x12\n\nrequest_id\x18\x05 \x01(\x04\x12/\n\x0b\x61\x64just_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\x07 \x01(\t\x12\x13\n\x0bposition_id\x18\x08 \x01(\x04\x12\x0e\n\x06source\x18\t \x01(\t\"(\n\x0b\x41ttachments\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\"\xec\x01\n\x13UpdateAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12.\n\x08products\x18\x02 \x03(\x0b\x32\x1c.adjust.CreatedAdjustProduct\x12\x0e\n\x06remark\x18\x03 \x01(\t\x12\x13\n\x0breason_type\x18\x04 \x01(\t\x12/\n\x0b\x61\x64just_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\x06 \x01(\t\x12\x12\n\nattachment\x18\x07 \x01(\t\x12\x13\n\x0bposition_id\x18\x08 \x01(\x04\"(\n\x13\x44\x65leteAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\"&\n\x14\x44\x65leteAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"<\n\x1a\x44\x65leteAdjustProductRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x0b\n\x03ids\x18\x02 \x03(\x04\"-\n\x1b\x44\x65leteAdjustProductResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\xc0\x03\n\x19GetAdjustBiCollectRequest\x12\x14\n\x0c\x63\x61tegory_ids\x18\x01 \x03(\x04\x12\x14\n\x0cproduct_name\x18\x02 \x01(\t\x12.\n\nstart_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12\x11\n\tstore_ids\x18\x08 \x03(\x04\x12\x16\n\x0e\x62om_product_id\x18\t \x03(\x04\x12\x15\n\rperiod_symbol\x18\n \x01(\t\x12\r\n\x05order\x18\x0b \x01(\t\x12\x0c\n\x04sort\x18\x0c \x01(\t\x12\x0c\n\x04\x63ode\x18\r \x01(\t\x12\x13\n\x0breason_type\x18\x0e \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0f \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x10 \x01(\t\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x13\n\x0bhour_offset\x18\x12 \x01(\r\x12\x14\n\x0cposition_ids\x18\x13 \x03(\x04\"\xcd\x06\n\x17\x41\x64justBiCollectResponse\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x05 \x01(\x04\x12\x15\n\rcategory_name\x18\x06 \x01(\t\x12\x14\n\x0cproduct_code\x18\x07 \x01(\t\x12\x12\n\nproduct_id\x18\x08 \x01(\x04\x12\x14\n\x0cproduct_name\x18\t \x01(\t\x12\x10\n\x08quantity\x18\x0b \x01(\x01\x12\x12\n\nstore_code\x18\x0c \x01(\t\x12\x10\n\x08store_id\x18\r \x01(\x04\x12\x12\n\nstore_name\x18\x0e \x01(\t\x12\x0f\n\x07unit_id\x18\x0f \x01(\x04\x12\x11\n\tunit_name\x18\x10 \x01(\t\x12\x13\n\x0breason_type\x18\x11 \x01(\t\x12\x13\n\x0breason_name\x18\x15 \x01(\t\x12\x16\n\x0e\x62om_product_id\x18\x12 \x01(\x04\x12\x18\n\x10\x62om_product_code\x18\x13 \x01(\t\x12\x18\n\x10\x62om_product_name\x18\x14 \x01(\t\x12\x13\n\x0b\x62om_unit_id\x18\x16 \x01(\x04\x12\x15\n\rbom_unit_code\x18\x17 \x01(\t\x12\x15\n\rbom_unit_name\x18\x18 \x01(\t\x12\x1e\n\x16\x62om_accounting_unit_id\x18\x19 \x01(\x04\x12 \n\x18\x62om_accounting_unit_code\x18\x1a \x01(\t\x12 \n\x18\x62om_accounting_unit_name\x18\x1b \x01(\t\x12\x0b\n\x03qty\x18\x1c \x01(\x01\x12\r\n\x05price\x18\x1d \x01(\x01\x12\x16\n\x0e\x61\x63\x63ounting_qty\x18\x1e \x01(\x01\x12\x0c\n\x04\x63ost\x18\x1f \x01(\x01\x12\x15\n\rperiod_symbol\x18  \x01(\t\x12\x13\n\x0bposition_id\x18! \x01(\x04\x12\x15\n\rposition_code\x18\" \x01(\t\x12\x15\n\rposition_name\x18# \x01(\t\x12/\n\x0b\x61\x64just_date\x18$ \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"}\n\x08\x41\x44_total\x12\r\n\x05\x63ount\x18\x01 \x01(\x04\x12\x14\n\x0csum_quantity\x18\x02 \x01(\x01\x12\x1f\n\x17sum_accounting_quantity\x18\x03 \x01(\x01\x12\x0f\n\x07sum_qty\x18\x04 \x01(\x01\x12\x1a\n\x12sum_accounting_qty\x18\x05 \x01(\x01\"l\n\x1aGetAdjustBiCollectResponse\x12-\n\x04rows\x18\x01 \x03(\x0b\x32\x1f.adjust.AdjustBiCollectResponse\x12\x1f\n\x05total\x18\x02 \x01(\x0b\x32\x10.adjust.AD_total\")\n\x13\x43\x61ncelAdjustRequest\x12\x12\n\nadjust_ids\x18\x01 \x03(\x04\"&\n\x14\x43\x61ncelAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\x85\x03\n\x1fGetAdjustCollectDetailedRequest\x12\x14\n\x0c\x63\x61tegory_ids\x18\x01 \x03(\x04\x12\x14\n\x0cproduct_name\x18\x02 \x01(\t\x12.\n\nstart_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12\x11\n\tstore_ids\x18\x08 \x03(\x04\x12\x16\n\x0e\x62om_product_id\x18\t \x03(\x04\x12\r\n\x05order\x18\n \x01(\t\x12\x0c\n\x04sort\x18\x0b \x01(\t\x12\x0c\n\x04\x63ode\x18\x0c \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0e \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x10 \x01(\t\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x14\n\x0cposition_ids\x18\x12 \x03(\x04\"\xf8\x06\n\x15\x41\x64justCollectDetailed\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x13\n\x0b\x61\x64just_code\x18\x04 \x01(\t\x12/\n\x0b\x61\x64just_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tadjust_id\x18\x06 \x01(\x04\x12\x15\n\rcategory_code\x18\x07 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x08 \x01(\x04\x12\x15\n\rcategory_name\x18\t \x01(\t\x12\n\n\x02id\x18\n \x01(\x04\x12\x14\n\x0cproduct_code\x18\x0b \x01(\t\x12\x12\n\nproduct_id\x18\x0c \x01(\x04\x12\x14\n\x0cproduct_name\x18\r \x01(\t\x12\x10\n\x08quantity\x18\x0e \x01(\x01\x12\x13\n\x0breason_type\x18\x0f \x01(\t\x12\x12\n\nstore_code\x18\x10 \x01(\t\x12\x10\n\x08store_id\x18\x11 \x01(\x04\x12\x12\n\nstore_name\x18\x12 \x01(\t\x12\x0f\n\x07unit_id\x18\x13 \x01(\x04\x12\x11\n\tunit_name\x18\x14 \x01(\t\x12\x16\n\x0e\x62om_product_id\x18\x15 \x01(\x04\x12\x18\n\x10\x62om_product_code\x18\x16 \x01(\t\x12\x18\n\x10\x62om_product_name\x18\x17 \x01(\t\x12\x13\n\x0b\x62om_unit_id\x18\x18 \x01(\x04\x12\x15\n\rbom_unit_code\x18\x19 \x01(\t\x12\x15\n\rbom_unit_name\x18\x1a \x01(\t\x12\x1e\n\x16\x62om_accounting_unit_id\x18\x1b \x01(\x04\x12 \n\x18\x62om_accounting_unit_code\x18\x1c \x01(\t\x12 \n\x18\x62om_accounting_unit_name\x18\x1d \x01(\t\x12\x0b\n\x03qty\x18\x1e \x01(\x01\x12\r\n\x05price\x18\x1f \x01(\x01\x12\x16\n\x0e\x61\x63\x63ounting_qty\x18  \x01(\x01\x12\x0c\n\x04\x63ost\x18\" \x01(\x01\x12\x13\n\x0b\x62ranch_type\x18# \x01(\t\x12\x0e\n\x06remark\x18$ \x01(\t\x12\x13\n\x0bposition_id\x18% \x01(\x04\x12\x15\n\rposition_code\x18& \x01(\t\x12\x15\n\rposition_name\x18\' \x01(\t\"p\n GetAdjustCollectDetailedResponse\x12+\n\x04rows\x18\x01 \x03(\x0b\x32\x1d.adjust.AdjustCollectDetailed\x12\x1f\n\x05total\x18\x02 \x01(\x0b\x32\x10.adjust.AD_total\"0\n\x1e\x41utoCloseCreatedAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"Y\n\x1d\x41utoCloseCreatedAdjustRequest\x12\x13\n\x0b\x61\x64just_date\x18\x01 \x01(\t\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x0f\n\x07user_id\x18\x03 \x01(\x04\"\xfe\x01\n\x1cGetMaterialAdjustDataRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tstore_ids\x18\x03 \x03(\x04\x12\x14\n\x0cmaterial_ids\x18\x04 \x03(\x04\x12\r\n\x05limit\x18\x05 \x01(\x04\x12\x0e\n\x06offset\x18\x06 \x01(\x04\x12\x14\n\x0cis_wms_store\x18\x07 \x01(\x08\x12\x0b\n\x03lan\x18\n \x01(\t\x12\x15\n\rinclude_total\x18\x0b \x01(\x08\"f\n\x1dGetMaterialAdjustDataResponse\x12$\n\x04rows\x18\x01 \x03(\x0b\x32\x16.adjust.AdjustResponse\x12\x1f\n\x05total\x18\x02 \x01(\x0b\x32\x10.adjust.AD_total\"\xb9\x05\n\x0e\x41\x64justResponse\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x05 \x01(\x04\x12\x15\n\rcategory_name\x18\x06 \x01(\t\x12\x14\n\x0cproduct_code\x18\x07 \x01(\t\x12\x12\n\nproduct_id\x18\x08 \x01(\x04\x12\x14\n\x0cproduct_name\x18\t \x01(\t\x12\x10\n\x08quantity\x18\x0b \x01(\x01\x12\x12\n\nstore_code\x18\x0c \x01(\t\x12\x10\n\x08store_id\x18\r \x01(\x04\x12\x12\n\nstore_name\x18\x0e \x01(\t\x12\x0f\n\x07unit_id\x18\x0f \x01(\x04\x12\x11\n\tunit_name\x18\x10 \x01(\t\x12\x13\n\x0breason_type\x18\x11 \x01(\t\x12\x16\n\x0e\x62om_product_id\x18\x12 \x01(\x04\x12\x18\n\x10\x62om_product_code\x18\x13 \x01(\t\x12\x18\n\x10\x62om_product_name\x18\x14 \x01(\t\x12\x0b\n\x03qty\x18\x15 \x01(\x01\x12\x13\n\x0b\x62om_unit_id\x18\x16 \x01(\x04\x12\x15\n\rbom_unit_code\x18\x17 \x01(\t\x12\x15\n\rbom_unit_name\x18\x18 \x01(\t\x12\x1e\n\x16\x62om_accounting_unit_id\x18\x19 \x01(\x04\x12 \n\x18\x62om_accounting_unit_code\x18\x1a \x01(\t\x12 \n\x18\x62om_accounting_unit_name\x18\x1b \x01(\t\x12\x16\n\x0e\x61\x63\x63ounting_qty\x18\x1c \x01(\x01\x12\r\n\x05price\x18\x1d \x01(\x01\x12\x0c\n\x04\x63ost\x18\x1e \x01(\x01\x12\x13\n\x0b\x61\x64just_date\x18\x1f \x01(\t\"\x9e\x01\n\x1a\x43reatedAdjustProductByCode\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x11\n\tunit_code\x18\x03 \x01(\t\x12\x10\n\x08quantity\x18\x04 \x01(\x01\x12\x13\n\x0breason_type\x18\x05 \x01(\t\x12$\n\tskuRemark\x18\x06 \x03(\x0b\x32\x11.adjust.SkuRemark\"\x86\x01\n\tSkuRemark\x12#\n\x04name\x18\x01 \x01(\x0b\x32\x15.adjust.SkuRemark.Tag\x12%\n\x06values\x18\x02 \x01(\x0b\x32\x15.adjust.SkuRemark.Tag\x1a-\n\x03Tag\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\"\xc3\x01\n\x1a\x43reatedAdjustByCodeRequest\x12\x14\n\x0c\x61\x64just_store\x18\x01 \x01(\t\x12\x34\n\x08products\x18\x02 \x03(\x0b\x32\".adjust.CreatedAdjustProductByCode\x12\x13\n\x0breason_type\x18\x03 \x01(\t\x12\x0e\n\x06remark\x18\x04 \x01(\t\x12\x12\n\nrequest_id\x18\x05 \x01(\t\x12\x13\n\x0b\x61\x64just_date\x18\x06 \x01(\t\x12\x0b\n\x03lan\x18\x07 \x01(\t\"\x93\x01\n\x16\x41\x64justPositionProducts\x12\x13\n\x0bposition_id\x18\x01 \x01(\x04\x12\x15\n\rposition_code\x18\x02 \x01(\t\x12\x15\n\rposition_name\x18\x03 \x01(\t\x12\'\n\x08products\x18\x04 \x03(\x0b\x32\x15.adjust.AdjustProduct\x12\r\n\x05total\x18\x05 \x01(\x04\x32\x88\x13\n\x06\x61\x64just\x12I\n\x04Ping\x12\x16.google.protobuf.Empty\x1a\x0c.adjust.Pong\"\x1b\x82\xd3\xe4\x93\x02\x15\x12\x13/api/v2/supply/ping\x12_\n\tGetAdjust\x12\x18.adjust.GetAdjustRequest\x1a\x19.adjust.GetAdjustResponse\"\x1d\x82\xd3\xe4\x93\x02\x17\x12\x15/api/v2/supply/adjust\x12\x88\x01\n\x10GetAdjustProduct\x12\x1f.adjust.GetAdjustProductRequest\x1a .adjust.GetAdjustProductResponse\"1\x82\xd3\xe4\x93\x02+\x12)/api/v2/supply/adjust/{adjust_id}/product\x12h\n\rGetAdjustByID\x12\x1c.adjust.GetAdjustByIDRequest\x1a\x0e.adjust.Adjust\")\x82\xd3\xe4\x93\x02#\x12!/api/v2/supply/adjust/{adjust_id}\x12\x82\x01\n\rConfirmAdjust\x12\x1c.adjust.ConfirmAdjustRequest\x1a\x1d.adjust.ConfirmAdjustResponse\"4\x82\xd3\xe4\x93\x02.\x1a)/api/v2/supply/adjust/{adjust_id}/confirm:\x01*\x12~\n\x0cSubmitAdjust\x12\x1b.adjust.SubmitAdjustRequest\x1a\x1c.adjust.SubmitAdjustResponse\"3\x82\xd3\xe4\x93\x02-\x1a(/api/v2/supply/adjust/{adjust_id}/submit:\x01*\x12\x82\x01\n\rApproveAdjust\x12\x1c.adjust.ApproveAdjustRequest\x1a\x1d.adjust.ApproveAdjustResponse\"4\x82\xd3\xe4\x93\x02.\x1a)/api/v2/supply/adjust/{adjust_id}/approve:\x01*\x12~\n\x0cRejectAdjust\x12\x1b.adjust.RejectAdjustRequest\x1a\x1c.adjust.RejectAdjustResponse\"3\x82\xd3\xe4\x93\x02-\x1a(/api/v2/supply/adjust/{adjust_id}/reject:\x01*\x12\x9e\x01\n\x19GetAdjustProductByStoreID\x12(.adjust.GetAdjustProductByStoreIDRequest\x1a).adjust.GetAdjustProductByStoreIDResponse\",\x82\xd3\xe4\x93\x02&\x12$/api/v2/supply/adjust/store/products\x12\x66\n\rCreatedAdjust\x12\x1c.adjust.CreatedAdjustRequest\x1a\x0e.adjust.Adjust\"\'\x82\xd3\xe4\x93\x02!\"\x1c/api/v2/supply/adjust/create:\x01*\x12p\n\x0cUpdateAdjust\x12\x1b.adjust.UpdateAdjustRequest\x1a\x0e.adjust.Adjust\"3\x82\xd3\xe4\x93\x02-\x1a(/api/v2/supply/adjust/{adjust_id}/update:\x01*\x12~\n\x0c\x44\x65leteAdjust\x12\x1b.adjust.DeleteAdjustRequest\x1a\x1c.adjust.DeleteAdjustResponse\"3\x82\xd3\xe4\x93\x02-\x1a(/api/v2/supply/adjust/{adjust_id}/delete:\x01*\x12\x9b\x01\n\x13\x44\x65leteAdjustProduct\x12\".adjust.DeleteAdjustProductRequest\x1a#.adjust.DeleteAdjustProductResponse\";\x82\xd3\xe4\x93\x02\x35\x1a\x30/api/v2/supply/adjust/product/{adjust_id}/delete:\x01*\x12\x85\x01\n\x12GetAdjustBiCollect\x12!.adjust.GetAdjustBiCollectRequest\x1a\".adjust.GetAdjustBiCollectResponse\"(\x82\xd3\xe4\x93\x02\"\x12 /api/v2/supply/adjust/bi/collect\x12~\n\x0c\x43\x61ncelAdjust\x12\x1b.adjust.CancelAdjustRequest\x1a\x1c.adjust.CancelAdjustResponse\"3\x82\xd3\xe4\x93\x02-\x1a(/api/v2/supply/adjust/auto_create/cancel:\x01*\x12\x98\x01\n\x18GetAdjustCollectDetailed\x12\'.adjust.GetAdjustCollectDetailedRequest\x1a(.adjust.GetAdjustCollectDetailedResponse\")\x82\xd3\xe4\x93\x02#\x12!/api/v2/supply/adjust/bi/detailed\x12\x91\x01\n\x16\x41utoCloseCreatedAdjust\x12%.adjust.AutoCloseCreatedAdjustRequest\x1a&.adjust.AutoCloseCreatedAdjustResponse\"(\x82\xd3\xe4\x93\x02\"\x12 /api/v2/supply/adjust/close/auto\x12\x91\x01\n\x15GetMaterialAdjustData\x12$.adjust.GetMaterialAdjustDataRequest\x1a%.adjust.GetMaterialAdjustDataResponse\"+\x82\xd3\xe4\x93\x02%\x12#/api/v2/supply/adjust/material/cost\x12o\n\x13\x43reatedAdjustByCode\x12\".adjust.CreatedAdjustByCodeRequest\x1a\x0e.adjust.Adjust\"$\x82\xd3\xe4\x93\x02\x1e\"\x19/api/v2/supply/adjust/pos:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_empty__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])



_GETADJUSTREQUEST_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='adjust.GetAdjustRequest.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CONFIRMED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=3, number=3,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=679,
  serialized_end=739,
)
_sym_db.RegisterEnumDescriptor(_GETADJUSTREQUEST_STATUS)


_PONG = _descriptor.Descriptor(
  name='Pong',
  full_name='adjust.Pong',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='msg', full_name='adjust.Pong.msg', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=116,
  serialized_end=135,
)


_CREATEADJUSTREQUEST = _descriptor.Descriptor(
  name='CreateAdjustRequest',
  full_name='adjust.CreateAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='adjust.CreateAdjustRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='adjust.CreateAdjustRequest.request_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=137,
  serialized_end=228,
)


_CREATEADJUSTRESPONSE = _descriptor.Descriptor(
  name='CreateAdjustResponse',
  full_name='adjust.CreateAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='adjust.CreateAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='adjust.CreateAdjustResponse.request_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=230,
  serialized_end=318,
)


_GETADJUSTREQUEST = _descriptor.Descriptor(
  name='GetAdjustRequest',
  full_name='adjust.GetAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='include_total', full_name='adjust.GetAdjustRequest.include_total', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='adjust.GetAdjustRequest.start_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='adjust.GetAdjustRequest.end_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='adjust.GetAdjustRequest.reason_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branches', full_name='adjust.GetAdjustRequest.branches', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='adjust.GetAdjustRequest.product_ids', index=5,
      number=6, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='adjust.GetAdjustRequest.offset', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='adjust.GetAdjustRequest.limit', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='adjust.GetAdjustRequest.status', index=8,
      number=9, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='adjust.GetAdjustRequest.code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='adjust.GetAdjustRequest.order', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='adjust.GetAdjustRequest.sort', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='adjust.GetAdjustRequest.branch_type', index=12,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sources', full_name='adjust.GetAdjustRequest.sources', index=13,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _GETADJUSTREQUEST_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=321,
  serialized_end=739,
)


_ADJUST = _descriptor.Descriptor(
  name='Adjust',
  full_name='adjust.Adjust',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='adjust.Adjust.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_order_number', full_name='adjust.Adjust.adjust_order_number', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_store', full_name='adjust.Adjust.adjust_store', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_store_secondary_id', full_name='adjust.Adjust.adjust_store_secondary_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='adjust.Adjust.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='adjust.Adjust.partner_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='adjust.Adjust.process_status', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='adjust.Adjust.reason_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='adjust.Adjust.remark', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='adjust.Adjust.status', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='adjust.Adjust.adjust_date', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='adjust.Adjust.created_at', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='adjust.Adjust.updated_at', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='adjust.Adjust.created_by', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='adjust.Adjust.updated_by', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='adjust.Adjust.user_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_batch_id', full_name='adjust.Adjust.branch_batch_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='adjust.Adjust.schedule_code', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_id', full_name='adjust.Adjust.schedule_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='adjust.Adjust.request_id', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='adjust.Adjust.created_name', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='adjust.Adjust.updated_name', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_id', full_name='adjust.Adjust.receive_id', index=22,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_code', full_name='adjust.Adjust.receive_code', index=23,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='adjust.Adjust.schedule_name', index=24,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='adjust.Adjust.branch_type', index=25,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='adjust.Adjust.attachments', index=26,
      number=31, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source', full_name='adjust.Adjust.source', index=27,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_name', full_name='adjust.Adjust.reason_name', index=28,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='adjust.Adjust.reject_reason', index=29,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='adjust.Adjust.total_amount', index=30,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_sales_amount', full_name='adjust.Adjust.total_sales_amount', index=31,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=742,
  serialized_end=1532,
)


_GETADJUSTRESPONSE = _descriptor.Descriptor(
  name='GetAdjustResponse',
  full_name='adjust.GetAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='adjust.GetAdjustResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='adjust.GetAdjustResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1534,
  serialized_end=1598,
)


_GETADJUSTPRODUCTREQUEST = _descriptor.Descriptor(
  name='GetAdjustProductRequest',
  full_name='adjust.GetAdjustProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='adjust.GetAdjustProductRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='adjust.GetAdjustProductRequest.include_total', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='adjust.GetAdjustProductRequest.order', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='adjust.GetAdjustProductRequest.offset', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='adjust.GetAdjustProductRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='adjust.GetAdjustProductRequest.lan', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1600,
  serialized_end=1726,
)


_ADJUSTPRODUCT = _descriptor.Descriptor(
  name='AdjustProduct',
  full_name='adjust.AdjustProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='adjust.AdjustProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='adjust.AdjustProduct.accounting_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='adjust.AdjustProduct.accounting_unit_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='adjust.AdjustProduct.accounting_unit_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='adjust.AdjustProduct.accounting_unit_spec', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='adjust.AdjustProduct.adjust_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_store', full_name='adjust.AdjustProduct.adjust_store', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='adjust.AdjustProduct.confirmed_quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='adjust.AdjustProduct.created_by', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_confirmed', full_name='adjust.AdjustProduct.is_confirmed', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_number', full_name='adjust.AdjustProduct.item_number', index=10,
      number=11, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='adjust.AdjustProduct.material_number', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='adjust.AdjustProduct.partner_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='adjust.AdjustProduct.product_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='adjust.AdjustProduct.product_id', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='adjust.AdjustProduct.product_name', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='adjust.AdjustProduct.quantity', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='adjust.AdjustProduct.spec', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_quantity', full_name='adjust.AdjustProduct.stocktake_quantity', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_unit_id', full_name='adjust.AdjustProduct.stocktake_unit_id', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='adjust.AdjustProduct.unit_id', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='adjust.AdjustProduct.unit_name', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='adjust.AdjustProduct.unit_spec', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='adjust.AdjustProduct.updated_by', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='adjust.AdjustProduct.adjust_date', index=24,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='adjust.AdjustProduct.updated_at', index=25,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='adjust.AdjustProduct.created_at', index=26,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='adjust.AdjustProduct.user_id', index=27,
      number=28, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='adjust.AdjustProduct.created_name', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='adjust.AdjustProduct.updated_name', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='adjust.AdjustProduct.reason_type', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_accounting_quantity', full_name='adjust.AdjustProduct.convert_accounting_quantity', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_bom', full_name='adjust.AdjustProduct.is_bom', index=32,
      number=33, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='adjust.AdjustProduct.position_id', index=33,
      number=34, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sku_remark', full_name='adjust.AdjustProduct.sku_remark', index=34,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='adjust.AdjustProduct.tax_rate', index=35,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='adjust.AdjustProduct.tax_price', index=36,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='adjust.AdjustProduct.amount', index=37,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_amount', full_name='adjust.AdjustProduct.tax_amount', index=38,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='adjust.AdjustProduct.cost_price', index=39,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='adjust.AdjustProduct.sales_amount', index=40,
      number=43, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='adjust.AdjustProduct.sales_price', index=41,
      number=44, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1729,
  serialized_end=2733,
)


_GETADJUSTPRODUCTRESPONSE = _descriptor.Descriptor(
  name='GetAdjustProductResponse',
  full_name='adjust.GetAdjustProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='adjust.GetAdjustProductResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='adjust.GetAdjustProductResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_rows', full_name='adjust.GetAdjustProductResponse.position_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2736,
  serialized_end=2869,
)


_GETADJUSTBYIDREQUEST = _descriptor.Descriptor(
  name='GetAdjustByIDRequest',
  full_name='adjust.GetAdjustByIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='adjust.GetAdjustByIDRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='adjust.GetAdjustByIDRequest.branch_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2871,
  serialized_end=2933,
)


_CONFIRMADJUSTREQUEST = _descriptor.Descriptor(
  name='ConfirmAdjustRequest',
  full_name='adjust.ConfirmAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='adjust.ConfirmAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='adjust.ConfirmAdjustRequest.branch_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2935,
  serialized_end=2997,
)


_CONFIRMADJUSTRESPONSE = _descriptor.Descriptor(
  name='ConfirmAdjustResponse',
  full_name='adjust.ConfirmAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='adjust.ConfirmAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2999,
  serialized_end=3038,
)


_SUBMITADJUSTREQUEST = _descriptor.Descriptor(
  name='SubmitAdjustRequest',
  full_name='adjust.SubmitAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='adjust.SubmitAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3040,
  serialized_end=3080,
)


_SUBMITADJUSTRESPONSE = _descriptor.Descriptor(
  name='SubmitAdjustResponse',
  full_name='adjust.SubmitAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='adjust.SubmitAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3082,
  serialized_end=3120,
)


_APPROVEADJUSTREQUEST = _descriptor.Descriptor(
  name='ApproveAdjustRequest',
  full_name='adjust.ApproveAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='adjust.ApproveAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3122,
  serialized_end=3163,
)


_APPROVEADJUSTRESPONSE = _descriptor.Descriptor(
  name='ApproveAdjustResponse',
  full_name='adjust.ApproveAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='adjust.ApproveAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3165,
  serialized_end=3204,
)


_REJECTADJUSTREQUEST = _descriptor.Descriptor(
  name='RejectAdjustRequest',
  full_name='adjust.RejectAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='adjust.RejectAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='adjust.RejectAdjustRequest.reject_reason', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3206,
  serialized_end=3269,
)


_REJECTADJUSTRESPONSE = _descriptor.Descriptor(
  name='RejectAdjustResponse',
  full_name='adjust.RejectAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='adjust.RejectAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3271,
  serialized_end=3309,
)


_GETADJUSTPRODUCTBYSTOREIDREQUEST = _descriptor.Descriptor(
  name='GetAdjustProductByStoreIDRequest',
  full_name='adjust.GetAdjustProductByStoreIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='adjust.GetAdjustProductByStoreIDRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='adjust.GetAdjustProductByStoreIDRequest.limit', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='adjust.GetAdjustProductByStoreIDRequest.offset', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='adjust.GetAdjustProductByStoreIDRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='adjust.GetAdjustProductByStoreIDRequest.adjust_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='adjust.GetAdjustProductByStoreIDRequest.search', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='adjust.GetAdjustProductByStoreIDRequest.search_fields', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='adjust.GetAdjustProductByStoreIDRequest.category_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='adjust.GetAdjustProductByStoreIDRequest.lan', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by', full_name='adjust.GetAdjustProductByStoreIDRequest.order_by', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='adjust.GetAdjustProductByStoreIDRequest.sort', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3312,
  serialized_end=3573,
)


_CREATEADJUSTPRODUCTUINT = _descriptor.Descriptor(
  name='CreateAdjustProductUint',
  full_name='adjust.CreateAdjustProductUint',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='adjust.CreateAdjustProductUint.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='adjust.CreateAdjustProductUint.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='adjust.CreateAdjustProductUint.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='adjust.CreateAdjustProductUint.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='adjust.CreateAdjustProductUint.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='adjust.CreateAdjustProductUint.updated', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='adjust.CreateAdjustProductUint.rate', index=6,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default', full_name='adjust.CreateAdjustProductUint.default', index=7,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='adjust.CreateAdjustProductUint.order', index=8,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase', full_name='adjust.CreateAdjustProductUint.purchase', index=9,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales', full_name='adjust.CreateAdjustProductUint.sales', index=10,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='adjust.CreateAdjustProductUint.stocktake', index=11,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom', full_name='adjust.CreateAdjustProductUint.bom', index=12,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default_stocktake', full_name='adjust.CreateAdjustProductUint.default_stocktake', index=13,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer', full_name='adjust.CreateAdjustProductUint.transfer', index=14,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='adjust.CreateAdjustProductUint.tax_rate', index=15,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='adjust.CreateAdjustProductUint.tax_price', index=16,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='adjust.CreateAdjustProductUint.cost_price', index=17,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='adjust.CreateAdjustProductUint.sales_price', index=18,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive', full_name='adjust.CreateAdjustProductUint.receive', index=19,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3576,
  serialized_end=3947,
)


_CREATEADJUSTPRODUCT = _descriptor.Descriptor(
  name='CreateAdjustProduct',
  full_name='adjust.CreateAdjustProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='adjust.CreateAdjustProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='loss_report_order', full_name='adjust.CreateAdjustProduct.loss_report_order', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='adjust.CreateAdjustProduct.product_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='adjust.CreateAdjustProduct.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='adjust.CreateAdjustProduct.spec', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='adjust.CreateAdjustProduct.storage_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_category_id', full_name='adjust.CreateAdjustProduct.product_category_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='adjust.CreateAdjustProduct.units', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='barcode', full_name='adjust.CreateAdjustProduct.barcode', index=8,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='real_inventory_qty', full_name='adjust.CreateAdjustProduct.real_inventory_qty', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3950,
  serialized_end=4220,
)


_GETADJUSTPRODUCTBYSTOREIDRESPONSE = _descriptor.Descriptor(
  name='GetAdjustProductByStoreIDResponse',
  full_name='adjust.GetAdjustProductByStoreIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='adjust.GetAdjustProductByStoreIDResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='adjust.GetAdjustProductByStoreIDResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_unchanged_rows', full_name='adjust.GetAdjustProductByStoreIDResponse.inventory_unchanged_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4223,
  serialized_end=4379,
)


_CREATEDADJUSTPRODUCT = _descriptor.Descriptor(
  name='CreatedAdjustProduct',
  full_name='adjust.CreatedAdjustProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='adjust.CreatedAdjustProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='adjust.CreatedAdjustProduct.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='adjust.CreatedAdjustProduct.unit_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='adjust.CreatedAdjustProduct.quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='adjust.CreatedAdjustProduct.reason_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='adjust.CreatedAdjustProduct.position_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='skuRemark', full_name='adjust.CreatedAdjustProduct.skuRemark', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='adjust.CreatedAdjustProduct.tax_rate', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='adjust.CreatedAdjustProduct.tax_price', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='adjust.CreatedAdjustProduct.amount', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_amount', full_name='adjust.CreatedAdjustProduct.tax_amount', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='adjust.CreatedAdjustProduct.cost_price', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='adjust.CreatedAdjustProduct.sales_price', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='adjust.CreatedAdjustProduct.sales_amount', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4382,
  serialized_end=4687,
)


_CREATEDADJUSTREQUEST = _descriptor.Descriptor(
  name='CreatedAdjustRequest',
  full_name='adjust.CreatedAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_store', full_name='adjust.CreatedAdjustRequest.adjust_store', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='adjust.CreatedAdjustRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='adjust.CreatedAdjustRequest.reason_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='adjust.CreatedAdjustRequest.remark', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='adjust.CreatedAdjustRequest.request_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='adjust.CreatedAdjustRequest.adjust_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='adjust.CreatedAdjustRequest.branch_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='adjust.CreatedAdjustRequest.position_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source', full_name='adjust.CreatedAdjustRequest.source', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4690,
  serialized_end=4946,
)


_ATTACHMENTS = _descriptor.Descriptor(
  name='Attachments',
  full_name='adjust.Attachments',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='adjust.Attachments.type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='adjust.Attachments.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4948,
  serialized_end=4988,
)


_UPDATEADJUSTREQUEST = _descriptor.Descriptor(
  name='UpdateAdjustRequest',
  full_name='adjust.UpdateAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='adjust.UpdateAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='adjust.UpdateAdjustRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='adjust.UpdateAdjustRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='adjust.UpdateAdjustRequest.reason_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='adjust.UpdateAdjustRequest.adjust_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='adjust.UpdateAdjustRequest.branch_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachment', full_name='adjust.UpdateAdjustRequest.attachment', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='adjust.UpdateAdjustRequest.position_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4991,
  serialized_end=5227,
)


_DELETEADJUSTREQUEST = _descriptor.Descriptor(
  name='DeleteAdjustRequest',
  full_name='adjust.DeleteAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='adjust.DeleteAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5229,
  serialized_end=5269,
)


_DELETEADJUSTRESPONSE = _descriptor.Descriptor(
  name='DeleteAdjustResponse',
  full_name='adjust.DeleteAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='adjust.DeleteAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5271,
  serialized_end=5309,
)


_DELETEADJUSTPRODUCTREQUEST = _descriptor.Descriptor(
  name='DeleteAdjustProductRequest',
  full_name='adjust.DeleteAdjustProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='adjust.DeleteAdjustProductRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='adjust.DeleteAdjustProductRequest.ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5311,
  serialized_end=5371,
)


_DELETEADJUSTPRODUCTRESPONSE = _descriptor.Descriptor(
  name='DeleteAdjustProductResponse',
  full_name='adjust.DeleteAdjustProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='adjust.DeleteAdjustProductResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5373,
  serialized_end=5418,
)


_GETADJUSTBICOLLECTREQUEST = _descriptor.Descriptor(
  name='GetAdjustBiCollectRequest',
  full_name='adjust.GetAdjustBiCollectRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='adjust.GetAdjustBiCollectRequest.category_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='adjust.GetAdjustBiCollectRequest.product_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='adjust.GetAdjustBiCollectRequest.start_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='adjust.GetAdjustBiCollectRequest.end_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='adjust.GetAdjustBiCollectRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='adjust.GetAdjustBiCollectRequest.offset', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='adjust.GetAdjustBiCollectRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='adjust.GetAdjustBiCollectRequest.store_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='adjust.GetAdjustBiCollectRequest.bom_product_id', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='adjust.GetAdjustBiCollectRequest.period_symbol', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='adjust.GetAdjustBiCollectRequest.order', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='adjust.GetAdjustBiCollectRequest.sort', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='adjust.GetAdjustBiCollectRequest.code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='adjust.GetAdjustBiCollectRequest.reason_type', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='adjust.GetAdjustBiCollectRequest.is_wms_store', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='adjust.GetAdjustBiCollectRequest.branch_type', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='adjust.GetAdjustBiCollectRequest.lan', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hour_offset', full_name='adjust.GetAdjustBiCollectRequest.hour_offset', index=17,
      number=18, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='adjust.GetAdjustBiCollectRequest.position_ids', index=18,
      number=19, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5421,
  serialized_end=5869,
)


_ADJUSTBICOLLECTRESPONSE = _descriptor.Descriptor(
  name='AdjustBiCollectResponse',
  full_name='adjust.AdjustBiCollectResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='adjust.AdjustBiCollectResponse.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='adjust.AdjustBiCollectResponse.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='adjust.AdjustBiCollectResponse.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='adjust.AdjustBiCollectResponse.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='adjust.AdjustBiCollectResponse.category_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='adjust.AdjustBiCollectResponse.category_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='adjust.AdjustBiCollectResponse.product_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='adjust.AdjustBiCollectResponse.product_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='adjust.AdjustBiCollectResponse.product_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='adjust.AdjustBiCollectResponse.quantity', index=9,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='adjust.AdjustBiCollectResponse.store_code', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='adjust.AdjustBiCollectResponse.store_id', index=11,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='adjust.AdjustBiCollectResponse.store_name', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='adjust.AdjustBiCollectResponse.unit_id', index=13,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='adjust.AdjustBiCollectResponse.unit_name', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='adjust.AdjustBiCollectResponse.reason_type', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_name', full_name='adjust.AdjustBiCollectResponse.reason_name', index=16,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='adjust.AdjustBiCollectResponse.bom_product_id', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_code', full_name='adjust.AdjustBiCollectResponse.bom_product_code', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_name', full_name='adjust.AdjustBiCollectResponse.bom_product_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_id', full_name='adjust.AdjustBiCollectResponse.bom_unit_id', index=20,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_code', full_name='adjust.AdjustBiCollectResponse.bom_unit_code', index=21,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_name', full_name='adjust.AdjustBiCollectResponse.bom_unit_name', index=22,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_id', full_name='adjust.AdjustBiCollectResponse.bom_accounting_unit_id', index=23,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_code', full_name='adjust.AdjustBiCollectResponse.bom_accounting_unit_code', index=24,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_name', full_name='adjust.AdjustBiCollectResponse.bom_accounting_unit_name', index=25,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='adjust.AdjustBiCollectResponse.qty', index=26,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='adjust.AdjustBiCollectResponse.price', index=27,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_qty', full_name='adjust.AdjustBiCollectResponse.accounting_qty', index=28,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost', full_name='adjust.AdjustBiCollectResponse.cost', index=29,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='adjust.AdjustBiCollectResponse.period_symbol', index=30,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='adjust.AdjustBiCollectResponse.position_id', index=31,
      number=33, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='adjust.AdjustBiCollectResponse.position_code', index=32,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='adjust.AdjustBiCollectResponse.position_name', index=33,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='adjust.AdjustBiCollectResponse.adjust_date', index=34,
      number=36, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5872,
  serialized_end=6717,
)


_AD_TOTAL = _descriptor.Descriptor(
  name='AD_total',
  full_name='adjust.AD_total',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='count', full_name='adjust.AD_total.count', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_quantity', full_name='adjust.AD_total.sum_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_accounting_quantity', full_name='adjust.AD_total.sum_accounting_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_qty', full_name='adjust.AD_total.sum_qty', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_accounting_qty', full_name='adjust.AD_total.sum_accounting_qty', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6719,
  serialized_end=6844,
)


_GETADJUSTBICOLLECTRESPONSE = _descriptor.Descriptor(
  name='GetAdjustBiCollectResponse',
  full_name='adjust.GetAdjustBiCollectResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='adjust.GetAdjustBiCollectResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='adjust.GetAdjustBiCollectResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6846,
  serialized_end=6954,
)


_CANCELADJUSTREQUEST = _descriptor.Descriptor(
  name='CancelAdjustRequest',
  full_name='adjust.CancelAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_ids', full_name='adjust.CancelAdjustRequest.adjust_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6956,
  serialized_end=6997,
)


_CANCELADJUSTRESPONSE = _descriptor.Descriptor(
  name='CancelAdjustResponse',
  full_name='adjust.CancelAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='adjust.CancelAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6999,
  serialized_end=7037,
)


_GETADJUSTCOLLECTDETAILEDREQUEST = _descriptor.Descriptor(
  name='GetAdjustCollectDetailedRequest',
  full_name='adjust.GetAdjustCollectDetailedRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='adjust.GetAdjustCollectDetailedRequest.category_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='adjust.GetAdjustCollectDetailedRequest.product_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='adjust.GetAdjustCollectDetailedRequest.start_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='adjust.GetAdjustCollectDetailedRequest.end_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='adjust.GetAdjustCollectDetailedRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='adjust.GetAdjustCollectDetailedRequest.offset', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='adjust.GetAdjustCollectDetailedRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='adjust.GetAdjustCollectDetailedRequest.store_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='adjust.GetAdjustCollectDetailedRequest.bom_product_id', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='adjust.GetAdjustCollectDetailedRequest.order', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='adjust.GetAdjustCollectDetailedRequest.sort', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='adjust.GetAdjustCollectDetailedRequest.code', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='adjust.GetAdjustCollectDetailedRequest.is_wms_store', index=12,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='adjust.GetAdjustCollectDetailedRequest.branch_type', index=13,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='adjust.GetAdjustCollectDetailedRequest.lan', index=14,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='adjust.GetAdjustCollectDetailedRequest.position_ids', index=15,
      number=18, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7040,
  serialized_end=7429,
)


_ADJUSTCOLLECTDETAILED = _descriptor.Descriptor(
  name='AdjustCollectDetailed',
  full_name='adjust.AdjustCollectDetailed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='adjust.AdjustCollectDetailed.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='adjust.AdjustCollectDetailed.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='adjust.AdjustCollectDetailed.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_code', full_name='adjust.AdjustCollectDetailed.adjust_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='adjust.AdjustCollectDetailed.adjust_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='adjust.AdjustCollectDetailed.adjust_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='adjust.AdjustCollectDetailed.category_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='adjust.AdjustCollectDetailed.category_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='adjust.AdjustCollectDetailed.category_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='adjust.AdjustCollectDetailed.id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='adjust.AdjustCollectDetailed.product_code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='adjust.AdjustCollectDetailed.product_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='adjust.AdjustCollectDetailed.product_name', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='adjust.AdjustCollectDetailed.quantity', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='adjust.AdjustCollectDetailed.reason_type', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='adjust.AdjustCollectDetailed.store_code', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='adjust.AdjustCollectDetailed.store_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='adjust.AdjustCollectDetailed.store_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='adjust.AdjustCollectDetailed.unit_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='adjust.AdjustCollectDetailed.unit_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='adjust.AdjustCollectDetailed.bom_product_id', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_code', full_name='adjust.AdjustCollectDetailed.bom_product_code', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_name', full_name='adjust.AdjustCollectDetailed.bom_product_name', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_id', full_name='adjust.AdjustCollectDetailed.bom_unit_id', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_code', full_name='adjust.AdjustCollectDetailed.bom_unit_code', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_name', full_name='adjust.AdjustCollectDetailed.bom_unit_name', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_id', full_name='adjust.AdjustCollectDetailed.bom_accounting_unit_id', index=26,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_code', full_name='adjust.AdjustCollectDetailed.bom_accounting_unit_code', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_name', full_name='adjust.AdjustCollectDetailed.bom_accounting_unit_name', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='adjust.AdjustCollectDetailed.qty', index=29,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='adjust.AdjustCollectDetailed.price', index=30,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_qty', full_name='adjust.AdjustCollectDetailed.accounting_qty', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost', full_name='adjust.AdjustCollectDetailed.cost', index=32,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='adjust.AdjustCollectDetailed.branch_type', index=33,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='adjust.AdjustCollectDetailed.remark', index=34,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='adjust.AdjustCollectDetailed.position_id', index=35,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='adjust.AdjustCollectDetailed.position_code', index=36,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='adjust.AdjustCollectDetailed.position_name', index=37,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7432,
  serialized_end=8320,
)


_GETADJUSTCOLLECTDETAILEDRESPONSE = _descriptor.Descriptor(
  name='GetAdjustCollectDetailedResponse',
  full_name='adjust.GetAdjustCollectDetailedResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='adjust.GetAdjustCollectDetailedResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='adjust.GetAdjustCollectDetailedResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8322,
  serialized_end=8434,
)


_AUTOCLOSECREATEDADJUSTRESPONSE = _descriptor.Descriptor(
  name='AutoCloseCreatedAdjustResponse',
  full_name='adjust.AutoCloseCreatedAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='adjust.AutoCloseCreatedAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8436,
  serialized_end=8484,
)


_AUTOCLOSECREATEDADJUSTREQUEST = _descriptor.Descriptor(
  name='AutoCloseCreatedAdjustRequest',
  full_name='adjust.AutoCloseCreatedAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='adjust.AutoCloseCreatedAdjustRequest.adjust_date', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='adjust.AutoCloseCreatedAdjustRequest.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='adjust.AutoCloseCreatedAdjustRequest.user_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8486,
  serialized_end=8575,
)


_GETMATERIALADJUSTDATAREQUEST = _descriptor.Descriptor(
  name='GetMaterialAdjustDataRequest',
  full_name='adjust.GetMaterialAdjustDataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='adjust.GetMaterialAdjustDataRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='adjust.GetMaterialAdjustDataRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='adjust.GetMaterialAdjustDataRequest.store_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_ids', full_name='adjust.GetMaterialAdjustDataRequest.material_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='adjust.GetMaterialAdjustDataRequest.limit', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='adjust.GetMaterialAdjustDataRequest.offset', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='adjust.GetMaterialAdjustDataRequest.is_wms_store', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='adjust.GetMaterialAdjustDataRequest.lan', index=7,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='adjust.GetMaterialAdjustDataRequest.include_total', index=8,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8578,
  serialized_end=8832,
)


_GETMATERIALADJUSTDATARESPONSE = _descriptor.Descriptor(
  name='GetMaterialAdjustDataResponse',
  full_name='adjust.GetMaterialAdjustDataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='adjust.GetMaterialAdjustDataResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='adjust.GetMaterialAdjustDataResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8834,
  serialized_end=8936,
)


_ADJUSTRESPONSE = _descriptor.Descriptor(
  name='AdjustResponse',
  full_name='adjust.AdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='adjust.AdjustResponse.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='adjust.AdjustResponse.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='adjust.AdjustResponse.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='adjust.AdjustResponse.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='adjust.AdjustResponse.category_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='adjust.AdjustResponse.category_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='adjust.AdjustResponse.product_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='adjust.AdjustResponse.product_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='adjust.AdjustResponse.product_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='adjust.AdjustResponse.quantity', index=9,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='adjust.AdjustResponse.store_code', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='adjust.AdjustResponse.store_id', index=11,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='adjust.AdjustResponse.store_name', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='adjust.AdjustResponse.unit_id', index=13,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='adjust.AdjustResponse.unit_name', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='adjust.AdjustResponse.reason_type', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='adjust.AdjustResponse.bom_product_id', index=16,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_code', full_name='adjust.AdjustResponse.bom_product_code', index=17,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_name', full_name='adjust.AdjustResponse.bom_product_name', index=18,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='adjust.AdjustResponse.qty', index=19,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_id', full_name='adjust.AdjustResponse.bom_unit_id', index=20,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_code', full_name='adjust.AdjustResponse.bom_unit_code', index=21,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_name', full_name='adjust.AdjustResponse.bom_unit_name', index=22,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_id', full_name='adjust.AdjustResponse.bom_accounting_unit_id', index=23,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_code', full_name='adjust.AdjustResponse.bom_accounting_unit_code', index=24,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_name', full_name='adjust.AdjustResponse.bom_accounting_unit_name', index=25,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_qty', full_name='adjust.AdjustResponse.accounting_qty', index=26,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='adjust.AdjustResponse.price', index=27,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost', full_name='adjust.AdjustResponse.cost', index=28,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='adjust.AdjustResponse.adjust_date', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8939,
  serialized_end=9636,
)


_CREATEDADJUSTPRODUCTBYCODE = _descriptor.Descriptor(
  name='CreatedAdjustProductByCode',
  full_name='adjust.CreatedAdjustProductByCode',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='adjust.CreatedAdjustProductByCode.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='adjust.CreatedAdjustProductByCode.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_code', full_name='adjust.CreatedAdjustProductByCode.unit_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='adjust.CreatedAdjustProductByCode.quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='adjust.CreatedAdjustProductByCode.reason_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='skuRemark', full_name='adjust.CreatedAdjustProductByCode.skuRemark', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9639,
  serialized_end=9797,
)


_SKUREMARK_TAG = _descriptor.Descriptor(
  name='Tag',
  full_name='adjust.SkuRemark.Tag',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='adjust.SkuRemark.Tag.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='adjust.SkuRemark.Tag.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='adjust.SkuRemark.Tag.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9889,
  serialized_end=9934,
)

_SKUREMARK = _descriptor.Descriptor(
  name='SkuRemark',
  full_name='adjust.SkuRemark',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='adjust.SkuRemark.name', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='values', full_name='adjust.SkuRemark.values', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_SKUREMARK_TAG, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9800,
  serialized_end=9934,
)


_CREATEDADJUSTBYCODEREQUEST = _descriptor.Descriptor(
  name='CreatedAdjustByCodeRequest',
  full_name='adjust.CreatedAdjustByCodeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_store', full_name='adjust.CreatedAdjustByCodeRequest.adjust_store', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='adjust.CreatedAdjustByCodeRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='adjust.CreatedAdjustByCodeRequest.reason_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='adjust.CreatedAdjustByCodeRequest.remark', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='adjust.CreatedAdjustByCodeRequest.request_id', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='adjust.CreatedAdjustByCodeRequest.adjust_date', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='adjust.CreatedAdjustByCodeRequest.lan', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9937,
  serialized_end=10132,
)


_ADJUSTPOSITIONPRODUCTS = _descriptor.Descriptor(
  name='AdjustPositionProducts',
  full_name='adjust.AdjustPositionProducts',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='position_id', full_name='adjust.AdjustPositionProducts.position_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='adjust.AdjustPositionProducts.position_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='adjust.AdjustPositionProducts.position_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='adjust.AdjustPositionProducts.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='adjust.AdjustPositionProducts.total', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10135,
  serialized_end=10282,
)

_CREATEADJUSTREQUEST.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEADJUSTRESPONSE.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTREQUEST.fields_by_name['status'].enum_type = _GETADJUSTREQUEST_STATUS
_GETADJUSTREQUEST_STATUS.containing_type = _GETADJUSTREQUEST
_ADJUST.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUST.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUST.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_GETADJUSTRESPONSE.fields_by_name['rows'].message_type = _ADJUST
_ADJUSTPRODUCT.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUSTPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUSTPRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTPRODUCTRESPONSE.fields_by_name['rows'].message_type = _ADJUSTPRODUCT
_GETADJUSTPRODUCTRESPONSE.fields_by_name['position_rows'].message_type = _ADJUSTPOSITIONPRODUCTS
_GETADJUSTPRODUCTBYSTOREIDREQUEST.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEADJUSTPRODUCT.fields_by_name['units'].message_type = _CREATEADJUSTPRODUCTUINT
_GETADJUSTPRODUCTBYSTOREIDRESPONSE.fields_by_name['rows'].message_type = _CREATEADJUSTPRODUCT
_GETADJUSTPRODUCTBYSTOREIDRESPONSE.fields_by_name['inventory_unchanged_rows'].message_type = _CREATEADJUSTPRODUCT
_CREATEDADJUSTPRODUCT.fields_by_name['skuRemark'].message_type = _SKUREMARK
_CREATEDADJUSTREQUEST.fields_by_name['products'].message_type = _CREATEDADJUSTPRODUCT
_CREATEDADJUSTREQUEST.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATEADJUSTREQUEST.fields_by_name['products'].message_type = _CREATEDADJUSTPRODUCT
_UPDATEADJUSTREQUEST.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTBICOLLECTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTBICOLLECTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUSTBICOLLECTRESPONSE.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTBICOLLECTRESPONSE.fields_by_name['rows'].message_type = _ADJUSTBICOLLECTRESPONSE
_GETADJUSTBICOLLECTRESPONSE.fields_by_name['total'].message_type = _AD_TOTAL
_GETADJUSTCOLLECTDETAILEDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTCOLLECTDETAILEDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUSTCOLLECTDETAILED.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTCOLLECTDETAILEDRESPONSE.fields_by_name['rows'].message_type = _ADJUSTCOLLECTDETAILED
_GETADJUSTCOLLECTDETAILEDRESPONSE.fields_by_name['total'].message_type = _AD_TOTAL
_GETMATERIALADJUSTDATAREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETMATERIALADJUSTDATAREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETMATERIALADJUSTDATARESPONSE.fields_by_name['rows'].message_type = _ADJUSTRESPONSE
_GETMATERIALADJUSTDATARESPONSE.fields_by_name['total'].message_type = _AD_TOTAL
_CREATEDADJUSTPRODUCTBYCODE.fields_by_name['skuRemark'].message_type = _SKUREMARK
_SKUREMARK_TAG.containing_type = _SKUREMARK
_SKUREMARK.fields_by_name['name'].message_type = _SKUREMARK_TAG
_SKUREMARK.fields_by_name['values'].message_type = _SKUREMARK_TAG
_CREATEDADJUSTBYCODEREQUEST.fields_by_name['products'].message_type = _CREATEDADJUSTPRODUCTBYCODE
_ADJUSTPOSITIONPRODUCTS.fields_by_name['products'].message_type = _ADJUSTPRODUCT
DESCRIPTOR.message_types_by_name['Pong'] = _PONG
DESCRIPTOR.message_types_by_name['CreateAdjustRequest'] = _CREATEADJUSTREQUEST
DESCRIPTOR.message_types_by_name['CreateAdjustResponse'] = _CREATEADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustRequest'] = _GETADJUSTREQUEST
DESCRIPTOR.message_types_by_name['Adjust'] = _ADJUST
DESCRIPTOR.message_types_by_name['GetAdjustResponse'] = _GETADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustProductRequest'] = _GETADJUSTPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['AdjustProduct'] = _ADJUSTPRODUCT
DESCRIPTOR.message_types_by_name['GetAdjustProductResponse'] = _GETADJUSTPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustByIDRequest'] = _GETADJUSTBYIDREQUEST
DESCRIPTOR.message_types_by_name['ConfirmAdjustRequest'] = _CONFIRMADJUSTREQUEST
DESCRIPTOR.message_types_by_name['ConfirmAdjustResponse'] = _CONFIRMADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['SubmitAdjustRequest'] = _SUBMITADJUSTREQUEST
DESCRIPTOR.message_types_by_name['SubmitAdjustResponse'] = _SUBMITADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['ApproveAdjustRequest'] = _APPROVEADJUSTREQUEST
DESCRIPTOR.message_types_by_name['ApproveAdjustResponse'] = _APPROVEADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['RejectAdjustRequest'] = _REJECTADJUSTREQUEST
DESCRIPTOR.message_types_by_name['RejectAdjustResponse'] = _REJECTADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustProductByStoreIDRequest'] = _GETADJUSTPRODUCTBYSTOREIDREQUEST
DESCRIPTOR.message_types_by_name['CreateAdjustProductUint'] = _CREATEADJUSTPRODUCTUINT
DESCRIPTOR.message_types_by_name['CreateAdjustProduct'] = _CREATEADJUSTPRODUCT
DESCRIPTOR.message_types_by_name['GetAdjustProductByStoreIDResponse'] = _GETADJUSTPRODUCTBYSTOREIDRESPONSE
DESCRIPTOR.message_types_by_name['CreatedAdjustProduct'] = _CREATEDADJUSTPRODUCT
DESCRIPTOR.message_types_by_name['CreatedAdjustRequest'] = _CREATEDADJUSTREQUEST
DESCRIPTOR.message_types_by_name['Attachments'] = _ATTACHMENTS
DESCRIPTOR.message_types_by_name['UpdateAdjustRequest'] = _UPDATEADJUSTREQUEST
DESCRIPTOR.message_types_by_name['DeleteAdjustRequest'] = _DELETEADJUSTREQUEST
DESCRIPTOR.message_types_by_name['DeleteAdjustResponse'] = _DELETEADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['DeleteAdjustProductRequest'] = _DELETEADJUSTPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['DeleteAdjustProductResponse'] = _DELETEADJUSTPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustBiCollectRequest'] = _GETADJUSTBICOLLECTREQUEST
DESCRIPTOR.message_types_by_name['AdjustBiCollectResponse'] = _ADJUSTBICOLLECTRESPONSE
DESCRIPTOR.message_types_by_name['AD_total'] = _AD_TOTAL
DESCRIPTOR.message_types_by_name['GetAdjustBiCollectResponse'] = _GETADJUSTBICOLLECTRESPONSE
DESCRIPTOR.message_types_by_name['CancelAdjustRequest'] = _CANCELADJUSTREQUEST
DESCRIPTOR.message_types_by_name['CancelAdjustResponse'] = _CANCELADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustCollectDetailedRequest'] = _GETADJUSTCOLLECTDETAILEDREQUEST
DESCRIPTOR.message_types_by_name['AdjustCollectDetailed'] = _ADJUSTCOLLECTDETAILED
DESCRIPTOR.message_types_by_name['GetAdjustCollectDetailedResponse'] = _GETADJUSTCOLLECTDETAILEDRESPONSE
DESCRIPTOR.message_types_by_name['AutoCloseCreatedAdjustResponse'] = _AUTOCLOSECREATEDADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['AutoCloseCreatedAdjustRequest'] = _AUTOCLOSECREATEDADJUSTREQUEST
DESCRIPTOR.message_types_by_name['GetMaterialAdjustDataRequest'] = _GETMATERIALADJUSTDATAREQUEST
DESCRIPTOR.message_types_by_name['GetMaterialAdjustDataResponse'] = _GETMATERIALADJUSTDATARESPONSE
DESCRIPTOR.message_types_by_name['AdjustResponse'] = _ADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['CreatedAdjustProductByCode'] = _CREATEDADJUSTPRODUCTBYCODE
DESCRIPTOR.message_types_by_name['SkuRemark'] = _SKUREMARK
DESCRIPTOR.message_types_by_name['CreatedAdjustByCodeRequest'] = _CREATEDADJUSTBYCODEREQUEST
DESCRIPTOR.message_types_by_name['AdjustPositionProducts'] = _ADJUSTPOSITIONPRODUCTS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Pong = _reflection.GeneratedProtocolMessageType('Pong', (_message.Message,), dict(
  DESCRIPTOR = _PONG,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.Pong)
  ))
_sym_db.RegisterMessage(Pong)

CreateAdjustRequest = _reflection.GeneratedProtocolMessageType('CreateAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEADJUSTREQUEST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.CreateAdjustRequest)
  ))
_sym_db.RegisterMessage(CreateAdjustRequest)

CreateAdjustResponse = _reflection.GeneratedProtocolMessageType('CreateAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEADJUSTRESPONSE,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.CreateAdjustResponse)
  ))
_sym_db.RegisterMessage(CreateAdjustResponse)

GetAdjustRequest = _reflection.GeneratedProtocolMessageType('GetAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTREQUEST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.GetAdjustRequest)
  ))
_sym_db.RegisterMessage(GetAdjustRequest)

Adjust = _reflection.GeneratedProtocolMessageType('Adjust', (_message.Message,), dict(
  DESCRIPTOR = _ADJUST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.Adjust)
  ))
_sym_db.RegisterMessage(Adjust)

GetAdjustResponse = _reflection.GeneratedProtocolMessageType('GetAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTRESPONSE,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.GetAdjustResponse)
  ))
_sym_db.RegisterMessage(GetAdjustResponse)

GetAdjustProductRequest = _reflection.GeneratedProtocolMessageType('GetAdjustProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTPRODUCTREQUEST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.GetAdjustProductRequest)
  ))
_sym_db.RegisterMessage(GetAdjustProductRequest)

AdjustProduct = _reflection.GeneratedProtocolMessageType('AdjustProduct', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTPRODUCT,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.AdjustProduct)
  ))
_sym_db.RegisterMessage(AdjustProduct)

GetAdjustProductResponse = _reflection.GeneratedProtocolMessageType('GetAdjustProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTPRODUCTRESPONSE,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.GetAdjustProductResponse)
  ))
_sym_db.RegisterMessage(GetAdjustProductResponse)

GetAdjustByIDRequest = _reflection.GeneratedProtocolMessageType('GetAdjustByIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTBYIDREQUEST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.GetAdjustByIDRequest)
  ))
_sym_db.RegisterMessage(GetAdjustByIDRequest)

ConfirmAdjustRequest = _reflection.GeneratedProtocolMessageType('ConfirmAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMADJUSTREQUEST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.ConfirmAdjustRequest)
  ))
_sym_db.RegisterMessage(ConfirmAdjustRequest)

ConfirmAdjustResponse = _reflection.GeneratedProtocolMessageType('ConfirmAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMADJUSTRESPONSE,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.ConfirmAdjustResponse)
  ))
_sym_db.RegisterMessage(ConfirmAdjustResponse)

SubmitAdjustRequest = _reflection.GeneratedProtocolMessageType('SubmitAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITADJUSTREQUEST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.SubmitAdjustRequest)
  ))
_sym_db.RegisterMessage(SubmitAdjustRequest)

SubmitAdjustResponse = _reflection.GeneratedProtocolMessageType('SubmitAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITADJUSTRESPONSE,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.SubmitAdjustResponse)
  ))
_sym_db.RegisterMessage(SubmitAdjustResponse)

ApproveAdjustRequest = _reflection.GeneratedProtocolMessageType('ApproveAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _APPROVEADJUSTREQUEST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.ApproveAdjustRequest)
  ))
_sym_db.RegisterMessage(ApproveAdjustRequest)

ApproveAdjustResponse = _reflection.GeneratedProtocolMessageType('ApproveAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _APPROVEADJUSTRESPONSE,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.ApproveAdjustResponse)
  ))
_sym_db.RegisterMessage(ApproveAdjustResponse)

RejectAdjustRequest = _reflection.GeneratedProtocolMessageType('RejectAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _REJECTADJUSTREQUEST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.RejectAdjustRequest)
  ))
_sym_db.RegisterMessage(RejectAdjustRequest)

RejectAdjustResponse = _reflection.GeneratedProtocolMessageType('RejectAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _REJECTADJUSTRESPONSE,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.RejectAdjustResponse)
  ))
_sym_db.RegisterMessage(RejectAdjustResponse)

GetAdjustProductByStoreIDRequest = _reflection.GeneratedProtocolMessageType('GetAdjustProductByStoreIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTPRODUCTBYSTOREIDREQUEST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.GetAdjustProductByStoreIDRequest)
  ))
_sym_db.RegisterMessage(GetAdjustProductByStoreIDRequest)

CreateAdjustProductUint = _reflection.GeneratedProtocolMessageType('CreateAdjustProductUint', (_message.Message,), dict(
  DESCRIPTOR = _CREATEADJUSTPRODUCTUINT,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.CreateAdjustProductUint)
  ))
_sym_db.RegisterMessage(CreateAdjustProductUint)

CreateAdjustProduct = _reflection.GeneratedProtocolMessageType('CreateAdjustProduct', (_message.Message,), dict(
  DESCRIPTOR = _CREATEADJUSTPRODUCT,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.CreateAdjustProduct)
  ))
_sym_db.RegisterMessage(CreateAdjustProduct)

GetAdjustProductByStoreIDResponse = _reflection.GeneratedProtocolMessageType('GetAdjustProductByStoreIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTPRODUCTBYSTOREIDRESPONSE,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.GetAdjustProductByStoreIDResponse)
  ))
_sym_db.RegisterMessage(GetAdjustProductByStoreIDResponse)

CreatedAdjustProduct = _reflection.GeneratedProtocolMessageType('CreatedAdjustProduct', (_message.Message,), dict(
  DESCRIPTOR = _CREATEDADJUSTPRODUCT,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.CreatedAdjustProduct)
  ))
_sym_db.RegisterMessage(CreatedAdjustProduct)

CreatedAdjustRequest = _reflection.GeneratedProtocolMessageType('CreatedAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEDADJUSTREQUEST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.CreatedAdjustRequest)
  ))
_sym_db.RegisterMessage(CreatedAdjustRequest)

Attachments = _reflection.GeneratedProtocolMessageType('Attachments', (_message.Message,), dict(
  DESCRIPTOR = _ATTACHMENTS,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.Attachments)
  ))
_sym_db.RegisterMessage(Attachments)

UpdateAdjustRequest = _reflection.GeneratedProtocolMessageType('UpdateAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEADJUSTREQUEST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.UpdateAdjustRequest)
  ))
_sym_db.RegisterMessage(UpdateAdjustRequest)

DeleteAdjustRequest = _reflection.GeneratedProtocolMessageType('DeleteAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEADJUSTREQUEST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.DeleteAdjustRequest)
  ))
_sym_db.RegisterMessage(DeleteAdjustRequest)

DeleteAdjustResponse = _reflection.GeneratedProtocolMessageType('DeleteAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETEADJUSTRESPONSE,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.DeleteAdjustResponse)
  ))
_sym_db.RegisterMessage(DeleteAdjustResponse)

DeleteAdjustProductRequest = _reflection.GeneratedProtocolMessageType('DeleteAdjustProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEADJUSTPRODUCTREQUEST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.DeleteAdjustProductRequest)
  ))
_sym_db.RegisterMessage(DeleteAdjustProductRequest)

DeleteAdjustProductResponse = _reflection.GeneratedProtocolMessageType('DeleteAdjustProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETEADJUSTPRODUCTRESPONSE,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.DeleteAdjustProductResponse)
  ))
_sym_db.RegisterMessage(DeleteAdjustProductResponse)

GetAdjustBiCollectRequest = _reflection.GeneratedProtocolMessageType('GetAdjustBiCollectRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTBICOLLECTREQUEST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.GetAdjustBiCollectRequest)
  ))
_sym_db.RegisterMessage(GetAdjustBiCollectRequest)

AdjustBiCollectResponse = _reflection.GeneratedProtocolMessageType('AdjustBiCollectResponse', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTBICOLLECTRESPONSE,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.AdjustBiCollectResponse)
  ))
_sym_db.RegisterMessage(AdjustBiCollectResponse)

AD_total = _reflection.GeneratedProtocolMessageType('AD_total', (_message.Message,), dict(
  DESCRIPTOR = _AD_TOTAL,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.AD_total)
  ))
_sym_db.RegisterMessage(AD_total)

GetAdjustBiCollectResponse = _reflection.GeneratedProtocolMessageType('GetAdjustBiCollectResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTBICOLLECTRESPONSE,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.GetAdjustBiCollectResponse)
  ))
_sym_db.RegisterMessage(GetAdjustBiCollectResponse)

CancelAdjustRequest = _reflection.GeneratedProtocolMessageType('CancelAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _CANCELADJUSTREQUEST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.CancelAdjustRequest)
  ))
_sym_db.RegisterMessage(CancelAdjustRequest)

CancelAdjustResponse = _reflection.GeneratedProtocolMessageType('CancelAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _CANCELADJUSTRESPONSE,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.CancelAdjustResponse)
  ))
_sym_db.RegisterMessage(CancelAdjustResponse)

GetAdjustCollectDetailedRequest = _reflection.GeneratedProtocolMessageType('GetAdjustCollectDetailedRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTCOLLECTDETAILEDREQUEST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.GetAdjustCollectDetailedRequest)
  ))
_sym_db.RegisterMessage(GetAdjustCollectDetailedRequest)

AdjustCollectDetailed = _reflection.GeneratedProtocolMessageType('AdjustCollectDetailed', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTCOLLECTDETAILED,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.AdjustCollectDetailed)
  ))
_sym_db.RegisterMessage(AdjustCollectDetailed)

GetAdjustCollectDetailedResponse = _reflection.GeneratedProtocolMessageType('GetAdjustCollectDetailedResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTCOLLECTDETAILEDRESPONSE,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.GetAdjustCollectDetailedResponse)
  ))
_sym_db.RegisterMessage(GetAdjustCollectDetailedResponse)

AutoCloseCreatedAdjustResponse = _reflection.GeneratedProtocolMessageType('AutoCloseCreatedAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _AUTOCLOSECREATEDADJUSTRESPONSE,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.AutoCloseCreatedAdjustResponse)
  ))
_sym_db.RegisterMessage(AutoCloseCreatedAdjustResponse)

AutoCloseCreatedAdjustRequest = _reflection.GeneratedProtocolMessageType('AutoCloseCreatedAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _AUTOCLOSECREATEDADJUSTREQUEST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.AutoCloseCreatedAdjustRequest)
  ))
_sym_db.RegisterMessage(AutoCloseCreatedAdjustRequest)

GetMaterialAdjustDataRequest = _reflection.GeneratedProtocolMessageType('GetMaterialAdjustDataRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETMATERIALADJUSTDATAREQUEST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.GetMaterialAdjustDataRequest)
  ))
_sym_db.RegisterMessage(GetMaterialAdjustDataRequest)

GetMaterialAdjustDataResponse = _reflection.GeneratedProtocolMessageType('GetMaterialAdjustDataResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETMATERIALADJUSTDATARESPONSE,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.GetMaterialAdjustDataResponse)
  ))
_sym_db.RegisterMessage(GetMaterialAdjustDataResponse)

AdjustResponse = _reflection.GeneratedProtocolMessageType('AdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTRESPONSE,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.AdjustResponse)
  ))
_sym_db.RegisterMessage(AdjustResponse)

CreatedAdjustProductByCode = _reflection.GeneratedProtocolMessageType('CreatedAdjustProductByCode', (_message.Message,), dict(
  DESCRIPTOR = _CREATEDADJUSTPRODUCTBYCODE,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.CreatedAdjustProductByCode)
  ))
_sym_db.RegisterMessage(CreatedAdjustProductByCode)

SkuRemark = _reflection.GeneratedProtocolMessageType('SkuRemark', (_message.Message,), dict(

  Tag = _reflection.GeneratedProtocolMessageType('Tag', (_message.Message,), dict(
    DESCRIPTOR = _SKUREMARK_TAG,
    __module__ = 'adjust_pb2'
    # @@protoc_insertion_point(class_scope:adjust.SkuRemark.Tag)
    ))
  ,
  DESCRIPTOR = _SKUREMARK,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.SkuRemark)
  ))
_sym_db.RegisterMessage(SkuRemark)
_sym_db.RegisterMessage(SkuRemark.Tag)

CreatedAdjustByCodeRequest = _reflection.GeneratedProtocolMessageType('CreatedAdjustByCodeRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEDADJUSTBYCODEREQUEST,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.CreatedAdjustByCodeRequest)
  ))
_sym_db.RegisterMessage(CreatedAdjustByCodeRequest)

AdjustPositionProducts = _reflection.GeneratedProtocolMessageType('AdjustPositionProducts', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTPOSITIONPRODUCTS,
  __module__ = 'adjust_pb2'
  # @@protoc_insertion_point(class_scope:adjust.AdjustPositionProducts)
  ))
_sym_db.RegisterMessage(AdjustPositionProducts)



_ADJUST = _descriptor.ServiceDescriptor(
  name='adjust',
  full_name='adjust.adjust',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=10285,
  serialized_end=12725,
  methods=[
  _descriptor.MethodDescriptor(
    name='Ping',
    full_name='adjust.adjust.Ping',
    index=0,
    containing_service=None,
    input_type=google_dot_protobuf_dot_empty__pb2._EMPTY,
    output_type=_PONG,
    serialized_options=_b('\202\323\344\223\002\025\022\023/api/v2/supply/ping'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjust',
    full_name='adjust.adjust.GetAdjust',
    index=1,
    containing_service=None,
    input_type=_GETADJUSTREQUEST,
    output_type=_GETADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\002\027\022\025/api/v2/supply/adjust'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustProduct',
    full_name='adjust.adjust.GetAdjustProduct',
    index=2,
    containing_service=None,
    input_type=_GETADJUSTPRODUCTREQUEST,
    output_type=_GETADJUSTPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\002+\022)/api/v2/supply/adjust/{adjust_id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustByID',
    full_name='adjust.adjust.GetAdjustByID',
    index=3,
    containing_service=None,
    input_type=_GETADJUSTBYIDREQUEST,
    output_type=_ADJUST,
    serialized_options=_b('\202\323\344\223\002#\022!/api/v2/supply/adjust/{adjust_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmAdjust',
    full_name='adjust.adjust.ConfirmAdjust',
    index=4,
    containing_service=None,
    input_type=_CONFIRMADJUSTREQUEST,
    output_type=_CONFIRMADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\032)/api/v2/supply/adjust/{adjust_id}/confirm:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='SubmitAdjust',
    full_name='adjust.adjust.SubmitAdjust',
    index=5,
    containing_service=None,
    input_type=_SUBMITADJUSTREQUEST,
    output_type=_SUBMITADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\002-\032(/api/v2/supply/adjust/{adjust_id}/submit:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ApproveAdjust',
    full_name='adjust.adjust.ApproveAdjust',
    index=6,
    containing_service=None,
    input_type=_APPROVEADJUSTREQUEST,
    output_type=_APPROVEADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\002.\032)/api/v2/supply/adjust/{adjust_id}/approve:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='RejectAdjust',
    full_name='adjust.adjust.RejectAdjust',
    index=7,
    containing_service=None,
    input_type=_REJECTADJUSTREQUEST,
    output_type=_REJECTADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\002-\032(/api/v2/supply/adjust/{adjust_id}/reject:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustProductByStoreID',
    full_name='adjust.adjust.GetAdjustProductByStoreID',
    index=8,
    containing_service=None,
    input_type=_GETADJUSTPRODUCTBYSTOREIDREQUEST,
    output_type=_GETADJUSTPRODUCTBYSTOREIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\022$/api/v2/supply/adjust/store/products'),
  ),
  _descriptor.MethodDescriptor(
    name='CreatedAdjust',
    full_name='adjust.adjust.CreatedAdjust',
    index=9,
    containing_service=None,
    input_type=_CREATEDADJUSTREQUEST,
    output_type=_ADJUST,
    serialized_options=_b('\202\323\344\223\002!\"\034/api/v2/supply/adjust/create:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateAdjust',
    full_name='adjust.adjust.UpdateAdjust',
    index=10,
    containing_service=None,
    input_type=_UPDATEADJUSTREQUEST,
    output_type=_ADJUST,
    serialized_options=_b('\202\323\344\223\002-\032(/api/v2/supply/adjust/{adjust_id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteAdjust',
    full_name='adjust.adjust.DeleteAdjust',
    index=11,
    containing_service=None,
    input_type=_DELETEADJUSTREQUEST,
    output_type=_DELETEADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\002-\032(/api/v2/supply/adjust/{adjust_id}/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteAdjustProduct',
    full_name='adjust.adjust.DeleteAdjustProduct',
    index=12,
    containing_service=None,
    input_type=_DELETEADJUSTPRODUCTREQUEST,
    output_type=_DELETEADJUSTPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\0320/api/v2/supply/adjust/product/{adjust_id}/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustBiCollect',
    full_name='adjust.adjust.GetAdjustBiCollect',
    index=13,
    containing_service=None,
    input_type=_GETADJUSTBICOLLECTREQUEST,
    output_type=_GETADJUSTBICOLLECTRESPONSE,
    serialized_options=_b('\202\323\344\223\002\"\022 /api/v2/supply/adjust/bi/collect'),
  ),
  _descriptor.MethodDescriptor(
    name='CancelAdjust',
    full_name='adjust.adjust.CancelAdjust',
    index=14,
    containing_service=None,
    input_type=_CANCELADJUSTREQUEST,
    output_type=_CANCELADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\002-\032(/api/v2/supply/adjust/auto_create/cancel:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustCollectDetailed',
    full_name='adjust.adjust.GetAdjustCollectDetailed',
    index=15,
    containing_service=None,
    input_type=_GETADJUSTCOLLECTDETAILEDREQUEST,
    output_type=_GETADJUSTCOLLECTDETAILEDRESPONSE,
    serialized_options=_b('\202\323\344\223\002#\022!/api/v2/supply/adjust/bi/detailed'),
  ),
  _descriptor.MethodDescriptor(
    name='AutoCloseCreatedAdjust',
    full_name='adjust.adjust.AutoCloseCreatedAdjust',
    index=16,
    containing_service=None,
    input_type=_AUTOCLOSECREATEDADJUSTREQUEST,
    output_type=_AUTOCLOSECREATEDADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\002\"\022 /api/v2/supply/adjust/close/auto'),
  ),
  _descriptor.MethodDescriptor(
    name='GetMaterialAdjustData',
    full_name='adjust.adjust.GetMaterialAdjustData',
    index=17,
    containing_service=None,
    input_type=_GETMATERIALADJUSTDATAREQUEST,
    output_type=_GETMATERIALADJUSTDATARESPONSE,
    serialized_options=_b('\202\323\344\223\002%\022#/api/v2/supply/adjust/material/cost'),
  ),
  _descriptor.MethodDescriptor(
    name='CreatedAdjustByCode',
    full_name='adjust.adjust.CreatedAdjustByCode',
    index=18,
    containing_service=None,
    input_type=_CREATEDADJUSTBYCODEREQUEST,
    output_type=_ADJUST,
    serialized_options=_b('\202\323\344\223\002\036\"\031/api/v2/supply/adjust/pos:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_ADJUST)

DESCRIPTOR.services_by_name['adjust'] = _ADJUST

# @@protoc_insertion_point(module_scope)
