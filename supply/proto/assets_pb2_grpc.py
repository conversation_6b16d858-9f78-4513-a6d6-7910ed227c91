# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import assets_pb2 as assets__pb2


class AssetsRecServiceStub(object):
  """固定资产收货相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateAssetsReceiving = channel.unary_unary(
        '/receiving.AssetsRecService/CreateAssetsReceiving',
        request_serializer=assets__pb2.CreateAssetsReceivingRequest.SerializeToString,
        response_deserializer=assets__pb2.CreateAssetsReceivingResponse.FromString,
        )
    self.ConfirmAssetsReceiving = channel.unary_unary(
        '/receiving.AssetsRecService/ConfirmAssetsReceiving',
        request_serializer=assets__pb2.ConfirmAssetsReceivingRequest.SerializeToString,
        response_deserializer=assets__pb2.ConfirmAssetsReceivingResponse.FromString,
        )
    self.ListAssetsReceiving = channel.unary_unary(
        '/receiving.AssetsRecService/ListAssetsReceiving',
        request_serializer=assets__pb2.ListAssetsReceivingRequest.SerializeToString,
        response_deserializer=assets__pb2.ListAssetsReceivingResponse.FromString,
        )
    self.GetAssetById = channel.unary_unary(
        '/receiving.AssetsRecService/GetAssetById',
        request_serializer=assets__pb2.GetAssetByIdRequest.SerializeToString,
        response_deserializer=assets__pb2.Assets.FromString,
        )
    self.ListAssetProductsById = channel.unary_unary(
        '/receiving.AssetsRecService/ListAssetProductsById',
        request_serializer=assets__pb2.ListAssetProductsByIdRequest.SerializeToString,
        response_deserializer=assets__pb2.ListAssetProductsByIdResponse.FromString,
        )


class AssetsRecServiceServicer(object):
  """固定资产收货相关服务
  """

  def CreateAssetsReceiving(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ConfirmAssetsReceiving(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListAssetsReceiving(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetAssetById(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListAssetProductsById(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_AssetsRecServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateAssetsReceiving': grpc.unary_unary_rpc_method_handler(
          servicer.CreateAssetsReceiving,
          request_deserializer=assets__pb2.CreateAssetsReceivingRequest.FromString,
          response_serializer=assets__pb2.CreateAssetsReceivingResponse.SerializeToString,
      ),
      'ConfirmAssetsReceiving': grpc.unary_unary_rpc_method_handler(
          servicer.ConfirmAssetsReceiving,
          request_deserializer=assets__pb2.ConfirmAssetsReceivingRequest.FromString,
          response_serializer=assets__pb2.ConfirmAssetsReceivingResponse.SerializeToString,
      ),
      'ListAssetsReceiving': grpc.unary_unary_rpc_method_handler(
          servicer.ListAssetsReceiving,
          request_deserializer=assets__pb2.ListAssetsReceivingRequest.FromString,
          response_serializer=assets__pb2.ListAssetsReceivingResponse.SerializeToString,
      ),
      'GetAssetById': grpc.unary_unary_rpc_method_handler(
          servicer.GetAssetById,
          request_deserializer=assets__pb2.GetAssetByIdRequest.FromString,
          response_serializer=assets__pb2.Assets.SerializeToString,
      ),
      'ListAssetProductsById': grpc.unary_unary_rpc_method_handler(
          servicer.ListAssetProductsById,
          request_deserializer=assets__pb2.ListAssetProductsByIdRequest.FromString,
          response_serializer=assets__pb2.ListAssetProductsByIdResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'receiving.AssetsRecService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
