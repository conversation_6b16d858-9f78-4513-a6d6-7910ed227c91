syntax = "proto3";

package material_convert;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";


service MaterialConvert{
    // 创建物料转换单
    rpc CreateMaterialConvert (CreateMaterialConvertRequest) returns (CreateMaterialConvertResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/material/conversion"
        body: "*"
        };
    }
    // 物料转换单列表查询
    rpc ListMaterialConvert (ListMaterialConvertRequest) returns (ListMaterialConvertResponse){
        option (google.api.http) = {
        get: "/api/v2/supply/material/conversion/list"
        };
    }
    // 查询物料转换单详情
    rpc GetMaterialConvertDetail (GetMaterialConvertDetailRequest) returns (GetMaterialConvertDetailResponse){
        option (google.api.http) = {
        get: "/api/v2/supply/material/conversion/{convert_id}"
        };
    }
    // 更新物料转换单
    rpc UpdateMaterialConvert (UpdateMaterialConvertRequest) returns (UpdateMaterialConvertResponse){
        option (google.api.http) = {
        put: "/api/v2/supply/material/conversion/{convert_id}"
        body: "*"
        };
    }
}

message CreateMaterialConvertRequest{
    // 区分门店/仓库/加工中心：STORE/WAREHOUSE/MACHINING_CENTER
    string branch_type = 2;
    // 订单状态:
    //  'INITED'      新建
    //  'CONFIRMED'   已确认
    //  'INVALID'     已作废
    string status = 4;
    // 转换类型（自动AUTO、手动MANUAL）
    string convert_type = 5;
    // 物料转换日期
    google.protobuf.Timestamp convert_date = 6;
    // 门店/仓库/加工中心id
    uint64 branch_id = 7;
    // 门店/仓库/加工中心code
    string branch_code = 8;
    // 门店/仓库/加工中心名称
    string branch_name = 9;
    // 仓位id
    uint64 position_id = 10;
    // 仓位名称
    string position_name = 11;
    // 仓位code
    string position_code = 12;
    // 转换规则ID
    uint64 convert_rule = 13;
    // 是否开启多仓位
    bool opened_position = 14;
    // 备注
    string remark = 15;
    // 是否自动确认(用于自动物料转换前端手动不需要传)
    bool auto_confirm = 16;
    // 幂等性校验请求id
    uint64 request_id = 20;
    repeated Material materials = 21;
    // 成本中心ID
    uint64 cost_center_id = 30;
}
message CreateMaterialConvertResponse{
    uint64 convert_id = 1;
    string result = 2;
}

message ListMaterialConvertRequest{
    // 开始转换日期
    google.protobuf.Timestamp start_date = 1;
    // 结束转换日期
    google.protobuf.Timestamp end_date = 2;
    // 门店/仓库/加工中心ID列表
    repeated uint64 branch_ids = 3;
    // 区分门店/仓库/加工中心：STORE/WAREHOUSE/MACHINING_CENTER
    string branch_type = 4;
    // 单号
    string code = 5;
    // 转换规则ID
    repeated uint64 convert_rules = 6;
    // 仓位ID列表
    repeated uint64 position_ids = 7;
    // 转换类型（自动AUTO、手动MANUAL、全部ALL）
    string convert_type = 8;
    // 订单状态 'ALL'全部/'INITED'新建'/CONFIRMED'已确认/'INVALID'已作废
    string status = 9;
    int64 limit = 10;
    uint64 offset = 11;
    bool include_total = 12;
    // 排序方式  asc or desc
    string order = 13;
    // 排序字段
    string sort = 14;
}

message Material{
    uint64 id = 1;
    // 关联的物料转换id
    uint64 main_id = 2;
    // 物料/商品id
    uint64 product_id = 3;
    // 商品code
    string product_code = 4;
    // 商品名称
    string product_name = 5;
    // 商品类型
    string product_type = 6;
    // 单位id
    uint64 unit_id = 7;
    // 单位名称
    string unit_name = 8;
    // 单位规格
    string unit_spec = 9;
    // 物料数量
    double quantity = 10;
    // 物料类型(原始物料origin/目标物料target)
    string type = 11;
    // 单位转换率
    double unit_rate = 12;
    uint64 partner_id = 16;
    uint64 created_by = 17;
    string created_name = 18;
    uint64 updated_by = 19;
    string updated_name = 20;
    google.protobuf.Timestamp created_at = 21;
    google.protobuf.Timestamp updated_at = 22;
}

message MaterialDetail{
    uint64 id = 1;
    string branch_type = 2;
    string code = 3;
    // 订单状态
    string status = 4;
    // 转换类型（自动AUTO、手动MANUAL）
    string convert_type = 5;
    // 物料转换日期
    google.protobuf.Timestamp convert_date = 6;
    // 门店/仓库/加工中心id
    uint64 branch_id = 7;
    // 门店/仓库/加工中心code
    string branch_code = 8;
    // 门店/仓库/加工中心名称
    string branch_name = 9;
    // 仓位id
    uint64 position_id = 10;
    // 仓位名称
    string position_name = 11;
    // 仓位code
    string position_code = 12;
    // 转换规则ID
    uint64 convert_rule = 13;
    string remark = 14;
    // 幂等性校验请求id
    uint64 request_id = 15;
    uint64 partner_id = 16;
    uint64 created_by = 17;
    string created_name = 18;
    uint64 updated_by = 19;
    string updated_name = 20;
    google.protobuf.Timestamp created_at = 21;
    google.protobuf.Timestamp updated_at = 22;
    string process_status = 23;
    // 是否开启多仓位
    bool opened_position = 24;
    // 成本中心ID
    uint64 cost_center_id = 30;
}

message ListMaterialConvertResponse{
    repeated MaterialDetail rows = 1;
    uint64 total = 2;
}

message GetMaterialConvertDetailRequest{
    // 转换单id
    uint64 convert_id = 1;
}

message GetMaterialConvertDetailResponse{
    uint64 id = 1;
    // 区分门店/仓库/加工中心：STORE/WAREHOUSE/MACHINING_CENTER
    string branch_type = 2;
    // 编号
    string code = 3;
    // 订单状态:
    string status = 4;
    // 转换类型（自动AUTO、手动MANUAL）
    string convert_type = 5;
    // 物料转换日期
    google.protobuf.Timestamp convert_date = 6;
    // 门店/仓库/加工中心id
    uint64 branch_id = 7;
    // 门店/仓库/加工中心code
    string branch_code = 8;
    // 门店/仓库/加工中心名称
    string branch_name = 9;
    // 仓位id
    uint64 position_id = 10;
    // 仓位名称
    string position_name = 11;
    // 仓位code
    string position_code = 12;
    // 转换规则ID
    uint64 convert_rule = 13;
    // 备注
    string remark = 14;
    // 幂等性校验请求id
    uint64 request_id = 15;
    uint64 partner_id = 16;
    uint64 created_by = 17;
    string created_name = 18;
    uint64 updated_by = 19;
    string updated_name = 20;
    string process_status = 21;
    google.protobuf.Timestamp created_at = 22;
    google.protobuf.Timestamp updated_at = 23;
    // 是否开启多仓位
    bool opened_position = 24;
    repeated Material materials = 30;
    // 成本中心ID
    uint64 cost_center_id = 35;
}

message UpdateMaterialConvertRequest{
    // 转换单id
    uint64 convert_id = 1;
    //CONFIRMED' 确认/ 'INVALID'作废
    string status = 2;
    // 是否更新详情(保存操作，不更新状态)
    bool update_detail = 3;
    // update_detail=true时才传以下参数
    // 区分门店/仓库/加工中心：STORE/WAREHOUSE/MACHINING_CENTER
    string branch_type = 4;
    // 转换类型（自动AUTO、手动MANUAL）
    string convert_type = 5;
    // 物料转换日期
    google.protobuf.Timestamp convert_date = 6;
    // 门店/仓库/加工中心id
    uint64 branch_id = 7;
    // 门店/仓库/加工中心code
    string branch_code = 8;
    // 门店/仓库/加工中心名称
    string branch_name = 9;
    // 仓位id
    uint64 position_id = 10;
    // 仓位名称
    string position_name = 11;
    // 仓位code
    string position_code = 12;
    // 转换规则ID
    uint64 convert_rule = 13;
    // 备注
    string remark = 14;
    // 是否开启多仓位
    bool opened_position = 15;
    repeated Material materials = 20;
    // 成本中心ID
    uint64 cost_center_id = 30;
}

message UpdateMaterialConvertResponse{
    uint64 convert_id = 1;
    string result = 2;
}
