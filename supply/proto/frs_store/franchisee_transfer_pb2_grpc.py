# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from frs_store import franchisee_transfer_pb2 as frs__store_dot_franchisee__transfer__pb2


class FranchiseeTransferStub(object):
  """Transfer 调拨服务 - 移动端 加盟商
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateTransfer = channel.unary_unary(
        '/franchisee_transfer.FranchiseeTransfer/CreateTransfer',
        request_serializer=frs__store_dot_franchisee__transfer__pb2.CreateTransferRequest.SerializeToString,
        response_deserializer=frs__store_dot_franchisee__transfer__pb2.Transfer.FromString,
        )
    self.UpdateTransfer = channel.unary_unary(
        '/franchisee_transfer.FranchiseeTransfer/UpdateTransfer',
        request_serializer=frs__store_dot_franchisee__transfer__pb2.UpdateTransferRequest.SerializeToString,
        response_deserializer=frs__store_dot_franchisee__transfer__pb2.UpdateTransferResponse.FromString,
        )
    self.GetTransfer = channel.unary_unary(
        '/franchisee_transfer.FranchiseeTransfer/GetTransfer',
        request_serializer=frs__store_dot_franchisee__transfer__pb2.GetTransferRequest.SerializeToString,
        response_deserializer=frs__store_dot_franchisee__transfer__pb2.GetTransferResponse.FromString,
        )
    self.GetTransferByID = channel.unary_unary(
        '/franchisee_transfer.FranchiseeTransfer/GetTransferByID',
        request_serializer=frs__store_dot_franchisee__transfer__pb2.GetTransferByIDRequest.SerializeToString,
        response_deserializer=frs__store_dot_franchisee__transfer__pb2.GetTransferByIDResponse.FromString,
        )
    self.GetTransferProductByBranchID = channel.unary_unary(
        '/franchisee_transfer.FranchiseeTransfer/GetTransferProductByBranchID',
        request_serializer=frs__store_dot_franchisee__transfer__pb2.GetTransferProductByBranchIDRequest.SerializeToString,
        response_deserializer=frs__store_dot_franchisee__transfer__pb2.GetTransferProductByBranchIDResponse.FromString,
        )
    self.GetTransferProductCategoryByBranchID = channel.unary_unary(
        '/franchisee_transfer.FranchiseeTransfer/GetTransferProductCategoryByBranchID',
        request_serializer=frs__store_dot_franchisee__transfer__pb2.GetTransferProductCategoryByBranchIDRequest.SerializeToString,
        response_deserializer=frs__store_dot_franchisee__transfer__pb2.GetTransferProductCategoryByBranchIDResponse.FromString,
        )
    self.GetTransferRegionByBranchID = channel.unary_unary(
        '/franchisee_transfer.FranchiseeTransfer/GetTransferRegionByBranchID',
        request_serializer=frs__store_dot_franchisee__transfer__pb2.GetTransferRegionByBranchIDRequest.SerializeToString,
        response_deserializer=frs__store_dot_franchisee__transfer__pb2.GetTransferRegionByBranchIDResponse.FromString,
        )
    self.GetTransferProductByTransferID = channel.unary_unary(
        '/franchisee_transfer.FranchiseeTransfer/GetTransferProductByTransferID',
        request_serializer=frs__store_dot_franchisee__transfer__pb2.GetTransferProductByTransferIDRequest.SerializeToString,
        response_deserializer=frs__store_dot_franchisee__transfer__pb2.GetTransferProductByTransferIDResponse.FromString,
        )
    self.ConfirmTransfer = channel.unary_unary(
        '/franchisee_transfer.FranchiseeTransfer/ConfirmTransfer',
        request_serializer=frs__store_dot_franchisee__transfer__pb2.ConfirmTransferRequest.SerializeToString,
        response_deserializer=frs__store_dot_franchisee__transfer__pb2.Transfer.FromString,
        )
    self.DeleteTransferProduct = channel.unary_unary(
        '/franchisee_transfer.FranchiseeTransfer/DeleteTransferProduct',
        request_serializer=frs__store_dot_franchisee__transfer__pb2.DeleteTransferProductRequest.SerializeToString,
        response_deserializer=frs__store_dot_franchisee__transfer__pb2.DeleteTransferProductResponse.FromString,
        )
    self.DeleteTransfer = channel.unary_unary(
        '/franchisee_transfer.FranchiseeTransfer/DeleteTransfer',
        request_serializer=frs__store_dot_franchisee__transfer__pb2.DeleteTransferRequest.SerializeToString,
        response_deserializer=frs__store_dot_franchisee__transfer__pb2.DeleteTransferResponse.FromString,
        )
    self.SubmitTransfer = channel.unary_unary(
        '/franchisee_transfer.FranchiseeTransfer/SubmitTransfer',
        request_serializer=frs__store_dot_franchisee__transfer__pb2.SubmitTransferRequest.SerializeToString,
        response_deserializer=frs__store_dot_franchisee__transfer__pb2.Transfer.FromString,
        )
    self.CancelTransfer = channel.unary_unary(
        '/franchisee_transfer.FranchiseeTransfer/CancelTransfer',
        request_serializer=frs__store_dot_franchisee__transfer__pb2.CancelTransferRequest.SerializeToString,
        response_deserializer=frs__store_dot_franchisee__transfer__pb2.Transfer.FromString,
        )
    self.GetTransferCollect = channel.unary_unary(
        '/franchisee_transfer.FranchiseeTransfer/GetTransferCollect',
        request_serializer=frs__store_dot_franchisee__transfer__pb2.GetTransferCollectRequest.SerializeToString,
        response_deserializer=frs__store_dot_franchisee__transfer__pb2.GetTransferCollectResponse.FromString,
        )
    self.GetTransferCollectDetailed = channel.unary_unary(
        '/franchisee_transfer.FranchiseeTransfer/GetTransferCollectDetailed',
        request_serializer=frs__store_dot_franchisee__transfer__pb2.GetTransferCollectDetailedRequest.SerializeToString,
        response_deserializer=frs__store_dot_franchisee__transfer__pb2.GetTransferCollectDetailedResponse.FromString,
        )
    self.GetTransferLog = channel.unary_unary(
        '/franchisee_transfer.FranchiseeTransfer/GetTransferLog',
        request_serializer=frs__store_dot_franchisee__transfer__pb2.GetTransferLogRequest.SerializeToString,
        response_deserializer=frs__store_dot_franchisee__transfer__pb2.GetTransferLogResponse.FromString,
        )


class FranchiseeTransferServicer(object):
  """Transfer 调拨服务 - 移动端 加盟商
  """

  def CreateTransfer(self, request, context):
    """CreateTransfer 创建调拨单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateTransfer(self, request, context):
    """UpdateTransfer 修改调拨单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransfer(self, request, context):
    """GetTransfer 查询调拨单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferByID(self, request, context):
    """GetTransferByID 查询一个调拨单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferProductByBranchID(self, request, context):
    """GetTransferProductByBranchID 取得门店可调拨商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferProductCategoryByBranchID(self, request, context):
    """GetTransferProductCategoryByBranchID 取得门店可调拨商品的分类列表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferRegionByBranchID(self, request, context):
    """GetTransferRegionByID 查询相同属性区域门店
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferProductByTransferID(self, request, context):
    """GetTransferProductByTransferID 获取一个调拨单商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ConfirmTransfer(self, request, context):
    """ConfirmTransfer 确认调拨单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteTransferProduct(self, request, context):
    """DeleteTransferProduct 删除调拨单商品
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteTransfer(self, request, context):
    """DeleteTransfer 删除调拨单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SubmitTransfer(self, request, context):
    """SubmitTransfer 提交调拨单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CancelTransfer(self, request, context):
    """CancelTransfer 取消调拨单13
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferCollect(self, request, context):
    """GetTransferCollect调拨单汇总报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferCollectDetailed(self, request, context):
    """GetTransferCollectDetailed调拨单明细汇总报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetTransferLog(self, request, context):
    """调拨历史记录
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_FranchiseeTransferServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.CreateTransfer,
          request_deserializer=frs__store_dot_franchisee__transfer__pb2.CreateTransferRequest.FromString,
          response_serializer=frs__store_dot_franchisee__transfer__pb2.Transfer.SerializeToString,
      ),
      'UpdateTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateTransfer,
          request_deserializer=frs__store_dot_franchisee__transfer__pb2.UpdateTransferRequest.FromString,
          response_serializer=frs__store_dot_franchisee__transfer__pb2.UpdateTransferResponse.SerializeToString,
      ),
      'GetTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransfer,
          request_deserializer=frs__store_dot_franchisee__transfer__pb2.GetTransferRequest.FromString,
          response_serializer=frs__store_dot_franchisee__transfer__pb2.GetTransferResponse.SerializeToString,
      ),
      'GetTransferByID': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferByID,
          request_deserializer=frs__store_dot_franchisee__transfer__pb2.GetTransferByIDRequest.FromString,
          response_serializer=frs__store_dot_franchisee__transfer__pb2.GetTransferByIDResponse.SerializeToString,
      ),
      'GetTransferProductByBranchID': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferProductByBranchID,
          request_deserializer=frs__store_dot_franchisee__transfer__pb2.GetTransferProductByBranchIDRequest.FromString,
          response_serializer=frs__store_dot_franchisee__transfer__pb2.GetTransferProductByBranchIDResponse.SerializeToString,
      ),
      'GetTransferProductCategoryByBranchID': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferProductCategoryByBranchID,
          request_deserializer=frs__store_dot_franchisee__transfer__pb2.GetTransferProductCategoryByBranchIDRequest.FromString,
          response_serializer=frs__store_dot_franchisee__transfer__pb2.GetTransferProductCategoryByBranchIDResponse.SerializeToString,
      ),
      'GetTransferRegionByBranchID': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferRegionByBranchID,
          request_deserializer=frs__store_dot_franchisee__transfer__pb2.GetTransferRegionByBranchIDRequest.FromString,
          response_serializer=frs__store_dot_franchisee__transfer__pb2.GetTransferRegionByBranchIDResponse.SerializeToString,
      ),
      'GetTransferProductByTransferID': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferProductByTransferID,
          request_deserializer=frs__store_dot_franchisee__transfer__pb2.GetTransferProductByTransferIDRequest.FromString,
          response_serializer=frs__store_dot_franchisee__transfer__pb2.GetTransferProductByTransferIDResponse.SerializeToString,
      ),
      'ConfirmTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.ConfirmTransfer,
          request_deserializer=frs__store_dot_franchisee__transfer__pb2.ConfirmTransferRequest.FromString,
          response_serializer=frs__store_dot_franchisee__transfer__pb2.Transfer.SerializeToString,
      ),
      'DeleteTransferProduct': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteTransferProduct,
          request_deserializer=frs__store_dot_franchisee__transfer__pb2.DeleteTransferProductRequest.FromString,
          response_serializer=frs__store_dot_franchisee__transfer__pb2.DeleteTransferProductResponse.SerializeToString,
      ),
      'DeleteTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteTransfer,
          request_deserializer=frs__store_dot_franchisee__transfer__pb2.DeleteTransferRequest.FromString,
          response_serializer=frs__store_dot_franchisee__transfer__pb2.DeleteTransferResponse.SerializeToString,
      ),
      'SubmitTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.SubmitTransfer,
          request_deserializer=frs__store_dot_franchisee__transfer__pb2.SubmitTransferRequest.FromString,
          response_serializer=frs__store_dot_franchisee__transfer__pb2.Transfer.SerializeToString,
      ),
      'CancelTransfer': grpc.unary_unary_rpc_method_handler(
          servicer.CancelTransfer,
          request_deserializer=frs__store_dot_franchisee__transfer__pb2.CancelTransferRequest.FromString,
          response_serializer=frs__store_dot_franchisee__transfer__pb2.Transfer.SerializeToString,
      ),
      'GetTransferCollect': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferCollect,
          request_deserializer=frs__store_dot_franchisee__transfer__pb2.GetTransferCollectRequest.FromString,
          response_serializer=frs__store_dot_franchisee__transfer__pb2.GetTransferCollectResponse.SerializeToString,
      ),
      'GetTransferCollectDetailed': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferCollectDetailed,
          request_deserializer=frs__store_dot_franchisee__transfer__pb2.GetTransferCollectDetailedRequest.FromString,
          response_serializer=frs__store_dot_franchisee__transfer__pb2.GetTransferCollectDetailedResponse.SerializeToString,
      ),
      'GetTransferLog': grpc.unary_unary_rpc_method_handler(
          servicer.GetTransferLog,
          request_deserializer=frs__store_dot_franchisee__transfer__pb2.GetTransferLogRequest.FromString,
          response_serializer=frs__store_dot_franchisee__transfer__pb2.GetTransferLogResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'franchisee_transfer.FranchiseeTransfer', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
