# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frs_store/franchisee_receive_diff.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frs_store/franchisee_receive_diff.proto',
  package='franchisee_receive_diff',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\'frs_store/franchisee_receive_diff.proto\x12\x17\x66ranchisee_receive_diff\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"#\n\x15GetHistoryByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\"P\n\x0fHistoryResponse\x12.\n\x04rows\x18\x01 \x03(\x0b\x32 .franchisee_receive_diff.History\x12\r\n\x05total\x18\x02 \x01(\x04\"\x82\x01\n\x07History\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x12\n\nupdated_by\x18\x03 \x01(\x04\x12.\n\nupdated_at\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x17\n\x0fupdated_by_name\x18\x05 \x01(\t\"\xbe\x02\n\x18\x43reateReceiveDiffRequest\x12\x14\n\x0creceiving_id\x18\x01 \x01(\x04\x12G\n\x12\x63onfirmed_products\x18\x02 \x03(\x0b\x32+.franchisee_receive_diff.ReceiveDiffProduct\x12\x11\n\treview_by\x18\x03 \x01(\x04\x12\x0b\n\x03lan\x18\x04 \x01(\t\x12\x39\n\x0b\x61ttachments\x18\x05 \x03(\x0b\x32$.franchisee_receive_diff.Attachments\x12\x31\n\rdelivery_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x11pre_delivery_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"6\n\x19\x43reateReceiveDiffResponse\x12\x19\n\x11receiving_diff_id\x18\x01 \x01(\x04\"\xda\x06\n\x16ListReceiveDiffRequest\x12\x11\n\tstore_ids\x18\x01 \x03(\x04\x12\x0e\n\x06status\x18\x02 \x03(\t\x12.\n\nstart_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x05 \x01(\x05\x12\x0e\n\x06offset\x18\x06 \x01(\x05\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12\x11\n\tis_direct\x18\x08 \x01(\x08\x12\x15\n\rreceiving_ids\x18\t \x01(\x04\x12\x16\n\x0ereceiving_code\x18\n \x01(\t\x12\x13\n\x0bproduct_ids\x18\x0b \x03(\x04\x12\x0c\n\x04\x63ode\x18\x0c \x01(\t\x12\x16\n\x0elogistics_type\x18\r \x03(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\x12\r\n\x05order\x18\x0f \x01(\t\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x34\n\x10return_date_from\x18\x12 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0ereturn_date_to\x18\x13 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x36\n\x12\x64\x65livery_date_from\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x10\x64\x65livery_date_to\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x11receive_date_from\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x33\n\x0freceive_date_to\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x34\n\x10\x64\x65mand_date_from\x18\x18 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x32\n\x0e\x64\x65mand_date_to\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0b\n\x03ids\x18\x1a \x03(\x04\x12\x14\n\x0c\x64\x65livery_bys\x18\x1b \x03(\x04\x12\x11\n\tdiff_type\x18\x1c \x01(\t\"\\\n\x17ListReceiveDiffResponse\x12\x32\n\x04rows\x18\x01 \x03(\x0b\x32$.franchisee_receive_diff.ReceiveDiff\x12\r\n\x05total\x18\x02 \x01(\x04\"j\n\x19GetReceiveDiffByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12\x0b\n\x03lan\x18\x05 \x01(\t\"\x8e\x01\n GetReceiveDiffProductByIdRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\x05\x12\x0e\n\x06offset\x18\x03 \x01(\x05\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12\x0c\n\x04sort\x18\x05 \x01(\t\x12\r\n\x05order\x18\x06 \x01(\t\x12\x0b\n\x03lan\x18\x07 \x01(\t\"\x9d\x01\n!GetReceiveDiffProductByIdResponse\x12\x39\n\x04rows\x18\x01 \x03(\x0b\x32+.franchisee_receive_diff.ReceiveDiffProduct\x12\r\n\x05total\x18\x02 \x01(\x04\x12\x16\n\x0etotal_d_amount\x18\x03 \x01(\x01\x12\x16\n\x0etotal_s_amount\x18\x04 \x01(\x01\"3\n\x18SubmitReceiveDiffRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\",\n\x19SubmitReceiveDiffResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"4\n\x19\x43onfirmReceiveDiffRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"-\n\x1a\x43onfirmReceiveDiffResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"\x85\x01\n\x18RejectReceiveDiffRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x15\n\rreject_reason\x18\x02 \x01(\t\x12\x39\n\x0b\x61ttachments\x18\x03 \x03(\x0b\x32$.franchisee_receive_diff.Attachments\x12\x0b\n\x03lan\x18\x04 \x01(\t\",\n\x19RejectReceiveDiffResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"3\n\x18\x44\x65leteReceiveDiffRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\",\n\x19\x44\x65leteReceiveDiffResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"\xc2\x01\n\x18UpdateReceiveDiffRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x32\n\x08products\x18\x02 \x03(\x0b\x32 .franchisee_receive_diff.Product\x12\x39\n\x0b\x61ttachments\x18\x03 \x03(\x0b\x32$.franchisee_receive_diff.Attachments\x12\x0b\n\x03lan\x18\x04 \x01(\t\x12\x0e\n\x06remark\x18\x05 \x01(\t\x12\x0e\n\x06reason\x18\x06 \x01(\t\",\n\x19UpdateReceiveDiffResponse\x12\x0f\n\x07payload\x18\x01 \x01(\x08\"\xc0\n\n\x0bReceiveDiff\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x13\n\x0breceived_by\x18\x03 \x01(\x04\x12\x14\n\x0creceiving_id\x18\x04 \x01(\x04\x12\x16\n\x0ereceiving_code\x18\x05 \x01(\t\x12\x11\n\tmaster_id\x18\x06 \x01(\x04\x12\x13\n\x0bmaster_code\x18\x07 \x01(\t\x12\x0e\n\x06status\x18\x08 \x01(\t\x12/\n\x0b\x64\x65mand_date\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1c\n\x14\x64\x65livery_note_number\x18\n \x01(\t\x12\x13\n\x0b\x64\x65livery_by\x18\x0b \x01(\x04\x12\x31\n\rdelivery_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0breason_type\x18\r \x01(\t\x12\x11\n\treview_by\x18\x0e \x01(\x04\x12\x0e\n\x06remark\x18\x0f \x01(\t\x12\x1a\n\x12store_secondary_id\x18\x10 \x01(\t\x12\x18\n\x10inventory_status\x18\x11 \x01(\t\x12\x18\n\x10inventory_req_id\x18\x12 \x01(\x04\x12.\n\ncreated_at\x18\x13 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x14 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x15 \x01(\x04\x12\x12\n\nupdated_by\x18\x16 \x01(\x04\x12\x12\n\npartner_id\x18\x17 \x01(\x04\x12\x11\n\tis_direct\x18\x18 \x01(\x08\x12\x14\n\x0c\x63reated_name\x18\x19 \x01(\t\x12\x14\n\x0cupdated_name\x18\x1a \x01(\t\x12\x15\n\rreject_reason\x18\x1b \x01(\t\x12\x39\n\x0b\x61ttachments\x18\x1c \x03(\x0b\x32$.franchisee_receive_diff.Attachments\x12\x14\n\x0cproduct_nums\x18\x1d \x01(\x04\x12\x15\n\rreceived_type\x18\x1f \x01(\t\x12\x12\n\nrequest_id\x18  \x01(\x04\x12\x16\n\x0elogistics_type\x18! \x01(\t\x12\x11\n\tsend_type\x18\" \x01(\t\x12\x35\n\x11pre_delivery_date\x18$ \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\treturn_id\x18% \x01(\x04\x12-\n\tsend_date\x18& \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\' \x01(\t\x12\x16\n\x0esub_receive_by\x18( \x01(\x04\x12\x18\n\x10\x64\x65livery_by_name\x18) \x01(\t\x12\x30\n\x0creceive_date\x18* \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x11\x61uto_confirm_date\x18+ \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x1b\n\x13product_names_brief\x18, \x03(\t\x12\x18\n\x10\x64\x65livery_by_code\x18- \x01(\t\x12\x15\n\rfranchisee_id\x18. \x01(\x04\x12\x11\n\tdiff_type\x18/ \x01(\t\x12\x18\n\x10received_by_name\x18\x30 \x01(\t\x12\x18\n\x10received_by_code\x18\x31 \x01(\t\x12\x11\n\trefund_id\x18\x32 \x01(\x04\x12\x13\n\x0brefund_code\x18\x33 \x01(\t\"\xa9\t\n\x12ReceiveDiffProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x14\n\x0creceiving_id\x18\x02 \x01(\x04\x12\x13\n\x0breceived_by\x18\x03 \x01(\x04\x12\x12\n\nproduct_id\x18\x04 \x01(\x04\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x05 \x01(\x04\x12\x0f\n\x07unit_id\x18\x06 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x07 \x01(\t\x12\x14\n\x0cproduct_name\x18\x08 \x01(\t\x12\x17\n\x0fmaterial_number\x18\t \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\n \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x0b \x01(\t\x12\x11\n\tunit_name\x18\x0c \x01(\t\x12\x11\n\tunit_spec\x18\r \x01(\t\x12\x11\n\tunit_rate\x18\x0e \x01(\x01\x12\x10\n\x08quantity\x18\x11 \x01(\x01\x12$\n\x1creceived_accounting_quantity\x18\x12 \x01(\x01\x12\x19\n\x11received_quantity\x18\x13 \x01(\x01\x12%\n\x1d\x63onfirmed_accounting_quantity\x18\x14 \x01(\x01\x12\x1a\n\x12\x63onfirmed_quantity\x18\x15 \x01(\x01\x12 \n\x18\x64iff_accounting_quantity\x18\x16 \x01(\x01\x12\x15\n\rdiff_quantity\x18\x17 \x01(\x01\x12\"\n\x1as_diff_accounting_quantity\x18\x18 \x01(\x01\x12\x17\n\x0fs_diff_quantity\x18\x19 \x01(\x01\x12\"\n\x1a\x64_diff_accounting_quantity\x18\x1a \x01(\x01\x12\x17\n\x0f\x64_diff_quantity\x18\x1b \x01(\x01\x12/\n\x0b\x64\x65mand_date\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x1d \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x1e \x01(\x04\x12.\n\nupdated_at\x18\x1f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18  \x01(\x04\x12\x12\n\npartner_id\x18! \x01(\x04\x12\x0f\n\x07\x64iff_id\x18\" \x01(\x04\x12\x13\n\x0breason_type\x18# \x01(\t\x12\x15\n\rreject_reason\x18$ \x01(\t\x12\x39\n\x0b\x61ttachments\x18% \x03(\x0b\x32$.franchisee_receive_diff.Attachments\x12\x14\n\x0c\x63reated_name\x18& \x01(\t\x12\x14\n\x0cupdated_name\x18\' \x01(\t\x12\x0e\n\x06remark\x18( \x01(\t\x12\x12\n\ncost_price\x18) \x01(\x01\x12\x11\n\ttax_price\x18* \x01(\x01\x12\x10\n\x08tax_rate\x18+ \x01(\x01\x12\x16\n\x0esub_receive_by\x18, \x01(\x04\x12\x0c\n\x04spec\x18- \x01(\t\x12\x14\n\x0cretail_price\x18/ \x01(\x01\x12\x10\n\x08\x64_amount\x18\x30 \x01(\x01\x12\x10\n\x08s_amount\x18\x31 \x01(\x01\"l\n\x07Product\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x17\n\x0fs_diff_quantity\x18\x02 \x01(\x01\x12\x17\n\x0f\x64_diff_quantity\x18\x03 \x01(\x01\x12\x13\n\x0breason_type\x18\x04 \x01(\t\x12\x0e\n\x06remark\x18\x05 \x01(\t\"R\n\x0b\x44iffProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06remark\x18\x02 \x01(\t\x12\x13\n\x0breason_type\x18\x03 \x01(\t\x12\x12\n\ncreated_by\x18\x04 \x01(\x04\"\xd2\x02\n\x1a\x43reateReceivingDiffRequest\x12\x14\n\x0creceiving_id\x18\x01 \x01(\x04\x12I\n\x12\x63onfirmed_products\x18\x02 \x03(\x0b\x32-.franchisee_receive_diff.ReceivingDiffProduct\x12\x11\n\treview_by\x18\x03 \x01(\x04\x12\x0b\n\x03lan\x18\x04 \x01(\t\x12\x39\n\x0b\x61ttachments\x18\x05 \x03(\x0b\x32$.franchisee_receive_diff.Attachments\x12\x31\n\rdelivery_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x35\n\x11pre_delivery_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06remark\x18\x08 \x01(\t\"\xf9\x08\n\x14ReceivingDiffProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x14\n\x0creceiving_id\x18\x02 \x01(\x04\x12\x13\n\x0breceived_by\x18\x03 \x01(\x04\x12\x12\n\nproduct_id\x18\x04 \x01(\x04\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x05 \x01(\x04\x12\x0f\n\x07unit_id\x18\x06 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x07 \x01(\t\x12\x14\n\x0cproduct_name\x18\x08 \x01(\t\x12\x17\n\x0fmaterial_number\x18\t \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\n \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x0b \x01(\t\x12\x11\n\tunit_name\x18\x0c \x01(\t\x12\x11\n\tunit_spec\x18\r \x01(\t\x12\x11\n\tunit_rate\x18\x0e \x01(\x01\x12\x10\n\x08quantity\x18\x11 \x01(\x01\x12$\n\x1creceived_accounting_quantity\x18\x12 \x01(\x01\x12\x19\n\x11received_quantity\x18\x13 \x01(\x01\x12%\n\x1d\x63onfirmed_accounting_quantity\x18\x14 \x01(\x01\x12\x1a\n\x12\x63onfirmed_quantity\x18\x15 \x01(\x01\x12 \n\x18\x64iff_accounting_quantity\x18\x16 \x01(\x01\x12\x15\n\rdiff_quantity\x18\x17 \x01(\x01\x12\"\n\x1as_diff_accounting_quantity\x18\x18 \x01(\x01\x12\x17\n\x0fs_diff_quantity\x18\x19 \x01(\x01\x12\"\n\x1a\x64_diff_accounting_quantity\x18\x1a \x01(\x01\x12\x17\n\x0f\x64_diff_quantity\x18\x1b \x01(\x01\x12/\n\x0b\x64\x65mand_date\x18\x1c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x1d \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x1e \x01(\x04\x12.\n\nupdated_at\x18\x1f \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nupdated_by\x18  \x01(\x04\x12\x12\n\npartner_id\x18! \x01(\x04\x12\x0f\n\x07\x64iff_id\x18\" \x01(\x04\x12\x13\n\x0breason_type\x18# \x01(\t\x12\x15\n\rreject_reason\x18$ \x01(\t\x12\x39\n\x0b\x61ttachments\x18% \x03(\x0b\x32$.franchisee_receive_diff.Attachments\x12\x14\n\x0c\x63reated_name\x18& \x01(\t\x12\x14\n\x0cupdated_name\x18\' \x01(\t\x12\x0e\n\x06remark\x18( \x01(\t\x12\x12\n\ncost_price\x18) \x01(\x01\x12\x11\n\ttax_price\x18* \x01(\x01\x12\x10\n\x08tax_rate\x18+ \x01(\x01\x12\x16\n\x0esub_receive_by\x18, \x01(\x04\x12\x14\n\x0cretail_price\x18- \x01(\x01\"8\n\x1b\x43reateReceivingDiffResponse\x12\x19\n\x11receiving_diff_id\x18\x01 \x01(\x04\"(\n\x0b\x41ttachments\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t2\xd6\x0e\n\x1e\x46ranchiseeReceivingDiffService\x12\xa4\x01\n\x0fListReceiveDiff\x12/.franchisee_receive_diff.ListReceiveDiffRequest\x1a\x30.franchisee_receive_diff.ListReceiveDiffResponse\".\x82\xd3\xe4\x93\x02(\x12&/api/v2/supply/franchisee/receive-diff\x12\xa3\x01\n\x12GetReceiveDiffById\x12\x32.franchisee_receive_diff.GetReceiveDiffByIdRequest\x1a$.franchisee_receive_diff.ReceiveDiff\"3\x82\xd3\xe4\x93\x02-\x12+/api/v2/supply/franchisee/receive-diff/{id}\x12\xcf\x01\n\x19GetReceiveDiffProductById\x12\x39.franchisee_receive_diff.GetReceiveDiffProductByIdRequest\x1a:.franchisee_receive_diff.GetReceiveDiffProductByIdResponse\";\x82\xd3\xe4\x93\x02\x35\x12\x33/api/v2/supply/franchisee/receive-diff/{id}/product\x12\xb9\x01\n\x11SubmitReceiveDiff\x12\x31.franchisee_receive_diff.SubmitReceiveDiffRequest\x1a\x32.franchisee_receive_diff.SubmitReceiveDiffResponse\"=\x82\xd3\xe4\x93\x02\x37\"2/api/v2/supply/franchisee/receive-diff/{id}/submit:\x01*\x12\xbd\x01\n\x12\x43onfirmReceiveDiff\x12\x32.franchisee_receive_diff.ConfirmReceiveDiffRequest\x1a\x33.franchisee_receive_diff.ConfirmReceiveDiffResponse\">\x82\xd3\xe4\x93\x02\x38\"3/api/v2/supply/franchisee/receive-diff/{id}/confirm:\x01*\x12\xb9\x01\n\x11RejectReceiveDiff\x12\x31.franchisee_receive_diff.RejectReceiveDiffRequest\x1a\x32.franchisee_receive_diff.RejectReceiveDiffResponse\"=\x82\xd3\xe4\x93\x02\x37\"2/api/v2/supply/franchisee/receive-diff/{id}/reject:\x01*\x12\xb9\x01\n\x11UpdateReceiveDiff\x12\x31.franchisee_receive_diff.UpdateReceiveDiffRequest\x1a\x32.franchisee_receive_diff.UpdateReceiveDiffResponse\"=\x82\xd3\xe4\x93\x02\x37\"2/api/v2/supply/franchisee/receive-diff/{id}/update:\x01*\x12\xb9\x01\n\x11\x44\x65leteReceiveDiff\x12\x31.franchisee_receive_diff.DeleteReceiveDiffRequest\x1a\x32.franchisee_receive_diff.DeleteReceiveDiffResponse\"=\x82\xd3\xe4\x93\x02\x37\"2/api/v2/supply/franchisee/receive-diff/{id}/delete:\x01*\x12\xa7\x01\n\x0eGetHistoryById\x12..franchisee_receive_diff.GetHistoryByIdRequest\x1a(.franchisee_receive_diff.HistoryResponse\";\x82\xd3\xe4\x93\x02\x35\x12\x33/api/v2/supply/franchisee/receive-diff/{id}/history\x12\xba\x01\n\x13\x43reateReceivingDiff\x12\x33.franchisee_receive_diff.CreateReceivingDiffRequest\x1a\x34.franchisee_receive_diff.CreateReceivingDiffResponse\"8\x82\xd3\xe4\x93\x02\x32\"-/api/v2/supply/franchisee/receive-diff/create:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_GETHISTORYBYIDREQUEST = _descriptor.Descriptor(
  name='GetHistoryByIdRequest',
  full_name='franchisee_receive_diff.GetHistoryByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receive_diff.GetHistoryByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=131,
  serialized_end=166,
)


_HISTORYRESPONSE = _descriptor.Descriptor(
  name='HistoryResponse',
  full_name='franchisee_receive_diff.HistoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_receive_diff.HistoryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_receive_diff.HistoryResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=168,
  serialized_end=248,
)


_HISTORY = _descriptor.Descriptor(
  name='History',
  full_name='franchisee_receive_diff.History',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receive_diff.History.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_receive_diff.History.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='franchisee_receive_diff.History.updated_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='franchisee_receive_diff.History.updated_at', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by_name', full_name='franchisee_receive_diff.History.updated_by_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=251,
  serialized_end=381,
)


_CREATERECEIVEDIFFREQUEST = _descriptor.Descriptor(
  name='CreateReceiveDiffRequest',
  full_name='franchisee_receive_diff.CreateReceiveDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receiving_id', full_name='franchisee_receive_diff.CreateReceiveDiffRequest.receiving_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_products', full_name='franchisee_receive_diff.CreateReceiveDiffRequest.confirmed_products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='franchisee_receive_diff.CreateReceiveDiffRequest.review_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receive_diff.CreateReceiveDiffRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_receive_diff.CreateReceiveDiffRequest.attachments', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='franchisee_receive_diff.CreateReceiveDiffRequest.delivery_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_delivery_date', full_name='franchisee_receive_diff.CreateReceiveDiffRequest.pre_delivery_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=384,
  serialized_end=702,
)


_CREATERECEIVEDIFFRESPONSE = _descriptor.Descriptor(
  name='CreateReceiveDiffResponse',
  full_name='franchisee_receive_diff.CreateReceiveDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receiving_diff_id', full_name='franchisee_receive_diff.CreateReceiveDiffResponse.receiving_diff_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=704,
  serialized_end=758,
)


_LISTRECEIVEDIFFREQUEST = _descriptor.Descriptor(
  name='ListReceiveDiffRequest',
  full_name='franchisee_receive_diff.ListReceiveDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='franchisee_receive_diff.ListReceiveDiffRequest.store_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_receive_diff.ListReceiveDiffRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='franchisee_receive_diff.ListReceiveDiffRequest.start_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='franchisee_receive_diff.ListReceiveDiffRequest.end_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_receive_diff.ListReceiveDiffRequest.limit', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_receive_diff.ListReceiveDiffRequest.offset', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='franchisee_receive_diff.ListReceiveDiffRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='franchisee_receive_diff.ListReceiveDiffRequest.is_direct', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_ids', full_name='franchisee_receive_diff.ListReceiveDiffRequest.receiving_ids', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_code', full_name='franchisee_receive_diff.ListReceiveDiffRequest.receiving_code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='franchisee_receive_diff.ListReceiveDiffRequest.product_ids', index=10,
      number=11, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='franchisee_receive_diff.ListReceiveDiffRequest.code', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='franchisee_receive_diff.ListReceiveDiffRequest.logistics_type', index=12,
      number=13, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='franchisee_receive_diff.ListReceiveDiffRequest.sort', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='franchisee_receive_diff.ListReceiveDiffRequest.order', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receive_diff.ListReceiveDiffRequest.lan', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_from', full_name='franchisee_receive_diff.ListReceiveDiffRequest.return_date_from', index=16,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_date_to', full_name='franchisee_receive_diff.ListReceiveDiffRequest.return_date_to', index=17,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_from', full_name='franchisee_receive_diff.ListReceiveDiffRequest.delivery_date_from', index=18,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date_to', full_name='franchisee_receive_diff.ListReceiveDiffRequest.delivery_date_to', index=19,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_date_from', full_name='franchisee_receive_diff.ListReceiveDiffRequest.receive_date_from', index=20,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_date_to', full_name='franchisee_receive_diff.ListReceiveDiffRequest.receive_date_to', index=21,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date_from', full_name='franchisee_receive_diff.ListReceiveDiffRequest.demand_date_from', index=22,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date_to', full_name='franchisee_receive_diff.ListReceiveDiffRequest.demand_date_to', index=23,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='franchisee_receive_diff.ListReceiveDiffRequest.ids', index=24,
      number=26, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_bys', full_name='franchisee_receive_diff.ListReceiveDiffRequest.delivery_bys', index=25,
      number=27, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_type', full_name='franchisee_receive_diff.ListReceiveDiffRequest.diff_type', index=26,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=761,
  serialized_end=1619,
)


_LISTRECEIVEDIFFRESPONSE = _descriptor.Descriptor(
  name='ListReceiveDiffResponse',
  full_name='franchisee_receive_diff.ListReceiveDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_receive_diff.ListReceiveDiffResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_receive_diff.ListReceiveDiffResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1621,
  serialized_end=1713,
)


_GETRECEIVEDIFFBYIDREQUEST = _descriptor.Descriptor(
  name='GetReceiveDiffByIdRequest',
  full_name='franchisee_receive_diff.GetReceiveDiffByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receive_diff.GetReceiveDiffByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_receive_diff.GetReceiveDiffByIdRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_receive_diff.GetReceiveDiffByIdRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='franchisee_receive_diff.GetReceiveDiffByIdRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receive_diff.GetReceiveDiffByIdRequest.lan', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1715,
  serialized_end=1821,
)


_GETRECEIVEDIFFPRODUCTBYIDREQUEST = _descriptor.Descriptor(
  name='GetReceiveDiffProductByIdRequest',
  full_name='franchisee_receive_diff.GetReceiveDiffProductByIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receive_diff.GetReceiveDiffProductByIdRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_receive_diff.GetReceiveDiffProductByIdRequest.limit', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_receive_diff.GetReceiveDiffProductByIdRequest.offset', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='franchisee_receive_diff.GetReceiveDiffProductByIdRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='franchisee_receive_diff.GetReceiveDiffProductByIdRequest.sort', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='franchisee_receive_diff.GetReceiveDiffProductByIdRequest.order', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receive_diff.GetReceiveDiffProductByIdRequest.lan', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1824,
  serialized_end=1966,
)


_GETRECEIVEDIFFPRODUCTBYIDRESPONSE = _descriptor.Descriptor(
  name='GetReceiveDiffProductByIdResponse',
  full_name='franchisee_receive_diff.GetReceiveDiffProductByIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_receive_diff.GetReceiveDiffProductByIdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_receive_diff.GetReceiveDiffProductByIdResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_d_amount', full_name='franchisee_receive_diff.GetReceiveDiffProductByIdResponse.total_d_amount', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_s_amount', full_name='franchisee_receive_diff.GetReceiveDiffProductByIdResponse.total_s_amount', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1969,
  serialized_end=2126,
)


_SUBMITRECEIVEDIFFREQUEST = _descriptor.Descriptor(
  name='SubmitReceiveDiffRequest',
  full_name='franchisee_receive_diff.SubmitReceiveDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receive_diff.SubmitReceiveDiffRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receive_diff.SubmitReceiveDiffRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2128,
  serialized_end=2179,
)


_SUBMITRECEIVEDIFFRESPONSE = _descriptor.Descriptor(
  name='SubmitReceiveDiffResponse',
  full_name='franchisee_receive_diff.SubmitReceiveDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_receive_diff.SubmitReceiveDiffResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2181,
  serialized_end=2225,
)


_CONFIRMRECEIVEDIFFREQUEST = _descriptor.Descriptor(
  name='ConfirmReceiveDiffRequest',
  full_name='franchisee_receive_diff.ConfirmReceiveDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receive_diff.ConfirmReceiveDiffRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receive_diff.ConfirmReceiveDiffRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2227,
  serialized_end=2279,
)


_CONFIRMRECEIVEDIFFRESPONSE = _descriptor.Descriptor(
  name='ConfirmReceiveDiffResponse',
  full_name='franchisee_receive_diff.ConfirmReceiveDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_receive_diff.ConfirmReceiveDiffResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2281,
  serialized_end=2326,
)


_REJECTRECEIVEDIFFREQUEST = _descriptor.Descriptor(
  name='RejectReceiveDiffRequest',
  full_name='franchisee_receive_diff.RejectReceiveDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receive_diff.RejectReceiveDiffRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='franchisee_receive_diff.RejectReceiveDiffRequest.reject_reason', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_receive_diff.RejectReceiveDiffRequest.attachments', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receive_diff.RejectReceiveDiffRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2329,
  serialized_end=2462,
)


_REJECTRECEIVEDIFFRESPONSE = _descriptor.Descriptor(
  name='RejectReceiveDiffResponse',
  full_name='franchisee_receive_diff.RejectReceiveDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_receive_diff.RejectReceiveDiffResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2464,
  serialized_end=2508,
)


_DELETERECEIVEDIFFREQUEST = _descriptor.Descriptor(
  name='DeleteReceiveDiffRequest',
  full_name='franchisee_receive_diff.DeleteReceiveDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receive_diff.DeleteReceiveDiffRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receive_diff.DeleteReceiveDiffRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2510,
  serialized_end=2561,
)


_DELETERECEIVEDIFFRESPONSE = _descriptor.Descriptor(
  name='DeleteReceiveDiffResponse',
  full_name='franchisee_receive_diff.DeleteReceiveDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_receive_diff.DeleteReceiveDiffResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2563,
  serialized_end=2607,
)


_UPDATERECEIVEDIFFREQUEST = _descriptor.Descriptor(
  name='UpdateReceiveDiffRequest',
  full_name='franchisee_receive_diff.UpdateReceiveDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receive_diff.UpdateReceiveDiffRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='franchisee_receive_diff.UpdateReceiveDiffRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_receive_diff.UpdateReceiveDiffRequest.attachments', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receive_diff.UpdateReceiveDiffRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_receive_diff.UpdateReceiveDiffRequest.remark', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='franchisee_receive_diff.UpdateReceiveDiffRequest.reason', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2610,
  serialized_end=2804,
)


_UPDATERECEIVEDIFFRESPONSE = _descriptor.Descriptor(
  name='UpdateReceiveDiffResponse',
  full_name='franchisee_receive_diff.UpdateReceiveDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='payload', full_name='franchisee_receive_diff.UpdateReceiveDiffResponse.payload', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2806,
  serialized_end=2850,
)


_RECEIVEDIFF = _descriptor.Descriptor(
  name='ReceiveDiff',
  full_name='franchisee_receive_diff.ReceiveDiff',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receive_diff.ReceiveDiff.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='franchisee_receive_diff.ReceiveDiff.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='franchisee_receive_diff.ReceiveDiff.received_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_id', full_name='franchisee_receive_diff.ReceiveDiff.receiving_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_code', full_name='franchisee_receive_diff.ReceiveDiff.receiving_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='master_id', full_name='franchisee_receive_diff.ReceiveDiff.master_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='master_code', full_name='franchisee_receive_diff.ReceiveDiff.master_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_receive_diff.ReceiveDiff.status', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='franchisee_receive_diff.ReceiveDiff.demand_date', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_note_number', full_name='franchisee_receive_diff.ReceiveDiff.delivery_note_number', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='franchisee_receive_diff.ReceiveDiff.delivery_by', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='franchisee_receive_diff.ReceiveDiff.delivery_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='franchisee_receive_diff.ReceiveDiff.reason_type', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='franchisee_receive_diff.ReceiveDiff.review_by', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_receive_diff.ReceiveDiff.remark', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_secondary_id', full_name='franchisee_receive_diff.ReceiveDiff.store_secondary_id', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_status', full_name='franchisee_receive_diff.ReceiveDiff.inventory_status', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_req_id', full_name='franchisee_receive_diff.ReceiveDiff.inventory_req_id', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='franchisee_receive_diff.ReceiveDiff.created_at', index=18,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='franchisee_receive_diff.ReceiveDiff.updated_at', index=19,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_receive_diff.ReceiveDiff.created_by', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='franchisee_receive_diff.ReceiveDiff.updated_by', index=21,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='franchisee_receive_diff.ReceiveDiff.partner_id', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='franchisee_receive_diff.ReceiveDiff.is_direct', index=23,
      number=24, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='franchisee_receive_diff.ReceiveDiff.created_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='franchisee_receive_diff.ReceiveDiff.updated_name', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='franchisee_receive_diff.ReceiveDiff.reject_reason', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_receive_diff.ReceiveDiff.attachments', index=27,
      number=28, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_nums', full_name='franchisee_receive_diff.ReceiveDiff.product_nums', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_type', full_name='franchisee_receive_diff.ReceiveDiff.received_type', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='franchisee_receive_diff.ReceiveDiff.request_id', index=30,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='franchisee_receive_diff.ReceiveDiff.logistics_type', index=31,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='send_type', full_name='franchisee_receive_diff.ReceiveDiff.send_type', index=32,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_delivery_date', full_name='franchisee_receive_diff.ReceiveDiff.pre_delivery_date', index=33,
      number=36, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_id', full_name='franchisee_receive_diff.ReceiveDiff.return_id', index=34,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='send_date', full_name='franchisee_receive_diff.ReceiveDiff.send_date', index=35,
      number=38, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='franchisee_receive_diff.ReceiveDiff.branch_type', index=36,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='franchisee_receive_diff.ReceiveDiff.sub_receive_by', index=37,
      number=40, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_name', full_name='franchisee_receive_diff.ReceiveDiff.delivery_by_name', index=38,
      number=41, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_date', full_name='franchisee_receive_diff.ReceiveDiff.receive_date', index=39,
      number=42, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auto_confirm_date', full_name='franchisee_receive_diff.ReceiveDiff.auto_confirm_date', index=40,
      number=43, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_names_brief', full_name='franchisee_receive_diff.ReceiveDiff.product_names_brief', index=41,
      number=44, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_code', full_name='franchisee_receive_diff.ReceiveDiff.delivery_by_code', index=42,
      number=45, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='franchisee_id', full_name='franchisee_receive_diff.ReceiveDiff.franchisee_id', index=43,
      number=46, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_type', full_name='franchisee_receive_diff.ReceiveDiff.diff_type', index=44,
      number=47, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by_name', full_name='franchisee_receive_diff.ReceiveDiff.received_by_name', index=45,
      number=48, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by_code', full_name='franchisee_receive_diff.ReceiveDiff.received_by_code', index=46,
      number=49, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_id', full_name='franchisee_receive_diff.ReceiveDiff.refund_id', index=47,
      number=50, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='refund_code', full_name='franchisee_receive_diff.ReceiveDiff.refund_code', index=48,
      number=51, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2853,
  serialized_end=4197,
)


_RECEIVEDIFFPRODUCT = _descriptor.Descriptor(
  name='ReceiveDiffProduct',
  full_name='franchisee_receive_diff.ReceiveDiffProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receive_diff.ReceiveDiffProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_id', full_name='franchisee_receive_diff.ReceiveDiffProduct.receiving_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='franchisee_receive_diff.ReceiveDiffProduct.received_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='franchisee_receive_diff.ReceiveDiffProduct.product_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='franchisee_receive_diff.ReceiveDiffProduct.accounting_unit_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='franchisee_receive_diff.ReceiveDiffProduct.unit_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='franchisee_receive_diff.ReceiveDiffProduct.product_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='franchisee_receive_diff.ReceiveDiffProduct.product_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='franchisee_receive_diff.ReceiveDiffProduct.material_number', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='franchisee_receive_diff.ReceiveDiffProduct.accounting_unit_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='franchisee_receive_diff.ReceiveDiffProduct.accounting_unit_spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='franchisee_receive_diff.ReceiveDiffProduct.unit_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='franchisee_receive_diff.ReceiveDiffProduct.unit_spec', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='franchisee_receive_diff.ReceiveDiffProduct.unit_rate', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='franchisee_receive_diff.ReceiveDiffProduct.quantity', index=14,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_accounting_quantity', full_name='franchisee_receive_diff.ReceiveDiffProduct.received_accounting_quantity', index=15,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_quantity', full_name='franchisee_receive_diff.ReceiveDiffProduct.received_quantity', index=16,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_accounting_quantity', full_name='franchisee_receive_diff.ReceiveDiffProduct.confirmed_accounting_quantity', index=17,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='franchisee_receive_diff.ReceiveDiffProduct.confirmed_quantity', index=18,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_accounting_quantity', full_name='franchisee_receive_diff.ReceiveDiffProduct.diff_accounting_quantity', index=19,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='franchisee_receive_diff.ReceiveDiffProduct.diff_quantity', index=20,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_accounting_quantity', full_name='franchisee_receive_diff.ReceiveDiffProduct.s_diff_accounting_quantity', index=21,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_quantity', full_name='franchisee_receive_diff.ReceiveDiffProduct.s_diff_quantity', index=22,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_accounting_quantity', full_name='franchisee_receive_diff.ReceiveDiffProduct.d_diff_accounting_quantity', index=23,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_quantity', full_name='franchisee_receive_diff.ReceiveDiffProduct.d_diff_quantity', index=24,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='franchisee_receive_diff.ReceiveDiffProduct.demand_date', index=25,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='franchisee_receive_diff.ReceiveDiffProduct.created_at', index=26,
      number=29, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_receive_diff.ReceiveDiffProduct.created_by', index=27,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='franchisee_receive_diff.ReceiveDiffProduct.updated_at', index=28,
      number=31, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='franchisee_receive_diff.ReceiveDiffProduct.updated_by', index=29,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='franchisee_receive_diff.ReceiveDiffProduct.partner_id', index=30,
      number=33, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_id', full_name='franchisee_receive_diff.ReceiveDiffProduct.diff_id', index=31,
      number=34, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='franchisee_receive_diff.ReceiveDiffProduct.reason_type', index=32,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='franchisee_receive_diff.ReceiveDiffProduct.reject_reason', index=33,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_receive_diff.ReceiveDiffProduct.attachments', index=34,
      number=37, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='franchisee_receive_diff.ReceiveDiffProduct.created_name', index=35,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='franchisee_receive_diff.ReceiveDiffProduct.updated_name', index=36,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_receive_diff.ReceiveDiffProduct.remark', index=37,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='franchisee_receive_diff.ReceiveDiffProduct.cost_price', index=38,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='franchisee_receive_diff.ReceiveDiffProduct.tax_price', index=39,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='franchisee_receive_diff.ReceiveDiffProduct.tax_rate', index=40,
      number=43, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='franchisee_receive_diff.ReceiveDiffProduct.sub_receive_by', index=41,
      number=44, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='franchisee_receive_diff.ReceiveDiffProduct.spec', index=42,
      number=45, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='retail_price', full_name='franchisee_receive_diff.ReceiveDiffProduct.retail_price', index=43,
      number=47, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_amount', full_name='franchisee_receive_diff.ReceiveDiffProduct.d_amount', index=44,
      number=48, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_amount', full_name='franchisee_receive_diff.ReceiveDiffProduct.s_amount', index=45,
      number=49, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4200,
  serialized_end=5393,
)


_PRODUCT = _descriptor.Descriptor(
  name='Product',
  full_name='franchisee_receive_diff.Product',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receive_diff.Product.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_quantity', full_name='franchisee_receive_diff.Product.s_diff_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_quantity', full_name='franchisee_receive_diff.Product.d_diff_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='franchisee_receive_diff.Product.reason_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_receive_diff.Product.remark', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5395,
  serialized_end=5503,
)


_DIFFPRODUCT = _descriptor.Descriptor(
  name='DiffProduct',
  full_name='franchisee_receive_diff.DiffProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receive_diff.DiffProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_receive_diff.DiffProduct.remark', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='franchisee_receive_diff.DiffProduct.reason_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_receive_diff.DiffProduct.created_by', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5505,
  serialized_end=5587,
)


_CREATERECEIVINGDIFFREQUEST = _descriptor.Descriptor(
  name='CreateReceivingDiffRequest',
  full_name='franchisee_receive_diff.CreateReceivingDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receiving_id', full_name='franchisee_receive_diff.CreateReceivingDiffRequest.receiving_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_products', full_name='franchisee_receive_diff.CreateReceivingDiffRequest.confirmed_products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='review_by', full_name='franchisee_receive_diff.CreateReceivingDiffRequest.review_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_receive_diff.CreateReceivingDiffRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_receive_diff.CreateReceivingDiffRequest.attachments', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_date', full_name='franchisee_receive_diff.CreateReceivingDiffRequest.delivery_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_delivery_date', full_name='franchisee_receive_diff.CreateReceivingDiffRequest.pre_delivery_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_receive_diff.CreateReceivingDiffRequest.remark', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5590,
  serialized_end=5928,
)


_RECEIVINGDIFFPRODUCT = _descriptor.Descriptor(
  name='ReceivingDiffProduct',
  full_name='franchisee_receive_diff.ReceivingDiffProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_receive_diff.ReceivingDiffProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_id', full_name='franchisee_receive_diff.ReceivingDiffProduct.receiving_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_by', full_name='franchisee_receive_diff.ReceivingDiffProduct.received_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='franchisee_receive_diff.ReceivingDiffProduct.product_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='franchisee_receive_diff.ReceivingDiffProduct.accounting_unit_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='franchisee_receive_diff.ReceivingDiffProduct.unit_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='franchisee_receive_diff.ReceivingDiffProduct.product_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='franchisee_receive_diff.ReceivingDiffProduct.product_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='franchisee_receive_diff.ReceivingDiffProduct.material_number', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='franchisee_receive_diff.ReceivingDiffProduct.accounting_unit_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='franchisee_receive_diff.ReceivingDiffProduct.accounting_unit_spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='franchisee_receive_diff.ReceivingDiffProduct.unit_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='franchisee_receive_diff.ReceivingDiffProduct.unit_spec', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='franchisee_receive_diff.ReceivingDiffProduct.unit_rate', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='franchisee_receive_diff.ReceivingDiffProduct.quantity', index=14,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_accounting_quantity', full_name='franchisee_receive_diff.ReceivingDiffProduct.received_accounting_quantity', index=15,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='received_quantity', full_name='franchisee_receive_diff.ReceivingDiffProduct.received_quantity', index=16,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_accounting_quantity', full_name='franchisee_receive_diff.ReceivingDiffProduct.confirmed_accounting_quantity', index=17,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='franchisee_receive_diff.ReceivingDiffProduct.confirmed_quantity', index=18,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_accounting_quantity', full_name='franchisee_receive_diff.ReceivingDiffProduct.diff_accounting_quantity', index=19,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_quantity', full_name='franchisee_receive_diff.ReceivingDiffProduct.diff_quantity', index=20,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_accounting_quantity', full_name='franchisee_receive_diff.ReceivingDiffProduct.s_diff_accounting_quantity', index=21,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_quantity', full_name='franchisee_receive_diff.ReceivingDiffProduct.s_diff_quantity', index=22,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_accounting_quantity', full_name='franchisee_receive_diff.ReceivingDiffProduct.d_diff_accounting_quantity', index=23,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_quantity', full_name='franchisee_receive_diff.ReceivingDiffProduct.d_diff_quantity', index=24,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='franchisee_receive_diff.ReceivingDiffProduct.demand_date', index=25,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='franchisee_receive_diff.ReceivingDiffProduct.created_at', index=26,
      number=29, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_receive_diff.ReceivingDiffProduct.created_by', index=27,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='franchisee_receive_diff.ReceivingDiffProduct.updated_at', index=28,
      number=31, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='franchisee_receive_diff.ReceivingDiffProduct.updated_by', index=29,
      number=32, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='franchisee_receive_diff.ReceivingDiffProduct.partner_id', index=30,
      number=33, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_id', full_name='franchisee_receive_diff.ReceivingDiffProduct.diff_id', index=31,
      number=34, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='franchisee_receive_diff.ReceivingDiffProduct.reason_type', index=32,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='franchisee_receive_diff.ReceivingDiffProduct.reject_reason', index=33,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_receive_diff.ReceivingDiffProduct.attachments', index=34,
      number=37, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='franchisee_receive_diff.ReceivingDiffProduct.created_name', index=35,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='franchisee_receive_diff.ReceivingDiffProduct.updated_name', index=36,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_receive_diff.ReceivingDiffProduct.remark', index=37,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='franchisee_receive_diff.ReceivingDiffProduct.cost_price', index=38,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='franchisee_receive_diff.ReceivingDiffProduct.tax_price', index=39,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='franchisee_receive_diff.ReceivingDiffProduct.tax_rate', index=40,
      number=43, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_receive_by', full_name='franchisee_receive_diff.ReceivingDiffProduct.sub_receive_by', index=41,
      number=44, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='retail_price', full_name='franchisee_receive_diff.ReceivingDiffProduct.retail_price', index=42,
      number=45, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5931,
  serialized_end=7076,
)


_CREATERECEIVINGDIFFRESPONSE = _descriptor.Descriptor(
  name='CreateReceivingDiffResponse',
  full_name='franchisee_receive_diff.CreateReceivingDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receiving_diff_id', full_name='franchisee_receive_diff.CreateReceivingDiffResponse.receiving_diff_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7078,
  serialized_end=7134,
)


_ATTACHMENTS = _descriptor.Descriptor(
  name='Attachments',
  full_name='franchisee_receive_diff.Attachments',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='franchisee_receive_diff.Attachments.type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='franchisee_receive_diff.Attachments.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7136,
  serialized_end=7176,
)

_HISTORYRESPONSE.fields_by_name['rows'].message_type = _HISTORY
_HISTORY.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATERECEIVEDIFFREQUEST.fields_by_name['confirmed_products'].message_type = _RECEIVEDIFFPRODUCT
_CREATERECEIVEDIFFREQUEST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_CREATERECEIVEDIFFREQUEST.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATERECEIVEDIFFREQUEST.fields_by_name['pre_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEDIFFREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEDIFFREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEDIFFREQUEST.fields_by_name['return_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEDIFFREQUEST.fields_by_name['return_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEDIFFREQUEST.fields_by_name['delivery_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEDIFFREQUEST.fields_by_name['delivery_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEDIFFREQUEST.fields_by_name['receive_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEDIFFREQUEST.fields_by_name['receive_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEDIFFREQUEST.fields_by_name['demand_date_from'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEDIFFREQUEST.fields_by_name['demand_date_to'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTRECEIVEDIFFRESPONSE.fields_by_name['rows'].message_type = _RECEIVEDIFF
_GETRECEIVEDIFFPRODUCTBYIDRESPONSE.fields_by_name['rows'].message_type = _RECEIVEDIFFPRODUCT
_REJECTRECEIVEDIFFREQUEST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_UPDATERECEIVEDIFFREQUEST.fields_by_name['products'].message_type = _PRODUCT
_UPDATERECEIVEDIFFREQUEST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_RECEIVEDIFF.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFF.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFF.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFF.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFF.fields_by_name['attachments'].message_type = _ATTACHMENTS
_RECEIVEDIFF.fields_by_name['pre_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFF.fields_by_name['send_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFF.fields_by_name['receive_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFF.fields_by_name['auto_confirm_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFFPRODUCT.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFFPRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFFPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVEDIFFPRODUCT.fields_by_name['attachments'].message_type = _ATTACHMENTS
_CREATERECEIVINGDIFFREQUEST.fields_by_name['confirmed_products'].message_type = _RECEIVINGDIFFPRODUCT
_CREATERECEIVINGDIFFREQUEST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_CREATERECEIVINGDIFFREQUEST.fields_by_name['delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATERECEIVINGDIFFREQUEST.fields_by_name['pre_delivery_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVINGDIFFPRODUCT.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVINGDIFFPRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVINGDIFFPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVINGDIFFPRODUCT.fields_by_name['attachments'].message_type = _ATTACHMENTS
DESCRIPTOR.message_types_by_name['GetHistoryByIdRequest'] = _GETHISTORYBYIDREQUEST
DESCRIPTOR.message_types_by_name['HistoryResponse'] = _HISTORYRESPONSE
DESCRIPTOR.message_types_by_name['History'] = _HISTORY
DESCRIPTOR.message_types_by_name['CreateReceiveDiffRequest'] = _CREATERECEIVEDIFFREQUEST
DESCRIPTOR.message_types_by_name['CreateReceiveDiffResponse'] = _CREATERECEIVEDIFFRESPONSE
DESCRIPTOR.message_types_by_name['ListReceiveDiffRequest'] = _LISTRECEIVEDIFFREQUEST
DESCRIPTOR.message_types_by_name['ListReceiveDiffResponse'] = _LISTRECEIVEDIFFRESPONSE
DESCRIPTOR.message_types_by_name['GetReceiveDiffByIdRequest'] = _GETRECEIVEDIFFBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetReceiveDiffProductByIdRequest'] = _GETRECEIVEDIFFPRODUCTBYIDREQUEST
DESCRIPTOR.message_types_by_name['GetReceiveDiffProductByIdResponse'] = _GETRECEIVEDIFFPRODUCTBYIDRESPONSE
DESCRIPTOR.message_types_by_name['SubmitReceiveDiffRequest'] = _SUBMITRECEIVEDIFFREQUEST
DESCRIPTOR.message_types_by_name['SubmitReceiveDiffResponse'] = _SUBMITRECEIVEDIFFRESPONSE
DESCRIPTOR.message_types_by_name['ConfirmReceiveDiffRequest'] = _CONFIRMRECEIVEDIFFREQUEST
DESCRIPTOR.message_types_by_name['ConfirmReceiveDiffResponse'] = _CONFIRMRECEIVEDIFFRESPONSE
DESCRIPTOR.message_types_by_name['RejectReceiveDiffRequest'] = _REJECTRECEIVEDIFFREQUEST
DESCRIPTOR.message_types_by_name['RejectReceiveDiffResponse'] = _REJECTRECEIVEDIFFRESPONSE
DESCRIPTOR.message_types_by_name['DeleteReceiveDiffRequest'] = _DELETERECEIVEDIFFREQUEST
DESCRIPTOR.message_types_by_name['DeleteReceiveDiffResponse'] = _DELETERECEIVEDIFFRESPONSE
DESCRIPTOR.message_types_by_name['UpdateReceiveDiffRequest'] = _UPDATERECEIVEDIFFREQUEST
DESCRIPTOR.message_types_by_name['UpdateReceiveDiffResponse'] = _UPDATERECEIVEDIFFRESPONSE
DESCRIPTOR.message_types_by_name['ReceiveDiff'] = _RECEIVEDIFF
DESCRIPTOR.message_types_by_name['ReceiveDiffProduct'] = _RECEIVEDIFFPRODUCT
DESCRIPTOR.message_types_by_name['Product'] = _PRODUCT
DESCRIPTOR.message_types_by_name['DiffProduct'] = _DIFFPRODUCT
DESCRIPTOR.message_types_by_name['CreateReceivingDiffRequest'] = _CREATERECEIVINGDIFFREQUEST
DESCRIPTOR.message_types_by_name['ReceivingDiffProduct'] = _RECEIVINGDIFFPRODUCT
DESCRIPTOR.message_types_by_name['CreateReceivingDiffResponse'] = _CREATERECEIVINGDIFFRESPONSE
DESCRIPTOR.message_types_by_name['Attachments'] = _ATTACHMENTS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetHistoryByIdRequest = _reflection.GeneratedProtocolMessageType('GetHistoryByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETHISTORYBYIDREQUEST,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.GetHistoryByIdRequest)
  ))
_sym_db.RegisterMessage(GetHistoryByIdRequest)

HistoryResponse = _reflection.GeneratedProtocolMessageType('HistoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _HISTORYRESPONSE,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.HistoryResponse)
  ))
_sym_db.RegisterMessage(HistoryResponse)

History = _reflection.GeneratedProtocolMessageType('History', (_message.Message,), dict(
  DESCRIPTOR = _HISTORY,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.History)
  ))
_sym_db.RegisterMessage(History)

CreateReceiveDiffRequest = _reflection.GeneratedProtocolMessageType('CreateReceiveDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATERECEIVEDIFFREQUEST,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.CreateReceiveDiffRequest)
  ))
_sym_db.RegisterMessage(CreateReceiveDiffRequest)

CreateReceiveDiffResponse = _reflection.GeneratedProtocolMessageType('CreateReceiveDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATERECEIVEDIFFRESPONSE,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.CreateReceiveDiffResponse)
  ))
_sym_db.RegisterMessage(CreateReceiveDiffResponse)

ListReceiveDiffRequest = _reflection.GeneratedProtocolMessageType('ListReceiveDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTRECEIVEDIFFREQUEST,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.ListReceiveDiffRequest)
  ))
_sym_db.RegisterMessage(ListReceiveDiffRequest)

ListReceiveDiffResponse = _reflection.GeneratedProtocolMessageType('ListReceiveDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTRECEIVEDIFFRESPONSE,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.ListReceiveDiffResponse)
  ))
_sym_db.RegisterMessage(ListReceiveDiffResponse)

GetReceiveDiffByIdRequest = _reflection.GeneratedProtocolMessageType('GetReceiveDiffByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVEDIFFBYIDREQUEST,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.GetReceiveDiffByIdRequest)
  ))
_sym_db.RegisterMessage(GetReceiveDiffByIdRequest)

GetReceiveDiffProductByIdRequest = _reflection.GeneratedProtocolMessageType('GetReceiveDiffProductByIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVEDIFFPRODUCTBYIDREQUEST,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.GetReceiveDiffProductByIdRequest)
  ))
_sym_db.RegisterMessage(GetReceiveDiffProductByIdRequest)

GetReceiveDiffProductByIdResponse = _reflection.GeneratedProtocolMessageType('GetReceiveDiffProductByIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVEDIFFPRODUCTBYIDRESPONSE,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.GetReceiveDiffProductByIdResponse)
  ))
_sym_db.RegisterMessage(GetReceiveDiffProductByIdResponse)

SubmitReceiveDiffRequest = _reflection.GeneratedProtocolMessageType('SubmitReceiveDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITRECEIVEDIFFREQUEST,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.SubmitReceiveDiffRequest)
  ))
_sym_db.RegisterMessage(SubmitReceiveDiffRequest)

SubmitReceiveDiffResponse = _reflection.GeneratedProtocolMessageType('SubmitReceiveDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITRECEIVEDIFFRESPONSE,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.SubmitReceiveDiffResponse)
  ))
_sym_db.RegisterMessage(SubmitReceiveDiffResponse)

ConfirmReceiveDiffRequest = _reflection.GeneratedProtocolMessageType('ConfirmReceiveDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMRECEIVEDIFFREQUEST,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.ConfirmReceiveDiffRequest)
  ))
_sym_db.RegisterMessage(ConfirmReceiveDiffRequest)

ConfirmReceiveDiffResponse = _reflection.GeneratedProtocolMessageType('ConfirmReceiveDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMRECEIVEDIFFRESPONSE,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.ConfirmReceiveDiffResponse)
  ))
_sym_db.RegisterMessage(ConfirmReceiveDiffResponse)

RejectReceiveDiffRequest = _reflection.GeneratedProtocolMessageType('RejectReceiveDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _REJECTRECEIVEDIFFREQUEST,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.RejectReceiveDiffRequest)
  ))
_sym_db.RegisterMessage(RejectReceiveDiffRequest)

RejectReceiveDiffResponse = _reflection.GeneratedProtocolMessageType('RejectReceiveDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _REJECTRECEIVEDIFFRESPONSE,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.RejectReceiveDiffResponse)
  ))
_sym_db.RegisterMessage(RejectReceiveDiffResponse)

DeleteReceiveDiffRequest = _reflection.GeneratedProtocolMessageType('DeleteReceiveDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETERECEIVEDIFFREQUEST,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.DeleteReceiveDiffRequest)
  ))
_sym_db.RegisterMessage(DeleteReceiveDiffRequest)

DeleteReceiveDiffResponse = _reflection.GeneratedProtocolMessageType('DeleteReceiveDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETERECEIVEDIFFRESPONSE,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.DeleteReceiveDiffResponse)
  ))
_sym_db.RegisterMessage(DeleteReceiveDiffResponse)

UpdateReceiveDiffRequest = _reflection.GeneratedProtocolMessageType('UpdateReceiveDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERECEIVEDIFFREQUEST,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.UpdateReceiveDiffRequest)
  ))
_sym_db.RegisterMessage(UpdateReceiveDiffRequest)

UpdateReceiveDiffResponse = _reflection.GeneratedProtocolMessageType('UpdateReceiveDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATERECEIVEDIFFRESPONSE,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.UpdateReceiveDiffResponse)
  ))
_sym_db.RegisterMessage(UpdateReceiveDiffResponse)

ReceiveDiff = _reflection.GeneratedProtocolMessageType('ReceiveDiff', (_message.Message,), dict(
  DESCRIPTOR = _RECEIVEDIFF,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.ReceiveDiff)
  ))
_sym_db.RegisterMessage(ReceiveDiff)

ReceiveDiffProduct = _reflection.GeneratedProtocolMessageType('ReceiveDiffProduct', (_message.Message,), dict(
  DESCRIPTOR = _RECEIVEDIFFPRODUCT,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.ReceiveDiffProduct)
  ))
_sym_db.RegisterMessage(ReceiveDiffProduct)

Product = _reflection.GeneratedProtocolMessageType('Product', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCT,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.Product)
  ))
_sym_db.RegisterMessage(Product)

DiffProduct = _reflection.GeneratedProtocolMessageType('DiffProduct', (_message.Message,), dict(
  DESCRIPTOR = _DIFFPRODUCT,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.DiffProduct)
  ))
_sym_db.RegisterMessage(DiffProduct)

CreateReceivingDiffRequest = _reflection.GeneratedProtocolMessageType('CreateReceivingDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATERECEIVINGDIFFREQUEST,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.CreateReceivingDiffRequest)
  ))
_sym_db.RegisterMessage(CreateReceivingDiffRequest)

ReceivingDiffProduct = _reflection.GeneratedProtocolMessageType('ReceivingDiffProduct', (_message.Message,), dict(
  DESCRIPTOR = _RECEIVINGDIFFPRODUCT,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.ReceivingDiffProduct)
  ))
_sym_db.RegisterMessage(ReceivingDiffProduct)

CreateReceivingDiffResponse = _reflection.GeneratedProtocolMessageType('CreateReceivingDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATERECEIVINGDIFFRESPONSE,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.CreateReceivingDiffResponse)
  ))
_sym_db.RegisterMessage(CreateReceivingDiffResponse)

Attachments = _reflection.GeneratedProtocolMessageType('Attachments', (_message.Message,), dict(
  DESCRIPTOR = _ATTACHMENTS,
  __module__ = 'frs_store.franchisee_receive_diff_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_receive_diff.Attachments)
  ))
_sym_db.RegisterMessage(Attachments)



_FRANCHISEERECEIVINGDIFFSERVICE = _descriptor.ServiceDescriptor(
  name='FranchiseeReceivingDiffService',
  full_name='franchisee_receive_diff.FranchiseeReceivingDiffService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=7179,
  serialized_end=9057,
  methods=[
  _descriptor.MethodDescriptor(
    name='ListReceiveDiff',
    full_name='franchisee_receive_diff.FranchiseeReceivingDiffService.ListReceiveDiff',
    index=0,
    containing_service=None,
    input_type=_LISTRECEIVEDIFFREQUEST,
    output_type=_LISTRECEIVEDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\002(\022&/api/v2/supply/franchisee/receive-diff'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReceiveDiffById',
    full_name='franchisee_receive_diff.FranchiseeReceivingDiffService.GetReceiveDiffById',
    index=1,
    containing_service=None,
    input_type=_GETRECEIVEDIFFBYIDREQUEST,
    output_type=_RECEIVEDIFF,
    serialized_options=_b('\202\323\344\223\002-\022+/api/v2/supply/franchisee/receive-diff/{id}'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReceiveDiffProductById',
    full_name='franchisee_receive_diff.FranchiseeReceivingDiffService.GetReceiveDiffProductById',
    index=2,
    containing_service=None,
    input_type=_GETRECEIVEDIFFPRODUCTBYIDREQUEST,
    output_type=_GETRECEIVEDIFFPRODUCTBYIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\0223/api/v2/supply/franchisee/receive-diff/{id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='SubmitReceiveDiff',
    full_name='franchisee_receive_diff.FranchiseeReceivingDiffService.SubmitReceiveDiff',
    index=3,
    containing_service=None,
    input_type=_SUBMITRECEIVEDIFFREQUEST,
    output_type=_SUBMITRECEIVEDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\"2/api/v2/supply/franchisee/receive-diff/{id}/submit:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmReceiveDiff',
    full_name='franchisee_receive_diff.FranchiseeReceivingDiffService.ConfirmReceiveDiff',
    index=4,
    containing_service=None,
    input_type=_CONFIRMRECEIVEDIFFREQUEST,
    output_type=_CONFIRMRECEIVEDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\"3/api/v2/supply/franchisee/receive-diff/{id}/confirm:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='RejectReceiveDiff',
    full_name='franchisee_receive_diff.FranchiseeReceivingDiffService.RejectReceiveDiff',
    index=5,
    containing_service=None,
    input_type=_REJECTRECEIVEDIFFREQUEST,
    output_type=_REJECTRECEIVEDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\"2/api/v2/supply/franchisee/receive-diff/{id}/reject:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateReceiveDiff',
    full_name='franchisee_receive_diff.FranchiseeReceivingDiffService.UpdateReceiveDiff',
    index=6,
    containing_service=None,
    input_type=_UPDATERECEIVEDIFFREQUEST,
    output_type=_UPDATERECEIVEDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\"2/api/v2/supply/franchisee/receive-diff/{id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteReceiveDiff',
    full_name='franchisee_receive_diff.FranchiseeReceivingDiffService.DeleteReceiveDiff',
    index=7,
    containing_service=None,
    input_type=_DELETERECEIVEDIFFREQUEST,
    output_type=_DELETERECEIVEDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\"2/api/v2/supply/franchisee/receive-diff/{id}/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetHistoryById',
    full_name='franchisee_receive_diff.FranchiseeReceivingDiffService.GetHistoryById',
    index=8,
    containing_service=None,
    input_type=_GETHISTORYBYIDREQUEST,
    output_type=_HISTORYRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\0223/api/v2/supply/franchisee/receive-diff/{id}/history'),
  ),
  _descriptor.MethodDescriptor(
    name='CreateReceivingDiff',
    full_name='franchisee_receive_diff.FranchiseeReceivingDiffService.CreateReceivingDiff',
    index=9,
    containing_service=None,
    input_type=_CREATERECEIVINGDIFFREQUEST,
    output_type=_CREATERECEIVINGDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\"-/api/v2/supply/franchisee/receive-diff/create:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_FRANCHISEERECEIVINGDIFFSERVICE)

DESCRIPTOR.services_by_name['FranchiseeReceivingDiffService'] = _FRANCHISEERECEIVINGDIFFSERVICE

# @@protoc_insertion_point(module_scope)
