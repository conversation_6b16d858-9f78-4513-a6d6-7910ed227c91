# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from frs_store import receive_diff_bi_pb2 as frs__store_dot_receive__diff__bi__pb2


class FranchiseeStoreReceiveDiffBiServiceStub(object):
  """收货差异单报表相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetReceiveDiffCollect = channel.unary_unary(
        '/franchisee_store_bi.FranchiseeStoreReceiveDiffBiService/GetReceiveDiffCollect',
        request_serializer=frs__store_dot_receive__diff__bi__pb2.GetReceiveDiffCollectRequest.SerializeToString,
        response_deserializer=frs__store_dot_receive__diff__bi__pb2.GetReceiveDiffCollectResponse.FromString,
        )
    self.GetReceiveDiffDetail = channel.unary_unary(
        '/franchisee_store_bi.FranchiseeStoreReceiveDiffBiService/GetReceiveDiffDetail',
        request_serializer=frs__store_dot_receive__diff__bi__pb2.GetReceiveDiffDetailRequest.SerializeToString,
        response_deserializer=frs__store_dot_receive__diff__bi__pb2.GetReceiveDiffDetailResponse.FromString,
        )


class FranchiseeStoreReceiveDiffBiServiceServicer(object):
  """收货差异单报表相关服务
  """

  def GetReceiveDiffCollect(self, request, context):
    """查询收货差异汇总报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceiveDiffDetail(self, request, context):
    """查询收货差异详情报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_FranchiseeStoreReceiveDiffBiServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetReceiveDiffCollect': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceiveDiffCollect,
          request_deserializer=frs__store_dot_receive__diff__bi__pb2.GetReceiveDiffCollectRequest.FromString,
          response_serializer=frs__store_dot_receive__diff__bi__pb2.GetReceiveDiffCollectResponse.SerializeToString,
      ),
      'GetReceiveDiffDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceiveDiffDetail,
          request_deserializer=frs__store_dot_receive__diff__bi__pb2.GetReceiveDiffDetailRequest.FromString,
          response_serializer=frs__store_dot_receive__diff__bi__pb2.GetReceiveDiffDetailResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'franchisee_store_bi.FranchiseeStoreReceiveDiffBiService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
