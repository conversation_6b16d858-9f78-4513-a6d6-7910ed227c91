# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frs_store/franchisee_adjust.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frs_store/franchisee_adjust.proto',
  package='franchisee_adjust',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n!frs_store/franchisee_adjust.proto\x12\x11\x66ranchisee_adjust\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"X\n\x14\x43reateAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x30\n\x0crequest_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xd6\x03\n\x10GetAdjustRequest\x12\x15\n\rinclude_total\x18\x01 \x01(\x08\x12.\n\nstart_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0breason_type\x18\x04 \x01(\t\x12\x10\n\x08\x62ranches\x18\x05 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x06 \x03(\x04\x12\x0e\n\x06offset\x18\x07 \x01(\r\x12\r\n\x05limit\x18\x08 \x01(\r\x12:\n\x06status\x18\t \x03(\x0e\x32*.franchisee_adjust.GetAdjustRequest.STATUS\x12\x0c\n\x04\x63ode\x18\n \x01(\t\x12\r\n\x05order\x18\x0c \x01(\t\x12\x0c\n\x04sort\x18\r \x01(\t\x12\x0b\n\x03ids\x18\x0e \x03(\x04\x12\x13\n\x0b\x62ranch_type\x18\x1e \x01(\t\x12\x0f\n\x07sources\x18\x1f \x03(\t\"X\n\x06STATUS\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tSUBMITTED\x10\x02\x12\x0c\n\x08\x41PPROVED\x10\x03\x12\x0c\n\x08REJECTED\x10\x04\x12\r\n\tCANCELLED\x10\x05\"\xe6\x05\n\x06\x41\x64just\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x14\n\x0c\x61\x64just_store\x18\x03 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x12\n\npartner_id\x18\x06 \x01(\x04\x12\x16\n\x0eprocess_status\x18\x07 \x01(\t\x12\x13\n\x0breason_type\x18\x08 \x01(\t\x12\x0e\n\x06remark\x18\t \x01(\t\x12\x0e\n\x06status\x18\n \x01(\t\x12/\n\x0b\x61\x64just_date\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x0e \x01(\x04\x12\x12\n\nupdated_by\x18\x0f \x01(\x04\x12\x0f\n\x07user_id\x18\x10 \x01(\x04\x12\x13\n\x0breason_name\x18\x11 \x01(\t\x12\x13\n\x0bschedule_id\x18\x13 \x01(\x04\x12\x12\n\nrequest_id\x18\x14 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x15 \x01(\t\x12\x14\n\x0cupdated_name\x18\x16 \x01(\t\x12\x15\n\rschedule_name\x18\x1c \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x1e \x01(\t\x12\x33\n\x0b\x61ttachments\x18\x1f \x03(\x0b\x32\x1e.franchisee_adjust.Attachments\x12\x0e\n\x06source\x18  \x01(\t\x12\x0f\n\x07\x65xtends\x18! \x01(\t\x12\x10\n\x08is_empty\x18\" \x01(\x08\x12:\n\x0bstatus_plan\x18# \x03(\x0b\x32%.franchisee_adjust.ScheduleStatusPlan\x12\x15\n\rreject_reason\x18$ \x01(\t\x12\x14\n\x0ctotal_amount\x18& \x01(\x01\x12\x1a\n\x12total_sales_amount\x18\' \x01(\x01\"\x91\x01\n\x12ScheduleStatusPlan\x12(\n\x04time\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0cstart_status\x18\x02 \x03(\t\x12\x12\n\nend_status\x18\x03 \x01(\t\x12\x13\n\x0btime_around\x18\x04 \x01(\t\x12\x12\n\ndoc_filter\x18\x05 \x01(\t\"K\n\x11GetAdjustResponse\x12\'\n\x04rows\x18\x01 \x03(\x0b\x32\x19.franchisee_adjust.Adjust\x12\r\n\x05total\x18\x02 \x01(\r\"~\n\x17GetAdjustProductRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x15\n\rinclude_total\x18\x02 \x01(\x08\x12\r\n\x05order\x18\x03 \x01(\t\x12\x0e\n\x06offset\x18\x04 \x01(\r\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x0b\n\x03lan\x18\x06 \x01(\t\"\xbb\x08\n\rAdjustProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x02 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x03 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x04 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x05 \x01(\t\x12\x11\n\tadjust_id\x18\x06 \x01(\x04\x12\x14\n\x0c\x61\x64just_store\x18\x07 \x01(\x04\x12\x1a\n\x12\x63onfirmed_quantity\x18\x08 \x01(\x01\x12\x12\n\ncreated_by\x18\t \x01(\x04\x12\x14\n\x0cis_confirmed\x18\n \x01(\x08\x12\x13\n\x0bitem_number\x18\x0b \x01(\r\x12\x17\n\x0fmaterial_number\x18\x0c \x01(\t\x12\x12\n\npartner_id\x18\r \x01(\x04\x12\x14\n\x0cproduct_code\x18\x0e \x01(\t\x12\x12\n\nproduct_id\x18\x0f \x01(\x04\x12\x14\n\x0cproduct_name\x18\x10 \x01(\t\x12\x10\n\x08quantity\x18\x11 \x01(\x01\x12\x0c\n\x04spec\x18\x12 \x01(\t\x12\x1a\n\x12stocktake_quantity\x18\x13 \x01(\x01\x12\x19\n\x11stocktake_unit_id\x18\x14 \x01(\x04\x12\x0f\n\x07unit_id\x18\x15 \x01(\x04\x12\x11\n\tunit_name\x18\x16 \x01(\t\x12\x11\n\tunit_spec\x18\x17 \x01(\t\x12\x12\n\nupdated_by\x18\x18 \x01(\x04\x12/\n\x0b\x61\x64just_date\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x1b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07user_id\x18\x1c \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1d \x01(\t\x12\x14\n\x0cupdated_name\x18\x1e \x01(\t\x12\x13\n\x0breason_type\x18\x1f \x01(\t\x12#\n\x1b\x63onvert_accounting_quantity\x18  \x01(\x01\x12\x0e\n\x06is_bom\x18! \x01(\x08\x12\x13\n\x0bposition_id\x18\" \x01(\x04\x12\x12\n\nsku_remark\x18# \x01(\t\x12\x39\n\x05units\x18$ \x03(\x0b\x32*.franchisee_adjust.CreateAdjustProductUint\x12\x12\n\nmodel_name\x18% \x01(\t\x12\x10\n\x08tax_rate\x18& \x01(\x01\x12\x11\n\ttax_price\x18\' \x01(\x01\x12\x0e\n\x06\x61mount\x18( \x01(\x01\x12\x12\n\ntax_amount\x18) \x01(\x01\x12\x12\n\ncost_price\x18* \x01(\x01\x12\x14\n\x0csales_amount\x18+ \x01(\x01\x12\x13\n\x0bsales_price\x18, \x01(\x01\"\x9b\x01\n\x18GetAdjustProductResponse\x12.\n\x04rows\x18\x01 \x03(\x0b\x32 .franchisee_adjust.AdjustProduct\x12\r\n\x05total\x18\x02 \x01(\r\x12@\n\rposition_rows\x18\x03 \x03(\x0b\x32).franchisee_adjust.AdjustPositionProducts\")\n\x14GetAdjustByIDRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\">\n\x14\x43onfirmAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x02 \x01(\t\"\'\n\x15\x43onfirmAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"(\n\x13SubmitAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\"&\n\x14SubmitAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\")\n\x14\x41pproveAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\"T\n\x15\x41pproveAdjustResponse\x12\x13\n\x0b\x64\x65scription\x18\x01 \x01(\t\x12\x11\n\tadjust_id\x18\x02 \x01(\x04\x12\x13\n\x0b\x61\x64just_code\x18\x03 \x01(\t\"/\n\x19\x42\x61tchApproveAdjustRequest\x12\x12\n\nadjust_ids\x18\x01 \x03(\x04\"T\n\x1a\x42\x61tchApproveAdjustResponse\x12\x36\n\x04rows\x18\x01 \x03(\x0b\x32(.franchisee_adjust.ApproveAdjustResponse\"?\n\x13RejectAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x15\n\rreject_reason\x18\x02 \x01(\t\"S\n\x14RejectAdjustResponse\x12\x13\n\x0b\x64\x65scription\x18\x01 \x01(\t\x12\x11\n\tadjust_id\x18\x02 \x01(\x04\x12\x13\n\x0b\x61\x64just_code\x18\x03 \x01(\t\"E\n\x18\x42\x61tchRejectAdjustRequest\x12\x12\n\nadjust_ids\x18\x01 \x03(\x04\x12\x15\n\rreject_reason\x18\x02 \x01(\t\"R\n\x19\x42\x61tchRejectAdjustResponse\x12\x35\n\x04rows\x18\x01 \x03(\x0b\x32\'.franchisee_adjust.RejectAdjustResponse\"\x85\x02\n GetAdjustProductByStoreIDRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\r\x12\x0e\n\x06offset\x18\x03 \x01(\r\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12/\n\x0b\x61\x64just_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06search\x18\x06 \x01(\t\x12\x15\n\rsearch_fields\x18\x07 \x01(\t\x12\x14\n\x0c\x63\x61tegory_ids\x18\x08 \x03(\x04\x12\x0b\n\x03lan\x18\t \x01(\t\x12\x10\n\x08order_by\x18\n \x01(\t\x12\x0c\n\x04sort\x18\x0b \x01(\t\"\xe2\x02\n\x17\x43reateAdjustProductUint\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x0f\n\x07updated\x18\x07 \x01(\t\x12\x0c\n\x04rate\x18\x08 \x01(\x01\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\t \x01(\x08\x12\r\n\x05order\x18\n \x01(\x08\x12\x10\n\x08purchase\x18\x0b \x01(\x08\x12\r\n\x05sales\x18\x0c \x01(\x08\x12\x11\n\tstocktake\x18\r \x01(\x08\x12\x0b\n\x03\x62om\x18\x0e \x01(\x08\x12\x19\n\x11\x64\x65\x66\x61ult_stocktake\x18\x0f \x01(\x08\x12\x10\n\x08transfer\x18\x10 \x01(\x08\x12\x10\n\x08tax_rate\x18\x11 \x01(\x01\x12\x11\n\ttax_price\x18\x12 \x01(\x01\x12\x12\n\ncost_price\x18\x13 \x01(\x01\x12\x13\n\x0bsales_price\x18\x14 \x01(\x01\"\xaf\x02\n\x14\x41\x64justProductByStore\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x19\n\x11loss_report_order\x18\x02 \x01(\t\x12\x14\n\x0cproduct_code\x18\x03 \x01(\t\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12\x12\n\nmodel_name\x18\x05 \x01(\t\x12\x14\n\x0cstorage_type\x18\x06 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x07 \x01(\x04\x12\x15\n\rcategory_name\x18\n \x01(\t\x12\x39\n\x05units\x18\x08 \x03(\x0b\x32*.franchisee_adjust.CreateAdjustProductUint\x12\x0f\n\x07\x62\x61rcode\x18\t \x03(\t\x12\x1a\n\x12real_inventory_qty\x18\x0b \x01(\x01\"\xb4\x01\n!GetAdjustProductByStoreIDResponse\x12\x35\n\x04rows\x18\x01 \x03(\x0b\x32\'.franchisee_adjust.AdjustProductByStore\x12\r\n\x05total\x18\x02 \x01(\r\x12I\n\x18inventory_unchanged_rows\x18\x03 \x03(\x0b\x32\'.franchisee_adjust.AdjustProductByStore\"Q\n\x0c\x43\x61tegoryItem\x12\x13\n\x0b\x63\x61tegory_id\x18\x01 \x01(\x04\x12\x15\n\rcategory_name\x18\x02 \x01(\t\x12\x15\n\rproduct_count\x18\x03 \x01(\x04\"<\n(GetAdjustProductCategoryByStoreIDRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\"d\n)GetAdjustProductCategoryByStoreIDResponse\x12\x37\n\x0e\x63\x61tegory_items\x18\x01 \x03(\x0b\x32\x1f.franchisee_adjust.CategoryItem\"\xbb\x02\n\x13\x43reateAdjustProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x0f\n\x07unit_id\x18\x03 \x01(\x04\x12\x10\n\x08quantity\x18\x04 \x01(\x01\x12\x13\n\x0breason_type\x18\x05 \x01(\t\x12\x13\n\x0bposition_id\x18\x06 \x01(\x04\x12/\n\tskuRemark\x18\x07 \x03(\x0b\x32\x1c.franchisee_adjust.SkuRemark\x12\x10\n\x08tax_rate\x18\x08 \x01(\x01\x12\x11\n\ttax_price\x18\t \x01(\x01\x12\x0e\n\x06\x61mount\x18\n \x01(\x01\x12\x12\n\ntax_amount\x18\x0b \x01(\x01\x12\x12\n\ncost_price\x18\x0c \x01(\x01\x12\x13\n\x0bsales_price\x18\r \x01(\x01\x12\x14\n\x0csales_amount\x18\x0e \x01(\x01\"\xbe\x02\n\x13\x43reateAdjustRequest\x12\x14\n\x0c\x61\x64just_store\x18\x01 \x01(\x04\x12\x38\n\x08products\x18\x02 \x03(\x0b\x32&.franchisee_adjust.CreateAdjustProduct\x12\x13\n\x0breason_type\x18\x03 \x01(\t\x12\x0e\n\x06remark\x18\x04 \x01(\t\x12\x12\n\nrequest_id\x18\x05 \x01(\x04\x12/\n\x0b\x61\x64just_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\x07 \x01(\t\x12\x13\n\x0bposition_id\x18\x08 \x01(\x04\x12\x0e\n\x06source\x18\t \x01(\t\x12\x33\n\x0b\x61ttachments\x18\x0b \x03(\x0b\x32\x1e.franchisee_adjust.Attachments\"(\n\x0b\x41ttachments\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\"\x97\x02\n\x13UpdateAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x38\n\x08products\x18\x02 \x03(\x0b\x32&.franchisee_adjust.CreateAdjustProduct\x12\x0e\n\x06remark\x18\x03 \x01(\t\x12\x13\n\x0breason_type\x18\x04 \x01(\t\x12/\n\x0b\x61\x64just_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\x06 \x01(\t\x12\x33\n\x0b\x61ttachments\x18\x07 \x03(\x0b\x32\x1e.franchisee_adjust.Attachments\x12\x13\n\x0bposition_id\x18\x08 \x01(\x04\"(\n\x13\x44\x65leteAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\"&\n\x14\x44\x65leteAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"<\n\x1a\x44\x65leteAdjustProductRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x0b\n\x03ids\x18\x02 \x03(\x04\"-\n\x1b\x44\x65leteAdjustProductResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"}\n\x08\x41\x44_total\x12\r\n\x05\x63ount\x18\x01 \x01(\x04\x12\x14\n\x0csum_quantity\x18\x02 \x01(\x01\x12\x1f\n\x17sum_accounting_quantity\x18\x03 \x01(\x01\x12\x0f\n\x07sum_qty\x18\x04 \x01(\x01\x12\x1a\n\x12sum_accounting_qty\x18\x05 \x01(\x01\"(\n\x13\x43\x61ncelAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\"&\n\x14\x43\x61ncelAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\xb9\x05\n\x0e\x41\x64justResponse\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x05 \x01(\x04\x12\x15\n\rcategory_name\x18\x06 \x01(\t\x12\x14\n\x0cproduct_code\x18\x07 \x01(\t\x12\x12\n\nproduct_id\x18\x08 \x01(\x04\x12\x14\n\x0cproduct_name\x18\t \x01(\t\x12\x10\n\x08quantity\x18\x0b \x01(\x01\x12\x12\n\nstore_code\x18\x0c \x01(\t\x12\x10\n\x08store_id\x18\r \x01(\x04\x12\x12\n\nstore_name\x18\x0e \x01(\t\x12\x0f\n\x07unit_id\x18\x0f \x01(\x04\x12\x11\n\tunit_name\x18\x10 \x01(\t\x12\x13\n\x0breason_type\x18\x11 \x01(\t\x12\x16\n\x0e\x62om_product_id\x18\x12 \x01(\x04\x12\x18\n\x10\x62om_product_code\x18\x13 \x01(\t\x12\x18\n\x10\x62om_product_name\x18\x14 \x01(\t\x12\x0b\n\x03qty\x18\x15 \x01(\x01\x12\x13\n\x0b\x62om_unit_id\x18\x16 \x01(\x04\x12\x15\n\rbom_unit_code\x18\x17 \x01(\t\x12\x15\n\rbom_unit_name\x18\x18 \x01(\t\x12\x1e\n\x16\x62om_accounting_unit_id\x18\x19 \x01(\x04\x12 \n\x18\x62om_accounting_unit_code\x18\x1a \x01(\t\x12 \n\x18\x62om_accounting_unit_name\x18\x1b \x01(\t\x12\x16\n\x0e\x61\x63\x63ounting_qty\x18\x1c \x01(\x01\x12\r\n\x05price\x18\x1d \x01(\x01\x12\x0c\n\x04\x63ost\x18\x1e \x01(\x01\x12\x13\n\x0b\x61\x64just_date\x18\x1f \x01(\t\"\x9c\x01\n\tSkuRemark\x12.\n\x04name\x18\x01 \x01(\x0b\x32 .franchisee_adjust.SkuRemark.Tag\x12\x30\n\x06values\x18\x02 \x01(\x0b\x32 .franchisee_adjust.SkuRemark.Tag\x1a-\n\x03Tag\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\"\x9e\x01\n\x16\x41\x64justPositionProducts\x12\x13\n\x0bposition_id\x18\x01 \x01(\x04\x12\x15\n\rposition_code\x18\x02 \x01(\t\x12\x15\n\rposition_name\x18\x03 \x01(\t\x12\x32\n\x08products\x18\x04 \x03(\x0b\x32 .franchisee_adjust.AdjustProduct\x12\r\n\x05total\x18\x05 \x01(\x04\"(\n\x13GetAdjustLogRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\"K\n\x14GetAdjustLogResponse\x12$\n\x04rows\x18\x01 \x03(\x0b\x32\x16.franchisee_adjust.Log\x12\r\n\x05total\x18\x02 \x01(\x04\"\x8b\x01\n\x03Log\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x12\n\ncreated_by\x18\x03 \x01(\x04\x12.\n\ncreated_at\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06reason\x18\x05 \x01(\t\x12\x14\n\x0c\x63reated_name\x18\x06 \x01(\t\"\xa9\x01\n\x1a\x43reatedAdjustProductByCode\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x11\n\tunit_code\x18\x03 \x01(\t\x12\x10\n\x08quantity\x18\x04 \x01(\x01\x12\x13\n\x0breason_type\x18\x05 \x01(\t\x12/\n\tskuRemark\x18\x06 \x03(\x0b\x32\x1c.franchisee_adjust.SkuRemark\"\xce\x01\n\x1a\x43reatedAdjustByCodeRequest\x12\x14\n\x0c\x61\x64just_store\x18\x01 \x01(\t\x12?\n\x08products\x18\x02 \x03(\x0b\x32-.franchisee_adjust.CreatedAdjustProductByCode\x12\x13\n\x0breason_type\x18\x03 \x01(\t\x12\x0e\n\x06remark\x18\x04 \x01(\t\x12\x12\n\nrequest_id\x18\x05 \x01(\t\x12\x13\n\x0b\x61\x64just_date\x18\x06 \x01(\t\x12\x0b\n\x03lan\x18\x07 \x01(\t2\xa1\x17\n\x10\x46ranchiseeAdjust\x12\x80\x01\n\tGetAdjust\x12#.franchisee_adjust.GetAdjustRequest\x1a$.franchisee_adjust.GetAdjustResponse\"(\x82\xd3\xe4\x93\x02\"\x12 /api/v2/supply/franchisee/adjust\x12\x85\x01\n\x0c\x43reateAdjust\x12&.franchisee_adjust.CreateAdjustRequest\x1a\x19.franchisee_adjust.Adjust\"2\x82\xd3\xe4\x93\x02,\"\'/api/v2/supply/franchisee/adjust/create:\x01*\x12\xa9\x01\n\x10GetAdjustProduct\x12*.franchisee_adjust.GetAdjustProductRequest\x1a+.franchisee_adjust.GetAdjustProductResponse\"<\x82\xd3\xe4\x93\x02\x36\x12\x34/api/v2/supply/franchisee/adjust/{adjust_id}/product\x12\x89\x01\n\rGetAdjustByID\x12\'.franchisee_adjust.GetAdjustByIDRequest\x1a\x19.franchisee_adjust.Adjust\"4\x82\xd3\xe4\x93\x02.\x12,/api/v2/supply/franchisee/adjust/{adjust_id}\x12\xa3\x01\n\rConfirmAdjust\x12\'.franchisee_adjust.ConfirmAdjustRequest\x1a(.franchisee_adjust.ConfirmAdjustResponse\"?\x82\xd3\xe4\x93\x02\x39\"4/api/v2/supply/franchisee/adjust/{adjust_id}/confirm:\x01*\x12\x9f\x01\n\x0cSubmitAdjust\x12&.franchisee_adjust.SubmitAdjustRequest\x1a\'.franchisee_adjust.SubmitAdjustResponse\">\x82\xd3\xe4\x93\x02\x38\"3/api/v2/supply/franchisee/adjust/{adjust_id}/submit:\x01*\x12\xa3\x01\n\rApproveAdjust\x12\'.franchisee_adjust.ApproveAdjustRequest\x1a(.franchisee_adjust.ApproveAdjustResponse\"?\x82\xd3\xe4\x93\x02\x39\"4/api/v2/supply/franchisee/adjust/{adjust_id}/approve:\x01*\x12\xac\x01\n\x12\x42\x61tchApproveAdjust\x12,.franchisee_adjust.BatchApproveAdjustRequest\x1a-.franchisee_adjust.BatchApproveAdjustResponse\"9\x82\xd3\xe4\x93\x02\x33\"./api/v2/supply/franchisee/adjust/approve/batch:\x01*\x12\x9f\x01\n\x0cRejectAdjust\x12&.franchisee_adjust.RejectAdjustRequest\x1a\'.franchisee_adjust.RejectAdjustResponse\">\x82\xd3\xe4\x93\x02\x38\"3/api/v2/supply/franchisee/adjust/{adjust_id}/reject:\x01*\x12\xa8\x01\n\x11\x42\x61tchRejectAdjust\x12+.franchisee_adjust.BatchRejectAdjustRequest\x1a,.franchisee_adjust.BatchRejectAdjustResponse\"8\x82\xd3\xe4\x93\x02\x32\"-/api/v2/supply/franchisee/adjust/reject/batch:\x01*\x12\xbf\x01\n\x19GetAdjustProductByStoreID\x12\x33.franchisee_adjust.GetAdjustProductByStoreIDRequest\x1a\x34.franchisee_adjust.GetAdjustProductByStoreIDResponse\"7\x82\xd3\xe4\x93\x02\x31\x12//api/v2/supply/franchisee/adjust/store/products\x12\xd7\x01\n!GetAdjustProductCategoryByStoreID\x12;.franchisee_adjust.GetAdjustProductCategoryByStoreIDRequest\x1a<.franchisee_adjust.GetAdjustProductCategoryByStoreIDResponse\"7\x82\xd3\xe4\x93\x02\x31\x12//api/v2/supply/franchisee/adjust/store/category\x12\x91\x01\n\x0cUpdateAdjust\x12&.franchisee_adjust.UpdateAdjustRequest\x1a\x19.franchisee_adjust.Adjust\">\x82\xd3\xe4\x93\x02\x38\"3/api/v2/supply/franchisee/adjust/{adjust_id}/update:\x01*\x12\x9f\x01\n\x0c\x44\x65leteAdjust\x12&.franchisee_adjust.DeleteAdjustRequest\x1a\'.franchisee_adjust.DeleteAdjustResponse\">\x82\xd3\xe4\x93\x02\x38\"3/api/v2/supply/franchisee/adjust/{adjust_id}/delete:\x01*\x12\xbc\x01\n\x13\x44\x65leteAdjustProduct\x12-.franchisee_adjust.DeleteAdjustProductRequest\x1a..franchisee_adjust.DeleteAdjustProductResponse\"F\x82\xd3\xe4\x93\x02@\";/api/v2/supply/franchisee/adjust/product/{adjust_id}/delete:\x01*\x12\x9f\x01\n\x0c\x43\x61ncelAdjust\x12&.franchisee_adjust.CancelAdjustRequest\x1a\'.franchisee_adjust.CancelAdjustResponse\">\x82\xd3\xe4\x93\x02\x38\"3/api/v2/supply/franchisee/adjust/auto_create/cancel:\x01*\x12\x90\x01\n\x13\x43reatedAdjustByCode\x12-.franchisee_adjust.CreatedAdjustByCodeRequest\x1a\x19.franchisee_adjust.Adjust\"/\x82\xd3\xe4\x93\x02)\"$/api/v2/supply/franchisee/adjust/pos:\x01*\x12\x99\x01\n\x0cGetAdjustLog\x12&.franchisee_adjust.GetAdjustLogRequest\x1a\'.franchisee_adjust.GetAdjustLogResponse\"8\x82\xd3\xe4\x93\x02\x32\x12\x30/api/v2/supply/franchisee/adjust/{adjust_id}/logb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])



_GETADJUSTREQUEST_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='franchisee_adjust.GetAdjustRequest.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REJECTED', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=5, number=5,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=592,
  serialized_end=680,
)
_sym_db.RegisterEnumDescriptor(_GETADJUSTREQUEST_STATUS)


_CREATEADJUSTRESPONSE = _descriptor.Descriptor(
  name='CreateAdjustResponse',
  full_name='franchisee_adjust.CreateAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='franchisee_adjust.CreateAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='franchisee_adjust.CreateAdjustResponse.request_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=119,
  serialized_end=207,
)


_GETADJUSTREQUEST = _descriptor.Descriptor(
  name='GetAdjustRequest',
  full_name='franchisee_adjust.GetAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='include_total', full_name='franchisee_adjust.GetAdjustRequest.include_total', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='franchisee_adjust.GetAdjustRequest.start_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='franchisee_adjust.GetAdjustRequest.end_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='franchisee_adjust.GetAdjustRequest.reason_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branches', full_name='franchisee_adjust.GetAdjustRequest.branches', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='franchisee_adjust.GetAdjustRequest.product_ids', index=5,
      number=6, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_adjust.GetAdjustRequest.offset', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_adjust.GetAdjustRequest.limit', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_adjust.GetAdjustRequest.status', index=8,
      number=9, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='franchisee_adjust.GetAdjustRequest.code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='franchisee_adjust.GetAdjustRequest.order', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='franchisee_adjust.GetAdjustRequest.sort', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='franchisee_adjust.GetAdjustRequest.ids', index=12,
      number=14, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='franchisee_adjust.GetAdjustRequest.branch_type', index=13,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sources', full_name='franchisee_adjust.GetAdjustRequest.sources', index=14,
      number=31, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _GETADJUSTREQUEST_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=210,
  serialized_end=680,
)


_ADJUST = _descriptor.Descriptor(
  name='Adjust',
  full_name='franchisee_adjust.Adjust',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_adjust.Adjust.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_store', full_name='franchisee_adjust.Adjust.adjust_store', index=1,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='franchisee_adjust.Adjust.code', index=2,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='franchisee_adjust.Adjust.partner_id', index=3,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='franchisee_adjust.Adjust.process_status', index=4,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='franchisee_adjust.Adjust.reason_type', index=5,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_adjust.Adjust.remark', index=6,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_adjust.Adjust.status', index=7,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='franchisee_adjust.Adjust.adjust_date', index=8,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='franchisee_adjust.Adjust.created_at', index=9,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='franchisee_adjust.Adjust.updated_at', index=10,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_adjust.Adjust.created_by', index=11,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='franchisee_adjust.Adjust.updated_by', index=12,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='franchisee_adjust.Adjust.user_id', index=13,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_name', full_name='franchisee_adjust.Adjust.reason_name', index=14,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_id', full_name='franchisee_adjust.Adjust.schedule_id', index=15,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='franchisee_adjust.Adjust.request_id', index=16,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='franchisee_adjust.Adjust.created_name', index=17,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='franchisee_adjust.Adjust.updated_name', index=18,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='franchisee_adjust.Adjust.schedule_name', index=19,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='franchisee_adjust.Adjust.branch_type', index=20,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_adjust.Adjust.attachments', index=21,
      number=31, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source', full_name='franchisee_adjust.Adjust.source', index=22,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extends', full_name='franchisee_adjust.Adjust.extends', index=23,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_empty', full_name='franchisee_adjust.Adjust.is_empty', index=24,
      number=34, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status_plan', full_name='franchisee_adjust.Adjust.status_plan', index=25,
      number=35, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='franchisee_adjust.Adjust.reject_reason', index=26,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='franchisee_adjust.Adjust.total_amount', index=27,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_sales_amount', full_name='franchisee_adjust.Adjust.total_sales_amount', index=28,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=683,
  serialized_end=1425,
)


_SCHEDULESTATUSPLAN = _descriptor.Descriptor(
  name='ScheduleStatusPlan',
  full_name='franchisee_adjust.ScheduleStatusPlan',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='time', full_name='franchisee_adjust.ScheduleStatusPlan.time', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_status', full_name='franchisee_adjust.ScheduleStatusPlan.start_status', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_status', full_name='franchisee_adjust.ScheduleStatusPlan.end_status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='time_around', full_name='franchisee_adjust.ScheduleStatusPlan.time_around', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='doc_filter', full_name='franchisee_adjust.ScheduleStatusPlan.doc_filter', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1428,
  serialized_end=1573,
)


_GETADJUSTRESPONSE = _descriptor.Descriptor(
  name='GetAdjustResponse',
  full_name='franchisee_adjust.GetAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_adjust.GetAdjustResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_adjust.GetAdjustResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1575,
  serialized_end=1650,
)


_GETADJUSTPRODUCTREQUEST = _descriptor.Descriptor(
  name='GetAdjustProductRequest',
  full_name='franchisee_adjust.GetAdjustProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='franchisee_adjust.GetAdjustProductRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='franchisee_adjust.GetAdjustProductRequest.include_total', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='franchisee_adjust.GetAdjustProductRequest.order', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_adjust.GetAdjustProductRequest.offset', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_adjust.GetAdjustProductRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_adjust.GetAdjustProductRequest.lan', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1652,
  serialized_end=1778,
)


_ADJUSTPRODUCT = _descriptor.Descriptor(
  name='AdjustProduct',
  full_name='franchisee_adjust.AdjustProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_adjust.AdjustProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='franchisee_adjust.AdjustProduct.accounting_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='franchisee_adjust.AdjustProduct.accounting_unit_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='franchisee_adjust.AdjustProduct.accounting_unit_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='franchisee_adjust.AdjustProduct.accounting_unit_spec', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='franchisee_adjust.AdjustProduct.adjust_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_store', full_name='franchisee_adjust.AdjustProduct.adjust_store', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='franchisee_adjust.AdjustProduct.confirmed_quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_adjust.AdjustProduct.created_by', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_confirmed', full_name='franchisee_adjust.AdjustProduct.is_confirmed', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_number', full_name='franchisee_adjust.AdjustProduct.item_number', index=10,
      number=11, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='franchisee_adjust.AdjustProduct.material_number', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='franchisee_adjust.AdjustProduct.partner_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='franchisee_adjust.AdjustProduct.product_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='franchisee_adjust.AdjustProduct.product_id', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='franchisee_adjust.AdjustProduct.product_name', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='franchisee_adjust.AdjustProduct.quantity', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='franchisee_adjust.AdjustProduct.spec', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_quantity', full_name='franchisee_adjust.AdjustProduct.stocktake_quantity', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_unit_id', full_name='franchisee_adjust.AdjustProduct.stocktake_unit_id', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='franchisee_adjust.AdjustProduct.unit_id', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='franchisee_adjust.AdjustProduct.unit_name', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='franchisee_adjust.AdjustProduct.unit_spec', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='franchisee_adjust.AdjustProduct.updated_by', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='franchisee_adjust.AdjustProduct.adjust_date', index=24,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='franchisee_adjust.AdjustProduct.updated_at', index=25,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='franchisee_adjust.AdjustProduct.created_at', index=26,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='franchisee_adjust.AdjustProduct.user_id', index=27,
      number=28, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='franchisee_adjust.AdjustProduct.created_name', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='franchisee_adjust.AdjustProduct.updated_name', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='franchisee_adjust.AdjustProduct.reason_type', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_accounting_quantity', full_name='franchisee_adjust.AdjustProduct.convert_accounting_quantity', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_bom', full_name='franchisee_adjust.AdjustProduct.is_bom', index=32,
      number=33, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='franchisee_adjust.AdjustProduct.position_id', index=33,
      number=34, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sku_remark', full_name='franchisee_adjust.AdjustProduct.sku_remark', index=34,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='franchisee_adjust.AdjustProduct.units', index=35,
      number=36, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_name', full_name='franchisee_adjust.AdjustProduct.model_name', index=36,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='franchisee_adjust.AdjustProduct.tax_rate', index=37,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='franchisee_adjust.AdjustProduct.tax_price', index=38,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='franchisee_adjust.AdjustProduct.amount', index=39,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_amount', full_name='franchisee_adjust.AdjustProduct.tax_amount', index=40,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='franchisee_adjust.AdjustProduct.cost_price', index=41,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='franchisee_adjust.AdjustProduct.sales_amount', index=42,
      number=43, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='franchisee_adjust.AdjustProduct.sales_price', index=43,
      number=44, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1781,
  serialized_end=2864,
)


_GETADJUSTPRODUCTRESPONSE = _descriptor.Descriptor(
  name='GetAdjustProductResponse',
  full_name='franchisee_adjust.GetAdjustProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_adjust.GetAdjustProductResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_adjust.GetAdjustProductResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_rows', full_name='franchisee_adjust.GetAdjustProductResponse.position_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2867,
  serialized_end=3022,
)


_GETADJUSTBYIDREQUEST = _descriptor.Descriptor(
  name='GetAdjustByIDRequest',
  full_name='franchisee_adjust.GetAdjustByIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='franchisee_adjust.GetAdjustByIDRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3024,
  serialized_end=3065,
)


_CONFIRMADJUSTREQUEST = _descriptor.Descriptor(
  name='ConfirmAdjustRequest',
  full_name='franchisee_adjust.ConfirmAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='franchisee_adjust.ConfirmAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='franchisee_adjust.ConfirmAdjustRequest.branch_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3067,
  serialized_end=3129,
)


_CONFIRMADJUSTRESPONSE = _descriptor.Descriptor(
  name='ConfirmAdjustResponse',
  full_name='franchisee_adjust.ConfirmAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='franchisee_adjust.ConfirmAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3131,
  serialized_end=3170,
)


_SUBMITADJUSTREQUEST = _descriptor.Descriptor(
  name='SubmitAdjustRequest',
  full_name='franchisee_adjust.SubmitAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='franchisee_adjust.SubmitAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3172,
  serialized_end=3212,
)


_SUBMITADJUSTRESPONSE = _descriptor.Descriptor(
  name='SubmitAdjustResponse',
  full_name='franchisee_adjust.SubmitAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='franchisee_adjust.SubmitAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3214,
  serialized_end=3252,
)


_APPROVEADJUSTREQUEST = _descriptor.Descriptor(
  name='ApproveAdjustRequest',
  full_name='franchisee_adjust.ApproveAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='franchisee_adjust.ApproveAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3254,
  serialized_end=3295,
)


_APPROVEADJUSTRESPONSE = _descriptor.Descriptor(
  name='ApproveAdjustResponse',
  full_name='franchisee_adjust.ApproveAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='description', full_name='franchisee_adjust.ApproveAdjustResponse.description', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='franchisee_adjust.ApproveAdjustResponse.adjust_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_code', full_name='franchisee_adjust.ApproveAdjustResponse.adjust_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3297,
  serialized_end=3381,
)


_BATCHAPPROVEADJUSTREQUEST = _descriptor.Descriptor(
  name='BatchApproveAdjustRequest',
  full_name='franchisee_adjust.BatchApproveAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_ids', full_name='franchisee_adjust.BatchApproveAdjustRequest.adjust_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3383,
  serialized_end=3430,
)


_BATCHAPPROVEADJUSTRESPONSE = _descriptor.Descriptor(
  name='BatchApproveAdjustResponse',
  full_name='franchisee_adjust.BatchApproveAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_adjust.BatchApproveAdjustResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3432,
  serialized_end=3516,
)


_REJECTADJUSTREQUEST = _descriptor.Descriptor(
  name='RejectAdjustRequest',
  full_name='franchisee_adjust.RejectAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='franchisee_adjust.RejectAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='franchisee_adjust.RejectAdjustRequest.reject_reason', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3518,
  serialized_end=3581,
)


_REJECTADJUSTRESPONSE = _descriptor.Descriptor(
  name='RejectAdjustResponse',
  full_name='franchisee_adjust.RejectAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='description', full_name='franchisee_adjust.RejectAdjustResponse.description', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='franchisee_adjust.RejectAdjustResponse.adjust_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_code', full_name='franchisee_adjust.RejectAdjustResponse.adjust_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3583,
  serialized_end=3666,
)


_BATCHREJECTADJUSTREQUEST = _descriptor.Descriptor(
  name='BatchRejectAdjustRequest',
  full_name='franchisee_adjust.BatchRejectAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_ids', full_name='franchisee_adjust.BatchRejectAdjustRequest.adjust_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='franchisee_adjust.BatchRejectAdjustRequest.reject_reason', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3668,
  serialized_end=3737,
)


_BATCHREJECTADJUSTRESPONSE = _descriptor.Descriptor(
  name='BatchRejectAdjustResponse',
  full_name='franchisee_adjust.BatchRejectAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_adjust.BatchRejectAdjustResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3739,
  serialized_end=3821,
)


_GETADJUSTPRODUCTBYSTOREIDREQUEST = _descriptor.Descriptor(
  name='GetAdjustProductByStoreIDRequest',
  full_name='franchisee_adjust.GetAdjustProductByStoreIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='franchisee_adjust.GetAdjustProductByStoreIDRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='franchisee_adjust.GetAdjustProductByStoreIDRequest.limit', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='franchisee_adjust.GetAdjustProductByStoreIDRequest.offset', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='franchisee_adjust.GetAdjustProductByStoreIDRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='franchisee_adjust.GetAdjustProductByStoreIDRequest.adjust_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='franchisee_adjust.GetAdjustProductByStoreIDRequest.search', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='franchisee_adjust.GetAdjustProductByStoreIDRequest.search_fields', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='franchisee_adjust.GetAdjustProductByStoreIDRequest.category_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_adjust.GetAdjustProductByStoreIDRequest.lan', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by', full_name='franchisee_adjust.GetAdjustProductByStoreIDRequest.order_by', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='franchisee_adjust.GetAdjustProductByStoreIDRequest.sort', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3824,
  serialized_end=4085,
)


_CREATEADJUSTPRODUCTUINT = _descriptor.Descriptor(
  name='CreateAdjustProductUint',
  full_name='franchisee_adjust.CreateAdjustProductUint',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_adjust.CreateAdjustProductUint.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='franchisee_adjust.CreateAdjustProductUint.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='franchisee_adjust.CreateAdjustProductUint.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='franchisee_adjust.CreateAdjustProductUint.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='franchisee_adjust.CreateAdjustProductUint.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='franchisee_adjust.CreateAdjustProductUint.updated', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='franchisee_adjust.CreateAdjustProductUint.rate', index=6,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default', full_name='franchisee_adjust.CreateAdjustProductUint.default', index=7,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='franchisee_adjust.CreateAdjustProductUint.order', index=8,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase', full_name='franchisee_adjust.CreateAdjustProductUint.purchase', index=9,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales', full_name='franchisee_adjust.CreateAdjustProductUint.sales', index=10,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='franchisee_adjust.CreateAdjustProductUint.stocktake', index=11,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom', full_name='franchisee_adjust.CreateAdjustProductUint.bom', index=12,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default_stocktake', full_name='franchisee_adjust.CreateAdjustProductUint.default_stocktake', index=13,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer', full_name='franchisee_adjust.CreateAdjustProductUint.transfer', index=14,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='franchisee_adjust.CreateAdjustProductUint.tax_rate', index=15,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='franchisee_adjust.CreateAdjustProductUint.tax_price', index=16,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='franchisee_adjust.CreateAdjustProductUint.cost_price', index=17,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='franchisee_adjust.CreateAdjustProductUint.sales_price', index=18,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4088,
  serialized_end=4442,
)


_ADJUSTPRODUCTBYSTORE = _descriptor.Descriptor(
  name='AdjustProductByStore',
  full_name='franchisee_adjust.AdjustProductByStore',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='franchisee_adjust.AdjustProductByStore.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='loss_report_order', full_name='franchisee_adjust.AdjustProductByStore.loss_report_order', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='franchisee_adjust.AdjustProductByStore.product_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='franchisee_adjust.AdjustProductByStore.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_name', full_name='franchisee_adjust.AdjustProductByStore.model_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='franchisee_adjust.AdjustProductByStore.storage_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='franchisee_adjust.AdjustProductByStore.category_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='franchisee_adjust.AdjustProductByStore.category_name', index=7,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='franchisee_adjust.AdjustProductByStore.units', index=8,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='barcode', full_name='franchisee_adjust.AdjustProductByStore.barcode', index=9,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='real_inventory_qty', full_name='franchisee_adjust.AdjustProductByStore.real_inventory_qty', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4445,
  serialized_end=4748,
)


_GETADJUSTPRODUCTBYSTOREIDRESPONSE = _descriptor.Descriptor(
  name='GetAdjustProductByStoreIDResponse',
  full_name='franchisee_adjust.GetAdjustProductByStoreIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_adjust.GetAdjustProductByStoreIDResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_adjust.GetAdjustProductByStoreIDResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_unchanged_rows', full_name='franchisee_adjust.GetAdjustProductByStoreIDResponse.inventory_unchanged_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4751,
  serialized_end=4931,
)


_CATEGORYITEM = _descriptor.Descriptor(
  name='CategoryItem',
  full_name='franchisee_adjust.CategoryItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='category_id', full_name='franchisee_adjust.CategoryItem.category_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='franchisee_adjust.CategoryItem.category_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_count', full_name='franchisee_adjust.CategoryItem.product_count', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4933,
  serialized_end=5014,
)


_GETADJUSTPRODUCTCATEGORYBYSTOREIDREQUEST = _descriptor.Descriptor(
  name='GetAdjustProductCategoryByStoreIDRequest',
  full_name='franchisee_adjust.GetAdjustProductCategoryByStoreIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='franchisee_adjust.GetAdjustProductCategoryByStoreIDRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5016,
  serialized_end=5076,
)


_GETADJUSTPRODUCTCATEGORYBYSTOREIDRESPONSE = _descriptor.Descriptor(
  name='GetAdjustProductCategoryByStoreIDResponse',
  full_name='franchisee_adjust.GetAdjustProductCategoryByStoreIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='category_items', full_name='franchisee_adjust.GetAdjustProductCategoryByStoreIDResponse.category_items', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5078,
  serialized_end=5178,
)


_CREATEADJUSTPRODUCT = _descriptor.Descriptor(
  name='CreateAdjustProduct',
  full_name='franchisee_adjust.CreateAdjustProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_adjust.CreateAdjustProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='franchisee_adjust.CreateAdjustProduct.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='franchisee_adjust.CreateAdjustProduct.unit_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='franchisee_adjust.CreateAdjustProduct.quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='franchisee_adjust.CreateAdjustProduct.reason_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='franchisee_adjust.CreateAdjustProduct.position_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='skuRemark', full_name='franchisee_adjust.CreateAdjustProduct.skuRemark', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='franchisee_adjust.CreateAdjustProduct.tax_rate', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='franchisee_adjust.CreateAdjustProduct.tax_price', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='franchisee_adjust.CreateAdjustProduct.amount', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_amount', full_name='franchisee_adjust.CreateAdjustProduct.tax_amount', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='franchisee_adjust.CreateAdjustProduct.cost_price', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='franchisee_adjust.CreateAdjustProduct.sales_price', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='franchisee_adjust.CreateAdjustProduct.sales_amount', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5181,
  serialized_end=5496,
)


_CREATEADJUSTREQUEST = _descriptor.Descriptor(
  name='CreateAdjustRequest',
  full_name='franchisee_adjust.CreateAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_store', full_name='franchisee_adjust.CreateAdjustRequest.adjust_store', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='franchisee_adjust.CreateAdjustRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='franchisee_adjust.CreateAdjustRequest.reason_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_adjust.CreateAdjustRequest.remark', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='franchisee_adjust.CreateAdjustRequest.request_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='franchisee_adjust.CreateAdjustRequest.adjust_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='franchisee_adjust.CreateAdjustRequest.branch_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='franchisee_adjust.CreateAdjustRequest.position_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source', full_name='franchisee_adjust.CreateAdjustRequest.source', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_adjust.CreateAdjustRequest.attachments', index=9,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5499,
  serialized_end=5817,
)


_ATTACHMENTS = _descriptor.Descriptor(
  name='Attachments',
  full_name='franchisee_adjust.Attachments',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='franchisee_adjust.Attachments.type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='franchisee_adjust.Attachments.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5819,
  serialized_end=5859,
)


_UPDATEADJUSTREQUEST = _descriptor.Descriptor(
  name='UpdateAdjustRequest',
  full_name='franchisee_adjust.UpdateAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='franchisee_adjust.UpdateAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='franchisee_adjust.UpdateAdjustRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_adjust.UpdateAdjustRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='franchisee_adjust.UpdateAdjustRequest.reason_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='franchisee_adjust.UpdateAdjustRequest.adjust_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='franchisee_adjust.UpdateAdjustRequest.branch_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='franchisee_adjust.UpdateAdjustRequest.attachments', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='franchisee_adjust.UpdateAdjustRequest.position_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5862,
  serialized_end=6141,
)


_DELETEADJUSTREQUEST = _descriptor.Descriptor(
  name='DeleteAdjustRequest',
  full_name='franchisee_adjust.DeleteAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='franchisee_adjust.DeleteAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6143,
  serialized_end=6183,
)


_DELETEADJUSTRESPONSE = _descriptor.Descriptor(
  name='DeleteAdjustResponse',
  full_name='franchisee_adjust.DeleteAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='franchisee_adjust.DeleteAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6185,
  serialized_end=6223,
)


_DELETEADJUSTPRODUCTREQUEST = _descriptor.Descriptor(
  name='DeleteAdjustProductRequest',
  full_name='franchisee_adjust.DeleteAdjustProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='franchisee_adjust.DeleteAdjustProductRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='franchisee_adjust.DeleteAdjustProductRequest.ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6225,
  serialized_end=6285,
)


_DELETEADJUSTPRODUCTRESPONSE = _descriptor.Descriptor(
  name='DeleteAdjustProductResponse',
  full_name='franchisee_adjust.DeleteAdjustProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='franchisee_adjust.DeleteAdjustProductResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6287,
  serialized_end=6332,
)


_AD_TOTAL = _descriptor.Descriptor(
  name='AD_total',
  full_name='franchisee_adjust.AD_total',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='count', full_name='franchisee_adjust.AD_total.count', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_quantity', full_name='franchisee_adjust.AD_total.sum_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_accounting_quantity', full_name='franchisee_adjust.AD_total.sum_accounting_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_qty', full_name='franchisee_adjust.AD_total.sum_qty', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_accounting_qty', full_name='franchisee_adjust.AD_total.sum_accounting_qty', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6334,
  serialized_end=6459,
)


_CANCELADJUSTREQUEST = _descriptor.Descriptor(
  name='CancelAdjustRequest',
  full_name='franchisee_adjust.CancelAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='franchisee_adjust.CancelAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6461,
  serialized_end=6501,
)


_CANCELADJUSTRESPONSE = _descriptor.Descriptor(
  name='CancelAdjustResponse',
  full_name='franchisee_adjust.CancelAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='franchisee_adjust.CancelAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6503,
  serialized_end=6541,
)


_ADJUSTRESPONSE = _descriptor.Descriptor(
  name='AdjustResponse',
  full_name='franchisee_adjust.AdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='franchisee_adjust.AdjustResponse.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='franchisee_adjust.AdjustResponse.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='franchisee_adjust.AdjustResponse.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='franchisee_adjust.AdjustResponse.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='franchisee_adjust.AdjustResponse.category_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='franchisee_adjust.AdjustResponse.category_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='franchisee_adjust.AdjustResponse.product_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='franchisee_adjust.AdjustResponse.product_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='franchisee_adjust.AdjustResponse.product_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='franchisee_adjust.AdjustResponse.quantity', index=9,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='franchisee_adjust.AdjustResponse.store_code', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='franchisee_adjust.AdjustResponse.store_id', index=11,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='franchisee_adjust.AdjustResponse.store_name', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='franchisee_adjust.AdjustResponse.unit_id', index=13,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='franchisee_adjust.AdjustResponse.unit_name', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='franchisee_adjust.AdjustResponse.reason_type', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='franchisee_adjust.AdjustResponse.bom_product_id', index=16,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_code', full_name='franchisee_adjust.AdjustResponse.bom_product_code', index=17,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_name', full_name='franchisee_adjust.AdjustResponse.bom_product_name', index=18,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='franchisee_adjust.AdjustResponse.qty', index=19,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_id', full_name='franchisee_adjust.AdjustResponse.bom_unit_id', index=20,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_code', full_name='franchisee_adjust.AdjustResponse.bom_unit_code', index=21,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_name', full_name='franchisee_adjust.AdjustResponse.bom_unit_name', index=22,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_id', full_name='franchisee_adjust.AdjustResponse.bom_accounting_unit_id', index=23,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_code', full_name='franchisee_adjust.AdjustResponse.bom_accounting_unit_code', index=24,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_name', full_name='franchisee_adjust.AdjustResponse.bom_accounting_unit_name', index=25,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_qty', full_name='franchisee_adjust.AdjustResponse.accounting_qty', index=26,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='franchisee_adjust.AdjustResponse.price', index=27,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost', full_name='franchisee_adjust.AdjustResponse.cost', index=28,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='franchisee_adjust.AdjustResponse.adjust_date', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6544,
  serialized_end=7241,
)


_SKUREMARK_TAG = _descriptor.Descriptor(
  name='Tag',
  full_name='franchisee_adjust.SkuRemark.Tag',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_adjust.SkuRemark.Tag.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='franchisee_adjust.SkuRemark.Tag.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='franchisee_adjust.SkuRemark.Tag.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7355,
  serialized_end=7400,
)

_SKUREMARK = _descriptor.Descriptor(
  name='SkuRemark',
  full_name='franchisee_adjust.SkuRemark',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='franchisee_adjust.SkuRemark.name', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='values', full_name='franchisee_adjust.SkuRemark.values', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_SKUREMARK_TAG, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7244,
  serialized_end=7400,
)


_ADJUSTPOSITIONPRODUCTS = _descriptor.Descriptor(
  name='AdjustPositionProducts',
  full_name='franchisee_adjust.AdjustPositionProducts',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='position_id', full_name='franchisee_adjust.AdjustPositionProducts.position_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='franchisee_adjust.AdjustPositionProducts.position_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='franchisee_adjust.AdjustPositionProducts.position_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='franchisee_adjust.AdjustPositionProducts.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_adjust.AdjustPositionProducts.total', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7403,
  serialized_end=7561,
)


_GETADJUSTLOGREQUEST = _descriptor.Descriptor(
  name='GetAdjustLogRequest',
  full_name='franchisee_adjust.GetAdjustLogRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='franchisee_adjust.GetAdjustLogRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7563,
  serialized_end=7603,
)


_GETADJUSTLOGRESPONSE = _descriptor.Descriptor(
  name='GetAdjustLogResponse',
  full_name='franchisee_adjust.GetAdjustLogResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='franchisee_adjust.GetAdjustLogResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='franchisee_adjust.GetAdjustLogResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7605,
  serialized_end=7680,
)


_LOG = _descriptor.Descriptor(
  name='Log',
  full_name='franchisee_adjust.Log',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_adjust.Log.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='franchisee_adjust.Log.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='franchisee_adjust.Log.created_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='franchisee_adjust.Log.created_at', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason', full_name='franchisee_adjust.Log.reason', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='franchisee_adjust.Log.created_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7683,
  serialized_end=7822,
)


_CREATEDADJUSTPRODUCTBYCODE = _descriptor.Descriptor(
  name='CreatedAdjustProductByCode',
  full_name='franchisee_adjust.CreatedAdjustProductByCode',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='franchisee_adjust.CreatedAdjustProductByCode.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='franchisee_adjust.CreatedAdjustProductByCode.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_code', full_name='franchisee_adjust.CreatedAdjustProductByCode.unit_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='franchisee_adjust.CreatedAdjustProductByCode.quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='franchisee_adjust.CreatedAdjustProductByCode.reason_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='skuRemark', full_name='franchisee_adjust.CreatedAdjustProductByCode.skuRemark', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7825,
  serialized_end=7994,
)


_CREATEDADJUSTBYCODEREQUEST = _descriptor.Descriptor(
  name='CreatedAdjustByCodeRequest',
  full_name='franchisee_adjust.CreatedAdjustByCodeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_store', full_name='franchisee_adjust.CreatedAdjustByCodeRequest.adjust_store', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='franchisee_adjust.CreatedAdjustByCodeRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='franchisee_adjust.CreatedAdjustByCodeRequest.reason_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='franchisee_adjust.CreatedAdjustByCodeRequest.remark', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='franchisee_adjust.CreatedAdjustByCodeRequest.request_id', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='franchisee_adjust.CreatedAdjustByCodeRequest.adjust_date', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='franchisee_adjust.CreatedAdjustByCodeRequest.lan', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7997,
  serialized_end=8203,
)

_CREATEADJUSTRESPONSE.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTREQUEST.fields_by_name['status'].enum_type = _GETADJUSTREQUEST_STATUS
_GETADJUSTREQUEST_STATUS.containing_type = _GETADJUSTREQUEST
_ADJUST.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUST.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUST.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_ADJUST.fields_by_name['status_plan'].message_type = _SCHEDULESTATUSPLAN
_SCHEDULESTATUSPLAN.fields_by_name['time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTRESPONSE.fields_by_name['rows'].message_type = _ADJUST
_ADJUSTPRODUCT.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUSTPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUSTPRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUSTPRODUCT.fields_by_name['units'].message_type = _CREATEADJUSTPRODUCTUINT
_GETADJUSTPRODUCTRESPONSE.fields_by_name['rows'].message_type = _ADJUSTPRODUCT
_GETADJUSTPRODUCTRESPONSE.fields_by_name['position_rows'].message_type = _ADJUSTPOSITIONPRODUCTS
_BATCHAPPROVEADJUSTRESPONSE.fields_by_name['rows'].message_type = _APPROVEADJUSTRESPONSE
_BATCHREJECTADJUSTRESPONSE.fields_by_name['rows'].message_type = _REJECTADJUSTRESPONSE
_GETADJUSTPRODUCTBYSTOREIDREQUEST.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUSTPRODUCTBYSTORE.fields_by_name['units'].message_type = _CREATEADJUSTPRODUCTUINT
_GETADJUSTPRODUCTBYSTOREIDRESPONSE.fields_by_name['rows'].message_type = _ADJUSTPRODUCTBYSTORE
_GETADJUSTPRODUCTBYSTOREIDRESPONSE.fields_by_name['inventory_unchanged_rows'].message_type = _ADJUSTPRODUCTBYSTORE
_GETADJUSTPRODUCTCATEGORYBYSTOREIDRESPONSE.fields_by_name['category_items'].message_type = _CATEGORYITEM
_CREATEADJUSTPRODUCT.fields_by_name['skuRemark'].message_type = _SKUREMARK
_CREATEADJUSTREQUEST.fields_by_name['products'].message_type = _CREATEADJUSTPRODUCT
_CREATEADJUSTREQUEST.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEADJUSTREQUEST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_UPDATEADJUSTREQUEST.fields_by_name['products'].message_type = _CREATEADJUSTPRODUCT
_UPDATEADJUSTREQUEST.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATEADJUSTREQUEST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_SKUREMARK_TAG.containing_type = _SKUREMARK
_SKUREMARK.fields_by_name['name'].message_type = _SKUREMARK_TAG
_SKUREMARK.fields_by_name['values'].message_type = _SKUREMARK_TAG
_ADJUSTPOSITIONPRODUCTS.fields_by_name['products'].message_type = _ADJUSTPRODUCT
_GETADJUSTLOGRESPONSE.fields_by_name['rows'].message_type = _LOG
_LOG.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEDADJUSTPRODUCTBYCODE.fields_by_name['skuRemark'].message_type = _SKUREMARK
_CREATEDADJUSTBYCODEREQUEST.fields_by_name['products'].message_type = _CREATEDADJUSTPRODUCTBYCODE
DESCRIPTOR.message_types_by_name['CreateAdjustResponse'] = _CREATEADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustRequest'] = _GETADJUSTREQUEST
DESCRIPTOR.message_types_by_name['Adjust'] = _ADJUST
DESCRIPTOR.message_types_by_name['ScheduleStatusPlan'] = _SCHEDULESTATUSPLAN
DESCRIPTOR.message_types_by_name['GetAdjustResponse'] = _GETADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustProductRequest'] = _GETADJUSTPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['AdjustProduct'] = _ADJUSTPRODUCT
DESCRIPTOR.message_types_by_name['GetAdjustProductResponse'] = _GETADJUSTPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustByIDRequest'] = _GETADJUSTBYIDREQUEST
DESCRIPTOR.message_types_by_name['ConfirmAdjustRequest'] = _CONFIRMADJUSTREQUEST
DESCRIPTOR.message_types_by_name['ConfirmAdjustResponse'] = _CONFIRMADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['SubmitAdjustRequest'] = _SUBMITADJUSTREQUEST
DESCRIPTOR.message_types_by_name['SubmitAdjustResponse'] = _SUBMITADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['ApproveAdjustRequest'] = _APPROVEADJUSTREQUEST
DESCRIPTOR.message_types_by_name['ApproveAdjustResponse'] = _APPROVEADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['BatchApproveAdjustRequest'] = _BATCHAPPROVEADJUSTREQUEST
DESCRIPTOR.message_types_by_name['BatchApproveAdjustResponse'] = _BATCHAPPROVEADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['RejectAdjustRequest'] = _REJECTADJUSTREQUEST
DESCRIPTOR.message_types_by_name['RejectAdjustResponse'] = _REJECTADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['BatchRejectAdjustRequest'] = _BATCHREJECTADJUSTREQUEST
DESCRIPTOR.message_types_by_name['BatchRejectAdjustResponse'] = _BATCHREJECTADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustProductByStoreIDRequest'] = _GETADJUSTPRODUCTBYSTOREIDREQUEST
DESCRIPTOR.message_types_by_name['CreateAdjustProductUint'] = _CREATEADJUSTPRODUCTUINT
DESCRIPTOR.message_types_by_name['AdjustProductByStore'] = _ADJUSTPRODUCTBYSTORE
DESCRIPTOR.message_types_by_name['GetAdjustProductByStoreIDResponse'] = _GETADJUSTPRODUCTBYSTOREIDRESPONSE
DESCRIPTOR.message_types_by_name['CategoryItem'] = _CATEGORYITEM
DESCRIPTOR.message_types_by_name['GetAdjustProductCategoryByStoreIDRequest'] = _GETADJUSTPRODUCTCATEGORYBYSTOREIDREQUEST
DESCRIPTOR.message_types_by_name['GetAdjustProductCategoryByStoreIDResponse'] = _GETADJUSTPRODUCTCATEGORYBYSTOREIDRESPONSE
DESCRIPTOR.message_types_by_name['CreateAdjustProduct'] = _CREATEADJUSTPRODUCT
DESCRIPTOR.message_types_by_name['CreateAdjustRequest'] = _CREATEADJUSTREQUEST
DESCRIPTOR.message_types_by_name['Attachments'] = _ATTACHMENTS
DESCRIPTOR.message_types_by_name['UpdateAdjustRequest'] = _UPDATEADJUSTREQUEST
DESCRIPTOR.message_types_by_name['DeleteAdjustRequest'] = _DELETEADJUSTREQUEST
DESCRIPTOR.message_types_by_name['DeleteAdjustResponse'] = _DELETEADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['DeleteAdjustProductRequest'] = _DELETEADJUSTPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['DeleteAdjustProductResponse'] = _DELETEADJUSTPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['AD_total'] = _AD_TOTAL
DESCRIPTOR.message_types_by_name['CancelAdjustRequest'] = _CANCELADJUSTREQUEST
DESCRIPTOR.message_types_by_name['CancelAdjustResponse'] = _CANCELADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['AdjustResponse'] = _ADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['SkuRemark'] = _SKUREMARK
DESCRIPTOR.message_types_by_name['AdjustPositionProducts'] = _ADJUSTPOSITIONPRODUCTS
DESCRIPTOR.message_types_by_name['GetAdjustLogRequest'] = _GETADJUSTLOGREQUEST
DESCRIPTOR.message_types_by_name['GetAdjustLogResponse'] = _GETADJUSTLOGRESPONSE
DESCRIPTOR.message_types_by_name['Log'] = _LOG
DESCRIPTOR.message_types_by_name['CreatedAdjustProductByCode'] = _CREATEDADJUSTPRODUCTBYCODE
DESCRIPTOR.message_types_by_name['CreatedAdjustByCodeRequest'] = _CREATEDADJUSTBYCODEREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CreateAdjustResponse = _reflection.GeneratedProtocolMessageType('CreateAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEADJUSTRESPONSE,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.CreateAdjustResponse)
  ))
_sym_db.RegisterMessage(CreateAdjustResponse)

GetAdjustRequest = _reflection.GeneratedProtocolMessageType('GetAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTREQUEST,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.GetAdjustRequest)
  ))
_sym_db.RegisterMessage(GetAdjustRequest)

Adjust = _reflection.GeneratedProtocolMessageType('Adjust', (_message.Message,), dict(
  DESCRIPTOR = _ADJUST,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.Adjust)
  ))
_sym_db.RegisterMessage(Adjust)

ScheduleStatusPlan = _reflection.GeneratedProtocolMessageType('ScheduleStatusPlan', (_message.Message,), dict(
  DESCRIPTOR = _SCHEDULESTATUSPLAN,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.ScheduleStatusPlan)
  ))
_sym_db.RegisterMessage(ScheduleStatusPlan)

GetAdjustResponse = _reflection.GeneratedProtocolMessageType('GetAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTRESPONSE,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.GetAdjustResponse)
  ))
_sym_db.RegisterMessage(GetAdjustResponse)

GetAdjustProductRequest = _reflection.GeneratedProtocolMessageType('GetAdjustProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTPRODUCTREQUEST,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.GetAdjustProductRequest)
  ))
_sym_db.RegisterMessage(GetAdjustProductRequest)

AdjustProduct = _reflection.GeneratedProtocolMessageType('AdjustProduct', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTPRODUCT,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.AdjustProduct)
  ))
_sym_db.RegisterMessage(AdjustProduct)

GetAdjustProductResponse = _reflection.GeneratedProtocolMessageType('GetAdjustProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTPRODUCTRESPONSE,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.GetAdjustProductResponse)
  ))
_sym_db.RegisterMessage(GetAdjustProductResponse)

GetAdjustByIDRequest = _reflection.GeneratedProtocolMessageType('GetAdjustByIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTBYIDREQUEST,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.GetAdjustByIDRequest)
  ))
_sym_db.RegisterMessage(GetAdjustByIDRequest)

ConfirmAdjustRequest = _reflection.GeneratedProtocolMessageType('ConfirmAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMADJUSTREQUEST,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.ConfirmAdjustRequest)
  ))
_sym_db.RegisterMessage(ConfirmAdjustRequest)

ConfirmAdjustResponse = _reflection.GeneratedProtocolMessageType('ConfirmAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMADJUSTRESPONSE,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.ConfirmAdjustResponse)
  ))
_sym_db.RegisterMessage(ConfirmAdjustResponse)

SubmitAdjustRequest = _reflection.GeneratedProtocolMessageType('SubmitAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITADJUSTREQUEST,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.SubmitAdjustRequest)
  ))
_sym_db.RegisterMessage(SubmitAdjustRequest)

SubmitAdjustResponse = _reflection.GeneratedProtocolMessageType('SubmitAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITADJUSTRESPONSE,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.SubmitAdjustResponse)
  ))
_sym_db.RegisterMessage(SubmitAdjustResponse)

ApproveAdjustRequest = _reflection.GeneratedProtocolMessageType('ApproveAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _APPROVEADJUSTREQUEST,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.ApproveAdjustRequest)
  ))
_sym_db.RegisterMessage(ApproveAdjustRequest)

ApproveAdjustResponse = _reflection.GeneratedProtocolMessageType('ApproveAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _APPROVEADJUSTRESPONSE,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.ApproveAdjustResponse)
  ))
_sym_db.RegisterMessage(ApproveAdjustResponse)

BatchApproveAdjustRequest = _reflection.GeneratedProtocolMessageType('BatchApproveAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _BATCHAPPROVEADJUSTREQUEST,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.BatchApproveAdjustRequest)
  ))
_sym_db.RegisterMessage(BatchApproveAdjustRequest)

BatchApproveAdjustResponse = _reflection.GeneratedProtocolMessageType('BatchApproveAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _BATCHAPPROVEADJUSTRESPONSE,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.BatchApproveAdjustResponse)
  ))
_sym_db.RegisterMessage(BatchApproveAdjustResponse)

RejectAdjustRequest = _reflection.GeneratedProtocolMessageType('RejectAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _REJECTADJUSTREQUEST,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.RejectAdjustRequest)
  ))
_sym_db.RegisterMessage(RejectAdjustRequest)

RejectAdjustResponse = _reflection.GeneratedProtocolMessageType('RejectAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _REJECTADJUSTRESPONSE,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.RejectAdjustResponse)
  ))
_sym_db.RegisterMessage(RejectAdjustResponse)

BatchRejectAdjustRequest = _reflection.GeneratedProtocolMessageType('BatchRejectAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _BATCHREJECTADJUSTREQUEST,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.BatchRejectAdjustRequest)
  ))
_sym_db.RegisterMessage(BatchRejectAdjustRequest)

BatchRejectAdjustResponse = _reflection.GeneratedProtocolMessageType('BatchRejectAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _BATCHREJECTADJUSTRESPONSE,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.BatchRejectAdjustResponse)
  ))
_sym_db.RegisterMessage(BatchRejectAdjustResponse)

GetAdjustProductByStoreIDRequest = _reflection.GeneratedProtocolMessageType('GetAdjustProductByStoreIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTPRODUCTBYSTOREIDREQUEST,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.GetAdjustProductByStoreIDRequest)
  ))
_sym_db.RegisterMessage(GetAdjustProductByStoreIDRequest)

CreateAdjustProductUint = _reflection.GeneratedProtocolMessageType('CreateAdjustProductUint', (_message.Message,), dict(
  DESCRIPTOR = _CREATEADJUSTPRODUCTUINT,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.CreateAdjustProductUint)
  ))
_sym_db.RegisterMessage(CreateAdjustProductUint)

AdjustProductByStore = _reflection.GeneratedProtocolMessageType('AdjustProductByStore', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTPRODUCTBYSTORE,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.AdjustProductByStore)
  ))
_sym_db.RegisterMessage(AdjustProductByStore)

GetAdjustProductByStoreIDResponse = _reflection.GeneratedProtocolMessageType('GetAdjustProductByStoreIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTPRODUCTBYSTOREIDRESPONSE,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.GetAdjustProductByStoreIDResponse)
  ))
_sym_db.RegisterMessage(GetAdjustProductByStoreIDResponse)

CategoryItem = _reflection.GeneratedProtocolMessageType('CategoryItem', (_message.Message,), dict(
  DESCRIPTOR = _CATEGORYITEM,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.CategoryItem)
  ))
_sym_db.RegisterMessage(CategoryItem)

GetAdjustProductCategoryByStoreIDRequest = _reflection.GeneratedProtocolMessageType('GetAdjustProductCategoryByStoreIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTPRODUCTCATEGORYBYSTOREIDREQUEST,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.GetAdjustProductCategoryByStoreIDRequest)
  ))
_sym_db.RegisterMessage(GetAdjustProductCategoryByStoreIDRequest)

GetAdjustProductCategoryByStoreIDResponse = _reflection.GeneratedProtocolMessageType('GetAdjustProductCategoryByStoreIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTPRODUCTCATEGORYBYSTOREIDRESPONSE,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.GetAdjustProductCategoryByStoreIDResponse)
  ))
_sym_db.RegisterMessage(GetAdjustProductCategoryByStoreIDResponse)

CreateAdjustProduct = _reflection.GeneratedProtocolMessageType('CreateAdjustProduct', (_message.Message,), dict(
  DESCRIPTOR = _CREATEADJUSTPRODUCT,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.CreateAdjustProduct)
  ))
_sym_db.RegisterMessage(CreateAdjustProduct)

CreateAdjustRequest = _reflection.GeneratedProtocolMessageType('CreateAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEADJUSTREQUEST,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.CreateAdjustRequest)
  ))
_sym_db.RegisterMessage(CreateAdjustRequest)

Attachments = _reflection.GeneratedProtocolMessageType('Attachments', (_message.Message,), dict(
  DESCRIPTOR = _ATTACHMENTS,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.Attachments)
  ))
_sym_db.RegisterMessage(Attachments)

UpdateAdjustRequest = _reflection.GeneratedProtocolMessageType('UpdateAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEADJUSTREQUEST,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.UpdateAdjustRequest)
  ))
_sym_db.RegisterMessage(UpdateAdjustRequest)

DeleteAdjustRequest = _reflection.GeneratedProtocolMessageType('DeleteAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEADJUSTREQUEST,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.DeleteAdjustRequest)
  ))
_sym_db.RegisterMessage(DeleteAdjustRequest)

DeleteAdjustResponse = _reflection.GeneratedProtocolMessageType('DeleteAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETEADJUSTRESPONSE,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.DeleteAdjustResponse)
  ))
_sym_db.RegisterMessage(DeleteAdjustResponse)

DeleteAdjustProductRequest = _reflection.GeneratedProtocolMessageType('DeleteAdjustProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEADJUSTPRODUCTREQUEST,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.DeleteAdjustProductRequest)
  ))
_sym_db.RegisterMessage(DeleteAdjustProductRequest)

DeleteAdjustProductResponse = _reflection.GeneratedProtocolMessageType('DeleteAdjustProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETEADJUSTPRODUCTRESPONSE,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.DeleteAdjustProductResponse)
  ))
_sym_db.RegisterMessage(DeleteAdjustProductResponse)

AD_total = _reflection.GeneratedProtocolMessageType('AD_total', (_message.Message,), dict(
  DESCRIPTOR = _AD_TOTAL,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.AD_total)
  ))
_sym_db.RegisterMessage(AD_total)

CancelAdjustRequest = _reflection.GeneratedProtocolMessageType('CancelAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _CANCELADJUSTREQUEST,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.CancelAdjustRequest)
  ))
_sym_db.RegisterMessage(CancelAdjustRequest)

CancelAdjustResponse = _reflection.GeneratedProtocolMessageType('CancelAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _CANCELADJUSTRESPONSE,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.CancelAdjustResponse)
  ))
_sym_db.RegisterMessage(CancelAdjustResponse)

AdjustResponse = _reflection.GeneratedProtocolMessageType('AdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTRESPONSE,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.AdjustResponse)
  ))
_sym_db.RegisterMessage(AdjustResponse)

SkuRemark = _reflection.GeneratedProtocolMessageType('SkuRemark', (_message.Message,), dict(

  Tag = _reflection.GeneratedProtocolMessageType('Tag', (_message.Message,), dict(
    DESCRIPTOR = _SKUREMARK_TAG,
    __module__ = 'frs_store.franchisee_adjust_pb2'
    # @@protoc_insertion_point(class_scope:franchisee_adjust.SkuRemark.Tag)
    ))
  ,
  DESCRIPTOR = _SKUREMARK,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.SkuRemark)
  ))
_sym_db.RegisterMessage(SkuRemark)
_sym_db.RegisterMessage(SkuRemark.Tag)

AdjustPositionProducts = _reflection.GeneratedProtocolMessageType('AdjustPositionProducts', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTPOSITIONPRODUCTS,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.AdjustPositionProducts)
  ))
_sym_db.RegisterMessage(AdjustPositionProducts)

GetAdjustLogRequest = _reflection.GeneratedProtocolMessageType('GetAdjustLogRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTLOGREQUEST,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.GetAdjustLogRequest)
  ))
_sym_db.RegisterMessage(GetAdjustLogRequest)

GetAdjustLogResponse = _reflection.GeneratedProtocolMessageType('GetAdjustLogResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTLOGRESPONSE,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.GetAdjustLogResponse)
  ))
_sym_db.RegisterMessage(GetAdjustLogResponse)

Log = _reflection.GeneratedProtocolMessageType('Log', (_message.Message,), dict(
  DESCRIPTOR = _LOG,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.Log)
  ))
_sym_db.RegisterMessage(Log)

CreatedAdjustProductByCode = _reflection.GeneratedProtocolMessageType('CreatedAdjustProductByCode', (_message.Message,), dict(
  DESCRIPTOR = _CREATEDADJUSTPRODUCTBYCODE,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.CreatedAdjustProductByCode)
  ))
_sym_db.RegisterMessage(CreatedAdjustProductByCode)

CreatedAdjustByCodeRequest = _reflection.GeneratedProtocolMessageType('CreatedAdjustByCodeRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEDADJUSTBYCODEREQUEST,
  __module__ = 'frs_store.franchisee_adjust_pb2'
  # @@protoc_insertion_point(class_scope:franchisee_adjust.CreatedAdjustByCodeRequest)
  ))
_sym_db.RegisterMessage(CreatedAdjustByCodeRequest)



_FRANCHISEEADJUST = _descriptor.ServiceDescriptor(
  name='FranchiseeAdjust',
  full_name='franchisee_adjust.FranchiseeAdjust',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=8206,
  serialized_end=11183,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetAdjust',
    full_name='franchisee_adjust.FranchiseeAdjust.GetAdjust',
    index=0,
    containing_service=None,
    input_type=_GETADJUSTREQUEST,
    output_type=_GETADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\002\"\022 /api/v2/supply/franchisee/adjust'),
  ),
  _descriptor.MethodDescriptor(
    name='CreateAdjust',
    full_name='franchisee_adjust.FranchiseeAdjust.CreateAdjust',
    index=1,
    containing_service=None,
    input_type=_CREATEADJUSTREQUEST,
    output_type=_ADJUST,
    serialized_options=_b('\202\323\344\223\002,\"\'/api/v2/supply/franchisee/adjust/create:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustProduct',
    full_name='franchisee_adjust.FranchiseeAdjust.GetAdjustProduct',
    index=2,
    containing_service=None,
    input_type=_GETADJUSTPRODUCTREQUEST,
    output_type=_GETADJUSTPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\0026\0224/api/v2/supply/franchisee/adjust/{adjust_id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustByID',
    full_name='franchisee_adjust.FranchiseeAdjust.GetAdjustByID',
    index=3,
    containing_service=None,
    input_type=_GETADJUSTBYIDREQUEST,
    output_type=_ADJUST,
    serialized_options=_b('\202\323\344\223\002.\022,/api/v2/supply/franchisee/adjust/{adjust_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmAdjust',
    full_name='franchisee_adjust.FranchiseeAdjust.ConfirmAdjust',
    index=4,
    containing_service=None,
    input_type=_CONFIRMADJUSTREQUEST,
    output_type=_CONFIRMADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\"4/api/v2/supply/franchisee/adjust/{adjust_id}/confirm:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='SubmitAdjust',
    full_name='franchisee_adjust.FranchiseeAdjust.SubmitAdjust',
    index=5,
    containing_service=None,
    input_type=_SUBMITADJUSTREQUEST,
    output_type=_SUBMITADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\"3/api/v2/supply/franchisee/adjust/{adjust_id}/submit:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ApproveAdjust',
    full_name='franchisee_adjust.FranchiseeAdjust.ApproveAdjust',
    index=6,
    containing_service=None,
    input_type=_APPROVEADJUSTREQUEST,
    output_type=_APPROVEADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\"4/api/v2/supply/franchisee/adjust/{adjust_id}/approve:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='BatchApproveAdjust',
    full_name='franchisee_adjust.FranchiseeAdjust.BatchApproveAdjust',
    index=7,
    containing_service=None,
    input_type=_BATCHAPPROVEADJUSTREQUEST,
    output_type=_BATCHAPPROVEADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\0023\"./api/v2/supply/franchisee/adjust/approve/batch:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='RejectAdjust',
    full_name='franchisee_adjust.FranchiseeAdjust.RejectAdjust',
    index=8,
    containing_service=None,
    input_type=_REJECTADJUSTREQUEST,
    output_type=_REJECTADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\"3/api/v2/supply/franchisee/adjust/{adjust_id}/reject:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='BatchRejectAdjust',
    full_name='franchisee_adjust.FranchiseeAdjust.BatchRejectAdjust',
    index=9,
    containing_service=None,
    input_type=_BATCHREJECTADJUSTREQUEST,
    output_type=_BATCHREJECTADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\"-/api/v2/supply/franchisee/adjust/reject/batch:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustProductByStoreID',
    full_name='franchisee_adjust.FranchiseeAdjust.GetAdjustProductByStoreID',
    index=10,
    containing_service=None,
    input_type=_GETADJUSTPRODUCTBYSTOREIDREQUEST,
    output_type=_GETADJUSTPRODUCTBYSTOREIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0021\022//api/v2/supply/franchisee/adjust/store/products'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustProductCategoryByStoreID',
    full_name='franchisee_adjust.FranchiseeAdjust.GetAdjustProductCategoryByStoreID',
    index=11,
    containing_service=None,
    input_type=_GETADJUSTPRODUCTCATEGORYBYSTOREIDREQUEST,
    output_type=_GETADJUSTPRODUCTCATEGORYBYSTOREIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0021\022//api/v2/supply/franchisee/adjust/store/category'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateAdjust',
    full_name='franchisee_adjust.FranchiseeAdjust.UpdateAdjust',
    index=12,
    containing_service=None,
    input_type=_UPDATEADJUSTREQUEST,
    output_type=_ADJUST,
    serialized_options=_b('\202\323\344\223\0028\"3/api/v2/supply/franchisee/adjust/{adjust_id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteAdjust',
    full_name='franchisee_adjust.FranchiseeAdjust.DeleteAdjust',
    index=13,
    containing_service=None,
    input_type=_DELETEADJUSTREQUEST,
    output_type=_DELETEADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\"3/api/v2/supply/franchisee/adjust/{adjust_id}/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteAdjustProduct',
    full_name='franchisee_adjust.FranchiseeAdjust.DeleteAdjustProduct',
    index=14,
    containing_service=None,
    input_type=_DELETEADJUSTPRODUCTREQUEST,
    output_type=_DELETEADJUSTPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\002@\";/api/v2/supply/franchisee/adjust/product/{adjust_id}/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CancelAdjust',
    full_name='franchisee_adjust.FranchiseeAdjust.CancelAdjust',
    index=15,
    containing_service=None,
    input_type=_CANCELADJUSTREQUEST,
    output_type=_CANCELADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\"3/api/v2/supply/franchisee/adjust/auto_create/cancel:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='CreatedAdjustByCode',
    full_name='franchisee_adjust.FranchiseeAdjust.CreatedAdjustByCode',
    index=16,
    containing_service=None,
    input_type=_CREATEDADJUSTBYCODEREQUEST,
    output_type=_ADJUST,
    serialized_options=_b('\202\323\344\223\002)\"$/api/v2/supply/franchisee/adjust/pos:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustLog',
    full_name='franchisee_adjust.FranchiseeAdjust.GetAdjustLog',
    index=17,
    containing_service=None,
    input_type=_GETADJUSTLOGREQUEST,
    output_type=_GETADJUSTLOGRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\0220/api/v2/supply/franchisee/adjust/{adjust_id}/log'),
  ),
])
_sym_db.RegisterServiceDescriptor(_FRANCHISEEADJUST)

DESCRIPTOR.services_by_name['FranchiseeAdjust'] = _FRANCHISEEADJUST

# @@protoc_insertion_point(module_scope)
