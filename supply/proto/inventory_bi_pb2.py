# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: inventory_bi.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='inventory_bi.proto',
  package='inventory_bi',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x12inventory_bi.proto\x12\x0cinventory_bi\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"]\n\x1cQuerySnapshotForSalesRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x03\x12\x10\n\x08\x62iz_date\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x0b\n\x03lan\x18\x04 \x01(\t\"w\n\x10SnapshotForSales\x12\x10\n\x08store_id\x18\x01 \x01(\x03\x12\x12\n\nproduct_id\x18\x02 \x01(\x03\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x15\n\ryesterday_qty\x18\x04 \x01(\x01\x12\x18\n\x10week_average_qty\x18\x05 \x01(\x01\"M\n\x1dQuerySnapshotForSalesResponse\x12,\n\x04rows\x18\x01 \x03(\x0b\x32\x1e.inventory_bi.SnapshotForSales\"\xd3\x01\n\x1fQueryInventoryLogInTotalRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x03\x12\x13\n\x0bproduct_ids\x18\x02 \x03(\x03\x12.\n\nstart_time\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x05 \x01(\x03\x12\x0e\n\x06offset\x18\x06 \x01(\x03\x12\x0b\n\x03lan\x18\x07 \x01(\t\"\\\n QueryInventoryLogInTotalResponse\x12\r\n\x05total\x18\x01 \x01(\x03\x12)\n\x04rows\x18\x02 \x03(\x0b\x32\x1b.inventory_bi.GroupEventLog\"\x89\x01\n\rGroupEventLog\x12\x12\n\nproduct_id\x18\x01 \x01(\x03\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x03 \x01(\t\x12\x0b\n\x03qty\x18\x04 \x01(\x01\x12\r\n\x05\x63ount\x18\x05 \x01(\x03\x12\x14\n\x0cproduct_name\x18\x06 \x01(\t\x12\x14\n\x0cproduct_code\x18\x07 \x01(\t\"\x8a\x02\n\x18RealtimeInventoryRequest\x12\x12\n\nbranch_ids\x18\x01 \x03(\x04\x12\x13\n\x0bgeo_regions\x18\x02 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x03 \x03(\t\x12\x13\n\x0bproduct_ids\x18\x04 \x03(\x04\x12\r\n\x05limit\x18\x05 \x01(\x05\x12\x0e\n\x06offset\x18\x06 \x01(\x05\x12\r\n\x05order\x18\x07 \x01(\t\x12\x0c\n\x04sort\x18\x08 \x01(\t\x12\x0f\n\x07\x65xclude\x18\t \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\n \x01(\t\x12\x0b\n\x03lan\x18\x0b \x01(\t\x12\x14\n\x0cposition_ids\x18\x0c \x03(\x04\x12\x15\n\rreturn_fields\x18\r \x01(\t\"Q\n\x19RealtimeInventoryResponse\x12%\n\x04rows\x18\x01 \x03(\x0b\x32\x17.inventory_bi.Inventory\x12\r\n\x05total\x18\x02 \x01(\x05\"\xf6\x02\n\x15\x44\x61ilyInventoryRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x02 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x03 \x03(\x04\x12.\n\nstart_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x06 \x01(\x05\x12\x0e\n\x06offset\x18\x07 \x01(\x05\x12\x0c\n\x04\x63ode\x18\t \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\n \x01(\t\x12\x0e\n\x06if_pre\x18\x0b \x01(\t\x12\x0e\n\x06if_end\x18\x0c \x01(\t\x12\x15\n\rexclude_empty\x18\r \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x0e \x01(\t\x12\x0b\n\x03lan\x18\x0f \x01(\t\x12\x14\n\x0cposition_ids\x18\x10 \x03(\x03\x12\x15\n\rreturn_fields\x18\x11 \x01(\t\"S\n\x16\x44\x61ilyInventoryResponse\x12*\n\x04rows\x18\x01 \x03(\x0b\x32\x1c.inventory_bi.DailyInventory\x12\r\n\x05total\x18\x02 \x01(\x05\"v\n\x15QueryAccoutingRequest\x12\x10\n\x08\x62\x61tch_id\x18\x01 \x01(\x04\x12\x10\n\x08\x62\x61tch_no\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x04 \x01(\t\x12\x0e\n\x06\x64\x65tail\x18\x05 \x01(\x08\x12\x0b\n\x03lan\x18\x06 \x01(\t\"\x18\n\x16QueryAccoutingResponse\"\xd7\x02\n\x18QueryInventoryLogRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x13\n\x0bproduct_ids\x18\x02 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x03 \x03(\x04\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x05 \x01(\t\x12\r\n\x05limit\x18\x06 \x01(\x03\x12\x0e\n\x06offset\x18\x07 \x01(\x03\x12.\n\nstart_date\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\norder_type\x18\n \x01(\t\x12\x14\n\x0c\x61\x63\x63ount_type\x18\x0b \x01(\t\x12\x0b\n\x03lan\x18\x0c \x01(\t\x12\x14\n\x0cposition_ids\x18\r \x03(\x04\x12\x15\n\rreturn_fields\x18\x0e \x01(\t\"h\n\x19QueryInventoryLogResponse\x12(\n\x04rows\x18\x01 \x03(\x0b\x32\x1a.inventory_bi.InventoryLog\x12\r\n\x05total\x18\x02 \x01(\x05\x12\x12\n\namount_sum\x18\x03 \x01(\x01\"Q\n QueryMcuRealtimeInventoryRequest\x12\x0b\n\x03mcu\x18\x01 \x01(\t\x12\x13\n\x0bproduct_ids\x18\x02 \x03(\x04\x12\x0b\n\x03lan\x18\x03 \x01(\t\"\\\n!QueryMcuRealtimeInventoryResponse\x12(\n\x04rows\x18\x01 \x03(\x0b\x32\x1a.inventory_bi.McuInventory\x12\r\n\x05total\x18\x02 \x01(\x05\"\xd8\x06\n\tInventory\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nproduct_id\x18\x05 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x08 \x01(\x04\x12\x15\n\rcategory_code\x18\t \x01(\t\x12\x15\n\rcategory_name\x18\n \x01(\t\x12\x0c\n\x04spec\x18\x0b \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x0c \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\r \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x0e \x01(\t\x12\x0b\n\x03qty\x18\x0f \x01(\x01\x12\x16\n\x0e\x64\x65mand_unit_id\x18\x10 \x01(\x04\x12\x18\n\x10\x64\x65mand_unit_code\x18\x11 \x01(\t\x12\x18\n\x10\x64\x65mand_unit_name\x18\x12 \x01(\t\x12\x12\n\ndemand_qty\x18\x13 \x01(\x01\x12\x16\n\x0eproduct_status\x18\x14 \x01(\t\x12\x12\n\nfreeze_qty\x18\x15 \x01(\x01\x12\x12\n\nbroker_qty\x18\x16 \x01(\x01\x12/\n\x0c\x65xtra_detail\x18\x17 \x03(\x0b\x32\x19.inventory_bi.ExtraDetail\x12\x18\n\x10purchase_unit_id\x18\x18 \x01(\x04\x12\x1a\n\x12purchase_unit_code\x18\x19 \x01(\t\x12\x1a\n\x12purchase_unit_name\x18\x1a \x01(\t\x12\x14\n\x0cpurchase_qty\x18\x1b \x01(\x01\x12)\n\x08\x63hildren\x18\x1c \x03(\x0b\x32\x17.inventory_bi.Inventory\x12\x13\n\x0bposition_id\x18\x1d \x01(\x04\x12\x15\n\rposition_name\x18\x1e \x01(\t\x12\x15\n\rposition_code\x18\x1f \x01(\t\x12\x12\n\nprimary_id\x18\" \x01(\x04\x12\x11\n\ttax_price\x18# \x01(\x01\x12\x12\n\ncost_price\x18$ \x01(\x01\x12\x12\n\nsku_amount\x18% \x01(\x01\x12\x19\n\x11\x64\x65mand_broker_qty\x18& \x01(\x01\"\xd4\x01\n\x0b\x45xtraDetail\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x0b\n\x03qty\x18\x02 \x01(\x01\x12\x10\n\x08sku_type\x18\x03 \x01(\t\x12\x16\n\x0esub_account_id\x18\x04 \x01(\x04\x12\x12\n\nproduct_id\x18\x05 \x01(\t\x12\x16\n\x0equantity_avail\x18\x06 \x01(\x01\x12\x17\n\x0fquantity_freeze\x18\x07 \x01(\x01\x12\x17\n\x0fquantity_broker\x18\x08 \x01(\x01\x12\x0e\n\x06\x61mount\x18\t \x01(\x01\x12\x12\n\ndemand_qty\x18\n \x01(\x01\"\xe2\x0b\n\x0e\x44\x61ilyInventory\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nproduct_id\x18\x05 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x08 \x01(\x04\x12\x15\n\rcategory_code\x18\t \x01(\t\x12\x15\n\rcategory_name\x18\n \x01(\t\x12\x0c\n\x04spec\x18\x0b \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x0c \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\r \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x0e \x01(\t\x12\x0f\n\x07pre_qty\x18\x0f \x01(\x01\x12\x0b\n\x03qty\x18\x10 \x01(\x01\x12\x15\n\rtrans_deposit\x18\x11 \x01(\x01\x12\x16\n\x0etrans_withdraw\x18\x12 \x01(\x01\x12\x14\n\x0cstoc_deposit\x18\x13 \x01(\x01\x12\x15\n\rstoc_withdraw\x18\x14 \x01(\x01\x12\x13\n\x0b\x61\x64j_deposit\x18\x15 \x01(\x01\x12\x14\n\x0c\x61\x64j_withdraw\x18\x16 \x01(\x01\x12\x13\n\x0brec_deposit\x18\x17 \x01(\x01\x12\x18\n\x10rec_diff_deposit\x18\x18 \x01(\x01\x12\x19\n\x11rec_diff_withdraw\x18\x19 \x01(\x01\x12\x14\n\x0cret_withdraw\x18\x1a \x01(\x01\x12\x12\n\nstart_time\x18\x1b \x01(\t\x12\x10\n\x08\x65nd_time\x18\x1c \x01(\t\x12\x15\n\rsales_deposit\x18\x1d \x01(\x01\x12\x16\n\x0esales_withdraw\x18\x1e \x01(\x01\x12\x14\n\x0cllyl_deposit\x18\x1f \x01(\x01\x12\x15\n\rllyl_withdraw\x18  \x01(\x01\x12\x15\n\rcpcrk_deposit\x18! \x01(\x01\x12\x16\n\x0e\x63pcrk_withdraw\x18\" \x01(\x01\x12\x14\n\x0crec_withdraw\x18# \x01(\x01\x12\x13\n\x0bret_deposit\x18$ \x01(\x01\x12\x12\n\nfreeze_qty\x18% \x01(\x01\x12\x18\n\x10purchase_deposit\x18& \x01(\x01\x12\x10\n\x08low_cost\x18\' \x01(\x01\x12\x18\n\x10inventory_adjust\x18( \x01(\x01\x12\x16\n\x0einventory_init\x18) \x01(\x01\x12\x13\n\x0bsub_account\x18* \x01(\t\x12.\n\x08\x63hildren\x18+ \x03(\x0b\x32\x1c.inventory_bi.DailyInventory\x12\x13\n\x0bposition_id\x18, \x01(\x04\x12\x15\n\rposition_name\x18- \x01(\t\x12\x15\n\rposition_code\x18. \x01(\t\x12\x15\n\rspick_deposit\x18/ \x01(\x01\x12\x1e\n\x16material_trans_deposit\x18\x30 \x01(\x01\x12\x1f\n\x17material_trans_withdraw\x18\x31 \x01(\x01\x12\x1a\n\x12processing_deposit\x18\x32 \x01(\x01\x12\x1b\n\x13processing_withdraw\x18\x33 \x01(\x01\x12\x17\n\x0fpacking_deposit\x18\x34 \x01(\x01\x12\x18\n\x10packing_withdraw\x18\x35 \x01(\x01\x12\x14\n\x0cret_transfer\x18\x36 \x01(\x01\x12\x16\n\x0etrans_transfer\x18\x37 \x01(\x01\x12\x16\n\x0etrans_delivery\x18\x38 \x01(\x01\x12\x16\n\x0etrans_purchase\x18\x39 \x01(\x01\x12\x1c\n\x14trans_return_release\x18: \x01(\x01\x12\x1e\n\x16trans_transfer_release\x18; \x01(\x01\x12\x1e\n\x16trans_delivery_release\x18< \x01(\x01\x12\x1e\n\x16trans_purchase_release\x18= \x01(\x01\x12\x13\n\x0btrans_begin\x18> \x01(\x01\x12\x11\n\ttrans_end\x18? \x01(\x01\"\xd5\x05\n\x0cInventoryLog\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08store_id\x18\x02 \x01(\x04\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x12\n\nstore_code\x18\x04 \x01(\t\x12\x12\n\nproduct_id\x18\x05 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x0c\n\x04spec\x18\x08 \x01(\t\x12\x12\n\norder_code\x18\t \x01(\t\x12\x12\n\norder_type\x18\n \x01(\t\x12.\n\norder_time\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06\x61\x63tion\x18\x0c \x01(\t\x12\x0e\n\x06status\x18\r \x01(\t\x12\x0b\n\x03qty\x18\x0e \x01(\x01\x12\x10\n\x08stock_id\x18\x0f \x01(\t\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x10 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_code\x18\x11 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x12 \x01(\t\x12\x16\n\x0e\x64\x65mand_unit_id\x18\x13 \x01(\x04\x12\x18\n\x10\x64\x65mand_unit_code\x18\x14 \x01(\t\x12\x18\n\x10\x64\x65mand_unit_name\x18\x15 \x01(\t\x12\x12\n\ndemand_qty\x18\x16 \x01(\x01\x12\x13\n\x0b\x63\x61tegory_id\x18\x17 \x01(\x04\x12\x15\n\rcategory_code\x18\x18 \x01(\t\x12\x15\n\rcategory_name\x18\x19 \x01(\t\x12\x14\n\x0cjde_order_id\x18\x1a \x01(\t\x12\x16\n\x0ejde_order_type\x18\x1b \x01(\t\x12\x0f\n\x07jde_mcu\x18\x1c \x01(\t\x12\x14\n\x0c\x61\x63\x63ount_type\x18\x1d \x01(\t\x12\x16\n\x0esub_account_id\x18\x1e \x01(\x04\x12\x18\n\x10sub_account_code\x18\x1f \x01(\t\x12\x18\n\x10sub_account_name\x18  \x01(\t\"\xd7\x01\n\x0cMcuInventory\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0e\n\x06mcu_id\x18\x02 \x01(\x04\x12\x10\n\x08mcu_code\x18\x03 \x01(\t\x12\x10\n\x08mcu_name\x18\x04 \x01(\t\x12\x12\n\nproduct_id\x18\x05 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x06 \x01(\t\x12\x14\n\x0cproduct_name\x18\x07 \x01(\t\x12\x0f\n\x07unit_id\x18\x08 \x01(\x04\x12\x11\n\tunit_code\x18\t \x01(\t\x12\x11\n\tunit_name\x18\n \x01(\t\x12\x10\n\x08quantity\x18\x0b \x01(\x01\",\n\x11\x42\x61tchQueryRequest\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0b\n\x03lan\x18\x02 \x01(\t\"$\n\x12\x42\x61tchQueryResponse\x12\x0e\n\x06result\x18\x01 \x01(\t\"\xe4\x02\n\x1fQueryBohJdeInventoryDiffRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tstore_ids\x18\x03 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x04 \x03(\x04\x12\x13\n\x0bupper_limit\x18\x05 \x01(\t\x12\x13\n\x0blower_limit\x18\x07 \x01(\t\x12\r\n\x05limit\x18\x08 \x01(\x04\x12\x0e\n\x06offset\x18\t \x01(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\n \x03(\x04\x12\x1c\n\x14\x65xclude_cost_product\x18\x0b \x01(\x08\x12\r\n\x05order\x18\x0c \x01(\t\x12\x0c\n\x04sort\x18\r \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0e \x01(\x08\x12\x0b\n\x03lan\x18\x0f \x01(\t\"\x90\x03\n\x13\x42ohJdeInventoryDiff\x12\x32\n\x0einventory_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nstore_code\x18\x02 \x01(\t\x12\x12\n\nstore_name\x18\x03 \x01(\t\x12\x14\n\x0cproduct_code\x18\x04 \x01(\t\x12\x14\n\x0cproduct_name\x18\x05 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x06 \x01(\t\x12\x15\n\rboh_inventory\x18\x07 \x01(\x01\x12\x15\n\rjde_inventory\x18\x08 \x01(\x01\x12\x1a\n\x12inventory_diff_qty\x18\t \x01(\x01\x12\x37\n\x13inventory_diff_time\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x14\n\x0cproduct_type\x18\x0b \x01(\t\x12\x0e\n\x06status\x18\x0c \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\r \x01(\x04\x12\x15\n\rcategory_name\x18\x0e \x01(\t\"b\n QueryBohJdeInventoryDiffResponse\x12/\n\x04rows\x18\x01 \x03(\x0b\x32!.inventory_bi.BohJdeInventoryDiff\x12\r\n\x05total\x18\x02 \x01(\x04\"K\n\x07\x41\x63\x63ount\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x19\n\x11\x64istribution_type\x18\x03 \x01(\t\"a\n\"RealtimeInventoryByAccountsRequest\x12\'\n\x08\x61\x63\x63ounts\x18\x01 \x03(\x0b\x32\x15.inventory_bi.Account\x12\x12\n\ncheck_type\x18\x02 \x01(\t\"Z\n#RealtimeInventoryByAccountsResponse\x12\r\n\x05total\x18\x01 \x01(\x05\x12$\n\x04rows\x18\x02 \x03(\x0b\x32\x16.inventory_bi.Accounts\"\x90\x01\n\x08\x41\x63\x63ounts\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x0b\n\x03qty\x18\x03 \x01(\x01\x12\r\n\x05round\x18\x04 \x01(\x01\x12,\n\x0csub_accounts\x18\x05 \x03(\x0b\x32\x16.inventory_bi.Accounts\x12\x13\n\x0b\x62ranch_name\x18\x06 \x01(\t2\x8e\x0c\n\x12InventoryBiService\x12\x8f\x01\n\x11RealtimeInventory\x12&.inventory_bi.RealtimeInventoryRequest\x1a\'.inventory_bi.RealtimeInventoryResponse\")\x82\xd3\xe4\x93\x02#\x12!/api/v2/supply/inventory/realtime\x12\x83\x01\n\x0e\x44\x61ilyInventory\x12#.inventory_bi.DailyInventoryRequest\x1a$.inventory_bi.DailyInventoryResponse\"&\x82\xd3\xe4\x93\x02 \x12\x1e/api/v2/supply/inventory/daily\x12\x88\x01\n\x0eQueryAccouting\x12#.inventory_bi.QueryAccoutingRequest\x1a$.inventory_bi.QueryAccoutingResponse\"+\x82\xd3\xe4\x93\x02%\x12#/api/v2/supply/inventory/accounting\x12\x8a\x01\n\x11QueryInventoryLog\x12&.inventory_bi.QueryInventoryLogRequest\x1a\'.inventory_bi.QueryInventoryLogResponse\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/api/v2/supply/inventory/log\x12\xa6\x01\n\x19QueryMcuRealtimeInventory\x12..inventory_bi.QueryMcuRealtimeInventoryRequest\x1a/.inventory_bi.QueryMcuRealtimeInventoryResponse\"(\x82\xd3\xe4\x93\x02\"\x12 /api/v2/supply/mcu/inventory/log\x12z\n\nBatchQuery\x12\x1f.inventory_bi.BatchQueryRequest\x1a .inventory_bi.BatchQueryResponse\")\x82\xd3\xe4\x93\x02#\x12!/api/v2/supply/batch/result/query\x12\xa5\x01\n\x18QueryInventoryLogInTotal\x12-.inventory_bi.QueryInventoryLogInTotalRequest\x1a..inventory_bi.QueryInventoryLogInTotalResponse\"*\x82\xd3\xe4\x93\x02$\x12\"/api/v2/supply/inventory/log/total\x12\x98\x01\n\x15QuerySnapshotForSales\x12*.inventory_bi.QuerySnapshotForSalesRequest\x1a+.inventory_bi.QuerySnapshotForSalesResponse\"&\x82\xd3\xe4\x93\x02 \x12\x1e/api/v2/supply/inventory/sales\x12\xa0\x01\n\x18QueryBohJdeInventoryDiff\x12-.inventory_bi.QueryBohJdeInventoryDiffRequest\x1a..inventory_bi.QueryBohJdeInventoryDiffResponse\"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/api/v2/supply/inventory/diff\x12\xbc\x01\n\x1bRealtimeInventoryByAccounts\x12\x30.inventory_bi.RealtimeInventoryByAccountsRequest\x1a\x31.inventory_bi.RealtimeInventoryByAccountsResponse\"8\x82\xd3\xe4\x93\x02\x32\"-/api/v2/supply/inventory/realtime/by/accounts:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_QUERYSNAPSHOTFORSALESREQUEST = _descriptor.Descriptor(
  name='QuerySnapshotForSalesRequest',
  full_name='inventory_bi.QuerySnapshotForSalesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='inventory_bi.QuerySnapshotForSalesRequest.store_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='biz_date', full_name='inventory_bi.QuerySnapshotForSalesRequest.biz_date', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory_bi.QuerySnapshotForSalesRequest.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='inventory_bi.QuerySnapshotForSalesRequest.lan', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=99,
  serialized_end=192,
)


_SNAPSHOTFORSALES = _descriptor.Descriptor(
  name='SnapshotForSales',
  full_name='inventory_bi.SnapshotForSales',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='inventory_bi.SnapshotForSales.store_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory_bi.SnapshotForSales.product_id', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory_bi.SnapshotForSales.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='yesterday_qty', full_name='inventory_bi.SnapshotForSales.yesterday_qty', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='week_average_qty', full_name='inventory_bi.SnapshotForSales.week_average_qty', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=194,
  serialized_end=313,
)


_QUERYSNAPSHOTFORSALESRESPONSE = _descriptor.Descriptor(
  name='QuerySnapshotForSalesResponse',
  full_name='inventory_bi.QuerySnapshotForSalesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='inventory_bi.QuerySnapshotForSalesResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=315,
  serialized_end=392,
)


_QUERYINVENTORYLOGINTOTALREQUEST = _descriptor.Descriptor(
  name='QueryInventoryLogInTotalRequest',
  full_name='inventory_bi.QueryInventoryLogInTotalRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory_bi.QueryInventoryLogInTotalRequest.branch_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='inventory_bi.QueryInventoryLogInTotalRequest.product_ids', index=1,
      number=2, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_time', full_name='inventory_bi.QueryInventoryLogInTotalRequest.start_time', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='inventory_bi.QueryInventoryLogInTotalRequest.end_time', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='inventory_bi.QueryInventoryLogInTotalRequest.limit', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='inventory_bi.QueryInventoryLogInTotalRequest.offset', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='inventory_bi.QueryInventoryLogInTotalRequest.lan', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=395,
  serialized_end=606,
)


_QUERYINVENTORYLOGINTOTALRESPONSE = _descriptor.Descriptor(
  name='QueryInventoryLogInTotalResponse',
  full_name='inventory_bi.QueryInventoryLogInTotalResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory_bi.QueryInventoryLogInTotalResponse.total', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='inventory_bi.QueryInventoryLogInTotalResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=608,
  serialized_end=700,
)


_GROUPEVENTLOG = _descriptor.Descriptor(
  name='GroupEventLog',
  full_name='inventory_bi.GroupEventLog',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory_bi.GroupEventLog.product_id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory_bi.GroupEventLog.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory_bi.GroupEventLog.action', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='inventory_bi.GroupEventLog.qty', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='count', full_name='inventory_bi.GroupEventLog.count', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='inventory_bi.GroupEventLog.product_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='inventory_bi.GroupEventLog.product_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=703,
  serialized_end=840,
)


_REALTIMEINVENTORYREQUEST = _descriptor.Descriptor(
  name='RealtimeInventoryRequest',
  full_name='inventory_bi.RealtimeInventoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='inventory_bi.RealtimeInventoryRequest.branch_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='geo_regions', full_name='inventory_bi.RealtimeInventoryRequest.geo_regions', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='inventory_bi.RealtimeInventoryRequest.category_ids', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='inventory_bi.RealtimeInventoryRequest.product_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='inventory_bi.RealtimeInventoryRequest.limit', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='inventory_bi.RealtimeInventoryRequest.offset', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='inventory_bi.RealtimeInventoryRequest.order', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='inventory_bi.RealtimeInventoryRequest.sort', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exclude', full_name='inventory_bi.RealtimeInventoryRequest.exclude', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='inventory_bi.RealtimeInventoryRequest.branch_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='inventory_bi.RealtimeInventoryRequest.lan', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='inventory_bi.RealtimeInventoryRequest.position_ids', index=11,
      number=12, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='inventory_bi.RealtimeInventoryRequest.return_fields', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=843,
  serialized_end=1109,
)


_REALTIMEINVENTORYRESPONSE = _descriptor.Descriptor(
  name='RealtimeInventoryResponse',
  full_name='inventory_bi.RealtimeInventoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='inventory_bi.RealtimeInventoryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory_bi.RealtimeInventoryResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1111,
  serialized_end=1192,
)


_DAILYINVENTORYREQUEST = _descriptor.Descriptor(
  name='DailyInventoryRequest',
  full_name='inventory_bi.DailyInventoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory_bi.DailyInventoryRequest.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='inventory_bi.DailyInventoryRequest.category_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='inventory_bi.DailyInventoryRequest.product_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='inventory_bi.DailyInventoryRequest.start_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='inventory_bi.DailyInventoryRequest.end_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='inventory_bi.DailyInventoryRequest.limit', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='inventory_bi.DailyInventoryRequest.offset', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory_bi.DailyInventoryRequest.code', index=7,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory_bi.DailyInventoryRequest.action', index=8,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='if_pre', full_name='inventory_bi.DailyInventoryRequest.if_pre', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='if_end', full_name='inventory_bi.DailyInventoryRequest.if_end', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exclude_empty', full_name='inventory_bi.DailyInventoryRequest.exclude_empty', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='inventory_bi.DailyInventoryRequest.branch_type', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='inventory_bi.DailyInventoryRequest.lan', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='inventory_bi.DailyInventoryRequest.position_ids', index=14,
      number=16, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='inventory_bi.DailyInventoryRequest.return_fields', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1195,
  serialized_end=1569,
)


_DAILYINVENTORYRESPONSE = _descriptor.Descriptor(
  name='DailyInventoryResponse',
  full_name='inventory_bi.DailyInventoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='inventory_bi.DailyInventoryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory_bi.DailyInventoryResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1571,
  serialized_end=1654,
)


_QUERYACCOUTINGREQUEST = _descriptor.Descriptor(
  name='QueryAccoutingRequest',
  full_name='inventory_bi.QueryAccoutingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='batch_id', full_name='inventory_bi.QueryAccoutingRequest.batch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_no', full_name='inventory_bi.QueryAccoutingRequest.batch_no', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory_bi.QueryAccoutingRequest.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory_bi.QueryAccoutingRequest.action', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='detail', full_name='inventory_bi.QueryAccoutingRequest.detail', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='inventory_bi.QueryAccoutingRequest.lan', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1656,
  serialized_end=1774,
)


_QUERYACCOUTINGRESPONSE = _descriptor.Descriptor(
  name='QueryAccoutingResponse',
  full_name='inventory_bi.QueryAccoutingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1776,
  serialized_end=1800,
)


_QUERYINVENTORYLOGREQUEST = _descriptor.Descriptor(
  name='QueryInventoryLogRequest',
  full_name='inventory_bi.QueryInventoryLogRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory_bi.QueryInventoryLogRequest.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='inventory_bi.QueryInventoryLogRequest.product_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='inventory_bi.QueryInventoryLogRequest.category_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory_bi.QueryInventoryLogRequest.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory_bi.QueryInventoryLogRequest.action', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='inventory_bi.QueryInventoryLogRequest.limit', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='inventory_bi.QueryInventoryLogRequest.offset', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='inventory_bi.QueryInventoryLogRequest.start_date', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='inventory_bi.QueryInventoryLogRequest.end_date', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='inventory_bi.QueryInventoryLogRequest.order_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account_type', full_name='inventory_bi.QueryInventoryLogRequest.account_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='inventory_bi.QueryInventoryLogRequest.lan', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='inventory_bi.QueryInventoryLogRequest.position_ids', index=12,
      number=13, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='return_fields', full_name='inventory_bi.QueryInventoryLogRequest.return_fields', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1803,
  serialized_end=2146,
)


_QUERYINVENTORYLOGRESPONSE = _descriptor.Descriptor(
  name='QueryInventoryLogResponse',
  full_name='inventory_bi.QueryInventoryLogResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='inventory_bi.QueryInventoryLogResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory_bi.QueryInventoryLogResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount_sum', full_name='inventory_bi.QueryInventoryLogResponse.amount_sum', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2148,
  serialized_end=2252,
)


_QUERYMCUREALTIMEINVENTORYREQUEST = _descriptor.Descriptor(
  name='QueryMcuRealtimeInventoryRequest',
  full_name='inventory_bi.QueryMcuRealtimeInventoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='mcu', full_name='inventory_bi.QueryMcuRealtimeInventoryRequest.mcu', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='inventory_bi.QueryMcuRealtimeInventoryRequest.product_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='inventory_bi.QueryMcuRealtimeInventoryRequest.lan', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2254,
  serialized_end=2335,
)


_QUERYMCUREALTIMEINVENTORYRESPONSE = _descriptor.Descriptor(
  name='QueryMcuRealtimeInventoryResponse',
  full_name='inventory_bi.QueryMcuRealtimeInventoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='inventory_bi.QueryMcuRealtimeInventoryResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory_bi.QueryMcuRealtimeInventoryResponse.total', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2337,
  serialized_end=2429,
)


_INVENTORY = _descriptor.Descriptor(
  name='Inventory',
  full_name='inventory_bi.Inventory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory_bi.Inventory.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='inventory_bi.Inventory.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='inventory_bi.Inventory.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='inventory_bi.Inventory.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory_bi.Inventory.product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='inventory_bi.Inventory.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='inventory_bi.Inventory.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='inventory_bi.Inventory.category_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='inventory_bi.Inventory.category_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='inventory_bi.Inventory.category_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='inventory_bi.Inventory.spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='inventory_bi.Inventory.accounting_unit_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='inventory_bi.Inventory.accounting_unit_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='inventory_bi.Inventory.accounting_unit_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='inventory_bi.Inventory.qty', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_id', full_name='inventory_bi.Inventory.demand_unit_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_code', full_name='inventory_bi.Inventory.demand_unit_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_name', full_name='inventory_bi.Inventory.demand_unit_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_qty', full_name='inventory_bi.Inventory.demand_qty', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_status', full_name='inventory_bi.Inventory.product_status', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='freeze_qty', full_name='inventory_bi.Inventory.freeze_qty', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='broker_qty', full_name='inventory_bi.Inventory.broker_qty', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extra_detail', full_name='inventory_bi.Inventory.extra_detail', index=22,
      number=23, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_unit_id', full_name='inventory_bi.Inventory.purchase_unit_id', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_unit_code', full_name='inventory_bi.Inventory.purchase_unit_code', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_unit_name', full_name='inventory_bi.Inventory.purchase_unit_name', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_qty', full_name='inventory_bi.Inventory.purchase_qty', index=26,
      number=27, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='children', full_name='inventory_bi.Inventory.children', index=27,
      number=28, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='inventory_bi.Inventory.position_id', index=28,
      number=29, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='inventory_bi.Inventory.position_name', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='inventory_bi.Inventory.position_code', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='primary_id', full_name='inventory_bi.Inventory.primary_id', index=31,
      number=34, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='inventory_bi.Inventory.tax_price', index=32,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='inventory_bi.Inventory.cost_price', index=33,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sku_amount', full_name='inventory_bi.Inventory.sku_amount', index=34,
      number=37, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_broker_qty', full_name='inventory_bi.Inventory.demand_broker_qty', index=35,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2432,
  serialized_end=3288,
)


_EXTRADETAIL = _descriptor.Descriptor(
  name='ExtraDetail',
  full_name='inventory_bi.ExtraDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory_bi.ExtraDetail.code', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='inventory_bi.ExtraDetail.qty', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sku_type', full_name='inventory_bi.ExtraDetail.sku_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_id', full_name='inventory_bi.ExtraDetail.sub_account_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory_bi.ExtraDetail.product_id', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity_avail', full_name='inventory_bi.ExtraDetail.quantity_avail', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity_freeze', full_name='inventory_bi.ExtraDetail.quantity_freeze', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity_broker', full_name='inventory_bi.ExtraDetail.quantity_broker', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='inventory_bi.ExtraDetail.amount', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_qty', full_name='inventory_bi.ExtraDetail.demand_qty', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3291,
  serialized_end=3503,
)


_DAILYINVENTORY = _descriptor.Descriptor(
  name='DailyInventory',
  full_name='inventory_bi.DailyInventory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory_bi.DailyInventory.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='inventory_bi.DailyInventory.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='inventory_bi.DailyInventory.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='inventory_bi.DailyInventory.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory_bi.DailyInventory.product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='inventory_bi.DailyInventory.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='inventory_bi.DailyInventory.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='inventory_bi.DailyInventory.category_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='inventory_bi.DailyInventory.category_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='inventory_bi.DailyInventory.category_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='inventory_bi.DailyInventory.spec', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='inventory_bi.DailyInventory.accounting_unit_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='inventory_bi.DailyInventory.accounting_unit_code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='inventory_bi.DailyInventory.accounting_unit_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pre_qty', full_name='inventory_bi.DailyInventory.pre_qty', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='inventory_bi.DailyInventory.qty', index=15,
      number=16, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_deposit', full_name='inventory_bi.DailyInventory.trans_deposit', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_withdraw', full_name='inventory_bi.DailyInventory.trans_withdraw', index=17,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stoc_deposit', full_name='inventory_bi.DailyInventory.stoc_deposit', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stoc_withdraw', full_name='inventory_bi.DailyInventory.stoc_withdraw', index=19,
      number=20, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adj_deposit', full_name='inventory_bi.DailyInventory.adj_deposit', index=20,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adj_withdraw', full_name='inventory_bi.DailyInventory.adj_withdraw', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_deposit', full_name='inventory_bi.DailyInventory.rec_deposit', index=22,
      number=23, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_diff_deposit', full_name='inventory_bi.DailyInventory.rec_diff_deposit', index=23,
      number=24, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_diff_withdraw', full_name='inventory_bi.DailyInventory.rec_diff_withdraw', index=24,
      number=25, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ret_withdraw', full_name='inventory_bi.DailyInventory.ret_withdraw', index=25,
      number=26, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_time', full_name='inventory_bi.DailyInventory.start_time', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='inventory_bi.DailyInventory.end_time', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_deposit', full_name='inventory_bi.DailyInventory.sales_deposit', index=28,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_withdraw', full_name='inventory_bi.DailyInventory.sales_withdraw', index=29,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='llyl_deposit', full_name='inventory_bi.DailyInventory.llyl_deposit', index=30,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='llyl_withdraw', full_name='inventory_bi.DailyInventory.llyl_withdraw', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cpcrk_deposit', full_name='inventory_bi.DailyInventory.cpcrk_deposit', index=32,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cpcrk_withdraw', full_name='inventory_bi.DailyInventory.cpcrk_withdraw', index=33,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rec_withdraw', full_name='inventory_bi.DailyInventory.rec_withdraw', index=34,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ret_deposit', full_name='inventory_bi.DailyInventory.ret_deposit', index=35,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='freeze_qty', full_name='inventory_bi.DailyInventory.freeze_qty', index=36,
      number=37, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase_deposit', full_name='inventory_bi.DailyInventory.purchase_deposit', index=37,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='low_cost', full_name='inventory_bi.DailyInventory.low_cost', index=38,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_adjust', full_name='inventory_bi.DailyInventory.inventory_adjust', index=39,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_init', full_name='inventory_bi.DailyInventory.inventory_init', index=40,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account', full_name='inventory_bi.DailyInventory.sub_account', index=41,
      number=42, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='children', full_name='inventory_bi.DailyInventory.children', index=42,
      number=43, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='inventory_bi.DailyInventory.position_id', index=43,
      number=44, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='inventory_bi.DailyInventory.position_name', index=44,
      number=45, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='inventory_bi.DailyInventory.position_code', index=45,
      number=46, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spick_deposit', full_name='inventory_bi.DailyInventory.spick_deposit', index=46,
      number=47, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_trans_deposit', full_name='inventory_bi.DailyInventory.material_trans_deposit', index=47,
      number=48, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_trans_withdraw', full_name='inventory_bi.DailyInventory.material_trans_withdraw', index=48,
      number=49, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_deposit', full_name='inventory_bi.DailyInventory.processing_deposit', index=49,
      number=50, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='processing_withdraw', full_name='inventory_bi.DailyInventory.processing_withdraw', index=50,
      number=51, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_deposit', full_name='inventory_bi.DailyInventory.packing_deposit', index=51,
      number=52, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='packing_withdraw', full_name='inventory_bi.DailyInventory.packing_withdraw', index=52,
      number=53, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ret_transfer', full_name='inventory_bi.DailyInventory.ret_transfer', index=53,
      number=54, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_transfer', full_name='inventory_bi.DailyInventory.trans_transfer', index=54,
      number=55, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_delivery', full_name='inventory_bi.DailyInventory.trans_delivery', index=55,
      number=56, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_purchase', full_name='inventory_bi.DailyInventory.trans_purchase', index=56,
      number=57, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_return_release', full_name='inventory_bi.DailyInventory.trans_return_release', index=57,
      number=58, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_transfer_release', full_name='inventory_bi.DailyInventory.trans_transfer_release', index=58,
      number=59, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_delivery_release', full_name='inventory_bi.DailyInventory.trans_delivery_release', index=59,
      number=60, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_purchase_release', full_name='inventory_bi.DailyInventory.trans_purchase_release', index=60,
      number=61, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_begin', full_name='inventory_bi.DailyInventory.trans_begin', index=61,
      number=62, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='trans_end', full_name='inventory_bi.DailyInventory.trans_end', index=62,
      number=63, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3506,
  serialized_end=5012,
)


_INVENTORYLOG = _descriptor.Descriptor(
  name='InventoryLog',
  full_name='inventory_bi.InventoryLog',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory_bi.InventoryLog.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='inventory_bi.InventoryLog.store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='inventory_bi.InventoryLog.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='inventory_bi.InventoryLog.store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory_bi.InventoryLog.product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='inventory_bi.InventoryLog.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='inventory_bi.InventoryLog.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='inventory_bi.InventoryLog.spec', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_code', full_name='inventory_bi.InventoryLog.order_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_type', full_name='inventory_bi.InventoryLog.order_type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_time', full_name='inventory_bi.InventoryLog.order_time', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='inventory_bi.InventoryLog.action', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='inventory_bi.InventoryLog.status', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='inventory_bi.InventoryLog.qty', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stock_id', full_name='inventory_bi.InventoryLog.stock_id', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='inventory_bi.InventoryLog.accounting_unit_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_code', full_name='inventory_bi.InventoryLog.accounting_unit_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='inventory_bi.InventoryLog.accounting_unit_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_id', full_name='inventory_bi.InventoryLog.demand_unit_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_code', full_name='inventory_bi.InventoryLog.demand_unit_code', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_unit_name', full_name='inventory_bi.InventoryLog.demand_unit_name', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_qty', full_name='inventory_bi.InventoryLog.demand_qty', index=21,
      number=22, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='inventory_bi.InventoryLog.category_id', index=22,
      number=23, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='inventory_bi.InventoryLog.category_code', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='inventory_bi.InventoryLog.category_name', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_order_id', full_name='inventory_bi.InventoryLog.jde_order_id', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_order_type', full_name='inventory_bi.InventoryLog.jde_order_type', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_mcu', full_name='inventory_bi.InventoryLog.jde_mcu', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account_type', full_name='inventory_bi.InventoryLog.account_type', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_id', full_name='inventory_bi.InventoryLog.sub_account_id', index=29,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_code', full_name='inventory_bi.InventoryLog.sub_account_code', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_account_name', full_name='inventory_bi.InventoryLog.sub_account_name', index=31,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5015,
  serialized_end=5740,
)


_MCUINVENTORY = _descriptor.Descriptor(
  name='McuInventory',
  full_name='inventory_bi.McuInventory',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory_bi.McuInventory.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mcu_id', full_name='inventory_bi.McuInventory.mcu_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mcu_code', full_name='inventory_bi.McuInventory.mcu_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mcu_name', full_name='inventory_bi.McuInventory.mcu_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory_bi.McuInventory.product_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='inventory_bi.McuInventory.product_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='inventory_bi.McuInventory.product_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='inventory_bi.McuInventory.unit_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_code', full_name='inventory_bi.McuInventory.unit_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='inventory_bi.McuInventory.unit_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='inventory_bi.McuInventory.quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5743,
  serialized_end=5958,
)


_BATCHQUERYREQUEST = _descriptor.Descriptor(
  name='BatchQueryRequest',
  full_name='inventory_bi.BatchQueryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory_bi.BatchQueryRequest.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='inventory_bi.BatchQueryRequest.lan', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5960,
  serialized_end=6004,
)


_BATCHQUERYRESPONSE = _descriptor.Descriptor(
  name='BatchQueryResponse',
  full_name='inventory_bi.BatchQueryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='inventory_bi.BatchQueryResponse.result', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6006,
  serialized_end=6042,
)


_QUERYBOHJDEINVENTORYDIFFREQUEST = _descriptor.Descriptor(
  name='QueryBohJdeInventoryDiffRequest',
  full_name='inventory_bi.QueryBohJdeInventoryDiffRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='inventory_bi.QueryBohJdeInventoryDiffRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='inventory_bi.QueryBohJdeInventoryDiffRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='inventory_bi.QueryBohJdeInventoryDiffRequest.store_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='inventory_bi.QueryBohJdeInventoryDiffRequest.product_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='upper_limit', full_name='inventory_bi.QueryBohJdeInventoryDiffRequest.upper_limit', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lower_limit', full_name='inventory_bi.QueryBohJdeInventoryDiffRequest.lower_limit', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='inventory_bi.QueryBohJdeInventoryDiffRequest.limit', index=6,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='inventory_bi.QueryBohJdeInventoryDiffRequest.offset', index=7,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='inventory_bi.QueryBohJdeInventoryDiffRequest.category_ids', index=8,
      number=10, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='exclude_cost_product', full_name='inventory_bi.QueryBohJdeInventoryDiffRequest.exclude_cost_product', index=9,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='inventory_bi.QueryBohJdeInventoryDiffRequest.order', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='inventory_bi.QueryBohJdeInventoryDiffRequest.sort', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='inventory_bi.QueryBohJdeInventoryDiffRequest.is_wms_store', index=12,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='inventory_bi.QueryBohJdeInventoryDiffRequest.lan', index=13,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6045,
  serialized_end=6401,
)


_BOHJDEINVENTORYDIFF = _descriptor.Descriptor(
  name='BohJdeInventoryDiff',
  full_name='inventory_bi.BohJdeInventoryDiff',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='inventory_date', full_name='inventory_bi.BohJdeInventoryDiff.inventory_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='inventory_bi.BohJdeInventoryDiff.store_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='inventory_bi.BohJdeInventoryDiff.store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='inventory_bi.BohJdeInventoryDiff.product_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='inventory_bi.BohJdeInventoryDiff.product_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='inventory_bi.BohJdeInventoryDiff.accounting_unit_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='boh_inventory', full_name='inventory_bi.BohJdeInventoryDiff.boh_inventory', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='jde_inventory', full_name='inventory_bi.BohJdeInventoryDiff.jde_inventory', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_diff_qty', full_name='inventory_bi.BohJdeInventoryDiff.inventory_diff_qty', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_diff_time', full_name='inventory_bi.BohJdeInventoryDiff.inventory_diff_time', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='inventory_bi.BohJdeInventoryDiff.product_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='inventory_bi.BohJdeInventoryDiff.status', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='inventory_bi.BohJdeInventoryDiff.category_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='inventory_bi.BohJdeInventoryDiff.category_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6404,
  serialized_end=6804,
)


_QUERYBOHJDEINVENTORYDIFFRESPONSE = _descriptor.Descriptor(
  name='QueryBohJdeInventoryDiffResponse',
  full_name='inventory_bi.QueryBohJdeInventoryDiffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='inventory_bi.QueryBohJdeInventoryDiffResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory_bi.QueryBohJdeInventoryDiffResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6806,
  serialized_end=6904,
)


_ACCOUNT = _descriptor.Descriptor(
  name='Account',
  full_name='inventory_bi.Account',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory_bi.Account.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory_bi.Account.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='distribution_type', full_name='inventory_bi.Account.distribution_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6906,
  serialized_end=6981,
)


_REALTIMEINVENTORYBYACCOUNTSREQUEST = _descriptor.Descriptor(
  name='RealtimeInventoryByAccountsRequest',
  full_name='inventory_bi.RealtimeInventoryByAccountsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounts', full_name='inventory_bi.RealtimeInventoryByAccountsRequest.accounts', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='check_type', full_name='inventory_bi.RealtimeInventoryByAccountsRequest.check_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6983,
  serialized_end=7080,
)


_REALTIMEINVENTORYBYACCOUNTSRESPONSE = _descriptor.Descriptor(
  name='RealtimeInventoryByAccountsResponse',
  full_name='inventory_bi.RealtimeInventoryByAccountsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory_bi.RealtimeInventoryByAccountsResponse.total', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='inventory_bi.RealtimeInventoryByAccountsResponse.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7082,
  serialized_end=7172,
)


_ACCOUNTS = _descriptor.Descriptor(
  name='Accounts',
  full_name='inventory_bi.Accounts',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory_bi.Accounts.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='inventory_bi.Accounts.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='inventory_bi.Accounts.qty', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='round', full_name='inventory_bi.Accounts.round', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sub_accounts', full_name='inventory_bi.Accounts.sub_accounts', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='inventory_bi.Accounts.branch_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7175,
  serialized_end=7319,
)

_QUERYSNAPSHOTFORSALESRESPONSE.fields_by_name['rows'].message_type = _SNAPSHOTFORSALES
_QUERYINVENTORYLOGINTOTALREQUEST.fields_by_name['start_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYINVENTORYLOGINTOTALREQUEST.fields_by_name['end_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYINVENTORYLOGINTOTALRESPONSE.fields_by_name['rows'].message_type = _GROUPEVENTLOG
_REALTIMEINVENTORYRESPONSE.fields_by_name['rows'].message_type = _INVENTORY
_DAILYINVENTORYREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DAILYINVENTORYREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_DAILYINVENTORYRESPONSE.fields_by_name['rows'].message_type = _DAILYINVENTORY
_QUERYINVENTORYLOGREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYINVENTORYLOGREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYINVENTORYLOGRESPONSE.fields_by_name['rows'].message_type = _INVENTORYLOG
_QUERYMCUREALTIMEINVENTORYRESPONSE.fields_by_name['rows'].message_type = _MCUINVENTORY
_INVENTORY.fields_by_name['extra_detail'].message_type = _EXTRADETAIL
_INVENTORY.fields_by_name['children'].message_type = _INVENTORY
_DAILYINVENTORY.fields_by_name['children'].message_type = _DAILYINVENTORY
_INVENTORYLOG.fields_by_name['order_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYBOHJDEINVENTORYDIFFREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYBOHJDEINVENTORYDIFFREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_BOHJDEINVENTORYDIFF.fields_by_name['inventory_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_BOHJDEINVENTORYDIFF.fields_by_name['inventory_diff_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_QUERYBOHJDEINVENTORYDIFFRESPONSE.fields_by_name['rows'].message_type = _BOHJDEINVENTORYDIFF
_REALTIMEINVENTORYBYACCOUNTSREQUEST.fields_by_name['accounts'].message_type = _ACCOUNT
_REALTIMEINVENTORYBYACCOUNTSRESPONSE.fields_by_name['rows'].message_type = _ACCOUNTS
_ACCOUNTS.fields_by_name['sub_accounts'].message_type = _ACCOUNTS
DESCRIPTOR.message_types_by_name['QuerySnapshotForSalesRequest'] = _QUERYSNAPSHOTFORSALESREQUEST
DESCRIPTOR.message_types_by_name['SnapshotForSales'] = _SNAPSHOTFORSALES
DESCRIPTOR.message_types_by_name['QuerySnapshotForSalesResponse'] = _QUERYSNAPSHOTFORSALESRESPONSE
DESCRIPTOR.message_types_by_name['QueryInventoryLogInTotalRequest'] = _QUERYINVENTORYLOGINTOTALREQUEST
DESCRIPTOR.message_types_by_name['QueryInventoryLogInTotalResponse'] = _QUERYINVENTORYLOGINTOTALRESPONSE
DESCRIPTOR.message_types_by_name['GroupEventLog'] = _GROUPEVENTLOG
DESCRIPTOR.message_types_by_name['RealtimeInventoryRequest'] = _REALTIMEINVENTORYREQUEST
DESCRIPTOR.message_types_by_name['RealtimeInventoryResponse'] = _REALTIMEINVENTORYRESPONSE
DESCRIPTOR.message_types_by_name['DailyInventoryRequest'] = _DAILYINVENTORYREQUEST
DESCRIPTOR.message_types_by_name['DailyInventoryResponse'] = _DAILYINVENTORYRESPONSE
DESCRIPTOR.message_types_by_name['QueryAccoutingRequest'] = _QUERYACCOUTINGREQUEST
DESCRIPTOR.message_types_by_name['QueryAccoutingResponse'] = _QUERYACCOUTINGRESPONSE
DESCRIPTOR.message_types_by_name['QueryInventoryLogRequest'] = _QUERYINVENTORYLOGREQUEST
DESCRIPTOR.message_types_by_name['QueryInventoryLogResponse'] = _QUERYINVENTORYLOGRESPONSE
DESCRIPTOR.message_types_by_name['QueryMcuRealtimeInventoryRequest'] = _QUERYMCUREALTIMEINVENTORYREQUEST
DESCRIPTOR.message_types_by_name['QueryMcuRealtimeInventoryResponse'] = _QUERYMCUREALTIMEINVENTORYRESPONSE
DESCRIPTOR.message_types_by_name['Inventory'] = _INVENTORY
DESCRIPTOR.message_types_by_name['ExtraDetail'] = _EXTRADETAIL
DESCRIPTOR.message_types_by_name['DailyInventory'] = _DAILYINVENTORY
DESCRIPTOR.message_types_by_name['InventoryLog'] = _INVENTORYLOG
DESCRIPTOR.message_types_by_name['McuInventory'] = _MCUINVENTORY
DESCRIPTOR.message_types_by_name['BatchQueryRequest'] = _BATCHQUERYREQUEST
DESCRIPTOR.message_types_by_name['BatchQueryResponse'] = _BATCHQUERYRESPONSE
DESCRIPTOR.message_types_by_name['QueryBohJdeInventoryDiffRequest'] = _QUERYBOHJDEINVENTORYDIFFREQUEST
DESCRIPTOR.message_types_by_name['BohJdeInventoryDiff'] = _BOHJDEINVENTORYDIFF
DESCRIPTOR.message_types_by_name['QueryBohJdeInventoryDiffResponse'] = _QUERYBOHJDEINVENTORYDIFFRESPONSE
DESCRIPTOR.message_types_by_name['Account'] = _ACCOUNT
DESCRIPTOR.message_types_by_name['RealtimeInventoryByAccountsRequest'] = _REALTIMEINVENTORYBYACCOUNTSREQUEST
DESCRIPTOR.message_types_by_name['RealtimeInventoryByAccountsResponse'] = _REALTIMEINVENTORYBYACCOUNTSRESPONSE
DESCRIPTOR.message_types_by_name['Accounts'] = _ACCOUNTS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

QuerySnapshotForSalesRequest = _reflection.GeneratedProtocolMessageType('QuerySnapshotForSalesRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYSNAPSHOTFORSALESREQUEST,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.QuerySnapshotForSalesRequest)
  ))
_sym_db.RegisterMessage(QuerySnapshotForSalesRequest)

SnapshotForSales = _reflection.GeneratedProtocolMessageType('SnapshotForSales', (_message.Message,), dict(
  DESCRIPTOR = _SNAPSHOTFORSALES,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.SnapshotForSales)
  ))
_sym_db.RegisterMessage(SnapshotForSales)

QuerySnapshotForSalesResponse = _reflection.GeneratedProtocolMessageType('QuerySnapshotForSalesResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYSNAPSHOTFORSALESRESPONSE,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.QuerySnapshotForSalesResponse)
  ))
_sym_db.RegisterMessage(QuerySnapshotForSalesResponse)

QueryInventoryLogInTotalRequest = _reflection.GeneratedProtocolMessageType('QueryInventoryLogInTotalRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYINVENTORYLOGINTOTALREQUEST,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.QueryInventoryLogInTotalRequest)
  ))
_sym_db.RegisterMessage(QueryInventoryLogInTotalRequest)

QueryInventoryLogInTotalResponse = _reflection.GeneratedProtocolMessageType('QueryInventoryLogInTotalResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYINVENTORYLOGINTOTALRESPONSE,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.QueryInventoryLogInTotalResponse)
  ))
_sym_db.RegisterMessage(QueryInventoryLogInTotalResponse)

GroupEventLog = _reflection.GeneratedProtocolMessageType('GroupEventLog', (_message.Message,), dict(
  DESCRIPTOR = _GROUPEVENTLOG,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.GroupEventLog)
  ))
_sym_db.RegisterMessage(GroupEventLog)

RealtimeInventoryRequest = _reflection.GeneratedProtocolMessageType('RealtimeInventoryRequest', (_message.Message,), dict(
  DESCRIPTOR = _REALTIMEINVENTORYREQUEST,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.RealtimeInventoryRequest)
  ))
_sym_db.RegisterMessage(RealtimeInventoryRequest)

RealtimeInventoryResponse = _reflection.GeneratedProtocolMessageType('RealtimeInventoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _REALTIMEINVENTORYRESPONSE,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.RealtimeInventoryResponse)
  ))
_sym_db.RegisterMessage(RealtimeInventoryResponse)

DailyInventoryRequest = _reflection.GeneratedProtocolMessageType('DailyInventoryRequest', (_message.Message,), dict(
  DESCRIPTOR = _DAILYINVENTORYREQUEST,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.DailyInventoryRequest)
  ))
_sym_db.RegisterMessage(DailyInventoryRequest)

DailyInventoryResponse = _reflection.GeneratedProtocolMessageType('DailyInventoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _DAILYINVENTORYRESPONSE,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.DailyInventoryResponse)
  ))
_sym_db.RegisterMessage(DailyInventoryResponse)

QueryAccoutingRequest = _reflection.GeneratedProtocolMessageType('QueryAccoutingRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYACCOUTINGREQUEST,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.QueryAccoutingRequest)
  ))
_sym_db.RegisterMessage(QueryAccoutingRequest)

QueryAccoutingResponse = _reflection.GeneratedProtocolMessageType('QueryAccoutingResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYACCOUTINGRESPONSE,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.QueryAccoutingResponse)
  ))
_sym_db.RegisterMessage(QueryAccoutingResponse)

QueryInventoryLogRequest = _reflection.GeneratedProtocolMessageType('QueryInventoryLogRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYINVENTORYLOGREQUEST,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.QueryInventoryLogRequest)
  ))
_sym_db.RegisterMessage(QueryInventoryLogRequest)

QueryInventoryLogResponse = _reflection.GeneratedProtocolMessageType('QueryInventoryLogResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYINVENTORYLOGRESPONSE,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.QueryInventoryLogResponse)
  ))
_sym_db.RegisterMessage(QueryInventoryLogResponse)

QueryMcuRealtimeInventoryRequest = _reflection.GeneratedProtocolMessageType('QueryMcuRealtimeInventoryRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYMCUREALTIMEINVENTORYREQUEST,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.QueryMcuRealtimeInventoryRequest)
  ))
_sym_db.RegisterMessage(QueryMcuRealtimeInventoryRequest)

QueryMcuRealtimeInventoryResponse = _reflection.GeneratedProtocolMessageType('QueryMcuRealtimeInventoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYMCUREALTIMEINVENTORYRESPONSE,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.QueryMcuRealtimeInventoryResponse)
  ))
_sym_db.RegisterMessage(QueryMcuRealtimeInventoryResponse)

Inventory = _reflection.GeneratedProtocolMessageType('Inventory', (_message.Message,), dict(
  DESCRIPTOR = _INVENTORY,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.Inventory)
  ))
_sym_db.RegisterMessage(Inventory)

ExtraDetail = _reflection.GeneratedProtocolMessageType('ExtraDetail', (_message.Message,), dict(
  DESCRIPTOR = _EXTRADETAIL,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.ExtraDetail)
  ))
_sym_db.RegisterMessage(ExtraDetail)

DailyInventory = _reflection.GeneratedProtocolMessageType('DailyInventory', (_message.Message,), dict(
  DESCRIPTOR = _DAILYINVENTORY,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.DailyInventory)
  ))
_sym_db.RegisterMessage(DailyInventory)

InventoryLog = _reflection.GeneratedProtocolMessageType('InventoryLog', (_message.Message,), dict(
  DESCRIPTOR = _INVENTORYLOG,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.InventoryLog)
  ))
_sym_db.RegisterMessage(InventoryLog)

McuInventory = _reflection.GeneratedProtocolMessageType('McuInventory', (_message.Message,), dict(
  DESCRIPTOR = _MCUINVENTORY,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.McuInventory)
  ))
_sym_db.RegisterMessage(McuInventory)

BatchQueryRequest = _reflection.GeneratedProtocolMessageType('BatchQueryRequest', (_message.Message,), dict(
  DESCRIPTOR = _BATCHQUERYREQUEST,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.BatchQueryRequest)
  ))
_sym_db.RegisterMessage(BatchQueryRequest)

BatchQueryResponse = _reflection.GeneratedProtocolMessageType('BatchQueryResponse', (_message.Message,), dict(
  DESCRIPTOR = _BATCHQUERYRESPONSE,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.BatchQueryResponse)
  ))
_sym_db.RegisterMessage(BatchQueryResponse)

QueryBohJdeInventoryDiffRequest = _reflection.GeneratedProtocolMessageType('QueryBohJdeInventoryDiffRequest', (_message.Message,), dict(
  DESCRIPTOR = _QUERYBOHJDEINVENTORYDIFFREQUEST,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.QueryBohJdeInventoryDiffRequest)
  ))
_sym_db.RegisterMessage(QueryBohJdeInventoryDiffRequest)

BohJdeInventoryDiff = _reflection.GeneratedProtocolMessageType('BohJdeInventoryDiff', (_message.Message,), dict(
  DESCRIPTOR = _BOHJDEINVENTORYDIFF,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.BohJdeInventoryDiff)
  ))
_sym_db.RegisterMessage(BohJdeInventoryDiff)

QueryBohJdeInventoryDiffResponse = _reflection.GeneratedProtocolMessageType('QueryBohJdeInventoryDiffResponse', (_message.Message,), dict(
  DESCRIPTOR = _QUERYBOHJDEINVENTORYDIFFRESPONSE,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.QueryBohJdeInventoryDiffResponse)
  ))
_sym_db.RegisterMessage(QueryBohJdeInventoryDiffResponse)

Account = _reflection.GeneratedProtocolMessageType('Account', (_message.Message,), dict(
  DESCRIPTOR = _ACCOUNT,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.Account)
  ))
_sym_db.RegisterMessage(Account)

RealtimeInventoryByAccountsRequest = _reflection.GeneratedProtocolMessageType('RealtimeInventoryByAccountsRequest', (_message.Message,), dict(
  DESCRIPTOR = _REALTIMEINVENTORYBYACCOUNTSREQUEST,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.RealtimeInventoryByAccountsRequest)
  ))
_sym_db.RegisterMessage(RealtimeInventoryByAccountsRequest)

RealtimeInventoryByAccountsResponse = _reflection.GeneratedProtocolMessageType('RealtimeInventoryByAccountsResponse', (_message.Message,), dict(
  DESCRIPTOR = _REALTIMEINVENTORYBYACCOUNTSRESPONSE,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.RealtimeInventoryByAccountsResponse)
  ))
_sym_db.RegisterMessage(RealtimeInventoryByAccountsResponse)

Accounts = _reflection.GeneratedProtocolMessageType('Accounts', (_message.Message,), dict(
  DESCRIPTOR = _ACCOUNTS,
  __module__ = 'inventory_bi_pb2'
  # @@protoc_insertion_point(class_scope:inventory_bi.Accounts)
  ))
_sym_db.RegisterMessage(Accounts)



_INVENTORYBISERVICE = _descriptor.ServiceDescriptor(
  name='InventoryBiService',
  full_name='inventory_bi.InventoryBiService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=7322,
  serialized_end=8872,
  methods=[
  _descriptor.MethodDescriptor(
    name='RealtimeInventory',
    full_name='inventory_bi.InventoryBiService.RealtimeInventory',
    index=0,
    containing_service=None,
    input_type=_REALTIMEINVENTORYREQUEST,
    output_type=_REALTIMEINVENTORYRESPONSE,
    serialized_options=_b('\202\323\344\223\002#\022!/api/v2/supply/inventory/realtime'),
  ),
  _descriptor.MethodDescriptor(
    name='DailyInventory',
    full_name='inventory_bi.InventoryBiService.DailyInventory',
    index=1,
    containing_service=None,
    input_type=_DAILYINVENTORYREQUEST,
    output_type=_DAILYINVENTORYRESPONSE,
    serialized_options=_b('\202\323\344\223\002 \022\036/api/v2/supply/inventory/daily'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryAccouting',
    full_name='inventory_bi.InventoryBiService.QueryAccouting',
    index=2,
    containing_service=None,
    input_type=_QUERYACCOUTINGREQUEST,
    output_type=_QUERYACCOUTINGRESPONSE,
    serialized_options=_b('\202\323\344\223\002%\022#/api/v2/supply/inventory/accounting'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryInventoryLog',
    full_name='inventory_bi.InventoryBiService.QueryInventoryLog',
    index=3,
    containing_service=None,
    input_type=_QUERYINVENTORYLOGREQUEST,
    output_type=_QUERYINVENTORYLOGRESPONSE,
    serialized_options=_b('\202\323\344\223\002\036\022\034/api/v2/supply/inventory/log'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryMcuRealtimeInventory',
    full_name='inventory_bi.InventoryBiService.QueryMcuRealtimeInventory',
    index=4,
    containing_service=None,
    input_type=_QUERYMCUREALTIMEINVENTORYREQUEST,
    output_type=_QUERYMCUREALTIMEINVENTORYRESPONSE,
    serialized_options=_b('\202\323\344\223\002\"\022 /api/v2/supply/mcu/inventory/log'),
  ),
  _descriptor.MethodDescriptor(
    name='BatchQuery',
    full_name='inventory_bi.InventoryBiService.BatchQuery',
    index=5,
    containing_service=None,
    input_type=_BATCHQUERYREQUEST,
    output_type=_BATCHQUERYRESPONSE,
    serialized_options=_b('\202\323\344\223\002#\022!/api/v2/supply/batch/result/query'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryInventoryLogInTotal',
    full_name='inventory_bi.InventoryBiService.QueryInventoryLogInTotal',
    index=6,
    containing_service=None,
    input_type=_QUERYINVENTORYLOGINTOTALREQUEST,
    output_type=_QUERYINVENTORYLOGINTOTALRESPONSE,
    serialized_options=_b('\202\323\344\223\002$\022\"/api/v2/supply/inventory/log/total'),
  ),
  _descriptor.MethodDescriptor(
    name='QuerySnapshotForSales',
    full_name='inventory_bi.InventoryBiService.QuerySnapshotForSales',
    index=7,
    containing_service=None,
    input_type=_QUERYSNAPSHOTFORSALESREQUEST,
    output_type=_QUERYSNAPSHOTFORSALESRESPONSE,
    serialized_options=_b('\202\323\344\223\002 \022\036/api/v2/supply/inventory/sales'),
  ),
  _descriptor.MethodDescriptor(
    name='QueryBohJdeInventoryDiff',
    full_name='inventory_bi.InventoryBiService.QueryBohJdeInventoryDiff',
    index=8,
    containing_service=None,
    input_type=_QUERYBOHJDEINVENTORYDIFFREQUEST,
    output_type=_QUERYBOHJDEINVENTORYDIFFRESPONSE,
    serialized_options=_b('\202\323\344\223\002\037\022\035/api/v2/supply/inventory/diff'),
  ),
  _descriptor.MethodDescriptor(
    name='RealtimeInventoryByAccounts',
    full_name='inventory_bi.InventoryBiService.RealtimeInventoryByAccounts',
    index=9,
    containing_service=None,
    input_type=_REALTIMEINVENTORYBYACCOUNTSREQUEST,
    output_type=_REALTIMEINVENTORYBYACCOUNTSRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\"-/api/v2/supply/inventory/realtime/by/accounts:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_INVENTORYBISERVICE)

DESCRIPTOR.services_by_name['InventoryBiService'] = _INVENTORYBISERVICE

# @@protoc_insertion_point(module_scope)
