# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: inventory_cut.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='inventory_cut.proto',
  package='inventory_cut',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x13inventory_cut.proto\x12\rinventory_cut\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\\\n\x13TaskCallBackRequest\x12\x11\n\trequestID\x18\x05 \x01(\x04\x12\x10\n\x08\x63\x61tegory\x18\n \x01(\r\x12\x0f\n\x07success\x18\x0f \x01(\x08\x12\x0f\n\x07message\x18\x14 \x01(\t\",\n\x0b\x43ommonReply\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x0f\n\x07message\x18\n \x01(\t\"o\n\x17\x43utDailySnapshotRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x13\n\x0bproduct_ids\x18\x02 \x03(\x04\x12,\n\x08\x65nd_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"G\n\x18\x43utDailySnapshotResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\n\n\x02id\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\"\xcf\x01\n\x19ListCostTriggerLogRequest\x12.\n\nstart_time\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nperiod_ids\x18\x03 \x03(\x04\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\r\n\x05limit\x18\x05 \x01(\x04\x12\x0e\n\x06offset\x18\x06 \x01(\x04\x12\x11\n\treq_types\x18\x07 \x03(\t\"X\n\x1aListCostTriggerLogResponse\x12+\n\x04rows\x18\x01 \x03(\x0b\x32\x1d.inventory_cut.CostTriggerLog\x12\r\n\x05total\x18\x02 \x01(\x04\"\xaa\x03\n\x0e\x43ostTriggerLog\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x10\n\x08\x62\x61tch_no\x18\x02 \x01(\x04\x12\x11\n\tbranch_id\x18\x03 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x04 \x01(\t\x12\x10\n\x08req_type\x18\x05 \x01(\t\x12\x11\n\tperiod_id\x18\x06 \x01(\x04\x12.\n\nstart_time\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04sent\x18\t \x01(\t\x12\x0b\n\x03ret\x18\n \x01(\t\x12\x0f\n\x07success\x18\x0b \x01(\x08\x12\x0b\n\x03msg\x18\x0c \x01(\t\x12\x0e\n\x06status\x18\r \x01(\t\x12\x12\n\npartner_id\x18\x0e \x01(\x04\x12\x12\n\ncreated_by\x18\x0f \x01(\x04\x12.\n\ncreated_at\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xc7\x01\n\x11ListPeriodRequest\x12.\n\nstart_time\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nperiod_ids\x18\x03 \x03(\x04\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\r\n\x05limit\x18\x05 \x01(\x04\x12\x0e\n\x06offset\x18\x06 \x01(\x04\x12\x11\n\tbranch_id\x18\x07 \x01(\x04\"H\n\x12ListPeriodResponse\x12#\n\x04rows\x18\x01 \x03(\x0b\x32\x15.inventory_cut.Period\x12\r\n\x05total\x18\x02 \x01(\x04\"\xe0\x02\n\x06Period\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x11\n\tbranch_id\x18\x02 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x03 \x01(\t\x12\x10\n\x08req_type\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x0c\n\x04name\x18\x06 \x01(\t\x12.\n\nstart_time\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\t \x01(\t\x12\x12\n\npartner_id\x18\x0e \x01(\x04\x12\x12\n\ncreated_by\x18\x0f \x01(\x04\x12.\n\ncreated_at\x18\x10 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x11 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\x83\x02\n\x17TriggerCostCountRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\x12\x13\n\x0bproduct_ids\x18\x02 \x03(\x04\x12\x13\n\x0b\x62ranch_type\x18\x03 \x01(\t\x12.\n\nstart_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tperiod_id\x18\x06 \x01(\x04\x12\x11\n\ttask_type\x18\x07 \x01(\t\x12\x13\n\x0breport_type\x18\x08 \x01(\t\x12\x12\n\nextra_type\x18\t \x01(\t\"G\n\x18TriggerCostCountResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\n\n\x02id\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t2\xd7\x05\n\x13InventoryCutService\x12\x92\x01\n\x10\x43utDailySnapshot\x12&.inventory_cut.CutDailySnapshotRequest\x1a\'.inventory_cut.CutDailySnapshotResponse\"-\x82\xd3\xe4\x93\x02\'\"\"/api/v2/supply/inventory/daily/cut:\x01*\x12\x7f\n\x0cTaskCallBack\x12\".inventory_cut.TaskCallBackRequest\x1a\x1a.inventory_cut.CommonReply\"/\x82\xd3\xe4\x93\x02)\"$/api/v2/supply/cost/trigger/callback:\x01*\x12\x96\x01\n\x12ListCostTriggerLog\x12(.inventory_cut.ListCostTriggerLogRequest\x1a).inventory_cut.ListCostTriggerLogResponse\"+\x82\xd3\xe4\x93\x02%\" /api/v2/supply/cost/trigger/list:\x01*\x12}\n\nListPeriod\x12 .inventory_cut.ListPeriodRequest\x1a!.inventory_cut.ListPeriodResponse\"*\x82\xd3\xe4\x93\x02$\"\x1f/api/v2/supply/cost/period/list:\x01*\x12\x91\x01\n\x10TriggerCostCount\x12&.inventory_cut.TriggerCostCountRequest\x1a\'.inventory_cut.TriggerCostCountResponse\",\x82\xd3\xe4\x93\x02&\"!/api/v2/supply/cost/trigger/count:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_TASKCALLBACKREQUEST = _descriptor.Descriptor(
  name='TaskCallBackRequest',
  full_name='inventory_cut.TaskCallBackRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='requestID', full_name='inventory_cut.TaskCallBackRequest.requestID', index=0,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category', full_name='inventory_cut.TaskCallBackRequest.category', index=1,
      number=10, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='success', full_name='inventory_cut.TaskCallBackRequest.success', index=2,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='message', full_name='inventory_cut.TaskCallBackRequest.message', index=3,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=101,
  serialized_end=193,
)


_COMMONREPLY = _descriptor.Descriptor(
  name='CommonReply',
  full_name='inventory_cut.CommonReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory_cut.CommonReply.code', index=0,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='message', full_name='inventory_cut.CommonReply.message', index=1,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=195,
  serialized_end=239,
)


_CUTDAILYSNAPSHOTREQUEST = _descriptor.Descriptor(
  name='CutDailySnapshotRequest',
  full_name='inventory_cut.CutDailySnapshotRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory_cut.CutDailySnapshotRequest.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='inventory_cut.CutDailySnapshotRequest.product_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='inventory_cut.CutDailySnapshotRequest.end_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=241,
  serialized_end=352,
)


_CUTDAILYSNAPSHOTRESPONSE = _descriptor.Descriptor(
  name='CutDailySnapshotResponse',
  full_name='inventory_cut.CutDailySnapshotResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='inventory_cut.CutDailySnapshotResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory_cut.CutDailySnapshotResponse.id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='inventory_cut.CutDailySnapshotResponse.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=354,
  serialized_end=425,
)


_LISTCOSTTRIGGERLOGREQUEST = _descriptor.Descriptor(
  name='ListCostTriggerLogRequest',
  full_name='inventory_cut.ListCostTriggerLogRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_time', full_name='inventory_cut.ListCostTriggerLogRequest.start_time', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='inventory_cut.ListCostTriggerLogRequest.end_time', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_ids', full_name='inventory_cut.ListCostTriggerLogRequest.period_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='inventory_cut.ListCostTriggerLogRequest.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='inventory_cut.ListCostTriggerLogRequest.limit', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='inventory_cut.ListCostTriggerLogRequest.offset', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='req_types', full_name='inventory_cut.ListCostTriggerLogRequest.req_types', index=6,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=428,
  serialized_end=635,
)


_LISTCOSTTRIGGERLOGRESPONSE = _descriptor.Descriptor(
  name='ListCostTriggerLogResponse',
  full_name='inventory_cut.ListCostTriggerLogResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='inventory_cut.ListCostTriggerLogResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory_cut.ListCostTriggerLogResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=637,
  serialized_end=725,
)


_COSTTRIGGERLOG = _descriptor.Descriptor(
  name='CostTriggerLog',
  full_name='inventory_cut.CostTriggerLog',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory_cut.CostTriggerLog.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='batch_no', full_name='inventory_cut.CostTriggerLog.batch_no', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory_cut.CostTriggerLog.branch_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='inventory_cut.CostTriggerLog.branch_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='req_type', full_name='inventory_cut.CostTriggerLog.req_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_id', full_name='inventory_cut.CostTriggerLog.period_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_time', full_name='inventory_cut.CostTriggerLog.start_time', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='inventory_cut.CostTriggerLog.end_time', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sent', full_name='inventory_cut.CostTriggerLog.sent', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ret', full_name='inventory_cut.CostTriggerLog.ret', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='success', full_name='inventory_cut.CostTriggerLog.success', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='msg', full_name='inventory_cut.CostTriggerLog.msg', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='inventory_cut.CostTriggerLog.status', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='inventory_cut.CostTriggerLog.partner_id', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='inventory_cut.CostTriggerLog.created_by', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='inventory_cut.CostTriggerLog.created_at', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='inventory_cut.CostTriggerLog.updated_at', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=728,
  serialized_end=1154,
)


_LISTPERIODREQUEST = _descriptor.Descriptor(
  name='ListPeriodRequest',
  full_name='inventory_cut.ListPeriodRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_time', full_name='inventory_cut.ListPeriodRequest.start_time', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='inventory_cut.ListPeriodRequest.end_time', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_ids', full_name='inventory_cut.ListPeriodRequest.period_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='inventory_cut.ListPeriodRequest.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='inventory_cut.ListPeriodRequest.limit', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='inventory_cut.ListPeriodRequest.offset', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory_cut.ListPeriodRequest.branch_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1157,
  serialized_end=1356,
)


_LISTPERIODRESPONSE = _descriptor.Descriptor(
  name='ListPeriodResponse',
  full_name='inventory_cut.ListPeriodResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='inventory_cut.ListPeriodResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='inventory_cut.ListPeriodResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1358,
  serialized_end=1430,
)


_PERIOD = _descriptor.Descriptor(
  name='Period',
  full_name='inventory_cut.Period',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory_cut.Period.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory_cut.Period.branch_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='inventory_cut.Period.branch_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='req_type', full_name='inventory_cut.Period.req_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='inventory_cut.Period.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='inventory_cut.Period.name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_time', full_name='inventory_cut.Period.start_time', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_time', full_name='inventory_cut.Period.end_time', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='inventory_cut.Period.status', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='inventory_cut.Period.partner_id', index=9,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='inventory_cut.Period.created_by', index=10,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='inventory_cut.Period.created_at', index=11,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='inventory_cut.Period.updated_at', index=12,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1433,
  serialized_end=1785,
)


_TRIGGERCOSTCOUNTREQUEST = _descriptor.Descriptor(
  name='TriggerCostCountRequest',
  full_name='inventory_cut.TriggerCostCountRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='inventory_cut.TriggerCostCountRequest.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='inventory_cut.TriggerCostCountRequest.product_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='inventory_cut.TriggerCostCountRequest.branch_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='inventory_cut.TriggerCostCountRequest.start_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='inventory_cut.TriggerCostCountRequest.end_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_id', full_name='inventory_cut.TriggerCostCountRequest.period_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='task_type', full_name='inventory_cut.TriggerCostCountRequest.task_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='report_type', full_name='inventory_cut.TriggerCostCountRequest.report_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='extra_type', full_name='inventory_cut.TriggerCostCountRequest.extra_type', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1788,
  serialized_end=2047,
)


_TRIGGERCOSTCOUNTRESPONSE = _descriptor.Descriptor(
  name='TriggerCostCountResponse',
  full_name='inventory_cut.TriggerCostCountResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='inventory_cut.TriggerCostCountResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='inventory_cut.TriggerCostCountResponse.id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='inventory_cut.TriggerCostCountResponse.status', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2049,
  serialized_end=2120,
)

_CUTDAILYSNAPSHOTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTCOSTTRIGGERLOGREQUEST.fields_by_name['start_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTCOSTTRIGGERLOGREQUEST.fields_by_name['end_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTCOSTTRIGGERLOGRESPONSE.fields_by_name['rows'].message_type = _COSTTRIGGERLOG
_COSTTRIGGERLOG.fields_by_name['start_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_COSTTRIGGERLOG.fields_by_name['end_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_COSTTRIGGERLOG.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_COSTTRIGGERLOG.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPERIODREQUEST.fields_by_name['start_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPERIODREQUEST.fields_by_name['end_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPERIODRESPONSE.fields_by_name['rows'].message_type = _PERIOD
_PERIOD.fields_by_name['start_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PERIOD.fields_by_name['end_time'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PERIOD.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PERIOD.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRIGGERCOSTCOUNTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_TRIGGERCOSTCOUNTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['TaskCallBackRequest'] = _TASKCALLBACKREQUEST
DESCRIPTOR.message_types_by_name['CommonReply'] = _COMMONREPLY
DESCRIPTOR.message_types_by_name['CutDailySnapshotRequest'] = _CUTDAILYSNAPSHOTREQUEST
DESCRIPTOR.message_types_by_name['CutDailySnapshotResponse'] = _CUTDAILYSNAPSHOTRESPONSE
DESCRIPTOR.message_types_by_name['ListCostTriggerLogRequest'] = _LISTCOSTTRIGGERLOGREQUEST
DESCRIPTOR.message_types_by_name['ListCostTriggerLogResponse'] = _LISTCOSTTRIGGERLOGRESPONSE
DESCRIPTOR.message_types_by_name['CostTriggerLog'] = _COSTTRIGGERLOG
DESCRIPTOR.message_types_by_name['ListPeriodRequest'] = _LISTPERIODREQUEST
DESCRIPTOR.message_types_by_name['ListPeriodResponse'] = _LISTPERIODRESPONSE
DESCRIPTOR.message_types_by_name['Period'] = _PERIOD
DESCRIPTOR.message_types_by_name['TriggerCostCountRequest'] = _TRIGGERCOSTCOUNTREQUEST
DESCRIPTOR.message_types_by_name['TriggerCostCountResponse'] = _TRIGGERCOSTCOUNTRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

TaskCallBackRequest = _reflection.GeneratedProtocolMessageType('TaskCallBackRequest', (_message.Message,), dict(
  DESCRIPTOR = _TASKCALLBACKREQUEST,
  __module__ = 'inventory_cut_pb2'
  # @@protoc_insertion_point(class_scope:inventory_cut.TaskCallBackRequest)
  ))
_sym_db.RegisterMessage(TaskCallBackRequest)

CommonReply = _reflection.GeneratedProtocolMessageType('CommonReply', (_message.Message,), dict(
  DESCRIPTOR = _COMMONREPLY,
  __module__ = 'inventory_cut_pb2'
  # @@protoc_insertion_point(class_scope:inventory_cut.CommonReply)
  ))
_sym_db.RegisterMessage(CommonReply)

CutDailySnapshotRequest = _reflection.GeneratedProtocolMessageType('CutDailySnapshotRequest', (_message.Message,), dict(
  DESCRIPTOR = _CUTDAILYSNAPSHOTREQUEST,
  __module__ = 'inventory_cut_pb2'
  # @@protoc_insertion_point(class_scope:inventory_cut.CutDailySnapshotRequest)
  ))
_sym_db.RegisterMessage(CutDailySnapshotRequest)

CutDailySnapshotResponse = _reflection.GeneratedProtocolMessageType('CutDailySnapshotResponse', (_message.Message,), dict(
  DESCRIPTOR = _CUTDAILYSNAPSHOTRESPONSE,
  __module__ = 'inventory_cut_pb2'
  # @@protoc_insertion_point(class_scope:inventory_cut.CutDailySnapshotResponse)
  ))
_sym_db.RegisterMessage(CutDailySnapshotResponse)

ListCostTriggerLogRequest = _reflection.GeneratedProtocolMessageType('ListCostTriggerLogRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTCOSTTRIGGERLOGREQUEST,
  __module__ = 'inventory_cut_pb2'
  # @@protoc_insertion_point(class_scope:inventory_cut.ListCostTriggerLogRequest)
  ))
_sym_db.RegisterMessage(ListCostTriggerLogRequest)

ListCostTriggerLogResponse = _reflection.GeneratedProtocolMessageType('ListCostTriggerLogResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTCOSTTRIGGERLOGRESPONSE,
  __module__ = 'inventory_cut_pb2'
  # @@protoc_insertion_point(class_scope:inventory_cut.ListCostTriggerLogResponse)
  ))
_sym_db.RegisterMessage(ListCostTriggerLogResponse)

CostTriggerLog = _reflection.GeneratedProtocolMessageType('CostTriggerLog', (_message.Message,), dict(
  DESCRIPTOR = _COSTTRIGGERLOG,
  __module__ = 'inventory_cut_pb2'
  # @@protoc_insertion_point(class_scope:inventory_cut.CostTriggerLog)
  ))
_sym_db.RegisterMessage(CostTriggerLog)

ListPeriodRequest = _reflection.GeneratedProtocolMessageType('ListPeriodRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTPERIODREQUEST,
  __module__ = 'inventory_cut_pb2'
  # @@protoc_insertion_point(class_scope:inventory_cut.ListPeriodRequest)
  ))
_sym_db.RegisterMessage(ListPeriodRequest)

ListPeriodResponse = _reflection.GeneratedProtocolMessageType('ListPeriodResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTPERIODRESPONSE,
  __module__ = 'inventory_cut_pb2'
  # @@protoc_insertion_point(class_scope:inventory_cut.ListPeriodResponse)
  ))
_sym_db.RegisterMessage(ListPeriodResponse)

Period = _reflection.GeneratedProtocolMessageType('Period', (_message.Message,), dict(
  DESCRIPTOR = _PERIOD,
  __module__ = 'inventory_cut_pb2'
  # @@protoc_insertion_point(class_scope:inventory_cut.Period)
  ))
_sym_db.RegisterMessage(Period)

TriggerCostCountRequest = _reflection.GeneratedProtocolMessageType('TriggerCostCountRequest', (_message.Message,), dict(
  DESCRIPTOR = _TRIGGERCOSTCOUNTREQUEST,
  __module__ = 'inventory_cut_pb2'
  # @@protoc_insertion_point(class_scope:inventory_cut.TriggerCostCountRequest)
  ))
_sym_db.RegisterMessage(TriggerCostCountRequest)

TriggerCostCountResponse = _reflection.GeneratedProtocolMessageType('TriggerCostCountResponse', (_message.Message,), dict(
  DESCRIPTOR = _TRIGGERCOSTCOUNTRESPONSE,
  __module__ = 'inventory_cut_pb2'
  # @@protoc_insertion_point(class_scope:inventory_cut.TriggerCostCountResponse)
  ))
_sym_db.RegisterMessage(TriggerCostCountResponse)



_INVENTORYCUTSERVICE = _descriptor.ServiceDescriptor(
  name='InventoryCutService',
  full_name='inventory_cut.InventoryCutService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=2123,
  serialized_end=2850,
  methods=[
  _descriptor.MethodDescriptor(
    name='CutDailySnapshot',
    full_name='inventory_cut.InventoryCutService.CutDailySnapshot',
    index=0,
    containing_service=None,
    input_type=_CUTDAILYSNAPSHOTREQUEST,
    output_type=_CUTDAILYSNAPSHOTRESPONSE,
    serialized_options=_b('\202\323\344\223\002\'\"\"/api/v2/supply/inventory/daily/cut:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='TaskCallBack',
    full_name='inventory_cut.InventoryCutService.TaskCallBack',
    index=1,
    containing_service=None,
    input_type=_TASKCALLBACKREQUEST,
    output_type=_COMMONREPLY,
    serialized_options=_b('\202\323\344\223\002)\"$/api/v2/supply/cost/trigger/callback:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListCostTriggerLog',
    full_name='inventory_cut.InventoryCutService.ListCostTriggerLog',
    index=2,
    containing_service=None,
    input_type=_LISTCOSTTRIGGERLOGREQUEST,
    output_type=_LISTCOSTTRIGGERLOGRESPONSE,
    serialized_options=_b('\202\323\344\223\002%\" /api/v2/supply/cost/trigger/list:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListPeriod',
    full_name='inventory_cut.InventoryCutService.ListPeriod',
    index=3,
    containing_service=None,
    input_type=_LISTPERIODREQUEST,
    output_type=_LISTPERIODRESPONSE,
    serialized_options=_b('\202\323\344\223\002$\"\037/api/v2/supply/cost/period/list:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='TriggerCostCount',
    full_name='inventory_cut.InventoryCutService.TriggerCostCount',
    index=4,
    containing_service=None,
    input_type=_TRIGGERCOSTCOUNTREQUEST,
    output_type=_TRIGGERCOSTCOUNTRESPONSE,
    serialized_options=_b('\202\323\344\223\002&\"!/api/v2/supply/cost/trigger/count:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_INVENTORYCUTSERVICE)

DESCRIPTOR.services_by_name['InventoryCutService'] = _INVENTORYCUTSERVICE

# @@protoc_insertion_point(module_scope)
