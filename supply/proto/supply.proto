syntax = "proto3";

package supply;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

service supply {
    rpc Ping (Null) returns (Pong) {
        option (google.api.http) = {
        get: "/api/v2/supply/ping"
        };
    }
    rpc Error (Null) returns (Null) {
    }

    // DEMOMETHOD
    rpc TestMethod (Point) returns (Note) {
        option (google.api.http) = {
        post: "/api/v2/supply/demo/method"
        body: "*"
        };
    }

    // 夜间生成门市订货单批次
    rpc ActDemandBatchStore (ActDemandBatchRequest) returns (DemandBatch) {
        option (google.api.http) = {
        post: "/api/v2/supply/demand/store/batch"
        body: "*"
        };
    };

    // 夜间检查未发送订货单，生成门市要货单
    rpc CheckAndHandleDemand (Bizdt) returns (Response) {
        option (google.api.http) = {
        put: "/api/v2/supply/demand/check_handle"
        body: "*"
        };

    };

    // 查询门店订货单
    rpc ListDemand (QueryDemandRequest) returns (QueryDemandResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/demand"
        };
    }

    // 获取门市订货单详细
    rpc GetDemandDetail (IdRequest) returns (DemandEntity) {
        option (google.api.http) = {
        get: "/api/v2/supply/demand/{id}"
        };
    };

    // 获取订货商品信息
    rpc GetDemandProductDetail (IdRequest) returns (QueryDemandProductResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/demand/{id}/product"
        };
    }

    // 门店订货
    rpc UpdateDemandProduct (UpdateDemandProductRequest) returns (UpdateDemandProductRequest) {
        option (google.api.http) = {
        put: "/api/v2/supply/demand/product"
        body: "*"
        };
    }

    // 修改订单状态统一入口
    rpc ChangeDemandStatus (ChangeDemandStatusRequest) returns (Response) {
        option (google.api.http) = {
        put: "/api/v2/supply/demand/status/{id}"
        body: "*"
        };
    };

    // 创建门店紧急订货订单和门店主配单
    rpc CreateDemandMain (DemandMainRequest) returns (IdRequest) {
        option (google.api.http) = {
        post: "/api/v2/supply/demand/main/store"
        body: "*"
        };
    };

    // 创建商品主配单
    rpc CreateProductMain (ProductMainRequest) returns (IdRequest) {
        option (google.api.http) = {
        post: "/api/v2/supply/demand/main/product"
        body: "*"
        };
    }

    // 导出订单商品
    rpc ExportDemandMain (ExportDemandMainRequest) returns (ExportDemandMainResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/demand/export"
        };
    };

    // 重置门店订单
    rpc ResetDemandMain (IdRequest) returns (Response) {
        option (google.api.http) = {
        put: "/api/v2/supply/demand/main/{id}/reset"
        };
    };

    // 删除订货单(门市订货单不能删除)
    rpc DeleteDemandMain (IdRequest) returns (Response) {
        option (google.api.http) = {
        put: "/api/v2/supply/demand/main/{id}/delete"
        };
    }

    // 查询要货订单
    rpc ListDemandOrder (ListDemandOrderRequest) returns (ListDemandOrderResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/demand/main/order"
        };
    }

    // 获取要货单详细
    rpc GetDemandOrderDetail (IdRequest) returns (GetDemandOrderDetailResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/demand/order/{id}/detail"
        };
    }

    // 获取要货单商品详细
    rpc GetDemandOrderProductDetail (IdRequest) returns (GetDemandOrderProductDetailResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/demand/order/{id}/product"
        };
    }

    // 根据门店id查询可订货商品
    rpc GetValidProductByStoreId (GetValidProductByStoreIdRequest) returns (QueryDemandProductResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/store/{id}/valid/product"
        };
    }

    // 根据商品id获取可订货门店(此接口会很慢)
    rpc GetValidStoreByProductId (GetValidStoreByProductIdRequest) returns (GetValidStoreByProductIdResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/demand/valid/store"
        body: "*"
        };
    }

    // 修改订货单信息
    rpc UpdateDemandInfo (UpdateDemandInfoRequest) returns (Response) {
        option (google.api.http) = {
        post: "/api/v2/supply/demand/{id}/info"
        body: "*"
        };
    }

    // 删除紧急订货单或者主配单订货商品
    rpc DeleteDemandProduct (DeleteDemandProductRequest) returns (Response) {
        option (google.api.http) = {
        put: "/api/v2/supply/demand/delete/{demand_id}/product"
        body: "*"
        };
    }

    // 创建缺货检核单
    rpc CreateVacancyOrder (CreateVacancyOrderRequest) returns (IdRequest) {
        option (google.api.http) = {
        put: "/api/v2/supply/order/vacancy"
        body: "*"
        };
    }

    // 查询缺货检核单
    rpc ListVacancy (ListVacancyRequest) returns (ListVacancyResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/order/vacancy/list"
        };
    }

    // 查看缺货检核单详细信息
    rpc GetVacancyDetail (IdRequest) returns (Vacancy) {
        option (google.api.http) = {
        get: "/api/v2/supply/vacancy/{id}/detail"
        };
    }

    // 查看缺货检核单缺货商品
    rpc GetVacancyProduct (IdRequest) returns (GetVacancyProductResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/vacancy/{id}/product"
        };
    }

    // 查看要货单缺货商品记录
    rpc GetVacancyOrderProduct (IdRequest) returns (GetVacancyOrderProductResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/vacancy/order/{id}/product"
        };
    }

    // 保存缺货检核配送数量
    rpc UpdateVacancyQuantity (UpdateVacancyQuantityRequest) returns (Response) {
        option (google.api.http) = {
        post: "/api/v2/supply/vacancy/update/{vacancy_id}/product"
        body: "*"
        };
    }

    // 重新检核
    rpc ReVacancyQuantity (IdRequest) returns (Response) {
        option (google.api.http) = {
        put: "/api/v2/supply/vacancy/re_check/{id}"
        };
    }

    // 执行发送缺货检核成功的单子
    rpc ExecuteVacancy (IdRequest) returns (Response) {
        option (google.api.http) = {
        put: "/api/v2/supply/vacancy/execute/{id}"
        };
    }

    // 修改缺货检核单备注
    rpc UpdateVacancyRemark (UpdateVacancyRemarkRequest) returns (Response) {
        option (google.api.http) = {
        put: "/api/v2/supply/vacancy/remark/{vacancy_id}"
        body: "*"
        };
    }

    // 导出缺货信息
    rpc ExportVacancyProduct (IdRequest) returns (Response) {
    }

    // 查询首配信息
    rpc ListFirstDelivery (ListFirstDeliveryRequest) returns (ListFirstDeliveryResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/delivery/list"
        };
    }

    // 导出首配报表
    rpc ExportFirstDelivery (IdRequest) returns (Response) {
    }

    // 要货单报表
    rpc QueryBiOrderReport (QueryOrderBiReportRequest) returns (QueryOrderBiReportResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/bi/order/list"
        };
    }

    rpc CancelDemandOrder (CancelDemandOrderRequest) returns (CancelDemandOrderResponse) {
        option (google.api.http) = {
            put: "/api/v2/supply/demand/order/cancel"
            body: "*"
            };
    }
    // 导入主配单
    rpc UploadDemandMaster (UploadDemandMasterRequest) returns (UploadDemandMasterResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/demand/master/upload"
        body: "*"
        };
    };
    // 查询导入明细
    rpc GetDemandMasterUploadByBatchId (GetDemandMasterUploadByBatchIdRequest) returns (GetDemandMasterUploadByBatchIdResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/demand_master_upload/{batch_id}"
        };
    }
    // 查询导入记录
    rpc GetDemandMasterUpload (GetDemandMasterUploadRequest) returns (GetDemandMasterUploadResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/demand_master_upload"
        };
    }
    // 审核主配导入记录开始干活
    rpc ApproveDemandMasterUpload (ApproveDemandMasterUploadRequest) returns (ApproveDemandMasterUploadResponse) {
        option (google.api.http) = {
        put: "/api/v2/supply/demand_master_upload/{batch_id}"
        };
    }
    // 取消主配导入
    rpc CancelDemandMasterUpload (CancelDemandMasterUploadRequest) returns (CancelDemandMasterUploadResponse) {
        option (google.api.http) = {
        put: "/api/v2/supply/demand_master_upload/{batch_id}/cancel"
        };
    }

    rpc ListForecastStore (ListForecastStoreRequest) returns (ListForecastStoreResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/demand_suggest/forecast/stores"
        body: "*"
        };
    }

    // 查询面包和茶饮的一周内的预测销售量
    rpc ListProductSaleForecast (ListProductSaleForecastRequest) returns (ListProductSaleForecastResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/demand_suggest/forecast/products"
        body: "*"
        };
    }

    // 确认面包和茶饮的一周内的预测销售量
    rpc ConfirmProductSaleForecast (ConfirmProductSaleForecastRequest) returns (ConfirmProductSaleForecastResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/demand_suggest/forecast/confirm"
        body: "*"
        };
    }
    // 建议订货量查询
    rpc GetProductSaleForecast (GetProductSaleForecastRequest) returns (GetProductSaleForecastResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/demand_suggest/forecast"
        body: "*"
        };
    }
    // 根据门店id查询可调整订货商品
    rpc GetDemandAdjustProductByStoreId (GetDemandAdjustProductByStoreIdRequest) returns (GetDemandAdjustProductByStoreIdResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/demand/adjust/product/{store_id}"
        };
    }
    // 创建订货调整单
    rpc CreateDemandAdjust (CreateDemandAdjustRequest) returns (CreateDemandAdjustResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/demand/adjust/create"
        body: "*"
        };
    }

    // 根据订单id获取订货单商品对应的仓库
    rpc GetDistributionByDemand (IdRequest) returns (GetDistributionByDemandResponse) {
        option (google.api.http) = {
            get: "/api/v2/supply/demand/{id}/distribution"
            };
    }
    // 调整单导入
    rpc UploadOrderCollection (UploadOrderCollectionRequest) returns (UploadOrderCollectionResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/order/collection/upload"
        body: "*"
        };
    }
    // 查询调整单导入明细
    rpc GetOrderCollectionUploadByBatchId (GetOrderCollectionUploadByBatchIdRequest) returns (GetOrderCollectionUploadByBatchIdResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/order_collection_upload/{batch_id}"
        };
    }
    // 查询调整单导入记录
    rpc GetOrderCollectionUpload (GetOrderCollectionUploadRequest) returns (GetOrderCollectionUploadResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/order_collection_upload"
        };
    }
    // 审核调整单导入记录开始干活
    rpc ApproveOrderCollectionUpload (ApproveOrderCollectionUploadRequest) returns (ApproveOrderCollectionUploadResponse) {
        option (google.api.http) = {
        put: "/api/v2/supply/order_collection_upload/{batch_id}"
        };
    }
    // 取消调整单导入导入
    rpc CancelOrderCollectionUpload (CancelOrderCollectionUploadRequest) returns (CancelOrderCollectionUploadResponse) {
        option (google.api.http) = {
        put: "/api/v2/supply/order_collection_upload/{batch_id}/cancel"
        };
    }
    //Wms-boh单据核对表
    rpc WmsBohDocSummaryReport (WmsBohDocSummaryReportRequest) returns (WmsBohDocSummaryReportResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/wms_boh_doc_summary"
        };
    }
    //Wms-boh单据核对表
    rpc WmsBohDocDetailReport (WmsBohDocDetailReportRequest) returns (WmsBohDocDetailReportResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/wms_boh_doc_detail"
        };
    }
    //智能订货配置 smart-demand
    rpc CreateSmartDemandRule (CreateSmartDemandRuleRequest) returns (CreateSmartDemandRuleResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/smart_demand_rule"
        body: "*"
        };
    }
    //更新智能订货配置 smart-demand
    rpc PutSmartDemandRule (PutSmartDemandRuleRequest) returns (PutSmartDemandRuleResponse) {
        option (google.api.http) = {
        put: "/api/v2/supply/smart_demand_rule"
        body: "*"
        };
    }
    //状态智能订货配置 smart-demand
    rpc ActionSmartDemandRule (ActionSmartDemandRuleRequest) returns (ActionSmartDemandRuleResponse) {
        option (google.api.http) = {
        put: "/api/v2/supply/smart_demand_rule_status"
        body: "*"
        };
    }
    //获取智能订货配置列表加明细
    rpc GetSmartDemandRule (GetSmartDemandRuleRequest) returns (GetSmartDemandRuleResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/smart_demand_rule"
        };
    }

    // 退货单报表
    rpc QueryReturnOrders (QueryReturnOrdersRequest) returns (QueryReturnOrdersResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/returns/orders/list"
        body: "*"
        };
    }

    //熊猫不走demo环境演示api
    rpc DemoGetStoreProduct (DemoGetStoreProductRequest) returns (DemoGetStoreProductResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/demo/{store_id}/order_product"
        };
    }
    //熊猫不走demo环境提交订货成品
    rpc DemoSplitStoreProduct (DemoSplitStoreProductRequest) returns (DemoSplitStoreProductResponse) {
        option (google.api.http) = {
        put: "/api/v2/supply/demo/{store_id}/order_split"
        body: "*"
        };
    }
    //熊猫不走demo环境保存订货单
    rpc DemoSaveStoreProduct (DemoSaveStoreProductRequest) returns (DemoSaveStoreProductResponse) {
        option (google.api.http) = {
        put: "/api/v2/supply/demo/{store_id}/order_save"
        body: "*"
        };
    }
}

message DistributionProduct {
    uint64 product_id = 1;
    uint64 distribute_by = 2;
    string distribution_type = 3;
}

message GetDistributionByDemandResponse {
    repeated DistributionProduct products = 1;
}


message ConfirmProductSaleForecastRequest {
    message ForecastInfo {
        uint64 id = 1;
        int64 confirm_amount = 2;
    }
    repeated ForecastInfo forecast_infos = 1;
}

message ConfirmProductSaleForecastResponse {
    bool success = 1;
}

message ListProductSaleForecastRequest {
    uint64 store_id = 1;
    google.protobuf.Timestamp forecast_date = 2;
    string product_sale_type = 3;
    // 排序(默认asc)
    string order = 4;
    string sort = 5;
}

message ListProductSaleForecastResponse {
    message ForecastInfo {
        uint64 id = 4;
        int64 forecast_amount = 1;
        int64 confirm_amount = 2;
        google.protobuf.Timestamp forecast_date = 3;
    }
    message ProductSaleForecast {
        uint64 product_id = 2;
        repeated ForecastInfo forecast_infos = 3;
    }
    int64 total = 1;
    repeated ProductSaleForecast sale_forecasts = 2;
}

message ListForecastStoreRequest {
    google.protobuf.Timestamp forecast_date = 1;
    repeated uint64 store_ids = 2;
    int64 limit = 3;
    int64 offset = 4;
    string store_type = 5;
}

message ListForecastStoreResponse {
    message ForecastStore {
        uint64 store_id = 1;
        google.protobuf.Timestamp forecast_date = 2;
        uint64 updated_by = 3;
        google.protobuf.Timestamp updated_at = 4;

    }
    uint64 total = 1;
    repeated ForecastStore forecast_stores = 2;
}


message UpdateSaleForecastAmountRequest {
    uint64 store_id = 1;
    map<uint64, string> status_data = 2; // <id>: <status>
    map<uint64, int64> amount_data = 3; // <id>: <amount>
}

message UpdateSaleForecastAmountResponse {
    bool success = 1;
}

message ListSaleForecastAmountRequest {
    uint64 store_id = 7;
    // 预测日期
    google.protobuf.Timestamp forecast_date = 2;
    // 偏移
    int32 offset = 3;
    // 每页数量
    int32 limit = 4;
    // asc|desc
    string order = 5;
    // 状态(INIT:新建, CONFIRM: 确认)
}


message ListSaleForecastAmountResponse {
    int64 total = 1;
    repeated SaleForecastAmount rows = 2;
}

message SaleForecastAmount {
    google.protobuf.Timestamp forecast_date = 1;
    int64 forecast_amount = 2;
    int64 confirm_amount = 3;
    uint64 partner_id = 4;
    uint64 store_id = 5;
    uint64 product_id = 6;
    string status = 7;
}

message Null {
}
message Pong {
    string pong = 1;
}

message Point {
    bytes x = 1;
    int32 y = 2;
}

message Note {
    bytes data = 1;
    string desc = 2;
}

// 主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT, 总部帮订:MASTER)
enum SubType {
    // 门店主配
    STORE = 0;
    // 商品主配
    PRODUCT = 1;
    // 总部帮订
    MASTER = 2;
}

// 门市订货(SD)、紧急订货(HD)、主配订货(MD)
enum Type {
    // 门市订货
    SD = 0;
    // 紧急订货
    HD = 1;
    // 主配订货
    MD = 2;
}

enum Tag_type {
    //成品订货
    FINISHED = 0;
    //茶饮订货
    TEA = 1;
    //面包订货
    BREAD = 2;
    //原料订货
    RAW = 3;
}

// 取消冷链要货单
message CancelDemandOrderRequest {
    repeated uint64 ids = 1;
    string action = 2;
    // 附件
    //    string attachments = 3;
    //    string action = 1;
    //    // id 和附件关联
    repeated Attachments attachments = 3;
}

message Attachments {
    uint64 id = 1;
    string attachment = 2;
}

message CancelDemandOrderResponse {
    string result = 1;
}

// 根据商品id获得可订货门店请求参数
message GetValidStoreByProductIdRequest {
    // 商品id
    repeated uint64 product_ids = 1;
    // 订单类型:MD(只有MD)
    string type = 2;
    // 订货日期
    google.protobuf.Timestamp order_date = 3;
    // 区域id
    repeated uint64 branch_ids = 4;
    // 分类id
    repeated uint64 category_ids = 5;
    string lan = 6;
}

// 根据商品id获得可订货门店返回参数
message GetValidStoreByProductIdResponse {
    message ValidStore {
        // 门店id
        uint64 store_id = 1;
        // 门店code
        string store_code = 2;
        // 门店名称
        string store_name = 3;
        // 商品信息
        repeated StoreProduct store_products = 4;
    }

    message StoreProduct {
        // 到货天数
        int32 arrival_days = 1;
        // 配送/供应商中心
        uint64 distribute_by = 2;
        // 配送(NMD)、采购(PUR)
        string distribution_type = 3;
        // 递增订量
        float increment_quantity = 4;
        // 最大订货量
        float max_quantity = 5;
        // 最小订货量
        float min_quantity = 6;
        // 订货单位id
        uint64 unit_id = 7;
        // 订货单位名称
        string unit_name = 8;
        // 订货规格
        string unit_spec = 9;
        // 直采价格
        float purchase_price = 10;
        // 直采税率
        float purchase_tax = 11;
        // 商品id
        uint64 product_id = 12;
        // 商品编码
        string product_code = 13;
        // 商品名称
        string product_name = 14;
    }

    repeated ValidStore rows = 1;
    string description = 2;

}

// 要货单报表请求返回
message QueryOrderBiReportResponse {
    message OrderBiReport {
        string code = 1;
        google.protobuf.Timestamp demand_date = 2;
        google.protobuf.Timestamp expect_date = 3;
        string store_code = 4;
        string store_name = 5;
        string product_code = 6;
        string product_name = 7;
        string spec = 8;
        uint64 category_id = 9;
        float quantity = 10;
        uint64 unit_id = 11;
        string unit_name = 12;
        uint64 jde_order_id = 13;
        string jde_mcu = 14;
        string jde_order_type = 15;
        string havi_code = 16;
    }
    repeated OrderBiReport rows = 1;
    int32 total = 2;
    string classification = 3;
    float sum_quantity = 4;
    float sum_accounting_quantity = 5;
}

// 要货单报表请求参数
message QueryOrderBiReportRequest {
    // 开始订货日期
    google.protobuf.Timestamp start_demand_date = 1;
    // 结束订货日期
    google.protobuf.Timestamp end_demand_date = 2;
    // 开始到货日期
    google.protobuf.Timestamp start_arrival_date = 3;
    // 结束到货日期
    google.protobuf.Timestamp end_arrival_date = 4;
    // 查询分类（汇总:sum, 明细:detail）
    string classification = 5;
    // 订单分类(门市订货单:SD, 门市紧急订货单:HD, 主配单:MD)
    string type = 6;
    // 商品id
    repeated uint64 product_ids = 7;
    // 商品类别
    repeated uint64 category_ids = 8;
    // 门店id
    repeated uint64 store_ids = 9;
    // 每页数量
    int32 limit = 10;
    // 偏移量
    int32 offset = 11;
    // 排序(默认asc)
    string order = 12;
    string sort = 13;
    string code = 14;
    string havi_code = 15;
    int64 jde_order_id = 16;
    bool is_wms_store = 17;
    string lan = 18;
}

message UpdateVacancyRemarkRequest {
    uint64 vacancy_id = 1;
    string remark = 2;
}

// 首配查询请求参数
message ListFirstDeliveryRequest {
    // 门店编号
    string store_code = 1;
    // 商品编号
    string product_code = 2;
    // 配送开始日期
    google.protobuf.Timestamp start_order_date = 3;
    // 配送结束日期
    google.protobuf.Timestamp end_order_date = 4;
    // 每页数量
    int32 limit = 5;
    // 偏移量
    int32 offset = 6;
    // 排序
    string order = 7;
    string sort = 8;
    string lan = 9;
}

// 首配查询返回参数
message ListFirstDeliveryResponse {
    message FirstDelivery {
        // 门店编号
        string store_code = 1;
        // 门店名称
        string store_name = 2;
        // 商品编号
        string product_code = 3;
        // 商品名称
        string product_name = 4;
        // 配送数量
        float distr_quantity = 5;
        // 订货单位
        string unit_name = 6;
        // 配送日期
        google.protobuf.Timestamp order_date = 7;
    }
    repeated FirstDelivery rows = 1;
    int32 total = 2;
}

// 保存缺货检核配送数量请求参数
message UpdateVacancyQuantityRequest {
    message vacancy_product {
        // 要货单商品记录id
        uint64 id = 1;
        // 修改的配送数量
        float quantity = 2;
    }
    uint64 vacancy_id = 1;
    repeated vacancy_product vacancy_products = 2;
}

// 缺货商品的配送单信息
message GetVacancyOrderProductResponse {
    repeated VacancyOrderProduct rows = 1;
}

// 缺货商品关联的配送单商品
message VacancyOrderProduct {
    // 配送单商品记录id
    uint64 id = 1;
    // 门店名称
    string store_name = 2;
    // 门店编码
    string store_code = 3;
    // 订货日期
    google.protobuf.Timestamp order_date = 4;
    // 商品id
    uint64 product_id = 5;
    // 商品编码
    string product_code = 6;
    // 商品名称
    string product_name = 7;
    // 要货单编号
    string order_code = 8;
    // 要货单id
    uint64 order_id = 9;
    // 单位id
    uint64 unit_id = 10;
    // 单位名称
    string unit_name = 11;
    // 最大订货量
    float max_quantity = 12;
    // 最小订货量
    float min_quantity = 13;
    // 递增订货量
    float increment_quantity = 14;
    // 配送中心id
    uint64 center_id = 15;
    // 要货单订货数量
    float quantity = 16;
    // 配送数量
    float distr_quantity = 17;
    // 仓库可用库存
    float valid_quantity = 18;
    // 仓库缺货数量
    float vacancy_quantity = 19;
    // 仓库名称
    string repo_name = 20;
}

// 查看缺货检核单缺货商品返回参数
message GetVacancyProductResponse {
    repeated VacancyProduct rows = 1;
    int32 total = 2;
}

// 缺货商品
message VacancyProduct {
    uint64 id = 1;
    // 缺货主表id
    uint64 vacancy_id = 2;
    // 缺货商品id
    uint64 product_id = 3;
    // 缺货商品名称
    string product_name = 4;
    // 缺货商品编号
    string product_code = 5;
    // 总订货数量
    float order_quantity = 6;
    // 单位id
    uint64 unit_id = 7;
    // 单位名称
    string unit_name = 8;
    // 仓库编号
    string repo_code = 9;
    // 仓库名称
    string repo_name = 10;
    // 可用数量
    float valid_quantity = 11;
    // 可用数量
    float promise_quantity = 12;
    // 缺少数量
    float vacancy_quantity = 13;
}

// 查询缺货检核单参数
message ListVacancyRequest {
    // 起始日期
    google.protobuf.Timestamp start_date = 1;
    // 结束日期
    google.protobuf.Timestamp end_date = 2;
    // 偏移
    int32 offset = 3;
    // 每页数量
    int32 limit = 4;
    // asc|desc
    string order = 5;
    // 状态(INITED:新建, CHECKING:正在检核, CHECKED:检核完成, EXECUTING:正在执行,SUCCESS:执行成功)
    string status = 6;
    repeated uint64 distrcenter_ids = 7;
}

message ListVacancyResponse {
    repeated Vacancy rows = 1;
    int32 total = 2;
}

// 查询缺货检核单返回参数
message Vacancy {
    uint64 id = 1;
    // 检核单编号
    string code = 2;
    // 要货日期
    google.protobuf.Timestamp check_date = 3;
    // 商户id
    uint64 partner_id = 4;
    // 备注
    string remark = 5;
    // 原因
    string reason = 6;
    // 创建人id
    uint64 created_by = 7;
    // 创建时间
    google.protobuf.Timestamp created_at = 8;
    // 创建人
    string created_name = 9;
    // 更新人id
    uint64 updated_by = 10;
    // 更新时间
    google.protobuf.Timestamp updated_at = 11;
    // 更新人名称
    string updated_name = 12;
    // 状态(INITED:新建, CHECKING:正在检核, CHECKED:检核完成, EXECUTING:正在执行,SUCCESS:执行成功, FAILED:失败(原因在reason))
    string status = 13;
    string distrcenter_ids = 14;
}

// 创建缺货检核单请求
message CreateVacancyOrderRequest {
    // 检核日期
    google.protobuf.Timestamp order_date = 1;
    // 备注
    string remark = 2;
    repeated uint64 distrcenter_ids = 3;
}

// 删除订货商品请求
message DeleteDemandProductRequest {
    // 订货单id
    uint64 demand_id = 1;
    // 订货单订货记录id
    repeated uint64 demand_product_ids = 2;
}

// 查询可订货商品请求参数
message GetValidProductByStoreIdRequest {
    // 门店id
    uint64 id = 1;
    // 订货类型:门市订货(SD)、紧急订货(HD)、主配订货(MD)
    string type = 2;
    // 订货日期
    google.protobuf.Timestamp order_date = 3;
    // 物流模式: 配送-NMD, 直送-PUR
    string distribution_type = 4;
    // 配送中心
    uint64 vendor_id = 5;
    // 商品分类
    repeated uint64 category_ids = 6;
    string lan = 7;
    // 排序条件
    string order_by = 8;
    // asc or desc, 暂时未使用
    string sort = 9;
}

// 更新订货单信息参数
message UpdateDemandInfoRequest {
    uint64 id = 1;
    // 备注
    string remark = 2;
    // 到货日期(只有主配单和紧急订货单才有的参数，门市订货单请不要传)
    google.protobuf.Timestamp arrival_date = 3;
    string attachments = 4;
}

message Bizdt {
    // 营业日期
    google.protobuf.Timestamp bizdt = 1;
}

message ChangeDemandStatusRequest {
    uint64 id = 1;
    // 单据业务状态(INITED,F_COMMIT(成品订货提交),COMMIT(提交), CAL_DONE(原料计算完成, 这步不能通过前端修改), CAL_FAILED(原料计算失败，这步也不能通过前端修改),REJECTED(驳回订货单),APPROVED(审核订货单通过),FREEZE(冻结订货单),CANCELLED
    string status = 2;
    string description = 3;
    // 附件
    string attachments = 4;
}

//夜间执行门市订货批处理请求参数
message ActDemandBatchRequest {
    // 执行模式: create, recover
    string mode = 1;
    // 营业日期
    google.protobuf.Timestamp bizdt = 2;
}

// 订货批次
message DemandBatch {
    uint64 id = 1;
    // 营业日期
    google.protobuf.Timestamp bizdt = 2;
    // 商户id
    uint64 partner_id = 3;
    // 人员id
    uint64 user_id = 4;
    // 批次状态
    string status = 5;
    // 门市订货单生成成功的个数
    uint64 success = 6;
    // 门市订货单生成失败的个数
    uint64 failed = 7;
    // 门市订货单生成处理中的个数
    uint64 processing = 8;
    // 重试次数
    int32 retry_count = 9;
    // 创建时间
    google.protobuf.Timestamp created_at = 10;
    // 更新时间
    google.protobuf.Timestamp updated_at = 11;
    // 描述
    string reason_type = 12;
}

// 要货批次
message OrderBatch {
    uint64 id = 1;
    // 营业日期
    google.protobuf.Timestamp bizdt = 3;
    // 商户id
    uint64 partner_id = 2;
    // 批次状态
    string status = 4;
    // 门市要货单生成成功的个数
    uint64 success = 5;
    // 门市要货单生成失败的个数
    uint64 failed = 6;
    // 门市要货单生成处理中的个数
    uint64 processing = 7;
    // 重试次数
    int32 retry_count = 8;
    // 创建时间
    google.protobuf.Timestamp created_at = 9;
    // 更新时间
    google.protobuf.Timestamp updated_at = 10;
    // 描述
    string reason_type = 11;
}

// 商户门市订货单批次
message DemandStoreBatch {
    uint64 id = 1;
    uint64 batch_id = 2;
    // 订货日期
    google.protobuf.Timestamp demand_date = 3;
    // 商户id
    uint64 partner_id = 4;
    // 门店id
    uint64 store_id = 5;
    // 门店编码
    string store_secondary_id = 6;
    // 门店类型
    string store_type = 7;
    // 门店名称
    string store_name = 8;
    // 门市订货单批次状态
    string status = 9;
    // 备注
    string reason_type = 10;
    // 重试次数
    int32 retry_count = 11;
    // 创建时间
    google.protobuf.Timestamp created_at = 12;
    // 更新时间
    google.protobuf.Timestamp updated_at = 13;
}

// 要货单批次
message OrderStoreBatch {
    uint64 id = 1;
    uint64 batch_id = 2;
    // 要货日期
    google.protobuf.Timestamp order_date = 3;
    // 商户id
    uint64 partner_id = 4;
    // 门店id
    uint64 store_id = 5;
    // 门店编码
    string store_secondary_id = 6;
    // 门店类型
    string store_type = 7;
    // 门店名称
    string store_name = 8;
    // 门市订货单批次状态
    string status = 9;
    // 备注
    string reason_type = 10;
    // 重试次数
    int32 retry_count = 11;
    // 创建时间
    google.protobuf.Timestamp created_at = 12;
    // 更新时间
    google.protobuf.Timestamp updated_at = 13;
}

// 创建订单请求参数
message DemandMainRequest {
    // 订货日期
    google.protobuf.Timestamp demand_date = 1;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 2;
    // 备注
    string remark = 3;
    // 门市紧急订货单-HD，主配单-MD
    string type = 4;
    // 主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT)(主配单才有的字段)
    string sub_type = 5;
    // 紧急订货单和主配单的参数(门店id或者商品id)
    uint64 object_id = 6;
    message item_id {
        // 门店id或者商品id
        uint64 object_by = 1;
        // 订货数量
        float quantity = 2;
        // 商品订货物流模式(NMD:配送, PUR:直送)
        string distribution_type = 3;
        // 供应商id
        uint64 vendor_id = 4;
        // 订货方式
        string order_type = 5;
    }
    // 紧急订货单和主配单的参数
    repeated item_id item_ids = 7;
    string bus_type = 8;
    // 附件
    string attachments = 9;
}

// 创建商品主配单请求参数
message ProductMainRequest {
    // 订货日期
    google.protobuf.Timestamp demand_date = 1;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 2;
    // 备注
    string remark = 3;
    // 主配单:MD
    string type = 4;
    // 商品主配:PRODUCT
    string sub_type = 5;
    // 商品主配单的参数
    repeated StoreItem items = 6;

    message StoreItem {
        // 门店id
        uint64 store_id = 1;
        // 订货的详细信息
        repeated ProductItem product_items = 2;
    }
    message ProductItem {
        // 商品id
        uint64 product_id = 1;
        // 订货数量
        double quantity = 2;
        // 商品订货物流模式(NMD:配送, PUR:直送)
        string distribution_type = 3;
        // 配送中心(不修改则可以不传)
        uint64 distribution_by = 4;
        // 供应商id
        uint64 vendor_id = 5;
        // 订货方式
        string order_type = 6;
    }
    // 唯一请求id
    uint64 batch_id = 7;

}

message GetDemandOrderProductDetailResponse {
    repeated OrderProduct rows = 1;
    uint64 total = 2;
}

// 获取要货单详细返回对象
message GetDemandOrderDetailResponse {

    // 要货单id
    uint64 id = 1;
    // 要货单单号
    string code = 2;
    // 要货单关联订单类型(SD, HD, MD)
    string type = 3;
    // 要货单关联订单主配类型(STORE, PRODUCT, MASTER)
    string sub_type = 4;
    // 原因
    string reason_type = 5;
    // 收货门店id
    uint64 receive_by = 6;
    // 门店编码
    string store_secondary_id = 7;
    // 门店名称
    string store_name = 8;
    // 配送中心id/供应商id
    uint64 distribute_by = 9;
    // 配送:NMD/直送:PUR
    string distribution_type = 10;
    // 要货日期
    google.protobuf.Timestamp order_date = 11;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 12;
    // 描述
    string description = 13;
    // 是否收货
    uint32 is_received = 14;
    uint64 partner_id = 15;
    // 订货单编号
    string demand_code = 16;
    // 存储类型
    string storage_type = 17;
    // 物流状态
    string status = 18;
    // 此字段暂时对前端无用
    string process_status = 19;
    uint64 created_by = 20;
    uint64 updated_by = 21;
    google.protobuf.Timestamp created_at = 22;
    google.protobuf.Timestamp updated_at = 23;
    // 创建人姓名
    string created_name = 24;
    // 更新人姓名
    string updated_name = 25;
    // jde单号
    uint64 jde_order_id = 26;
    bool is_adjust = 27;
    string send_type = 28;
    string havi_code = 29;
    string pre_havi_code = 30;
    string attachments = 31;
}

// 查询要货订单参数
message ListDemandOrderRequest {
    // 起始日期
    google.protobuf.Timestamp start_date = 1;
    // 结束日期
    google.protobuf.Timestamp end_date = 2;
    // 起始到货日期
    google.protobuf.Timestamp start_arrival_date = 3;
    // 结束到货日期
    google.protobuf.Timestamp end_arrival_date = 4;
    // 要货单状态
    repeated string status = 5;
    // 门店id
    repeated uint64 store_ids = 6;
    // 订货单号
    string demand_code = 7;
    // 要货单号
    string order_code = 8;
    // 储藏类型
    repeated string storage_type = 9;
    // 订货类型(SD, HD, MD)
    repeated string demand_type = 10;
    // 门店类型(热麦:MIX, 如果要查询非的字段使用neq_开头，例如:["neq_MIX", "TEA"], 不传查询所有)
    repeated string store_type = 11;
    // 物流状态(状态-INITED:创建, CHECKED:检核完成, CHECKING:正在检核, SENT:发送成功, SEND_FAILED:发送失败, CANCELLED:缺货取消)
    repeated string trans_mode = 12;
    // 偏移
    uint64 offset = 14;
    // 每页数量
    uint64 limit = 15;
    // 是否收货('0':未，'1':已收货)
    string is_received = 16;
    // jde单号
    uint64 jde_order_id = 17;
    // 物流模式(配送:NMD, 直送:PUR)
    string distribution_type = 18;
    // 配送中心/供应商
    repeated uint64 distribute_bys = 19;
    bool is_adjust = 20;
    string send_type = 21;
    // 排序(默认asc)
    string order = 22;
    string sort = 23;
    string havi_code = 24;
    string pre_havi_code = 25;
    string lan = 26;
}

// 查询要货订单返回
message ListDemandOrderResponse {
    repeated DemandOrder rows = 1;
    uint64 total = 2;

}

// 导出主配单请求参数
message ExportDemandMainRequest {
    // 门市订货(SD)、紧急订货(HD)、主配订货(MD)
    string type = 1;
    // 主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT, 总部帮订:MASTER)(主配单才有的字段)
    string sub_type = 2;
    // 起始日期2018-12-29
    google.protobuf.Timestamp start_date = 3;
    // 结束日期2018-12-29
    google.protobuf.Timestamp end_date = 4;
    // 单据状态
    string status = 5;
}

// 导出订货商品返回参数
message ExportDemandMainResponse {
    repeated ExportDemandMainDetail rows = 1;
}

// 更新订货商品参数
message UpdateDemandProductRequest {
    message products {
        // 订货记录id(如果有此id则要传此id)
        uint64 id = 1;
        // 商品id
        uint64 product_id = 2;
        // 订货数量
        double quantity = 3;
        // 订货热麦类型("FINISHED","成品订货"),("TEA", "茶饮订货"),("BREAD", "面包订货")("RAW", "原料订货")
        string tag_type = 4;
        // 商品订货物流模式(配送:NMD, 直送:PUR)， 成品订货不用传
        string distribution_type = 5;
        // 配送/供应商中心(不修改就不需要传)
        uint64 distribution_by = 6;
        string unit_id = 7;
        double unit_rate = 8;
        // 供应商id
        uint64 vendor_id = 9;
        // 订货方式
        string order_type = 10;
    }
    // 主配单id
    uint64 demand_id = 1;
    // 更新的商品信息
    repeated products product = 2;
    // 订货日期(不传默认当天)
    google.protobuf.Timestamp order_date = 3;
    // 附件
    string attachments = 4;
}

//查询门店订货单参数
message QueryDemandRequest {
    // 门店ids（,分割）
    repeated uint64 store_ids = 1;
    // 是否包含商品('0'是不含，'1'是含, 不传查询所有, 主意这里是字符0, 1)
    string has_product = 2;
    // 门市订货(SD)、紧急订货(HD)、主配订货(MD)
    string type = 3;
    // 主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT, 总部帮订:MASTER)(主配单才有的字段)
    string sub_type = 4;
    // 门店类型(热麦:MIX, 如果要查询非的字段使用neq_开头，例如:["neq_MIX", "TEA"], 不传查询所有)
    repeated string store_type = 5;
    // 订货日期起始时间
    google.protobuf.Timestamp start_date = 6;
    // 订货日期结束时间
    google.protobuf.Timestamp end_date = 7;
    // 订单状态(INITED,F_COMMIT, CAL_DONE, CAL_FAILED, COMMIT, REJECTED, APPROVED, FREEZE, CANCELLED)
    repeated string status = 8;
    // 偏移(默认0)
    uint64 offset = 10;
    // 查询的每页数量
    uint64 limit = 11;
    // 订货单号
    repeated string codes = 12;
    bool is_plan = 13;
    bool is_adjust = 14;
    string send_type = 15;
    // 排序(默认asc)
    string order = 16;
    string sort = 17;
    string havi_code = 18;
    string pre_havi_code = 19;
    string bus_type = 20;
    // 计划名称模糊查询
    string plan_name = 21;
    string lan = 22;
}

// 查询门店订货单
message QueryDemandResponse {
    repeated DemandEntity rows = 1;
    uint64 total = 2;
}

// 获取订货商品返回参数
message QueryDemandProductResponse {
    repeated QueryDemandProductEntity rows = 1;
    int32 total = 2;
}

// 可订货商品实体
message QueryDemandProductEntity {
    // 到货天数
    int32 arrival_days = 1;
    // 配送/供应商中心
    uint64 distribute_by = 2;
    // 配送(NMD)、采购(PUR)
    string distribution_type = 3;
    // 递增订量
    float increment_quantity = 4;
    // 最大订货量
    float max_quantity = 5;
    // 最小订货量
    float min_quantity = 6;
    // 商品类别id
    uint64 product_category_id = 7;
    // 商品编码
    string product_code = 8;
    // 商品id
    uint64 product_id = 9;
    // 商品名称
    string product_name = 10;
    // 订货数量
    double quantity = 11;
    // 规格
    string spec = 12;
    // 商品储藏类型编码
    string storage_type = 13;
    // 订货单位id
    uint64 unit_id = 14;
    // 订货单位名称
    string unit_name = 15;
    // 订货规格
    string unit_spec = 16;
    // 销售类型
    string sale_type = 17;
    // 商品属性
    string product_type = 18;
    // 建议订货量
    float suggest_quantity = 20;
    // 成品订货量
    float finished_quantity = 21;
    // 热麦订货量
    float bread_quantity = 22;
    // 茶饮订货量
    float tea_quantity = 23;
    // 原料订货量
    float raw_quantity = 24;
    // 直采价格
    float purchase_price = 25;
    // 直采税率
    float purchase_tax = 26;
    // 订货记录id(如果有此id则要传此id)
    uint64 id = 27;
    // 配送周期类型
    string distribution_circle = 28;
    // 配送中心
    uint64 distribution_center_id = 29;
    // 供应商
    uint64 vendor_id = 30;
    //
    string cycle_extends = 31;
    //
    string is_first_delivery = 32;
    // 昨日销售
    double yesterday_sales = 33;
    // 一周平均销量
    double average_week_sales = 34;
    // 昨日订货量
    double yesterday_order_qty = 35;
    // 单位转换率
    double unit_rate = 36;
    // 核算订货单位id
    uint64 accounting_unit_id = 37;
    // 核算订货单位名称
    string accounting_unit_name = 38;
    // 核算订货规格
    string accounting_unit_spec = 39;
    // 核算订货数量
    double accounting_quantity = 40;
    bool has_new_suggest_qty = 41;
    repeated string barcode = 42;
    // 门店待收货数量
    double store_unfinish_qty = 43;
    // 门店实时库存
    double store_inv_qty = 44;
    uint64 partner_id = 45;
    // 实时库存数量
    double real_inventory_qty = 46;
    string price = 47;
    string image = 48;
    string currency = 49;
}

message IdRequest {
    // 对象id
    uint64 id = 1;
    string lan = 2;
}

message DemandEntity {
    // 单据id
    uint64 id = 1;
    // 生成的批次id
    uint64 batch_id = 2;
    // 编码
    string code = 3;
    // 门市订货(SD)、紧急订货(HD)、主配订货(MD)
    string type = 4;
    // 原因类型
    string reason_type = 5;
    // 收获门店id
    uint64 receive_by = 6;
    // 收获门店名称
    string receive_name = 7;
    // 供应商id
    uint64 distribute_by = 8;
    // 门店编码
    string store_secondary_id = 9;
    // 门店类型
    string store_type = 10;
    // 供应商类别
    string distribution_type = 11;
    // 订货日期
    google.protobuf.Timestamp demand_date = 12;
    // 审核人id
    uint64 review_by = 13;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 14;
    // 描述
    string description = 15;
    // 商户id
    uint64 partner_id = 16;
    // 主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT, 总部帮订:MASTER)(主配单才有的字段)
    string sub_type = 17;
    // 备注
    string remark = 18;
    // 是否有商品
    int32 has_product = 19;
    // 是否是系统操作(1是，0不是)
    int32 is_sys = 20;
    // 单据业务状态(INITED,F_COMMIT, CAL_DONE, CAL_FAILED, COMMIT, REJECTED, APPROVED, FREEZE, CANCELLED)
    string status = 21;
    // 生成要货单处理状态(INITED,PROCESSING,SUCCESS,FAILED)
    string process_status = 22;
    // 更新时间
    google.protobuf.Timestamp updated_at = 23;
    // 更新人id
    uint64 updated_by = 24;
    string created_name = 25;
    string updated_name = 26;
    // 创建日期
    google.protobuf.Timestamp created_at = 27;
    // 创建人id
    uint64 created_by = 28;
    // 订货品相
    int32 product_count = 29;
    bool is_plan = 30;
    bool is_adjust = 31;
    string send_type = 32;
    string havi_code = 33;
    string pre_havi_code = 34;
    string bus_type = 35;
    string attachments = 36;
}

// 统一返回对象
message Response {
    string description = 1;
}

// 要货单对象
message DemandOrder {
    // 要货单id
    uint64 id = 1;
    // 要货单号
    string code = 2;
    // 门市订货(SD)、紧急订货(HD)、主配订货(MD)
    string type = 3;
    // 主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT, 总部帮订:MASTER)(主配单才有的字段)
    string sub_type = 4;
    string reason_type = 5;
    // 收货门店
    uint64 receive_by = 6;
    // 门店编码
    string store_secondary_id = 7;
    // 收货门店名称
    string store_name = 8;
    // 门店类型
    string store_type = 9;
    // 配送中心/供应商id
    uint64 distribute_by = 10;
    // 配送/直送
    string distribution_type = 11;
    // 订货日期
    google.protobuf.Timestamp order_date = 12;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 13;
    // 描述
    string description = 14;
    // 是否收货
    int32 is_received = 15;
    // 商户id
    uint64 partner_id = 16;
    // 订货单id
    uint64 demand_id = 17;
    // 订货单号
    string demand_code = 18;
    // 储藏类型编码
    string storage_type = 19;
    // 要货单(配送状态-INITED:创建, CHECKED:检核完成, CHECKING:正在检核, SENT:发送成功, SEND_FAILED:发送失败)
    string status = 20;
    string process_status = 21;
    // 更新人id
    uint64 updated_by = 22;
    // 更新时间
    google.protobuf.Timestamp updated_at = 23;
    // 创建时间
    google.protobuf.Timestamp created_at = 24;
    // 创建人id
    uint64 created_by = 25;
    // 创建人姓名
    string created_name = 26;
    // 更信任姓名
    string updated_name = 27;
    // jde单号
    uint64 jde_order_id = 28;
    // 商品品项数
    int32 product_count = 29;
    bool is_adjust = 30;
    string send_type = 31;
    string havi_code = 32;
    string pre_havi_code = 33;
    // 是否每日订货
    bool is_plan = 34;
    // 订单附件
    string attachments = 35;
}

// 订货单对象
message ExportDemandMainDetail {
    // 订货单号
    string code = 1;
    // 订货日期
    google.protobuf.Timestamp demand_date = 2;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 3;
    // 门店编码
    string store_secondary_id = 4;
    // 门店名称
    string store_name = 5;
    // 商品编码
    string product_code = 6;
    // 商品名称
    string product_name = 7;
    // 规格
    string product_spec = 8;
    // 类别编码
    string category_code = 9;
    // 类别(DQ产品>干货>原材料-包装材料>干货-包材)
    string category_name = 10;
    // 订货数量
    double quantity = 11;
    // 订货单位
    string unit_name = 12;
    // 核算数量
    double accounting_quantity = 13;
    // 核算单位
    string accounting_unit_name = 14;
    bool is_plan = 15;
    bool is_adjust = 16;
    string send_type = 17;
    string havi_code = 18;
}

// 要货单商品对象
message OrderProduct {

    // 要货商品记录id
    uint64 id = 1;
    // 要货单id
    uint64 demand_order_id = 2;
    // 商品id
    uint64 product_id = 3;
    // 商品分类id
    uint64 category_id = 4;
    // 商品编码
    string product_code = 5;
    // 商品名称
    string product_name = 6;
    // 订货记录商品行号
    int32 line = 7;
    // 销售类型
    string sale_type = 8;
    // 商品属性
    string product_type = 9;
    // 单位id
    uint64 unit_id = 11;
    // 单位名称
    string unit_name = 12;
    // 单位规格
    string unit_spec = 13;
    // 核算单位id
    uint64 accounting_unit_id = 14;
    // 核算单位名称
    string accounting_unit_name = 15;
    // 核算单位规格
    string accounting_unit_spec = 16;
    // 订货数量
    float quantity = 17;
    // 核算数量
    float accounting_quantity = 18;
    // 收货数量
    float received_quantity = 19;
    // 收货核算数量
    float received_accounting_quantity = 20;
    // 采购税率
    float purchase_tax = 21;
    // 采购价格
    float purchase_price = 22;
    // 是否收货
    uint32 is_received = 23;
    // 比率
    float unit_rate = 24;
    // 要货日期
    google.protobuf.Timestamp order_date = 25;
    // 配送数量
    float distr_quantity = 26;
}
// 导入主配单参数
message UploadDemandMasterRequest {
    // 文件名称
    string file_name = 1;
    // 导入类型
    string file_type = 2;
    // 文件流
    string file = 3;
    string lan = 4;
}
message UploadDemandMaster {
    string store_code = 1;
    string store_name = 2;
    string product_code = 3;
    string product_name = 4;
    double quantity = 5;
    string start_date = 6;
    string end_date = 7;
    uint64 row_num = 8;
    uint64 store_id = 9;
    uint64 product_id = 10;
    uint64 id = 11;
    string status = 12;
    uint64 partner_id = 13;
    string error = 14;
    uint64 batch_id = 15;
    string store_type = 16;
    uint64 centre_id = 17;
    string centre_name = 18;
    string centre_code = 19;
    string remark = 20;
}
message UploadDemandMasterResponse {
    bool result = 1;
    repeated UploadDemandMaster rows = 2;
    string file_name = 3;
}

message GetDemandMasterUploadByBatchIdRequest {
    uint64 batch_id = 1;
    uint64 offset = 2;
    uint64 limit = 3;
    bool include_total = 4;
}
message DemandMasterUploadByBatchDetail {
    string store_code = 1;
    string store_name = 2;
    string product_code = 3;
    string product_name = 4;
    double quantity = 5;
    google.protobuf.Timestamp start_date = 6;
    google.protobuf.Timestamp end_date = 7;
    uint64 row_num = 8;
    uint64 store_id = 9;
    uint64 product_id = 10;
    uint64 id = 11;
    string status = 12;
    uint64 partner_id = 13;
    string error = 14;
    uint64 batch_id = 15;
    google.protobuf.Timestamp created_at = 16;
    google.protobuf.Timestamp updated_at = 17;
    uint64 created_by = 18;
    uint64 updated_by = 19;
    uint64 master_id = 20;
    string master_code = 21;
    uint64 centre_id = 22;
    string centre_name = 23;
    string centre_code = 24;
    string remark = 25;
}
message GetDemandMasterUploadByBatchIdResponse {
    repeated DemandMasterUploadByBatchDetail rows = 1;
    uint64 total = 2;
    string file_name = 3;
    string status = 4;
    string updated_name = 5;
}
message GetDemandMasterUploadRequest {
    google.protobuf.Timestamp start_date = 1;
    google.protobuf.Timestamp end_date = 2;
    uint64 offset = 3;
    uint64 limit = 4;
    string file_name = 5;
    bool include_total = 6;
    string file_type = 7;
}
message DemandMasterUploadSummary {
    uint64 id = 1;
    uint64 partner_id = 2;
    string status = 3;
    string file_name = 4;
    uint64 created_by = 5;
    uint64 updated_by = 6;
    google.protobuf.Timestamp created_at = 7;
    google.protobuf.Timestamp updated_at = 8;
    string created_name = 9;
    string updated_name = 10;
    google.protobuf.Timestamp upload_date = 11;
    string file_type = 12;
}
message GetDemandMasterUploadResponse {
    repeated DemandMasterUploadSummary rows = 1;
    uint64 total = 2;
}
message ApproveDemandMasterUploadRequest {
    uint64 batch_id = 1;
}
message ApproveDemandMasterUploadResponse {
    bool result = 1;
}
message CancelDemandMasterUploadRequest {
    uint64 batch_id = 1;
}
message CancelDemandMasterUploadResponse {
    bool result = 1;
}
message GetProductSaleForecastRequest {
    uint64 store_id = 1;
    google.protobuf.Timestamp demand_day = 2;
    repeated uint64 product_ids = 3;
    bool is_tea_and_bread = 4;
}
message GetProductSaleForecastResponse {
    map<uint64, double> map_field = 1;
}
// 查询可订货商品请求参数
message GetDemandAdjustProductByStoreIdRequest {
    // 门店id
    uint64 store_id = 1;
    // 订货类型:门市订货(SD)、紧急订货(HD)、主配订货(MD)
    string type = 2;
    // 订货日期
    google.protobuf.Timestamp order_date = 3;
    // 物流模式: 配送-NMD, 直送-PUR
    string distribution_type = 4;
    // 配送中心
    uint64 vendor_id = 5;
    repeated uint64 category_ids = 6;
    string lan = 7;
}
message DemandAdjustProductUnit {
    // 订货单位id
    uint64 unit_id = 1;
    // 订货单位名称
    string unit_name = 2;
    // 订货code
    string unit_code = 3;
    // 比率
    double unit_rate = 4;
}
message DemandAdjustProduct {
    // 到货天数
    int32 arrival_days = 1;
    // 配送/供应商中心
    uint64 distribute_by = 2;
    // 配送(NMD)、采购(PUR)
    string distribution_type = 3;
    // 递增订量
    float increment_quantity = 4;
    // 最大订货量
    float max_quantity = 5;
    // 最小订货量
    float min_quantity = 6;
    // 商品类别id
    uint64 product_category_id = 7;
    // 商品编码
    string product_code = 8;
    // 商品id
    uint64 product_id = 9;
    // 商品名称
    string product_name = 10;
    // 订货数量
    double quantity = 11;
    // 规格
    string spec = 12;
    // 商品储藏类型编码
    string storage_type = 13;
    //多单位调整
    repeated DemandAdjustProductUnit units = 14;
    // 销售类型
    string sale_type = 17;
    // 商品属性
    string product_type = 18;
    // 建议订货量
    float suggest_quantity = 20;
    // 成品订货量
    float finished_quantity = 21;
    // 热麦订货量
    float bread_quantity = 22;
    // 茶饮订货量
    float tea_quantity = 23;
    // 原料订货量
    float raw_quantity = 24;
    // 直采价格
    float purchase_price = 25;
    // 直采税率
    float purchase_tax = 26;
    // 订货记录id(如果有此id则要传此id)
    uint64 id = 27;
    // 配送周期类型
    string distribution_circle = 28;
    // 配送中心
    uint64 distribution_center_id = 29;
    // 供应商
    uint64 vendor_id = 30;
    //
    string cycle_extends = 31;
}
message GetDemandAdjustProductByStoreIdResponse {
    repeated DemandAdjustProduct rows = 1;
    uint64 total = 2;
}
message CreateDemandAdjustRequest {
    // 订货日期
    google.protobuf.Timestamp demand_date = 1;
    // 到货日期
    google.protobuf.Timestamp arrival_date = 2;
    // 备注
    string remark = 3;
    // 门市紧急订货单-HD，主配单-MD
    string type = 4;
    // 主配类型枚举类(门店主配:STORE, 商品主配:PRODUCT)(主配单才有的字段)
    string sub_type = 5;
    // 门店id
    uint64 store_id = 6;
    message Products {
        // 门店id或者商品id
        uint64 product_id = 1;
        // 订货数量
        double quantity = 2;
        // 商品订货物流模式(NMD:配送, PUR:直送)
        string distribution_type = 3;
        uint64 centre_id = 4;
        uint64 unit_id = 5;
    }
    // 商品
    repeated Products products = 7;
}
message CreateDemandAdjustResponse {
    bool result = 1;
}
message UploadOrderCollectionRequest {
    // 文件名称
    string file_name = 1;
    // 导入类型
    string file_type = 2;
    // 文件流
    string file = 3;
}
message UploadOrderCollection {
    string store_code = 1;
    string store_name = 2;
    string product_code = 3;
    string product_name = 4;
    double quantity = 5;
    google.protobuf.Timestamp start_date = 6;
    google.protobuf.Timestamp end_date = 7;
    uint64 row_num = 8;
    uint64 store_id = 9;
    uint64 product_id = 10;
    uint64 id = 11;
    string status = 12;
    uint64 partner_id = 13;
    string error = 14;
    uint64 batch_id = 15;
    string store_type = 16;
    uint64 vendor_id = 17;
    string vendor_name = 18;
    string vendor_code = 19;
    string remark = 20;
    uint64 return_reason_id = 21;
    string return_reason_name = 22;
    string return_reason_code = 23;
    string storage_type = 24;
    uint64 master_id = 25;
    string master_code = 26;
}
message UploadOrderCollectionResponse {
    bool result = 1;
    repeated UploadOrderCollection rows = 2;
    string file_name = 3;
}
message GetOrderCollectionUploadByBatchIdRequest {
    uint64 batch_id = 1;
    uint64 offset = 2;
    uint64 limit = 3;
    bool include_total = 4;
}
message GetOrderCollectionUploadByBatchIdResponse {
    repeated UploadOrderCollection rows = 1;
    uint64 total = 2;
    string file_name = 3;
    string status = 4;
    string updated_name = 5;
}
message GetOrderCollectionUploadRequest {
    google.protobuf.Timestamp start_date = 1;
    google.protobuf.Timestamp end_date = 2;
    uint64 offset = 3;
    uint64 limit = 4;
    string file_name = 5;
    bool include_total = 6;
    string file_type = 7;
}
message OrderCollectionUploadSummary {
    uint64 id = 1;
    uint64 partner_id = 2;
    string status = 3;
    string file_name = 4;
    uint64 created_by = 5;
    uint64 updated_by = 6;
    google.protobuf.Timestamp created_at = 7;
    google.protobuf.Timestamp updated_at = 8;
    string created_name = 9;
    string updated_name = 10;
    google.protobuf.Timestamp upload_date = 11;
    string file_type = 12;
}
message GetOrderCollectionUploadResponse {
    repeated OrderCollectionUploadSummary rows = 1;
    uint64 total = 2;
}
message ApproveOrderCollectionUploadRequest {
    uint64 batch_id = 1;
}
message ApproveOrderCollectionUploadResponse {
    bool result = 1;
}
message CancelOrderCollectionUploadRequest {
    uint64 batch_id = 1;
}
message CancelOrderCollectionUploadResponse {
    bool result = 1;
}

message WmsBohDocSummaryReportRequest {
    string bus_date = 1;
    repeated uint64 store_ids = 2;
    uint64 limit = 3;
    uint64 offset = 4;
}
message WmsBohDocSummaryReportResponse {
    message WmsBohDocSummary {
        string bus_date = 1;
        string boh_type = 2;
        string boh_doc = 3;
        string boh_status = 4;
        uint64 boh_count = 5;
        double boh_out_qty = 6;
        double boh_rec_qty = 7;

        string jde_type = 8;
        string jde_doc = 9;
        string jde_status = 10;
        uint64 jde_count = 11;
        double jde_out_qty = 12;
        double jde_rec_qty = 13;

        string wms_type = 14;
        string wms_doc = 15;
        string wms_status = 16;
        uint64 wms_count = 17;
        double wms_out_qty = 18;
        double wms_rec_qty = 19;

        uint64 store_id = 20;
        string store_code = 21;
        string store_name = 22;
    }
    repeated WmsBohDocSummary rows = 1;
    uint64 total = 2;
}
message WmsBohDocDetailReportRequest {
    string bus_date = 1;
    repeated uint64 store_ids = 2;
    repeated uint64 product_ids = 3;
    repeated string boh_types = 4;
    bool is_boh_wms_diff = 5;
    bool is_boh_jde_diff = 6;
    bool is_wms_jde_diff = 7;
    uint64 limit = 8;
    uint64 offset = 9;
}
message WmsBohDocDetailReportResponse {
    message WmsBohDocDetail {
        string bus_date = 1;
        string boh_type = 2;
        string boh_doc_code = 3;
        string boh_status = 4;
        uint64 boh_count = 5;
        double boh_out_qty = 6;
        double boh_rec_qty = 7;

        string jde_type = 8;
        string jde_doc_code = 9;
        string jde_status = 10;
        uint64 jde_count = 11;
        double jde_out_qty = 12;
        double jde_rec_qty = 13;

        string wms_type = 14;
        string wms_doc_code = 15;
        string wms_status = 16;
        uint64 wms_count = 17;
        double wms_out_qty = 18;
        double wms_rec_qty = 19;

        uint64 store_id = 20;
        string store_code = 21;
        string store_name = 22;

        bool is_boh_wms_diff = 23;
        bool is_boh_jde_diff = 24;
        bool is_wms_jde_diff = 25;
        string product_code = 26;
        string product_name = 27;
        string send_type = 28;
    }
    repeated WmsBohDocDetail rows = 1;
    uint64 total = 2;
}
message CreateSmartDemandRuleRequest {
    string name = 1;
    google.protobuf.Timestamp start = 2;
    google.protobuf.Timestamp end = 3;
    string branch_method = 4;
    string product_method = 5;
    repeated uint64 branch_ids = 6;
    repeated uint64 product_ids = 7;
    string remark = 8;
}
message CreateSmartDemandRuleResponse {
    uint64 id = 1;
    bool result = 2;
}
message PutSmartDemandRuleRequest {
    uint64 id = 1;
    string name = 2;
    google.protobuf.Timestamp start = 3;
    google.protobuf.Timestamp end = 4;
    string branch_method = 5;
    string product_method = 6;
    repeated uint64 branch_ids = 7;
    repeated uint64 product_ids = 8;
    string remark = 9;
    bool only_remark = 10;
}
message PutSmartDemandRuleResponse {
    uint64 id = 1;
    bool result = 2;
    string remark = 3;
}
message ActionSmartDemandRuleRequest {
    uint64 id = 1;
    //动作
    enum Action {
        none = 0;
        confirm = 1;
        cancel = 2;
        callback = 3;
    }
    Action action = 2;
}
message ActionSmartDemandRuleResponse {
    uint64 id = 1;
    bool result = 2;
}
message GetSmartDemandRuleRequest {
    uint64 id = 1;
    bool is_detail = 2;
    string lan = 3;
}
message GetSmartDemandRuleResponse {
    message DemandRule {
        string name = 1;
        google.protobuf.Timestamp start = 2;
        google.protobuf.Timestamp end = 3;
        string branch_method = 4;
        string product_method = 5;
        message DataInfo {
            uint64 id = 1;
            string code = 2;
            string name = 3;
        }
        repeated DataInfo branch_rows = 6;
        repeated DataInfo product_rows = 7;
        string remark = 8;
        uint64 id = 9;
        string status = 10;
        google.protobuf.Timestamp updated_at = 11;
        google.protobuf.Timestamp created_at = 12;
        string updated_name = 13;
        string code = 14;
    }
    repeated DemandRule rows = 1;
    uint64 total = 2;
}


// 退货单报表查询
message QueryReturnOrdersRequest {
    // 退货日期start
    google.protobuf.Timestamp return_date_from = 1;
    // 退货日期end
    google.protobuf.Timestamp return_date_to = 2;
    // 实际提货日期start
    google.protobuf.Timestamp delivery_date_from = 3;
    // 实际提货日期end
    google.protobuf.Timestamp delivery_date_to = 4;
    // 物流模式(0, 配送，1直送， 2全部)
    uint32 is_direct = 5;
    // 退货单号
    string return_code = 6;
    // wms单号
    string havi_code = 7;
    // jde单号
    string jde_code = 8;
    // 收货单号,只有冷链原单退货的有
    string receiving_code = 9;
    //门店id
    repeated uint64 store_ids = 10;
    // 商品id
    repeated uint64 product_ids = 11;
    // 分页
    uint32 limit = 12;
    // 偏离
    uint32 offset = 13;
    // 是否夏辉门店
    bool is_wms_store = 14;
    // 是否汇总， 若不汇总则查明细
    bool is_sum = 15;
    // 商品ids
    repeated uint64 category_ids = 16;
    // lan
    string lan = 17;
}

// 退货单报表明细
message QueryReturnOrdersResponse {
    // 退货单数据
    repeated ReturnOrder rows = 1;
    // 查询出的总条数
    int32 total = 2;
    // 汇总还是明细
    string classification = 3;
    // 退货数量
    float sum_quantity = 4;
    // 退货核算数量
    float sum_accounting_quantity = 5;
}

message ReturnOrder {
    // 退货单号
    string code = 1;
    // 物流模式(0, 配送，1直送)
    bool is_direct = 2;
    // 夏辉单号
    string havi_code = 3;
    // jde_单号
    string jde_order_id = 4;
    // 收货单号
    string receiving_code = 5;
    // 退货日期
    string created_at = 6;
    // 非原单,配送退货预计提货日期
    string return_delivery_date = 7;
    // 原单预计提货日期
    string pre_return_date = 8;
    // 实际提货日期
    string delivery_date = 9;
    // 门店编号
    string store_code = 10;
    // 门店名称
    string store_name = 11;
    // 商品编号
    string product_code = 12;
    // 商品名称
    string product_name = 13;
    // 储藏类型
    string storage_type = 14;
    // 退货数量
    double quantity = 15;
    // 退货核算数量
    double accounting_quantity = 16;
    // 是否确认
    bool is_confirmed = 17;
    // 退货单状态
    string status = 18;
    // 订货单位
    uint64 unit_id = 19;
    // 单位名称
    string unit_name = 20;
    // spec
    string unit_spec = 21;
    // account_unit_id
    // 核算订货单位
    uint64 accounting_unit_id = 22;
    // 单位名称
    string accounting_unit_name = 23;
    // spec
    string accounting_unit_spec = 24;
    // store_id
    uint64 store_id = 25;
    // 商品类别
    uint64  category_id = 26;
    // 配送时间
    string send_date = 27;
    // 仓库或者供应商id
    uint64 distribute_by = 28;
    // 退货原因
    string return_reason = 29;
}

//// 熊猫不走
message DemoGetStoreProductRequest {
    uint64 store_id = 1;
}
message DemoGetStoreProductResponse {
    message GetStoreProduct {
        string product_code = 1;
        string product_name = 2;
        string unit_name = 3;
        repeated uint64 qty = 4;
        uint64 product_id = 5;
    }
    repeated GetStoreProduct rows = 1;
    uint64 total = 2;
}
message DemoSplitStoreProductRequest {
    message SplitStoreProduct {
        uint64 product_id = 1;
        double qty = 2;
    }
    uint64 store_id = 1;
    repeated SplitStoreProduct rows = 2;
}
message DemoSplitStoreProductResponse {
    message SplitStoreProductResponse {
        string product_code = 1;
        string product_name = 2;
        string unit_name = 3;
        double qty = 4;
        double inv_qty = 5;
        double order_qty = 6;
        uint64 product_id = 7;
    }
    repeated SplitStoreProductResponse rows = 1;
    uint64 total = 2;
}
message DemoSaveStoreProductRequest {
    uint64 store_id = 1;
    google.protobuf.Timestamp order_date = 2;
    message SplitStoreProductResponse {
        string product_code = 1;
        string product_name = 2;
        string unit_name = 3;
        double qty = 4;
        double inv_qty = 5;
        double order_qty = 6;
        uint64 product_id = 7;
    }
    repeated SplitStoreProductResponse rows = 3;
}
message DemoSaveStoreProductResponse {
    uint64 id = 1;
}
//// 熊猫不走
