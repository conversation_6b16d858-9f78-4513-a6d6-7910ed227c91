syntax = "proto3";

package reduction_augmentation_demand;
import "google/api/annotations.proto";
//import "google/protobuf/timestamp.proto";

//物料服务
service ReductionAugmentationDemand {
    //  减配货列表页面
    rpc ListReductionAugmentationDemand(ListReductionAugmentationDemandRequest) returns (ListReductionAugmentationDemandResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/reduction_augmentation_demand"
        };
    };
    // 减配货详情
    rpc RetrieveReductionAugmentationDemand (RetrieveReductionAugmentationDemandRequest) returns (RetrieveReductionAugmentationDemandResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/reduction_augmentation_demand/with/id/{id}"
        };
    };
    // 创建减配货
    rpc CreateReductionAugmentationDemand (CreateReductionAugmentationDemandRequest) returns (CreateReductionAugmentationDemandResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/reduction_augmentation_demand"
        };
    };
    // 更新减配货
    rpc UpdateReductionAugmentationDemand (UpdateReductionAugmentationDemandRequest) returns (UpdateReductionAugmentationDemandResponse) {
        option (google.api.http) = {
        patch: "/api/v2/supply/reduction_augmentation_demand/{id}"
        };
    };
    // 按规则id查询商品列表
    rpc ListReductionAugmentationDemandProduct (ListReductionAugmentationDemandProductRequest) returns (ListReductionAugmentationDemandProductResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/reduction_augmentation_demand/product"
        };
    };
    // 按规则id查询门店列表
    rpc ListReductionAugmentationDemandStore (ListReductionAugmentationDemandStoreRequest) returns (ListReductionAugmentationDemandStoreResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/reduction_augmentation_demand/store"
        };
    };
    // 聚合商品数量（分配商品预估与门店订货明细处, 商品id与门店id不能都多传，必须有一个为单一id）
    rpc SumDemandQuantity (SumDemandQuantityRequest) returns (SumDemandQuantityResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/reduction_augmentation_demand/sum"
        };
    };
    // 计算商品分配
    rpc CalculateAllocation (CalculateAllocationRequest) returns (CalculateAllocationResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/reduction_augmentation_demand/calculate"
        };
    };
    // 获取计算商品分配列表
    rpc ListCalculateAllocationResult (ListCalculateAllocationResultRequest) returns (ListCalculateAllocationResultResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/reduction_augmentation_demand/calculate/{rule_id}"
        };
    };
//    // 获取计算商品分配的门店
//    rpc ListCalculateAllocationStore (ListCalculateAllocationStoreRequest) returns (ListCalculateAllocationStoreResponse) {
//        option (google.api.http) = {
//        get: "/api/v2/supply/reduction_augmentation_demand/calculate/store/{rule_id}"
//        };
//    };
    // 删除计算商品分配
    rpc DestroyCalculateAllocation (DestroyCalculateAllocationRequest) returns (DestroyCalculateAllocationResponse) {
        option (google.api.http) = {
        delete: "/api/v2/supply/reduction_augmentation_demand/calculate/{rule_id}"
        };
    };
    //  列表排除门店
    rpc ListReductionAugmentationDemandExcludedStore(ListReductionAugmentationDemandExcludedStoreRequest) returns (ListReductionAugmentationDemandExcludedStoreResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/reduction_augmentation_demand/excluded/store"
        };
    };
    //  新建排除门店
    rpc CreateReductionAugmentationDemandExcludedStore(CreateReductionAugmentationDemandExcludedStoreRequest) returns (CreateReductionAugmentationDemandExcludedStoreResponse) {
        option (google.api.http) = {
        post: "/api/v2/supply/reduction_augmentation_demand/excluded/store"
        };
    };
    //  按id删除排除门店
    rpc DestroyReductionAugmentationDemandExcludedStore(DestroyReductionAugmentationDemandExcludedStoreRequest) returns (DestroyReductionAugmentationDemandExcludedStoreResponse) {
        option (google.api.http) = {
        delete: "/api/v2/supply/reduction_augmentation_demand/excluded/store/{id}"
        };
    };
    //  按
    rpc ReductionAugmentationDemandProductAgg(ReductionAugmentationDemandProductAggRequest) returns (ReductionAugmentationDemandProductAggResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/reduction_augmentation_demand/product/agg"
        };
    };
}

message ListReductionAugmentationDemandRequest {
    // 暂时不支持模糊搜索
    string status = 1; // INITED: 新建, CONFIRMED: 已确认, VOIDED：已作废
    string company = 2; // 公司id
    string allocation_type = 3; // 分配类型 INCREASE: 增加， REDUCE: 减少
    string name = 4; // 名字
    string code = 5; // 编号
    int32 limit = 6;
    int32 offset = 7;
}

message ListReductionAugmentationDemandResponse {
    message Detail {
        uint64 id = 1; // 规则id
        string status = 2; // INITED: 新建, CONFIRMED: 已确认, VOIDED：已作废
        repeated uint64 company = 3; // 公司id
        string allocation_type = 4; // INCREASE: 增加， REDUCE: 减少
        string name = 5; // 名字
        string code = 6; // 编号
        string start_demand_date = 7; // 开始订单日期
        string end_demand_date = 8; // 结束订单日期
        double rate = 9; // 分配比率
        string updated_at = 10; // 更新时间
    }
    repeated Detail rows = 1;
    int32 count = 2;
}

message RetrieveReductionAugmentationDemandResponse {
    uint64 id = 1; // 规则id
    string status = 2; // INITED: 新建, CONFIRMED: 已确认, VOIDED：已作废
    repeated uint64 company = 3; // 公司id
    string allocation_type = 4; // INCREASE: 增加， REDUCE: 减少
    string name = 5; // 名字
    string code = 6; // 编号
    string start_demand_date = 7; // 开始订单日期
    string end_demand_date = 8; // 结束订单日期
    double rate = 9; // 分配比率
    string updated_at = 10; // 更新时间
//    double lower_limit = 11; // 分配下限或下限
    int32 order_by_demand_day = 12; // N天订货金额排序
    string quantity_per_time = 13; //分配单次数量， min_quantity: 最小订货量 increment_quantity：递增定量
    bool allow_reduce_to_zero = 14; // 允许减配为0
    repeated uint64 order_types = 15; // 订货类型id
    int32 store_sum = 16; // 门店数量
    int32 product_sum = 17; // 商品数量
    double allocation_quantity = 18; // 分配数量
    string order_by_type = 19; // 排序类型
}


message RetrieveReductionAugmentationDemandRequest {
    uint64 id = 1; // id
}

message ListReductionAugmentationDemandProductRequest {
    uint64 rule_id = 1; // 规则id
    int32 limit = 2;
    int32 offset = 3;
}

message ListReductionAugmentationDemandProductResponse {
    message AllocationProduct {
        uint64 product_id = 1; // 商品id
        string product_code = 2; // 商品编号
        string product_name = 3;  // 商品名称
        double allocation_quantity = 4; // 分配数量
        double remain_quantity = 5; // 剩余数量
        double rate = 6; // 比率
    }
    repeated AllocationProduct rows = 1;
    int32 count = 2;
}

message ListReductionAugmentationDemandStoreRequest {
    uint64 rule_id = 1; // 规则id
    int32 limit = 2;
    int32 offset = 3;
}

message ListReductionAugmentationDemandStoreResponse {
    message AllocationStore {
        uint64 store_id = 1; // 门店id
        string store_code = 2; // 门店编号
        string store_name = 3;  // 门店名称
    }
    repeated AllocationStore rows = 1;
    int32 count = 2;
}

message SumDemandQuantityRequest {
    repeated string product_ids = 1; // 商品id
    repeated string store_ids = 2; // 门店id
    string start_demand_date = 3; // 开始订单日期
    string end_demand_date = 4; // 结束订单日期
    repeated uint64 order_types= 5; // 订货类型id
    int32 limit = 6;
    int32 offset = 7;
}

message SumDemandQuantityResponse {
    message SumDemandProduct {
        uint64 product_id = 1; // 商品id
        string product_name = 2; // 商品名称
        double quantity = 3; // 订单数量
    }
    repeated SumDemandProduct rows = 1;
    int32 count = 2;
}

message CalculateAllocationRequest {
    message Product {
        uint64 product_id = 1; // 商品id
        double allocation_quantity = 2; // 分配数量
        double rate = 3; // 比率
    }
    repeated Product products = 1;
    string allocation_type = 2; // INCREASE: 增加， REDUCE: 减少
//    string name = 3; // 名字
//    string code = 4; // 编号
    string start_demand_date = 5; // 开始订单日期
    string end_demand_date = 6; // 结束订单日期
    double rate = 7; // 分配比率
//    string lower_limit = 8; // 分配下限
    int32 order_by_demand_day = 9; // N天订货金额排序
    string quantity_per_time = 10; //分配单次数量， min_quantity: 最小订货量 increment_quantity：递增定量
    bool allow_reduce_to_zero = 11; // 允许减配为0
    repeated uint64 order_types= 12; // 订货类型id
    repeated uint64 store_ids = 13; // 门店id
    uint64 rule_id = 14; // 计算结果的id
    string order_by_type = 15; // 排序类型
}

message CalculateAllocationResponse {
    message RemainProduct {
        uint64 product_id = 1; // 商品id
        string product_code = 2; // 商品编号
        string product_name = 3; // 商品名称
        double remain_quantity = 4; // 分配数量
    }
    message AllocationResult {
        uint64 product_id = 1; // 商品id
        string product_code = 2; // 商品编号
        string product_name = 3; // 商品名称
        int32 store_count = 4; // 门店数量
        double allocation_quantity = 5; // 分配数量
    }
    repeated RemainProduct remain_products = 1;
    repeated AllocationResult allocation_result = 2;
    uint64 rule_id = 3; // 规则id
}

message ListCalculateAllocationResultRequest {
    uint64 rule_id = 1; // 规则id
    int32 limit = 2;
    int32 offset = 3;
}

message ListCalculateAllocationResultResponse {
    message AllocationDetail {
        string product_code = 1; // 商品编号
        string product_name = 2; // 商品名称
        string store_code = 3; // 门店编号
        string store_name = 4; // 门店编号
        double demand_quantity = 5; // 订货数量
        double allocation_quantity = 6; // 分配数量
        double final_quantity = 7; // 最终订货数量
        string order_type = 8; // 订货类型
        string order_source = 9; // 订单来源
        string order_code = 10; // 订单号
        uint64 product_id = 11;
        uint64 store_id = 12;
    }
    repeated AllocationDetail rows = 1;
    int32 count = 2; // 总条数
}


message CreateReductionAugmentationDemandRequest {
    repeated string company = 1; // 公司id
    string allocation_type = 2; // INCREASE: 增加， REDUCE: 减少
    string name = 3; // 名字
    string code = 4; // 编号
    string start_demand_date = 5; // 开始订单日期
    string end_demand_date = 6; // 结束订单日期
    double rate = 7; // 分配比率
//    string lower_limit = 8; // 分配下限
    int32 order_by_demand_day = 9; // N天订货金额排序
    string quantity_per_time = 10; //分配单次数量， min_quantity: 最小订货量 increment_quantity：递增定量
    bool allow_reduce_to_zero = 11; // 允许减配为0
    repeated uint64 order_types = 12; // 订货类型id
    repeated uint64 store_ids = 13; // 门店id
    message Product {
        uint64 product_id = 1; // 商品id
        double allocation_quantity = 2; // 分配数量
        double rate = 3; // 比率
        double remain_quantity = 4; // 剩余数量
    }
    repeated Product products = 14;
    uint64 calculated_rule_id = 15; // 计算结果的id
    string order_by_type = 16; // 排序类型
}

message CreateReductionAugmentationDemandResponse {
    uint64 id = 1; // 规则id
}

message UpdateReductionAugmentationDemandRequest {
    repeated string company = 1; // 公司id
    string allocation_type = 2; // INCREASE: 增加， REDUCE: 减少
    string name = 3; // 名字
//    string code = 4; // 编号
    string start_demand_date = 5; // 开始订单日期
    string end_demand_date = 6; // 结束订单日期
    double rate = 7; // 分配比率
//    string lower_limit = 8; // 分配下限
    int32 order_by_demand_day = 9; // N天订货金额排序
    string quantity_per_time = 10; //分配单次数量， min_quantity: 最小订货量 increment_quantity：递增定量
    bool allow_reduce_to_zero = 11; // 允许减配为0
    repeated uint64 order_types = 12; // 订货类型id
    repeated uint64 store_ids = 13; // 门店id
    message Product {
        uint64 product_id = 1; // 商品id
        double allocation_quantity = 2; // 分配数量
        double rate = 3; // 比率
        double remain_quantity = 4; // 剩余数量
    }
    repeated Product products = 14;
    uint64 calculated_rule_id = 15; // 计算结果的id
    uint64 id = 16; // 规则的id
    string status = 17; // 状态修改
    string order_by_type = 18; // 排序类型
}

message UpdateReductionAugmentationDemandResponse {
    bool result = 1;
}

message DestroyCalculateAllocationRequest {
    uint64 rule_id = 1; // 规则id
}

message DestroyCalculateAllocationResponse {
    bool result = 1;
}

message ListCalculateAllocationStoreRequest {
    uint64 rule_id = 1; // 规则id
    int32 limit = 2;
    int32 offset = 3;
    repeated uint64 store_ids = 4; // 门店id
}

message ListCalculateAllocationStoreResponse {
    message Store {
        uint64 store_id = 1; // 门店id
        string store_code = 2; // 门店编号
        string store_name = 3; // 门店名称
    }
    repeated Store rows = 1;
}

message ListReductionAugmentationDemandExcludedStoreRequest {
    // 分配类型 INCREASE: 增加， REDUCE: 减少
    string allocation_type = 1;
    // 名字
    repeated uint64 store_id = 2;
    int32 limit = 3;
    int32 offset = 4;
}

message ListReductionAugmentationDemandExcludedStoreResponse {
    message Store {
        // id
        uint64 id = 1;
        // 门店id
        uint64 store_id = 2;
        // 门店编号
        string store_code = 3;
        // 门店名称
        string store_name = 4;
        // 分配类型 INCREASE: 增加， REDUCE: 减少
        string allocation_type = 5;
    }
    repeated Store stores = 1;
    // 总条数
    int32 count = 2;
}

message CreateReductionAugmentationDemandExcludedStoreRequest {
    message Store {
        // 门店id
        uint64 store_id = 1;
        // 门店编号
        string store_code = 2;
    }

    repeated Store stores = 1;
    // 分配类型 INCREASE: 增加， REDUCE: 减少
    string allocation_type = 2;
}

message CreateReductionAugmentationDemandExcludedStoreResponse {
    // id
    bool result = 1;
}

message DestroyReductionAugmentationDemandExcludedStoreRequest {
    uint64 id = 1;
}

message DestroyReductionAugmentationDemandExcludedStoreResponse {
    bool result = 1;
}

message ReductionAugmentationDemandProductAggRequest{
    uint64 rule_id = 1; // 规则id
    int32 limit = 2;
    int32 offset = 3;
}

message ReductionAugmentationDemandProductAggResponse{
    message AllocationResult {
        uint64 product_id = 1; // 商品id
        string product_code = 2; // 商品编号
        string product_name = 3; // 商品名称
        int32 store_count = 4; // 门店数量
        double allocation_quantity = 5; // 分配数量
    }
    repeated AllocationResult rows = 1;
    int32 count = 2;
}