syntax = "proto3";

package material;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

//物料服务
service material {
    // 物料分差报表
    rpc GetMaterialDifference (GetMaterialDifferenceRequest) returns (GetMaterialDifferenceResponse) {
        option (google.api.http) = {
        get: "/api/v2/supply/material_difference"
        };
    }
}
message GetMaterialDifferenceRequest {
    // 营业日
    google.protobuf.Timestamp sales_date = 1;
    bool close_store = 2;
    bool close_product = 3;
    bool close_start = 4;
    bool close_end = 5;
    int64 offset = 6;
    int64 limit = 7;
    uint64 store_id = 8;
    repeated uint64 product_ids =9;
}
message MaterialDifference{
    string store_code = 1;
    string store_name = 2;
    bool close_store = 3;
    string branch_region =4;
    string geo_region =5;
    string product_code = 6;
    string product_name = 7;
    bool close_product = 8;
    string product_category = 9;
    string product_type = 10;
    string bom_type = 11;
    string sale_type = 12;
    string accounting_unit_name =13;
    double product_price = 14;
    double start_quantity = 15;
    double start_price = 16;
    double end_quantity = 17;
    double end_price = 18;
    double receiving_quantity = 19;
    double receiving_price = 20;
    double transfer_quantity = 21;
    double transfer_price = 22;
    double sales_quantity = 23;
    double sales_price = 24;
    double diff_quantity = 25;
    double diff_price = 26;
    double diff_percentage = 27;
}
message GetMaterialDifferenceResponse {
    repeated MaterialDifference rows = 1;
    uint64 total = 2;
}
