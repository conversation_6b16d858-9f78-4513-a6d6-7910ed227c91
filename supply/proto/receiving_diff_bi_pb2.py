# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: receiving_diff_bi.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='receiving_diff_bi.proto',
  package='receiving',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x17receiving_diff_bi.proto\x12\treceiving\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xd9\x02\n\x1eGetReceivingDiffCollectRequest\x12\x0e\n\x06st_ids\x18\x01 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x02 \x03(\x04\x12\x11\n\tis_direct\x18\x03 \x01(\x08\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12.\n\nstart_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x07 \x01(\x05\x12\x0e\n\x06offset\x18\x08 \x01(\x05\x12\x15\n\rinclude_total\x18\t \x01(\x08\x12\x14\n\x0cis_wms_store\x18\n \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x0b \x01(\t\x12\x14\n\x0c\x64\x65livery_bys\x18\x0c \x03(\x04\x12\x13\n\x0bproduct_ids\x18\r \x03(\x04\"u\n\x1fGetReceivingDiffCollectResponse\x12-\n\x04rows\x18\x01 \x03(\x0b\x32\x1f.receiving.ReceivingDiffCollect\x12#\n\x05total\x18\x02 \x01(\x0b\x32\x14.receiving.DiffTotal\"\xd0\x02\n\x1dGetReceivingDiffDetailRequest\x12\x0e\n\x06st_ids\x18\x01 \x03(\x04\x12\x14\n\x0c\x63\x61tegory_ids\x18\x02 \x03(\x04\x12\x11\n\tis_direct\x18\x03 \x01(\x08\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12.\n\nstart_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x07 \x01(\x05\x12\x0e\n\x06offset\x18\x08 \x01(\x05\x12\x15\n\rinclude_total\x18\t \x01(\x08\x12\x0c\n\x04\x63ode\x18\n \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x0b \x01(\t\x12\x14\n\x0c\x64\x65livery_bys\x18\x0c \x03(\x04\x12\x13\n\x0bproduct_ids\x18\r \x03(\x04\"u\n\x1eGetReceivingDiffDetailResponse\x12.\n\x04rows\x18\x01 \x03(\x0b\x32 .receiving.ReceivingDiffDetailed\x12#\n\x05total\x18\x02 \x01(\x0b\x32\x14.receiving.DiffTotal\"\x8b\x01\n\tDiffTotal\x12\r\n\x05\x63ount\x18\x01 \x01(\x01\x12\x14\n\x0csum_quantity\x18\x02 \x01(\x01\x12\x1f\n\x17sum_accounting_quantity\x18\x03 \x01(\x01\x12\x1b\n\x13sum_d_diff_quantity\x18\x04 \x01(\x01\x12\x1b\n\x13sum_s_diff_quantity\x18\x05 \x01(\x01\"\xf1\x04\n\x14ReceivingDiffCollect\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x01 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x02 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x03 \x01(\x04\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x15\n\rcategory_name\x18\x05 \x01(\t\x12\x17\n\x0f\x63\x61tegory_parent\x18\x06 \x01(\t\x12\x12\n\nproduct_id\x18\x07 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x08 \x01(\t\x12\x14\n\x0cproduct_name\x18\t \x01(\t\x12\x14\n\x0cproduct_spec\x18\n \x01(\t\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x0b \x01(\x01\x12\x10\n\x08quantity\x18\x0c \x01(\x01\x12\x10\n\x08store_id\x18\r \x01(\x04\x12\x12\n\nstore_code\x18\x0e \x01(\t\x12\x12\n\nstore_name\x18\x0f \x01(\t\x12\x0f\n\x07unit_id\x18\x10 \x01(\x04\x12\x11\n\tunit_name\x18\x11 \x01(\t\x12\n\n\x02id\x18\x12 \x01(\x04\x12\x13\n\x0bposition_id\x18\x19 \x01(\x04\x12\x15\n\rposition_code\x18\x1a \x01(\t\x12\x15\n\rposition_name\x18\x1b \x01(\t\x12\x13\n\x0b\x64\x65livery_by\x18\x1c \x01(\x04\x12\x18\n\x10\x64\x65livery_by_name\x18\x1d \x01(\t\x12\x18\n\x10\x64\x65livery_by_code\x18\x1e \x01(\t\x12\x16\n\x0elogistics_type\x18\x1f \x01(\t\x12\x17\n\x0fs_diff_quantity\x18  \x01(\x01\x12\x17\n\x0f\x64_diff_quantity\x18! \x01(\x01\"\xb7\x06\n\x15ReceivingDiffDetailed\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x01 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x02 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x03 \x01(\x04\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x15\n\rcategory_name\x18\x05 \x01(\t\x12\x17\n\x0f\x63\x61tegory_parent\x18\x06 \x01(\t\x12\x0f\n\x07\x64iff_id\x18\x07 \x01(\x04\x12\x11\n\tdiff_date\x18\x08 \x01(\t\x12\x11\n\tdiff_code\x18\t \x01(\t\x12\x12\n\nproduct_id\x18\n \x01(\x04\x12\x14\n\x0cproduct_code\x18\x0b \x01(\t\x12\x14\n\x0cproduct_name\x18\x0c \x01(\t\x12\x14\n\x0cproduct_spec\x18\r \x01(\t\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x0e \x01(\x01\x12\x10\n\x08quantity\x18\x0f \x01(\x01\x12\x10\n\x08store_id\x18\x10 \x01(\x04\x12\x12\n\nstore_code\x18\x11 \x01(\t\x12\x12\n\nstore_name\x18\x12 \x01(\t\x12\x0f\n\x07unit_id\x18\x13 \x01(\x04\x12\x11\n\tunit_name\x18\x14 \x01(\t\x12\n\n\x02id\x18\x15 \x01(\x04\x12\x13\n\x0breason_type\x18\x16 \x01(\t\x12\x13\n\x0bposition_id\x18\x19 \x01(\x04\x12\x15\n\rposition_code\x18\x1a \x01(\t\x12\x15\n\rposition_name\x18\x1b \x01(\t\x12\x13\n\x0b\x64\x65livery_by\x18\x1c \x01(\x04\x12\x18\n\x10\x64\x65livery_by_name\x18\x1d \x01(\t\x12\x18\n\x10\x64\x65livery_by_code\x18\x1e \x01(\t\x12\x16\n\x0elogistics_type\x18\x1f \x01(\t\x12/\n\x0b\x64\x65mand_date\x18  \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18! \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x16\n\x0ereceiving_code\x18\" \x01(\t\x12\x17\n\x0fs_diff_quantity\x18# \x01(\x01\x12\x17\n\x0f\x64_diff_quantity\x18$ \x01(\x01\x32\xe0\x02\n\x16ReceivingDiffBiService\x12\xa2\x01\n\x17GetReceivingDiffCollect\x12).receiving.GetReceivingDiffCollectRequest\x1a*.receiving.GetReceivingDiffCollectResponse\"0\x82\xd3\xe4\x93\x02*\x12(/api/v2/supply/receiving/diff/bi/collect\x12\xa0\x01\n\x16GetReceivingDiffDetail\x12(.receiving.GetReceivingDiffDetailRequest\x1a).receiving.GetReceivingDiffDetailResponse\"1\x82\xd3\xe4\x93\x02+\x12)/api/v2/supply/receiving/diff/bi/detailedb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_GETRECEIVINGDIFFCOLLECTREQUEST = _descriptor.Descriptor(
  name='GetReceivingDiffCollectRequest',
  full_name='receiving.GetReceivingDiffCollectRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='st_ids', full_name='receiving.GetReceivingDiffCollectRequest.st_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='receiving.GetReceivingDiffCollectRequest.category_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='receiving.GetReceivingDiffCollectRequest.is_direct', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='receiving.GetReceivingDiffCollectRequest.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='receiving.GetReceivingDiffCollectRequest.start_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='receiving.GetReceivingDiffCollectRequest.end_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='receiving.GetReceivingDiffCollectRequest.limit', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='receiving.GetReceivingDiffCollectRequest.offset', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='receiving.GetReceivingDiffCollectRequest.include_total', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='receiving.GetReceivingDiffCollectRequest.is_wms_store', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='receiving.GetReceivingDiffCollectRequest.branch_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_bys', full_name='receiving.GetReceivingDiffCollectRequest.delivery_bys', index=11,
      number=12, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='receiving.GetReceivingDiffCollectRequest.product_ids', index=12,
      number=13, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=102,
  serialized_end=447,
)


_GETRECEIVINGDIFFCOLLECTRESPONSE = _descriptor.Descriptor(
  name='GetReceivingDiffCollectResponse',
  full_name='receiving.GetReceivingDiffCollectResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='receiving.GetReceivingDiffCollectResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='receiving.GetReceivingDiffCollectResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=449,
  serialized_end=566,
)


_GETRECEIVINGDIFFDETAILREQUEST = _descriptor.Descriptor(
  name='GetReceivingDiffDetailRequest',
  full_name='receiving.GetReceivingDiffDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='st_ids', full_name='receiving.GetReceivingDiffDetailRequest.st_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='receiving.GetReceivingDiffDetailRequest.category_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_direct', full_name='receiving.GetReceivingDiffDetailRequest.is_direct', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='receiving.GetReceivingDiffDetailRequest.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='receiving.GetReceivingDiffDetailRequest.start_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='receiving.GetReceivingDiffDetailRequest.end_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='receiving.GetReceivingDiffDetailRequest.limit', index=6,
      number=7, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='receiving.GetReceivingDiffDetailRequest.offset', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='receiving.GetReceivingDiffDetailRequest.include_total', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='receiving.GetReceivingDiffDetailRequest.code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='receiving.GetReceivingDiffDetailRequest.branch_type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_bys', full_name='receiving.GetReceivingDiffDetailRequest.delivery_bys', index=11,
      number=12, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='receiving.GetReceivingDiffDetailRequest.product_ids', index=12,
      number=13, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=569,
  serialized_end=905,
)


_GETRECEIVINGDIFFDETAILRESPONSE = _descriptor.Descriptor(
  name='GetReceivingDiffDetailResponse',
  full_name='receiving.GetReceivingDiffDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='receiving.GetReceivingDiffDetailResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='receiving.GetReceivingDiffDetailResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=907,
  serialized_end=1024,
)


_DIFFTOTAL = _descriptor.Descriptor(
  name='DiffTotal',
  full_name='receiving.DiffTotal',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='count', full_name='receiving.DiffTotal.count', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_quantity', full_name='receiving.DiffTotal.sum_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_accounting_quantity', full_name='receiving.DiffTotal.sum_accounting_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_d_diff_quantity', full_name='receiving.DiffTotal.sum_d_diff_quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_s_diff_quantity', full_name='receiving.DiffTotal.sum_s_diff_quantity', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1027,
  serialized_end=1166,
)


_RECEIVINGDIFFCOLLECT = _descriptor.Descriptor(
  name='ReceivingDiffCollect',
  full_name='receiving.ReceivingDiffCollect',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='receiving.ReceivingDiffCollect.accounting_unit_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='receiving.ReceivingDiffCollect.accounting_unit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='receiving.ReceivingDiffCollect.category_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='receiving.ReceivingDiffCollect.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='receiving.ReceivingDiffCollect.category_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_parent', full_name='receiving.ReceivingDiffCollect.category_parent', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='receiving.ReceivingDiffCollect.product_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='receiving.ReceivingDiffCollect.product_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='receiving.ReceivingDiffCollect.product_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='receiving.ReceivingDiffCollect.product_spec', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='receiving.ReceivingDiffCollect.accounting_quantity', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='receiving.ReceivingDiffCollect.quantity', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='receiving.ReceivingDiffCollect.store_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='receiving.ReceivingDiffCollect.store_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='receiving.ReceivingDiffCollect.store_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='receiving.ReceivingDiffCollect.unit_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='receiving.ReceivingDiffCollect.unit_name', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='receiving.ReceivingDiffCollect.id', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='receiving.ReceivingDiffCollect.position_id', index=18,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='receiving.ReceivingDiffCollect.position_code', index=19,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='receiving.ReceivingDiffCollect.position_name', index=20,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='receiving.ReceivingDiffCollect.delivery_by', index=21,
      number=28, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_name', full_name='receiving.ReceivingDiffCollect.delivery_by_name', index=22,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_code', full_name='receiving.ReceivingDiffCollect.delivery_by_code', index=23,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='receiving.ReceivingDiffCollect.logistics_type', index=24,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_quantity', full_name='receiving.ReceivingDiffCollect.s_diff_quantity', index=25,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_quantity', full_name='receiving.ReceivingDiffCollect.d_diff_quantity', index=26,
      number=33, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1169,
  serialized_end=1794,
)


_RECEIVINGDIFFDETAILED = _descriptor.Descriptor(
  name='ReceivingDiffDetailed',
  full_name='receiving.ReceivingDiffDetailed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='receiving.ReceivingDiffDetailed.accounting_unit_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='receiving.ReceivingDiffDetailed.accounting_unit_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='receiving.ReceivingDiffDetailed.category_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='receiving.ReceivingDiffDetailed.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='receiving.ReceivingDiffDetailed.category_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_parent', full_name='receiving.ReceivingDiffDetailed.category_parent', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_id', full_name='receiving.ReceivingDiffDetailed.diff_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_date', full_name='receiving.ReceivingDiffDetailed.diff_date', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='diff_code', full_name='receiving.ReceivingDiffDetailed.diff_code', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='receiving.ReceivingDiffDetailed.product_id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='receiving.ReceivingDiffDetailed.product_code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='receiving.ReceivingDiffDetailed.product_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_spec', full_name='receiving.ReceivingDiffDetailed.product_spec', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='receiving.ReceivingDiffDetailed.accounting_quantity', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='receiving.ReceivingDiffDetailed.quantity', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='receiving.ReceivingDiffDetailed.store_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='receiving.ReceivingDiffDetailed.store_code', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='receiving.ReceivingDiffDetailed.store_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='receiving.ReceivingDiffDetailed.unit_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='receiving.ReceivingDiffDetailed.unit_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='receiving.ReceivingDiffDetailed.id', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='receiving.ReceivingDiffDetailed.reason_type', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='receiving.ReceivingDiffDetailed.position_id', index=22,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='receiving.ReceivingDiffDetailed.position_code', index=23,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='receiving.ReceivingDiffDetailed.position_name', index=24,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by', full_name='receiving.ReceivingDiffDetailed.delivery_by', index=25,
      number=28, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_name', full_name='receiving.ReceivingDiffDetailed.delivery_by_name', index=26,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='delivery_by_code', full_name='receiving.ReceivingDiffDetailed.delivery_by_code', index=27,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='logistics_type', full_name='receiving.ReceivingDiffDetailed.logistics_type', index=28,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='demand_date', full_name='receiving.ReceivingDiffDetailed.demand_date', index=29,
      number=32, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='receiving.ReceivingDiffDetailed.created_at', index=30,
      number=33, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receiving_code', full_name='receiving.ReceivingDiffDetailed.receiving_code', index=31,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='s_diff_quantity', full_name='receiving.ReceivingDiffDetailed.s_diff_quantity', index=32,
      number=35, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='d_diff_quantity', full_name='receiving.ReceivingDiffDetailed.d_diff_quantity', index=33,
      number=36, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1797,
  serialized_end=2620,
)

_GETRECEIVINGDIFFCOLLECTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVINGDIFFCOLLECTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVINGDIFFCOLLECTRESPONSE.fields_by_name['rows'].message_type = _RECEIVINGDIFFCOLLECT
_GETRECEIVINGDIFFCOLLECTRESPONSE.fields_by_name['total'].message_type = _DIFFTOTAL
_GETRECEIVINGDIFFDETAILREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVINGDIFFDETAILREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETRECEIVINGDIFFDETAILRESPONSE.fields_by_name['rows'].message_type = _RECEIVINGDIFFDETAILED
_GETRECEIVINGDIFFDETAILRESPONSE.fields_by_name['total'].message_type = _DIFFTOTAL
_RECEIVINGDIFFDETAILED.fields_by_name['demand_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_RECEIVINGDIFFDETAILED.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['GetReceivingDiffCollectRequest'] = _GETRECEIVINGDIFFCOLLECTREQUEST
DESCRIPTOR.message_types_by_name['GetReceivingDiffCollectResponse'] = _GETRECEIVINGDIFFCOLLECTRESPONSE
DESCRIPTOR.message_types_by_name['GetReceivingDiffDetailRequest'] = _GETRECEIVINGDIFFDETAILREQUEST
DESCRIPTOR.message_types_by_name['GetReceivingDiffDetailResponse'] = _GETRECEIVINGDIFFDETAILRESPONSE
DESCRIPTOR.message_types_by_name['DiffTotal'] = _DIFFTOTAL
DESCRIPTOR.message_types_by_name['ReceivingDiffCollect'] = _RECEIVINGDIFFCOLLECT
DESCRIPTOR.message_types_by_name['ReceivingDiffDetailed'] = _RECEIVINGDIFFDETAILED
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetReceivingDiffCollectRequest = _reflection.GeneratedProtocolMessageType('GetReceivingDiffCollectRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGDIFFCOLLECTREQUEST,
  __module__ = 'receiving_diff_bi_pb2'
  # @@protoc_insertion_point(class_scope:receiving.GetReceivingDiffCollectRequest)
  ))
_sym_db.RegisterMessage(GetReceivingDiffCollectRequest)

GetReceivingDiffCollectResponse = _reflection.GeneratedProtocolMessageType('GetReceivingDiffCollectResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGDIFFCOLLECTRESPONSE,
  __module__ = 'receiving_diff_bi_pb2'
  # @@protoc_insertion_point(class_scope:receiving.GetReceivingDiffCollectResponse)
  ))
_sym_db.RegisterMessage(GetReceivingDiffCollectResponse)

GetReceivingDiffDetailRequest = _reflection.GeneratedProtocolMessageType('GetReceivingDiffDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGDIFFDETAILREQUEST,
  __module__ = 'receiving_diff_bi_pb2'
  # @@protoc_insertion_point(class_scope:receiving.GetReceivingDiffDetailRequest)
  ))
_sym_db.RegisterMessage(GetReceivingDiffDetailRequest)

GetReceivingDiffDetailResponse = _reflection.GeneratedProtocolMessageType('GetReceivingDiffDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRECEIVINGDIFFDETAILRESPONSE,
  __module__ = 'receiving_diff_bi_pb2'
  # @@protoc_insertion_point(class_scope:receiving.GetReceivingDiffDetailResponse)
  ))
_sym_db.RegisterMessage(GetReceivingDiffDetailResponse)

DiffTotal = _reflection.GeneratedProtocolMessageType('DiffTotal', (_message.Message,), dict(
  DESCRIPTOR = _DIFFTOTAL,
  __module__ = 'receiving_diff_bi_pb2'
  # @@protoc_insertion_point(class_scope:receiving.DiffTotal)
  ))
_sym_db.RegisterMessage(DiffTotal)

ReceivingDiffCollect = _reflection.GeneratedProtocolMessageType('ReceivingDiffCollect', (_message.Message,), dict(
  DESCRIPTOR = _RECEIVINGDIFFCOLLECT,
  __module__ = 'receiving_diff_bi_pb2'
  # @@protoc_insertion_point(class_scope:receiving.ReceivingDiffCollect)
  ))
_sym_db.RegisterMessage(ReceivingDiffCollect)

ReceivingDiffDetailed = _reflection.GeneratedProtocolMessageType('ReceivingDiffDetailed', (_message.Message,), dict(
  DESCRIPTOR = _RECEIVINGDIFFDETAILED,
  __module__ = 'receiving_diff_bi_pb2'
  # @@protoc_insertion_point(class_scope:receiving.ReceivingDiffDetailed)
  ))
_sym_db.RegisterMessage(ReceivingDiffDetailed)



_RECEIVINGDIFFBISERVICE = _descriptor.ServiceDescriptor(
  name='ReceivingDiffBiService',
  full_name='receiving.ReceivingDiffBiService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=2623,
  serialized_end=2975,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetReceivingDiffCollect',
    full_name='receiving.ReceivingDiffBiService.GetReceivingDiffCollect',
    index=0,
    containing_service=None,
    input_type=_GETRECEIVINGDIFFCOLLECTREQUEST,
    output_type=_GETRECEIVINGDIFFCOLLECTRESPONSE,
    serialized_options=_b('\202\323\344\223\002*\022(/api/v2/supply/receiving/diff/bi/collect'),
  ),
  _descriptor.MethodDescriptor(
    name='GetReceivingDiffDetail',
    full_name='receiving.ReceivingDiffBiService.GetReceivingDiffDetail',
    index=1,
    containing_service=None,
    input_type=_GETRECEIVINGDIFFDETAILREQUEST,
    output_type=_GETRECEIVINGDIFFDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\002+\022)/api/v2/supply/receiving/diff/bi/detailed'),
  ),
])
_sym_db.RegisterServiceDescriptor(_RECEIVINGDIFFBISERVICE)

DESCRIPTOR.services_by_name['ReceivingDiffBiService'] = _RECEIVINGDIFFBISERVICE

# @@protoc_insertion_point(module_scope)
