syntax = "proto3";
package receiving;
import "google/api/annotations.proto";
// import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

// 固定资产收货相关服务
service AssetsRecService{

    rpc CreateAssetsReceiving(CreateAssetsReceivingRequest) returns (CreateAssetsReceivingResponse){
        option (google.api.http) = {
            post:"/api/v2/supply/asset/receiving"
            body:"*"
        };
    }

    rpc ConfirmAssetsReceiving(ConfirmAssetsReceivingRequest) returns (ConfirmAssetsReceivingResponse){
        option (google.api.http) = {
            put:"/api/v2/supply/asset/receiving/{id}/confirm"
            body:"*"
        };
    }

    rpc ListAssetsReceiving(ListAssetsReceivingRequest) returns (ListAssetsReceivingResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/asset/receiving"
        };
    }

    rpc GetAssetById(GetAssetByIdRequest) returns (Assets){
        option (google.api.http) = {
            get:"/api/v2/supply/asset/receiving/{id}"
        };
    }

    rpc ListAssetProductsById(ListAssetProductsByIdRequest) returns (ListAssetProductsByIdResponse){
        option (google.api.http) = {
            get:"/api/v2/supply/asset/receiving/{id}/product"
        };
    }
}



message CreateAssetsReceivingRequest{
    // jde公司
    string kcoo = 1;
    // jde单据编号
    uint64 doco = 2;
    // jde单据类型
    string dcto = 3;
    // jde行号
    uint64 lnid = 4;
    // 仓库编号
    string mcu = 5;
    // 门店编号
    uint64 an8 = 6;
    //
    repeated AssetsProduct products = 7;
    // 订货日期
    google.protobuf.Timestamp trdj = 8;
    // 期望到货日期
    google.protobuf.Timestamp qrdj = 9;

}

message CreateAssetsReceivingResponse{
    bool result = 1;
    repeated ConfirmAssetsProduct products = 2;
}

message ConfirmAssetsReceivingRequest{
    uint64 id = 1;
    repeated ConfirmAssetsProduct products = 2;

}
 
message ConfirmAssetsReceivingResponse{
    bool result = 1;
}

message ListAssetsReceivingRequest{
    // 订货单号
    repeated uint64 asset_id = 1;
    // 门店id
    repeated uint64 store_ids = 2;
    //
    repeated string status = 3;
    // 配送开始日期
    google.protobuf.Timestamp start_date = 5;
    // 配送结束日期
    google.protobuf.Timestamp end_date = 6;
    // 分页大小
    int32 limit = 7;
    // 跳过行数
    int32 offset = 8;
    // JDE单号
    string jde_order_id = 9;
    // 收货单号
    string code = 10;
    
}

message ListAssetsReceivingResponse{
    repeated Assets rows = 1;
    uint64 total = 2;
}

message GetAssetByIdRequest{
    uint64 id = 1;
}

message ListAssetProductsByIdRequest{
    uint64 id = 1;
    // 分页大小
    int32 limit = 2;
    // 跳过行数
    int32 offset = 3;
    // 返回总条数
    bool include_total = 4;
}

message ListAssetProductsByIdResponse{
    repeated AssetsProduct rows = 1;
    uint64 total = 2;
}

message AssetsProduct{
    //
    uint64 id = 1;
    //
    uint64 asset_id = 2;
    // 资产编号
    string litm = 3;
    // 单位
    string uom = 4;
    // 数量
    double uorg = 5;
    //
    uint64 created_by = 6;
    //
    google.protobuf.Timestamp created_at = 7;
    //
    string created_name = 8;
    //
    uint64 updated_by = 9;
    //
    google.protobuf.Timestamp updated_at = 10;
    //
    string updated_name = 11;
    //
    string status = 12;
    //
    uint64 partner_id = 13;
    // 
    uint64 product_id = 14;
    // 
    string product_name = 15;
}

message ConfirmAssetsProduct{
    uint64 id = 1;
    double quantity = 2;
}

message Assets{
    // jde公司
    string kcoo = 1;
    // jde单据编号
    string doco = 2;
    // jde单据类型
    string dcto = 3;
    // jde行号
    uint64 lnid = 4;
    // 仓库编号
    string mcu = 5;
    // 门店编号
    uint64 an8 = 6;
    // 订货日期
    google.protobuf.Timestamp trdj = 8;
    // 期望到货日期
    google.protobuf.Timestamp qrdj = 9;
    //
    uint64 dl03 = 10;
    //
    string ktln = 11;
    // 
    string status = 13;
    //
    uint64 partner_id = 14;
    //
    uint64 products_num = 15;
    //
    uint64 created_by = 16;
    //
    google.protobuf.Timestamp created_at = 17;
    //
    string created_name = 18;
    //
    uint64 updated_by = 19;
    //
    google.protobuf.Timestamp updated_at = 20;
    //
    string updated_name = 21;
    //
    uint64 id = 22;
    // 
    string code = 23;
    // 
    uint64 mcu_id = 24;
    // 
    string mcu_name = 25;
    // 
    uint64 store_id = 26;
    // 
    string store_name = 27;

}

