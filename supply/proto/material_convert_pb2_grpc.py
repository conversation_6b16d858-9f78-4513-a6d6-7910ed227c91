# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import material_convert_pb2 as material__convert__pb2


class MaterialConvertStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateMaterialConvert = channel.unary_unary(
        '/material_convert.MaterialConvert/CreateMaterialConvert',
        request_serializer=material__convert__pb2.CreateMaterialConvertRequest.SerializeToString,
        response_deserializer=material__convert__pb2.CreateMaterialConvertResponse.FromString,
        )
    self.ListMaterialConvert = channel.unary_unary(
        '/material_convert.MaterialConvert/ListMaterialConvert',
        request_serializer=material__convert__pb2.ListMaterialConvertRequest.SerializeToString,
        response_deserializer=material__convert__pb2.ListMaterialConvertResponse.FromString,
        )
    self.GetMaterialConvertDetail = channel.unary_unary(
        '/material_convert.MaterialConvert/GetMaterialConvertDetail',
        request_serializer=material__convert__pb2.GetMaterialConvertDetailRequest.SerializeToString,
        response_deserializer=material__convert__pb2.GetMaterialConvertDetailResponse.FromString,
        )
    self.UpdateMaterialConvert = channel.unary_unary(
        '/material_convert.MaterialConvert/UpdateMaterialConvert',
        request_serializer=material__convert__pb2.UpdateMaterialConvertRequest.SerializeToString,
        response_deserializer=material__convert__pb2.UpdateMaterialConvertResponse.FromString,
        )


class MaterialConvertServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def CreateMaterialConvert(self, request, context):
    """创建物料转换单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListMaterialConvert(self, request, context):
    """物料转换单列表查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetMaterialConvertDetail(self, request, context):
    """查询物料转换单详情
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateMaterialConvert(self, request, context):
    """更新物料转换单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_MaterialConvertServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateMaterialConvert': grpc.unary_unary_rpc_method_handler(
          servicer.CreateMaterialConvert,
          request_deserializer=material__convert__pb2.CreateMaterialConvertRequest.FromString,
          response_serializer=material__convert__pb2.CreateMaterialConvertResponse.SerializeToString,
      ),
      'ListMaterialConvert': grpc.unary_unary_rpc_method_handler(
          servicer.ListMaterialConvert,
          request_deserializer=material__convert__pb2.ListMaterialConvertRequest.FromString,
          response_serializer=material__convert__pb2.ListMaterialConvertResponse.SerializeToString,
      ),
      'GetMaterialConvertDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetMaterialConvertDetail,
          request_deserializer=material__convert__pb2.GetMaterialConvertDetailRequest.FromString,
          response_serializer=material__convert__pb2.GetMaterialConvertDetailResponse.SerializeToString,
      ),
      'UpdateMaterialConvert': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateMaterialConvert,
          request_deserializer=material__convert__pb2.UpdateMaterialConvertRequest.FromString,
          response_serializer=material__convert__pb2.UpdateMaterialConvertResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'material_convert.MaterialConvert', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
