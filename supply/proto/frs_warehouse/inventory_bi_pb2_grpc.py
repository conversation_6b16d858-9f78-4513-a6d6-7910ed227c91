# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from frs_warehouse import inventory_bi_pb2 as frs__warehouse_dot_inventory__bi__pb2


class FrsWarehouseInventoryBiServiceStub(object):
  """加盟商门店库存报表相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.RealtimeInventory = channel.unary_unary(
        '/frs_warehouse.FrsWarehouseInventoryBiService/RealtimeInventory',
        request_serializer=frs__warehouse_dot_inventory__bi__pb2.RealtimeInventoryRequest.SerializeToString,
        response_deserializer=frs__warehouse_dot_inventory__bi__pb2.RealtimeInventoryResponse.FromString,
        )
    self.DailyInventory = channel.unary_unary(
        '/frs_warehouse.FrsWarehouseInventoryBiService/DailyInventory',
        request_serializer=frs__warehouse_dot_inventory__bi__pb2.DailyInventoryRequest.SerializeToString,
        response_deserializer=frs__warehouse_dot_inventory__bi__pb2.DailyInventoryResponse.FromString,
        )
    self.QueryInventoryLog = channel.unary_unary(
        '/frs_warehouse.FrsWarehouseInventoryBiService/QueryInventoryLog',
        request_serializer=frs__warehouse_dot_inventory__bi__pb2.QueryInventoryLogRequest.SerializeToString,
        response_deserializer=frs__warehouse_dot_inventory__bi__pb2.QueryInventoryLogResponse.FromString,
        )


class FrsWarehouseInventoryBiServiceServicer(object):
  """加盟商门店库存报表相关服务
  """

  def RealtimeInventory(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DailyInventory(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryInventoryLog(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_FrsWarehouseInventoryBiServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'RealtimeInventory': grpc.unary_unary_rpc_method_handler(
          servicer.RealtimeInventory,
          request_deserializer=frs__warehouse_dot_inventory__bi__pb2.RealtimeInventoryRequest.FromString,
          response_serializer=frs__warehouse_dot_inventory__bi__pb2.RealtimeInventoryResponse.SerializeToString,
      ),
      'DailyInventory': grpc.unary_unary_rpc_method_handler(
          servicer.DailyInventory,
          request_deserializer=frs__warehouse_dot_inventory__bi__pb2.DailyInventoryRequest.FromString,
          response_serializer=frs__warehouse_dot_inventory__bi__pb2.DailyInventoryResponse.SerializeToString,
      ),
      'QueryInventoryLog': grpc.unary_unary_rpc_method_handler(
          servicer.QueryInventoryLog,
          request_deserializer=frs__warehouse_dot_inventory__bi__pb2.QueryInventoryLogRequest.FromString,
          response_serializer=frs__warehouse_dot_inventory__bi__pb2.QueryInventoryLogResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'frs_warehouse.FrsWarehouseInventoryBiService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
