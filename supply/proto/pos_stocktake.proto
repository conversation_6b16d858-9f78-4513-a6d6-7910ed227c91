syntax = "proto3";
package pos_stocktake;
import "google/api/annotations.proto";
// import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

// 固定资产收货相关服务
service PosStocktake{

    rpc UploadPosStocktake(UploadPosStocktakeRequest) returns (UploadPosStocktakeResponse){
        option (google.api.http) = {
            put:"/api/v2/supply/stocktake/pos/sync"
            body:"*"
        };
    }

    rpc GetStocktakeQuantity(GetStocktakeQuantityRequest) returns (GetStocktakeQuantityReponse){
        option (google.api.http) = {
            get:"/api/v2/supply/stocktake/weight/qty/{store_code}"
            body:"*"
        };
    }
}


message UploadPosStocktakeRequest {
//    int64 partner_id = 1;
    string store_code = 1;
    double quantity = 2;
    string status = 3;
    string sync_type = 4;
}

message UploadPosStocktakeResponse {
    bool result = 1;
}

message GetStocktakeQuantityRequest{
    string store_code = 1;
}

message GetStocktakeQuantityReponse{
    string quantity = 1;
    string status = 2;
}
