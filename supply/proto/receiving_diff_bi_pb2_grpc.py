# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import receiving_diff_bi_pb2 as receiving__diff__bi__pb2


class ReceivingDiffBiServiceStub(object):
  """收货差异单报表相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetReceivingDiffCollect = channel.unary_unary(
        '/receiving.ReceivingDiffBiService/GetReceivingDiffCollect',
        request_serializer=receiving__diff__bi__pb2.GetReceivingDiffCollectRequest.SerializeToString,
        response_deserializer=receiving__diff__bi__pb2.GetReceivingDiffCollectResponse.FromString,
        )
    self.GetReceivingDiffDetail = channel.unary_unary(
        '/receiving.ReceivingDiffBiService/GetReceivingDiffDetail',
        request_serializer=receiving__diff__bi__pb2.GetReceivingDiffDetailRequest.SerializeToString,
        response_deserializer=receiving__diff__bi__pb2.GetReceivingDiffDetailResponse.FromString,
        )


class ReceivingDiffBiServiceServicer(object):
  """收货差异单报表相关服务
  """

  def GetReceivingDiffCollect(self, request, context):
    """查询收货差异汇总报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetReceivingDiffDetail(self, request, context):
    """查询收货差异详情报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_ReceivingDiffBiServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetReceivingDiffCollect': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceivingDiffCollect,
          request_deserializer=receiving__diff__bi__pb2.GetReceivingDiffCollectRequest.FromString,
          response_serializer=receiving__diff__bi__pb2.GetReceivingDiffCollectResponse.SerializeToString,
      ),
      'GetReceivingDiffDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetReceivingDiffDetail,
          request_deserializer=receiving__diff__bi__pb2.GetReceivingDiffDetailRequest.FromString,
          response_serializer=receiving__diff__bi__pb2.GetReceivingDiffDetailResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'receiving.ReceivingDiffBiService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
