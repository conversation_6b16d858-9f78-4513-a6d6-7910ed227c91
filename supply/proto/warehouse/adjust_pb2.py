# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: warehouse/adjust.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='warehouse/adjust.proto',
  package='warehouse',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n\x16warehouse/adjust.proto\x12\twarehouse\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x13\n\x04Pong\x12\x0b\n\x03msg\x18\x01 \x01(\t\"[\n\x13\x43reateAdjustRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\x04\x12\x30\n\x0crequest_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"X\n\x14\x43reateAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\x12\x30\n\x0crequest_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xc1\x03\n\x10GetAdjustRequest\x12\x15\n\rinclude_total\x18\x01 \x01(\x08\x12.\n\nstart_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0breason_type\x18\x04 \x01(\t\x12\x10\n\x08\x62ranches\x18\x05 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x06 \x03(\x04\x12\x0e\n\x06offset\x18\x07 \x01(\r\x12\r\n\x05limit\x18\x08 \x01(\r\x12\x32\n\x06status\x18\t \x03(\x0e\x32\".warehouse.GetAdjustRequest.STATUS\x12\x0c\n\x04\x63ode\x18\n \x01(\t\x12\r\n\x05order\x18\x0c \x01(\t\x12\x0c\n\x04sort\x18\r \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x1e \x01(\t\x12\x0f\n\x07sources\x18\x1f \x03(\t\"X\n\x06STATUS\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06INITED\x10\x01\x12\r\n\tSUBMITTED\x10\x02\x12\x0c\n\x08\x41PPROVED\x10\x03\x12\x0c\n\x08REJECTED\x10\x04\x12\r\n\tCANCELLED\x10\x05\"\xab\x06\n\x06\x41\x64just\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1b\n\x13\x61\x64just_order_number\x18\x02 \x01(\x04\x12\x14\n\x0c\x61\x64just_store\x18\x03 \x01(\x04\x12!\n\x19\x61\x64just_store_secondary_id\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x12\n\npartner_id\x18\x06 \x01(\x04\x12\x16\n\x0eprocess_status\x18\x07 \x01(\t\x12\x13\n\x0breason_type\x18\x08 \x01(\t\x12\x0e\n\x06remark\x18\t \x01(\t\x12\x0e\n\x06status\x18\n \x01(\t\x12/\n\x0b\x61\x64just_date\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\ncreated_by\x18\x0e \x01(\x04\x12\x12\n\nupdated_by\x18\x0f \x01(\x04\x12\x0f\n\x07user_id\x18\x10 \x01(\x04\x12\x17\n\x0f\x62ranch_batch_id\x18\x11 \x01(\x04\x12\x15\n\rschedule_code\x18\x12 \x01(\t\x12\x13\n\x0bschedule_id\x18\x13 \x01(\x04\x12\x12\n\nrequest_id\x18\x14 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x15 \x01(\t\x12\x14\n\x0cupdated_name\x18\x16 \x01(\t\x12\x12\n\nreceive_id\x18\x19 \x01(\x04\x12\x14\n\x0creceive_code\x18\x1a \x01(\t\x12\x15\n\rschedule_name\x18\x1c \x01(\t\x12\x13\n\x0b\x62ranch_type\x18\x1e \x01(\t\x12+\n\x0b\x61ttachments\x18\x1f \x03(\x0b\x32\x16.warehouse.Attachments\x12\x0e\n\x06source\x18  \x01(\t\x12\x13\n\x0breason_name\x18! \x01(\t\x12\x15\n\rreject_reason\x18$ \x01(\t\x12\x14\n\x0ctotal_amount\x18& \x01(\x01\x12\x1a\n\x12total_sales_amount\x18\' \x01(\x01\x12\x10\n\x08\x63urrency\x18( \x01(\t\"C\n\x11GetAdjustResponse\x12\x1f\n\x04rows\x18\x01 \x03(\x0b\x32\x11.warehouse.Adjust\x12\r\n\x05total\x18\x02 \x01(\r\"~\n\x17GetAdjustProductRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x15\n\rinclude_total\x18\x02 \x01(\x08\x12\r\n\x05order\x18\x03 \x01(\t\x12\x0e\n\x06offset\x18\x04 \x01(\r\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x0b\n\x03lan\x18\x06 \x01(\t\"\xd4\x08\n\rAdjustProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x02 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x03 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x04 \x01(\t\x12\x1c\n\x14\x61\x63\x63ounting_unit_spec\x18\x05 \x01(\t\x12\x11\n\tadjust_id\x18\x06 \x01(\x04\x12\x14\n\x0c\x61\x64just_store\x18\x07 \x01(\x04\x12\x1a\n\x12\x63onfirmed_quantity\x18\x08 \x01(\x01\x12\x12\n\ncreated_by\x18\t \x01(\x04\x12\x14\n\x0cis_confirmed\x18\n \x01(\x08\x12\x13\n\x0bitem_number\x18\x0b \x01(\r\x12\x17\n\x0fmaterial_number\x18\x0c \x01(\t\x12\x12\n\npartner_id\x18\r \x01(\x04\x12\x14\n\x0cproduct_code\x18\x0e \x01(\t\x12\x12\n\nproduct_id\x18\x0f \x01(\x04\x12\x14\n\x0cproduct_name\x18\x10 \x01(\t\x12\x10\n\x08quantity\x18\x11 \x01(\x01\x12\x0c\n\x04spec\x18\x12 \x01(\t\x12\x1a\n\x12stocktake_quantity\x18\x13 \x01(\x01\x12\x19\n\x11stocktake_unit_id\x18\x14 \x01(\x04\x12\x0f\n\x07unit_id\x18\x15 \x01(\x04\x12\x11\n\tunit_name\x18\x16 \x01(\t\x12\x11\n\tunit_spec\x18\x17 \x01(\t\x12\x12\n\nupdated_by\x18\x18 \x01(\x04\x12/\n\x0b\x61\x64just_date\x18\x19 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x1a \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\ncreated_at\x18\x1b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07user_id\x18\x1c \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x1d \x01(\t\x12\x14\n\x0cupdated_name\x18\x1e \x01(\t\x12\x13\n\x0breason_type\x18\x1f \x01(\t\x12#\n\x1b\x63onvert_accounting_quantity\x18  \x01(\x01\x12\x0e\n\x06is_bom\x18! \x01(\x08\x12\x13\n\x0bposition_id\x18\" \x01(\x04\x12\x12\n\nsku_remark\x18# \x01(\t\x12\x31\n\x05units\x18$ \x03(\x0b\x32\".warehouse.CreateAdjustProductUint\x12\x12\n\nmodel_name\x18% \x01(\t\x12\x10\n\x08tax_rate\x18& \x01(\x01\x12\x11\n\ttax_price\x18\' \x01(\x01\x12\x0e\n\x06\x61mount\x18( \x01(\x01\x12\x12\n\ntax_amount\x18) \x01(\x01\x12\x12\n\ncost_price\x18* \x01(\x01\x12\x14\n\x0csales_amount\x18+ \x01(\x01\x12\x13\n\x0bsales_price\x18, \x01(\x01\x12\r\n\x05price\x18- \x01(\t\x12\x10\n\x08\x63urrency\x18. \x01(\t\"\x8b\x01\n\x18GetAdjustProductResponse\x12&\n\x04rows\x18\x01 \x03(\x0b\x32\x18.warehouse.AdjustProduct\x12\r\n\x05total\x18\x02 \x01(\r\x12\x38\n\rposition_rows\x18\x03 \x03(\x0b\x32!.warehouse.AdjustPositionProducts\">\n\x14GetAdjustByIDRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x02 \x01(\t\">\n\x14\x43onfirmAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x02 \x01(\t\"\'\n\x15\x43onfirmAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"(\n\x13SubmitAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\"&\n\x14SubmitAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\")\n\x14\x41pproveAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\"\'\n\x15\x41pproveAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"?\n\x13RejectAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x15\n\rreject_reason\x18\x02 \x01(\t\"&\n\x14RejectAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\x85\x02\n GetAdjustProductByStoreIDRequest\x12\x10\n\x08store_id\x18\x01 \x01(\x04\x12\r\n\x05limit\x18\x02 \x01(\r\x12\x0e\n\x06offset\x18\x03 \x01(\r\x12\x15\n\rinclude_total\x18\x04 \x01(\x08\x12/\n\x0b\x61\x64just_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06search\x18\x06 \x01(\t\x12\x15\n\rsearch_fields\x18\x07 \x01(\t\x12\x14\n\x0c\x63\x61tegory_ids\x18\x08 \x03(\x04\x12\x0b\n\x03lan\x18\t \x01(\t\x12\x10\n\x08order_by\x18\n \x01(\t\x12\x0c\n\x04sort\x18\x0b \x01(\t\"\xcd\x02\n\x17\x43reateAdjustProductUint\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x10\n\x08scope_id\x18\x03 \x01(\x04\x12\x0c\n\x04name\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x0f\n\x07updated\x18\x07 \x01(\t\x12\x0c\n\x04rate\x18\x08 \x01(\x01\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\t \x01(\x08\x12\r\n\x05order\x18\n \x01(\x08\x12\x10\n\x08purchase\x18\x0b \x01(\x08\x12\r\n\x05sales\x18\x0c \x01(\x08\x12\x11\n\tstocktake\x18\r \x01(\x08\x12\x0b\n\x03\x62om\x18\x0e \x01(\x08\x12\x19\n\x11\x64\x65\x66\x61ult_stocktake\x18\x0f \x01(\x08\x12\x10\n\x08transfer\x18\x10 \x01(\x08\x12\x10\n\x08tax_rate\x18\x11 \x01(\x01\x12\x11\n\ttax_price\x18\x12 \x01(\x01\x12\x12\n\ncost_price\x18\x13 \x01(\x01\"\xb8\x02\n\x13\x43reateAdjustProduct\x12\x12\n\nproduct_id\x18\x01 \x01(\x04\x12\x19\n\x11loss_report_order\x18\x02 \x01(\t\x12\x14\n\x0cproduct_code\x18\x03 \x01(\t\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12\x12\n\nmodel_name\x18\x05 \x01(\t\x12\x14\n\x0cstorage_type\x18\x06 \x01(\t\x12\x1b\n\x13product_category_id\x18\x07 \x01(\x04\x12\x31\n\x05units\x18\x08 \x03(\x0b\x32\".warehouse.CreateAdjustProductUint\x12\x0f\n\x07\x62\x61rcode\x18\t \x03(\t\x12\x1a\n\x12real_inventory_qty\x18\n \x01(\x01\x12\r\n\x05price\x18\x0b \x01(\t\x12\x10\n\x08\x63urrency\x18\x0c \x01(\t\"\xa2\x01\n!GetAdjustProductByStoreIDResponse\x12,\n\x04rows\x18\x01 \x03(\x0b\x32\x1e.warehouse.CreateAdjustProduct\x12\r\n\x05total\x18\x02 \x01(\r\x12@\n\x18inventory_unchanged_rows\x18\x03 \x03(\x0b\x32\x1e.warehouse.CreateAdjustProduct\"\xb4\x02\n\x14\x43reatedAdjustProduct\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x0f\n\x07unit_id\x18\x03 \x01(\x04\x12\x10\n\x08quantity\x18\x04 \x01(\x01\x12\x13\n\x0breason_type\x18\x05 \x01(\t\x12\x13\n\x0bposition_id\x18\x06 \x01(\x04\x12\'\n\tskuRemark\x18\x07 \x03(\x0b\x32\x14.warehouse.SkuRemark\x12\x10\n\x08tax_rate\x18\x08 \x01(\x01\x12\x11\n\ttax_price\x18\t \x01(\x01\x12\x0e\n\x06\x61mount\x18\n \x01(\x01\x12\x12\n\ntax_amount\x18\x0b \x01(\x01\x12\x12\n\ncost_price\x18\x0c \x01(\x01\x12\x13\n\x0bsales_price\x18\r \x01(\x01\x12\x14\n\x0csales_amount\x18\x0e \x01(\x01\"\xb0\x02\n\x14\x43reatedAdjustRequest\x12\x14\n\x0c\x61\x64just_store\x18\x01 \x01(\x04\x12\x31\n\x08products\x18\x02 \x03(\x0b\x32\x1f.warehouse.CreatedAdjustProduct\x12\x13\n\x0breason_type\x18\x03 \x01(\t\x12\x0e\n\x06remark\x18\x04 \x01(\t\x12\x12\n\nrequest_id\x18\x05 \x01(\x04\x12/\n\x0b\x61\x64just_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\x07 \x01(\t\x12\x13\n\x0bposition_id\x18\x08 \x01(\x04\x12\x0e\n\x06source\x18\t \x01(\t\x12+\n\x0b\x61ttachments\x18\x0b \x03(\x0b\x32\x16.warehouse.Attachments\"(\n\x0b\x41ttachments\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\"\x88\x02\n\x13UpdateAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x31\n\x08products\x18\x02 \x03(\x0b\x32\x1f.warehouse.CreatedAdjustProduct\x12\x0e\n\x06remark\x18\x03 \x01(\t\x12\x13\n\x0breason_type\x18\x04 \x01(\t\x12/\n\x0b\x61\x64just_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x13\n\x0b\x62ranch_type\x18\x06 \x01(\t\x12+\n\x0b\x61ttachments\x18\x07 \x03(\x0b\x32\x16.warehouse.Attachments\x12\x13\n\x0bposition_id\x18\x08 \x01(\x04\"(\n\x13\x44\x65leteAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\"&\n\x14\x44\x65leteAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"<\n\x1a\x44\x65leteAdjustProductRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\x12\x0b\n\x03ids\x18\x02 \x03(\x04\"-\n\x1b\x44\x65leteAdjustProductResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\xc0\x03\n\x19GetAdjustBiCollectRequest\x12\x14\n\x0c\x63\x61tegory_ids\x18\x01 \x03(\x04\x12\x14\n\x0cproduct_name\x18\x02 \x01(\t\x12.\n\nstart_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12\x11\n\tstore_ids\x18\x08 \x03(\x04\x12\x16\n\x0e\x62om_product_id\x18\t \x03(\x04\x12\x15\n\rperiod_symbol\x18\n \x01(\t\x12\r\n\x05order\x18\x0b \x01(\t\x12\x0c\n\x04sort\x18\x0c \x01(\t\x12\x0c\n\x04\x63ode\x18\r \x01(\t\x12\x13\n\x0breason_type\x18\x0e \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0f \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x10 \x01(\t\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x13\n\x0bhour_offset\x18\x12 \x01(\r\x12\x14\n\x0cposition_ids\x18\x13 \x03(\x04\"\xcd\x06\n\x17\x41\x64justBiCollectResponse\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x05 \x01(\x04\x12\x15\n\rcategory_name\x18\x06 \x01(\t\x12\x14\n\x0cproduct_code\x18\x07 \x01(\t\x12\x12\n\nproduct_id\x18\x08 \x01(\x04\x12\x14\n\x0cproduct_name\x18\t \x01(\t\x12\x10\n\x08quantity\x18\x0b \x01(\x01\x12\x12\n\nstore_code\x18\x0c \x01(\t\x12\x10\n\x08store_id\x18\r \x01(\x04\x12\x12\n\nstore_name\x18\x0e \x01(\t\x12\x0f\n\x07unit_id\x18\x0f \x01(\x04\x12\x11\n\tunit_name\x18\x10 \x01(\t\x12\x13\n\x0breason_type\x18\x11 \x01(\t\x12\x13\n\x0breason_name\x18\x15 \x01(\t\x12\x16\n\x0e\x62om_product_id\x18\x12 \x01(\x04\x12\x18\n\x10\x62om_product_code\x18\x13 \x01(\t\x12\x18\n\x10\x62om_product_name\x18\x14 \x01(\t\x12\x13\n\x0b\x62om_unit_id\x18\x16 \x01(\x04\x12\x15\n\rbom_unit_code\x18\x17 \x01(\t\x12\x15\n\rbom_unit_name\x18\x18 \x01(\t\x12\x1e\n\x16\x62om_accounting_unit_id\x18\x19 \x01(\x04\x12 \n\x18\x62om_accounting_unit_code\x18\x1a \x01(\t\x12 \n\x18\x62om_accounting_unit_name\x18\x1b \x01(\t\x12\x0b\n\x03qty\x18\x1c \x01(\x01\x12\r\n\x05price\x18\x1d \x01(\x01\x12\x16\n\x0e\x61\x63\x63ounting_qty\x18\x1e \x01(\x01\x12\x0c\n\x04\x63ost\x18\x1f \x01(\x01\x12\x15\n\rperiod_symbol\x18  \x01(\t\x12\x13\n\x0bposition_id\x18! \x01(\x04\x12\x15\n\rposition_code\x18\" \x01(\t\x12\x15\n\rposition_name\x18# \x01(\t\x12/\n\x0b\x61\x64just_date\x18$ \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"}\n\x08\x41\x44_total\x12\r\n\x05\x63ount\x18\x01 \x01(\x04\x12\x14\n\x0csum_quantity\x18\x02 \x01(\x01\x12\x1f\n\x17sum_accounting_quantity\x18\x03 \x01(\x01\x12\x0f\n\x07sum_qty\x18\x04 \x01(\x01\x12\x1a\n\x12sum_accounting_qty\x18\x05 \x01(\x01\"r\n\x1aGetAdjustBiCollectResponse\x12\x30\n\x04rows\x18\x01 \x03(\x0b\x32\".warehouse.AdjustBiCollectResponse\x12\"\n\x05total\x18\x02 \x01(\x0b\x32\x13.warehouse.AD_total\"(\n\x13\x43\x61ncelAdjustRequest\x12\x11\n\tadjust_id\x18\x01 \x01(\x04\"&\n\x14\x43\x61ncelAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"\x9a\x03\n\x1fGetAdjustCollectDetailedRequest\x12\x14\n\x0c\x63\x61tegory_ids\x18\x01 \x03(\x04\x12\x14\n\x0cproduct_name\x18\x02 \x01(\t\x12.\n\nstart_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x05 \x01(\r\x12\x0e\n\x06offset\x18\x06 \x01(\r\x12\x15\n\rinclude_total\x18\x07 \x01(\x08\x12\x11\n\tstore_ids\x18\x08 \x03(\x04\x12\x16\n\x0e\x62om_product_id\x18\t \x03(\x04\x12\r\n\x05order\x18\n \x01(\t\x12\x0c\n\x04sort\x18\x0b \x01(\t\x12\x0c\n\x04\x63ode\x18\x0c \x01(\t\x12\x14\n\x0cis_wms_store\x18\x0e \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x10 \x01(\t\x12\x0b\n\x03lan\x18\x11 \x01(\t\x12\x14\n\x0cposition_ids\x18\x12 \x03(\x04\x12\x13\n\x0breason_type\x18\x13 \x01(\t\"\x8d\x07\n\x15\x41\x64justCollectDetailed\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x13\n\x0b\x61\x64just_code\x18\x04 \x01(\t\x12/\n\x0b\x61\x64just_date\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tadjust_id\x18\x06 \x01(\x04\x12\x15\n\rcategory_code\x18\x07 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x08 \x01(\x04\x12\x15\n\rcategory_name\x18\t \x01(\t\x12\n\n\x02id\x18\n \x01(\x04\x12\x14\n\x0cproduct_code\x18\x0b \x01(\t\x12\x12\n\nproduct_id\x18\x0c \x01(\x04\x12\x14\n\x0cproduct_name\x18\r \x01(\t\x12\x10\n\x08quantity\x18\x0e \x01(\x01\x12\x13\n\x0breason_type\x18\x0f \x01(\t\x12\x13\n\x0breason_name\x18! \x01(\t\x12\x12\n\nstore_code\x18\x10 \x01(\t\x12\x10\n\x08store_id\x18\x11 \x01(\x04\x12\x12\n\nstore_name\x18\x12 \x01(\t\x12\x0f\n\x07unit_id\x18\x13 \x01(\x04\x12\x11\n\tunit_name\x18\x14 \x01(\t\x12\x16\n\x0e\x62om_product_id\x18\x15 \x01(\x04\x12\x18\n\x10\x62om_product_code\x18\x16 \x01(\t\x12\x18\n\x10\x62om_product_name\x18\x17 \x01(\t\x12\x13\n\x0b\x62om_unit_id\x18\x18 \x01(\x04\x12\x15\n\rbom_unit_code\x18\x19 \x01(\t\x12\x15\n\rbom_unit_name\x18\x1a \x01(\t\x12\x1e\n\x16\x62om_accounting_unit_id\x18\x1b \x01(\x04\x12 \n\x18\x62om_accounting_unit_code\x18\x1c \x01(\t\x12 \n\x18\x62om_accounting_unit_name\x18\x1d \x01(\t\x12\x0b\n\x03qty\x18\x1e \x01(\x01\x12\r\n\x05price\x18\x1f \x01(\x01\x12\x16\n\x0e\x61\x63\x63ounting_qty\x18  \x01(\x01\x12\x0c\n\x04\x63ost\x18\" \x01(\x01\x12\x13\n\x0b\x62ranch_type\x18# \x01(\t\x12\x0e\n\x06remark\x18$ \x01(\t\x12\x13\n\x0bposition_id\x18% \x01(\x04\x12\x15\n\rposition_code\x18& \x01(\t\x12\x15\n\rposition_name\x18\' \x01(\t\"v\n GetAdjustCollectDetailedResponse\x12.\n\x04rows\x18\x01 \x03(\x0b\x32 .warehouse.AdjustCollectDetailed\x12\"\n\x05total\x18\x02 \x01(\x0b\x32\x13.warehouse.AD_total\"0\n\x1e\x41utoCloseCreatedAdjustResponse\x12\x0e\n\x06result\x18\x01 \x01(\x08\"Y\n\x1d\x41utoCloseCreatedAdjustRequest\x12\x13\n\x0b\x61\x64just_date\x18\x01 \x01(\t\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x0f\n\x07user_id\x18\x03 \x01(\x04\"\xfe\x01\n\x1cGetMaterialAdjustDataRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tstore_ids\x18\x03 \x03(\x04\x12\x14\n\x0cmaterial_ids\x18\x04 \x03(\x04\x12\r\n\x05limit\x18\x05 \x01(\x04\x12\x0e\n\x06offset\x18\x06 \x01(\x04\x12\x14\n\x0cis_wms_store\x18\x07 \x01(\x08\x12\x0b\n\x03lan\x18\n \x01(\t\x12\x15\n\rinclude_total\x18\x0b \x01(\x08\"l\n\x1dGetMaterialAdjustDataResponse\x12\'\n\x04rows\x18\x01 \x03(\x0b\x32\x19.warehouse.AdjustResponse\x12\"\n\x05total\x18\x02 \x01(\x0b\x32\x13.warehouse.AD_total\"\xb9\x05\n\x0e\x41\x64justResponse\x12\x1b\n\x13\x61\x63\x63ounting_quantity\x18\x01 \x01(\x01\x12\x1a\n\x12\x61\x63\x63ounting_unit_id\x18\x02 \x01(\x04\x12\x1c\n\x14\x61\x63\x63ounting_unit_name\x18\x03 \x01(\t\x12\x15\n\rcategory_code\x18\x04 \x01(\t\x12\x13\n\x0b\x63\x61tegory_id\x18\x05 \x01(\x04\x12\x15\n\rcategory_name\x18\x06 \x01(\t\x12\x14\n\x0cproduct_code\x18\x07 \x01(\t\x12\x12\n\nproduct_id\x18\x08 \x01(\x04\x12\x14\n\x0cproduct_name\x18\t \x01(\t\x12\x10\n\x08quantity\x18\x0b \x01(\x01\x12\x12\n\nstore_code\x18\x0c \x01(\t\x12\x10\n\x08store_id\x18\r \x01(\x04\x12\x12\n\nstore_name\x18\x0e \x01(\t\x12\x0f\n\x07unit_id\x18\x0f \x01(\x04\x12\x11\n\tunit_name\x18\x10 \x01(\t\x12\x13\n\x0breason_type\x18\x11 \x01(\t\x12\x16\n\x0e\x62om_product_id\x18\x12 \x01(\x04\x12\x18\n\x10\x62om_product_code\x18\x13 \x01(\t\x12\x18\n\x10\x62om_product_name\x18\x14 \x01(\t\x12\x0b\n\x03qty\x18\x15 \x01(\x01\x12\x13\n\x0b\x62om_unit_id\x18\x16 \x01(\x04\x12\x15\n\rbom_unit_code\x18\x17 \x01(\t\x12\x15\n\rbom_unit_name\x18\x18 \x01(\t\x12\x1e\n\x16\x62om_accounting_unit_id\x18\x19 \x01(\x04\x12 \n\x18\x62om_accounting_unit_code\x18\x1a \x01(\t\x12 \n\x18\x62om_accounting_unit_name\x18\x1b \x01(\t\x12\x16\n\x0e\x61\x63\x63ounting_qty\x18\x1c \x01(\x01\x12\r\n\x05price\x18\x1d \x01(\x01\x12\x0c\n\x04\x63ost\x18\x1e \x01(\x01\x12\x13\n\x0b\x61\x64just_date\x18\x1f \x01(\t\"\xa1\x01\n\x1a\x43reatedAdjustProductByCode\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x02 \x01(\t\x12\x11\n\tunit_code\x18\x03 \x01(\t\x12\x10\n\x08quantity\x18\x04 \x01(\x01\x12\x13\n\x0breason_type\x18\x05 \x01(\t\x12\'\n\tskuRemark\x18\x06 \x03(\x0b\x32\x14.warehouse.SkuRemark\"\x8c\x01\n\tSkuRemark\x12&\n\x04name\x18\x01 \x01(\x0b\x32\x18.warehouse.SkuRemark.Tag\x12(\n\x06values\x18\x02 \x01(\x0b\x32\x18.warehouse.SkuRemark.Tag\x1a-\n\x03Tag\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\"\xc6\x01\n\x1a\x43reatedAdjustByCodeRequest\x12\x14\n\x0c\x61\x64just_store\x18\x01 \x01(\t\x12\x37\n\x08products\x18\x02 \x03(\x0b\x32%.warehouse.CreatedAdjustProductByCode\x12\x13\n\x0breason_type\x18\x03 \x01(\t\x12\x0e\n\x06remark\x18\x04 \x01(\t\x12\x12\n\nrequest_id\x18\x05 \x01(\t\x12\x13\n\x0b\x61\x64just_date\x18\x06 \x01(\t\x12\x0b\n\x03lan\x18\x07 \x01(\t\"\x96\x01\n\x16\x41\x64justPositionProducts\x12\x13\n\x0bposition_id\x18\x01 \x01(\x04\x12\x15\n\rposition_code\x18\x02 \x01(\t\x12\x15\n\rposition_name\x18\x03 \x01(\t\x12*\n\x08products\x18\x04 \x03(\x0b\x32\x18.warehouse.AdjustProduct\x12\r\n\x05total\x18\x05 \x01(\x04\x32\xc3\x15\n\x0fWarehouseAdjust\x12V\n\x04Ping\x12\x16.google.protobuf.Empty\x1a\x0f.warehouse.Pong\"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/api/v2/supply/warehouse/ping\x12o\n\tGetAdjust\x12\x1b.warehouse.GetAdjustRequest\x1a\x1c.warehouse.GetAdjustResponse\"\'\x82\xd3\xe4\x93\x02!\x12\x1f/api/v2/supply/warehouse/adjust\x12\x98\x01\n\x10GetAdjustProduct\x12\".warehouse.GetAdjustProductRequest\x1a#.warehouse.GetAdjustProductResponse\";\x82\xd3\xe4\x93\x02\x35\x12\x33/api/v2/supply/warehouse/adjust/{adjust_id}/product\x12x\n\rGetAdjustByID\x12\x1f.warehouse.GetAdjustByIDRequest\x1a\x11.warehouse.Adjust\"3\x82\xd3\xe4\x93\x02-\x12+/api/v2/supply/warehouse/adjust/{adjust_id}\x12\x92\x01\n\rConfirmAdjust\x12\x1f.warehouse.ConfirmAdjustRequest\x1a .warehouse.ConfirmAdjustResponse\">\x82\xd3\xe4\x93\x02\x38\x1a\x33/api/v2/supply/warehouse/adjust/{adjust_id}/confirm:\x01*\x12\x8e\x01\n\x0cSubmitAdjust\x12\x1e.warehouse.SubmitAdjustRequest\x1a\x1f.warehouse.SubmitAdjustResponse\"=\x82\xd3\xe4\x93\x02\x37\x1a\x32/api/v2/supply/warehouse/adjust/{adjust_id}/submit:\x01*\x12\x92\x01\n\rApproveAdjust\x12\x1f.warehouse.ApproveAdjustRequest\x1a .warehouse.ApproveAdjustResponse\">\x82\xd3\xe4\x93\x02\x38\x1a\x33/api/v2/supply/warehouse/adjust/{adjust_id}/approve:\x01*\x12\x8e\x01\n\x0cRejectAdjust\x12\x1e.warehouse.RejectAdjustRequest\x1a\x1f.warehouse.RejectAdjustResponse\"=\x82\xd3\xe4\x93\x02\x37\x1a\x32/api/v2/supply/warehouse/adjust/{adjust_id}/reject:\x01*\x12\xae\x01\n\x19GetAdjustProductByStoreID\x12+.warehouse.GetAdjustProductByStoreIDRequest\x1a,.warehouse.GetAdjustProductByStoreIDResponse\"6\x82\xd3\xe4\x93\x02\x30\x12./api/v2/supply/warehouse/adjust/store/products\x12v\n\rCreatedAdjust\x12\x1f.warehouse.CreatedAdjustRequest\x1a\x11.warehouse.Adjust\"1\x82\xd3\xe4\x93\x02+\"&/api/v2/supply/warehouse/adjust/create:\x01*\x12\x80\x01\n\x0cUpdateAdjust\x12\x1e.warehouse.UpdateAdjustRequest\x1a\x11.warehouse.Adjust\"=\x82\xd3\xe4\x93\x02\x37\x1a\x32/api/v2/supply/warehouse/adjust/{adjust_id}/update:\x01*\x12\x8e\x01\n\x0c\x44\x65leteAdjust\x12\x1e.warehouse.DeleteAdjustRequest\x1a\x1f.warehouse.DeleteAdjustResponse\"=\x82\xd3\xe4\x93\x02\x37\x1a\x32/api/v2/supply/warehouse/adjust/{adjust_id}/delete:\x01*\x12\xab\x01\n\x13\x44\x65leteAdjustProduct\x12%.warehouse.DeleteAdjustProductRequest\x1a&.warehouse.DeleteAdjustProductResponse\"E\x82\xd3\xe4\x93\x02?\x1a:/api/v2/supply/warehouse/adjust/product/{adjust_id}/delete:\x01*\x12\x95\x01\n\x12GetAdjustBiCollect\x12$.warehouse.GetAdjustBiCollectRequest\x1a%.warehouse.GetAdjustBiCollectResponse\"2\x82\xd3\xe4\x93\x02,\x12*/api/v2/supply/warehouse/adjust/bi/collect\x12\x8e\x01\n\x0c\x43\x61ncelAdjust\x12\x1e.warehouse.CancelAdjustRequest\x1a\x1f.warehouse.CancelAdjustResponse\"=\x82\xd3\xe4\x93\x02\x37\x1a\x32/api/v2/supply/warehouse/adjust/auto_create/cancel:\x01*\x12\xa8\x01\n\x18GetAdjustCollectDetailed\x12*.warehouse.GetAdjustCollectDetailedRequest\x1a+.warehouse.GetAdjustCollectDetailedResponse\"3\x82\xd3\xe4\x93\x02-\x12+/api/v2/supply/warehouse/adjust/bi/detailed\x12\xa1\x01\n\x16\x41utoCloseCreatedAdjust\x12(.warehouse.AutoCloseCreatedAdjustRequest\x1a).warehouse.AutoCloseCreatedAdjustResponse\"2\x82\xd3\xe4\x93\x02,\x12*/api/v2/supply/warehouse/adjust/close/auto\x12\xa1\x01\n\x15GetMaterialAdjustData\x12\'.warehouse.GetMaterialAdjustDataRequest\x1a(.warehouse.GetMaterialAdjustDataResponse\"5\x82\xd3\xe4\x93\x02/\x12-/api/v2/supply/warehouse/adjust/material/cost\x12\x7f\n\x13\x43reatedAdjustByCode\x12%.warehouse.CreatedAdjustByCodeRequest\x1a\x11.warehouse.Adjust\".\x82\xd3\xe4\x93\x02(\"#/api/v2/supply/warehouse/adjust/pos:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_empty__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])



_GETADJUSTREQUEST_STATUS = _descriptor.EnumDescriptor(
  name='STATUS',
  full_name='warehouse.GetAdjustRequest.STATUS',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='INITED', index=1, number=1,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SUBMITTED', index=2, number=2,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='APPROVED', index=3, number=3,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REJECTED', index=4, number=4,
      serialized_options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='CANCELLED', index=5, number=5,
      serialized_options=None,
      type=None),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=695,
  serialized_end=783,
)
_sym_db.RegisterEnumDescriptor(_GETADJUSTREQUEST_STATUS)


_PONG = _descriptor.Descriptor(
  name='Pong',
  full_name='warehouse.Pong',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='msg', full_name='warehouse.Pong.msg', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=129,
  serialized_end=148,
)


_CREATEADJUSTREQUEST = _descriptor.Descriptor(
  name='CreateAdjustRequest',
  full_name='warehouse.CreateAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='request_id', full_name='warehouse.CreateAdjustRequest.request_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='warehouse.CreateAdjustRequest.request_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=150,
  serialized_end=241,
)


_CREATEADJUSTRESPONSE = _descriptor.Descriptor(
  name='CreateAdjustResponse',
  full_name='warehouse.CreateAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.CreateAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_date', full_name='warehouse.CreateAdjustResponse.request_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=243,
  serialized_end=331,
)


_GETADJUSTREQUEST = _descriptor.Descriptor(
  name='GetAdjustRequest',
  full_name='warehouse.GetAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='include_total', full_name='warehouse.GetAdjustRequest.include_total', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='warehouse.GetAdjustRequest.start_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='warehouse.GetAdjustRequest.end_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='warehouse.GetAdjustRequest.reason_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branches', full_name='warehouse.GetAdjustRequest.branches', index=4,
      number=5, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='warehouse.GetAdjustRequest.product_ids', index=5,
      number=6, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='warehouse.GetAdjustRequest.offset', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='warehouse.GetAdjustRequest.limit', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.GetAdjustRequest.status', index=8,
      number=9, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.GetAdjustRequest.code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='warehouse.GetAdjustRequest.order', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='warehouse.GetAdjustRequest.sort', index=11,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.GetAdjustRequest.branch_type', index=12,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sources', full_name='warehouse.GetAdjustRequest.sources', index=13,
      number=31, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _GETADJUSTREQUEST_STATUS,
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=334,
  serialized_end=783,
)


_ADJUST = _descriptor.Descriptor(
  name='Adjust',
  full_name='warehouse.Adjust',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.Adjust.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_order_number', full_name='warehouse.Adjust.adjust_order_number', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_store', full_name='warehouse.Adjust.adjust_store', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_store_secondary_id', full_name='warehouse.Adjust.adjust_store_secondary_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.Adjust.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='warehouse.Adjust.partner_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='warehouse.Adjust.process_status', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='warehouse.Adjust.reason_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='warehouse.Adjust.remark', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.Adjust.status', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='warehouse.Adjust.adjust_date', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='warehouse.Adjust.created_at', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='warehouse.Adjust.updated_at', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='warehouse.Adjust.created_by', index=13,
      number=14, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='warehouse.Adjust.updated_by', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='warehouse.Adjust.user_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_batch_id', full_name='warehouse.Adjust.branch_batch_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_code', full_name='warehouse.Adjust.schedule_code', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_id', full_name='warehouse.Adjust.schedule_id', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='warehouse.Adjust.request_id', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='warehouse.Adjust.created_name', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='warehouse.Adjust.updated_name', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_id', full_name='warehouse.Adjust.receive_id', index=22,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receive_code', full_name='warehouse.Adjust.receive_code', index=23,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='schedule_name', full_name='warehouse.Adjust.schedule_name', index=24,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.Adjust.branch_type', index=25,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='warehouse.Adjust.attachments', index=26,
      number=31, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source', full_name='warehouse.Adjust.source', index=27,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_name', full_name='warehouse.Adjust.reason_name', index=28,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='warehouse.Adjust.reject_reason', index=29,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_amount', full_name='warehouse.Adjust.total_amount', index=30,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total_sales_amount', full_name='warehouse.Adjust.total_sales_amount', index=31,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='warehouse.Adjust.currency', index=32,
      number=40, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=786,
  serialized_end=1597,
)


_GETADJUSTRESPONSE = _descriptor.Descriptor(
  name='GetAdjustResponse',
  full_name='warehouse.GetAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.GetAdjustResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.GetAdjustResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1599,
  serialized_end=1666,
)


_GETADJUSTPRODUCTREQUEST = _descriptor.Descriptor(
  name='GetAdjustProductRequest',
  full_name='warehouse.GetAdjustProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='warehouse.GetAdjustProductRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='warehouse.GetAdjustProductRequest.include_total', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='warehouse.GetAdjustProductRequest.order', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='warehouse.GetAdjustProductRequest.offset', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='warehouse.GetAdjustProductRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.GetAdjustProductRequest.lan', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1668,
  serialized_end=1794,
)


_ADJUSTPRODUCT = _descriptor.Descriptor(
  name='AdjustProduct',
  full_name='warehouse.AdjustProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.AdjustProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='warehouse.AdjustProduct.accounting_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='warehouse.AdjustProduct.accounting_unit_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='warehouse.AdjustProduct.accounting_unit_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_spec', full_name='warehouse.AdjustProduct.accounting_unit_spec', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='warehouse.AdjustProduct.adjust_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_store', full_name='warehouse.AdjustProduct.adjust_store', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='confirmed_quantity', full_name='warehouse.AdjustProduct.confirmed_quantity', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='warehouse.AdjustProduct.created_by', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_confirmed', full_name='warehouse.AdjustProduct.is_confirmed', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='item_number', full_name='warehouse.AdjustProduct.item_number', index=10,
      number=11, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_number', full_name='warehouse.AdjustProduct.material_number', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='warehouse.AdjustProduct.partner_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='warehouse.AdjustProduct.product_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='warehouse.AdjustProduct.product_id', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.AdjustProduct.product_name', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='warehouse.AdjustProduct.quantity', index=16,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spec', full_name='warehouse.AdjustProduct.spec', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_quantity', full_name='warehouse.AdjustProduct.stocktake_quantity', index=18,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake_unit_id', full_name='warehouse.AdjustProduct.stocktake_unit_id', index=19,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='warehouse.AdjustProduct.unit_id', index=20,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='warehouse.AdjustProduct.unit_name', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='warehouse.AdjustProduct.unit_spec', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='warehouse.AdjustProduct.updated_by', index=23,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='warehouse.AdjustProduct.adjust_date', index=24,
      number=25, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='warehouse.AdjustProduct.updated_at', index=25,
      number=26, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='warehouse.AdjustProduct.created_at', index=26,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='warehouse.AdjustProduct.user_id', index=27,
      number=28, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='warehouse.AdjustProduct.created_name', index=28,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='warehouse.AdjustProduct.updated_name', index=29,
      number=30, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='warehouse.AdjustProduct.reason_type', index=30,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_accounting_quantity', full_name='warehouse.AdjustProduct.convert_accounting_quantity', index=31,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_bom', full_name='warehouse.AdjustProduct.is_bom', index=32,
      number=33, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='warehouse.AdjustProduct.position_id', index=33,
      number=34, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sku_remark', full_name='warehouse.AdjustProduct.sku_remark', index=34,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='warehouse.AdjustProduct.units', index=35,
      number=36, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_name', full_name='warehouse.AdjustProduct.model_name', index=36,
      number=37, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='warehouse.AdjustProduct.tax_rate', index=37,
      number=38, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='warehouse.AdjustProduct.tax_price', index=38,
      number=39, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='warehouse.AdjustProduct.amount', index=39,
      number=40, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_amount', full_name='warehouse.AdjustProduct.tax_amount', index=40,
      number=41, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='warehouse.AdjustProduct.cost_price', index=41,
      number=42, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='warehouse.AdjustProduct.sales_amount', index=42,
      number=43, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='warehouse.AdjustProduct.sales_price', index=43,
      number=44, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='warehouse.AdjustProduct.price', index=44,
      number=45, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='warehouse.AdjustProduct.currency', index=45,
      number=46, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1797,
  serialized_end=2905,
)


_GETADJUSTPRODUCTRESPONSE = _descriptor.Descriptor(
  name='GetAdjustProductResponse',
  full_name='warehouse.GetAdjustProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.GetAdjustProductResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.GetAdjustProductResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_rows', full_name='warehouse.GetAdjustProductResponse.position_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2908,
  serialized_end=3047,
)


_GETADJUSTBYIDREQUEST = _descriptor.Descriptor(
  name='GetAdjustByIDRequest',
  full_name='warehouse.GetAdjustByIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='warehouse.GetAdjustByIDRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.GetAdjustByIDRequest.branch_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3049,
  serialized_end=3111,
)


_CONFIRMADJUSTREQUEST = _descriptor.Descriptor(
  name='ConfirmAdjustRequest',
  full_name='warehouse.ConfirmAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='warehouse.ConfirmAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.ConfirmAdjustRequest.branch_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3113,
  serialized_end=3175,
)


_CONFIRMADJUSTRESPONSE = _descriptor.Descriptor(
  name='ConfirmAdjustResponse',
  full_name='warehouse.ConfirmAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.ConfirmAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3177,
  serialized_end=3216,
)


_SUBMITADJUSTREQUEST = _descriptor.Descriptor(
  name='SubmitAdjustRequest',
  full_name='warehouse.SubmitAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='warehouse.SubmitAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3218,
  serialized_end=3258,
)


_SUBMITADJUSTRESPONSE = _descriptor.Descriptor(
  name='SubmitAdjustResponse',
  full_name='warehouse.SubmitAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.SubmitAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3260,
  serialized_end=3298,
)


_APPROVEADJUSTREQUEST = _descriptor.Descriptor(
  name='ApproveAdjustRequest',
  full_name='warehouse.ApproveAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='warehouse.ApproveAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3300,
  serialized_end=3341,
)


_APPROVEADJUSTRESPONSE = _descriptor.Descriptor(
  name='ApproveAdjustResponse',
  full_name='warehouse.ApproveAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.ApproveAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3343,
  serialized_end=3382,
)


_REJECTADJUSTREQUEST = _descriptor.Descriptor(
  name='RejectAdjustRequest',
  full_name='warehouse.RejectAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='warehouse.RejectAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reject_reason', full_name='warehouse.RejectAdjustRequest.reject_reason', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3384,
  serialized_end=3447,
)


_REJECTADJUSTRESPONSE = _descriptor.Descriptor(
  name='RejectAdjustResponse',
  full_name='warehouse.RejectAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.RejectAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3449,
  serialized_end=3487,
)


_GETADJUSTPRODUCTBYSTOREIDREQUEST = _descriptor.Descriptor(
  name='GetAdjustProductByStoreIDRequest',
  full_name='warehouse.GetAdjustProductByStoreIDRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='store_id', full_name='warehouse.GetAdjustProductByStoreIDRequest.store_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='warehouse.GetAdjustProductByStoreIDRequest.limit', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='warehouse.GetAdjustProductByStoreIDRequest.offset', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='warehouse.GetAdjustProductByStoreIDRequest.include_total', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='warehouse.GetAdjustProductByStoreIDRequest.adjust_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search', full_name='warehouse.GetAdjustProductByStoreIDRequest.search', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='search_fields', full_name='warehouse.GetAdjustProductByStoreIDRequest.search_fields', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='warehouse.GetAdjustProductByStoreIDRequest.category_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.GetAdjustProductByStoreIDRequest.lan', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order_by', full_name='warehouse.GetAdjustProductByStoreIDRequest.order_by', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='warehouse.GetAdjustProductByStoreIDRequest.sort', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3490,
  serialized_end=3751,
)


_CREATEADJUSTPRODUCTUINT = _descriptor.Descriptor(
  name='CreateAdjustProductUint',
  full_name='warehouse.CreateAdjustProductUint',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.CreateAdjustProductUint.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='warehouse.CreateAdjustProductUint.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='scope_id', full_name='warehouse.CreateAdjustProductUint.scope_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='warehouse.CreateAdjustProductUint.name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.CreateAdjustProductUint.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated', full_name='warehouse.CreateAdjustProductUint.updated', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rate', full_name='warehouse.CreateAdjustProductUint.rate', index=6,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default', full_name='warehouse.CreateAdjustProductUint.default', index=7,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='warehouse.CreateAdjustProductUint.order', index=8,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='purchase', full_name='warehouse.CreateAdjustProductUint.purchase', index=9,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales', full_name='warehouse.CreateAdjustProductUint.sales', index=10,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stocktake', full_name='warehouse.CreateAdjustProductUint.stocktake', index=11,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom', full_name='warehouse.CreateAdjustProductUint.bom', index=12,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='default_stocktake', full_name='warehouse.CreateAdjustProductUint.default_stocktake', index=13,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='transfer', full_name='warehouse.CreateAdjustProductUint.transfer', index=14,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='warehouse.CreateAdjustProductUint.tax_rate', index=15,
      number=17, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='warehouse.CreateAdjustProductUint.tax_price', index=16,
      number=18, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='warehouse.CreateAdjustProductUint.cost_price', index=17,
      number=19, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3754,
  serialized_end=4087,
)


_CREATEADJUSTPRODUCT = _descriptor.Descriptor(
  name='CreateAdjustProduct',
  full_name='warehouse.CreateAdjustProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='product_id', full_name='warehouse.CreateAdjustProduct.product_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='loss_report_order', full_name='warehouse.CreateAdjustProduct.loss_report_order', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='warehouse.CreateAdjustProduct.product_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.CreateAdjustProduct.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='model_name', full_name='warehouse.CreateAdjustProduct.model_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='storage_type', full_name='warehouse.CreateAdjustProduct.storage_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_category_id', full_name='warehouse.CreateAdjustProduct.product_category_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='units', full_name='warehouse.CreateAdjustProduct.units', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='barcode', full_name='warehouse.CreateAdjustProduct.barcode', index=8,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='real_inventory_qty', full_name='warehouse.CreateAdjustProduct.real_inventory_qty', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='warehouse.CreateAdjustProduct.price', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='currency', full_name='warehouse.CreateAdjustProduct.currency', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4090,
  serialized_end=4402,
)


_GETADJUSTPRODUCTBYSTOREIDRESPONSE = _descriptor.Descriptor(
  name='GetAdjustProductByStoreIDResponse',
  full_name='warehouse.GetAdjustProductByStoreIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.GetAdjustProductByStoreIDResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.GetAdjustProductByStoreIDResponse.total', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='inventory_unchanged_rows', full_name='warehouse.GetAdjustProductByStoreIDResponse.inventory_unchanged_rows', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4405,
  serialized_end=4567,
)


_CREATEDADJUSTPRODUCT = _descriptor.Descriptor(
  name='CreatedAdjustProduct',
  full_name='warehouse.CreatedAdjustProduct',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.CreatedAdjustProduct.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='warehouse.CreatedAdjustProduct.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='warehouse.CreatedAdjustProduct.unit_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='warehouse.CreatedAdjustProduct.quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='warehouse.CreatedAdjustProduct.reason_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='warehouse.CreatedAdjustProduct.position_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='skuRemark', full_name='warehouse.CreatedAdjustProduct.skuRemark', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_rate', full_name='warehouse.CreatedAdjustProduct.tax_rate', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_price', full_name='warehouse.CreatedAdjustProduct.tax_price', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='amount', full_name='warehouse.CreatedAdjustProduct.amount', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tax_amount', full_name='warehouse.CreatedAdjustProduct.tax_amount', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_price', full_name='warehouse.CreatedAdjustProduct.cost_price', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_price', full_name='warehouse.CreatedAdjustProduct.sales_price', index=12,
      number=13, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sales_amount', full_name='warehouse.CreatedAdjustProduct.sales_amount', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4570,
  serialized_end=4878,
)


_CREATEDADJUSTREQUEST = _descriptor.Descriptor(
  name='CreatedAdjustRequest',
  full_name='warehouse.CreatedAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_store', full_name='warehouse.CreatedAdjustRequest.adjust_store', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='warehouse.CreatedAdjustRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='warehouse.CreatedAdjustRequest.reason_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='warehouse.CreatedAdjustRequest.remark', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='warehouse.CreatedAdjustRequest.request_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='warehouse.CreatedAdjustRequest.adjust_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.CreatedAdjustRequest.branch_type', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='warehouse.CreatedAdjustRequest.position_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='source', full_name='warehouse.CreatedAdjustRequest.source', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='warehouse.CreatedAdjustRequest.attachments', index=9,
      number=11, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4881,
  serialized_end=5185,
)


_ATTACHMENTS = _descriptor.Descriptor(
  name='Attachments',
  full_name='warehouse.Attachments',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse.Attachments.type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='url', full_name='warehouse.Attachments.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5187,
  serialized_end=5227,
)


_UPDATEADJUSTREQUEST = _descriptor.Descriptor(
  name='UpdateAdjustRequest',
  full_name='warehouse.UpdateAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='warehouse.UpdateAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='warehouse.UpdateAdjustRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='warehouse.UpdateAdjustRequest.remark', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='warehouse.UpdateAdjustRequest.reason_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='warehouse.UpdateAdjustRequest.adjust_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.UpdateAdjustRequest.branch_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='attachments', full_name='warehouse.UpdateAdjustRequest.attachments', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='warehouse.UpdateAdjustRequest.position_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5230,
  serialized_end=5494,
)


_DELETEADJUSTREQUEST = _descriptor.Descriptor(
  name='DeleteAdjustRequest',
  full_name='warehouse.DeleteAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='warehouse.DeleteAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5496,
  serialized_end=5536,
)


_DELETEADJUSTRESPONSE = _descriptor.Descriptor(
  name='DeleteAdjustResponse',
  full_name='warehouse.DeleteAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.DeleteAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5538,
  serialized_end=5576,
)


_DELETEADJUSTPRODUCTREQUEST = _descriptor.Descriptor(
  name='DeleteAdjustProductRequest',
  full_name='warehouse.DeleteAdjustProductRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='warehouse.DeleteAdjustProductRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ids', full_name='warehouse.DeleteAdjustProductRequest.ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5578,
  serialized_end=5638,
)


_DELETEADJUSTPRODUCTRESPONSE = _descriptor.Descriptor(
  name='DeleteAdjustProductResponse',
  full_name='warehouse.DeleteAdjustProductResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.DeleteAdjustProductResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5640,
  serialized_end=5685,
)


_GETADJUSTBICOLLECTREQUEST = _descriptor.Descriptor(
  name='GetAdjustBiCollectRequest',
  full_name='warehouse.GetAdjustBiCollectRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='warehouse.GetAdjustBiCollectRequest.category_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.GetAdjustBiCollectRequest.product_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='warehouse.GetAdjustBiCollectRequest.start_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='warehouse.GetAdjustBiCollectRequest.end_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='warehouse.GetAdjustBiCollectRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='warehouse.GetAdjustBiCollectRequest.offset', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='warehouse.GetAdjustBiCollectRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='warehouse.GetAdjustBiCollectRequest.store_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='warehouse.GetAdjustBiCollectRequest.bom_product_id', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='warehouse.GetAdjustBiCollectRequest.period_symbol', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='warehouse.GetAdjustBiCollectRequest.order', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='warehouse.GetAdjustBiCollectRequest.sort', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.GetAdjustBiCollectRequest.code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='warehouse.GetAdjustBiCollectRequest.reason_type', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='warehouse.GetAdjustBiCollectRequest.is_wms_store', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.GetAdjustBiCollectRequest.branch_type', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.GetAdjustBiCollectRequest.lan', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hour_offset', full_name='warehouse.GetAdjustBiCollectRequest.hour_offset', index=17,
      number=18, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='warehouse.GetAdjustBiCollectRequest.position_ids', index=18,
      number=19, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5688,
  serialized_end=6136,
)


_ADJUSTBICOLLECTRESPONSE = _descriptor.Descriptor(
  name='AdjustBiCollectResponse',
  full_name='warehouse.AdjustBiCollectResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='warehouse.AdjustBiCollectResponse.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='warehouse.AdjustBiCollectResponse.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='warehouse.AdjustBiCollectResponse.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='warehouse.AdjustBiCollectResponse.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='warehouse.AdjustBiCollectResponse.category_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='warehouse.AdjustBiCollectResponse.category_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='warehouse.AdjustBiCollectResponse.product_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='warehouse.AdjustBiCollectResponse.product_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.AdjustBiCollectResponse.product_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='warehouse.AdjustBiCollectResponse.quantity', index=9,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='warehouse.AdjustBiCollectResponse.store_code', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='warehouse.AdjustBiCollectResponse.store_id', index=11,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='warehouse.AdjustBiCollectResponse.store_name', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='warehouse.AdjustBiCollectResponse.unit_id', index=13,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='warehouse.AdjustBiCollectResponse.unit_name', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='warehouse.AdjustBiCollectResponse.reason_type', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_name', full_name='warehouse.AdjustBiCollectResponse.reason_name', index=16,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='warehouse.AdjustBiCollectResponse.bom_product_id', index=17,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_code', full_name='warehouse.AdjustBiCollectResponse.bom_product_code', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_name', full_name='warehouse.AdjustBiCollectResponse.bom_product_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_id', full_name='warehouse.AdjustBiCollectResponse.bom_unit_id', index=20,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_code', full_name='warehouse.AdjustBiCollectResponse.bom_unit_code', index=21,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_name', full_name='warehouse.AdjustBiCollectResponse.bom_unit_name', index=22,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_id', full_name='warehouse.AdjustBiCollectResponse.bom_accounting_unit_id', index=23,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_code', full_name='warehouse.AdjustBiCollectResponse.bom_accounting_unit_code', index=24,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_name', full_name='warehouse.AdjustBiCollectResponse.bom_accounting_unit_name', index=25,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='warehouse.AdjustBiCollectResponse.qty', index=26,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='warehouse.AdjustBiCollectResponse.price', index=27,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_qty', full_name='warehouse.AdjustBiCollectResponse.accounting_qty', index=28,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost', full_name='warehouse.AdjustBiCollectResponse.cost', index=29,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='period_symbol', full_name='warehouse.AdjustBiCollectResponse.period_symbol', index=30,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='warehouse.AdjustBiCollectResponse.position_id', index=31,
      number=33, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='warehouse.AdjustBiCollectResponse.position_code', index=32,
      number=34, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='warehouse.AdjustBiCollectResponse.position_name', index=33,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='warehouse.AdjustBiCollectResponse.adjust_date', index=34,
      number=36, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6139,
  serialized_end=6984,
)


_AD_TOTAL = _descriptor.Descriptor(
  name='AD_total',
  full_name='warehouse.AD_total',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='count', full_name='warehouse.AD_total.count', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_quantity', full_name='warehouse.AD_total.sum_quantity', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_accounting_quantity', full_name='warehouse.AD_total.sum_accounting_quantity', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_qty', full_name='warehouse.AD_total.sum_qty', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sum_accounting_qty', full_name='warehouse.AD_total.sum_accounting_qty', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6986,
  serialized_end=7111,
)


_GETADJUSTBICOLLECTRESPONSE = _descriptor.Descriptor(
  name='GetAdjustBiCollectResponse',
  full_name='warehouse.GetAdjustBiCollectResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.GetAdjustBiCollectResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.GetAdjustBiCollectResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7113,
  serialized_end=7227,
)


_CANCELADJUSTREQUEST = _descriptor.Descriptor(
  name='CancelAdjustRequest',
  full_name='warehouse.CancelAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='warehouse.CancelAdjustRequest.adjust_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7229,
  serialized_end=7269,
)


_CANCELADJUSTRESPONSE = _descriptor.Descriptor(
  name='CancelAdjustResponse',
  full_name='warehouse.CancelAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.CancelAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7271,
  serialized_end=7309,
)


_GETADJUSTCOLLECTDETAILEDREQUEST = _descriptor.Descriptor(
  name='GetAdjustCollectDetailedRequest',
  full_name='warehouse.GetAdjustCollectDetailedRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='category_ids', full_name='warehouse.GetAdjustCollectDetailedRequest.category_ids', index=0,
      number=1, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.GetAdjustCollectDetailedRequest.product_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='warehouse.GetAdjustCollectDetailedRequest.start_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='warehouse.GetAdjustCollectDetailedRequest.end_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='warehouse.GetAdjustCollectDetailedRequest.limit', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='warehouse.GetAdjustCollectDetailedRequest.offset', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='warehouse.GetAdjustCollectDetailedRequest.include_total', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='warehouse.GetAdjustCollectDetailedRequest.store_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='warehouse.GetAdjustCollectDetailedRequest.bom_product_id', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='warehouse.GetAdjustCollectDetailedRequest.order', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='warehouse.GetAdjustCollectDetailedRequest.sort', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.GetAdjustCollectDetailedRequest.code', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='warehouse.GetAdjustCollectDetailedRequest.is_wms_store', index=12,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.GetAdjustCollectDetailedRequest.branch_type', index=13,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.GetAdjustCollectDetailedRequest.lan', index=14,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='warehouse.GetAdjustCollectDetailedRequest.position_ids', index=15,
      number=18, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='warehouse.GetAdjustCollectDetailedRequest.reason_type', index=16,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7312,
  serialized_end=7722,
)


_ADJUSTCOLLECTDETAILED = _descriptor.Descriptor(
  name='AdjustCollectDetailed',
  full_name='warehouse.AdjustCollectDetailed',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='warehouse.AdjustCollectDetailed.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='warehouse.AdjustCollectDetailed.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='warehouse.AdjustCollectDetailed.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_code', full_name='warehouse.AdjustCollectDetailed.adjust_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='warehouse.AdjustCollectDetailed.adjust_date', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_id', full_name='warehouse.AdjustCollectDetailed.adjust_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='warehouse.AdjustCollectDetailed.category_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='warehouse.AdjustCollectDetailed.category_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='warehouse.AdjustCollectDetailed.category_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.AdjustCollectDetailed.id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='warehouse.AdjustCollectDetailed.product_code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='warehouse.AdjustCollectDetailed.product_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.AdjustCollectDetailed.product_name', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='warehouse.AdjustCollectDetailed.quantity', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='warehouse.AdjustCollectDetailed.reason_type', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_name', full_name='warehouse.AdjustCollectDetailed.reason_name', index=15,
      number=33, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='warehouse.AdjustCollectDetailed.store_code', index=16,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='warehouse.AdjustCollectDetailed.store_id', index=17,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='warehouse.AdjustCollectDetailed.store_name', index=18,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='warehouse.AdjustCollectDetailed.unit_id', index=19,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='warehouse.AdjustCollectDetailed.unit_name', index=20,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='warehouse.AdjustCollectDetailed.bom_product_id', index=21,
      number=21, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_code', full_name='warehouse.AdjustCollectDetailed.bom_product_code', index=22,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_name', full_name='warehouse.AdjustCollectDetailed.bom_product_name', index=23,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_id', full_name='warehouse.AdjustCollectDetailed.bom_unit_id', index=24,
      number=24, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_code', full_name='warehouse.AdjustCollectDetailed.bom_unit_code', index=25,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_name', full_name='warehouse.AdjustCollectDetailed.bom_unit_name', index=26,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_id', full_name='warehouse.AdjustCollectDetailed.bom_accounting_unit_id', index=27,
      number=27, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_code', full_name='warehouse.AdjustCollectDetailed.bom_accounting_unit_code', index=28,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_name', full_name='warehouse.AdjustCollectDetailed.bom_accounting_unit_name', index=29,
      number=29, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='warehouse.AdjustCollectDetailed.qty', index=30,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='warehouse.AdjustCollectDetailed.price', index=31,
      number=31, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_qty', full_name='warehouse.AdjustCollectDetailed.accounting_qty', index=32,
      number=32, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost', full_name='warehouse.AdjustCollectDetailed.cost', index=33,
      number=34, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.AdjustCollectDetailed.branch_type', index=34,
      number=35, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='warehouse.AdjustCollectDetailed.remark', index=35,
      number=36, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='warehouse.AdjustCollectDetailed.position_id', index=36,
      number=37, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='warehouse.AdjustCollectDetailed.position_code', index=37,
      number=38, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='warehouse.AdjustCollectDetailed.position_name', index=38,
      number=39, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7725,
  serialized_end=8634,
)


_GETADJUSTCOLLECTDETAILEDRESPONSE = _descriptor.Descriptor(
  name='GetAdjustCollectDetailedResponse',
  full_name='warehouse.GetAdjustCollectDetailedResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.GetAdjustCollectDetailedResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.GetAdjustCollectDetailedResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8636,
  serialized_end=8754,
)


_AUTOCLOSECREATEDADJUSTRESPONSE = _descriptor.Descriptor(
  name='AutoCloseCreatedAdjustResponse',
  full_name='warehouse.AutoCloseCreatedAdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.AutoCloseCreatedAdjustResponse.result', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8756,
  serialized_end=8804,
)


_AUTOCLOSECREATEDADJUSTREQUEST = _descriptor.Descriptor(
  name='AutoCloseCreatedAdjustRequest',
  full_name='warehouse.AutoCloseCreatedAdjustRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='warehouse.AutoCloseCreatedAdjustRequest.adjust_date', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='warehouse.AutoCloseCreatedAdjustRequest.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='warehouse.AutoCloseCreatedAdjustRequest.user_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8806,
  serialized_end=8895,
)


_GETMATERIALADJUSTDATAREQUEST = _descriptor.Descriptor(
  name='GetMaterialAdjustDataRequest',
  full_name='warehouse.GetMaterialAdjustDataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='warehouse.GetMaterialAdjustDataRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='warehouse.GetMaterialAdjustDataRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='warehouse.GetMaterialAdjustDataRequest.store_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_ids', full_name='warehouse.GetMaterialAdjustDataRequest.material_ids', index=3,
      number=4, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='warehouse.GetMaterialAdjustDataRequest.limit', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='warehouse.GetMaterialAdjustDataRequest.offset', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='is_wms_store', full_name='warehouse.GetMaterialAdjustDataRequest.is_wms_store', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.GetMaterialAdjustDataRequest.lan', index=7,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='warehouse.GetMaterialAdjustDataRequest.include_total', index=8,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8898,
  serialized_end=9152,
)


_GETMATERIALADJUSTDATARESPONSE = _descriptor.Descriptor(
  name='GetMaterialAdjustDataResponse',
  full_name='warehouse.GetMaterialAdjustDataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.GetMaterialAdjustDataResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.GetMaterialAdjustDataResponse.total', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9154,
  serialized_end=9262,
)


_ADJUSTRESPONSE = _descriptor.Descriptor(
  name='AdjustResponse',
  full_name='warehouse.AdjustResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounting_quantity', full_name='warehouse.AdjustResponse.accounting_quantity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_id', full_name='warehouse.AdjustResponse.accounting_unit_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_unit_name', full_name='warehouse.AdjustResponse.accounting_unit_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_code', full_name='warehouse.AdjustResponse.category_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_id', full_name='warehouse.AdjustResponse.category_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='category_name', full_name='warehouse.AdjustResponse.category_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='warehouse.AdjustResponse.product_code', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='warehouse.AdjustResponse.product_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.AdjustResponse.product_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='warehouse.AdjustResponse.quantity', index=9,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='warehouse.AdjustResponse.store_code', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='warehouse.AdjustResponse.store_id', index=11,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='warehouse.AdjustResponse.store_name', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='warehouse.AdjustResponse.unit_id', index=13,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='warehouse.AdjustResponse.unit_name', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='warehouse.AdjustResponse.reason_type', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_id', full_name='warehouse.AdjustResponse.bom_product_id', index=16,
      number=18, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_code', full_name='warehouse.AdjustResponse.bom_product_code', index=17,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_product_name', full_name='warehouse.AdjustResponse.bom_product_name', index=18,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='qty', full_name='warehouse.AdjustResponse.qty', index=19,
      number=21, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_id', full_name='warehouse.AdjustResponse.bom_unit_id', index=20,
      number=22, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_code', full_name='warehouse.AdjustResponse.bom_unit_code', index=21,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_unit_name', full_name='warehouse.AdjustResponse.bom_unit_name', index=22,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_id', full_name='warehouse.AdjustResponse.bom_accounting_unit_id', index=23,
      number=25, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_code', full_name='warehouse.AdjustResponse.bom_accounting_unit_code', index=24,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='bom_accounting_unit_name', full_name='warehouse.AdjustResponse.bom_accounting_unit_name', index=25,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='accounting_qty', full_name='warehouse.AdjustResponse.accounting_qty', index=26,
      number=28, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='warehouse.AdjustResponse.price', index=27,
      number=29, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost', full_name='warehouse.AdjustResponse.cost', index=28,
      number=30, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='warehouse.AdjustResponse.adjust_date', index=29,
      number=31, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9265,
  serialized_end=9962,
)


_CREATEDADJUSTPRODUCTBYCODE = _descriptor.Descriptor(
  name='CreatedAdjustProductByCode',
  full_name='warehouse.CreatedAdjustProductByCode',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.CreatedAdjustProductByCode.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='warehouse.CreatedAdjustProductByCode.product_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_code', full_name='warehouse.CreatedAdjustProductByCode.unit_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='warehouse.CreatedAdjustProductByCode.quantity', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='warehouse.CreatedAdjustProductByCode.reason_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='skuRemark', full_name='warehouse.CreatedAdjustProductByCode.skuRemark', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9965,
  serialized_end=10126,
)


_SKUREMARK_TAG = _descriptor.Descriptor(
  name='Tag',
  full_name='warehouse.SkuRemark.Tag',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.SkuRemark.Tag.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.SkuRemark.Tag.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='warehouse.SkuRemark.Tag.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10224,
  serialized_end=10269,
)

_SKUREMARK = _descriptor.Descriptor(
  name='SkuRemark',
  full_name='warehouse.SkuRemark',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='warehouse.SkuRemark.name', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='values', full_name='warehouse.SkuRemark.values', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[_SKUREMARK_TAG, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10129,
  serialized_end=10269,
)


_CREATEDADJUSTBYCODEREQUEST = _descriptor.Descriptor(
  name='CreatedAdjustByCodeRequest',
  full_name='warehouse.CreatedAdjustByCodeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='adjust_store', full_name='warehouse.CreatedAdjustByCodeRequest.adjust_store', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='warehouse.CreatedAdjustByCodeRequest.products', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reason_type', full_name='warehouse.CreatedAdjustByCodeRequest.reason_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='warehouse.CreatedAdjustByCodeRequest.remark', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='warehouse.CreatedAdjustByCodeRequest.request_id', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='adjust_date', full_name='warehouse.CreatedAdjustByCodeRequest.adjust_date', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='lan', full_name='warehouse.CreatedAdjustByCodeRequest.lan', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10272,
  serialized_end=10470,
)


_ADJUSTPOSITIONPRODUCTS = _descriptor.Descriptor(
  name='AdjustPositionProducts',
  full_name='warehouse.AdjustPositionProducts',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='position_id', full_name='warehouse.AdjustPositionProducts.position_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='warehouse.AdjustPositionProducts.position_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='warehouse.AdjustPositionProducts.position_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='products', full_name='warehouse.AdjustPositionProducts.products', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.AdjustPositionProducts.total', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=10473,
  serialized_end=10623,
)

_CREATEADJUSTREQUEST.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEADJUSTRESPONSE.fields_by_name['request_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTREQUEST.fields_by_name['status'].enum_type = _GETADJUSTREQUEST_STATUS
_GETADJUSTREQUEST_STATUS.containing_type = _GETADJUSTREQUEST
_ADJUST.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUST.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUST.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_GETADJUSTRESPONSE.fields_by_name['rows'].message_type = _ADJUST
_ADJUSTPRODUCT.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUSTPRODUCT.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUSTPRODUCT.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUSTPRODUCT.fields_by_name['units'].message_type = _CREATEADJUSTPRODUCTUINT
_GETADJUSTPRODUCTRESPONSE.fields_by_name['rows'].message_type = _ADJUSTPRODUCT
_GETADJUSTPRODUCTRESPONSE.fields_by_name['position_rows'].message_type = _ADJUSTPOSITIONPRODUCTS
_GETADJUSTPRODUCTBYSTOREIDREQUEST.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEADJUSTPRODUCT.fields_by_name['units'].message_type = _CREATEADJUSTPRODUCTUINT
_GETADJUSTPRODUCTBYSTOREIDRESPONSE.fields_by_name['rows'].message_type = _CREATEADJUSTPRODUCT
_GETADJUSTPRODUCTBYSTOREIDRESPONSE.fields_by_name['inventory_unchanged_rows'].message_type = _CREATEADJUSTPRODUCT
_CREATEDADJUSTPRODUCT.fields_by_name['skuRemark'].message_type = _SKUREMARK
_CREATEDADJUSTREQUEST.fields_by_name['products'].message_type = _CREATEDADJUSTPRODUCT
_CREATEDADJUSTREQUEST.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEDADJUSTREQUEST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_UPDATEADJUSTREQUEST.fields_by_name['products'].message_type = _CREATEDADJUSTPRODUCT
_UPDATEADJUSTREQUEST.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATEADJUSTREQUEST.fields_by_name['attachments'].message_type = _ATTACHMENTS
_GETADJUSTBICOLLECTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTBICOLLECTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUSTBICOLLECTRESPONSE.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTBICOLLECTRESPONSE.fields_by_name['rows'].message_type = _ADJUSTBICOLLECTRESPONSE
_GETADJUSTBICOLLECTRESPONSE.fields_by_name['total'].message_type = _AD_TOTAL
_GETADJUSTCOLLECTDETAILEDREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTCOLLECTDETAILEDREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_ADJUSTCOLLECTDETAILED.fields_by_name['adjust_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETADJUSTCOLLECTDETAILEDRESPONSE.fields_by_name['rows'].message_type = _ADJUSTCOLLECTDETAILED
_GETADJUSTCOLLECTDETAILEDRESPONSE.fields_by_name['total'].message_type = _AD_TOTAL
_GETMATERIALADJUSTDATAREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETMATERIALADJUSTDATAREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETMATERIALADJUSTDATARESPONSE.fields_by_name['rows'].message_type = _ADJUSTRESPONSE
_GETMATERIALADJUSTDATARESPONSE.fields_by_name['total'].message_type = _AD_TOTAL
_CREATEDADJUSTPRODUCTBYCODE.fields_by_name['skuRemark'].message_type = _SKUREMARK
_SKUREMARK_TAG.containing_type = _SKUREMARK
_SKUREMARK.fields_by_name['name'].message_type = _SKUREMARK_TAG
_SKUREMARK.fields_by_name['values'].message_type = _SKUREMARK_TAG
_CREATEDADJUSTBYCODEREQUEST.fields_by_name['products'].message_type = _CREATEDADJUSTPRODUCTBYCODE
_ADJUSTPOSITIONPRODUCTS.fields_by_name['products'].message_type = _ADJUSTPRODUCT
DESCRIPTOR.message_types_by_name['Pong'] = _PONG
DESCRIPTOR.message_types_by_name['CreateAdjustRequest'] = _CREATEADJUSTREQUEST
DESCRIPTOR.message_types_by_name['CreateAdjustResponse'] = _CREATEADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustRequest'] = _GETADJUSTREQUEST
DESCRIPTOR.message_types_by_name['Adjust'] = _ADJUST
DESCRIPTOR.message_types_by_name['GetAdjustResponse'] = _GETADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustProductRequest'] = _GETADJUSTPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['AdjustProduct'] = _ADJUSTPRODUCT
DESCRIPTOR.message_types_by_name['GetAdjustProductResponse'] = _GETADJUSTPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustByIDRequest'] = _GETADJUSTBYIDREQUEST
DESCRIPTOR.message_types_by_name['ConfirmAdjustRequest'] = _CONFIRMADJUSTREQUEST
DESCRIPTOR.message_types_by_name['ConfirmAdjustResponse'] = _CONFIRMADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['SubmitAdjustRequest'] = _SUBMITADJUSTREQUEST
DESCRIPTOR.message_types_by_name['SubmitAdjustResponse'] = _SUBMITADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['ApproveAdjustRequest'] = _APPROVEADJUSTREQUEST
DESCRIPTOR.message_types_by_name['ApproveAdjustResponse'] = _APPROVEADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['RejectAdjustRequest'] = _REJECTADJUSTREQUEST
DESCRIPTOR.message_types_by_name['RejectAdjustResponse'] = _REJECTADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustProductByStoreIDRequest'] = _GETADJUSTPRODUCTBYSTOREIDREQUEST
DESCRIPTOR.message_types_by_name['CreateAdjustProductUint'] = _CREATEADJUSTPRODUCTUINT
DESCRIPTOR.message_types_by_name['CreateAdjustProduct'] = _CREATEADJUSTPRODUCT
DESCRIPTOR.message_types_by_name['GetAdjustProductByStoreIDResponse'] = _GETADJUSTPRODUCTBYSTOREIDRESPONSE
DESCRIPTOR.message_types_by_name['CreatedAdjustProduct'] = _CREATEDADJUSTPRODUCT
DESCRIPTOR.message_types_by_name['CreatedAdjustRequest'] = _CREATEDADJUSTREQUEST
DESCRIPTOR.message_types_by_name['Attachments'] = _ATTACHMENTS
DESCRIPTOR.message_types_by_name['UpdateAdjustRequest'] = _UPDATEADJUSTREQUEST
DESCRIPTOR.message_types_by_name['DeleteAdjustRequest'] = _DELETEADJUSTREQUEST
DESCRIPTOR.message_types_by_name['DeleteAdjustResponse'] = _DELETEADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['DeleteAdjustProductRequest'] = _DELETEADJUSTPRODUCTREQUEST
DESCRIPTOR.message_types_by_name['DeleteAdjustProductResponse'] = _DELETEADJUSTPRODUCTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustBiCollectRequest'] = _GETADJUSTBICOLLECTREQUEST
DESCRIPTOR.message_types_by_name['AdjustBiCollectResponse'] = _ADJUSTBICOLLECTRESPONSE
DESCRIPTOR.message_types_by_name['AD_total'] = _AD_TOTAL
DESCRIPTOR.message_types_by_name['GetAdjustBiCollectResponse'] = _GETADJUSTBICOLLECTRESPONSE
DESCRIPTOR.message_types_by_name['CancelAdjustRequest'] = _CANCELADJUSTREQUEST
DESCRIPTOR.message_types_by_name['CancelAdjustResponse'] = _CANCELADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['GetAdjustCollectDetailedRequest'] = _GETADJUSTCOLLECTDETAILEDREQUEST
DESCRIPTOR.message_types_by_name['AdjustCollectDetailed'] = _ADJUSTCOLLECTDETAILED
DESCRIPTOR.message_types_by_name['GetAdjustCollectDetailedResponse'] = _GETADJUSTCOLLECTDETAILEDRESPONSE
DESCRIPTOR.message_types_by_name['AutoCloseCreatedAdjustResponse'] = _AUTOCLOSECREATEDADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['AutoCloseCreatedAdjustRequest'] = _AUTOCLOSECREATEDADJUSTREQUEST
DESCRIPTOR.message_types_by_name['GetMaterialAdjustDataRequest'] = _GETMATERIALADJUSTDATAREQUEST
DESCRIPTOR.message_types_by_name['GetMaterialAdjustDataResponse'] = _GETMATERIALADJUSTDATARESPONSE
DESCRIPTOR.message_types_by_name['AdjustResponse'] = _ADJUSTRESPONSE
DESCRIPTOR.message_types_by_name['CreatedAdjustProductByCode'] = _CREATEDADJUSTPRODUCTBYCODE
DESCRIPTOR.message_types_by_name['SkuRemark'] = _SKUREMARK
DESCRIPTOR.message_types_by_name['CreatedAdjustByCodeRequest'] = _CREATEDADJUSTBYCODEREQUEST
DESCRIPTOR.message_types_by_name['AdjustPositionProducts'] = _ADJUSTPOSITIONPRODUCTS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Pong = _reflection.GeneratedProtocolMessageType('Pong', (_message.Message,), dict(
  DESCRIPTOR = _PONG,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.Pong)
  ))
_sym_db.RegisterMessage(Pong)

CreateAdjustRequest = _reflection.GeneratedProtocolMessageType('CreateAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEADJUSTREQUEST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CreateAdjustRequest)
  ))
_sym_db.RegisterMessage(CreateAdjustRequest)

CreateAdjustResponse = _reflection.GeneratedProtocolMessageType('CreateAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEADJUSTRESPONSE,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CreateAdjustResponse)
  ))
_sym_db.RegisterMessage(CreateAdjustResponse)

GetAdjustRequest = _reflection.GeneratedProtocolMessageType('GetAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTREQUEST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetAdjustRequest)
  ))
_sym_db.RegisterMessage(GetAdjustRequest)

Adjust = _reflection.GeneratedProtocolMessageType('Adjust', (_message.Message,), dict(
  DESCRIPTOR = _ADJUST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.Adjust)
  ))
_sym_db.RegisterMessage(Adjust)

GetAdjustResponse = _reflection.GeneratedProtocolMessageType('GetAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTRESPONSE,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetAdjustResponse)
  ))
_sym_db.RegisterMessage(GetAdjustResponse)

GetAdjustProductRequest = _reflection.GeneratedProtocolMessageType('GetAdjustProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTPRODUCTREQUEST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetAdjustProductRequest)
  ))
_sym_db.RegisterMessage(GetAdjustProductRequest)

AdjustProduct = _reflection.GeneratedProtocolMessageType('AdjustProduct', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTPRODUCT,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.AdjustProduct)
  ))
_sym_db.RegisterMessage(AdjustProduct)

GetAdjustProductResponse = _reflection.GeneratedProtocolMessageType('GetAdjustProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTPRODUCTRESPONSE,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetAdjustProductResponse)
  ))
_sym_db.RegisterMessage(GetAdjustProductResponse)

GetAdjustByIDRequest = _reflection.GeneratedProtocolMessageType('GetAdjustByIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTBYIDREQUEST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetAdjustByIDRequest)
  ))
_sym_db.RegisterMessage(GetAdjustByIDRequest)

ConfirmAdjustRequest = _reflection.GeneratedProtocolMessageType('ConfirmAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMADJUSTREQUEST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ConfirmAdjustRequest)
  ))
_sym_db.RegisterMessage(ConfirmAdjustRequest)

ConfirmAdjustResponse = _reflection.GeneratedProtocolMessageType('ConfirmAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _CONFIRMADJUSTRESPONSE,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ConfirmAdjustResponse)
  ))
_sym_db.RegisterMessage(ConfirmAdjustResponse)

SubmitAdjustRequest = _reflection.GeneratedProtocolMessageType('SubmitAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITADJUSTREQUEST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.SubmitAdjustRequest)
  ))
_sym_db.RegisterMessage(SubmitAdjustRequest)

SubmitAdjustResponse = _reflection.GeneratedProtocolMessageType('SubmitAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _SUBMITADJUSTRESPONSE,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.SubmitAdjustResponse)
  ))
_sym_db.RegisterMessage(SubmitAdjustResponse)

ApproveAdjustRequest = _reflection.GeneratedProtocolMessageType('ApproveAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _APPROVEADJUSTREQUEST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ApproveAdjustRequest)
  ))
_sym_db.RegisterMessage(ApproveAdjustRequest)

ApproveAdjustResponse = _reflection.GeneratedProtocolMessageType('ApproveAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _APPROVEADJUSTRESPONSE,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ApproveAdjustResponse)
  ))
_sym_db.RegisterMessage(ApproveAdjustResponse)

RejectAdjustRequest = _reflection.GeneratedProtocolMessageType('RejectAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _REJECTADJUSTREQUEST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.RejectAdjustRequest)
  ))
_sym_db.RegisterMessage(RejectAdjustRequest)

RejectAdjustResponse = _reflection.GeneratedProtocolMessageType('RejectAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _REJECTADJUSTRESPONSE,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.RejectAdjustResponse)
  ))
_sym_db.RegisterMessage(RejectAdjustResponse)

GetAdjustProductByStoreIDRequest = _reflection.GeneratedProtocolMessageType('GetAdjustProductByStoreIDRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTPRODUCTBYSTOREIDREQUEST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetAdjustProductByStoreIDRequest)
  ))
_sym_db.RegisterMessage(GetAdjustProductByStoreIDRequest)

CreateAdjustProductUint = _reflection.GeneratedProtocolMessageType('CreateAdjustProductUint', (_message.Message,), dict(
  DESCRIPTOR = _CREATEADJUSTPRODUCTUINT,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CreateAdjustProductUint)
  ))
_sym_db.RegisterMessage(CreateAdjustProductUint)

CreateAdjustProduct = _reflection.GeneratedProtocolMessageType('CreateAdjustProduct', (_message.Message,), dict(
  DESCRIPTOR = _CREATEADJUSTPRODUCT,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CreateAdjustProduct)
  ))
_sym_db.RegisterMessage(CreateAdjustProduct)

GetAdjustProductByStoreIDResponse = _reflection.GeneratedProtocolMessageType('GetAdjustProductByStoreIDResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTPRODUCTBYSTOREIDRESPONSE,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetAdjustProductByStoreIDResponse)
  ))
_sym_db.RegisterMessage(GetAdjustProductByStoreIDResponse)

CreatedAdjustProduct = _reflection.GeneratedProtocolMessageType('CreatedAdjustProduct', (_message.Message,), dict(
  DESCRIPTOR = _CREATEDADJUSTPRODUCT,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CreatedAdjustProduct)
  ))
_sym_db.RegisterMessage(CreatedAdjustProduct)

CreatedAdjustRequest = _reflection.GeneratedProtocolMessageType('CreatedAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEDADJUSTREQUEST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CreatedAdjustRequest)
  ))
_sym_db.RegisterMessage(CreatedAdjustRequest)

Attachments = _reflection.GeneratedProtocolMessageType('Attachments', (_message.Message,), dict(
  DESCRIPTOR = _ATTACHMENTS,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.Attachments)
  ))
_sym_db.RegisterMessage(Attachments)

UpdateAdjustRequest = _reflection.GeneratedProtocolMessageType('UpdateAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEADJUSTREQUEST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.UpdateAdjustRequest)
  ))
_sym_db.RegisterMessage(UpdateAdjustRequest)

DeleteAdjustRequest = _reflection.GeneratedProtocolMessageType('DeleteAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEADJUSTREQUEST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.DeleteAdjustRequest)
  ))
_sym_db.RegisterMessage(DeleteAdjustRequest)

DeleteAdjustResponse = _reflection.GeneratedProtocolMessageType('DeleteAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETEADJUSTRESPONSE,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.DeleteAdjustResponse)
  ))
_sym_db.RegisterMessage(DeleteAdjustResponse)

DeleteAdjustProductRequest = _reflection.GeneratedProtocolMessageType('DeleteAdjustProductRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEADJUSTPRODUCTREQUEST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.DeleteAdjustProductRequest)
  ))
_sym_db.RegisterMessage(DeleteAdjustProductRequest)

DeleteAdjustProductResponse = _reflection.GeneratedProtocolMessageType('DeleteAdjustProductResponse', (_message.Message,), dict(
  DESCRIPTOR = _DELETEADJUSTPRODUCTRESPONSE,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.DeleteAdjustProductResponse)
  ))
_sym_db.RegisterMessage(DeleteAdjustProductResponse)

GetAdjustBiCollectRequest = _reflection.GeneratedProtocolMessageType('GetAdjustBiCollectRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTBICOLLECTREQUEST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetAdjustBiCollectRequest)
  ))
_sym_db.RegisterMessage(GetAdjustBiCollectRequest)

AdjustBiCollectResponse = _reflection.GeneratedProtocolMessageType('AdjustBiCollectResponse', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTBICOLLECTRESPONSE,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.AdjustBiCollectResponse)
  ))
_sym_db.RegisterMessage(AdjustBiCollectResponse)

AD_total = _reflection.GeneratedProtocolMessageType('AD_total', (_message.Message,), dict(
  DESCRIPTOR = _AD_TOTAL,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.AD_total)
  ))
_sym_db.RegisterMessage(AD_total)

GetAdjustBiCollectResponse = _reflection.GeneratedProtocolMessageType('GetAdjustBiCollectResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTBICOLLECTRESPONSE,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetAdjustBiCollectResponse)
  ))
_sym_db.RegisterMessage(GetAdjustBiCollectResponse)

CancelAdjustRequest = _reflection.GeneratedProtocolMessageType('CancelAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _CANCELADJUSTREQUEST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CancelAdjustRequest)
  ))
_sym_db.RegisterMessage(CancelAdjustRequest)

CancelAdjustResponse = _reflection.GeneratedProtocolMessageType('CancelAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _CANCELADJUSTRESPONSE,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CancelAdjustResponse)
  ))
_sym_db.RegisterMessage(CancelAdjustResponse)

GetAdjustCollectDetailedRequest = _reflection.GeneratedProtocolMessageType('GetAdjustCollectDetailedRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTCOLLECTDETAILEDREQUEST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetAdjustCollectDetailedRequest)
  ))
_sym_db.RegisterMessage(GetAdjustCollectDetailedRequest)

AdjustCollectDetailed = _reflection.GeneratedProtocolMessageType('AdjustCollectDetailed', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTCOLLECTDETAILED,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.AdjustCollectDetailed)
  ))
_sym_db.RegisterMessage(AdjustCollectDetailed)

GetAdjustCollectDetailedResponse = _reflection.GeneratedProtocolMessageType('GetAdjustCollectDetailedResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETADJUSTCOLLECTDETAILEDRESPONSE,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetAdjustCollectDetailedResponse)
  ))
_sym_db.RegisterMessage(GetAdjustCollectDetailedResponse)

AutoCloseCreatedAdjustResponse = _reflection.GeneratedProtocolMessageType('AutoCloseCreatedAdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _AUTOCLOSECREATEDADJUSTRESPONSE,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.AutoCloseCreatedAdjustResponse)
  ))
_sym_db.RegisterMessage(AutoCloseCreatedAdjustResponse)

AutoCloseCreatedAdjustRequest = _reflection.GeneratedProtocolMessageType('AutoCloseCreatedAdjustRequest', (_message.Message,), dict(
  DESCRIPTOR = _AUTOCLOSECREATEDADJUSTREQUEST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.AutoCloseCreatedAdjustRequest)
  ))
_sym_db.RegisterMessage(AutoCloseCreatedAdjustRequest)

GetMaterialAdjustDataRequest = _reflection.GeneratedProtocolMessageType('GetMaterialAdjustDataRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETMATERIALADJUSTDATAREQUEST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetMaterialAdjustDataRequest)
  ))
_sym_db.RegisterMessage(GetMaterialAdjustDataRequest)

GetMaterialAdjustDataResponse = _reflection.GeneratedProtocolMessageType('GetMaterialAdjustDataResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETMATERIALADJUSTDATARESPONSE,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetMaterialAdjustDataResponse)
  ))
_sym_db.RegisterMessage(GetMaterialAdjustDataResponse)

AdjustResponse = _reflection.GeneratedProtocolMessageType('AdjustResponse', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTRESPONSE,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.AdjustResponse)
  ))
_sym_db.RegisterMessage(AdjustResponse)

CreatedAdjustProductByCode = _reflection.GeneratedProtocolMessageType('CreatedAdjustProductByCode', (_message.Message,), dict(
  DESCRIPTOR = _CREATEDADJUSTPRODUCTBYCODE,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CreatedAdjustProductByCode)
  ))
_sym_db.RegisterMessage(CreatedAdjustProductByCode)

SkuRemark = _reflection.GeneratedProtocolMessageType('SkuRemark', (_message.Message,), dict(

  Tag = _reflection.GeneratedProtocolMessageType('Tag', (_message.Message,), dict(
    DESCRIPTOR = _SKUREMARK_TAG,
    __module__ = 'warehouse.adjust_pb2'
    # @@protoc_insertion_point(class_scope:warehouse.SkuRemark.Tag)
    ))
  ,
  DESCRIPTOR = _SKUREMARK,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.SkuRemark)
  ))
_sym_db.RegisterMessage(SkuRemark)
_sym_db.RegisterMessage(SkuRemark.Tag)

CreatedAdjustByCodeRequest = _reflection.GeneratedProtocolMessageType('CreatedAdjustByCodeRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEDADJUSTBYCODEREQUEST,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CreatedAdjustByCodeRequest)
  ))
_sym_db.RegisterMessage(CreatedAdjustByCodeRequest)

AdjustPositionProducts = _reflection.GeneratedProtocolMessageType('AdjustPositionProducts', (_message.Message,), dict(
  DESCRIPTOR = _ADJUSTPOSITIONPRODUCTS,
  __module__ = 'warehouse.adjust_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.AdjustPositionProducts)
  ))
_sym_db.RegisterMessage(AdjustPositionProducts)



_WAREHOUSEADJUST = _descriptor.ServiceDescriptor(
  name='WarehouseAdjust',
  full_name='warehouse.WarehouseAdjust',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=10626,
  serialized_end=13381,
  methods=[
  _descriptor.MethodDescriptor(
    name='Ping',
    full_name='warehouse.WarehouseAdjust.Ping',
    index=0,
    containing_service=None,
    input_type=google_dot_protobuf_dot_empty__pb2._EMPTY,
    output_type=_PONG,
    serialized_options=_b('\202\323\344\223\002\037\022\035/api/v2/supply/warehouse/ping'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjust',
    full_name='warehouse.WarehouseAdjust.GetAdjust',
    index=1,
    containing_service=None,
    input_type=_GETADJUSTREQUEST,
    output_type=_GETADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\002!\022\037/api/v2/supply/warehouse/adjust'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustProduct',
    full_name='warehouse.WarehouseAdjust.GetAdjustProduct',
    index=2,
    containing_service=None,
    input_type=_GETADJUSTPRODUCTREQUEST,
    output_type=_GETADJUSTPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\0025\0223/api/v2/supply/warehouse/adjust/{adjust_id}/product'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustByID',
    full_name='warehouse.WarehouseAdjust.GetAdjustByID',
    index=3,
    containing_service=None,
    input_type=_GETADJUSTBYIDREQUEST,
    output_type=_ADJUST,
    serialized_options=_b('\202\323\344\223\002-\022+/api/v2/supply/warehouse/adjust/{adjust_id}'),
  ),
  _descriptor.MethodDescriptor(
    name='ConfirmAdjust',
    full_name='warehouse.WarehouseAdjust.ConfirmAdjust',
    index=4,
    containing_service=None,
    input_type=_CONFIRMADJUSTREQUEST,
    output_type=_CONFIRMADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\0323/api/v2/supply/warehouse/adjust/{adjust_id}/confirm:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='SubmitAdjust',
    full_name='warehouse.WarehouseAdjust.SubmitAdjust',
    index=5,
    containing_service=None,
    input_type=_SUBMITADJUSTREQUEST,
    output_type=_SUBMITADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\0322/api/v2/supply/warehouse/adjust/{adjust_id}/submit:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ApproveAdjust',
    full_name='warehouse.WarehouseAdjust.ApproveAdjust',
    index=6,
    containing_service=None,
    input_type=_APPROVEADJUSTREQUEST,
    output_type=_APPROVEADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\0028\0323/api/v2/supply/warehouse/adjust/{adjust_id}/approve:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='RejectAdjust',
    full_name='warehouse.WarehouseAdjust.RejectAdjust',
    index=7,
    containing_service=None,
    input_type=_REJECTADJUSTREQUEST,
    output_type=_REJECTADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\0322/api/v2/supply/warehouse/adjust/{adjust_id}/reject:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustProductByStoreID',
    full_name='warehouse.WarehouseAdjust.GetAdjustProductByStoreID',
    index=8,
    containing_service=None,
    input_type=_GETADJUSTPRODUCTBYSTOREIDREQUEST,
    output_type=_GETADJUSTPRODUCTBYSTOREIDRESPONSE,
    serialized_options=_b('\202\323\344\223\0020\022./api/v2/supply/warehouse/adjust/store/products'),
  ),
  _descriptor.MethodDescriptor(
    name='CreatedAdjust',
    full_name='warehouse.WarehouseAdjust.CreatedAdjust',
    index=9,
    containing_service=None,
    input_type=_CREATEDADJUSTREQUEST,
    output_type=_ADJUST,
    serialized_options=_b('\202\323\344\223\002+\"&/api/v2/supply/warehouse/adjust/create:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateAdjust',
    full_name='warehouse.WarehouseAdjust.UpdateAdjust',
    index=10,
    containing_service=None,
    input_type=_UPDATEADJUSTREQUEST,
    output_type=_ADJUST,
    serialized_options=_b('\202\323\344\223\0027\0322/api/v2/supply/warehouse/adjust/{adjust_id}/update:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteAdjust',
    full_name='warehouse.WarehouseAdjust.DeleteAdjust',
    index=11,
    containing_service=None,
    input_type=_DELETEADJUSTREQUEST,
    output_type=_DELETEADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\0322/api/v2/supply/warehouse/adjust/{adjust_id}/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='DeleteAdjustProduct',
    full_name='warehouse.WarehouseAdjust.DeleteAdjustProduct',
    index=12,
    containing_service=None,
    input_type=_DELETEADJUSTPRODUCTREQUEST,
    output_type=_DELETEADJUSTPRODUCTRESPONSE,
    serialized_options=_b('\202\323\344\223\002?\032:/api/v2/supply/warehouse/adjust/product/{adjust_id}/delete:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustBiCollect',
    full_name='warehouse.WarehouseAdjust.GetAdjustBiCollect',
    index=13,
    containing_service=None,
    input_type=_GETADJUSTBICOLLECTREQUEST,
    output_type=_GETADJUSTBICOLLECTRESPONSE,
    serialized_options=_b('\202\323\344\223\002,\022*/api/v2/supply/warehouse/adjust/bi/collect'),
  ),
  _descriptor.MethodDescriptor(
    name='CancelAdjust',
    full_name='warehouse.WarehouseAdjust.CancelAdjust',
    index=14,
    containing_service=None,
    input_type=_CANCELADJUSTREQUEST,
    output_type=_CANCELADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\0027\0322/api/v2/supply/warehouse/adjust/auto_create/cancel:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetAdjustCollectDetailed',
    full_name='warehouse.WarehouseAdjust.GetAdjustCollectDetailed',
    index=15,
    containing_service=None,
    input_type=_GETADJUSTCOLLECTDETAILEDREQUEST,
    output_type=_GETADJUSTCOLLECTDETAILEDRESPONSE,
    serialized_options=_b('\202\323\344\223\002-\022+/api/v2/supply/warehouse/adjust/bi/detailed'),
  ),
  _descriptor.MethodDescriptor(
    name='AutoCloseCreatedAdjust',
    full_name='warehouse.WarehouseAdjust.AutoCloseCreatedAdjust',
    index=16,
    containing_service=None,
    input_type=_AUTOCLOSECREATEDADJUSTREQUEST,
    output_type=_AUTOCLOSECREATEDADJUSTRESPONSE,
    serialized_options=_b('\202\323\344\223\002,\022*/api/v2/supply/warehouse/adjust/close/auto'),
  ),
  _descriptor.MethodDescriptor(
    name='GetMaterialAdjustData',
    full_name='warehouse.WarehouseAdjust.GetMaterialAdjustData',
    index=17,
    containing_service=None,
    input_type=_GETMATERIALADJUSTDATAREQUEST,
    output_type=_GETMATERIALADJUSTDATARESPONSE,
    serialized_options=_b('\202\323\344\223\002/\022-/api/v2/supply/warehouse/adjust/material/cost'),
  ),
  _descriptor.MethodDescriptor(
    name='CreatedAdjustByCode',
    full_name='warehouse.WarehouseAdjust.CreatedAdjustByCode',
    index=18,
    containing_service=None,
    input_type=_CREATEDADJUSTBYCODEREQUEST,
    output_type=_ADJUST,
    serialized_options=_b('\202\323\344\223\002(\"#/api/v2/supply/warehouse/adjust/pos:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_WAREHOUSEADJUST)

DESCRIPTOR.services_by_name['WarehouseAdjust'] = _WAREHOUSEADJUST

# @@protoc_insertion_point(module_scope)
