syntax = "proto3";
package warehouse;
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// 门店退货相关服务
service WarehouseReturnService {

    // 创建退货单
    rpc CreateReturn (CreateReturnRequest) returns (CreateReturnResponse) {
        option (google.api.http) = {
            post:"/api/v2/supply/warehouse/returns"
            body:"*"
        };
    }

    // 查询可退货商品
    rpc GetValidProduct (GetValidProductRequest) returns (ValidProduct) {
        option (google.api.http) = {
            get:"/api/v2/supply/warehouse/returns/valid_product"
        };
    }

    // 查询退货单
    rpc ListReturn (ListReturnRequest) returns (ListReturnResponse) {
        option (google.api.http) = {
            get:"/api/v2/supply/warehouse/returns"
        };
    }


    // 根据id查询退货单详情
    rpc GetReturnById (GetReturnByIdRequest) returns (Returns) {
        option (google.api.http) = {
            get:"/api/v2/supply/warehouse/returns/{id}"
        };
    }

    // 根据id查询退货单商品详情
    // 查询退货单详情时需调用
    rpc GetReturnProductById (GetReturnProductByIdRequest) returns (GetReturnProductByIdResponse) {
        option (google.api.http) = {
            get:"/api/v2/supply/warehouse/returns/{id}/product"
        };
    }

    // 提交退货单
    rpc SubmitReturn (SubmitReturnRequest) returns (ReturnCommonResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/warehouse/returns/{id}/submit"
            body:"*"
        };
    }

    // 驳回退货单
    rpc RejectReturn (RejectReturnRequest) returns (ReturnCommonResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/warehouse/returns/{id}/reject"
            body:"*"
        };
    }

    // 审核退货单
    rpc ApproveReturn (ApproveReturnRequest) returns (ReturnCommonResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/warehouse/returns/{id}/approve"
            body:"*"
        };
    }

    // 确认退货
    rpc ConfirmReturn (ConfirmReturnRequest) returns (ReturnCommonResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/warehouse/returns/{id}/confirm"
            body:"*"
        };
    }

    // 确认提货
    rpc DeliveryReturn (DeliveryReturnRequest) returns (ReturnCommonResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/warehouse/returns/{id}/delivery"
            body:"*"
        };
    }

    // 更新退货单
    rpc UpdateReturn (UpdateReturnRequest) returns (ReturnCommonResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/warehouse/returns/{id}/update"
            body:"*"
        };
    }

    // 删除新建的退货单
    rpc DeleteReturn (DeleteReturnRequest) returns (ReturnCommonResponse) {
        option (google.api.http) = {
            put:"/api/v2/supply/warehouse/returns/{id}/delete"
            body:"*"
        };
    }

    // 校验按收货原单退货是否超退
    rpc CheckReturnAvailableByrec (CheckReturnAvailableRequest) returns (ReturnCommonResponse) {
        option (google.api.http) = {
            post:"/api/v2/supply/warehouse/returns/check"
            body:"*"
        };
    }
}
message Returns {
    // id
    uint64 id = 1;
    // 退货单单号
    string code = 2;
    // 退货门店
    uint64 return_by = 3;
    // 退货编号
    string return_number = 4;
    // 退货单状态
    string status = 5;
    // 审核人
    uint64 review_by = 6;
    // 提货状态
    // 原extends
    bool is_returned = 7;
    // 退货提货单单单号
    string return_delivery_number = 8;
    // 退货日期
    google.protobuf.Timestamp return_date = 9;
    // 预计退货提货时间
    google.protobuf.Timestamp return_delivery_date = 10;
    // 退货原因
    string return_reason = 11;
    // 退货类型: 原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD
    string type = 12;
    // 退货方类型： store/ warehouse/ machining
    string sub_type = 13;
    // 备注
    string remark = 14;
    // 预留门店号
    string store_secondary_id = 15;
    // 创建时间
    google.protobuf.Timestamp created_at = 16;
    // 更新时间
    google.protobuf.Timestamp updated_at = 17;
    // 创建人id
    uint64 created_by = 18;
    // 更新人id
    uint64 updated_by = 19;
    // 退货接受方 门店id
    uint64 return_to = 20;
    // // 库存引擎调用
    // enum inventoryStatus {
    //     SENT = 0; // 已发送
    //     SENT_SUC = 2; //调用成功
    //     SENT_FAIL = 3; //调用失败
    // }
    string inventory_status = 21;
    // 库存id预留字段
    uint64 inventory_req_id = 22;
    // 商户id
    uint64 partner_id = 23;
    // 收否直送
    bool is_direct = 24;
    // 驳回原因
    string reject_reason = 25;
    // 附件
    repeated ReturnAttachments attachments = 26;
    // 创建人
    string created_name = 27;
    // 更新人
    string updated_name = 28;
    // 商品数量
    uint64 product_nums = 29;
    // 单据物流来源类型:PUR 直送/ NMD 配送
    string logistics_type = 31;
    // 提货方式
    string trans_type = 35;
    // 来源
    uint64 source_id = 37;
    string source_code = 38;
    // 实际提货日期
    google.protobuf.Timestamp delivery_date = 39;
    uint64 request_id = 40;
    // 接收方名字
    string return_to_code = 41;
    string return_to_name = 42;
    // 配送方名字
    string return_by_code = 43;
    string return_by_name = 44;
    // 加盟商信息
    uint64 franchisee_id = 55;
    string franchisee_code = 56;
    string franchisee_name = 57;
    string payment_way = 58;
    string receive_code = 59;
    string delivery_code = 60;
    // 退款单号
    uint64 refund_id = 50;
    string refund_code = 51;
    string warehouse_type = 52;
        // 是否长效
    string long_effect = 53;
}

message ValidProduct {
    // id
    uint64 id = 1;
    // 商品编号
    string product_code = 2;
    // 商品名称
    string product_name = 3;
    // 规格
    string spec = 4;
    // 单位
    Unit unit = 5;
}

message Unit {
    // id
    uint64 id = 1;
    // 单位数量
    int32 quantity = 2;
    //
    double rate = 3;
}

message ProductDetail {
    // id
    uint64 id = 1;
    // 退货单id
    uint64 return_id = 2;
    // 退货门店
    uint64 return_by = 3;

    // 商品编码
    string product_code = 5;
    // 商品id
    uint64 product_id = 6;
    // 商品名称
    string product_name = 7;
    // 数量
    double quantity = 8;

    // 单位id
    uint64 accounting_unit_id = 9;
    // 单位名称
    string accounting_unit_name = 10;
    // 单位规格
    string accounting_unit_spec = 11;
    // 单位id
    uint64 unit_id = 12;
    // 单位名称
    string unit_name = 13;
    // 单位换算比例
    double unit_rate = 14;
    // 单位规格
    string unit_spec = 15;
    // 确认退货数量
    double accounting_confirmed_quantity = 16;
    //
    double accounting_quantity = 17;
    //
    double accounting_returned_quantity = 18;
    // 确认收货数量
    double confirmed_quantity = 19;
    // 退货数量
    double returned_quantity = 20;

    // 状态
    bool is_confirmed = 21;
    // 退货日期
    google.protobuf.Timestamp return_date = 22;
    // 接收方
    uint64 return_to = 23;
    // 订货日期
    // google.protobuf.Timestamp demand_date = 23;

    // 库存引擎调用
    // enum inventoryStatus {
    //     SENT = 1; // 已发送
    //     SENT_SUC = 2; //调用成功
    //     SENT_FAIL = 3; //调用失败
    // }
    string inventory_status = 24;
    // 库存id预留字段
    uint64 inventory_req_id = 25;
    // 创建时间
    google.protobuf.Timestamp created_at = 26;
    // 创建人id
    uint64 created_by = 27;
    // 更新日期
    google.protobuf.Timestamp updated_at = 28;
    // 更新人id
    uint64 updated_by = 29;
    // 商户id
    uint64 partner_id = 30;
    // 创建人
    string created_name = 31;
    // 更新人
    string updated_name = 32;
    string storage_type = 35;
    // 未税单价
    double price = 36;
    // 税率
    float tax_rate = 37;
    // 含税单价
    double price_tax = 38;
    // 采购总价
    double sum_price = 39;
    // 附件
    repeated ReturnAttachments attachments = 45;
}

message ReturnProduct {
    // 退货单商品主键id
    uint64 id = 1;
    // 商品主档id
    uint64 product_id = 2;
    // 数量
    double quantity = 3;
    // 收货数量
    double confirmed_quantity = 4;
    //
    string product_code = 5;
    string product_name = 6;
    // 订货单位id
    uint64 unit_id = 7;
    string unit_name = 8;
    string unit_spec = 9;
    // 单位换算比例
    float unit_rate = 10;
    // 税相关
    float tax_rate = 11;
    float price = 12;
    float price_tax = 13;
    // 退货接收方 - vendor_id/warehouse_id
    uint64 return_to = 14;
    string logistics_type = 15;
    // 附件
    repeated ReturnAttachments attachments = 16;
}

message CreateReturnRequest {
    // 退货方id
    uint64 return_by = 1;
    // 单据物流来源类型:PUR 直送/ NMD 配送
    string logistics_type = 2;
    // 退货类型: 原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD
    string type = 3;
    // 退货方类型： store/ warehouse/ machining
    string sub_type = 4;
    // 交货日期
    google.protobuf.Timestamp return_delivery_date = 5;
    // 退货单来源id
    uint64 source_id = 6;
    string source_code = 7;
    // 退货原因
    string return_reason = 8;
    // 备注
    string remark = 9;
    // 附件
    repeated ReturnAttachments attachments = 10;
    // 唯一请求号，保证不重复请求
    uint64 request_id = 11;
    // 商品
    repeated ReturnProduct products = 12;
    // 语言
    string lan = 20;
}

message CreateReturnResponse {
    repeated uint64 return_id = 1;
    bool payload = 2;
}

message CheckReturnAvailableRequest {
    // 退货方id
    uint64 return_by = 1;
    // 交货日期
    google.protobuf.Timestamp return_delivery_date = 2;
    // 退货类型: 原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD
    string type = 3;
    // 退货方类型： store/ warehouse/ machining
    string sub_type = 4;
    // 退货原因
    string return_reason = 5;
    // 备注
    string remark = 6;
    // 商品
    repeated ReturnProduct products = 7;
    // 退货接收方 - vendor_id/warehouse_id
    uint64 return_to = 9;
    // 附件
    repeated ReturnAttachments attachments = 10;
    // 单据物流来源类型:PUR 直送/ NMD 配送
    string logistics_type = 11;
    // 退货单来源id
    uint64 source_id = 12;
    string source_code = 13;
    // 退货单id
    uint64 return_id = 14;
    string lan = 15; 
}

message GetValidProductRequest {
    // id
    uint64 id = 1;
    // 分页大小
    int32 limit = 2;
    // 跳过行数
    int32 offset = 3;
    // 返回总条数
    bool include_total = 4;
    // 商品名称
    string product_name = 5;
    string lan = 6;

}

message GetValidProductResponse {
    bool payload = 1;
}

message ListReturnRequest {
    // 退货方id
    repeated uint64 return_by = 1;
    // 订单状态
    repeated string status = 2;
    // 退货单号
    string code = 3;
    // 来源id
    uint64 source_id = 4;
    string source_code = 5;
    // 单据物流来源类型:PUR 直送/ NMD 配送
    string logistics_type = 6;
    // 退货类型: 原单退货 BO/ 非原单退货 NBO/ 库存调整 IAD/ 对账调整 CAD/ 收货差异 BD
    string type = 7;
    // 退货方类型： store/ warehouse/ machining
    string sub_type = 8;
    // 商品ids
    repeated uint64 product_ids = 9;
    // 退货开始日期
    google.protobuf.Timestamp return_date_from = 10;
    // 退货结束日期
    google.protobuf.Timestamp return_date_to = 11;
    // 提货开始日期
    google.protobuf.Timestamp delivery_date_from = 12;
    // 提货结束日期
    google.protobuf.Timestamp delivery_date_to = 13;
    // 分页大小
    int32 limit = 20;
    // 跳过行数
    int32 offset = 21;
    // 排序
    string order = 22;
    // 排序字段
    string sort = 23;
    
    string lan = 24;
    // 退货方接收方
    repeated uint64 return_to = 25;

}

message ListReturnResponse {
    repeated Returns rows = 1;
    uint64 total = 2;
}

message GetReturnByIdRequest {
    // 退货单id
    uint64 id = 1;
    string lan = 2;
}


message GetReturnProductByIdRequest {
    // id
    uint64 id = 1;
    // 分页大小
    int32 limit = 2;
    // 跳过行数
    int32 offset = 3;
    // 排序字段
    string sort = 4;
    // 排序顺序
    string order = 5;
    string lan = 6;

}

message GetReturnProductByIdResponse {
    repeated ProductDetail rows = 1;
    uint64 total = 2;
}

message SubmitReturnRequest {
    // id
    uint64 id = 1;
    string lan = 2;
}


message RejectReturnRequest {
    // id
    uint64 id = 1;
    // 驳回原因
    string reject_reason = 2;
    string lan = 3;
}


message ApproveReturnRequest {
    // id
    uint64 id = 1;
    // 提货方式
    string trans_type = 2;
    string lan = 3;
    // 退回仓库类型(Scrap/ Normal)
    string warehouse_type = 4;
}


message ConfirmReturnRequest {
    // id
    uint64 id = 1;
    string lan = 2;
}


message DeliveryReturnRequest {
    uint64 id = 1;
    string lan = 2;
}


message UpdateReturnRequest {
    //
    uint64 id = 1;
    //
    repeated ReturnProduct products = 2;
    // 退货原因
    string return_reason = 3;
    // 备注
    string remark = 4;
    // 退货交货时间
    google.protobuf.Timestamp return_delivery_date = 5;
    // 供应商/配送中心
    uint64 return_to = 6;
    // 附件
    repeated ReturnAttachments attachments = 7;
    string lan = 8;
    // 单据物流来源类型:PUR 直送/ NMD 配送
    string logistics_type = 9;
}


message DeleteReturnRequest {
    //
    uint64 id = 1;
    string lan = 2;
}


message ReturnCommonResponse {
    bool payload = 1;
    // wrapper.Status status = 2;
}

message ReturnAttachments {
    string type = 1;
    string url = 2;
}