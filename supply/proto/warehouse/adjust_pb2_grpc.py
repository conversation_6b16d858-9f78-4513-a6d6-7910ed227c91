# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from warehouse import adjust_pb2 as warehouse_dot_adjust__pb2


class WarehouseAdjustStub(object):
  """Adjust 每日损耗服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.Ping = channel.unary_unary(
        '/warehouse.WarehouseAdjust/Ping',
        request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
        response_deserializer=warehouse_dot_adjust__pb2.Pong.FromString,
        )
    self.GetAdjust = channel.unary_unary(
        '/warehouse.WarehouseAdjust/GetAdjust',
        request_serializer=warehouse_dot_adjust__pb2.GetAdjustRequest.SerializeToString,
        response_deserializer=warehouse_dot_adjust__pb2.GetAdjustResponse.FromString,
        )
    self.GetAdjustProduct = channel.unary_unary(
        '/warehouse.WarehouseAdjust/GetAdjustProduct',
        request_serializer=warehouse_dot_adjust__pb2.GetAdjustProductRequest.SerializeToString,
        response_deserializer=warehouse_dot_adjust__pb2.GetAdjustProductResponse.FromString,
        )
    self.GetAdjustByID = channel.unary_unary(
        '/warehouse.WarehouseAdjust/GetAdjustByID',
        request_serializer=warehouse_dot_adjust__pb2.GetAdjustByIDRequest.SerializeToString,
        response_deserializer=warehouse_dot_adjust__pb2.Adjust.FromString,
        )
    self.ConfirmAdjust = channel.unary_unary(
        '/warehouse.WarehouseAdjust/ConfirmAdjust',
        request_serializer=warehouse_dot_adjust__pb2.ConfirmAdjustRequest.SerializeToString,
        response_deserializer=warehouse_dot_adjust__pb2.ConfirmAdjustResponse.FromString,
        )
    self.SubmitAdjust = channel.unary_unary(
        '/warehouse.WarehouseAdjust/SubmitAdjust',
        request_serializer=warehouse_dot_adjust__pb2.SubmitAdjustRequest.SerializeToString,
        response_deserializer=warehouse_dot_adjust__pb2.SubmitAdjustResponse.FromString,
        )
    self.ApproveAdjust = channel.unary_unary(
        '/warehouse.WarehouseAdjust/ApproveAdjust',
        request_serializer=warehouse_dot_adjust__pb2.ApproveAdjustRequest.SerializeToString,
        response_deserializer=warehouse_dot_adjust__pb2.ApproveAdjustResponse.FromString,
        )
    self.RejectAdjust = channel.unary_unary(
        '/warehouse.WarehouseAdjust/RejectAdjust',
        request_serializer=warehouse_dot_adjust__pb2.RejectAdjustRequest.SerializeToString,
        response_deserializer=warehouse_dot_adjust__pb2.RejectAdjustResponse.FromString,
        )
    self.GetAdjustProductByStoreID = channel.unary_unary(
        '/warehouse.WarehouseAdjust/GetAdjustProductByStoreID',
        request_serializer=warehouse_dot_adjust__pb2.GetAdjustProductByStoreIDRequest.SerializeToString,
        response_deserializer=warehouse_dot_adjust__pb2.GetAdjustProductByStoreIDResponse.FromString,
        )
    self.CreatedAdjust = channel.unary_unary(
        '/warehouse.WarehouseAdjust/CreatedAdjust',
        request_serializer=warehouse_dot_adjust__pb2.CreatedAdjustRequest.SerializeToString,
        response_deserializer=warehouse_dot_adjust__pb2.Adjust.FromString,
        )
    self.UpdateAdjust = channel.unary_unary(
        '/warehouse.WarehouseAdjust/UpdateAdjust',
        request_serializer=warehouse_dot_adjust__pb2.UpdateAdjustRequest.SerializeToString,
        response_deserializer=warehouse_dot_adjust__pb2.Adjust.FromString,
        )
    self.DeleteAdjust = channel.unary_unary(
        '/warehouse.WarehouseAdjust/DeleteAdjust',
        request_serializer=warehouse_dot_adjust__pb2.DeleteAdjustRequest.SerializeToString,
        response_deserializer=warehouse_dot_adjust__pb2.DeleteAdjustResponse.FromString,
        )
    self.DeleteAdjustProduct = channel.unary_unary(
        '/warehouse.WarehouseAdjust/DeleteAdjustProduct',
        request_serializer=warehouse_dot_adjust__pb2.DeleteAdjustProductRequest.SerializeToString,
        response_deserializer=warehouse_dot_adjust__pb2.DeleteAdjustProductResponse.FromString,
        )
    self.GetAdjustBiCollect = channel.unary_unary(
        '/warehouse.WarehouseAdjust/GetAdjustBiCollect',
        request_serializer=warehouse_dot_adjust__pb2.GetAdjustBiCollectRequest.SerializeToString,
        response_deserializer=warehouse_dot_adjust__pb2.GetAdjustBiCollectResponse.FromString,
        )
    self.CancelAdjust = channel.unary_unary(
        '/warehouse.WarehouseAdjust/CancelAdjust',
        request_serializer=warehouse_dot_adjust__pb2.CancelAdjustRequest.SerializeToString,
        response_deserializer=warehouse_dot_adjust__pb2.CancelAdjustResponse.FromString,
        )
    self.GetAdjustCollectDetailed = channel.unary_unary(
        '/warehouse.WarehouseAdjust/GetAdjustCollectDetailed',
        request_serializer=warehouse_dot_adjust__pb2.GetAdjustCollectDetailedRequest.SerializeToString,
        response_deserializer=warehouse_dot_adjust__pb2.GetAdjustCollectDetailedResponse.FromString,
        )
    self.AutoCloseCreatedAdjust = channel.unary_unary(
        '/warehouse.WarehouseAdjust/AutoCloseCreatedAdjust',
        request_serializer=warehouse_dot_adjust__pb2.AutoCloseCreatedAdjustRequest.SerializeToString,
        response_deserializer=warehouse_dot_adjust__pb2.AutoCloseCreatedAdjustResponse.FromString,
        )
    self.GetMaterialAdjustData = channel.unary_unary(
        '/warehouse.WarehouseAdjust/GetMaterialAdjustData',
        request_serializer=warehouse_dot_adjust__pb2.GetMaterialAdjustDataRequest.SerializeToString,
        response_deserializer=warehouse_dot_adjust__pb2.GetMaterialAdjustDataResponse.FromString,
        )
    self.CreatedAdjustByCode = channel.unary_unary(
        '/warehouse.WarehouseAdjust/CreatedAdjustByCode',
        request_serializer=warehouse_dot_adjust__pb2.CreatedAdjustByCodeRequest.SerializeToString,
        response_deserializer=warehouse_dot_adjust__pb2.Adjust.FromString,
        )


class WarehouseAdjustServicer(object):
  """Adjust 每日损耗服务
  """

  def Ping(self, request, context):
    """Ping 健康检查
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetAdjust(self, request, context):
    """GetAdjust查询每日损耗表9
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetAdjustProduct(self, request, context):
    """GetAdjustProduct每日损耗表商品查询10
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetAdjustByID(self, request, context):
    """GetAdjustByID查询一个每日损耗表11
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ConfirmAdjust(self, request, context):
    """ConfirmAdjust确认一个每日损耗表12
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SubmitAdjust(self, request, context):
    """提交一个报废单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ApproveAdjust(self, request, context):
    """审核一个报废单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RejectAdjust(self, request, context):
    """驳回一个报废单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetAdjustProductByStoreID(self, request, context):
    """GetAdjustProductByStoreID查询门店可损耗商品13
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreatedAdjust(self, request, context):
    """CreatedAdjust手动创建一个每日损耗表14
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateAdjust(self, request, context):
    """UpdateAdjust更新一个每日损耗表15
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteAdjust(self, request, context):
    """DeleteAdjust删除一个每日损耗表16
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DeleteAdjustProduct(self, request, context):
    """DeleteAdjustProduct删除每日损耗表的商品17
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetAdjustBiCollect(self, request, context):
    """GetAdjustBiCollect每日损耗表汇总报表18
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CancelAdjust(self, request, context):
    """CancelAdjust 取消每日损耗表20
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetAdjustCollectDetailed(self, request, context):
    """GetAdjustCollectDetailed损耗表明细汇总报表21
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def AutoCloseCreatedAdjust(self, request, context):
    """AutoCloseCreatedAdjust自动关闭未确认的损耗表,不传参数默认前一天，传date操作date日期的单子22
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetMaterialAdjustData(self, request, context):
    """物料报废量成本报表
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def CreatedAdjustByCode(self, request, context):
    """第三方根据code创建报废
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_WarehouseAdjustServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'Ping': grpc.unary_unary_rpc_method_handler(
          servicer.Ping,
          request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
          response_serializer=warehouse_dot_adjust__pb2.Pong.SerializeToString,
      ),
      'GetAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.GetAdjust,
          request_deserializer=warehouse_dot_adjust__pb2.GetAdjustRequest.FromString,
          response_serializer=warehouse_dot_adjust__pb2.GetAdjustResponse.SerializeToString,
      ),
      'GetAdjustProduct': grpc.unary_unary_rpc_method_handler(
          servicer.GetAdjustProduct,
          request_deserializer=warehouse_dot_adjust__pb2.GetAdjustProductRequest.FromString,
          response_serializer=warehouse_dot_adjust__pb2.GetAdjustProductResponse.SerializeToString,
      ),
      'GetAdjustByID': grpc.unary_unary_rpc_method_handler(
          servicer.GetAdjustByID,
          request_deserializer=warehouse_dot_adjust__pb2.GetAdjustByIDRequest.FromString,
          response_serializer=warehouse_dot_adjust__pb2.Adjust.SerializeToString,
      ),
      'ConfirmAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.ConfirmAdjust,
          request_deserializer=warehouse_dot_adjust__pb2.ConfirmAdjustRequest.FromString,
          response_serializer=warehouse_dot_adjust__pb2.ConfirmAdjustResponse.SerializeToString,
      ),
      'SubmitAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.SubmitAdjust,
          request_deserializer=warehouse_dot_adjust__pb2.SubmitAdjustRequest.FromString,
          response_serializer=warehouse_dot_adjust__pb2.SubmitAdjustResponse.SerializeToString,
      ),
      'ApproveAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.ApproveAdjust,
          request_deserializer=warehouse_dot_adjust__pb2.ApproveAdjustRequest.FromString,
          response_serializer=warehouse_dot_adjust__pb2.ApproveAdjustResponse.SerializeToString,
      ),
      'RejectAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.RejectAdjust,
          request_deserializer=warehouse_dot_adjust__pb2.RejectAdjustRequest.FromString,
          response_serializer=warehouse_dot_adjust__pb2.RejectAdjustResponse.SerializeToString,
      ),
      'GetAdjustProductByStoreID': grpc.unary_unary_rpc_method_handler(
          servicer.GetAdjustProductByStoreID,
          request_deserializer=warehouse_dot_adjust__pb2.GetAdjustProductByStoreIDRequest.FromString,
          response_serializer=warehouse_dot_adjust__pb2.GetAdjustProductByStoreIDResponse.SerializeToString,
      ),
      'CreatedAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.CreatedAdjust,
          request_deserializer=warehouse_dot_adjust__pb2.CreatedAdjustRequest.FromString,
          response_serializer=warehouse_dot_adjust__pb2.Adjust.SerializeToString,
      ),
      'UpdateAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateAdjust,
          request_deserializer=warehouse_dot_adjust__pb2.UpdateAdjustRequest.FromString,
          response_serializer=warehouse_dot_adjust__pb2.Adjust.SerializeToString,
      ),
      'DeleteAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteAdjust,
          request_deserializer=warehouse_dot_adjust__pb2.DeleteAdjustRequest.FromString,
          response_serializer=warehouse_dot_adjust__pb2.DeleteAdjustResponse.SerializeToString,
      ),
      'DeleteAdjustProduct': grpc.unary_unary_rpc_method_handler(
          servicer.DeleteAdjustProduct,
          request_deserializer=warehouse_dot_adjust__pb2.DeleteAdjustProductRequest.FromString,
          response_serializer=warehouse_dot_adjust__pb2.DeleteAdjustProductResponse.SerializeToString,
      ),
      'GetAdjustBiCollect': grpc.unary_unary_rpc_method_handler(
          servicer.GetAdjustBiCollect,
          request_deserializer=warehouse_dot_adjust__pb2.GetAdjustBiCollectRequest.FromString,
          response_serializer=warehouse_dot_adjust__pb2.GetAdjustBiCollectResponse.SerializeToString,
      ),
      'CancelAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.CancelAdjust,
          request_deserializer=warehouse_dot_adjust__pb2.CancelAdjustRequest.FromString,
          response_serializer=warehouse_dot_adjust__pb2.CancelAdjustResponse.SerializeToString,
      ),
      'GetAdjustCollectDetailed': grpc.unary_unary_rpc_method_handler(
          servicer.GetAdjustCollectDetailed,
          request_deserializer=warehouse_dot_adjust__pb2.GetAdjustCollectDetailedRequest.FromString,
          response_serializer=warehouse_dot_adjust__pb2.GetAdjustCollectDetailedResponse.SerializeToString,
      ),
      'AutoCloseCreatedAdjust': grpc.unary_unary_rpc_method_handler(
          servicer.AutoCloseCreatedAdjust,
          request_deserializer=warehouse_dot_adjust__pb2.AutoCloseCreatedAdjustRequest.FromString,
          response_serializer=warehouse_dot_adjust__pb2.AutoCloseCreatedAdjustResponse.SerializeToString,
      ),
      'GetMaterialAdjustData': grpc.unary_unary_rpc_method_handler(
          servicer.GetMaterialAdjustData,
          request_deserializer=warehouse_dot_adjust__pb2.GetMaterialAdjustDataRequest.FromString,
          response_serializer=warehouse_dot_adjust__pb2.GetMaterialAdjustDataResponse.SerializeToString,
      ),
      'CreatedAdjustByCode': grpc.unary_unary_rpc_method_handler(
          servicer.CreatedAdjustByCode,
          request_deserializer=warehouse_dot_adjust__pb2.CreatedAdjustByCodeRequest.FromString,
          response_serializer=warehouse_dot_adjust__pb2.Adjust.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'warehouse.WarehouseAdjust', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
