# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: warehouse/production_process_receipts.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='warehouse/production_process_receipts.proto',
  package='warehouse',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n+warehouse/production_process_receipts.proto\x12\twarehouse\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xaa\x02\n&CreateProductionProcessReceiptsRequest\x12\x30\n\x0cprocess_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x18\n\x10process_store_id\x18\x02 \x01(\x04\x12\x1a\n\x12process_store_name\x18\x03 \x01(\t\x12\x1a\n\x12process_store_code\x18\x04 \x01(\t\x12\x0e\n\x06remark\x18\x05 \x01(\t\x12\x12\n\nrequest_id\x18\x06 \x01(\x04\x12-\n\x07\x64\x65tails\x18\x07 \x03(\x0b\x32\x1c.warehouse.ProductionDetails\x12)\n\x05items\x18\x08 \x03(\x0b\x32\x1a.warehouse.ProductionItems\"M\n\'CreateProductionProcessReceiptsResponse\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\"\xc7\x01\n\x16ProductionReceiptsRows\x12\x17\n\x0fproduction_rule\x18\x01 \x01(\x04\x12\x1c\n\x14production_rule_code\x18\x02 \x01(\t\x12\x1c\n\x14production_rule_name\x18\x03 \x01(\t\x12-\n\x07\x64\x65tails\x18\x04 \x03(\x0b\x32\x1c.warehouse.ProductionDetails\x12)\n\x05items\x18\x05 \x03(\x0b\x32\x1a.warehouse.ProductionItems\"\xdd\x02\n\x11ProductionDetails\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\nproduct_id\x18\x02 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x03 \x01(\t\x12\x14\n\x0cproduct_name\x18\x04 \x01(\t\x12\x0f\n\x07unit_id\x18\x05 \x01(\x04\x12\x11\n\tunit_code\x18\x06 \x01(\t\x12\x11\n\tunit_name\x18\x07 \x01(\t\x12\x0c\n\x04type\x18\x08 \x01(\t\x12\x10\n\x08quantity\x18\t \x01(\x01\x12\x17\n\x0fproduction_rule\x18\n \x01(\x04\x12\x1c\n\x14production_rule_code\x18\x0b \x01(\t\x12\x1c\n\x14production_rule_name\x18\x0c \x01(\t\x12\x18\n\x10specification_id\x18\r \x01(\x04\x12\x1a\n\x12specification_code\x18\x0e \x01(\t\x12\x1a\n\x12specification_name\x18\x0f \x01(\t\"\xbf\x02\n\x0fProductionItems\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x18\n\x10\x66rom_material_id\x18\x02 \x01(\x04\x12\x1a\n\x12\x66rom_material_code\x18\x03 \x01(\t\x12\x1a\n\x12\x66rom_material_name\x18\x04 \x01(\t\x12\x16\n\x0eto_material_id\x18\x05 \x01(\x04\x12\x18\n\x10to_material_code\x18\x06 \x01(\t\x12\x18\n\x10to_material_name\x18\x07 \x01(\t\x12\x18\n\x10theoretical_rate\x18\x08 \x01(\x01\x12\x13\n\x0b\x61\x63tual_rate\x18\t \x01(\x01\x12\x17\n\x0fproduction_rule\x18\n \x01(\x04\x12\x1c\n\x14production_rule_code\x18\x0b \x01(\t\x12\x1c\n\x14production_rule_name\x18\x0c \x01(\t\"\xf3\x01\n$ListProductionProcessReceiptsRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x19\n\x11process_store_ids\x18\x03 \x03(\x04\x12\x0c\n\x04\x63ode\x18\x04 \x01(\t\x12\x0e\n\x06status\x18\x05 \x03(\t\x12\r\n\x05limit\x18\x06 \x01(\x03\x12\x0e\n\x06offset\x18\x07 \x01(\x04\x12\x15\n\rinclude_total\x18\x08 \x01(\x08\"\xbe\x03\n\x14ProductionProcessRow\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x12\n\ncreated_by\x18\x03 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x04 \x01(\t\x12\x12\n\nupdated_by\x18\x05 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x06 \x01(\t\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06status\x18\t \x01(\t\x12\x0c\n\x04\x63ode\x18\n \x01(\t\x12\x30\n\x0cprocess_date\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x18\n\x10process_store_id\x18\x0c \x01(\x04\x12\x1a\n\x12process_store_name\x18\r \x01(\t\x12\x1a\n\x12process_store_code\x18\x0e \x01(\t\x12\x0e\n\x06remark\x18\x0f \x01(\t\x12\x12\n\nrequest_id\x18\x10 \x01(\x04\x12\x0c\n\x04type\x18\x11 \x01(\t\"e\n%ListProductionProcessReceiptsResponse\x12-\n\x04rows\x18\x01 \x03(\x0b\x32\x1f.warehouse.ProductionProcessRow\x12\r\n\x05total\x18\x02 \x01(\x04\">\n(GetProductionProcessReceiptDetailRequest\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\"\x95\x04\n)GetProductionProcessReceiptDetailResponse\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x12\n\npartner_id\x18\x02 \x01(\x04\x12\x12\n\ncreated_by\x18\x03 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x04 \x01(\t\x12\x12\n\nupdated_by\x18\x05 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x06 \x01(\t\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0f\n\x07main_id\x18\t \x01(\x04\x12\x0e\n\x06status\x18\n \x01(\t\x12\x0c\n\x04\x63ode\x18\x0b \x01(\t\x12\x30\n\x0cprocess_date\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x18\n\x10process_store_id\x18\r \x01(\x04\x12\x1a\n\x12process_store_name\x18\x0e \x01(\t\x12\x1a\n\x12process_store_code\x18\x0f \x01(\t\x12\x0e\n\x06remark\x18\x10 \x01(\t\x12\x12\n\nrequest_id\x18\x11 \x01(\x04\x12/\n\x04rows\x18\x12 \x03(\x0b\x32!.warehouse.ProductionReceiptsRows\x12\x0c\n\x04type\x18\x13 \x01(\t\"\xc4\x02\n&UpdateProductionProcessReceiptsRequest\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x30\n\x0cprocess_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x18\n\x10process_store_id\x18\x03 \x01(\x04\x12\x1a\n\x12process_store_name\x18\x04 \x01(\t\x12\x1a\n\x12process_store_code\x18\x05 \x01(\t\x12\x0e\n\x06remark\x18\x06 \x01(\t\x12-\n\x07\x64\x65tails\x18\x07 \x03(\x0b\x32\x1c.warehouse.ProductionDetails\x12)\n\x05items\x18\x08 \x03(\x0b\x32\x1a.warehouse.ProductionItems\x12\x18\n\x10receipt_rule_ids\x18\t \x03(\x04\"M\n\'UpdateProductionProcessReceiptsResponse\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\"R\n,ChangeProductionProcessReceiptsStatusRequest\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\"S\n-ChangeProductionProcessReceiptsStatusResponse\x12\x12\n\nreceipt_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\"\x8c\x02\n\x1fGetMaterialToProductRateRequest\x12\x15\n\rinclude_total\x18\x01 \x01(\x08\x12\x11\n\tstore_ids\x18\x02 \x03(\x04\x12.\n\nstart_date\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06offset\x18\x05 \x01(\r\x12\r\n\x05limit\x18\x06 \x01(\r\x12\x14\n\x0cmaterial_ids\x18\x07 \x03(\x04\x12\x13\n\x0bproduct_ids\x18\x08 \x03(\x04\x12\x17\n\x0f\x63onversion_rate\x18\t \x01(\t\"\xfb\x02\n\x18MaterialToProductRateRow\x12\x13\n\x0bmaterial_id\x18\x01 \x01(\x04\x12\x15\n\rmaterial_code\x18\x02 \x01(\t\x12\x15\n\rmaterial_name\x18\x03 \x01(\t\x12\x12\n\nproduct_id\x18\x04 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x05 \x01(\t\x12\x14\n\x0cproduct_name\x18\x06 \x01(\t\x12\x30\n\x0cprocess_date\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x10\n\x08store_id\x18\x08 \x01(\x04\x12\x12\n\nstore_name\x18\t \x01(\t\x12\x12\n\nstore_code\x18\n \x01(\t\x12\x18\n\x10theoretical_rate\x18\x0b \x01(\x01\x12\x13\n\x0b\x61\x63tual_rate\x18\x0c \x01(\x01\x12\x0c\n\x04\x63ode\x18\r \x01(\t\x12\x19\n\x11material_quantity\x18\x0e \x01(\x01\x12\x18\n\x10product_quantity\x18\x0f \x01(\x01\"d\n GetMaterialToProductRateResponse\x12\x31\n\x04rows\x18\x01 \x03(\x0b\x32#.warehouse.MaterialToProductRateRow\x12\r\n\x05total\x18\x02 \x01(\x04\"-\n\x18GetRuleByBranchIdRequest\x12\x11\n\tbranch_id\x18\x01 \x01(\x04\"/\n\x05Rules\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\";\n\x19GetRuleByBranchIdResponse\x12\x1e\n\x04rows\x18\x01 \x03(\x0b\x32\x10.warehouse.Rules2\x9f\x0b\n\"WarehouseProductionProcessReceipts\x12\xc9\x01\n\x1f\x43reateProductionProcessReceipts\x12\x31.warehouse.CreateProductionProcessReceiptsRequest\x1a\x32.warehouse.CreateProductionProcessReceiptsResponse\"?\x82\xd3\xe4\x93\x02\x39\"4/api/v2/supply/warehouse/production_process/receipts:\x01*\x12\xbc\x01\n\x1dListProductionProcessReceipts\x12/.warehouse.ListProductionProcessReceiptsRequest\x1a\x30.warehouse.ListProductionProcessReceiptsResponse\"8\x82\xd3\xe4\x93\x02\x32\x12\x30/api/v2/supply/warehouse/production_process/list\x12\xd7\x01\n!GetProductionProcessReceiptDetail\x12\x33.warehouse.GetProductionProcessReceiptDetailRequest\x1a\x34.warehouse.GetProductionProcessReceiptDetailResponse\"G\x82\xd3\xe4\x93\x02\x41\x12?/api/v2/supply/warehouse/production_process/{receipt_id}/detail\x12\xcd\x01\n\x1fUpdateProductionProcessReceipts\x12\x31.warehouse.UpdateProductionProcessReceiptsRequest\x1a\x32.warehouse.UpdateProductionProcessReceiptsResponse\"C\x82\xd3\xe4\x93\x02=\x1a\x38/api/v2/supply/warehouse/production_process/{receipt_id}:\x01*\x12\xe8\x01\n%ChangeProductionProcessReceiptsStatus\x12\x37.warehouse.ChangeProductionProcessReceiptsStatusRequest\x1a\x38.warehouse.ChangeProductionProcessReceiptsStatusResponse\"L\x82\xd3\xe4\x93\x02\x46\x1a\x41/api/v2/supply/warehouse/production_process/{receipt_id}/{status}:\x01*\x12\xad\x01\n\x18GetMaterialToProductRate\x12*.warehouse.GetMaterialToProductRateRequest\x1a+.warehouse.GetMaterialToProductRateResponse\"8\x82\xd3\xe4\x93\x02\x32\x12\x30/api/v2/supply/warehouse/production_process/rate\x12\xa8\x01\n\x11GetRuleByBranchId\x12#.warehouse.GetRuleByBranchIdRequest\x1a$.warehouse.GetRuleByBranchIdResponse\"H\x82\xd3\xe4\x93\x02\x42\x12@/api/v2/supply/warehouse/production_process/{branch_id}/rule/getb\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_CREATEPRODUCTIONPROCESSRECEIPTSREQUEST = _descriptor.Descriptor(
  name='CreateProductionProcessReceiptsRequest',
  full_name='warehouse.CreateProductionProcessReceiptsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='process_date', full_name='warehouse.CreateProductionProcessReceiptsRequest.process_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_store_id', full_name='warehouse.CreateProductionProcessReceiptsRequest.process_store_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_store_name', full_name='warehouse.CreateProductionProcessReceiptsRequest.process_store_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_store_code', full_name='warehouse.CreateProductionProcessReceiptsRequest.process_store_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='warehouse.CreateProductionProcessReceiptsRequest.remark', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='warehouse.CreateProductionProcessReceiptsRequest.request_id', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='details', full_name='warehouse.CreateProductionProcessReceiptsRequest.details', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='items', full_name='warehouse.CreateProductionProcessReceiptsRequest.items', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=122,
  serialized_end=420,
)


_CREATEPRODUCTIONPROCESSRECEIPTSRESPONSE = _descriptor.Descriptor(
  name='CreateProductionProcessReceiptsResponse',
  full_name='warehouse.CreateProductionProcessReceiptsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='warehouse.CreateProductionProcessReceiptsResponse.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.CreateProductionProcessReceiptsResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=422,
  serialized_end=499,
)


_PRODUCTIONRECEIPTSROWS = _descriptor.Descriptor(
  name='ProductionReceiptsRows',
  full_name='warehouse.ProductionReceiptsRows',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='production_rule', full_name='warehouse.ProductionReceiptsRows.production_rule', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='production_rule_code', full_name='warehouse.ProductionReceiptsRows.production_rule_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='production_rule_name', full_name='warehouse.ProductionReceiptsRows.production_rule_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='details', full_name='warehouse.ProductionReceiptsRows.details', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='items', full_name='warehouse.ProductionReceiptsRows.items', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=502,
  serialized_end=701,
)


_PRODUCTIONDETAILS = _descriptor.Descriptor(
  name='ProductionDetails',
  full_name='warehouse.ProductionDetails',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.ProductionDetails.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='warehouse.ProductionDetails.product_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='warehouse.ProductionDetails.product_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.ProductionDetails.product_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='warehouse.ProductionDetails.unit_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_code', full_name='warehouse.ProductionDetails.unit_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='warehouse.ProductionDetails.unit_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse.ProductionDetails.type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='warehouse.ProductionDetails.quantity', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='production_rule', full_name='warehouse.ProductionDetails.production_rule', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='production_rule_code', full_name='warehouse.ProductionDetails.production_rule_code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='production_rule_name', full_name='warehouse.ProductionDetails.production_rule_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='specification_id', full_name='warehouse.ProductionDetails.specification_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='specification_code', full_name='warehouse.ProductionDetails.specification_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='specification_name', full_name='warehouse.ProductionDetails.specification_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=704,
  serialized_end=1053,
)


_PRODUCTIONITEMS = _descriptor.Descriptor(
  name='ProductionItems',
  full_name='warehouse.ProductionItems',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.ProductionItems.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='from_material_id', full_name='warehouse.ProductionItems.from_material_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='from_material_code', full_name='warehouse.ProductionItems.from_material_code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='from_material_name', full_name='warehouse.ProductionItems.from_material_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='to_material_id', full_name='warehouse.ProductionItems.to_material_id', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='to_material_code', full_name='warehouse.ProductionItems.to_material_code', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='to_material_name', full_name='warehouse.ProductionItems.to_material_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theoretical_rate', full_name='warehouse.ProductionItems.theoretical_rate', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actual_rate', full_name='warehouse.ProductionItems.actual_rate', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='production_rule', full_name='warehouse.ProductionItems.production_rule', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='production_rule_code', full_name='warehouse.ProductionItems.production_rule_code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='production_rule_name', full_name='warehouse.ProductionItems.production_rule_name', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1056,
  serialized_end=1375,
)


_LISTPRODUCTIONPROCESSRECEIPTSREQUEST = _descriptor.Descriptor(
  name='ListProductionProcessReceiptsRequest',
  full_name='warehouse.ListProductionProcessReceiptsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='warehouse.ListProductionProcessReceiptsRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='warehouse.ListProductionProcessReceiptsRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_store_ids', full_name='warehouse.ListProductionProcessReceiptsRequest.process_store_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.ListProductionProcessReceiptsRequest.code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.ListProductionProcessReceiptsRequest.status', index=4,
      number=5, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='warehouse.ListProductionProcessReceiptsRequest.limit', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='warehouse.ListProductionProcessReceiptsRequest.offset', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='warehouse.ListProductionProcessReceiptsRequest.include_total', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1378,
  serialized_end=1621,
)


_PRODUCTIONPROCESSROW = _descriptor.Descriptor(
  name='ProductionProcessRow',
  full_name='warehouse.ProductionProcessRow',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.ProductionProcessRow.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='warehouse.ProductionProcessRow.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='warehouse.ProductionProcessRow.created_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='warehouse.ProductionProcessRow.created_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='warehouse.ProductionProcessRow.updated_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='warehouse.ProductionProcessRow.updated_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='warehouse.ProductionProcessRow.created_at', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='warehouse.ProductionProcessRow.updated_at', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.ProductionProcessRow.status', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.ProductionProcessRow.code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_date', full_name='warehouse.ProductionProcessRow.process_date', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_store_id', full_name='warehouse.ProductionProcessRow.process_store_id', index=11,
      number=12, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_store_name', full_name='warehouse.ProductionProcessRow.process_store_name', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_store_code', full_name='warehouse.ProductionProcessRow.process_store_code', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='warehouse.ProductionProcessRow.remark', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='warehouse.ProductionProcessRow.request_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse.ProductionProcessRow.type', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1624,
  serialized_end=2070,
)


_LISTPRODUCTIONPROCESSRECEIPTSRESPONSE = _descriptor.Descriptor(
  name='ListProductionProcessReceiptsResponse',
  full_name='warehouse.ListProductionProcessReceiptsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.ListProductionProcessReceiptsResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.ListProductionProcessReceiptsResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2072,
  serialized_end=2173,
)


_GETPRODUCTIONPROCESSRECEIPTDETAILREQUEST = _descriptor.Descriptor(
  name='GetProductionProcessReceiptDetailRequest',
  full_name='warehouse.GetProductionProcessReceiptDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='warehouse.GetProductionProcessReceiptDetailRequest.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2175,
  serialized_end=2237,
)


_GETPRODUCTIONPROCESSRECEIPTDETAILRESPONSE = _descriptor.Descriptor(
  name='GetProductionProcessReceiptDetailResponse',
  full_name='warehouse.GetProductionProcessReceiptDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.GetProductionProcessReceiptDetailResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='warehouse.GetProductionProcessReceiptDetailResponse.partner_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='warehouse.GetProductionProcessReceiptDetailResponse.created_by', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='warehouse.GetProductionProcessReceiptDetailResponse.created_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='warehouse.GetProductionProcessReceiptDetailResponse.updated_by', index=4,
      number=5, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='warehouse.GetProductionProcessReceiptDetailResponse.updated_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='warehouse.GetProductionProcessReceiptDetailResponse.created_at', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='warehouse.GetProductionProcessReceiptDetailResponse.updated_at', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_id', full_name='warehouse.GetProductionProcessReceiptDetailResponse.main_id', index=8,
      number=9, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.GetProductionProcessReceiptDetailResponse.status', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.GetProductionProcessReceiptDetailResponse.code', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_date', full_name='warehouse.GetProductionProcessReceiptDetailResponse.process_date', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_store_id', full_name='warehouse.GetProductionProcessReceiptDetailResponse.process_store_id', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_store_name', full_name='warehouse.GetProductionProcessReceiptDetailResponse.process_store_name', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_store_code', full_name='warehouse.GetProductionProcessReceiptDetailResponse.process_store_code', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='warehouse.GetProductionProcessReceiptDetailResponse.remark', index=15,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='warehouse.GetProductionProcessReceiptDetailResponse.request_id', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.GetProductionProcessReceiptDetailResponse.rows', index=17,
      number=18, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse.GetProductionProcessReceiptDetailResponse.type', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2240,
  serialized_end=2773,
)


_UPDATEPRODUCTIONPROCESSRECEIPTSREQUEST = _descriptor.Descriptor(
  name='UpdateProductionProcessReceiptsRequest',
  full_name='warehouse.UpdateProductionProcessReceiptsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='warehouse.UpdateProductionProcessReceiptsRequest.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_date', full_name='warehouse.UpdateProductionProcessReceiptsRequest.process_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_store_id', full_name='warehouse.UpdateProductionProcessReceiptsRequest.process_store_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_store_name', full_name='warehouse.UpdateProductionProcessReceiptsRequest.process_store_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_store_code', full_name='warehouse.UpdateProductionProcessReceiptsRequest.process_store_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='warehouse.UpdateProductionProcessReceiptsRequest.remark', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='details', full_name='warehouse.UpdateProductionProcessReceiptsRequest.details', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='items', full_name='warehouse.UpdateProductionProcessReceiptsRequest.items', index=7,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='receipt_rule_ids', full_name='warehouse.UpdateProductionProcessReceiptsRequest.receipt_rule_ids', index=8,
      number=9, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2776,
  serialized_end=3100,
)


_UPDATEPRODUCTIONPROCESSRECEIPTSRESPONSE = _descriptor.Descriptor(
  name='UpdateProductionProcessReceiptsResponse',
  full_name='warehouse.UpdateProductionProcessReceiptsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='warehouse.UpdateProductionProcessReceiptsResponse.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.UpdateProductionProcessReceiptsResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3102,
  serialized_end=3179,
)


_CHANGEPRODUCTIONPROCESSRECEIPTSSTATUSREQUEST = _descriptor.Descriptor(
  name='ChangeProductionProcessReceiptsStatusRequest',
  full_name='warehouse.ChangeProductionProcessReceiptsStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='warehouse.ChangeProductionProcessReceiptsStatusRequest.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.ChangeProductionProcessReceiptsStatusRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3181,
  serialized_end=3263,
)


_CHANGEPRODUCTIONPROCESSRECEIPTSSTATUSRESPONSE = _descriptor.Descriptor(
  name='ChangeProductionProcessReceiptsStatusResponse',
  full_name='warehouse.ChangeProductionProcessReceiptsStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='receipt_id', full_name='warehouse.ChangeProductionProcessReceiptsStatusResponse.receipt_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.ChangeProductionProcessReceiptsStatusResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3265,
  serialized_end=3348,
)


_GETMATERIALTOPRODUCTRATEREQUEST = _descriptor.Descriptor(
  name='GetMaterialToProductRateRequest',
  full_name='warehouse.GetMaterialToProductRateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='include_total', full_name='warehouse.GetMaterialToProductRateRequest.include_total', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_ids', full_name='warehouse.GetMaterialToProductRateRequest.store_ids', index=1,
      number=2, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='start_date', full_name='warehouse.GetMaterialToProductRateRequest.start_date', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='warehouse.GetMaterialToProductRateRequest.end_date', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='warehouse.GetMaterialToProductRateRequest.offset', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='warehouse.GetMaterialToProductRateRequest.limit', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_ids', full_name='warehouse.GetMaterialToProductRateRequest.material_ids', index=6,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_ids', full_name='warehouse.GetMaterialToProductRateRequest.product_ids', index=7,
      number=8, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='conversion_rate', full_name='warehouse.GetMaterialToProductRateRequest.conversion_rate', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3351,
  serialized_end=3619,
)


_MATERIALTOPRODUCTRATEROW = _descriptor.Descriptor(
  name='MaterialToProductRateRow',
  full_name='warehouse.MaterialToProductRateRow',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='material_id', full_name='warehouse.MaterialToProductRateRow.material_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_code', full_name='warehouse.MaterialToProductRateRow.material_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_name', full_name='warehouse.MaterialToProductRateRow.material_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='warehouse.MaterialToProductRateRow.product_id', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='warehouse.MaterialToProductRateRow.product_code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.MaterialToProductRateRow.product_name', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_date', full_name='warehouse.MaterialToProductRateRow.process_date', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_id', full_name='warehouse.MaterialToProductRateRow.store_id', index=7,
      number=8, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_name', full_name='warehouse.MaterialToProductRateRow.store_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='store_code', full_name='warehouse.MaterialToProductRateRow.store_code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='theoretical_rate', full_name='warehouse.MaterialToProductRateRow.theoretical_rate', index=10,
      number=11, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actual_rate', full_name='warehouse.MaterialToProductRateRow.actual_rate', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.MaterialToProductRateRow.code', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='material_quantity', full_name='warehouse.MaterialToProductRateRow.material_quantity', index=13,
      number=14, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_quantity', full_name='warehouse.MaterialToProductRateRow.product_quantity', index=14,
      number=15, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3622,
  serialized_end=4001,
)


_GETMATERIALTOPRODUCTRATERESPONSE = _descriptor.Descriptor(
  name='GetMaterialToProductRateResponse',
  full_name='warehouse.GetMaterialToProductRateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.GetMaterialToProductRateResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.GetMaterialToProductRateResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4003,
  serialized_end=4103,
)


_GETRULEBYBRANCHIDREQUEST = _descriptor.Descriptor(
  name='GetRuleByBranchIdRequest',
  full_name='warehouse.GetRuleByBranchIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='warehouse.GetRuleByBranchIdRequest.branch_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4105,
  serialized_end=4150,
)


_RULES = _descriptor.Descriptor(
  name='Rules',
  full_name='warehouse.Rules',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.Rules.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.Rules.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='name', full_name='warehouse.Rules.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4152,
  serialized_end=4199,
)


_GETRULEBYBRANCHIDRESPONSE = _descriptor.Descriptor(
  name='GetRuleByBranchIdResponse',
  full_name='warehouse.GetRuleByBranchIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.GetRuleByBranchIdResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4201,
  serialized_end=4260,
)

_CREATEPRODUCTIONPROCESSRECEIPTSREQUEST.fields_by_name['process_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEPRODUCTIONPROCESSRECEIPTSREQUEST.fields_by_name['details'].message_type = _PRODUCTIONDETAILS
_CREATEPRODUCTIONPROCESSRECEIPTSREQUEST.fields_by_name['items'].message_type = _PRODUCTIONITEMS
_PRODUCTIONRECEIPTSROWS.fields_by_name['details'].message_type = _PRODUCTIONDETAILS
_PRODUCTIONRECEIPTSROWS.fields_by_name['items'].message_type = _PRODUCTIONITEMS
_LISTPRODUCTIONPROCESSRECEIPTSREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPRODUCTIONPROCESSRECEIPTSREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCTIONPROCESSROW.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCTIONPROCESSROW.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_PRODUCTIONPROCESSROW.fields_by_name['process_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTPRODUCTIONPROCESSRECEIPTSRESPONSE.fields_by_name['rows'].message_type = _PRODUCTIONPROCESSROW
_GETPRODUCTIONPROCESSRECEIPTDETAILRESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPRODUCTIONPROCESSRECEIPTDETAILRESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPRODUCTIONPROCESSRECEIPTDETAILRESPONSE.fields_by_name['process_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETPRODUCTIONPROCESSRECEIPTDETAILRESPONSE.fields_by_name['rows'].message_type = _PRODUCTIONRECEIPTSROWS
_UPDATEPRODUCTIONPROCESSRECEIPTSREQUEST.fields_by_name['process_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATEPRODUCTIONPROCESSRECEIPTSREQUEST.fields_by_name['details'].message_type = _PRODUCTIONDETAILS
_UPDATEPRODUCTIONPROCESSRECEIPTSREQUEST.fields_by_name['items'].message_type = _PRODUCTIONITEMS
_GETMATERIALTOPRODUCTRATEREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETMATERIALTOPRODUCTRATEREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_MATERIALTOPRODUCTRATEROW.fields_by_name['process_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETMATERIALTOPRODUCTRATERESPONSE.fields_by_name['rows'].message_type = _MATERIALTOPRODUCTRATEROW
_GETRULEBYBRANCHIDRESPONSE.fields_by_name['rows'].message_type = _RULES
DESCRIPTOR.message_types_by_name['CreateProductionProcessReceiptsRequest'] = _CREATEPRODUCTIONPROCESSRECEIPTSREQUEST
DESCRIPTOR.message_types_by_name['CreateProductionProcessReceiptsResponse'] = _CREATEPRODUCTIONPROCESSRECEIPTSRESPONSE
DESCRIPTOR.message_types_by_name['ProductionReceiptsRows'] = _PRODUCTIONRECEIPTSROWS
DESCRIPTOR.message_types_by_name['ProductionDetails'] = _PRODUCTIONDETAILS
DESCRIPTOR.message_types_by_name['ProductionItems'] = _PRODUCTIONITEMS
DESCRIPTOR.message_types_by_name['ListProductionProcessReceiptsRequest'] = _LISTPRODUCTIONPROCESSRECEIPTSREQUEST
DESCRIPTOR.message_types_by_name['ProductionProcessRow'] = _PRODUCTIONPROCESSROW
DESCRIPTOR.message_types_by_name['ListProductionProcessReceiptsResponse'] = _LISTPRODUCTIONPROCESSRECEIPTSRESPONSE
DESCRIPTOR.message_types_by_name['GetProductionProcessReceiptDetailRequest'] = _GETPRODUCTIONPROCESSRECEIPTDETAILREQUEST
DESCRIPTOR.message_types_by_name['GetProductionProcessReceiptDetailResponse'] = _GETPRODUCTIONPROCESSRECEIPTDETAILRESPONSE
DESCRIPTOR.message_types_by_name['UpdateProductionProcessReceiptsRequest'] = _UPDATEPRODUCTIONPROCESSRECEIPTSREQUEST
DESCRIPTOR.message_types_by_name['UpdateProductionProcessReceiptsResponse'] = _UPDATEPRODUCTIONPROCESSRECEIPTSRESPONSE
DESCRIPTOR.message_types_by_name['ChangeProductionProcessReceiptsStatusRequest'] = _CHANGEPRODUCTIONPROCESSRECEIPTSSTATUSREQUEST
DESCRIPTOR.message_types_by_name['ChangeProductionProcessReceiptsStatusResponse'] = _CHANGEPRODUCTIONPROCESSRECEIPTSSTATUSRESPONSE
DESCRIPTOR.message_types_by_name['GetMaterialToProductRateRequest'] = _GETMATERIALTOPRODUCTRATEREQUEST
DESCRIPTOR.message_types_by_name['MaterialToProductRateRow'] = _MATERIALTOPRODUCTRATEROW
DESCRIPTOR.message_types_by_name['GetMaterialToProductRateResponse'] = _GETMATERIALTOPRODUCTRATERESPONSE
DESCRIPTOR.message_types_by_name['GetRuleByBranchIdRequest'] = _GETRULEBYBRANCHIDREQUEST
DESCRIPTOR.message_types_by_name['Rules'] = _RULES
DESCRIPTOR.message_types_by_name['GetRuleByBranchIdResponse'] = _GETRULEBYBRANCHIDRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CreateProductionProcessReceiptsRequest = _reflection.GeneratedProtocolMessageType('CreateProductionProcessReceiptsRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPRODUCTIONPROCESSRECEIPTSREQUEST,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CreateProductionProcessReceiptsRequest)
  ))
_sym_db.RegisterMessage(CreateProductionProcessReceiptsRequest)

CreateProductionProcessReceiptsResponse = _reflection.GeneratedProtocolMessageType('CreateProductionProcessReceiptsResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEPRODUCTIONPROCESSRECEIPTSRESPONSE,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CreateProductionProcessReceiptsResponse)
  ))
_sym_db.RegisterMessage(CreateProductionProcessReceiptsResponse)

ProductionReceiptsRows = _reflection.GeneratedProtocolMessageType('ProductionReceiptsRows', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTIONRECEIPTSROWS,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ProductionReceiptsRows)
  ))
_sym_db.RegisterMessage(ProductionReceiptsRows)

ProductionDetails = _reflection.GeneratedProtocolMessageType('ProductionDetails', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTIONDETAILS,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ProductionDetails)
  ))
_sym_db.RegisterMessage(ProductionDetails)

ProductionItems = _reflection.GeneratedProtocolMessageType('ProductionItems', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTIONITEMS,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ProductionItems)
  ))
_sym_db.RegisterMessage(ProductionItems)

ListProductionProcessReceiptsRequest = _reflection.GeneratedProtocolMessageType('ListProductionProcessReceiptsRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTPRODUCTIONPROCESSRECEIPTSREQUEST,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ListProductionProcessReceiptsRequest)
  ))
_sym_db.RegisterMessage(ListProductionProcessReceiptsRequest)

ProductionProcessRow = _reflection.GeneratedProtocolMessageType('ProductionProcessRow', (_message.Message,), dict(
  DESCRIPTOR = _PRODUCTIONPROCESSROW,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ProductionProcessRow)
  ))
_sym_db.RegisterMessage(ProductionProcessRow)

ListProductionProcessReceiptsResponse = _reflection.GeneratedProtocolMessageType('ListProductionProcessReceiptsResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTPRODUCTIONPROCESSRECEIPTSRESPONSE,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ListProductionProcessReceiptsResponse)
  ))
_sym_db.RegisterMessage(ListProductionProcessReceiptsResponse)

GetProductionProcessReceiptDetailRequest = _reflection.GeneratedProtocolMessageType('GetProductionProcessReceiptDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTIONPROCESSRECEIPTDETAILREQUEST,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetProductionProcessReceiptDetailRequest)
  ))
_sym_db.RegisterMessage(GetProductionProcessReceiptDetailRequest)

GetProductionProcessReceiptDetailResponse = _reflection.GeneratedProtocolMessageType('GetProductionProcessReceiptDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPRODUCTIONPROCESSRECEIPTDETAILRESPONSE,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetProductionProcessReceiptDetailResponse)
  ))
_sym_db.RegisterMessage(GetProductionProcessReceiptDetailResponse)

UpdateProductionProcessReceiptsRequest = _reflection.GeneratedProtocolMessageType('UpdateProductionProcessReceiptsRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPRODUCTIONPROCESSRECEIPTSREQUEST,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.UpdateProductionProcessReceiptsRequest)
  ))
_sym_db.RegisterMessage(UpdateProductionProcessReceiptsRequest)

UpdateProductionProcessReceiptsResponse = _reflection.GeneratedProtocolMessageType('UpdateProductionProcessReceiptsResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEPRODUCTIONPROCESSRECEIPTSRESPONSE,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.UpdateProductionProcessReceiptsResponse)
  ))
_sym_db.RegisterMessage(UpdateProductionProcessReceiptsResponse)

ChangeProductionProcessReceiptsStatusRequest = _reflection.GeneratedProtocolMessageType('ChangeProductionProcessReceiptsStatusRequest', (_message.Message,), dict(
  DESCRIPTOR = _CHANGEPRODUCTIONPROCESSRECEIPTSSTATUSREQUEST,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ChangeProductionProcessReceiptsStatusRequest)
  ))
_sym_db.RegisterMessage(ChangeProductionProcessReceiptsStatusRequest)

ChangeProductionProcessReceiptsStatusResponse = _reflection.GeneratedProtocolMessageType('ChangeProductionProcessReceiptsStatusResponse', (_message.Message,), dict(
  DESCRIPTOR = _CHANGEPRODUCTIONPROCESSRECEIPTSSTATUSRESPONSE,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ChangeProductionProcessReceiptsStatusResponse)
  ))
_sym_db.RegisterMessage(ChangeProductionProcessReceiptsStatusResponse)

GetMaterialToProductRateRequest = _reflection.GeneratedProtocolMessageType('GetMaterialToProductRateRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETMATERIALTOPRODUCTRATEREQUEST,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetMaterialToProductRateRequest)
  ))
_sym_db.RegisterMessage(GetMaterialToProductRateRequest)

MaterialToProductRateRow = _reflection.GeneratedProtocolMessageType('MaterialToProductRateRow', (_message.Message,), dict(
  DESCRIPTOR = _MATERIALTOPRODUCTRATEROW,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.MaterialToProductRateRow)
  ))
_sym_db.RegisterMessage(MaterialToProductRateRow)

GetMaterialToProductRateResponse = _reflection.GeneratedProtocolMessageType('GetMaterialToProductRateResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETMATERIALTOPRODUCTRATERESPONSE,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetMaterialToProductRateResponse)
  ))
_sym_db.RegisterMessage(GetMaterialToProductRateResponse)

GetRuleByBranchIdRequest = _reflection.GeneratedProtocolMessageType('GetRuleByBranchIdRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETRULEBYBRANCHIDREQUEST,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetRuleByBranchIdRequest)
  ))
_sym_db.RegisterMessage(GetRuleByBranchIdRequest)

Rules = _reflection.GeneratedProtocolMessageType('Rules', (_message.Message,), dict(
  DESCRIPTOR = _RULES,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.Rules)
  ))
_sym_db.RegisterMessage(Rules)

GetRuleByBranchIdResponse = _reflection.GeneratedProtocolMessageType('GetRuleByBranchIdResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETRULEBYBRANCHIDRESPONSE,
  __module__ = 'warehouse.production_process_receipts_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetRuleByBranchIdResponse)
  ))
_sym_db.RegisterMessage(GetRuleByBranchIdResponse)



_WAREHOUSEPRODUCTIONPROCESSRECEIPTS = _descriptor.ServiceDescriptor(
  name='WarehouseProductionProcessReceipts',
  full_name='warehouse.WarehouseProductionProcessReceipts',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=4263,
  serialized_end=5702,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateProductionProcessReceipts',
    full_name='warehouse.WarehouseProductionProcessReceipts.CreateProductionProcessReceipts',
    index=0,
    containing_service=None,
    input_type=_CREATEPRODUCTIONPROCESSRECEIPTSREQUEST,
    output_type=_CREATEPRODUCTIONPROCESSRECEIPTSRESPONSE,
    serialized_options=_b('\202\323\344\223\0029\"4/api/v2/supply/warehouse/production_process/receipts:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListProductionProcessReceipts',
    full_name='warehouse.WarehouseProductionProcessReceipts.ListProductionProcessReceipts',
    index=1,
    containing_service=None,
    input_type=_LISTPRODUCTIONPROCESSRECEIPTSREQUEST,
    output_type=_LISTPRODUCTIONPROCESSRECEIPTSRESPONSE,
    serialized_options=_b('\202\323\344\223\0022\0220/api/v2/supply/warehouse/production_process/list'),
  ),
  _descriptor.MethodDescriptor(
    name='GetProductionProcessReceiptDetail',
    full_name='warehouse.WarehouseProductionProcessReceipts.GetProductionProcessReceiptDetail',
    index=2,
    containing_service=None,
    input_type=_GETPRODUCTIONPROCESSRECEIPTDETAILREQUEST,
    output_type=_GETPRODUCTIONPROCESSRECEIPTDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\002A\022?/api/v2/supply/warehouse/production_process/{receipt_id}/detail'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateProductionProcessReceipts',
    full_name='warehouse.WarehouseProductionProcessReceipts.UpdateProductionProcessReceipts',
    index=3,
    containing_service=None,
    input_type=_UPDATEPRODUCTIONPROCESSRECEIPTSREQUEST,
    output_type=_UPDATEPRODUCTIONPROCESSRECEIPTSRESPONSE,
    serialized_options=_b('\202\323\344\223\002=\0328/api/v2/supply/warehouse/production_process/{receipt_id}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ChangeProductionProcessReceiptsStatus',
    full_name='warehouse.WarehouseProductionProcessReceipts.ChangeProductionProcessReceiptsStatus',
    index=4,
    containing_service=None,
    input_type=_CHANGEPRODUCTIONPROCESSRECEIPTSSTATUSREQUEST,
    output_type=_CHANGEPRODUCTIONPROCESSRECEIPTSSTATUSRESPONSE,
    serialized_options=_b('\202\323\344\223\002F\032A/api/v2/supply/warehouse/production_process/{receipt_id}/{status}:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='GetMaterialToProductRate',
    full_name='warehouse.WarehouseProductionProcessReceipts.GetMaterialToProductRate',
    index=5,
    containing_service=None,
    input_type=_GETMATERIALTOPRODUCTRATEREQUEST,
    output_type=_GETMATERIALTOPRODUCTRATERESPONSE,
    serialized_options=_b('\202\323\344\223\0022\0220/api/v2/supply/warehouse/production_process/rate'),
  ),
  _descriptor.MethodDescriptor(
    name='GetRuleByBranchId',
    full_name='warehouse.WarehouseProductionProcessReceipts.GetRuleByBranchId',
    index=6,
    containing_service=None,
    input_type=_GETRULEBYBRANCHIDREQUEST,
    output_type=_GETRULEBYBRANCHIDRESPONSE,
    serialized_options=_b('\202\323\344\223\002B\022@/api/v2/supply/warehouse/production_process/{branch_id}/rule/get'),
  ),
])
_sym_db.RegisterServiceDescriptor(_WAREHOUSEPRODUCTIONPROCESSRECEIPTS)

DESCRIPTOR.services_by_name['WarehouseProductionProcessReceipts'] = _WAREHOUSEPRODUCTIONPROCESSRECEIPTS

# @@protoc_insertion_point(module_scope)
