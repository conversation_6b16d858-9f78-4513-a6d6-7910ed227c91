# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from warehouse import production_process_receipts_pb2 as warehouse_dot_production__process__receipts__pb2


class WarehouseProductionProcessReceiptsStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.CreateProductionProcessReceipts = channel.unary_unary(
        '/warehouse.WarehouseProductionProcessReceipts/CreateProductionProcessReceipts',
        request_serializer=warehouse_dot_production__process__receipts__pb2.CreateProductionProcessReceiptsRequest.SerializeToString,
        response_deserializer=warehouse_dot_production__process__receipts__pb2.CreateProductionProcessReceiptsResponse.FromString,
        )
    self.ListProductionProcessReceipts = channel.unary_unary(
        '/warehouse.WarehouseProductionProcessReceipts/ListProductionProcessReceipts',
        request_serializer=warehouse_dot_production__process__receipts__pb2.ListProductionProcessReceiptsRequest.SerializeToString,
        response_deserializer=warehouse_dot_production__process__receipts__pb2.ListProductionProcessReceiptsResponse.FromString,
        )
    self.GetProductionProcessReceiptDetail = channel.unary_unary(
        '/warehouse.WarehouseProductionProcessReceipts/GetProductionProcessReceiptDetail',
        request_serializer=warehouse_dot_production__process__receipts__pb2.GetProductionProcessReceiptDetailRequest.SerializeToString,
        response_deserializer=warehouse_dot_production__process__receipts__pb2.GetProductionProcessReceiptDetailResponse.FromString,
        )
    self.UpdateProductionProcessReceipts = channel.unary_unary(
        '/warehouse.WarehouseProductionProcessReceipts/UpdateProductionProcessReceipts',
        request_serializer=warehouse_dot_production__process__receipts__pb2.UpdateProductionProcessReceiptsRequest.SerializeToString,
        response_deserializer=warehouse_dot_production__process__receipts__pb2.UpdateProductionProcessReceiptsResponse.FromString,
        )
    self.ChangeProductionProcessReceiptsStatus = channel.unary_unary(
        '/warehouse.WarehouseProductionProcessReceipts/ChangeProductionProcessReceiptsStatus',
        request_serializer=warehouse_dot_production__process__receipts__pb2.ChangeProductionProcessReceiptsStatusRequest.SerializeToString,
        response_deserializer=warehouse_dot_production__process__receipts__pb2.ChangeProductionProcessReceiptsStatusResponse.FromString,
        )
    self.GetMaterialToProductRate = channel.unary_unary(
        '/warehouse.WarehouseProductionProcessReceipts/GetMaterialToProductRate',
        request_serializer=warehouse_dot_production__process__receipts__pb2.GetMaterialToProductRateRequest.SerializeToString,
        response_deserializer=warehouse_dot_production__process__receipts__pb2.GetMaterialToProductRateResponse.FromString,
        )
    self.GetRuleByBranchId = channel.unary_unary(
        '/warehouse.WarehouseProductionProcessReceipts/GetRuleByBranchId',
        request_serializer=warehouse_dot_production__process__receipts__pb2.GetRuleByBranchIdRequest.SerializeToString,
        response_deserializer=warehouse_dot_production__process__receipts__pb2.GetRuleByBranchIdResponse.FromString,
        )


class WarehouseProductionProcessReceiptsServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def CreateProductionProcessReceipts(self, request, context):
    """创建生产单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListProductionProcessReceipts(self, request, context):
    """生产单列表查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetProductionProcessReceiptDetail(self, request, context):
    """生产单详情查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def UpdateProductionProcessReceipts(self, request, context):
    """更新生产单
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ChangeProductionProcessReceiptsStatus(self, request, context):
    """生产单状态变更
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetMaterialToProductRate(self, request, context):
    """生产转化率查询
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetRuleByBranchId(self, request, context):
    """根据选择的仓库获取对应区域、门店下的规则ID
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_WarehouseProductionProcessReceiptsServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'CreateProductionProcessReceipts': grpc.unary_unary_rpc_method_handler(
          servicer.CreateProductionProcessReceipts,
          request_deserializer=warehouse_dot_production__process__receipts__pb2.CreateProductionProcessReceiptsRequest.FromString,
          response_serializer=warehouse_dot_production__process__receipts__pb2.CreateProductionProcessReceiptsResponse.SerializeToString,
      ),
      'ListProductionProcessReceipts': grpc.unary_unary_rpc_method_handler(
          servicer.ListProductionProcessReceipts,
          request_deserializer=warehouse_dot_production__process__receipts__pb2.ListProductionProcessReceiptsRequest.FromString,
          response_serializer=warehouse_dot_production__process__receipts__pb2.ListProductionProcessReceiptsResponse.SerializeToString,
      ),
      'GetProductionProcessReceiptDetail': grpc.unary_unary_rpc_method_handler(
          servicer.GetProductionProcessReceiptDetail,
          request_deserializer=warehouse_dot_production__process__receipts__pb2.GetProductionProcessReceiptDetailRequest.FromString,
          response_serializer=warehouse_dot_production__process__receipts__pb2.GetProductionProcessReceiptDetailResponse.SerializeToString,
      ),
      'UpdateProductionProcessReceipts': grpc.unary_unary_rpc_method_handler(
          servicer.UpdateProductionProcessReceipts,
          request_deserializer=warehouse_dot_production__process__receipts__pb2.UpdateProductionProcessReceiptsRequest.FromString,
          response_serializer=warehouse_dot_production__process__receipts__pb2.UpdateProductionProcessReceiptsResponse.SerializeToString,
      ),
      'ChangeProductionProcessReceiptsStatus': grpc.unary_unary_rpc_method_handler(
          servicer.ChangeProductionProcessReceiptsStatus,
          request_deserializer=warehouse_dot_production__process__receipts__pb2.ChangeProductionProcessReceiptsStatusRequest.FromString,
          response_serializer=warehouse_dot_production__process__receipts__pb2.ChangeProductionProcessReceiptsStatusResponse.SerializeToString,
      ),
      'GetMaterialToProductRate': grpc.unary_unary_rpc_method_handler(
          servicer.GetMaterialToProductRate,
          request_deserializer=warehouse_dot_production__process__receipts__pb2.GetMaterialToProductRateRequest.FromString,
          response_serializer=warehouse_dot_production__process__receipts__pb2.GetMaterialToProductRateResponse.SerializeToString,
      ),
      'GetRuleByBranchId': grpc.unary_unary_rpc_method_handler(
          servicer.GetRuleByBranchId,
          request_deserializer=warehouse_dot_production__process__receipts__pb2.GetRuleByBranchIdRequest.FromString,
          response_serializer=warehouse_dot_production__process__receipts__pb2.GetRuleByBranchIdResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'warehouse.WarehouseProductionProcessReceipts', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
