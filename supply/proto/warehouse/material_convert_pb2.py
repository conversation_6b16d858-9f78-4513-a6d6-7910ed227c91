# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: warehouse/material_convert.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.api import annotations_pb2 as google_dot_api_dot_annotations__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='warehouse/material_convert.proto',
  package='warehouse',
  syntax='proto3',
  serialized_options=None,
  serialized_pb=_b('\n warehouse/material_convert.proto\x12\twarehouse\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xb4\x03\n\x1c\x43reateMaterialConvertRequest\x12\x13\n\x0b\x62ranch_type\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x14\n\x0c\x63onvert_type\x18\x05 \x01(\t\x12\x30\n\x0c\x63onvert_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tbranch_id\x18\x07 \x01(\x04\x12\x13\n\x0b\x62ranch_code\x18\x08 \x01(\t\x12\x13\n\x0b\x62ranch_name\x18\t \x01(\t\x12\x13\n\x0bposition_id\x18\n \x01(\x04\x12\x15\n\rposition_name\x18\x0b \x01(\t\x12\x15\n\rposition_code\x18\x0c \x01(\t\x12\x14\n\x0c\x63onvert_rule\x18\r \x01(\x04\x12\x17\n\x0fopened_position\x18\x0e \x01(\x08\x12\x0e\n\x06remark\x18\x0f \x01(\t\x12\x14\n\x0c\x61uto_confirm\x18\x10 \x01(\x08\x12\x12\n\nrequest_id\x18\x14 \x01(\x04\x12&\n\tmaterials\x18\x15 \x03(\x0b\x32\x13.warehouse.Material\x12\x16\n\x0e\x63ost_center_id\x18\x1e \x01(\x04\"C\n\x1d\x43reateMaterialConvertResponse\x12\x12\n\nconvert_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t\"\xd7\x02\n\x1aListMaterialConvertRequest\x12.\n\nstart_date\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_date\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x12\n\nbranch_ids\x18\x03 \x03(\x04\x12\x13\n\x0b\x62ranch_type\x18\x04 \x01(\t\x12\x0c\n\x04\x63ode\x18\x05 \x01(\t\x12\x15\n\rconvert_rules\x18\x06 \x03(\x04\x12\x14\n\x0cposition_ids\x18\x07 \x03(\x04\x12\x14\n\x0c\x63onvert_type\x18\x08 \x01(\t\x12\x0e\n\x06status\x18\t \x01(\t\x12\r\n\x05limit\x18\n \x01(\x03\x12\x0e\n\x06offset\x18\x0b \x01(\x04\x12\x15\n\rinclude_total\x18\x0c \x01(\x08\x12\r\n\x05order\x18\r \x01(\t\x12\x0c\n\x04sort\x18\x0e \x01(\t\"\xaf\x03\n\x08Material\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x0f\n\x07main_id\x18\x02 \x01(\x04\x12\x12\n\nproduct_id\x18\x03 \x01(\x04\x12\x14\n\x0cproduct_code\x18\x04 \x01(\t\x12\x14\n\x0cproduct_name\x18\x05 \x01(\t\x12\x14\n\x0cproduct_type\x18\x06 \x01(\t\x12\x0f\n\x07unit_id\x18\x07 \x01(\x04\x12\x11\n\tunit_name\x18\x08 \x01(\t\x12\x11\n\tunit_spec\x18\t \x01(\t\x12\x10\n\x08quantity\x18\n \x01(\x01\x12\x0c\n\x04type\x18\x0b \x01(\t\x12\x11\n\tunit_rate\x18\x0c \x01(\x01\x12\x12\n\npartner_id\x18\x10 \x01(\x04\x12\x12\n\ncreated_by\x18\x11 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x12 \x01(\t\x12\x12\n\nupdated_by\x18\x13 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x14 \x01(\t\x12.\n\ncreated_at\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xe2\x04\n\x0eMaterialDetail\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x14\n\x0c\x63onvert_type\x18\x05 \x01(\t\x12\x30\n\x0c\x63onvert_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tbranch_id\x18\x07 \x01(\x04\x12\x13\n\x0b\x62ranch_code\x18\x08 \x01(\t\x12\x13\n\x0b\x62ranch_name\x18\t \x01(\t\x12\x13\n\x0bposition_id\x18\n \x01(\x04\x12\x15\n\rposition_name\x18\x0b \x01(\t\x12\x15\n\rposition_code\x18\x0c \x01(\t\x12\x14\n\x0c\x63onvert_rule\x18\r \x01(\x04\x12\x0e\n\x06remark\x18\x0e \x01(\t\x12\x12\n\nrequest_id\x18\x0f \x01(\x04\x12\x12\n\npartner_id\x18\x10 \x01(\x04\x12\x12\n\ncreated_by\x18\x11 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x12 \x01(\t\x12\x12\n\nupdated_by\x18\x13 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x14 \x01(\t\x12.\n\ncreated_at\x18\x15 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x16\n\x0eprocess_status\x18\x17 \x01(\t\x12\x17\n\x0fopened_position\x18\x18 \x01(\x08\x12\x16\n\x0e\x63ost_center_id\x18\x1e \x01(\x04\"U\n\x1bListMaterialConvertResponse\x12\'\n\x04rows\x18\x01 \x03(\x0b\x32\x19.warehouse.MaterialDetail\x12\r\n\x05total\x18\x02 \x01(\x04\"5\n\x1fGetMaterialConvertDetailRequest\x12\x12\n\nconvert_id\x18\x01 \x01(\x04\"\x9c\x05\n GetMaterialConvertDetailResponse\x12\n\n\x02id\x18\x01 \x01(\x04\x12\x13\n\x0b\x62ranch_type\x18\x02 \x01(\t\x12\x0c\n\x04\x63ode\x18\x03 \x01(\t\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x14\n\x0c\x63onvert_type\x18\x05 \x01(\t\x12\x30\n\x0c\x63onvert_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tbranch_id\x18\x07 \x01(\x04\x12\x13\n\x0b\x62ranch_code\x18\x08 \x01(\t\x12\x13\n\x0b\x62ranch_name\x18\t \x01(\t\x12\x13\n\x0bposition_id\x18\n \x01(\x04\x12\x15\n\rposition_name\x18\x0b \x01(\t\x12\x15\n\rposition_code\x18\x0c \x01(\t\x12\x14\n\x0c\x63onvert_rule\x18\r \x01(\x04\x12\x0e\n\x06remark\x18\x0e \x01(\t\x12\x12\n\nrequest_id\x18\x0f \x01(\x04\x12\x12\n\npartner_id\x18\x10 \x01(\x04\x12\x12\n\ncreated_by\x18\x11 \x01(\x04\x12\x14\n\x0c\x63reated_name\x18\x12 \x01(\t\x12\x12\n\nupdated_by\x18\x13 \x01(\x04\x12\x14\n\x0cupdated_name\x18\x14 \x01(\t\x12\x16\n\x0eprocess_status\x18\x15 \x01(\t\x12.\n\ncreated_at\x18\x16 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x17 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x17\n\x0fopened_position\x18\x18 \x01(\x08\x12&\n\tmaterials\x18\x1e \x03(\x0b\x32\x13.warehouse.Material\x12\x16\n\x0e\x63ost_center_id\x18# \x01(\x04\"\xb5\x03\n\x1cUpdateMaterialConvertRequest\x12\x12\n\nconvert_id\x18\x01 \x01(\x04\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x15\n\rupdate_detail\x18\x03 \x01(\x08\x12\x13\n\x0b\x62ranch_type\x18\x04 \x01(\t\x12\x14\n\x0c\x63onvert_type\x18\x05 \x01(\t\x12\x30\n\x0c\x63onvert_date\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x11\n\tbranch_id\x18\x07 \x01(\x04\x12\x13\n\x0b\x62ranch_code\x18\x08 \x01(\t\x12\x13\n\x0b\x62ranch_name\x18\t \x01(\t\x12\x13\n\x0bposition_id\x18\n \x01(\x04\x12\x15\n\rposition_name\x18\x0b \x01(\t\x12\x15\n\rposition_code\x18\x0c \x01(\t\x12\x14\n\x0c\x63onvert_rule\x18\r \x01(\x04\x12\x0e\n\x06remark\x18\x0e \x01(\t\x12\x17\n\x0fopened_position\x18\x0f \x01(\x08\x12&\n\tmaterials\x18\x14 \x03(\x0b\x32\x13.warehouse.Material\x12\x16\n\x0e\x63ost_center_id\x18\x1e \x01(\x04\"C\n\x1dUpdateMaterialConvertResponse\x12\x12\n\nconvert_id\x18\x01 \x01(\x04\x12\x0e\n\x06result\x18\x02 \x01(\t2\xdc\x05\n\x18WarehouseMaterialConvert\x12\xa3\x01\n\x15\x43reateMaterialConvert\x12\'.warehouse.CreateMaterialConvertRequest\x1a(.warehouse.CreateMaterialConvertResponse\"7\x82\xd3\xe4\x93\x02\x31\",/api/v2/supply/warehouse/material/conversion:\x01*\x12\x9f\x01\n\x13ListMaterialConvert\x12%.warehouse.ListMaterialConvertRequest\x1a&.warehouse.ListMaterialConvertResponse\"9\x82\xd3\xe4\x93\x02\x33\x12\x31/api/v2/supply/warehouse/material/conversion/list\x12\xbd\x01\n\x18GetMaterialConvertDetail\x12*.warehouse.GetMaterialConvertDetailRequest\x1a+.warehouse.GetMaterialConvertDetailResponse\"H\x82\xd3\xe4\x93\x02\x42\x12@/api/v2/supply/warehouse/material/conversion/{convert_id}/detail\x12\xb7\x01\n\x15UpdateMaterialConvert\x12\'.warehouse.UpdateMaterialConvertRequest\x1a(.warehouse.UpdateMaterialConvertResponse\"K\x82\xd3\xe4\x93\x02\x45\x1a@/api/v2/supply/warehouse/material/conversion/{convert_id}/update:\x01*b\x06proto3')
  ,
  dependencies=[google_dot_api_dot_annotations__pb2.DESCRIPTOR,google_dot_protobuf_dot_timestamp__pb2.DESCRIPTOR,])




_CREATEMATERIALCONVERTREQUEST = _descriptor.Descriptor(
  name='CreateMaterialConvertRequest',
  full_name='warehouse.CreateMaterialConvertRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.CreateMaterialConvertRequest.branch_type', index=0,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.CreateMaterialConvertRequest.status', index=1,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_type', full_name='warehouse.CreateMaterialConvertRequest.convert_type', index=2,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_date', full_name='warehouse.CreateMaterialConvertRequest.convert_date', index=3,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='warehouse.CreateMaterialConvertRequest.branch_id', index=4,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_code', full_name='warehouse.CreateMaterialConvertRequest.branch_code', index=5,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='warehouse.CreateMaterialConvertRequest.branch_name', index=6,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='warehouse.CreateMaterialConvertRequest.position_id', index=7,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='warehouse.CreateMaterialConvertRequest.position_name', index=8,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='warehouse.CreateMaterialConvertRequest.position_code', index=9,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_rule', full_name='warehouse.CreateMaterialConvertRequest.convert_rule', index=10,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='opened_position', full_name='warehouse.CreateMaterialConvertRequest.opened_position', index=11,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='warehouse.CreateMaterialConvertRequest.remark', index=12,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='auto_confirm', full_name='warehouse.CreateMaterialConvertRequest.auto_confirm', index=13,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='warehouse.CreateMaterialConvertRequest.request_id', index=14,
      number=20, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='materials', full_name='warehouse.CreateMaterialConvertRequest.materials', index=15,
      number=21, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='warehouse.CreateMaterialConvertRequest.cost_center_id', index=16,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=111,
  serialized_end=547,
)


_CREATEMATERIALCONVERTRESPONSE = _descriptor.Descriptor(
  name='CreateMaterialConvertResponse',
  full_name='warehouse.CreateMaterialConvertResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='convert_id', full_name='warehouse.CreateMaterialConvertResponse.convert_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.CreateMaterialConvertResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=549,
  serialized_end=616,
)


_LISTMATERIALCONVERTREQUEST = _descriptor.Descriptor(
  name='ListMaterialConvertRequest',
  full_name='warehouse.ListMaterialConvertRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_date', full_name='warehouse.ListMaterialConvertRequest.start_date', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='end_date', full_name='warehouse.ListMaterialConvertRequest.end_date', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_ids', full_name='warehouse.ListMaterialConvertRequest.branch_ids', index=2,
      number=3, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.ListMaterialConvertRequest.branch_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.ListMaterialConvertRequest.code', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_rules', full_name='warehouse.ListMaterialConvertRequest.convert_rules', index=5,
      number=6, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_ids', full_name='warehouse.ListMaterialConvertRequest.position_ids', index=6,
      number=7, type=4, cpp_type=4, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_type', full_name='warehouse.ListMaterialConvertRequest.convert_type', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.ListMaterialConvertRequest.status', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='limit', full_name='warehouse.ListMaterialConvertRequest.limit', index=9,
      number=10, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='offset', full_name='warehouse.ListMaterialConvertRequest.offset', index=10,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='include_total', full_name='warehouse.ListMaterialConvertRequest.include_total', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='order', full_name='warehouse.ListMaterialConvertRequest.order', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sort', full_name='warehouse.ListMaterialConvertRequest.sort', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=619,
  serialized_end=962,
)


_MATERIAL = _descriptor.Descriptor(
  name='Material',
  full_name='warehouse.Material',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.Material.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='main_id', full_name='warehouse.Material.main_id', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_id', full_name='warehouse.Material.product_id', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_code', full_name='warehouse.Material.product_code', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_name', full_name='warehouse.Material.product_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='product_type', full_name='warehouse.Material.product_type', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_id', full_name='warehouse.Material.unit_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_name', full_name='warehouse.Material.unit_name', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_spec', full_name='warehouse.Material.unit_spec', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='quantity', full_name='warehouse.Material.quantity', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='type', full_name='warehouse.Material.type', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unit_rate', full_name='warehouse.Material.unit_rate', index=11,
      number=12, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='warehouse.Material.partner_id', index=12,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='warehouse.Material.created_by', index=13,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='warehouse.Material.created_name', index=14,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='warehouse.Material.updated_by', index=15,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='warehouse.Material.updated_name', index=16,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='warehouse.Material.created_at', index=17,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='warehouse.Material.updated_at', index=18,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=965,
  serialized_end=1396,
)


_MATERIALDETAIL = _descriptor.Descriptor(
  name='MaterialDetail',
  full_name='warehouse.MaterialDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.MaterialDetail.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.MaterialDetail.branch_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.MaterialDetail.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.MaterialDetail.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_type', full_name='warehouse.MaterialDetail.convert_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_date', full_name='warehouse.MaterialDetail.convert_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='warehouse.MaterialDetail.branch_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_code', full_name='warehouse.MaterialDetail.branch_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='warehouse.MaterialDetail.branch_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='warehouse.MaterialDetail.position_id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='warehouse.MaterialDetail.position_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='warehouse.MaterialDetail.position_code', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_rule', full_name='warehouse.MaterialDetail.convert_rule', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='warehouse.MaterialDetail.remark', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='warehouse.MaterialDetail.request_id', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='warehouse.MaterialDetail.partner_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='warehouse.MaterialDetail.created_by', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='warehouse.MaterialDetail.created_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='warehouse.MaterialDetail.updated_by', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='warehouse.MaterialDetail.updated_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='warehouse.MaterialDetail.created_at', index=20,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='warehouse.MaterialDetail.updated_at', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='warehouse.MaterialDetail.process_status', index=22,
      number=23, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='opened_position', full_name='warehouse.MaterialDetail.opened_position', index=23,
      number=24, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='warehouse.MaterialDetail.cost_center_id', index=24,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1399,
  serialized_end=2009,
)


_LISTMATERIALCONVERTRESPONSE = _descriptor.Descriptor(
  name='ListMaterialConvertResponse',
  full_name='warehouse.ListMaterialConvertResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='rows', full_name='warehouse.ListMaterialConvertResponse.rows', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='total', full_name='warehouse.ListMaterialConvertResponse.total', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2011,
  serialized_end=2096,
)


_GETMATERIALCONVERTDETAILREQUEST = _descriptor.Descriptor(
  name='GetMaterialConvertDetailRequest',
  full_name='warehouse.GetMaterialConvertDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='convert_id', full_name='warehouse.GetMaterialConvertDetailRequest.convert_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2098,
  serialized_end=2151,
)


_GETMATERIALCONVERTDETAILRESPONSE = _descriptor.Descriptor(
  name='GetMaterialConvertDetailResponse',
  full_name='warehouse.GetMaterialConvertDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='warehouse.GetMaterialConvertDetailResponse.id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.GetMaterialConvertDetailResponse.branch_type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='warehouse.GetMaterialConvertDetailResponse.code', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.GetMaterialConvertDetailResponse.status', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_type', full_name='warehouse.GetMaterialConvertDetailResponse.convert_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_date', full_name='warehouse.GetMaterialConvertDetailResponse.convert_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='warehouse.GetMaterialConvertDetailResponse.branch_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_code', full_name='warehouse.GetMaterialConvertDetailResponse.branch_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='warehouse.GetMaterialConvertDetailResponse.branch_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='warehouse.GetMaterialConvertDetailResponse.position_id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='warehouse.GetMaterialConvertDetailResponse.position_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='warehouse.GetMaterialConvertDetailResponse.position_code', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_rule', full_name='warehouse.GetMaterialConvertDetailResponse.convert_rule', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='warehouse.GetMaterialConvertDetailResponse.remark', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='request_id', full_name='warehouse.GetMaterialConvertDetailResponse.request_id', index=14,
      number=15, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='warehouse.GetMaterialConvertDetailResponse.partner_id', index=15,
      number=16, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_by', full_name='warehouse.GetMaterialConvertDetailResponse.created_by', index=16,
      number=17, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_name', full_name='warehouse.GetMaterialConvertDetailResponse.created_name', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_by', full_name='warehouse.GetMaterialConvertDetailResponse.updated_by', index=18,
      number=19, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_name', full_name='warehouse.GetMaterialConvertDetailResponse.updated_name', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='process_status', full_name='warehouse.GetMaterialConvertDetailResponse.process_status', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='created_at', full_name='warehouse.GetMaterialConvertDetailResponse.created_at', index=21,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='updated_at', full_name='warehouse.GetMaterialConvertDetailResponse.updated_at', index=22,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='opened_position', full_name='warehouse.GetMaterialConvertDetailResponse.opened_position', index=23,
      number=24, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='materials', full_name='warehouse.GetMaterialConvertDetailResponse.materials', index=24,
      number=30, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='warehouse.GetMaterialConvertDetailResponse.cost_center_id', index=25,
      number=35, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2154,
  serialized_end=2822,
)


_UPDATEMATERIALCONVERTREQUEST = _descriptor.Descriptor(
  name='UpdateMaterialConvertRequest',
  full_name='warehouse.UpdateMaterialConvertRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='convert_id', full_name='warehouse.UpdateMaterialConvertRequest.convert_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='warehouse.UpdateMaterialConvertRequest.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='update_detail', full_name='warehouse.UpdateMaterialConvertRequest.update_detail', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_type', full_name='warehouse.UpdateMaterialConvertRequest.branch_type', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_type', full_name='warehouse.UpdateMaterialConvertRequest.convert_type', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_date', full_name='warehouse.UpdateMaterialConvertRequest.convert_date', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_id', full_name='warehouse.UpdateMaterialConvertRequest.branch_id', index=6,
      number=7, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_code', full_name='warehouse.UpdateMaterialConvertRequest.branch_code', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='branch_name', full_name='warehouse.UpdateMaterialConvertRequest.branch_name', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_id', full_name='warehouse.UpdateMaterialConvertRequest.position_id', index=9,
      number=10, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_name', full_name='warehouse.UpdateMaterialConvertRequest.position_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='position_code', full_name='warehouse.UpdateMaterialConvertRequest.position_code', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='convert_rule', full_name='warehouse.UpdateMaterialConvertRequest.convert_rule', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='remark', full_name='warehouse.UpdateMaterialConvertRequest.remark', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='opened_position', full_name='warehouse.UpdateMaterialConvertRequest.opened_position', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='materials', full_name='warehouse.UpdateMaterialConvertRequest.materials', index=15,
      number=20, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cost_center_id', full_name='warehouse.UpdateMaterialConvertRequest.cost_center_id', index=16,
      number=30, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2825,
  serialized_end=3262,
)


_UPDATEMATERIALCONVERTRESPONSE = _descriptor.Descriptor(
  name='UpdateMaterialConvertResponse',
  full_name='warehouse.UpdateMaterialConvertResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='convert_id', full_name='warehouse.UpdateMaterialConvertResponse.convert_id', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='result', full_name='warehouse.UpdateMaterialConvertResponse.result', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3264,
  serialized_end=3331,
)

_CREATEMATERIALCONVERTREQUEST.fields_by_name['convert_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_CREATEMATERIALCONVERTREQUEST.fields_by_name['materials'].message_type = _MATERIAL
_LISTMATERIALCONVERTREQUEST.fields_by_name['start_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTMATERIALCONVERTREQUEST.fields_by_name['end_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_MATERIAL.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_MATERIAL.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_MATERIALDETAIL.fields_by_name['convert_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_MATERIALDETAIL.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_MATERIALDETAIL.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_LISTMATERIALCONVERTRESPONSE.fields_by_name['rows'].message_type = _MATERIALDETAIL
_GETMATERIALCONVERTDETAILRESPONSE.fields_by_name['convert_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETMATERIALCONVERTDETAILRESPONSE.fields_by_name['created_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETMATERIALCONVERTDETAILRESPONSE.fields_by_name['updated_at'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_GETMATERIALCONVERTDETAILRESPONSE.fields_by_name['materials'].message_type = _MATERIAL
_UPDATEMATERIALCONVERTREQUEST.fields_by_name['convert_date'].message_type = google_dot_protobuf_dot_timestamp__pb2._TIMESTAMP
_UPDATEMATERIALCONVERTREQUEST.fields_by_name['materials'].message_type = _MATERIAL
DESCRIPTOR.message_types_by_name['CreateMaterialConvertRequest'] = _CREATEMATERIALCONVERTREQUEST
DESCRIPTOR.message_types_by_name['CreateMaterialConvertResponse'] = _CREATEMATERIALCONVERTRESPONSE
DESCRIPTOR.message_types_by_name['ListMaterialConvertRequest'] = _LISTMATERIALCONVERTREQUEST
DESCRIPTOR.message_types_by_name['Material'] = _MATERIAL
DESCRIPTOR.message_types_by_name['MaterialDetail'] = _MATERIALDETAIL
DESCRIPTOR.message_types_by_name['ListMaterialConvertResponse'] = _LISTMATERIALCONVERTRESPONSE
DESCRIPTOR.message_types_by_name['GetMaterialConvertDetailRequest'] = _GETMATERIALCONVERTDETAILREQUEST
DESCRIPTOR.message_types_by_name['GetMaterialConvertDetailResponse'] = _GETMATERIALCONVERTDETAILRESPONSE
DESCRIPTOR.message_types_by_name['UpdateMaterialConvertRequest'] = _UPDATEMATERIALCONVERTREQUEST
DESCRIPTOR.message_types_by_name['UpdateMaterialConvertResponse'] = _UPDATEMATERIALCONVERTRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CreateMaterialConvertRequest = _reflection.GeneratedProtocolMessageType('CreateMaterialConvertRequest', (_message.Message,), dict(
  DESCRIPTOR = _CREATEMATERIALCONVERTREQUEST,
  __module__ = 'warehouse.material_convert_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CreateMaterialConvertRequest)
  ))
_sym_db.RegisterMessage(CreateMaterialConvertRequest)

CreateMaterialConvertResponse = _reflection.GeneratedProtocolMessageType('CreateMaterialConvertResponse', (_message.Message,), dict(
  DESCRIPTOR = _CREATEMATERIALCONVERTRESPONSE,
  __module__ = 'warehouse.material_convert_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.CreateMaterialConvertResponse)
  ))
_sym_db.RegisterMessage(CreateMaterialConvertResponse)

ListMaterialConvertRequest = _reflection.GeneratedProtocolMessageType('ListMaterialConvertRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTMATERIALCONVERTREQUEST,
  __module__ = 'warehouse.material_convert_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ListMaterialConvertRequest)
  ))
_sym_db.RegisterMessage(ListMaterialConvertRequest)

Material = _reflection.GeneratedProtocolMessageType('Material', (_message.Message,), dict(
  DESCRIPTOR = _MATERIAL,
  __module__ = 'warehouse.material_convert_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.Material)
  ))
_sym_db.RegisterMessage(Material)

MaterialDetail = _reflection.GeneratedProtocolMessageType('MaterialDetail', (_message.Message,), dict(
  DESCRIPTOR = _MATERIALDETAIL,
  __module__ = 'warehouse.material_convert_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.MaterialDetail)
  ))
_sym_db.RegisterMessage(MaterialDetail)

ListMaterialConvertResponse = _reflection.GeneratedProtocolMessageType('ListMaterialConvertResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTMATERIALCONVERTRESPONSE,
  __module__ = 'warehouse.material_convert_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.ListMaterialConvertResponse)
  ))
_sym_db.RegisterMessage(ListMaterialConvertResponse)

GetMaterialConvertDetailRequest = _reflection.GeneratedProtocolMessageType('GetMaterialConvertDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETMATERIALCONVERTDETAILREQUEST,
  __module__ = 'warehouse.material_convert_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetMaterialConvertDetailRequest)
  ))
_sym_db.RegisterMessage(GetMaterialConvertDetailRequest)

GetMaterialConvertDetailResponse = _reflection.GeneratedProtocolMessageType('GetMaterialConvertDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETMATERIALCONVERTDETAILRESPONSE,
  __module__ = 'warehouse.material_convert_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.GetMaterialConvertDetailResponse)
  ))
_sym_db.RegisterMessage(GetMaterialConvertDetailResponse)

UpdateMaterialConvertRequest = _reflection.GeneratedProtocolMessageType('UpdateMaterialConvertRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEMATERIALCONVERTREQUEST,
  __module__ = 'warehouse.material_convert_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.UpdateMaterialConvertRequest)
  ))
_sym_db.RegisterMessage(UpdateMaterialConvertRequest)

UpdateMaterialConvertResponse = _reflection.GeneratedProtocolMessageType('UpdateMaterialConvertResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEMATERIALCONVERTRESPONSE,
  __module__ = 'warehouse.material_convert_pb2'
  # @@protoc_insertion_point(class_scope:warehouse.UpdateMaterialConvertResponse)
  ))
_sym_db.RegisterMessage(UpdateMaterialConvertResponse)



_WAREHOUSEMATERIALCONVERT = _descriptor.ServiceDescriptor(
  name='WarehouseMaterialConvert',
  full_name='warehouse.WarehouseMaterialConvert',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  serialized_start=3334,
  serialized_end=4066,
  methods=[
  _descriptor.MethodDescriptor(
    name='CreateMaterialConvert',
    full_name='warehouse.WarehouseMaterialConvert.CreateMaterialConvert',
    index=0,
    containing_service=None,
    input_type=_CREATEMATERIALCONVERTREQUEST,
    output_type=_CREATEMATERIALCONVERTRESPONSE,
    serialized_options=_b('\202\323\344\223\0021\",/api/v2/supply/warehouse/material/conversion:\001*'),
  ),
  _descriptor.MethodDescriptor(
    name='ListMaterialConvert',
    full_name='warehouse.WarehouseMaterialConvert.ListMaterialConvert',
    index=1,
    containing_service=None,
    input_type=_LISTMATERIALCONVERTREQUEST,
    output_type=_LISTMATERIALCONVERTRESPONSE,
    serialized_options=_b('\202\323\344\223\0023\0221/api/v2/supply/warehouse/material/conversion/list'),
  ),
  _descriptor.MethodDescriptor(
    name='GetMaterialConvertDetail',
    full_name='warehouse.WarehouseMaterialConvert.GetMaterialConvertDetail',
    index=2,
    containing_service=None,
    input_type=_GETMATERIALCONVERTDETAILREQUEST,
    output_type=_GETMATERIALCONVERTDETAILRESPONSE,
    serialized_options=_b('\202\323\344\223\002B\022@/api/v2/supply/warehouse/material/conversion/{convert_id}/detail'),
  ),
  _descriptor.MethodDescriptor(
    name='UpdateMaterialConvert',
    full_name='warehouse.WarehouseMaterialConvert.UpdateMaterialConvert',
    index=3,
    containing_service=None,
    input_type=_UPDATEMATERIALCONVERTREQUEST,
    output_type=_UPDATEMATERIALCONVERTRESPONSE,
    serialized_options=_b('\202\323\344\223\002E\032@/api/v2/supply/warehouse/material/conversion/{convert_id}/update:\001*'),
  ),
])
_sym_db.RegisterServiceDescriptor(_WAREHOUSEMATERIALCONVERT)

DESCRIPTOR.services_by_name['WarehouseMaterialConvert'] = _WAREHOUSEMATERIALCONVERT

# @@protoc_insertion_point(module_scope)
