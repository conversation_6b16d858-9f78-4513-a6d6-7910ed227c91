# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

from warehouse import inventory_bi_pb2 as warehouse_dot_inventory__bi__pb2


class WarehouseInventoryBiServiceStub(object):
  """库存报表相关服务
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.RealtimeInventory = channel.unary_unary(
        '/warehouse.WarehouseInventoryBiService/RealtimeInventory',
        request_serializer=warehouse_dot_inventory__bi__pb2.RealtimeInventoryRequest.SerializeToString,
        response_deserializer=warehouse_dot_inventory__bi__pb2.RealtimeInventoryResponse.FromString,
        )
    self.DailyInventory = channel.unary_unary(
        '/warehouse.WarehouseInventoryBiService/DailyInventory',
        request_serializer=warehouse_dot_inventory__bi__pb2.DailyInventoryRequest.SerializeToString,
        response_deserializer=warehouse_dot_inventory__bi__pb2.DailyInventoryResponse.FromString,
        )
    self.QueryInventoryLog = channel.unary_unary(
        '/warehouse.WarehouseInventoryBiService/QueryInventoryLog',
        request_serializer=warehouse_dot_inventory__bi__pb2.QueryInventoryLogRequest.SerializeToString,
        response_deserializer=warehouse_dot_inventory__bi__pb2.QueryInventoryLogResponse.FromString,
        )
    self.RealtimeInventoryByAccounts = channel.unary_unary(
        '/warehouse.WarehouseInventoryBiService/RealtimeInventoryByAccounts',
        request_serializer=warehouse_dot_inventory__bi__pb2.RealtimeInventoryByAccountsRequest.SerializeToString,
        response_deserializer=warehouse_dot_inventory__bi__pb2.RealtimeInventoryByAccountsResponse.FromString,
        )
    self.SummaryInventory = channel.unary_unary(
        '/warehouse.WarehouseInventoryBiService/SummaryInventory',
        request_serializer=warehouse_dot_inventory__bi__pb2.SummaryInventoryRequest.SerializeToString,
        response_deserializer=warehouse_dot_inventory__bi__pb2.SummaryInventoryResponse.FromString,
        )


class WarehouseInventoryBiServiceServicer(object):
  """库存报表相关服务
  """

  def RealtimeInventory(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def DailyInventory(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def QueryInventoryLog(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def RealtimeInventoryByAccounts(self, request, context):
    """按账户(目前只兼容仓库/加工中心查询发货仓位库存)和指定商品查询实时库存，包装给前端用
    查询库存前校验当前账户是否有子账户，若有返回子账户的库存
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SummaryInventory(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_WarehouseInventoryBiServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'RealtimeInventory': grpc.unary_unary_rpc_method_handler(
          servicer.RealtimeInventory,
          request_deserializer=warehouse_dot_inventory__bi__pb2.RealtimeInventoryRequest.FromString,
          response_serializer=warehouse_dot_inventory__bi__pb2.RealtimeInventoryResponse.SerializeToString,
      ),
      'DailyInventory': grpc.unary_unary_rpc_method_handler(
          servicer.DailyInventory,
          request_deserializer=warehouse_dot_inventory__bi__pb2.DailyInventoryRequest.FromString,
          response_serializer=warehouse_dot_inventory__bi__pb2.DailyInventoryResponse.SerializeToString,
      ),
      'QueryInventoryLog': grpc.unary_unary_rpc_method_handler(
          servicer.QueryInventoryLog,
          request_deserializer=warehouse_dot_inventory__bi__pb2.QueryInventoryLogRequest.FromString,
          response_serializer=warehouse_dot_inventory__bi__pb2.QueryInventoryLogResponse.SerializeToString,
      ),
      'RealtimeInventoryByAccounts': grpc.unary_unary_rpc_method_handler(
          servicer.RealtimeInventoryByAccounts,
          request_deserializer=warehouse_dot_inventory__bi__pb2.RealtimeInventoryByAccountsRequest.FromString,
          response_serializer=warehouse_dot_inventory__bi__pb2.RealtimeInventoryByAccountsResponse.SerializeToString,
      ),
      'SummaryInventory': grpc.unary_unary_rpc_method_handler(
          servicer.SummaryInventory,
          request_deserializer=warehouse_dot_inventory__bi__pb2.SummaryInventoryRequest.FromString,
          response_serializer=warehouse_dot_inventory__bi__pb2.SummaryInventoryResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'warehouse.WarehouseInventoryBiService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
